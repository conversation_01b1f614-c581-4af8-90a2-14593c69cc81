import {
  require_baseSum
} from "./chunk-3S6JOO2D.js";
import {
  require_identity
} from "./chunk-64Z5HK43.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/sum.js
var require_sum = __commonJS({
  "node_modules/lodash/sum.js"(exports, module) {
    var baseSum = require_baseSum();
    var identity = require_identity();
    function sum(array) {
      return array && array.length ? baseSum(array, identity) : 0;
    }
    module.exports = sum;
  }
});
export default require_sum();
//# sourceMappingURL=lodash_sum.js.map
