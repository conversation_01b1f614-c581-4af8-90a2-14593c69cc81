import {
  toInteger
} from "./chunk-SQMTRHET.js";
import {
  toDate
} from "./chunk-HUOGXHLH.js";
import {
  requiredArgs
} from "./chunk-Z43A42SM.js";
import "./chunk-PLDDJCW6.js";

// node_modules/date-fns/esm/addDays/index.js
function addDays(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var amount = toInteger(dirtyAmount);
  if (isNaN(amount)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  if (!amount) {
    return date;
  }
  date.setDate(date.getDate() + amount);
  return date;
}
export {
  addDays as default
};
//# sourceMappingURL=date-fns_addDays.js.map
