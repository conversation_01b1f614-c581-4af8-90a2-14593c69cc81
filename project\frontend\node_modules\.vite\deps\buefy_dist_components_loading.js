import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/buefy/dist/components/loading/index.js
var require_loading = __commonJS({
  "node_modules/buefy/dist/components/loading/index.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports) : typeof define === "function" && define.amd ? define(["exports"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.Loading = {}));
    })(exports, function(exports2) {
      "use strict";
      function ownKeys(e, r) {
        var t = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var o = Object.getOwnPropertySymbols(e);
          r && (o = o.filter(function(r2) {
            return Object.getOwnPropertyDescriptor(e, r2).enumerable;
          })), t.push.apply(t, o);
        }
        return t;
      }
      function _objectSpread2(e) {
        for (var r = 1; r < arguments.length; r++) {
          var t = null != arguments[r] ? arguments[r] : {};
          r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
            _defineProperty(e, r2, t[r2]);
          }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
            Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
          });
        }
        return e;
      }
      function _toPrimitive(t, r) {
        if ("object" != typeof t || !t) return t;
        var e = t[Symbol.toPrimitive];
        if (void 0 !== e) {
          var i = e.call(t, r || "default");
          if ("object" != typeof i) return i;
          throw new TypeError("@@toPrimitive must return a primitive value.");
        }
        return ("string" === r ? String : Number)(t);
      }
      function _toPropertyKey(t) {
        var i = _toPrimitive(t, "string");
        return "symbol" == typeof i ? i : String(i);
      }
      function _typeof(o) {
        "@babel/helpers - typeof";
        return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
          return typeof o2;
        } : function(o2) {
          return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
        }, _typeof(o);
      }
      function _defineProperty(obj, key, value) {
        key = _toPropertyKey(key);
        if (key in obj) {
          Object.defineProperty(obj, key, {
            value,
            enumerable: true,
            configurable: true,
            writable: true
          });
        } else {
          obj[key] = value;
        }
        return obj;
      }
      var isObject = function isObject2(item) {
        return _typeof(item) === "object" && !Array.isArray(item);
      };
      var mergeFn = function mergeFn2(target, source) {
        var deep = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (deep || !Object.assign) {
          var isDeep = function isDeep2(prop) {
            return isObject(source[prop]) && target !== null && target.hasOwnProperty(prop) && isObject(target[prop]);
          };
          var replaced = Object.getOwnPropertyNames(source).map(function(prop) {
            return _defineProperty({}, prop, isDeep(prop) ? mergeFn2(target[prop], source[prop], deep) : source[prop]);
          }).reduce(function(a, b) {
            return _objectSpread2(_objectSpread2({}, a), b);
          }, {});
          return _objectSpread2(_objectSpread2({}, target), replaced);
        } else {
          return Object.assign(target, source);
        }
      };
      var merge = mergeFn;
      function removeElement(el) {
        if (typeof el.remove !== "undefined") {
          el.remove();
        } else if (typeof el.parentNode !== "undefined" && el.parentNode !== null) {
          el.parentNode.removeChild(el);
        }
      }
      var isSSR = typeof window === "undefined";
      var HTMLElement = isSSR ? Object : window.HTMLElement;
      var script = {
        name: "BLoading",
        // deprecated, to replace with default 'value' in the next breaking change
        model: {
          prop: "active",
          event: "update:active"
        },
        props: {
          active: Boolean,
          programmatic: Boolean,
          container: [Object, Function, HTMLElement],
          isFullPage: {
            type: Boolean,
            default: true
          },
          animation: {
            type: String,
            default: "fade"
          },
          canCancel: {
            type: Boolean,
            default: false
          },
          onCancel: {
            type: Function,
            default: function _default() {
            }
          }
        },
        data: function data() {
          return {
            isActive: this.active || false,
            displayInFullPage: this.isFullPage
          };
        },
        watch: {
          active: function active(value) {
            this.isActive = value;
          },
          isFullPage: function isFullPage(value) {
            this.displayInFullPage = value;
          }
        },
        methods: {
          /**
          * Close the Modal if canCancel.
          */
          cancel: function cancel() {
            if (!this.canCancel || !this.isActive) return;
            this.close();
          },
          /**
          * Emit events, and destroy modal if it's programmatic.
          */
          close: function close() {
            var _this = this;
            this.onCancel.apply(null, arguments);
            this.$emit("close");
            this.$emit("update:active", false);
            if (this.programmatic) {
              this.isActive = false;
              setTimeout(function() {
                _this.$destroy();
                removeElement(_this.$el);
              }, 150);
            }
          },
          /**
          * Keypress event that is bound to the document.
          */
          keyPress: function keyPress(_ref) {
            var key = _ref.key;
            if (key === "Escape" || key === "Esc") this.cancel();
          }
        },
        created: function created() {
          if (typeof window !== "undefined") {
            document.addEventListener("keyup", this.keyPress);
          }
        },
        beforeMount: function beforeMount() {
          if (this.programmatic) {
            if (!this.container) {
              document.body.appendChild(this.$el);
            } else {
              this.displayInFullPage = false;
              this.$emit("update:is-full-page", false);
              this.container.appendChild(this.$el);
            }
          }
        },
        mounted: function mounted() {
          if (this.programmatic) this.isActive = true;
        },
        beforeDestroy: function beforeDestroy() {
          if (typeof window !== "undefined") {
            document.removeEventListener("keyup", this.keyPress);
          }
        }
      };
      function normalizeComponent(template, style, script2, scopeId, isFunctionalTemplate, moduleIdentifier, shadowMode, createInjector, createInjectorSSR, createInjectorShadow) {
        if (typeof shadowMode !== "boolean") {
          createInjectorSSR = createInjector;
          createInjector = shadowMode;
          shadowMode = false;
        }
        const options = typeof script2 === "function" ? script2.options : script2;
        if (template && template.render) {
          options.render = template.render;
          options.staticRenderFns = template.staticRenderFns;
          options._compiled = true;
          if (isFunctionalTemplate) {
            options.functional = true;
          }
        }
        if (scopeId) {
          options._scopeId = scopeId;
        }
        let hook;
        if (moduleIdentifier) {
          hook = function(context) {
            context = context || // cached call
            this.$vnode && this.$vnode.ssrContext || // stateful
            this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext;
            if (!context && typeof __VUE_SSR_CONTEXT__ !== "undefined") {
              context = __VUE_SSR_CONTEXT__;
            }
            if (style) {
              style.call(this, createInjectorSSR(context));
            }
            if (context && context._registeredComponents) {
              context._registeredComponents.add(moduleIdentifier);
            }
          };
          options._ssrRegister = hook;
        } else if (style) {
          hook = shadowMode ? function(context) {
            style.call(this, createInjectorShadow(context, this.$root.$options.shadowRoot));
          } : function(context) {
            style.call(this, createInjector(context));
          };
        }
        if (hook) {
          if (options.functional) {
            const originalRender = options.render;
            options.render = function renderWithStyleInjection(h, context) {
              hook.call(context);
              return originalRender(h, context);
            };
          } else {
            const existing = options.beforeCreate;
            options.beforeCreate = existing ? [].concat(existing, hook) : [hook];
          }
        }
        return script2;
      }
      const __vue_script__ = script;
      var __vue_render__ = function() {
        var _vm = this;
        var _h = _vm.$createElement;
        var _c = _vm._self._c || _h;
        return _c("transition", { attrs: { "name": _vm.animation } }, [_c("div", { directives: [{ name: "show", rawName: "v-show", value: _vm.isActive, expression: "isActive" }], staticClass: "loading-overlay is-active", class: { "is-full-page": _vm.displayInFullPage } }, [_c("div", { staticClass: "loading-background", on: { "click": _vm.cancel } }), _vm._t("default", [_c("div", { staticClass: "loading-icon" })])], 2)]);
      };
      var __vue_staticRenderFns__ = [];
      const __vue_inject_styles__ = void 0;
      const __vue_scope_id__ = void 0;
      const __vue_module_identifier__ = void 0;
      const __vue_is_functional_template__ = false;
      const __vue_component__ = normalizeComponent(
        { render: __vue_render__, staticRenderFns: __vue_staticRenderFns__ },
        __vue_inject_styles__,
        __vue_script__,
        __vue_scope_id__,
        __vue_is_functional_template__,
        __vue_module_identifier__,
        false,
        void 0,
        void 0,
        void 0
      );
      var Loading = __vue_component__;
      var VueInstance;
      var use = function use2(plugin) {
        if (typeof window !== "undefined" && window.Vue) {
          window.Vue.use(plugin);
        }
      };
      var registerComponent = function registerComponent2(Vue, component) {
        Vue.component(component.name, component);
      };
      var registerComponentProgrammatic = function registerComponentProgrammatic2(Vue, property, component) {
        if (!Vue.prototype.$buefy) Vue.prototype.$buefy = {};
        Vue.prototype.$buefy[property] = component;
      };
      var localVueInstance;
      var LoadingProgrammatic = {
        open: function open(params) {
          var defaultParam = {
            programmatic: true
          };
          var propsData = merge(defaultParam, params);
          var vm = typeof window !== "undefined" && window.Vue ? window.Vue : localVueInstance || VueInstance;
          var LoadingComponent = vm.extend(Loading);
          return new LoadingComponent({
            el: document.createElement("div"),
            propsData
          });
        }
      };
      var Plugin = {
        install: function install(Vue) {
          localVueInstance = Vue;
          registerComponent(Vue, Loading);
          registerComponentProgrammatic(Vue, "loading", LoadingProgrammatic);
        }
      };
      use(Plugin);
      exports2.BLoading = Loading;
      exports2.LoadingProgrammatic = LoadingProgrammatic;
      exports2["default"] = Plugin;
      Object.defineProperty(exports2, "__esModule", { value: true });
    });
  }
});
export default require_loading();
/*! Bundled license information:

buefy/dist/components/loading/index.js:
  (*! Buefy v0.9.29 | MIT License | github.com/buefy/buefy *)
*/
//# sourceMappingURL=buefy_dist_components_loading.js.map
