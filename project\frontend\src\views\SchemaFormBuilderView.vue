<template>
  <div class="schema-editor">
    <!-- Header -->
    <div class="page-header">
      <div class="tab-buttons">
        <div class="title">
          <strong for="form-slug" class="label">{{ formSlug }} ({{ locale }}) </strong>
        </div>
        <button
          v-for="tab in TABS.filter(t => AVAILABLE_TABS.includes(t.value))"
          :key="tab.value"
          class="button"
          type="button"
          :class="[currentTab === tab.value ? 'is-primary' : 'is-outlined']"
          @click.prevent="
            () => {
              currentTab = tab.value;
              loadedTab[tab.value] = true;
            }
          "
        >
          <ReactiveIcon :icon="tab.icon"></ReactiveIcon>
          <span>{{ tab.value }}</span>
        </button>
      </div>
      <div class="json-editor__header is-flex">
        <div class="field is-horizontal is-grouped is-grouped-right">
          <p class="control">
            <span v-if="!saveable" id="error-message" class="has-text-danger">
              Json schema is invalid.
            </span>
          </p>
          <p class="control">
            <button
              id="save"
              class="button is-primary"
              :class="{ 'is-loading': isLoading }"
              type="button"
              :disabled="!saveable || isLoading"
              @click.prevent="isSaveModalActive = true"
            >
              <ReactiveIcon icon="lucide:save"></ReactiveIcon>
              <span>Save {{ saveType.toUpperCase() }}</span>
            </button>
          </p>
        </div>
      </div>
    </div>
    <hr />

    <!-- Schema Editor -->
    <section v-show="currentTab === 'schema'" v-if="loadedTab.schema" class="json-editor">
      <div class="container">
        <BuilderSaveHistory :schema-type="schemaType" />
        <hr />
        <!-- JSON field -->
        <div class="field">
          <SchemaJsonBox
            v-if="schemaType === 'form'"
            :schema="state.formSchema"
            :original="state.originalFormSchema"
            :valid.sync="saveable"
            :editor-json-schema-type="'form'"
            height="60vh"
            @update:schema="setFormSchema"
          />
          <SchemaJsonBox
            v-else-if="schemaType === 'page'"
            :schema="state.pageSchema"
            :original="state.originalPageSchema"
            :valid.sync="saveable"
            :editor-json-schema-type="'page'"
            height="60vh"
            @update:schema="setPageSchema"
          />
        </div>
      </div>
    </section>

    <!-- Report Editor -->
    <template v-if="schemaType === 'form'">
      <section v-show="currentTab === 'report'" v-if="loadedTab.report" class="json-editor">
        <div class="container">
          <BuilderSaveHistory schema-type="report" />
          <hr />
          <!-- JSON field -->
          <div class="field">
            <SchemaJsonBox
              :schema.sync="state.reportSchema"
              :original="state.originalReportSchema"
              :valid.sync="saveable"
              height="60vh"
            />
          </div>
        </div>
      </section>
    </template>

    <!-- Builder -->
    <section v-show="currentTab === 'builder'" v-if="loadedTab.builder" class="builder">
      <template v-if="schemaType === 'form'">
        <BuilderPage :form-slug="formSlug" class="builder-page" />
      </template>
      <template v-else-if="schemaType === 'page'">
        <BuilderSectionWrapper
          :section.sync="state.editable"
          :default-loaded="true"
          :draggable="false"
          :removable="false"
          class="builder-page builder-for-page has-text-centered"
        />
      </template>
    </section>
    <BuilderSaveModal
      :schema-type="currentTab === 'report' ? 'report' : saveType"
      :active.sync="isSaveModalActive"
    />
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

import { useDynamicFormStore } from '@core/store/modules/dynamic-form-module';

import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';

import BuilderPage from '@/components/builder/BuilderPage.vue';
import BuilderSaveHistory from '@/components/builder/BuilderSaveHistory.vue';
import BuilderSaveModal from '@/components/builder/BuilderSaveModal.vue';
import BuilderSectionWrapper from '@/components/builder/BuilderSectionWrapper.vue';
import SchemaJsonBox from '@/components/builder/SchemaJsonBox.vue';
import { useBuilderState } from '@/composables/builder/use-builder-state';
import { convertEditableSectionToSchema, convertEditableToSchema } from '@/helpers/builder';

// Props
const props = defineProps({
  formSlug: {
    type: String,
    required: true,
  },
  schemaType: {
    type: String as () => Types.SchemaEditorType,
    default: 'form',
  },
});

// Variable
const dynamicFormStore = useDynamicFormStore();
const { state, isLoading, getSchemaInfo, getPageInfo, setFormSchema, setPageSchema } =
  useBuilderState();

const { locale } = useI18n();

const TABS = [
  { icon: 'lucide:braces', value: 'schema' },
  { icon: 'lucide:brackets', value: 'report' },
  { icon: 'lucide:shapes', value: 'builder' },
] as const;
type TabEnum = (typeof TABS)[number]['value'];
const AVAILABLE_TABS: TabEnum[] =
  props.schemaType === 'form' ? ['schema', 'report', 'builder'] : ['schema', 'builder'];
const currentTab = ref<TabEnum>('schema');
const loadedTab = reactive<Record<TabEnum, boolean>>({
  schema: true,
  report: false,
  builder: false,
});
const saveable = ref(true);
const isSaveModalActive = ref(false);

const saveType = computed(() => (currentTab.value === 'report' ? 'report' : props.schemaType));

// Watcher
watch(
  [() => locale.value, () => props.formSlug],
  async () => {
    state.formSlug = props.formSlug;
    state.name = props.formSlug;
    state.editable = null;
    if (props.schemaType === 'form') {
      await getSchemaInfo();
    } else if (props.schemaType === 'page') {
      await getPageInfo();
    }
  },
  { immediate: true },
);

watch(
  () => state.editable,
  val => {
    if (!val) return;

    if (props.schemaType === 'form') {
      state.formSchema = convertEditableToSchema(val);
      dynamicFormStore.setFormSchema(state.formSchema as Types.ISchema);
    } else if (props.schemaType === 'page') {
      state.pageSchema = convertEditableSectionToSchema(val);
    }
  },
  { deep: true },
);
</script>

<style lang="scss" scoped>
.schema-editor {
  .page-header {
    display: flex;
    padding: 5px;

    .tab-buttons {
      flex-grow: 1;
      display: flex;
      align-items: baseline;
      gap: 10px;
      position: relative;
    }
  }

  div.jsoneditor {
    border-color: #04bebe;
  }
  div.jsoneditor-menu {
    background-color: #04bebe;
    border-bottom-color: #04bebe;
  }
}
</style>
