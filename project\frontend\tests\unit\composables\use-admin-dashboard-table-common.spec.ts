import { describe, expect, test, vi } from 'vitest';

import { useAdminDashboardTableCommon } from '@/composables/use-admin-dashboard-table-common';

describe('use-admin-dashboard-table-common', () => {
  const {
    filteredStatus,
    sorting,

    columnsSetting,
    computedColumns,
    computedCells,
    ICON_TYPE_MAP,
    CELL_TYPE_MAP,
    BASE_STATUS_ICON_CLASS,
    BASE_EKYC_ICON_CLASS,
    BASE_DECISION_ICON_CLASS,

    getStatusText,
    getStatusClass,
    getFaceCompareText,
    getFaceCompareClass,
    getDetailUrl,
    truncateWithEllipses,
    onClickSort,
    resetDashboardColumns,
    getComputedColumn,
    getMappedCell,
    getTypeIcon,
  } = useAdminDashboardTableCommon();

  const mocks = vi.hoisted(() => {
    return {
      useAdminWorkspaceManager: vi.fn().mockReturnValue({
        workspaces: { value: [] },
        currentWorkspace: {
          value: {
            id: 1,
            name: 'test-workspace',
            slug: 'test-workspace',
            is_owner: true,
            role: 'admin',
            created_at: new Date(),
            credit: 100,
            credit_expired_at: null,
            enable_post_paid: false,
            current_package: {
              name: 'free',
            },
          },
        },
      }),
    };
  });

  vi.mock('@/composables/use-admin-workspace-manager', () => ({
    useAdminWorkspaceManager: mocks.useAdminWorkspaceManager,
  }));

  describe('Should get correct value from getStatusText', () => {
    test.each`
      value     | expected
      ${'pass'} | ${'Pass'}
      ${'fail'} | ${'Unmatched'}
      ${1}      | ${''}
      ${false}  | ${''}
    `('getStatusText with value=$value', ({ value, expected }) => {
      const result = getStatusText(value);
      expect(result).toEqual(expected);
    });
  });

  describe('Should get correct value from getStatusClass', () => {
    test.each`
      value     | expected
      ${'pass'} | ${'is-pass'}
      ${'fail'} | ${'is-fail'}
      ${1}      | ${''}
      ${false}  | ${''}
    `('getStatusClass with value=$value', ({ value, expected }) => {
      const result = getStatusClass(value);
      expect(result).toEqual(expected);
    });
  });

  describe('Should get correct value from getFaceCompareText', () => {
    test.each([
      {
        value: { data: { 'ekyc:face_compare': { message: 'match', score: 0 } } },
        expected: 'Matched',
      },
      {
        value: { data: { 'ekyc:face_compare': { message: 'unmatch', score: 0 } } },
        expected: 'Unmatched',
      },
      {
        value: { data: { 'ekyc:face_compare': { message: '', score: 0 } } },
        expected: '',
      },
      {
        value: { data: { 'ekyc:face_compare': { message: '', score: 10 } } },
        expected: 10,
      },
    ])(`%s`, ({ value, expected }) => {
      const result = getFaceCompareText(value);
      expect(result).toEqual(expected);
    });
  });

  describe('Should get correct value from getFaceCompareClass', () => {
    test.each([
      {
        value: { data: { 'ekyc:face_compare': { message: 'match', score: 0 } } },
        expected: 'is-pass',
      },
      {
        value: { data: { 'ekyc:face_compare': { message: 'unmatch', score: 0 } } },
        expected: 'is-fail',
      },
      {
        value: { data: { 'ekyc:face_compare': { message: '', score: 0 } } },
        expected: '',
      },
      {
        value: { data: { 'ekyc:face_compare': { message: '', score: 10 } } },
        expected: 'is-warning',
      },
    ])(`%s`, ({ value, expected }) => {
      const result = getFaceCompareClass(value);
      expect(result).toEqual(expected);
    });
  });

  test('Should get url from row data', () => {
    const MOCK_ROW_DATA = {
      slug: 'test-appliedform-slug',
      applied_form: { info: { form: 'test-form-slug' } },
    };
    const result = getDetailUrl(MOCK_ROW_DATA);
    expect(result).toMatch(/.*?workspace=test-workspace/); // NOSONAR
  });

  describe('Should get correct result from truncateWithEllipses', () => {
    test.each([
      {
        title: 'Not pass any params',
        text: undefined,
        max: undefined,
        expected: '',
      },
      {
        title: 'Pass only "text"',
        text: 'This is test message to check truncateWithEllipses logic',
        max: undefined,
        expected: 'This is test messag&hellip;',
      },
      {
        title: 'Pass all params',
        text: 'This is test message to check truncateWithEllipses logic',
        max: 40,
        expected: 'This is test message to check truncateW&hellip;',
      },
      {
        title: 'Pass max value over text lenght',
        text: 'This is test message to check truncateWithEllipses logic',
        max: 100,
        expected: 'This is test message to check truncateWithEllipses logic',
      },
    ])('$title', ({ text, max, expected }) => {
      const result = truncateWithEllipses(text, max);
      expect(result).toStrictEqual(expected);
    });
  });

  describe('Should get correct type icon', () => {
    test.each([
      {
        type: 'id',
        expected: ICON_TYPE_MAP.id,
      },
      {
        type: 'date',
        expected: ICON_TYPE_MAP.date,
      },
      {
        type: 'parameter',
        expected: ICON_TYPE_MAP.parameter,
      },
      {
        type: 'custom',
        expected: ICON_TYPE_MAP.custom,
      },
      {
        type: 'status',
        expected: BASE_STATUS_ICON_CLASS,
      },
      {
        type: 'ekyc',
        expected: BASE_EKYC_ICON_CLASS,
      },
      {
        type: 'decision',
        expected: BASE_DECISION_ICON_CLASS,
      },
      {
        type: 'other-type',
        expected: {
          icon: 'feather:message-circle',
          class: 'default',
        },
      },
    ])('$type', ({ type, expected }) => {
      const result = getTypeIcon(type);
      expect(result).toStrictEqual(expected);
    });
  });

  test('Should get valid value from resetDashboardColumns', () => {
    columnsSetting.value = [
      {
        label: 'Test label',
        cells: [{ type: 'form' }],
      },
    ];
    resetDashboardColumns();
    expect(columnsSetting.value).toStrictEqual([]);
  });

  describe('Should get valid value from onClickSort', () => {
    test('Non exists sort key', () => {
      sorting.value = {};
      onClickSort('test-1');
      expect(sorting.value).toStrictEqual({
        'test-1': '',
      });
    });

    test('Exists sort key with "" value', () => {
      sorting.value = {
        'test-1': '',
      };
      onClickSort('test-1');
      expect(sorting.value).toStrictEqual({
        'test-1': '-',
      });
    });

    test('Exists sort key with "-" value', () => {
      sorting.value = {
        'test-1': '-',
      };
      onClickSort('test-1');
      expect(sorting.value).toStrictEqual({
        'test-1': '',
      });
    });
  });

  describe('Should get correct value from getComputedColumn', () => {
    test('Has custom column label', () => {
      const MOCK_COLUMN: Types.ApplicationDashboardColumnSetting = {
        label: 'Test Label',
        cells: [{ type: 'form' }],
      };
      const EXPECTED_RESULT = {
        label: 'Test Label',
        cells: [CELL_TYPE_MAP.form],
        stylingCellType: 'form',
        getSortKey: undefined,
        icon: expect.anything(),
      };
      const result = getComputedColumn(MOCK_COLUMN);
      expect(result).toStrictEqual(EXPECTED_RESULT);
    });

    test('Genarated column label', () => {
      const MOCK_COLUMN: Types.ApplicationDashboardColumnSetting = {
        cells: [{ type: 'form' }, { type: 'date' }],
      };
      const EXPECTED_RESULT = {
        label: `${CELL_TYPE_MAP?.form?.label} / ${CELL_TYPE_MAP?.date?.label}`,
        cells: [CELL_TYPE_MAP.form, CELL_TYPE_MAP.date],
        stylingCellType: 'date',
        icon: expect.anything(),
      };
      const result = getComputedColumn(MOCK_COLUMN);
      expect(result).toStrictEqual({ ...EXPECTED_RESULT, getSortKey: expect.anything() });
    });
  });

  describe('Should get correct payload from getMappedCell', () => {
    test.each([
      {
        type: 'form',
        expectedCellTypeObj: { styling: 'form' },
        expected: CELL_TYPE_MAP.form,
      },
      {
        type: 'id',
        expectedCellTypeObj: { styling: 'id' },
        expected: CELL_TYPE_MAP.id,
      },
      {
        type: 'date',
        expectedCellTypeObj: { styling: 'date' },
        expected: CELL_TYPE_MAP.date,
      },
      {
        type: 'name',
        expectedCellTypeObj: { styling: 'name' },
        expected: CELL_TYPE_MAP.name,
      },
      {
        type: 'status',
        expectedCellTypeObj: { styling: 'status' },
        expected: CELL_TYPE_MAP.status,
      },
      {
        type: 'mobile',
        expectedCellTypeObj: { styling: 'mobile' },
        expected: CELL_TYPE_MAP.mobile,
      },
      {
        type: 'email',
        expectedCellTypeObj: { styling: 'email' },
        expected: CELL_TYPE_MAP.email,
      },
      {
        type: 'document_number',
        expectedCellTypeObj: { styling: 'document_number' },
        expected: CELL_TYPE_MAP.document_number,
      },
      {
        type: 'ekyc',
        expectedCellTypeObj: { styling: 'ekyc' },
        expected: CELL_TYPE_MAP.ekyc,
      },
      {
        type: 'aml',
        expectedCellTypeObj: { styling: 'aml' },
        expected: CELL_TYPE_MAP.aml,
      },
      {
        type: 'otp',
        expectedCellTypeObj: { styling: 'otp' },
        expected: CELL_TYPE_MAP.otp,
      },
      {
        type: 'liveness',
        expectedCellTypeObj: { styling: 'liveness' },
        expected: CELL_TYPE_MAP.liveness,
      },
      {
        type: 'face_compare',
        expectedCellTypeObj: { styling: 'face_compare' },
        expected: CELL_TYPE_MAP.face_compare,
      },
      {
        type: 'submit',
        expectedCellTypeObj: { styling: 'submit' },
        expected: CELL_TYPE_MAP.submit,
      },
      {
        type: 'warning',
        expectedCellTypeObj: { styling: 'warning' },
        expected: CELL_TYPE_MAP.warning,
      },
      {
        type: 'latest_error',
        expectedCellTypeObj: { styling: 'latest_error' },
        expected: CELL_TYPE_MAP.latest_error,
      },
      {
        type: 'parameter',
        label: 'parameter type label',
        class: 'test-class',
        sort_key: 'test',
        expected: {
          type: 'parameter',
          label: 'parameter type label',
          class: 'test-class',
          sort_key: 'test',
        },
        expectedCellTypeObj: { styling: 'parameter' },
      },
      {
        type: 'decision',
        label: 'decision type label',
        key: 'decision key',
        sort_key: 'test',
        class: 'test-class',
        expected: {
          type: 'decision',
          label: 'decision type label',
          key: 'decision key',
          sort_key: 'test',
          class: 'test-class',
        },
        expectedCellTypeObj: { styling: 'decision' },
      },
    ] as (Types.ApplicationDashboardCellSetting & {
      expected: any;
      expectedCellTypeObj: { styling: string };
      cellTypeObj: { styling: string };
    })[])(`Cell type = $type`, ({ expected, expectedCellTypeObj, ...value }) => {
      const cellTypeObj = { styling: 'general' };
      const result = getMappedCell(value, cellTypeObj);
      const expectedResult = ['decision', 'parameter'].includes(value?.type)
        ? {
            ...expected,
            getValue: expect.anything(),
            getSortKey: expect.anything(),
          }
        : expected;

      expect(result).toEqual(expectedResult);
      expect(cellTypeObj).toEqual(expectedCellTypeObj);
    });
  });

  test('Should get filtered columns', () => {
    const MOCK_COLUMN_SETTINGS: Types.ApplicationDashboardColumnSetting[] = [
      {
        label: 'label-1',
        cells: [],
        show_only: ['incomplete'],
      },
      {
        label: 'label-2',
        cells: [],
      },
      {
        label: 'label-3',
        cells: [],
        show_only: [],
      },
    ];
    const expectedResult = [
      {
        label: 'label-1',
        cells: [],
        show_only: ['incomplete'],
        getSortKey: undefined,
        stylingCellType: 'generic',
        icon: expect.anything(),
      },
      {
        label: 'label-2',
        cells: [],
        getSortKey: undefined,
        stylingCellType: 'generic',
        icon: expect.anything(),
      },
    ];
    filteredStatus.value = 'incomplete';
    columnsSetting.value = [...MOCK_COLUMN_SETTINGS];

    expect(computedColumns.value).toHaveLength(2);
    expect(computedColumns.value).toStrictEqual(expectedResult);
  });

  test('Should get correct computed cells', () => {
    const MOCK_COLUMN_SETTINGS: Types.ApplicationDashboardColumnSetting[] = [
      {
        label: 'label-1',
        cells: [{ type: 'form' }],
        show_only: ['incomplete'],
      },
      {
        label: 'label-2',
        cells: [{ type: 'form' }],
      },
      {
        label: 'label-3',
        cells: [{ type: 'form' }],
        show_only: [],
      },
    ];
    const expectedResult = [CELL_TYPE_MAP.form, CELL_TYPE_MAP.form];
    filteredStatus.value = 'incomplete';
    columnsSetting.value = [...MOCK_COLUMN_SETTINGS];

    expect(computedCells.value).toHaveLength(2);
    expect(computedCells.value).toStrictEqual(expectedResult);
  });
});
