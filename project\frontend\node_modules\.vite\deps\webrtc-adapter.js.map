{"version": 3, "sources": ["../../sdp/sdp.js", "../../webrtc-adapter/src/js/utils.js", "../../webrtc-adapter/src/js/chrome/chrome_shim.js", "../../webrtc-adapter/src/js/chrome/getusermedia.js", "../../webrtc-adapter/src/js/firefox/firefox_shim.js", "../../webrtc-adapter/src/js/firefox/getusermedia.js", "../../webrtc-adapter/src/js/firefox/getdisplaymedia.js", "../../webrtc-adapter/src/js/safari/safari_shim.js", "../../webrtc-adapter/src/js/common_shim.js", "../../webrtc-adapter/src/js/adapter_factory.js", "../../webrtc-adapter/src/js/adapter_core.js"], "sourcesContent": ["/* eslint-env node */\n'use strict';\n\n// SDP helpers.\nconst SDPUtils = {};\n\n// Generate an alphanumeric identifier for cname or mids.\n// TODO: use UUIDs instead? https://gist.github.com/jed/982883\nSDPUtils.generateIdentifier = function() {\n  return Math.random().toString(36).substring(2, 12);\n};\n\n// The RTCP CNAME used by all peerconnections from the same JS.\nSDPUtils.localCName = SDPUtils.generateIdentifier();\n\n// Splits SDP into lines, dealing with both CRLF and LF.\nSDPUtils.splitLines = function(blob) {\n  return blob.trim().split('\\n').map(line => line.trim());\n};\n// Splits SDP into sessionpart and mediasections. Ensures CRLF.\nSDPUtils.splitSections = function(blob) {\n  const parts = blob.split('\\nm=');\n  return parts.map((part, index) => (index > 0 ?\n    'm=' + part : part).trim() + '\\r\\n');\n};\n\n// Returns the session description.\nSDPUtils.getDescription = function(blob) {\n  const sections = SDPUtils.splitSections(blob);\n  return sections && sections[0];\n};\n\n// Returns the individual media sections.\nSDPUtils.getMediaSections = function(blob) {\n  const sections = SDPUtils.splitSections(blob);\n  sections.shift();\n  return sections;\n};\n\n// Returns lines that start with a certain prefix.\nSDPUtils.matchPrefix = function(blob, prefix) {\n  return SDPUtils.splitLines(blob).filter(line => line.indexOf(prefix) === 0);\n};\n\n// Parses an ICE candidate line. Sample input:\n// candidate:702786350 2 udp 41819902 ******* 60769 typ relay raddr *******\n// rport 55996\"\n// Input can be prefixed with a=.\nSDPUtils.parseCandidate = function(line) {\n  let parts;\n  // Parse both variants.\n  if (line.indexOf('a=candidate:') === 0) {\n    parts = line.substring(12).split(' ');\n  } else {\n    parts = line.substring(10).split(' ');\n  }\n\n  const candidate = {\n    foundation: parts[0],\n    component: {1: 'rtp', 2: 'rtcp'}[parts[1]] || parts[1],\n    protocol: parts[2].toLowerCase(),\n    priority: parseInt(parts[3], 10),\n    ip: parts[4],\n    address: parts[4], // address is an alias for ip.\n    port: parseInt(parts[5], 10),\n    // skip parts[6] == 'typ'\n    type: parts[7],\n  };\n\n  for (let i = 8; i < parts.length; i += 2) {\n    switch (parts[i]) {\n      case 'raddr':\n        candidate.relatedAddress = parts[i + 1];\n        break;\n      case 'rport':\n        candidate.relatedPort = parseInt(parts[i + 1], 10);\n        break;\n      case 'tcptype':\n        candidate.tcpType = parts[i + 1];\n        break;\n      case 'ufrag':\n        candidate.ufrag = parts[i + 1]; // for backward compatibility.\n        candidate.usernameFragment = parts[i + 1];\n        break;\n      default: // extension handling, in particular ufrag. Don't overwrite.\n        if (candidate[parts[i]] === undefined) {\n          candidate[parts[i]] = parts[i + 1];\n        }\n        break;\n    }\n  }\n  return candidate;\n};\n\n// Translates a candidate object into SDP candidate attribute.\n// This does not include the a= prefix!\nSDPUtils.writeCandidate = function(candidate) {\n  const sdp = [];\n  sdp.push(candidate.foundation);\n\n  const component = candidate.component;\n  if (component === 'rtp') {\n    sdp.push(1);\n  } else if (component === 'rtcp') {\n    sdp.push(2);\n  } else {\n    sdp.push(component);\n  }\n  sdp.push(candidate.protocol.toUpperCase());\n  sdp.push(candidate.priority);\n  sdp.push(candidate.address || candidate.ip);\n  sdp.push(candidate.port);\n\n  const type = candidate.type;\n  sdp.push('typ');\n  sdp.push(type);\n  if (type !== 'host' && candidate.relatedAddress &&\n      candidate.relatedPort) {\n    sdp.push('raddr');\n    sdp.push(candidate.relatedAddress);\n    sdp.push('rport');\n    sdp.push(candidate.relatedPort);\n  }\n  if (candidate.tcpType && candidate.protocol.toLowerCase() === 'tcp') {\n    sdp.push('tcptype');\n    sdp.push(candidate.tcpType);\n  }\n  if (candidate.usernameFragment || candidate.ufrag) {\n    sdp.push('ufrag');\n    sdp.push(candidate.usernameFragment || candidate.ufrag);\n  }\n  return 'candidate:' + sdp.join(' ');\n};\n\n// Parses an ice-options line, returns an array of option tags.\n// Sample input:\n// a=ice-options:foo bar\nSDPUtils.parseIceOptions = function(line) {\n  return line.substring(14).split(' ');\n};\n\n// Parses a rtpmap line, returns RTCRtpCoddecParameters. Sample input:\n// a=rtpmap:111 opus/48000/2\nSDPUtils.parseRtpMap = function(line) {\n  let parts = line.substring(9).split(' ');\n  const parsed = {\n    payloadType: parseInt(parts.shift(), 10), // was: id\n  };\n\n  parts = parts[0].split('/');\n\n  parsed.name = parts[0];\n  parsed.clockRate = parseInt(parts[1], 10); // was: clockrate\n  parsed.channels = parts.length === 3 ? parseInt(parts[2], 10) : 1;\n  // legacy alias, got renamed back to channels in ORTC.\n  parsed.numChannels = parsed.channels;\n  return parsed;\n};\n\n// Generates a rtpmap line from RTCRtpCodecCapability or\n// RTCRtpCodecParameters.\nSDPUtils.writeRtpMap = function(codec) {\n  let pt = codec.payloadType;\n  if (codec.preferredPayloadType !== undefined) {\n    pt = codec.preferredPayloadType;\n  }\n  const channels = codec.channels || codec.numChannels || 1;\n  return 'a=rtpmap:' + pt + ' ' + codec.name + '/' + codec.clockRate +\n      (channels !== 1 ? '/' + channels : '') + '\\r\\n';\n};\n\n// Parses a extmap line (headerextension from RFC 5285). Sample input:\n// a=extmap:2 urn:ietf:params:rtp-hdrext:toffset\n// a=extmap:2/sendonly urn:ietf:params:rtp-hdrext:toffset\nSDPUtils.parseExtmap = function(line) {\n  const parts = line.substring(9).split(' ');\n  return {\n    id: parseInt(parts[0], 10),\n    direction: parts[0].indexOf('/') > 0 ? parts[0].split('/')[1] : 'sendrecv',\n    uri: parts[1],\n    attributes: parts.slice(2).join(' '),\n  };\n};\n\n// Generates an extmap line from RTCRtpHeaderExtensionParameters or\n// RTCRtpHeaderExtension.\nSDPUtils.writeExtmap = function(headerExtension) {\n  return 'a=extmap:' + (headerExtension.id || headerExtension.preferredId) +\n      (headerExtension.direction && headerExtension.direction !== 'sendrecv'\n        ? '/' + headerExtension.direction\n        : '') +\n      ' ' + headerExtension.uri +\n      (headerExtension.attributes ? ' ' + headerExtension.attributes : '') +\n      '\\r\\n';\n};\n\n// Parses a fmtp line, returns dictionary. Sample input:\n// a=fmtp:96 vbr=on;cng=on\n// Also deals with vbr=on; cng=on\n// Non-key-value such as telephone-events `0-15` get parsed as\n// {`0-15`:undefined}\nSDPUtils.parseFmtp = function(line) {\n  const parsed = {};\n  let kv;\n  const parts = line.substring(line.indexOf(' ') + 1).split(';');\n  for (let j = 0; j < parts.length; j++) {\n    kv = parts[j].trim().split('=');\n    parsed[kv[0].trim()] = kv[1];\n  }\n  return parsed;\n};\n\n// Generates a fmtp line from RTCRtpCodecCapability or RTCRtpCodecParameters.\nSDPUtils.writeFmtp = function(codec) {\n  let line = '';\n  let pt = codec.payloadType;\n  if (codec.preferredPayloadType !== undefined) {\n    pt = codec.preferredPayloadType;\n  }\n  if (codec.parameters && Object.keys(codec.parameters).length) {\n    const params = [];\n    Object.keys(codec.parameters).forEach(param => {\n      if (codec.parameters[param] !== undefined) {\n        params.push(param + '=' + codec.parameters[param]);\n      } else {\n        params.push(param);\n      }\n    });\n    line += 'a=fmtp:' + pt + ' ' + params.join(';') + '\\r\\n';\n  }\n  return line;\n};\n\n// Parses a rtcp-fb line, returns RTCPRtcpFeedback object. Sample input:\n// a=rtcp-fb:98 nack rpsi\nSDPUtils.parseRtcpFb = function(line) {\n  const parts = line.substring(line.indexOf(' ') + 1).split(' ');\n  return {\n    type: parts.shift(),\n    parameter: parts.join(' '),\n  };\n};\n\n// Generate a=rtcp-fb lines from RTCRtpCodecCapability or RTCRtpCodecParameters.\nSDPUtils.writeRtcpFb = function(codec) {\n  let lines = '';\n  let pt = codec.payloadType;\n  if (codec.preferredPayloadType !== undefined) {\n    pt = codec.preferredPayloadType;\n  }\n  if (codec.rtcpFeedback && codec.rtcpFeedback.length) {\n    // FIXME: special handling for trr-int?\n    codec.rtcpFeedback.forEach(fb => {\n      lines += 'a=rtcp-fb:' + pt + ' ' + fb.type +\n      (fb.parameter && fb.parameter.length ? ' ' + fb.parameter : '') +\n          '\\r\\n';\n    });\n  }\n  return lines;\n};\n\n// Parses a RFC 5576 ssrc media attribute. Sample input:\n// a=ssrc:3735928559 cname:something\nSDPUtils.parseSsrcMedia = function(line) {\n  const sp = line.indexOf(' ');\n  const parts = {\n    ssrc: parseInt(line.substring(7, sp), 10),\n  };\n  const colon = line.indexOf(':', sp);\n  if (colon > -1) {\n    parts.attribute = line.substring(sp + 1, colon);\n    parts.value = line.substring(colon + 1);\n  } else {\n    parts.attribute = line.substring(sp + 1);\n  }\n  return parts;\n};\n\n// Parse a ssrc-group line (see RFC 5576). Sample input:\n// a=ssrc-group:semantics 12 34\nSDPUtils.parseSsrcGroup = function(line) {\n  const parts = line.substring(13).split(' ');\n  return {\n    semantics: parts.shift(),\n    ssrcs: parts.map(ssrc => parseInt(ssrc, 10)),\n  };\n};\n\n// Extracts the MID (RFC 5888) from a media section.\n// Returns the MID or undefined if no mid line was found.\nSDPUtils.getMid = function(mediaSection) {\n  const mid = SDPUtils.matchPrefix(mediaSection, 'a=mid:')[0];\n  if (mid) {\n    return mid.substring(6);\n  }\n};\n\n// Parses a fingerprint line for DTLS-SRTP.\nSDPUtils.parseFingerprint = function(line) {\n  const parts = line.substring(14).split(' ');\n  return {\n    algorithm: parts[0].toLowerCase(), // algorithm is case-sensitive in Edge.\n    value: parts[1].toUpperCase(), // the definition is upper-case in RFC 4572.\n  };\n};\n\n// Extracts DTLS parameters from SDP media section or sessionpart.\n// FIXME: for consistency with other functions this should only\n//   get the fingerprint line as input. See also getIceParameters.\nSDPUtils.getDtlsParameters = function(mediaSection, sessionpart) {\n  const lines = SDPUtils.matchPrefix(mediaSection + sessionpart,\n    'a=fingerprint:');\n  // Note: a=setup line is ignored since we use the 'auto' role in Edge.\n  return {\n    role: 'auto',\n    fingerprints: lines.map(SDPUtils.parseFingerprint),\n  };\n};\n\n// Serializes DTLS parameters to SDP.\nSDPUtils.writeDtlsParameters = function(params, setupType) {\n  let sdp = 'a=setup:' + setupType + '\\r\\n';\n  params.fingerprints.forEach(fp => {\n    sdp += 'a=fingerprint:' + fp.algorithm + ' ' + fp.value + '\\r\\n';\n  });\n  return sdp;\n};\n\n// Parses a=crypto lines into\n//   https://rawgit.com/aboba/edgertc/master/msortc-rs4.html#dictionary-rtcsrtpsdesparameters-members\nSDPUtils.parseCryptoLine = function(line) {\n  const parts = line.substring(9).split(' ');\n  return {\n    tag: parseInt(parts[0], 10),\n    cryptoSuite: parts[1],\n    keyParams: parts[2],\n    sessionParams: parts.slice(3),\n  };\n};\n\nSDPUtils.writeCryptoLine = function(parameters) {\n  return 'a=crypto:' + parameters.tag + ' ' +\n    parameters.cryptoSuite + ' ' +\n    (typeof parameters.keyParams === 'object'\n      ? SDPUtils.writeCryptoKeyParams(parameters.keyParams)\n      : parameters.keyParams) +\n    (parameters.sessionParams ? ' ' + parameters.sessionParams.join(' ') : '') +\n    '\\r\\n';\n};\n\n// Parses the crypto key parameters into\n//   https://rawgit.com/aboba/edgertc/master/msortc-rs4.html#rtcsrtpkeyparam*\nSDPUtils.parseCryptoKeyParams = function(keyParams) {\n  if (keyParams.indexOf('inline:') !== 0) {\n    return null;\n  }\n  const parts = keyParams.substring(7).split('|');\n  return {\n    keyMethod: 'inline',\n    keySalt: parts[0],\n    lifeTime: parts[1],\n    mkiValue: parts[2] ? parts[2].split(':')[0] : undefined,\n    mkiLength: parts[2] ? parts[2].split(':')[1] : undefined,\n  };\n};\n\nSDPUtils.writeCryptoKeyParams = function(keyParams) {\n  return keyParams.keyMethod + ':'\n    + keyParams.keySalt +\n    (keyParams.lifeTime ? '|' + keyParams.lifeTime : '') +\n    (keyParams.mkiValue && keyParams.mkiLength\n      ? '|' + keyParams.mkiValue + ':' + keyParams.mkiLength\n      : '');\n};\n\n// Extracts all SDES parameters.\nSDPUtils.getCryptoParameters = function(mediaSection, sessionpart) {\n  const lines = SDPUtils.matchPrefix(mediaSection + sessionpart,\n    'a=crypto:');\n  return lines.map(SDPUtils.parseCryptoLine);\n};\n\n// Parses ICE information from SDP media section or sessionpart.\n// FIXME: for consistency with other functions this should only\n//   get the ice-ufrag and ice-pwd lines as input.\nSDPUtils.getIceParameters = function(mediaSection, sessionpart) {\n  const ufrag = SDPUtils.matchPrefix(mediaSection + sessionpart,\n    'a=ice-ufrag:')[0];\n  const pwd = SDPUtils.matchPrefix(mediaSection + sessionpart,\n    'a=ice-pwd:')[0];\n  if (!(ufrag && pwd)) {\n    return null;\n  }\n  return {\n    usernameFragment: ufrag.substring(12),\n    password: pwd.substring(10),\n  };\n};\n\n// Serializes ICE parameters to SDP.\nSDPUtils.writeIceParameters = function(params) {\n  let sdp = 'a=ice-ufrag:' + params.usernameFragment + '\\r\\n' +\n      'a=ice-pwd:' + params.password + '\\r\\n';\n  if (params.iceLite) {\n    sdp += 'a=ice-lite\\r\\n';\n  }\n  return sdp;\n};\n\n// Parses the SDP media section and returns RTCRtpParameters.\nSDPUtils.parseRtpParameters = function(mediaSection) {\n  const description = {\n    codecs: [],\n    headerExtensions: [],\n    fecMechanisms: [],\n    rtcp: [],\n  };\n  const lines = SDPUtils.splitLines(mediaSection);\n  const mline = lines[0].split(' ');\n  description.profile = mline[2];\n  for (let i = 3; i < mline.length; i++) { // find all codecs from mline[3..]\n    const pt = mline[i];\n    const rtpmapline = SDPUtils.matchPrefix(\n      mediaSection, 'a=rtpmap:' + pt + ' ')[0];\n    if (rtpmapline) {\n      const codec = SDPUtils.parseRtpMap(rtpmapline);\n      const fmtps = SDPUtils.matchPrefix(\n        mediaSection, 'a=fmtp:' + pt + ' ');\n      // Only the first a=fmtp:<pt> is considered.\n      codec.parameters = fmtps.length ? SDPUtils.parseFmtp(fmtps[0]) : {};\n      codec.rtcpFeedback = SDPUtils.matchPrefix(\n        mediaSection, 'a=rtcp-fb:' + pt + ' ')\n        .map(SDPUtils.parseRtcpFb);\n      description.codecs.push(codec);\n      // parse FEC mechanisms from rtpmap lines.\n      switch (codec.name.toUpperCase()) {\n        case 'RED':\n        case 'ULPFEC':\n          description.fecMechanisms.push(codec.name.toUpperCase());\n          break;\n        default: // only RED and ULPFEC are recognized as FEC mechanisms.\n          break;\n      }\n    }\n  }\n  SDPUtils.matchPrefix(mediaSection, 'a=extmap:').forEach(line => {\n    description.headerExtensions.push(SDPUtils.parseExtmap(line));\n  });\n  const wildcardRtcpFb = SDPUtils.matchPrefix(mediaSection, 'a=rtcp-fb:* ')\n    .map(SDPUtils.parseRtcpFb);\n  description.codecs.forEach(codec => {\n    wildcardRtcpFb.forEach(fb=> {\n      const duplicate = codec.rtcpFeedback.find(existingFeedback => {\n        return existingFeedback.type === fb.type &&\n          existingFeedback.parameter === fb.parameter;\n      });\n      if (!duplicate) {\n        codec.rtcpFeedback.push(fb);\n      }\n    });\n  });\n  // FIXME: parse rtcp.\n  return description;\n};\n\n// Generates parts of the SDP media section describing the capabilities /\n// parameters.\nSDPUtils.writeRtpDescription = function(kind, caps) {\n  let sdp = '';\n\n  // Build the mline.\n  sdp += 'm=' + kind + ' ';\n  sdp += caps.codecs.length > 0 ? '9' : '0'; // reject if no codecs.\n  sdp += ' ' + (caps.profile || 'UDP/TLS/RTP/SAVPF') + ' ';\n  sdp += caps.codecs.map(codec => {\n    if (codec.preferredPayloadType !== undefined) {\n      return codec.preferredPayloadType;\n    }\n    return codec.payloadType;\n  }).join(' ') + '\\r\\n';\n\n  sdp += 'c=IN IP4 0.0.0.0\\r\\n';\n  sdp += 'a=rtcp:9 IN IP4 0.0.0.0\\r\\n';\n\n  // Add a=rtpmap lines for each codec. Also fmtp and rtcp-fb.\n  caps.codecs.forEach(codec => {\n    sdp += SDPUtils.writeRtpMap(codec);\n    sdp += SDPUtils.writeFmtp(codec);\n    sdp += SDPUtils.writeRtcpFb(codec);\n  });\n  let maxptime = 0;\n  caps.codecs.forEach(codec => {\n    if (codec.maxptime > maxptime) {\n      maxptime = codec.maxptime;\n    }\n  });\n  if (maxptime > 0) {\n    sdp += 'a=maxptime:' + maxptime + '\\r\\n';\n  }\n\n  if (caps.headerExtensions) {\n    caps.headerExtensions.forEach(extension => {\n      sdp += SDPUtils.writeExtmap(extension);\n    });\n  }\n  // FIXME: write fecMechanisms.\n  return sdp;\n};\n\n// Parses the SDP media section and returns an array of\n// RTCRtpEncodingParameters.\nSDPUtils.parseRtpEncodingParameters = function(mediaSection) {\n  const encodingParameters = [];\n  const description = SDPUtils.parseRtpParameters(mediaSection);\n  const hasRed = description.fecMechanisms.indexOf('RED') !== -1;\n  const hasUlpfec = description.fecMechanisms.indexOf('ULPFEC') !== -1;\n\n  // filter a=ssrc:... cname:, ignore PlanB-msid\n  const ssrcs = SDPUtils.matchPrefix(mediaSection, 'a=ssrc:')\n    .map(line => SDPUtils.parseSsrcMedia(line))\n    .filter(parts => parts.attribute === 'cname');\n  const primarySsrc = ssrcs.length > 0 && ssrcs[0].ssrc;\n  let secondarySsrc;\n\n  const flows = SDPUtils.matchPrefix(mediaSection, 'a=ssrc-group:FID')\n    .map(line => {\n      const parts = line.substring(17).split(' ');\n      return parts.map(part => parseInt(part, 10));\n    });\n  if (flows.length > 0 && flows[0].length > 1 && flows[0][0] === primarySsrc) {\n    secondarySsrc = flows[0][1];\n  }\n\n  description.codecs.forEach(codec => {\n    if (codec.name.toUpperCase() === 'RTX' && codec.parameters.apt) {\n      let encParam = {\n        ssrc: primarySsrc,\n        codecPayloadType: parseInt(codec.parameters.apt, 10),\n      };\n      if (primarySsrc && secondarySsrc) {\n        encParam.rtx = {ssrc: secondarySsrc};\n      }\n      encodingParameters.push(encParam);\n      if (hasRed) {\n        encParam = JSON.parse(JSON.stringify(encParam));\n        encParam.fec = {\n          ssrc: primarySsrc,\n          mechanism: hasUlpfec ? 'red+ulpfec' : 'red',\n        };\n        encodingParameters.push(encParam);\n      }\n    }\n  });\n  if (encodingParameters.length === 0 && primarySsrc) {\n    encodingParameters.push({\n      ssrc: primarySsrc,\n    });\n  }\n\n  // we support both b=AS and b=TIAS but interpret AS as TIAS.\n  let bandwidth = SDPUtils.matchPrefix(mediaSection, 'b=');\n  if (bandwidth.length) {\n    if (bandwidth[0].indexOf('b=TIAS:') === 0) {\n      bandwidth = parseInt(bandwidth[0].substring(7), 10);\n    } else if (bandwidth[0].indexOf('b=AS:') === 0) {\n      // use formula from JSEP to convert b=AS to TIAS value.\n      bandwidth = parseInt(bandwidth[0].substring(5), 10) * 1000 * 0.95\n          - (50 * 40 * 8);\n    } else {\n      bandwidth = undefined;\n    }\n    encodingParameters.forEach(params => {\n      params.maxBitrate = bandwidth;\n    });\n  }\n  return encodingParameters;\n};\n\n// parses http://draft.ortc.org/#rtcrtcpparameters*\nSDPUtils.parseRtcpParameters = function(mediaSection) {\n  const rtcpParameters = {};\n\n  // Gets the first SSRC. Note that with RTX there might be multiple\n  // SSRCs.\n  const remoteSsrc = SDPUtils.matchPrefix(mediaSection, 'a=ssrc:')\n    .map(line => SDPUtils.parseSsrcMedia(line))\n    .filter(obj => obj.attribute === 'cname')[0];\n  if (remoteSsrc) {\n    rtcpParameters.cname = remoteSsrc.value;\n    rtcpParameters.ssrc = remoteSsrc.ssrc;\n  }\n\n  // Edge uses the compound attribute instead of reducedSize\n  // compound is !reducedSize\n  const rsize = SDPUtils.matchPrefix(mediaSection, 'a=rtcp-rsize');\n  rtcpParameters.reducedSize = rsize.length > 0;\n  rtcpParameters.compound = rsize.length === 0;\n\n  // parses the rtcp-mux attrіbute.\n  // Note that Edge does not support unmuxed RTCP.\n  const mux = SDPUtils.matchPrefix(mediaSection, 'a=rtcp-mux');\n  rtcpParameters.mux = mux.length > 0;\n\n  return rtcpParameters;\n};\n\nSDPUtils.writeRtcpParameters = function(rtcpParameters) {\n  let sdp = '';\n  if (rtcpParameters.reducedSize) {\n    sdp += 'a=rtcp-rsize\\r\\n';\n  }\n  if (rtcpParameters.mux) {\n    sdp += 'a=rtcp-mux\\r\\n';\n  }\n  if (rtcpParameters.ssrc !== undefined && rtcpParameters.cname) {\n    sdp += 'a=ssrc:' + rtcpParameters.ssrc +\n      ' cname:' + rtcpParameters.cname + '\\r\\n';\n  }\n  return sdp;\n};\n\n\n// parses either a=msid: or a=ssrc:... msid lines and returns\n// the id of the MediaStream and MediaStreamTrack.\nSDPUtils.parseMsid = function(mediaSection) {\n  let parts;\n  const spec = SDPUtils.matchPrefix(mediaSection, 'a=msid:');\n  if (spec.length === 1) {\n    parts = spec[0].substring(7).split(' ');\n    return {stream: parts[0], track: parts[1]};\n  }\n  const planB = SDPUtils.matchPrefix(mediaSection, 'a=ssrc:')\n    .map(line => SDPUtils.parseSsrcMedia(line))\n    .filter(msidParts => msidParts.attribute === 'msid');\n  if (planB.length > 0) {\n    parts = planB[0].value.split(' ');\n    return {stream: parts[0], track: parts[1]};\n  }\n};\n\n// SCTP\n// parses draft-ietf-mmusic-sctp-sdp-26 first and falls back\n// to draft-ietf-mmusic-sctp-sdp-05\nSDPUtils.parseSctpDescription = function(mediaSection) {\n  const mline = SDPUtils.parseMLine(mediaSection);\n  const maxSizeLine = SDPUtils.matchPrefix(mediaSection, 'a=max-message-size:');\n  let maxMessageSize;\n  if (maxSizeLine.length > 0) {\n    maxMessageSize = parseInt(maxSizeLine[0].substring(19), 10);\n  }\n  if (isNaN(maxMessageSize)) {\n    maxMessageSize = 65536;\n  }\n  const sctpPort = SDPUtils.matchPrefix(mediaSection, 'a=sctp-port:');\n  if (sctpPort.length > 0) {\n    return {\n      port: parseInt(sctpPort[0].substring(12), 10),\n      protocol: mline.fmt,\n      maxMessageSize,\n    };\n  }\n  const sctpMapLines = SDPUtils.matchPrefix(mediaSection, 'a=sctpmap:');\n  if (sctpMapLines.length > 0) {\n    const parts = sctpMapLines[0]\n      .substring(10)\n      .split(' ');\n    return {\n      port: parseInt(parts[0], 10),\n      protocol: parts[1],\n      maxMessageSize,\n    };\n  }\n};\n\n// SCTP\n// outputs the draft-ietf-mmusic-sctp-sdp-26 version that all browsers\n// support by now receiving in this format, unless we originally parsed\n// as the draft-ietf-mmusic-sctp-sdp-05 format (indicated by the m-line\n// protocol of DTLS/SCTP -- without UDP/ or TCP/)\nSDPUtils.writeSctpDescription = function(media, sctp) {\n  let output = [];\n  if (media.protocol !== 'DTLS/SCTP') {\n    output = [\n      'm=' + media.kind + ' 9 ' + media.protocol + ' ' + sctp.protocol + '\\r\\n',\n      'c=IN IP4 0.0.0.0\\r\\n',\n      'a=sctp-port:' + sctp.port + '\\r\\n',\n    ];\n  } else {\n    output = [\n      'm=' + media.kind + ' 9 ' + media.protocol + ' ' + sctp.port + '\\r\\n',\n      'c=IN IP4 0.0.0.0\\r\\n',\n      'a=sctpmap:' + sctp.port + ' ' + sctp.protocol + ' 65535\\r\\n',\n    ];\n  }\n  if (sctp.maxMessageSize !== undefined) {\n    output.push('a=max-message-size:' + sctp.maxMessageSize + '\\r\\n');\n  }\n  return output.join('');\n};\n\n// Generate a session ID for SDP.\n// https://tools.ietf.org/html/draft-ietf-rtcweb-jsep-20#section-5.2.1\n// recommends using a cryptographically random +ve 64-bit value\n// but right now this should be acceptable and within the right range\nSDPUtils.generateSessionId = function() {\n  return Math.random().toString().substr(2, 22);\n};\n\n// Write boiler plate for start of SDP\n// sessId argument is optional - if not supplied it will\n// be generated randomly\n// sessVersion is optional and defaults to 2\n// sessUser is optional and defaults to 'thisisadapterortc'\nSDPUtils.writeSessionBoilerplate = function(sessId, sessVer, sessUser) {\n  let sessionId;\n  const version = sessVer !== undefined ? sessVer : 2;\n  if (sessId) {\n    sessionId = sessId;\n  } else {\n    sessionId = SDPUtils.generateSessionId();\n  }\n  const user = sessUser || 'thisisadapterortc';\n  // FIXME: sess-id should be an NTP timestamp.\n  return 'v=0\\r\\n' +\n      'o=' + user + ' ' + sessionId + ' ' + version +\n        ' IN IP4 127.0.0.1\\r\\n' +\n      's=-\\r\\n' +\n      't=0 0\\r\\n';\n};\n\n// Gets the direction from the mediaSection or the sessionpart.\nSDPUtils.getDirection = function(mediaSection, sessionpart) {\n  // Look for sendrecv, sendonly, recvonly, inactive, default to sendrecv.\n  const lines = SDPUtils.splitLines(mediaSection);\n  for (let i = 0; i < lines.length; i++) {\n    switch (lines[i]) {\n      case 'a=sendrecv':\n      case 'a=sendonly':\n      case 'a=recvonly':\n      case 'a=inactive':\n        return lines[i].substring(2);\n      default:\n        // FIXME: What should happen here?\n    }\n  }\n  if (sessionpart) {\n    return SDPUtils.getDirection(sessionpart);\n  }\n  return 'sendrecv';\n};\n\nSDPUtils.getKind = function(mediaSection) {\n  const lines = SDPUtils.splitLines(mediaSection);\n  const mline = lines[0].split(' ');\n  return mline[0].substring(2);\n};\n\nSDPUtils.isRejected = function(mediaSection) {\n  return mediaSection.split(' ', 2)[1] === '0';\n};\n\nSDPUtils.parseMLine = function(mediaSection) {\n  const lines = SDPUtils.splitLines(mediaSection);\n  const parts = lines[0].substring(2).split(' ');\n  return {\n    kind: parts[0],\n    port: parseInt(parts[1], 10),\n    protocol: parts[2],\n    fmt: parts.slice(3).join(' '),\n  };\n};\n\nSDPUtils.parseOLine = function(mediaSection) {\n  const line = SDPUtils.matchPrefix(mediaSection, 'o=')[0];\n  const parts = line.substring(2).split(' ');\n  return {\n    username: parts[0],\n    sessionId: parts[1],\n    sessionVersion: parseInt(parts[2], 10),\n    netType: parts[3],\n    addressType: parts[4],\n    address: parts[5],\n  };\n};\n\n// a very naive interpretation of a valid SDP.\nSDPUtils.isValidSDP = function(blob) {\n  if (typeof blob !== 'string' || blob.length === 0) {\n    return false;\n  }\n  const lines = SDPUtils.splitLines(blob);\n  for (let i = 0; i < lines.length; i++) {\n    if (lines[i].length < 2 || lines[i].charAt(1) !== '=') {\n      return false;\n    }\n    // TODO: check the modifier a bit more.\n  }\n  return true;\n};\n\n// Expose public methods.\nif (typeof module === 'object') {\n  module.exports = SDPUtils;\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\n\nlet logDisabled_ = true;\nlet deprecationWarnings_ = true;\n\n/**\n * Extract browser version out of the provided user agent string.\n *\n * @param {!string} uastring userAgent string.\n * @param {!string} expr Regular expression used as match criteria.\n * @param {!number} pos position in the version string to be returned.\n * @return {!number} browser version.\n */\nexport function extractVersion(uastring, expr, pos) {\n  const match = uastring.match(expr);\n  return match && match.length >= pos && parseFloat(match[pos], 10);\n}\n\n// Wraps the peerconnection event eventNameToWrap in a function\n// which returns the modified event object (or false to prevent\n// the event).\nexport function wrapPeerConnectionEvent(window, eventNameToWrap, wrapper) {\n  if (!window.RTCPeerConnection) {\n    return;\n  }\n  const proto = window.RTCPeerConnection.prototype;\n  const nativeAddEventListener = proto.addEventListener;\n  proto.addEventListener = function(nativeEventName, cb) {\n    if (nativeEventName !== eventNameToWrap) {\n      return nativeAddEventListener.apply(this, arguments);\n    }\n    const wrappedCallback = (e) => {\n      const modifiedEvent = wrapper(e);\n      if (modifiedEvent) {\n        if (cb.handleEvent) {\n          cb.handleEvent(modifiedEvent);\n        } else {\n          cb(modifiedEvent);\n        }\n      }\n    };\n    this._eventMap = this._eventMap || {};\n    if (!this._eventMap[eventNameToWrap]) {\n      this._eventMap[eventNameToWrap] = new Map();\n    }\n    this._eventMap[eventNameToWrap].set(cb, wrappedCallback);\n    return nativeAddEventListener.apply(this, [nativeEventName,\n      wrappedCallback]);\n  };\n\n  const nativeRemoveEventListener = proto.removeEventListener;\n  proto.removeEventListener = function(nativeEventName, cb) {\n    if (nativeEventName !== eventNameToWrap || !this._eventMap\n        || !this._eventMap[eventNameToWrap]) {\n      return nativeRemoveEventListener.apply(this, arguments);\n    }\n    if (!this._eventMap[eventNameToWrap].has(cb)) {\n      return nativeRemoveEventListener.apply(this, arguments);\n    }\n    const unwrappedCb = this._eventMap[eventNameToWrap].get(cb);\n    this._eventMap[eventNameToWrap].delete(cb);\n    if (this._eventMap[eventNameToWrap].size === 0) {\n      delete this._eventMap[eventNameToWrap];\n    }\n    if (Object.keys(this._eventMap).length === 0) {\n      delete this._eventMap;\n    }\n    return nativeRemoveEventListener.apply(this, [nativeEventName,\n      unwrappedCb]);\n  };\n\n  Object.defineProperty(proto, 'on' + eventNameToWrap, {\n    get() {\n      return this['_on' + eventNameToWrap];\n    },\n    set(cb) {\n      if (this['_on' + eventNameToWrap]) {\n        this.removeEventListener(eventNameToWrap,\n          this['_on' + eventNameToWrap]);\n        delete this['_on' + eventNameToWrap];\n      }\n      if (cb) {\n        this.addEventListener(eventNameToWrap,\n          this['_on' + eventNameToWrap] = cb);\n      }\n    },\n    enumerable: true,\n    configurable: true\n  });\n}\n\nexport function disableLog(bool) {\n  if (typeof bool !== 'boolean') {\n    return new Error('Argument type: ' + typeof bool +\n        '. Please use a boolean.');\n  }\n  logDisabled_ = bool;\n  return (bool) ? 'adapter.js logging disabled' :\n    'adapter.js logging enabled';\n}\n\n/**\n * Disable or enable deprecation warnings\n * @param {!boolean} bool set to true to disable warnings.\n */\nexport function disableWarnings(bool) {\n  if (typeof bool !== 'boolean') {\n    return new Error('Argument type: ' + typeof bool +\n        '. Please use a boolean.');\n  }\n  deprecationWarnings_ = !bool;\n  return 'adapter.js deprecation warnings ' + (bool ? 'disabled' : 'enabled');\n}\n\nexport function log() {\n  if (typeof window === 'object') {\n    if (logDisabled_) {\n      return;\n    }\n    if (typeof console !== 'undefined' && typeof console.log === 'function') {\n      console.log.apply(console, arguments);\n    }\n  }\n}\n\n/**\n * Shows a deprecation warning suggesting the modern and spec-compatible API.\n */\nexport function deprecated(oldMethod, newMethod) {\n  if (!deprecationWarnings_) {\n    return;\n  }\n  console.warn(oldMethod + ' is deprecated, please use ' + newMethod +\n      ' instead.');\n}\n\n/**\n * Browser detector.\n *\n * @return {object} result containing browser and version\n *     properties.\n */\nexport function detectBrowser(window) {\n  // Returned result object.\n  const result = {browser: null, version: null};\n\n  // Fail early if it's not a browser\n  if (typeof window === 'undefined' || !window.navigator ||\n      !window.navigator.userAgent) {\n    result.browser = 'Not a browser.';\n    return result;\n  }\n\n  const {navigator} = window;\n\n  // Prefer navigator.userAgentData.\n  if (navigator.userAgentData && navigator.userAgentData.brands) {\n    const chromium = navigator.userAgentData.brands.find((brand) => {\n      return brand.brand === 'Chromium';\n    });\n    if (chromium) {\n      return {browser: 'chrome', version: parseInt(chromium.version, 10)};\n    }\n  }\n\n  if (navigator.mozGetUserMedia) { // Firefox.\n    result.browser = 'firefox';\n    result.version = parseInt(extractVersion(navigator.userAgent,\n      /Firefox\\/(\\d+)\\./, 1));\n  } else if (navigator.webkitGetUserMedia ||\n      (window.isSecureContext === false && window.webkitRTCPeerConnection)) {\n    // Chrome, Chromium, Webview, Opera.\n    // Version matches Chrome/WebRTC version.\n    // Chrome 74 removed webkitGetUserMedia on http as well so we need the\n    // more complicated fallback to webkitRTCPeerConnection.\n    result.browser = 'chrome';\n    result.version = parseInt(extractVersion(navigator.userAgent,\n      /Chrom(e|ium)\\/(\\d+)\\./, 2));\n  } else if (window.RTCPeerConnection &&\n      navigator.userAgent.match(/AppleWebKit\\/(\\d+)\\./)) { // Safari.\n    result.browser = 'safari';\n    result.version = parseInt(extractVersion(navigator.userAgent,\n      /AppleWebKit\\/(\\d+)\\./, 1));\n    result.supportsUnifiedPlan = window.RTCRtpTransceiver &&\n        'currentDirection' in window.RTCRtpTransceiver.prototype;\n    // Only for internal usage.\n    result._safariVersion = extractVersion(navigator.userAgent,\n      /Version\\/(\\d+(\\.?\\d+))/, 1);\n  } else { // Default fallthrough: not supported.\n    result.browser = 'Not a supported browser.';\n    return result;\n  }\n\n  return result;\n}\n\n/**\n * Checks if something is an object.\n *\n * @param {*} val The something you want to check.\n * @return true if val is an object, false otherwise.\n */\nfunction isObject(val) {\n  return Object.prototype.toString.call(val) === '[object Object]';\n}\n\n/**\n * Remove all empty objects and undefined values\n * from a nested object -- an enhanced and vanilla version\n * of Lodash's `compact`.\n */\nexport function compactObject(data) {\n  if (!isObject(data)) {\n    return data;\n  }\n\n  return Object.keys(data).reduce(function(accumulator, key) {\n    const isObj = isObject(data[key]);\n    const value = isObj ? compactObject(data[key]) : data[key];\n    const isEmptyObject = isObj && !Object.keys(value).length;\n    if (value === undefined || isEmptyObject) {\n      return accumulator;\n    }\n    return Object.assign(accumulator, {[key]: value});\n  }, {});\n}\n\n/* iterates the stats graph recursively. */\nexport function walkStats(stats, base, resultSet) {\n  if (!base || resultSet.has(base.id)) {\n    return;\n  }\n  resultSet.set(base.id, base);\n  Object.keys(base).forEach(name => {\n    if (name.endsWith('Id')) {\n      walkStats(stats, stats.get(base[name]), resultSet);\n    } else if (name.endsWith('Ids')) {\n      base[name].forEach(id => {\n        walkStats(stats, stats.get(id), resultSet);\n      });\n    }\n  });\n}\n\n/* filter getStats for a sender/receiver track. */\nexport function filterStats(result, track, outbound) {\n  const streamStatsType = outbound ? 'outbound-rtp' : 'inbound-rtp';\n  const filteredResult = new Map();\n  if (track === null) {\n    return filteredResult;\n  }\n  const trackStats = [];\n  result.forEach(value => {\n    if (value.type === 'track' &&\n        value.trackIdentifier === track.id) {\n      trackStats.push(value);\n    }\n  });\n  trackStats.forEach(trackStat => {\n    result.forEach(stats => {\n      if (stats.type === streamStatsType && stats.trackId === trackStat.id) {\n        walkStats(result, stats, filteredResult);\n      }\n    });\n  });\n  return filteredResult;\n}\n\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\nimport * as utils from '../utils.js';\n\nexport {shimGetUserMedia} from './getusermedia';\n\nexport function shimMediaStream(window) {\n  window.MediaStream = window.MediaStream || window.webkitMediaStream;\n}\n\nexport function shimOnTrack(window) {\n  if (typeof window === 'object' && window.RTCPeerConnection && !('ontrack' in\n      window.RTCPeerConnection.prototype)) {\n    Object.defineProperty(window.RTCPeerConnection.prototype, 'ontrack', {\n      get() {\n        return this._ontrack;\n      },\n      set(f) {\n        if (this._ontrack) {\n          this.removeEventListener('track', this._ontrack);\n        }\n        this.addEventListener('track', this._ontrack = f);\n      },\n      enumerable: true,\n      configurable: true\n    });\n    const origSetRemoteDescription =\n        window.RTCPeerConnection.prototype.setRemoteDescription;\n    window.RTCPeerConnection.prototype.setRemoteDescription =\n      function setRemoteDescription() {\n        if (!this._ontrackpoly) {\n          this._ontrackpoly = (e) => {\n            // onaddstream does not fire when a track is added to an existing\n            // stream. But stream.onaddtrack is implemented so we use that.\n            e.stream.addEventListener('addtrack', te => {\n              let receiver;\n              if (window.RTCPeerConnection.prototype.getReceivers) {\n                receiver = this.getReceivers()\n                  .find(r => r.track && r.track.id === te.track.id);\n              } else {\n                receiver = {track: te.track};\n              }\n\n              const event = new Event('track');\n              event.track = te.track;\n              event.receiver = receiver;\n              event.transceiver = {receiver};\n              event.streams = [e.stream];\n              this.dispatchEvent(event);\n            });\n            e.stream.getTracks().forEach(track => {\n              let receiver;\n              if (window.RTCPeerConnection.prototype.getReceivers) {\n                receiver = this.getReceivers()\n                  .find(r => r.track && r.track.id === track.id);\n              } else {\n                receiver = {track};\n              }\n              const event = new Event('track');\n              event.track = track;\n              event.receiver = receiver;\n              event.transceiver = {receiver};\n              event.streams = [e.stream];\n              this.dispatchEvent(event);\n            });\n          };\n          this.addEventListener('addstream', this._ontrackpoly);\n        }\n        return origSetRemoteDescription.apply(this, arguments);\n      };\n  } else {\n    // even if RTCRtpTransceiver is in window, it is only used and\n    // emitted in unified-plan. Unfortunately this means we need\n    // to unconditionally wrap the event.\n    utils.wrapPeerConnectionEvent(window, 'track', e => {\n      if (!e.transceiver) {\n        Object.defineProperty(e, 'transceiver',\n          {value: {receiver: e.receiver}});\n      }\n      return e;\n    });\n  }\n}\n\nexport function shimGetSendersWithDtmf(window) {\n  // Overrides addTrack/removeTrack, depends on shimAddTrackRemoveTrack.\n  if (typeof window === 'object' && window.RTCPeerConnection &&\n      !('getSenders' in window.RTCPeerConnection.prototype) &&\n      'createDTMFSender' in window.RTCPeerConnection.prototype) {\n    const shimSenderWithDtmf = function(pc, track) {\n      return {\n        track,\n        get dtmf() {\n          if (this._dtmf === undefined) {\n            if (track.kind === 'audio') {\n              this._dtmf = pc.createDTMFSender(track);\n            } else {\n              this._dtmf = null;\n            }\n          }\n          return this._dtmf;\n        },\n        _pc: pc\n      };\n    };\n\n    // augment addTrack when getSenders is not available.\n    if (!window.RTCPeerConnection.prototype.getSenders) {\n      window.RTCPeerConnection.prototype.getSenders = function getSenders() {\n        this._senders = this._senders || [];\n        return this._senders.slice(); // return a copy of the internal state.\n      };\n      const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\n      window.RTCPeerConnection.prototype.addTrack =\n        function addTrack(track, stream) {\n          let sender = origAddTrack.apply(this, arguments);\n          if (!sender) {\n            sender = shimSenderWithDtmf(this, track);\n            this._senders.push(sender);\n          }\n          return sender;\n        };\n\n      const origRemoveTrack = window.RTCPeerConnection.prototype.removeTrack;\n      window.RTCPeerConnection.prototype.removeTrack =\n        function removeTrack(sender) {\n          origRemoveTrack.apply(this, arguments);\n          const idx = this._senders.indexOf(sender);\n          if (idx !== -1) {\n            this._senders.splice(idx, 1);\n          }\n        };\n    }\n    const origAddStream = window.RTCPeerConnection.prototype.addStream;\n    window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\n      this._senders = this._senders || [];\n      origAddStream.apply(this, [stream]);\n      stream.getTracks().forEach(track => {\n        this._senders.push(shimSenderWithDtmf(this, track));\n      });\n    };\n\n    const origRemoveStream = window.RTCPeerConnection.prototype.removeStream;\n    window.RTCPeerConnection.prototype.removeStream =\n      function removeStream(stream) {\n        this._senders = this._senders || [];\n        origRemoveStream.apply(this, [stream]);\n\n        stream.getTracks().forEach(track => {\n          const sender = this._senders.find(s => s.track === track);\n          if (sender) { // remove sender\n            this._senders.splice(this._senders.indexOf(sender), 1);\n          }\n        });\n      };\n  } else if (typeof window === 'object' && window.RTCPeerConnection &&\n             'getSenders' in window.RTCPeerConnection.prototype &&\n             'createDTMFSender' in window.RTCPeerConnection.prototype &&\n             window.RTCRtpSender &&\n             !('dtmf' in window.RTCRtpSender.prototype)) {\n    const origGetSenders = window.RTCPeerConnection.prototype.getSenders;\n    window.RTCPeerConnection.prototype.getSenders = function getSenders() {\n      const senders = origGetSenders.apply(this, []);\n      senders.forEach(sender => sender._pc = this);\n      return senders;\n    };\n\n    Object.defineProperty(window.RTCRtpSender.prototype, 'dtmf', {\n      get() {\n        if (this._dtmf === undefined) {\n          if (this.track.kind === 'audio') {\n            this._dtmf = this._pc.createDTMFSender(this.track);\n          } else {\n            this._dtmf = null;\n          }\n        }\n        return this._dtmf;\n      }\n    });\n  }\n}\n\nexport function shimSenderReceiverGetStats(window) {\n  if (!(typeof window === 'object' && window.RTCPeerConnection &&\n      window.RTCRtpSender && window.RTCRtpReceiver)) {\n    return;\n  }\n\n  // shim sender stats.\n  if (!('getStats' in window.RTCRtpSender.prototype)) {\n    const origGetSenders = window.RTCPeerConnection.prototype.getSenders;\n    if (origGetSenders) {\n      window.RTCPeerConnection.prototype.getSenders = function getSenders() {\n        const senders = origGetSenders.apply(this, []);\n        senders.forEach(sender => sender._pc = this);\n        return senders;\n      };\n    }\n\n    const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\n    if (origAddTrack) {\n      window.RTCPeerConnection.prototype.addTrack = function addTrack() {\n        const sender = origAddTrack.apply(this, arguments);\n        sender._pc = this;\n        return sender;\n      };\n    }\n    window.RTCRtpSender.prototype.getStats = function getStats() {\n      const sender = this;\n      return this._pc.getStats().then(result =>\n        /* Note: this will include stats of all senders that\n         *   send a track with the same id as sender.track as\n         *   it is not possible to identify the RTCRtpSender.\n         */\n        utils.filterStats(result, sender.track, true));\n    };\n  }\n\n  // shim receiver stats.\n  if (!('getStats' in window.RTCRtpReceiver.prototype)) {\n    const origGetReceivers = window.RTCPeerConnection.prototype.getReceivers;\n    if (origGetReceivers) {\n      window.RTCPeerConnection.prototype.getReceivers =\n        function getReceivers() {\n          const receivers = origGetReceivers.apply(this, []);\n          receivers.forEach(receiver => receiver._pc = this);\n          return receivers;\n        };\n    }\n    utils.wrapPeerConnectionEvent(window, 'track', e => {\n      e.receiver._pc = e.srcElement;\n      return e;\n    });\n    window.RTCRtpReceiver.prototype.getStats = function getStats() {\n      const receiver = this;\n      return this._pc.getStats().then(result =>\n        utils.filterStats(result, receiver.track, false));\n    };\n  }\n\n  if (!('getStats' in window.RTCRtpSender.prototype &&\n      'getStats' in window.RTCRtpReceiver.prototype)) {\n    return;\n  }\n\n  // shim RTCPeerConnection.getStats(track).\n  const origGetStats = window.RTCPeerConnection.prototype.getStats;\n  window.RTCPeerConnection.prototype.getStats = function getStats() {\n    if (arguments.length > 0 &&\n        arguments[0] instanceof window.MediaStreamTrack) {\n      const track = arguments[0];\n      let sender;\n      let receiver;\n      let err;\n      this.getSenders().forEach(s => {\n        if (s.track === track) {\n          if (sender) {\n            err = true;\n          } else {\n            sender = s;\n          }\n        }\n      });\n      this.getReceivers().forEach(r => {\n        if (r.track === track) {\n          if (receiver) {\n            err = true;\n          } else {\n            receiver = r;\n          }\n        }\n        return r.track === track;\n      });\n      if (err || (sender && receiver)) {\n        return Promise.reject(new DOMException(\n          'There are more than one sender or receiver for the track.',\n          'InvalidAccessError'));\n      } else if (sender) {\n        return sender.getStats();\n      } else if (receiver) {\n        return receiver.getStats();\n      }\n      return Promise.reject(new DOMException(\n        'There is no sender or receiver for the track.',\n        'InvalidAccessError'));\n    }\n    return origGetStats.apply(this, arguments);\n  };\n}\n\nexport function shimAddTrackRemoveTrackWithNative(window) {\n  // shim addTrack/removeTrack with native variants in order to make\n  // the interactions with legacy getLocalStreams behave as in other browsers.\n  // Keeps a mapping stream.id => [stream, rtpsenders...]\n  window.RTCPeerConnection.prototype.getLocalStreams =\n    function getLocalStreams() {\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\n      return Object.keys(this._shimmedLocalStreams)\n        .map(streamId => this._shimmedLocalStreams[streamId][0]);\n    };\n\n  const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\n  window.RTCPeerConnection.prototype.addTrack =\n    function addTrack(track, stream) {\n      if (!stream) {\n        return origAddTrack.apply(this, arguments);\n      }\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\n\n      const sender = origAddTrack.apply(this, arguments);\n      if (!this._shimmedLocalStreams[stream.id]) {\n        this._shimmedLocalStreams[stream.id] = [stream, sender];\n      } else if (this._shimmedLocalStreams[stream.id].indexOf(sender) === -1) {\n        this._shimmedLocalStreams[stream.id].push(sender);\n      }\n      return sender;\n    };\n\n  const origAddStream = window.RTCPeerConnection.prototype.addStream;\n  window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\n    this._shimmedLocalStreams = this._shimmedLocalStreams || {};\n\n    stream.getTracks().forEach(track => {\n      const alreadyExists = this.getSenders().find(s => s.track === track);\n      if (alreadyExists) {\n        throw new DOMException('Track already exists.',\n          'InvalidAccessError');\n      }\n    });\n    const existingSenders = this.getSenders();\n    origAddStream.apply(this, arguments);\n    const newSenders = this.getSenders()\n      .filter(newSender => existingSenders.indexOf(newSender) === -1);\n    this._shimmedLocalStreams[stream.id] = [stream].concat(newSenders);\n  };\n\n  const origRemoveStream = window.RTCPeerConnection.prototype.removeStream;\n  window.RTCPeerConnection.prototype.removeStream =\n    function removeStream(stream) {\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\n      delete this._shimmedLocalStreams[stream.id];\n      return origRemoveStream.apply(this, arguments);\n    };\n\n  const origRemoveTrack = window.RTCPeerConnection.prototype.removeTrack;\n  window.RTCPeerConnection.prototype.removeTrack =\n    function removeTrack(sender) {\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\n      if (sender) {\n        Object.keys(this._shimmedLocalStreams).forEach(streamId => {\n          const idx = this._shimmedLocalStreams[streamId].indexOf(sender);\n          if (idx !== -1) {\n            this._shimmedLocalStreams[streamId].splice(idx, 1);\n          }\n          if (this._shimmedLocalStreams[streamId].length === 1) {\n            delete this._shimmedLocalStreams[streamId];\n          }\n        });\n      }\n      return origRemoveTrack.apply(this, arguments);\n    };\n}\n\nexport function shimAddTrackRemoveTrack(window, browserDetails) {\n  if (!window.RTCPeerConnection) {\n    return;\n  }\n  // shim addTrack and removeTrack.\n  if (window.RTCPeerConnection.prototype.addTrack &&\n      browserDetails.version >= 65) {\n    return shimAddTrackRemoveTrackWithNative(window);\n  }\n\n  // also shim pc.getLocalStreams when addTrack is shimmed\n  // to return the original streams.\n  const origGetLocalStreams = window.RTCPeerConnection.prototype\n    .getLocalStreams;\n  window.RTCPeerConnection.prototype.getLocalStreams =\n    function getLocalStreams() {\n      const nativeStreams = origGetLocalStreams.apply(this);\n      this._reverseStreams = this._reverseStreams || {};\n      return nativeStreams.map(stream => this._reverseStreams[stream.id]);\n    };\n\n  const origAddStream = window.RTCPeerConnection.prototype.addStream;\n  window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\n    this._streams = this._streams || {};\n    this._reverseStreams = this._reverseStreams || {};\n\n    stream.getTracks().forEach(track => {\n      const alreadyExists = this.getSenders().find(s => s.track === track);\n      if (alreadyExists) {\n        throw new DOMException('Track already exists.',\n          'InvalidAccessError');\n      }\n    });\n    // Add identity mapping for consistency with addTrack.\n    // Unless this is being used with a stream from addTrack.\n    if (!this._reverseStreams[stream.id]) {\n      const newStream = new window.MediaStream(stream.getTracks());\n      this._streams[stream.id] = newStream;\n      this._reverseStreams[newStream.id] = stream;\n      stream = newStream;\n    }\n    origAddStream.apply(this, [stream]);\n  };\n\n  const origRemoveStream = window.RTCPeerConnection.prototype.removeStream;\n  window.RTCPeerConnection.prototype.removeStream =\n    function removeStream(stream) {\n      this._streams = this._streams || {};\n      this._reverseStreams = this._reverseStreams || {};\n\n      origRemoveStream.apply(this, [(this._streams[stream.id] || stream)]);\n      delete this._reverseStreams[(this._streams[stream.id] ?\n        this._streams[stream.id].id : stream.id)];\n      delete this._streams[stream.id];\n    };\n\n  window.RTCPeerConnection.prototype.addTrack =\n    function addTrack(track, stream) {\n      if (this.signalingState === 'closed') {\n        throw new DOMException(\n          'The RTCPeerConnection\\'s signalingState is \\'closed\\'.',\n          'InvalidStateError');\n      }\n      const streams = [].slice.call(arguments, 1);\n      if (streams.length !== 1 ||\n          !streams[0].getTracks().find(t => t === track)) {\n        // this is not fully correct but all we can manage without\n        // [[associated MediaStreams]] internal slot.\n        throw new DOMException(\n          'The adapter.js addTrack polyfill only supports a single ' +\n          ' stream which is associated with the specified track.',\n          'NotSupportedError');\n      }\n\n      const alreadyExists = this.getSenders().find(s => s.track === track);\n      if (alreadyExists) {\n        throw new DOMException('Track already exists.',\n          'InvalidAccessError');\n      }\n\n      this._streams = this._streams || {};\n      this._reverseStreams = this._reverseStreams || {};\n      const oldStream = this._streams[stream.id];\n      if (oldStream) {\n        // this is using odd Chrome behaviour, use with caution:\n        // https://bugs.chromium.org/p/webrtc/issues/detail?id=7815\n        // Note: we rely on the high-level addTrack/dtmf shim to\n        // create the sender with a dtmf sender.\n        oldStream.addTrack(track);\n\n        // Trigger ONN async.\n        Promise.resolve().then(() => {\n          this.dispatchEvent(new Event('negotiationneeded'));\n        });\n      } else {\n        const newStream = new window.MediaStream([track]);\n        this._streams[stream.id] = newStream;\n        this._reverseStreams[newStream.id] = stream;\n        this.addStream(newStream);\n      }\n      return this.getSenders().find(s => s.track === track);\n    };\n\n  // replace the internal stream id with the external one and\n  // vice versa.\n  function replaceInternalStreamId(pc, description) {\n    let sdp = description.sdp;\n    Object.keys(pc._reverseStreams || []).forEach(internalId => {\n      const externalStream = pc._reverseStreams[internalId];\n      const internalStream = pc._streams[externalStream.id];\n      sdp = sdp.replace(new RegExp(internalStream.id, 'g'),\n        externalStream.id);\n    });\n    return new RTCSessionDescription({\n      type: description.type,\n      sdp\n    });\n  }\n  function replaceExternalStreamId(pc, description) {\n    let sdp = description.sdp;\n    Object.keys(pc._reverseStreams || []).forEach(internalId => {\n      const externalStream = pc._reverseStreams[internalId];\n      const internalStream = pc._streams[externalStream.id];\n      sdp = sdp.replace(new RegExp(externalStream.id, 'g'),\n        internalStream.id);\n    });\n    return new RTCSessionDescription({\n      type: description.type,\n      sdp\n    });\n  }\n  ['createOffer', 'createAnswer'].forEach(function(method) {\n    const nativeMethod = window.RTCPeerConnection.prototype[method];\n    const methodObj = {[method]() {\n      const args = arguments;\n      const isLegacyCall = arguments.length &&\n          typeof arguments[0] === 'function';\n      if (isLegacyCall) {\n        return nativeMethod.apply(this, [\n          (description) => {\n            const desc = replaceInternalStreamId(this, description);\n            args[0].apply(null, [desc]);\n          },\n          (err) => {\n            if (args[1]) {\n              args[1].apply(null, err);\n            }\n          }, arguments[2]\n        ]);\n      }\n      return nativeMethod.apply(this, arguments)\n        .then(description => replaceInternalStreamId(this, description));\n    }};\n    window.RTCPeerConnection.prototype[method] = methodObj[method];\n  });\n\n  const origSetLocalDescription =\n      window.RTCPeerConnection.prototype.setLocalDescription;\n  window.RTCPeerConnection.prototype.setLocalDescription =\n    function setLocalDescription() {\n      if (!arguments.length || !arguments[0].type) {\n        return origSetLocalDescription.apply(this, arguments);\n      }\n      arguments[0] = replaceExternalStreamId(this, arguments[0]);\n      return origSetLocalDescription.apply(this, arguments);\n    };\n\n  // TODO: mangle getStats: https://w3c.github.io/webrtc-stats/#dom-rtcmediastreamstats-streamidentifier\n\n  const origLocalDescription = Object.getOwnPropertyDescriptor(\n    window.RTCPeerConnection.prototype, 'localDescription');\n  Object.defineProperty(window.RTCPeerConnection.prototype,\n    'localDescription', {\n      get() {\n        const description = origLocalDescription.get.apply(this);\n        if (description.type === '') {\n          return description;\n        }\n        return replaceInternalStreamId(this, description);\n      }\n    });\n\n  window.RTCPeerConnection.prototype.removeTrack =\n    function removeTrack(sender) {\n      if (this.signalingState === 'closed') {\n        throw new DOMException(\n          'The RTCPeerConnection\\'s signalingState is \\'closed\\'.',\n          'InvalidStateError');\n      }\n      // We can not yet check for sender instanceof RTCRtpSender\n      // since we shim RTPSender. So we check if sender._pc is set.\n      if (!sender._pc) {\n        throw new DOMException('Argument 1 of RTCPeerConnection.removeTrack ' +\n            'does not implement interface RTCRtpSender.', 'TypeError');\n      }\n      const isLocal = sender._pc === this;\n      if (!isLocal) {\n        throw new DOMException('Sender was not created by this connection.',\n          'InvalidAccessError');\n      }\n\n      // Search for the native stream the senders track belongs to.\n      this._streams = this._streams || {};\n      let stream;\n      Object.keys(this._streams).forEach(streamid => {\n        const hasTrack = this._streams[streamid].getTracks()\n          .find(track => sender.track === track);\n        if (hasTrack) {\n          stream = this._streams[streamid];\n        }\n      });\n\n      if (stream) {\n        if (stream.getTracks().length === 1) {\n          // if this is the last track of the stream, remove the stream. This\n          // takes care of any shimmed _senders.\n          this.removeStream(this._reverseStreams[stream.id]);\n        } else {\n          // relying on the same odd chrome behaviour as above.\n          stream.removeTrack(sender.track);\n        }\n        this.dispatchEvent(new Event('negotiationneeded'));\n      }\n    };\n}\n\nexport function shimPeerConnection(window, browserDetails) {\n  if (!window.RTCPeerConnection && window.webkitRTCPeerConnection) {\n    // very basic support for old versions.\n    window.RTCPeerConnection = window.webkitRTCPeerConnection;\n  }\n  if (!window.RTCPeerConnection) {\n    return;\n  }\n\n  // shim implicit creation of RTCSessionDescription/RTCIceCandidate\n  if (browserDetails.version < 53) {\n    ['setLocalDescription', 'setRemoteDescription', 'addIceCandidate']\n      .forEach(function(method) {\n        const nativeMethod = window.RTCPeerConnection.prototype[method];\n        const methodObj = {[method]() {\n          arguments[0] = new ((method === 'addIceCandidate') ?\n            window.RTCIceCandidate :\n            window.RTCSessionDescription)(arguments[0]);\n          return nativeMethod.apply(this, arguments);\n        }};\n        window.RTCPeerConnection.prototype[method] = methodObj[method];\n      });\n  }\n}\n\n// Attempt to fix ONN in plan-b mode.\nexport function fixNegotiationNeeded(window, browserDetails) {\n  utils.wrapPeerConnectionEvent(window, 'negotiationneeded', e => {\n    const pc = e.target;\n    if (browserDetails.version < 72 || (pc.getConfiguration &&\n        pc.getConfiguration().sdpSemantics === 'plan-b')) {\n      if (pc.signalingState !== 'stable') {\n        return;\n      }\n    }\n    return e;\n  });\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\nimport * as utils from '../utils.js';\nconst logging = utils.log;\n\nexport function shimGetUserMedia(window, browserDetails) {\n  const navigator = window && window.navigator;\n\n  if (!navigator.mediaDevices) {\n    return;\n  }\n\n  const constraintsToChrome_ = function(c) {\n    if (typeof c !== 'object' || c.mandatory || c.optional) {\n      return c;\n    }\n    const cc = {};\n    Object.keys(c).forEach(key => {\n      if (key === 'require' || key === 'advanced' || key === 'mediaSource') {\n        return;\n      }\n      const r = (typeof c[key] === 'object') ? c[key] : {ideal: c[key]};\n      if (r.exact !== undefined && typeof r.exact === 'number') {\n        r.min = r.max = r.exact;\n      }\n      const oldname_ = function(prefix, name) {\n        if (prefix) {\n          return prefix + name.charAt(0).toUpperCase() + name.slice(1);\n        }\n        return (name === 'deviceId') ? 'sourceId' : name;\n      };\n      if (r.ideal !== undefined) {\n        cc.optional = cc.optional || [];\n        let oc = {};\n        if (typeof r.ideal === 'number') {\n          oc[oldname_('min', key)] = r.ideal;\n          cc.optional.push(oc);\n          oc = {};\n          oc[oldname_('max', key)] = r.ideal;\n          cc.optional.push(oc);\n        } else {\n          oc[oldname_('', key)] = r.ideal;\n          cc.optional.push(oc);\n        }\n      }\n      if (r.exact !== undefined && typeof r.exact !== 'number') {\n        cc.mandatory = cc.mandatory || {};\n        cc.mandatory[oldname_('', key)] = r.exact;\n      } else {\n        ['min', 'max'].forEach(mix => {\n          if (r[mix] !== undefined) {\n            cc.mandatory = cc.mandatory || {};\n            cc.mandatory[oldname_(mix, key)] = r[mix];\n          }\n        });\n      }\n    });\n    if (c.advanced) {\n      cc.optional = (cc.optional || []).concat(c.advanced);\n    }\n    return cc;\n  };\n\n  const shimConstraints_ = function(constraints, func) {\n    if (browserDetails.version >= 61) {\n      return func(constraints);\n    }\n    constraints = JSON.parse(JSON.stringify(constraints));\n    if (constraints && typeof constraints.audio === 'object') {\n      const remap = function(obj, a, b) {\n        if (a in obj && !(b in obj)) {\n          obj[b] = obj[a];\n          delete obj[a];\n        }\n      };\n      constraints = JSON.parse(JSON.stringify(constraints));\n      remap(constraints.audio, 'autoGainControl', 'googAutoGainControl');\n      remap(constraints.audio, 'noiseSuppression', 'googNoiseSuppression');\n      constraints.audio = constraintsToChrome_(constraints.audio);\n    }\n    if (constraints && typeof constraints.video === 'object') {\n      // Shim facingMode for mobile & surface pro.\n      let face = constraints.video.facingMode;\n      face = face && ((typeof face === 'object') ? face : {ideal: face});\n      const getSupportedFacingModeLies = browserDetails.version < 66;\n\n      if ((face && (face.exact === 'user' || face.exact === 'environment' ||\n                    face.ideal === 'user' || face.ideal === 'environment')) &&\n          !(navigator.mediaDevices.getSupportedConstraints &&\n            navigator.mediaDevices.getSupportedConstraints().facingMode &&\n            !getSupportedFacingModeLies)) {\n        delete constraints.video.facingMode;\n        let matches;\n        if (face.exact === 'environment' || face.ideal === 'environment') {\n          matches = ['back', 'rear'];\n        } else if (face.exact === 'user' || face.ideal === 'user') {\n          matches = ['front'];\n        }\n        if (matches) {\n          // Look for matches in label, or use last cam for back (typical).\n          return navigator.mediaDevices.enumerateDevices()\n            .then(devices => {\n              devices = devices.filter(d => d.kind === 'videoinput');\n              let dev = devices.find(d => matches.some(match =>\n                d.label.toLowerCase().includes(match)));\n              if (!dev && devices.length && matches.includes('back')) {\n                dev = devices[devices.length - 1]; // more likely the back cam\n              }\n              if (dev) {\n                constraints.video.deviceId = face.exact\n                  ? {exact: dev.deviceId}\n                  : {ideal: dev.deviceId};\n              }\n              constraints.video = constraintsToChrome_(constraints.video);\n              logging('chrome: ' + JSON.stringify(constraints));\n              return func(constraints);\n            });\n        }\n      }\n      constraints.video = constraintsToChrome_(constraints.video);\n    }\n    logging('chrome: ' + JSON.stringify(constraints));\n    return func(constraints);\n  };\n\n  const shimError_ = function(e) {\n    if (browserDetails.version >= 64) {\n      return e;\n    }\n    return {\n      name: {\n        PermissionDeniedError: 'NotAllowedError',\n        PermissionDismissedError: 'NotAllowedError',\n        InvalidStateError: 'NotAllowedError',\n        DevicesNotFoundError: 'NotFoundError',\n        ConstraintNotSatisfiedError: 'OverconstrainedError',\n        TrackStartError: 'NotReadableError',\n        MediaDeviceFailedDueToShutdown: 'NotAllowedError',\n        MediaDeviceKillSwitchOn: 'NotAllowedError',\n        TabCaptureError: 'AbortError',\n        ScreenCaptureError: 'AbortError',\n        DeviceCaptureError: 'AbortError'\n      }[e.name] || e.name,\n      message: e.message,\n      constraint: e.constraint || e.constraintName,\n      toString() {\n        return this.name + (this.message && ': ') + this.message;\n      }\n    };\n  };\n\n  const getUserMedia_ = function(constraints, onSuccess, onError) {\n    shimConstraints_(constraints, c => {\n      navigator.webkitGetUserMedia(c, onSuccess, e => {\n        if (onError) {\n          onError(shimError_(e));\n        }\n      });\n    });\n  };\n  navigator.getUserMedia = getUserMedia_.bind(navigator);\n\n  // Even though Chrome 45 has navigator.mediaDevices and a getUserMedia\n  // function which returns a Promise, it does not accept spec-style\n  // constraints.\n  if (navigator.mediaDevices.getUserMedia) {\n    const origGetUserMedia = navigator.mediaDevices.getUserMedia.\n      bind(navigator.mediaDevices);\n    navigator.mediaDevices.getUserMedia = function(cs) {\n      return shimConstraints_(cs, c => origGetUserMedia(c).then(stream => {\n        if (c.audio && !stream.getAudioTracks().length ||\n            c.video && !stream.getVideoTracks().length) {\n          stream.getTracks().forEach(track => {\n            track.stop();\n          });\n          throw new DOMException('', 'NotFoundError');\n        }\n        return stream;\n      }, e => Promise.reject(shimError_(e))));\n    };\n  }\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\n\nimport * as utils from '../utils';\nexport {shimGetUserMedia} from './getusermedia';\nexport {shimGetDisplayMedia} from './getdisplaymedia';\n\nexport function shimOnTrack(window) {\n  if (typeof window === 'object' && window.RTCTrackEvent &&\n      ('receiver' in window.RTCTrackEvent.prototype) &&\n      !('transceiver' in window.RTCTrackEvent.prototype)) {\n    Object.defineProperty(window.RTCTrackEvent.prototype, 'transceiver', {\n      get() {\n        return {receiver: this.receiver};\n      }\n    });\n  }\n}\n\nexport function shimPeerConnection(window, browserDetails) {\n  if (typeof window !== 'object' ||\n      !(window.RTCPeerConnection || window.mozRTCPeerConnection)) {\n    return; // probably media.peerconnection.enabled=false in about:config\n  }\n  if (!window.RTCPeerConnection && window.mozRTCPeerConnection) {\n    // very basic support for old versions.\n    window.RTCPeerConnection = window.mozRTCPeerConnection;\n  }\n\n  if (browserDetails.version < 53) {\n    // shim away need for obsolete RTCIceCandidate/RTCSessionDescription.\n    ['setLocalDescription', 'setRemoteDescription', 'addIceCandidate']\n      .forEach(function(method) {\n        const nativeMethod = window.RTCPeerConnection.prototype[method];\n        const methodObj = {[method]() {\n          arguments[0] = new ((method === 'addIceCandidate') ?\n            window.RTCIceCandidate :\n            window.RTCSessionDescription)(arguments[0]);\n          return nativeMethod.apply(this, arguments);\n        }};\n        window.RTCPeerConnection.prototype[method] = methodObj[method];\n      });\n  }\n\n  const modernStatsTypes = {\n    inboundrtp: 'inbound-rtp',\n    outboundrtp: 'outbound-rtp',\n    candidatepair: 'candidate-pair',\n    localcandidate: 'local-candidate',\n    remotecandidate: 'remote-candidate'\n  };\n\n  const nativeGetStats = window.RTCPeerConnection.prototype.getStats;\n  window.RTCPeerConnection.prototype.getStats = function getStats() {\n    const [selector, onSucc, onErr] = arguments;\n    return nativeGetStats.apply(this, [selector || null])\n      .then(stats => {\n        if (browserDetails.version < 53 && !onSucc) {\n          // Shim only promise getStats with spec-hyphens in type names\n          // Leave callback version alone; misc old uses of forEach before Map\n          try {\n            stats.forEach(stat => {\n              stat.type = modernStatsTypes[stat.type] || stat.type;\n            });\n          } catch (e) {\n            if (e.name !== 'TypeError') {\n              throw e;\n            }\n            // Avoid TypeError: \"type\" is read-only, in old versions. 34-43ish\n            stats.forEach((stat, i) => {\n              stats.set(i, Object.assign({}, stat, {\n                type: modernStatsTypes[stat.type] || stat.type\n              }));\n            });\n          }\n        }\n        return stats;\n      })\n      .then(onSucc, onErr);\n  };\n}\n\nexport function shimSenderGetStats(window) {\n  if (!(typeof window === 'object' && window.RTCPeerConnection &&\n      window.RTCRtpSender)) {\n    return;\n  }\n  if (window.RTCRtpSender && 'getStats' in window.RTCRtpSender.prototype) {\n    return;\n  }\n  const origGetSenders = window.RTCPeerConnection.prototype.getSenders;\n  if (origGetSenders) {\n    window.RTCPeerConnection.prototype.getSenders = function getSenders() {\n      const senders = origGetSenders.apply(this, []);\n      senders.forEach(sender => sender._pc = this);\n      return senders;\n    };\n  }\n\n  const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\n  if (origAddTrack) {\n    window.RTCPeerConnection.prototype.addTrack = function addTrack() {\n      const sender = origAddTrack.apply(this, arguments);\n      sender._pc = this;\n      return sender;\n    };\n  }\n  window.RTCRtpSender.prototype.getStats = function getStats() {\n    return this.track ? this._pc.getStats(this.track) :\n      Promise.resolve(new Map());\n  };\n}\n\nexport function shimReceiverGetStats(window) {\n  if (!(typeof window === 'object' && window.RTCPeerConnection &&\n      window.RTCRtpSender)) {\n    return;\n  }\n  if (window.RTCRtpSender && 'getStats' in window.RTCRtpReceiver.prototype) {\n    return;\n  }\n  const origGetReceivers = window.RTCPeerConnection.prototype.getReceivers;\n  if (origGetReceivers) {\n    window.RTCPeerConnection.prototype.getReceivers = function getReceivers() {\n      const receivers = origGetReceivers.apply(this, []);\n      receivers.forEach(receiver => receiver._pc = this);\n      return receivers;\n    };\n  }\n  utils.wrapPeerConnectionEvent(window, 'track', e => {\n    e.receiver._pc = e.srcElement;\n    return e;\n  });\n  window.RTCRtpReceiver.prototype.getStats = function getStats() {\n    return this._pc.getStats(this.track);\n  };\n}\n\nexport function shimRemoveStream(window) {\n  if (!window.RTCPeerConnection ||\n      'removeStream' in window.RTCPeerConnection.prototype) {\n    return;\n  }\n  window.RTCPeerConnection.prototype.removeStream =\n    function removeStream(stream) {\n      utils.deprecated('removeStream', 'removeTrack');\n      this.getSenders().forEach(sender => {\n        if (sender.track && stream.getTracks().includes(sender.track)) {\n          this.removeTrack(sender);\n        }\n      });\n    };\n}\n\nexport function shimRTCDataChannel(window) {\n  // rename DataChannel to RTCDataChannel (native fix in FF60):\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1173851\n  if (window.DataChannel && !window.RTCDataChannel) {\n    window.RTCDataChannel = window.DataChannel;\n  }\n}\n\nexport function shimAddTransceiver(window) {\n  // https://github.com/webrtcHacks/adapter/issues/998#issuecomment-516921647\n  // Firefox ignores the init sendEncodings options passed to addTransceiver\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1396918\n  if (!(typeof window === 'object' && window.RTCPeerConnection)) {\n    return;\n  }\n  const origAddTransceiver = window.RTCPeerConnection.prototype.addTransceiver;\n  if (origAddTransceiver) {\n    window.RTCPeerConnection.prototype.addTransceiver =\n      function addTransceiver() {\n        this.setParametersPromises = [];\n        // WebIDL input coercion and validation\n        let sendEncodings = arguments[1] && arguments[1].sendEncodings;\n        if (sendEncodings === undefined) {\n          sendEncodings = [];\n        }\n        sendEncodings = [...sendEncodings];\n        const shouldPerformCheck = sendEncodings.length > 0;\n        if (shouldPerformCheck) {\n          // If sendEncodings params are provided, validate grammar\n          sendEncodings.forEach((encodingParam) => {\n            if ('rid' in encodingParam) {\n              const ridRegex = /^[a-z0-9]{0,16}$/i;\n              if (!ridRegex.test(encodingParam.rid)) {\n                throw new TypeError('Invalid RID value provided.');\n              }\n            }\n            if ('scaleResolutionDownBy' in encodingParam) {\n              if (!(parseFloat(encodingParam.scaleResolutionDownBy) >= 1.0)) {\n                throw new RangeError('scale_resolution_down_by must be >= 1.0');\n              }\n            }\n            if ('maxFramerate' in encodingParam) {\n              if (!(parseFloat(encodingParam.maxFramerate) >= 0)) {\n                throw new RangeError('max_framerate must be >= 0.0');\n              }\n            }\n          });\n        }\n        const transceiver = origAddTransceiver.apply(this, arguments);\n        if (shouldPerformCheck) {\n          // Check if the init options were applied. If not we do this in an\n          // asynchronous way and save the promise reference in a global object.\n          // This is an ugly hack, but at the same time is way more robust than\n          // checking the sender parameters before and after the createOffer\n          // Also note that after the createoffer we are not 100% sure that\n          // the params were asynchronously applied so we might miss the\n          // opportunity to recreate offer.\n          const {sender} = transceiver;\n          const params = sender.getParameters();\n          if (!('encodings' in params) ||\n              // Avoid being fooled by patched getParameters() below.\n              (params.encodings.length === 1 &&\n               Object.keys(params.encodings[0]).length === 0)) {\n            params.encodings = sendEncodings;\n            sender.sendEncodings = sendEncodings;\n            this.setParametersPromises.push(sender.setParameters(params)\n              .then(() => {\n                delete sender.sendEncodings;\n              }).catch(() => {\n                delete sender.sendEncodings;\n              })\n            );\n          }\n        }\n        return transceiver;\n      };\n  }\n}\n\nexport function shimGetParameters(window) {\n  if (!(typeof window === 'object' && window.RTCRtpSender)) {\n    return;\n  }\n  const origGetParameters = window.RTCRtpSender.prototype.getParameters;\n  if (origGetParameters) {\n    window.RTCRtpSender.prototype.getParameters =\n      function getParameters() {\n        const params = origGetParameters.apply(this, arguments);\n        if (!('encodings' in params)) {\n          params.encodings = [].concat(this.sendEncodings || [{}]);\n        }\n        return params;\n      };\n  }\n}\n\nexport function shimCreateOffer(window) {\n  // https://github.com/webrtcHacks/adapter/issues/998#issuecomment-516921647\n  // Firefox ignores the init sendEncodings options passed to addTransceiver\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1396918\n  if (!(typeof window === 'object' && window.RTCPeerConnection)) {\n    return;\n  }\n  const origCreateOffer = window.RTCPeerConnection.prototype.createOffer;\n  window.RTCPeerConnection.prototype.createOffer = function createOffer() {\n    if (this.setParametersPromises && this.setParametersPromises.length) {\n      return Promise.all(this.setParametersPromises)\n        .then(() => {\n          return origCreateOffer.apply(this, arguments);\n        })\n        .finally(() => {\n          this.setParametersPromises = [];\n        });\n    }\n    return origCreateOffer.apply(this, arguments);\n  };\n}\n\nexport function shimCreateAnswer(window) {\n  // https://github.com/webrtcHacks/adapter/issues/998#issuecomment-516921647\n  // Firefox ignores the init sendEncodings options passed to addTransceiver\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1396918\n  if (!(typeof window === 'object' && window.RTCPeerConnection)) {\n    return;\n  }\n  const origCreateAnswer = window.RTCPeerConnection.prototype.createAnswer;\n  window.RTCPeerConnection.prototype.createAnswer = function createAnswer() {\n    if (this.setParametersPromises && this.setParametersPromises.length) {\n      return Promise.all(this.setParametersPromises)\n        .then(() => {\n          return origCreateAnswer.apply(this, arguments);\n        })\n        .finally(() => {\n          this.setParametersPromises = [];\n        });\n    }\n    return origCreateAnswer.apply(this, arguments);\n  };\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\n\nimport * as utils from '../utils';\n\nexport function shimGetUserMedia(window, browserDetails) {\n  const navigator = window && window.navigator;\n  const MediaStreamTrack = window && window.MediaStreamTrack;\n\n  navigator.getUserMedia = function(constraints, onSuccess, onError) {\n    // Replace Firefox 44+'s deprecation warning with unprefixed version.\n    utils.deprecated('navigator.getUserMedia',\n      'navigator.mediaDevices.getUserMedia');\n    navigator.mediaDevices.getUserMedia(constraints).then(onSuccess, onError);\n  };\n\n  if (!(browserDetails.version > 55 &&\n      'autoGainControl' in navigator.mediaDevices.getSupportedConstraints())) {\n    const remap = function(obj, a, b) {\n      if (a in obj && !(b in obj)) {\n        obj[b] = obj[a];\n        delete obj[a];\n      }\n    };\n\n    const nativeGetUserMedia = navigator.mediaDevices.getUserMedia.\n      bind(navigator.mediaDevices);\n    navigator.mediaDevices.getUserMedia = function(c) {\n      if (typeof c === 'object' && typeof c.audio === 'object') {\n        c = JSON.parse(JSON.stringify(c));\n        remap(c.audio, 'autoGainControl', 'mozAutoGainControl');\n        remap(c.audio, 'noiseSuppression', 'mozNoiseSuppression');\n      }\n      return nativeGetUserMedia(c);\n    };\n\n    if (MediaStreamTrack && MediaStreamTrack.prototype.getSettings) {\n      const nativeGetSettings = MediaStreamTrack.prototype.getSettings;\n      MediaStreamTrack.prototype.getSettings = function() {\n        const obj = nativeGetSettings.apply(this, arguments);\n        remap(obj, 'mozAutoGainControl', 'autoGainControl');\n        remap(obj, 'mozNoiseSuppression', 'noiseSuppression');\n        return obj;\n      };\n    }\n\n    if (MediaStreamTrack && MediaStreamTrack.prototype.applyConstraints) {\n      const nativeApplyConstraints =\n        MediaStreamTrack.prototype.applyConstraints;\n      MediaStreamTrack.prototype.applyConstraints = function(c) {\n        if (this.kind === 'audio' && typeof c === 'object') {\n          c = JSON.parse(JSON.stringify(c));\n          remap(c, 'autoGainControl', 'mozAutoGainControl');\n          remap(c, 'noiseSuppression', 'mozNoiseSuppression');\n        }\n        return nativeApplyConstraints.apply(this, [c]);\n      };\n    }\n  }\n}\n", "/*\n *  Copyright (c) 2018 The adapter.js project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\n\nexport function shimGetDisplayMedia(window, preferredMediaSource) {\n  if (window.navigator.mediaDevices &&\n    'getDisplayMedia' in window.navigator.mediaDevices) {\n    return;\n  }\n  if (!(window.navigator.mediaDevices)) {\n    return;\n  }\n  window.navigator.mediaDevices.getDisplayMedia =\n    function getDisplayMedia(constraints) {\n      if (!(constraints && constraints.video)) {\n        const err = new DOMException('getDisplayMedia without video ' +\n            'constraints is undefined');\n        err.name = 'NotFoundError';\n        // from https://heycam.github.io/webidl/#idl-DOMException-error-names\n        err.code = 8;\n        return Promise.reject(err);\n      }\n      if (constraints.video === true) {\n        constraints.video = {mediaSource: preferredMediaSource};\n      } else {\n        constraints.video.mediaSource = preferredMediaSource;\n      }\n      return window.navigator.mediaDevices.getUserMedia(constraints);\n    };\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n'use strict';\nimport * as utils from '../utils';\n\nexport function shimLocalStreamsAPI(window) {\n  if (typeof window !== 'object' || !window.RTCPeerConnection) {\n    return;\n  }\n  if (!('getLocalStreams' in window.RTCPeerConnection.prototype)) {\n    window.RTCPeerConnection.prototype.getLocalStreams =\n      function getLocalStreams() {\n        if (!this._localStreams) {\n          this._localStreams = [];\n        }\n        return this._localStreams;\n      };\n  }\n  if (!('addStream' in window.RTCPeerConnection.prototype)) {\n    const _addTrack = window.RTCPeerConnection.prototype.addTrack;\n    window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\n      if (!this._localStreams) {\n        this._localStreams = [];\n      }\n      if (!this._localStreams.includes(stream)) {\n        this._localStreams.push(stream);\n      }\n      // Try to emulate Chrome's behaviour of adding in audio-video order.\n      // Safari orders by track id.\n      stream.getAudioTracks().forEach(track => _addTrack.call(this, track,\n        stream));\n      stream.getVideoTracks().forEach(track => _addTrack.call(this, track,\n        stream));\n    };\n\n    window.RTCPeerConnection.prototype.addTrack =\n      function addTrack(track, ...streams) {\n        if (streams) {\n          streams.forEach((stream) => {\n            if (!this._localStreams) {\n              this._localStreams = [stream];\n            } else if (!this._localStreams.includes(stream)) {\n              this._localStreams.push(stream);\n            }\n          });\n        }\n        return _addTrack.apply(this, arguments);\n      };\n  }\n  if (!('removeStream' in window.RTCPeerConnection.prototype)) {\n    window.RTCPeerConnection.prototype.removeStream =\n      function removeStream(stream) {\n        if (!this._localStreams) {\n          this._localStreams = [];\n        }\n        const index = this._localStreams.indexOf(stream);\n        if (index === -1) {\n          return;\n        }\n        this._localStreams.splice(index, 1);\n        const tracks = stream.getTracks();\n        this.getSenders().forEach(sender => {\n          if (tracks.includes(sender.track)) {\n            this.removeTrack(sender);\n          }\n        });\n      };\n  }\n}\n\nexport function shimRemoteStreamsAPI(window) {\n  if (typeof window !== 'object' || !window.RTCPeerConnection) {\n    return;\n  }\n  if (!('getRemoteStreams' in window.RTCPeerConnection.prototype)) {\n    window.RTCPeerConnection.prototype.getRemoteStreams =\n      function getRemoteStreams() {\n        return this._remoteStreams ? this._remoteStreams : [];\n      };\n  }\n  if (!('onaddstream' in window.RTCPeerConnection.prototype)) {\n    Object.defineProperty(window.RTCPeerConnection.prototype, 'onaddstream', {\n      get() {\n        return this._onaddstream;\n      },\n      set(f) {\n        if (this._onaddstream) {\n          this.removeEventListener('addstream', this._onaddstream);\n          this.removeEventListener('track', this._onaddstreampoly);\n        }\n        this.addEventListener('addstream', this._onaddstream = f);\n        this.addEventListener('track', this._onaddstreampoly = (e) => {\n          e.streams.forEach(stream => {\n            if (!this._remoteStreams) {\n              this._remoteStreams = [];\n            }\n            if (this._remoteStreams.includes(stream)) {\n              return;\n            }\n            this._remoteStreams.push(stream);\n            const event = new Event('addstream');\n            event.stream = stream;\n            this.dispatchEvent(event);\n          });\n        });\n      }\n    });\n    const origSetRemoteDescription =\n      window.RTCPeerConnection.prototype.setRemoteDescription;\n    window.RTCPeerConnection.prototype.setRemoteDescription =\n      function setRemoteDescription() {\n        const pc = this;\n        if (!this._onaddstreampoly) {\n          this.addEventListener('track', this._onaddstreampoly = function(e) {\n            e.streams.forEach(stream => {\n              if (!pc._remoteStreams) {\n                pc._remoteStreams = [];\n              }\n              if (pc._remoteStreams.indexOf(stream) >= 0) {\n                return;\n              }\n              pc._remoteStreams.push(stream);\n              const event = new Event('addstream');\n              event.stream = stream;\n              pc.dispatchEvent(event);\n            });\n          });\n        }\n        return origSetRemoteDescription.apply(pc, arguments);\n      };\n  }\n}\n\nexport function shimCallbacksAPI(window) {\n  if (typeof window !== 'object' || !window.RTCPeerConnection) {\n    return;\n  }\n  const prototype = window.RTCPeerConnection.prototype;\n  const origCreateOffer = prototype.createOffer;\n  const origCreateAnswer = prototype.createAnswer;\n  const setLocalDescription = prototype.setLocalDescription;\n  const setRemoteDescription = prototype.setRemoteDescription;\n  const addIceCandidate = prototype.addIceCandidate;\n\n  prototype.createOffer =\n    function createOffer(successCallback, failureCallback) {\n      const options = (arguments.length >= 2) ? arguments[2] : arguments[0];\n      const promise = origCreateOffer.apply(this, [options]);\n      if (!failureCallback) {\n        return promise;\n      }\n      promise.then(successCallback, failureCallback);\n      return Promise.resolve();\n    };\n\n  prototype.createAnswer =\n    function createAnswer(successCallback, failureCallback) {\n      const options = (arguments.length >= 2) ? arguments[2] : arguments[0];\n      const promise = origCreateAnswer.apply(this, [options]);\n      if (!failureCallback) {\n        return promise;\n      }\n      promise.then(successCallback, failureCallback);\n      return Promise.resolve();\n    };\n\n  let withCallback = function(description, successCallback, failureCallback) {\n    const promise = setLocalDescription.apply(this, [description]);\n    if (!failureCallback) {\n      return promise;\n    }\n    promise.then(successCallback, failureCallback);\n    return Promise.resolve();\n  };\n  prototype.setLocalDescription = withCallback;\n\n  withCallback = function(description, successCallback, failureCallback) {\n    const promise = setRemoteDescription.apply(this, [description]);\n    if (!failureCallback) {\n      return promise;\n    }\n    promise.then(successCallback, failureCallback);\n    return Promise.resolve();\n  };\n  prototype.setRemoteDescription = withCallback;\n\n  withCallback = function(candidate, successCallback, failureCallback) {\n    const promise = addIceCandidate.apply(this, [candidate]);\n    if (!failureCallback) {\n      return promise;\n    }\n    promise.then(successCallback, failureCallback);\n    return Promise.resolve();\n  };\n  prototype.addIceCandidate = withCallback;\n}\n\nexport function shimGetUserMedia(window) {\n  const navigator = window && window.navigator;\n\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    // shim not needed in Safari 12.1\n    const mediaDevices = navigator.mediaDevices;\n    const _getUserMedia = mediaDevices.getUserMedia.bind(mediaDevices);\n    navigator.mediaDevices.getUserMedia = (constraints) => {\n      return _getUserMedia(shimConstraints(constraints));\n    };\n  }\n\n  if (!navigator.getUserMedia && navigator.mediaDevices &&\n    navigator.mediaDevices.getUserMedia) {\n    navigator.getUserMedia = function getUserMedia(constraints, cb, errcb) {\n      navigator.mediaDevices.getUserMedia(constraints)\n        .then(cb, errcb);\n    }.bind(navigator);\n  }\n}\n\nexport function shimConstraints(constraints) {\n  if (constraints && constraints.video !== undefined) {\n    return Object.assign({},\n      constraints,\n      {video: utils.compactObject(constraints.video)}\n    );\n  }\n\n  return constraints;\n}\n\nexport function shimRTCIceServerUrls(window) {\n  if (!window.RTCPeerConnection) {\n    return;\n  }\n  // migrate from non-spec RTCIceServer.url to RTCIceServer.urls\n  const OrigPeerConnection = window.RTCPeerConnection;\n  window.RTCPeerConnection =\n    function RTCPeerConnection(pcConfig, pcConstraints) {\n      if (pcConfig && pcConfig.iceServers) {\n        const newIceServers = [];\n        for (let i = 0; i < pcConfig.iceServers.length; i++) {\n          let server = pcConfig.iceServers[i];\n          if (server.urls === undefined && server.url) {\n            utils.deprecated('RTCIceServer.url', 'RTCIceServer.urls');\n            server = JSON.parse(JSON.stringify(server));\n            server.urls = server.url;\n            delete server.url;\n            newIceServers.push(server);\n          } else {\n            newIceServers.push(pcConfig.iceServers[i]);\n          }\n        }\n        pcConfig.iceServers = newIceServers;\n      }\n      return new OrigPeerConnection(pcConfig, pcConstraints);\n    };\n  window.RTCPeerConnection.prototype = OrigPeerConnection.prototype;\n  // wrap static methods. Currently just generateCertificate.\n  if ('generateCertificate' in OrigPeerConnection) {\n    Object.defineProperty(window.RTCPeerConnection, 'generateCertificate', {\n      get() {\n        return OrigPeerConnection.generateCertificate;\n      }\n    });\n  }\n}\n\nexport function shimTrackEventTransceiver(window) {\n  // Add event.transceiver member over deprecated event.receiver\n  if (typeof window === 'object' && window.RTCTrackEvent &&\n      'receiver' in window.RTCTrackEvent.prototype &&\n      !('transceiver' in window.RTCTrackEvent.prototype)) {\n    Object.defineProperty(window.RTCTrackEvent.prototype, 'transceiver', {\n      get() {\n        return {receiver: this.receiver};\n      }\n    });\n  }\n}\n\nexport function shimCreateOfferLegacy(window) {\n  const origCreateOffer = window.RTCPeerConnection.prototype.createOffer;\n  window.RTCPeerConnection.prototype.createOffer =\n    function createOffer(offerOptions) {\n      if (offerOptions) {\n        if (typeof offerOptions.offerToReceiveAudio !== 'undefined') {\n          // support bit values\n          offerOptions.offerToReceiveAudio =\n            !!offerOptions.offerToReceiveAudio;\n        }\n        const audioTransceiver = this.getTransceivers().find(transceiver =>\n          transceiver.receiver.track.kind === 'audio');\n        if (offerOptions.offerToReceiveAudio === false && audioTransceiver) {\n          if (audioTransceiver.direction === 'sendrecv') {\n            if (audioTransceiver.setDirection) {\n              audioTransceiver.setDirection('sendonly');\n            } else {\n              audioTransceiver.direction = 'sendonly';\n            }\n          } else if (audioTransceiver.direction === 'recvonly') {\n            if (audioTransceiver.setDirection) {\n              audioTransceiver.setDirection('inactive');\n            } else {\n              audioTransceiver.direction = 'inactive';\n            }\n          }\n        } else if (offerOptions.offerToReceiveAudio === true &&\n            !audioTransceiver) {\n          this.addTransceiver('audio', {direction: 'recvonly'});\n        }\n\n        if (typeof offerOptions.offerToReceiveVideo !== 'undefined') {\n          // support bit values\n          offerOptions.offerToReceiveVideo =\n            !!offerOptions.offerToReceiveVideo;\n        }\n        const videoTransceiver = this.getTransceivers().find(transceiver =>\n          transceiver.receiver.track.kind === 'video');\n        if (offerOptions.offerToReceiveVideo === false && videoTransceiver) {\n          if (videoTransceiver.direction === 'sendrecv') {\n            if (videoTransceiver.setDirection) {\n              videoTransceiver.setDirection('sendonly');\n            } else {\n              videoTransceiver.direction = 'sendonly';\n            }\n          } else if (videoTransceiver.direction === 'recvonly') {\n            if (videoTransceiver.setDirection) {\n              videoTransceiver.setDirection('inactive');\n            } else {\n              videoTransceiver.direction = 'inactive';\n            }\n          }\n        } else if (offerOptions.offerToReceiveVideo === true &&\n            !videoTransceiver) {\n          this.addTransceiver('video', {direction: 'recvonly'});\n        }\n      }\n      return origCreateOffer.apply(this, arguments);\n    };\n}\n\nexport function shimAudioContext(window) {\n  if (typeof window !== 'object' || window.AudioContext) {\n    return;\n  }\n  window.AudioContext = window.webkitAudioContext;\n}\n\n", "/*\n *  Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n'use strict';\n\nimport SDPUtils from 'sdp';\nimport * as utils from './utils';\n\nexport function shimRTCIceCandidate(window) {\n  // foundation is arbitrarily chosen as an indicator for full support for\n  // https://w3c.github.io/webrtc-pc/#rtcicecandidate-interface\n  if (!window.RTCIceCandidate || (window.RTCIceCandidate && 'foundation' in\n      window.RTCIceCandidate.prototype)) {\n    return;\n  }\n\n  const NativeRTCIceCandidate = window.RTCIceCandidate;\n  window.RTCIceCandidate = function RTCIceCandidate(args) {\n    // Remove the a= which shouldn't be part of the candidate string.\n    if (typeof args === 'object' && args.candidate &&\n        args.candidate.indexOf('a=') === 0) {\n      args = JSON.parse(JSON.stringify(args));\n      args.candidate = args.candidate.substring(2);\n    }\n\n    if (args.candidate && args.candidate.length) {\n      // Augment the native candidate with the parsed fields.\n      const nativeCandidate = new NativeRTCIceCandidate(args);\n      const parsedCandidate = SDPUtils.parseCandidate(args.candidate);\n      for (const key in parsedCandidate) {\n        if (!(key in nativeCandidate)) {\n          Object.defineProperty(nativeCandidate, key,\n            {value: parsedCandidate[key]});\n        }\n      }\n\n      // Override serializer to not serialize the extra attributes.\n      nativeCandidate.toJSON = function toJSON() {\n        return {\n          candidate: nativeCandidate.candidate,\n          sdpMid: nativeCandidate.sdpMid,\n          sdpMLineIndex: nativeCandidate.sdpMLineIndex,\n          usernameFragment: nativeCandidate.usernameFragment,\n        };\n      };\n      return nativeCandidate;\n    }\n    return new NativeRTCIceCandidate(args);\n  };\n  window.RTCIceCandidate.prototype = NativeRTCIceCandidate.prototype;\n\n  // Hook up the augmented candidate in onicecandidate and\n  // addEventListener('icecandidate', ...)\n  utils.wrapPeerConnectionEvent(window, 'icecandidate', e => {\n    if (e.candidate) {\n      Object.defineProperty(e, 'candidate', {\n        value: new window.RTCIceCandidate(e.candidate),\n        writable: 'false'\n      });\n    }\n    return e;\n  });\n}\n\nexport function shimRTCIceCandidateRelayProtocol(window) {\n  if (!window.RTCIceCandidate || (window.RTCIceCandidate && 'relayProtocol' in\n      window.RTCIceCandidate.prototype)) {\n    return;\n  }\n\n  // Hook up the augmented candidate in onicecandidate and\n  // addEventListener('icecandidate', ...)\n  utils.wrapPeerConnectionEvent(window, 'icecandidate', e => {\n    if (e.candidate) {\n      const parsedCandidate = SDPUtils.parseCandidate(e.candidate.candidate);\n      if (parsedCandidate.type === 'relay') {\n        // This is a libwebrtc-specific mapping of local type preference\n        // to relayProtocol.\n        e.candidate.relayProtocol = {\n          0: 'tls',\n          1: 'tcp',\n          2: 'udp',\n        }[parsedCandidate.priority >> 24];\n      }\n    }\n    return e;\n  });\n}\n\nexport function shimMaxMessageSize(window, browserDetails) {\n  if (!window.RTCPeerConnection) {\n    return;\n  }\n\n  if (!('sctp' in window.RTCPeerConnection.prototype)) {\n    Object.defineProperty(window.RTCPeerConnection.prototype, 'sctp', {\n      get() {\n        return typeof this._sctp === 'undefined' ? null : this._sctp;\n      }\n    });\n  }\n\n  const sctpInDescription = function(description) {\n    if (!description || !description.sdp) {\n      return false;\n    }\n    const sections = SDPUtils.splitSections(description.sdp);\n    sections.shift();\n    return sections.some(mediaSection => {\n      const mLine = SDPUtils.parseMLine(mediaSection);\n      return mLine && mLine.kind === 'application'\n          && mLine.protocol.indexOf('SCTP') !== -1;\n    });\n  };\n\n  const getRemoteFirefoxVersion = function(description) {\n    // TODO: Is there a better solution for detecting Firefox?\n    const match = description.sdp.match(/mozilla...THIS_IS_SDPARTA-(\\d+)/);\n    if (match === null || match.length < 2) {\n      return -1;\n    }\n    const version = parseInt(match[1], 10);\n    // Test for NaN (yes, this is ugly)\n    return version !== version ? -1 : version;\n  };\n\n  const getCanSendMaxMessageSize = function(remoteIsFirefox) {\n    // Every implementation we know can send at least 64 KiB.\n    // Note: Although Chrome is technically able to send up to 256 KiB, the\n    //       data does not reach the other peer reliably.\n    //       See: https://bugs.chromium.org/p/webrtc/issues/detail?id=8419\n    let canSendMaxMessageSize = 65536;\n    if (browserDetails.browser === 'firefox') {\n      if (browserDetails.version < 57) {\n        if (remoteIsFirefox === -1) {\n          // FF < 57 will send in 16 KiB chunks using the deprecated PPID\n          // fragmentation.\n          canSendMaxMessageSize = 16384;\n        } else {\n          // However, other FF (and RAWRTC) can reassemble PPID-fragmented\n          // messages. Thus, supporting ~2 GiB when sending.\n          canSendMaxMessageSize = 2147483637;\n        }\n      } else if (browserDetails.version < 60) {\n        // Currently, all FF >= 57 will reset the remote maximum message size\n        // to the default value when a data channel is created at a later\n        // stage. :(\n        // See: https://bugzilla.mozilla.org/show_bug.cgi?id=1426831\n        canSendMaxMessageSize =\n          browserDetails.version === 57 ? 65535 : 65536;\n      } else {\n        // FF >= 60 supports sending ~2 GiB\n        canSendMaxMessageSize = 2147483637;\n      }\n    }\n    return canSendMaxMessageSize;\n  };\n\n  const getMaxMessageSize = function(description, remoteIsFirefox) {\n    // Note: 65536 bytes is the default value from the SDP spec. Also,\n    //       every implementation we know supports receiving 65536 bytes.\n    let maxMessageSize = 65536;\n\n    // FF 57 has a slightly incorrect default remote max message size, so\n    // we need to adjust it here to avoid a failure when sending.\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=1425697\n    if (browserDetails.browser === 'firefox'\n         && browserDetails.version === 57) {\n      maxMessageSize = 65535;\n    }\n\n    const match = SDPUtils.matchPrefix(description.sdp,\n      'a=max-message-size:');\n    if (match.length > 0) {\n      maxMessageSize = parseInt(match[0].substring(19), 10);\n    } else if (browserDetails.browser === 'firefox' &&\n                remoteIsFirefox !== -1) {\n      // If the maximum message size is not present in the remote SDP and\n      // both local and remote are Firefox, the remote peer can receive\n      // ~2 GiB.\n      maxMessageSize = 2147483637;\n    }\n    return maxMessageSize;\n  };\n\n  const origSetRemoteDescription =\n      window.RTCPeerConnection.prototype.setRemoteDescription;\n  window.RTCPeerConnection.prototype.setRemoteDescription =\n    function setRemoteDescription() {\n      this._sctp = null;\n      // Chrome decided to not expose .sctp in plan-b mode.\n      // As usual, adapter.js has to do an 'ugly worakaround'\n      // to cover up the mess.\n      if (browserDetails.browser === 'chrome' && browserDetails.version >= 76) {\n        const {sdpSemantics} = this.getConfiguration();\n        if (sdpSemantics === 'plan-b') {\n          Object.defineProperty(this, 'sctp', {\n            get() {\n              return typeof this._sctp === 'undefined' ? null : this._sctp;\n            },\n            enumerable: true,\n            configurable: true,\n          });\n        }\n      }\n\n      if (sctpInDescription(arguments[0])) {\n        // Check if the remote is FF.\n        const isFirefox = getRemoteFirefoxVersion(arguments[0]);\n\n        // Get the maximum message size the local peer is capable of sending\n        const canSendMMS = getCanSendMaxMessageSize(isFirefox);\n\n        // Get the maximum message size of the remote peer.\n        const remoteMMS = getMaxMessageSize(arguments[0], isFirefox);\n\n        // Determine final maximum message size\n        let maxMessageSize;\n        if (canSendMMS === 0 && remoteMMS === 0) {\n          maxMessageSize = Number.POSITIVE_INFINITY;\n        } else if (canSendMMS === 0 || remoteMMS === 0) {\n          maxMessageSize = Math.max(canSendMMS, remoteMMS);\n        } else {\n          maxMessageSize = Math.min(canSendMMS, remoteMMS);\n        }\n\n        // Create a dummy RTCSctpTransport object and the 'maxMessageSize'\n        // attribute.\n        const sctp = {};\n        Object.defineProperty(sctp, 'maxMessageSize', {\n          get() {\n            return maxMessageSize;\n          }\n        });\n        this._sctp = sctp;\n      }\n\n      return origSetRemoteDescription.apply(this, arguments);\n    };\n}\n\nexport function shimSendThrowTypeError(window) {\n  if (!(window.RTCPeerConnection &&\n      'createDataChannel' in window.RTCPeerConnection.prototype)) {\n    return;\n  }\n\n  // Note: Although Firefox >= 57 has a native implementation, the maximum\n  //       message size can be reset for all data channels at a later stage.\n  //       See: https://bugzilla.mozilla.org/show_bug.cgi?id=1426831\n\n  function wrapDcSend(dc, pc) {\n    const origDataChannelSend = dc.send;\n    dc.send = function send() {\n      const data = arguments[0];\n      const length = data.length || data.size || data.byteLength;\n      if (dc.readyState === 'open' &&\n          pc.sctp && length > pc.sctp.maxMessageSize) {\n        throw new TypeError('Message too large (can send a maximum of ' +\n          pc.sctp.maxMessageSize + ' bytes)');\n      }\n      return origDataChannelSend.apply(dc, arguments);\n    };\n  }\n  const origCreateDataChannel =\n    window.RTCPeerConnection.prototype.createDataChannel;\n  window.RTCPeerConnection.prototype.createDataChannel =\n    function createDataChannel() {\n      const dataChannel = origCreateDataChannel.apply(this, arguments);\n      wrapDcSend(dataChannel, this);\n      return dataChannel;\n    };\n  utils.wrapPeerConnectionEvent(window, 'datachannel', e => {\n    wrapDcSend(e.channel, e.target);\n    return e;\n  });\n}\n\n\n/* shims RTCConnectionState by pretending it is the same as iceConnectionState.\n * See https://bugs.chromium.org/p/webrtc/issues/detail?id=6145#c12\n * for why this is a valid hack in Chrome. In Firefox it is slightly incorrect\n * since DTLS failures would be hidden. See\n * https://bugzilla.mozilla.org/show_bug.cgi?id=1265827\n * for the Firefox tracking bug.\n */\nexport function shimConnectionState(window) {\n  if (!window.RTCPeerConnection ||\n      'connectionState' in window.RTCPeerConnection.prototype) {\n    return;\n  }\n  const proto = window.RTCPeerConnection.prototype;\n  Object.defineProperty(proto, 'connectionState', {\n    get() {\n      return {\n        completed: 'connected',\n        checking: 'connecting'\n      }[this.iceConnectionState] || this.iceConnectionState;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(proto, 'onconnectionstatechange', {\n    get() {\n      return this._onconnectionstatechange || null;\n    },\n    set(cb) {\n      if (this._onconnectionstatechange) {\n        this.removeEventListener('connectionstatechange',\n          this._onconnectionstatechange);\n        delete this._onconnectionstatechange;\n      }\n      if (cb) {\n        this.addEventListener('connectionstatechange',\n          this._onconnectionstatechange = cb);\n      }\n    },\n    enumerable: true,\n    configurable: true\n  });\n\n  ['setLocalDescription', 'setRemoteDescription'].forEach((method) => {\n    const origMethod = proto[method];\n    proto[method] = function() {\n      if (!this._connectionstatechangepoly) {\n        this._connectionstatechangepoly = e => {\n          const pc = e.target;\n          if (pc._lastConnectionState !== pc.connectionState) {\n            pc._lastConnectionState = pc.connectionState;\n            const newEvent = new Event('connectionstatechange', e);\n            pc.dispatchEvent(newEvent);\n          }\n          return e;\n        };\n        this.addEventListener('iceconnectionstatechange',\n          this._connectionstatechangepoly);\n      }\n      return origMethod.apply(this, arguments);\n    };\n  });\n}\n\nexport function removeExtmapAllowMixed(window, browserDetails) {\n  /* remove a=extmap-allow-mixed for webrtc.org < M71 */\n  if (!window.RTCPeerConnection) {\n    return;\n  }\n  if (browserDetails.browser === 'chrome' && browserDetails.version >= 71) {\n    return;\n  }\n  if (browserDetails.browser === 'safari' &&\n      browserDetails._safariVersion >= 13.1) {\n    return;\n  }\n  const nativeSRD = window.RTCPeerConnection.prototype.setRemoteDescription;\n  window.RTCPeerConnection.prototype.setRemoteDescription =\n  function setRemoteDescription(desc) {\n    if (desc && desc.sdp && desc.sdp.indexOf('\\na=extmap-allow-mixed') !== -1) {\n      const sdp = desc.sdp.split('\\n').filter((line) => {\n        return line.trim() !== 'a=extmap-allow-mixed';\n      }).join('\\n');\n      // Safari enforces read-only-ness of RTCSessionDescription fields.\n      if (window.RTCSessionDescription &&\n          desc instanceof window.RTCSessionDescription) {\n        arguments[0] = new window.RTCSessionDescription({\n          type: desc.type,\n          sdp,\n        });\n      } else {\n        desc.sdp = sdp;\n      }\n    }\n    return nativeSRD.apply(this, arguments);\n  };\n}\n\nexport function shimAddIceCandidateNullOrEmpty(window, browserDetails) {\n  // Support for addIceCandidate(null or undefined)\n  // as well as addIceCandidate({candidate: \"\", ...})\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=978582\n  // Note: must be called before other polyfills which change the signature.\n  if (!(window.RTCPeerConnection && window.RTCPeerConnection.prototype)) {\n    return;\n  }\n  const nativeAddIceCandidate =\n      window.RTCPeerConnection.prototype.addIceCandidate;\n  if (!nativeAddIceCandidate || nativeAddIceCandidate.length === 0) {\n    return;\n  }\n  window.RTCPeerConnection.prototype.addIceCandidate =\n    function addIceCandidate() {\n      if (!arguments[0]) {\n        if (arguments[1]) {\n          arguments[1].apply(null);\n        }\n        return Promise.resolve();\n      }\n      // Firefox 68+ emits and processes {candidate: \"\", ...}, ignore\n      // in older versions.\n      // Native support for ignoring exists for Chrome M77+.\n      // Safari ignores as well, exact version unknown but works in the same\n      // version that also ignores addIceCandidate(null).\n      if (((browserDetails.browser === 'chrome' && browserDetails.version < 78)\n           || (browserDetails.browser === 'firefox'\n               && browserDetails.version < 68)\n           || (browserDetails.browser === 'safari'))\n          && arguments[0] && arguments[0].candidate === '') {\n        return Promise.resolve();\n      }\n      return nativeAddIceCandidate.apply(this, arguments);\n    };\n}\n\n// Note: Make sure to call this ahead of APIs that modify\n// setLocalDescription.length\nexport function shimParameterlessSetLocalDescription(window, browserDetails) {\n  if (!(window.RTCPeerConnection && window.RTCPeerConnection.prototype)) {\n    return;\n  }\n  const nativeSetLocalDescription =\n      window.RTCPeerConnection.prototype.setLocalDescription;\n  if (!nativeSetLocalDescription || nativeSetLocalDescription.length === 0) {\n    return;\n  }\n  window.RTCPeerConnection.prototype.setLocalDescription =\n    function setLocalDescription() {\n      let desc = arguments[0] || {};\n      if (typeof desc !== 'object' || (desc.type && desc.sdp)) {\n        return nativeSetLocalDescription.apply(this, arguments);\n      }\n      // The remaining steps should technically happen when SLD comes off the\n      // RTCPeerConnection's operations chain (not ahead of going on it), but\n      // this is too difficult to shim. Instead, this shim only covers the\n      // common case where the operations chain is empty. This is imperfect, but\n      // should cover many cases. Rationale: Even if we can't reduce the glare\n      // window to zero on imperfect implementations, there's value in tapping\n      // into the perfect negotiation pattern that several browsers support.\n      desc = {type: desc.type, sdp: desc.sdp};\n      if (!desc.type) {\n        switch (this.signalingState) {\n          case 'stable':\n          case 'have-local-offer':\n          case 'have-remote-pranswer':\n            desc.type = 'offer';\n            break;\n          default:\n            desc.type = 'answer';\n            break;\n        }\n      }\n      if (desc.sdp || (desc.type !== 'offer' && desc.type !== 'answer')) {\n        return nativeSetLocalDescription.apply(this, [desc]);\n      }\n      const func = desc.type === 'offer' ? this.createOffer : this.createAnswer;\n      return func.apply(this)\n        .then(d => nativeSetLocalDescription.apply(this, [d]));\n    };\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\nimport * as utils from './utils';\n\n// Browser shims.\nimport * as chromeShim from './chrome/chrome_shim';\nimport * as firefoxShim from './firefox/firefox_shim';\nimport * as safariShim from './safari/safari_shim';\nimport * as commonShim from './common_shim';\nimport * as sdp from 'sdp';\n\n// Shimming starts here.\nexport function adapterFactory({window} = {}, options = {\n  shimChrome: true,\n  shimFirefox: true,\n  shimSafari: true,\n}) {\n  // Utils.\n  const logging = utils.log;\n  const browserDetails = utils.detectBrowser(window);\n\n  const adapter = {\n    browserDetails,\n    commonShim,\n    extractVersion: utils.extractVersion,\n    disableLog: utils.disableLog,\n    disableWarnings: utils.disableWarnings,\n    // Expose sdp as a convenience. For production apps include directly.\n    sdp,\n  };\n\n  // Shim browser if found.\n  switch (browserDetails.browser) {\n    case 'chrome':\n      if (!chromeShim || !chromeShim.shimPeerConnection ||\n          !options.shimChrome) {\n        logging('Chrome shim is not included in this adapter release.');\n        return adapter;\n      }\n      if (browserDetails.version === null) {\n        logging('Chrome shim can not determine version, not shimming.');\n        return adapter;\n      }\n      logging('adapter.js shimming chrome.');\n      // Export to the adapter global object visible in the browser.\n      adapter.browserShim = chromeShim;\n\n      // Must be called before shimPeerConnection.\n      commonShim.shimAddIceCandidateNullOrEmpty(window, browserDetails);\n      commonShim.shimParameterlessSetLocalDescription(window, browserDetails);\n\n      chromeShim.shimGetUserMedia(window, browserDetails);\n      chromeShim.shimMediaStream(window, browserDetails);\n      chromeShim.shimPeerConnection(window, browserDetails);\n      chromeShim.shimOnTrack(window, browserDetails);\n      chromeShim.shimAddTrackRemoveTrack(window, browserDetails);\n      chromeShim.shimGetSendersWithDtmf(window, browserDetails);\n      chromeShim.shimSenderReceiverGetStats(window, browserDetails);\n      chromeShim.fixNegotiationNeeded(window, browserDetails);\n\n      commonShim.shimRTCIceCandidate(window, browserDetails);\n      commonShim.shimRTCIceCandidateRelayProtocol(window, browserDetails);\n      commonShim.shimConnectionState(window, browserDetails);\n      commonShim.shimMaxMessageSize(window, browserDetails);\n      commonShim.shimSendThrowTypeError(window, browserDetails);\n      commonShim.removeExtmapAllowMixed(window, browserDetails);\n      break;\n    case 'firefox':\n      if (!firefoxShim || !firefoxShim.shimPeerConnection ||\n          !options.shimFirefox) {\n        logging('Firefox shim is not included in this adapter release.');\n        return adapter;\n      }\n      logging('adapter.js shimming firefox.');\n      // Export to the adapter global object visible in the browser.\n      adapter.browserShim = firefoxShim;\n\n      // Must be called before shimPeerConnection.\n      commonShim.shimAddIceCandidateNullOrEmpty(window, browserDetails);\n      commonShim.shimParameterlessSetLocalDescription(window, browserDetails);\n\n      firefoxShim.shimGetUserMedia(window, browserDetails);\n      firefoxShim.shimPeerConnection(window, browserDetails);\n      firefoxShim.shimOnTrack(window, browserDetails);\n      firefoxShim.shimRemoveStream(window, browserDetails);\n      firefoxShim.shimSenderGetStats(window, browserDetails);\n      firefoxShim.shimReceiverGetStats(window, browserDetails);\n      firefoxShim.shimRTCDataChannel(window, browserDetails);\n      firefoxShim.shimAddTransceiver(window, browserDetails);\n      firefoxShim.shimGetParameters(window, browserDetails);\n      firefoxShim.shimCreateOffer(window, browserDetails);\n      firefoxShim.shimCreateAnswer(window, browserDetails);\n\n      commonShim.shimRTCIceCandidate(window, browserDetails);\n      commonShim.shimConnectionState(window, browserDetails);\n      commonShim.shimMaxMessageSize(window, browserDetails);\n      commonShim.shimSendThrowTypeError(window, browserDetails);\n      break;\n    case 'safari':\n      if (!safariShim || !options.shimSafari) {\n        logging('Safari shim is not included in this adapter release.');\n        return adapter;\n      }\n      logging('adapter.js shimming safari.');\n      // Export to the adapter global object visible in the browser.\n      adapter.browserShim = safariShim;\n\n      // Must be called before shimCallbackAPI.\n      commonShim.shimAddIceCandidateNullOrEmpty(window, browserDetails);\n      commonShim.shimParameterlessSetLocalDescription(window, browserDetails);\n\n      safariShim.shimRTCIceServerUrls(window, browserDetails);\n      safariShim.shimCreateOfferLegacy(window, browserDetails);\n      safariShim.shimCallbacksAPI(window, browserDetails);\n      safariShim.shimLocalStreamsAPI(window, browserDetails);\n      safariShim.shimRemoteStreamsAPI(window, browserDetails);\n      safariShim.shimTrackEventTransceiver(window, browserDetails);\n      safariShim.shimGetUserMedia(window, browserDetails);\n      safariShim.shimAudioContext(window, browserDetails);\n\n      commonShim.shimRTCIceCandidate(window, browserDetails);\n      commonShim.shimRTCIceCandidateRelayProtocol(window, browserDetails);\n      commonShim.shimMaxMessageSize(window, browserDetails);\n      commonShim.shimSendThrowTypeError(window, browserDetails);\n      commonShim.removeExtmapAllowMixed(window, browserDetails);\n      break;\n    default:\n      logging('Unsupported browser!');\n      break;\n  }\n\n  return adapter;\n}\n", "/*\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\n *\n *  Use of this source code is governed by a BSD-style license\n *  that can be found in the LICENSE file in the root of the source\n *  tree.\n */\n/* eslint-env node */\n\n'use strict';\n\nimport {adapterFactory} from './adapter_factory.js';\n\nconst adapter =\n  adapterFactory({window: typeof window === 'undefined' ? undefined : window});\nexport default adapter;\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAIA,QAAMA,YAAW,CAAC;AAIlB,IAAAA,UAAS,qBAAqB,WAAW;AACvC,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,IACnD;AAGA,IAAAA,UAAS,aAAaA,UAAS,mBAAmB;AAGlD,IAAAA,UAAS,aAAa,SAAS,MAAM;AACnC,aAAO,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AAAA,IACxD;AAEA,IAAAA,UAAS,gBAAgB,SAAS,MAAM;AACtC,YAAM,QAAQ,KAAK,MAAM,MAAM;AAC/B,aAAO,MAAM,IAAI,CAAC,MAAM,WAAW,QAAQ,IACzC,OAAO,OAAO,MAAM,KAAK,IAAI,MAAM;AAAA,IACvC;AAGA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,YAAM,WAAWA,UAAS,cAAc,IAAI;AAC5C,aAAO,YAAY,SAAS,CAAC;AAAA,IAC/B;AAGA,IAAAA,UAAS,mBAAmB,SAAS,MAAM;AACzC,YAAM,WAAWA,UAAS,cAAc,IAAI;AAC5C,eAAS,MAAM;AACf,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,cAAc,SAAS,MAAM,QAAQ;AAC5C,aAAOA,UAAS,WAAW,IAAI,EAAE,OAAO,UAAQ,KAAK,QAAQ,MAAM,MAAM,CAAC;AAAA,IAC5E;AAMA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,UAAI;AAEJ,UAAI,KAAK,QAAQ,cAAc,MAAM,GAAG;AACtC,gBAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAAA,MACtC,OAAO;AACL,gBAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAAA,MACtC;AAEA,YAAM,YAAY;AAAA,QAChB,YAAY,MAAM,CAAC;AAAA,QACnB,WAAW,EAAC,GAAG,OAAO,GAAG,OAAM,EAAE,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AAAA,QACrD,UAAU,MAAM,CAAC,EAAE,YAAY;AAAA,QAC/B,UAAU,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC/B,IAAI,MAAM,CAAC;AAAA,QACX,SAAS,MAAM,CAAC;AAAA;AAAA,QAChB,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA;AAAA,QAE3B,MAAM,MAAM,CAAC;AAAA,MACf;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,gBAAQ,MAAM,CAAC,GAAG;AAAA,UAChB,KAAK;AACH,sBAAU,iBAAiB,MAAM,IAAI,CAAC;AACtC;AAAA,UACF,KAAK;AACH,sBAAU,cAAc,SAAS,MAAM,IAAI,CAAC,GAAG,EAAE;AACjD;AAAA,UACF,KAAK;AACH,sBAAU,UAAU,MAAM,IAAI,CAAC;AAC/B;AAAA,UACF,KAAK;AACH,sBAAU,QAAQ,MAAM,IAAI,CAAC;AAC7B,sBAAU,mBAAmB,MAAM,IAAI,CAAC;AACxC;AAAA,UACF;AACE,gBAAI,UAAU,MAAM,CAAC,CAAC,MAAM,QAAW;AACrC,wBAAU,MAAM,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,YACnC;AACA;AAAA,QACJ;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,iBAAiB,SAAS,WAAW;AAC5C,YAAMC,OAAM,CAAC;AACb,MAAAA,KAAI,KAAK,UAAU,UAAU;AAE7B,YAAM,YAAY,UAAU;AAC5B,UAAI,cAAc,OAAO;AACvB,QAAAA,KAAI,KAAK,CAAC;AAAA,MACZ,WAAW,cAAc,QAAQ;AAC/B,QAAAA,KAAI,KAAK,CAAC;AAAA,MACZ,OAAO;AACL,QAAAA,KAAI,KAAK,SAAS;AAAA,MACpB;AACA,MAAAA,KAAI,KAAK,UAAU,SAAS,YAAY,CAAC;AACzC,MAAAA,KAAI,KAAK,UAAU,QAAQ;AAC3B,MAAAA,KAAI,KAAK,UAAU,WAAW,UAAU,EAAE;AAC1C,MAAAA,KAAI,KAAK,UAAU,IAAI;AAEvB,YAAM,OAAO,UAAU;AACvB,MAAAA,KAAI,KAAK,KAAK;AACd,MAAAA,KAAI,KAAK,IAAI;AACb,UAAI,SAAS,UAAU,UAAU,kBAC7B,UAAU,aAAa;AACzB,QAAAA,KAAI,KAAK,OAAO;AAChB,QAAAA,KAAI,KAAK,UAAU,cAAc;AACjC,QAAAA,KAAI,KAAK,OAAO;AAChB,QAAAA,KAAI,KAAK,UAAU,WAAW;AAAA,MAChC;AACA,UAAI,UAAU,WAAW,UAAU,SAAS,YAAY,MAAM,OAAO;AACnE,QAAAA,KAAI,KAAK,SAAS;AAClB,QAAAA,KAAI,KAAK,UAAU,OAAO;AAAA,MAC5B;AACA,UAAI,UAAU,oBAAoB,UAAU,OAAO;AACjD,QAAAA,KAAI,KAAK,OAAO;AAChB,QAAAA,KAAI,KAAK,UAAU,oBAAoB,UAAU,KAAK;AAAA,MACxD;AACA,aAAO,eAAeA,KAAI,KAAK,GAAG;AAAA,IACpC;AAKA,IAAAD,UAAS,kBAAkB,SAAS,MAAM;AACxC,aAAO,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAAA,IACrC;AAIA,IAAAA,UAAS,cAAc,SAAS,MAAM;AACpC,UAAI,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACvC,YAAM,SAAS;AAAA,QACb,aAAa,SAAS,MAAM,MAAM,GAAG,EAAE;AAAA;AAAA,MACzC;AAEA,cAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAE1B,aAAO,OAAO,MAAM,CAAC;AACrB,aAAO,YAAY,SAAS,MAAM,CAAC,GAAG,EAAE;AACxC,aAAO,WAAW,MAAM,WAAW,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,IAAI;AAEhE,aAAO,cAAc,OAAO;AAC5B,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,cAAc,SAAS,OAAO;AACrC,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,yBAAyB,QAAW;AAC5C,aAAK,MAAM;AAAA,MACb;AACA,YAAM,WAAW,MAAM,YAAY,MAAM,eAAe;AACxD,aAAO,cAAc,KAAK,MAAM,MAAM,OAAO,MAAM,MAAM,aACpD,aAAa,IAAI,MAAM,WAAW,MAAM;AAAA,IAC/C;AAKA,IAAAA,UAAS,cAAc,SAAS,MAAM;AACpC,YAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QACzB,WAAW,MAAM,CAAC,EAAE,QAAQ,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QAChE,KAAK,MAAM,CAAC;AAAA,QACZ,YAAY,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,MACrC;AAAA,IACF;AAIA,IAAAA,UAAS,cAAc,SAAS,iBAAiB;AAC/C,aAAO,eAAe,gBAAgB,MAAM,gBAAgB,gBACvD,gBAAgB,aAAa,gBAAgB,cAAc,aACxD,MAAM,gBAAgB,YACtB,MACJ,MAAM,gBAAgB,OACrB,gBAAgB,aAAa,MAAM,gBAAgB,aAAa,MACjE;AAAA,IACN;AAOA,IAAAA,UAAS,YAAY,SAAS,MAAM;AAClC,YAAM,SAAS,CAAC;AAChB,UAAI;AACJ,YAAM,QAAQ,KAAK,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,GAAG;AAC7D,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAK,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG;AAC9B,eAAO,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,YAAY,SAAS,OAAO;AACnC,UAAI,OAAO;AACX,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,yBAAyB,QAAW;AAC5C,aAAK,MAAM;AAAA,MACb;AACA,UAAI,MAAM,cAAc,OAAO,KAAK,MAAM,UAAU,EAAE,QAAQ;AAC5D,cAAM,SAAS,CAAC;AAChB,eAAO,KAAK,MAAM,UAAU,EAAE,QAAQ,WAAS;AAC7C,cAAI,MAAM,WAAW,KAAK,MAAM,QAAW;AACzC,mBAAO,KAAK,QAAQ,MAAM,MAAM,WAAW,KAAK,CAAC;AAAA,UACnD,OAAO;AACL,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF,CAAC;AACD,gBAAQ,YAAY,KAAK,MAAM,OAAO,KAAK,GAAG,IAAI;AAAA,MACpD;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,cAAc,SAAS,MAAM;AACpC,YAAM,QAAQ,KAAK,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,GAAG;AAC7D,aAAO;AAAA,QACL,MAAM,MAAM,MAAM;AAAA,QAClB,WAAW,MAAM,KAAK,GAAG;AAAA,MAC3B;AAAA,IACF;AAGA,IAAAA,UAAS,cAAc,SAAS,OAAO;AACrC,UAAI,QAAQ;AACZ,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,yBAAyB,QAAW;AAC5C,aAAK,MAAM;AAAA,MACb;AACA,UAAI,MAAM,gBAAgB,MAAM,aAAa,QAAQ;AAEnD,cAAM,aAAa,QAAQ,QAAM;AAC/B,mBAAS,eAAe,KAAK,MAAM,GAAG,QACrC,GAAG,aAAa,GAAG,UAAU,SAAS,MAAM,GAAG,YAAY,MACxD;AAAA,QACN,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,YAAM,KAAK,KAAK,QAAQ,GAAG;AAC3B,YAAM,QAAQ;AAAA,QACZ,MAAM,SAAS,KAAK,UAAU,GAAG,EAAE,GAAG,EAAE;AAAA,MAC1C;AACA,YAAM,QAAQ,KAAK,QAAQ,KAAK,EAAE;AAClC,UAAI,QAAQ,IAAI;AACd,cAAM,YAAY,KAAK,UAAU,KAAK,GAAG,KAAK;AAC9C,cAAM,QAAQ,KAAK,UAAU,QAAQ,CAAC;AAAA,MACxC,OAAO;AACL,cAAM,YAAY,KAAK,UAAU,KAAK,CAAC;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,YAAM,QAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1C,aAAO;AAAA,QACL,WAAW,MAAM,MAAM;AAAA,QACvB,OAAO,MAAM,IAAI,UAAQ,SAAS,MAAM,EAAE,CAAC;AAAA,MAC7C;AAAA,IACF;AAIA,IAAAA,UAAS,SAAS,SAAS,cAAc;AACvC,YAAM,MAAMA,UAAS,YAAY,cAAc,QAAQ,EAAE,CAAC;AAC1D,UAAI,KAAK;AACP,eAAO,IAAI,UAAU,CAAC;AAAA,MACxB;AAAA,IACF;AAGA,IAAAA,UAAS,mBAAmB,SAAS,MAAM;AACzC,YAAM,QAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1C,aAAO;AAAA,QACL,WAAW,MAAM,CAAC,EAAE,YAAY;AAAA;AAAA,QAChC,OAAO,MAAM,CAAC,EAAE,YAAY;AAAA;AAAA,MAC9B;AAAA,IACF;AAKA,IAAAA,UAAS,oBAAoB,SAAS,cAAc,aAAa;AAC/D,YAAM,QAAQA,UAAS;AAAA,QAAY,eAAe;AAAA,QAChD;AAAA,MAAgB;AAElB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,cAAc,MAAM,IAAIA,UAAS,gBAAgB;AAAA,MACnD;AAAA,IACF;AAGA,IAAAA,UAAS,sBAAsB,SAAS,QAAQ,WAAW;AACzD,UAAIC,OAAM,aAAa,YAAY;AACnC,aAAO,aAAa,QAAQ,QAAM;AAChC,QAAAA,QAAO,mBAAmB,GAAG,YAAY,MAAM,GAAG,QAAQ;AAAA,MAC5D,CAAC;AACD,aAAOA;AAAA,IACT;AAIA,IAAAD,UAAS,kBAAkB,SAAS,MAAM;AACxC,YAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC1B,aAAa,MAAM,CAAC;AAAA,QACpB,WAAW,MAAM,CAAC;AAAA,QAClB,eAAe,MAAM,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAEA,IAAAA,UAAS,kBAAkB,SAAS,YAAY;AAC9C,aAAO,cAAc,WAAW,MAAM,MACpC,WAAW,cAAc,OACxB,OAAO,WAAW,cAAc,WAC7BA,UAAS,qBAAqB,WAAW,SAAS,IAClD,WAAW,cACd,WAAW,gBAAgB,MAAM,WAAW,cAAc,KAAK,GAAG,IAAI,MACvE;AAAA,IACJ;AAIA,IAAAA,UAAS,uBAAuB,SAAS,WAAW;AAClD,UAAI,UAAU,QAAQ,SAAS,MAAM,GAAG;AACtC,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,UAAU,UAAU,CAAC,EAAE,MAAM,GAAG;AAC9C,aAAO;AAAA,QACL,WAAW;AAAA,QACX,SAAS,MAAM,CAAC;AAAA,QAChB,UAAU,MAAM,CAAC;AAAA,QACjB,UAAU,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QAC9C,WAAW,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,MACjD;AAAA,IACF;AAEA,IAAAA,UAAS,uBAAuB,SAAS,WAAW;AAClD,aAAO,UAAU,YAAY,MACzB,UAAU,WACX,UAAU,WAAW,MAAM,UAAU,WAAW,OAChD,UAAU,YAAY,UAAU,YAC7B,MAAM,UAAU,WAAW,MAAM,UAAU,YAC3C;AAAA,IACR;AAGA,IAAAA,UAAS,sBAAsB,SAAS,cAAc,aAAa;AACjE,YAAM,QAAQA,UAAS;AAAA,QAAY,eAAe;AAAA,QAChD;AAAA,MAAW;AACb,aAAO,MAAM,IAAIA,UAAS,eAAe;AAAA,IAC3C;AAKA,IAAAA,UAAS,mBAAmB,SAAS,cAAc,aAAa;AAC9D,YAAM,QAAQA,UAAS;AAAA,QAAY,eAAe;AAAA,QAChD;AAAA,MAAc,EAAE,CAAC;AACnB,YAAM,MAAMA,UAAS;AAAA,QAAY,eAAe;AAAA,QAC9C;AAAA,MAAY,EAAE,CAAC;AACjB,UAAI,EAAE,SAAS,MAAM;AACnB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,kBAAkB,MAAM,UAAU,EAAE;AAAA,QACpC,UAAU,IAAI,UAAU,EAAE;AAAA,MAC5B;AAAA,IACF;AAGA,IAAAA,UAAS,qBAAqB,SAAS,QAAQ;AAC7C,UAAIC,OAAM,iBAAiB,OAAO,mBAAmB,mBAClC,OAAO,WAAW;AACrC,UAAI,OAAO,SAAS;AAClB,QAAAA,QAAO;AAAA,MACT;AACA,aAAOA;AAAA,IACT;AAGA,IAAAD,UAAS,qBAAqB,SAAS,cAAc;AACnD,YAAM,cAAc;AAAA,QAClB,QAAQ,CAAC;AAAA,QACT,kBAAkB,CAAC;AAAA,QACnB,eAAe,CAAC;AAAA,QAChB,MAAM,CAAC;AAAA,MACT;AACA,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAChC,kBAAY,UAAU,MAAM,CAAC;AAC7B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,KAAK,MAAM,CAAC;AAClB,cAAM,aAAaA,UAAS;AAAA,UAC1B;AAAA,UAAc,cAAc,KAAK;AAAA,QAAG,EAAE,CAAC;AACzC,YAAI,YAAY;AACd,gBAAM,QAAQA,UAAS,YAAY,UAAU;AAC7C,gBAAM,QAAQA,UAAS;AAAA,YACrB;AAAA,YAAc,YAAY,KAAK;AAAA,UAAG;AAEpC,gBAAM,aAAa,MAAM,SAASA,UAAS,UAAU,MAAM,CAAC,CAAC,IAAI,CAAC;AAClE,gBAAM,eAAeA,UAAS;AAAA,YAC5B;AAAA,YAAc,eAAe,KAAK;AAAA,UAAG,EACpC,IAAIA,UAAS,WAAW;AAC3B,sBAAY,OAAO,KAAK,KAAK;AAE7B,kBAAQ,MAAM,KAAK,YAAY,GAAG;AAAA,YAChC,KAAK;AAAA,YACL,KAAK;AACH,0BAAY,cAAc,KAAK,MAAM,KAAK,YAAY,CAAC;AACvD;AAAA,YACF;AACE;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AACA,MAAAA,UAAS,YAAY,cAAc,WAAW,EAAE,QAAQ,UAAQ;AAC9D,oBAAY,iBAAiB,KAAKA,UAAS,YAAY,IAAI,CAAC;AAAA,MAC9D,CAAC;AACD,YAAM,iBAAiBA,UAAS,YAAY,cAAc,cAAc,EACrE,IAAIA,UAAS,WAAW;AAC3B,kBAAY,OAAO,QAAQ,WAAS;AAClC,uBAAe,QAAQ,QAAK;AAC1B,gBAAM,YAAY,MAAM,aAAa,KAAK,sBAAoB;AAC5D,mBAAO,iBAAiB,SAAS,GAAG,QAClC,iBAAiB,cAAc,GAAG;AAAA,UACtC,CAAC;AACD,cAAI,CAAC,WAAW;AACd,kBAAM,aAAa,KAAK,EAAE;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,sBAAsB,SAAS,MAAM,MAAM;AAClD,UAAIC,OAAM;AAGV,MAAAA,QAAO,OAAO,OAAO;AACrB,MAAAA,QAAO,KAAK,OAAO,SAAS,IAAI,MAAM;AACtC,MAAAA,QAAO,OAAO,KAAK,WAAW,uBAAuB;AACrD,MAAAA,QAAO,KAAK,OAAO,IAAI,WAAS;AAC9B,YAAI,MAAM,yBAAyB,QAAW;AAC5C,iBAAO,MAAM;AAAA,QACf;AACA,eAAO,MAAM;AAAA,MACf,CAAC,EAAE,KAAK,GAAG,IAAI;AAEf,MAAAA,QAAO;AACP,MAAAA,QAAO;AAGP,WAAK,OAAO,QAAQ,WAAS;AAC3B,QAAAA,QAAOD,UAAS,YAAY,KAAK;AACjC,QAAAC,QAAOD,UAAS,UAAU,KAAK;AAC/B,QAAAC,QAAOD,UAAS,YAAY,KAAK;AAAA,MACnC,CAAC;AACD,UAAI,WAAW;AACf,WAAK,OAAO,QAAQ,WAAS;AAC3B,YAAI,MAAM,WAAW,UAAU;AAC7B,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,CAAC;AACD,UAAI,WAAW,GAAG;AAChB,QAAAC,QAAO,gBAAgB,WAAW;AAAA,MACpC;AAEA,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,QAAQ,eAAa;AACzC,UAAAA,QAAOD,UAAS,YAAY,SAAS;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,aAAOC;AAAA,IACT;AAIA,IAAAD,UAAS,6BAA6B,SAAS,cAAc;AAC3D,YAAM,qBAAqB,CAAC;AAC5B,YAAM,cAAcA,UAAS,mBAAmB,YAAY;AAC5D,YAAM,SAAS,YAAY,cAAc,QAAQ,KAAK,MAAM;AAC5D,YAAM,YAAY,YAAY,cAAc,QAAQ,QAAQ,MAAM;AAGlE,YAAM,QAAQA,UAAS,YAAY,cAAc,SAAS,EACvD,IAAI,UAAQA,UAAS,eAAe,IAAI,CAAC,EACzC,OAAO,WAAS,MAAM,cAAc,OAAO;AAC9C,YAAM,cAAc,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE;AACjD,UAAI;AAEJ,YAAM,QAAQA,UAAS,YAAY,cAAc,kBAAkB,EAChE,IAAI,UAAQ;AACX,cAAM,QAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1C,eAAO,MAAM,IAAI,UAAQ,SAAS,MAAM,EAAE,CAAC;AAAA,MAC7C,CAAC;AACH,UAAI,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC,EAAE,CAAC,MAAM,aAAa;AAC1E,wBAAgB,MAAM,CAAC,EAAE,CAAC;AAAA,MAC5B;AAEA,kBAAY,OAAO,QAAQ,WAAS;AAClC,YAAI,MAAM,KAAK,YAAY,MAAM,SAAS,MAAM,WAAW,KAAK;AAC9D,cAAI,WAAW;AAAA,YACb,MAAM;AAAA,YACN,kBAAkB,SAAS,MAAM,WAAW,KAAK,EAAE;AAAA,UACrD;AACA,cAAI,eAAe,eAAe;AAChC,qBAAS,MAAM,EAAC,MAAM,cAAa;AAAA,UACrC;AACA,6BAAmB,KAAK,QAAQ;AAChC,cAAI,QAAQ;AACV,uBAAW,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC9C,qBAAS,MAAM;AAAA,cACb,MAAM;AAAA,cACN,WAAW,YAAY,eAAe;AAAA,YACxC;AACA,+BAAmB,KAAK,QAAQ;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,mBAAmB,WAAW,KAAK,aAAa;AAClD,2BAAmB,KAAK;AAAA,UACtB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAGA,UAAI,YAAYA,UAAS,YAAY,cAAc,IAAI;AACvD,UAAI,UAAU,QAAQ;AACpB,YAAI,UAAU,CAAC,EAAE,QAAQ,SAAS,MAAM,GAAG;AACzC,sBAAY,SAAS,UAAU,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE;AAAA,QACpD,WAAW,UAAU,CAAC,EAAE,QAAQ,OAAO,MAAM,GAAG;AAE9C,sBAAY,SAAS,UAAU,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,IAAI,MAAO,OACtD,KAAK,KAAK;AAAA,QACnB,OAAO;AACL,sBAAY;AAAA,QACd;AACA,2BAAmB,QAAQ,YAAU;AACnC,iBAAO,aAAa;AAAA,QACtB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,sBAAsB,SAAS,cAAc;AACpD,YAAM,iBAAiB,CAAC;AAIxB,YAAM,aAAaA,UAAS,YAAY,cAAc,SAAS,EAC5D,IAAI,UAAQA,UAAS,eAAe,IAAI,CAAC,EACzC,OAAO,SAAO,IAAI,cAAc,OAAO,EAAE,CAAC;AAC7C,UAAI,YAAY;AACd,uBAAe,QAAQ,WAAW;AAClC,uBAAe,OAAO,WAAW;AAAA,MACnC;AAIA,YAAM,QAAQA,UAAS,YAAY,cAAc,cAAc;AAC/D,qBAAe,cAAc,MAAM,SAAS;AAC5C,qBAAe,WAAW,MAAM,WAAW;AAI3C,YAAM,MAAMA,UAAS,YAAY,cAAc,YAAY;AAC3D,qBAAe,MAAM,IAAI,SAAS;AAElC,aAAO;AAAA,IACT;AAEA,IAAAA,UAAS,sBAAsB,SAAS,gBAAgB;AACtD,UAAIC,OAAM;AACV,UAAI,eAAe,aAAa;AAC9B,QAAAA,QAAO;AAAA,MACT;AACA,UAAI,eAAe,KAAK;AACtB,QAAAA,QAAO;AAAA,MACT;AACA,UAAI,eAAe,SAAS,UAAa,eAAe,OAAO;AAC7D,QAAAA,QAAO,YAAY,eAAe,OAChC,YAAY,eAAe,QAAQ;AAAA,MACvC;AACA,aAAOA;AAAA,IACT;AAKA,IAAAD,UAAS,YAAY,SAAS,cAAc;AAC1C,UAAI;AACJ,YAAM,OAAOA,UAAS,YAAY,cAAc,SAAS;AACzD,UAAI,KAAK,WAAW,GAAG;AACrB,gBAAQ,KAAK,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG;AACtC,eAAO,EAAC,QAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,EAAC;AAAA,MAC3C;AACA,YAAM,QAAQA,UAAS,YAAY,cAAc,SAAS,EACvD,IAAI,UAAQA,UAAS,eAAe,IAAI,CAAC,EACzC,OAAO,eAAa,UAAU,cAAc,MAAM;AACrD,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,CAAC,EAAE,MAAM,MAAM,GAAG;AAChC,eAAO,EAAC,QAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,EAAC;AAAA,MAC3C;AAAA,IACF;AAKA,IAAAA,UAAS,uBAAuB,SAAS,cAAc;AACrD,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,cAAcA,UAAS,YAAY,cAAc,qBAAqB;AAC5E,UAAI;AACJ,UAAI,YAAY,SAAS,GAAG;AAC1B,yBAAiB,SAAS,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;AAAA,MAC5D;AACA,UAAI,MAAM,cAAc,GAAG;AACzB,yBAAiB;AAAA,MACnB;AACA,YAAM,WAAWA,UAAS,YAAY,cAAc,cAAc;AAClE,UAAI,SAAS,SAAS,GAAG;AACvB,eAAO;AAAA,UACL,MAAM,SAAS,SAAS,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;AAAA,UAC5C,UAAU,MAAM;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAeA,UAAS,YAAY,cAAc,YAAY;AACpE,UAAI,aAAa,SAAS,GAAG;AAC3B,cAAM,QAAQ,aAAa,CAAC,EACzB,UAAU,EAAE,EACZ,MAAM,GAAG;AACZ,eAAO;AAAA,UACL,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UAC3B,UAAU,MAAM,CAAC;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,IAAAA,UAAS,uBAAuB,SAAS,OAAO,MAAM;AACpD,UAAI,SAAS,CAAC;AACd,UAAI,MAAM,aAAa,aAAa;AAClC,iBAAS;AAAA,UACP,OAAO,MAAM,OAAO,QAAQ,MAAM,WAAW,MAAM,KAAK,WAAW;AAAA,UACnE;AAAA,UACA,iBAAiB,KAAK,OAAO;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,iBAAS;AAAA,UACP,OAAO,MAAM,OAAO,QAAQ,MAAM,WAAW,MAAM,KAAK,OAAO;AAAA,UAC/D;AAAA,UACA,eAAe,KAAK,OAAO,MAAM,KAAK,WAAW;AAAA,QACnD;AAAA,MACF;AACA,UAAI,KAAK,mBAAmB,QAAW;AACrC,eAAO,KAAK,wBAAwB,KAAK,iBAAiB,MAAM;AAAA,MAClE;AACA,aAAO,OAAO,KAAK,EAAE;AAAA,IACvB;AAMA,IAAAA,UAAS,oBAAoB,WAAW;AACtC,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,OAAO,GAAG,EAAE;AAAA,IAC9C;AAOA,IAAAA,UAAS,0BAA0B,SAAS,QAAQ,SAAS,UAAU;AACrE,UAAI;AACJ,YAAM,UAAU,YAAY,SAAY,UAAU;AAClD,UAAI,QAAQ;AACV,oBAAY;AAAA,MACd,OAAO;AACL,oBAAYA,UAAS,kBAAkB;AAAA,MACzC;AACA,YAAM,OAAO,YAAY;AAEzB,aAAO,cACI,OAAO,MAAM,YAAY,MAAM,UACpC;AAAA,IAGR;AAGA,IAAAA,UAAS,eAAe,SAAS,cAAc,aAAa;AAE1D,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAQ,MAAM,CAAC,GAAG;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,MAAM,CAAC,EAAE,UAAU,CAAC;AAAA,UAC7B;AAAA,QAEF;AAAA,MACF;AACA,UAAI,aAAa;AACf,eAAOA,UAAS,aAAa,WAAW;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,UAAS,UAAU,SAAS,cAAc;AACxC,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAChC,aAAO,MAAM,CAAC,EAAE,UAAU,CAAC;AAAA,IAC7B;AAEA,IAAAA,UAAS,aAAa,SAAS,cAAc;AAC3C,aAAO,aAAa,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;AAAA,IAC3C;AAEA,IAAAA,UAAS,aAAa,SAAS,cAAc;AAC3C,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,QAAQ,MAAM,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG;AAC7C,aAAO;AAAA,QACL,MAAM,MAAM,CAAC;AAAA,QACb,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC3B,UAAU,MAAM,CAAC;AAAA,QACjB,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,MAC9B;AAAA,IACF;AAEA,IAAAA,UAAS,aAAa,SAAS,cAAc;AAC3C,YAAM,OAAOA,UAAS,YAAY,cAAc,IAAI,EAAE,CAAC;AACvD,YAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,UAAU,MAAM,CAAC;AAAA,QACjB,WAAW,MAAM,CAAC;AAAA,QAClB,gBAAgB,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QACrC,SAAS,MAAM,CAAC;AAAA,QAChB,aAAa,MAAM,CAAC;AAAA,QACpB,SAAS,MAAM,CAAC;AAAA,MAClB;AAAA,IACF;AAGA,IAAAA,UAAS,aAAa,SAAS,MAAM;AACnC,UAAI,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AACjD,eAAO;AAAA,MACT;AACA,YAAM,QAAQA,UAAS,WAAW,IAAI;AACtC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK;AACrD,iBAAO;AAAA,QACT;AAAA,MAEF;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,WAAW,UAAU;AAC9B,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;ACzxBA,IAAI,eAAe;AACnB,IAAI,uBAAuB;AAUpB,SAAS,eAAe,UAAU,MAAM,KAAK;AAClD,QAAM,QAAQ,SAAS,MAAM,IAAI;AACjC,SAAO,SAAS,MAAM,UAAU,OAAO,WAAW,MAAM,GAAG,GAAG,EAAE;AAClE;AAKO,SAAS,wBAAwBE,SAAQ,iBAAiB,SAAS;AACxE,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AACA,QAAM,QAAQA,QAAO,kBAAkB;AACvC,QAAM,yBAAyB,MAAM;AACrC,QAAM,mBAAmB,SAAS,iBAAiB,IAAI;AACrD,QAAI,oBAAoB,iBAAiB;AACvC,aAAO,uBAAuB,MAAM,MAAM,SAAS;AAAA,IACrD;AACA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,YAAM,gBAAgB,QAAQ,CAAC;AAC/B,UAAI,eAAe;AACjB,YAAI,GAAG,aAAa;AAClB,aAAG,YAAY,aAAa;AAAA,QAC9B,OAAO;AACL,aAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,KAAK,aAAa,CAAC;AACpC,QAAI,CAAC,KAAK,UAAU,eAAe,GAAG;AACpC,WAAK,UAAU,eAAe,IAAI,oBAAI,IAAI;AAAA,IAC5C;AACA,SAAK,UAAU,eAAe,EAAE,IAAI,IAAI,eAAe;AACvD,WAAO,uBAAuB,MAAM,MAAM;AAAA,MAAC;AAAA,MACzC;AAAA,IAAe,CAAC;AAAA,EACpB;AAEA,QAAM,4BAA4B,MAAM;AACxC,QAAM,sBAAsB,SAAS,iBAAiB,IAAI;AACxD,QAAI,oBAAoB,mBAAmB,CAAC,KAAK,aAC1C,CAAC,KAAK,UAAU,eAAe,GAAG;AACvC,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AACA,QAAI,CAAC,KAAK,UAAU,eAAe,EAAE,IAAI,EAAE,GAAG;AAC5C,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AACA,UAAM,cAAc,KAAK,UAAU,eAAe,EAAE,IAAI,EAAE;AAC1D,SAAK,UAAU,eAAe,EAAE,OAAO,EAAE;AACzC,QAAI,KAAK,UAAU,eAAe,EAAE,SAAS,GAAG;AAC9C,aAAO,KAAK,UAAU,eAAe;AAAA,IACvC;AACA,QAAI,OAAO,KAAK,KAAK,SAAS,EAAE,WAAW,GAAG;AAC5C,aAAO,KAAK;AAAA,IACd;AACA,WAAO,0BAA0B,MAAM,MAAM;AAAA,MAAC;AAAA,MAC5C;AAAA,IAAW,CAAC;AAAA,EAChB;AAEA,SAAO,eAAe,OAAO,OAAO,iBAAiB;AAAA,IACnD,MAAM;AACJ,aAAO,KAAK,QAAQ,eAAe;AAAA,IACrC;AAAA,IACA,IAAI,IAAI;AACN,UAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,aAAK;AAAA,UAAoB;AAAA,UACvB,KAAK,QAAQ,eAAe;AAAA,QAAC;AAC/B,eAAO,KAAK,QAAQ,eAAe;AAAA,MACrC;AACA,UAAI,IAAI;AACN,aAAK;AAAA,UAAiB;AAAA,UACpB,KAAK,QAAQ,eAAe,IAAI;AAAA,QAAE;AAAA,MACtC;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACH;AAEO,SAAS,WAAW,MAAM;AAC/B,MAAI,OAAO,SAAS,WAAW;AAC7B,WAAO,IAAI,MAAM,oBAAoB,OAAO,OACxC,yBAAyB;AAAA,EAC/B;AACA,iBAAe;AACf,SAAQ,OAAQ,gCACd;AACJ;AAMO,SAAS,gBAAgB,MAAM;AACpC,MAAI,OAAO,SAAS,WAAW;AAC7B,WAAO,IAAI,MAAM,oBAAoB,OAAO,OACxC,yBAAyB;AAAA,EAC/B;AACA,yBAAuB,CAAC;AACxB,SAAO,sCAAsC,OAAO,aAAa;AACnE;AAEO,SAAS,MAAM;AACpB,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,cAAc;AAChB;AAAA,IACF;AACA,QAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,YAAY;AACvE,cAAQ,IAAI,MAAM,SAAS,SAAS;AAAA,IACtC;AAAA,EACF;AACF;AAKO,SAAS,WAAW,WAAW,WAAW;AAC/C,MAAI,CAAC,sBAAsB;AACzB;AAAA,EACF;AACA,UAAQ,KAAK,YAAY,gCAAgC,YACrD,WAAW;AACjB;AAQO,SAAS,cAAcA,SAAQ;AAEpC,QAAM,SAAS,EAAC,SAAS,MAAM,SAAS,KAAI;AAG5C,MAAI,OAAOA,YAAW,eAAe,CAACA,QAAO,aACzC,CAACA,QAAO,UAAU,WAAW;AAC/B,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,QAAM,EAAC,UAAS,IAAIA;AAGpB,MAAI,UAAU,iBAAiB,UAAU,cAAc,QAAQ;AAC7D,UAAM,WAAW,UAAU,cAAc,OAAO,KAAK,CAAC,UAAU;AAC9D,aAAO,MAAM,UAAU;AAAA,IACzB,CAAC;AACD,QAAI,UAAU;AACZ,aAAO,EAAC,SAAS,UAAU,SAAS,SAAS,SAAS,SAAS,EAAE,EAAC;AAAA,IACpE;AAAA,EACF;AAEA,MAAI,UAAU,iBAAiB;AAC7B,WAAO,UAAU;AACjB,WAAO,UAAU,SAAS;AAAA,MAAe,UAAU;AAAA,MACjD;AAAA,MAAoB;AAAA,IAAC,CAAC;AAAA,EAC1B,WAAW,UAAU,sBAChBA,QAAO,oBAAoB,SAASA,QAAO,yBAA0B;AAKxE,WAAO,UAAU;AACjB,WAAO,UAAU,SAAS;AAAA,MAAe,UAAU;AAAA,MACjD;AAAA,MAAyB;AAAA,IAAC,CAAC;AAAA,EAC/B,WAAWA,QAAO,qBACd,UAAU,UAAU,MAAM,sBAAsB,GAAG;AACrD,WAAO,UAAU;AACjB,WAAO,UAAU,SAAS;AAAA,MAAe,UAAU;AAAA,MACjD;AAAA,MAAwB;AAAA,IAAC,CAAC;AAC5B,WAAO,sBAAsBA,QAAO,qBAChC,sBAAsBA,QAAO,kBAAkB;AAEnD,WAAO,iBAAiB;AAAA,MAAe,UAAU;AAAA,MAC/C;AAAA,MAA0B;AAAA,IAAC;AAAA,EAC/B,OAAO;AACL,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AAOO,SAAS,cAAc,MAAM;AAClC,MAAI,CAAC,SAAS,IAAI,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,IAAI,EAAE,OAAO,SAAS,aAAa,KAAK;AACzD,UAAM,QAAQ,SAAS,KAAK,GAAG,CAAC;AAChC,UAAM,QAAQ,QAAQ,cAAc,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG;AACzD,UAAM,gBAAgB,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;AACnD,QAAI,UAAU,UAAa,eAAe;AACxC,aAAO;AAAA,IACT;AACA,WAAO,OAAO,OAAO,aAAa,EAAC,CAAC,GAAG,GAAG,MAAK,CAAC;AAAA,EAClD,GAAG,CAAC,CAAC;AACP;AAGO,SAAS,UAAU,OAAO,MAAM,WAAW;AAChD,MAAI,CAAC,QAAQ,UAAU,IAAI,KAAK,EAAE,GAAG;AACnC;AAAA,EACF;AACA,YAAU,IAAI,KAAK,IAAI,IAAI;AAC3B,SAAO,KAAK,IAAI,EAAE,QAAQ,UAAQ;AAChC,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,gBAAU,OAAO,MAAM,IAAI,KAAK,IAAI,CAAC,GAAG,SAAS;AAAA,IACnD,WAAW,KAAK,SAAS,KAAK,GAAG;AAC/B,WAAK,IAAI,EAAE,QAAQ,QAAM;AACvB,kBAAU,OAAO,MAAM,IAAI,EAAE,GAAG,SAAS;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAGO,SAAS,YAAY,QAAQ,OAAO,UAAU;AACnD,QAAM,kBAAkB,WAAW,iBAAiB;AACpD,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC;AACpB,SAAO,QAAQ,WAAS;AACtB,QAAI,MAAM,SAAS,WACf,MAAM,oBAAoB,MAAM,IAAI;AACtC,iBAAW,KAAK,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AACD,aAAW,QAAQ,eAAa;AAC9B,WAAO,QAAQ,WAAS;AACtB,UAAI,MAAM,SAAS,mBAAmB,MAAM,YAAY,UAAU,IAAI;AACpE,kBAAU,QAAQ,OAAO,cAAc;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;;;AClRA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACUA,IAAM,UAAgB;AAEf,SAAS,iBAAiBC,SAAQ,gBAAgB;AACvD,QAAM,YAAYA,WAAUA,QAAO;AAEnC,MAAI,CAAC,UAAU,cAAc;AAC3B;AAAA,EACF;AAEA,QAAM,uBAAuB,SAAS,GAAG;AACvC,QAAI,OAAO,MAAM,YAAY,EAAE,aAAa,EAAE,UAAU;AACtD,aAAO;AAAA,IACT;AACA,UAAM,KAAK,CAAC;AACZ,WAAO,KAAK,CAAC,EAAE,QAAQ,SAAO;AAC5B,UAAI,QAAQ,aAAa,QAAQ,cAAc,QAAQ,eAAe;AACpE;AAAA,MACF;AACA,YAAM,IAAK,OAAO,EAAE,GAAG,MAAM,WAAY,EAAE,GAAG,IAAI,EAAC,OAAO,EAAE,GAAG,EAAC;AAChE,UAAI,EAAE,UAAU,UAAa,OAAO,EAAE,UAAU,UAAU;AACxD,UAAE,MAAM,EAAE,MAAM,EAAE;AAAA,MACpB;AACA,YAAM,WAAW,SAAS,QAAQ,MAAM;AACtC,YAAI,QAAQ;AACV,iBAAO,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,QAC7D;AACA,eAAQ,SAAS,aAAc,aAAa;AAAA,MAC9C;AACA,UAAI,EAAE,UAAU,QAAW;AACzB,WAAG,WAAW,GAAG,YAAY,CAAC;AAC9B,YAAI,KAAK,CAAC;AACV,YAAI,OAAO,EAAE,UAAU,UAAU;AAC/B,aAAG,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE;AAC7B,aAAG,SAAS,KAAK,EAAE;AACnB,eAAK,CAAC;AACN,aAAG,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE;AAC7B,aAAG,SAAS,KAAK,EAAE;AAAA,QACrB,OAAO;AACL,aAAG,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;AAC1B,aAAG,SAAS,KAAK,EAAE;AAAA,QACrB;AAAA,MACF;AACA,UAAI,EAAE,UAAU,UAAa,OAAO,EAAE,UAAU,UAAU;AACxD,WAAG,YAAY,GAAG,aAAa,CAAC;AAChC,WAAG,UAAU,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;AAAA,MACtC,OAAO;AACL,SAAC,OAAO,KAAK,EAAE,QAAQ,SAAO;AAC5B,cAAI,EAAE,GAAG,MAAM,QAAW;AACxB,eAAG,YAAY,GAAG,aAAa,CAAC;AAChC,eAAG,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG;AAAA,UAC1C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,EAAE,UAAU;AACd,SAAG,YAAY,GAAG,YAAY,CAAC,GAAG,OAAO,EAAE,QAAQ;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB,SAAS,aAAa,MAAM;AACnD,QAAI,eAAe,WAAW,IAAI;AAChC,aAAO,KAAK,WAAW;AAAA,IACzB;AACA,kBAAc,KAAK,MAAM,KAAK,UAAU,WAAW,CAAC;AACpD,QAAI,eAAe,OAAO,YAAY,UAAU,UAAU;AACxD,YAAM,QAAQ,SAAS,KAAK,GAAG,GAAG;AAChC,YAAI,KAAK,OAAO,EAAE,KAAK,MAAM;AAC3B,cAAI,CAAC,IAAI,IAAI,CAAC;AACd,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,oBAAc,KAAK,MAAM,KAAK,UAAU,WAAW,CAAC;AACpD,YAAM,YAAY,OAAO,mBAAmB,qBAAqB;AACjE,YAAM,YAAY,OAAO,oBAAoB,sBAAsB;AACnE,kBAAY,QAAQ,qBAAqB,YAAY,KAAK;AAAA,IAC5D;AACA,QAAI,eAAe,OAAO,YAAY,UAAU,UAAU;AAExD,UAAI,OAAO,YAAY,MAAM;AAC7B,aAAO,SAAU,OAAO,SAAS,WAAY,OAAO,EAAC,OAAO,KAAI;AAChE,YAAM,6BAA6B,eAAe,UAAU;AAE5D,UAAK,SAAS,KAAK,UAAU,UAAU,KAAK,UAAU,iBACxC,KAAK,UAAU,UAAU,KAAK,UAAU,kBAClD,EAAE,UAAU,aAAa,2BACvB,UAAU,aAAa,wBAAwB,EAAE,cACjD,CAAC,6BAA6B;AAClC,eAAO,YAAY,MAAM;AACzB,YAAI;AACJ,YAAI,KAAK,UAAU,iBAAiB,KAAK,UAAU,eAAe;AAChE,oBAAU,CAAC,QAAQ,MAAM;AAAA,QAC3B,WAAW,KAAK,UAAU,UAAU,KAAK,UAAU,QAAQ;AACzD,oBAAU,CAAC,OAAO;AAAA,QACpB;AACA,YAAI,SAAS;AAEX,iBAAO,UAAU,aAAa,iBAAiB,EAC5C,KAAK,aAAW;AACf,sBAAU,QAAQ,OAAO,OAAK,EAAE,SAAS,YAAY;AACrD,gBAAI,MAAM,QAAQ,KAAK,OAAK,QAAQ,KAAK,WACvC,EAAE,MAAM,YAAY,EAAE,SAAS,KAAK,CAAC,CAAC;AACxC,gBAAI,CAAC,OAAO,QAAQ,UAAU,QAAQ,SAAS,MAAM,GAAG;AACtD,oBAAM,QAAQ,QAAQ,SAAS,CAAC;AAAA,YAClC;AACA,gBAAI,KAAK;AACP,0BAAY,MAAM,WAAW,KAAK,QAC9B,EAAC,OAAO,IAAI,SAAQ,IACpB,EAAC,OAAO,IAAI,SAAQ;AAAA,YAC1B;AACA,wBAAY,QAAQ,qBAAqB,YAAY,KAAK;AAC1D,oBAAQ,aAAa,KAAK,UAAU,WAAW,CAAC;AAChD,mBAAO,KAAK,WAAW;AAAA,UACzB,CAAC;AAAA,QACL;AAAA,MACF;AACA,kBAAY,QAAQ,qBAAqB,YAAY,KAAK;AAAA,IAC5D;AACA,YAAQ,aAAa,KAAK,UAAU,WAAW,CAAC;AAChD,WAAO,KAAK,WAAW;AAAA,EACzB;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,QAAI,eAAe,WAAW,IAAI;AAChC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,6BAA6B;AAAA,QAC7B,iBAAiB;AAAA,QACjB,gCAAgC;AAAA,QAChC,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,MACtB,EAAE,EAAE,IAAI,KAAK,EAAE;AAAA,MACf,SAAS,EAAE;AAAA,MACX,YAAY,EAAE,cAAc,EAAE;AAAA,MAC9B,WAAW;AACT,eAAO,KAAK,QAAQ,KAAK,WAAW,QAAQ,KAAK;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,gBAAgB,SAAS,aAAa,WAAW,SAAS;AAC9D,qBAAiB,aAAa,OAAK;AACjC,gBAAU,mBAAmB,GAAG,WAAW,OAAK;AAC9C,YAAI,SAAS;AACX,kBAAQ,WAAW,CAAC,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,YAAU,eAAe,cAAc,KAAK,SAAS;AAKrD,MAAI,UAAU,aAAa,cAAc;AACvC,UAAM,mBAAmB,UAAU,aAAa,aAC9C,KAAK,UAAU,YAAY;AAC7B,cAAU,aAAa,eAAe,SAAS,IAAI;AACjD,aAAO,iBAAiB,IAAI,OAAK,iBAAiB,CAAC,EAAE,KAAK,YAAU;AAClE,YAAI,EAAE,SAAS,CAAC,OAAO,eAAe,EAAE,UACpC,EAAE,SAAS,CAAC,OAAO,eAAe,EAAE,QAAQ;AAC9C,iBAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,kBAAM,KAAK;AAAA,UACb,CAAC;AACD,gBAAM,IAAI,aAAa,IAAI,eAAe;AAAA,QAC5C;AACA,eAAO;AAAA,MACT,GAAG,OAAK,QAAQ,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;AAAA,IACxC;AAAA,EACF;AACF;;;AD/KO,SAAS,gBAAgBC,SAAQ;AACtC,EAAAA,QAAO,cAAcA,QAAO,eAAeA,QAAO;AACpD;AAEO,SAAS,YAAYA,SAAQ;AAClC,MAAI,OAAOA,YAAW,YAAYA,QAAO,qBAAqB,EAAE,aAC5DA,QAAO,kBAAkB,YAAY;AACvC,WAAO,eAAeA,QAAO,kBAAkB,WAAW,WAAW;AAAA,MACnE,MAAM;AACJ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,GAAG;AACL,YAAI,KAAK,UAAU;AACjB,eAAK,oBAAoB,SAAS,KAAK,QAAQ;AAAA,QACjD;AACA,aAAK,iBAAiB,SAAS,KAAK,WAAW,CAAC;AAAA,MAClD;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,UAAM,2BACFA,QAAO,kBAAkB,UAAU;AACvC,IAAAA,QAAO,kBAAkB,UAAU,uBACjC,SAAS,uBAAuB;AAC9B,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,CAAC,MAAM;AAGzB,YAAE,OAAO,iBAAiB,YAAY,QAAM;AAC1C,gBAAI;AACJ,gBAAIA,QAAO,kBAAkB,UAAU,cAAc;AACnD,yBAAW,KAAK,aAAa,EAC1B,KAAK,OAAK,EAAE,SAAS,EAAE,MAAM,OAAO,GAAG,MAAM,EAAE;AAAA,YACpD,OAAO;AACL,yBAAW,EAAC,OAAO,GAAG,MAAK;AAAA,YAC7B;AAEA,kBAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,kBAAM,QAAQ,GAAG;AACjB,kBAAM,WAAW;AACjB,kBAAM,cAAc,EAAC,SAAQ;AAC7B,kBAAM,UAAU,CAAC,EAAE,MAAM;AACzB,iBAAK,cAAc,KAAK;AAAA,UAC1B,CAAC;AACD,YAAE,OAAO,UAAU,EAAE,QAAQ,WAAS;AACpC,gBAAI;AACJ,gBAAIA,QAAO,kBAAkB,UAAU,cAAc;AACnD,yBAAW,KAAK,aAAa,EAC1B,KAAK,OAAK,EAAE,SAAS,EAAE,MAAM,OAAO,MAAM,EAAE;AAAA,YACjD,OAAO;AACL,yBAAW,EAAC,MAAK;AAAA,YACnB;AACA,kBAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,kBAAM,QAAQ;AACd,kBAAM,WAAW;AACjB,kBAAM,cAAc,EAAC,SAAQ;AAC7B,kBAAM,UAAU,CAAC,EAAE,MAAM;AACzB,iBAAK,cAAc,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH;AACA,aAAK,iBAAiB,aAAa,KAAK,YAAY;AAAA,MACtD;AACA,aAAO,yBAAyB,MAAM,MAAM,SAAS;AAAA,IACvD;AAAA,EACJ,OAAO;AAIL,IAAM,wBAAwBA,SAAQ,SAAS,OAAK;AAClD,UAAI,CAAC,EAAE,aAAa;AAClB,eAAO;AAAA,UAAe;AAAA,UAAG;AAAA,UACvB,EAAC,OAAO,EAAC,UAAU,EAAE,SAAQ,EAAC;AAAA,QAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEO,SAAS,uBAAuBA,SAAQ;AAE7C,MAAI,OAAOA,YAAW,YAAYA,QAAO,qBACrC,EAAE,gBAAgBA,QAAO,kBAAkB,cAC3C,sBAAsBA,QAAO,kBAAkB,WAAW;AAC5D,UAAM,qBAAqB,SAAS,IAAI,OAAO;AAC7C,aAAO;AAAA,QACL;AAAA,QACA,IAAI,OAAO;AACT,cAAI,KAAK,UAAU,QAAW;AAC5B,gBAAI,MAAM,SAAS,SAAS;AAC1B,mBAAK,QAAQ,GAAG,iBAAiB,KAAK;AAAA,YACxC,OAAO;AACL,mBAAK,QAAQ;AAAA,YACf;AAAA,UACF;AACA,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAGA,QAAI,CAACA,QAAO,kBAAkB,UAAU,YAAY;AAClD,MAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,aAAK,WAAW,KAAK,YAAY,CAAC;AAClC,eAAO,KAAK,SAAS,MAAM;AAAA,MAC7B;AACA,YAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,MAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,OAAO,QAAQ;AAC/B,YAAI,SAAS,aAAa,MAAM,MAAM,SAAS;AAC/C,YAAI,CAAC,QAAQ;AACX,mBAAS,mBAAmB,MAAM,KAAK;AACvC,eAAK,SAAS,KAAK,MAAM;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAEF,YAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,MAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,QAAQ;AAC3B,wBAAgB,MAAM,MAAM,SAAS;AACrC,cAAM,MAAM,KAAK,SAAS,QAAQ,MAAM;AACxC,YAAI,QAAQ,IAAI;AACd,eAAK,SAAS,OAAO,KAAK,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACJ;AACA,UAAM,gBAAgBA,QAAO,kBAAkB,UAAU;AACzD,IAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,WAAK,WAAW,KAAK,YAAY,CAAC;AAClC,oBAAc,MAAM,MAAM,CAAC,MAAM,CAAC;AAClC,aAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,aAAK,SAAS,KAAK,mBAAmB,MAAM,KAAK,CAAC;AAAA,MACpD,CAAC;AAAA,IACH;AAEA,UAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,IAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,WAAK,WAAW,KAAK,YAAY,CAAC;AAClC,uBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC;AAErC,aAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,cAAM,SAAS,KAAK,SAAS,KAAK,OAAK,EAAE,UAAU,KAAK;AACxD,YAAI,QAAQ;AACV,eAAK,SAAS,OAAO,KAAK,SAAS,QAAQ,MAAM,GAAG,CAAC;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACJ,WAAW,OAAOA,YAAW,YAAYA,QAAO,qBACrC,gBAAgBA,QAAO,kBAAkB,aACzC,sBAAsBA,QAAO,kBAAkB,aAC/CA,QAAO,gBACP,EAAE,UAAUA,QAAO,aAAa,YAAY;AACrD,UAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,IAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,YAAM,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAC7C,cAAQ,QAAQ,YAAU,OAAO,MAAM,IAAI;AAC3C,aAAO;AAAA,IACT;AAEA,WAAO,eAAeA,QAAO,aAAa,WAAW,QAAQ;AAAA,MAC3D,MAAM;AACJ,YAAI,KAAK,UAAU,QAAW;AAC5B,cAAI,KAAK,MAAM,SAAS,SAAS;AAC/B,iBAAK,QAAQ,KAAK,IAAI,iBAAiB,KAAK,KAAK;AAAA,UACnD,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AACA,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,2BAA2BA,SAAQ;AACjD,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,qBACvCA,QAAO,gBAAgBA,QAAO,iBAAiB;AACjD;AAAA,EACF;AAGA,MAAI,EAAE,cAAcA,QAAO,aAAa,YAAY;AAClD,UAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,QAAI,gBAAgB;AAClB,MAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,cAAM,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAC7C,gBAAQ,QAAQ,YAAU,OAAO,MAAM,IAAI;AAC3C,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,QAAI,cAAc;AAChB,MAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,cAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,eAAO,MAAM;AACb,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAAA,QAAO,aAAa,UAAU,WAAW,SAAS,WAAW;AAC3D,YAAM,SAAS;AACf,aAAO,KAAK,IAAI,SAAS,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,QAKxB,YAAY,QAAQ,OAAO,OAAO,IAAI;AAAA,OAAC;AAAA,IACjD;AAAA,EACF;AAGA,MAAI,EAAE,cAAcA,QAAO,eAAe,YAAY;AACpD,UAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,QAAI,kBAAkB;AACpB,MAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,eAAe;AACtB,cAAM,YAAY,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACjD,kBAAU,QAAQ,cAAY,SAAS,MAAM,IAAI;AACjD,eAAO;AAAA,MACT;AAAA,IACJ;AACA,IAAM,wBAAwBA,SAAQ,SAAS,OAAK;AAClD,QAAE,SAAS,MAAM,EAAE;AACnB,aAAO;AAAA,IACT,CAAC;AACD,IAAAA,QAAO,eAAe,UAAU,WAAW,SAAS,WAAW;AAC7D,YAAM,WAAW;AACjB,aAAO,KAAK,IAAI,SAAS,EAAE,KAAK,YACxB,YAAY,QAAQ,SAAS,OAAO,KAAK,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,MAAI,EAAE,cAAcA,QAAO,aAAa,aACpC,cAAcA,QAAO,eAAe,YAAY;AAClD;AAAA,EACF;AAGA,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,EAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,QAAI,UAAU,SAAS,KACnB,UAAU,CAAC,aAAaA,QAAO,kBAAkB;AACnD,YAAM,QAAQ,UAAU,CAAC;AACzB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,WAAK,WAAW,EAAE,QAAQ,OAAK;AAC7B,YAAI,EAAE,UAAU,OAAO;AACrB,cAAI,QAAQ;AACV,kBAAM;AAAA,UACR,OAAO;AACL,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,aAAa,EAAE,QAAQ,OAAK;AAC/B,YAAI,EAAE,UAAU,OAAO;AACrB,cAAI,UAAU;AACZ,kBAAM;AAAA,UACR,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF;AACA,eAAO,EAAE,UAAU;AAAA,MACrB,CAAC;AACD,UAAI,OAAQ,UAAU,UAAW;AAC/B,eAAO,QAAQ,OAAO,IAAI;AAAA,UACxB;AAAA,UACA;AAAA,QAAoB,CAAC;AAAA,MACzB,WAAW,QAAQ;AACjB,eAAO,OAAO,SAAS;AAAA,MACzB,WAAW,UAAU;AACnB,eAAO,SAAS,SAAS;AAAA,MAC3B;AACA,aAAO,QAAQ,OAAO,IAAI;AAAA,QACxB;AAAA,QACA;AAAA,MAAoB,CAAC;AAAA,IACzB;AACA,WAAO,aAAa,MAAM,MAAM,SAAS;AAAA,EAC3C;AACF;AAEO,SAAS,kCAAkCA,SAAQ;AAIxD,EAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAC1D,WAAO,OAAO,KAAK,KAAK,oBAAoB,EACzC,IAAI,cAAY,KAAK,qBAAqB,QAAQ,EAAE,CAAC,CAAC;AAAA,EAC3D;AAEF,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,EAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,OAAO,QAAQ;AAC/B,QAAI,CAAC,QAAQ;AACX,aAAO,aAAa,MAAM,MAAM,SAAS;AAAA,IAC3C;AACA,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAE1D,UAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,QAAI,CAAC,KAAK,qBAAqB,OAAO,EAAE,GAAG;AACzC,WAAK,qBAAqB,OAAO,EAAE,IAAI,CAAC,QAAQ,MAAM;AAAA,IACxD,WAAW,KAAK,qBAAqB,OAAO,EAAE,EAAE,QAAQ,MAAM,MAAM,IAAI;AACtE,WAAK,qBAAqB,OAAO,EAAE,EAAE,KAAK,MAAM;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAEF,QAAM,gBAAgBA,QAAO,kBAAkB,UAAU;AACzD,EAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAE1D,WAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,YAAM,gBAAgB,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AACnE,UAAI,eAAe;AACjB,cAAM,IAAI;AAAA,UAAa;AAAA,UACrB;AAAA,QAAoB;AAAA,MACxB;AAAA,IACF,CAAC;AACD,UAAM,kBAAkB,KAAK,WAAW;AACxC,kBAAc,MAAM,MAAM,SAAS;AACnC,UAAM,aAAa,KAAK,WAAW,EAChC,OAAO,eAAa,gBAAgB,QAAQ,SAAS,MAAM,EAAE;AAChE,SAAK,qBAAqB,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,UAAU;AAAA,EACnE;AAEA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,EAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAC1D,WAAO,KAAK,qBAAqB,OAAO,EAAE;AAC1C,WAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,EAC/C;AAEF,QAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,EAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,QAAQ;AAC3B,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAC1D,QAAI,QAAQ;AACV,aAAO,KAAK,KAAK,oBAAoB,EAAE,QAAQ,cAAY;AACzD,cAAM,MAAM,KAAK,qBAAqB,QAAQ,EAAE,QAAQ,MAAM;AAC9D,YAAI,QAAQ,IAAI;AACd,eAAK,qBAAqB,QAAQ,EAAE,OAAO,KAAK,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,qBAAqB,QAAQ,EAAE,WAAW,GAAG;AACpD,iBAAO,KAAK,qBAAqB,QAAQ;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,EAC9C;AACJ;AAEO,SAAS,wBAAwBA,SAAQ,gBAAgB;AAC9D,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,MAAIA,QAAO,kBAAkB,UAAU,YACnC,eAAe,WAAW,IAAI;AAChC,WAAO,kCAAkCA,OAAM;AAAA,EACjD;AAIA,QAAM,sBAAsBA,QAAO,kBAAkB,UAClD;AACH,EAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,UAAM,gBAAgB,oBAAoB,MAAM,IAAI;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAChD,WAAO,cAAc,IAAI,YAAU,KAAK,gBAAgB,OAAO,EAAE,CAAC;AAAA,EACpE;AAEF,QAAM,gBAAgBA,QAAO,kBAAkB,UAAU;AACzD,EAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAEhD,WAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,YAAM,gBAAgB,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AACnE,UAAI,eAAe;AACjB,cAAM,IAAI;AAAA,UAAa;AAAA,UACrB;AAAA,QAAoB;AAAA,MACxB;AAAA,IACF,CAAC;AAGD,QAAI,CAAC,KAAK,gBAAgB,OAAO,EAAE,GAAG;AACpC,YAAM,YAAY,IAAIA,QAAO,YAAY,OAAO,UAAU,CAAC;AAC3D,WAAK,SAAS,OAAO,EAAE,IAAI;AAC3B,WAAK,gBAAgB,UAAU,EAAE,IAAI;AACrC,eAAS;AAAA,IACX;AACA,kBAAc,MAAM,MAAM,CAAC,MAAM,CAAC;AAAA,EACpC;AAEA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,EAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAEhD,qBAAiB,MAAM,MAAM,CAAE,KAAK,SAAS,OAAO,EAAE,KAAK,MAAO,CAAC;AACnE,WAAO,KAAK,gBAAiB,KAAK,SAAS,OAAO,EAAE,IAClD,KAAK,SAAS,OAAO,EAAE,EAAE,KAAK,OAAO,EAAG;AAC1C,WAAO,KAAK,SAAS,OAAO,EAAE;AAAA,EAChC;AAEF,EAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,OAAO,QAAQ;AAC/B,QAAI,KAAK,mBAAmB,UAAU;AACpC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,MAAmB;AAAA,IACvB;AACA,UAAM,UAAU,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AAC1C,QAAI,QAAQ,WAAW,KACnB,CAAC,QAAQ,CAAC,EAAE,UAAU,EAAE,KAAK,OAAK,MAAM,KAAK,GAAG;AAGlD,YAAM,IAAI;AAAA,QACR;AAAA,QAEA;AAAA,MAAmB;AAAA,IACvB;AAEA,UAAM,gBAAgB,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AACnE,QAAI,eAAe;AACjB,YAAM,IAAI;AAAA,QAAa;AAAA,QACrB;AAAA,MAAoB;AAAA,IACxB;AAEA,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAChD,UAAM,YAAY,KAAK,SAAS,OAAO,EAAE;AACzC,QAAI,WAAW;AAKb,gBAAU,SAAS,KAAK;AAGxB,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,cAAc,IAAI,MAAM,mBAAmB,CAAC;AAAA,MACnD,CAAC;AAAA,IACH,OAAO;AACL,YAAM,YAAY,IAAIA,QAAO,YAAY,CAAC,KAAK,CAAC;AAChD,WAAK,SAAS,OAAO,EAAE,IAAI;AAC3B,WAAK,gBAAgB,UAAU,EAAE,IAAI;AACrC,WAAK,UAAU,SAAS;AAAA,IAC1B;AACA,WAAO,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AAAA,EACtD;AAIF,WAAS,wBAAwB,IAAI,aAAa;AAChD,QAAIC,OAAM,YAAY;AACtB,WAAO,KAAK,GAAG,mBAAmB,CAAC,CAAC,EAAE,QAAQ,gBAAc;AAC1D,YAAM,iBAAiB,GAAG,gBAAgB,UAAU;AACpD,YAAM,iBAAiB,GAAG,SAAS,eAAe,EAAE;AACpD,MAAAA,OAAMA,KAAI;AAAA,QAAQ,IAAI,OAAO,eAAe,IAAI,GAAG;AAAA,QACjD,eAAe;AAAA,MAAE;AAAA,IACrB,CAAC;AACD,WAAO,IAAI,sBAAsB;AAAA,MAC/B,MAAM,YAAY;AAAA,MAClB,KAAAA;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,wBAAwB,IAAI,aAAa;AAChD,QAAIA,OAAM,YAAY;AACtB,WAAO,KAAK,GAAG,mBAAmB,CAAC,CAAC,EAAE,QAAQ,gBAAc;AAC1D,YAAM,iBAAiB,GAAG,gBAAgB,UAAU;AACpD,YAAM,iBAAiB,GAAG,SAAS,eAAe,EAAE;AACpD,MAAAA,OAAMA,KAAI;AAAA,QAAQ,IAAI,OAAO,eAAe,IAAI,GAAG;AAAA,QACjD,eAAe;AAAA,MAAE;AAAA,IACrB,CAAC;AACD,WAAO,IAAI,sBAAsB;AAAA,MAC/B,MAAM,YAAY;AAAA,MAClB,KAAAA;AAAA,IACF,CAAC;AAAA,EACH;AACA,GAAC,eAAe,cAAc,EAAE,QAAQ,SAAS,QAAQ;AACvD,UAAM,eAAeD,QAAO,kBAAkB,UAAU,MAAM;AAC9D,UAAM,YAAY,EAAC,CAAC,MAAM,IAAI;AAC5B,YAAM,OAAO;AACb,YAAM,eAAe,UAAU,UAC3B,OAAO,UAAU,CAAC,MAAM;AAC5B,UAAI,cAAc;AAChB,eAAO,aAAa,MAAM,MAAM;AAAA,UAC9B,CAAC,gBAAgB;AACf,kBAAM,OAAO,wBAAwB,MAAM,WAAW;AACtD,iBAAK,CAAC,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC;AAAA,UAC5B;AAAA,UACA,CAAC,QAAQ;AACP,gBAAI,KAAK,CAAC,GAAG;AACX,mBAAK,CAAC,EAAE,MAAM,MAAM,GAAG;AAAA,YACzB;AAAA,UACF;AAAA,UAAG,UAAU,CAAC;AAAA,QAChB,CAAC;AAAA,MACH;AACA,aAAO,aAAa,MAAM,MAAM,SAAS,EACtC,KAAK,iBAAe,wBAAwB,MAAM,WAAW,CAAC;AAAA,IACnE,EAAC;AACD,IAAAA,QAAO,kBAAkB,UAAU,MAAM,IAAI,UAAU,MAAM;AAAA,EAC/D,CAAC;AAED,QAAM,0BACFA,QAAO,kBAAkB,UAAU;AACvC,EAAAA,QAAO,kBAAkB,UAAU,sBACjC,SAAS,sBAAsB;AAC7B,QAAI,CAAC,UAAU,UAAU,CAAC,UAAU,CAAC,EAAE,MAAM;AAC3C,aAAO,wBAAwB,MAAM,MAAM,SAAS;AAAA,IACtD;AACA,cAAU,CAAC,IAAI,wBAAwB,MAAM,UAAU,CAAC,CAAC;AACzD,WAAO,wBAAwB,MAAM,MAAM,SAAS;AAAA,EACtD;AAIF,QAAM,uBAAuB,OAAO;AAAA,IAClCA,QAAO,kBAAkB;AAAA,IAAW;AAAA,EAAkB;AACxD,SAAO;AAAA,IAAeA,QAAO,kBAAkB;AAAA,IAC7C;AAAA,IAAoB;AAAA,MAClB,MAAM;AACJ,cAAM,cAAc,qBAAqB,IAAI,MAAM,IAAI;AACvD,YAAI,YAAY,SAAS,IAAI;AAC3B,iBAAO;AAAA,QACT;AACA,eAAO,wBAAwB,MAAM,WAAW;AAAA,MAClD;AAAA,IACF;AAAA,EAAC;AAEH,EAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,QAAQ;AAC3B,QAAI,KAAK,mBAAmB,UAAU;AACpC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,MAAmB;AAAA,IACvB;AAGA,QAAI,CAAC,OAAO,KAAK;AACf,YAAM,IAAI,aAAa,0FAC2B,WAAW;AAAA,IAC/D;AACA,UAAM,UAAU,OAAO,QAAQ;AAC/B,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI;AAAA,QAAa;AAAA,QACrB;AAAA,MAAoB;AAAA,IACxB;AAGA,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,QAAI;AACJ,WAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,cAAY;AAC7C,YAAM,WAAW,KAAK,SAAS,QAAQ,EAAE,UAAU,EAChD,KAAK,WAAS,OAAO,UAAU,KAAK;AACvC,UAAI,UAAU;AACZ,iBAAS,KAAK,SAAS,QAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAED,QAAI,QAAQ;AACV,UAAI,OAAO,UAAU,EAAE,WAAW,GAAG;AAGnC,aAAK,aAAa,KAAK,gBAAgB,OAAO,EAAE,CAAC;AAAA,MACnD,OAAO;AAEL,eAAO,YAAY,OAAO,KAAK;AAAA,MACjC;AACA,WAAK,cAAc,IAAI,MAAM,mBAAmB,CAAC;AAAA,IACnD;AAAA,EACF;AACJ;AAEO,SAAS,mBAAmBA,SAAQ,gBAAgB;AACzD,MAAI,CAACA,QAAO,qBAAqBA,QAAO,yBAAyB;AAE/D,IAAAA,QAAO,oBAAoBA,QAAO;AAAA,EACpC;AACA,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAGA,MAAI,eAAe,UAAU,IAAI;AAC/B,KAAC,uBAAuB,wBAAwB,iBAAiB,EAC9D,QAAQ,SAAS,QAAQ;AACxB,YAAM,eAAeA,QAAO,kBAAkB,UAAU,MAAM;AAC9D,YAAM,YAAY,EAAC,CAAC,MAAM,IAAI;AAC5B,kBAAU,CAAC,IAAI,KAAM,WAAW,oBAC9BA,QAAO,kBACPA,QAAO,uBAAuB,UAAU,CAAC,CAAC;AAC5C,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C,EAAC;AACD,MAAAA,QAAO,kBAAkB,UAAU,MAAM,IAAI,UAAU,MAAM;AAAA,IAC/D,CAAC;AAAA,EACL;AACF;AAGO,SAAS,qBAAqBA,SAAQ,gBAAgB;AAC3D,EAAM,wBAAwBA,SAAQ,qBAAqB,OAAK;AAC9D,UAAM,KAAK,EAAE;AACb,QAAI,eAAe,UAAU,MAAO,GAAG,oBACnC,GAAG,iBAAiB,EAAE,iBAAiB,UAAW;AACpD,UAAI,GAAG,mBAAmB,UAAU;AAClC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;;;AEznBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAAE;AAAA,EAAA,mBAAAC;AAAA,EAAA,0BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;;;ACYO,SAASC,kBAAiBC,SAAQ,gBAAgB;AACvD,QAAM,YAAYA,WAAUA,QAAO;AACnC,QAAM,mBAAmBA,WAAUA,QAAO;AAE1C,YAAU,eAAe,SAAS,aAAa,WAAW,SAAS;AAEjE,IAAM;AAAA,MAAW;AAAA,MACf;AAAA,IAAqC;AACvC,cAAU,aAAa,aAAa,WAAW,EAAE,KAAK,WAAW,OAAO;AAAA,EAC1E;AAEA,MAAI,EAAE,eAAe,UAAU,MAC3B,qBAAqB,UAAU,aAAa,wBAAwB,IAAI;AAC1E,UAAM,QAAQ,SAAS,KAAK,GAAG,GAAG;AAChC,UAAI,KAAK,OAAO,EAAE,KAAK,MAAM;AAC3B,YAAI,CAAC,IAAI,IAAI,CAAC;AACd,eAAO,IAAI,CAAC;AAAA,MACd;AAAA,IACF;AAEA,UAAM,qBAAqB,UAAU,aAAa,aAChD,KAAK,UAAU,YAAY;AAC7B,cAAU,aAAa,eAAe,SAAS,GAAG;AAChD,UAAI,OAAO,MAAM,YAAY,OAAO,EAAE,UAAU,UAAU;AACxD,YAAI,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAChC,cAAM,EAAE,OAAO,mBAAmB,oBAAoB;AACtD,cAAM,EAAE,OAAO,oBAAoB,qBAAqB;AAAA,MAC1D;AACA,aAAO,mBAAmB,CAAC;AAAA,IAC7B;AAEA,QAAI,oBAAoB,iBAAiB,UAAU,aAAa;AAC9D,YAAM,oBAAoB,iBAAiB,UAAU;AACrD,uBAAiB,UAAU,cAAc,WAAW;AAClD,cAAM,MAAM,kBAAkB,MAAM,MAAM,SAAS;AACnD,cAAM,KAAK,sBAAsB,iBAAiB;AAClD,cAAM,KAAK,uBAAuB,kBAAkB;AACpD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,oBAAoB,iBAAiB,UAAU,kBAAkB;AACnE,YAAM,yBACJ,iBAAiB,UAAU;AAC7B,uBAAiB,UAAU,mBAAmB,SAAS,GAAG;AACxD,YAAI,KAAK,SAAS,WAAW,OAAO,MAAM,UAAU;AAClD,cAAI,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAChC,gBAAM,GAAG,mBAAmB,oBAAoB;AAChD,gBAAM,GAAG,oBAAoB,qBAAqB;AAAA,QACpD;AACA,eAAO,uBAAuB,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACF;;;ACxDO,SAAS,oBAAoBC,SAAQ,sBAAsB;AAChE,MAAIA,QAAO,UAAU,gBACnB,qBAAqBA,QAAO,UAAU,cAAc;AACpD;AAAA,EACF;AACA,MAAI,CAAEA,QAAO,UAAU,cAAe;AACpC;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,aAAa,kBAC5B,SAAS,gBAAgB,aAAa;AACpC,QAAI,EAAE,eAAe,YAAY,QAAQ;AACvC,YAAM,MAAM,IAAI,aAAa,wDACC;AAC9B,UAAI,OAAO;AAEX,UAAI,OAAO;AACX,aAAO,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,QAAI,YAAY,UAAU,MAAM;AAC9B,kBAAY,QAAQ,EAAC,aAAa,qBAAoB;AAAA,IACxD,OAAO;AACL,kBAAY,MAAM,cAAc;AAAA,IAClC;AACA,WAAOA,QAAO,UAAU,aAAa,aAAa,WAAW;AAAA,EAC/D;AACJ;;;AFrBO,SAASC,aAAYC,SAAQ;AAClC,MAAI,OAAOA,YAAW,YAAYA,QAAO,iBACpC,cAAcA,QAAO,cAAc,aACpC,EAAE,iBAAiBA,QAAO,cAAc,YAAY;AACtD,WAAO,eAAeA,QAAO,cAAc,WAAW,eAAe;AAAA,MACnE,MAAM;AACJ,eAAO,EAAC,UAAU,KAAK,SAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAASC,oBAAmBD,SAAQ,gBAAgB;AACzD,MAAI,OAAOA,YAAW,YAClB,EAAEA,QAAO,qBAAqBA,QAAO,uBAAuB;AAC9D;AAAA,EACF;AACA,MAAI,CAACA,QAAO,qBAAqBA,QAAO,sBAAsB;AAE5D,IAAAA,QAAO,oBAAoBA,QAAO;AAAA,EACpC;AAEA,MAAI,eAAe,UAAU,IAAI;AAE/B,KAAC,uBAAuB,wBAAwB,iBAAiB,EAC9D,QAAQ,SAAS,QAAQ;AACxB,YAAM,eAAeA,QAAO,kBAAkB,UAAU,MAAM;AAC9D,YAAM,YAAY,EAAC,CAAC,MAAM,IAAI;AAC5B,kBAAU,CAAC,IAAI,KAAM,WAAW,oBAC9BA,QAAO,kBACPA,QAAO,uBAAuB,UAAU,CAAC,CAAC;AAC5C,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C,EAAC;AACD,MAAAA,QAAO,kBAAkB,UAAU,MAAM,IAAI,UAAU,MAAM;AAAA,IAC/D,CAAC;AAAA,EACL;AAEA,QAAM,mBAAmB;AAAA,IACvB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EACnB;AAEA,QAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,EAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,UAAM,CAAC,UAAU,QAAQ,KAAK,IAAI;AAClC,WAAO,eAAe,MAAM,MAAM,CAAC,YAAY,IAAI,CAAC,EACjD,KAAK,WAAS;AACb,UAAI,eAAe,UAAU,MAAM,CAAC,QAAQ;AAG1C,YAAI;AACF,gBAAM,QAAQ,UAAQ;AACpB,iBAAK,OAAO,iBAAiB,KAAK,IAAI,KAAK,KAAK;AAAA,UAClD,CAAC;AAAA,QACH,SAAS,GAAG;AACV,cAAI,EAAE,SAAS,aAAa;AAC1B,kBAAM;AAAA,UACR;AAEA,gBAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,kBAAM,IAAI,GAAG,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,cACnC,MAAM,iBAAiB,KAAK,IAAI,KAAK,KAAK;AAAA,YAC5C,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC,EACA,KAAK,QAAQ,KAAK;AAAA,EACvB;AACF;AAEO,SAAS,mBAAmBA,SAAQ;AACzC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,qBACvCA,QAAO,eAAe;AACxB;AAAA,EACF;AACA,MAAIA,QAAO,gBAAgB,cAAcA,QAAO,aAAa,WAAW;AACtE;AAAA,EACF;AACA,QAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,MAAI,gBAAgB;AAClB,IAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,YAAM,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAC7C,cAAQ,QAAQ,YAAU,OAAO,MAAM,IAAI;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,MAAI,cAAc;AAChB,IAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,YAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,aAAO,MAAM;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACA,EAAAA,QAAO,aAAa,UAAU,WAAW,SAAS,WAAW;AAC3D,WAAO,KAAK,QAAQ,KAAK,IAAI,SAAS,KAAK,KAAK,IAC9C,QAAQ,QAAQ,oBAAI,IAAI,CAAC;AAAA,EAC7B;AACF;AAEO,SAAS,qBAAqBA,SAAQ;AAC3C,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,qBACvCA,QAAO,eAAe;AACxB;AAAA,EACF;AACA,MAAIA,QAAO,gBAAgB,cAAcA,QAAO,eAAe,WAAW;AACxE;AAAA,EACF;AACA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,MAAI,kBAAkB;AACpB,IAAAA,QAAO,kBAAkB,UAAU,eAAe,SAAS,eAAe;AACxE,YAAM,YAAY,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACjD,gBAAU,QAAQ,cAAY,SAAS,MAAM,IAAI;AACjD,aAAO;AAAA,IACT;AAAA,EACF;AACA,EAAM,wBAAwBA,SAAQ,SAAS,OAAK;AAClD,MAAE,SAAS,MAAM,EAAE;AACnB,WAAO;AAAA,EACT,CAAC;AACD,EAAAA,QAAO,eAAe,UAAU,WAAW,SAAS,WAAW;AAC7D,WAAO,KAAK,IAAI,SAAS,KAAK,KAAK;AAAA,EACrC;AACF;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,CAACA,QAAO,qBACR,kBAAkBA,QAAO,kBAAkB,WAAW;AACxD;AAAA,EACF;AACA,EAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,IAAM,WAAW,gBAAgB,aAAa;AAC9C,SAAK,WAAW,EAAE,QAAQ,YAAU;AAClC,UAAI,OAAO,SAAS,OAAO,UAAU,EAAE,SAAS,OAAO,KAAK,GAAG;AAC7D,aAAK,YAAY,MAAM;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AACJ;AAEO,SAAS,mBAAmBA,SAAQ;AAGzC,MAAIA,QAAO,eAAe,CAACA,QAAO,gBAAgB;AAChD,IAAAA,QAAO,iBAAiBA,QAAO;AAAA,EACjC;AACF;AAEO,SAAS,mBAAmBA,SAAQ;AAIzC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,oBAAoB;AAC7D;AAAA,EACF;AACA,QAAM,qBAAqBA,QAAO,kBAAkB,UAAU;AAC9D,MAAI,oBAAoB;AACtB,IAAAA,QAAO,kBAAkB,UAAU,iBACjC,SAAS,iBAAiB;AACxB,WAAK,wBAAwB,CAAC;AAE9B,UAAI,gBAAgB,UAAU,CAAC,KAAK,UAAU,CAAC,EAAE;AACjD,UAAI,kBAAkB,QAAW;AAC/B,wBAAgB,CAAC;AAAA,MACnB;AACA,sBAAgB,CAAC,GAAG,aAAa;AACjC,YAAM,qBAAqB,cAAc,SAAS;AAClD,UAAI,oBAAoB;AAEtB,sBAAc,QAAQ,CAAC,kBAAkB;AACvC,cAAI,SAAS,eAAe;AAC1B,kBAAM,WAAW;AACjB,gBAAI,CAAC,SAAS,KAAK,cAAc,GAAG,GAAG;AACrC,oBAAM,IAAI,UAAU,6BAA6B;AAAA,YACnD;AAAA,UACF;AACA,cAAI,2BAA2B,eAAe;AAC5C,gBAAI,EAAE,WAAW,cAAc,qBAAqB,KAAK,IAAM;AAC7D,oBAAM,IAAI,WAAW,yCAAyC;AAAA,YAChE;AAAA,UACF;AACA,cAAI,kBAAkB,eAAe;AACnC,gBAAI,EAAE,WAAW,cAAc,YAAY,KAAK,IAAI;AAClD,oBAAM,IAAI,WAAW,8BAA8B;AAAA,YACrD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,cAAc,mBAAmB,MAAM,MAAM,SAAS;AAC5D,UAAI,oBAAoB;AAQtB,cAAM,EAAC,OAAM,IAAI;AACjB,cAAM,SAAS,OAAO,cAAc;AACpC,YAAI,EAAE,eAAe;AAAA,QAEhB,OAAO,UAAU,WAAW,KAC5B,OAAO,KAAK,OAAO,UAAU,CAAC,CAAC,EAAE,WAAW,GAAI;AACnD,iBAAO,YAAY;AACnB,iBAAO,gBAAgB;AACvB,eAAK,sBAAsB;AAAA,YAAK,OAAO,cAAc,MAAM,EACxD,KAAK,MAAM;AACV,qBAAO,OAAO;AAAA,YAChB,CAAC,EAAE,MAAM,MAAM;AACb,qBAAO,OAAO;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACJ;AACF;AAEO,SAAS,kBAAkBA,SAAQ;AACxC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,eAAe;AACxD;AAAA,EACF;AACA,QAAM,oBAAoBA,QAAO,aAAa,UAAU;AACxD,MAAI,mBAAmB;AACrB,IAAAA,QAAO,aAAa,UAAU,gBAC5B,SAAS,gBAAgB;AACvB,YAAM,SAAS,kBAAkB,MAAM,MAAM,SAAS;AACtD,UAAI,EAAE,eAAe,SAAS;AAC5B,eAAO,YAAY,CAAC,EAAE,OAAO,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA,EACJ;AACF;AAEO,SAAS,gBAAgBA,SAAQ;AAItC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,oBAAoB;AAC7D;AAAA,EACF;AACA,QAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,EAAAA,QAAO,kBAAkB,UAAU,cAAc,SAAS,cAAc;AACtE,QAAI,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;AACnE,aAAO,QAAQ,IAAI,KAAK,qBAAqB,EAC1C,KAAK,MAAM;AACV,eAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,MAC9C,CAAC,EACA,QAAQ,MAAM;AACb,aAAK,wBAAwB,CAAC;AAAA,MAChC,CAAC;AAAA,IACL;AACA,WAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,EAC9C;AACF;AAEO,SAAS,iBAAiBA,SAAQ;AAIvC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,oBAAoB;AAC7D;AAAA,EACF;AACA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,EAAAA,QAAO,kBAAkB,UAAU,eAAe,SAAS,eAAe;AACxE,QAAI,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;AACnE,aAAO,QAAQ,IAAI,KAAK,qBAAqB,EAC1C,KAAK,MAAM;AACV,eAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,MAC/C,CAAC,EACA,QAAQ,MAAM;AACb,aAAK,wBAAwB,CAAC;AAAA,MAChC,CAAC;AAAA,IACL;AACA,WAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,EAC/C;AACF;;;AG3SA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAAE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAUO,SAAS,oBAAoBC,SAAQ;AAC1C,MAAI,OAAOA,YAAW,YAAY,CAACA,QAAO,mBAAmB;AAC3D;AAAA,EACF;AACA,MAAI,EAAE,qBAAqBA,QAAO,kBAAkB,YAAY;AAC9D,IAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,CAAC;AAAA,MACxB;AACA,aAAO,KAAK;AAAA,IACd;AAAA,EACJ;AACA,MAAI,EAAE,eAAeA,QAAO,kBAAkB,YAAY;AACxD,UAAM,YAAYA,QAAO,kBAAkB,UAAU;AACrD,IAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,CAAC;AAAA,MACxB;AACA,UAAI,CAAC,KAAK,cAAc,SAAS,MAAM,GAAG;AACxC,aAAK,cAAc,KAAK,MAAM;AAAA,MAChC;AAGA,aAAO,eAAe,EAAE,QAAQ,WAAS,UAAU;AAAA,QAAK;AAAA,QAAM;AAAA,QAC5D;AAAA,MAAM,CAAC;AACT,aAAO,eAAe,EAAE,QAAQ,WAAS,UAAU;AAAA,QAAK;AAAA,QAAM;AAAA,QAC5D;AAAA,MAAM,CAAC;AAAA,IACX;AAEA,IAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,UAAU,SAAS;AACnC,UAAI,SAAS;AACX,gBAAQ,QAAQ,CAAC,WAAW;AAC1B,cAAI,CAAC,KAAK,eAAe;AACvB,iBAAK,gBAAgB,CAAC,MAAM;AAAA,UAC9B,WAAW,CAAC,KAAK,cAAc,SAAS,MAAM,GAAG;AAC/C,iBAAK,cAAc,KAAK,MAAM;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,UAAU,MAAM,MAAM,SAAS;AAAA,IACxC;AAAA,EACJ;AACA,MAAI,EAAE,kBAAkBA,QAAO,kBAAkB,YAAY;AAC3D,IAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,CAAC;AAAA,MACxB;AACA,YAAM,QAAQ,KAAK,cAAc,QAAQ,MAAM;AAC/C,UAAI,UAAU,IAAI;AAChB;AAAA,MACF;AACA,WAAK,cAAc,OAAO,OAAO,CAAC;AAClC,YAAM,SAAS,OAAO,UAAU;AAChC,WAAK,WAAW,EAAE,QAAQ,YAAU;AAClC,YAAI,OAAO,SAAS,OAAO,KAAK,GAAG;AACjC,eAAK,YAAY,MAAM;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACJ;AACF;AAEO,SAAS,qBAAqBA,SAAQ;AAC3C,MAAI,OAAOA,YAAW,YAAY,CAACA,QAAO,mBAAmB;AAC3D;AAAA,EACF;AACA,MAAI,EAAE,sBAAsBA,QAAO,kBAAkB,YAAY;AAC/D,IAAAA,QAAO,kBAAkB,UAAU,mBACjC,SAAS,mBAAmB;AAC1B,aAAO,KAAK,iBAAiB,KAAK,iBAAiB,CAAC;AAAA,IACtD;AAAA,EACJ;AACA,MAAI,EAAE,iBAAiBA,QAAO,kBAAkB,YAAY;AAC1D,WAAO,eAAeA,QAAO,kBAAkB,WAAW,eAAe;AAAA,MACvE,MAAM;AACJ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,GAAG;AACL,YAAI,KAAK,cAAc;AACrB,eAAK,oBAAoB,aAAa,KAAK,YAAY;AACvD,eAAK,oBAAoB,SAAS,KAAK,gBAAgB;AAAA,QACzD;AACA,aAAK,iBAAiB,aAAa,KAAK,eAAe,CAAC;AACxD,aAAK,iBAAiB,SAAS,KAAK,mBAAmB,CAAC,MAAM;AAC5D,YAAE,QAAQ,QAAQ,YAAU;AAC1B,gBAAI,CAAC,KAAK,gBAAgB;AACxB,mBAAK,iBAAiB,CAAC;AAAA,YACzB;AACA,gBAAI,KAAK,eAAe,SAAS,MAAM,GAAG;AACxC;AAAA,YACF;AACA,iBAAK,eAAe,KAAK,MAAM;AAC/B,kBAAM,QAAQ,IAAI,MAAM,WAAW;AACnC,kBAAM,SAAS;AACf,iBAAK,cAAc,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,UAAM,2BACJA,QAAO,kBAAkB,UAAU;AACrC,IAAAA,QAAO,kBAAkB,UAAU,uBACjC,SAAS,uBAAuB;AAC9B,YAAM,KAAK;AACX,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAS,GAAG;AACjE,YAAE,QAAQ,QAAQ,YAAU;AAC1B,gBAAI,CAAC,GAAG,gBAAgB;AACtB,iBAAG,iBAAiB,CAAC;AAAA,YACvB;AACA,gBAAI,GAAG,eAAe,QAAQ,MAAM,KAAK,GAAG;AAC1C;AAAA,YACF;AACA,eAAG,eAAe,KAAK,MAAM;AAC7B,kBAAM,QAAQ,IAAI,MAAM,WAAW;AACnC,kBAAM,SAAS;AACf,eAAG,cAAc,KAAK;AAAA,UACxB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO,yBAAyB,MAAM,IAAI,SAAS;AAAA,IACrD;AAAA,EACJ;AACF;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,OAAOA,YAAW,YAAY,CAACA,QAAO,mBAAmB;AAC3D;AAAA,EACF;AACA,QAAM,YAAYA,QAAO,kBAAkB;AAC3C,QAAM,kBAAkB,UAAU;AAClC,QAAM,mBAAmB,UAAU;AACnC,QAAM,sBAAsB,UAAU;AACtC,QAAM,uBAAuB,UAAU;AACvC,QAAM,kBAAkB,UAAU;AAElC,YAAU,cACR,SAAS,YAAY,iBAAiB,iBAAiB;AACrD,UAAM,UAAW,UAAU,UAAU,IAAK,UAAU,CAAC,IAAI,UAAU,CAAC;AACpE,UAAM,UAAU,gBAAgB,MAAM,MAAM,CAAC,OAAO,CAAC;AACrD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEF,YAAU,eACR,SAAS,aAAa,iBAAiB,iBAAiB;AACtD,UAAM,UAAW,UAAU,UAAU,IAAK,UAAU,CAAC,IAAI,UAAU,CAAC;AACpE,UAAM,UAAU,iBAAiB,MAAM,MAAM,CAAC,OAAO,CAAC;AACtD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEF,MAAI,eAAe,SAAS,aAAa,iBAAiB,iBAAiB;AACzE,UAAM,UAAU,oBAAoB,MAAM,MAAM,CAAC,WAAW,CAAC;AAC7D,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,YAAU,sBAAsB;AAEhC,iBAAe,SAAS,aAAa,iBAAiB,iBAAiB;AACrE,UAAM,UAAU,qBAAqB,MAAM,MAAM,CAAC,WAAW,CAAC;AAC9D,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,YAAU,uBAAuB;AAEjC,iBAAe,SAAS,WAAW,iBAAiB,iBAAiB;AACnE,UAAM,UAAU,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC;AACvD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,YAAU,kBAAkB;AAC9B;AAEO,SAASC,kBAAiBD,SAAQ;AACvC,QAAM,YAAYA,WAAUA,QAAO;AAEnC,MAAI,UAAU,gBAAgB,UAAU,aAAa,cAAc;AAEjE,UAAM,eAAe,UAAU;AAC/B,UAAM,gBAAgB,aAAa,aAAa,KAAK,YAAY;AACjE,cAAU,aAAa,eAAe,CAAC,gBAAgB;AACrD,aAAO,cAAc,gBAAgB,WAAW,CAAC;AAAA,IACnD;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,gBAAgB,UAAU,gBACvC,UAAU,aAAa,cAAc;AACrC,cAAU,gBAAe,SAAS,aAAa,aAAa,IAAI,OAAO;AACrE,gBAAU,aAAa,aAAa,WAAW,EAC5C,KAAK,IAAI,KAAK;AAAA,IACnB,GAAE,KAAK,SAAS;AAAA,EAClB;AACF;AAEO,SAAS,gBAAgB,aAAa;AAC3C,MAAI,eAAe,YAAY,UAAU,QAAW;AAClD,WAAO,OAAO;AAAA,MAAO,CAAC;AAAA,MACpB;AAAA,MACA,EAAC,OAAa,cAAc,YAAY,KAAK,EAAC;AAAA,IAChD;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,qBAAqBA,SAAQ;AAC3C,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,QAAM,qBAAqBA,QAAO;AAClC,EAAAA,QAAO,oBACL,SAAS,kBAAkB,UAAU,eAAe;AAClD,QAAI,YAAY,SAAS,YAAY;AACnC,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,SAAS,WAAW,QAAQ,KAAK;AACnD,YAAI,SAAS,SAAS,WAAW,CAAC;AAClC,YAAI,OAAO,SAAS,UAAa,OAAO,KAAK;AAC3C,UAAM,WAAW,oBAAoB,mBAAmB;AACxD,mBAAS,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAC1C,iBAAO,OAAO,OAAO;AACrB,iBAAO,OAAO;AACd,wBAAc,KAAK,MAAM;AAAA,QAC3B,OAAO;AACL,wBAAc,KAAK,SAAS,WAAW,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF;AACA,eAAS,aAAa;AAAA,IACxB;AACA,WAAO,IAAI,mBAAmB,UAAU,aAAa;AAAA,EACvD;AACF,EAAAA,QAAO,kBAAkB,YAAY,mBAAmB;AAExD,MAAI,yBAAyB,oBAAoB;AAC/C,WAAO,eAAeA,QAAO,mBAAmB,uBAAuB;AAAA,MACrE,MAAM;AACJ,eAAO,mBAAmB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,0BAA0BA,SAAQ;AAEhD,MAAI,OAAOA,YAAW,YAAYA,QAAO,iBACrC,cAAcA,QAAO,cAAc,aACnC,EAAE,iBAAiBA,QAAO,cAAc,YAAY;AACtD,WAAO,eAAeA,QAAO,cAAc,WAAW,eAAe;AAAA,MACnE,MAAM;AACJ,eAAO,EAAC,UAAU,KAAK,SAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,sBAAsBA,SAAQ;AAC5C,QAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,EAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,cAAc;AACjC,QAAI,cAAc;AAChB,UAAI,OAAO,aAAa,wBAAwB,aAAa;AAE3D,qBAAa,sBACX,CAAC,CAAC,aAAa;AAAA,MACnB;AACA,YAAM,mBAAmB,KAAK,gBAAgB,EAAE,KAAK,iBACnD,YAAY,SAAS,MAAM,SAAS,OAAO;AAC7C,UAAI,aAAa,wBAAwB,SAAS,kBAAkB;AAClE,YAAI,iBAAiB,cAAc,YAAY;AAC7C,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF,WAAW,iBAAiB,cAAc,YAAY;AACpD,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,WAAW,aAAa,wBAAwB,QAC5C,CAAC,kBAAkB;AACrB,aAAK,eAAe,SAAS,EAAC,WAAW,WAAU,CAAC;AAAA,MACtD;AAEA,UAAI,OAAO,aAAa,wBAAwB,aAAa;AAE3D,qBAAa,sBACX,CAAC,CAAC,aAAa;AAAA,MACnB;AACA,YAAM,mBAAmB,KAAK,gBAAgB,EAAE,KAAK,iBACnD,YAAY,SAAS,MAAM,SAAS,OAAO;AAC7C,UAAI,aAAa,wBAAwB,SAAS,kBAAkB;AAClE,YAAI,iBAAiB,cAAc,YAAY;AAC7C,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF,WAAW,iBAAiB,cAAc,YAAY;AACpD,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,WAAW,aAAa,wBAAwB,QAC5C,CAAC,kBAAkB;AACrB,aAAK,eAAe,SAAS,EAAC,WAAW,WAAU,CAAC;AAAA,MACtD;AAAA,IACF;AACA,WAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,EAC9C;AACJ;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,OAAOA,YAAW,YAAYA,QAAO,cAAc;AACrD;AAAA,EACF;AACA,EAAAA,QAAO,eAAeA,QAAO;AAC/B;;;AC9VA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,iBAAqB;AAGd,SAAS,oBAAoBE,SAAQ;AAG1C,MAAI,CAACA,QAAO,mBAAoBA,QAAO,mBAAmB,gBACtDA,QAAO,gBAAgB,WAAY;AACrC;AAAA,EACF;AAEA,QAAM,wBAAwBA,QAAO;AACrC,EAAAA,QAAO,kBAAkB,SAAS,gBAAgB,MAAM;AAEtD,QAAI,OAAO,SAAS,YAAY,KAAK,aACjC,KAAK,UAAU,QAAQ,IAAI,MAAM,GAAG;AACtC,aAAO,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AACtC,WAAK,YAAY,KAAK,UAAU,UAAU,CAAC;AAAA,IAC7C;AAEA,QAAI,KAAK,aAAa,KAAK,UAAU,QAAQ;AAE3C,YAAM,kBAAkB,IAAI,sBAAsB,IAAI;AACtD,YAAM,kBAAkB,WAAAC,QAAS,eAAe,KAAK,SAAS;AAC9D,iBAAW,OAAO,iBAAiB;AACjC,YAAI,EAAE,OAAO,kBAAkB;AAC7B,iBAAO;AAAA,YAAe;AAAA,YAAiB;AAAA,YACrC,EAAC,OAAO,gBAAgB,GAAG,EAAC;AAAA,UAAC;AAAA,QACjC;AAAA,MACF;AAGA,sBAAgB,SAAS,SAAS,SAAS;AACzC,eAAO;AAAA,UACL,WAAW,gBAAgB;AAAA,UAC3B,QAAQ,gBAAgB;AAAA,UACxB,eAAe,gBAAgB;AAAA,UAC/B,kBAAkB,gBAAgB;AAAA,QACpC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,IAAI,sBAAsB,IAAI;AAAA,EACvC;AACA,EAAAD,QAAO,gBAAgB,YAAY,sBAAsB;AAIzD,EAAM,wBAAwBA,SAAQ,gBAAgB,OAAK;AACzD,QAAI,EAAE,WAAW;AACf,aAAO,eAAe,GAAG,aAAa;AAAA,QACpC,OAAO,IAAIA,QAAO,gBAAgB,EAAE,SAAS;AAAA,QAC7C,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,iCAAiCA,SAAQ;AACvD,MAAI,CAACA,QAAO,mBAAoBA,QAAO,mBAAmB,mBACtDA,QAAO,gBAAgB,WAAY;AACrC;AAAA,EACF;AAIA,EAAM,wBAAwBA,SAAQ,gBAAgB,OAAK;AACzD,QAAI,EAAE,WAAW;AACf,YAAM,kBAAkB,WAAAC,QAAS,eAAe,EAAE,UAAU,SAAS;AACrE,UAAI,gBAAgB,SAAS,SAAS;AAGpC,UAAE,UAAU,gBAAgB;AAAA,UAC1B,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACL,EAAE,gBAAgB,YAAY,EAAE;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,mBAAmBD,SAAQ,gBAAgB;AACzD,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,MAAI,EAAE,UAAUA,QAAO,kBAAkB,YAAY;AACnD,WAAO,eAAeA,QAAO,kBAAkB,WAAW,QAAQ;AAAA,MAChE,MAAM;AACJ,eAAO,OAAO,KAAK,UAAU,cAAc,OAAO,KAAK;AAAA,MACzD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,oBAAoB,SAAS,aAAa;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,KAAK;AACpC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,WAAAC,QAAS,cAAc,YAAY,GAAG;AACvD,aAAS,MAAM;AACf,WAAO,SAAS,KAAK,kBAAgB;AACnC,YAAM,QAAQ,WAAAA,QAAS,WAAW,YAAY;AAC9C,aAAO,SAAS,MAAM,SAAS,iBACxB,MAAM,SAAS,QAAQ,MAAM,MAAM;AAAA,IAC5C,CAAC;AAAA,EACH;AAEA,QAAM,0BAA0B,SAAS,aAAa;AAEpD,UAAM,QAAQ,YAAY,IAAI,MAAM,iCAAiC;AACrE,QAAI,UAAU,QAAQ,MAAM,SAAS,GAAG;AACtC,aAAO;AAAA,IACT;AACA,UAAM,UAAU,SAAS,MAAM,CAAC,GAAG,EAAE;AAErC,WAAO,YAAY,UAAU,KAAK;AAAA,EACpC;AAEA,QAAM,2BAA2B,SAAS,iBAAiB;AAKzD,QAAI,wBAAwB;AAC5B,QAAI,eAAe,YAAY,WAAW;AACxC,UAAI,eAAe,UAAU,IAAI;AAC/B,YAAI,oBAAoB,IAAI;AAG1B,kCAAwB;AAAA,QAC1B,OAAO;AAGL,kCAAwB;AAAA,QAC1B;AAAA,MACF,WAAW,eAAe,UAAU,IAAI;AAKtC,gCACE,eAAe,YAAY,KAAK,QAAQ;AAAA,MAC5C,OAAO;AAEL,gCAAwB;AAAA,MAC1B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,SAAS,aAAa,iBAAiB;AAG/D,QAAI,iBAAiB;AAKrB,QAAI,eAAe,YAAY,aACvB,eAAe,YAAY,IAAI;AACrC,uBAAiB;AAAA,IACnB;AAEA,UAAM,QAAQ,WAAAA,QAAS;AAAA,MAAY,YAAY;AAAA,MAC7C;AAAA,IAAqB;AACvB,QAAI,MAAM,SAAS,GAAG;AACpB,uBAAiB,SAAS,MAAM,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;AAAA,IACtD,WAAW,eAAe,YAAY,aAC1B,oBAAoB,IAAI;AAIlC,uBAAiB;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAEA,QAAM,2BACFD,QAAO,kBAAkB,UAAU;AACvC,EAAAA,QAAO,kBAAkB,UAAU,uBACjC,SAAS,uBAAuB;AAC9B,SAAK,QAAQ;AAIb,QAAI,eAAe,YAAY,YAAY,eAAe,WAAW,IAAI;AACvE,YAAM,EAAC,aAAY,IAAI,KAAK,iBAAiB;AAC7C,UAAI,iBAAiB,UAAU;AAC7B,eAAO,eAAe,MAAM,QAAQ;AAAA,UAClC,MAAM;AACJ,mBAAO,OAAO,KAAK,UAAU,cAAc,OAAO,KAAK;AAAA,UACzD;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,kBAAkB,UAAU,CAAC,CAAC,GAAG;AAEnC,YAAM,YAAY,wBAAwB,UAAU,CAAC,CAAC;AAGtD,YAAM,aAAa,yBAAyB,SAAS;AAGrD,YAAM,YAAY,kBAAkB,UAAU,CAAC,GAAG,SAAS;AAG3D,UAAI;AACJ,UAAI,eAAe,KAAK,cAAc,GAAG;AACvC,yBAAiB,OAAO;AAAA,MAC1B,WAAW,eAAe,KAAK,cAAc,GAAG;AAC9C,yBAAiB,KAAK,IAAI,YAAY,SAAS;AAAA,MACjD,OAAO;AACL,yBAAiB,KAAK,IAAI,YAAY,SAAS;AAAA,MACjD;AAIA,YAAM,OAAO,CAAC;AACd,aAAO,eAAe,MAAM,kBAAkB;AAAA,QAC5C,MAAM;AACJ,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,WAAK,QAAQ;AAAA,IACf;AAEA,WAAO,yBAAyB,MAAM,MAAM,SAAS;AAAA,EACvD;AACJ;AAEO,SAAS,uBAAuBA,SAAQ;AAC7C,MAAI,EAAEA,QAAO,qBACT,uBAAuBA,QAAO,kBAAkB,YAAY;AAC9D;AAAA,EACF;AAMA,WAAS,WAAW,IAAI,IAAI;AAC1B,UAAM,sBAAsB,GAAG;AAC/B,OAAG,OAAO,SAAS,OAAO;AACxB,YAAM,OAAO,UAAU,CAAC;AACxB,YAAM,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK;AAChD,UAAI,GAAG,eAAe,UAClB,GAAG,QAAQ,SAAS,GAAG,KAAK,gBAAgB;AAC9C,cAAM,IAAI,UAAU,8CAClB,GAAG,KAAK,iBAAiB,SAAS;AAAA,MACtC;AACA,aAAO,oBAAoB,MAAM,IAAI,SAAS;AAAA,IAChD;AAAA,EACF;AACA,QAAM,wBACJA,QAAO,kBAAkB,UAAU;AACrC,EAAAA,QAAO,kBAAkB,UAAU,oBACjC,SAAS,oBAAoB;AAC3B,UAAM,cAAc,sBAAsB,MAAM,MAAM,SAAS;AAC/D,eAAW,aAAa,IAAI;AAC5B,WAAO;AAAA,EACT;AACF,EAAM,wBAAwBA,SAAQ,eAAe,OAAK;AACxD,eAAW,EAAE,SAAS,EAAE,MAAM;AAC9B,WAAO;AAAA,EACT,CAAC;AACH;AAUO,SAAS,oBAAoBA,SAAQ;AAC1C,MAAI,CAACA,QAAO,qBACR,qBAAqBA,QAAO,kBAAkB,WAAW;AAC3D;AAAA,EACF;AACA,QAAM,QAAQA,QAAO,kBAAkB;AACvC,SAAO,eAAe,OAAO,mBAAmB;AAAA,IAC9C,MAAM;AACJ,aAAO;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,MACZ,EAAE,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACrC;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,SAAO,eAAe,OAAO,2BAA2B;AAAA,IACtD,MAAM;AACJ,aAAO,KAAK,4BAA4B;AAAA,IAC1C;AAAA,IACA,IAAI,IAAI;AACN,UAAI,KAAK,0BAA0B;AACjC,aAAK;AAAA,UAAoB;AAAA,UACvB,KAAK;AAAA,QAAwB;AAC/B,eAAO,KAAK;AAAA,MACd;AACA,UAAI,IAAI;AACN,aAAK;AAAA,UAAiB;AAAA,UACpB,KAAK,2BAA2B;AAAA,QAAE;AAAA,MACtC;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AAED,GAAC,uBAAuB,sBAAsB,EAAE,QAAQ,CAAC,WAAW;AAClE,UAAM,aAAa,MAAM,MAAM;AAC/B,UAAM,MAAM,IAAI,WAAW;AACzB,UAAI,CAAC,KAAK,4BAA4B;AACpC,aAAK,6BAA6B,OAAK;AACrC,gBAAM,KAAK,EAAE;AACb,cAAI,GAAG,yBAAyB,GAAG,iBAAiB;AAClD,eAAG,uBAAuB,GAAG;AAC7B,kBAAM,WAAW,IAAI,MAAM,yBAAyB,CAAC;AACrD,eAAG,cAAc,QAAQ;AAAA,UAC3B;AACA,iBAAO;AAAA,QACT;AACA,aAAK;AAAA,UAAiB;AAAA,UACpB,KAAK;AAAA,QAA0B;AAAA,MACnC;AACA,aAAO,WAAW,MAAM,MAAM,SAAS;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AAEO,SAAS,uBAAuBA,SAAQ,gBAAgB;AAE7D,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AACA,MAAI,eAAe,YAAY,YAAY,eAAe,WAAW,IAAI;AACvE;AAAA,EACF;AACA,MAAI,eAAe,YAAY,YAC3B,eAAe,kBAAkB,MAAM;AACzC;AAAA,EACF;AACA,QAAM,YAAYA,QAAO,kBAAkB,UAAU;AACrD,EAAAA,QAAO,kBAAkB,UAAU,uBACnC,SAAS,qBAAqB,MAAM;AAClC,QAAI,QAAQ,KAAK,OAAO,KAAK,IAAI,QAAQ,wBAAwB,MAAM,IAAI;AACzE,YAAME,OAAM,KAAK,IAAI,MAAM,IAAI,EAAE,OAAO,CAAC,SAAS;AAChD,eAAO,KAAK,KAAK,MAAM;AAAA,MACzB,CAAC,EAAE,KAAK,IAAI;AAEZ,UAAIF,QAAO,yBACP,gBAAgBA,QAAO,uBAAuB;AAChD,kBAAU,CAAC,IAAI,IAAIA,QAAO,sBAAsB;AAAA,UAC9C,MAAM,KAAK;AAAA,UACX,KAAAE;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,aAAK,MAAMA;AAAA,MACb;AAAA,IACF;AACA,WAAO,UAAU,MAAM,MAAM,SAAS;AAAA,EACxC;AACF;AAEO,SAAS,+BAA+BF,SAAQ,gBAAgB;AAKrE,MAAI,EAAEA,QAAO,qBAAqBA,QAAO,kBAAkB,YAAY;AACrE;AAAA,EACF;AACA,QAAM,wBACFA,QAAO,kBAAkB,UAAU;AACvC,MAAI,CAAC,yBAAyB,sBAAsB,WAAW,GAAG;AAChE;AAAA,EACF;AACA,EAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,QAAI,CAAC,UAAU,CAAC,GAAG;AACjB,UAAI,UAAU,CAAC,GAAG;AAChB,kBAAU,CAAC,EAAE,MAAM,IAAI;AAAA,MACzB;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAMA,SAAM,eAAe,YAAY,YAAY,eAAe,UAAU,MAC7D,eAAe,YAAY,aACxB,eAAe,UAAU,MAC5B,eAAe,YAAY,aAC7B,UAAU,CAAC,KAAK,UAAU,CAAC,EAAE,cAAc,IAAI;AACpD,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,sBAAsB,MAAM,MAAM,SAAS;AAAA,EACpD;AACJ;AAIO,SAAS,qCAAqCA,SAAQ,gBAAgB;AAC3E,MAAI,EAAEA,QAAO,qBAAqBA,QAAO,kBAAkB,YAAY;AACrE;AAAA,EACF;AACA,QAAM,4BACFA,QAAO,kBAAkB,UAAU;AACvC,MAAI,CAAC,6BAA6B,0BAA0B,WAAW,GAAG;AACxE;AAAA,EACF;AACA,EAAAA,QAAO,kBAAkB,UAAU,sBACjC,SAAS,sBAAsB;AAC7B,QAAI,OAAO,UAAU,CAAC,KAAK,CAAC;AAC5B,QAAI,OAAO,SAAS,YAAa,KAAK,QAAQ,KAAK,KAAM;AACvD,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AAQA,WAAO,EAAC,MAAM,KAAK,MAAM,KAAK,KAAK,IAAG;AACtC,QAAI,CAAC,KAAK,MAAM;AACd,cAAQ,KAAK,gBAAgB;AAAA,QAC3B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO;AACZ;AAAA,QACF;AACE,eAAK,OAAO;AACZ;AAAA,MACJ;AAAA,IACF;AACA,QAAI,KAAK,OAAQ,KAAK,SAAS,WAAW,KAAK,SAAS,UAAW;AACjE,aAAO,0BAA0B,MAAM,MAAM,CAAC,IAAI,CAAC;AAAA,IACrD;AACA,UAAM,OAAO,KAAK,SAAS,UAAU,KAAK,cAAc,KAAK;AAC7D,WAAO,KAAK,MAAM,IAAI,EACnB,KAAK,OAAK,0BAA0B,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EACzD;AACJ;;;AChcA,UAAqB;AAGd,SAAS,eAAe,EAAC,QAAAG,QAAM,IAAI,CAAC,GAAG,UAAU;AAAA,EACtD,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AACd,GAAG;AAED,QAAMC,WAAgB;AACtB,QAAM,iBAAuB,cAAcD,OAAM;AAEjD,QAAME,WAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,EACF;AAGA,UAAQ,eAAe,SAAS;AAAA,IAC9B,KAAK;AACH,UAAI,CAAC,uBAAc,CAAY,sBAC3B,CAAC,QAAQ,YAAY;AACvB,QAAAD,SAAQ,sDAAsD;AAC9D,eAAOC;AAAA,MACT;AACA,UAAI,eAAe,YAAY,MAAM;AACnC,QAAAD,SAAQ,sDAAsD;AAC9D,eAAOC;AAAA,MACT;AACA,MAAAD,SAAQ,6BAA6B;AAErC,MAAAC,SAAQ,cAAc;AAGtB,MAAW,+BAA+BF,SAAQ,cAAc;AAChE,MAAW,qCAAqCA,SAAQ,cAAc;AAEtE,MAAW,iBAAiBA,SAAQ,cAAc;AAClD,MAAW,gBAAgBA,SAAQ,cAAc;AACjD,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,YAAYA,SAAQ,cAAc;AAC7C,MAAW,wBAAwBA,SAAQ,cAAc;AACzD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD,MAAW,2BAA2BA,SAAQ,cAAc;AAC5D,MAAW,qBAAqBA,SAAQ,cAAc;AAEtD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,iCAAiCA,SAAQ,cAAc;AAClE,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD;AAAA,IACF,KAAK;AACH,UAAI,CAAC,wBAAe,CAAaG,uBAC7B,CAAC,QAAQ,aAAa;AACxB,QAAAF,SAAQ,uDAAuD;AAC/D,eAAOC;AAAA,MACT;AACA,MAAAD,SAAQ,8BAA8B;AAEtC,MAAAC,SAAQ,cAAc;AAGtB,MAAW,+BAA+BF,SAAQ,cAAc;AAChE,MAAW,qCAAqCA,SAAQ,cAAc;AAEtE,MAAYI,kBAAiBJ,SAAQ,cAAc;AACnD,MAAYG,oBAAmBH,SAAQ,cAAc;AACrD,MAAYK,aAAYL,SAAQ,cAAc;AAC9C,MAAY,iBAAiBA,SAAQ,cAAc;AACnD,MAAY,mBAAmBA,SAAQ,cAAc;AACrD,MAAY,qBAAqBA,SAAQ,cAAc;AACvD,MAAY,mBAAmBA,SAAQ,cAAc;AACrD,MAAY,mBAAmBA,SAAQ,cAAc;AACrD,MAAY,kBAAkBA,SAAQ,cAAc;AACpD,MAAY,gBAAgBA,SAAQ,cAAc;AAClD,MAAY,iBAAiBA,SAAQ,cAAc;AAEnD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD;AAAA,IACF,KAAK;AACH,UAAI,CAAC,uBAAc,CAAC,QAAQ,YAAY;AACtC,QAAAC,SAAQ,sDAAsD;AAC9D,eAAOC;AAAA,MACT;AACA,MAAAD,SAAQ,6BAA6B;AAErC,MAAAC,SAAQ,cAAc;AAGtB,MAAW,+BAA+BF,SAAQ,cAAc;AAChE,MAAW,qCAAqCA,SAAQ,cAAc;AAEtE,MAAW,qBAAqBA,SAAQ,cAAc;AACtD,MAAW,sBAAsBA,SAAQ,cAAc;AACvD,MAAW,iBAAiBA,SAAQ,cAAc;AAClD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,qBAAqBA,SAAQ,cAAc;AACtD,MAAW,0BAA0BA,SAAQ,cAAc;AAC3D,MAAWI,kBAAiBJ,SAAQ,cAAc;AAClD,MAAW,iBAAiBA,SAAQ,cAAc;AAElD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,iCAAiCA,SAAQ,cAAc;AAClE,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD;AAAA,IACF;AACE,MAAAC,SAAQ,sBAAsB;AAC9B;AAAA,EACJ;AAEA,SAAOC;AACT;;;AC5HA,IAAM,UACJ,eAAe,EAAC,QAAQ,OAAO,WAAW,cAAc,SAAY,OAAM,CAAC;AAC7E,IAAO,uBAAQ;", "names": ["SDPUtils", "sdp", "window", "window", "window", "sdp", "shimGetUserMedia", "shimOnTrack", "shimPeerConnection", "shimGetUserMedia", "window", "window", "shimOnTrack", "window", "shimPeerConnection", "shimGetUserMedia", "window", "shimGetUserMedia", "window", "SDPUtils", "sdp", "window", "logging", "adapter", "shimPeerConnection", "shimGetUserMedia", "shimOnTrack"]}