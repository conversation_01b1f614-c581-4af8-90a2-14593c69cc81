{"version": 3, "sources": ["../../orderedmap/dist/index.js", "../../prosemirror-model/dist/index.js", "../../prosemirror-transform/dist/index.js", "../../prosemirror-state/dist/index.js"], "sourcesContent": ["// ::- Persistent data structure representing an ordered mapping from\n// strings to values, with some convenient update methods.\nfunction OrderedMap(content) {\n  this.content = content;\n}\n\nOrderedMap.prototype = {\n  constructor: OrderedMap,\n\n  find: function(key) {\n    for (var i = 0; i < this.content.length; i += 2)\n      if (this.content[i] === key) return i\n    return -1\n  },\n\n  // :: (string) → ?any\n  // Retrieve the value stored under `key`, or return undefined when\n  // no such key exists.\n  get: function(key) {\n    var found = this.find(key);\n    return found == -1 ? undefined : this.content[found + 1]\n  },\n\n  // :: (string, any, ?string) → OrderedMap\n  // Create a new map by replacing the value of `key` with a new\n  // value, or adding a binding to the end of the map. If `newKey` is\n  // given, the key of the binding will be replaced with that key.\n  update: function(key, value, newKey) {\n    var self = newKey && newKey != key ? this.remove(newKey) : this;\n    var found = self.find(key), content = self.content.slice();\n    if (found == -1) {\n      content.push(newKey || key, value);\n    } else {\n      content[found + 1] = value;\n      if (newKey) content[found] = newKey;\n    }\n    return new OrderedMap(content)\n  },\n\n  // :: (string) → OrderedMap\n  // Return a map with the given key removed, if it existed.\n  remove: function(key) {\n    var found = this.find(key);\n    if (found == -1) return this\n    var content = this.content.slice();\n    content.splice(found, 2);\n    return new OrderedMap(content)\n  },\n\n  // :: (string, any) → OrderedMap\n  // Add a new key to the start of the map.\n  addToStart: function(key, value) {\n    return new OrderedMap([key, value].concat(this.remove(key).content))\n  },\n\n  // :: (string, any) → OrderedMap\n  // Add a new key to the end of the map.\n  addToEnd: function(key, value) {\n    var content = this.remove(key).content.slice();\n    content.push(key, value);\n    return new OrderedMap(content)\n  },\n\n  // :: (string, string, any) → OrderedMap\n  // Add a key after the given key. If `place` is not found, the new\n  // key is added to the end.\n  addBefore: function(place, key, value) {\n    var without = this.remove(key), content = without.content.slice();\n    var found = without.find(place);\n    content.splice(found == -1 ? content.length : found, 0, key, value);\n    return new OrderedMap(content)\n  },\n\n  // :: ((key: string, value: any))\n  // Call the given function for each key/value pair in the map, in\n  // order.\n  forEach: function(f) {\n    for (var i = 0; i < this.content.length; i += 2)\n      f(this.content[i], this.content[i + 1]);\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a new map by prepending the keys in this map that don't\n  // appear in `map` before the keys in `map`.\n  prepend: function(map) {\n    map = OrderedMap.from(map);\n    if (!map.size) return this\n    return new OrderedMap(map.content.concat(this.subtract(map).content))\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a new map by appending the keys in this map that don't\n  // appear in `map` after the keys in `map`.\n  append: function(map) {\n    map = OrderedMap.from(map);\n    if (!map.size) return this\n    return new OrderedMap(this.subtract(map).content.concat(map.content))\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a map containing all the keys in this map that don't\n  // appear in `map`.\n  subtract: function(map) {\n    var result = this;\n    map = OrderedMap.from(map);\n    for (var i = 0; i < map.content.length; i += 2)\n      result = result.remove(map.content[i]);\n    return result\n  },\n\n  // :: () → Object\n  // Turn ordered map into a plain object.\n  toObject: function() {\n    var result = {};\n    this.forEach(function(key, value) { result[key] = value; });\n    return result\n  },\n\n  // :: number\n  // The amount of keys in this map.\n  get size() {\n    return this.content.length >> 1\n  }\n};\n\n// :: (?union<Object, OrderedMap>) → OrderedMap\n// Return a map with the given content. If null, create an empty\n// map. If given an ordered map, return that map itself. If given an\n// object, create a map from the object's properties.\nOrderedMap.from = function(value) {\n  if (value instanceof OrderedMap) return value\n  var content = [];\n  if (value) for (var prop in value) content.push(prop, value[prop]);\n  return new OrderedMap(content)\n};\n\nexport default OrderedMap;\n", "import OrderedMap from 'orderedmap';\n\nfunction findDiffStart(a, b, pos) {\n    for (let i = 0;; i++) {\n        if (i == a.childCount || i == b.childCount)\n            return a.childCount == b.childCount ? null : pos;\n        let childA = a.child(i), childB = b.child(i);\n        if (childA == childB) {\n            pos += childA.nodeSize;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return pos;\n        if (childA.isText && childA.text != childB.text) {\n            for (let j = 0; childA.text[j] == childB.text[j]; j++)\n                pos++;\n            return pos;\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffStart(childA.content, childB.content, pos + 1);\n            if (inner != null)\n                return inner;\n        }\n        pos += childA.nodeSize;\n    }\n}\nfunction findDiffEnd(a, b, posA, posB) {\n    for (let iA = a.childCount, iB = b.childCount;;) {\n        if (iA == 0 || iB == 0)\n            return iA == iB ? null : { a: posA, b: posB };\n        let childA = a.child(--iA), childB = b.child(--iB), size = childA.nodeSize;\n        if (childA == childB) {\n            posA -= size;\n            posB -= size;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return { a: posA, b: posB };\n        if (childA.isText && childA.text != childB.text) {\n            let same = 0, minSize = Math.min(childA.text.length, childB.text.length);\n            while (same < minSize && childA.text[childA.text.length - same - 1] == childB.text[childB.text.length - same - 1]) {\n                same++;\n                posA--;\n                posB--;\n            }\n            return { a: posA, b: posB };\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffEnd(childA.content, childB.content, posA - 1, posB - 1);\n            if (inner)\n                return inner;\n        }\n        posA -= size;\n        posB -= size;\n    }\n}\n\n/**\nA fragment represents a node's collection of child nodes.\n\nLike nodes, fragments are persistent data structures, and you\nshould not mutate them or their content. Rather, you create new\ninstances whenever needed. The API tries to make this easy.\n*/\nclass Fragment {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The child nodes in this fragment.\n    */\n    content, size) {\n        this.content = content;\n        this.size = size || 0;\n        if (size == null)\n            for (let i = 0; i < content.length; i++)\n                this.size += content[i].nodeSize;\n    }\n    /**\n    Invoke a callback for all descendant nodes between the given two\n    positions (relative to start of this fragment). Doesn't descend\n    into a node when the callback returns `false`.\n    */\n    nodesBetween(from, to, f, nodeStart = 0, parent) {\n        for (let i = 0, pos = 0; pos < to; i++) {\n            let child = this.content[i], end = pos + child.nodeSize;\n            if (end > from && f(child, nodeStart + pos, parent || null, i) !== false && child.content.size) {\n                let start = pos + 1;\n                child.nodesBetween(Math.max(0, from - start), Math.min(child.content.size, to - start), f, nodeStart + start);\n            }\n            pos = end;\n        }\n    }\n    /**\n    Call the given callback for every descendant node. `pos` will be\n    relative to the start of the fragment. The callback may return\n    `false` to prevent traversal of a given node's children.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.size, f);\n    }\n    /**\n    Extract the text between `from` and `to`. See the same method on\n    [`Node`](https://prosemirror.net/docs/ref/#model.Node.textBetween).\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        let text = \"\", first = true;\n        this.nodesBetween(from, to, (node, pos) => {\n            let nodeText = node.isText ? node.text.slice(Math.max(from, pos) - pos, to - pos)\n                : !node.isLeaf ? \"\"\n                    : leafText ? (typeof leafText === \"function\" ? leafText(node) : leafText)\n                        : node.type.spec.leafText ? node.type.spec.leafText(node)\n                            : \"\";\n            if (node.isBlock && (node.isLeaf && nodeText || node.isTextblock) && blockSeparator) {\n                if (first)\n                    first = false;\n                else\n                    text += blockSeparator;\n            }\n            text += nodeText;\n        }, 0);\n        return text;\n    }\n    /**\n    Create a new fragment containing the combined content of this\n    fragment and the other.\n    */\n    append(other) {\n        if (!other.size)\n            return this;\n        if (!this.size)\n            return other;\n        let last = this.lastChild, first = other.firstChild, content = this.content.slice(), i = 0;\n        if (last.isText && last.sameMarkup(first)) {\n            content[content.length - 1] = last.withText(last.text + first.text);\n            i = 1;\n        }\n        for (; i < other.content.length; i++)\n            content.push(other.content[i]);\n        return new Fragment(content, this.size + other.size);\n    }\n    /**\n    Cut out the sub-fragment between the two given positions.\n    */\n    cut(from, to = this.size) {\n        if (from == 0 && to == this.size)\n            return this;\n        let result = [], size = 0;\n        if (to > from)\n            for (let i = 0, pos = 0; pos < to; i++) {\n                let child = this.content[i], end = pos + child.nodeSize;\n                if (end > from) {\n                    if (pos < from || end > to) {\n                        if (child.isText)\n                            child = child.cut(Math.max(0, from - pos), Math.min(child.text.length, to - pos));\n                        else\n                            child = child.cut(Math.max(0, from - pos - 1), Math.min(child.content.size, to - pos - 1));\n                    }\n                    result.push(child);\n                    size += child.nodeSize;\n                }\n                pos = end;\n            }\n        return new Fragment(result, size);\n    }\n    /**\n    @internal\n    */\n    cutByIndex(from, to) {\n        if (from == to)\n            return Fragment.empty;\n        if (from == 0 && to == this.content.length)\n            return this;\n        return new Fragment(this.content.slice(from, to));\n    }\n    /**\n    Create a new fragment in which the node at the given index is\n    replaced by the given node.\n    */\n    replaceChild(index, node) {\n        let current = this.content[index];\n        if (current == node)\n            return this;\n        let copy = this.content.slice();\n        let size = this.size + node.nodeSize - current.nodeSize;\n        copy[index] = node;\n        return new Fragment(copy, size);\n    }\n    /**\n    Create a new fragment by prepending the given node to this\n    fragment.\n    */\n    addToStart(node) {\n        return new Fragment([node].concat(this.content), this.size + node.nodeSize);\n    }\n    /**\n    Create a new fragment by appending the given node to this\n    fragment.\n    */\n    addToEnd(node) {\n        return new Fragment(this.content.concat(node), this.size + node.nodeSize);\n    }\n    /**\n    Compare this fragment to another one.\n    */\n    eq(other) {\n        if (this.content.length != other.content.length)\n            return false;\n        for (let i = 0; i < this.content.length; i++)\n            if (!this.content[i].eq(other.content[i]))\n                return false;\n        return true;\n    }\n    /**\n    The first child of the fragment, or `null` if it is empty.\n    */\n    get firstChild() { return this.content.length ? this.content[0] : null; }\n    /**\n    The last child of the fragment, or `null` if it is empty.\n    */\n    get lastChild() { return this.content.length ? this.content[this.content.length - 1] : null; }\n    /**\n    The number of child nodes in this fragment.\n    */\n    get childCount() { return this.content.length; }\n    /**\n    Get the child node at the given index. Raise an error when the\n    index is out of range.\n    */\n    child(index) {\n        let found = this.content[index];\n        if (!found)\n            throw new RangeError(\"Index \" + index + \" out of range for \" + this);\n        return found;\n    }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) {\n        return this.content[index] || null;\n    }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) {\n        for (let i = 0, p = 0; i < this.content.length; i++) {\n            let child = this.content[i];\n            f(child, p, i);\n            p += child.nodeSize;\n        }\n    }\n    /**\n    Find the first position at which this fragment and another\n    fragment differ, or `null` if they are the same.\n    */\n    findDiffStart(other, pos = 0) {\n        return findDiffStart(this, other, pos);\n    }\n    /**\n    Find the first position, searching from the end, at which this\n    fragment and the given fragment differ, or `null` if they are\n    the same. Since this position will not be the same in both\n    nodes, an object with two separate positions is returned.\n    */\n    findDiffEnd(other, pos = this.size, otherPos = other.size) {\n        return findDiffEnd(this, other, pos, otherPos);\n    }\n    /**\n    Find the index and inner offset corresponding to a given relative\n    position in this fragment. The result object will be reused\n    (overwritten) the next time the function is called. @internal\n    */\n    findIndex(pos) {\n        if (pos == 0)\n            return retIndex(0, pos);\n        if (pos == this.size)\n            return retIndex(this.content.length, pos);\n        if (pos > this.size || pos < 0)\n            throw new RangeError(`Position ${pos} outside of fragment (${this})`);\n        for (let i = 0, curPos = 0;; i++) {\n            let cur = this.child(i), end = curPos + cur.nodeSize;\n            if (end >= pos) {\n                if (end == pos)\n                    return retIndex(i + 1, end);\n                return retIndex(i, curPos);\n            }\n            curPos = end;\n        }\n    }\n    /**\n    Return a debugging string that describes this fragment.\n    */\n    toString() { return \"<\" + this.toStringInner() + \">\"; }\n    /**\n    @internal\n    */\n    toStringInner() { return this.content.join(\", \"); }\n    /**\n    Create a JSON-serializeable representation of this fragment.\n    */\n    toJSON() {\n        return this.content.length ? this.content.map(n => n.toJSON()) : null;\n    }\n    /**\n    Deserialize a fragment from its JSON representation.\n    */\n    static fromJSON(schema, value) {\n        if (!value)\n            return Fragment.empty;\n        if (!Array.isArray(value))\n            throw new RangeError(\"Invalid input for Fragment.fromJSON\");\n        return new Fragment(value.map(schema.nodeFromJSON));\n    }\n    /**\n    Build a fragment from an array of nodes. Ensures that adjacent\n    text nodes with the same marks are joined together.\n    */\n    static fromArray(array) {\n        if (!array.length)\n            return Fragment.empty;\n        let joined, size = 0;\n        for (let i = 0; i < array.length; i++) {\n            let node = array[i];\n            size += node.nodeSize;\n            if (i && node.isText && array[i - 1].sameMarkup(node)) {\n                if (!joined)\n                    joined = array.slice(0, i);\n                joined[joined.length - 1] = node\n                    .withText(joined[joined.length - 1].text + node.text);\n            }\n            else if (joined) {\n                joined.push(node);\n            }\n        }\n        return new Fragment(joined || array, size);\n    }\n    /**\n    Create a fragment from something that can be interpreted as a\n    set of nodes. For `null`, it returns the empty fragment. For a\n    fragment, the fragment itself. For a node or array of nodes, a\n    fragment containing those nodes.\n    */\n    static from(nodes) {\n        if (!nodes)\n            return Fragment.empty;\n        if (nodes instanceof Fragment)\n            return nodes;\n        if (Array.isArray(nodes))\n            return this.fromArray(nodes);\n        if (nodes.attrs)\n            return new Fragment([nodes], nodes.nodeSize);\n        throw new RangeError(\"Can not convert \" + nodes + \" to a Fragment\" +\n            (nodes.nodesBetween ? \" (looks like multiple versions of prosemirror-model were loaded)\" : \"\"));\n    }\n}\n/**\nAn empty fragment. Intended to be reused whenever a node doesn't\ncontain anything (rather than allocating a new empty fragment for\neach leaf node).\n*/\nFragment.empty = new Fragment([], 0);\nconst found = { index: 0, offset: 0 };\nfunction retIndex(index, offset) {\n    found.index = index;\n    found.offset = offset;\n    return found;\n}\n\nfunction compareDeep(a, b) {\n    if (a === b)\n        return true;\n    if (!(a && typeof a == \"object\") ||\n        !(b && typeof b == \"object\"))\n        return false;\n    let array = Array.isArray(a);\n    if (Array.isArray(b) != array)\n        return false;\n    if (array) {\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!compareDeep(a[i], b[i]))\n                return false;\n    }\n    else {\n        for (let p in a)\n            if (!(p in b) || !compareDeep(a[p], b[p]))\n                return false;\n        for (let p in b)\n            if (!(p in a))\n                return false;\n    }\n    return true;\n}\n\n/**\nA mark is a piece of information that can be attached to a node,\nsuch as it being emphasized, in code font, or a link. It has a\ntype and optionally a set of attributes that provide further\ninformation (such as the target of the link). Marks are created\nthrough a `Schema`, which controls which types exist and which\nattributes they have.\n*/\nclass Mark {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of this mark.\n    */\n    type, \n    /**\n    The attributes associated with this mark.\n    */\n    attrs) {\n        this.type = type;\n        this.attrs = attrs;\n    }\n    /**\n    Given a set of marks, create a new set which contains this one as\n    well, in the right position. If this mark is already in the set,\n    the set itself is returned. If any marks that are set to be\n    [exclusive](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) with this mark are present,\n    those are replaced by this one.\n    */\n    addToSet(set) {\n        let copy, placed = false;\n        for (let i = 0; i < set.length; i++) {\n            let other = set[i];\n            if (this.eq(other))\n                return set;\n            if (this.type.excludes(other.type)) {\n                if (!copy)\n                    copy = set.slice(0, i);\n            }\n            else if (other.type.excludes(this.type)) {\n                return set;\n            }\n            else {\n                if (!placed && other.type.rank > this.type.rank) {\n                    if (!copy)\n                        copy = set.slice(0, i);\n                    copy.push(this);\n                    placed = true;\n                }\n                if (copy)\n                    copy.push(other);\n            }\n        }\n        if (!copy)\n            copy = set.slice();\n        if (!placed)\n            copy.push(this);\n        return copy;\n    }\n    /**\n    Remove this mark from the given set, returning a new set. If this\n    mark is not in the set, the set itself is returned.\n    */\n    removeFromSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return set.slice(0, i).concat(set.slice(i + 1));\n        return set;\n    }\n    /**\n    Test whether this mark is in the given set of marks.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return true;\n        return false;\n    }\n    /**\n    Test whether this mark has the same type and attributes as\n    another mark.\n    */\n    eq(other) {\n        return this == other ||\n            (this.type == other.type && compareDeep(this.attrs, other.attrs));\n    }\n    /**\n    Convert this mark to a JSON-serializeable representation.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        return obj;\n    }\n    /**\n    Deserialize a mark from JSON.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Mark.fromJSON\");\n        let type = schema.marks[json.type];\n        if (!type)\n            throw new RangeError(`There is no mark type ${json.type} in this schema`);\n        let mark = type.create(json.attrs);\n        type.checkAttrs(mark.attrs);\n        return mark;\n    }\n    /**\n    Test whether two sets of marks are identical.\n    */\n    static sameSet(a, b) {\n        if (a == b)\n            return true;\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!a[i].eq(b[i]))\n                return false;\n        return true;\n    }\n    /**\n    Create a properly sorted mark set from null, a single mark, or an\n    unsorted array of marks.\n    */\n    static setFrom(marks) {\n        if (!marks || Array.isArray(marks) && marks.length == 0)\n            return Mark.none;\n        if (marks instanceof Mark)\n            return [marks];\n        let copy = marks.slice();\n        copy.sort((a, b) => a.type.rank - b.type.rank);\n        return copy;\n    }\n}\n/**\nThe empty set of marks.\n*/\nMark.none = [];\n\n/**\nError type raised by [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) when\ngiven an invalid replacement.\n*/\nclass ReplaceError extends Error {\n}\n/*\nReplaceError = function(this: any, message: string) {\n  let err = Error.call(this, message)\n  ;(err as any).__proto__ = ReplaceError.prototype\n  return err\n} as any\n\nReplaceError.prototype = Object.create(Error.prototype)\nReplaceError.prototype.constructor = ReplaceError\nReplaceError.prototype.name = \"ReplaceError\"\n*/\n/**\nA slice represents a piece cut out of a larger document. It\nstores not only a fragment, but also the depth up to which nodes on\nboth side are ‘open’ (cut through).\n*/\nclass Slice {\n    /**\n    Create a slice. When specifying a non-zero open depth, you must\n    make sure that there are nodes of at least that depth at the\n    appropriate side of the fragment—i.e. if the fragment is an\n    empty paragraph node, `openStart` and `openEnd` can't be greater\n    than 1.\n    \n    It is not necessary for the content of open nodes to conform to\n    the schema's content constraints, though it should be a valid\n    start/end/middle for such a node, depending on which sides are\n    open.\n    */\n    constructor(\n    /**\n    The slice's content.\n    */\n    content, \n    /**\n    The open depth at the start of the fragment.\n    */\n    openStart, \n    /**\n    The open depth at the end.\n    */\n    openEnd) {\n        this.content = content;\n        this.openStart = openStart;\n        this.openEnd = openEnd;\n    }\n    /**\n    The size this slice would add when inserted into a document.\n    */\n    get size() {\n        return this.content.size - this.openStart - this.openEnd;\n    }\n    /**\n    @internal\n    */\n    insertAt(pos, fragment) {\n        let content = insertInto(this.content, pos + this.openStart, fragment);\n        return content && new Slice(content, this.openStart, this.openEnd);\n    }\n    /**\n    @internal\n    */\n    removeBetween(from, to) {\n        return new Slice(removeRange(this.content, from + this.openStart, to + this.openStart), this.openStart, this.openEnd);\n    }\n    /**\n    Tests whether this slice is equal to another slice.\n    */\n    eq(other) {\n        return this.content.eq(other.content) && this.openStart == other.openStart && this.openEnd == other.openEnd;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.content + \"(\" + this.openStart + \",\" + this.openEnd + \")\";\n    }\n    /**\n    Convert a slice to a JSON-serializable representation.\n    */\n    toJSON() {\n        if (!this.content.size)\n            return null;\n        let json = { content: this.content.toJSON() };\n        if (this.openStart > 0)\n            json.openStart = this.openStart;\n        if (this.openEnd > 0)\n            json.openEnd = this.openEnd;\n        return json;\n    }\n    /**\n    Deserialize a slice from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            return Slice.empty;\n        let openStart = json.openStart || 0, openEnd = json.openEnd || 0;\n        if (typeof openStart != \"number\" || typeof openEnd != \"number\")\n            throw new RangeError(\"Invalid input for Slice.fromJSON\");\n        return new Slice(Fragment.fromJSON(schema, json.content), openStart, openEnd);\n    }\n    /**\n    Create a slice from a fragment by taking the maximum possible\n    open value on both side of the fragment.\n    */\n    static maxOpen(fragment, openIsolating = true) {\n        let openStart = 0, openEnd = 0;\n        for (let n = fragment.firstChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.firstChild)\n            openStart++;\n        for (let n = fragment.lastChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.lastChild)\n            openEnd++;\n        return new Slice(fragment, openStart, openEnd);\n    }\n}\n/**\nThe empty slice.\n*/\nSlice.empty = new Slice(Fragment.empty, 0, 0);\nfunction removeRange(content, from, to) {\n    let { index, offset } = content.findIndex(from), child = content.maybeChild(index);\n    let { index: indexTo, offset: offsetTo } = content.findIndex(to);\n    if (offset == from || child.isText) {\n        if (offsetTo != to && !content.child(indexTo).isText)\n            throw new RangeError(\"Removing non-flat range\");\n        return content.cut(0, from).append(content.cut(to));\n    }\n    if (index != indexTo)\n        throw new RangeError(\"Removing non-flat range\");\n    return content.replaceChild(index, child.copy(removeRange(child.content, from - offset - 1, to - offset - 1)));\n}\nfunction insertInto(content, dist, insert, parent) {\n    let { index, offset } = content.findIndex(dist), child = content.maybeChild(index);\n    if (offset == dist || child.isText) {\n        if (parent && !parent.canReplace(index, index, insert))\n            return null;\n        return content.cut(0, dist).append(insert).append(content.cut(dist));\n    }\n    let inner = insertInto(child.content, dist - offset - 1, insert);\n    return inner && content.replaceChild(index, child.copy(inner));\n}\nfunction replace($from, $to, slice) {\n    if (slice.openStart > $from.depth)\n        throw new ReplaceError(\"Inserted content deeper than insertion position\");\n    if ($from.depth - slice.openStart != $to.depth - slice.openEnd)\n        throw new ReplaceError(\"Inconsistent open depths\");\n    return replaceOuter($from, $to, slice, 0);\n}\nfunction replaceOuter($from, $to, slice, depth) {\n    let index = $from.index(depth), node = $from.node(depth);\n    if (index == $to.index(depth) && depth < $from.depth - slice.openStart) {\n        let inner = replaceOuter($from, $to, slice, depth + 1);\n        return node.copy(node.content.replaceChild(index, inner));\n    }\n    else if (!slice.content.size) {\n        return close(node, replaceTwoWay($from, $to, depth));\n    }\n    else if (!slice.openStart && !slice.openEnd && $from.depth == depth && $to.depth == depth) { // Simple, flat case\n        let parent = $from.parent, content = parent.content;\n        return close(parent, content.cut(0, $from.parentOffset).append(slice.content).append(content.cut($to.parentOffset)));\n    }\n    else {\n        let { start, end } = prepareSliceForReplace(slice, $from);\n        return close(node, replaceThreeWay($from, start, end, $to, depth));\n    }\n}\nfunction checkJoin(main, sub) {\n    if (!sub.type.compatibleContent(main.type))\n        throw new ReplaceError(\"Cannot join \" + sub.type.name + \" onto \" + main.type.name);\n}\nfunction joinable($before, $after, depth) {\n    let node = $before.node(depth);\n    checkJoin(node, $after.node(depth));\n    return node;\n}\nfunction addNode(child, target) {\n    let last = target.length - 1;\n    if (last >= 0 && child.isText && child.sameMarkup(target[last]))\n        target[last] = child.withText(target[last].text + child.text);\n    else\n        target.push(child);\n}\nfunction addRange($start, $end, depth, target) {\n    let node = ($end || $start).node(depth);\n    let startIndex = 0, endIndex = $end ? $end.index(depth) : node.childCount;\n    if ($start) {\n        startIndex = $start.index(depth);\n        if ($start.depth > depth) {\n            startIndex++;\n        }\n        else if ($start.textOffset) {\n            addNode($start.nodeAfter, target);\n            startIndex++;\n        }\n    }\n    for (let i = startIndex; i < endIndex; i++)\n        addNode(node.child(i), target);\n    if ($end && $end.depth == depth && $end.textOffset)\n        addNode($end.nodeBefore, target);\n}\nfunction close(node, content) {\n    node.type.checkContent(content);\n    return node.copy(content);\n}\nfunction replaceThreeWay($from, $start, $end, $to, depth) {\n    let openStart = $from.depth > depth && joinable($from, $start, depth + 1);\n    let openEnd = $to.depth > depth && joinable($end, $to, depth + 1);\n    let content = [];\n    addRange(null, $from, depth, content);\n    if (openStart && openEnd && $start.index(depth) == $end.index(depth)) {\n        checkJoin(openStart, openEnd);\n        addNode(close(openStart, replaceThreeWay($from, $start, $end, $to, depth + 1)), content);\n    }\n    else {\n        if (openStart)\n            addNode(close(openStart, replaceTwoWay($from, $start, depth + 1)), content);\n        addRange($start, $end, depth, content);\n        if (openEnd)\n            addNode(close(openEnd, replaceTwoWay($end, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction replaceTwoWay($from, $to, depth) {\n    let content = [];\n    addRange(null, $from, depth, content);\n    if ($from.depth > depth) {\n        let type = joinable($from, $to, depth + 1);\n        addNode(close(type, replaceTwoWay($from, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction prepareSliceForReplace(slice, $along) {\n    let extra = $along.depth - slice.openStart, parent = $along.node(extra);\n    let node = parent.copy(slice.content);\n    for (let i = extra - 1; i >= 0; i--)\n        node = $along.node(i).copy(Fragment.from(node));\n    return { start: node.resolveNoCache(slice.openStart + extra),\n        end: node.resolveNoCache(node.content.size - slice.openEnd - extra) };\n}\n\n/**\nYou can [_resolve_](https://prosemirror.net/docs/ref/#model.Node.resolve) a position to get more\ninformation about it. Objects of this class represent such a\nresolved position, providing various pieces of context\ninformation, and some helper methods.\n\nThroughout this interface, methods that take an optional `depth`\nparameter will interpret undefined as `this.depth` and negative\nnumbers as `this.depth + value`.\n*/\nclass ResolvedPos {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position that was resolved.\n    */\n    pos, \n    /**\n    @internal\n    */\n    path, \n    /**\n    The offset this position has into its parent node.\n    */\n    parentOffset) {\n        this.pos = pos;\n        this.path = path;\n        this.parentOffset = parentOffset;\n        this.depth = path.length / 3 - 1;\n    }\n    /**\n    @internal\n    */\n    resolveDepth(val) {\n        if (val == null)\n            return this.depth;\n        if (val < 0)\n            return this.depth + val;\n        return val;\n    }\n    /**\n    The parent node that the position points into. Note that even if\n    a position points into a text node, that node is not considered\n    the parent—text nodes are ‘flat’ in this model, and have no content.\n    */\n    get parent() { return this.node(this.depth); }\n    /**\n    The root node in which the position was resolved.\n    */\n    get doc() { return this.node(0); }\n    /**\n    The ancestor node at the given level. `p.node(p.depth)` is the\n    same as `p.parent`.\n    */\n    node(depth) { return this.path[this.resolveDepth(depth) * 3]; }\n    /**\n    The index into the ancestor at the given level. If this points\n    at the 3rd node in the 2nd paragraph on the top level, for\n    example, `p.index(0)` is 1 and `p.index(1)` is 2.\n    */\n    index(depth) { return this.path[this.resolveDepth(depth) * 3 + 1]; }\n    /**\n    The index pointing after this position into the ancestor at the\n    given level.\n    */\n    indexAfter(depth) {\n        depth = this.resolveDepth(depth);\n        return this.index(depth) + (depth == this.depth && !this.textOffset ? 0 : 1);\n    }\n    /**\n    The (absolute) position at the start of the node at the given\n    level.\n    */\n    start(depth) {\n        depth = this.resolveDepth(depth);\n        return depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n    }\n    /**\n    The (absolute) position at the end of the node at the given\n    level.\n    */\n    end(depth) {\n        depth = this.resolveDepth(depth);\n        return this.start(depth) + this.node(depth).content.size;\n    }\n    /**\n    The (absolute) position directly before the wrapping node at the\n    given level, or, when `depth` is `this.depth + 1`, the original\n    position.\n    */\n    before(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position before the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1];\n    }\n    /**\n    The (absolute) position directly after the wrapping node at the\n    given level, or the original position when `depth` is `this.depth + 1`.\n    */\n    after(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position after the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1] + this.path[depth * 3].nodeSize;\n    }\n    /**\n    When this position points into a text node, this returns the\n    distance between the position and the start of the text node.\n    Will be zero for positions that point between nodes.\n    */\n    get textOffset() { return this.pos - this.path[this.path.length - 1]; }\n    /**\n    Get the node directly after the position, if any. If the position\n    points into a text node, only the part of that node after the\n    position is returned.\n    */\n    get nodeAfter() {\n        let parent = this.parent, index = this.index(this.depth);\n        if (index == parent.childCount)\n            return null;\n        let dOff = this.pos - this.path[this.path.length - 1], child = parent.child(index);\n        return dOff ? parent.child(index).cut(dOff) : child;\n    }\n    /**\n    Get the node directly before the position, if any. If the\n    position points into a text node, only the part of that node\n    before the position is returned.\n    */\n    get nodeBefore() {\n        let index = this.index(this.depth);\n        let dOff = this.pos - this.path[this.path.length - 1];\n        if (dOff)\n            return this.parent.child(index).cut(0, dOff);\n        return index == 0 ? null : this.parent.child(index - 1);\n    }\n    /**\n    Get the position at the given index in the parent node at the\n    given depth (which defaults to `this.depth`).\n    */\n    posAtIndex(index, depth) {\n        depth = this.resolveDepth(depth);\n        let node = this.path[depth * 3], pos = depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n        for (let i = 0; i < index; i++)\n            pos += node.child(i).nodeSize;\n        return pos;\n    }\n    /**\n    Get the marks at this position, factoring in the surrounding\n    marks' [`inclusive`](https://prosemirror.net/docs/ref/#model.MarkSpec.inclusive) property. If the\n    position is at the start of a non-empty node, the marks of the\n    node after it (if any) are returned.\n    */\n    marks() {\n        let parent = this.parent, index = this.index();\n        // In an empty parent, return the empty array\n        if (parent.content.size == 0)\n            return Mark.none;\n        // When inside a text node, just return the text node's marks\n        if (this.textOffset)\n            return parent.child(index).marks;\n        let main = parent.maybeChild(index - 1), other = parent.maybeChild(index);\n        // If the `after` flag is true of there is no node before, make\n        // the node after this position the main reference.\n        if (!main) {\n            let tmp = main;\n            main = other;\n            other = tmp;\n        }\n        // Use all marks in the main node, except those that have\n        // `inclusive` set to false and are not present in the other node.\n        let marks = main.marks;\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!other || !marks[i].isInSet(other.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    Get the marks after the current position, if any, except those\n    that are non-inclusive and not present at position `$end`. This\n    is mostly useful for getting the set of marks to preserve after a\n    deletion. Will return `null` if this position is at the end of\n    its parent node or its parent node isn't a textblock (in which\n    case no marks should be preserved).\n    */\n    marksAcross($end) {\n        let after = this.parent.maybeChild(this.index());\n        if (!after || !after.isInline)\n            return null;\n        let marks = after.marks, next = $end.parent.maybeChild($end.index());\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!next || !marks[i].isInSet(next.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    The depth up to which this position and the given (non-resolved)\n    position share the same parent nodes.\n    */\n    sharedDepth(pos) {\n        for (let depth = this.depth; depth > 0; depth--)\n            if (this.start(depth) <= pos && this.end(depth) >= pos)\n                return depth;\n        return 0;\n    }\n    /**\n    Returns a range based on the place where this position and the\n    given position diverge around block content. If both point into\n    the same textblock, for example, a range around that textblock\n    will be returned. If they point into different blocks, the range\n    around those blocks in their shared ancestor is returned. You can\n    pass in an optional predicate that will be called with a parent\n    node to see if a range into that parent is acceptable.\n    */\n    blockRange(other = this, pred) {\n        if (other.pos < this.pos)\n            return other.blockRange(this);\n        for (let d = this.depth - (this.parent.inlineContent || this.pos == other.pos ? 1 : 0); d >= 0; d--)\n            if (other.pos <= this.end(d) && (!pred || pred(this.node(d))))\n                return new NodeRange(this, other, d);\n        return null;\n    }\n    /**\n    Query whether the given position shares the same parent node.\n    */\n    sameParent(other) {\n        return this.pos - this.parentOffset == other.pos - other.parentOffset;\n    }\n    /**\n    Return the greater of this and the given position.\n    */\n    max(other) {\n        return other.pos > this.pos ? other : this;\n    }\n    /**\n    Return the smaller of this and the given position.\n    */\n    min(other) {\n        return other.pos < this.pos ? other : this;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let str = \"\";\n        for (let i = 1; i <= this.depth; i++)\n            str += (str ? \"/\" : \"\") + this.node(i).type.name + \"_\" + this.index(i - 1);\n        return str + \":\" + this.parentOffset;\n    }\n    /**\n    @internal\n    */\n    static resolve(doc, pos) {\n        if (!(pos >= 0 && pos <= doc.content.size))\n            throw new RangeError(\"Position \" + pos + \" out of range\");\n        let path = [];\n        let start = 0, parentOffset = pos;\n        for (let node = doc;;) {\n            let { index, offset } = node.content.findIndex(parentOffset);\n            let rem = parentOffset - offset;\n            path.push(node, index, start + offset);\n            if (!rem)\n                break;\n            node = node.child(index);\n            if (node.isText)\n                break;\n            parentOffset = rem - 1;\n            start += offset + 1;\n        }\n        return new ResolvedPos(pos, path, parentOffset);\n    }\n    /**\n    @internal\n    */\n    static resolveCached(doc, pos) {\n        let cache = resolveCache.get(doc);\n        if (cache) {\n            for (let i = 0; i < cache.elts.length; i++) {\n                let elt = cache.elts[i];\n                if (elt.pos == pos)\n                    return elt;\n            }\n        }\n        else {\n            resolveCache.set(doc, cache = new ResolveCache);\n        }\n        let result = cache.elts[cache.i] = ResolvedPos.resolve(doc, pos);\n        cache.i = (cache.i + 1) % resolveCacheSize;\n        return result;\n    }\n}\nclass ResolveCache {\n    constructor() {\n        this.elts = [];\n        this.i = 0;\n    }\n}\nconst resolveCacheSize = 12, resolveCache = new WeakMap();\n/**\nRepresents a flat range of content, i.e. one that starts and\nends in the same node.\n*/\nclass NodeRange {\n    /**\n    Construct a node range. `$from` and `$to` should point into the\n    same node until at least the given `depth`, since a node range\n    denotes an adjacent set of nodes in a single parent node.\n    */\n    constructor(\n    /**\n    A resolved position along the start of the content. May have a\n    `depth` greater than this object's `depth` property, since\n    these are the positions that were used to compute the range,\n    not re-resolved positions directly at its boundaries.\n    */\n    $from, \n    /**\n    A position along the end of the content. See\n    caveat for [`$from`](https://prosemirror.net/docs/ref/#model.NodeRange.$from).\n    */\n    $to, \n    /**\n    The depth of the node that this range points into.\n    */\n    depth) {\n        this.$from = $from;\n        this.$to = $to;\n        this.depth = depth;\n    }\n    /**\n    The position at the start of the range.\n    */\n    get start() { return this.$from.before(this.depth + 1); }\n    /**\n    The position at the end of the range.\n    */\n    get end() { return this.$to.after(this.depth + 1); }\n    /**\n    The parent node that the range points into.\n    */\n    get parent() { return this.$from.node(this.depth); }\n    /**\n    The start index of the range in the parent node.\n    */\n    get startIndex() { return this.$from.index(this.depth); }\n    /**\n    The end index of the range in the parent node.\n    */\n    get endIndex() { return this.$to.indexAfter(this.depth); }\n}\n\nconst emptyAttrs = Object.create(null);\n/**\nThis class represents a node in the tree that makes up a\nProseMirror document. So a document is an instance of `Node`, with\nchildren that are also instances of `Node`.\n\nNodes are persistent data structures. Instead of changing them, you\ncreate new ones with the content you want. Old ones keep pointing\nat the old document shape. This is made cheaper by sharing\nstructure between the old and new data as much as possible, which a\ntree shape like this (without back pointers) makes easy.\n\n**Do not** directly mutate the properties of a `Node` object. See\n[the guide](https://prosemirror.net/docs/guide/#doc) for more information.\n*/\nclass Node {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of node that this is.\n    */\n    type, \n    /**\n    An object mapping attribute names to values. The kind of\n    attributes allowed and required are\n    [determined](https://prosemirror.net/docs/ref/#model.NodeSpec.attrs) by the node type.\n    */\n    attrs, \n    // A fragment holding the node's children.\n    content, \n    /**\n    The marks (things like whether it is emphasized or part of a\n    link) applied to this node.\n    */\n    marks = Mark.none) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.content = content || Fragment.empty;\n    }\n    /**\n    The array of this node's child nodes.\n    */\n    get children() { return this.content.content; }\n    /**\n    The size of this node, as defined by the integer-based [indexing\n    scheme](https://prosemirror.net/docs/guide/#doc.indexing). For text nodes, this is the\n    amount of characters. For other leaf nodes, it is one. For\n    non-leaf nodes, it is the size of the content plus two (the\n    start and end token).\n    */\n    get nodeSize() { return this.isLeaf ? 1 : 2 + this.content.size; }\n    /**\n    The number of children that the node has.\n    */\n    get childCount() { return this.content.childCount; }\n    /**\n    Get the child node at the given index. Raises an error when the\n    index is out of range.\n    */\n    child(index) { return this.content.child(index); }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) { return this.content.maybeChild(index); }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) { this.content.forEach(f); }\n    /**\n    Invoke a callback for all descendant nodes recursively between\n    the given two positions that are relative to start of this\n    node's content. The callback is invoked with the node, its\n    position relative to the original node (method receiver),\n    its parent node, and its child index. When the callback returns\n    false for a given node, that node's children will not be\n    recursed over. The last parameter can be used to specify a\n    starting position to count from.\n    */\n    nodesBetween(from, to, f, startPos = 0) {\n        this.content.nodesBetween(from, to, f, startPos, this);\n    }\n    /**\n    Call the given callback for every descendant node. Doesn't\n    descend into a node when the callback returns `false`.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.content.size, f);\n    }\n    /**\n    Concatenates all the text nodes found in this fragment and its\n    children.\n    */\n    get textContent() {\n        return (this.isLeaf && this.type.spec.leafText)\n            ? this.type.spec.leafText(this)\n            : this.textBetween(0, this.content.size, \"\");\n    }\n    /**\n    Get all text between positions `from` and `to`. When\n    `blockSeparator` is given, it will be inserted to separate text\n    from different block nodes. If `leafText` is given, it'll be\n    inserted for every non-text leaf node encountered, otherwise\n    [`leafText`](https://prosemirror.net/docs/ref/#model.NodeSpec.leafText) will be used.\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        return this.content.textBetween(from, to, blockSeparator, leafText);\n    }\n    /**\n    Returns this node's first child, or `null` if there are no\n    children.\n    */\n    get firstChild() { return this.content.firstChild; }\n    /**\n    Returns this node's last child, or `null` if there are no\n    children.\n    */\n    get lastChild() { return this.content.lastChild; }\n    /**\n    Test whether two nodes represent the same piece of document.\n    */\n    eq(other) {\n        return this == other || (this.sameMarkup(other) && this.content.eq(other.content));\n    }\n    /**\n    Compare the markup (type, attributes, and marks) of this node to\n    those of another. Returns `true` if both have the same markup.\n    */\n    sameMarkup(other) {\n        return this.hasMarkup(other.type, other.attrs, other.marks);\n    }\n    /**\n    Check whether this node's markup correspond to the given type,\n    attributes, and marks.\n    */\n    hasMarkup(type, attrs, marks) {\n        return this.type == type &&\n            compareDeep(this.attrs, attrs || type.defaultAttrs || emptyAttrs) &&\n            Mark.sameSet(this.marks, marks || Mark.none);\n    }\n    /**\n    Create a new node with the same markup as this node, containing\n    the given content (or empty, if no content is given).\n    */\n    copy(content = null) {\n        if (content == this.content)\n            return this;\n        return new Node(this.type, this.attrs, content, this.marks);\n    }\n    /**\n    Create a copy of this node, with the given set of marks instead\n    of the node's own marks.\n    */\n    mark(marks) {\n        return marks == this.marks ? this : new Node(this.type, this.attrs, this.content, marks);\n    }\n    /**\n    Create a copy of this node with only the content between the\n    given positions. If `to` is not given, it defaults to the end of\n    the node.\n    */\n    cut(from, to = this.content.size) {\n        if (from == 0 && to == this.content.size)\n            return this;\n        return this.copy(this.content.cut(from, to));\n    }\n    /**\n    Cut out the part of the document between the given positions, and\n    return it as a `Slice` object.\n    */\n    slice(from, to = this.content.size, includeParents = false) {\n        if (from == to)\n            return Slice.empty;\n        let $from = this.resolve(from), $to = this.resolve(to);\n        let depth = includeParents ? 0 : $from.sharedDepth(to);\n        let start = $from.start(depth), node = $from.node(depth);\n        let content = node.content.cut($from.pos - start, $to.pos - start);\n        return new Slice(content, $from.depth - depth, $to.depth - depth);\n    }\n    /**\n    Replace the part of the document between the given positions with\n    the given slice. The slice must 'fit', meaning its open sides\n    must be able to connect to the surrounding content, and its\n    content nodes must be valid children for the node they are placed\n    into. If any of this is violated, an error of type\n    [`ReplaceError`](https://prosemirror.net/docs/ref/#model.ReplaceError) is thrown.\n    */\n    replace(from, to, slice) {\n        return replace(this.resolve(from), this.resolve(to), slice);\n    }\n    /**\n    Find the node directly after the given position.\n    */\n    nodeAt(pos) {\n        for (let node = this;;) {\n            let { index, offset } = node.content.findIndex(pos);\n            node = node.maybeChild(index);\n            if (!node)\n                return null;\n            if (offset == pos || node.isText)\n                return node;\n            pos -= offset + 1;\n        }\n    }\n    /**\n    Find the (direct) child node after the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childAfter(pos) {\n        let { index, offset } = this.content.findIndex(pos);\n        return { node: this.content.maybeChild(index), index, offset };\n    }\n    /**\n    Find the (direct) child node before the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childBefore(pos) {\n        if (pos == 0)\n            return { node: null, index: 0, offset: 0 };\n        let { index, offset } = this.content.findIndex(pos);\n        if (offset < pos)\n            return { node: this.content.child(index), index, offset };\n        let node = this.content.child(index - 1);\n        return { node, index: index - 1, offset: offset - node.nodeSize };\n    }\n    /**\n    Resolve the given position in the document, returning an\n    [object](https://prosemirror.net/docs/ref/#model.ResolvedPos) with information about its context.\n    */\n    resolve(pos) { return ResolvedPos.resolveCached(this, pos); }\n    /**\n    @internal\n    */\n    resolveNoCache(pos) { return ResolvedPos.resolve(this, pos); }\n    /**\n    Test whether a given mark or mark type occurs in this document\n    between the two given positions.\n    */\n    rangeHasMark(from, to, type) {\n        let found = false;\n        if (to > from)\n            this.nodesBetween(from, to, node => {\n                if (type.isInSet(node.marks))\n                    found = true;\n                return !found;\n            });\n        return found;\n    }\n    /**\n    True when this is a block (non-inline node)\n    */\n    get isBlock() { return this.type.isBlock; }\n    /**\n    True when this is a textblock node, a block node with inline\n    content.\n    */\n    get isTextblock() { return this.type.isTextblock; }\n    /**\n    True when this node allows inline content.\n    */\n    get inlineContent() { return this.type.inlineContent; }\n    /**\n    True when this is an inline node (a text node or a node that can\n    appear among text).\n    */\n    get isInline() { return this.type.isInline; }\n    /**\n    True when this is a text node.\n    */\n    get isText() { return this.type.isText; }\n    /**\n    True when this is a leaf node.\n    */\n    get isLeaf() { return this.type.isLeaf; }\n    /**\n    True when this is an atom, i.e. when it does not have directly\n    editable content. This is usually the same as `isLeaf`, but can\n    be configured with the [`atom` property](https://prosemirror.net/docs/ref/#model.NodeSpec.atom)\n    on a node's spec (typically used when the node is displayed as\n    an uneditable [node view](https://prosemirror.net/docs/ref/#view.NodeView)).\n    */\n    get isAtom() { return this.type.isAtom; }\n    /**\n    Return a string representation of this node for debugging\n    purposes.\n    */\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        let name = this.type.name;\n        if (this.content.size)\n            name += \"(\" + this.content.toStringInner() + \")\";\n        return wrapMarks(this.marks, name);\n    }\n    /**\n    Get the content match in this node at the given index.\n    */\n    contentMatchAt(index) {\n        let match = this.type.contentMatch.matchFragment(this.content, 0, index);\n        if (!match)\n            throw new Error(\"Called contentMatchAt on a node with invalid content\");\n        return match;\n    }\n    /**\n    Test whether replacing the range between `from` and `to` (by\n    child index) with the given replacement fragment (which defaults\n    to the empty fragment) would leave the node's content valid. You\n    can optionally pass `start` and `end` indices into the\n    replacement fragment.\n    */\n    canReplace(from, to, replacement = Fragment.empty, start = 0, end = replacement.childCount) {\n        let one = this.contentMatchAt(from).matchFragment(replacement, start, end);\n        let two = one && one.matchFragment(this.content, to);\n        if (!two || !two.validEnd)\n            return false;\n        for (let i = start; i < end; i++)\n            if (!this.type.allowsMarks(replacement.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Test whether replacing the range `from` to `to` (by index) with\n    a node of the given type would leave the node's content valid.\n    */\n    canReplaceWith(from, to, type, marks) {\n        if (marks && !this.type.allowsMarks(marks))\n            return false;\n        let start = this.contentMatchAt(from).matchType(type);\n        let end = start && start.matchFragment(this.content, to);\n        return end ? end.validEnd : false;\n    }\n    /**\n    Test whether the given node's content could be appended to this\n    node. If that node is empty, this will only return true if there\n    is at least one node type that can appear in both nodes (to avoid\n    merging completely incompatible nodes).\n    */\n    canAppend(other) {\n        if (other.content.size)\n            return this.canReplace(this.childCount, this.childCount, other.content);\n        else\n            return this.type.compatibleContent(other.type);\n    }\n    /**\n    Check whether this node and its descendants conform to the\n    schema, and raise an exception when they do not.\n    */\n    check() {\n        this.type.checkContent(this.content);\n        this.type.checkAttrs(this.attrs);\n        let copy = Mark.none;\n        for (let i = 0; i < this.marks.length; i++) {\n            let mark = this.marks[i];\n            mark.type.checkAttrs(mark.attrs);\n            copy = mark.addToSet(copy);\n        }\n        if (!Mark.sameSet(copy, this.marks))\n            throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(m => m.type.name)}`);\n        this.content.forEach(node => node.check());\n    }\n    /**\n    Return a JSON-serializeable representation of this node.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        if (this.content.size)\n            obj.content = this.content.toJSON();\n        if (this.marks.length)\n            obj.marks = this.marks.map(n => n.toJSON());\n        return obj;\n    }\n    /**\n    Deserialize a node from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Node.fromJSON\");\n        let marks = undefined;\n        if (json.marks) {\n            if (!Array.isArray(json.marks))\n                throw new RangeError(\"Invalid mark data for Node.fromJSON\");\n            marks = json.marks.map(schema.markFromJSON);\n        }\n        if (json.type == \"text\") {\n            if (typeof json.text != \"string\")\n                throw new RangeError(\"Invalid text node in JSON\");\n            return schema.text(json.text, marks);\n        }\n        let content = Fragment.fromJSON(schema, json.content);\n        let node = schema.nodeType(json.type).create(json.attrs, content, marks);\n        node.type.checkAttrs(node.attrs);\n        return node;\n    }\n}\nNode.prototype.text = undefined;\nclass TextNode extends Node {\n    /**\n    @internal\n    */\n    constructor(type, attrs, content, marks) {\n        super(type, attrs, null, marks);\n        if (!content)\n            throw new RangeError(\"Empty text nodes are not allowed\");\n        this.text = content;\n    }\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        return wrapMarks(this.marks, JSON.stringify(this.text));\n    }\n    get textContent() { return this.text; }\n    textBetween(from, to) { return this.text.slice(from, to); }\n    get nodeSize() { return this.text.length; }\n    mark(marks) {\n        return marks == this.marks ? this : new TextNode(this.type, this.attrs, this.text, marks);\n    }\n    withText(text) {\n        if (text == this.text)\n            return this;\n        return new TextNode(this.type, this.attrs, text, this.marks);\n    }\n    cut(from = 0, to = this.text.length) {\n        if (from == 0 && to == this.text.length)\n            return this;\n        return this.withText(this.text.slice(from, to));\n    }\n    eq(other) {\n        return this.sameMarkup(other) && this.text == other.text;\n    }\n    toJSON() {\n        let base = super.toJSON();\n        base.text = this.text;\n        return base;\n    }\n}\nfunction wrapMarks(marks, str) {\n    for (let i = marks.length - 1; i >= 0; i--)\n        str = marks[i].type.name + \"(\" + str + \")\";\n    return str;\n}\n\n/**\nInstances of this class represent a match state of a node type's\n[content expression](https://prosemirror.net/docs/ref/#model.NodeSpec.content), and can be used to\nfind out whether further content matches here, and whether a given\nposition is a valid end of the node.\n*/\nclass ContentMatch {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    True when this match state represents a valid end of the node.\n    */\n    validEnd) {\n        this.validEnd = validEnd;\n        /**\n        @internal\n        */\n        this.next = [];\n        /**\n        @internal\n        */\n        this.wrapCache = [];\n    }\n    /**\n    @internal\n    */\n    static parse(string, nodeTypes) {\n        let stream = new TokenStream(string, nodeTypes);\n        if (stream.next == null)\n            return ContentMatch.empty;\n        let expr = parseExpr(stream);\n        if (stream.next)\n            stream.err(\"Unexpected trailing text\");\n        let match = dfa(nfa(expr));\n        checkForDeadEnds(match, stream);\n        return match;\n    }\n    /**\n    Match a node type, returning a match after that node if\n    successful.\n    */\n    matchType(type) {\n        for (let i = 0; i < this.next.length; i++)\n            if (this.next[i].type == type)\n                return this.next[i].next;\n        return null;\n    }\n    /**\n    Try to match a fragment. Returns the resulting match when\n    successful.\n    */\n    matchFragment(frag, start = 0, end = frag.childCount) {\n        let cur = this;\n        for (let i = start; cur && i < end; i++)\n            cur = cur.matchType(frag.child(i).type);\n        return cur;\n    }\n    /**\n    @internal\n    */\n    get inlineContent() {\n        return this.next.length != 0 && this.next[0].type.isInline;\n    }\n    /**\n    Get the first matching node type at this match position that can\n    be generated.\n    */\n    get defaultType() {\n        for (let i = 0; i < this.next.length; i++) {\n            let { type } = this.next[i];\n            if (!(type.isText || type.hasRequiredAttrs()))\n                return type;\n        }\n        return null;\n    }\n    /**\n    @internal\n    */\n    compatible(other) {\n        for (let i = 0; i < this.next.length; i++)\n            for (let j = 0; j < other.next.length; j++)\n                if (this.next[i].type == other.next[j].type)\n                    return true;\n        return false;\n    }\n    /**\n    Try to match the given fragment, and if that fails, see if it can\n    be made to match by inserting nodes in front of it. When\n    successful, return a fragment of inserted nodes (which may be\n    empty if nothing had to be inserted). When `toEnd` is true, only\n    return a fragment if the resulting match goes to the end of the\n    content expression.\n    */\n    fillBefore(after, toEnd = false, startIndex = 0) {\n        let seen = [this];\n        function search(match, types) {\n            let finished = match.matchFragment(after, startIndex);\n            if (finished && (!toEnd || finished.validEnd))\n                return Fragment.from(types.map(tp => tp.createAndFill()));\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!(type.isText || type.hasRequiredAttrs()) && seen.indexOf(next) == -1) {\n                    seen.push(next);\n                    let found = search(next, types.concat(type));\n                    if (found)\n                        return found;\n                }\n            }\n            return null;\n        }\n        return search(this, []);\n    }\n    /**\n    Find a set of wrapping node types that would allow a node of the\n    given type to appear at this position. The result may be empty\n    (when it fits directly) and will be null when no such wrapping\n    exists.\n    */\n    findWrapping(target) {\n        for (let i = 0; i < this.wrapCache.length; i += 2)\n            if (this.wrapCache[i] == target)\n                return this.wrapCache[i + 1];\n        let computed = this.computeWrapping(target);\n        this.wrapCache.push(target, computed);\n        return computed;\n    }\n    /**\n    @internal\n    */\n    computeWrapping(target) {\n        let seen = Object.create(null), active = [{ match: this, type: null, via: null }];\n        while (active.length) {\n            let current = active.shift(), match = current.match;\n            if (match.matchType(target)) {\n                let result = [];\n                for (let obj = current; obj.type; obj = obj.via)\n                    result.push(obj.type);\n                return result.reverse();\n            }\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!type.isLeaf && !type.hasRequiredAttrs() && !(type.name in seen) && (!current.type || next.validEnd)) {\n                    active.push({ match: type.contentMatch, type, via: current });\n                    seen[type.name] = true;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n    The number of outgoing edges this node has in the finite\n    automaton that describes the content expression.\n    */\n    get edgeCount() {\n        return this.next.length;\n    }\n    /**\n    Get the _n_​th outgoing edge from this node in the finite\n    automaton that describes the content expression.\n    */\n    edge(n) {\n        if (n >= this.next.length)\n            throw new RangeError(`There's no ${n}th edge in this content match`);\n        return this.next[n];\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let seen = [];\n        function scan(m) {\n            seen.push(m);\n            for (let i = 0; i < m.next.length; i++)\n                if (seen.indexOf(m.next[i].next) == -1)\n                    scan(m.next[i].next);\n        }\n        scan(this);\n        return seen.map((m, i) => {\n            let out = i + (m.validEnd ? \"*\" : \" \") + \" \";\n            for (let i = 0; i < m.next.length; i++)\n                out += (i ? \", \" : \"\") + m.next[i].type.name + \"->\" + seen.indexOf(m.next[i].next);\n            return out;\n        }).join(\"\\n\");\n    }\n}\n/**\n@internal\n*/\nContentMatch.empty = new ContentMatch(true);\nclass TokenStream {\n    constructor(string, nodeTypes) {\n        this.string = string;\n        this.nodeTypes = nodeTypes;\n        this.inline = null;\n        this.pos = 0;\n        this.tokens = string.split(/\\s*(?=\\b|\\W|$)/);\n        if (this.tokens[this.tokens.length - 1] == \"\")\n            this.tokens.pop();\n        if (this.tokens[0] == \"\")\n            this.tokens.shift();\n    }\n    get next() { return this.tokens[this.pos]; }\n    eat(tok) { return this.next == tok && (this.pos++ || true); }\n    err(str) { throw new SyntaxError(str + \" (in content expression '\" + this.string + \"')\"); }\n}\nfunction parseExpr(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSeq(stream));\n    } while (stream.eat(\"|\"));\n    return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n}\nfunction parseExprSeq(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSubscript(stream));\n    } while (stream.next && stream.next != \")\" && stream.next != \"|\");\n    return exprs.length == 1 ? exprs[0] : { type: \"seq\", exprs };\n}\nfunction parseExprSubscript(stream) {\n    let expr = parseExprAtom(stream);\n    for (;;) {\n        if (stream.eat(\"+\"))\n            expr = { type: \"plus\", expr };\n        else if (stream.eat(\"*\"))\n            expr = { type: \"star\", expr };\n        else if (stream.eat(\"?\"))\n            expr = { type: \"opt\", expr };\n        else if (stream.eat(\"{\"))\n            expr = parseExprRange(stream, expr);\n        else\n            break;\n    }\n    return expr;\n}\nfunction parseNum(stream) {\n    if (/\\D/.test(stream.next))\n        stream.err(\"Expected number, got '\" + stream.next + \"'\");\n    let result = Number(stream.next);\n    stream.pos++;\n    return result;\n}\nfunction parseExprRange(stream, expr) {\n    let min = parseNum(stream), max = min;\n    if (stream.eat(\",\")) {\n        if (stream.next != \"}\")\n            max = parseNum(stream);\n        else\n            max = -1;\n    }\n    if (!stream.eat(\"}\"))\n        stream.err(\"Unclosed braced range\");\n    return { type: \"range\", min, max, expr };\n}\nfunction resolveName(stream, name) {\n    let types = stream.nodeTypes, type = types[name];\n    if (type)\n        return [type];\n    let result = [];\n    for (let typeName in types) {\n        let type = types[typeName];\n        if (type.isInGroup(name))\n            result.push(type);\n    }\n    if (result.length == 0)\n        stream.err(\"No node type or group '\" + name + \"' found\");\n    return result;\n}\nfunction parseExprAtom(stream) {\n    if (stream.eat(\"(\")) {\n        let expr = parseExpr(stream);\n        if (!stream.eat(\")\"))\n            stream.err(\"Missing closing paren\");\n        return expr;\n    }\n    else if (!/\\W/.test(stream.next)) {\n        let exprs = resolveName(stream, stream.next).map(type => {\n            if (stream.inline == null)\n                stream.inline = type.isInline;\n            else if (stream.inline != type.isInline)\n                stream.err(\"Mixing inline and block content\");\n            return { type: \"name\", value: type };\n        });\n        stream.pos++;\n        return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n    }\n    else {\n        stream.err(\"Unexpected token '\" + stream.next + \"'\");\n    }\n}\n// Construct an NFA from an expression as returned by the parser. The\n// NFA is represented as an array of states, which are themselves\n// arrays of edges, which are `{term, to}` objects. The first state is\n// the entry state and the last node is the success state.\n//\n// Note that unlike typical NFAs, the edge ordering in this one is\n// significant, in that it is used to contruct filler content when\n// necessary.\nfunction nfa(expr) {\n    let nfa = [[]];\n    connect(compile(expr, 0), node());\n    return nfa;\n    function node() { return nfa.push([]) - 1; }\n    function edge(from, to, term) {\n        let edge = { term, to };\n        nfa[from].push(edge);\n        return edge;\n    }\n    function connect(edges, to) {\n        edges.forEach(edge => edge.to = to);\n    }\n    function compile(expr, from) {\n        if (expr.type == \"choice\") {\n            return expr.exprs.reduce((out, expr) => out.concat(compile(expr, from)), []);\n        }\n        else if (expr.type == \"seq\") {\n            for (let i = 0;; i++) {\n                let next = compile(expr.exprs[i], from);\n                if (i == expr.exprs.length - 1)\n                    return next;\n                connect(next, from = node());\n            }\n        }\n        else if (expr.type == \"star\") {\n            let loop = node();\n            edge(from, loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"plus\") {\n            let loop = node();\n            connect(compile(expr.expr, from), loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"opt\") {\n            return [edge(from)].concat(compile(expr.expr, from));\n        }\n        else if (expr.type == \"range\") {\n            let cur = from;\n            for (let i = 0; i < expr.min; i++) {\n                let next = node();\n                connect(compile(expr.expr, cur), next);\n                cur = next;\n            }\n            if (expr.max == -1) {\n                connect(compile(expr.expr, cur), cur);\n            }\n            else {\n                for (let i = expr.min; i < expr.max; i++) {\n                    let next = node();\n                    edge(cur, next);\n                    connect(compile(expr.expr, cur), next);\n                    cur = next;\n                }\n            }\n            return [edge(cur)];\n        }\n        else if (expr.type == \"name\") {\n            return [edge(from, undefined, expr.value)];\n        }\n        else {\n            throw new Error(\"Unknown expr type\");\n        }\n    }\n}\nfunction cmp(a, b) { return b - a; }\n// Get the set of nodes reachable by null edges from `node`. Omit\n// nodes with only a single null-out-edge, since they may lead to\n// needless duplicated nodes.\nfunction nullFrom(nfa, node) {\n    let result = [];\n    scan(node);\n    return result.sort(cmp);\n    function scan(node) {\n        let edges = nfa[node];\n        if (edges.length == 1 && !edges[0].term)\n            return scan(edges[0].to);\n        result.push(node);\n        for (let i = 0; i < edges.length; i++) {\n            let { term, to } = edges[i];\n            if (!term && result.indexOf(to) == -1)\n                scan(to);\n        }\n    }\n}\n// Compiles an NFA as produced by `nfa` into a DFA, modeled as a set\n// of state objects (`ContentMatch` instances) with transitions\n// between them.\nfunction dfa(nfa) {\n    let labeled = Object.create(null);\n    return explore(nullFrom(nfa, 0));\n    function explore(states) {\n        let out = [];\n        states.forEach(node => {\n            nfa[node].forEach(({ term, to }) => {\n                if (!term)\n                    return;\n                let set;\n                for (let i = 0; i < out.length; i++)\n                    if (out[i][0] == term)\n                        set = out[i][1];\n                nullFrom(nfa, to).forEach(node => {\n                    if (!set)\n                        out.push([term, set = []]);\n                    if (set.indexOf(node) == -1)\n                        set.push(node);\n                });\n            });\n        });\n        let state = labeled[states.join(\",\")] = new ContentMatch(states.indexOf(nfa.length - 1) > -1);\n        for (let i = 0; i < out.length; i++) {\n            let states = out[i][1].sort(cmp);\n            state.next.push({ type: out[i][0], next: labeled[states.join(\",\")] || explore(states) });\n        }\n        return state;\n    }\n}\nfunction checkForDeadEnds(match, stream) {\n    for (let i = 0, work = [match]; i < work.length; i++) {\n        let state = work[i], dead = !state.validEnd, nodes = [];\n        for (let j = 0; j < state.next.length; j++) {\n            let { type, next } = state.next[j];\n            nodes.push(type.name);\n            if (dead && !(type.isText || type.hasRequiredAttrs()))\n                dead = false;\n            if (work.indexOf(next) == -1)\n                work.push(next);\n        }\n        if (dead)\n            stream.err(\"Only non-generatable nodes (\" + nodes.join(\", \") + \") in a required position (see https://prosemirror.net/docs/guide/#generatable)\");\n    }\n}\n\n// For node types where all attrs have a default value (or which don't\n// have any attributes), build up a single reusable default attribute\n// object, and use it for all nodes that don't specify specific\n// attributes.\nfunction defaultAttrs(attrs) {\n    let defaults = Object.create(null);\n    for (let attrName in attrs) {\n        let attr = attrs[attrName];\n        if (!attr.hasDefault)\n            return null;\n        defaults[attrName] = attr.default;\n    }\n    return defaults;\n}\nfunction computeAttrs(attrs, value) {\n    let built = Object.create(null);\n    for (let name in attrs) {\n        let given = value && value[name];\n        if (given === undefined) {\n            let attr = attrs[name];\n            if (attr.hasDefault)\n                given = attr.default;\n            else\n                throw new RangeError(\"No value supplied for attribute \" + name);\n        }\n        built[name] = given;\n    }\n    return built;\n}\nfunction checkAttrs(attrs, values, type, name) {\n    for (let name in values)\n        if (!(name in attrs))\n            throw new RangeError(`Unsupported attribute ${name} for ${type} of type ${name}`);\n    for (let name in attrs) {\n        let attr = attrs[name];\n        if (attr.validate)\n            attr.validate(values[name]);\n    }\n}\nfunction initAttrs(typeName, attrs) {\n    let result = Object.create(null);\n    if (attrs)\n        for (let name in attrs)\n            result[name] = new Attribute(typeName, name, attrs[name]);\n    return result;\n}\n/**\nNode types are objects allocated once per `Schema` and used to\n[tag](https://prosemirror.net/docs/ref/#model.Node.type) `Node` instances. They contain information\nabout the node type, such as its name and what kind of node it\nrepresents.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name the node type has in this schema.\n    */\n    name, \n    /**\n    A link back to the `Schema` the node type belongs to.\n    */\n    schema, \n    /**\n    The spec that this type is based on\n    */\n    spec) {\n        this.name = name;\n        this.schema = schema;\n        this.spec = spec;\n        /**\n        The set of marks allowed in this node. `null` means all marks\n        are allowed.\n        */\n        this.markSet = null;\n        this.groups = spec.group ? spec.group.split(\" \") : [];\n        this.attrs = initAttrs(name, spec.attrs);\n        this.defaultAttrs = defaultAttrs(this.attrs);\n        this.contentMatch = null;\n        this.inlineContent = null;\n        this.isBlock = !(spec.inline || name == \"text\");\n        this.isText = name == \"text\";\n    }\n    /**\n    True if this is an inline type.\n    */\n    get isInline() { return !this.isBlock; }\n    /**\n    True if this is a textblock type, a block that contains inline\n    content.\n    */\n    get isTextblock() { return this.isBlock && this.inlineContent; }\n    /**\n    True for node types that allow no content.\n    */\n    get isLeaf() { return this.contentMatch == ContentMatch.empty; }\n    /**\n    True when this node is an atom, i.e. when it does not have\n    directly editable content.\n    */\n    get isAtom() { return this.isLeaf || !!this.spec.atom; }\n    /**\n    Return true when this node type is part of the given\n    [group](https://prosemirror.net/docs/ref/#model.NodeSpec.group).\n    */\n    isInGroup(group) {\n        return this.groups.indexOf(group) > -1;\n    }\n    /**\n    The node type's [whitespace](https://prosemirror.net/docs/ref/#model.NodeSpec.whitespace) option.\n    */\n    get whitespace() {\n        return this.spec.whitespace || (this.spec.code ? \"pre\" : \"normal\");\n    }\n    /**\n    Tells you whether this node type has any required attributes.\n    */\n    hasRequiredAttrs() {\n        for (let n in this.attrs)\n            if (this.attrs[n].isRequired)\n                return true;\n        return false;\n    }\n    /**\n    Indicates whether this node allows some of the same content as\n    the given node type.\n    */\n    compatibleContent(other) {\n        return this == other || this.contentMatch.compatible(other.contentMatch);\n    }\n    /**\n    @internal\n    */\n    computeAttrs(attrs) {\n        if (!attrs && this.defaultAttrs)\n            return this.defaultAttrs;\n        else\n            return computeAttrs(this.attrs, attrs);\n    }\n    /**\n    Create a `Node` of this type. The given attributes are\n    checked and defaulted (you can pass `null` to use the type's\n    defaults entirely, if no required attributes exist). `content`\n    may be a `Fragment`, a node, an array of nodes, or\n    `null`. Similarly `marks` may be `null` to default to the empty\n    set of marks.\n    */\n    create(attrs = null, content, marks) {\n        if (this.isText)\n            throw new Error(\"NodeType.create can't construct text nodes\");\n        return new Node(this, this.computeAttrs(attrs), Fragment.from(content), Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but check the given content\n    against the node type's content restrictions, and throw an error\n    if it doesn't match.\n    */\n    createChecked(attrs = null, content, marks) {\n        content = Fragment.from(content);\n        this.checkContent(content);\n        return new Node(this, this.computeAttrs(attrs), content, Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but see if it is\n    necessary to add nodes to the start or end of the given fragment\n    to make it fit the node. If no fitting wrapping can be found,\n    return null. Note that, due to the fact that required nodes can\n    always be created, this will always succeed if you pass null or\n    `Fragment.empty` as content.\n    */\n    createAndFill(attrs = null, content, marks) {\n        attrs = this.computeAttrs(attrs);\n        content = Fragment.from(content);\n        if (content.size) {\n            let before = this.contentMatch.fillBefore(content);\n            if (!before)\n                return null;\n            content = before.append(content);\n        }\n        let matched = this.contentMatch.matchFragment(content);\n        let after = matched && matched.fillBefore(Fragment.empty, true);\n        if (!after)\n            return null;\n        return new Node(this, attrs, content.append(after), Mark.setFrom(marks));\n    }\n    /**\n    Returns true if the given fragment is valid content for this node\n    type.\n    */\n    validContent(content) {\n        let result = this.contentMatch.matchFragment(content);\n        if (!result || !result.validEnd)\n            return false;\n        for (let i = 0; i < content.childCount; i++)\n            if (!this.allowsMarks(content.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Throws a RangeError if the given fragment is not valid content for this\n    node type.\n    @internal\n    */\n    checkContent(content) {\n        if (!this.validContent(content))\n            throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0, 50)}`);\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"node\", this.name);\n    }\n    /**\n    Check whether the given mark type is allowed in this node.\n    */\n    allowsMarkType(markType) {\n        return this.markSet == null || this.markSet.indexOf(markType) > -1;\n    }\n    /**\n    Test whether the given set of marks are allowed in this node.\n    */\n    allowsMarks(marks) {\n        if (this.markSet == null)\n            return true;\n        for (let i = 0; i < marks.length; i++)\n            if (!this.allowsMarkType(marks[i].type))\n                return false;\n        return true;\n    }\n    /**\n    Removes the marks that are not allowed in this node from the given set.\n    */\n    allowedMarks(marks) {\n        if (this.markSet == null)\n            return marks;\n        let copy;\n        for (let i = 0; i < marks.length; i++) {\n            if (!this.allowsMarkType(marks[i].type)) {\n                if (!copy)\n                    copy = marks.slice(0, i);\n            }\n            else if (copy) {\n                copy.push(marks[i]);\n            }\n        }\n        return !copy ? marks : copy.length ? copy : Mark.none;\n    }\n    /**\n    @internal\n    */\n    static compile(nodes, schema) {\n        let result = Object.create(null);\n        nodes.forEach((name, spec) => result[name] = new NodeType(name, schema, spec));\n        let topType = schema.spec.topNode || \"doc\";\n        if (!result[topType])\n            throw new RangeError(\"Schema is missing its top node type ('\" + topType + \"')\");\n        if (!result.text)\n            throw new RangeError(\"Every schema needs a 'text' type\");\n        for (let _ in result.text.attrs)\n            throw new RangeError(\"The text node type should not have attributes\");\n        return result;\n    }\n}\nfunction validateType(typeName, attrName, type) {\n    let types = type.split(\"|\");\n    return (value) => {\n        let name = value === null ? \"null\" : typeof value;\n        if (types.indexOf(name) < 0)\n            throw new RangeError(`Expected value of type ${types} for attribute ${attrName} on type ${typeName}, got ${name}`);\n    };\n}\n// Attribute descriptors\nclass Attribute {\n    constructor(typeName, attrName, options) {\n        this.hasDefault = Object.prototype.hasOwnProperty.call(options, \"default\");\n        this.default = options.default;\n        this.validate = typeof options.validate == \"string\" ? validateType(typeName, attrName, options.validate) : options.validate;\n    }\n    get isRequired() {\n        return !this.hasDefault;\n    }\n}\n// Marks\n/**\nLike nodes, marks (which are associated with nodes to signify\nthings like emphasis or being part of a link) are\n[tagged](https://prosemirror.net/docs/ref/#model.Mark.type) with type objects, which are\ninstantiated once per `Schema`.\n*/\nclass MarkType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the mark type.\n    */\n    name, \n    /**\n    @internal\n    */\n    rank, \n    /**\n    The schema that this mark type instance is part of.\n    */\n    schema, \n    /**\n    The spec on which the type is based.\n    */\n    spec) {\n        this.name = name;\n        this.rank = rank;\n        this.schema = schema;\n        this.spec = spec;\n        this.attrs = initAttrs(name, spec.attrs);\n        this.excluded = null;\n        let defaults = defaultAttrs(this.attrs);\n        this.instance = defaults ? new Mark(this, defaults) : null;\n    }\n    /**\n    Create a mark of this type. `attrs` may be `null` or an object\n    containing only some of the mark's attributes. The others, if\n    they have defaults, will be added.\n    */\n    create(attrs = null) {\n        if (!attrs && this.instance)\n            return this.instance;\n        return new Mark(this, computeAttrs(this.attrs, attrs));\n    }\n    /**\n    @internal\n    */\n    static compile(marks, schema) {\n        let result = Object.create(null), rank = 0;\n        marks.forEach((name, spec) => result[name] = new MarkType(name, rank++, schema, spec));\n        return result;\n    }\n    /**\n    When there is a mark of this type in the given set, a new set\n    without it is returned. Otherwise, the input set is returned.\n    */\n    removeFromSet(set) {\n        for (var i = 0; i < set.length; i++)\n            if (set[i].type == this) {\n                set = set.slice(0, i).concat(set.slice(i + 1));\n                i--;\n            }\n        return set;\n    }\n    /**\n    Tests whether there is a mark of this type in the given set.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (set[i].type == this)\n                return set[i];\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"mark\", this.name);\n    }\n    /**\n    Queries whether a given mark type is\n    [excluded](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) by this one.\n    */\n    excludes(other) {\n        return this.excluded.indexOf(other) > -1;\n    }\n}\n/**\nA document schema. Holds [node](https://prosemirror.net/docs/ref/#model.NodeType) and [mark\ntype](https://prosemirror.net/docs/ref/#model.MarkType) objects for the nodes and marks that may\noccur in conforming documents, and provides functionality for\ncreating and deserializing such documents.\n\nWhen given, the type parameters provide the names of the nodes and\nmarks in this schema.\n*/\nclass Schema {\n    /**\n    Construct a schema from a schema [specification](https://prosemirror.net/docs/ref/#model.SchemaSpec).\n    */\n    constructor(spec) {\n        /**\n        The [linebreak\n        replacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement) node defined\n        in this schema, if any.\n        */\n        this.linebreakReplacement = null;\n        /**\n        An object for storing whatever values modules may want to\n        compute and cache per schema. (If you want to store something\n        in it, try to use property names unlikely to clash.)\n        */\n        this.cached = Object.create(null);\n        let instanceSpec = this.spec = {};\n        for (let prop in spec)\n            instanceSpec[prop] = spec[prop];\n        instanceSpec.nodes = OrderedMap.from(spec.nodes),\n            instanceSpec.marks = OrderedMap.from(spec.marks || {}),\n            this.nodes = NodeType.compile(this.spec.nodes, this);\n        this.marks = MarkType.compile(this.spec.marks, this);\n        let contentExprCache = Object.create(null);\n        for (let prop in this.nodes) {\n            if (prop in this.marks)\n                throw new RangeError(prop + \" can not be both a node and a mark\");\n            let type = this.nodes[prop], contentExpr = type.spec.content || \"\", markExpr = type.spec.marks;\n            type.contentMatch = contentExprCache[contentExpr] ||\n                (contentExprCache[contentExpr] = ContentMatch.parse(contentExpr, this.nodes));\n            type.inlineContent = type.contentMatch.inlineContent;\n            if (type.spec.linebreakReplacement) {\n                if (this.linebreakReplacement)\n                    throw new RangeError(\"Multiple linebreak nodes defined\");\n                if (!type.isInline || !type.isLeaf)\n                    throw new RangeError(\"Linebreak replacement nodes must be inline leaf nodes\");\n                this.linebreakReplacement = type;\n            }\n            type.markSet = markExpr == \"_\" ? null :\n                markExpr ? gatherMarks(this, markExpr.split(\" \")) :\n                    markExpr == \"\" || !type.inlineContent ? [] : null;\n        }\n        for (let prop in this.marks) {\n            let type = this.marks[prop], excl = type.spec.excludes;\n            type.excluded = excl == null ? [type] : excl == \"\" ? [] : gatherMarks(this, excl.split(\" \"));\n        }\n        this.nodeFromJSON = json => Node.fromJSON(this, json);\n        this.markFromJSON = json => Mark.fromJSON(this, json);\n        this.topNodeType = this.nodes[this.spec.topNode || \"doc\"];\n        this.cached.wrappings = Object.create(null);\n    }\n    /**\n    Create a node in this schema. The `type` may be a string or a\n    `NodeType` instance. Attributes will be extended with defaults,\n    `content` may be a `Fragment`, `null`, a `Node`, or an array of\n    nodes.\n    */\n    node(type, attrs = null, content, marks) {\n        if (typeof type == \"string\")\n            type = this.nodeType(type);\n        else if (!(type instanceof NodeType))\n            throw new RangeError(\"Invalid node type: \" + type);\n        else if (type.schema != this)\n            throw new RangeError(\"Node type from different schema used (\" + type.name + \")\");\n        return type.createChecked(attrs, content, marks);\n    }\n    /**\n    Create a text node in the schema. Empty text nodes are not\n    allowed.\n    */\n    text(text, marks) {\n        let type = this.nodes.text;\n        return new TextNode(type, type.defaultAttrs, text, Mark.setFrom(marks));\n    }\n    /**\n    Create a mark with the given type and attributes.\n    */\n    mark(type, attrs) {\n        if (typeof type == \"string\")\n            type = this.marks[type];\n        return type.create(attrs);\n    }\n    /**\n    @internal\n    */\n    nodeType(name) {\n        let found = this.nodes[name];\n        if (!found)\n            throw new RangeError(\"Unknown node type: \" + name);\n        return found;\n    }\n}\nfunction gatherMarks(schema, marks) {\n    let found = [];\n    for (let i = 0; i < marks.length; i++) {\n        let name = marks[i], mark = schema.marks[name], ok = mark;\n        if (mark) {\n            found.push(mark);\n        }\n        else {\n            for (let prop in schema.marks) {\n                let mark = schema.marks[prop];\n                if (name == \"_\" || (mark.spec.group && mark.spec.group.split(\" \").indexOf(name) > -1))\n                    found.push(ok = mark);\n            }\n        }\n        if (!ok)\n            throw new SyntaxError(\"Unknown mark type: '\" + marks[i] + \"'\");\n    }\n    return found;\n}\n\nfunction isTagRule(rule) { return rule.tag != null; }\nfunction isStyleRule(rule) { return rule.style != null; }\n/**\nA DOM parser represents a strategy for parsing DOM content into a\nProseMirror document conforming to a given schema. Its behavior is\ndefined by an array of [rules](https://prosemirror.net/docs/ref/#model.ParseRule).\n*/\nclass DOMParser {\n    /**\n    Create a parser that targets the given schema, using the given\n    parsing rules.\n    */\n    constructor(\n    /**\n    The schema into which the parser parses.\n    */\n    schema, \n    /**\n    The set of [parse rules](https://prosemirror.net/docs/ref/#model.ParseRule) that the parser\n    uses, in order of precedence.\n    */\n    rules) {\n        this.schema = schema;\n        this.rules = rules;\n        /**\n        @internal\n        */\n        this.tags = [];\n        /**\n        @internal\n        */\n        this.styles = [];\n        let matchedStyles = this.matchedStyles = [];\n        rules.forEach(rule => {\n            if (isTagRule(rule)) {\n                this.tags.push(rule);\n            }\n            else if (isStyleRule(rule)) {\n                let prop = /[^=]*/.exec(rule.style)[0];\n                if (matchedStyles.indexOf(prop) < 0)\n                    matchedStyles.push(prop);\n                this.styles.push(rule);\n            }\n        });\n        // Only normalize list elements when lists in the schema can't directly contain themselves\n        this.normalizeLists = !this.tags.some(r => {\n            if (!/^(ul|ol)\\b/.test(r.tag) || !r.node)\n                return false;\n            let node = schema.nodes[r.node];\n            return node.contentMatch.matchType(node);\n        });\n    }\n    /**\n    Parse a document from the content of a DOM node.\n    */\n    parse(dom, options = {}) {\n        let context = new ParseContext(this, options, false);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return context.finish();\n    }\n    /**\n    Parses the content of the given DOM node, like\n    [`parse`](https://prosemirror.net/docs/ref/#model.DOMParser.parse), and takes the same set of\n    options. But unlike that method, which produces a whole node,\n    this one returns a slice that is open at the sides, meaning that\n    the schema constraints aren't applied to the start of nodes to\n    the left of the input and the end of nodes at the end.\n    */\n    parseSlice(dom, options = {}) {\n        let context = new ParseContext(this, options, true);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return Slice.maxOpen(context.finish());\n    }\n    /**\n    @internal\n    */\n    matchTag(dom, context, after) {\n        for (let i = after ? this.tags.indexOf(after) + 1 : 0; i < this.tags.length; i++) {\n            let rule = this.tags[i];\n            if (matches(dom, rule.tag) &&\n                (rule.namespace === undefined || dom.namespaceURI == rule.namespace) &&\n                (!rule.context || context.matchesContext(rule.context))) {\n                if (rule.getAttrs) {\n                    let result = rule.getAttrs(dom);\n                    if (result === false)\n                        continue;\n                    rule.attrs = result || undefined;\n                }\n                return rule;\n            }\n        }\n    }\n    /**\n    @internal\n    */\n    matchStyle(prop, value, context, after) {\n        for (let i = after ? this.styles.indexOf(after) + 1 : 0; i < this.styles.length; i++) {\n            let rule = this.styles[i], style = rule.style;\n            if (style.indexOf(prop) != 0 ||\n                rule.context && !context.matchesContext(rule.context) ||\n                // Test that the style string either precisely matches the prop,\n                // or has an '=' sign after the prop, followed by the given\n                // value.\n                style.length > prop.length &&\n                    (style.charCodeAt(prop.length) != 61 || style.slice(prop.length + 1) != value))\n                continue;\n            if (rule.getAttrs) {\n                let result = rule.getAttrs(value);\n                if (result === false)\n                    continue;\n                rule.attrs = result || undefined;\n            }\n            return rule;\n        }\n    }\n    /**\n    @internal\n    */\n    static schemaRules(schema) {\n        let result = [];\n        function insert(rule) {\n            let priority = rule.priority == null ? 50 : rule.priority, i = 0;\n            for (; i < result.length; i++) {\n                let next = result[i], nextPriority = next.priority == null ? 50 : next.priority;\n                if (nextPriority < priority)\n                    break;\n            }\n            result.splice(i, 0, rule);\n        }\n        for (let name in schema.marks) {\n            let rules = schema.marks[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.mark || rule.ignore || rule.clearMark))\n                        rule.mark = name;\n                });\n        }\n        for (let name in schema.nodes) {\n            let rules = schema.nodes[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.node || rule.ignore || rule.mark))\n                        rule.node = name;\n                });\n        }\n        return result;\n    }\n    /**\n    Construct a DOM parser using the parsing rules listed in a\n    schema's [node specs](https://prosemirror.net/docs/ref/#model.NodeSpec.parseDOM), reordered by\n    [priority](https://prosemirror.net/docs/ref/#model.GenericParseRule.priority).\n    */\n    static fromSchema(schema) {\n        return schema.cached.domParser ||\n            (schema.cached.domParser = new DOMParser(schema, DOMParser.schemaRules(schema)));\n    }\n}\nconst blockTags = {\n    address: true, article: true, aside: true, blockquote: true, canvas: true,\n    dd: true, div: true, dl: true, fieldset: true, figcaption: true, figure: true,\n    footer: true, form: true, h1: true, h2: true, h3: true, h4: true, h5: true,\n    h6: true, header: true, hgroup: true, hr: true, li: true, noscript: true, ol: true,\n    output: true, p: true, pre: true, section: true, table: true, tfoot: true, ul: true\n};\nconst ignoreTags = {\n    head: true, noscript: true, object: true, script: true, style: true, title: true\n};\nconst listTags = { ol: true, ul: true };\n// Using a bitfield for node context options\nconst OPT_PRESERVE_WS = 1, OPT_PRESERVE_WS_FULL = 2, OPT_OPEN_LEFT = 4;\nfunction wsOptionsFor(type, preserveWhitespace, base) {\n    if (preserveWhitespace != null)\n        return (preserveWhitespace ? OPT_PRESERVE_WS : 0) |\n            (preserveWhitespace === \"full\" ? OPT_PRESERVE_WS_FULL : 0);\n    return type && type.whitespace == \"pre\" ? OPT_PRESERVE_WS | OPT_PRESERVE_WS_FULL : base & ~OPT_OPEN_LEFT;\n}\nclass NodeContext {\n    constructor(type, attrs, marks, solid, match, options) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.solid = solid;\n        this.options = options;\n        this.content = [];\n        // Marks applied to the node's children\n        this.activeMarks = Mark.none;\n        this.match = match || (options & OPT_OPEN_LEFT ? null : type.contentMatch);\n    }\n    findWrapping(node) {\n        if (!this.match) {\n            if (!this.type)\n                return [];\n            let fill = this.type.contentMatch.fillBefore(Fragment.from(node));\n            if (fill) {\n                this.match = this.type.contentMatch.matchFragment(fill);\n            }\n            else {\n                let start = this.type.contentMatch, wrap;\n                if (wrap = start.findWrapping(node.type)) {\n                    this.match = start;\n                    return wrap;\n                }\n                else {\n                    return null;\n                }\n            }\n        }\n        return this.match.findWrapping(node.type);\n    }\n    finish(openEnd) {\n        if (!(this.options & OPT_PRESERVE_WS)) { // Strip trailing whitespace\n            let last = this.content[this.content.length - 1], m;\n            if (last && last.isText && (m = /[ \\t\\r\\n\\u000c]+$/.exec(last.text))) {\n                let text = last;\n                if (last.text.length == m[0].length)\n                    this.content.pop();\n                else\n                    this.content[this.content.length - 1] = text.withText(text.text.slice(0, text.text.length - m[0].length));\n            }\n        }\n        let content = Fragment.from(this.content);\n        if (!openEnd && this.match)\n            content = content.append(this.match.fillBefore(Fragment.empty, true));\n        return this.type ? this.type.create(this.attrs, content, this.marks) : content;\n    }\n    inlineContext(node) {\n        if (this.type)\n            return this.type.inlineContent;\n        if (this.content.length)\n            return this.content[0].isInline;\n        return node.parentNode && !blockTags.hasOwnProperty(node.parentNode.nodeName.toLowerCase());\n    }\n}\nclass ParseContext {\n    constructor(\n    // The parser we are using.\n    parser, \n    // The options passed to this parse.\n    options, isOpen) {\n        this.parser = parser;\n        this.options = options;\n        this.isOpen = isOpen;\n        this.open = 0;\n        this.localPreserveWS = false;\n        let topNode = options.topNode, topContext;\n        let topOptions = wsOptionsFor(null, options.preserveWhitespace, 0) | (isOpen ? OPT_OPEN_LEFT : 0);\n        if (topNode)\n            topContext = new NodeContext(topNode.type, topNode.attrs, Mark.none, true, options.topMatch || topNode.type.contentMatch, topOptions);\n        else if (isOpen)\n            topContext = new NodeContext(null, null, Mark.none, true, null, topOptions);\n        else\n            topContext = new NodeContext(parser.schema.topNodeType, null, Mark.none, true, null, topOptions);\n        this.nodes = [topContext];\n        this.find = options.findPositions;\n        this.needsBlock = false;\n    }\n    get top() {\n        return this.nodes[this.open];\n    }\n    // Add a DOM node to the content. Text is inserted as text node,\n    // otherwise, the node is passed to `addElement` or, if it has a\n    // `style` attribute, `addElementWithStyles`.\n    addDOM(dom, marks) {\n        if (dom.nodeType == 3)\n            this.addTextNode(dom, marks);\n        else if (dom.nodeType == 1)\n            this.addElement(dom, marks);\n    }\n    addTextNode(dom, marks) {\n        let value = dom.nodeValue;\n        let top = this.top, preserveWS = (top.options & OPT_PRESERVE_WS_FULL) ? \"full\"\n            : this.localPreserveWS || (top.options & OPT_PRESERVE_WS) > 0;\n        if (preserveWS === \"full\" ||\n            top.inlineContext(dom) ||\n            /[^ \\t\\r\\n\\u000c]/.test(value)) {\n            if (!preserveWS) {\n                value = value.replace(/[ \\t\\r\\n\\u000c]+/g, \" \");\n                // If this starts with whitespace, and there is no node before it, or\n                // a hard break, or a text node that ends with whitespace, strip the\n                // leading space.\n                if (/^[ \\t\\r\\n\\u000c]/.test(value) && this.open == this.nodes.length - 1) {\n                    let nodeBefore = top.content[top.content.length - 1];\n                    let domNodeBefore = dom.previousSibling;\n                    if (!nodeBefore ||\n                        (domNodeBefore && domNodeBefore.nodeName == 'BR') ||\n                        (nodeBefore.isText && /[ \\t\\r\\n\\u000c]$/.test(nodeBefore.text)))\n                        value = value.slice(1);\n                }\n            }\n            else if (preserveWS !== \"full\") {\n                value = value.replace(/\\r?\\n|\\r/g, \" \");\n            }\n            else {\n                value = value.replace(/\\r\\n?/g, \"\\n\");\n            }\n            if (value)\n                this.insertNode(this.parser.schema.text(value), marks, !/\\S/.test(value));\n            this.findInText(dom);\n        }\n        else {\n            this.findInside(dom);\n        }\n    }\n    // Try to find a handler for the given tag and use that to parse. If\n    // none is found, the element's content nodes are added directly.\n    addElement(dom, marks, matchAfter) {\n        let outerWS = this.localPreserveWS, top = this.top;\n        if (dom.tagName == \"PRE\" || /pre/.test(dom.style && dom.style.whiteSpace))\n            this.localPreserveWS = true;\n        let name = dom.nodeName.toLowerCase(), ruleID;\n        if (listTags.hasOwnProperty(name) && this.parser.normalizeLists)\n            normalizeList(dom);\n        let rule = (this.options.ruleFromNode && this.options.ruleFromNode(dom)) ||\n            (ruleID = this.parser.matchTag(dom, this, matchAfter));\n        out: if (rule ? rule.ignore : ignoreTags.hasOwnProperty(name)) {\n            this.findInside(dom);\n            this.ignoreFallback(dom, marks);\n        }\n        else if (!rule || rule.skip || rule.closeParent) {\n            if (rule && rule.closeParent)\n                this.open = Math.max(0, this.open - 1);\n            else if (rule && rule.skip.nodeType)\n                dom = rule.skip;\n            let sync, oldNeedsBlock = this.needsBlock;\n            if (blockTags.hasOwnProperty(name)) {\n                if (top.content.length && top.content[0].isInline && this.open) {\n                    this.open--;\n                    top = this.top;\n                }\n                sync = true;\n                if (!top.type)\n                    this.needsBlock = true;\n            }\n            else if (!dom.firstChild) {\n                this.leafFallback(dom, marks);\n                break out;\n            }\n            let innerMarks = rule && rule.skip ? marks : this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addAll(dom, innerMarks);\n            if (sync)\n                this.sync(top);\n            this.needsBlock = oldNeedsBlock;\n        }\n        else {\n            let innerMarks = this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addElementByRule(dom, rule, innerMarks, rule.consuming === false ? ruleID : undefined);\n        }\n        this.localPreserveWS = outerWS;\n    }\n    // Called for leaf DOM nodes that would otherwise be ignored\n    leafFallback(dom, marks) {\n        if (dom.nodeName == \"BR\" && this.top.type && this.top.type.inlineContent)\n            this.addTextNode(dom.ownerDocument.createTextNode(\"\\n\"), marks);\n    }\n    // Called for ignored nodes\n    ignoreFallback(dom, marks) {\n        // Ignored BR nodes should at least create an inline context\n        if (dom.nodeName == \"BR\" && (!this.top.type || !this.top.type.inlineContent))\n            this.findPlace(this.parser.schema.text(\"-\"), marks, true);\n    }\n    // Run any style parser associated with the node's styles. Either\n    // return an updated array of marks, or null to indicate some of the\n    // styles had a rule with `ignore` set.\n    readStyles(dom, marks) {\n        let styles = dom.style;\n        // Because many properties will only show up in 'normalized' form\n        // in `style.item` (i.e. text-decoration becomes\n        // text-decoration-line, text-decoration-color, etc), we directly\n        // query the styles mentioned in our rules instead of iterating\n        // over the items.\n        if (styles && styles.length)\n            for (let i = 0; i < this.parser.matchedStyles.length; i++) {\n                let name = this.parser.matchedStyles[i], value = styles.getPropertyValue(name);\n                if (value)\n                    for (let after = undefined;;) {\n                        let rule = this.parser.matchStyle(name, value, this, after);\n                        if (!rule)\n                            break;\n                        if (rule.ignore)\n                            return null;\n                        if (rule.clearMark)\n                            marks = marks.filter(m => !rule.clearMark(m));\n                        else\n                            marks = marks.concat(this.parser.schema.marks[rule.mark].create(rule.attrs));\n                        if (rule.consuming === false)\n                            after = rule;\n                        else\n                            break;\n                    }\n            }\n        return marks;\n    }\n    // Look up a handler for the given node. If none are found, return\n    // false. Otherwise, apply it, use its return value to drive the way\n    // the node's content is wrapped, and return true.\n    addElementByRule(dom, rule, marks, continueAfter) {\n        let sync, nodeType;\n        if (rule.node) {\n            nodeType = this.parser.schema.nodes[rule.node];\n            if (!nodeType.isLeaf) {\n                let inner = this.enter(nodeType, rule.attrs || null, marks, rule.preserveWhitespace);\n                if (inner) {\n                    sync = true;\n                    marks = inner;\n                }\n            }\n            else if (!this.insertNode(nodeType.create(rule.attrs), marks, dom.nodeName == \"BR\")) {\n                this.leafFallback(dom, marks);\n            }\n        }\n        else {\n            let markType = this.parser.schema.marks[rule.mark];\n            marks = marks.concat(markType.create(rule.attrs));\n        }\n        let startIn = this.top;\n        if (nodeType && nodeType.isLeaf) {\n            this.findInside(dom);\n        }\n        else if (continueAfter) {\n            this.addElement(dom, marks, continueAfter);\n        }\n        else if (rule.getContent) {\n            this.findInside(dom);\n            rule.getContent(dom, this.parser.schema).forEach(node => this.insertNode(node, marks, false));\n        }\n        else {\n            let contentDOM = dom;\n            if (typeof rule.contentElement == \"string\")\n                contentDOM = dom.querySelector(rule.contentElement);\n            else if (typeof rule.contentElement == \"function\")\n                contentDOM = rule.contentElement(dom);\n            else if (rule.contentElement)\n                contentDOM = rule.contentElement;\n            this.findAround(dom, contentDOM, true);\n            this.addAll(contentDOM, marks);\n            this.findAround(dom, contentDOM, false);\n        }\n        if (sync && this.sync(startIn))\n            this.open--;\n    }\n    // Add all child nodes between `startIndex` and `endIndex` (or the\n    // whole node, if not given). If `sync` is passed, use it to\n    // synchronize after every block element.\n    addAll(parent, marks, startIndex, endIndex) {\n        let index = startIndex || 0;\n        for (let dom = startIndex ? parent.childNodes[startIndex] : parent.firstChild, end = endIndex == null ? null : parent.childNodes[endIndex]; dom != end; dom = dom.nextSibling, ++index) {\n            this.findAtPoint(parent, index);\n            this.addDOM(dom, marks);\n        }\n        this.findAtPoint(parent, index);\n    }\n    // Try to find a way to fit the given node type into the current\n    // context. May add intermediate wrappers and/or leave non-solid\n    // nodes that we're in.\n    findPlace(node, marks, cautious) {\n        let route, sync;\n        for (let depth = this.open, penalty = 0; depth >= 0; depth--) {\n            let cx = this.nodes[depth];\n            let found = cx.findWrapping(node);\n            if (found && (!route || route.length > found.length + penalty)) {\n                route = found;\n                sync = cx;\n                if (!found.length)\n                    break;\n            }\n            if (cx.solid) {\n                if (cautious)\n                    break;\n                penalty += 2;\n            }\n        }\n        if (!route)\n            return null;\n        this.sync(sync);\n        for (let i = 0; i < route.length; i++)\n            marks = this.enterInner(route[i], null, marks, false);\n        return marks;\n    }\n    // Try to insert the given node, adjusting the context when needed.\n    insertNode(node, marks, cautious) {\n        if (node.isInline && this.needsBlock && !this.top.type) {\n            let block = this.textblockFromContext();\n            if (block)\n                marks = this.enterInner(block, null, marks);\n        }\n        let innerMarks = this.findPlace(node, marks, cautious);\n        if (innerMarks) {\n            this.closeExtra();\n            let top = this.top;\n            if (top.match)\n                top.match = top.match.matchType(node.type);\n            let nodeMarks = Mark.none;\n            for (let m of innerMarks.concat(node.marks))\n                if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, node.type))\n                    nodeMarks = m.addToSet(nodeMarks);\n            top.content.push(node.mark(nodeMarks));\n            return true;\n        }\n        return false;\n    }\n    // Try to start a node of the given type, adjusting the context when\n    // necessary.\n    enter(type, attrs, marks, preserveWS) {\n        let innerMarks = this.findPlace(type.create(attrs), marks, false);\n        if (innerMarks)\n            innerMarks = this.enterInner(type, attrs, marks, true, preserveWS);\n        return innerMarks;\n    }\n    // Open a node of the given type\n    enterInner(type, attrs, marks, solid = false, preserveWS) {\n        this.closeExtra();\n        let top = this.top;\n        top.match = top.match && top.match.matchType(type);\n        let options = wsOptionsFor(type, preserveWS, top.options);\n        if ((top.options & OPT_OPEN_LEFT) && top.content.length == 0)\n            options |= OPT_OPEN_LEFT;\n        let applyMarks = Mark.none;\n        marks = marks.filter(m => {\n            if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, type)) {\n                applyMarks = m.addToSet(applyMarks);\n                return false;\n            }\n            return true;\n        });\n        this.nodes.push(new NodeContext(type, attrs, applyMarks, solid, null, options));\n        this.open++;\n        return marks;\n    }\n    // Make sure all nodes above this.open are finished and added to\n    // their parents\n    closeExtra(openEnd = false) {\n        let i = this.nodes.length - 1;\n        if (i > this.open) {\n            for (; i > this.open; i--)\n                this.nodes[i - 1].content.push(this.nodes[i].finish(openEnd));\n            this.nodes.length = this.open + 1;\n        }\n    }\n    finish() {\n        this.open = 0;\n        this.closeExtra(this.isOpen);\n        return this.nodes[0].finish(!!(this.isOpen || this.options.topOpen));\n    }\n    sync(to) {\n        for (let i = this.open; i >= 0; i--) {\n            if (this.nodes[i] == to) {\n                this.open = i;\n                return true;\n            }\n            else if (this.localPreserveWS) {\n                this.nodes[i].options |= OPT_PRESERVE_WS;\n            }\n        }\n        return false;\n    }\n    get currentPos() {\n        this.closeExtra();\n        let pos = 0;\n        for (let i = this.open; i >= 0; i--) {\n            let content = this.nodes[i].content;\n            for (let j = content.length - 1; j >= 0; j--)\n                pos += content[j].nodeSize;\n            if (i)\n                pos++;\n        }\n        return pos;\n    }\n    findAtPoint(parent, offset) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == parent && this.find[i].offset == offset)\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findInside(parent) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node))\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findAround(parent, content, before) {\n        if (parent != content && this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node)) {\n                    let pos = content.compareDocumentPosition(this.find[i].node);\n                    if (pos & (before ? 2 : 4))\n                        this.find[i].pos = this.currentPos;\n                }\n            }\n    }\n    findInText(textNode) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == textNode)\n                    this.find[i].pos = this.currentPos - (textNode.nodeValue.length - this.find[i].offset);\n            }\n    }\n    // Determines whether the given context string matches this context.\n    matchesContext(context) {\n        if (context.indexOf(\"|\") > -1)\n            return context.split(/\\s*\\|\\s*/).some(this.matchesContext, this);\n        let parts = context.split(\"/\");\n        let option = this.options.context;\n        let useRoot = !this.isOpen && (!option || option.parent.type == this.nodes[0].type);\n        let minDepth = -(option ? option.depth + 1 : 0) + (useRoot ? 0 : 1);\n        let match = (i, depth) => {\n            for (; i >= 0; i--) {\n                let part = parts[i];\n                if (part == \"\") {\n                    if (i == parts.length - 1 || i == 0)\n                        continue;\n                    for (; depth >= minDepth; depth--)\n                        if (match(i - 1, depth))\n                            return true;\n                    return false;\n                }\n                else {\n                    let next = depth > 0 || (depth == 0 && useRoot) ? this.nodes[depth].type\n                        : option && depth >= minDepth ? option.node(depth - minDepth).type\n                            : null;\n                    if (!next || (next.name != part && !next.isInGroup(part)))\n                        return false;\n                    depth--;\n                }\n            }\n            return true;\n        };\n        return match(parts.length - 1, this.open);\n    }\n    textblockFromContext() {\n        let $context = this.options.context;\n        if ($context)\n            for (let d = $context.depth; d >= 0; d--) {\n                let deflt = $context.node(d).contentMatchAt($context.indexAfter(d)).defaultType;\n                if (deflt && deflt.isTextblock && deflt.defaultAttrs)\n                    return deflt;\n            }\n        for (let name in this.parser.schema.nodes) {\n            let type = this.parser.schema.nodes[name];\n            if (type.isTextblock && type.defaultAttrs)\n                return type;\n        }\n    }\n}\n// Kludge to work around directly nested list nodes produced by some\n// tools and allowed by browsers to mean that the nested list is\n// actually part of the list item above it.\nfunction normalizeList(dom) {\n    for (let child = dom.firstChild, prevItem = null; child; child = child.nextSibling) {\n        let name = child.nodeType == 1 ? child.nodeName.toLowerCase() : null;\n        if (name && listTags.hasOwnProperty(name) && prevItem) {\n            prevItem.appendChild(child);\n            child = prevItem;\n        }\n        else if (name == \"li\") {\n            prevItem = child;\n        }\n        else if (name) {\n            prevItem = null;\n        }\n    }\n}\n// Apply a CSS selector.\nfunction matches(dom, selector) {\n    return (dom.matches || dom.msMatchesSelector || dom.webkitMatchesSelector || dom.mozMatchesSelector).call(dom, selector);\n}\nfunction copy(obj) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    return copy;\n}\n// Used when finding a mark at the top level of a fragment parse.\n// Checks whether it would be reasonable to apply a given mark type to\n// a given node, by looking at the way the mark occurs in the schema.\nfunction markMayApply(markType, nodeType) {\n    let nodes = nodeType.schema.nodes;\n    for (let name in nodes) {\n        let parent = nodes[name];\n        if (!parent.allowsMarkType(markType))\n            continue;\n        let seen = [], scan = (match) => {\n            seen.push(match);\n            for (let i = 0; i < match.edgeCount; i++) {\n                let { type, next } = match.edge(i);\n                if (type == nodeType)\n                    return true;\n                if (seen.indexOf(next) < 0 && scan(next))\n                    return true;\n            }\n        };\n        if (scan(parent.contentMatch))\n            return true;\n    }\n}\n\n/**\nA DOM serializer knows how to convert ProseMirror nodes and\nmarks of various types to DOM nodes.\n*/\nclass DOMSerializer {\n    /**\n    Create a serializer. `nodes` should map node names to functions\n    that take a node and return a description of the corresponding\n    DOM. `marks` does the same for mark names, but also gets an\n    argument that tells it whether the mark's content is block or\n    inline content (for typical use, it'll always be inline). A mark\n    serializer may be `null` to indicate that marks of that type\n    should not be serialized.\n    */\n    constructor(\n    /**\n    The node serialization functions.\n    */\n    nodes, \n    /**\n    The mark serialization functions.\n    */\n    marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    /**\n    Serialize the content of this fragment to a DOM fragment. When\n    not in the browser, the `document` option, containing a DOM\n    document, should be passed so that the serializer can create\n    nodes.\n    */\n    serializeFragment(fragment, options = {}, target) {\n        if (!target)\n            target = doc(options).createDocumentFragment();\n        let top = target, active = [];\n        fragment.forEach(node => {\n            if (active.length || node.marks.length) {\n                let keep = 0, rendered = 0;\n                while (keep < active.length && rendered < node.marks.length) {\n                    let next = node.marks[rendered];\n                    if (!this.marks[next.type.name]) {\n                        rendered++;\n                        continue;\n                    }\n                    if (!next.eq(active[keep][0]) || next.type.spec.spanning === false)\n                        break;\n                    keep++;\n                    rendered++;\n                }\n                while (keep < active.length)\n                    top = active.pop()[1];\n                while (rendered < node.marks.length) {\n                    let add = node.marks[rendered++];\n                    let markDOM = this.serializeMark(add, node.isInline, options);\n                    if (markDOM) {\n                        active.push([add, top]);\n                        top.appendChild(markDOM.dom);\n                        top = markDOM.contentDOM || markDOM.dom;\n                    }\n                }\n            }\n            top.appendChild(this.serializeNodeInner(node, options));\n        });\n        return target;\n    }\n    /**\n    @internal\n    */\n    serializeNodeInner(node, options) {\n        let { dom, contentDOM } = renderSpec(doc(options), this.nodes[node.type.name](node), null, node.attrs);\n        if (contentDOM) {\n            if (node.isLeaf)\n                throw new RangeError(\"Content hole not allowed in a leaf node spec\");\n            this.serializeFragment(node.content, options, contentDOM);\n        }\n        return dom;\n    }\n    /**\n    Serialize this node to a DOM node. This can be useful when you\n    need to serialize a part of a document, as opposed to the whole\n    document. To serialize a whole document, use\n    [`serializeFragment`](https://prosemirror.net/docs/ref/#model.DOMSerializer.serializeFragment) on\n    its [content](https://prosemirror.net/docs/ref/#model.Node.content).\n    */\n    serializeNode(node, options = {}) {\n        let dom = this.serializeNodeInner(node, options);\n        for (let i = node.marks.length - 1; i >= 0; i--) {\n            let wrap = this.serializeMark(node.marks[i], node.isInline, options);\n            if (wrap) {\n                (wrap.contentDOM || wrap.dom).appendChild(dom);\n                dom = wrap.dom;\n            }\n        }\n        return dom;\n    }\n    /**\n    @internal\n    */\n    serializeMark(mark, inline, options = {}) {\n        let toDOM = this.marks[mark.type.name];\n        return toDOM && renderSpec(doc(options), toDOM(mark, inline), null, mark.attrs);\n    }\n    static renderSpec(doc, structure, xmlNS = null, blockArraysIn) {\n        return renderSpec(doc, structure, xmlNS, blockArraysIn);\n    }\n    /**\n    Build a serializer using the [`toDOM`](https://prosemirror.net/docs/ref/#model.NodeSpec.toDOM)\n    properties in a schema's node and mark specs.\n    */\n    static fromSchema(schema) {\n        return schema.cached.domSerializer ||\n            (schema.cached.domSerializer = new DOMSerializer(this.nodesFromSchema(schema), this.marksFromSchema(schema)));\n    }\n    /**\n    Gather the serializers in a schema's node specs into an object.\n    This can be useful as a base to build a custom serializer from.\n    */\n    static nodesFromSchema(schema) {\n        let result = gatherToDOM(schema.nodes);\n        if (!result.text)\n            result.text = node => node.text;\n        return result;\n    }\n    /**\n    Gather the serializers in a schema's mark specs into an object.\n    */\n    static marksFromSchema(schema) {\n        return gatherToDOM(schema.marks);\n    }\n}\nfunction gatherToDOM(obj) {\n    let result = {};\n    for (let name in obj) {\n        let toDOM = obj[name].spec.toDOM;\n        if (toDOM)\n            result[name] = toDOM;\n    }\n    return result;\n}\nfunction doc(options) {\n    return options.document || window.document;\n}\nconst suspiciousAttributeCache = new WeakMap();\nfunction suspiciousAttributes(attrs) {\n    let value = suspiciousAttributeCache.get(attrs);\n    if (value === undefined)\n        suspiciousAttributeCache.set(attrs, value = suspiciousAttributesInner(attrs));\n    return value;\n}\nfunction suspiciousAttributesInner(attrs) {\n    let result = null;\n    function scan(value) {\n        if (value && typeof value == \"object\") {\n            if (Array.isArray(value)) {\n                if (typeof value[0] == \"string\") {\n                    if (!result)\n                        result = [];\n                    result.push(value);\n                }\n                else {\n                    for (let i = 0; i < value.length; i++)\n                        scan(value[i]);\n                }\n            }\n            else {\n                for (let prop in value)\n                    scan(value[prop]);\n            }\n        }\n    }\n    scan(attrs);\n    return result;\n}\nfunction renderSpec(doc, structure, xmlNS, blockArraysIn) {\n    if (typeof structure == \"string\")\n        return { dom: doc.createTextNode(structure) };\n    if (structure.nodeType != null)\n        return { dom: structure };\n    if (structure.dom && structure.dom.nodeType != null)\n        return structure;\n    let tagName = structure[0], suspicious;\n    if (typeof tagName != \"string\")\n        throw new RangeError(\"Invalid array passed to renderSpec\");\n    if (blockArraysIn && (suspicious = suspiciousAttributes(blockArraysIn)) &&\n        suspicious.indexOf(structure) > -1)\n        throw new RangeError(\"Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.\");\n    let space = tagName.indexOf(\" \");\n    if (space > 0) {\n        xmlNS = tagName.slice(0, space);\n        tagName = tagName.slice(space + 1);\n    }\n    let contentDOM;\n    let dom = (xmlNS ? doc.createElementNS(xmlNS, tagName) : doc.createElement(tagName));\n    let attrs = structure[1], start = 1;\n    if (attrs && typeof attrs == \"object\" && attrs.nodeType == null && !Array.isArray(attrs)) {\n        start = 2;\n        for (let name in attrs)\n            if (attrs[name] != null) {\n                let space = name.indexOf(\" \");\n                if (space > 0)\n                    dom.setAttributeNS(name.slice(0, space), name.slice(space + 1), attrs[name]);\n                else if (name == \"style\" && dom.style)\n                    dom.style.cssText = attrs[name];\n                else\n                    dom.setAttribute(name, attrs[name]);\n            }\n    }\n    for (let i = start; i < structure.length; i++) {\n        let child = structure[i];\n        if (child === 0) {\n            if (i < structure.length - 1 || i > start)\n                throw new RangeError(\"Content hole must be the only child of its parent node\");\n            return { dom, contentDOM: dom };\n        }\n        else {\n            let { dom: inner, contentDOM: innerContent } = renderSpec(doc, child, xmlNS, blockArraysIn);\n            dom.appendChild(inner);\n            if (innerContent) {\n                if (contentDOM)\n                    throw new RangeError(\"Multiple content holes\");\n                contentDOM = innerContent;\n            }\n        }\n    }\n    return { dom, contentDOM };\n}\n\nexport { ContentMatch, DOMParser, DOMSerializer, Fragment, Mark, MarkType, Node, NodeRange, NodeType, ReplaceError, ResolvedPos, Schema, Slice };\n", "import { Replace<PERSON><PERSON>r, <PERSON>lice, <PERSON>ag<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'prosemirror-model';\n\n// Recovery values encode a range index and an offset. They are\n// represented as numbers, because tons of them will be created when\n// mapping, for example, a large number of decorations. The number's\n// lower 16 bits provide the index, the remaining bits the offset.\n//\n// Note: We intentionally don't use bit shift operators to en- and\n// decode these, since those clip to 32 bits, which we might in rare\n// cases want to overflow. A 64-bit float can represent 48-bit\n// integers precisely.\nconst lower16 = 0xffff;\nconst factor16 = Math.pow(2, 16);\nfunction makeRecover(index, offset) { return index + offset * factor16; }\nfunction recoverIndex(value) { return value & lower16; }\nfunction recoverOffset(value) { return (value - (value & lower16)) / factor16; }\nconst DEL_BEFORE = 1, DEL_AFTER = 2, DEL_ACROSS = 4, DEL_SIDE = 8;\n/**\nAn object representing a mapped position with extra\ninformation.\n*/\nclass MapResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The mapped version of the position.\n    */\n    pos, \n    /**\n    @internal\n    */\n    delInfo, \n    /**\n    @internal\n    */\n    recover) {\n        this.pos = pos;\n        this.delInfo = delInfo;\n        this.recover = recover;\n    }\n    /**\n    Tells you whether the position was deleted, that is, whether the\n    step removed the token on the side queried (via the `assoc`)\n    argument from the document.\n    */\n    get deleted() { return (this.delInfo & DEL_SIDE) > 0; }\n    /**\n    Tells you whether the token before the mapped position was deleted.\n    */\n    get deletedBefore() { return (this.delInfo & (DEL_BEFORE | DEL_ACROSS)) > 0; }\n    /**\n    True when the token after the mapped position was deleted.\n    */\n    get deletedAfter() { return (this.delInfo & (DEL_AFTER | DEL_ACROSS)) > 0; }\n    /**\n    Tells whether any of the steps mapped through deletes across the\n    position (including both the token before and after the\n    position).\n    */\n    get deletedAcross() { return (this.delInfo & DEL_ACROSS) > 0; }\n}\n/**\nA map describing the deletions and insertions made by a step, which\ncan be used to find the correspondence between positions in the\npre-step version of a document and the same position in the\npost-step version.\n*/\nclass StepMap {\n    /**\n    Create a position map. The modifications to the document are\n    represented as an array of numbers, in which each group of three\n    represents a modified chunk as `[start, oldSize, newSize]`.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    ranges, \n    /**\n    @internal\n    */\n    inverted = false) {\n        this.ranges = ranges;\n        this.inverted = inverted;\n        if (!ranges.length && StepMap.empty)\n            return StepMap.empty;\n    }\n    /**\n    @internal\n    */\n    recover(value) {\n        let diff = 0, index = recoverIndex(value);\n        if (!this.inverted)\n            for (let i = 0; i < index; i++)\n                diff += this.ranges[i * 3 + 2] - this.ranges[i * 3 + 1];\n        return this.ranges[index * 3] + diff + recoverOffset(value);\n    }\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    map(pos, assoc = 1) { return this._map(pos, assoc, true); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let diff = 0, oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex], end = start + oldSize;\n            if (pos <= end) {\n                let side = !oldSize ? assoc : pos == start ? -1 : pos == end ? 1 : assoc;\n                let result = start + diff + (side < 0 ? 0 : newSize);\n                if (simple)\n                    return result;\n                let recover = pos == (assoc < 0 ? start : end) ? null : makeRecover(i / 3, pos - start);\n                let del = pos == start ? DEL_AFTER : pos == end ? DEL_BEFORE : DEL_ACROSS;\n                if (assoc < 0 ? pos != start : pos != end)\n                    del |= DEL_SIDE;\n                return new MapResult(result, del, recover);\n            }\n            diff += newSize - oldSize;\n        }\n        return simple ? pos + diff : new MapResult(pos + diff, 0, null);\n    }\n    /**\n    @internal\n    */\n    touches(pos, recover) {\n        let diff = 0, index = recoverIndex(recover);\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], end = start + oldSize;\n            if (pos <= end && i == index * 3)\n                return true;\n            diff += this.ranges[i + newIndex] - oldSize;\n        }\n        return false;\n    }\n    /**\n    Calls the given function on each of the changed ranges included in\n    this map.\n    */\n    forEach(f) {\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0, diff = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i], oldStart = start - (this.inverted ? diff : 0), newStart = start + (this.inverted ? 0 : diff);\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex];\n            f(oldStart, oldStart + oldSize, newStart, newStart + newSize);\n            diff += newSize - oldSize;\n        }\n    }\n    /**\n    Create an inverted version of this map. The result can be used to\n    map positions in the post-step document to the pre-step document.\n    */\n    invert() {\n        return new StepMap(this.ranges, !this.inverted);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return (this.inverted ? \"-\" : \"\") + JSON.stringify(this.ranges);\n    }\n    /**\n    Create a map that moves all positions by offset `n` (which may be\n    negative). This can be useful when applying steps meant for a\n    sub-document to a larger document, or vice-versa.\n    */\n    static offset(n) {\n        return n == 0 ? StepMap.empty : new StepMap(n < 0 ? [0, -n, 0] : [0, 0, n]);\n    }\n}\n/**\nA StepMap that contains no changed ranges.\n*/\nStepMap.empty = new StepMap([]);\n/**\nA mapping represents a pipeline of zero or more [step\nmaps](https://prosemirror.net/docs/ref/#transform.StepMap). It has special provisions for losslessly\nhandling mapping positions through a series of steps in which some\nsteps are inverted versions of earlier steps. (This comes up when\n‘[rebasing](https://prosemirror.net/docs/guide/#transform.rebasing)’ steps for\ncollaboration or history management.)\n*/\nclass Mapping {\n    /**\n    Create a new mapping with the given position maps.\n    */\n    constructor(maps, \n    /**\n    @internal\n    */\n    mirror, \n    /**\n    The starting position in the `maps` array, used when `map` or\n    `mapResult` is called.\n    */\n    from = 0, \n    /**\n    The end position in the `maps` array.\n    */\n    to = maps ? maps.length : 0) {\n        this.mirror = mirror;\n        this.from = from;\n        this.to = to;\n        this._maps = maps || [];\n        this.ownData = !(maps || mirror);\n    }\n    /**\n    The step maps in this mapping.\n    */\n    get maps() { return this._maps; }\n    /**\n    Create a mapping that maps only through a part of this one.\n    */\n    slice(from = 0, to = this.maps.length) {\n        return new Mapping(this._maps, this.mirror, from, to);\n    }\n    /**\n    Add a step map to the end of this mapping. If `mirrors` is\n    given, it should be the index of the step map that is the mirror\n    image of this one.\n    */\n    appendMap(map, mirrors) {\n        if (!this.ownData) {\n            this._maps = this._maps.slice();\n            this.mirror = this.mirror && this.mirror.slice();\n            this.ownData = true;\n        }\n        this.to = this._maps.push(map);\n        if (mirrors != null)\n            this.setMirror(this._maps.length - 1, mirrors);\n    }\n    /**\n    Add all the step maps in a given mapping to this one (preserving\n    mirroring information).\n    */\n    appendMapping(mapping) {\n        for (let i = 0, startSize = this._maps.length; i < mapping._maps.length; i++) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i], mirr != null && mirr < i ? startSize + mirr : undefined);\n        }\n    }\n    /**\n    Finds the offset of the step map that mirrors the map at the\n    given offset, in this mapping (as per the second argument to\n    `appendMap`).\n    */\n    getMirror(n) {\n        if (this.mirror)\n            for (let i = 0; i < this.mirror.length; i++)\n                if (this.mirror[i] == n)\n                    return this.mirror[i + (i % 2 ? -1 : 1)];\n    }\n    /**\n    @internal\n    */\n    setMirror(n, m) {\n        if (!this.mirror)\n            this.mirror = [];\n        this.mirror.push(n, m);\n    }\n    /**\n    Append the inverse of the given mapping to this one.\n    */\n    appendMappingInverted(mapping) {\n        for (let i = mapping.maps.length - 1, totalSize = this._maps.length + mapping._maps.length; i >= 0; i--) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i].invert(), mirr != null && mirr > i ? totalSize - mirr - 1 : undefined);\n        }\n    }\n    /**\n    Create an inverted version of this mapping.\n    */\n    invert() {\n        let inverse = new Mapping;\n        inverse.appendMappingInverted(this);\n        return inverse;\n    }\n    /**\n    Map a position through this mapping.\n    */\n    map(pos, assoc = 1) {\n        if (this.mirror)\n            return this._map(pos, assoc, true);\n        for (let i = this.from; i < this.to; i++)\n            pos = this._maps[i].map(pos, assoc);\n        return pos;\n    }\n    /**\n    Map a position through this mapping, returning a mapping\n    result.\n    */\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let delInfo = 0;\n        for (let i = this.from; i < this.to; i++) {\n            let map = this._maps[i], result = map.mapResult(pos, assoc);\n            if (result.recover != null) {\n                let corr = this.getMirror(i);\n                if (corr != null && corr > i && corr < this.to) {\n                    i = corr;\n                    pos = this._maps[corr].recover(result.recover);\n                    continue;\n                }\n            }\n            delInfo |= result.delInfo;\n            pos = result.pos;\n        }\n        return simple ? pos : new MapResult(pos, delInfo, null);\n    }\n}\n\nconst stepsByID = Object.create(null);\n/**\nA step object represents an atomic change. It generally applies\nonly to the document it was created for, since the positions\nstored in it will only make sense for that document.\n\nNew steps are defined by creating classes that extend `Step`,\noverriding the `apply`, `invert`, `map`, `getMap` and `fromJSON`\nmethods, and registering your class with a unique\nJSON-serialization identifier using\n[`Step.jsonID`](https://prosemirror.net/docs/ref/#transform.Step^jsonID).\n*/\nclass Step {\n    /**\n    Get the step map that represents the changes made by this step,\n    and which can be used to transform between positions in the old\n    and the new document.\n    */\n    getMap() { return StepMap.empty; }\n    /**\n    Try to merge this step with another one, to be applied directly\n    after it. Returns the merged step when possible, null if the\n    steps can't be merged.\n    */\n    merge(other) { return null; }\n    /**\n    Deserialize a step from its JSON representation. Will call\n    through to the step class' own implementation of this method.\n    */\n    static fromJSON(schema, json) {\n        if (!json || !json.stepType)\n            throw new RangeError(\"Invalid input for Step.fromJSON\");\n        let type = stepsByID[json.stepType];\n        if (!type)\n            throw new RangeError(`No step type ${json.stepType} defined`);\n        return type.fromJSON(schema, json);\n    }\n    /**\n    To be able to serialize steps to JSON, each step needs a string\n    ID to attach to its JSON representation. Use this method to\n    register an ID for your step classes. Try to pick something\n    that's unlikely to clash with steps from other modules.\n    */\n    static jsonID(id, stepClass) {\n        if (id in stepsByID)\n            throw new RangeError(\"Duplicate use of step JSON ID \" + id);\n        stepsByID[id] = stepClass;\n        stepClass.prototype.jsonID = id;\n        return stepClass;\n    }\n}\n/**\nThe result of [applying](https://prosemirror.net/docs/ref/#transform.Step.apply) a step. Contains either a\nnew document or a failure value.\n*/\nclass StepResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The transformed document, if successful.\n    */\n    doc, \n    /**\n    The failure message, if unsuccessful.\n    */\n    failed) {\n        this.doc = doc;\n        this.failed = failed;\n    }\n    /**\n    Create a successful step result.\n    */\n    static ok(doc) { return new StepResult(doc, null); }\n    /**\n    Create a failed step result.\n    */\n    static fail(message) { return new StepResult(null, message); }\n    /**\n    Call [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) with the given\n    arguments. Create a successful result if it succeeds, and a\n    failed one if it throws a `ReplaceError`.\n    */\n    static fromReplace(doc, from, to, slice) {\n        try {\n            return StepResult.ok(doc.replace(from, to, slice));\n        }\n        catch (e) {\n            if (e instanceof ReplaceError)\n                return StepResult.fail(e.message);\n            throw e;\n        }\n    }\n}\n\nfunction mapFragment(fragment, f, parent) {\n    let mapped = [];\n    for (let i = 0; i < fragment.childCount; i++) {\n        let child = fragment.child(i);\n        if (child.content.size)\n            child = child.copy(mapFragment(child.content, f, child));\n        if (child.isInline)\n            child = f(child, parent, i);\n        mapped.push(child);\n    }\n    return Fragment.fromArray(mapped);\n}\n/**\nAdd a mark to all inline content between two positions.\n*/\nclass AddMarkStep extends Step {\n    /**\n    Create a mark step.\n    */\n    constructor(\n    /**\n    The start of the marked range.\n    */\n    from, \n    /**\n    The end of the marked range.\n    */\n    to, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to), $from = doc.resolve(this.from);\n        let parent = $from.node($from.sharedDepth(this.to));\n        let slice = new Slice(mapFragment(oldSlice.content, (node, parent) => {\n            if (!node.isAtom || !parent.type.allowsMarkType(this.mark.type))\n                return node;\n            return node.mark(this.mark.addToSet(node.marks));\n        }, parent), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new RemoveMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new AddMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof AddMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new AddMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"addMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for AddMarkStep.fromJSON\");\n        return new AddMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addMark\", AddMarkStep);\n/**\nRemove a mark from all inline content between two positions.\n*/\nclass RemoveMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The start of the unmarked range.\n    */\n    from, \n    /**\n    The end of the unmarked range.\n    */\n    to, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to);\n        let slice = new Slice(mapFragment(oldSlice.content, node => {\n            return node.mark(this.mark.removeFromSet(node.marks));\n        }, doc), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new AddMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new RemoveMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof RemoveMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new RemoveMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"removeMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for RemoveMarkStep.fromJSON\");\n        return new RemoveMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeMark\", RemoveMarkStep);\n/**\nAdd a mark to a specific node.\n*/\nclass AddNodeMarkStep extends Step {\n    /**\n    Create a node mark step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.addToSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new Slice(Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (node) {\n            let newSet = this.mark.addToSet(node.marks);\n            if (newSet.length == node.marks.length) {\n                for (let i = 0; i < node.marks.length; i++)\n                    if (!node.marks[i].isInSet(newSet))\n                        return new AddNodeMarkStep(this.pos, node.marks[i]);\n                return new AddNodeMarkStep(this.pos, this.mark);\n            }\n        }\n        return new RemoveNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AddNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"addNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for AddNodeMarkStep.fromJSON\");\n        return new AddNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addNodeMark\", AddNodeMarkStep);\n/**\nRemove a mark from a specific node.\n*/\nclass RemoveNodeMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.removeFromSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new Slice(Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node || !this.mark.isInSet(node.marks))\n            return this;\n        return new AddNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new RemoveNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"removeNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for RemoveNodeMarkStep.fromJSON\");\n        return new RemoveNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeNodeMark\", RemoveNodeMarkStep);\n\n/**\nReplace a part of the document with a slice of new content.\n*/\nclass ReplaceStep extends Step {\n    /**\n    The given `slice` should fit the 'gap' between `from` and\n    `to`—the depths must line up, and the surrounding nodes must be\n    able to be joined with the open sides of the slice. When\n    `structure` is true, the step will fail if the content between\n    from and to is not just a sequence of closing and then opening\n    tokens (this is to guard against rebased replace steps\n    overwriting something they weren't supposed to).\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.slice = slice;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && contentBetween(doc, this.from, this.to))\n            return StepResult.fail(\"Structure replace would overwrite content\");\n        return StepResult.fromReplace(doc, this.from, this.to, this.slice);\n    }\n    getMap() {\n        return new StepMap([this.from, this.to - this.from, this.slice.size]);\n    }\n    invert(doc) {\n        return new ReplaceStep(this.from, this.from + this.slice.size, doc.slice(this.from, this.to));\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deletedAcross && to.deletedAcross)\n            return null;\n        return new ReplaceStep(from.pos, Math.max(from.pos, to.pos), this.slice, this.structure);\n    }\n    merge(other) {\n        if (!(other instanceof ReplaceStep) || other.structure || this.structure)\n            return null;\n        if (this.from + this.slice.size == other.from && !this.slice.openEnd && !other.slice.openStart) {\n            let slice = this.slice.size + other.slice.size == 0 ? Slice.empty\n                : new Slice(this.slice.content.append(other.slice.content), this.slice.openStart, other.slice.openEnd);\n            return new ReplaceStep(this.from, this.to + (other.to - other.from), slice, this.structure);\n        }\n        else if (other.to == this.from && !this.slice.openStart && !other.slice.openEnd) {\n            let slice = this.slice.size + other.slice.size == 0 ? Slice.empty\n                : new Slice(other.slice.content.append(this.slice.content), other.slice.openStart, this.slice.openEnd);\n            return new ReplaceStep(other.from, this.to, slice, this.structure);\n        }\n        else {\n            return null;\n        }\n    }\n    toJSON() {\n        let json = { stepType: \"replace\", from: this.from, to: this.to };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceStep.fromJSON\");\n        return new ReplaceStep(json.from, json.to, Slice.fromJSON(schema, json.slice), !!json.structure);\n    }\n}\nStep.jsonID(\"replace\", ReplaceStep);\n/**\nReplace a part of the document with a slice of content, but\npreserve a range of the replaced content by moving it into the\nslice.\n*/\nclass ReplaceAroundStep extends Step {\n    /**\n    Create a replace-around step with the given range and gap.\n    `insert` should be the point in the slice into which the content\n    of the gap should be moved. `structure` has the same meaning as\n    it has in the [`ReplaceStep`](https://prosemirror.net/docs/ref/#transform.ReplaceStep) class.\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The start of preserved range.\n    */\n    gapFrom, \n    /**\n    The end of preserved range.\n    */\n    gapTo, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    The position in the slice where the preserved range should be\n    inserted.\n    */\n    insert, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.gapFrom = gapFrom;\n        this.gapTo = gapTo;\n        this.slice = slice;\n        this.insert = insert;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && (contentBetween(doc, this.from, this.gapFrom) ||\n            contentBetween(doc, this.gapTo, this.to)))\n            return StepResult.fail(\"Structure gap-replace would overwrite content\");\n        let gap = doc.slice(this.gapFrom, this.gapTo);\n        if (gap.openStart || gap.openEnd)\n            return StepResult.fail(\"Gap is not a flat range\");\n        let inserted = this.slice.insertAt(this.insert, gap.content);\n        if (!inserted)\n            return StepResult.fail(\"Content does not fit in gap\");\n        return StepResult.fromReplace(doc, this.from, this.to, inserted);\n    }\n    getMap() {\n        return new StepMap([this.from, this.gapFrom - this.from, this.insert,\n            this.gapTo, this.to - this.gapTo, this.slice.size - this.insert]);\n    }\n    invert(doc) {\n        let gap = this.gapTo - this.gapFrom;\n        return new ReplaceAroundStep(this.from, this.from + this.slice.size + gap, this.from + this.insert, this.from + this.insert + gap, doc.slice(this.from, this.to).removeBetween(this.gapFrom - this.from, this.gapTo - this.from), this.gapFrom - this.from, this.structure);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        let gapFrom = this.from == this.gapFrom ? from.pos : mapping.map(this.gapFrom, -1);\n        let gapTo = this.to == this.gapTo ? to.pos : mapping.map(this.gapTo, 1);\n        if ((from.deletedAcross && to.deletedAcross) || gapFrom < from.pos || gapTo > to.pos)\n            return null;\n        return new ReplaceAroundStep(from.pos, to.pos, gapFrom, gapTo, this.slice, this.insert, this.structure);\n    }\n    toJSON() {\n        let json = { stepType: \"replaceAround\", from: this.from, to: this.to,\n            gapFrom: this.gapFrom, gapTo: this.gapTo, insert: this.insert };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\" ||\n            typeof json.gapFrom != \"number\" || typeof json.gapTo != \"number\" || typeof json.insert != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceAroundStep.fromJSON\");\n        return new ReplaceAroundStep(json.from, json.to, json.gapFrom, json.gapTo, Slice.fromJSON(schema, json.slice), json.insert, !!json.structure);\n    }\n}\nStep.jsonID(\"replaceAround\", ReplaceAroundStep);\nfunction contentBetween(doc, from, to) {\n    let $from = doc.resolve(from), dist = to - from, depth = $from.depth;\n    while (dist > 0 && depth > 0 && $from.indexAfter(depth) == $from.node(depth).childCount) {\n        depth--;\n        dist--;\n    }\n    if (dist > 0) {\n        let next = $from.node(depth).maybeChild($from.indexAfter(depth));\n        while (dist > 0) {\n            if (!next || next.isLeaf)\n                return true;\n            next = next.firstChild;\n            dist--;\n        }\n    }\n    return false;\n}\n\nfunction addMark(tr, from, to, mark) {\n    let removed = [], added = [];\n    let removing, adding;\n    tr.doc.nodesBetween(from, to, (node, pos, parent) => {\n        if (!node.isInline)\n            return;\n        let marks = node.marks;\n        if (!mark.isInSet(marks) && parent.type.allowsMarkType(mark.type)) {\n            let start = Math.max(pos, from), end = Math.min(pos + node.nodeSize, to);\n            let newSet = mark.addToSet(marks);\n            for (let i = 0; i < marks.length; i++) {\n                if (!marks[i].isInSet(newSet)) {\n                    if (removing && removing.to == start && removing.mark.eq(marks[i]))\n                        removing.to = end;\n                    else\n                        removed.push(removing = new RemoveMarkStep(start, end, marks[i]));\n                }\n            }\n            if (adding && adding.to == start)\n                adding.to = end;\n            else\n                added.push(adding = new AddMarkStep(start, end, mark));\n        }\n    });\n    removed.forEach(s => tr.step(s));\n    added.forEach(s => tr.step(s));\n}\nfunction removeMark(tr, from, to, mark) {\n    let matched = [], step = 0;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        if (!node.isInline)\n            return;\n        step++;\n        let toRemove = null;\n        if (mark instanceof MarkType) {\n            let set = node.marks, found;\n            while (found = mark.isInSet(set)) {\n                (toRemove || (toRemove = [])).push(found);\n                set = found.removeFromSet(set);\n            }\n        }\n        else if (mark) {\n            if (mark.isInSet(node.marks))\n                toRemove = [mark];\n        }\n        else {\n            toRemove = node.marks;\n        }\n        if (toRemove && toRemove.length) {\n            let end = Math.min(pos + node.nodeSize, to);\n            for (let i = 0; i < toRemove.length; i++) {\n                let style = toRemove[i], found;\n                for (let j = 0; j < matched.length; j++) {\n                    let m = matched[j];\n                    if (m.step == step - 1 && style.eq(matched[j].style))\n                        found = m;\n                }\n                if (found) {\n                    found.to = end;\n                    found.step = step;\n                }\n                else {\n                    matched.push({ style, from: Math.max(pos, from), to: end, step });\n                }\n            }\n        }\n    });\n    matched.forEach(m => tr.step(new RemoveMarkStep(m.from, m.to, m.style)));\n}\nfunction clearIncompatible(tr, pos, parentType, match = parentType.contentMatch, clearNewlines = true) {\n    let node = tr.doc.nodeAt(pos);\n    let replSteps = [], cur = pos + 1;\n    for (let i = 0; i < node.childCount; i++) {\n        let child = node.child(i), end = cur + child.nodeSize;\n        let allowed = match.matchType(child.type);\n        if (!allowed) {\n            replSteps.push(new ReplaceStep(cur, end, Slice.empty));\n        }\n        else {\n            match = allowed;\n            for (let j = 0; j < child.marks.length; j++)\n                if (!parentType.allowsMarkType(child.marks[j].type))\n                    tr.step(new RemoveMarkStep(cur, end, child.marks[j]));\n            if (clearNewlines && child.isText && parentType.whitespace != \"pre\") {\n                let m, newline = /\\r?\\n|\\r/g, slice;\n                while (m = newline.exec(child.text)) {\n                    if (!slice)\n                        slice = new Slice(Fragment.from(parentType.schema.text(\" \", parentType.allowedMarks(child.marks))), 0, 0);\n                    replSteps.push(new ReplaceStep(cur + m.index, cur + m.index + m[0].length, slice));\n                }\n            }\n        }\n        cur = end;\n    }\n    if (!match.validEnd) {\n        let fill = match.fillBefore(Fragment.empty, true);\n        tr.replace(cur, cur, new Slice(fill, 0, 0));\n    }\n    for (let i = replSteps.length - 1; i >= 0; i--)\n        tr.step(replSteps[i]);\n}\n\nfunction canCut(node, start, end) {\n    return (start == 0 || node.canReplace(start, node.childCount)) &&\n        (end == node.childCount || node.canReplace(0, end));\n}\n/**\nTry to find a target depth to which the content in the given range\ncan be lifted. Will not go across\n[isolating](https://prosemirror.net/docs/ref/#model.NodeSpec.isolating) parent nodes.\n*/\nfunction liftTarget(range) {\n    let parent = range.parent;\n    let content = parent.content.cutByIndex(range.startIndex, range.endIndex);\n    for (let depth = range.depth;; --depth) {\n        let node = range.$from.node(depth);\n        let index = range.$from.index(depth), endIndex = range.$to.indexAfter(depth);\n        if (depth < range.depth && node.canReplace(index, endIndex, content))\n            return depth;\n        if (depth == 0 || node.type.spec.isolating || !canCut(node, index, endIndex))\n            break;\n    }\n    return null;\n}\nfunction lift(tr, range, target) {\n    let { $from, $to, depth } = range;\n    let gapStart = $from.before(depth + 1), gapEnd = $to.after(depth + 1);\n    let start = gapStart, end = gapEnd;\n    let before = Fragment.empty, openStart = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $from.index(d) > 0) {\n            splitting = true;\n            before = Fragment.from($from.node(d).copy(before));\n            openStart++;\n        }\n        else {\n            start--;\n        }\n    let after = Fragment.empty, openEnd = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $to.after(d + 1) < $to.end(d)) {\n            splitting = true;\n            after = Fragment.from($to.node(d).copy(after));\n            openEnd++;\n        }\n        else {\n            end++;\n        }\n    tr.step(new ReplaceAroundStep(start, end, gapStart, gapEnd, new Slice(before.append(after), openStart, openEnd), before.size - openStart, true));\n}\n/**\nTry to find a valid way to wrap the content in the given range in a\nnode of the given type. May introduce extra nodes around and inside\nthe wrapper node, if necessary. Returns null if no valid wrapping\ncould be found. When `innerRange` is given, that range's content is\nused as the content to fit into the wrapping, instead of the\ncontent of `range`.\n*/\nfunction findWrapping(range, nodeType, attrs = null, innerRange = range) {\n    let around = findWrappingOutside(range, nodeType);\n    let inner = around && findWrappingInside(innerRange, nodeType);\n    if (!inner)\n        return null;\n    return around.map(withAttrs)\n        .concat({ type: nodeType, attrs }).concat(inner.map(withAttrs));\n}\nfunction withAttrs(type) { return { type, attrs: null }; }\nfunction findWrappingOutside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let around = parent.contentMatchAt(startIndex).findWrapping(type);\n    if (!around)\n        return null;\n    let outer = around.length ? around[0] : type;\n    return parent.canReplaceWith(startIndex, endIndex, outer) ? around : null;\n}\nfunction findWrappingInside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let inner = parent.child(startIndex);\n    let inside = type.contentMatch.findWrapping(inner.type);\n    if (!inside)\n        return null;\n    let lastType = inside.length ? inside[inside.length - 1] : type;\n    let innerMatch = lastType.contentMatch;\n    for (let i = startIndex; innerMatch && i < endIndex; i++)\n        innerMatch = innerMatch.matchType(parent.child(i).type);\n    if (!innerMatch || !innerMatch.validEnd)\n        return null;\n    return inside;\n}\nfunction wrap(tr, range, wrappers) {\n    let content = Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--) {\n        if (content.size) {\n            let match = wrappers[i].type.contentMatch.matchFragment(content);\n            if (!match || !match.validEnd)\n                throw new RangeError(\"Wrapper type given to Transform.wrap does not form valid content of its parent wrapper\");\n        }\n        content = Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    }\n    let start = range.start, end = range.end;\n    tr.step(new ReplaceAroundStep(start, end, start, end, new Slice(content, 0, 0), wrappers.length, true));\n}\nfunction setBlockType(tr, from, to, type, attrs) {\n    if (!type.isTextblock)\n        throw new RangeError(\"Type given to setBlockType should be a textblock\");\n    let mapFrom = tr.steps.length;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        let attrsHere = typeof attrs == \"function\" ? attrs(node) : attrs;\n        if (node.isTextblock && !node.hasMarkup(type, attrsHere) &&\n            canChangeType(tr.doc, tr.mapping.slice(mapFrom).map(pos), type)) {\n            let convertNewlines = null;\n            if (type.schema.linebreakReplacement) {\n                let pre = type.whitespace == \"pre\", supportLinebreak = !!type.contentMatch.matchType(type.schema.linebreakReplacement);\n                if (pre && !supportLinebreak)\n                    convertNewlines = false;\n                else if (!pre && supportLinebreak)\n                    convertNewlines = true;\n            }\n            // Ensure all markup that isn't allowed in the new node type is cleared\n            if (convertNewlines === false)\n                replaceLinebreaks(tr, node, pos, mapFrom);\n            clearIncompatible(tr, tr.mapping.slice(mapFrom).map(pos, 1), type, undefined, convertNewlines === null);\n            let mapping = tr.mapping.slice(mapFrom);\n            let startM = mapping.map(pos, 1), endM = mapping.map(pos + node.nodeSize, 1);\n            tr.step(new ReplaceAroundStep(startM, endM, startM + 1, endM - 1, new Slice(Fragment.from(type.create(attrsHere, null, node.marks)), 0, 0), 1, true));\n            if (convertNewlines === true)\n                replaceNewlines(tr, node, pos, mapFrom);\n            return false;\n        }\n    });\n}\nfunction replaceNewlines(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.isText) {\n            let m, newline = /\\r?\\n|\\r/g;\n            while (m = newline.exec(child.text)) {\n                let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset + m.index);\n                tr.replaceWith(start, start + 1, node.type.schema.linebreakReplacement.create());\n            }\n        }\n    });\n}\nfunction replaceLinebreaks(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.type == child.type.schema.linebreakReplacement) {\n            let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset);\n            tr.replaceWith(start, start + 1, node.type.schema.text(\"\\n\"));\n        }\n    });\n}\nfunction canChangeType(doc, pos, type) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return $pos.parent.canReplaceWith(index, index + 1, type);\n}\n/**\nChange the type, attributes, and/or marks of the node at `pos`.\nWhen `type` isn't given, the existing node type is preserved,\n*/\nfunction setNodeMarkup(tr, pos, type, attrs, marks) {\n    let node = tr.doc.nodeAt(pos);\n    if (!node)\n        throw new RangeError(\"No node at given position\");\n    if (!type)\n        type = node.type;\n    let newNode = type.create(attrs, null, marks || node.marks);\n    if (node.isLeaf)\n        return tr.replaceWith(pos, pos + node.nodeSize, newNode);\n    if (!type.validContent(node.content))\n        throw new RangeError(\"Invalid content for node type \" + type.name);\n    tr.step(new ReplaceAroundStep(pos, pos + node.nodeSize, pos + 1, pos + node.nodeSize - 1, new Slice(Fragment.from(newNode), 0, 0), 1, true));\n}\n/**\nCheck whether splitting at the given position is allowed.\n*/\nfunction canSplit(doc, pos, depth = 1, typesAfter) {\n    let $pos = doc.resolve(pos), base = $pos.depth - depth;\n    let innerType = (typesAfter && typesAfter[typesAfter.length - 1]) || $pos.parent;\n    if (base < 0 || $pos.parent.type.spec.isolating ||\n        !$pos.parent.canReplace($pos.index(), $pos.parent.childCount) ||\n        !innerType.type.validContent($pos.parent.content.cutByIndex($pos.index(), $pos.parent.childCount)))\n        return false;\n    for (let d = $pos.depth - 1, i = depth - 2; d > base; d--, i--) {\n        let node = $pos.node(d), index = $pos.index(d);\n        if (node.type.spec.isolating)\n            return false;\n        let rest = node.content.cutByIndex(index, node.childCount);\n        let overrideChild = typesAfter && typesAfter[i + 1];\n        if (overrideChild)\n            rest = rest.replaceChild(0, overrideChild.type.create(overrideChild.attrs));\n        let after = (typesAfter && typesAfter[i]) || node;\n        if (!node.canReplace(index + 1, node.childCount) || !after.type.validContent(rest))\n            return false;\n    }\n    let index = $pos.indexAfter(base);\n    let baseType = typesAfter && typesAfter[0];\n    return $pos.node(base).canReplaceWith(index, index, baseType ? baseType.type : $pos.node(base + 1).type);\n}\nfunction split(tr, pos, depth = 1, typesAfter) {\n    let $pos = tr.doc.resolve(pos), before = Fragment.empty, after = Fragment.empty;\n    for (let d = $pos.depth, e = $pos.depth - depth, i = depth - 1; d > e; d--, i--) {\n        before = Fragment.from($pos.node(d).copy(before));\n        let typeAfter = typesAfter && typesAfter[i];\n        after = Fragment.from(typeAfter ? typeAfter.type.create(typeAfter.attrs, after) : $pos.node(d).copy(after));\n    }\n    tr.step(new ReplaceStep(pos, pos, new Slice(before.append(after), depth, depth), true));\n}\n/**\nTest whether the blocks before and after a given position can be\njoined.\n*/\nfunction canJoin(doc, pos) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return joinable($pos.nodeBefore, $pos.nodeAfter) &&\n        $pos.parent.canReplace(index, index + 1);\n}\nfunction canAppendWithSubstitutedLinebreaks(a, b) {\n    if (!b.content.size)\n        a.type.compatibleContent(b.type);\n    let match = a.contentMatchAt(a.childCount);\n    let { linebreakReplacement } = a.type.schema;\n    for (let i = 0; i < b.childCount; i++) {\n        let child = b.child(i);\n        let type = child.type == linebreakReplacement ? a.type.schema.nodes.text : child.type;\n        match = match.matchType(type);\n        if (!match)\n            return false;\n        if (!a.type.allowsMarks(child.marks))\n            return false;\n    }\n    return match.validEnd;\n}\nfunction joinable(a, b) {\n    return !!(a && b && !a.isLeaf && canAppendWithSubstitutedLinebreaks(a, b));\n}\n/**\nFind an ancestor of the given position that can be joined to the\nblock before (or after if `dir` is positive). Returns the joinable\npoint, if any.\n*/\nfunction joinPoint(doc, pos, dir = -1) {\n    let $pos = doc.resolve(pos);\n    for (let d = $pos.depth;; d--) {\n        let before, after, index = $pos.index(d);\n        if (d == $pos.depth) {\n            before = $pos.nodeBefore;\n            after = $pos.nodeAfter;\n        }\n        else if (dir > 0) {\n            before = $pos.node(d + 1);\n            index++;\n            after = $pos.node(d).maybeChild(index);\n        }\n        else {\n            before = $pos.node(d).maybeChild(index - 1);\n            after = $pos.node(d + 1);\n        }\n        if (before && !before.isTextblock && joinable(before, after) &&\n            $pos.node(d).canReplace(index, index + 1))\n            return pos;\n        if (d == 0)\n            break;\n        pos = dir < 0 ? $pos.before(d) : $pos.after(d);\n    }\n}\nfunction join(tr, pos, depth) {\n    let convertNewlines = null;\n    let { linebreakReplacement } = tr.doc.type.schema;\n    let $before = tr.doc.resolve(pos - depth), beforeType = $before.node().type;\n    if (linebreakReplacement && beforeType.inlineContent) {\n        let pre = beforeType.whitespace == \"pre\";\n        let supportLinebreak = !!beforeType.contentMatch.matchType(linebreakReplacement);\n        if (pre && !supportLinebreak)\n            convertNewlines = false;\n        else if (!pre && supportLinebreak)\n            convertNewlines = true;\n    }\n    let mapFrom = tr.steps.length;\n    if (convertNewlines === false) {\n        let $after = tr.doc.resolve(pos + depth);\n        replaceLinebreaks(tr, $after.node(), $after.before(), mapFrom);\n    }\n    if (beforeType.inlineContent)\n        clearIncompatible(tr, pos + depth - 1, beforeType, $before.node().contentMatchAt($before.index()), convertNewlines == null);\n    let mapping = tr.mapping.slice(mapFrom), start = mapping.map(pos - depth);\n    tr.step(new ReplaceStep(start, mapping.map(pos + depth, -1), Slice.empty, true));\n    if (convertNewlines === true) {\n        let $full = tr.doc.resolve(start);\n        replaceNewlines(tr, $full.node(), $full.before(), tr.steps.length);\n    }\n    return tr;\n}\n/**\nTry to find a point where a node of the given type can be inserted\nnear `pos`, by searching up the node hierarchy when `pos` itself\nisn't a valid place but is at the start or end of a node. Return\nnull if no position was found.\n*/\nfunction insertPoint(doc, pos, nodeType) {\n    let $pos = doc.resolve(pos);\n    if ($pos.parent.canReplaceWith($pos.index(), $pos.index(), nodeType))\n        return pos;\n    if ($pos.parentOffset == 0)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.index(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.before(d + 1);\n            if (index > 0)\n                return null;\n        }\n    if ($pos.parentOffset == $pos.parent.content.size)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.indexAfter(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.after(d + 1);\n            if (index < $pos.node(d).childCount)\n                return null;\n        }\n    return null;\n}\n/**\nFinds a position at or around the given position where the given\nslice can be inserted. Will look at parent nodes' nearest boundary\nand try there, even if the original position wasn't directly at the\nstart or end of that node. Returns null when no position was found.\n*/\nfunction dropPoint(doc, pos, slice) {\n    let $pos = doc.resolve(pos);\n    if (!slice.content.size)\n        return pos;\n    let content = slice.content;\n    for (let i = 0; i < slice.openStart; i++)\n        content = content.firstChild.content;\n    for (let pass = 1; pass <= (slice.openStart == 0 && slice.size ? 2 : 1); pass++) {\n        for (let d = $pos.depth; d >= 0; d--) {\n            let bias = d == $pos.depth ? 0 : $pos.pos <= ($pos.start(d + 1) + $pos.end(d + 1)) / 2 ? -1 : 1;\n            let insertPos = $pos.index(d) + (bias > 0 ? 1 : 0);\n            let parent = $pos.node(d), fits = false;\n            if (pass == 1) {\n                fits = parent.canReplace(insertPos, insertPos, content);\n            }\n            else {\n                let wrapping = parent.contentMatchAt(insertPos).findWrapping(content.firstChild.type);\n                fits = wrapping && parent.canReplaceWith(insertPos, insertPos, wrapping[0]);\n            }\n            if (fits)\n                return bias == 0 ? $pos.pos : bias < 0 ? $pos.before(d + 1) : $pos.after(d + 1);\n        }\n    }\n    return null;\n}\n\n/**\n‘Fit’ a slice into a given position in the document, producing a\n[step](https://prosemirror.net/docs/ref/#transform.Step) that inserts it. Will return null if\nthere's no meaningful way to insert the slice here, or inserting it\nwould be a no-op (an empty slice over an empty range).\n*/\nfunction replaceStep(doc, from, to = from, slice = Slice.empty) {\n    if (from == to && !slice.size)\n        return null;\n    let $from = doc.resolve(from), $to = doc.resolve(to);\n    // Optimization -- avoid work if it's obvious that it's not needed.\n    if (fitsTrivially($from, $to, slice))\n        return new ReplaceStep(from, to, slice);\n    return new Fitter($from, $to, slice).fit();\n}\nfunction fitsTrivially($from, $to, slice) {\n    return !slice.openStart && !slice.openEnd && $from.start() == $to.start() &&\n        $from.parent.canReplace($from.index(), $to.index(), slice.content);\n}\n// Algorithm for 'placing' the elements of a slice into a gap:\n//\n// We consider the content of each node that is open to the left to be\n// independently placeable. I.e. in <p(\"foo\"), p(\"bar\")>, when the\n// paragraph on the left is open, \"foo\" can be placed (somewhere on\n// the left side of the replacement gap) independently from p(\"bar\").\n//\n// This class tracks the state of the placement progress in the\n// following properties:\n//\n//  - `frontier` holds a stack of `{type, match}` objects that\n//    represent the open side of the replacement. It starts at\n//    `$from`, then moves forward as content is placed, and is finally\n//    reconciled with `$to`.\n//\n//  - `unplaced` is a slice that represents the content that hasn't\n//    been placed yet.\n//\n//  - `placed` is a fragment of placed content. Its open-start value\n//    is implicit in `$from`, and its open-end value in `frontier`.\nclass Fitter {\n    constructor($from, $to, unplaced) {\n        this.$from = $from;\n        this.$to = $to;\n        this.unplaced = unplaced;\n        this.frontier = [];\n        this.placed = Fragment.empty;\n        for (let i = 0; i <= $from.depth; i++) {\n            let node = $from.node(i);\n            this.frontier.push({\n                type: node.type,\n                match: node.contentMatchAt($from.indexAfter(i))\n            });\n        }\n        for (let i = $from.depth; i > 0; i--)\n            this.placed = Fragment.from($from.node(i).copy(this.placed));\n    }\n    get depth() { return this.frontier.length - 1; }\n    fit() {\n        // As long as there's unplaced content, try to place some of it.\n        // If that fails, either increase the open score of the unplaced\n        // slice, or drop nodes from it, and then try again.\n        while (this.unplaced.size) {\n            let fit = this.findFittable();\n            if (fit)\n                this.placeNodes(fit);\n            else\n                this.openMore() || this.dropNode();\n        }\n        // When there's inline content directly after the frontier _and_\n        // directly after `this.$to`, we must generate a `ReplaceAround`\n        // step that pulls that content into the node after the frontier.\n        // That means the fitting must be done to the end of the textblock\n        // node after `this.$to`, not `this.$to` itself.\n        let moveInline = this.mustMoveInline(), placedSize = this.placed.size - this.depth - this.$from.depth;\n        let $from = this.$from, $to = this.close(moveInline < 0 ? this.$to : $from.doc.resolve(moveInline));\n        if (!$to)\n            return null;\n        // If closing to `$to` succeeded, create a step\n        let content = this.placed, openStart = $from.depth, openEnd = $to.depth;\n        while (openStart && openEnd && content.childCount == 1) { // Normalize by dropping open parent nodes\n            content = content.firstChild.content;\n            openStart--;\n            openEnd--;\n        }\n        let slice = new Slice(content, openStart, openEnd);\n        if (moveInline > -1)\n            return new ReplaceAroundStep($from.pos, moveInline, this.$to.pos, this.$to.end(), slice, placedSize);\n        if (slice.size || $from.pos != this.$to.pos) // Don't generate no-op steps\n            return new ReplaceStep($from.pos, $to.pos, slice);\n        return null;\n    }\n    // Find a position on the start spine of `this.unplaced` that has\n    // content that can be moved somewhere on the frontier. Returns two\n    // depths, one for the slice and one for the frontier.\n    findFittable() {\n        let startDepth = this.unplaced.openStart;\n        for (let cur = this.unplaced.content, d = 0, openEnd = this.unplaced.openEnd; d < startDepth; d++) {\n            let node = cur.firstChild;\n            if (cur.childCount > 1)\n                openEnd = 0;\n            if (node.type.spec.isolating && openEnd <= d) {\n                startDepth = d;\n                break;\n            }\n            cur = node.content;\n        }\n        // Only try wrapping nodes (pass 2) after finding a place without\n        // wrapping failed.\n        for (let pass = 1; pass <= 2; pass++) {\n            for (let sliceDepth = pass == 1 ? startDepth : this.unplaced.openStart; sliceDepth >= 0; sliceDepth--) {\n                let fragment, parent = null;\n                if (sliceDepth) {\n                    parent = contentAt(this.unplaced.content, sliceDepth - 1).firstChild;\n                    fragment = parent.content;\n                }\n                else {\n                    fragment = this.unplaced.content;\n                }\n                let first = fragment.firstChild;\n                for (let frontierDepth = this.depth; frontierDepth >= 0; frontierDepth--) {\n                    let { type, match } = this.frontier[frontierDepth], wrap, inject = null;\n                    // In pass 1, if the next node matches, or there is no next\n                    // node but the parents look compatible, we've found a\n                    // place.\n                    if (pass == 1 && (first ? match.matchType(first.type) || (inject = match.fillBefore(Fragment.from(first), false))\n                        : parent && type.compatibleContent(parent.type)))\n                        return { sliceDepth, frontierDepth, parent, inject };\n                    // In pass 2, look for a set of wrapping nodes that make\n                    // `first` fit here.\n                    else if (pass == 2 && first && (wrap = match.findWrapping(first.type)))\n                        return { sliceDepth, frontierDepth, parent, wrap };\n                    // Don't continue looking further up if the parent node\n                    // would fit here.\n                    if (parent && match.matchType(parent.type))\n                        break;\n                }\n            }\n        }\n    }\n    openMore() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (!inner.childCount || inner.firstChild.isLeaf)\n            return false;\n        this.unplaced = new Slice(content, openStart + 1, Math.max(openEnd, inner.size + openStart >= content.size - openEnd ? openStart + 1 : 0));\n        return true;\n    }\n    dropNode() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (inner.childCount <= 1 && openStart > 0) {\n            let openAtEnd = content.size - openStart <= openStart + inner.size;\n            this.unplaced = new Slice(dropFromFragment(content, openStart - 1, 1), openStart - 1, openAtEnd ? openStart - 1 : openEnd);\n        }\n        else {\n            this.unplaced = new Slice(dropFromFragment(content, openStart, 1), openStart, openEnd);\n        }\n    }\n    // Move content from the unplaced slice at `sliceDepth` to the\n    // frontier node at `frontierDepth`. Close that frontier node when\n    // applicable.\n    placeNodes({ sliceDepth, frontierDepth, parent, inject, wrap }) {\n        while (this.depth > frontierDepth)\n            this.closeFrontierNode();\n        if (wrap)\n            for (let i = 0; i < wrap.length; i++)\n                this.openFrontierNode(wrap[i]);\n        let slice = this.unplaced, fragment = parent ? parent.content : slice.content;\n        let openStart = slice.openStart - sliceDepth;\n        let taken = 0, add = [];\n        let { match, type } = this.frontier[frontierDepth];\n        if (inject) {\n            for (let i = 0; i < inject.childCount; i++)\n                add.push(inject.child(i));\n            match = match.matchFragment(inject);\n        }\n        // Computes the amount of (end) open nodes at the end of the\n        // fragment. When 0, the parent is open, but no more. When\n        // negative, nothing is open.\n        let openEndCount = (fragment.size + sliceDepth) - (slice.content.size - slice.openEnd);\n        // Scan over the fragment, fitting as many child nodes as\n        // possible.\n        while (taken < fragment.childCount) {\n            let next = fragment.child(taken), matches = match.matchType(next.type);\n            if (!matches)\n                break;\n            taken++;\n            if (taken > 1 || openStart == 0 || next.content.size) { // Drop empty open nodes\n                match = matches;\n                add.push(closeNodeStart(next.mark(type.allowedMarks(next.marks)), taken == 1 ? openStart : 0, taken == fragment.childCount ? openEndCount : -1));\n            }\n        }\n        let toEnd = taken == fragment.childCount;\n        if (!toEnd)\n            openEndCount = -1;\n        this.placed = addToFragment(this.placed, frontierDepth, Fragment.from(add));\n        this.frontier[frontierDepth].match = match;\n        // If the parent types match, and the entire node was moved, and\n        // it's not open, close this frontier node right away.\n        if (toEnd && openEndCount < 0 && parent && parent.type == this.frontier[this.depth].type && this.frontier.length > 1)\n            this.closeFrontierNode();\n        // Add new frontier nodes for any open nodes at the end.\n        for (let i = 0, cur = fragment; i < openEndCount; i++) {\n            let node = cur.lastChild;\n            this.frontier.push({ type: node.type, match: node.contentMatchAt(node.childCount) });\n            cur = node.content;\n        }\n        // Update `this.unplaced`. Drop the entire node from which we\n        // placed it we got to its end, otherwise just drop the placed\n        // nodes.\n        this.unplaced = !toEnd ? new Slice(dropFromFragment(slice.content, sliceDepth, taken), slice.openStart, slice.openEnd)\n            : sliceDepth == 0 ? Slice.empty\n                : new Slice(dropFromFragment(slice.content, sliceDepth - 1, 1), sliceDepth - 1, openEndCount < 0 ? slice.openEnd : sliceDepth - 1);\n    }\n    mustMoveInline() {\n        if (!this.$to.parent.isTextblock)\n            return -1;\n        let top = this.frontier[this.depth], level;\n        if (!top.type.isTextblock || !contentAfterFits(this.$to, this.$to.depth, top.type, top.match, false) ||\n            (this.$to.depth == this.depth && (level = this.findCloseLevel(this.$to)) && level.depth == this.depth))\n            return -1;\n        let { depth } = this.$to, after = this.$to.after(depth);\n        while (depth > 1 && after == this.$to.end(--depth))\n            ++after;\n        return after;\n    }\n    findCloseLevel($to) {\n        scan: for (let i = Math.min(this.depth, $to.depth); i >= 0; i--) {\n            let { match, type } = this.frontier[i];\n            let dropInner = i < $to.depth && $to.end(i + 1) == $to.pos + ($to.depth - (i + 1));\n            let fit = contentAfterFits($to, i, type, match, dropInner);\n            if (!fit)\n                continue;\n            for (let d = i - 1; d >= 0; d--) {\n                let { match, type } = this.frontier[d];\n                let matches = contentAfterFits($to, d, type, match, true);\n                if (!matches || matches.childCount)\n                    continue scan;\n            }\n            return { depth: i, fit, move: dropInner ? $to.doc.resolve($to.after(i + 1)) : $to };\n        }\n    }\n    close($to) {\n        let close = this.findCloseLevel($to);\n        if (!close)\n            return null;\n        while (this.depth > close.depth)\n            this.closeFrontierNode();\n        if (close.fit.childCount)\n            this.placed = addToFragment(this.placed, close.depth, close.fit);\n        $to = close.move;\n        for (let d = close.depth + 1; d <= $to.depth; d++) {\n            let node = $to.node(d), add = node.type.contentMatch.fillBefore(node.content, true, $to.index(d));\n            this.openFrontierNode(node.type, node.attrs, add);\n        }\n        return $to;\n    }\n    openFrontierNode(type, attrs = null, content) {\n        let top = this.frontier[this.depth];\n        top.match = top.match.matchType(type);\n        this.placed = addToFragment(this.placed, this.depth, Fragment.from(type.create(attrs, content)));\n        this.frontier.push({ type, match: type.contentMatch });\n    }\n    closeFrontierNode() {\n        let open = this.frontier.pop();\n        let add = open.match.fillBefore(Fragment.empty, true);\n        if (add.childCount)\n            this.placed = addToFragment(this.placed, this.frontier.length, add);\n    }\n}\nfunction dropFromFragment(fragment, depth, count) {\n    if (depth == 0)\n        return fragment.cutByIndex(count, fragment.childCount);\n    return fragment.replaceChild(0, fragment.firstChild.copy(dropFromFragment(fragment.firstChild.content, depth - 1, count)));\n}\nfunction addToFragment(fragment, depth, content) {\n    if (depth == 0)\n        return fragment.append(content);\n    return fragment.replaceChild(fragment.childCount - 1, fragment.lastChild.copy(addToFragment(fragment.lastChild.content, depth - 1, content)));\n}\nfunction contentAt(fragment, depth) {\n    for (let i = 0; i < depth; i++)\n        fragment = fragment.firstChild.content;\n    return fragment;\n}\nfunction closeNodeStart(node, openStart, openEnd) {\n    if (openStart <= 0)\n        return node;\n    let frag = node.content;\n    if (openStart > 1)\n        frag = frag.replaceChild(0, closeNodeStart(frag.firstChild, openStart - 1, frag.childCount == 1 ? openEnd - 1 : 0));\n    if (openStart > 0) {\n        frag = node.type.contentMatch.fillBefore(frag).append(frag);\n        if (openEnd <= 0)\n            frag = frag.append(node.type.contentMatch.matchFragment(frag).fillBefore(Fragment.empty, true));\n    }\n    return node.copy(frag);\n}\nfunction contentAfterFits($to, depth, type, match, open) {\n    let node = $to.node(depth), index = open ? $to.indexAfter(depth) : $to.index(depth);\n    if (index == node.childCount && !type.compatibleContent(node.type))\n        return null;\n    let fit = match.fillBefore(node.content, true, index);\n    return fit && !invalidMarks(type, node.content, index) ? fit : null;\n}\nfunction invalidMarks(type, fragment, start) {\n    for (let i = start; i < fragment.childCount; i++)\n        if (!type.allowsMarks(fragment.child(i).marks))\n            return true;\n    return false;\n}\nfunction definesContent(type) {\n    return type.spec.defining || type.spec.definingForContent;\n}\nfunction replaceRange(tr, from, to, slice) {\n    if (!slice.size)\n        return tr.deleteRange(from, to);\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    if (fitsTrivially($from, $to, slice))\n        return tr.step(new ReplaceStep(from, to, slice));\n    let targetDepths = coveredDepths($from, tr.doc.resolve(to));\n    // Can't replace the whole document, so remove 0 if it's present\n    if (targetDepths[targetDepths.length - 1] == 0)\n        targetDepths.pop();\n    // Negative numbers represent not expansion over the whole node at\n    // that depth, but replacing from $from.before(-D) to $to.pos.\n    let preferredTarget = -($from.depth + 1);\n    targetDepths.unshift(preferredTarget);\n    // This loop picks a preferred target depth, if one of the covering\n    // depths is not outside of a defining node, and adds negative\n    // depths for any depth that has $from at its start and does not\n    // cross a defining node.\n    for (let d = $from.depth, pos = $from.pos - 1; d > 0; d--, pos--) {\n        let spec = $from.node(d).type.spec;\n        if (spec.defining || spec.definingAsContext || spec.isolating)\n            break;\n        if (targetDepths.indexOf(d) > -1)\n            preferredTarget = d;\n        else if ($from.before(d) == pos)\n            targetDepths.splice(1, 0, -d);\n    }\n    // Try to fit each possible depth of the slice into each possible\n    // target depth, starting with the preferred depths.\n    let preferredTargetIndex = targetDepths.indexOf(preferredTarget);\n    let leftNodes = [], preferredDepth = slice.openStart;\n    for (let content = slice.content, i = 0;; i++) {\n        let node = content.firstChild;\n        leftNodes.push(node);\n        if (i == slice.openStart)\n            break;\n        content = node.content;\n    }\n    // Back up preferredDepth to cover defining textblocks directly\n    // above it, possibly skipping a non-defining textblock.\n    for (let d = preferredDepth - 1; d >= 0; d--) {\n        let leftNode = leftNodes[d], def = definesContent(leftNode.type);\n        if (def && !leftNode.sameMarkup($from.node(Math.abs(preferredTarget) - 1)))\n            preferredDepth = d;\n        else if (def || !leftNode.type.isTextblock)\n            break;\n    }\n    for (let j = slice.openStart; j >= 0; j--) {\n        let openDepth = (j + preferredDepth + 1) % (slice.openStart + 1);\n        let insert = leftNodes[openDepth];\n        if (!insert)\n            continue;\n        for (let i = 0; i < targetDepths.length; i++) {\n            // Loop over possible expansion levels, starting with the\n            // preferred one\n            let targetDepth = targetDepths[(i + preferredTargetIndex) % targetDepths.length], expand = true;\n            if (targetDepth < 0) {\n                expand = false;\n                targetDepth = -targetDepth;\n            }\n            let parent = $from.node(targetDepth - 1), index = $from.index(targetDepth - 1);\n            if (parent.canReplaceWith(index, index, insert.type, insert.marks))\n                return tr.replace($from.before(targetDepth), expand ? $to.after(targetDepth) : to, new Slice(closeFragment(slice.content, 0, slice.openStart, openDepth), openDepth, slice.openEnd));\n        }\n    }\n    let startSteps = tr.steps.length;\n    for (let i = targetDepths.length - 1; i >= 0; i--) {\n        tr.replace(from, to, slice);\n        if (tr.steps.length > startSteps)\n            break;\n        let depth = targetDepths[i];\n        if (depth < 0)\n            continue;\n        from = $from.before(depth);\n        to = $to.after(depth);\n    }\n}\nfunction closeFragment(fragment, depth, oldOpen, newOpen, parent) {\n    if (depth < oldOpen) {\n        let first = fragment.firstChild;\n        fragment = fragment.replaceChild(0, first.copy(closeFragment(first.content, depth + 1, oldOpen, newOpen, first)));\n    }\n    if (depth > newOpen) {\n        let match = parent.contentMatchAt(0);\n        let start = match.fillBefore(fragment).append(fragment);\n        fragment = start.append(match.matchFragment(start).fillBefore(Fragment.empty, true));\n    }\n    return fragment;\n}\nfunction replaceRangeWith(tr, from, to, node) {\n    if (!node.isInline && from == to && tr.doc.resolve(from).parent.content.size) {\n        let point = insertPoint(tr.doc, from, node.type);\n        if (point != null)\n            from = to = point;\n    }\n    tr.replaceRange(from, to, new Slice(Fragment.from(node), 0, 0));\n}\nfunction deleteRange(tr, from, to) {\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    let covered = coveredDepths($from, $to);\n    for (let i = 0; i < covered.length; i++) {\n        let depth = covered[i], last = i == covered.length - 1;\n        if ((last && depth == 0) || $from.node(depth).type.contentMatch.validEnd)\n            return tr.delete($from.start(depth), $to.end(depth));\n        if (depth > 0 && (last || $from.node(depth - 1).canReplace($from.index(depth - 1), $to.indexAfter(depth - 1))))\n            return tr.delete($from.before(depth), $to.after(depth));\n    }\n    for (let d = 1; d <= $from.depth && d <= $to.depth; d++) {\n        if (from - $from.start(d) == $from.depth - d && to > $from.end(d) && $to.end(d) - to != $to.depth - d &&\n            $from.start(d - 1) == $to.start(d - 1) && $from.node(d - 1).canReplace($from.index(d - 1), $to.index(d - 1)))\n            return tr.delete($from.before(d), to);\n    }\n    tr.delete(from, to);\n}\n// Returns an array of all depths for which $from - $to spans the\n// whole content of the nodes at that depth.\nfunction coveredDepths($from, $to) {\n    let result = [], minDepth = Math.min($from.depth, $to.depth);\n    for (let d = minDepth; d >= 0; d--) {\n        let start = $from.start(d);\n        if (start < $from.pos - ($from.depth - d) ||\n            $to.end(d) > $to.pos + ($to.depth - d) ||\n            $from.node(d).type.spec.isolating ||\n            $to.node(d).type.spec.isolating)\n            break;\n        if (start == $to.start(d) ||\n            (d == $from.depth && d == $to.depth && $from.parent.inlineContent && $to.parent.inlineContent &&\n                d && $to.start(d - 1) == start - 1))\n            result.push(d);\n    }\n    return result;\n}\n\n/**\nUpdate an attribute in a specific node.\n*/\nclass AttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.pos = pos;\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at attribute step's position\");\n        let attrs = Object.create(null);\n        for (let name in node.attrs)\n            attrs[name] = node.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = node.type.create(attrs, null, node.marks);\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new Slice(Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new AttrStep(this.pos, this.attr, doc.nodeAt(this.pos).attrs[this.attr]);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AttrStep(pos.pos, this.attr, this.value);\n    }\n    toJSON() {\n        return { stepType: \"attr\", pos: this.pos, attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\" || typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for AttrStep.fromJSON\");\n        return new AttrStep(json.pos, json.attr, json.value);\n    }\n}\nStep.jsonID(\"attr\", AttrStep);\n/**\nUpdate an attribute in the doc node.\n*/\nclass DocAttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let attrs = Object.create(null);\n        for (let name in doc.attrs)\n            attrs[name] = doc.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = doc.type.create(attrs, doc.content, doc.marks);\n        return StepResult.ok(updated);\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new DocAttrStep(this.attr, doc.attrs[this.attr]);\n    }\n    map(mapping) {\n        return this;\n    }\n    toJSON() {\n        return { stepType: \"docAttr\", attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for DocAttrStep.fromJSON\");\n        return new DocAttrStep(json.attr, json.value);\n    }\n}\nStep.jsonID(\"docAttr\", DocAttrStep);\n\n/**\n@internal\n*/\nlet TransformError = class extends Error {\n};\nTransformError = function TransformError(message) {\n    let err = Error.call(this, message);\n    err.__proto__ = TransformError.prototype;\n    return err;\n};\nTransformError.prototype = Object.create(Error.prototype);\nTransformError.prototype.constructor = TransformError;\nTransformError.prototype.name = \"TransformError\";\n/**\nAbstraction to build up and track an array of\n[steps](https://prosemirror.net/docs/ref/#transform.Step) representing a document transformation.\n\nMost transforming methods return the `Transform` object itself, so\nthat they can be chained.\n*/\nclass Transform {\n    /**\n    Create a transform that starts with the given document.\n    */\n    constructor(\n    /**\n    The current document (the result of applying the steps in the\n    transform).\n    */\n    doc) {\n        this.doc = doc;\n        /**\n        The steps in this transform.\n        */\n        this.steps = [];\n        /**\n        The documents before each of the steps.\n        */\n        this.docs = [];\n        /**\n        A mapping with the maps for each of the steps in this transform.\n        */\n        this.mapping = new Mapping;\n    }\n    /**\n    The starting document.\n    */\n    get before() { return this.docs.length ? this.docs[0] : this.doc; }\n    /**\n    Apply a new step in this transform, saving the result. Throws an\n    error when the step fails.\n    */\n    step(step) {\n        let result = this.maybeStep(step);\n        if (result.failed)\n            throw new TransformError(result.failed);\n        return this;\n    }\n    /**\n    Try to apply a step in this transformation, ignoring it if it\n    fails. Returns the step result.\n    */\n    maybeStep(step) {\n        let result = step.apply(this.doc);\n        if (!result.failed)\n            this.addStep(step, result.doc);\n        return result;\n    }\n    /**\n    True when the document has been changed (when there are any\n    steps).\n    */\n    get docChanged() {\n        return this.steps.length > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        this.docs.push(this.doc);\n        this.steps.push(step);\n        this.mapping.appendMap(step.getMap());\n        this.doc = doc;\n    }\n    /**\n    Replace the part of the document between `from` and `to` with the\n    given `slice`.\n    */\n    replace(from, to = from, slice = Slice.empty) {\n        let step = replaceStep(this.doc, from, to, slice);\n        if (step)\n            this.step(step);\n        return this;\n    }\n    /**\n    Replace the given range with the given content, which may be a\n    fragment, node, or array of nodes.\n    */\n    replaceWith(from, to, content) {\n        return this.replace(from, to, new Slice(Fragment.from(content), 0, 0));\n    }\n    /**\n    Delete the content between the given positions.\n    */\n    delete(from, to) {\n        return this.replace(from, to, Slice.empty);\n    }\n    /**\n    Insert the given content at the given position.\n    */\n    insert(pos, content) {\n        return this.replaceWith(pos, pos, content);\n    }\n    /**\n    Replace a range of the document with a given slice, using\n    `from`, `to`, and the slice's\n    [`openStart`](https://prosemirror.net/docs/ref/#model.Slice.openStart) property as hints, rather\n    than fixed start and end points. This method may grow the\n    replaced area or close open nodes in the slice in order to get a\n    fit that is more in line with WYSIWYG expectations, by dropping\n    fully covered parent nodes of the replaced region when they are\n    marked [non-defining as\n    context](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext), or including an\n    open parent node from the slice that _is_ marked as [defining\n    its content](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n    \n    This is the method, for example, to handle paste. The similar\n    [`replace`](https://prosemirror.net/docs/ref/#transform.Transform.replace) method is a more\n    primitive tool which will _not_ move the start and end of its given\n    range, and is useful in situations where you need more precise\n    control over what happens.\n    */\n    replaceRange(from, to, slice) {\n        replaceRange(this, from, to, slice);\n        return this;\n    }\n    /**\n    Replace the given range with a node, but use `from` and `to` as\n    hints, rather than precise positions. When from and to are the same\n    and are at the start or end of a parent node in which the given\n    node doesn't fit, this method may _move_ them out towards a parent\n    that does allow the given node to be placed. When the given range\n    completely covers a parent node, this method may completely replace\n    that parent node.\n    */\n    replaceRangeWith(from, to, node) {\n        replaceRangeWith(this, from, to, node);\n        return this;\n    }\n    /**\n    Delete the given range, expanding it to cover fully covered\n    parent nodes until a valid replace is found.\n    */\n    deleteRange(from, to) {\n        deleteRange(this, from, to);\n        return this;\n    }\n    /**\n    Split the content in the given range off from its parent, if there\n    is sibling content before or after it, and move it up the tree to\n    the depth specified by `target`. You'll probably want to use\n    [`liftTarget`](https://prosemirror.net/docs/ref/#transform.liftTarget) to compute `target`, to make\n    sure the lift is valid.\n    */\n    lift(range, target) {\n        lift(this, range, target);\n        return this;\n    }\n    /**\n    Join the blocks around the given position. If depth is 2, their\n    last and first siblings are also joined, and so on.\n    */\n    join(pos, depth = 1) {\n        join(this, pos, depth);\n        return this;\n    }\n    /**\n    Wrap the given [range](https://prosemirror.net/docs/ref/#model.NodeRange) in the given set of wrappers.\n    The wrappers are assumed to be valid in this position, and should\n    probably be computed with [`findWrapping`](https://prosemirror.net/docs/ref/#transform.findWrapping).\n    */\n    wrap(range, wrappers) {\n        wrap(this, range, wrappers);\n        return this;\n    }\n    /**\n    Set the type of all textblocks (partly) between `from` and `to` to\n    the given node type with the given attributes.\n    */\n    setBlockType(from, to = from, type, attrs = null) {\n        setBlockType(this, from, to, type, attrs);\n        return this;\n    }\n    /**\n    Change the type, attributes, and/or marks of the node at `pos`.\n    When `type` isn't given, the existing node type is preserved,\n    */\n    setNodeMarkup(pos, type, attrs = null, marks) {\n        setNodeMarkup(this, pos, type, attrs, marks);\n        return this;\n    }\n    /**\n    Set a single attribute on a given node to a new value.\n    The `pos` addresses the document content. Use `setDocAttribute`\n    to set attributes on the document itself.\n    */\n    setNodeAttribute(pos, attr, value) {\n        this.step(new AttrStep(pos, attr, value));\n        return this;\n    }\n    /**\n    Set a single attribute on the document to a new value.\n    */\n    setDocAttribute(attr, value) {\n        this.step(new DocAttrStep(attr, value));\n        return this;\n    }\n    /**\n    Add a mark to the node at position `pos`.\n    */\n    addNodeMark(pos, mark) {\n        this.step(new AddNodeMarkStep(pos, mark));\n        return this;\n    }\n    /**\n    Remove a mark (or all marks of the given type) from the node at\n    position `pos`.\n    */\n    removeNodeMark(pos, mark) {\n        let node = this.doc.nodeAt(pos);\n        if (!node)\n            throw new RangeError(\"No node at position \" + pos);\n        if (mark instanceof Mark) {\n            if (mark.isInSet(node.marks))\n                this.step(new RemoveNodeMarkStep(pos, mark));\n        }\n        else {\n            let set = node.marks, found, steps = [];\n            while (found = mark.isInSet(set)) {\n                steps.push(new RemoveNodeMarkStep(pos, found));\n                set = found.removeFromSet(set);\n            }\n            for (let i = steps.length - 1; i >= 0; i--)\n                this.step(steps[i]);\n        }\n        return this;\n    }\n    /**\n    Split the node at the given position, and optionally, if `depth` is\n    greater than one, any number of nodes above that. By default, the\n    parts split off will inherit the node type of the original node.\n    This can be changed by passing an array of types and attributes to\n    use after the split (with the outermost nodes coming first).\n    */\n    split(pos, depth = 1, typesAfter) {\n        split(this, pos, depth, typesAfter);\n        return this;\n    }\n    /**\n    Add the given mark to the inline content between `from` and `to`.\n    */\n    addMark(from, to, mark) {\n        addMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Remove marks from inline nodes between `from` and `to`. When\n    `mark` is a single mark, remove precisely that mark. When it is\n    a mark type, remove all marks of that type. When it is null,\n    remove all marks of any type.\n    */\n    removeMark(from, to, mark) {\n        removeMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Removes all marks and nodes from the content of the node at\n    `pos` that don't match the given new parent node type. Accepts\n    an optional starting [content match](https://prosemirror.net/docs/ref/#model.ContentMatch) as\n    third argument.\n    */\n    clearIncompatible(pos, parentType, match) {\n        clearIncompatible(this, pos, parentType, match);\n        return this;\n    }\n}\n\nexport { AddMarkStep, AddNodeMarkStep, AttrStep, DocAttrStep, MapResult, Mapping, RemoveMarkStep, RemoveNodeMarkStep, ReplaceAroundStep, ReplaceStep, Step, StepMap, StepResult, Transform, TransformError, canJoin, canSplit, dropPoint, findWrapping, insertPoint, joinPoint, liftTarget, replaceStep };\n", "import { Slice, Fragment, <PERSON>, Node } from 'prosemirror-model';\nimport { ReplaceStep, ReplaceAroundStep, Transform } from 'prosemirror-transform';\n\nconst classesById = Object.create(null);\n/**\nSuperclass for editor selections. Every selection type should\nextend this. Should not be instantiated directly.\n*/\nclass Selection {\n    /**\n    Initialize a selection with the head and anchor and ranges. If no\n    ranges are given, constructs a single range across `$anchor` and\n    `$head`.\n    */\n    constructor(\n    /**\n    The resolved anchor of the selection (the side that stays in\n    place when the selection is modified).\n    */\n    $anchor, \n    /**\n    The resolved head of the selection (the side that moves when\n    the selection is modified).\n    */\n    $head, ranges) {\n        this.$anchor = $anchor;\n        this.$head = $head;\n        this.ranges = ranges || [new SelectionRange($anchor.min($head), $anchor.max($head))];\n    }\n    /**\n    The selection's anchor, as an unresolved position.\n    */\n    get anchor() { return this.$anchor.pos; }\n    /**\n    The selection's head.\n    */\n    get head() { return this.$head.pos; }\n    /**\n    The lower bound of the selection's main range.\n    */\n    get from() { return this.$from.pos; }\n    /**\n    The upper bound of the selection's main range.\n    */\n    get to() { return this.$to.pos; }\n    /**\n    The resolved lower  bound of the selection's main range.\n    */\n    get $from() {\n        return this.ranges[0].$from;\n    }\n    /**\n    The resolved upper bound of the selection's main range.\n    */\n    get $to() {\n        return this.ranges[0].$to;\n    }\n    /**\n    Indicates whether the selection contains any content.\n    */\n    get empty() {\n        let ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++)\n            if (ranges[i].$from.pos != ranges[i].$to.pos)\n                return false;\n        return true;\n    }\n    /**\n    Get the content of this selection as a slice.\n    */\n    content() {\n        return this.$from.doc.slice(this.from, this.to, true);\n    }\n    /**\n    Replace the selection with a slice or, if no slice is given,\n    delete the selection. Will append to the given transaction.\n    */\n    replace(tr, content = Slice.empty) {\n        // Put the new selection at the position after the inserted\n        // content. When that ended in an inline node, search backwards,\n        // to get the position after that node. If not, search forward.\n        let lastNode = content.content.lastChild, lastParent = null;\n        for (let i = 0; i < content.openEnd; i++) {\n            lastParent = lastNode;\n            lastNode = lastNode.lastChild;\n        }\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            tr.replaceRange(mapping.map($from.pos), mapping.map($to.pos), i ? Slice.empty : content);\n            if (i == 0)\n                selectionToInsertionEnd(tr, mapFrom, (lastNode ? lastNode.isInline : lastParent && lastParent.isTextblock) ? -1 : 1);\n        }\n    }\n    /**\n    Replace the selection with the given node, appending the changes\n    to the given transaction.\n    */\n    replaceWith(tr, node) {\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            let from = mapping.map($from.pos), to = mapping.map($to.pos);\n            if (i) {\n                tr.deleteRange(from, to);\n            }\n            else {\n                tr.replaceRangeWith(from, to, node);\n                selectionToInsertionEnd(tr, mapFrom, node.isInline ? -1 : 1);\n            }\n        }\n    }\n    /**\n    Find a valid cursor or leaf node selection starting at the given\n    position and searching back if `dir` is negative, and forward if\n    positive. When `textOnly` is true, only consider cursor\n    selections. Will return null when no valid selection position is\n    found.\n    */\n    static findFrom($pos, dir, textOnly = false) {\n        let inner = $pos.parent.inlineContent ? new TextSelection($pos)\n            : findSelectionIn($pos.node(0), $pos.parent, $pos.pos, $pos.index(), dir, textOnly);\n        if (inner)\n            return inner;\n        for (let depth = $pos.depth - 1; depth >= 0; depth--) {\n            let found = dir < 0\n                ? findSelectionIn($pos.node(0), $pos.node(depth), $pos.before(depth + 1), $pos.index(depth), dir, textOnly)\n                : findSelectionIn($pos.node(0), $pos.node(depth), $pos.after(depth + 1), $pos.index(depth) + 1, dir, textOnly);\n            if (found)\n                return found;\n        }\n        return null;\n    }\n    /**\n    Find a valid cursor or leaf node selection near the given\n    position. Searches forward first by default, but if `bias` is\n    negative, it will search backwards first.\n    */\n    static near($pos, bias = 1) {\n        return this.findFrom($pos, bias) || this.findFrom($pos, -bias) || new AllSelection($pos.node(0));\n    }\n    /**\n    Find the cursor or leaf node selection closest to the start of\n    the given document. Will return an\n    [`AllSelection`](https://prosemirror.net/docs/ref/#state.AllSelection) if no valid position\n    exists.\n    */\n    static atStart(doc) {\n        return findSelectionIn(doc, doc, 0, 0, 1) || new AllSelection(doc);\n    }\n    /**\n    Find the cursor or leaf node selection closest to the end of the\n    given document.\n    */\n    static atEnd(doc) {\n        return findSelectionIn(doc, doc, doc.content.size, doc.childCount, -1) || new AllSelection(doc);\n    }\n    /**\n    Deserialize the JSON representation of a selection. Must be\n    implemented for custom classes (as a static class method).\n    */\n    static fromJSON(doc, json) {\n        if (!json || !json.type)\n            throw new RangeError(\"Invalid input for Selection.fromJSON\");\n        let cls = classesById[json.type];\n        if (!cls)\n            throw new RangeError(`No selection type ${json.type} defined`);\n        return cls.fromJSON(doc, json);\n    }\n    /**\n    To be able to deserialize selections from JSON, custom selection\n    classes must register themselves with an ID string, so that they\n    can be disambiguated. Try to pick something that's unlikely to\n    clash with classes from other modules.\n    */\n    static jsonID(id, selectionClass) {\n        if (id in classesById)\n            throw new RangeError(\"Duplicate use of selection JSON ID \" + id);\n        classesById[id] = selectionClass;\n        selectionClass.prototype.jsonID = id;\n        return selectionClass;\n    }\n    /**\n    Get a [bookmark](https://prosemirror.net/docs/ref/#state.SelectionBookmark) for this selection,\n    which is a value that can be mapped without having access to a\n    current document, and later resolved to a real selection for a\n    given document again. (This is used mostly by the history to\n    track and restore old selections.) The default implementation of\n    this method just converts the selection to a text selection and\n    returns the bookmark for that.\n    */\n    getBookmark() {\n        return TextSelection.between(this.$anchor, this.$head).getBookmark();\n    }\n}\nSelection.prototype.visible = true;\n/**\nRepresents a selected range in a document.\n*/\nclass SelectionRange {\n    /**\n    Create a range.\n    */\n    constructor(\n    /**\n    The lower bound of the range.\n    */\n    $from, \n    /**\n    The upper bound of the range.\n    */\n    $to) {\n        this.$from = $from;\n        this.$to = $to;\n    }\n}\nlet warnedAboutTextSelection = false;\nfunction checkTextSelection($pos) {\n    if (!warnedAboutTextSelection && !$pos.parent.inlineContent) {\n        warnedAboutTextSelection = true;\n        console[\"warn\"](\"TextSelection endpoint not pointing into a node with inline content (\" + $pos.parent.type.name + \")\");\n    }\n}\n/**\nA text selection represents a classical editor selection, with a\nhead (the moving side) and anchor (immobile side), both of which\npoint into textblock nodes. It can be empty (a regular cursor\nposition).\n*/\nclass TextSelection extends Selection {\n    /**\n    Construct a text selection between the given points.\n    */\n    constructor($anchor, $head = $anchor) {\n        checkTextSelection($anchor);\n        checkTextSelection($head);\n        super($anchor, $head);\n    }\n    /**\n    Returns a resolved position if this is a cursor selection (an\n    empty text selection), and null otherwise.\n    */\n    get $cursor() { return this.$anchor.pos == this.$head.pos ? this.$head : null; }\n    map(doc, mapping) {\n        let $head = doc.resolve(mapping.map(this.head));\n        if (!$head.parent.inlineContent)\n            return Selection.near($head);\n        let $anchor = doc.resolve(mapping.map(this.anchor));\n        return new TextSelection($anchor.parent.inlineContent ? $anchor : $head, $head);\n    }\n    replace(tr, content = Slice.empty) {\n        super.replace(tr, content);\n        if (content == Slice.empty) {\n            let marks = this.$from.marksAcross(this.$to);\n            if (marks)\n                tr.ensureMarks(marks);\n        }\n    }\n    eq(other) {\n        return other instanceof TextSelection && other.anchor == this.anchor && other.head == this.head;\n    }\n    getBookmark() {\n        return new TextBookmark(this.anchor, this.head);\n    }\n    toJSON() {\n        return { type: \"text\", anchor: this.anchor, head: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid input for TextSelection.fromJSON\");\n        return new TextSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n    }\n    /**\n    Create a text selection from non-resolved positions.\n    */\n    static create(doc, anchor, head = anchor) {\n        let $anchor = doc.resolve(anchor);\n        return new this($anchor, head == anchor ? $anchor : doc.resolve(head));\n    }\n    /**\n    Return a text selection that spans the given positions or, if\n    they aren't text positions, find a text selection near them.\n    `bias` determines whether the method searches forward (default)\n    or backwards (negative number) first. Will fall back to calling\n    [`Selection.near`](https://prosemirror.net/docs/ref/#state.Selection^near) when the document\n    doesn't contain a valid text position.\n    */\n    static between($anchor, $head, bias) {\n        let dPos = $anchor.pos - $head.pos;\n        if (!bias || dPos)\n            bias = dPos >= 0 ? 1 : -1;\n        if (!$head.parent.inlineContent) {\n            let found = Selection.findFrom($head, bias, true) || Selection.findFrom($head, -bias, true);\n            if (found)\n                $head = found.$head;\n            else\n                return Selection.near($head, bias);\n        }\n        if (!$anchor.parent.inlineContent) {\n            if (dPos == 0) {\n                $anchor = $head;\n            }\n            else {\n                $anchor = (Selection.findFrom($anchor, -bias, true) || Selection.findFrom($anchor, bias, true)).$anchor;\n                if (($anchor.pos < $head.pos) != (dPos < 0))\n                    $anchor = $head;\n            }\n        }\n        return new TextSelection($anchor, $head);\n    }\n}\nSelection.jsonID(\"text\", TextSelection);\nclass TextBookmark {\n    constructor(anchor, head) {\n        this.anchor = anchor;\n        this.head = head;\n    }\n    map(mapping) {\n        return new TextBookmark(mapping.map(this.anchor), mapping.map(this.head));\n    }\n    resolve(doc) {\n        return TextSelection.between(doc.resolve(this.anchor), doc.resolve(this.head));\n    }\n}\n/**\nA node selection is a selection that points at a single node. All\nnodes marked [selectable](https://prosemirror.net/docs/ref/#model.NodeSpec.selectable) can be the\ntarget of a node selection. In such a selection, `from` and `to`\npoint directly before and after the selected node, `anchor` equals\n`from`, and `head` equals `to`..\n*/\nclass NodeSelection extends Selection {\n    /**\n    Create a node selection. Does not verify the validity of its\n    argument.\n    */\n    constructor($pos) {\n        let node = $pos.nodeAfter;\n        let $end = $pos.node(0).resolve($pos.pos + node.nodeSize);\n        super($pos, $end);\n        this.node = node;\n    }\n    map(doc, mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        let $pos = doc.resolve(pos);\n        if (deleted)\n            return Selection.near($pos);\n        return new NodeSelection($pos);\n    }\n    content() {\n        return new Slice(Fragment.from(this.node), 0, 0);\n    }\n    eq(other) {\n        return other instanceof NodeSelection && other.anchor == this.anchor;\n    }\n    toJSON() {\n        return { type: \"node\", anchor: this.anchor };\n    }\n    getBookmark() { return new NodeBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\")\n            throw new RangeError(\"Invalid input for NodeSelection.fromJSON\");\n        return new NodeSelection(doc.resolve(json.anchor));\n    }\n    /**\n    Create a node selection from non-resolved positions.\n    */\n    static create(doc, from) {\n        return new NodeSelection(doc.resolve(from));\n    }\n    /**\n    Determines whether the given node may be selected as a node\n    selection.\n    */\n    static isSelectable(node) {\n        return !node.isText && node.type.spec.selectable !== false;\n    }\n}\nNodeSelection.prototype.visible = false;\nSelection.jsonID(\"node\", NodeSelection);\nclass NodeBookmark {\n    constructor(anchor) {\n        this.anchor = anchor;\n    }\n    map(mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        return deleted ? new TextBookmark(pos, pos) : new NodeBookmark(pos);\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.anchor), node = $pos.nodeAfter;\n        if (node && NodeSelection.isSelectable(node))\n            return new NodeSelection($pos);\n        return Selection.near($pos);\n    }\n}\n/**\nA selection type that represents selecting the whole document\n(which can not necessarily be expressed with a text selection, when\nthere are for example leaf block nodes at the start or end of the\ndocument).\n*/\nclass AllSelection extends Selection {\n    /**\n    Create an all-selection over the given document.\n    */\n    constructor(doc) {\n        super(doc.resolve(0), doc.resolve(doc.content.size));\n    }\n    replace(tr, content = Slice.empty) {\n        if (content == Slice.empty) {\n            tr.delete(0, tr.doc.content.size);\n            let sel = Selection.atStart(tr.doc);\n            if (!sel.eq(tr.selection))\n                tr.setSelection(sel);\n        }\n        else {\n            super.replace(tr, content);\n        }\n    }\n    toJSON() { return { type: \"all\" }; }\n    /**\n    @internal\n    */\n    static fromJSON(doc) { return new AllSelection(doc); }\n    map(doc) { return new AllSelection(doc); }\n    eq(other) { return other instanceof AllSelection; }\n    getBookmark() { return AllBookmark; }\n}\nSelection.jsonID(\"all\", AllSelection);\nconst AllBookmark = {\n    map() { return this; },\n    resolve(doc) { return new AllSelection(doc); }\n};\n// FIXME we'll need some awareness of text direction when scanning for selections\n// Try to find a selection inside the given node. `pos` points at the\n// position where the search starts. When `text` is true, only return\n// text selections.\nfunction findSelectionIn(doc, node, pos, index, dir, text = false) {\n    if (node.inlineContent)\n        return TextSelection.create(doc, pos);\n    for (let i = index - (dir > 0 ? 0 : 1); dir > 0 ? i < node.childCount : i >= 0; i += dir) {\n        let child = node.child(i);\n        if (!child.isAtom) {\n            let inner = findSelectionIn(doc, child, pos + dir, dir < 0 ? child.childCount : 0, dir, text);\n            if (inner)\n                return inner;\n        }\n        else if (!text && NodeSelection.isSelectable(child)) {\n            return NodeSelection.create(doc, pos - (dir < 0 ? child.nodeSize : 0));\n        }\n        pos += child.nodeSize * dir;\n    }\n    return null;\n}\nfunction selectionToInsertionEnd(tr, startLen, bias) {\n    let last = tr.steps.length - 1;\n    if (last < startLen)\n        return;\n    let step = tr.steps[last];\n    if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep))\n        return;\n    let map = tr.mapping.maps[last], end;\n    map.forEach((_from, _to, _newFrom, newTo) => { if (end == null)\n        end = newTo; });\n    tr.setSelection(Selection.near(tr.doc.resolve(end), bias));\n}\n\nconst UPDATED_SEL = 1, UPDATED_MARKS = 2, UPDATED_SCROLL = 4;\n/**\nAn editor state transaction, which can be applied to a state to\ncreate an updated state. Use\n[`EditorState.tr`](https://prosemirror.net/docs/ref/#state.EditorState.tr) to create an instance.\n\nTransactions track changes to the document (they are a subclass of\n[`Transform`](https://prosemirror.net/docs/ref/#transform.Transform)), but also other state changes,\nlike selection updates and adjustments of the set of [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks). In addition, you can store\nmetadata properties in a transaction, which are extra pieces of\ninformation that client code or plugins can use to describe what a\ntransaction represents, so that they can update their [own\nstate](https://prosemirror.net/docs/ref/#state.StateField) accordingly.\n\nThe [editor view](https://prosemirror.net/docs/ref/#view.EditorView) uses a few metadata\nproperties: it will attach a property `\"pointer\"` with the value\n`true` to selection transactions directly caused by mouse or touch\ninput, a `\"composition\"` property holding an ID identifying the\ncomposition that caused it to transactions caused by composed DOM\ninput, and a `\"uiEvent\"` property of that may be `\"paste\"`,\n`\"cut\"`, or `\"drop\"`.\n*/\nclass Transaction extends Transform {\n    /**\n    @internal\n    */\n    constructor(state) {\n        super(state.doc);\n        // The step count for which the current selection is valid.\n        this.curSelectionFor = 0;\n        // Bitfield to track which aspects of the state were updated by\n        // this transaction.\n        this.updated = 0;\n        // Object used to store metadata properties for the transaction.\n        this.meta = Object.create(null);\n        this.time = Date.now();\n        this.curSelection = state.selection;\n        this.storedMarks = state.storedMarks;\n    }\n    /**\n    The transaction's current selection. This defaults to the editor\n    selection [mapped](https://prosemirror.net/docs/ref/#state.Selection.map) through the steps in the\n    transaction, but can be overwritten with\n    [`setSelection`](https://prosemirror.net/docs/ref/#state.Transaction.setSelection).\n    */\n    get selection() {\n        if (this.curSelectionFor < this.steps.length) {\n            this.curSelection = this.curSelection.map(this.doc, this.mapping.slice(this.curSelectionFor));\n            this.curSelectionFor = this.steps.length;\n        }\n        return this.curSelection;\n    }\n    /**\n    Update the transaction's current selection. Will determine the\n    selection that the editor gets when the transaction is applied.\n    */\n    setSelection(selection) {\n        if (selection.$from.doc != this.doc)\n            throw new RangeError(\"Selection passed to setSelection must point at the current document\");\n        this.curSelection = selection;\n        this.curSelectionFor = this.steps.length;\n        this.updated = (this.updated | UPDATED_SEL) & ~UPDATED_MARKS;\n        this.storedMarks = null;\n        return this;\n    }\n    /**\n    Whether the selection was explicitly updated by this transaction.\n    */\n    get selectionSet() {\n        return (this.updated & UPDATED_SEL) > 0;\n    }\n    /**\n    Set the current stored marks.\n    */\n    setStoredMarks(marks) {\n        this.storedMarks = marks;\n        this.updated |= UPDATED_MARKS;\n        return this;\n    }\n    /**\n    Make sure the current stored marks or, if that is null, the marks\n    at the selection, match the given set of marks. Does nothing if\n    this is already the case.\n    */\n    ensureMarks(marks) {\n        if (!Mark.sameSet(this.storedMarks || this.selection.$from.marks(), marks))\n            this.setStoredMarks(marks);\n        return this;\n    }\n    /**\n    Add a mark to the set of stored marks.\n    */\n    addStoredMark(mark) {\n        return this.ensureMarks(mark.addToSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Remove a mark or mark type from the set of stored marks.\n    */\n    removeStoredMark(mark) {\n        return this.ensureMarks(mark.removeFromSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Whether the stored marks were explicitly set for this transaction.\n    */\n    get storedMarksSet() {\n        return (this.updated & UPDATED_MARKS) > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        super.addStep(step, doc);\n        this.updated = this.updated & ~UPDATED_MARKS;\n        this.storedMarks = null;\n    }\n    /**\n    Update the timestamp for the transaction.\n    */\n    setTime(time) {\n        this.time = time;\n        return this;\n    }\n    /**\n    Replace the current selection with the given slice.\n    */\n    replaceSelection(slice) {\n        this.selection.replace(this, slice);\n        return this;\n    }\n    /**\n    Replace the selection with the given node. When `inheritMarks` is\n    true and the content is inline, it inherits the marks from the\n    place where it is inserted.\n    */\n    replaceSelectionWith(node, inheritMarks = true) {\n        let selection = this.selection;\n        if (inheritMarks)\n            node = node.mark(this.storedMarks || (selection.empty ? selection.$from.marks() : (selection.$from.marksAcross(selection.$to) || Mark.none)));\n        selection.replaceWith(this, node);\n        return this;\n    }\n    /**\n    Delete the selection.\n    */\n    deleteSelection() {\n        this.selection.replace(this);\n        return this;\n    }\n    /**\n    Replace the given range, or the selection if no range is given,\n    with a text node containing the given string.\n    */\n    insertText(text, from, to) {\n        let schema = this.doc.type.schema;\n        if (from == null) {\n            if (!text)\n                return this.deleteSelection();\n            return this.replaceSelectionWith(schema.text(text), true);\n        }\n        else {\n            if (to == null)\n                to = from;\n            to = to == null ? from : to;\n            if (!text)\n                return this.deleteRange(from, to);\n            let marks = this.storedMarks;\n            if (!marks) {\n                let $from = this.doc.resolve(from);\n                marks = to == from ? $from.marks() : $from.marksAcross(this.doc.resolve(to));\n            }\n            this.replaceRangeWith(from, to, schema.text(text, marks));\n            if (!this.selection.empty)\n                this.setSelection(Selection.near(this.selection.$to));\n            return this;\n        }\n    }\n    /**\n    Store a metadata property in this transaction, keyed either by\n    name or by plugin.\n    */\n    setMeta(key, value) {\n        this.meta[typeof key == \"string\" ? key : key.key] = value;\n        return this;\n    }\n    /**\n    Retrieve a metadata property for a given name or plugin.\n    */\n    getMeta(key) {\n        return this.meta[typeof key == \"string\" ? key : key.key];\n    }\n    /**\n    Returns true if this transaction doesn't contain any metadata,\n    and can thus safely be extended.\n    */\n    get isGeneric() {\n        for (let _ in this.meta)\n            return false;\n        return true;\n    }\n    /**\n    Indicate that the editor should scroll the selection into view\n    when updated to the state produced by this transaction.\n    */\n    scrollIntoView() {\n        this.updated |= UPDATED_SCROLL;\n        return this;\n    }\n    /**\n    True when this transaction has had `scrollIntoView` called on it.\n    */\n    get scrolledIntoView() {\n        return (this.updated & UPDATED_SCROLL) > 0;\n    }\n}\n\nfunction bind(f, self) {\n    return !self || !f ? f : f.bind(self);\n}\nclass FieldDesc {\n    constructor(name, desc, self) {\n        this.name = name;\n        this.init = bind(desc.init, self);\n        this.apply = bind(desc.apply, self);\n    }\n}\nconst baseFields = [\n    new FieldDesc(\"doc\", {\n        init(config) { return config.doc || config.schema.topNodeType.createAndFill(); },\n        apply(tr) { return tr.doc; }\n    }),\n    new FieldDesc(\"selection\", {\n        init(config, instance) { return config.selection || Selection.atStart(instance.doc); },\n        apply(tr) { return tr.selection; }\n    }),\n    new FieldDesc(\"storedMarks\", {\n        init(config) { return config.storedMarks || null; },\n        apply(tr, _marks, _old, state) { return state.selection.$cursor ? tr.storedMarks : null; }\n    }),\n    new FieldDesc(\"scrollToSelection\", {\n        init() { return 0; },\n        apply(tr, prev) { return tr.scrolledIntoView ? prev + 1 : prev; }\n    })\n];\n// Object wrapping the part of a state object that stays the same\n// across transactions. Stored in the state's `config` property.\nclass Configuration {\n    constructor(schema, plugins) {\n        this.schema = schema;\n        this.plugins = [];\n        this.pluginsByKey = Object.create(null);\n        this.fields = baseFields.slice();\n        if (plugins)\n            plugins.forEach(plugin => {\n                if (this.pluginsByKey[plugin.key])\n                    throw new RangeError(\"Adding different instances of a keyed plugin (\" + plugin.key + \")\");\n                this.plugins.push(plugin);\n                this.pluginsByKey[plugin.key] = plugin;\n                if (plugin.spec.state)\n                    this.fields.push(new FieldDesc(plugin.key, plugin.spec.state, plugin));\n            });\n    }\n}\n/**\nThe state of a ProseMirror editor is represented by an object of\nthis type. A state is a persistent data structure—it isn't\nupdated, but rather a new state value is computed from an old one\nusing the [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) method.\n\nA state holds a number of built-in fields, and plugins can\n[define](https://prosemirror.net/docs/ref/#state.PluginSpec.state) additional fields.\n*/\nclass EditorState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    config) {\n        this.config = config;\n    }\n    /**\n    The schema of the state's document.\n    */\n    get schema() {\n        return this.config.schema;\n    }\n    /**\n    The plugins that are active in this state.\n    */\n    get plugins() {\n        return this.config.plugins;\n    }\n    /**\n    Apply the given transaction to produce a new state.\n    */\n    apply(tr) {\n        return this.applyTransaction(tr).state;\n    }\n    /**\n    @internal\n    */\n    filterTransaction(tr, ignore = -1) {\n        for (let i = 0; i < this.config.plugins.length; i++)\n            if (i != ignore) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.filterTransaction && !plugin.spec.filterTransaction.call(plugin, tr, this))\n                    return false;\n            }\n        return true;\n    }\n    /**\n    Verbose variant of [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) that\n    returns the precise transactions that were applied (which might\n    be influenced by the [transaction\n    hooks](https://prosemirror.net/docs/ref/#state.PluginSpec.filterTransaction) of\n    plugins) along with the new state.\n    */\n    applyTransaction(rootTr) {\n        if (!this.filterTransaction(rootTr))\n            return { state: this, transactions: [] };\n        let trs = [rootTr], newState = this.applyInner(rootTr), seen = null;\n        // This loop repeatedly gives plugins a chance to respond to\n        // transactions as new transactions are added, making sure to only\n        // pass the transactions the plugin did not see before.\n        for (;;) {\n            let haveNew = false;\n            for (let i = 0; i < this.config.plugins.length; i++) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.appendTransaction) {\n                    let n = seen ? seen[i].n : 0, oldState = seen ? seen[i].state : this;\n                    let tr = n < trs.length &&\n                        plugin.spec.appendTransaction.call(plugin, n ? trs.slice(n) : trs, oldState, newState);\n                    if (tr && newState.filterTransaction(tr, i)) {\n                        tr.setMeta(\"appendedTransaction\", rootTr);\n                        if (!seen) {\n                            seen = [];\n                            for (let j = 0; j < this.config.plugins.length; j++)\n                                seen.push(j < i ? { state: newState, n: trs.length } : { state: this, n: 0 });\n                        }\n                        trs.push(tr);\n                        newState = newState.applyInner(tr);\n                        haveNew = true;\n                    }\n                    if (seen)\n                        seen[i] = { state: newState, n: trs.length };\n                }\n            }\n            if (!haveNew)\n                return { state: newState, transactions: trs };\n        }\n    }\n    /**\n    @internal\n    */\n    applyInner(tr) {\n        if (!tr.before.eq(this.doc))\n            throw new RangeError(\"Applying a mismatched transaction\");\n        let newInstance = new EditorState(this.config), fields = this.config.fields;\n        for (let i = 0; i < fields.length; i++) {\n            let field = fields[i];\n            newInstance[field.name] = field.apply(tr, this[field.name], this, newInstance);\n        }\n        return newInstance;\n    }\n    /**\n    Start a [transaction](https://prosemirror.net/docs/ref/#state.Transaction) from this state.\n    */\n    get tr() { return new Transaction(this); }\n    /**\n    Create a new state.\n    */\n    static create(config) {\n        let $config = new Configuration(config.doc ? config.doc.type.schema : config.schema, config.plugins);\n        let instance = new EditorState($config);\n        for (let i = 0; i < $config.fields.length; i++)\n            instance[$config.fields[i].name] = $config.fields[i].init(config, instance);\n        return instance;\n    }\n    /**\n    Create a new state based on this one, but with an adjusted set\n    of active plugins. State fields that exist in both sets of\n    plugins are kept unchanged. Those that no longer exist are\n    dropped, and those that are new are initialized using their\n    [`init`](https://prosemirror.net/docs/ref/#state.StateField.init) method, passing in the new\n    configuration object..\n    */\n    reconfigure(config) {\n        let $config = new Configuration(this.schema, config.plugins);\n        let fields = $config.fields, instance = new EditorState($config);\n        for (let i = 0; i < fields.length; i++) {\n            let name = fields[i].name;\n            instance[name] = this.hasOwnProperty(name) ? this[name] : fields[i].init(config, instance);\n        }\n        return instance;\n    }\n    /**\n    Serialize this state to JSON. If you want to serialize the state\n    of plugins, pass an object mapping property names to use in the\n    resulting JSON object to plugin objects. The argument may also be\n    a string or number, in which case it is ignored, to support the\n    way `JSON.stringify` calls `toString` methods.\n    */\n    toJSON(pluginFields) {\n        let result = { doc: this.doc.toJSON(), selection: this.selection.toJSON() };\n        if (this.storedMarks)\n            result.storedMarks = this.storedMarks.map(m => m.toJSON());\n        if (pluginFields && typeof pluginFields == 'object')\n            for (let prop in pluginFields) {\n                if (prop == \"doc\" || prop == \"selection\")\n                    throw new RangeError(\"The JSON fields `doc` and `selection` are reserved\");\n                let plugin = pluginFields[prop], state = plugin.spec.state;\n                if (state && state.toJSON)\n                    result[prop] = state.toJSON.call(plugin, this[plugin.key]);\n            }\n        return result;\n    }\n    /**\n    Deserialize a JSON representation of a state. `config` should\n    have at least a `schema` field, and should contain array of\n    plugins to initialize the state with. `pluginFields` can be used\n    to deserialize the state of plugins, by associating plugin\n    instances with the property names they use in the JSON object.\n    */\n    static fromJSON(config, json, pluginFields) {\n        if (!json)\n            throw new RangeError(\"Invalid input for EditorState.fromJSON\");\n        if (!config.schema)\n            throw new RangeError(\"Required config field 'schema' missing\");\n        let $config = new Configuration(config.schema, config.plugins);\n        let instance = new EditorState($config);\n        $config.fields.forEach(field => {\n            if (field.name == \"doc\") {\n                instance.doc = Node.fromJSON(config.schema, json.doc);\n            }\n            else if (field.name == \"selection\") {\n                instance.selection = Selection.fromJSON(instance.doc, json.selection);\n            }\n            else if (field.name == \"storedMarks\") {\n                if (json.storedMarks)\n                    instance.storedMarks = json.storedMarks.map(config.schema.markFromJSON);\n            }\n            else {\n                if (pluginFields)\n                    for (let prop in pluginFields) {\n                        let plugin = pluginFields[prop], state = plugin.spec.state;\n                        if (plugin.key == field.name && state && state.fromJSON &&\n                            Object.prototype.hasOwnProperty.call(json, prop)) {\n                            instance[field.name] = state.fromJSON.call(plugin, config, json[prop], instance);\n                            return;\n                        }\n                    }\n                instance[field.name] = field.init(config, instance);\n            }\n        });\n        return instance;\n    }\n}\n\nfunction bindProps(obj, self, target) {\n    for (let prop in obj) {\n        let val = obj[prop];\n        if (val instanceof Function)\n            val = val.bind(self);\n        else if (prop == \"handleDOMEvents\")\n            val = bindProps(val, self, {});\n        target[prop] = val;\n    }\n    return target;\n}\n/**\nPlugins bundle functionality that can be added to an editor.\nThey are part of the [editor state](https://prosemirror.net/docs/ref/#state.EditorState) and\nmay influence that state and the view that contains it.\n*/\nclass Plugin {\n    /**\n    Create a plugin.\n    */\n    constructor(\n    /**\n    The plugin's [spec object](https://prosemirror.net/docs/ref/#state.PluginSpec).\n    */\n    spec) {\n        this.spec = spec;\n        /**\n        The [props](https://prosemirror.net/docs/ref/#view.EditorProps) exported by this plugin.\n        */\n        this.props = {};\n        if (spec.props)\n            bindProps(spec.props, this, this.props);\n        this.key = spec.key ? spec.key.key : createKey(\"plugin\");\n    }\n    /**\n    Extract the plugin's state field from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\nconst keys = Object.create(null);\nfunction createKey(name) {\n    if (name in keys)\n        return name + \"$\" + ++keys[name];\n    keys[name] = 0;\n    return name + \"$\";\n}\n/**\nA key is used to [tag](https://prosemirror.net/docs/ref/#state.PluginSpec.key) plugins in a way\nthat makes it possible to find them, given an editor state.\nAssigning a key does mean only one plugin of that type can be\nactive in a state.\n*/\nclass PluginKey {\n    /**\n    Create a plugin key.\n    */\n    constructor(name = \"key\") { this.key = createKey(name); }\n    /**\n    Get the active plugin with this key, if any, from an editor\n    state.\n    */\n    get(state) { return state.config.pluginsByKey[this.key]; }\n    /**\n    Get the plugin's state from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\n\nexport { AllSelection, EditorState, NodeSelection, Plugin, PluginKey, Selection, SelectionRange, TextSelection, Transaction };\n"], "mappings": ";AAEA,SAAS,WAAW,SAAS;AAC3B,OAAK,UAAU;AACjB;AAEA,WAAW,YAAY;AAAA,EACrB,aAAa;AAAA,EAEb,MAAM,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,UAAI,KAAK,QAAQ,CAAC,MAAM,IAAK,QAAO;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,SAAS,KAAK;AACjB,QAAIA,SAAQ,KAAK,KAAK,GAAG;AACzB,WAAOA,UAAS,KAAK,SAAY,KAAK,QAAQA,SAAQ,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACnC,QAAI,OAAO,UAAU,UAAU,MAAM,KAAK,OAAO,MAAM,IAAI;AAC3D,QAAIA,SAAQ,KAAK,KAAK,GAAG,GAAG,UAAU,KAAK,QAAQ,MAAM;AACzD,QAAIA,UAAS,IAAI;AACf,cAAQ,KAAK,UAAU,KAAK,KAAK;AAAA,IACnC,OAAO;AACL,cAAQA,SAAQ,CAAC,IAAI;AACrB,UAAI,OAAQ,SAAQA,MAAK,IAAI;AAAA,IAC/B;AACA,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA,EAIA,QAAQ,SAAS,KAAK;AACpB,QAAIA,SAAQ,KAAK,KAAK,GAAG;AACzB,QAAIA,UAAS,GAAI,QAAO;AACxB,QAAI,UAAU,KAAK,QAAQ,MAAM;AACjC,YAAQ,OAAOA,QAAO,CAAC;AACvB,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS,KAAK,OAAO;AAC/B,WAAO,IAAI,WAAW,CAAC,KAAK,KAAK,EAAE,OAAO,KAAK,OAAO,GAAG,EAAE,OAAO,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA,EAIA,UAAU,SAAS,KAAK,OAAO;AAC7B,QAAI,UAAU,KAAK,OAAO,GAAG,EAAE,QAAQ,MAAM;AAC7C,YAAQ,KAAK,KAAK,KAAK;AACvB,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,SAAS,OAAO,KAAK,OAAO;AACrC,QAAI,UAAU,KAAK,OAAO,GAAG,GAAG,UAAU,QAAQ,QAAQ,MAAM;AAChE,QAAIA,SAAQ,QAAQ,KAAK,KAAK;AAC9B,YAAQ,OAAOA,UAAS,KAAK,QAAQ,SAASA,QAAO,GAAG,KAAK,KAAK;AAClE,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,GAAG;AACnB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,QAAE,KAAK,QAAQ,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,KAAK;AACrB,UAAM,WAAW,KAAK,GAAG;AACzB,QAAI,CAAC,IAAI,KAAM,QAAO;AACtB,WAAO,IAAI,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAS,GAAG,EAAE,OAAO,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK,GAAG;AACzB,QAAI,CAAC,IAAI,KAAM,QAAO;AACtB,WAAO,IAAI,WAAW,KAAK,SAAS,GAAG,EAAE,QAAQ,OAAO,IAAI,OAAO,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,SAAS,KAAK;AACtB,QAAI,SAAS;AACb,UAAM,WAAW,KAAK,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,QAAQ,KAAK;AAC3C,eAAS,OAAO,OAAO,IAAI,QAAQ,CAAC,CAAC;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAIA,UAAU,WAAW;AACnB,QAAI,SAAS,CAAC;AACd,SAAK,QAAQ,SAAS,KAAK,OAAO;AAAE,aAAO,GAAG,IAAI;AAAA,IAAO,CAAC;AAC1D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AACF;AAMA,WAAW,OAAO,SAAS,OAAO;AAChC,MAAI,iBAAiB,WAAY,QAAO;AACxC,MAAI,UAAU,CAAC;AACf,MAAI,MAAO,UAAS,QAAQ,MAAO,SAAQ,KAAK,MAAM,MAAM,IAAI,CAAC;AACjE,SAAO,IAAI,WAAW,OAAO;AAC/B;AAEA,IAAO,eAAQ;;;ACtIf,SAAS,cAAc,GAAG,GAAG,KAAK;AAC9B,WAAS,IAAI,KAAI,KAAK;AAClB,QAAI,KAAK,EAAE,cAAc,KAAK,EAAE;AAC5B,aAAO,EAAE,cAAc,EAAE,aAAa,OAAO;AACjD,QAAI,SAAS,EAAE,MAAM,CAAC,GAAG,SAAS,EAAE,MAAM,CAAC;AAC3C,QAAI,UAAU,QAAQ;AAClB,aAAO,OAAO;AACd;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,WAAW,MAAM;AACzB,aAAO;AACX,QAAI,OAAO,UAAU,OAAO,QAAQ,OAAO,MAAM;AAC7C,eAAS,IAAI,GAAG,OAAO,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC,GAAG;AAC9C;AACJ,aAAO;AAAA,IACX;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAC5C,UAAI,QAAQ,cAAc,OAAO,SAAS,OAAO,SAAS,MAAM,CAAC;AACjE,UAAI,SAAS;AACT,eAAO;AAAA,IACf;AACA,WAAO,OAAO;AAAA,EAClB;AACJ;AACA,SAAS,YAAY,GAAG,GAAG,MAAM,MAAM;AACnC,WAAS,KAAK,EAAE,YAAY,KAAK,EAAE,gBAAc;AAC7C,QAAI,MAAM,KAAK,MAAM;AACjB,aAAO,MAAM,KAAK,OAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAChD,QAAI,SAAS,EAAE,MAAM,EAAE,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,EAAE,GAAG,OAAO,OAAO;AAClE,QAAI,UAAU,QAAQ;AAClB,cAAQ;AACR,cAAQ;AACR;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,WAAW,MAAM;AACzB,aAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC9B,QAAI,OAAO,UAAU,OAAO,QAAQ,OAAO,MAAM;AAC7C,UAAI,OAAO,GAAG,UAAU,KAAK,IAAI,OAAO,KAAK,QAAQ,OAAO,KAAK,MAAM;AACvE,aAAO,OAAO,WAAW,OAAO,KAAK,OAAO,KAAK,SAAS,OAAO,CAAC,KAAK,OAAO,KAAK,OAAO,KAAK,SAAS,OAAO,CAAC,GAAG;AAC/G;AACA;AACA;AAAA,MACJ;AACA,aAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAAA,IAC9B;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAC5C,UAAI,QAAQ,YAAY,OAAO,SAAS,OAAO,SAAS,OAAO,GAAG,OAAO,CAAC;AAC1E,UAAI;AACA,eAAO;AAAA,IACf;AACA,YAAQ;AACR,YAAQ;AAAA,EACZ;AACJ;AASA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAIA,SAAS,MAAM;AACX,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ;AACpB,QAAI,QAAQ;AACR,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,aAAK,QAAQ,QAAQ,CAAC,EAAE;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,MAAM,IAAI,GAAG,YAAY,GAAG,QAAQ;AAC7C,aAAS,IAAI,GAAG,MAAM,GAAG,MAAM,IAAI,KAAK;AACpC,UAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,MAAM,MAAM,MAAM;AAC/C,UAAI,MAAM,QAAQ,EAAE,OAAO,YAAY,KAAK,UAAU,MAAM,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM;AAC5F,YAAI,QAAQ,MAAM;AAClB,cAAM,aAAa,KAAK,IAAI,GAAG,OAAO,KAAK,GAAG,KAAK,IAAI,MAAM,QAAQ,MAAM,KAAK,KAAK,GAAG,GAAG,YAAY,KAAK;AAAA,MAChH;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG;AACX,SAAK,aAAa,GAAG,KAAK,MAAM,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,IAAI,gBAAgB,UAAU;AAC5C,QAAI,OAAO,IAAI,QAAQ;AACvB,SAAK,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACvC,UAAI,WAAW,KAAK,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,KAAK,GAAG,IAC1E,CAAC,KAAK,SAAS,KACX,WAAY,OAAO,aAAa,aAAa,SAAS,IAAI,IAAI,WAC1D,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,SAAS,IAAI,IAClD;AAClB,UAAI,KAAK,YAAY,KAAK,UAAU,YAAY,KAAK,gBAAgB,gBAAgB;AACjF,YAAI;AACA,kBAAQ;AAAA;AAER,kBAAQ;AAAA,MAChB;AACA,cAAQ;AAAA,IACZ,GAAG,CAAC;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACV,QAAI,CAAC,MAAM;AACP,aAAO;AACX,QAAI,CAAC,KAAK;AACN,aAAO;AACX,QAAI,OAAO,KAAK,WAAW,QAAQ,MAAM,YAAY,UAAU,KAAK,QAAQ,MAAM,GAAG,IAAI;AACzF,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG;AACvC,cAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,SAAS,KAAK,OAAO,MAAM,IAAI;AAClE,UAAI;AAAA,IACR;AACA,WAAO,IAAI,MAAM,QAAQ,QAAQ;AAC7B,cAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AACjC,WAAO,IAAI,UAAS,SAAS,KAAK,OAAO,MAAM,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM,KAAK,KAAK,MAAM;AACtB,QAAI,QAAQ,KAAK,MAAM,KAAK;AACxB,aAAO;AACX,QAAI,SAAS,CAAC,GAAG,OAAO;AACxB,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,MAAM,GAAG,MAAM,IAAI,KAAK;AACpC,YAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,MAAM,MAAM,MAAM;AAC/C,YAAI,MAAM,MAAM;AACZ,cAAI,MAAM,QAAQ,MAAM,IAAI;AACxB,gBAAI,MAAM;AACN,sBAAQ,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,GAAG,KAAK,IAAI,MAAM,KAAK,QAAQ,KAAK,GAAG,CAAC;AAAA;AAEhF,sBAAQ,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,CAAC,GAAG,KAAK,IAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,UACjG;AACA,iBAAO,KAAK,KAAK;AACjB,kBAAQ,MAAM;AAAA,QAClB;AACA,cAAM;AAAA,MACV;AACJ,WAAO,IAAI,UAAS,QAAQ,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM,IAAI;AACjB,QAAI,QAAQ;AACR,aAAO,UAAS;AACpB,QAAI,QAAQ,KAAK,MAAM,KAAK,QAAQ;AAChC,aAAO;AACX,WAAO,IAAI,UAAS,KAAK,QAAQ,MAAM,MAAM,EAAE,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,MAAM;AACtB,QAAI,UAAU,KAAK,QAAQ,KAAK;AAChC,QAAI,WAAW;AACX,aAAO;AACX,QAAIC,QAAO,KAAK,QAAQ,MAAM;AAC9B,QAAI,OAAO,KAAK,OAAO,KAAK,WAAW,QAAQ;AAC/C,IAAAA,MAAK,KAAK,IAAI;AACd,WAAO,IAAI,UAASA,OAAM,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACb,WAAO,IAAI,UAAS,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACX,WAAO,IAAI,UAAS,KAAK,QAAQ,OAAO,IAAI,GAAG,KAAK,OAAO,KAAK,QAAQ;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,QAAI,KAAK,QAAQ,UAAU,MAAM,QAAQ;AACrC,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ;AACrC,UAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,CAAC;AACpC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,CAAC,IAAI;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIxE,IAAI,YAAY;AAAE,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAI7F,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,MAAM,OAAO;AACT,QAAIC,SAAQ,KAAK,QAAQ,KAAK;AAC9B,QAAI,CAACA;AACD,YAAM,IAAI,WAAW,WAAW,QAAQ,uBAAuB,IAAI;AACvE,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,QAAQ,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,GAAG;AACP,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AACjD,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,QAAE,OAAO,GAAG,CAAC;AACb,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,OAAO,MAAM,GAAG;AAC1B,WAAO,cAAc,MAAM,OAAO,GAAG;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO,MAAM,KAAK,MAAM,WAAW,MAAM,MAAM;AACvD,WAAO,YAAY,MAAM,OAAO,KAAK,QAAQ;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK;AACX,QAAI,OAAO;AACP,aAAO,SAAS,GAAG,GAAG;AAC1B,QAAI,OAAO,KAAK;AACZ,aAAO,SAAS,KAAK,QAAQ,QAAQ,GAAG;AAC5C,QAAI,MAAM,KAAK,QAAQ,MAAM;AACzB,YAAM,IAAI,WAAW,YAAY,GAAG,yBAAyB,IAAI,GAAG;AACxE,aAAS,IAAI,GAAG,SAAS,KAAI,KAAK;AAC9B,UAAI,MAAM,KAAK,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI;AAC5C,UAAI,OAAO,KAAK;AACZ,YAAI,OAAO;AACP,iBAAO,SAAS,IAAI,GAAG,GAAG;AAC9B,eAAO,SAAS,GAAG,MAAM;AAAA,MAC7B;AACA,eAAS;AAAA,IACb;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAAE,WAAO,MAAM,KAAK,cAAc,IAAI;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAItD,gBAAgB;AAAE,WAAO,KAAK,QAAQ,KAAK,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIlD,SAAS;AACL,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,IAAI,OAAK,EAAE,OAAO,CAAC,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,OAAO;AAC3B,QAAI,CAAC;AACD,aAAO,UAAS;AACpB,QAAI,CAAC,MAAM,QAAQ,KAAK;AACpB,YAAM,IAAI,WAAW,qCAAqC;AAC9D,WAAO,IAAI,UAAS,MAAM,IAAI,OAAO,YAAY,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU,OAAO;AACpB,QAAI,CAAC,MAAM;AACP,aAAO,UAAS;AACpB,QAAI,QAAQ,OAAO;AACnB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,OAAO,MAAM,CAAC;AAClB,cAAQ,KAAK;AACb,UAAI,KAAK,KAAK,UAAU,MAAM,IAAI,CAAC,EAAE,WAAW,IAAI,GAAG;AACnD,YAAI,CAAC;AACD,mBAAS,MAAM,MAAM,GAAG,CAAC;AAC7B,eAAO,OAAO,SAAS,CAAC,IAAI,KACvB,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,KAAK,IAAI;AAAA,MAC5D,WACS,QAAQ;AACb,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA,IACJ;AACA,WAAO,IAAI,UAAS,UAAU,OAAO,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK,OAAO;AACf,QAAI,CAAC;AACD,aAAO,UAAS;AACpB,QAAI,iBAAiB;AACjB,aAAO;AACX,QAAI,MAAM,QAAQ,KAAK;AACnB,aAAO,KAAK,UAAU,KAAK;AAC/B,QAAI,MAAM;AACN,aAAO,IAAI,UAAS,CAAC,KAAK,GAAG,MAAM,QAAQ;AAC/C,UAAM,IAAI,WAAW,qBAAqB,QAAQ,oBAC7C,MAAM,eAAe,qEAAqE,GAAG;AAAA,EACtG;AACJ;AAMA,SAAS,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC;AACnC,IAAM,QAAQ,EAAE,OAAO,GAAG,QAAQ,EAAE;AACpC,SAAS,SAAS,OAAO,QAAQ;AAC7B,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,SAAO;AACX;AAEA,SAAS,YAAY,GAAG,GAAG;AACvB,MAAI,MAAM;AACN,WAAO;AACX,MAAI,EAAE,KAAK,OAAO,KAAK,aACnB,EAAE,KAAK,OAAO,KAAK;AACnB,WAAO;AACX,MAAI,QAAQ,MAAM,QAAQ,CAAC;AAC3B,MAAI,MAAM,QAAQ,CAAC,KAAK;AACpB,WAAO;AACX,MAAI,OAAO;AACP,QAAI,EAAE,UAAU,EAAE;AACd,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,UAAI,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACvB,eAAO;AAAA,EACnB,OACK;AACD,aAAS,KAAK;AACV,UAAI,EAAE,KAAK,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,eAAO;AACf,aAAS,KAAK;AACV,UAAI,EAAE,KAAK;AACP,eAAO;AAAA,EACnB;AACA,SAAO;AACX;AAUA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIP,YAIA,MAIA,OAAO;AACH,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,KAAK;AACV,QAAID,OAAM,SAAS;AACnB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,UAAI,QAAQ,IAAI,CAAC;AACjB,UAAI,KAAK,GAAG,KAAK;AACb,eAAO;AACX,UAAI,KAAK,KAAK,SAAS,MAAM,IAAI,GAAG;AAChC,YAAI,CAACA;AACD,UAAAA,QAAO,IAAI,MAAM,GAAG,CAAC;AAAA,MAC7B,WACS,MAAM,KAAK,SAAS,KAAK,IAAI,GAAG;AACrC,eAAO;AAAA,MACX,OACK;AACD,YAAI,CAAC,UAAU,MAAM,KAAK,OAAO,KAAK,KAAK,MAAM;AAC7C,cAAI,CAACA;AACD,YAAAA,QAAO,IAAI,MAAM,GAAG,CAAC;AACzB,UAAAA,MAAK,KAAK,IAAI;AACd,mBAAS;AAAA,QACb;AACA,YAAIA;AACA,UAAAA,MAAK,KAAK,KAAK;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,CAACA;AACD,MAAAA,QAAO,IAAI,MAAM;AACrB,QAAI,CAAC;AACD,MAAAA,MAAK,KAAK,IAAI;AAClB,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK;AACf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,KAAK,GAAG,IAAI,CAAC,CAAC;AACd,eAAO,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC;AACtD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,KAAK,GAAG,IAAI,CAAC,CAAC;AACd,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,OAAO;AACN,WAAO,QAAQ,SACV,KAAK,QAAQ,MAAM,QAAQ,YAAY,KAAK,OAAO,MAAM,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,MAAM,EAAE,MAAM,KAAK,KAAK,KAAK;AACjC,aAAS,KAAK,KAAK,OAAO;AACtB,UAAI,QAAQ,KAAK;AACjB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,iCAAiC;AAC1D,QAAI,OAAO,OAAO,MAAM,KAAK,IAAI;AACjC,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,yBAAyB,KAAK,IAAI,iBAAiB;AAC5E,QAAI,OAAO,KAAK,OAAO,KAAK,KAAK;AACjC,SAAK,WAAW,KAAK,KAAK;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,GAAG,GAAG;AACjB,QAAI,KAAK;AACL,aAAO;AACX,QAAI,EAAE,UAAU,EAAE;AACd,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,UAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACb,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,OAAO;AAClB,QAAI,CAAC,SAAS,MAAM,QAAQ,KAAK,KAAK,MAAM,UAAU;AAClD,aAAO,MAAK;AAChB,QAAI,iBAAiB;AACjB,aAAO,CAAC,KAAK;AACjB,QAAIA,QAAO,MAAM,MAAM;AACvB,IAAAA,MAAK,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,IAAI;AAC7C,WAAOA;AAAA,EACX;AACJ;AAIA,KAAK,OAAO,CAAC;AAMb,IAAM,eAAN,cAA2B,MAAM;AACjC;AAiBA,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaR,YAIA,SAIA,WAIA,SAAS;AACL,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,KAAK,QAAQ,OAAO,KAAK,YAAY,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,KAAK,UAAU;AACpB,QAAI,UAAU,WAAW,KAAK,SAAS,MAAM,KAAK,WAAW,QAAQ;AACrE,WAAO,WAAW,IAAI,OAAM,SAAS,KAAK,WAAW,KAAK,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM,IAAI;AACpB,WAAO,IAAI,OAAM,YAAY,KAAK,SAAS,OAAO,KAAK,WAAW,KAAK,KAAK,SAAS,GAAG,KAAK,WAAW,KAAK,OAAO;AAAA,EACxH;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,WAAO,KAAK,QAAQ,GAAG,MAAM,OAAO,KAAK,KAAK,aAAa,MAAM,aAAa,KAAK,WAAW,MAAM;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,UAAU,MAAM,KAAK,YAAY,MAAM,KAAK,UAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,CAAC,KAAK,QAAQ;AACd,aAAO;AACX,QAAI,OAAO,EAAE,SAAS,KAAK,QAAQ,OAAO,EAAE;AAC5C,QAAI,KAAK,YAAY;AACjB,WAAK,YAAY,KAAK;AAC1B,QAAI,KAAK,UAAU;AACf,WAAK,UAAU,KAAK;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC;AACD,aAAO,OAAM;AACjB,QAAI,YAAY,KAAK,aAAa,GAAG,UAAU,KAAK,WAAW;AAC/D,QAAI,OAAO,aAAa,YAAY,OAAO,WAAW;AAClD,YAAM,IAAI,WAAW,kCAAkC;AAC3D,WAAO,IAAI,OAAM,SAAS,SAAS,QAAQ,KAAK,OAAO,GAAG,WAAW,OAAO;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,UAAU,gBAAgB,MAAM;AAC3C,QAAI,YAAY,GAAG,UAAU;AAC7B,aAAS,IAAI,SAAS,YAAY,KAAK,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,KAAK,KAAK,YAAY,IAAI,EAAE;AACjG;AACJ,aAAS,IAAI,SAAS,WAAW,KAAK,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,KAAK,KAAK,YAAY,IAAI,EAAE;AAChG;AACJ,WAAO,IAAI,OAAM,UAAU,WAAW,OAAO;AAAA,EACjD;AACJ;AAIA,MAAM,QAAQ,IAAI,MAAM,SAAS,OAAO,GAAG,CAAC;AAC5C,SAAS,YAAY,SAAS,MAAM,IAAI;AACpC,MAAI,EAAE,OAAO,OAAO,IAAI,QAAQ,UAAU,IAAI,GAAG,QAAQ,QAAQ,WAAW,KAAK;AACjF,MAAI,EAAE,OAAO,SAAS,QAAQ,SAAS,IAAI,QAAQ,UAAU,EAAE;AAC/D,MAAI,UAAU,QAAQ,MAAM,QAAQ;AAChC,QAAI,YAAY,MAAM,CAAC,QAAQ,MAAM,OAAO,EAAE;AAC1C,YAAM,IAAI,WAAW,yBAAyB;AAClD,WAAO,QAAQ,IAAI,GAAG,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,CAAC;AAAA,EACtD;AACA,MAAI,SAAS;AACT,UAAM,IAAI,WAAW,yBAAyB;AAClD,SAAO,QAAQ,aAAa,OAAO,MAAM,KAAK,YAAY,MAAM,SAAS,OAAO,SAAS,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC;AACjH;AACA,SAAS,WAAW,SAAS,MAAM,QAAQ,QAAQ;AAC/C,MAAI,EAAE,OAAO,OAAO,IAAI,QAAQ,UAAU,IAAI,GAAG,QAAQ,QAAQ,WAAW,KAAK;AACjF,MAAI,UAAU,QAAQ,MAAM,QAAQ;AAChC,QAAI,UAAU,CAAC,OAAO,WAAW,OAAO,OAAO,MAAM;AACjD,aAAO;AACX,WAAO,QAAQ,IAAI,GAAG,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,QAAQ,IAAI,IAAI,CAAC;AAAA,EACvE;AACA,MAAI,QAAQ,WAAW,MAAM,SAAS,OAAO,SAAS,GAAG,MAAM;AAC/D,SAAO,SAAS,QAAQ,aAAa,OAAO,MAAM,KAAK,KAAK,CAAC;AACjE;AACA,SAAS,QAAQ,OAAO,KAAK,OAAO;AAChC,MAAI,MAAM,YAAY,MAAM;AACxB,UAAM,IAAI,aAAa,iDAAiD;AAC5E,MAAI,MAAM,QAAQ,MAAM,aAAa,IAAI,QAAQ,MAAM;AACnD,UAAM,IAAI,aAAa,0BAA0B;AACrD,SAAO,aAAa,OAAO,KAAK,OAAO,CAAC;AAC5C;AACA,SAAS,aAAa,OAAO,KAAK,OAAO,OAAO;AAC5C,MAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,KAAK;AACvD,MAAI,SAAS,IAAI,MAAM,KAAK,KAAK,QAAQ,MAAM,QAAQ,MAAM,WAAW;AACpE,QAAI,QAAQ,aAAa,OAAO,KAAK,OAAO,QAAQ,CAAC;AACrD,WAAO,KAAK,KAAK,KAAK,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAC5D,WACS,CAAC,MAAM,QAAQ,MAAM;AAC1B,WAAO,MAAM,MAAM,cAAc,OAAO,KAAK,KAAK,CAAC;AAAA,EACvD,WACS,CAAC,MAAM,aAAa,CAAC,MAAM,WAAW,MAAM,SAAS,SAAS,IAAI,SAAS,OAAO;AACvF,QAAI,SAAS,MAAM,QAAQ,UAAU,OAAO;AAC5C,WAAO,MAAM,QAAQ,QAAQ,IAAI,GAAG,MAAM,YAAY,EAAE,OAAO,MAAM,OAAO,EAAE,OAAO,QAAQ,IAAI,IAAI,YAAY,CAAC,CAAC;AAAA,EACvH,OACK;AACD,QAAI,EAAE,OAAO,IAAI,IAAI,uBAAuB,OAAO,KAAK;AACxD,WAAO,MAAM,MAAM,gBAAgB,OAAO,OAAO,KAAK,KAAK,KAAK,CAAC;AAAA,EACrE;AACJ;AACA,SAAS,UAAU,MAAM,KAAK;AAC1B,MAAI,CAAC,IAAI,KAAK,kBAAkB,KAAK,IAAI;AACrC,UAAM,IAAI,aAAa,iBAAiB,IAAI,KAAK,OAAO,WAAW,KAAK,KAAK,IAAI;AACzF;AACA,SAAS,SAAS,SAAS,QAAQ,OAAO;AACtC,MAAI,OAAO,QAAQ,KAAK,KAAK;AAC7B,YAAU,MAAM,OAAO,KAAK,KAAK,CAAC;AAClC,SAAO;AACX;AACA,SAAS,QAAQ,OAAO,QAAQ;AAC5B,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,QAAQ,KAAK,MAAM,UAAU,MAAM,WAAW,OAAO,IAAI,CAAC;AAC1D,WAAO,IAAI,IAAI,MAAM,SAAS,OAAO,IAAI,EAAE,OAAO,MAAM,IAAI;AAAA;AAE5D,WAAO,KAAK,KAAK;AACzB;AACA,SAAS,SAAS,QAAQ,MAAM,OAAO,QAAQ;AAC3C,MAAI,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACtC,MAAI,aAAa,GAAG,WAAW,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK;AAC/D,MAAI,QAAQ;AACR,iBAAa,OAAO,MAAM,KAAK;AAC/B,QAAI,OAAO,QAAQ,OAAO;AACtB;AAAA,IACJ,WACS,OAAO,YAAY;AACxB,cAAQ,OAAO,WAAW,MAAM;AAChC;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,IAAI,YAAY,IAAI,UAAU;AACnC,YAAQ,KAAK,MAAM,CAAC,GAAG,MAAM;AACjC,MAAI,QAAQ,KAAK,SAAS,SAAS,KAAK;AACpC,YAAQ,KAAK,YAAY,MAAM;AACvC;AACA,SAAS,MAAM,MAAM,SAAS;AAC1B,OAAK,KAAK,aAAa,OAAO;AAC9B,SAAO,KAAK,KAAK,OAAO;AAC5B;AACA,SAAS,gBAAgB,OAAO,QAAQ,MAAM,KAAK,OAAO;AACtD,MAAI,YAAY,MAAM,QAAQ,SAAS,SAAS,OAAO,QAAQ,QAAQ,CAAC;AACxE,MAAI,UAAU,IAAI,QAAQ,SAAS,SAAS,MAAM,KAAK,QAAQ,CAAC;AAChE,MAAI,UAAU,CAAC;AACf,WAAS,MAAM,OAAO,OAAO,OAAO;AACpC,MAAI,aAAa,WAAW,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG;AAClE,cAAU,WAAW,OAAO;AAC5B,YAAQ,MAAM,WAAW,gBAAgB,OAAO,QAAQ,MAAM,KAAK,QAAQ,CAAC,CAAC,GAAG,OAAO;AAAA,EAC3F,OACK;AACD,QAAI;AACA,cAAQ,MAAM,WAAW,cAAc,OAAO,QAAQ,QAAQ,CAAC,CAAC,GAAG,OAAO;AAC9E,aAAS,QAAQ,MAAM,OAAO,OAAO;AACrC,QAAI;AACA,cAAQ,MAAM,SAAS,cAAc,MAAM,KAAK,QAAQ,CAAC,CAAC,GAAG,OAAO;AAAA,EAC5E;AACA,WAAS,KAAK,MAAM,OAAO,OAAO;AAClC,SAAO,IAAI,SAAS,OAAO;AAC/B;AACA,SAAS,cAAc,OAAO,KAAK,OAAO;AACtC,MAAI,UAAU,CAAC;AACf,WAAS,MAAM,OAAO,OAAO,OAAO;AACpC,MAAI,MAAM,QAAQ,OAAO;AACrB,QAAI,OAAO,SAAS,OAAO,KAAK,QAAQ,CAAC;AACzC,YAAQ,MAAM,MAAM,cAAc,OAAO,KAAK,QAAQ,CAAC,CAAC,GAAG,OAAO;AAAA,EACtE;AACA,WAAS,KAAK,MAAM,OAAO,OAAO;AAClC,SAAO,IAAI,SAAS,OAAO;AAC/B;AACA,SAAS,uBAAuB,OAAO,QAAQ;AAC3C,MAAI,QAAQ,OAAO,QAAQ,MAAM,WAAW,SAAS,OAAO,KAAK,KAAK;AACtE,MAAI,OAAO,OAAO,KAAK,MAAM,OAAO;AACpC,WAAS,IAAI,QAAQ,GAAG,KAAK,GAAG;AAC5B,WAAO,OAAO,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,CAAC;AAClD,SAAO;AAAA,IAAE,OAAO,KAAK,eAAe,MAAM,YAAY,KAAK;AAAA,IACvD,KAAK,KAAK,eAAe,KAAK,QAAQ,OAAO,MAAM,UAAU,KAAK;AAAA,EAAE;AAC5E;AAYA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAId,YAIA,KAIA,MAIA,cAAc;AACV,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,QAAQ,KAAK,SAAS,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK;AACd,QAAI,OAAO;AACP,aAAO,KAAK;AAChB,QAAI,MAAM;AACN,aAAO,KAAK,QAAQ;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI7C,IAAI,MAAM;AAAE,WAAO,KAAK,KAAK,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,KAAK,OAAO;AAAE,WAAO,KAAK,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9D,MAAM,OAAO;AAAE,WAAO,KAAK,KAAK,KAAK,aAAa,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnE,WAAW,OAAO;AACd,YAAQ,KAAK,aAAa,KAAK;AAC/B,WAAO,KAAK,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS,CAAC,KAAK,aAAa,IAAI;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACT,YAAQ,KAAK,aAAa,KAAK;AAC/B,WAAO,SAAS,IAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACP,YAAQ,KAAK,aAAa,KAAK;AAC/B,WAAO,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,EAAE,QAAQ;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACV,YAAQ,KAAK,aAAa,KAAK;AAC/B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,gDAAgD;AACzE,WAAO,SAAS,KAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,IAAI,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACT,YAAQ,KAAK,aAAa,KAAK;AAC/B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,+CAA+C;AACxE,WAAO,SAAS,KAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AAAE,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtE,IAAI,YAAY;AACZ,QAAI,SAAS,KAAK,QAAQ,QAAQ,KAAK,MAAM,KAAK,KAAK;AACvD,QAAI,SAAS,OAAO;AAChB,aAAO;AACX,QAAI,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,GAAG,QAAQ,OAAO,MAAM,KAAK;AACjF,WAAO,OAAO,OAAO,MAAM,KAAK,EAAE,IAAI,IAAI,IAAI;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACb,QAAI,QAAQ,KAAK,MAAM,KAAK,KAAK;AACjC,QAAI,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AACpD,QAAI;AACA,aAAO,KAAK,OAAO,MAAM,KAAK,EAAE,IAAI,GAAG,IAAI;AAC/C,WAAO,SAAS,IAAI,OAAO,KAAK,OAAO,MAAM,QAAQ,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO,OAAO;AACrB,YAAQ,KAAK,aAAa,KAAK;AAC/B,QAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,GAAG,MAAM,SAAS,IAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI;AACnF,aAAS,IAAI,GAAG,IAAI,OAAO;AACvB,aAAO,KAAK,MAAM,CAAC,EAAE;AACzB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACJ,QAAI,SAAS,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAE7C,QAAI,OAAO,QAAQ,QAAQ;AACvB,aAAO,KAAK;AAEhB,QAAI,KAAK;AACL,aAAO,OAAO,MAAM,KAAK,EAAE;AAC/B,QAAI,OAAO,OAAO,WAAW,QAAQ,CAAC,GAAG,QAAQ,OAAO,WAAW,KAAK;AAGxE,QAAI,CAAC,MAAM;AACP,UAAI,MAAM;AACV,aAAO;AACP,cAAQ;AAAA,IACZ;AAGA,QAAI,QAAQ,KAAK;AACjB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,MAAM,CAAC,EAAE,KAAK,KAAK,cAAc,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,MAAM,KAAK;AAClF,gBAAQ,MAAM,GAAG,EAAE,cAAc,KAAK;AAC9C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,MAAM;AACd,QAAI,QAAQ,KAAK,OAAO,WAAW,KAAK,MAAM,CAAC;AAC/C,QAAI,CAAC,SAAS,CAAC,MAAM;AACjB,aAAO;AACX,QAAI,QAAQ,MAAM,OAAO,OAAO,KAAK,OAAO,WAAW,KAAK,MAAM,CAAC;AACnE,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,MAAM,CAAC,EAAE,KAAK,KAAK,cAAc,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,KAAK,KAAK;AAChF,gBAAQ,MAAM,GAAG,EAAE,cAAc,KAAK;AAC9C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,KAAK;AACb,aAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG;AACpC,UAAI,KAAK,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,KAAK;AAC/C,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,QAAQ,MAAM,MAAM;AAC3B,QAAI,MAAM,MAAM,KAAK;AACjB,aAAO,MAAM,WAAW,IAAI;AAChC,aAAS,IAAI,KAAK,SAAS,KAAK,OAAO,iBAAiB,KAAK,OAAO,MAAM,MAAM,IAAI,IAAI,KAAK,GAAG;AAC5F,UAAI,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,KAAK,CAAC,CAAC;AACvD,eAAO,IAAI,UAAU,MAAM,OAAO,CAAC;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,MAAM,KAAK,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,MAAM,MAAM,KAAK,MAAM,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,MAAM,MAAM,KAAK,MAAM,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO;AAC7B,cAAQ,MAAM,MAAM,MAAM,KAAK,KAAK,CAAC,EAAE,KAAK,OAAO,MAAM,KAAK,MAAM,IAAI,CAAC;AAC7E,WAAO,MAAM,MAAM,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQE,MAAK,KAAK;AACrB,QAAI,EAAE,OAAO,KAAK,OAAOA,KAAI,QAAQ;AACjC,YAAM,IAAI,WAAW,cAAc,MAAM,eAAe;AAC5D,QAAI,OAAO,CAAC;AACZ,QAAI,QAAQ,GAAG,eAAe;AAC9B,aAAS,OAAOA,UAAO;AACnB,UAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,YAAY;AAC3D,UAAI,MAAM,eAAe;AACzB,WAAK,KAAK,MAAM,OAAO,QAAQ,MAAM;AACrC,UAAI,CAAC;AACD;AACJ,aAAO,KAAK,MAAM,KAAK;AACvB,UAAI,KAAK;AACL;AACJ,qBAAe,MAAM;AACrB,eAAS,SAAS;AAAA,IACtB;AACA,WAAO,IAAI,aAAY,KAAK,MAAM,YAAY;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,cAAcA,MAAK,KAAK;AAC3B,QAAI,QAAQ,aAAa,IAAIA,IAAG;AAChC,QAAI,OAAO;AACP,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAI,MAAM,MAAM,KAAK,CAAC;AACtB,YAAI,IAAI,OAAO;AACX,iBAAO;AAAA,MACf;AAAA,IACJ,OACK;AACD,mBAAa,IAAIA,MAAK,QAAQ,IAAI,cAAY;AAAA,IAClD;AACA,QAAI,SAAS,MAAM,KAAK,MAAM,CAAC,IAAI,aAAY,QAAQA,MAAK,GAAG;AAC/D,UAAM,KAAK,MAAM,IAAI,KAAK;AAC1B,WAAO;AAAA,EACX;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,cAAc;AACV,SAAK,OAAO,CAAC;AACb,SAAK,IAAI;AAAA,EACb;AACJ;AACA,IAAM,mBAAmB;AAAzB,IAA6B,eAAe,oBAAI,QAAQ;AAKxD,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAOA,OAKA,KAIA,OAAO;AACH,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AAAE,WAAO,KAAK,MAAM,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIxD,IAAI,MAAM;AAAE,WAAO,KAAK,IAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInD,IAAI,SAAS;AAAE,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInD,IAAI,aAAa;AAAE,WAAO,KAAK,MAAM,MAAM,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIxD,IAAI,WAAW;AAAE,WAAO,KAAK,IAAI,WAAW,KAAK,KAAK;AAAA,EAAG;AAC7D;AAEA,IAAM,aAAa,uBAAO,OAAO,IAAI;AAerC,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIP,YAIA,MAMA,OAEA,SAKA,QAAQ,KAAK,MAAM;AACf,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,UAAU,WAAW,SAAS;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9C,IAAI,WAAW;AAAE,WAAO,KAAK,SAAS,IAAI,IAAI,KAAK,QAAQ;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIjE,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD,MAAM,OAAO;AAAE,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIjD,WAAW,OAAO;AAAE,WAAO,KAAK,QAAQ,WAAW,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,QAAQ,GAAG;AAAE,SAAK,QAAQ,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtC,aAAa,MAAM,IAAI,GAAG,WAAW,GAAG;AACpC,SAAK,QAAQ,aAAa,MAAM,IAAI,GAAG,UAAU,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,GAAG;AACX,SAAK,aAAa,GAAG,KAAK,QAAQ,MAAM,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AACd,WAAQ,KAAK,UAAU,KAAK,KAAK,KAAK,WAChC,KAAK,KAAK,KAAK,SAAS,IAAI,IAC5B,KAAK,YAAY,GAAG,KAAK,QAAQ,MAAM,EAAE;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,MAAM,IAAI,gBAAgB,UAAU;AAC5C,WAAO,KAAK,QAAQ,YAAY,MAAM,IAAI,gBAAgB,QAAQ;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD,IAAI,YAAY;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAW;AAAA;AAAA;AAAA;AAAA,EAIjD,GAAG,OAAO;AACN,WAAO,QAAQ,SAAU,KAAK,WAAW,KAAK,KAAK,KAAK,QAAQ,GAAG,MAAM,OAAO;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AACd,WAAO,KAAK,UAAU,MAAM,MAAM,MAAM,OAAO,MAAM,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM,OAAO,OAAO;AAC1B,WAAO,KAAK,QAAQ,QAChB,YAAY,KAAK,OAAO,SAAS,KAAK,gBAAgB,UAAU,KAChE,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,UAAU,MAAM;AACjB,QAAI,WAAW,KAAK;AAChB,aAAO;AACX,WAAO,IAAI,MAAK,KAAK,MAAM,KAAK,OAAO,SAAS,KAAK,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO;AACR,WAAO,SAAS,KAAK,QAAQ,OAAO,IAAI,MAAK,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM,KAAK,KAAK,QAAQ,MAAM;AAC9B,QAAI,QAAQ,KAAK,MAAM,KAAK,QAAQ;AAChC,aAAO;AACX,WAAO,KAAK,KAAK,KAAK,QAAQ,IAAI,MAAM,EAAE,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,iBAAiB,OAAO;AACxD,QAAI,QAAQ;AACR,aAAO,MAAM;AACjB,QAAI,QAAQ,KAAK,QAAQ,IAAI,GAAG,MAAM,KAAK,QAAQ,EAAE;AACrD,QAAI,QAAQ,iBAAiB,IAAI,MAAM,YAAY,EAAE;AACrD,QAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,KAAK;AACvD,QAAI,UAAU,KAAK,QAAQ,IAAI,MAAM,MAAM,OAAO,IAAI,MAAM,KAAK;AACjE,WAAO,IAAI,MAAM,SAAS,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,MAAM,IAAI,OAAO;AACrB,WAAO,QAAQ,KAAK,QAAQ,IAAI,GAAG,KAAK,QAAQ,EAAE,GAAG,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK;AACR,aAAS,OAAO,UAAQ;AACpB,UAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,GAAG;AAClD,aAAO,KAAK,WAAW,KAAK;AAC5B,UAAI,CAAC;AACD,eAAO;AACX,UAAI,UAAU,OAAO,KAAK;AACtB,eAAO;AACX,aAAO,SAAS;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,KAAK;AACZ,QAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,GAAG;AAClD,WAAO,EAAE,MAAM,KAAK,QAAQ,WAAW,KAAK,GAAG,OAAO,OAAO;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,KAAK;AACb,QAAI,OAAO;AACP,aAAO,EAAE,MAAM,MAAM,OAAO,GAAG,QAAQ,EAAE;AAC7C,QAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,GAAG;AAClD,QAAI,SAAS;AACT,aAAO,EAAE,MAAM,KAAK,QAAQ,MAAM,KAAK,GAAG,OAAO,OAAO;AAC5D,QAAI,OAAO,KAAK,QAAQ,MAAM,QAAQ,CAAC;AACvC,WAAO,EAAE,MAAM,OAAO,QAAQ,GAAG,QAAQ,SAAS,KAAK,SAAS;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,KAAK;AAAE,WAAO,YAAY,cAAc,MAAM,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI5D,eAAe,KAAK;AAAE,WAAO,YAAY,QAAQ,MAAM,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,aAAa,MAAM,IAAI,MAAM;AACzB,QAAID,SAAQ;AACZ,QAAI,KAAK;AACL,WAAK,aAAa,MAAM,IAAI,UAAQ;AAChC,YAAI,KAAK,QAAQ,KAAK,KAAK;AACvB,UAAAA,SAAQ;AACZ,eAAO,CAACA;AAAA,MACZ,CAAC;AACL,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AAAE,WAAO,KAAK,KAAK;AAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,IAAI,cAAc;AAAE,WAAO,KAAK,KAAK;AAAA,EAAa;AAAA;AAAA;AAAA;AAAA,EAIlD,IAAI,gBAAgB;AAAE,WAAO,KAAK,KAAK;AAAA,EAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,IAAI,WAAW;AAAE,WAAO,KAAK,KAAK;AAAA,EAAU;AAAA;AAAA;AAAA;AAAA,EAI5C,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA,EAIxC,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxC,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxC,WAAW;AACP,QAAI,KAAK,KAAK,KAAK;AACf,aAAO,KAAK,KAAK,KAAK,cAAc,IAAI;AAC5C,QAAI,OAAO,KAAK,KAAK;AACrB,QAAI,KAAK,QAAQ;AACb,cAAQ,MAAM,KAAK,QAAQ,cAAc,IAAI;AACjD,WAAO,UAAU,KAAK,OAAO,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,OAAO;AAClB,QAAI,QAAQ,KAAK,KAAK,aAAa,cAAc,KAAK,SAAS,GAAG,KAAK;AACvE,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,sDAAsD;AAC1E,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,IAAI,cAAc,SAAS,OAAO,QAAQ,GAAG,MAAM,YAAY,YAAY;AACxF,QAAI,MAAM,KAAK,eAAe,IAAI,EAAE,cAAc,aAAa,OAAO,GAAG;AACzE,QAAI,MAAM,OAAO,IAAI,cAAc,KAAK,SAAS,EAAE;AACnD,QAAI,CAAC,OAAO,CAAC,IAAI;AACb,aAAO;AACX,aAAS,IAAI,OAAO,IAAI,KAAK;AACzB,UAAI,CAAC,KAAK,KAAK,YAAY,YAAY,MAAM,CAAC,EAAE,KAAK;AACjD,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM,IAAI,MAAM,OAAO;AAClC,QAAI,SAAS,CAAC,KAAK,KAAK,YAAY,KAAK;AACrC,aAAO;AACX,QAAI,QAAQ,KAAK,eAAe,IAAI,EAAE,UAAU,IAAI;AACpD,QAAI,MAAM,SAAS,MAAM,cAAc,KAAK,SAAS,EAAE;AACvD,WAAO,MAAM,IAAI,WAAW;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,OAAO;AACb,QAAI,MAAM,QAAQ;AACd,aAAO,KAAK,WAAW,KAAK,YAAY,KAAK,YAAY,MAAM,OAAO;AAAA;AAEtE,aAAO,KAAK,KAAK,kBAAkB,MAAM,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACJ,SAAK,KAAK,aAAa,KAAK,OAAO;AACnC,SAAK,KAAK,WAAW,KAAK,KAAK;AAC/B,QAAID,QAAO,KAAK;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,WAAK,KAAK,WAAW,KAAK,KAAK;AAC/B,MAAAA,QAAO,KAAK,SAASA,KAAI;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,QAAQA,OAAM,KAAK,KAAK;AAC9B,YAAM,IAAI,WAAW,wCAAwC,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,OAAK,EAAE,KAAK,IAAI,CAAC,EAAE;AACtH,SAAK,QAAQ,QAAQ,UAAQ,KAAK,MAAM,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,MAAM,EAAE,MAAM,KAAK,KAAK,KAAK;AACjC,aAAS,KAAK,KAAK,OAAO;AACtB,UAAI,QAAQ,KAAK;AACjB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ;AACb,UAAI,UAAU,KAAK,QAAQ,OAAO;AACtC,QAAI,KAAK,MAAM;AACX,UAAI,QAAQ,KAAK,MAAM,IAAI,OAAK,EAAE,OAAO,CAAC;AAC9C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,iCAAiC;AAC1D,QAAI,QAAQ;AACZ,QAAI,KAAK,OAAO;AACZ,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK;AACzB,cAAM,IAAI,WAAW,qCAAqC;AAC9D,cAAQ,KAAK,MAAM,IAAI,OAAO,YAAY;AAAA,IAC9C;AACA,QAAI,KAAK,QAAQ,QAAQ;AACrB,UAAI,OAAO,KAAK,QAAQ;AACpB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAO,OAAO,KAAK,KAAK,MAAM,KAAK;AAAA,IACvC;AACA,QAAI,UAAU,SAAS,SAAS,QAAQ,KAAK,OAAO;AACpD,QAAI,OAAO,OAAO,SAAS,KAAK,IAAI,EAAE,OAAO,KAAK,OAAO,SAAS,KAAK;AACvE,SAAK,KAAK,WAAW,KAAK,KAAK;AAC/B,WAAO;AAAA,EACX;AACJ;AACA,KAAK,UAAU,OAAO;AACtB,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA;AAAA;AAAA;AAAA,EAIxB,YAAY,MAAM,OAAO,SAAS,OAAO;AACrC,UAAM,MAAM,OAAO,MAAM,KAAK;AAC9B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,kCAAkC;AAC3D,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,WAAW;AACP,QAAI,KAAK,KAAK,KAAK;AACf,aAAO,KAAK,KAAK,KAAK,cAAc,IAAI;AAC5C,WAAO,UAAU,KAAK,OAAO,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,EAC1D;AAAA,EACA,IAAI,cAAc;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACtC,YAAY,MAAM,IAAI;AAAE,WAAO,KAAK,KAAK,MAAM,MAAM,EAAE;AAAA,EAAG;AAAA,EAC1D,IAAI,WAAW;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EAC1C,KAAK,OAAO;AACR,WAAO,SAAS,KAAK,QAAQ,OAAO,IAAI,UAAS,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK;AAAA,EAC5F;AAAA,EACA,SAAS,MAAM;AACX,QAAI,QAAQ,KAAK;AACb,aAAO;AACX,WAAO,IAAI,UAAS,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,KAAK;AAAA,EAC/D;AAAA,EACA,IAAI,OAAO,GAAG,KAAK,KAAK,KAAK,QAAQ;AACjC,QAAI,QAAQ,KAAK,MAAM,KAAK,KAAK;AAC7B,aAAO;AACX,WAAO,KAAK,SAAS,KAAK,KAAK,MAAM,MAAM,EAAE,CAAC;AAAA,EAClD;AAAA,EACA,GAAG,OAAO;AACN,WAAO,KAAK,WAAW,KAAK,KAAK,KAAK,QAAQ,MAAM;AAAA,EACxD;AAAA,EACA,SAAS;AACL,QAAI,OAAO,MAAM,OAAO;AACxB,SAAK,OAAO,KAAK;AACjB,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,OAAO,KAAK;AAC3B,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AACnC,UAAM,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,MAAM;AAC3C,SAAO;AACX;AAQA,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA;AAAA;AAAA,EAIf,YAIA,UAAU;AACN,SAAK,WAAW;AAIhB,SAAK,OAAO,CAAC;AAIb,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ,WAAW;AAC5B,QAAI,SAAS,IAAI,YAAY,QAAQ,SAAS;AAC9C,QAAI,OAAO,QAAQ;AACf,aAAO,cAAa;AACxB,QAAI,OAAO,UAAU,MAAM;AAC3B,QAAI,OAAO;AACP,aAAO,IAAI,0BAA0B;AACzC,QAAI,QAAQ,IAAI,IAAI,IAAI,CAAC;AACzB,qBAAiB,OAAO,MAAM;AAC9B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ;AAClC,UAAI,KAAK,KAAK,CAAC,EAAE,QAAQ;AACrB,eAAO,KAAK,KAAK,CAAC,EAAE;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,MAAM,QAAQ,GAAG,MAAM,KAAK,YAAY;AAClD,QAAI,MAAM;AACV,aAAS,IAAI,OAAO,OAAO,IAAI,KAAK;AAChC,YAAM,IAAI,UAAU,KAAK,MAAM,CAAC,EAAE,IAAI;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,gBAAgB;AAChB,WAAO,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,UAAI,EAAE,KAAK,IAAI,KAAK,KAAK,CAAC;AAC1B,UAAI,EAAE,KAAK,UAAU,KAAK,iBAAiB;AACvC,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ;AAClC,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ;AACnC,YAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,MAAM,KAAK,CAAC,EAAE;AACnC,iBAAO;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,OAAO,QAAQ,OAAO,aAAa,GAAG;AAC7C,QAAI,OAAO,CAAC,IAAI;AAChB,aAAS,OAAO,OAAO,OAAO;AAC1B,UAAI,WAAW,MAAM,cAAc,OAAO,UAAU;AACpD,UAAI,aAAa,CAAC,SAAS,SAAS;AAChC,eAAO,SAAS,KAAK,MAAM,IAAI,QAAM,GAAG,cAAc,CAAC,CAAC;AAC5D,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAI,EAAE,KAAK,UAAU,KAAK,iBAAiB,MAAM,KAAK,QAAQ,IAAI,KAAK,IAAI;AACvE,eAAK,KAAK,IAAI;AACd,cAAIC,SAAQ,OAAO,MAAM,MAAM,OAAO,IAAI,CAAC;AAC3C,cAAIA;AACA,mBAAOA;AAAA,QACf;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,OAAO,MAAM,CAAC,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,QAAQ;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,KAAK,UAAU,CAAC,KAAK;AACrB,eAAO,KAAK,UAAU,IAAI,CAAC;AACnC,QAAI,WAAW,KAAK,gBAAgB,MAAM;AAC1C,SAAK,UAAU,KAAK,QAAQ,QAAQ;AACpC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,QAAQ;AACpB,QAAI,OAAO,uBAAO,OAAO,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,MAAM,MAAM,MAAM,KAAK,KAAK,CAAC;AAChF,WAAO,OAAO,QAAQ;AAClB,UAAI,UAAU,OAAO,MAAM,GAAG,QAAQ,QAAQ;AAC9C,UAAI,MAAM,UAAU,MAAM,GAAG;AACzB,YAAI,SAAS,CAAC;AACd,iBAAS,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI;AACxC,iBAAO,KAAK,IAAI,IAAI;AACxB,eAAO,OAAO,QAAQ;AAAA,MAC1B;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAI,CAAC,KAAK,UAAU,CAAC,KAAK,iBAAiB,KAAK,EAAE,KAAK,QAAQ,UAAU,CAAC,QAAQ,QAAQ,KAAK,WAAW;AACtG,iBAAO,KAAK,EAAE,OAAO,KAAK,cAAc,MAAM,KAAK,QAAQ,CAAC;AAC5D,eAAK,KAAK,IAAI,IAAI;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,WAAO,KAAK,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,GAAG;AACJ,QAAI,KAAK,KAAK,KAAK;AACf,YAAM,IAAI,WAAW,cAAc,CAAC,+BAA+B;AACvE,WAAO,KAAK,KAAK,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG;AACb,WAAK,KAAK,CAAC;AACX,eAAS,IAAI,GAAG,IAAI,EAAE,KAAK,QAAQ;AAC/B,YAAI,KAAK,QAAQ,EAAE,KAAK,CAAC,EAAE,IAAI,KAAK;AAChC,eAAK,EAAE,KAAK,CAAC,EAAE,IAAI;AAAA,IAC/B;AACA,SAAK,IAAI;AACT,WAAO,KAAK,IAAI,CAAC,GAAG,MAAM;AACtB,UAAI,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO;AACzC,eAASE,KAAI,GAAGA,KAAI,EAAE,KAAK,QAAQA;AAC/B,gBAAQA,KAAI,OAAO,MAAM,EAAE,KAAKA,EAAC,EAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,EAAE,KAAKA,EAAC,EAAE,IAAI;AACrF,aAAO;AAAA,IACX,CAAC,EAAE,KAAK,IAAI;AAAA,EAChB;AACJ;AAIA,aAAa,QAAQ,IAAI,aAAa,IAAI;AAC1C,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,QAAQ,WAAW;AAC3B,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,SAAS,OAAO,MAAM,gBAAgB;AAC3C,QAAI,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,KAAK;AACvC,WAAK,OAAO,IAAI;AACpB,QAAI,KAAK,OAAO,CAAC,KAAK;AAClB,WAAK,OAAO,MAAM;AAAA,EAC1B;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO,KAAK,GAAG;AAAA,EAAG;AAAA,EAC3C,IAAI,KAAK;AAAE,WAAO,KAAK,QAAQ,QAAQ,KAAK,SAAS;AAAA,EAAO;AAAA,EAC5D,IAAI,KAAK;AAAE,UAAM,IAAI,YAAY,MAAM,8BAA8B,KAAK,SAAS,IAAI;AAAA,EAAG;AAC9F;AACA,SAAS,UAAU,QAAQ;AACvB,MAAI,QAAQ,CAAC;AACb,KAAG;AACC,UAAM,KAAK,aAAa,MAAM,CAAC;AAAA,EACnC,SAAS,OAAO,IAAI,GAAG;AACvB,SAAO,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,UAAU,MAAM;AAClE;AACA,SAAS,aAAa,QAAQ;AAC1B,MAAI,QAAQ,CAAC;AACb,KAAG;AACC,UAAM,KAAK,mBAAmB,MAAM,CAAC;AAAA,EACzC,SAAS,OAAO,QAAQ,OAAO,QAAQ,OAAO,OAAO,QAAQ;AAC7D,SAAO,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,OAAO,MAAM;AAC/D;AACA,SAAS,mBAAmB,QAAQ;AAChC,MAAI,OAAO,cAAc,MAAM;AAC/B,aAAS;AACL,QAAI,OAAO,IAAI,GAAG;AACd,aAAO,EAAE,MAAM,QAAQ,KAAK;AAAA,aACvB,OAAO,IAAI,GAAG;AACnB,aAAO,EAAE,MAAM,QAAQ,KAAK;AAAA,aACvB,OAAO,IAAI,GAAG;AACnB,aAAO,EAAE,MAAM,OAAO,KAAK;AAAA,aACtB,OAAO,IAAI,GAAG;AACnB,aAAO,eAAe,QAAQ,IAAI;AAAA;AAElC;AAAA,EACR;AACA,SAAO;AACX;AACA,SAAS,SAAS,QAAQ;AACtB,MAAI,KAAK,KAAK,OAAO,IAAI;AACrB,WAAO,IAAI,2BAA2B,OAAO,OAAO,GAAG;AAC3D,MAAI,SAAS,OAAO,OAAO,IAAI;AAC/B,SAAO;AACP,SAAO;AACX;AACA,SAAS,eAAe,QAAQ,MAAM;AAClC,MAAI,MAAM,SAAS,MAAM,GAAG,MAAM;AAClC,MAAI,OAAO,IAAI,GAAG,GAAG;AACjB,QAAI,OAAO,QAAQ;AACf,YAAM,SAAS,MAAM;AAAA;AAErB,YAAM;AAAA,EACd;AACA,MAAI,CAAC,OAAO,IAAI,GAAG;AACf,WAAO,IAAI,uBAAuB;AACtC,SAAO,EAAE,MAAM,SAAS,KAAK,KAAK,KAAK;AAC3C;AACA,SAAS,YAAY,QAAQ,MAAM;AAC/B,MAAI,QAAQ,OAAO,WAAW,OAAO,MAAM,IAAI;AAC/C,MAAI;AACA,WAAO,CAAC,IAAI;AAChB,MAAI,SAAS,CAAC;AACd,WAAS,YAAY,OAAO;AACxB,QAAIC,QAAO,MAAM,QAAQ;AACzB,QAAIA,MAAK,UAAU,IAAI;AACnB,aAAO,KAAKA,KAAI;AAAA,EACxB;AACA,MAAI,OAAO,UAAU;AACjB,WAAO,IAAI,4BAA4B,OAAO,SAAS;AAC3D,SAAO;AACX;AACA,SAAS,cAAc,QAAQ;AAC3B,MAAI,OAAO,IAAI,GAAG,GAAG;AACjB,QAAI,OAAO,UAAU,MAAM;AAC3B,QAAI,CAAC,OAAO,IAAI,GAAG;AACf,aAAO,IAAI,uBAAuB;AACtC,WAAO;AAAA,EACX,WACS,CAAC,KAAK,KAAK,OAAO,IAAI,GAAG;AAC9B,QAAI,QAAQ,YAAY,QAAQ,OAAO,IAAI,EAAE,IAAI,UAAQ;AACrD,UAAI,OAAO,UAAU;AACjB,eAAO,SAAS,KAAK;AAAA,eAChB,OAAO,UAAU,KAAK;AAC3B,eAAO,IAAI,iCAAiC;AAChD,aAAO,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IACvC,CAAC;AACD,WAAO;AACP,WAAO,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,UAAU,MAAM;AAAA,EAClE,OACK;AACD,WAAO,IAAI,uBAAuB,OAAO,OAAO,GAAG;AAAA,EACvD;AACJ;AASA,SAAS,IAAI,MAAM;AACf,MAAIC,OAAM,CAAC,CAAC,CAAC;AACb,UAAQ,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC;AAChC,SAAOA;AACP,WAAS,OAAO;AAAE,WAAOA,KAAI,KAAK,CAAC,CAAC,IAAI;AAAA,EAAG;AAC3C,WAAS,KAAK,MAAM,IAAI,MAAM;AAC1B,QAAIC,QAAO,EAAE,MAAM,GAAG;AACtB,IAAAD,KAAI,IAAI,EAAE,KAAKC,KAAI;AACnB,WAAOA;AAAA,EACX;AACA,WAAS,QAAQ,OAAO,IAAI;AACxB,UAAM,QAAQ,CAAAA,UAAQA,MAAK,KAAK,EAAE;AAAA,EACtC;AACA,WAAS,QAAQC,OAAM,MAAM;AACzB,QAAIA,MAAK,QAAQ,UAAU;AACvB,aAAOA,MAAK,MAAM,OAAO,CAAC,KAAKA,UAAS,IAAI,OAAO,QAAQA,OAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAAA,IAC/E,WACSA,MAAK,QAAQ,OAAO;AACzB,eAAS,IAAI,KAAI,KAAK;AAClB,YAAI,OAAO,QAAQA,MAAK,MAAM,CAAC,GAAG,IAAI;AACtC,YAAI,KAAKA,MAAK,MAAM,SAAS;AACzB,iBAAO;AACX,gBAAQ,MAAM,OAAO,KAAK,CAAC;AAAA,MAC/B;AAAA,IACJ,WACSA,MAAK,QAAQ,QAAQ;AAC1B,UAAI,OAAO,KAAK;AAChB,WAAK,MAAM,IAAI;AACf,cAAQ,QAAQA,MAAK,MAAM,IAAI,GAAG,IAAI;AACtC,aAAO,CAAC,KAAK,IAAI,CAAC;AAAA,IACtB,WACSA,MAAK,QAAQ,QAAQ;AAC1B,UAAI,OAAO,KAAK;AAChB,cAAQ,QAAQA,MAAK,MAAM,IAAI,GAAG,IAAI;AACtC,cAAQ,QAAQA,MAAK,MAAM,IAAI,GAAG,IAAI;AACtC,aAAO,CAAC,KAAK,IAAI,CAAC;AAAA,IACtB,WACSA,MAAK,QAAQ,OAAO;AACzB,aAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQA,MAAK,MAAM,IAAI,CAAC;AAAA,IACvD,WACSA,MAAK,QAAQ,SAAS;AAC3B,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK,KAAK;AAC/B,YAAI,OAAO,KAAK;AAChB,gBAAQ,QAAQA,MAAK,MAAM,GAAG,GAAG,IAAI;AACrC,cAAM;AAAA,MACV;AACA,UAAIA,MAAK,OAAO,IAAI;AAChB,gBAAQ,QAAQA,MAAK,MAAM,GAAG,GAAG,GAAG;AAAA,MACxC,OACK;AACD,iBAAS,IAAIA,MAAK,KAAK,IAAIA,MAAK,KAAK,KAAK;AACtC,cAAI,OAAO,KAAK;AAChB,eAAK,KAAK,IAAI;AACd,kBAAQ,QAAQA,MAAK,MAAM,GAAG,GAAG,IAAI;AACrC,gBAAM;AAAA,QACV;AAAA,MACJ;AACA,aAAO,CAAC,KAAK,GAAG,CAAC;AAAA,IACrB,WACSA,MAAK,QAAQ,QAAQ;AAC1B,aAAO,CAAC,KAAK,MAAM,QAAWA,MAAK,KAAK,CAAC;AAAA,IAC7C,OACK;AACD,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACvC;AAAA,EACJ;AACJ;AACA,SAAS,IAAI,GAAG,GAAG;AAAE,SAAO,IAAI;AAAG;AAInC,SAAS,SAASF,MAAK,MAAM;AACzB,MAAI,SAAS,CAAC;AACd,OAAK,IAAI;AACT,SAAO,OAAO,KAAK,GAAG;AACtB,WAAS,KAAKG,OAAM;AAChB,QAAI,QAAQH,KAAIG,KAAI;AACpB,QAAI,MAAM,UAAU,KAAK,CAAC,MAAM,CAAC,EAAE;AAC/B,aAAO,KAAK,MAAM,CAAC,EAAE,EAAE;AAC3B,WAAO,KAAKA,KAAI;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,EAAE,MAAM,GAAG,IAAI,MAAM,CAAC;AAC1B,UAAI,CAAC,QAAQ,OAAO,QAAQ,EAAE,KAAK;AAC/B,aAAK,EAAE;AAAA,IACf;AAAA,EACJ;AACJ;AAIA,SAAS,IAAIH,MAAK;AACd,MAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,SAAO,QAAQ,SAASA,MAAK,CAAC,CAAC;AAC/B,WAAS,QAAQ,QAAQ;AACrB,QAAI,MAAM,CAAC;AACX,WAAO,QAAQ,UAAQ;AACnB,MAAAA,KAAI,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAChC,YAAI,CAAC;AACD;AACJ,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,cAAI,IAAI,CAAC,EAAE,CAAC,KAAK;AACb,kBAAM,IAAI,CAAC,EAAE,CAAC;AACtB,iBAASA,MAAK,EAAE,EAAE,QAAQ,CAAAG,UAAQ;AAC9B,cAAI,CAAC;AACD,gBAAI,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC;AAC7B,cAAI,IAAI,QAAQA,KAAI,KAAK;AACrB,gBAAI,KAAKA,KAAI;AAAA,QACrB,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AACD,QAAI,QAAQ,QAAQ,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,aAAa,OAAO,QAAQH,KAAI,SAAS,CAAC,IAAI,EAAE;AAC5F,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,UAAII,UAAS,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG;AAC/B,YAAM,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,QAAQA,QAAO,KAAK,GAAG,CAAC,KAAK,QAAQA,OAAM,EAAE,CAAC;AAAA,IAC3F;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iBAAiB,OAAO,QAAQ;AACrC,WAAS,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClD,QAAI,QAAQ,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,UAAU,QAAQ,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,UAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAM,KAAK,KAAK,IAAI;AACpB,UAAI,QAAQ,EAAE,KAAK,UAAU,KAAK,iBAAiB;AAC/C,eAAO;AACX,UAAI,KAAK,QAAQ,IAAI,KAAK;AACtB,aAAK,KAAK,IAAI;AAAA,IACtB;AACA,QAAI;AACA,aAAO,IAAI,iCAAiC,MAAM,KAAK,IAAI,IAAI,gFAAgF;AAAA,EACvJ;AACJ;AAMA,SAAS,aAAa,OAAO;AACzB,MAAI,WAAW,uBAAO,OAAO,IAAI;AACjC,WAAS,YAAY,OAAO;AACxB,QAAI,OAAO,MAAM,QAAQ;AACzB,QAAI,CAAC,KAAK;AACN,aAAO;AACX,aAAS,QAAQ,IAAI,KAAK;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,aAAa,OAAO,OAAO;AAChC,MAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,WAAS,QAAQ,OAAO;AACpB,QAAI,QAAQ,SAAS,MAAM,IAAI;AAC/B,QAAI,UAAU,QAAW;AACrB,UAAI,OAAO,MAAM,IAAI;AACrB,UAAI,KAAK;AACL,gBAAQ,KAAK;AAAA;AAEb,cAAM,IAAI,WAAW,qCAAqC,IAAI;AAAA,IACtE;AACA,UAAM,IAAI,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,WAAW,OAAO,QAAQ,MAAM,MAAM;AAC3C,WAASC,SAAQ;AACb,QAAI,EAAEA,SAAQ;AACV,YAAM,IAAI,WAAW,yBAAyBA,KAAI,QAAQ,IAAI,YAAYA,KAAI,EAAE;AACxF,WAASA,SAAQ,OAAO;AACpB,QAAI,OAAO,MAAMA,KAAI;AACrB,QAAI,KAAK;AACL,WAAK,SAAS,OAAOA,KAAI,CAAC;AAAA,EAClC;AACJ;AACA,SAAS,UAAU,UAAU,OAAO;AAChC,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,MAAI;AACA,aAAS,QAAQ;AACb,aAAO,IAAI,IAAI,IAAI,UAAU,UAAU,MAAM,MAAM,IAAI,CAAC;AAChE,SAAO;AACX;AAOA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAIA,MAIA,QAIA,MAAM;AACF,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;AAKZ,SAAK,UAAU;AACf,SAAK,SAAS,KAAK,QAAQ,KAAK,MAAM,MAAM,GAAG,IAAI,CAAC;AACpD,SAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAK,eAAe,aAAa,KAAK,KAAK;AAC3C,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,UAAU,EAAE,KAAK,UAAU,QAAQ;AACxC,SAAK,SAAS,QAAQ;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AAAE,WAAO,CAAC,KAAK;AAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC,IAAI,cAAc;AAAE,WAAO,KAAK,WAAW,KAAK;AAAA,EAAe;AAAA;AAAA;AAAA;AAAA,EAI/D,IAAI,SAAS;AAAE,WAAO,KAAK,gBAAgB,aAAa;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,IAAI,SAAS;AAAE,WAAO,KAAK,UAAU,CAAC,CAAC,KAAK,KAAK;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvD,UAAU,OAAO;AACb,WAAO,KAAK,OAAO,QAAQ,KAAK,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACb,WAAO,KAAK,KAAK,eAAe,KAAK,KAAK,OAAO,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACf,aAAS,KAAK,KAAK;AACf,UAAI,KAAK,MAAM,CAAC,EAAE;AACd,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,OAAO;AACrB,WAAO,QAAQ,SAAS,KAAK,aAAa,WAAW,MAAM,YAAY;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAChB,QAAI,CAAC,SAAS,KAAK;AACf,aAAO,KAAK;AAAA;AAEZ,aAAO,aAAa,KAAK,OAAO,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ,MAAM,SAAS,OAAO;AACjC,QAAI,KAAK;AACL,YAAM,IAAI,MAAM,4CAA4C;AAChE,WAAO,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,GAAG,SAAS,KAAK,OAAO,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,QAAQ,MAAM,SAAS,OAAO;AACxC,cAAU,SAAS,KAAK,OAAO;AAC/B,SAAK,aAAa,OAAO;AACzB,WAAO,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,GAAG,SAAS,KAAK,QAAQ,KAAK,CAAC;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,QAAQ,MAAM,SAAS,OAAO;AACxC,YAAQ,KAAK,aAAa,KAAK;AAC/B,cAAU,SAAS,KAAK,OAAO;AAC/B,QAAI,QAAQ,MAAM;AACd,UAAI,SAAS,KAAK,aAAa,WAAW,OAAO;AACjD,UAAI,CAAC;AACD,eAAO;AACX,gBAAU,OAAO,OAAO,OAAO;AAAA,IACnC;AACA,QAAI,UAAU,KAAK,aAAa,cAAc,OAAO;AACrD,QAAI,QAAQ,WAAW,QAAQ,WAAW,SAAS,OAAO,IAAI;AAC9D,QAAI,CAAC;AACD,aAAO;AACX,WAAO,IAAI,KAAK,MAAM,OAAO,QAAQ,OAAO,KAAK,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,SAAS;AAClB,QAAI,SAAS,KAAK,aAAa,cAAc,OAAO;AACpD,QAAI,CAAC,UAAU,CAAC,OAAO;AACnB,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,QAAQ,YAAY;AACpC,UAAI,CAAC,KAAK,YAAY,QAAQ,MAAM,CAAC,EAAE,KAAK;AACxC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,SAAS;AAClB,QAAI,CAAC,KAAK,aAAa,OAAO;AAC1B,YAAM,IAAI,WAAW,4BAA4B,KAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,eAAW,KAAK,OAAO,OAAO,QAAQ,KAAK,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,UAAU;AACrB,WAAO,KAAK,WAAW,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,IAAI;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO;AACf,QAAI,KAAK,WAAW;AAChB,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,CAAC,KAAK,eAAe,MAAM,CAAC,EAAE,IAAI;AAClC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAChB,QAAI,KAAK,WAAW;AAChB,aAAO;AACX,QAAIV;AACJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,CAAC,KAAK,eAAe,MAAM,CAAC,EAAE,IAAI,GAAG;AACrC,YAAI,CAACA;AACD,UAAAA,QAAO,MAAM,MAAM,GAAG,CAAC;AAAA,MAC/B,WACSA,OAAM;AACX,QAAAA,MAAK,KAAK,MAAM,CAAC,CAAC;AAAA,MACtB;AAAA,IACJ;AACA,WAAO,CAACA,QAAO,QAAQA,MAAK,SAASA,QAAO,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,OAAO,QAAQ;AAC1B,QAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,UAAM,QAAQ,CAAC,MAAM,SAAS,OAAO,IAAI,IAAI,IAAI,UAAS,MAAM,QAAQ,IAAI,CAAC;AAC7E,QAAI,UAAU,OAAO,KAAK,WAAW;AACrC,QAAI,CAAC,OAAO,OAAO;AACf,YAAM,IAAI,WAAW,2CAA2C,UAAU,IAAI;AAClF,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,WAAW,kCAAkC;AAC3D,aAAS,KAAK,OAAO,KAAK;AACtB,YAAM,IAAI,WAAW,+CAA+C;AACxE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,UAAU,UAAU,MAAM;AAC5C,MAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,SAAO,CAAC,UAAU;AACd,QAAI,OAAO,UAAU,OAAO,SAAS,OAAO;AAC5C,QAAI,MAAM,QAAQ,IAAI,IAAI;AACtB,YAAM,IAAI,WAAW,0BAA0B,KAAK,kBAAkB,QAAQ,YAAY,QAAQ,SAAS,IAAI,EAAE;AAAA,EACzH;AACJ;AAEA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,UAAU,UAAU,SAAS;AACrC,SAAK,aAAa,OAAO,UAAU,eAAe,KAAK,SAAS,SAAS;AACzE,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW,OAAO,QAAQ,YAAY,WAAW,aAAa,UAAU,UAAU,QAAQ,QAAQ,IAAI,QAAQ;AAAA,EACvH;AAAA,EACA,IAAI,aAAa;AACb,WAAO,CAAC,KAAK;AAAA,EACjB;AACJ;AAQA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAIA,MAIA,MAIA,QAIA,MAAM;AACF,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAK,WAAW;AAChB,QAAI,WAAW,aAAa,KAAK,KAAK;AACtC,SAAK,WAAW,WAAW,IAAI,KAAK,MAAM,QAAQ,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ,MAAM;AACjB,QAAI,CAAC,SAAS,KAAK;AACf,aAAO,KAAK;AAChB,WAAO,IAAI,KAAK,MAAM,aAAa,KAAK,OAAO,KAAK,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,OAAO,QAAQ;AAC1B,QAAI,SAAS,uBAAO,OAAO,IAAI,GAAG,OAAO;AACzC,UAAM,QAAQ,CAAC,MAAM,SAAS,OAAO,IAAI,IAAI,IAAI,UAAS,MAAM,QAAQ,QAAQ,IAAI,CAAC;AACrF,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK;AACf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,IAAI,CAAC,EAAE,QAAQ,MAAM;AACrB,cAAM,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC;AAC7C;AAAA,MACJ;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,IAAI,CAAC,EAAE,QAAQ;AACf,eAAO,IAAI,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,eAAW,KAAK,OAAO,OAAO,QAAQ,KAAK,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAO;AACZ,WAAO,KAAK,SAAS,QAAQ,KAAK,IAAI;AAAA,EAC1C;AACJ;AAUA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIT,YAAY,MAAM;AAMd,SAAK,uBAAuB;AAM5B,SAAK,SAAS,uBAAO,OAAO,IAAI;AAChC,QAAI,eAAe,KAAK,OAAO,CAAC;AAChC,aAAS,QAAQ;AACb,mBAAa,IAAI,IAAI,KAAK,IAAI;AAClC,iBAAa,QAAQ,aAAW,KAAK,KAAK,KAAK,GAC3C,aAAa,QAAQ,aAAW,KAAK,KAAK,SAAS,CAAC,CAAC,GACrD,KAAK,QAAQ,SAAS,QAAQ,KAAK,KAAK,OAAO,IAAI;AACvD,SAAK,QAAQ,SAAS,QAAQ,KAAK,KAAK,OAAO,IAAI;AACnD,QAAI,mBAAmB,uBAAO,OAAO,IAAI;AACzC,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,QAAQ,KAAK;AACb,cAAM,IAAI,WAAW,OAAO,oCAAoC;AACpE,UAAI,OAAO,KAAK,MAAM,IAAI,GAAG,cAAc,KAAK,KAAK,WAAW,IAAI,WAAW,KAAK,KAAK;AACzF,WAAK,eAAe,iBAAiB,WAAW,MAC3C,iBAAiB,WAAW,IAAI,aAAa,MAAM,aAAa,KAAK,KAAK;AAC/E,WAAK,gBAAgB,KAAK,aAAa;AACvC,UAAI,KAAK,KAAK,sBAAsB;AAChC,YAAI,KAAK;AACL,gBAAM,IAAI,WAAW,kCAAkC;AAC3D,YAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AACxB,gBAAM,IAAI,WAAW,uDAAuD;AAChF,aAAK,uBAAuB;AAAA,MAChC;AACA,WAAK,UAAU,YAAY,MAAM,OAC7B,WAAW,YAAY,MAAM,SAAS,MAAM,GAAG,CAAC,IAC5C,YAAY,MAAM,CAAC,KAAK,gBAAgB,CAAC,IAAI;AAAA,IACzD;AACA,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,OAAO,KAAK,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK;AAC9C,WAAK,WAAW,QAAQ,OAAO,CAAC,IAAI,IAAI,QAAQ,KAAK,CAAC,IAAI,YAAY,MAAM,KAAK,MAAM,GAAG,CAAC;AAAA,IAC/F;AACA,SAAK,eAAe,UAAQ,KAAK,SAAS,MAAM,IAAI;AACpD,SAAK,eAAe,UAAQ,KAAK,SAAS,MAAM,IAAI;AACpD,SAAK,cAAc,KAAK,MAAM,KAAK,KAAK,WAAW,KAAK;AACxD,SAAK,OAAO,YAAY,uBAAO,OAAO,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,MAAM,QAAQ,MAAM,SAAS,OAAO;AACrC,QAAI,OAAO,QAAQ;AACf,aAAO,KAAK,SAAS,IAAI;AAAA,aACpB,EAAE,gBAAgB;AACvB,YAAM,IAAI,WAAW,wBAAwB,IAAI;AAAA,aAC5C,KAAK,UAAU;AACpB,YAAM,IAAI,WAAW,2CAA2C,KAAK,OAAO,GAAG;AACnF,WAAO,KAAK,cAAc,OAAO,SAAS,KAAK;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM,OAAO;AACd,QAAI,OAAO,KAAK,MAAM;AACtB,WAAO,IAAI,SAAS,MAAM,KAAK,cAAc,MAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,MAAM,OAAO;AACd,QAAI,OAAO,QAAQ;AACf,aAAO,KAAK,MAAM,IAAI;AAC1B,WAAO,KAAK,OAAO,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM;AACX,QAAIC,SAAQ,KAAK,MAAM,IAAI;AAC3B,QAAI,CAACA;AACD,YAAM,IAAI,WAAW,wBAAwB,IAAI;AACrD,WAAOA;AAAA,EACX;AACJ;AACA,SAAS,YAAY,QAAQ,OAAO;AAChC,MAAIA,SAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO,MAAM,IAAI,GAAG,KAAK;AACrD,QAAI,MAAM;AACN,MAAAA,OAAM,KAAK,IAAI;AAAA,IACnB,OACK;AACD,eAAS,QAAQ,OAAO,OAAO;AAC3B,YAAIU,QAAO,OAAO,MAAM,IAAI;AAC5B,YAAI,QAAQ,OAAQA,MAAK,KAAK,SAASA,MAAK,KAAK,MAAM,MAAM,GAAG,EAAE,QAAQ,IAAI,IAAI;AAC9E,UAAAV,OAAM,KAAK,KAAKU,KAAI;AAAA,MAC5B;AAAA,IACJ;AACA,QAAI,CAAC;AACD,YAAM,IAAI,YAAY,yBAAyB,MAAM,CAAC,IAAI,GAAG;AAAA,EACrE;AACA,SAAOV;AACX;AAEA,SAAS,UAAU,MAAM;AAAE,SAAO,KAAK,OAAO;AAAM;AACpD,SAAS,YAAY,MAAM;AAAE,SAAO,KAAK,SAAS;AAAM;AAMxD,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,YAIA,QAKA,OAAO;AACH,SAAK,SAAS;AACd,SAAK,QAAQ;AAIb,SAAK,OAAO,CAAC;AAIb,SAAK,SAAS,CAAC;AACf,QAAI,gBAAgB,KAAK,gBAAgB,CAAC;AAC1C,UAAM,QAAQ,UAAQ;AAClB,UAAI,UAAU,IAAI,GAAG;AACjB,aAAK,KAAK,KAAK,IAAI;AAAA,MACvB,WACS,YAAY,IAAI,GAAG;AACxB,YAAI,OAAO,QAAQ,KAAK,KAAK,KAAK,EAAE,CAAC;AACrC,YAAI,cAAc,QAAQ,IAAI,IAAI;AAC9B,wBAAc,KAAK,IAAI;AAC3B,aAAK,OAAO,KAAK,IAAI;AAAA,MACzB;AAAA,IACJ,CAAC;AAED,SAAK,iBAAiB,CAAC,KAAK,KAAK,KAAK,OAAK;AACvC,UAAI,CAAC,aAAa,KAAK,EAAE,GAAG,KAAK,CAAC,EAAE;AAChC,eAAO;AACX,UAAI,OAAO,OAAO,MAAM,EAAE,IAAI;AAC9B,aAAO,KAAK,aAAa,UAAU,IAAI;AAAA,IAC3C,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,KAAK,UAAU,CAAC,GAAG;AACrB,QAAI,UAAU,IAAI,aAAa,MAAM,SAAS,KAAK;AACnD,YAAQ,OAAO,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ,EAAE;AACvD,WAAO,QAAQ,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,KAAK,UAAU,CAAC,GAAG;AAC1B,QAAI,UAAU,IAAI,aAAa,MAAM,SAAS,IAAI;AAClD,YAAQ,OAAO,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ,EAAE;AACvD,WAAO,MAAM,QAAQ,QAAQ,OAAO,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,KAAK,SAAS,OAAO;AAC1B,aAAS,IAAI,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC9E,UAAI,OAAO,KAAK,KAAK,CAAC;AACtB,UAAI,QAAQ,KAAK,KAAK,GAAG,MACpB,KAAK,cAAc,UAAa,IAAI,gBAAgB,KAAK,eACzD,CAAC,KAAK,WAAW,QAAQ,eAAe,KAAK,OAAO,IAAI;AACzD,YAAI,KAAK,UAAU;AACf,cAAI,SAAS,KAAK,SAAS,GAAG;AAC9B,cAAI,WAAW;AACX;AACJ,eAAK,QAAQ,UAAU;AAAA,QAC3B;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM,OAAO,SAAS,OAAO;AACpC,aAAS,IAAI,QAAQ,KAAK,OAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAClF,UAAI,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,KAAK;AACxC,UAAI,MAAM,QAAQ,IAAI,KAAK,KACvB,KAAK,WAAW,CAAC,QAAQ,eAAe,KAAK,OAAO;AAAA;AAAA;AAAA,MAIpD,MAAM,SAAS,KAAK,WACf,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,SAAS,CAAC,KAAK;AAC5E;AACJ,UAAI,KAAK,UAAU;AACf,YAAI,SAAS,KAAK,SAAS,KAAK;AAChC,YAAI,WAAW;AACX;AACJ,aAAK,QAAQ,UAAU;AAAA,MAC3B;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,YAAY,QAAQ;AACvB,QAAI,SAAS,CAAC;AACd,aAAS,OAAO,MAAM;AAClB,UAAI,WAAW,KAAK,YAAY,OAAO,KAAK,KAAK,UAAU,IAAI;AAC/D,aAAO,IAAI,OAAO,QAAQ,KAAK;AAC3B,YAAI,OAAO,OAAO,CAAC,GAAG,eAAe,KAAK,YAAY,OAAO,KAAK,KAAK;AACvE,YAAI,eAAe;AACf;AAAA,MACR;AACA,aAAO,OAAO,GAAG,GAAG,IAAI;AAAA,IAC5B;AACA,aAAS,QAAQ,OAAO,OAAO;AAC3B,UAAI,QAAQ,OAAO,MAAM,IAAI,EAAE,KAAK;AACpC,UAAI;AACA,cAAM,QAAQ,UAAQ;AAClB,iBAAO,OAAO,KAAK,IAAI,CAAC;AACxB,cAAI,EAAE,KAAK,QAAQ,KAAK,UAAU,KAAK;AACnC,iBAAK,OAAO;AAAA,QACpB,CAAC;AAAA,IACT;AACA,aAAS,QAAQ,OAAO,OAAO;AAC3B,UAAI,QAAQ,OAAO,MAAM,IAAI,EAAE,KAAK;AACpC,UAAI;AACA,cAAM,QAAQ,UAAQ;AAClB,iBAAO,OAAO,KAAK,IAAI,CAAC;AACxB,cAAI,EAAE,KAAK,QAAQ,KAAK,UAAU,KAAK;AACnC,iBAAK,OAAO;AAAA,QACpB,CAAC;AAAA,IACT;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,WAAW,QAAQ;AACtB,WAAO,OAAO,OAAO,cAChB,OAAO,OAAO,YAAY,IAAI,WAAU,QAAQ,WAAU,YAAY,MAAM,CAAC;AAAA,EACtF;AACJ;AACA,IAAM,YAAY;AAAA,EACd,SAAS;AAAA,EAAM,SAAS;AAAA,EAAM,OAAO;AAAA,EAAM,YAAY;AAAA,EAAM,QAAQ;AAAA,EACrE,IAAI;AAAA,EAAM,KAAK;AAAA,EAAM,IAAI;AAAA,EAAM,UAAU;AAAA,EAAM,YAAY;AAAA,EAAM,QAAQ;AAAA,EACzE,QAAQ;AAAA,EAAM,MAAM;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EACtE,IAAI;AAAA,EAAM,QAAQ;AAAA,EAAM,QAAQ;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,UAAU;AAAA,EAAM,IAAI;AAAA,EAC9E,QAAQ;AAAA,EAAM,GAAG;AAAA,EAAM,KAAK;AAAA,EAAM,SAAS;AAAA,EAAM,OAAO;AAAA,EAAM,OAAO;AAAA,EAAM,IAAI;AACnF;AACA,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EAAM,UAAU;AAAA,EAAM,QAAQ;AAAA,EAAM,QAAQ;AAAA,EAAM,OAAO;AAAA,EAAM,OAAO;AAChF;AACA,IAAM,WAAW,EAAE,IAAI,MAAM,IAAI,KAAK;AAEtC,IAAM,kBAAkB;AAAxB,IAA2B,uBAAuB;AAAlD,IAAqD,gBAAgB;AACrE,SAAS,aAAa,MAAM,oBAAoB,MAAM;AAClD,MAAI,sBAAsB;AACtB,YAAQ,qBAAqB,kBAAkB,MAC1C,uBAAuB,SAAS,uBAAuB;AAChE,SAAO,QAAQ,KAAK,cAAc,QAAQ,kBAAkB,uBAAuB,OAAO,CAAC;AAC/F;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,MAAM,OAAO,OAAO,OAAO,OAAO,SAAS;AACnD,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAEhB,SAAK,cAAc,KAAK;AACxB,SAAK,QAAQ,UAAU,UAAU,gBAAgB,OAAO,KAAK;AAAA,EACjE;AAAA,EACA,aAAa,MAAM;AACf,QAAI,CAAC,KAAK,OAAO;AACb,UAAI,CAAC,KAAK;AACN,eAAO,CAAC;AACZ,UAAI,OAAO,KAAK,KAAK,aAAa,WAAW,SAAS,KAAK,IAAI,CAAC;AAChE,UAAI,MAAM;AACN,aAAK,QAAQ,KAAK,KAAK,aAAa,cAAc,IAAI;AAAA,MAC1D,OACK;AACD,YAAI,QAAQ,KAAK,KAAK,cAAcW;AACpC,YAAIA,QAAO,MAAM,aAAa,KAAK,IAAI,GAAG;AACtC,eAAK,QAAQ;AACb,iBAAOA;AAAA,QACX,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,MAAM,aAAa,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,OAAO,SAAS;AACZ,QAAI,EAAE,KAAK,UAAU,kBAAkB;AACnC,UAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,GAAG;AAClD,UAAI,QAAQ,KAAK,WAAW,IAAI,oBAAoB,KAAK,KAAK,IAAI,IAAI;AAClE,YAAI,OAAO;AACX,YAAI,KAAK,KAAK,UAAU,EAAE,CAAC,EAAE;AACzB,eAAK,QAAQ,IAAI;AAAA;AAEjB,eAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,MAChH;AAAA,IACJ;AACA,QAAI,UAAU,SAAS,KAAK,KAAK,OAAO;AACxC,QAAI,CAAC,WAAW,KAAK;AACjB,gBAAU,QAAQ,OAAO,KAAK,MAAM,WAAW,SAAS,OAAO,IAAI,CAAC;AACxE,WAAO,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,OAAO,SAAS,KAAK,KAAK,IAAI;AAAA,EAC3E;AAAA,EACA,cAAc,MAAM;AAChB,QAAI,KAAK;AACL,aAAO,KAAK,KAAK;AACrB,QAAI,KAAK,QAAQ;AACb,aAAO,KAAK,QAAQ,CAAC,EAAE;AAC3B,WAAO,KAAK,cAAc,CAAC,UAAU,eAAe,KAAK,WAAW,SAAS,YAAY,CAAC;AAAA,EAC9F;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,YAEA,QAEA,SAAS,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,QAAI,UAAU,QAAQ,SAAS;AAC/B,QAAI,aAAa,aAAa,MAAM,QAAQ,oBAAoB,CAAC,KAAK,SAAS,gBAAgB;AAC/F,QAAI;AACA,mBAAa,IAAI,YAAY,QAAQ,MAAM,QAAQ,OAAO,KAAK,MAAM,MAAM,QAAQ,YAAY,QAAQ,KAAK,cAAc,UAAU;AAAA,aAC/H;AACL,mBAAa,IAAI,YAAY,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU;AAAA;AAE1E,mBAAa,IAAI,YAAY,OAAO,OAAO,aAAa,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU;AACnG,SAAK,QAAQ,CAAC,UAAU;AACxB,SAAK,OAAO,QAAQ;AACpB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,KAAK,MAAM,KAAK,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,OAAO;AACf,QAAI,IAAI,YAAY;AAChB,WAAK,YAAY,KAAK,KAAK;AAAA,aACtB,IAAI,YAAY;AACrB,WAAK,WAAW,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,YAAY,KAAK,OAAO;AACpB,QAAI,QAAQ,IAAI;AAChB,QAAI,MAAM,KAAK,KAAK,aAAc,IAAI,UAAU,uBAAwB,SAClE,KAAK,oBAAoB,IAAI,UAAU,mBAAmB;AAChE,QAAI,eAAe,UACf,IAAI,cAAc,GAAG,KACrB,mBAAmB,KAAK,KAAK,GAAG;AAChC,UAAI,CAAC,YAAY;AACb,gBAAQ,MAAM,QAAQ,qBAAqB,GAAG;AAI9C,YAAI,mBAAmB,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,MAAM,SAAS,GAAG;AACtE,cAAI,aAAa,IAAI,QAAQ,IAAI,QAAQ,SAAS,CAAC;AACnD,cAAI,gBAAgB,IAAI;AACxB,cAAI,CAAC,cACA,iBAAiB,cAAc,YAAY,QAC3C,WAAW,UAAU,mBAAmB,KAAK,WAAW,IAAI;AAC7D,oBAAQ,MAAM,MAAM,CAAC;AAAA,QAC7B;AAAA,MACJ,WACS,eAAe,QAAQ;AAC5B,gBAAQ,MAAM,QAAQ,aAAa,GAAG;AAAA,MAC1C,OACK;AACD,gBAAQ,MAAM,QAAQ,UAAU,IAAI;AAAA,MACxC;AACA,UAAI;AACA,aAAK,WAAW,KAAK,OAAO,OAAO,KAAK,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;AAC5E,WAAK,WAAW,GAAG;AAAA,IACvB,OACK;AACD,WAAK,WAAW,GAAG;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,WAAW,KAAK,OAAO,YAAY;AAC/B,QAAI,UAAU,KAAK,iBAAiB,MAAM,KAAK;AAC/C,QAAI,IAAI,WAAW,SAAS,MAAM,KAAK,IAAI,SAAS,IAAI,MAAM,UAAU;AACpE,WAAK,kBAAkB;AAC3B,QAAI,OAAO,IAAI,SAAS,YAAY,GAAG;AACvC,QAAI,SAAS,eAAe,IAAI,KAAK,KAAK,OAAO;AAC7C,oBAAc,GAAG;AACrB,QAAI,OAAQ,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,aAAa,GAAG,MACjE,SAAS,KAAK,OAAO,SAAS,KAAK,MAAM,UAAU;AACxD,QAAK,KAAI,OAAO,KAAK,SAAS,WAAW,eAAe,IAAI,GAAG;AAC3D,WAAK,WAAW,GAAG;AACnB,WAAK,eAAe,KAAK,KAAK;AAAA,IAClC,WACS,CAAC,QAAQ,KAAK,QAAQ,KAAK,aAAa;AAC7C,UAAI,QAAQ,KAAK;AACb,aAAK,OAAO,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC;AAAA,eAChC,QAAQ,KAAK,KAAK;AACvB,cAAM,KAAK;AACf,UAAI,MAAM,gBAAgB,KAAK;AAC/B,UAAI,UAAU,eAAe,IAAI,GAAG;AAChC,YAAI,IAAI,QAAQ,UAAU,IAAI,QAAQ,CAAC,EAAE,YAAY,KAAK,MAAM;AAC5D,eAAK;AACL,gBAAM,KAAK;AAAA,QACf;AACA,eAAO;AACP,YAAI,CAAC,IAAI;AACL,eAAK,aAAa;AAAA,MAC1B,WACS,CAAC,IAAI,YAAY;AACtB,aAAK,aAAa,KAAK,KAAK;AAC5B,cAAM;AAAA,MACV;AACA,UAAI,aAAa,QAAQ,KAAK,OAAO,QAAQ,KAAK,WAAW,KAAK,KAAK;AACvE,UAAI;AACA,aAAK,OAAO,KAAK,UAAU;AAC/B,UAAI;AACA,aAAK,KAAK,GAAG;AACjB,WAAK,aAAa;AAAA,IACtB,OACK;AACD,UAAI,aAAa,KAAK,WAAW,KAAK,KAAK;AAC3C,UAAI;AACA,aAAK,iBAAiB,KAAK,MAAM,YAAY,KAAK,cAAc,QAAQ,SAAS,MAAS;AAAA,IAClG;AACA,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa,KAAK,OAAO;AACrB,QAAI,IAAI,YAAY,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK;AACvD,WAAK,YAAY,IAAI,cAAc,eAAe,IAAI,GAAG,KAAK;AAAA,EACtE;AAAA;AAAA,EAEA,eAAe,KAAK,OAAO;AAEvB,QAAI,IAAI,YAAY,SAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,IAAI,KAAK;AAC1D,WAAK,UAAU,KAAK,OAAO,OAAO,KAAK,GAAG,GAAG,OAAO,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,KAAK,OAAO;AACnB,QAAI,SAAS,IAAI;AAMjB,QAAI,UAAU,OAAO;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,cAAc,QAAQ,KAAK;AACvD,YAAI,OAAO,KAAK,OAAO,cAAc,CAAC,GAAG,QAAQ,OAAO,iBAAiB,IAAI;AAC7E,YAAI;AACA,mBAAS,QAAQ,YAAa;AAC1B,gBAAI,OAAO,KAAK,OAAO,WAAW,MAAM,OAAO,MAAM,KAAK;AAC1D,gBAAI,CAAC;AACD;AACJ,gBAAI,KAAK;AACL,qBAAO;AACX,gBAAI,KAAK;AACL,sBAAQ,MAAM,OAAO,OAAK,CAAC,KAAK,UAAU,CAAC,CAAC;AAAA;AAE5C,sBAAQ,MAAM,OAAO,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI,EAAE,OAAO,KAAK,KAAK,CAAC;AAC/E,gBAAI,KAAK,cAAc;AACnB,sBAAQ;AAAA;AAER;AAAA,UACR;AAAA,MACR;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,KAAK,MAAM,OAAO,eAAe;AAC9C,QAAI,MAAM;AACV,QAAI,KAAK,MAAM;AACX,iBAAW,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI;AAC7C,UAAI,CAAC,SAAS,QAAQ;AAClB,YAAI,QAAQ,KAAK,MAAM,UAAU,KAAK,SAAS,MAAM,OAAO,KAAK,kBAAkB;AACnF,YAAI,OAAO;AACP,iBAAO;AACP,kBAAQ;AAAA,QACZ;AAAA,MACJ,WACS,CAAC,KAAK,WAAW,SAAS,OAAO,KAAK,KAAK,GAAG,OAAO,IAAI,YAAY,IAAI,GAAG;AACjF,aAAK,aAAa,KAAK,KAAK;AAAA,MAChC;AAAA,IACJ,OACK;AACD,UAAI,WAAW,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI;AACjD,cAAQ,MAAM,OAAO,SAAS,OAAO,KAAK,KAAK,CAAC;AAAA,IACpD;AACA,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY,SAAS,QAAQ;AAC7B,WAAK,WAAW,GAAG;AAAA,IACvB,WACS,eAAe;AACpB,WAAK,WAAW,KAAK,OAAO,aAAa;AAAA,IAC7C,WACS,KAAK,YAAY;AACtB,WAAK,WAAW,GAAG;AACnB,WAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,QAAQ,UAAQ,KAAK,WAAW,MAAM,OAAO,KAAK,CAAC;AAAA,IAChG,OACK;AACD,UAAI,aAAa;AACjB,UAAI,OAAO,KAAK,kBAAkB;AAC9B,qBAAa,IAAI,cAAc,KAAK,cAAc;AAAA,eAC7C,OAAO,KAAK,kBAAkB;AACnC,qBAAa,KAAK,eAAe,GAAG;AAAA,eAC/B,KAAK;AACV,qBAAa,KAAK;AACtB,WAAK,WAAW,KAAK,YAAY,IAAI;AACrC,WAAK,OAAO,YAAY,KAAK;AAC7B,WAAK,WAAW,KAAK,YAAY,KAAK;AAAA,IAC1C;AACA,QAAI,QAAQ,KAAK,KAAK,OAAO;AACzB,WAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,OAAO,YAAY,UAAU;AACxC,QAAI,QAAQ,cAAc;AAC1B,aAAS,MAAM,aAAa,OAAO,WAAW,UAAU,IAAI,OAAO,YAAY,MAAM,YAAY,OAAO,OAAO,OAAO,WAAW,QAAQ,GAAG,OAAO,KAAK,MAAM,IAAI,aAAa,EAAE,OAAO;AACpL,WAAK,YAAY,QAAQ,KAAK;AAC9B,WAAK,OAAO,KAAK,KAAK;AAAA,IAC1B;AACA,SAAK,YAAY,QAAQ,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM,OAAO,UAAU;AAC7B,QAAI,OAAO;AACX,aAAS,QAAQ,KAAK,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS;AAC1D,UAAI,KAAK,KAAK,MAAM,KAAK;AACzB,UAAIX,SAAQ,GAAG,aAAa,IAAI;AAChC,UAAIA,WAAU,CAAC,SAAS,MAAM,SAASA,OAAM,SAAS,UAAU;AAC5D,gBAAQA;AACR,eAAO;AACP,YAAI,CAACA,OAAM;AACP;AAAA,MACR;AACA,UAAI,GAAG,OAAO;AACV,YAAI;AACA;AACJ,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,QAAI,CAAC;AACD,aAAO;AACX,SAAK,KAAK,IAAI;AACd,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,cAAQ,KAAK,WAAW,MAAM,CAAC,GAAG,MAAM,OAAO,KAAK;AACxD,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,WAAW,MAAM,OAAO,UAAU;AAC9B,QAAI,KAAK,YAAY,KAAK,cAAc,CAAC,KAAK,IAAI,MAAM;AACpD,UAAI,QAAQ,KAAK,qBAAqB;AACtC,UAAI;AACA,gBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK;AAAA,IAClD;AACA,QAAI,aAAa,KAAK,UAAU,MAAM,OAAO,QAAQ;AACrD,QAAI,YAAY;AACZ,WAAK,WAAW;AAChB,UAAI,MAAM,KAAK;AACf,UAAI,IAAI;AACJ,YAAI,QAAQ,IAAI,MAAM,UAAU,KAAK,IAAI;AAC7C,UAAI,YAAY,KAAK;AACrB,eAAS,KAAK,WAAW,OAAO,KAAK,KAAK;AACtC,YAAI,IAAI,OAAO,IAAI,KAAK,eAAe,EAAE,IAAI,IAAI,aAAa,EAAE,MAAM,KAAK,IAAI;AAC3E,sBAAY,EAAE,SAAS,SAAS;AACxC,UAAI,QAAQ,KAAK,KAAK,KAAK,SAAS,CAAC;AACrC,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,MAAM,MAAM,OAAO,OAAO,YAAY;AAClC,QAAI,aAAa,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG,OAAO,KAAK;AAChE,QAAI;AACA,mBAAa,KAAK,WAAW,MAAM,OAAO,OAAO,MAAM,UAAU;AACrE,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,WAAW,MAAM,OAAO,OAAO,QAAQ,OAAO,YAAY;AACtD,SAAK,WAAW;AAChB,QAAI,MAAM,KAAK;AACf,QAAI,QAAQ,IAAI,SAAS,IAAI,MAAM,UAAU,IAAI;AACjD,QAAI,UAAU,aAAa,MAAM,YAAY,IAAI,OAAO;AACxD,QAAK,IAAI,UAAU,iBAAkB,IAAI,QAAQ,UAAU;AACvD,iBAAW;AACf,QAAI,aAAa,KAAK;AACtB,YAAQ,MAAM,OAAO,OAAK;AACtB,UAAI,IAAI,OAAO,IAAI,KAAK,eAAe,EAAE,IAAI,IAAI,aAAa,EAAE,MAAM,IAAI,GAAG;AACzE,qBAAa,EAAE,SAAS,UAAU;AAClC,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AACD,SAAK,MAAM,KAAK,IAAI,YAAY,MAAM,OAAO,YAAY,OAAO,MAAM,OAAO,CAAC;AAC9E,SAAK;AACL,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,WAAW,UAAU,OAAO;AACxB,QAAI,IAAI,KAAK,MAAM,SAAS;AAC5B,QAAI,IAAI,KAAK,MAAM;AACf,aAAO,IAAI,KAAK,MAAM;AAClB,aAAK,MAAM,IAAI,CAAC,EAAE,QAAQ,KAAK,KAAK,MAAM,CAAC,EAAE,OAAO,OAAO,CAAC;AAChE,WAAK,MAAM,SAAS,KAAK,OAAO;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,SAAK,OAAO;AACZ,SAAK,WAAW,KAAK,MAAM;AAC3B,WAAO,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAAA,EACvE;AAAA,EACA,KAAK,IAAI;AACL,aAAS,IAAI,KAAK,MAAM,KAAK,GAAG,KAAK;AACjC,UAAI,KAAK,MAAM,CAAC,KAAK,IAAI;AACrB,aAAK,OAAO;AACZ,eAAO;AAAA,MACX,WACS,KAAK,iBAAiB;AAC3B,aAAK,MAAM,CAAC,EAAE,WAAW;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,aAAa;AACb,SAAK,WAAW;AAChB,QAAI,MAAM;AACV,aAAS,IAAI,KAAK,MAAM,KAAK,GAAG,KAAK;AACjC,UAAI,UAAU,KAAK,MAAM,CAAC,EAAE;AAC5B,eAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG;AACrC,eAAO,QAAQ,CAAC,EAAE;AACtB,UAAI;AACA;AAAA,IACR;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,QAAQ,QAAQ;AACxB,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,UAAU,KAAK,KAAK,CAAC,EAAE,UAAU;AACtD,eAAK,KAAK,CAAC,EAAE,MAAM,KAAK;AAAA,MAChC;AAAA,EACR;AAAA,EACA,WAAW,QAAQ;AACf,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,OAAO,YAAY,KAAK,OAAO,SAAS,KAAK,KAAK,CAAC,EAAE,IAAI;AACrF,eAAK,KAAK,CAAC,EAAE,MAAM,KAAK;AAAA,MAChC;AAAA,EACR;AAAA,EACA,WAAW,QAAQ,SAAS,QAAQ;AAChC,QAAI,UAAU,WAAW,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,OAAO,YAAY,KAAK,OAAO,SAAS,KAAK,KAAK,CAAC,EAAE,IAAI,GAAG;AACxF,cAAI,MAAM,QAAQ,wBAAwB,KAAK,KAAK,CAAC,EAAE,IAAI;AAC3D,cAAI,OAAO,SAAS,IAAI;AACpB,iBAAK,KAAK,CAAC,EAAE,MAAM,KAAK;AAAA,QAChC;AAAA,MACJ;AAAA,EACR;AAAA,EACA,WAAW,UAAU;AACjB,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,QAAQ;AACrB,eAAK,KAAK,CAAC,EAAE,MAAM,KAAK,cAAc,SAAS,UAAU,SAAS,KAAK,KAAK,CAAC,EAAE;AAAA,MACvF;AAAA,EACR;AAAA;AAAA,EAEA,eAAe,SAAS;AACpB,QAAI,QAAQ,QAAQ,GAAG,IAAI;AACvB,aAAO,QAAQ,MAAM,UAAU,EAAE,KAAK,KAAK,gBAAgB,IAAI;AACnE,QAAI,QAAQ,QAAQ,MAAM,GAAG;AAC7B,QAAI,SAAS,KAAK,QAAQ;AAC1B,QAAI,UAAU,CAAC,KAAK,WAAW,CAAC,UAAU,OAAO,OAAO,QAAQ,KAAK,MAAM,CAAC,EAAE;AAC9E,QAAI,WAAW,EAAE,SAAS,OAAO,QAAQ,IAAI,MAAM,UAAU,IAAI;AACjE,QAAI,QAAQ,CAAC,GAAG,UAAU;AACtB,aAAO,KAAK,GAAG,KAAK;AAChB,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,QAAQ,IAAI;AACZ,cAAI,KAAK,MAAM,SAAS,KAAK,KAAK;AAC9B;AACJ,iBAAO,SAAS,UAAU;AACtB,gBAAI,MAAM,IAAI,GAAG,KAAK;AAClB,qBAAO;AACf,iBAAO;AAAA,QACX,OACK;AACD,cAAI,OAAO,QAAQ,KAAM,SAAS,KAAK,UAAW,KAAK,MAAM,KAAK,EAAE,OAC9D,UAAU,SAAS,WAAW,OAAO,KAAK,QAAQ,QAAQ,EAAE,OACxD;AACV,cAAI,CAAC,QAAS,KAAK,QAAQ,QAAQ,CAAC,KAAK,UAAU,IAAI;AACnD,mBAAO;AACX;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,MAAM,MAAM,SAAS,GAAG,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,uBAAuB;AACnB,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI;AACA,eAAS,IAAI,SAAS,OAAO,KAAK,GAAG,KAAK;AACtC,YAAI,QAAQ,SAAS,KAAK,CAAC,EAAE,eAAe,SAAS,WAAW,CAAC,CAAC,EAAE;AACpE,YAAI,SAAS,MAAM,eAAe,MAAM;AACpC,iBAAO;AAAA,MACf;AACJ,aAAS,QAAQ,KAAK,OAAO,OAAO,OAAO;AACvC,UAAI,OAAO,KAAK,OAAO,OAAO,MAAM,IAAI;AACxC,UAAI,KAAK,eAAe,KAAK;AACzB,eAAO;AAAA,IACf;AAAA,EACJ;AACJ;AAIA,SAAS,cAAc,KAAK;AACxB,WAAS,QAAQ,IAAI,YAAY,WAAW,MAAM,OAAO,QAAQ,MAAM,aAAa;AAChF,QAAI,OAAO,MAAM,YAAY,IAAI,MAAM,SAAS,YAAY,IAAI;AAChE,QAAI,QAAQ,SAAS,eAAe,IAAI,KAAK,UAAU;AACnD,eAAS,YAAY,KAAK;AAC1B,cAAQ;AAAA,IACZ,WACS,QAAQ,MAAM;AACnB,iBAAW;AAAA,IACf,WACS,MAAM;AACX,iBAAW;AAAA,IACf;AAAA,EACJ;AACJ;AAEA,SAAS,QAAQ,KAAK,UAAU;AAC5B,UAAQ,IAAI,WAAW,IAAI,qBAAqB,IAAI,yBAAyB,IAAI,oBAAoB,KAAK,KAAK,QAAQ;AAC3H;AACA,SAAS,KAAK,KAAK;AACf,MAAID,QAAO,CAAC;AACZ,WAAS,QAAQ;AACb,IAAAA,MAAK,IAAI,IAAI,IAAI,IAAI;AACzB,SAAOA;AACX;AAIA,SAAS,aAAa,UAAU,UAAU;AACtC,MAAI,QAAQ,SAAS,OAAO;AAC5B,WAAS,QAAQ,OAAO;AACpB,QAAI,SAAS,MAAM,IAAI;AACvB,QAAI,CAAC,OAAO,eAAe,QAAQ;AAC/B;AACJ,QAAI,OAAO,CAAC,GAAG,OAAO,CAAC,UAAU;AAC7B,WAAK,KAAK,KAAK;AACf,eAAS,IAAI,GAAG,IAAI,MAAM,WAAW,KAAK;AACtC,YAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAI,QAAQ;AACR,iBAAO;AACX,YAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,KAAK,IAAI;AACnC,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,QAAI,KAAK,OAAO,YAAY;AACxB,aAAO;AAAA,EACf;AACJ;AAMA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUhB,YAIA,OAIA,OAAO;AACH,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU,UAAU,CAAC,GAAG,QAAQ;AAC9C,QAAI,CAAC;AACD,eAAS,IAAI,OAAO,EAAE,uBAAuB;AACjD,QAAI,MAAM,QAAQ,SAAS,CAAC;AAC5B,aAAS,QAAQ,UAAQ;AACrB,UAAI,OAAO,UAAU,KAAK,MAAM,QAAQ;AACpC,YAAI,OAAO,GAAG,WAAW;AACzB,eAAO,OAAO,OAAO,UAAU,WAAW,KAAK,MAAM,QAAQ;AACzD,cAAI,OAAO,KAAK,MAAM,QAAQ;AAC9B,cAAI,CAAC,KAAK,MAAM,KAAK,KAAK,IAAI,GAAG;AAC7B;AACA;AAAA,UACJ;AACA,cAAI,CAAC,KAAK,GAAG,OAAO,IAAI,EAAE,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,aAAa;AACzD;AACJ;AACA;AAAA,QACJ;AACA,eAAO,OAAO,OAAO;AACjB,gBAAM,OAAO,IAAI,EAAE,CAAC;AACxB,eAAO,WAAW,KAAK,MAAM,QAAQ;AACjC,cAAI,MAAM,KAAK,MAAM,UAAU;AAC/B,cAAI,UAAU,KAAK,cAAc,KAAK,KAAK,UAAU,OAAO;AAC5D,cAAI,SAAS;AACT,mBAAO,KAAK,CAAC,KAAK,GAAG,CAAC;AACtB,gBAAI,YAAY,QAAQ,GAAG;AAC3B,kBAAM,QAAQ,cAAc,QAAQ;AAAA,UACxC;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,YAAY,KAAK,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAC1D,CAAC;AACD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,MAAM,SAAS;AAC9B,QAAI,EAAE,KAAK,WAAW,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,MAAM,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG,MAAM,KAAK,KAAK;AACrG,QAAI,YAAY;AACZ,UAAI,KAAK;AACL,cAAM,IAAI,WAAW,8CAA8C;AACvE,WAAK,kBAAkB,KAAK,SAAS,SAAS,UAAU;AAAA,IAC5D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,MAAM,UAAU,CAAC,GAAG;AAC9B,QAAI,MAAM,KAAK,mBAAmB,MAAM,OAAO;AAC/C,aAAS,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAIY,QAAO,KAAK,cAAc,KAAK,MAAM,CAAC,GAAG,KAAK,UAAU,OAAO;AACnE,UAAIA,OAAM;AACN,SAACA,MAAK,cAAcA,MAAK,KAAK,YAAY,GAAG;AAC7C,cAAMA,MAAK;AAAA,MACf;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM,QAAQ,UAAU,CAAC,GAAG;AACtC,QAAI,QAAQ,KAAK,MAAM,KAAK,KAAK,IAAI;AACrC,WAAO,SAAS,WAAW,IAAI,OAAO,GAAG,MAAM,MAAM,MAAM,GAAG,MAAM,KAAK,KAAK;AAAA,EAClF;AAAA,EACA,OAAO,WAAWV,MAAK,WAAW,QAAQ,MAAM,eAAe;AAC3D,WAAO,WAAWA,MAAK,WAAW,OAAO,aAAa;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAAW,QAAQ;AACtB,WAAO,OAAO,OAAO,kBAChB,OAAO,OAAO,gBAAgB,IAAI,eAAc,KAAK,gBAAgB,MAAM,GAAG,KAAK,gBAAgB,MAAM,CAAC;AAAA,EACnH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,gBAAgB,QAAQ;AAC3B,QAAI,SAAS,YAAY,OAAO,KAAK;AACrC,QAAI,CAAC,OAAO;AACR,aAAO,OAAO,UAAQ,KAAK;AAC/B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,gBAAgB,QAAQ;AAC3B,WAAO,YAAY,OAAO,KAAK;AAAA,EACnC;AACJ;AACA,SAAS,YAAY,KAAK;AACtB,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ,KAAK;AAClB,QAAI,QAAQ,IAAI,IAAI,EAAE,KAAK;AAC3B,QAAI;AACA,aAAO,IAAI,IAAI;AAAA,EACvB;AACA,SAAO;AACX;AACA,SAAS,IAAI,SAAS;AAClB,SAAO,QAAQ,YAAY,OAAO;AACtC;AACA,IAAM,2BAA2B,oBAAI,QAAQ;AAC7C,SAAS,qBAAqB,OAAO;AACjC,MAAI,QAAQ,yBAAyB,IAAI,KAAK;AAC9C,MAAI,UAAU;AACV,6BAAyB,IAAI,OAAO,QAAQ,0BAA0B,KAAK,CAAC;AAChF,SAAO;AACX;AACA,SAAS,0BAA0B,OAAO;AACtC,MAAI,SAAS;AACb,WAAS,KAAK,OAAO;AACjB,QAAI,SAAS,OAAO,SAAS,UAAU;AACnC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,YAAI,OAAO,MAAM,CAAC,KAAK,UAAU;AAC7B,cAAI,CAAC;AACD,qBAAS,CAAC;AACd,iBAAO,KAAK,KAAK;AAAA,QACrB,OACK;AACD,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,iBAAK,MAAM,CAAC,CAAC;AAAA,QACrB;AAAA,MACJ,OACK;AACD,iBAAS,QAAQ;AACb,eAAK,MAAM,IAAI,CAAC;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AACA,OAAK,KAAK;AACV,SAAO;AACX;AACA,SAAS,WAAWA,MAAK,WAAW,OAAO,eAAe;AACtD,MAAI,OAAO,aAAa;AACpB,WAAO,EAAE,KAAKA,KAAI,eAAe,SAAS,EAAE;AAChD,MAAI,UAAU,YAAY;AACtB,WAAO,EAAE,KAAK,UAAU;AAC5B,MAAI,UAAU,OAAO,UAAU,IAAI,YAAY;AAC3C,WAAO;AACX,MAAI,UAAU,UAAU,CAAC,GAAG;AAC5B,MAAI,OAAO,WAAW;AAClB,UAAM,IAAI,WAAW,oCAAoC;AAC7D,MAAI,kBAAkB,aAAa,qBAAqB,aAAa,MACjE,WAAW,QAAQ,SAAS,IAAI;AAChC,UAAM,IAAI,WAAW,8GAA8G;AACvI,MAAI,QAAQ,QAAQ,QAAQ,GAAG;AAC/B,MAAI,QAAQ,GAAG;AACX,YAAQ,QAAQ,MAAM,GAAG,KAAK;AAC9B,cAAU,QAAQ,MAAM,QAAQ,CAAC;AAAA,EACrC;AACA,MAAI;AACJ,MAAI,MAAO,QAAQA,KAAI,gBAAgB,OAAO,OAAO,IAAIA,KAAI,cAAc,OAAO;AAClF,MAAI,QAAQ,UAAU,CAAC,GAAG,QAAQ;AAClC,MAAI,SAAS,OAAO,SAAS,YAAY,MAAM,YAAY,QAAQ,CAAC,MAAM,QAAQ,KAAK,GAAG;AACtF,YAAQ;AACR,aAAS,QAAQ;AACb,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,YAAIW,SAAQ,KAAK,QAAQ,GAAG;AAC5B,YAAIA,SAAQ;AACR,cAAI,eAAe,KAAK,MAAM,GAAGA,MAAK,GAAG,KAAK,MAAMA,SAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,iBACtE,QAAQ,WAAW,IAAI;AAC5B,cAAI,MAAM,UAAU,MAAM,IAAI;AAAA;AAE9B,cAAI,aAAa,MAAM,MAAM,IAAI,CAAC;AAAA,MAC1C;AAAA,EACR;AACA,WAAS,IAAI,OAAO,IAAI,UAAU,QAAQ,KAAK;AAC3C,QAAI,QAAQ,UAAU,CAAC;AACvB,QAAI,UAAU,GAAG;AACb,UAAI,IAAI,UAAU,SAAS,KAAK,IAAI;AAChC,cAAM,IAAI,WAAW,wDAAwD;AACjF,aAAO,EAAE,KAAK,YAAY,IAAI;AAAA,IAClC,OACK;AACD,UAAI,EAAE,KAAK,OAAO,YAAY,aAAa,IAAI,WAAWX,MAAK,OAAO,OAAO,aAAa;AAC1F,UAAI,YAAY,KAAK;AACrB,UAAI,cAAc;AACd,YAAI;AACA,gBAAM,IAAI,WAAW,wBAAwB;AACjD,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,KAAK,WAAW;AAC7B;;;AC/2GA,IAAM,UAAU;AAChB,IAAM,WAAW,KAAK,IAAI,GAAG,EAAE;AAC/B,SAAS,YAAY,OAAO,QAAQ;AAAE,SAAO,QAAQ,SAAS;AAAU;AACxE,SAAS,aAAa,OAAO;AAAE,SAAO,QAAQ;AAAS;AACvD,SAAS,cAAc,OAAO;AAAE,UAAQ,SAAS,QAAQ,YAAY;AAAU;AAC/E,IAAM,aAAa;AAAnB,IAAsB,YAAY;AAAlC,IAAqC,aAAa;AAAlD,IAAqD,WAAW;AAKhE,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAIA,KAIA,SAIA,SAAS;AACL,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AAAE,YAAQ,KAAK,UAAU,YAAY;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItD,IAAI,gBAAgB;AAAE,YAAQ,KAAK,WAAW,aAAa,eAAe;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI7E,IAAI,eAAe;AAAE,YAAQ,KAAK,WAAW,YAAY,eAAe;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3E,IAAI,gBAAgB;AAAE,YAAQ,KAAK,UAAU,cAAc;AAAA,EAAG;AAClE;AAOA,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,YAIA,QAIA,WAAW,OAAO;AACd,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,QAAI,CAAC,OAAO,UAAU,SAAQ;AAC1B,aAAO,SAAQ;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,OAAO;AACX,QAAI,OAAO,GAAG,QAAQ,aAAa,KAAK;AACxC,QAAI,CAAC,KAAK;AACN,eAAS,IAAI,GAAG,IAAI,OAAO;AACvB,gBAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC;AAC9D,WAAO,KAAK,OAAO,QAAQ,CAAC,IAAI,OAAO,cAAc,KAAK;AAAA,EAC9D;AAAA,EACA,UAAU,KAAK,QAAQ,GAAG;AAAE,WAAO,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAAG;AAAA,EACjE,IAAI,KAAK,QAAQ,GAAG;AAAE,WAAO,KAAK,KAAK,KAAK,OAAO,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI1D,KAAK,KAAK,OAAO,QAAQ;AACrB,QAAI,OAAO,GAAG,WAAW,KAAK,WAAW,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AAC/E,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC5C,UAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO;AACrD,UAAI,QAAQ;AACR;AACJ,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,MAAM,QAAQ;AAC5F,UAAI,OAAO,KAAK;AACZ,YAAI,OAAO,CAAC,UAAU,QAAQ,OAAO,QAAQ,KAAK,OAAO,MAAM,IAAI;AACnE,YAAI,SAAS,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAC5C,YAAI;AACA,iBAAO;AACX,YAAI,UAAU,QAAQ,QAAQ,IAAI,QAAQ,OAAO,OAAO,YAAY,IAAI,GAAG,MAAM,KAAK;AACtF,YAAI,MAAM,OAAO,QAAQ,YAAY,OAAO,MAAM,aAAa;AAC/D,YAAI,QAAQ,IAAI,OAAO,QAAQ,OAAO;AAClC,iBAAO;AACX,eAAO,IAAI,UAAU,QAAQ,KAAK,OAAO;AAAA,MAC7C;AACA,cAAQ,UAAU;AAAA,IACtB;AACA,WAAO,SAAS,MAAM,OAAO,IAAI,UAAU,MAAM,MAAM,GAAG,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK,SAAS;AAClB,QAAI,OAAO,GAAG,QAAQ,aAAa,OAAO;AAC1C,QAAI,WAAW,KAAK,WAAW,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AACrE,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC5C,UAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO;AACrD,UAAI,QAAQ;AACR;AACJ,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,MAAM,QAAQ;AACvD,UAAI,OAAO,OAAO,KAAK,QAAQ;AAC3B,eAAO;AACX,cAAQ,KAAK,OAAO,IAAI,QAAQ,IAAI;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,GAAG;AACP,QAAI,WAAW,KAAK,WAAW,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AACrE,aAAS,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AACtD,UAAI,QAAQ,KAAK,OAAO,CAAC,GAAG,WAAW,SAAS,KAAK,WAAW,OAAO,IAAI,WAAW,SAAS,KAAK,WAAW,IAAI;AACnH,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,UAAU,KAAK,OAAO,IAAI,QAAQ;AAC3E,QAAE,UAAU,WAAW,SAAS,UAAU,WAAW,OAAO;AAC5D,cAAQ,UAAU;AAAA,IACtB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACL,WAAO,IAAI,SAAQ,KAAK,QAAQ,CAAC,KAAK,QAAQ;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,YAAQ,KAAK,WAAW,MAAM,MAAM,KAAK,UAAU,KAAK,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,GAAG;AACb,WAAO,KAAK,IAAI,SAAQ,QAAQ,IAAI,SAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAC9E;AACJ;AAIA,QAAQ,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAS9B,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA,EAIV,YAAY,MAIZ,QAKA,OAAO,GAIP,KAAK,OAAO,KAAK,SAAS,GAAG;AACzB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ,QAAQ,CAAC;AACtB,SAAK,UAAU,EAAE,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AAAE,WAAO,KAAK;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA,EAIhC,MAAM,OAAO,GAAG,KAAK,KAAK,KAAK,QAAQ;AACnC,WAAO,IAAI,SAAQ,KAAK,OAAO,KAAK,QAAQ,MAAM,EAAE;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK,SAAS;AACpB,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B,WAAK,SAAS,KAAK,UAAU,KAAK,OAAO,MAAM;AAC/C,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,KAAK,KAAK,MAAM,KAAK,GAAG;AAC7B,QAAI,WAAW;AACX,WAAK,UAAU,KAAK,MAAM,SAAS,GAAG,OAAO;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACnB,aAAS,IAAI,GAAG,YAAY,KAAK,MAAM,QAAQ,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC1E,UAAI,OAAO,QAAQ,UAAU,CAAC;AAC9B,WAAK,UAAU,QAAQ,MAAM,CAAC,GAAG,QAAQ,QAAQ,OAAO,IAAI,YAAY,OAAO,MAAS;AAAA,IAC5F;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG;AACT,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ;AACpC,YAAI,KAAK,OAAO,CAAC,KAAK;AAClB,iBAAO,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK,EAAE;AAAA;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,GAAG,GAAG;AACZ,QAAI,CAAC,KAAK;AACN,WAAK,SAAS,CAAC;AACnB,SAAK,OAAO,KAAK,GAAG,CAAC;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,SAAS;AAC3B,aAAS,IAAI,QAAQ,KAAK,SAAS,GAAG,YAAY,KAAK,MAAM,SAAS,QAAQ,MAAM,QAAQ,KAAK,GAAG,KAAK;AACrG,UAAI,OAAO,QAAQ,UAAU,CAAC;AAC9B,WAAK,UAAU,QAAQ,MAAM,CAAC,EAAE,OAAO,GAAG,QAAQ,QAAQ,OAAO,IAAI,YAAY,OAAO,IAAI,MAAS;AAAA,IACzG;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,UAAU,IAAI;AAClB,YAAQ,sBAAsB,IAAI;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK,QAAQ,GAAG;AAChB,QAAI,KAAK;AACL,aAAO,KAAK,KAAK,KAAK,OAAO,IAAI;AACrC,aAAS,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AACjC,YAAM,KAAK,MAAM,CAAC,EAAE,IAAI,KAAK,KAAK;AACtC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,KAAK,QAAQ,GAAG;AAAE,WAAO,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIjE,KAAK,KAAK,OAAO,QAAQ;AACrB,QAAI,UAAU;AACd,aAAS,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACtC,UAAI,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI,UAAU,KAAK,KAAK;AAC1D,UAAI,OAAO,WAAW,MAAM;AACxB,YAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,YAAI,QAAQ,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI;AAC5C,cAAI;AACJ,gBAAM,KAAK,MAAM,IAAI,EAAE,QAAQ,OAAO,OAAO;AAC7C;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW,OAAO;AAClB,YAAM,OAAO;AAAA,IACjB;AACA,WAAO,SAAS,MAAM,IAAI,UAAU,KAAK,SAAS,IAAI;AAAA,EAC1D;AACJ;AAEA,IAAM,YAAY,uBAAO,OAAO,IAAI;AAYpC,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,SAAS;AAAE,WAAO,QAAQ;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,MAAM,OAAO;AAAE,WAAO;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC,QAAQ,CAAC,KAAK;AACf,YAAM,IAAI,WAAW,iCAAiC;AAC1D,QAAI,OAAO,UAAU,KAAK,QAAQ;AAClC,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,gBAAgB,KAAK,QAAQ,UAAU;AAChE,WAAO,KAAK,SAAS,QAAQ,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,IAAI,WAAW;AACzB,QAAI,MAAM;AACN,YAAM,IAAI,WAAW,mCAAmC,EAAE;AAC9D,cAAU,EAAE,IAAI;AAChB,cAAU,UAAU,SAAS;AAC7B,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA,EAIb,YAIAY,MAIA,QAAQ;AACJ,SAAK,MAAMA;AACX,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,GAAGA,MAAK;AAAE,WAAO,IAAI,YAAWA,MAAK,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInD,OAAO,KAAK,SAAS;AAAE,WAAO,IAAI,YAAW,MAAM,OAAO;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7D,OAAO,YAAYA,MAAK,MAAM,IAAI,OAAO;AACrC,QAAI;AACA,aAAO,YAAW,GAAGA,KAAI,QAAQ,MAAM,IAAI,KAAK,CAAC;AAAA,IACrD,SACO,GAAG;AACN,UAAI,aAAa;AACb,eAAO,YAAW,KAAK,EAAE,OAAO;AACpC,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;AAEA,SAAS,YAAY,UAAU,GAAG,QAAQ;AACtC,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,SAAS,YAAY,KAAK;AAC1C,QAAI,QAAQ,SAAS,MAAM,CAAC;AAC5B,QAAI,MAAM,QAAQ;AACd,cAAQ,MAAM,KAAK,YAAY,MAAM,SAAS,GAAG,KAAK,CAAC;AAC3D,QAAI,MAAM;AACN,cAAQ,EAAE,OAAO,QAAQ,CAAC;AAC9B,WAAO,KAAK,KAAK;AAAA,EACrB;AACA,SAAO,SAAS,UAAU,MAAM;AACpC;AAIA,IAAM,cAAN,MAAM,qBAAoB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI3B,YAIA,MAIA,IAIA,MAAM;AACF,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,WAAWA,KAAI,MAAM,KAAK,MAAM,KAAK,EAAE,GAAG,QAAQA,KAAI,QAAQ,KAAK,IAAI;AAC3E,QAAI,SAAS,MAAM,KAAK,MAAM,YAAY,KAAK,EAAE,CAAC;AAClD,QAAI,QAAQ,IAAI,MAAM,YAAY,SAAS,SAAS,CAAC,MAAMC,YAAW;AAClE,UAAI,CAAC,KAAK,UAAU,CAACA,QAAO,KAAK,eAAe,KAAK,KAAK,IAAI;AAC1D,eAAO;AACX,aAAO,KAAK,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC;AAAA,IACnD,GAAG,MAAM,GAAG,SAAS,WAAW,SAAS,OAAO;AAChD,WAAO,WAAW,YAAYD,MAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,eAAe,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3D;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,KAAK,WAAW,GAAG,WAAW,KAAK,OAAO,GAAG;AAC7C,aAAO;AACX,WAAO,IAAI,aAAY,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI;AAAA,EACtD;AAAA,EACA,MAAM,OAAO;AACT,QAAI,iBAAiB,gBACjB,MAAM,KAAK,GAAG,KAAK,IAAI,KACvB,KAAK,QAAQ,MAAM,MAAM,KAAK,MAAM,MAAM;AAC1C,aAAO,IAAI,aAAY,KAAK,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,GAAG,KAAK,IAAI;AAClG,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO;AAAA,MAAE,UAAU;AAAA,MAAW,MAAM,KAAK,KAAK,OAAO;AAAA,MACjD,MAAM,KAAK;AAAA,MAAM,IAAI,KAAK;AAAA,IAAG;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM;AAClD,YAAM,IAAI,WAAW,wCAAwC;AACjE,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,IAAI,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EAC7E;AACJ;AACA,KAAK,OAAO,WAAW,WAAW;AAIlC,IAAM,iBAAN,MAAM,wBAAuB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI9B,YAIA,MAIA,IAIA,MAAM;AACF,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,WAAWA,KAAI,MAAM,KAAK,MAAM,KAAK,EAAE;AAC3C,QAAI,QAAQ,IAAI,MAAM,YAAY,SAAS,SAAS,UAAQ;AACxD,aAAO,KAAK,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK,CAAC;AAAA,IACxD,GAAGA,IAAG,GAAG,SAAS,WAAW,SAAS,OAAO;AAC7C,WAAO,WAAW,YAAYA,MAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,YAAY,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,EACxD;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,KAAK,WAAW,GAAG,WAAW,KAAK,OAAO,GAAG;AAC7C,aAAO;AACX,WAAO,IAAI,gBAAe,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI;AAAA,EACzD;AAAA,EACA,MAAM,OAAO;AACT,QAAI,iBAAiB,mBACjB,MAAM,KAAK,GAAG,KAAK,IAAI,KACvB,KAAK,QAAQ,MAAM,MAAM,KAAK,MAAM,MAAM;AAC1C,aAAO,IAAI,gBAAe,KAAK,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,GAAG,KAAK,IAAI;AACrG,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO;AAAA,MAAE,UAAU;AAAA,MAAc,MAAM,KAAK,KAAK,OAAO;AAAA,MACpD,MAAM,KAAK;AAAA,MAAM,IAAI,KAAK;AAAA,IAAG;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM;AAClD,YAAM,IAAI,WAAW,2CAA2C;AACpE,WAAO,IAAI,gBAAe,KAAK,MAAM,KAAK,IAAI,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EAChF;AACJ;AACA,KAAK,OAAO,cAAc,cAAc;AAIxC,IAAM,kBAAN,MAAM,yBAAwB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI/B,YAIA,KAIA,MAAM;AACF,UAAM;AACN,SAAK,MAAM;AACX,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,OAAOA,KAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,iCAAiC;AAC5D,QAAI,UAAU,KAAK,KAAK,OAAO,KAAK,OAAO,MAAM,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC;AAC/E,WAAO,WAAW,YAAYA,MAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,OAAOA,MAAK;AACR,QAAI,OAAOA,KAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,MAAM;AACN,UAAI,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK;AAC1C,UAAI,OAAO,UAAU,KAAK,MAAM,QAAQ;AACpC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AACnC,cAAI,CAAC,KAAK,MAAM,CAAC,EAAE,QAAQ,MAAM;AAC7B,mBAAO,IAAI,iBAAgB,KAAK,KAAK,KAAK,MAAM,CAAC,CAAC;AAC1D,eAAO,IAAI,iBAAgB,KAAK,KAAK,KAAK,IAAI;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,IAAI,mBAAmB,KAAK,KAAK,KAAK,IAAI;AAAA,EACrD;AAAA,EACA,IAAI,SAAS;AACT,QAAI,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC;AACvC,WAAO,IAAI,eAAe,OAAO,IAAI,iBAAgB,IAAI,KAAK,KAAK,IAAI;AAAA,EAC3E;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,eAAe,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO,EAAE;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,4CAA4C;AACrE,WAAO,IAAI,iBAAgB,KAAK,KAAK,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EACvE;AACJ;AACA,KAAK,OAAO,eAAe,eAAe;AAI1C,IAAM,qBAAN,MAAM,4BAA2B,KAAK;AAAA;AAAA;AAAA;AAAA,EAIlC,YAIA,KAIA,MAAM;AACF,UAAM;AACN,SAAK,MAAM;AACX,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,OAAOA,KAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,iCAAiC;AAC5D,QAAI,UAAU,KAAK,KAAK,OAAO,KAAK,OAAO,MAAM,KAAK,KAAK,cAAc,KAAK,KAAK,CAAC;AACpF,WAAO,WAAW,YAAYA,MAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,OAAOA,MAAK;AACR,QAAI,OAAOA,KAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,KAAK,KAAK;AACtC,aAAO;AACX,WAAO,IAAI,gBAAgB,KAAK,KAAK,KAAK,IAAI;AAAA,EAClD;AAAA,EACA,IAAI,SAAS;AACT,QAAI,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC;AACvC,WAAO,IAAI,eAAe,OAAO,IAAI,oBAAmB,IAAI,KAAK,KAAK,IAAI;AAAA,EAC9E;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,kBAAkB,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO,EAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,+CAA+C;AACxE,WAAO,IAAI,oBAAmB,KAAK,KAAK,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EAC1E;AACJ;AACA,KAAK,OAAO,kBAAkB,kBAAkB;AAKhD,IAAM,cAAN,MAAM,qBAAoB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU3B,YAIA,MAIA,IAIA,OAIA,YAAY,OAAO;AACf,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,KAAK,aAAa,eAAeA,MAAK,KAAK,MAAM,KAAK,EAAE;AACxD,aAAO,WAAW,KAAK,2CAA2C;AACtE,WAAO,WAAW,YAAYA,MAAK,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK;AAAA,EACrE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,QAAQ,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;AAAA,EACxE;AAAA,EACA,OAAOA,MAAK;AACR,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAMA,KAAI,MAAM,KAAK,MAAM,KAAK,EAAE,CAAC;AAAA,EAChG;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,KAAK,iBAAiB,GAAG;AACzB,aAAO;AACX,WAAO,IAAI,aAAY,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,GAAG,GAAG,KAAK,OAAO,KAAK,SAAS;AAAA,EAC3F;AAAA,EACA,MAAM,OAAO;AACT,QAAI,EAAE,iBAAiB,iBAAgB,MAAM,aAAa,KAAK;AAC3D,aAAO;AACX,QAAI,KAAK,OAAO,KAAK,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,MAAM,WAAW,CAAC,MAAM,MAAM,WAAW;AAC5F,UAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,MAAM,QAAQ,IAAI,MAAM,QACtD,IAAI,MAAM,KAAK,MAAM,QAAQ,OAAO,MAAM,MAAM,OAAO,GAAG,KAAK,MAAM,WAAW,MAAM,MAAM,OAAO;AACzG,aAAO,IAAI,aAAY,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,SAAS;AAAA,IAC9F,WACS,MAAM,MAAM,KAAK,QAAQ,CAAC,KAAK,MAAM,aAAa,CAAC,MAAM,MAAM,SAAS;AAC7E,UAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,MAAM,QAAQ,IAAI,MAAM,QACtD,IAAI,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,MAAM,OAAO,GAAG,MAAM,MAAM,WAAW,KAAK,MAAM,OAAO;AACzG,aAAO,IAAI,aAAY,MAAM,MAAM,KAAK,IAAI,OAAO,KAAK,SAAS;AAAA,IACrE,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,OAAO,EAAE,UAAU,WAAW,MAAM,KAAK,MAAM,IAAI,KAAK,GAAG;AAC/D,QAAI,KAAK,MAAM;AACX,WAAK,QAAQ,KAAK,MAAM,OAAO;AACnC,QAAI,KAAK;AACL,WAAK,YAAY;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM;AAClD,YAAM,IAAI,WAAW,wCAAwC;AACjE,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,IAAI,MAAM,SAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,CAAC,KAAK,SAAS;AAAA,EACnG;AACJ;AACA,KAAK,OAAO,WAAW,WAAW;AAMlC,IAAM,oBAAN,MAAM,2BAA0B,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjC,YAIA,MAIA,IAIA,SAIA,OAIA,OAKA,QAIA,YAAY,OAAO;AACf,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,KAAK,cAAc,eAAeA,MAAK,KAAK,MAAM,KAAK,OAAO,KAC9D,eAAeA,MAAK,KAAK,OAAO,KAAK,EAAE;AACvC,aAAO,WAAW,KAAK,+CAA+C;AAC1E,QAAI,MAAMA,KAAI,MAAM,KAAK,SAAS,KAAK,KAAK;AAC5C,QAAI,IAAI,aAAa,IAAI;AACrB,aAAO,WAAW,KAAK,yBAAyB;AACpD,QAAI,WAAW,KAAK,MAAM,SAAS,KAAK,QAAQ,IAAI,OAAO;AAC3D,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,6BAA6B;AACxD,WAAO,WAAW,YAAYA,MAAK,KAAK,MAAM,KAAK,IAAI,QAAQ;AAAA,EACnE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,QAAQ;AAAA,MAAC,KAAK;AAAA,MAAM,KAAK,UAAU,KAAK;AAAA,MAAM,KAAK;AAAA,MAC1D,KAAK;AAAA,MAAO,KAAK,KAAK,KAAK;AAAA,MAAO,KAAK,MAAM,OAAO,KAAK;AAAA,IAAM,CAAC;AAAA,EACxE;AAAA,EACA,OAAOA,MAAK;AACR,QAAI,MAAM,KAAK,QAAQ,KAAK;AAC5B,WAAO,IAAI,mBAAkB,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,SAAS,KAAKA,KAAI,MAAM,KAAK,MAAM,KAAK,EAAE,EAAE,cAAc,KAAK,UAAU,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,EAC9Q;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM,QAAQ,IAAI,KAAK,SAAS,EAAE;AACjF,QAAI,QAAQ,KAAK,MAAM,KAAK,QAAQ,GAAG,MAAM,QAAQ,IAAI,KAAK,OAAO,CAAC;AACtE,QAAK,KAAK,iBAAiB,GAAG,iBAAkB,UAAU,KAAK,OAAO,QAAQ,GAAG;AAC7E,aAAO;AACX,WAAO,IAAI,mBAAkB,KAAK,KAAK,GAAG,KAAK,SAAS,OAAO,KAAK,OAAO,KAAK,QAAQ,KAAK,SAAS;AAAA,EAC1G;AAAA,EACA,SAAS;AACL,QAAI,OAAO;AAAA,MAAE,UAAU;AAAA,MAAiB,MAAM,KAAK;AAAA,MAAM,IAAI,KAAK;AAAA,MAC9D,SAAS,KAAK;AAAA,MAAS,OAAO,KAAK;AAAA,MAAO,QAAQ,KAAK;AAAA,IAAO;AAClE,QAAI,KAAK,MAAM;AACX,WAAK,QAAQ,KAAK,MAAM,OAAO;AACnC,QAAI,KAAK;AACL,WAAK,YAAY;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM,YAClD,OAAO,KAAK,WAAW,YAAY,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,UAAU;AAC1F,YAAM,IAAI,WAAW,8CAA8C;AACvE,WAAO,IAAI,mBAAkB,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,MAAM,SAAS,QAAQ,KAAK,KAAK,GAAG,KAAK,QAAQ,CAAC,CAAC,KAAK,SAAS;AAAA,EAChJ;AACJ;AACA,KAAK,OAAO,iBAAiB,iBAAiB;AAC9C,SAAS,eAAeA,MAAK,MAAM,IAAI;AACnC,MAAI,QAAQA,KAAI,QAAQ,IAAI,GAAG,OAAO,KAAK,MAAM,QAAQ,MAAM;AAC/D,SAAO,OAAO,KAAK,QAAQ,KAAK,MAAM,WAAW,KAAK,KAAK,MAAM,KAAK,KAAK,EAAE,YAAY;AACrF;AACA;AAAA,EACJ;AACA,MAAI,OAAO,GAAG;AACV,QAAI,OAAO,MAAM,KAAK,KAAK,EAAE,WAAW,MAAM,WAAW,KAAK,CAAC;AAC/D,WAAO,OAAO,GAAG;AACb,UAAI,CAAC,QAAQ,KAAK;AACd,eAAO;AACX,aAAO,KAAK;AACZ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,QAAQ,IAAI,MAAM,IAAI,MAAM;AACjC,MAAI,UAAU,CAAC,GAAG,QAAQ,CAAC;AAC3B,MAAI,UAAU;AACd,KAAG,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,KAAK,WAAW;AACjD,QAAI,CAAC,KAAK;AACN;AACJ,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,KAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,eAAe,KAAK,IAAI,GAAG;AAC/D,UAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AACvE,UAAI,SAAS,KAAK,SAAS,KAAK;AAChC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,MAAM,GAAG;AAC3B,cAAI,YAAY,SAAS,MAAM,SAAS,SAAS,KAAK,GAAG,MAAM,CAAC,CAAC;AAC7D,qBAAS,KAAK;AAAA;AAEd,oBAAQ,KAAK,WAAW,IAAI,eAAe,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,QACxE;AAAA,MACJ;AACA,UAAI,UAAU,OAAO,MAAM;AACvB,eAAO,KAAK;AAAA;AAEZ,cAAM,KAAK,SAAS,IAAI,YAAY,OAAO,KAAK,IAAI,CAAC;AAAA,IAC7D;AAAA,EACJ,CAAC;AACD,UAAQ,QAAQ,OAAK,GAAG,KAAK,CAAC,CAAC;AAC/B,QAAM,QAAQ,OAAK,GAAG,KAAK,CAAC,CAAC;AACjC;AACA,SAAS,WAAW,IAAI,MAAM,IAAI,MAAM;AACpC,MAAI,UAAU,CAAC,GAAG,OAAO;AACzB,KAAG,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACzC,QAAI,CAAC,KAAK;AACN;AACJ;AACA,QAAI,WAAW;AACf,QAAI,gBAAgB,UAAU;AAC1B,UAAI,MAAM,KAAK,OAAOE;AACtB,aAAOA,SAAQ,KAAK,QAAQ,GAAG,GAAG;AAC9B,SAAC,aAAa,WAAW,CAAC,IAAI,KAAKA,MAAK;AACxC,cAAMA,OAAM,cAAc,GAAG;AAAA,MACjC;AAAA,IACJ,WACS,MAAM;AACX,UAAI,KAAK,QAAQ,KAAK,KAAK;AACvB,mBAAW,CAAC,IAAI;AAAA,IACxB,OACK;AACD,iBAAW,KAAK;AAAA,IACpB;AACA,QAAI,YAAY,SAAS,QAAQ;AAC7B,UAAI,MAAM,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAC1C,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAI,QAAQ,SAAS,CAAC,GAAGA;AACzB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,cAAI,IAAI,QAAQ,CAAC;AACjB,cAAI,EAAE,QAAQ,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,EAAE,KAAK;AAC/C,YAAAA,SAAQ;AAAA,QAChB;AACA,YAAIA,QAAO;AACP,UAAAA,OAAM,KAAK;AACX,UAAAA,OAAM,OAAO;AAAA,QACjB,OACK;AACD,kBAAQ,KAAK,EAAE,OAAO,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,UAAQ,QAAQ,OAAK,GAAG,KAAK,IAAI,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3E;AACA,SAAS,kBAAkB,IAAI,KAAK,YAAY,QAAQ,WAAW,cAAc,gBAAgB,MAAM;AACnG,MAAI,OAAO,GAAG,IAAI,OAAO,GAAG;AAC5B,MAAI,YAAY,CAAC,GAAG,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK;AACtC,QAAI,QAAQ,KAAK,MAAM,CAAC,GAAG,MAAM,MAAM,MAAM;AAC7C,QAAI,UAAU,MAAM,UAAU,MAAM,IAAI;AACxC,QAAI,CAAC,SAAS;AACV,gBAAU,KAAK,IAAI,YAAY,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,IACzD,OACK;AACD,cAAQ;AACR,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ;AACpC,YAAI,CAAC,WAAW,eAAe,MAAM,MAAM,CAAC,EAAE,IAAI;AAC9C,aAAG,KAAK,IAAI,eAAe,KAAK,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAC5D,UAAI,iBAAiB,MAAM,UAAU,WAAW,cAAc,OAAO;AACjE,YAAI,GAAG,UAAU,aAAa;AAC9B,eAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG;AACjC,cAAI,CAAC;AACD,oBAAQ,IAAI,MAAM,SAAS,KAAK,WAAW,OAAO,KAAK,KAAK,WAAW,aAAa,MAAM,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAC5G,oBAAU,KAAK,IAAI,YAAY,MAAM,EAAE,OAAO,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,KAAK,CAAC;AAAA,QACrF;AAAA,MACJ;AAAA,IACJ;AACA,UAAM;AAAA,EACV;AACA,MAAI,CAAC,MAAM,UAAU;AACjB,QAAI,OAAO,MAAM,WAAW,SAAS,OAAO,IAAI;AAChD,OAAG,QAAQ,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,EAC9C;AACA,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG;AACvC,OAAG,KAAK,UAAU,CAAC,CAAC;AAC5B;AAEA,SAAS,OAAO,MAAM,OAAO,KAAK;AAC9B,UAAQ,SAAS,KAAK,KAAK,WAAW,OAAO,KAAK,UAAU,OACvD,OAAO,KAAK,cAAc,KAAK,WAAW,GAAG,GAAG;AACzD;AAMA,SAAS,WAAW,OAAO;AACvB,MAAI,SAAS,MAAM;AACnB,MAAI,UAAU,OAAO,QAAQ,WAAW,MAAM,YAAY,MAAM,QAAQ;AACxE,WAAS,QAAQ,MAAM,SAAQ,EAAE,OAAO;AACpC,QAAI,OAAO,MAAM,MAAM,KAAK,KAAK;AACjC,QAAI,QAAQ,MAAM,MAAM,MAAM,KAAK,GAAG,WAAW,MAAM,IAAI,WAAW,KAAK;AAC3E,QAAI,QAAQ,MAAM,SAAS,KAAK,WAAW,OAAO,UAAU,OAAO;AAC/D,aAAO;AACX,QAAI,SAAS,KAAK,KAAK,KAAK,KAAK,aAAa,CAAC,OAAO,MAAM,OAAO,QAAQ;AACvE;AAAA,EACR;AACA,SAAO;AACX;AACA,SAAS,KAAK,IAAI,OAAO,QAAQ;AAC7B,MAAI,EAAE,OAAO,KAAK,MAAM,IAAI;AAC5B,MAAI,WAAW,MAAM,OAAO,QAAQ,CAAC,GAAG,SAAS,IAAI,MAAM,QAAQ,CAAC;AACpE,MAAI,QAAQ,UAAU,MAAM;AAC5B,MAAI,SAAS,SAAS,OAAO,YAAY;AACzC,WAAS,IAAI,OAAO,YAAY,OAAO,IAAI,QAAQ;AAC/C,QAAI,aAAa,MAAM,MAAM,CAAC,IAAI,GAAG;AACjC,kBAAY;AACZ,eAAS,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC;AACjD;AAAA,IACJ,OACK;AACD;AAAA,IACJ;AACJ,MAAI,QAAQ,SAAS,OAAO,UAAU;AACtC,WAAS,IAAI,OAAO,YAAY,OAAO,IAAI,QAAQ;AAC/C,QAAI,aAAa,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG;AAC5C,kBAAY;AACZ,cAAQ,SAAS,KAAK,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC;AAC7C;AAAA,IACJ,OACK;AACD;AAAA,IACJ;AACJ,KAAG,KAAK,IAAI,kBAAkB,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,OAAO,OAAO,KAAK,GAAG,WAAW,OAAO,GAAG,OAAO,OAAO,WAAW,IAAI,CAAC;AACnJ;AASA,SAAS,aAAa,OAAO,UAAU,QAAQ,MAAM,aAAa,OAAO;AACrE,MAAI,SAAS,oBAAoB,OAAO,QAAQ;AAChD,MAAI,QAAQ,UAAU,mBAAmB,YAAY,QAAQ;AAC7D,MAAI,CAAC;AACD,WAAO;AACX,SAAO,OAAO,IAAI,SAAS,EACtB,OAAO,EAAE,MAAM,UAAU,MAAM,CAAC,EAAE,OAAO,MAAM,IAAI,SAAS,CAAC;AACtE;AACA,SAAS,UAAU,MAAM;AAAE,SAAO,EAAE,MAAM,OAAO,KAAK;AAAG;AACzD,SAAS,oBAAoB,OAAO,MAAM;AACtC,MAAI,EAAE,QAAQ,YAAY,SAAS,IAAI;AACvC,MAAI,SAAS,OAAO,eAAe,UAAU,EAAE,aAAa,IAAI;AAChE,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,OAAO,SAAS,OAAO,CAAC,IAAI;AACxC,SAAO,OAAO,eAAe,YAAY,UAAU,KAAK,IAAI,SAAS;AACzE;AACA,SAAS,mBAAmB,OAAO,MAAM;AACrC,MAAI,EAAE,QAAQ,YAAY,SAAS,IAAI;AACvC,MAAI,QAAQ,OAAO,MAAM,UAAU;AACnC,MAAI,SAAS,KAAK,aAAa,aAAa,MAAM,IAAI;AACtD,MAAI,CAAC;AACD,WAAO;AACX,MAAI,WAAW,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC,IAAI;AAC3D,MAAI,aAAa,SAAS;AAC1B,WAAS,IAAI,YAAY,cAAc,IAAI,UAAU;AACjD,iBAAa,WAAW,UAAU,OAAO,MAAM,CAAC,EAAE,IAAI;AAC1D,MAAI,CAAC,cAAc,CAAC,WAAW;AAC3B,WAAO;AACX,SAAO;AACX;AACA,SAAS,KAAK,IAAI,OAAO,UAAU;AAC/B,MAAI,UAAU,SAAS;AACvB,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,QAAI,QAAQ,MAAM;AACd,UAAI,QAAQ,SAAS,CAAC,EAAE,KAAK,aAAa,cAAc,OAAO;AAC/D,UAAI,CAAC,SAAS,CAAC,MAAM;AACjB,cAAM,IAAI,WAAW,wFAAwF;AAAA,IACrH;AACA,cAAU,SAAS,KAAK,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC;AAAA,EAC/E;AACA,MAAI,QAAQ,MAAM,OAAO,MAAM,MAAM;AACrC,KAAG,KAAK,IAAI,kBAAkB,OAAO,KAAK,OAAO,KAAK,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,SAAS,QAAQ,IAAI,CAAC;AAC1G;AACA,SAAS,aAAa,IAAI,MAAM,IAAI,MAAM,OAAO;AAC7C,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,WAAW,kDAAkD;AAC3E,MAAI,UAAU,GAAG,MAAM;AACvB,KAAG,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACzC,QAAI,YAAY,OAAO,SAAS,aAAa,MAAM,IAAI,IAAI;AAC3D,QAAI,KAAK,eAAe,CAAC,KAAK,UAAU,MAAM,SAAS,KACnD,cAAc,GAAG,KAAK,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG;AACjE,UAAI,kBAAkB;AACtB,UAAI,KAAK,OAAO,sBAAsB;AAClC,YAAI,MAAM,KAAK,cAAc,OAAO,mBAAmB,CAAC,CAAC,KAAK,aAAa,UAAU,KAAK,OAAO,oBAAoB;AACrH,YAAI,OAAO,CAAC;AACR,4BAAkB;AAAA,iBACb,CAAC,OAAO;AACb,4BAAkB;AAAA,MAC1B;AAEA,UAAI,oBAAoB;AACpB,0BAAkB,IAAI,MAAM,KAAK,OAAO;AAC5C,wBAAkB,IAAI,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,KAAK,CAAC,GAAG,MAAM,QAAW,oBAAoB,IAAI;AACtG,UAAI,UAAU,GAAG,QAAQ,MAAM,OAAO;AACtC,UAAI,SAAS,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,QAAQ,IAAI,MAAM,KAAK,UAAU,CAAC;AAC3E,SAAG,KAAK,IAAI,kBAAkB,QAAQ,MAAM,SAAS,GAAG,OAAO,GAAG,IAAI,MAAM,SAAS,KAAK,KAAK,OAAO,WAAW,MAAM,KAAK,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;AACpJ,UAAI,oBAAoB;AACpB,wBAAgB,IAAI,MAAM,KAAK,OAAO;AAC1C,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AACA,SAAS,gBAAgB,IAAI,MAAM,KAAK,SAAS;AAC7C,OAAK,QAAQ,CAAC,OAAO,WAAW;AAC5B,QAAI,MAAM,QAAQ;AACd,UAAI,GAAG,UAAU;AACjB,aAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG;AACjC,YAAI,QAAQ,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,IAAI,SAAS,EAAE,KAAK;AACpE,WAAG,YAAY,OAAO,QAAQ,GAAG,KAAK,KAAK,OAAO,qBAAqB,OAAO,CAAC;AAAA,MACnF;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,kBAAkB,IAAI,MAAM,KAAK,SAAS;AAC/C,OAAK,QAAQ,CAAC,OAAO,WAAW;AAC5B,QAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,sBAAsB;AACtD,UAAI,QAAQ,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,IAAI,MAAM;AAC1D,SAAG,YAAY,OAAO,QAAQ,GAAG,KAAK,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAChE;AAAA,EACJ,CAAC;AACL;AACA,SAAS,cAAcF,MAAK,KAAK,MAAM;AACnC,MAAI,OAAOA,KAAI,QAAQ,GAAG,GAAG,QAAQ,KAAK,MAAM;AAChD,SAAO,KAAK,OAAO,eAAe,OAAO,QAAQ,GAAG,IAAI;AAC5D;AAKA,SAAS,cAAc,IAAI,KAAK,MAAM,OAAO,OAAO;AAChD,MAAI,OAAO,GAAG,IAAI,OAAO,GAAG;AAC5B,MAAI,CAAC;AACD,UAAM,IAAI,WAAW,2BAA2B;AACpD,MAAI,CAAC;AACD,WAAO,KAAK;AAChB,MAAI,UAAU,KAAK,OAAO,OAAO,MAAM,SAAS,KAAK,KAAK;AAC1D,MAAI,KAAK;AACL,WAAO,GAAG,YAAY,KAAK,MAAM,KAAK,UAAU,OAAO;AAC3D,MAAI,CAAC,KAAK,aAAa,KAAK,OAAO;AAC/B,UAAM,IAAI,WAAW,mCAAmC,KAAK,IAAI;AACrE,KAAG,KAAK,IAAI,kBAAkB,KAAK,MAAM,KAAK,UAAU,MAAM,GAAG,MAAM,KAAK,WAAW,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;AAC/I;AAIA,SAAS,SAASA,MAAK,KAAK,QAAQ,GAAG,YAAY;AAC/C,MAAI,OAAOA,KAAI,QAAQ,GAAG,GAAG,OAAO,KAAK,QAAQ;AACjD,MAAI,YAAa,cAAc,WAAW,WAAW,SAAS,CAAC,KAAM,KAAK;AAC1E,MAAI,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,aAClC,CAAC,KAAK,OAAO,WAAW,KAAK,MAAM,GAAG,KAAK,OAAO,UAAU,KAC5D,CAAC,UAAU,KAAK,aAAa,KAAK,OAAO,QAAQ,WAAW,KAAK,MAAM,GAAG,KAAK,OAAO,UAAU,CAAC;AACjG,WAAO;AACX,WAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,QAAQ,GAAG,IAAI,MAAM,KAAK,KAAK;AAC5D,QAAI,OAAO,KAAK,KAAK,CAAC,GAAGG,SAAQ,KAAK,MAAM,CAAC;AAC7C,QAAI,KAAK,KAAK,KAAK;AACf,aAAO;AACX,QAAI,OAAO,KAAK,QAAQ,WAAWA,QAAO,KAAK,UAAU;AACzD,QAAI,gBAAgB,cAAc,WAAW,IAAI,CAAC;AAClD,QAAI;AACA,aAAO,KAAK,aAAa,GAAG,cAAc,KAAK,OAAO,cAAc,KAAK,CAAC;AAC9E,QAAI,QAAS,cAAc,WAAW,CAAC,KAAM;AAC7C,QAAI,CAAC,KAAK,WAAWA,SAAQ,GAAG,KAAK,UAAU,KAAK,CAAC,MAAM,KAAK,aAAa,IAAI;AAC7E,aAAO;AAAA,EACf;AACA,MAAI,QAAQ,KAAK,WAAW,IAAI;AAChC,MAAI,WAAW,cAAc,WAAW,CAAC;AACzC,SAAO,KAAK,KAAK,IAAI,EAAE,eAAe,OAAO,OAAO,WAAW,SAAS,OAAO,KAAK,KAAK,OAAO,CAAC,EAAE,IAAI;AAC3G;AACA,SAAS,MAAM,IAAI,KAAK,QAAQ,GAAG,YAAY;AAC3C,MAAI,OAAO,GAAG,IAAI,QAAQ,GAAG,GAAG,SAAS,SAAS,OAAO,QAAQ,SAAS;AAC1E,WAAS,IAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,OAAO,IAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,KAAK;AAC7E,aAAS,SAAS,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC;AAChD,QAAI,YAAY,cAAc,WAAW,CAAC;AAC1C,YAAQ,SAAS,KAAK,YAAY,UAAU,KAAK,OAAO,UAAU,OAAO,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EAC9G;AACA,KAAG,KAAK,IAAI,YAAY,KAAK,KAAK,IAAI,MAAM,OAAO,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,IAAI,CAAC;AAC1F;AAKA,SAAS,QAAQH,MAAK,KAAK;AACvB,MAAI,OAAOA,KAAI,QAAQ,GAAG,GAAG,QAAQ,KAAK,MAAM;AAChD,SAAOI,UAAS,KAAK,YAAY,KAAK,SAAS,KAC3C,KAAK,OAAO,WAAW,OAAO,QAAQ,CAAC;AAC/C;AACA,SAAS,mCAAmC,GAAG,GAAG;AAC9C,MAAI,CAAC,EAAE,QAAQ;AACX,MAAE,KAAK,kBAAkB,EAAE,IAAI;AACnC,MAAI,QAAQ,EAAE,eAAe,EAAE,UAAU;AACzC,MAAI,EAAE,qBAAqB,IAAI,EAAE,KAAK;AACtC,WAAS,IAAI,GAAG,IAAI,EAAE,YAAY,KAAK;AACnC,QAAI,QAAQ,EAAE,MAAM,CAAC;AACrB,QAAI,OAAO,MAAM,QAAQ,uBAAuB,EAAE,KAAK,OAAO,MAAM,OAAO,MAAM;AACjF,YAAQ,MAAM,UAAU,IAAI;AAC5B,QAAI,CAAC;AACD,aAAO;AACX,QAAI,CAAC,EAAE,KAAK,YAAY,MAAM,KAAK;AAC/B,aAAO;AAAA,EACf;AACA,SAAO,MAAM;AACjB;AACA,SAASA,UAAS,GAAG,GAAG;AACpB,SAAO,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,UAAU,mCAAmC,GAAG,CAAC;AAC5E;AAMA,SAAS,UAAUJ,MAAK,KAAK,MAAM,IAAI;AACnC,MAAI,OAAOA,KAAI,QAAQ,GAAG;AAC1B,WAAS,IAAI,KAAK,SAAQ,KAAK;AAC3B,QAAI,QAAQ,OAAO,QAAQ,KAAK,MAAM,CAAC;AACvC,QAAI,KAAK,KAAK,OAAO;AACjB,eAAS,KAAK;AACd,cAAQ,KAAK;AAAA,IACjB,WACS,MAAM,GAAG;AACd,eAAS,KAAK,KAAK,IAAI,CAAC;AACxB;AACA,cAAQ,KAAK,KAAK,CAAC,EAAE,WAAW,KAAK;AAAA,IACzC,OACK;AACD,eAAS,KAAK,KAAK,CAAC,EAAE,WAAW,QAAQ,CAAC;AAC1C,cAAQ,KAAK,KAAK,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,UAAU,CAAC,OAAO,eAAeI,UAAS,QAAQ,KAAK,KACvD,KAAK,KAAK,CAAC,EAAE,WAAW,OAAO,QAAQ,CAAC;AACxC,aAAO;AACX,QAAI,KAAK;AACL;AACJ,UAAM,MAAM,IAAI,KAAK,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,EACjD;AACJ;AACA,SAAS,KAAK,IAAI,KAAK,OAAO;AAC1B,MAAI,kBAAkB;AACtB,MAAI,EAAE,qBAAqB,IAAI,GAAG,IAAI,KAAK;AAC3C,MAAI,UAAU,GAAG,IAAI,QAAQ,MAAM,KAAK,GAAG,aAAa,QAAQ,KAAK,EAAE;AACvE,MAAI,wBAAwB,WAAW,eAAe;AAClD,QAAI,MAAM,WAAW,cAAc;AACnC,QAAI,mBAAmB,CAAC,CAAC,WAAW,aAAa,UAAU,oBAAoB;AAC/E,QAAI,OAAO,CAAC;AACR,wBAAkB;AAAA,aACb,CAAC,OAAO;AACb,wBAAkB;AAAA,EAC1B;AACA,MAAI,UAAU,GAAG,MAAM;AACvB,MAAI,oBAAoB,OAAO;AAC3B,QAAI,SAAS,GAAG,IAAI,QAAQ,MAAM,KAAK;AACvC,sBAAkB,IAAI,OAAO,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO;AAAA,EACjE;AACA,MAAI,WAAW;AACX,sBAAkB,IAAI,MAAM,QAAQ,GAAG,YAAY,QAAQ,KAAK,EAAE,eAAe,QAAQ,MAAM,CAAC,GAAG,mBAAmB,IAAI;AAC9H,MAAI,UAAU,GAAG,QAAQ,MAAM,OAAO,GAAG,QAAQ,QAAQ,IAAI,MAAM,KAAK;AACxE,KAAG,KAAK,IAAI,YAAY,OAAO,QAAQ,IAAI,MAAM,OAAO,EAAE,GAAG,MAAM,OAAO,IAAI,CAAC;AAC/E,MAAI,oBAAoB,MAAM;AAC1B,QAAI,QAAQ,GAAG,IAAI,QAAQ,KAAK;AAChC,oBAAgB,IAAI,MAAM,KAAK,GAAG,MAAM,OAAO,GAAG,GAAG,MAAM,MAAM;AAAA,EACrE;AACA,SAAO;AACX;AAOA,SAAS,YAAYJ,MAAK,KAAK,UAAU;AACrC,MAAI,OAAOA,KAAI,QAAQ,GAAG;AAC1B,MAAI,KAAK,OAAO,eAAe,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,QAAQ;AAC/D,WAAO;AACX,MAAI,KAAK,gBAAgB;AACrB,aAAS,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,UAAI,KAAK,KAAK,CAAC,EAAE,eAAe,OAAO,OAAO,QAAQ;AAClD,eAAO,KAAK,OAAO,IAAI,CAAC;AAC5B,UAAI,QAAQ;AACR,eAAO;AAAA,IACf;AACJ,MAAI,KAAK,gBAAgB,KAAK,OAAO,QAAQ;AACzC,aAAS,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,UAAI,KAAK,KAAK,CAAC,EAAE,eAAe,OAAO,OAAO,QAAQ;AAClD,eAAO,KAAK,MAAM,IAAI,CAAC;AAC3B,UAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;AACrB,eAAO;AAAA,IACf;AACJ,SAAO;AACX;AAOA,SAAS,UAAUA,MAAK,KAAK,OAAO;AAChC,MAAI,OAAOA,KAAI,QAAQ,GAAG;AAC1B,MAAI,CAAC,MAAM,QAAQ;AACf,WAAO;AACX,MAAI,UAAU,MAAM;AACpB,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW;AACjC,cAAU,QAAQ,WAAW;AACjC,WAAS,OAAO,GAAG,SAAS,MAAM,aAAa,KAAK,MAAM,OAAO,IAAI,IAAI,QAAQ;AAC7E,aAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,UAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK;AAC9F,UAAI,YAAY,KAAK,MAAM,CAAC,KAAK,OAAO,IAAI,IAAI;AAChD,UAAI,SAAS,KAAK,KAAK,CAAC,GAAG,OAAO;AAClC,UAAI,QAAQ,GAAG;AACX,eAAO,OAAO,WAAW,WAAW,WAAW,OAAO;AAAA,MAC1D,OACK;AACD,YAAI,WAAW,OAAO,eAAe,SAAS,EAAE,aAAa,QAAQ,WAAW,IAAI;AACpF,eAAO,YAAY,OAAO,eAAe,WAAW,WAAW,SAAS,CAAC,CAAC;AAAA,MAC9E;AACA,UAAI;AACA,eAAO,QAAQ,IAAI,KAAK,MAAM,OAAO,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA,IACtF;AAAA,EACJ;AACA,SAAO;AACX;AAQA,SAAS,YAAYA,MAAK,MAAM,KAAK,MAAM,QAAQ,MAAM,OAAO;AAC5D,MAAI,QAAQ,MAAM,CAAC,MAAM;AACrB,WAAO;AACX,MAAI,QAAQA,KAAI,QAAQ,IAAI,GAAG,MAAMA,KAAI,QAAQ,EAAE;AAEnD,MAAI,cAAc,OAAO,KAAK,KAAK;AAC/B,WAAO,IAAI,YAAY,MAAM,IAAI,KAAK;AAC1C,SAAO,IAAI,OAAO,OAAO,KAAK,KAAK,EAAE,IAAI;AAC7C;AACA,SAAS,cAAc,OAAO,KAAK,OAAO;AACtC,SAAO,CAAC,MAAM,aAAa,CAAC,MAAM,WAAW,MAAM,MAAM,KAAK,IAAI,MAAM,KACpE,MAAM,OAAO,WAAW,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG,MAAM,OAAO;AACzE;AAqBA,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,OAAO,KAAK,UAAU;AAC9B,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS,SAAS;AACvB,aAAS,IAAI,GAAG,KAAK,MAAM,OAAO,KAAK;AACnC,UAAI,OAAO,MAAM,KAAK,CAAC;AACvB,WAAK,SAAS,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,eAAe,MAAM,WAAW,CAAC,CAAC;AAAA,MAClD,CAAC;AAAA,IACL;AACA,aAAS,IAAI,MAAM,OAAO,IAAI,GAAG;AAC7B,WAAK,SAAS,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,IAAI,QAAQ;AAAE,WAAO,KAAK,SAAS,SAAS;AAAA,EAAG;AAAA,EAC/C,MAAM;AAIF,WAAO,KAAK,SAAS,MAAM;AACvB,UAAI,MAAM,KAAK,aAAa;AAC5B,UAAI;AACA,aAAK,WAAW,GAAG;AAAA;AAEnB,aAAK,SAAS,KAAK,KAAK,SAAS;AAAA,IACzC;AAMA,QAAI,aAAa,KAAK,eAAe,GAAG,aAAa,KAAK,OAAO,OAAO,KAAK,QAAQ,KAAK,MAAM;AAChG,QAAI,QAAQ,KAAK,OAAO,MAAM,KAAK,MAAM,aAAa,IAAI,KAAK,MAAM,MAAM,IAAI,QAAQ,UAAU,CAAC;AAClG,QAAI,CAAC;AACD,aAAO;AAEX,QAAI,UAAU,KAAK,QAAQ,YAAY,MAAM,OAAO,UAAU,IAAI;AAClE,WAAO,aAAa,WAAW,QAAQ,cAAc,GAAG;AACpD,gBAAU,QAAQ,WAAW;AAC7B;AACA;AAAA,IACJ;AACA,QAAI,QAAQ,IAAI,MAAM,SAAS,WAAW,OAAO;AACjD,QAAI,aAAa;AACb,aAAO,IAAI,kBAAkB,MAAM,KAAK,YAAY,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,OAAO,UAAU;AACvG,QAAI,MAAM,QAAQ,MAAM,OAAO,KAAK,IAAI;AACpC,aAAO,IAAI,YAAY,MAAM,KAAK,IAAI,KAAK,KAAK;AACpD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,QAAI,aAAa,KAAK,SAAS;AAC/B,aAAS,MAAM,KAAK,SAAS,SAAS,IAAI,GAAG,UAAU,KAAK,SAAS,SAAS,IAAI,YAAY,KAAK;AAC/F,UAAI,OAAO,IAAI;AACf,UAAI,IAAI,aAAa;AACjB,kBAAU;AACd,UAAI,KAAK,KAAK,KAAK,aAAa,WAAW,GAAG;AAC1C,qBAAa;AACb;AAAA,MACJ;AACA,YAAM,KAAK;AAAA,IACf;AAGA,aAAS,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAClC,eAAS,aAAa,QAAQ,IAAI,aAAa,KAAK,SAAS,WAAW,cAAc,GAAG,cAAc;AACnG,YAAI,UAAU,SAAS;AACvB,YAAI,YAAY;AACZ,mBAAS,UAAU,KAAK,SAAS,SAAS,aAAa,CAAC,EAAE;AAC1D,qBAAW,OAAO;AAAA,QACtB,OACK;AACD,qBAAW,KAAK,SAAS;AAAA,QAC7B;AACA,YAAI,QAAQ,SAAS;AACrB,iBAAS,gBAAgB,KAAK,OAAO,iBAAiB,GAAG,iBAAiB;AACtE,cAAI,EAAE,MAAM,MAAM,IAAI,KAAK,SAAS,aAAa,GAAGK,OAAM,SAAS;AAInE,cAAI,QAAQ,MAAM,QAAQ,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,WAAW,SAAS,KAAK,KAAK,GAAG,KAAK,KACzG,UAAU,KAAK,kBAAkB,OAAO,IAAI;AAC9C,mBAAO,EAAE,YAAY,eAAe,QAAQ,OAAO;AAAA,mBAG9C,QAAQ,KAAK,UAAUA,QAAO,MAAM,aAAa,MAAM,IAAI;AAChE,mBAAO,EAAE,YAAY,eAAe,QAAQ,MAAAA,MAAK;AAGrD,cAAI,UAAU,MAAM,UAAU,OAAO,IAAI;AACrC;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,QAAI,EAAE,SAAS,WAAW,QAAQ,IAAI,KAAK;AAC3C,QAAI,QAAQ,UAAU,SAAS,SAAS;AACxC,QAAI,CAAC,MAAM,cAAc,MAAM,WAAW;AACtC,aAAO;AACX,SAAK,WAAW,IAAI,MAAM,SAAS,YAAY,GAAG,KAAK,IAAI,SAAS,MAAM,OAAO,aAAa,QAAQ,OAAO,UAAU,YAAY,IAAI,CAAC,CAAC;AACzI,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACP,QAAI,EAAE,SAAS,WAAW,QAAQ,IAAI,KAAK;AAC3C,QAAI,QAAQ,UAAU,SAAS,SAAS;AACxC,QAAI,MAAM,cAAc,KAAK,YAAY,GAAG;AACxC,UAAI,YAAY,QAAQ,OAAO,aAAa,YAAY,MAAM;AAC9D,WAAK,WAAW,IAAI,MAAM,iBAAiB,SAAS,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG,YAAY,YAAY,IAAI,OAAO;AAAA,IAC7H,OACK;AACD,WAAK,WAAW,IAAI,MAAM,iBAAiB,SAAS,WAAW,CAAC,GAAG,WAAW,OAAO;AAAA,IACzF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,EAAE,YAAY,eAAe,QAAQ,QAAQ,MAAAA,MAAK,GAAG;AAC5D,WAAO,KAAK,QAAQ;AAChB,WAAK,kBAAkB;AAC3B,QAAIA;AACA,eAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ;AAC7B,aAAK,iBAAiBA,MAAK,CAAC,CAAC;AACrC,QAAI,QAAQ,KAAK,UAAU,WAAW,SAAS,OAAO,UAAU,MAAM;AACtE,QAAI,YAAY,MAAM,YAAY;AAClC,QAAI,QAAQ,GAAG,MAAM,CAAC;AACtB,QAAI,EAAE,OAAO,KAAK,IAAI,KAAK,SAAS,aAAa;AACjD,QAAI,QAAQ;AACR,eAAS,IAAI,GAAG,IAAI,OAAO,YAAY;AACnC,YAAI,KAAK,OAAO,MAAM,CAAC,CAAC;AAC5B,cAAQ,MAAM,cAAc,MAAM;AAAA,IACtC;AAIA,QAAI,eAAgB,SAAS,OAAO,cAAe,MAAM,QAAQ,OAAO,MAAM;AAG9E,WAAO,QAAQ,SAAS,YAAY;AAChC,UAAI,OAAO,SAAS,MAAM,KAAK,GAAGC,WAAU,MAAM,UAAU,KAAK,IAAI;AACrE,UAAI,CAACA;AACD;AACJ;AACA,UAAI,QAAQ,KAAK,aAAa,KAAK,KAAK,QAAQ,MAAM;AAClD,gBAAQA;AACR,YAAI,KAAK,eAAe,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK,CAAC,GAAG,SAAS,IAAI,YAAY,GAAG,SAAS,SAAS,aAAa,eAAe,EAAE,CAAC;AAAA,MACnJ;AAAA,IACJ;AACA,QAAI,QAAQ,SAAS,SAAS;AAC9B,QAAI,CAAC;AACD,qBAAe;AACnB,SAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC;AAC1E,SAAK,SAAS,aAAa,EAAE,QAAQ;AAGrC,QAAI,SAAS,eAAe,KAAK,UAAU,OAAO,QAAQ,KAAK,SAAS,KAAK,KAAK,EAAE,QAAQ,KAAK,SAAS,SAAS;AAC/G,WAAK,kBAAkB;AAE3B,aAAS,IAAI,GAAG,MAAM,UAAU,IAAI,cAAc,KAAK;AACnD,UAAI,OAAO,IAAI;AACf,WAAK,SAAS,KAAK,EAAE,MAAM,KAAK,MAAM,OAAO,KAAK,eAAe,KAAK,UAAU,EAAE,CAAC;AACnF,YAAM,KAAK;AAAA,IACf;AAIA,SAAK,WAAW,CAAC,QAAQ,IAAI,MAAM,iBAAiB,MAAM,SAAS,YAAY,KAAK,GAAG,MAAM,WAAW,MAAM,OAAO,IAC/G,cAAc,IAAI,MAAM,QACpB,IAAI,MAAM,iBAAiB,MAAM,SAAS,aAAa,GAAG,CAAC,GAAG,aAAa,GAAG,eAAe,IAAI,MAAM,UAAU,aAAa,CAAC;AAAA,EAC7I;AAAA,EACA,iBAAiB;AACb,QAAI,CAAC,KAAK,IAAI,OAAO;AACjB,aAAO;AACX,QAAI,MAAM,KAAK,SAAS,KAAK,KAAK,GAAG;AACrC,QAAI,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,KAAK,KAC9F,KAAK,IAAI,SAAS,KAAK,UAAU,QAAQ,KAAK,eAAe,KAAK,GAAG,MAAM,MAAM,SAAS,KAAK;AAChG,aAAO;AACX,QAAI,EAAE,MAAM,IAAI,KAAK,KAAK,QAAQ,KAAK,IAAI,MAAM,KAAK;AACtD,WAAO,QAAQ,KAAK,SAAS,KAAK,IAAI,IAAI,EAAE,KAAK;AAC7C,QAAE;AACN,WAAO;AAAA,EACX;AAAA,EACA,eAAe,KAAK;AAChB,SAAM,UAAS,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK;AAC7D,UAAI,EAAE,OAAO,KAAK,IAAI,KAAK,SAAS,CAAC;AACrC,UAAI,YAAY,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,SAAS,IAAI;AAC/E,UAAI,MAAM,iBAAiB,KAAK,GAAG,MAAM,OAAO,SAAS;AACzD,UAAI,CAAC;AACD;AACJ,eAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC7B,YAAI,EAAE,OAAAC,QAAO,MAAAC,MAAK,IAAI,KAAK,SAAS,CAAC;AACrC,YAAIF,WAAU,iBAAiB,KAAK,GAAGE,OAAMD,QAAO,IAAI;AACxD,YAAI,CAACD,YAAWA,SAAQ;AACpB,mBAAS;AAAA,MACjB;AACA,aAAO,EAAE,OAAO,GAAG,KAAK,MAAM,YAAY,IAAI,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI;AAAA,IACtF;AAAA,EACJ;AAAA,EACA,MAAM,KAAK;AACP,QAAIG,SAAQ,KAAK,eAAe,GAAG;AACnC,QAAI,CAACA;AACD,aAAO;AACX,WAAO,KAAK,QAAQA,OAAM;AACtB,WAAK,kBAAkB;AAC3B,QAAIA,OAAM,IAAI;AACV,WAAK,SAAS,cAAc,KAAK,QAAQA,OAAM,OAAOA,OAAM,GAAG;AACnE,UAAMA,OAAM;AACZ,aAAS,IAAIA,OAAM,QAAQ,GAAG,KAAK,IAAI,OAAO,KAAK;AAC/C,UAAI,OAAO,IAAI,KAAK,CAAC,GAAG,MAAM,KAAK,KAAK,aAAa,WAAW,KAAK,SAAS,MAAM,IAAI,MAAM,CAAC,CAAC;AAChG,WAAK,iBAAiB,KAAK,MAAM,KAAK,OAAO,GAAG;AAAA,IACpD;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,MAAM,QAAQ,MAAM,SAAS;AAC1C,QAAI,MAAM,KAAK,SAAS,KAAK,KAAK;AAClC,QAAI,QAAQ,IAAI,MAAM,UAAU,IAAI;AACpC,SAAK,SAAS,cAAc,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,OAAO,OAAO,CAAC,CAAC;AAC/F,SAAK,SAAS,KAAK,EAAE,MAAM,OAAO,KAAK,aAAa,CAAC;AAAA,EACzD;AAAA,EACA,oBAAoB;AAChB,QAAI,OAAO,KAAK,SAAS,IAAI;AAC7B,QAAI,MAAM,KAAK,MAAM,WAAW,SAAS,OAAO,IAAI;AACpD,QAAI,IAAI;AACJ,WAAK,SAAS,cAAc,KAAK,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAAA,EAC1E;AACJ;AACA,SAAS,iBAAiB,UAAU,OAAO,OAAO;AAC9C,MAAI,SAAS;AACT,WAAO,SAAS,WAAW,OAAO,SAAS,UAAU;AACzD,SAAO,SAAS,aAAa,GAAG,SAAS,WAAW,KAAK,iBAAiB,SAAS,WAAW,SAAS,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC7H;AACA,SAAS,cAAc,UAAU,OAAO,SAAS;AAC7C,MAAI,SAAS;AACT,WAAO,SAAS,OAAO,OAAO;AAClC,SAAO,SAAS,aAAa,SAAS,aAAa,GAAG,SAAS,UAAU,KAAK,cAAc,SAAS,UAAU,SAAS,QAAQ,GAAG,OAAO,CAAC,CAAC;AAChJ;AACA,SAAS,UAAU,UAAU,OAAO;AAChC,WAAS,IAAI,GAAG,IAAI,OAAO;AACvB,eAAW,SAAS,WAAW;AACnC,SAAO;AACX;AACA,SAAS,eAAe,MAAM,WAAW,SAAS;AAC9C,MAAI,aAAa;AACb,WAAO;AACX,MAAI,OAAO,KAAK;AAChB,MAAI,YAAY;AACZ,WAAO,KAAK,aAAa,GAAG,eAAe,KAAK,YAAY,YAAY,GAAG,KAAK,cAAc,IAAI,UAAU,IAAI,CAAC,CAAC;AACtH,MAAI,YAAY,GAAG;AACf,WAAO,KAAK,KAAK,aAAa,WAAW,IAAI,EAAE,OAAO,IAAI;AAC1D,QAAI,WAAW;AACX,aAAO,KAAK,OAAO,KAAK,KAAK,aAAa,cAAc,IAAI,EAAE,WAAW,SAAS,OAAO,IAAI,CAAC;AAAA,EACtG;AACA,SAAO,KAAK,KAAK,IAAI;AACzB;AACA,SAAS,iBAAiB,KAAK,OAAO,MAAM,OAAO,MAAM;AACrD,MAAI,OAAO,IAAI,KAAK,KAAK,GAAG,QAAQ,OAAO,IAAI,WAAW,KAAK,IAAI,IAAI,MAAM,KAAK;AAClF,MAAI,SAAS,KAAK,cAAc,CAAC,KAAK,kBAAkB,KAAK,IAAI;AAC7D,WAAO;AACX,MAAI,MAAM,MAAM,WAAW,KAAK,SAAS,MAAM,KAAK;AACpD,SAAO,OAAO,CAAC,aAAa,MAAM,KAAK,SAAS,KAAK,IAAI,MAAM;AACnE;AACA,SAAS,aAAa,MAAM,UAAU,OAAO;AACzC,WAAS,IAAI,OAAO,IAAI,SAAS,YAAY;AACzC,QAAI,CAAC,KAAK,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK;AACzC,aAAO;AACf,SAAO;AACX;AACA,SAAS,eAAe,MAAM;AAC1B,SAAO,KAAK,KAAK,YAAY,KAAK,KAAK;AAC3C;AACA,SAAS,aAAa,IAAI,MAAM,IAAI,OAAO;AACvC,MAAI,CAAC,MAAM;AACP,WAAO,GAAG,YAAY,MAAM,EAAE;AAClC,MAAI,QAAQ,GAAG,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,IAAI,QAAQ,EAAE;AACzD,MAAI,cAAc,OAAO,KAAK,KAAK;AAC/B,WAAO,GAAG,KAAK,IAAI,YAAY,MAAM,IAAI,KAAK,CAAC;AACnD,MAAI,eAAe,cAAc,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;AAE1D,MAAI,aAAa,aAAa,SAAS,CAAC,KAAK;AACzC,iBAAa,IAAI;AAGrB,MAAI,kBAAkB,EAAE,MAAM,QAAQ;AACtC,eAAa,QAAQ,eAAe;AAKpC,WAAS,IAAI,MAAM,OAAO,MAAM,MAAM,MAAM,GAAG,IAAI,GAAG,KAAK,OAAO;AAC9D,QAAI,OAAO,MAAM,KAAK,CAAC,EAAE,KAAK;AAC9B,QAAI,KAAK,YAAY,KAAK,qBAAqB,KAAK;AAChD;AACJ,QAAI,aAAa,QAAQ,CAAC,IAAI;AAC1B,wBAAkB;AAAA,aACb,MAAM,OAAO,CAAC,KAAK;AACxB,mBAAa,OAAO,GAAG,GAAG,CAAC,CAAC;AAAA,EACpC;AAGA,MAAI,uBAAuB,aAAa,QAAQ,eAAe;AAC/D,MAAI,YAAY,CAAC,GAAG,iBAAiB,MAAM;AAC3C,WAAS,UAAU,MAAM,SAAS,IAAI,KAAI,KAAK;AAC3C,QAAI,OAAO,QAAQ;AACnB,cAAU,KAAK,IAAI;AACnB,QAAI,KAAK,MAAM;AACX;AACJ,cAAU,KAAK;AAAA,EACnB;AAGA,WAAS,IAAI,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC1C,QAAI,WAAW,UAAU,CAAC,GAAG,MAAM,eAAe,SAAS,IAAI;AAC/D,QAAI,OAAO,CAAC,SAAS,WAAW,MAAM,KAAK,KAAK,IAAI,eAAe,IAAI,CAAC,CAAC;AACrE,uBAAiB;AAAA,aACZ,OAAO,CAAC,SAAS,KAAK;AAC3B;AAAA,EACR;AACA,WAAS,IAAI,MAAM,WAAW,KAAK,GAAG,KAAK;AACvC,QAAI,aAAa,IAAI,iBAAiB,MAAM,MAAM,YAAY;AAC9D,QAAI,SAAS,UAAU,SAAS;AAChC,QAAI,CAAC;AACD;AACJ,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAG1C,UAAI,cAAc,cAAc,IAAI,wBAAwB,aAAa,MAAM,GAAG,SAAS;AAC3F,UAAI,cAAc,GAAG;AACjB,iBAAS;AACT,sBAAc,CAAC;AAAA,MACnB;AACA,UAAI,SAAS,MAAM,KAAK,cAAc,CAAC,GAAG,QAAQ,MAAM,MAAM,cAAc,CAAC;AAC7E,UAAI,OAAO,eAAe,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AAC7D,eAAO,GAAG,QAAQ,MAAM,OAAO,WAAW,GAAG,SAAS,IAAI,MAAM,WAAW,IAAI,IAAI,IAAI,MAAM,cAAc,MAAM,SAAS,GAAG,MAAM,WAAW,SAAS,GAAG,WAAW,MAAM,OAAO,CAAC;AAAA,IAC3L;AAAA,EACJ;AACA,MAAI,aAAa,GAAG,MAAM;AAC1B,WAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,OAAG,QAAQ,MAAM,IAAI,KAAK;AAC1B,QAAI,GAAG,MAAM,SAAS;AAClB;AACJ,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ;AACR;AACJ,WAAO,MAAM,OAAO,KAAK;AACzB,SAAK,IAAI,MAAM,KAAK;AAAA,EACxB;AACJ;AACA,SAAS,cAAc,UAAU,OAAO,SAAS,SAAS,QAAQ;AAC9D,MAAI,QAAQ,SAAS;AACjB,QAAI,QAAQ,SAAS;AACrB,eAAW,SAAS,aAAa,GAAG,MAAM,KAAK,cAAc,MAAM,SAAS,QAAQ,GAAG,SAAS,SAAS,KAAK,CAAC,CAAC;AAAA,EACpH;AACA,MAAI,QAAQ,SAAS;AACjB,QAAI,QAAQ,OAAO,eAAe,CAAC;AACnC,QAAI,QAAQ,MAAM,WAAW,QAAQ,EAAE,OAAO,QAAQ;AACtD,eAAW,MAAM,OAAO,MAAM,cAAc,KAAK,EAAE,WAAW,SAAS,OAAO,IAAI,CAAC;AAAA,EACvF;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,IAAI,MAAM,IAAI,MAAM;AAC1C,MAAI,CAAC,KAAK,YAAY,QAAQ,MAAM,GAAG,IAAI,QAAQ,IAAI,EAAE,OAAO,QAAQ,MAAM;AAC1E,QAAI,QAAQ,YAAY,GAAG,KAAK,MAAM,KAAK,IAAI;AAC/C,QAAI,SAAS;AACT,aAAO,KAAK;AAAA,EACpB;AACA,KAAG,aAAa,MAAM,IAAI,IAAI,MAAM,SAAS,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,SAAS,YAAY,IAAI,MAAM,IAAI;AAC/B,MAAI,QAAQ,GAAG,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,IAAI,QAAQ,EAAE;AACzD,MAAI,UAAU,cAAc,OAAO,GAAG;AACtC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,QAAQ,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,SAAS;AACrD,QAAK,QAAQ,SAAS,KAAM,MAAM,KAAK,KAAK,EAAE,KAAK,aAAa;AAC5D,aAAO,GAAG,OAAO,MAAM,MAAM,KAAK,GAAG,IAAI,IAAI,KAAK,CAAC;AACvD,QAAI,QAAQ,MAAM,QAAQ,MAAM,KAAK,QAAQ,CAAC,EAAE,WAAW,MAAM,MAAM,QAAQ,CAAC,GAAG,IAAI,WAAW,QAAQ,CAAC,CAAC;AACxG,aAAO,GAAG,OAAO,MAAM,OAAO,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC;AAAA,EAC9D;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,KAAK;AACrD,QAAI,OAAO,MAAM,MAAM,CAAC,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,QAAQ,KAChG,MAAM,MAAM,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,EAAE,WAAW,MAAM,MAAM,IAAI,CAAC,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC;AAC3G,aAAO,GAAG,OAAO,MAAM,OAAO,CAAC,GAAG,EAAE;AAAA,EAC5C;AACA,KAAG,OAAO,MAAM,EAAE;AACtB;AAGA,SAAS,cAAc,OAAO,KAAK;AAC/B,MAAI,SAAS,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK;AAC3D,WAAS,IAAI,UAAU,KAAK,GAAG,KAAK;AAChC,QAAI,QAAQ,MAAM,MAAM,CAAC;AACzB,QAAI,QAAQ,MAAM,OAAO,MAAM,QAAQ,MACnC,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,QAAQ,MACpC,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,aACxB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;AACtB;AACJ,QAAI,SAAS,IAAI,MAAM,CAAC,KACnB,KAAK,MAAM,SAAS,KAAK,IAAI,SAAS,MAAM,OAAO,iBAAiB,IAAI,OAAO,iBAC5E,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,QAAQ;AACrC,aAAO,KAAK,CAAC;AAAA,EACrB;AACA,SAAO;AACX;AAKA,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA;AAAA;AAAA;AAAA,EAIxB,YAIA,KAIA,MAEA,OAAO;AACH,UAAM;AACN,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,MAAMT,MAAK;AACP,QAAI,OAAOA,KAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,sCAAsC;AACjE,QAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,aAAS,QAAQ,KAAK;AAClB,YAAM,IAAI,IAAI,KAAK,MAAM,IAAI;AACjC,UAAM,KAAK,IAAI,IAAI,KAAK;AACxB,QAAI,UAAU,KAAK,KAAK,OAAO,OAAO,MAAM,KAAK,KAAK;AACtD,WAAO,WAAW,YAAYA,MAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,SAAS;AACL,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,OAAOA,MAAK;AACR,WAAO,IAAI,UAAS,KAAK,KAAK,KAAK,MAAMA,KAAI,OAAO,KAAK,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;AAAA,EAClF;AAAA,EACA,IAAI,SAAS;AACT,QAAI,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC;AACvC,WAAO,IAAI,eAAe,OAAO,IAAI,UAAS,IAAI,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EAChF;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM;AAAA,EACjF;AAAA,EACA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,OAAO,YAAY,OAAO,KAAK,QAAQ;AACnD,YAAM,IAAI,WAAW,qCAAqC;AAC9D,WAAO,IAAI,UAAS,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EACvD;AACJ;AACA,KAAK,OAAO,QAAQ,QAAQ;AAI5B,IAAM,cAAN,MAAM,qBAAoB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI3B,YAIA,MAEA,OAAO;AACH,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,MAAMA,MAAK;AACP,QAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,aAAS,QAAQA,KAAI;AACjB,YAAM,IAAI,IAAIA,KAAI,MAAM,IAAI;AAChC,UAAM,KAAK,IAAI,IAAI,KAAK;AACxB,QAAI,UAAUA,KAAI,KAAK,OAAO,OAAOA,KAAI,SAASA,KAAI,KAAK;AAC3D,WAAO,WAAW,GAAG,OAAO;AAAA,EAChC;AAAA,EACA,SAAS;AACL,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,OAAOA,MAAK;AACR,WAAO,IAAI,aAAY,KAAK,MAAMA,KAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EAC1D;AAAA,EACA,IAAI,SAAS;AACT,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,WAAW,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM;AAAA,EACrE;AAAA,EACA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ;AACpB,YAAM,IAAI,WAAW,wCAAwC;AACjE,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,KAAK;AAAA,EAChD;AACJ;AACA,KAAK,OAAO,WAAW,WAAW;AAKlC,IAAI,iBAAiB,cAAc,MAAM;AACzC;AACA,iBAAiB,SAASU,gBAAe,SAAS;AAC9C,MAAI,MAAM,MAAM,KAAK,MAAM,OAAO;AAClC,MAAI,YAAYA,gBAAe;AAC/B,SAAO;AACX;AACA,eAAe,YAAY,OAAO,OAAO,MAAM,SAAS;AACxD,eAAe,UAAU,cAAc;AACvC,eAAe,UAAU,OAAO;AAQhC,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAKAV,MAAK;AACD,SAAK,MAAMA;AAIX,SAAK,QAAQ,CAAC;AAId,SAAK,OAAO,CAAC;AAIb,SAAK,UAAU,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC,IAAI,KAAK;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,KAAK,MAAM;AACP,QAAI,SAAS,KAAK,UAAU,IAAI;AAChC,QAAI,OAAO;AACP,YAAM,IAAI,eAAe,OAAO,MAAM;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACZ,QAAI,SAAS,KAAK,MAAM,KAAK,GAAG;AAChC,QAAI,CAAC,OAAO;AACR,WAAK,QAAQ,MAAM,OAAO,GAAG;AACjC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACb,WAAO,KAAK,MAAM,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAMA,MAAK;AACf,SAAK,KAAK,KAAK,KAAK,GAAG;AACvB,SAAK,MAAM,KAAK,IAAI;AACpB,SAAK,QAAQ,UAAU,KAAK,OAAO,CAAC;AACpC,SAAK,MAAMA;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM,OAAO;AAC1C,QAAI,OAAO,YAAY,KAAK,KAAK,MAAM,IAAI,KAAK;AAChD,QAAI;AACA,WAAK,KAAK,IAAI;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,IAAI,SAAS;AAC3B,WAAO,KAAK,QAAQ,MAAM,IAAI,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,CAAC,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,IAAI;AACb,WAAO,KAAK,QAAQ,MAAM,IAAI,MAAM,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,SAAS;AACjB,WAAO,KAAK,YAAY,KAAK,KAAK,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,aAAa,MAAM,IAAI,OAAO;AAC1B,iBAAa,MAAM,MAAM,IAAI,KAAK;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,MAAM,IAAI,MAAM;AAC7B,qBAAiB,MAAM,MAAM,IAAI,IAAI;AACrC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,IAAI;AAClB,gBAAY,MAAM,MAAM,EAAE;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,OAAO,QAAQ;AAChB,SAAK,MAAM,OAAO,MAAM;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,KAAK,QAAQ,GAAG;AACjB,SAAK,MAAM,KAAK,KAAK;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,OAAO,UAAU;AAClB,SAAK,MAAM,OAAO,QAAQ;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC9C,iBAAa,MAAM,MAAM,IAAI,MAAM,KAAK;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK,MAAM,QAAQ,MAAM,OAAO;AAC1C,kBAAc,MAAM,KAAK,MAAM,OAAO,KAAK;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,KAAK,MAAM,OAAO;AAC/B,SAAK,KAAK,IAAI,SAAS,KAAK,MAAM,KAAK,CAAC;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,MAAM,OAAO;AACzB,SAAK,KAAK,IAAI,YAAY,MAAM,KAAK,CAAC;AACtC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,KAAK,MAAM;AACnB,SAAK,KAAK,IAAI,gBAAgB,KAAK,IAAI,CAAC;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,KAAK,MAAM;AACtB,QAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAC9B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,yBAAyB,GAAG;AACrD,QAAI,gBAAgB,MAAM;AACtB,UAAI,KAAK,QAAQ,KAAK,KAAK;AACvB,aAAK,KAAK,IAAI,mBAAmB,KAAK,IAAI,CAAC;AAAA,IACnD,OACK;AACD,UAAI,MAAM,KAAK,OAAOE,QAAO,QAAQ,CAAC;AACtC,aAAOA,SAAQ,KAAK,QAAQ,GAAG,GAAG;AAC9B,cAAM,KAAK,IAAI,mBAAmB,KAAKA,MAAK,CAAC;AAC7C,cAAMA,OAAM,cAAc,GAAG;AAAA,MACjC;AACA,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AACnC,aAAK,KAAK,MAAM,CAAC,CAAC;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAK,QAAQ,GAAG,YAAY;AAC9B,UAAM,MAAM,KAAK,OAAO,UAAU;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,IAAI,MAAM;AACpB,YAAQ,MAAM,MAAM,IAAI,IAAI;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM,IAAI,MAAM;AACvB,eAAW,MAAM,MAAM,IAAI,IAAI;AAC/B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,KAAK,YAAY,OAAO;AACtC,sBAAkB,MAAM,KAAK,YAAY,KAAK;AAC9C,WAAO;AAAA,EACX;AACJ;;;ACtmEA,IAAM,cAAc,uBAAO,OAAO,IAAI;AAKtC,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAKA,SAKA,OAAO,QAAQ;AACX,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,SAAS,UAAU,CAAC,IAAI,eAAe,QAAQ,IAAI,KAAK,GAAG,QAAQ,IAAI,KAAK,CAAC,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIxC,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,KAAK;AAAE,WAAO,KAAK,IAAI;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIhC,IAAI,QAAQ;AACR,WAAO,KAAK,OAAO,CAAC,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM;AACN,WAAO,KAAK,OAAO,CAAC,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACR,QAAI,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,UAAI,OAAO,CAAC,EAAE,MAAM,OAAO,OAAO,CAAC,EAAE,IAAI;AACrC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,IAAI,UAAU,MAAM,OAAO;AAI/B,QAAI,WAAW,QAAQ,QAAQ,WAAW,aAAa;AACvD,aAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,KAAK;AACtC,mBAAa;AACb,iBAAW,SAAS;AAAA,IACxB;AACA,QAAI,UAAU,GAAG,MAAM,QAAQ,SAAS,KAAK;AAC7C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,EAAE,OAAO,IAAI,IAAI,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,MAAM,OAAO;AAClE,SAAG,aAAa,QAAQ,IAAI,MAAM,GAAG,GAAG,QAAQ,IAAI,IAAI,GAAG,GAAG,IAAI,MAAM,QAAQ,OAAO;AACvF,UAAI,KAAK;AACL,gCAAwB,IAAI,UAAU,WAAW,SAAS,WAAW,cAAc,WAAW,eAAe,KAAK,CAAC;AAAA,IAC3H;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,IAAI,MAAM;AAClB,QAAI,UAAU,GAAG,MAAM,QAAQ,SAAS,KAAK;AAC7C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,EAAE,OAAO,IAAI,IAAI,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,MAAM,OAAO;AAClE,UAAI,OAAO,QAAQ,IAAI,MAAM,GAAG,GAAG,KAAK,QAAQ,IAAI,IAAI,GAAG;AAC3D,UAAI,GAAG;AACH,WAAG,YAAY,MAAM,EAAE;AAAA,MAC3B,OACK;AACD,WAAG,iBAAiB,MAAM,IAAI,IAAI;AAClC,gCAAwB,IAAI,SAAS,KAAK,WAAW,KAAK,CAAC;AAAA,MAC/D;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,MAAM,KAAK,WAAW,OAAO;AACzC,QAAI,QAAQ,KAAK,OAAO,gBAAgB,IAAI,cAAc,IAAI,IACxD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,GAAG,KAAK,QAAQ;AACtF,QAAI;AACA,aAAO;AACX,aAAS,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;AAClD,UAAIS,SAAQ,MAAM,IACZ,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,MAAM,KAAK,GAAG,KAAK,QAAQ,IACxG,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,QAAQ,CAAC,GAAG,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,QAAQ;AACjH,UAAIA;AACA,eAAOA;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK,MAAM,OAAO,GAAG;AACxB,WAAO,KAAK,SAAS,MAAM,IAAI,KAAK,KAAK,SAAS,MAAM,CAAC,IAAI,KAAK,IAAI,aAAa,KAAK,KAAK,CAAC,CAAC;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQC,MAAK;AAChB,WAAO,gBAAgBA,MAAKA,MAAK,GAAG,GAAG,CAAC,KAAK,IAAI,aAAaA,IAAG;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAMA,MAAK;AACd,WAAO,gBAAgBA,MAAKA,MAAKA,KAAI,QAAQ,MAAMA,KAAI,YAAY,EAAE,KAAK,IAAI,aAAaA,IAAG;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAASA,MAAK,MAAM;AACvB,QAAI,CAAC,QAAQ,CAAC,KAAK;AACf,YAAM,IAAI,WAAW,sCAAsC;AAC/D,QAAI,MAAM,YAAY,KAAK,IAAI;AAC/B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,qBAAqB,KAAK,IAAI,UAAU;AACjE,WAAO,IAAI,SAASA,MAAK,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,IAAI,gBAAgB;AAC9B,QAAI,MAAM;AACN,YAAM,IAAI,WAAW,wCAAwC,EAAE;AACnE,gBAAY,EAAE,IAAI;AAClB,mBAAe,UAAU,SAAS;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc;AACV,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,KAAK,EAAE,YAAY;AAAA,EACvE;AACJ;AACA,UAAU,UAAU,UAAU;AAI9B,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAIjB,YAIA,OAIA,KAAK;AACD,SAAK,QAAQ;AACb,SAAK,MAAM;AAAA,EACf;AACJ;AACA,IAAI,2BAA2B;AAC/B,SAAS,mBAAmB,MAAM;AAC9B,MAAI,CAAC,4BAA4B,CAAC,KAAK,OAAO,eAAe;AACzD,+BAA2B;AAC3B,YAAQ,MAAM,EAAE,0EAA0E,KAAK,OAAO,KAAK,OAAO,GAAG;AAAA,EACzH;AACJ;AAOA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,SAAS,QAAQ,SAAS;AAClC,uBAAmB,OAAO;AAC1B,uBAAmB,KAAK;AACxB,UAAM,SAAS,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AAAE,WAAO,KAAK,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,EAAM;AAAA,EAC/E,IAAIA,MAAK,SAAS;AACd,QAAI,QAAQA,KAAI,QAAQ,QAAQ,IAAI,KAAK,IAAI,CAAC;AAC9C,QAAI,CAAC,MAAM,OAAO;AACd,aAAO,UAAU,KAAK,KAAK;AAC/B,QAAI,UAAUA,KAAI,QAAQ,QAAQ,IAAI,KAAK,MAAM,CAAC;AAClD,WAAO,IAAI,eAAc,QAAQ,OAAO,gBAAgB,UAAU,OAAO,KAAK;AAAA,EAClF;AAAA,EACA,QAAQ,IAAI,UAAU,MAAM,OAAO;AAC/B,UAAM,QAAQ,IAAI,OAAO;AACzB,QAAI,WAAW,MAAM,OAAO;AACxB,UAAI,QAAQ,KAAK,MAAM,YAAY,KAAK,GAAG;AAC3C,UAAI;AACA,WAAG,YAAY,KAAK;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,GAAG,OAAO;AACN,WAAO,iBAAiB,kBAAiB,MAAM,UAAU,KAAK,UAAU,MAAM,QAAQ,KAAK;AAAA,EAC/F;AAAA,EACA,cAAc;AACV,WAAO,IAAI,aAAa,KAAK,QAAQ,KAAK,IAAI;AAAA,EAClD;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAASA,MAAK,MAAM;AACvB,QAAI,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,QAAQ;AACtD,YAAM,IAAI,WAAW,0CAA0C;AACnE,WAAO,IAAI,eAAcA,KAAI,QAAQ,KAAK,MAAM,GAAGA,KAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAOA,MAAK,QAAQ,OAAO,QAAQ;AACtC,QAAI,UAAUA,KAAI,QAAQ,MAAM;AAChC,WAAO,IAAI,KAAK,SAAS,QAAQ,SAAS,UAAUA,KAAI,QAAQ,IAAI,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ,SAAS,OAAO,MAAM;AACjC,QAAI,OAAO,QAAQ,MAAM,MAAM;AAC/B,QAAI,CAAC,QAAQ;AACT,aAAO,QAAQ,IAAI,IAAI;AAC3B,QAAI,CAAC,MAAM,OAAO,eAAe;AAC7B,UAAID,SAAQ,UAAU,SAAS,OAAO,MAAM,IAAI,KAAK,UAAU,SAAS,OAAO,CAAC,MAAM,IAAI;AAC1F,UAAIA;AACA,gBAAQA,OAAM;AAAA;AAEd,eAAO,UAAU,KAAK,OAAO,IAAI;AAAA,IACzC;AACA,QAAI,CAAC,QAAQ,OAAO,eAAe;AAC/B,UAAI,QAAQ,GAAG;AACX,kBAAU;AAAA,MACd,OACK;AACD,mBAAW,UAAU,SAAS,SAAS,CAAC,MAAM,IAAI,KAAK,UAAU,SAAS,SAAS,MAAM,IAAI,GAAG;AAChG,YAAK,QAAQ,MAAM,MAAM,OAAS,OAAO;AACrC,oBAAU;AAAA,MAClB;AAAA,IACJ;AACA,WAAO,IAAI,eAAc,SAAS,KAAK;AAAA,EAC3C;AACJ;AACA,UAAU,OAAO,QAAQ,aAAa;AACtC,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,SAAS;AACT,WAAO,IAAI,cAAa,QAAQ,IAAI,KAAK,MAAM,GAAG,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,EAC5E;AAAA,EACA,QAAQC,MAAK;AACT,WAAO,cAAc,QAAQA,KAAI,QAAQ,KAAK,MAAM,GAAGA,KAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,EACjF;AACJ;AAQA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,YAAY,MAAM;AACd,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,KAAK,QAAQ;AACxD,UAAM,MAAM,IAAI;AAChB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAIA,MAAK,SAAS;AACd,QAAI,EAAE,SAAS,IAAI,IAAI,QAAQ,UAAU,KAAK,MAAM;AACpD,QAAI,OAAOA,KAAI,QAAQ,GAAG;AAC1B,QAAI;AACA,aAAO,UAAU,KAAK,IAAI;AAC9B,WAAO,IAAI,eAAc,IAAI;AAAA,EACjC;AAAA,EACA,UAAU;AACN,WAAO,IAAI,MAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,EACnD;AAAA,EACA,GAAG,OAAO;AACN,WAAO,iBAAiB,kBAAiB,MAAM,UAAU,KAAK;AAAA,EAClE;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,QAAQ,QAAQ,KAAK,OAAO;AAAA,EAC/C;AAAA,EACA,cAAc;AAAE,WAAO,IAAI,aAAa,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItD,OAAO,SAASA,MAAK,MAAM;AACvB,QAAI,OAAO,KAAK,UAAU;AACtB,YAAM,IAAI,WAAW,0CAA0C;AACnE,WAAO,IAAI,eAAcA,KAAI,QAAQ,KAAK,MAAM,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAOA,MAAK,MAAM;AACrB,WAAO,IAAI,eAAcA,KAAI,QAAQ,IAAI,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,aAAa,MAAM;AACtB,WAAO,CAAC,KAAK,UAAU,KAAK,KAAK,KAAK,eAAe;AAAA,EACzD;AACJ;AACA,cAAc,UAAU,UAAU;AAClC,UAAU,OAAO,QAAQ,aAAa;AACtC,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACT,QAAI,EAAE,SAAS,IAAI,IAAI,QAAQ,UAAU,KAAK,MAAM;AACpD,WAAO,UAAU,IAAI,aAAa,KAAK,GAAG,IAAI,IAAI,cAAa,GAAG;AAAA,EACtE;AAAA,EACA,QAAQA,MAAK;AACT,QAAI,OAAOA,KAAI,QAAQ,KAAK,MAAM,GAAG,OAAO,KAAK;AACjD,QAAI,QAAQ,cAAc,aAAa,IAAI;AACvC,aAAO,IAAI,cAAc,IAAI;AACjC,WAAO,UAAU,KAAK,IAAI;AAAA,EAC9B;AACJ;AAOA,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAYA,MAAK;AACb,UAAMA,KAAI,QAAQ,CAAC,GAAGA,KAAI,QAAQA,KAAI,QAAQ,IAAI,CAAC;AAAA,EACvD;AAAA,EACA,QAAQ,IAAI,UAAU,MAAM,OAAO;AAC/B,QAAI,WAAW,MAAM,OAAO;AACxB,SAAG,OAAO,GAAG,GAAG,IAAI,QAAQ,IAAI;AAChC,UAAI,MAAM,UAAU,QAAQ,GAAG,GAAG;AAClC,UAAI,CAAC,IAAI,GAAG,GAAG,SAAS;AACpB,WAAG,aAAa,GAAG;AAAA,IAC3B,OACK;AACD,YAAM,QAAQ,IAAI,OAAO;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,SAAS;AAAE,WAAO,EAAE,MAAM,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInC,OAAO,SAASA,MAAK;AAAE,WAAO,IAAI,cAAaA,IAAG;AAAA,EAAG;AAAA,EACrD,IAAIA,MAAK;AAAE,WAAO,IAAI,cAAaA,IAAG;AAAA,EAAG;AAAA,EACzC,GAAG,OAAO;AAAE,WAAO,iBAAiB;AAAA,EAAc;AAAA,EAClD,cAAc;AAAE,WAAO;AAAA,EAAa;AACxC;AACA,UAAU,OAAO,OAAO,YAAY;AACpC,IAAM,cAAc;AAAA,EAChB,MAAM;AAAE,WAAO;AAAA,EAAM;AAAA,EACrB,QAAQA,MAAK;AAAE,WAAO,IAAI,aAAaA,IAAG;AAAA,EAAG;AACjD;AAKA,SAAS,gBAAgBA,MAAK,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO;AAC/D,MAAI,KAAK;AACL,WAAO,cAAc,OAAOA,MAAK,GAAG;AACxC,WAAS,IAAI,SAAS,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK,aAAa,KAAK,GAAG,KAAK,KAAK;AACtF,QAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,QAAI,CAAC,MAAM,QAAQ;AACf,UAAI,QAAQ,gBAAgBA,MAAK,OAAO,MAAM,KAAK,MAAM,IAAI,MAAM,aAAa,GAAG,KAAK,IAAI;AAC5F,UAAI;AACA,eAAO;AAAA,IACf,WACS,CAAC,QAAQ,cAAc,aAAa,KAAK,GAAG;AACjD,aAAO,cAAc,OAAOA,MAAK,OAAO,MAAM,IAAI,MAAM,WAAW,EAAE;AAAA,IACzE;AACA,WAAO,MAAM,WAAW;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,IAAI,UAAU,MAAM;AACjD,MAAI,OAAO,GAAG,MAAM,SAAS;AAC7B,MAAI,OAAO;AACP;AACJ,MAAI,OAAO,GAAG,MAAM,IAAI;AACxB,MAAI,EAAE,gBAAgB,eAAe,gBAAgB;AACjD;AACJ,MAAI,MAAM,GAAG,QAAQ,KAAK,IAAI,GAAG;AACjC,MAAI,QAAQ,CAAC,OAAO,KAAK,UAAU,UAAU;AAAE,QAAI,OAAO;AACtD,YAAM;AAAA,EAAO,CAAC;AAClB,KAAG,aAAa,UAAU,KAAK,GAAG,IAAI,QAAQ,GAAG,GAAG,IAAI,CAAC;AAC7D;AAEA,IAAM,cAAc;AAApB,IAAuB,gBAAgB;AAAvC,IAA0C,iBAAiB;AAuB3D,IAAM,cAAN,cAA0B,UAAU;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,OAAO;AACf,UAAM,MAAM,GAAG;AAEf,SAAK,kBAAkB;AAGvB,SAAK,UAAU;AAEf,SAAK,OAAO,uBAAO,OAAO,IAAI;AAC9B,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,eAAe,MAAM;AAC1B,SAAK,cAAc,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YAAY;AACZ,QAAI,KAAK,kBAAkB,KAAK,MAAM,QAAQ;AAC1C,WAAK,eAAe,KAAK,aAAa,IAAI,KAAK,KAAK,KAAK,QAAQ,MAAM,KAAK,eAAe,CAAC;AAC5F,WAAK,kBAAkB,KAAK,MAAM;AAAA,IACtC;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,WAAW;AACpB,QAAI,UAAU,MAAM,OAAO,KAAK;AAC5B,YAAM,IAAI,WAAW,qEAAqE;AAC9F,SAAK,eAAe;AACpB,SAAK,kBAAkB,KAAK,MAAM;AAClC,SAAK,WAAW,KAAK,UAAU,eAAe,CAAC;AAC/C,SAAK,cAAc;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACf,YAAQ,KAAK,UAAU,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,OAAO;AAClB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACf,QAAI,CAAC,KAAK,QAAQ,KAAK,eAAe,KAAK,UAAU,MAAM,MAAM,GAAG,KAAK;AACrE,WAAK,eAAe,KAAK;AAC7B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM;AAChB,WAAO,KAAK,YAAY,KAAK,SAAS,KAAK,eAAe,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACnB,WAAO,KAAK,YAAY,KAAK,cAAc,KAAK,eAAe,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,iBAAiB;AACjB,YAAQ,KAAK,UAAU,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAMA,MAAK;AACf,UAAM,QAAQ,MAAMA,IAAG;AACvB,SAAK,UAAU,KAAK,UAAU,CAAC;AAC/B,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM;AACV,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,OAAO;AACpB,SAAK,UAAU,QAAQ,MAAM,KAAK;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,MAAM,eAAe,MAAM;AAC5C,QAAI,YAAY,KAAK;AACrB,QAAI;AACA,aAAO,KAAK,KAAK,KAAK,gBAAgB,UAAU,QAAQ,UAAU,MAAM,MAAM,IAAK,UAAU,MAAM,YAAY,UAAU,GAAG,KAAK,KAAK,KAAM;AAChJ,cAAU,YAAY,MAAM,IAAI;AAChC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AACd,SAAK,UAAU,QAAQ,IAAI;AAC3B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM,MAAM,IAAI;AACvB,QAAI,SAAS,KAAK,IAAI,KAAK;AAC3B,QAAI,QAAQ,MAAM;AACd,UAAI,CAAC;AACD,eAAO,KAAK,gBAAgB;AAChC,aAAO,KAAK,qBAAqB,OAAO,KAAK,IAAI,GAAG,IAAI;AAAA,IAC5D,OACK;AACD,UAAI,MAAM;AACN,aAAK;AACT,WAAK,MAAM,OAAO,OAAO;AACzB,UAAI,CAAC;AACD,eAAO,KAAK,YAAY,MAAM,EAAE;AACpC,UAAI,QAAQ,KAAK;AACjB,UAAI,CAAC,OAAO;AACR,YAAI,QAAQ,KAAK,IAAI,QAAQ,IAAI;AACjC,gBAAQ,MAAM,OAAO,MAAM,MAAM,IAAI,MAAM,YAAY,KAAK,IAAI,QAAQ,EAAE,CAAC;AAAA,MAC/E;AACA,WAAK,iBAAiB,MAAM,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC;AACxD,UAAI,CAAC,KAAK,UAAU;AAChB,aAAK,aAAa,UAAU,KAAK,KAAK,UAAU,GAAG,CAAC;AACxD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,KAAK,OAAO;AAChB,SAAK,KAAK,OAAO,OAAO,WAAW,MAAM,IAAI,GAAG,IAAI;AACpD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,WAAO,KAAK,KAAK,OAAO,OAAO,WAAW,MAAM,IAAI,GAAG;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,aAAS,KAAK,KAAK;AACf,aAAO;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACb,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,mBAAmB;AACnB,YAAQ,KAAK,UAAU,kBAAkB;AAAA,EAC7C;AACJ;AAEA,SAAS,KAAK,GAAG,MAAM;AACnB,SAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI;AACxC;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM,MAAM,MAAM;AAC1B,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK,KAAK,MAAM,IAAI;AAChC,SAAK,QAAQ,KAAK,KAAK,OAAO,IAAI;AAAA,EACtC;AACJ;AACA,IAAM,aAAa;AAAA,EACf,IAAI,UAAU,OAAO;AAAA,IACjB,KAAK,QAAQ;AAAE,aAAO,OAAO,OAAO,OAAO,OAAO,YAAY,cAAc;AAAA,IAAG;AAAA,IAC/E,MAAM,IAAI;AAAE,aAAO,GAAG;AAAA,IAAK;AAAA,EAC/B,CAAC;AAAA,EACD,IAAI,UAAU,aAAa;AAAA,IACvB,KAAK,QAAQ,UAAU;AAAE,aAAO,OAAO,aAAa,UAAU,QAAQ,SAAS,GAAG;AAAA,IAAG;AAAA,IACrF,MAAM,IAAI;AAAE,aAAO,GAAG;AAAA,IAAW;AAAA,EACrC,CAAC;AAAA,EACD,IAAI,UAAU,eAAe;AAAA,IACzB,KAAK,QAAQ;AAAE,aAAO,OAAO,eAAe;AAAA,IAAM;AAAA,IAClD,MAAM,IAAI,QAAQ,MAAM,OAAO;AAAE,aAAO,MAAM,UAAU,UAAU,GAAG,cAAc;AAAA,IAAM;AAAA,EAC7F,CAAC;AAAA,EACD,IAAI,UAAU,qBAAqB;AAAA,IAC/B,OAAO;AAAE,aAAO;AAAA,IAAG;AAAA,IACnB,MAAM,IAAI,MAAM;AAAE,aAAO,GAAG,mBAAmB,OAAO,IAAI;AAAA,IAAM;AAAA,EACpE,CAAC;AACL;AAGA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,QAAQ,SAAS;AACzB,SAAK,SAAS;AACd,SAAK,UAAU,CAAC;AAChB,SAAK,eAAe,uBAAO,OAAO,IAAI;AACtC,SAAK,SAAS,WAAW,MAAM;AAC/B,QAAI;AACA,cAAQ,QAAQ,YAAU;AACtB,YAAI,KAAK,aAAa,OAAO,GAAG;AAC5B,gBAAM,IAAI,WAAW,mDAAmD,OAAO,MAAM,GAAG;AAC5F,aAAK,QAAQ,KAAK,MAAM;AACxB,aAAK,aAAa,OAAO,GAAG,IAAI;AAChC,YAAI,OAAO,KAAK;AACZ,eAAK,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,OAAO,KAAK,OAAO,MAAM,CAAC;AAAA,MAC7E,CAAC;AAAA,EACT;AACJ;AAUA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAId,YAIA,QAAQ;AACJ,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACT,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACV,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,IAAI;AACN,WAAO,KAAK,iBAAiB,EAAE,EAAE;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,IAAI,SAAS,IAAI;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,QAAQ;AAC5C,UAAI,KAAK,QAAQ;AACb,YAAI,SAAS,KAAK,OAAO,QAAQ,CAAC;AAClC,YAAI,OAAO,KAAK,qBAAqB,CAAC,OAAO,KAAK,kBAAkB,KAAK,QAAQ,IAAI,IAAI;AACrF,iBAAO;AAAA,MACf;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACrB,QAAI,CAAC,KAAK,kBAAkB,MAAM;AAC9B,aAAO,EAAE,OAAO,MAAM,cAAc,CAAC,EAAE;AAC3C,QAAI,MAAM,CAAC,MAAM,GAAG,WAAW,KAAK,WAAW,MAAM,GAAG,OAAO;AAI/D,eAAS;AACL,UAAI,UAAU;AACd,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,QAAQ,KAAK;AACjD,YAAI,SAAS,KAAK,OAAO,QAAQ,CAAC;AAClC,YAAI,OAAO,KAAK,mBAAmB;AAC/B,cAAI,IAAI,OAAO,KAAK,CAAC,EAAE,IAAI,GAAG,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AAChE,cAAI,KAAK,IAAI,IAAI,UACb,OAAO,KAAK,kBAAkB,KAAK,QAAQ,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,QAAQ;AACzF,cAAI,MAAM,SAAS,kBAAkB,IAAI,CAAC,GAAG;AACzC,eAAG,QAAQ,uBAAuB,MAAM;AACxC,gBAAI,CAAC,MAAM;AACP,qBAAO,CAAC;AACR,uBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,QAAQ;AAC5C,qBAAK,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,GAAG,IAAI,OAAO,IAAI,EAAE,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,YACpF;AACA,gBAAI,KAAK,EAAE;AACX,uBAAW,SAAS,WAAW,EAAE;AACjC,sBAAU;AAAA,UACd;AACA,cAAI;AACA,iBAAK,CAAC,IAAI,EAAE,OAAO,UAAU,GAAG,IAAI,OAAO;AAAA,QACnD;AAAA,MACJ;AACA,UAAI,CAAC;AACD,eAAO,EAAE,OAAO,UAAU,cAAc,IAAI;AAAA,IACpD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,IAAI;AACX,QAAI,CAAC,GAAG,OAAO,GAAG,KAAK,GAAG;AACtB,YAAM,IAAI,WAAW,mCAAmC;AAC5D,QAAI,cAAc,IAAI,aAAY,KAAK,MAAM,GAAG,SAAS,KAAK,OAAO;AACrE,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC;AACpB,kBAAY,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI,KAAK,MAAM,IAAI,GAAG,MAAM,WAAW;AAAA,IACjF;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK;AAAE,WAAO,IAAI,YAAY,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzC,OAAO,OAAO,QAAQ;AAClB,QAAI,UAAU,IAAI,cAAc,OAAO,MAAM,OAAO,IAAI,KAAK,SAAS,OAAO,QAAQ,OAAO,OAAO;AACnG,QAAI,WAAW,IAAI,aAAY,OAAO;AACtC,aAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,QAAQ;AACvC,eAAS,QAAQ,OAAO,CAAC,EAAE,IAAI,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,QAAQ,QAAQ;AAC9E,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,QAAQ;AAChB,QAAI,UAAU,IAAI,cAAc,KAAK,QAAQ,OAAO,OAAO;AAC3D,QAAI,SAAS,QAAQ,QAAQ,WAAW,IAAI,aAAY,OAAO;AAC/D,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,OAAO,OAAO,CAAC,EAAE;AACrB,eAAS,IAAI,IAAI,KAAK,eAAe,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,QAAQ,QAAQ;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc;AACjB,QAAI,SAAS,EAAE,KAAK,KAAK,IAAI,OAAO,GAAG,WAAW,KAAK,UAAU,OAAO,EAAE;AAC1E,QAAI,KAAK;AACL,aAAO,cAAc,KAAK,YAAY,IAAI,OAAK,EAAE,OAAO,CAAC;AAC7D,QAAI,gBAAgB,OAAO,gBAAgB;AACvC,eAAS,QAAQ,cAAc;AAC3B,YAAI,QAAQ,SAAS,QAAQ;AACzB,gBAAM,IAAI,WAAW,oDAAoD;AAC7E,YAAI,SAAS,aAAa,IAAI,GAAG,QAAQ,OAAO,KAAK;AACrD,YAAI,SAAS,MAAM;AACf,iBAAO,IAAI,IAAI,MAAM,OAAO,KAAK,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MACjE;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,QAAQ,MAAM,cAAc;AACxC,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,wCAAwC;AACjE,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,WAAW,wCAAwC;AACjE,QAAI,UAAU,IAAI,cAAc,OAAO,QAAQ,OAAO,OAAO;AAC7D,QAAI,WAAW,IAAI,aAAY,OAAO;AACtC,YAAQ,OAAO,QAAQ,WAAS;AAC5B,UAAI,MAAM,QAAQ,OAAO;AACrB,iBAAS,MAAM,KAAK,SAAS,OAAO,QAAQ,KAAK,GAAG;AAAA,MACxD,WACS,MAAM,QAAQ,aAAa;AAChC,iBAAS,YAAY,UAAU,SAAS,SAAS,KAAK,KAAK,SAAS;AAAA,MACxE,WACS,MAAM,QAAQ,eAAe;AAClC,YAAI,KAAK;AACL,mBAAS,cAAc,KAAK,YAAY,IAAI,OAAO,OAAO,YAAY;AAAA,MAC9E,OACK;AACD,YAAI;AACA,mBAAS,QAAQ,cAAc;AAC3B,gBAAI,SAAS,aAAa,IAAI,GAAG,QAAQ,OAAO,KAAK;AACrD,gBAAI,OAAO,OAAO,MAAM,QAAQ,SAAS,MAAM,YAC3C,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AAClD,uBAAS,MAAM,IAAI,IAAI,MAAM,SAAS,KAAK,QAAQ,QAAQ,KAAK,IAAI,GAAG,QAAQ;AAC/E;AAAA,YACJ;AAAA,UACJ;AACJ,iBAAS,MAAM,IAAI,IAAI,MAAM,KAAK,QAAQ,QAAQ;AAAA,MACtD;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,UAAU,KAAK,MAAM,QAAQ;AAClC,WAAS,QAAQ,KAAK;AAClB,QAAI,MAAM,IAAI,IAAI;AAClB,QAAI,eAAe;AACf,YAAM,IAAI,KAAK,IAAI;AAAA,aACd,QAAQ;AACb,YAAM,UAAU,KAAK,MAAM,CAAC,CAAC;AACjC,WAAO,IAAI,IAAI;AAAA,EACnB;AACA,SAAO;AACX;AAMA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIT,YAIA,MAAM;AACF,SAAK,OAAO;AAIZ,SAAK,QAAQ,CAAC;AACd,QAAI,KAAK;AACL,gBAAU,KAAK,OAAO,MAAM,KAAK,KAAK;AAC1C,SAAK,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,UAAU,QAAQ;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO;AAAE,WAAO,MAAM,KAAK,GAAG;AAAA,EAAG;AAC9C;AACA,IAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,SAAS,UAAU,MAAM;AACrB,MAAI,QAAQ;AACR,WAAO,OAAO,MAAM,EAAE,KAAK,IAAI;AACnC,OAAK,IAAI,IAAI;AACb,SAAO,OAAO;AAClB;AAOA,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAAY,OAAO,OAAO;AAAE,SAAK,MAAM,UAAU,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,IAAI,OAAO;AAAE,WAAO,MAAM,OAAO,aAAa,KAAK,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzD,SAAS,OAAO;AAAE,WAAO,MAAM,KAAK,GAAG;AAAA,EAAG;AAC9C;", "names": ["found", "copy", "found", "doc", "i", "type", "nfa", "edge", "expr", "node", "states", "name", "mark", "wrap", "space", "doc", "parent", "found", "index", "joinable", "wrap", "matches", "match", "type", "close", "TransformError", "found", "doc"]}