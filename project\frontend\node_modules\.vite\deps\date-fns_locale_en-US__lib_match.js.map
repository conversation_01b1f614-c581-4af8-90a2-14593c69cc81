{"version": 3, "sources": ["../../date-fns/locale/_lib/buildMatchFn/index.js", "../../date-fns/locale/_lib/buildMatchPatternFn/index.js", "../../date-fns/locale/en-US/_lib/match/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = buildMatchFn;\nfunction buildMatchFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    }) : findKey(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    });\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (object.hasOwnProperty(key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = buildMatchPatternFn;\nfunction buildMatchPatternFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\nmodule.exports = exports.default;", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\nvar matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(b|a)/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  wide: /^(before christ|before common era|anno domini|common era)/i\n};\nvar parseEraPatterns = {\n  any: [/^b/i, /^(a|c)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](th|st|nd|rd)? quarter/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,\n  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^may/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|mo|tu|we|th|fr|sa)/i,\n  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,\n  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],\n  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,\n  any: /^([ap]\\.?\\s?m\\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mi/i,\n    noon: /^no/i,\n    morning: /morning/i,\n    afternoon: /afternoon/i,\n    evening: /evening/i,\n    night: /night/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,aAAS,aAAa,MAAM;AAC1B,aAAO,SAAU,QAAQ;AACvB,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,YAAI,QAAQ,QAAQ;AACpB,YAAI,eAAe,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,iBAAiB;AAClG,YAAI,cAAc,OAAO,MAAM,YAAY;AAC3C,YAAI,CAAC,aAAa;AAChB,iBAAO;AAAA,QACT;AACA,YAAI,gBAAgB,YAAY,CAAC;AACjC,YAAI,gBAAgB,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,iBAAiB;AACnG,YAAI,MAAM,MAAM,QAAQ,aAAa,IAAI,UAAU,eAAe,SAAU,SAAS;AACnF,iBAAO,QAAQ,KAAK,aAAa;AAAA,QACnC,CAAC,IAAI,QAAQ,eAAe,SAAU,SAAS;AAC7C,iBAAO,QAAQ,KAAK,aAAa;AAAA,QACnC,CAAC;AACD,YAAI;AACJ,gBAAQ,KAAK,gBAAgB,KAAK,cAAc,GAAG,IAAI;AACvD,gBAAQ,QAAQ,gBAAgB,QAAQ,cAAc,KAAK,IAAI;AAC/D,YAAI,OAAO,OAAO,MAAM,cAAc,MAAM;AAC5C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,QAAQ,QAAQ,WAAW;AAClC,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,eAAe,GAAG,KAAK,UAAU,OAAO,GAAG,CAAC,GAAG;AACxD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,UAAU,OAAO,WAAW;AACnC,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,YAAI,UAAU,MAAM,GAAG,CAAC,GAAG;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU,QAAQ;AAAA;AAAA;;;AChDzB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,aAAS,oBAAoB,MAAM;AACjC,aAAO,SAAU,QAAQ;AACvB,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,YAAI,cAAc,OAAO,MAAM,KAAK,YAAY;AAChD,YAAI,CAAC,YAAa,QAAO;AACzB,YAAI,gBAAgB,YAAY,CAAC;AACjC,YAAI,cAAc,OAAO,MAAM,KAAK,YAAY;AAChD,YAAI,CAAC,YAAa,QAAO;AACzB,YAAI,QAAQ,KAAK,gBAAgB,KAAK,cAAc,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC;AACnF,gBAAQ,QAAQ,gBAAgB,QAAQ,cAAc,KAAK,IAAI;AAC/D,YAAI,OAAO,OAAO,MAAM,cAAc,MAAM;AAC5C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU,QAAQ;AAAA;AAAA;;;ACvBzB;AAAA;AAEA,QAAI,yBAAyB,gCAAwD;AACrF,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,SAAS,uBAAuB,sBAA8C;AAClF,QAAI,UAAU,uBAAuB,6BAAqD;AAC1F,QAAI,4BAA4B;AAChC,QAAI,4BAA4B;AAChC,QAAI,mBAAmB;AAAA,MACrB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,MAAM;AAAA,IACR;AACA,QAAI,mBAAmB;AAAA,MACrB,KAAK,CAAC,OAAO,SAAS;AAAA,IACxB;AACA,QAAI,uBAAuB;AAAA,MACzB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,MAAM;AAAA,IACR;AACA,QAAI,uBAAuB;AAAA,MACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAC9B;AACA,QAAI,qBAAqB;AAAA,MACvB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,MAAM;AAAA,IACR;AACA,QAAI,qBAAqB;AAAA,MACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC3F,KAAK,CAAC,QAAQ,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AAAA,IACrG;AACA,QAAI,mBAAmB;AAAA,MACrB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,IACR;AACA,QAAI,mBAAmB;AAAA,MACrB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MACxD,KAAK,CAAC,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,MAAM;AAAA,IAC3D;AACA,QAAI,yBAAyB;AAAA,MAC3B,QAAQ;AAAA,MACR,KAAK;AAAA,IACP;AACA,QAAI,yBAAyB;AAAA,MAC3B,KAAK;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,QAAQ;AAAA,MACV,gBAAgB,GAAG,QAAQ,SAAS;AAAA,QAClC,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe,SAAS,cAAc,OAAO;AAC3C,iBAAO,SAAS,OAAO,EAAE;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,MACD,MAAM,GAAG,OAAO,SAAS;AAAA,QACvB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB,CAAC;AAAA,MACD,UAAU,GAAG,OAAO,SAAS;AAAA,QAC3B,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,eAAe,SAAS,cAAc,OAAO;AAC3C,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,GAAG,OAAO,SAAS;AAAA,QACzB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB,CAAC;AAAA,MACD,MAAM,GAAG,OAAO,SAAS;AAAA,QACvB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB,CAAC;AAAA,MACD,YAAY,GAAG,OAAO,SAAS;AAAA,QAC7B,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB,CAAC;AAAA,IACH;AACA,QAAI,WAAW;AACf,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ;AAAA;AAAA;", "names": []}