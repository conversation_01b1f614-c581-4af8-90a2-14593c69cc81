{"version": 3, "sources": ["../../date-fns/locale/en-US/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'less than a second',\n    other: 'less than {{count}} seconds'\n  },\n  xSeconds: {\n    one: '1 second',\n    other: '{{count}} seconds'\n  },\n  halfAMinute: 'half a minute',\n  lessThanXMinutes: {\n    one: 'less than a minute',\n    other: 'less than {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'about 1 hour',\n    other: 'about {{count}} hours'\n  },\n  xHours: {\n    one: '1 hour',\n    other: '{{count}} hours'\n  },\n  xDays: {\n    one: '1 day',\n    other: '{{count}} days'\n  },\n  aboutXWeeks: {\n    one: 'about 1 week',\n    other: 'about {{count}} weeks'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weeks'\n  },\n  aboutXMonths: {\n    one: 'about 1 month',\n    other: 'about {{count}} months'\n  },\n  xMonths: {\n    one: '1 month',\n    other: '{{count}} months'\n  },\n  aboutXYears: {\n    one: 'about 1 year',\n    other: 'about {{count}} years'\n  },\n  xYears: {\n    one: '1 year',\n    other: '{{count}} years'\n  },\n  overXYears: {\n    one: 'over 1 year',\n    other: 'over {{count}} years'\n  },\n  almostXYears: {\n    one: 'almost 1 year',\n    other: 'almost {{count}} years'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return result + ' ago';\n    }\n  }\n  return result;\n};\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,uBAAuB;AAAA,MACzB,kBAAkB;AAAA,QAChB,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,aAAa;AAAA,MACb,kBAAkB;AAAA,QAChB,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,aAAa;AAAA,QACX,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,aAAa;AAAA,QACX,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,QACZ,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,aAAa;AAAA,QACX,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,QACV,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,QACZ,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,SAAS;AAClE,UAAI;AACJ,UAAI,aAAa,qBAAqB,KAAK;AAC3C,UAAI,OAAO,eAAe,UAAU;AAClC,iBAAS;AAAA,MACX,WAAW,UAAU,GAAG;AACtB,iBAAS,WAAW;AAAA,MACtB,OAAO;AACL,iBAAS,WAAW,MAAM,QAAQ,aAAa,MAAM,SAAS,CAAC;AAAA,MACjE;AACA,UAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,YAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,iBAAO,QAAQ;AAAA,QACjB,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,WAAW;AACf,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ;AAAA;AAAA;", "names": ["formatDistance"]}