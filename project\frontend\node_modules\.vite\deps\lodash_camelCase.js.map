{"version": 3, "sources": ["../../lodash/capitalize.js", "../../lodash/camelCase.js"], "sourcesContent": ["var toString = require('./toString'),\n    upperFirst = require('./upperFirst');\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\n\nmodule.exports = capitalize;\n", "var capitalize = require('./capitalize'),\n    createCompounder = require('./_createCompounder');\n\n/**\n * Converts `string` to [camel case](https://en.wikipedia.org/wiki/CamelCase).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the camel cased string.\n * @example\n *\n * _.camelCase('Foo Bar');\n * // => 'fooBar'\n *\n * _.camelCase('--foo-bar--');\n * // => 'fooBar'\n *\n * _.camelCase('__FOO_BAR__');\n * // => 'fooBar'\n */\nvar camelCase = createCompounder(function(result, word, index) {\n  word = word.toLowerCase();\n  return result + (index ? capitalize(word) : word);\n});\n\nmodule.exports = camelCase;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,aAAa;AAiBjB,aAAS,WAAW,QAAQ;AAC1B,aAAO,WAAW,SAAS,MAAM,EAAE,YAAY,CAAC;AAAA,IAClD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,mBAAmB;AAsBvB,QAAI,YAAY,iBAAiB,SAAS,QAAQ,MAAM,OAAO;AAC7D,aAAO,KAAK,YAAY;AACxB,aAAO,UAAU,QAAQ,WAAW,IAAI,IAAI;AAAA,IAC9C,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}