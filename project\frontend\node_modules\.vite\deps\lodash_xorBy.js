import {
  require_baseUniq
} from "./chunk-HE7TDZG7.js";
import {
  require_last
} from "./chunk-YXYNTHJR.js";
import {
  require_baseDifference
} from "./chunk-3SPSYUSI.js";
import {
  require_baseFlatten
} from "./chunk-ICXN6OJ6.js";
import "./chunk-TYXAQETL.js";
import {
  require_isArrayLikeObject
} from "./chunk-DOSSVMDF.js";
import {
  require_baseRest
} from "./chunk-SMZ2DSXW.js";
import "./chunk-TLVLGZ6X.js";
import "./chunk-JIR7Y6MV.js";
import "./chunk-3HWTEJRL.js";
import {
  require_baseIteratee
} from "./chunk-PBFE772U.js";
import "./chunk-O5S2LSZG.js";
import "./chunk-LQ553RKZ.js";
import "./chunk-A32U5YLP.js";
import "./chunk-TM56S4GI.js";
import "./chunk-ABTCRKER.js";
import "./chunk-5F5Z2YWX.js";
import "./chunk-EWR3BJJI.js";
import "./chunk-U7VWWHCD.js";
import "./chunk-VZITUV5G.js";
import {
  require_arrayFilter
} from "./chunk-OH26WOYB.js";
import "./chunk-6MOPH6ER.js";
import "./chunk-BVJZN6TR.js";
import "./chunk-WEVAHQUU.js";
import "./chunk-M2WBRPB3.js";
import "./chunk-CWSHORJK.js";
import "./chunk-U4XCUM7X.js";
import "./chunk-5PX5EJ7R.js";
import "./chunk-7I5PEIZF.js";
import "./chunk-MIX47OBP.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-WIEA6MZB.js";
import "./chunk-EHIGHKKH.js";
import "./chunk-D4QEHMHD.js";
import "./chunk-RWM7CMLN.js";
import "./chunk-GYHWOQRN.js";
import "./chunk-D5KFZMAO.js";
import "./chunk-64Z5HK43.js";
import "./chunk-IW7F5ZXM.js";
import "./chunk-F753BK7A.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseXor.js
var require_baseXor = __commonJS({
  "node_modules/lodash/_baseXor.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseFlatten = require_baseFlatten();
    var baseUniq = require_baseUniq();
    function baseXor(arrays, iteratee, comparator) {
      var length = arrays.length;
      if (length < 2) {
        return length ? baseUniq(arrays[0]) : [];
      }
      var index = -1, result = Array(length);
      while (++index < length) {
        var array = arrays[index], othIndex = -1;
        while (++othIndex < length) {
          if (othIndex != index) {
            result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);
          }
        }
      }
      return baseUniq(baseFlatten(result, 1), iteratee, comparator);
    }
    module.exports = baseXor;
  }
});

// node_modules/lodash/xorBy.js
var require_xorBy = __commonJS({
  "node_modules/lodash/xorBy.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var baseIteratee = require_baseIteratee();
    var baseRest = require_baseRest();
    var baseXor = require_baseXor();
    var isArrayLikeObject = require_isArrayLikeObject();
    var last = require_last();
    var xorBy = baseRest(function(arrays) {
      var iteratee = last(arrays);
      if (isArrayLikeObject(iteratee)) {
        iteratee = void 0;
      }
      return baseXor(arrayFilter(arrays, isArrayLikeObject), baseIteratee(iteratee, 2));
    });
    module.exports = xorBy;
  }
});
export default require_xorBy();
//# sourceMappingURL=lodash_xorBy.js.map
