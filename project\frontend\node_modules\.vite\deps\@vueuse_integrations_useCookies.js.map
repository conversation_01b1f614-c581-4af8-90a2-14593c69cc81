{"version": 3, "sources": ["../../universal-cookie/esm/index.mjs", "../../@vueuse/integrations/useCookies.mjs"], "sourcesContent": ["var dist = {};\n\nvar hasRequiredDist;\n\nfunction requireDist () {\n\tif (hasRequiredDist) return dist;\n\thasRequiredDist = 1;\n\tObject.defineProperty(dist, \"__esModule\", { value: true });\n\tdist.parse = parse;\n\tdist.serialize = serialize;\n\t/**\n\t * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n\t * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n\t * which has been replaced by the token definition in RFC 7230 appendix B.\n\t *\n\t * cookie-name       = token\n\t * token             = 1*tchar\n\t * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n\t *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n\t *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n\t *\n\t * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n\t * Allow same range as cookie value, except `=`, which delimits end of name.\n\t */\n\tconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\t/**\n\t * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n\t *\n\t * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n\t * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n\t *                     ; US-ASCII characters excluding CTLs,\n\t *                     ; whitespace DQUOTE, comma, semicolon,\n\t *                     ; and backslash\n\t *\n\t * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n\t * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n\t */\n\tconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\t/**\n\t * RegExp to match domain-value in RFC 6265 sec 4.1.1\n\t *\n\t * domain-value      = <subdomain>\n\t *                     ; defined in [RFC1034], Section 3.5, as\n\t *                     ; enhanced by [RFC1123], Section 2.1\n\t * <subdomain>       = <label> | <subdomain> \".\" <label>\n\t * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n\t *                     Labels must be 63 characters or less.\n\t *                     'let-dig' not 'letter' in the first char, per RFC1123\n\t * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n\t * <let-dig-hyp>     = <let-dig> | \"-\"\n\t * <let-dig>         = <letter> | <digit>\n\t * <letter>          = any one of the 52 alphabetic characters A through Z in\n\t *                     upper case and a through z in lower case\n\t * <digit>           = any one of the ten digits 0 through 9\n\t *\n\t * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n\t *\n\t * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n\t * character is not permitted, but a trailing %x2E (\".\"), if present, will\n\t * cause the user agent to ignore the attribute.)\n\t */\n\tconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\t/**\n\t * RegExp to match path-value in RFC 6265 sec 4.1.1\n\t *\n\t * path-value        = <any CHAR except CTLs or \";\">\n\t * CHAR              = %x01-7F\n\t *                     ; defined in RFC 5234 appendix B.1\n\t */\n\tconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\tconst __toString = Object.prototype.toString;\n\tconst NullObject = /* @__PURE__ */ (() => {\n\t    const C = function () { };\n\t    C.prototype = Object.create(null);\n\t    return C;\n\t})();\n\t/**\n\t * Parse a cookie header.\n\t *\n\t * Parse the given cookie header string into an object\n\t * The object has the various cookies as keys(names) => values\n\t */\n\tfunction parse(str, options) {\n\t    const obj = new NullObject();\n\t    const len = str.length;\n\t    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n\t    if (len < 2)\n\t        return obj;\n\t    const dec = options?.decode || decode;\n\t    let index = 0;\n\t    do {\n\t        const eqIdx = str.indexOf(\"=\", index);\n\t        if (eqIdx === -1)\n\t            break; // No more cookie pairs.\n\t        const colonIdx = str.indexOf(\";\", index);\n\t        const endIdx = colonIdx === -1 ? len : colonIdx;\n\t        if (eqIdx > endIdx) {\n\t            // backtrack on prior semicolon\n\t            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n\t            continue;\n\t        }\n\t        const keyStartIdx = startIndex(str, index, eqIdx);\n\t        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n\t        const key = str.slice(keyStartIdx, keyEndIdx);\n\t        // only assign once\n\t        if (obj[key] === undefined) {\n\t            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n\t            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\t            const value = dec(str.slice(valStartIdx, valEndIdx));\n\t            obj[key] = value;\n\t        }\n\t        index = endIdx + 1;\n\t    } while (index < len);\n\t    return obj;\n\t}\n\tfunction startIndex(str, index, max) {\n\t    do {\n\t        const code = str.charCodeAt(index);\n\t        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n\t            return index;\n\t    } while (++index < max);\n\t    return max;\n\t}\n\tfunction endIndex(str, index, min) {\n\t    while (index > min) {\n\t        const code = str.charCodeAt(--index);\n\t        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n\t            return index + 1;\n\t    }\n\t    return min;\n\t}\n\t/**\n\t * Serialize data into a cookie header.\n\t *\n\t * Serialize a name value pair into a cookie string suitable for\n\t * http headers. An optional options object specifies cookie parameters.\n\t *\n\t * serialize('foo', 'bar', { httpOnly: true })\n\t *   => \"foo=bar; httpOnly\"\n\t */\n\tfunction serialize(name, val, options) {\n\t    const enc = options?.encode || encodeURIComponent;\n\t    if (!cookieNameRegExp.test(name)) {\n\t        throw new TypeError(`argument name is invalid: ${name}`);\n\t    }\n\t    const value = enc(val);\n\t    if (!cookieValueRegExp.test(value)) {\n\t        throw new TypeError(`argument val is invalid: ${val}`);\n\t    }\n\t    let str = name + \"=\" + value;\n\t    if (!options)\n\t        return str;\n\t    if (options.maxAge !== undefined) {\n\t        if (!Number.isInteger(options.maxAge)) {\n\t            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n\t        }\n\t        str += \"; Max-Age=\" + options.maxAge;\n\t    }\n\t    if (options.domain) {\n\t        if (!domainValueRegExp.test(options.domain)) {\n\t            throw new TypeError(`option domain is invalid: ${options.domain}`);\n\t        }\n\t        str += \"; Domain=\" + options.domain;\n\t    }\n\t    if (options.path) {\n\t        if (!pathValueRegExp.test(options.path)) {\n\t            throw new TypeError(`option path is invalid: ${options.path}`);\n\t        }\n\t        str += \"; Path=\" + options.path;\n\t    }\n\t    if (options.expires) {\n\t        if (!isDate(options.expires) ||\n\t            !Number.isFinite(options.expires.valueOf())) {\n\t            throw new TypeError(`option expires is invalid: ${options.expires}`);\n\t        }\n\t        str += \"; Expires=\" + options.expires.toUTCString();\n\t    }\n\t    if (options.httpOnly) {\n\t        str += \"; HttpOnly\";\n\t    }\n\t    if (options.secure) {\n\t        str += \"; Secure\";\n\t    }\n\t    if (options.partitioned) {\n\t        str += \"; Partitioned\";\n\t    }\n\t    if (options.priority) {\n\t        const priority = typeof options.priority === \"string\"\n\t            ? options.priority.toLowerCase()\n\t            : undefined;\n\t        switch (priority) {\n\t            case \"low\":\n\t                str += \"; Priority=Low\";\n\t                break;\n\t            case \"medium\":\n\t                str += \"; Priority=Medium\";\n\t                break;\n\t            case \"high\":\n\t                str += \"; Priority=High\";\n\t                break;\n\t            default:\n\t                throw new TypeError(`option priority is invalid: ${options.priority}`);\n\t        }\n\t    }\n\t    if (options.sameSite) {\n\t        const sameSite = typeof options.sameSite === \"string\"\n\t            ? options.sameSite.toLowerCase()\n\t            : options.sameSite;\n\t        switch (sameSite) {\n\t            case true:\n\t            case \"strict\":\n\t                str += \"; SameSite=Strict\";\n\t                break;\n\t            case \"lax\":\n\t                str += \"; SameSite=Lax\";\n\t                break;\n\t            case \"none\":\n\t                str += \"; SameSite=None\";\n\t                break;\n\t            default:\n\t                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n\t        }\n\t    }\n\t    return str;\n\t}\n\t/**\n\t * URL-decode string value. Optimized to skip native call when no %.\n\t */\n\tfunction decode(str) {\n\t    if (str.indexOf(\"%\") === -1)\n\t        return str;\n\t    try {\n\t        return decodeURIComponent(str);\n\t    }\n\t    catch (e) {\n\t        return str;\n\t    }\n\t}\n\t/**\n\t * Determine if value is a Date.\n\t */\n\tfunction isDate(val) {\n\t    return __toString.call(val) === \"[object Date]\";\n\t}\n\t\n\treturn dist;\n}\n\nvar distExports = requireDist();\n\nfunction hasDocumentCookie() {\n    const testingValue = typeof global === 'undefined'\n        ? undefined\n        : global.TEST_HAS_DOCUMENT_COOKIE;\n    if (typeof testingValue === 'boolean') {\n        return testingValue;\n    }\n    // Can we get/set cookies on document.cookie?\n    return typeof document === 'object' && typeof document.cookie === 'string';\n}\nfunction parseCookies(cookies) {\n    if (typeof cookies === 'string') {\n        return distExports.parse(cookies);\n    }\n    else if (typeof cookies === 'object' && cookies !== null) {\n        return cookies;\n    }\n    else {\n        return {};\n    }\n}\nfunction readCookie(value, options = {}) {\n    const cleanValue = cleanupCookieValue(value);\n    if (!options.doNotParse) {\n        try {\n            return JSON.parse(cleanValue);\n        }\n        catch (e) {\n            // At least we tried\n        }\n    }\n    // Ignore clean value if we failed the deserialization\n    // It is not relevant anymore to trim those values\n    return value;\n}\nfunction cleanupCookieValue(value) {\n    // express prepend j: before serializing a cookie\n    if (value && value[0] === 'j' && value[1] === ':') {\n        return value.substr(2);\n    }\n    return value;\n}\n\nclass Cookies {\n    constructor(cookies, defaultSetOptions = {}) {\n        this.changeListeners = [];\n        this.HAS_DOCUMENT_COOKIE = false;\n        this.update = () => {\n            if (!this.HAS_DOCUMENT_COOKIE) {\n                return;\n            }\n            const previousCookies = this.cookies;\n            this.cookies = distExports.parse(document.cookie);\n            this._checkChanges(previousCookies);\n        };\n        const domCookies = typeof document === 'undefined' ? '' : document.cookie;\n        this.cookies = parseCookies(cookies || domCookies);\n        this.defaultSetOptions = defaultSetOptions;\n        this.HAS_DOCUMENT_COOKIE = hasDocumentCookie();\n    }\n    _emitChange(params) {\n        for (let i = 0; i < this.changeListeners.length; ++i) {\n            this.changeListeners[i](params);\n        }\n    }\n    _checkChanges(previousCookies) {\n        const names = new Set(Object.keys(previousCookies).concat(Object.keys(this.cookies)));\n        names.forEach((name) => {\n            if (previousCookies[name] !== this.cookies[name]) {\n                this._emitChange({\n                    name,\n                    value: readCookie(this.cookies[name]),\n                });\n            }\n        });\n    }\n    _startPolling() {\n        this.pollingInterval = setInterval(this.update, 300);\n    }\n    _stopPolling() {\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n        }\n    }\n    get(name, options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        return readCookie(this.cookies[name], options);\n    }\n    getAll(options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        const result = {};\n        for (let name in this.cookies) {\n            result[name] = readCookie(this.cookies[name], options);\n        }\n        return result;\n    }\n    set(name, value, options) {\n        if (options) {\n            options = Object.assign(Object.assign({}, this.defaultSetOptions), options);\n        }\n        else {\n            options = this.defaultSetOptions;\n        }\n        const stringValue = typeof value === 'string' ? value : JSON.stringify(value);\n        this.cookies = Object.assign(Object.assign({}, this.cookies), { [name]: stringValue });\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = distExports.serialize(name, stringValue, options);\n        }\n        this._emitChange({ name, value, options });\n    }\n    remove(name, options) {\n        const finalOptions = (options = Object.assign(Object.assign(Object.assign({}, this.defaultSetOptions), options), { expires: new Date(1970, 1, 1, 0, 0, 1), maxAge: 0 }));\n        this.cookies = Object.assign({}, this.cookies);\n        delete this.cookies[name];\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = distExports.serialize(name, '', finalOptions);\n        }\n        this._emitChange({ name, value: undefined, options });\n    }\n    addChangeListener(callback) {\n        this.changeListeners.push(callback);\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 1) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.addEventListener('change', this.update);\n            }\n            else {\n                this._startPolling();\n            }\n        }\n    }\n    removeChangeListener(callback) {\n        const idx = this.changeListeners.indexOf(callback);\n        if (idx >= 0) {\n            this.changeListeners.splice(idx, 1);\n        }\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 0) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.removeEventListener('change', this.update);\n            }\n            else {\n                this._stopPolling();\n            }\n        }\n    }\n    removeAllChangeListeners() {\n        while (this.changeListeners.length > 0) {\n            this.removeChangeListener(this.changeListeners[0]);\n        }\n    }\n}\n\nexport { Cookies as default };\n", "import { tryOnScopeDispose } from '@vueuse/shared';\nimport <PERSON><PERSON> from 'universal-cookie';\nimport { ref } from 'vue-demi';\n\nfunction createCookies(req) {\n  const universalCookie = new Cookie(req ? req.headers.cookie : null);\n  return (dependencies, { doNotParse = false, autoUpdateDependencies = false } = {}) => useCookies(dependencies, { doNotParse, autoUpdateDependencies }, universalCookie);\n}\nfunction useCookies(dependencies, { doNotParse = false, autoUpdateDependencies = false } = {}, cookies = new Cookie()) {\n  const watchingDependencies = autoUpdateDependencies ? [...dependencies || []] : dependencies;\n  let previousCookies = cookies.getAll({ doNotParse: true });\n  const touches = ref(0);\n  const onChange = () => {\n    const newCookies = cookies.getAll({ doNotParse: true });\n    if (shouldUpdate(\n      watchingDependencies || null,\n      newCookies,\n      previousCookies\n    )) {\n      touches.value++;\n    }\n    previousCookies = newCookies;\n  };\n  cookies.addChangeListener(onChange);\n  tryOnScopeDispose(() => {\n    cookies.removeChangeListener(onChange);\n  });\n  return {\n    /**\n     * Reactive get cookie by name. If **autoUpdateDependencies = true** then it will update watching dependencies\n     */\n    get: (...args) => {\n      if (autoUpdateDependencies && watchingDependencies && !watchingDependencies.includes(args[0]))\n        watchingDependencies.push(args[0]);\n      touches.value;\n      return cookies.get(args[0], { doNotParse, ...args[1] });\n    },\n    /**\n     * Reactive get all cookies\n     */\n    getAll: (...args) => {\n      touches.value;\n      return cookies.getAll({ doNotParse, ...args[0] });\n    },\n    set: (...args) => cookies.set(...args),\n    remove: (...args) => cookies.remove(...args),\n    addChangeListener: (...args) => cookies.addChangeListener(...args),\n    removeChangeListener: (...args) => cookies.removeChangeListener(...args)\n  };\n}\nfunction shouldUpdate(dependencies, newCookies, oldCookies) {\n  if (!dependencies)\n    return true;\n  for (const dependency of dependencies) {\n    if (newCookies[dependency] !== oldCookies[dependency])\n      return true;\n  }\n  return false;\n}\n\nexport { createCookies, useCookies };\n"], "mappings": ";;;;;;;;;;AAAA,IAAI,OAAO,CAAC;AAEZ,IAAI;AAEJ,SAAS,cAAe;AACvB,MAAI,gBAAiB,QAAO;AAC5B,oBAAkB;AAClB,SAAO,eAAe,MAAM,cAAc,EAAE,OAAO,KAAK,CAAC;AACzD,OAAK,QAAQ;AACb,OAAK,YAAY;AAejB,QAAM,mBAAmB;AAazB,QAAM,oBAAoB;AAwB1B,QAAM,oBAAoB;AAQ1B,QAAM,kBAAkB;AACxB,QAAM,aAAa,OAAO,UAAU;AACpC,QAAM,cAA8B,MAAM;AACtC,UAAM,IAAI,WAAY;AAAA,IAAE;AACxB,MAAE,YAAY,uBAAO,OAAO,IAAI;AAChC,WAAO;AAAA,EACX,GAAG;AAOH,WAAS,MAAM,KAAK,SAAS;AACzB,UAAM,MAAM,IAAI,WAAW;AAC3B,UAAM,MAAM,IAAI;AAEhB,QAAI,MAAM;AACN,aAAO;AACX,UAAM,MAAM,SAAS,UAAU;AAC/B,QAAI,QAAQ;AACZ,OAAG;AACC,YAAM,QAAQ,IAAI,QAAQ,KAAK,KAAK;AACpC,UAAI,UAAU;AACV;AACJ,YAAM,WAAW,IAAI,QAAQ,KAAK,KAAK;AACvC,YAAM,SAAS,aAAa,KAAK,MAAM;AACvC,UAAI,QAAQ,QAAQ;AAEhB,gBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;AAAA,MACJ;AACA,YAAM,cAAc,WAAW,KAAK,OAAO,KAAK;AAChD,YAAM,YAAY,SAAS,KAAK,OAAO,WAAW;AAClD,YAAM,MAAM,IAAI,MAAM,aAAa,SAAS;AAE5C,UAAI,IAAI,GAAG,MAAM,QAAW;AACxB,YAAI,cAAc,WAAW,KAAK,QAAQ,GAAG,MAAM;AACnD,YAAI,YAAY,SAAS,KAAK,QAAQ,WAAW;AACjD,cAAM,QAAQ,IAAI,IAAI,MAAM,aAAa,SAAS,CAAC;AACnD,YAAI,GAAG,IAAI;AAAA,MACf;AACA,cAAQ,SAAS;AAAA,IACrB,SAAS,QAAQ;AACjB,WAAO;AAAA,EACX;AACA,WAAS,WAAW,KAAK,OAAO,KAAK;AACjC,OAAG;AACC,YAAM,OAAO,IAAI,WAAW,KAAK;AACjC,UAAI,SAAS,MAAgB,SAAS;AAClC,eAAO;AAAA,IACf,SAAS,EAAE,QAAQ;AACnB,WAAO;AAAA,EACX;AACA,WAAS,SAAS,KAAK,OAAO,KAAK;AAC/B,WAAO,QAAQ,KAAK;AAChB,YAAM,OAAO,IAAI,WAAW,EAAE,KAAK;AACnC,UAAI,SAAS,MAAgB,SAAS;AAClC,eAAO,QAAQ;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AAUA,WAAS,UAAU,MAAM,KAAK,SAAS;AACnC,UAAM,MAAM,SAAS,UAAU;AAC/B,QAAI,CAAC,iBAAiB,KAAK,IAAI,GAAG;AAC9B,YAAM,IAAI,UAAU,6BAA6B,IAAI,EAAE;AAAA,IAC3D;AACA,UAAM,QAAQ,IAAI,GAAG;AACrB,QAAI,CAAC,kBAAkB,KAAK,KAAK,GAAG;AAChC,YAAM,IAAI,UAAU,4BAA4B,GAAG,EAAE;AAAA,IACzD;AACA,QAAI,MAAM,OAAO,MAAM;AACvB,QAAI,CAAC;AACD,aAAO;AACX,QAAI,QAAQ,WAAW,QAAW;AAC9B,UAAI,CAAC,OAAO,UAAU,QAAQ,MAAM,GAAG;AACnC,cAAM,IAAI,UAAU,6BAA6B,QAAQ,MAAM,EAAE;AAAA,MACrE;AACA,aAAO,eAAe,QAAQ;AAAA,IAClC;AACA,QAAI,QAAQ,QAAQ;AAChB,UAAI,CAAC,kBAAkB,KAAK,QAAQ,MAAM,GAAG;AACzC,cAAM,IAAI,UAAU,6BAA6B,QAAQ,MAAM,EAAE;AAAA,MACrE;AACA,aAAO,cAAc,QAAQ;AAAA,IACjC;AACA,QAAI,QAAQ,MAAM;AACd,UAAI,CAAC,gBAAgB,KAAK,QAAQ,IAAI,GAAG;AACrC,cAAM,IAAI,UAAU,2BAA2B,QAAQ,IAAI,EAAE;AAAA,MACjE;AACA,aAAO,YAAY,QAAQ;AAAA,IAC/B;AACA,QAAI,QAAQ,SAAS;AACjB,UAAI,CAAC,OAAO,QAAQ,OAAO,KACvB,CAAC,OAAO,SAAS,QAAQ,QAAQ,QAAQ,CAAC,GAAG;AAC7C,cAAM,IAAI,UAAU,8BAA8B,QAAQ,OAAO,EAAE;AAAA,MACvE;AACA,aAAO,eAAe,QAAQ,QAAQ,YAAY;AAAA,IACtD;AACA,QAAI,QAAQ,UAAU;AAClB,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,QAAQ;AAChB,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,aAAa;AACrB,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,UAAU;AAClB,YAAM,WAAW,OAAO,QAAQ,aAAa,WACvC,QAAQ,SAAS,YAAY,IAC7B;AACN,cAAQ,UAAU;AAAA,QACd,KAAK;AACD,iBAAO;AACP;AAAA,QACJ,KAAK;AACD,iBAAO;AACP;AAAA,QACJ,KAAK;AACD,iBAAO;AACP;AAAA,QACJ;AACI,gBAAM,IAAI,UAAU,+BAA+B,QAAQ,QAAQ,EAAE;AAAA,MAC7E;AAAA,IACJ;AACA,QAAI,QAAQ,UAAU;AAClB,YAAM,WAAW,OAAO,QAAQ,aAAa,WACvC,QAAQ,SAAS,YAAY,IAC7B,QAAQ;AACd,cAAQ,UAAU;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AACP;AAAA,QACJ,KAAK;AACD,iBAAO;AACP;AAAA,QACJ,KAAK;AACD,iBAAO;AACP;AAAA,QACJ;AACI,gBAAM,IAAI,UAAU,+BAA+B,QAAQ,QAAQ,EAAE;AAAA,MAC7E;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAIA,WAAS,OAAO,KAAK;AACjB,QAAI,IAAI,QAAQ,GAAG,MAAM;AACrB,aAAO;AACX,QAAI;AACA,aAAO,mBAAmB,GAAG;AAAA,IACjC,SACO,GAAG;AACN,aAAO;AAAA,IACX;AAAA,EACJ;AAIA,WAAS,OAAO,KAAK;AACjB,WAAO,WAAW,KAAK,GAAG,MAAM;AAAA,EACpC;AAEA,SAAO;AACR;AAEA,IAAI,cAAc,YAAY;AAE9B,SAAS,oBAAoB;AACzB,QAAM,eAAe,OAAO,WAAW,cACjC,SACA,OAAO;AACb,MAAI,OAAO,iBAAiB,WAAW;AACnC,WAAO;AAAA,EACX;AAEA,SAAO,OAAO,aAAa,YAAY,OAAO,SAAS,WAAW;AACtE;AACA,SAAS,aAAa,SAAS;AAC3B,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO,YAAY,MAAM,OAAO;AAAA,EACpC,WACS,OAAO,YAAY,YAAY,YAAY,MAAM;AACtD,WAAO;AAAA,EACX,OACK;AACD,WAAO,CAAC;AAAA,EACZ;AACJ;AACA,SAAS,WAAW,OAAO,UAAU,CAAC,GAAG;AACrC,QAAM,aAAa,mBAAmB,KAAK;AAC3C,MAAI,CAAC,QAAQ,YAAY;AACrB,QAAI;AACA,aAAO,KAAK,MAAM,UAAU;AAAA,IAChC,SACO,GAAG;AAAA,IAEV;AAAA,EACJ;AAGA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO;AAE/B,MAAI,SAAS,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,KAAK;AAC/C,WAAO,MAAM,OAAO,CAAC;AAAA,EACzB;AACA,SAAO;AACX;AAEA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,SAAS,oBAAoB,CAAC,GAAG;AACzC,SAAK,kBAAkB,CAAC;AACxB,SAAK,sBAAsB;AAC3B,SAAK,SAAS,MAAM;AAChB,UAAI,CAAC,KAAK,qBAAqB;AAC3B;AAAA,MACJ;AACA,YAAM,kBAAkB,KAAK;AAC7B,WAAK,UAAU,YAAY,MAAM,SAAS,MAAM;AAChD,WAAK,cAAc,eAAe;AAAA,IACtC;AACA,UAAM,aAAa,OAAO,aAAa,cAAc,KAAK,SAAS;AACnE,SAAK,UAAU,aAAa,WAAW,UAAU;AACjD,SAAK,oBAAoB;AACzB,SAAK,sBAAsB,kBAAkB;AAAA,EACjD;AAAA,EACA,YAAY,QAAQ;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,EAAE,GAAG;AAClD,WAAK,gBAAgB,CAAC,EAAE,MAAM;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,cAAc,iBAAiB;AAC3B,UAAM,QAAQ,IAAI,IAAI,OAAO,KAAK,eAAe,EAAE,OAAO,OAAO,KAAK,KAAK,OAAO,CAAC,CAAC;AACpF,UAAM,QAAQ,CAAC,SAAS;AACpB,UAAI,gBAAgB,IAAI,MAAM,KAAK,QAAQ,IAAI,GAAG;AAC9C,aAAK,YAAY;AAAA,UACb;AAAA,UACA,OAAO,WAAW,KAAK,QAAQ,IAAI,CAAC;AAAA,QACxC,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB;AACZ,SAAK,kBAAkB,YAAY,KAAK,QAAQ,GAAG;AAAA,EACvD;AAAA,EACA,eAAe;AACX,QAAI,KAAK,iBAAiB;AACtB,oBAAc,KAAK,eAAe;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,IAAI,MAAM,UAAU,CAAC,GAAG;AACpB,QAAI,CAAC,QAAQ,aAAa;AACtB,WAAK,OAAO;AAAA,IAChB;AACA,WAAO,WAAW,KAAK,QAAQ,IAAI,GAAG,OAAO;AAAA,EACjD;AAAA,EACA,OAAO,UAAU,CAAC,GAAG;AACjB,QAAI,CAAC,QAAQ,aAAa;AACtB,WAAK,OAAO;AAAA,IAChB;AACA,UAAM,SAAS,CAAC;AAChB,aAAS,QAAQ,KAAK,SAAS;AAC3B,aAAO,IAAI,IAAI,WAAW,KAAK,QAAQ,IAAI,GAAG,OAAO;AAAA,IACzD;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,MAAM,OAAO,SAAS;AACtB,QAAI,SAAS;AACT,gBAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,iBAAiB,GAAG,OAAO;AAAA,IAC9E,OACK;AACD,gBAAU,KAAK;AAAA,IACnB;AACA,UAAM,cAAc,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,KAAK;AAC5E,SAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,EAAE,CAAC,IAAI,GAAG,YAAY,CAAC;AACrF,QAAI,KAAK,qBAAqB;AAC1B,eAAS,SAAS,YAAY,UAAU,MAAM,aAAa,OAAO;AAAA,IACtE;AACA,SAAK,YAAY,EAAE,MAAM,OAAO,QAAQ,CAAC;AAAA,EAC7C;AAAA,EACA,OAAO,MAAM,SAAS;AAClB,UAAM,eAAgB,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,iBAAiB,GAAG,OAAO,GAAG,EAAE,SAAS,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,QAAQ,EAAE,CAAC;AACtK,SAAK,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAC7C,WAAO,KAAK,QAAQ,IAAI;AACxB,QAAI,KAAK,qBAAqB;AAC1B,eAAS,SAAS,YAAY,UAAU,MAAM,IAAI,YAAY;AAAA,IAClE;AACA,SAAK,YAAY,EAAE,MAAM,OAAO,QAAW,QAAQ,CAAC;AAAA,EACxD;AAAA,EACA,kBAAkB,UAAU;AACxB,SAAK,gBAAgB,KAAK,QAAQ;AAClC,QAAI,KAAK,uBAAuB,KAAK,gBAAgB,WAAW,GAAG;AAC/D,UAAI,OAAO,WAAW,YAAY,iBAAiB,QAAQ;AACvD,eAAO,YAAY,iBAAiB,UAAU,KAAK,MAAM;AAAA,MAC7D,OACK;AACD,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,qBAAqB,UAAU;AAC3B,UAAM,MAAM,KAAK,gBAAgB,QAAQ,QAAQ;AACjD,QAAI,OAAO,GAAG;AACV,WAAK,gBAAgB,OAAO,KAAK,CAAC;AAAA,IACtC;AACA,QAAI,KAAK,uBAAuB,KAAK,gBAAgB,WAAW,GAAG;AAC/D,UAAI,OAAO,WAAW,YAAY,iBAAiB,QAAQ;AACvD,eAAO,YAAY,oBAAoB,UAAU,KAAK,MAAM;AAAA,MAChE,OACK;AACD,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,2BAA2B;AACvB,WAAO,KAAK,gBAAgB,SAAS,GAAG;AACpC,WAAK,qBAAqB,KAAK,gBAAgB,CAAC,CAAC;AAAA,IACrD;AAAA,EACJ;AACJ;;;AC/YA,SAAS,cAAc,KAAK;AAC1B,QAAM,kBAAkB,IAAI,QAAO,MAAM,IAAI,QAAQ,SAAS,IAAI;AAClE,SAAO,CAAC,cAAc,EAAE,aAAa,OAAO,yBAAyB,MAAM,IAAI,CAAC,MAAM,WAAW,cAAc,EAAE,YAAY,uBAAuB,GAAG,eAAe;AACxK;AACA,SAAS,WAAW,cAAc,EAAE,aAAa,OAAO,yBAAyB,MAAM,IAAI,CAAC,GAAG,UAAU,IAAI,QAAO,GAAG;AACrH,QAAM,uBAAuB,yBAAyB,CAAC,GAAG,gBAAgB,CAAC,CAAC,IAAI;AAChF,MAAI,kBAAkB,QAAQ,OAAO,EAAE,YAAY,KAAK,CAAC;AACzD,QAAM,UAAU,MAAI,CAAC;AACrB,QAAM,WAAW,MAAM;AACrB,UAAM,aAAa,QAAQ,OAAO,EAAE,YAAY,KAAK,CAAC;AACtD,QAAI;AAAA,MACF,wBAAwB;AAAA,MACxB;AAAA,MACA;AAAA,IACF,GAAG;AACD,cAAQ;AAAA,IACV;AACA,sBAAkB;AAAA,EACpB;AACA,UAAQ,kBAAkB,QAAQ;AAClC,oBAAkB,MAAM;AACtB,YAAQ,qBAAqB,QAAQ;AAAA,EACvC,CAAC;AACD,SAAO;AAAA;AAAA;AAAA;AAAA,IAIL,KAAK,IAAI,SAAS;AAChB,UAAI,0BAA0B,wBAAwB,CAAC,qBAAqB,SAAS,KAAK,CAAC,CAAC;AAC1F,6BAAqB,KAAK,KAAK,CAAC,CAAC;AACnC,cAAQ;AACR,aAAO,QAAQ,IAAI,KAAK,CAAC,GAAG,EAAE,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC;AAAA,IACxD;AAAA;AAAA;AAAA;AAAA,IAIA,QAAQ,IAAI,SAAS;AACnB,cAAQ;AACR,aAAO,QAAQ,OAAO,EAAE,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC;AAAA,IAClD;AAAA,IACA,KAAK,IAAI,SAAS,QAAQ,IAAI,GAAG,IAAI;AAAA,IACrC,QAAQ,IAAI,SAAS,QAAQ,OAAO,GAAG,IAAI;AAAA,IAC3C,mBAAmB,IAAI,SAAS,QAAQ,kBAAkB,GAAG,IAAI;AAAA,IACjE,sBAAsB,IAAI,SAAS,QAAQ,qBAAqB,GAAG,IAAI;AAAA,EACzE;AACF;AACA,SAAS,aAAa,cAAc,YAAY,YAAY;AAC1D,MAAI,CAAC;AACH,WAAO;AACT,aAAW,cAAc,cAAc;AACrC,QAAI,WAAW,UAAU,MAAM,WAAW,UAAU;AAClD,aAAO;AAAA,EACX;AACA,SAAO;AACT;", "names": []}