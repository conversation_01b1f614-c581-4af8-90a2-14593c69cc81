{"version": 3, "sources": ["../../lodash/_baseLt.js", "../../lodash/min.js"], "sourcesContent": ["/**\n * The base implementation of `_.lt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n */\nfunction baseLt(value, other) {\n  return value < other;\n}\n\nmodule.exports = baseLt;\n", "var baseExtremum = require('./_baseExtremum'),\n    baseLt = require('./_baseLt'),\n    identity = require('./identity');\n\n/**\n * Computes the minimum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * _.min([4, 2, 8, 6]);\n * // => 2\n *\n * _.min([]);\n * // => undefined\n */\nfunction min(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseLt)\n    : undefined;\n}\n\nmodule.exports = min;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AASA,aAAS,OAAO,OAAO,OAAO;AAC5B,aAAO,QAAQ;AAAA,IACjB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,SAAS;AADb,QAEI,WAAW;AAoBf,aAAS,IAAI,OAAO;AAClB,aAAQ,SAAS,MAAM,SACnB,aAAa,OAAO,UAAU,MAAM,IACpC;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}