import {
  require_flatRest
} from "./chunk-46HMDFZU.js";
import {
  require_baseUnset
} from "./chunk-G43Q2PJ3.js";
import {
  require_isPlainObject
} from "./chunk-SEH7GCXK.js";
import "./chunk-WI7ETHBW.js";
import {
  require_baseClone
} from "./chunk-BMQZ3A7Y.js";
import {
  require_copyObject
} from "./chunk-ZOBWSDQD.js";
import "./chunk-K7A7KXLZ.js";
import {
  require_getAllKeysIn
} from "./chunk-HRBZELO4.js";
import "./chunk-OZGEKB37.js";
import "./chunk-ZAOAHT2A.js";
import "./chunk-A4TMC7AQ.js";
import "./chunk-MXIOLC32.js";
import "./chunk-LFGLJSP3.js";
import "./chunk-YXYNTHJR.js";
import "./chunk-ICXN6OJ6.js";
import "./chunk-TLVLGZ6X.js";
import "./chunk-JIR7Y6MV.js";
import "./chunk-3HWTEJRL.js";
import "./chunk-TM56S4GI.js";
import {
  require_castPath
} from "./chunk-ABTCRKER.js";
import "./chunk-VZITUV5G.js";
import "./chunk-OH26WOYB.js";
import "./chunk-6MOPH6ER.js";
import "./chunk-BVJZN6TR.js";
import "./chunk-WEVAHQUU.js";
import {
  require_arrayMap
} from "./chunk-CWSHORJK.js";
import "./chunk-U4XCUM7X.js";
import "./chunk-5PX5EJ7R.js";
import "./chunk-7I5PEIZF.js";
import "./chunk-MIX47OBP.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-WIEA6MZB.js";
import "./chunk-EHIGHKKH.js";
import "./chunk-D4QEHMHD.js";
import "./chunk-RWM7CMLN.js";
import "./chunk-GYHWOQRN.js";
import "./chunk-D5KFZMAO.js";
import "./chunk-64Z5HK43.js";
import "./chunk-IW7F5ZXM.js";
import "./chunk-F753BK7A.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_customOmitClone.js
var require_customOmitClone = __commonJS({
  "node_modules/lodash/_customOmitClone.js"(exports, module) {
    var isPlainObject = require_isPlainObject();
    function customOmitClone(value) {
      return isPlainObject(value) ? void 0 : value;
    }
    module.exports = customOmitClone;
  }
});

// node_modules/lodash/omit.js
var require_omit = __commonJS({
  "node_modules/lodash/omit.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseClone = require_baseClone();
    var baseUnset = require_baseUnset();
    var castPath = require_castPath();
    var copyObject = require_copyObject();
    var customOmitClone = require_customOmitClone();
    var flatRest = require_flatRest();
    var getAllKeysIn = require_getAllKeysIn();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_FLAT_FLAG = 2;
    var CLONE_SYMBOLS_FLAG = 4;
    var omit = flatRest(function(object, paths) {
      var result = {};
      if (object == null) {
        return result;
      }
      var isDeep = false;
      paths = arrayMap(paths, function(path) {
        path = castPath(path, object);
        isDeep || (isDeep = path.length > 1);
        return path;
      });
      copyObject(object, getAllKeysIn(object), result);
      if (isDeep) {
        result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);
      }
      var length = paths.length;
      while (length--) {
        baseUnset(result, paths[length]);
      }
      return result;
    });
    module.exports = omit;
  }
});
export default require_omit();
//# sourceMappingURL=lodash_omit.js.map
