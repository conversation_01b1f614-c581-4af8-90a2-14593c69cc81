{"version": 3, "sources": ["../../date-fns/locale/_lib/buildFormatLongFn/index.js", "../../date-fns/locale/en-US/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = buildFormatLongFn;\nfunction buildFormatLongFn(args) {\n  return function () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // TODO: Remove String()\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\nmodule.exports = exports.default;", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\nvar dateFormats = {\n  full: 'EEEE, MMMM do, y',\n  long: 'MMMM do, y',\n  medium: 'MMM d, y',\n  short: 'MM/dd/yyyy'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,aAAS,kBAAkB,MAAM;AAC/B,aAAO,WAAY;AACjB,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,YAAI,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI,KAAK;AACzD,YAAI,SAAS,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,YAAY;AAClE,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,UAAU,QAAQ;AAAA;AAAA;;;ACfzB;AAAA;AAEA,QAAI,yBAAyB,gCAAwD;AACrF,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,SAAS,uBAAuB,2BAAmD;AACvF,QAAI,cAAc;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AACA,QAAI,cAAc;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AACA,QAAI,kBAAkB;AAAA,MACpB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AACA,QAAI,aAAa;AAAA,MACf,OAAO,GAAG,OAAO,SAAS;AAAA,QACxB,SAAS;AAAA,QACT,cAAc;AAAA,MAChB,CAAC;AAAA,MACD,OAAO,GAAG,OAAO,SAAS;AAAA,QACxB,SAAS;AAAA,QACT,cAAc;AAAA,MAChB,CAAC;AAAA,MACD,WAAW,GAAG,OAAO,SAAS;AAAA,QAC5B,SAAS;AAAA,QACT,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,QAAI,WAAW;AACf,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ;AAAA;AAAA;", "names": []}