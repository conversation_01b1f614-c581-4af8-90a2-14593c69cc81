// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Default Schema Generator > should build schema to match the previous snapshot, locale=en 1`] = `
{
  "builder": {
    "enabled_input_param": false,
  },
  "steps": {
    "address_verification": {
      "builder": {
        "type": "address_verification",
      },
      "fields": {
        "utility_bill_electricity": "utility_bill_electricity",
        "utility_bill_telco": "utility_bill_telco",
        "utility_bill_water": "utility_bill_water",
      },
      "name": "address_verification",
      "sections": {
        "address_verification": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "utility_bill_electricity": {
              "bill_type": "electric_bill",
              "builder": {
                "type": "utility_bill",
              },
              "display": {
                "label": "Electricity bills",
              },
              "fields": {
                "validation_pass": "utility_bill_electricity_validation_pass",
              },
              "items": {
                "utility_bill_electricity_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Utility Bills Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "utility_bill_electricity_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "Please upload Electricity bills",
                  },
                  "validator_rule": "required|accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "utility_bill_electricity",
              "type": "UtilityBill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "utility_bill_telco": {
              "bill_type": "telco_bill",
              "builder": {
                "type": "utility_bill",
              },
              "display": {
                "label": "Telco bills",
              },
              "fields": {
                "validation_pass": "utility_bill_telco_validation_pass",
              },
              "items": {
                "utility_bill_telco_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Utility Bills Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "utility_bill_telco_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "Please upload Telco bills",
                  },
                  "validator_rule": "required|accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "utility_bill_telco",
              "type": "UtilityBill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "utility_bill_water": {
              "bill_type": "water_bill",
              "builder": {
                "type": "utility_bill",
              },
              "display": {
                "label": "Water bills",
              },
              "fields": {
                "validation_pass": "utility_bill_water_validation_pass",
              },
              "items": {
                "utility_bill_water_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Utility Bills Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "utility_bill_water_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "Please upload Water bills",
                  },
                  "validator_rule": "required|accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "utility_bill_water",
              "type": "UtilityBill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Address Verification",
          "name": "address_verification",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "Upload receipts for address verification purposes.",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "visible": undefined,
    },
    "bank_statement": {
      "builder": {
        "type": "bank_statement",
      },
      "fields": {
        "bank_statement_uploader": "bank_statement_uploader",
      },
      "name": "bank_statement",
      "sections": {
        "bank_statement": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "bank_statement_uploader": {
              "builder": {
                "type": "bank_statement_uploader",
              },
              "configs": {
                "allowed_bank_country": "THA",
                "transaction_latest_after": 1,
                "transaction_min_month": 6,
                "transaction_oldest_before": 180,
              },
              "display": {
                "icon_left": false,
                "label": "Upload Bank Statement",
              },
              "fields": {
                "validation_pass": "bank_statement_uploader_validation_pass",
              },
              "items": {
                "bank_statement_uploader_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Bank Statement Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "bank_statement_uploader_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "Please upload bank statement that match requirements",
                  },
                  "validator_rule": "accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "bank_statement_uploader",
              "type": "BankStatement.Uploader",
              "validator_messages": {
                "required": "Please upload bank statement that match requirements",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Bank Statement",
          "name": "bank_statement",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "visible": undefined,
    },
    "ekyc_document": {
      "builder": {
        "type": "ekyc_document",
      },
      "fields": {
        "date_of_birth": "date_of_birth",
        "date_of_expiry": "date_of_expiry",
        "date_of_issue": "date_of_issue",
        "document_numbers": "document_numbers",
        "ekyc_document_item": "ekyc_document",
        "full_name": "full_name",
        "full_name_en_first_name": "full_name_en_first_name",
        "full_name_en_last_name": "full_name_en_last_name",
        "gender": "gender",
        "home_address": "home_address",
        "max_attempt_warning": "ekyc_max_attempt_warning",
        "name_prefix": "name_prefix",
      },
      "name": "ekyc_document",
      "sections": {
        "ekyc_document": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "ekyc_document_preview": {
              "builder": {
                "type": "item",
              },
              "display": {
                "label": "",
              },
              "document_type": undefined,
              "layout": "DefaultWrapper",
              "name": "ekyc_document_preview",
              "type": "EkycDocumentPreviewWrapper",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Take a Photo of ID Document",
          "name": "ekyc_document",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
        "ekyc_document_2": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "ekyc_document": {
              "autofill_map": [
                {
                  "dest": "nid",
                  "src": "nid",
                },
                {
                  "dest": "document_number",
                  "src": "document_number",
                },
                {
                  "dest": "name_prefix",
                  "src": "title",
                },
                {
                  "dest": "full_name_first_name",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "name_type",
                  },
                  "src": "firstname",
                },
                {
                  "dest": "full_name_last_name",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "name_type",
                  },
                  "src": "lastname",
                },
                {
                  "dest": "full_name_middle_name",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "name_type",
                  },
                  "src": "middlename",
                },
                {
                  "dest": "full_name_full",
                  "params": {
                    "flag_equal": "full",
                    "flag_name": "name_type",
                  },
                  "src": "fullname",
                },
                {
                  "dest": "full_name_type",
                  "src": "name_type",
                },
                {
                  "dest": "full_name_show_middle_name",
                  "src": "show_middle_name",
                },
                {
                  "dest": "date_of_birth",
                  "src": "date_of_birth",
                },
                {
                  "dest": "date_of_issue",
                  "src": "date_of_issue",
                },
                {
                  "dest": "date_of_expiry",
                  "src": "date_of_expiry",
                },
                {
                  "dest": "address_type",
                  "src": "address_validation_type",
                },
                {
                  "dest": "home_address_country",
                  "src": "address_country_code",
                },
                {
                  "dest": "home_address_full",
                  "params": {
                    "flag_equal": "full",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address",
                },
                {
                  "dest": "home_address_address_1_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_address_1",
                },
                {
                  "dest": "home_address_address_2_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_address_2",
                },
                {
                  "dest": "home_address_city_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_city",
                },
                {
                  "dest": "home_address_postal_code_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_postal_code",
                },
                {
                  "dest": "home_address_zone_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_zone",
                },
                {
                  "dest": "home_address_province",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "province",
                },
                {
                  "dest": "home_address_district",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "district",
                },
                {
                  "dest": "home_address_subdistrict",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "subdistrict",
                },
                {
                  "dest": "home_address_address",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address",
                },
                {
                  "dest": "home_address_zipcode",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "zipcode",
                },
                {
                  "dest": "gender",
                  "src": "gender_code",
                },
                {
                  "dest": "full_name_en_first_name",
                  "src": "firstname_en",
                },
                {
                  "dest": "full_name_en_last_name",
                  "src": "lastname_en",
                },
              ],
              "builder": {
                "type": "ekyc_document_item",
              },
              "configs": {
                "check_ocr_fields": undefined,
                "check_warning": true,
                "enabled_vertical_experience": true,
              },
              "display": {
                "disabled_default_preview": true,
                "label": "",
              },
              "fields": {
                "country": "ekyc_document_country",
                "document_type": "ekyc_document_type",
                "preview": false,
              },
              "items": {
                "ekyc_document_country": {
                  "builder": {
                    "type": "country",
                  },
                  "display": {
                    "label": "Country",
                    "placeholder": "Select your country",
                    "searchable": false,
                  },
                  "enum": undefined,
                  "enum_final_filter_includes": [
                    "AFG",
                    "ALB",
                    "DZA",
                    "AGO",
                    "ATG",
                    "ARG",
                    "ARM",
                    "AUS",
                    "AUT",
                    "AZE",
                    "BHS",
                    "BHR",
                    "BGD",
                    "BRB",
                    "BLR",
                    "BEL",
                    "BLZ",
                    "BEN",
                    "BMU",
                    "BTN",
                    "BOL",
                    "BIH",
                    "BWA",
                    "BRA",
                    "BRN",
                    "BGR",
                    "BFA",
                    "CPV",
                    "KHM",
                    "CMR",
                    "CAN",
                    "CAF",
                    "TCD",
                    "CHL",
                    "CHN",
                    "COL",
                    "COM",
                    "COG",
                    "CRI",
                    "HRV",
                    "CUB",
                    "CYP",
                    "CZE",
                    "CIV",
                    "DJI",
                    "DMA",
                    "DOM",
                    "ECU",
                    "EGY",
                    "SLV",
                    "GNQ",
                    "EST",
                    "SWZ",
                    "ETH",
                    "FIN",
                    "FRA",
                    "GAB",
                    "GMB",
                    "GEO",
                    "DEU",
                    "GHA",
                    "GIB",
                    "GRC",
                    "GTM",
                    "GIN",
                    "GNB",
                    "GUY",
                    "HTI",
                    "HND",
                    "HKG",
                    "HUN",
                    "IND",
                    "IDN",
                    "IRN",
                    "IRQ",
                    "IRL",
                    "ISR",
                    "ITA",
                    "JAM",
                    "JPN",
                    "JOR",
                    "KAZ",
                    "KEN",
                    "KOR",
                    "KWT",
                    "KGZ",
                    "LAO",
                    "LVA",
                    "LBN",
                    "LSO",
                    "LBR",
                    "LIE",
                    "LTU",
                    "LUX",
                    "MDG",
                    "MWI",
                    "MYS",
                    "MDV",
                    "MLI",
                    "MLT",
                    "MHL",
                    "MRT",
                    "MUS",
                    "MEX",
                    "FSM",
                    "MDA",
                    "MCO",
                    "MNG",
                    "MNE",
                    "MAR",
                    "MOZ",
                    "NAM",
                    "NPL",
                    "NLD",
                    "NZL",
                    "NIC",
                    "NGA",
                    "MKD",
                    "NOR",
                    "OMN",
                    "PAK",
                    "PSE",
                    "PAN",
                    "PNG",
                    "PRY",
                    "PER",
                    "PHL",
                    "POL",
                    "PRT",
                    "QAT",
                    "ROU",
                    "RUS",
                    "RWA",
                    "KNA",
                    "LCA",
                    "VCT",
                    "SMR",
                    "STP",
                    "SAU",
                    "SEN",
                    "SRB",
                    "SYC",
                    "SLE",
                    "SGP",
                    "SVK",
                    "SVN",
                    "SOM",
                    "ZAF",
                    "SSD",
                    "ESP",
                    "LKA",
                    "SDN",
                    "SUR",
                    "SWE",
                    "CHE",
                    "SYR",
                    "TWN",
                    "TJK",
                    "TZA",
                    "THA",
                    "TLS",
                    "TGO",
                    "TON",
                    "TTO",
                    "TUN",
                    "TUR",
                    "TCA",
                    "UGA",
                    "UKR",
                    "ARE",
                    "GBR",
                    "USA",
                    "URY",
                    "UZB",
                    "VUT",
                    "VEN",
                    "VNM",
                    "ESH",
                    "YEM",
                    "ZMB",
                    "ZWE",
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "ekyc_document_country",
                  "prefill": {
                    "disabled": true,
                  },
                  "type": "CountrySelect",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "ekyc_document_type": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "disabled_default_preview": true,
                    "label": "Document type",
                    "reverse_direction": true,
                  },
                  "enum": [
                    {
                      "icon": "id-card",
                      "label": "ID Card",
                      "value": "front_card",
                    },
                    {
                      "icon": "passport",
                      "label": "Passport",
                      "value": "passport",
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "ekyc_document_type",
                  "prefill": {
                    "value": "front_card",
                  },
                  "type": "InputRadioStaticIcon",
                  "validator_rule": undefined,
                  "visible": {
                    "ekyc_document_country": "required",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "max_attempt_warning": {
                "allow_max_attempt_pass": false,
                "max_attempt_count": 5,
              },
              "name": "ekyc_document",
              "selector_field": "ekyc_document_type",
              "type": "Ekyc.Document",
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "ekyc_max_attempt_warning": {
              "builder": {
                "type": "ekyc_max_attempt_warning",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "back_btn": "back_btn",
                "bring_navbar_front": "bring_navbar_front",
                "cross_device_content": "cross_device_content",
                "warning_content": "warning_content",
              },
              "items": {
                "back_btn": {
                  "display": {
                    "hide_label": true,
                    "label": "Go to Home Page",
                  },
                  "layout": "InputControl",
                  "name": "back_btn",
                  "target": "/page/test",
                  "type": "TargetButton",
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                },
                "bring_navbar_front": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<style>
.app__dynamic-form .navbar {
    z-index: 100000!important;
}
</style>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "bring_navbar_front",
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "cross_device_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{cross_device_content.title}}</div>
          <div class="warning-text subtitle">{{_,ekyc.recorder.error_card_max_attempt_desc_cross_device|toI18n}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "cross_device_content",
                  "prefill": {
                    "value": {
                      "title": "ID verification failed",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "required|is:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{warning_content.title}}</div> 
          <div class="warning-text subtitle">{{warning_content.description}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_content",
                  "prefill": {
                    "value": {
                      "description": "Please contact the customer support.",
                      "title": "ID verification failed",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_image": {
                  "builder": {
                    "type": "image_preset",
                  },
                  "display": {
                    "image_name": "telephone",
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_image",
                  "styling": {
                    "--alignment": "center",
                    "--height": "auto",
                    "--width": "50%",
                  },
                  "type": "StaticImage",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "FullscreenWrapper",
              "name": "ekyc_max_attempt_warning",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": {
                "ekyc_document_max_attempt": "required",
              },
              "visible_flag_invert": undefined,
            },
          },
          "label": "Select the country and document type",
          "name": "ekyc_document_2",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
        "ekyc_document_3": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "date_of_birth": {
              "builder": {
                "type": "date",
              },
              "display": {
                "date_order": "dmy",
                "label": "Date of birth",
                "year_format": "ce",
              },
              "fields": {
                "allow_no_day": undefined,
                "allow_no_month": undefined,
                "allow_no_year": undefined,
              },
              "layout": "InputControl",
              "name": "date_of_birth",
              "store_date_format": "yyyy-MM-dd",
              "type": "InputDate",
              "validator_rule": "required",
              "value_constraints": {
                "from": {
                  "exclusive": false,
                  "today": {
                    "years": -100,
                  },
                },
                "reversed": {
                  "year": true,
                },
                "to": {
                  "exclusive": false,
                  "today": {},
                },
              },
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "date_of_expiry": {
              "builder": {
                "type": "date",
              },
              "display": {
                "date_order": "dmy",
                "label": "Date of expiry",
                "year_format": "ce",
              },
              "fields": {
                "allow_no_day": undefined,
                "allow_no_month": undefined,
                "allow_no_year": undefined,
              },
              "layout": "InputControl",
              "name": "date_of_expiry",
              "store_date_format": "yyyy-MM-dd",
              "type": "InputDate",
              "validator_rule": "required",
              "value_constraints": {
                "from": {
                  "exclusive": false,
                  "today": {},
                },
                "reversed": {
                  "year": false,
                },
                "to": {
                  "exclusive": false,
                  "today": {
                    "years": 100,
                  },
                },
              },
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "date_of_issue": {
              "builder": {
                "type": "date",
              },
              "display": {
                "date_order": "dmy",
                "label": "Date of issue",
                "year_format": "ce",
              },
              "fields": {
                "allow_no_day": undefined,
                "allow_no_month": undefined,
                "allow_no_year": undefined,
              },
              "layout": "InputControl",
              "name": "date_of_issue",
              "store_date_format": "yyyy-MM-dd",
              "type": "InputDate",
              "validator_rule": "required",
              "value_constraints": {
                "from": {
                  "exclusive": false,
                  "today": {
                    "years": -100,
                  },
                },
                "reversed": {
                  "year": true,
                },
                "to": {
                  "exclusive": false,
                  "today": {},
                },
              },
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "document_numbers": {
              "builder": {
                "type": "document_numbers",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "document_number": "document_number",
                "nid": "nid",
              },
              "items": {
                "document_number": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Document Number",
                    "mask": undefined,
                    "placeholder": "Document Number",
                  },
                  "layout": "InputControl",
                  "name": "document_number",
                  "props": {
                    "autocomplete": "off",
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required",
                  "visible": {
                    "ekyc_document_country": "required|is:THA",
                    "ekyc_document_type": "required|is:front_card",
                  },
                  "visible_flag_invert": true,
                },
                "nid": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "ID Card Number",
                    "mask": "national_id",
                    "placeholder": "ID Card Number",
                  },
                  "layout": "InputControl",
                  "name": "nid",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if_condition:ekyc_document_country,=,THA,ekyc_document_country,=,front_card|regex:/^[0-9]{13}$/i|pass_checksum",
                  "visible": {
                    "ekyc_document_country": "required|is:THA",
                    "ekyc_document_type": "required|is:front_card",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "document_numbers",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "full_name": {
              "builder": {
                "type": "full_name",
              },
              "display": {
                "hide_label": true,
                "label": "Please enter your name",
              },
              "fields": {
                "first_name": "full_name_first_name",
                "full": "full_name_full",
                "last_name": "full_name_last_name",
                "middle_name": "full_name_middle_name",
                "prefix": false,
                "show_middle_name": "full_name_show_middle_name",
                "type": "full_name_type",
              },
              "items": {
                "full_name_first_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "First name",
                    "mask": undefined,
                    "placeholder": "Type your answer here",
                  },
                  "layout": "InputControl",
                  "name": "full_name_first_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_unless": "Answer do not matches the selected format",
                  },
                  "validator_rule": "required_unless:full_name_type,full",
                  "visible": {
                    "full_name_type": "not_in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_full": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Full name",
                    "mask": undefined,
                    "placeholder": "Please enter your full name",
                  },
                  "layout": "InputControl",
                  "name": "full_name_full",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_if": "Please Enter Your Full Name",
                  },
                  "validator_rule": "required_if:full_name_type,full",
                  "visible": {
                    "full_name_type": "required|is:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_last_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Last name",
                    "mask": undefined,
                    "placeholder": "Type your answer here",
                  },
                  "layout": "InputControl",
                  "name": "full_name_last_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_unless": "Answer do not matches the selected format",
                  },
                  "validator_rule": "required_unless:full_name_type,full",
                  "visible": {
                    "full_name_type": "not_in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_middle_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Middle name",
                    "mask": undefined,
                    "placeholder": "Type your answer here",
                  },
                  "layout": "InputControl",
                  "name": "full_name_middle_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_if_condition": "Answer do not matches the selected format",
                  },
                  "validator_rule": "required_if_condition:full_name_type,!=,full,full_name_show_middle_name,=,true",
                  "visible": {
                    "full_name_show_middle_name": "required:accepted",
                    "full_name_type": "not_in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_show_middle_name": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "label": "Show Middle Name",
                  },
                  "enum": [
                    {
                      "label": "Option 1",
                      "value": true,
                    },
                    {
                      "label": "Option 2",
                      "value": false,
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "full_name_show_middle_name",
                  "type": "InputRadio",
                  "validator_rule": undefined,
                  "visible": {
                    "show_middle_name_flag": "required|accepted",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_type": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "label": "Name Type",
                  },
                  "enum": [
                    {
                      "label": "Option 1",
                      "value": "parts",
                    },
                    {
                      "label": "Option 2",
                      "value": "full",
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "full_name_type",
                  "type": "InputRadio",
                  "validator_rule": undefined,
                  "visible": {
                    "show_name_type": "required|accepted",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "full_name",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "full_name_en_first_name": {
              "builder": {
                "type": "data_field",
              },
              "display": {
                "hide_label": true,
                "label": "First name (EN)",
              },
              "layout": "DefaultWrapper",
              "name": "full_name_en_first_name",
              "type": "DataField",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "full_name_en_last_name": {
              "builder": {
                "type": "data_field",
              },
              "display": {
                "hide_label": true,
                "label": "Last name (EN)",
              },
              "layout": "DefaultWrapper",
              "name": "full_name_en_last_name",
              "type": "DataField",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "gender": {
              "boolean": undefined,
              "builder": {
                "type": "gender",
              },
              "display": {
                "label": "Gender",
              },
              "enum": [
                {
                  "label": "Male",
                  "value": "M",
                },
                {
                  "label": "Female",
                  "value": "F",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "gender",
              "type": "SingleSelectButton",
              "validator_messages": {
                "required": "Please select your option",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "home_address": {
              "builder": {
                "type": "address",
              },
              "display": {
                "label": "Address",
              },
              "fields": {
                "address": "home_address_address",
                "address_1_common": "home_address_address_1_common",
                "address_2_common": "home_address_address_2_common",
                "address_reference": false,
                "city_common": "home_address_city_common",
                "country": "home_address_country",
                "district": "home_address_district",
                "full": "home_address_full",
                "postal_code_common": "home_address_postal_code_common",
                "province": "home_address_province",
                "subdistrict": "home_address_subdistrict",
                "type": "address_type",
                "zipcode": "home_address_zipcode",
                "zone_common": "home_address_zone_common",
              },
              "items": {
                "address_type": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "label": "Address Type",
                  },
                  "enum": [
                    {
                      "label": "Option 1",
                      "value": "thai",
                    },
                    {
                      "label": "Option 2",
                      "value": "parts",
                    },
                    {
                      "label": "Option 3",
                      "value": "full",
                    },
                    {
                      "label": "schema.item.choice.options.3.label",
                      "value": "empty",
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "address_type",
                  "type": "InputRadio",
                  "validator_rule": undefined,
                  "visible": {
                    "show_address_type": "required|accepted",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_address": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Street Address",
                    "mask": undefined,
                    "placeholder": "Street Address",
                  },
                  "layout": "InputControl",
                  "name": "home_address_address",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_messages": {
                    "required_if": "The Street Address field is required.",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_address_1_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 1",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_address_1_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_address_2_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 2",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_address_2_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_city_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "City",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_city_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_country": {
                  "builder": {
                    "type": "country",
                  },
                  "display": {
                    "label": "Country",
                    "placeholder": "Country",
                    "searchable": true,
                  },
                  "enum": undefined,
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_country",
                  "prefill": {
                    "value": "THA",
                  },
                  "type": "CountrySelect",
                  "validator_rule": "required",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "home_address_district": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "District",
                    "placeholder": "District",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_district",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "The District field is required.",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_full": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address (Full)",
                    "mask": undefined,
                    "placeholder": "Please enter your address",
                  },
                  "layout": "InputControl",
                  "name": "home_address_full",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_messages": {
                    "required_if": "Please enter your address",
                  },
                  "validator_rule": "required_if:address_type,full",
                  "visible": {
                    "address_type": "required|in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_postal_code_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Postal code",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_postal_code_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_province": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "Province",
                    "placeholder": "Province",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_province",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "The Province field is required.",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_subdistrict": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "Sub-district",
                    "placeholder": "Sub-district",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_subdistrict",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "The Sub-district field is required.",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_zipcode": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "Postal / Zip Code",
                    "placeholder": "Postal / Zip Code",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_zipcode",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "The Postal / Zip Code field is required.",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_zone_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Zone",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_zone_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "home_address",
              "type": "AddressAutofill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "name_prefix": {
              "boolean": undefined,
              "builder": {
                "type": "name_prefix",
              },
              "display": {
                "label": "Name Prefix",
              },
              "enum": [
                {
                  "label": "Mr.",
                  "value": "Mr.",
                },
                {
                  "label": "Miss",
                  "value": "Miss",
                },
                {
                  "label": "Mrs.",
                  "value": "Mrs.",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "name_prefix",
              "type": "SingleSelectButton",
              "validator_messages": {
                "required": "Please select your option",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ID Document Details",
          "name": "ekyc_document_3",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "Ekyc",
      "visible": undefined,
    },
    "ekyc_liveness": {
      "builder": {
        "type": "ekyc_liveness",
      },
      "fields": {
        "ekyc_liveness_item": "ekyc_liveness",
        "max_attempt_warning": "ekyc_max_attempt_warning",
      },
      "name": "ekyc_liveness",
      "sections": {
        "ekyc_liveness": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "ekyc_liveness": {
              "builder": {
                "type": "ekyc_liveness_item",
              },
              "display": {
                "label": "",
              },
              "layout": "InputControl",
              "liveness": {
                "actionIconSetName": "smiley",
                "enableFaceSize": true,
                "enableIdleOnly": true,
              },
              "max_attempt_warning": {
                "allow_max_attempt_pass": false,
                "max_attempt_count": 5,
              },
              "name": "ekyc_liveness",
              "override_messages": {
                "ekyc": {
                  "liveness": {
                    "guide_subtitle": "Follow the instruction on the top of the screen.",
                  },
                  "video": {
                    "description": "Make sure that your face is in the frame and clearly visible.",
                    "title": "",
                  },
                },
              },
              "type": "Ekyc.Liveness",
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "ekyc_max_attempt_warning": {
              "builder": {
                "type": "ekyc_max_attempt_warning",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "back_btn": "back_btn",
                "bring_navbar_front": "bring_navbar_front",
                "cross_device_content": "cross_device_content",
                "warning_content": "warning_content",
              },
              "items": {
                "back_btn": {
                  "display": {
                    "hide_label": true,
                    "label": "Go to Home Page",
                  },
                  "layout": "InputControl",
                  "name": "back_btn",
                  "target": "/page/test",
                  "type": "TargetButton",
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                },
                "bring_navbar_front": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<style>
.app__dynamic-form .navbar {
    z-index: 100000!important;
}
</style>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "bring_navbar_front",
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "cross_device_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{cross_device_content.title}}</div>
          <div class="warning-text subtitle">{{_,ekyc.recorder.error_card_max_attempt_desc_cross_device|toI18n}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "cross_device_content",
                  "prefill": {
                    "value": {
                      "title": "Liveness verification failed",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "required|is:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{warning_content.title}}</div> 
          <div class="warning-text subtitle">{{warning_content.description}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_content",
                  "prefill": {
                    "value": {
                      "description": "Please contact the customer support.",
                      "title": "Liveness verification failed",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_image": {
                  "builder": {
                    "type": "image_preset",
                  },
                  "display": {
                    "image_name": "telephone",
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_image",
                  "styling": {
                    "--alignment": "center",
                    "--height": "auto",
                    "--width": "50%",
                  },
                  "type": "StaticImage",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "FullscreenWrapper",
              "name": "ekyc_max_attempt_warning",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": {
                "ekyc_liveness_max_attempt": "required",
              },
              "visible_flag_invert": undefined,
            },
          },
          "label": "Prepare to take a Selfie",
          "name": "ekyc_liveness",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "Ekyc",
      "visible": undefined,
    },
    "email": {
      "builder": {
        "type": "email",
      },
      "fields": {
        "otp_address": "email_otp_address",
        "otp_verify": "email_otp_verify",
      },
      "name": "email",
      "sections": {
        "email": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "email_image_1": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "email-verification",
                "label": "",
              },
              "layout": "InputControl",
              "name": "email_image_1",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "email_otp_address": {
              "builder": {
                "type": "email",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "confirmation": false,
                "email": "email_otp_address_email",
              },
              "items": {
                "email_otp_address_email": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "configs": {
                    "transform_value": [
                      "toLowerCase",
                    ],
                  },
                  "display": {
                    "hide_label": true,
                    "icon_right": "feather:mail",
                    "label": "Email",
                    "mask": undefined,
                    "placeholder": "<EMAIL>",
                  },
                  "layout": "InputControl",
                  "name": "email_otp_address_email",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required": "Please provide your e-mail",
                  },
                  "validator_rule": "required|email",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "email_otp_address",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Verify your Email",
          "name": "email",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "Enter a valid email to receive <b>Validation Code</b>.",
        },
        "email_2": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "email_image_2": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "email-verification-otp",
                "label": "",
              },
              "layout": "InputControl",
              "name": "email_image_2",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "email_otp_verify": {
              "builder": {
                "type": "otp_email",
              },
              "configs": {},
              "display": {
                "error_template": {
                  "item": false,
                  "section": false,
                },
                "label": "",
              },
              "email_field": "email_otp_address_email",
              "layout": "FullWidthWrapper",
              "name": "email_otp_verify",
              "override_heading": true,
              "override_subheading": true,
              "type": "OTP.VerifyEmail",
              "validator_messages": {
                "required": "Please enter OTP code",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Verify your Email",
          "name": "email_2",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "Enter a valid email to receive <b>Validation Code</b>.",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "OTP",
      "visible": undefined,
    },
    "personal": {
      "name": "personal",
      "sections": {
        "personal": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "contact_address": {
              "builder": {
                "type": "address",
              },
              "display": {
                "label": "Address",
              },
              "fields": {
                "address": "contact_address_address",
                "address_1_common": "contact_address_address_1_common",
                "address_2_common": "contact_address_address_2_common",
                "address_reference": false,
                "city_common": "contact_address_city_common",
                "country": "contact_address_country",
                "district": "contact_address_district",
                "full": "contact_address_full",
                "postal_code_common": "contact_address_postal_code_common",
                "province": "contact_address_province",
                "subdistrict": "contact_address_subdistrict",
                "type": false,
                "zipcode": "contact_address_zipcode",
                "zone_common": "contact_address_zone_common",
              },
              "items": {
                "contact_address_address": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Street Address",
                    "mask": undefined,
                    "placeholder": "Street Address",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_address",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_address_1_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 1",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_address_1_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_address_2_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 2",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_address_2_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_city_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "City",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_city_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_country": {
                  "builder": {
                    "type": "country",
                  },
                  "display": {
                    "label": "Country",
                    "placeholder": "Country",
                    "searchable": true,
                  },
                  "enum": undefined,
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_country",
                  "prefill": {
                    "value": "THA",
                  },
                  "type": "CountrySelect",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_district": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "District",
                    "placeholder": "District",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_district",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_full": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address (Full)",
                    "mask": undefined,
                    "placeholder": "Please enter your address",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_full",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": {
                    "contact_address_type": "required|in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "contact_address_postal_code_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Postal code",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_postal_code_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_province": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "Province",
                    "placeholder": "Province",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_province",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_subdistrict": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "Sub-district",
                    "placeholder": "Sub-district",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_subdistrict",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_zipcode": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "Postal / Zip Code",
                    "placeholder": "Postal / Zip Code",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_zipcode",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_zone_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Zone",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_zone_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "contact_address",
              "type": "AddressAutofill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "contact_full_name": {
              "builder": {
                "type": "full_name",
              },
              "display": {
                "label": "Please enter your name",
              },
              "fields": {
                "first_name": "contact_full_name_first_name",
                "full": false,
                "last_name": "contact_full_name_last_name",
                "middle_name": false,
                "prefix": false,
                "show_middle_name": false,
                "type": false,
              },
              "items": {
                "contact_full_name_first_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "First Name",
                    "mask": undefined,
                    "placeholder": "Your First Name",
                  },
                  "layout": "InputControl",
                  "name": "contact_full_name_first_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required": "Please Enter Your First Name",
                  },
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_full_name_last_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Last Name",
                    "mask": undefined,
                    "placeholder": "Your Last Name",
                  },
                  "layout": "InputControl",
                  "name": "contact_full_name_last_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required": "Please Enter Your Last Name",
                  },
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "contact_full_name",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Contact Details",
          "name": "personal",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "visible": undefined,
    },
    "telephone": {
      "builder": {
        "type": "telephone",
      },
      "fields": {
        "otp_address": "telephone_otp_address",
        "otp_verify": "telephone_otp_verify",
      },
      "name": "telephone",
      "sections": {
        "telephone": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "telephone_image_1": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "mobile-verification",
                "label": "",
              },
              "layout": "InputControl",
              "name": "telephone_image_1",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "telephone_otp_address": {
              "builder": {
                "type": "telephone",
              },
              "configs": {
                "accepted_countries": [
                  "HK",
                  "ID",
                  "PH",
                  "TH",
                ],
              },
              "display": {
                "hide_label": true,
                "icon_right": "feather:smartphone",
                "label": "Phone number",
                "placeholder": "Phone Number",
              },
              "layout": "InputControl",
              "name": "telephone_otp_address",
              "type": "InputTelephone",
              "validator_messages": {
                "required": "Please provide phone number",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Verify your Phone Number",
          "name": "telephone",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "Enter a valid mobile phone number to receive <b>OTP</b>",
        },
        "telephone_2": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "telephone_image_2": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "mobile-verification-otp",
                "label": "",
              },
              "layout": "InputControl",
              "name": "telephone_image_2",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "telephone_otp_verify": {
              "builder": {
                "type": "otp_item",
              },
              "configs": {},
              "digits": 6,
              "display": {
                "error_template": {
                  "item": false,
                  "section": true,
                },
                "label": "",
              },
              "layout": "FullWidthWrapper",
              "name": "telephone_otp_verify",
              "override_heading": true,
              "override_subheading": true,
              "tel_field": "telephone_otp_address",
              "type": "OTP.Verify",
              "validator_messages": {
                "required": "Please enter OTP code",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "Verify your Phone Number",
          "name": "telephone_2",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "Enter a valid mobile phone number to receive <b>OTP</b>",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "OTP",
      "visible": undefined,
    },
  },
}
`;

exports[`Default Schema Generator > should build schema to match the previous snapshot, locale=th 1`] = `
{
  "builder": {
    "enabled_input_param": false,
  },
  "steps": {
    "address_verification": {
      "builder": {
        "type": "address_verification",
      },
      "fields": {
        "utility_bill_electricity": "utility_bill_electricity",
        "utility_bill_telco": "utility_bill_telco",
        "utility_bill_water": "utility_bill_water",
      },
      "name": "address_verification",
      "sections": {
        "address_verification": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "utility_bill_electricity": {
              "bill_type": "electric_bill",
              "builder": {
                "type": "utility_bill",
              },
              "display": {
                "label": "ใบแจ้งหนี้ค่าไฟฟ้า",
              },
              "fields": {
                "validation_pass": "utility_bill_electricity_validation_pass",
              },
              "items": {
                "utility_bill_electricity_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Utility Bills Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "utility_bill_electricity_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "กรุณาอัปโหลดใบแจ้งหนี้ค่าไฟฟ้า",
                  },
                  "validator_rule": "required|accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "utility_bill_electricity",
              "type": "UtilityBill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "utility_bill_telco": {
              "bill_type": "telco_bill",
              "builder": {
                "type": "utility_bill",
              },
              "display": {
                "label": "ใบแจ้งหนี้ค่าโทรศัพท์",
              },
              "fields": {
                "validation_pass": "utility_bill_telco_validation_pass",
              },
              "items": {
                "utility_bill_telco_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Utility Bills Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "utility_bill_telco_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "กรุณาอัปโหลดใบแจ้งหนี้ค่าโทรศัพท์",
                  },
                  "validator_rule": "required|accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "utility_bill_telco",
              "type": "UtilityBill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "utility_bill_water": {
              "bill_type": "water_bill",
              "builder": {
                "type": "utility_bill",
              },
              "display": {
                "label": "ใบแจ้งหนี้ค่าน้ำประปา",
              },
              "fields": {
                "validation_pass": "utility_bill_water_validation_pass",
              },
              "items": {
                "utility_bill_water_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Utility Bills Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "utility_bill_water_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "กรุณาอัปโหลดใบแจ้งหนี้ค่าน้ำประปา",
                  },
                  "validator_rule": "required|accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "utility_bill_water",
              "type": "UtilityBill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ยืนยันที่อยู่ของคุณ",
          "name": "address_verification",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "อัปโหลดใบเสร็จเพื่อใช้ในการตรวจสอบที่อยู่",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "visible": undefined,
    },
    "bank_statement": {
      "builder": {
        "type": "bank_statement",
      },
      "fields": {
        "bank_statement_uploader": "bank_statement_uploader",
      },
      "name": "bank_statement",
      "sections": {
        "bank_statement": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "bank_statement_uploader": {
              "builder": {
                "type": "bank_statement_uploader",
              },
              "configs": {
                "allowed_bank_country": "THA",
                "transaction_latest_after": 1,
                "transaction_min_month": 6,
                "transaction_oldest_before": 180,
              },
              "display": {
                "icon_left": false,
                "label": "อัปโหลดรายการเดินบัญชี",
              },
              "fields": {
                "validation_pass": "bank_statement_uploader_validation_pass",
              },
              "items": {
                "bank_statement_uploader_validation_pass": {
                  "builder": {
                    "type": "data_field",
                  },
                  "display": {
                    "label": "Bank Statement Validation Passed",
                  },
                  "layout": "DefaultWrapper",
                  "name": "bank_statement_uploader_validation_pass",
                  "props": {
                    "disabled": true,
                  },
                  "type": "DataField",
                  "validator_messages": {
                    "accepted": "โปรดอัปโหลดรายการเดินบัญชีที่ตรงกับข้อกำหนด",
                  },
                  "validator_rule": "accepted",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "UploaderWrapper",
              "name": "bank_statement_uploader",
              "type": "BankStatement.Uploader",
              "validator_messages": {
                "required": "โปรดอัปโหลดรายการเดินบัญชีที่ตรงกับข้อกำหนด",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "รายการเดินบัญชี",
          "name": "bank_statement",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "visible": undefined,
    },
    "ekyc_document": {
      "builder": {
        "type": "ekyc_document",
      },
      "fields": {
        "date_of_birth": "date_of_birth",
        "date_of_expiry": "date_of_expiry",
        "date_of_issue": "date_of_issue",
        "document_numbers": "document_numbers",
        "ekyc_document_item": "ekyc_document",
        "full_name": "full_name",
        "full_name_en_first_name": "full_name_en_first_name",
        "full_name_en_last_name": "full_name_en_last_name",
        "gender": "gender",
        "home_address": "home_address",
        "max_attempt_warning": "ekyc_max_attempt_warning",
        "name_prefix": "name_prefix",
      },
      "name": "ekyc_document",
      "sections": {
        "ekyc_document": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "ekyc_document_preview": {
              "builder": {
                "type": "item",
              },
              "display": {
                "label": "",
              },
              "document_type": undefined,
              "layout": "DefaultWrapper",
              "name": "ekyc_document_preview",
              "type": "EkycDocumentPreviewWrapper",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ถ่ายภาพเอกสารยืนยันตัวตน",
          "name": "ekyc_document",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
        "ekyc_document_2": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "ekyc_document": {
              "autofill_map": [
                {
                  "dest": "nid",
                  "src": "nid",
                },
                {
                  "dest": "document_number",
                  "src": "document_number",
                },
                {
                  "dest": "name_prefix",
                  "src": "title",
                },
                {
                  "dest": "full_name_first_name",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "name_type",
                  },
                  "src": "firstname",
                },
                {
                  "dest": "full_name_last_name",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "name_type",
                  },
                  "src": "lastname",
                },
                {
                  "dest": "full_name_middle_name",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "name_type",
                  },
                  "src": "middlename",
                },
                {
                  "dest": "full_name_full",
                  "params": {
                    "flag_equal": "full",
                    "flag_name": "name_type",
                  },
                  "src": "fullname",
                },
                {
                  "dest": "full_name_type",
                  "src": "name_type",
                },
                {
                  "dest": "full_name_show_middle_name",
                  "src": "show_middle_name",
                },
                {
                  "dest": "date_of_birth",
                  "src": "date_of_birth",
                },
                {
                  "dest": "date_of_issue",
                  "src": "date_of_issue",
                },
                {
                  "dest": "date_of_expiry",
                  "src": "date_of_expiry",
                },
                {
                  "dest": "address_type",
                  "src": "address_validation_type",
                },
                {
                  "dest": "home_address_country",
                  "src": "address_country_code",
                },
                {
                  "dest": "home_address_full",
                  "params": {
                    "flag_equal": "full",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address",
                },
                {
                  "dest": "home_address_address_1_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_address_1",
                },
                {
                  "dest": "home_address_address_2_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_address_2",
                },
                {
                  "dest": "home_address_city_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_city",
                },
                {
                  "dest": "home_address_postal_code_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_postal_code",
                },
                {
                  "dest": "home_address_zone_common",
                  "params": {
                    "flag_equal": "parts",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address_zone",
                },
                {
                  "dest": "home_address_province",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "province",
                },
                {
                  "dest": "home_address_district",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "district",
                },
                {
                  "dest": "home_address_subdistrict",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "subdistrict",
                },
                {
                  "dest": "home_address_address",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "address",
                },
                {
                  "dest": "home_address_zipcode",
                  "params": {
                    "flag_equal": "thai",
                    "flag_name": "address_validation_type",
                  },
                  "src": "zipcode",
                },
                {
                  "dest": "gender",
                  "src": "gender_code",
                },
                {
                  "dest": "full_name_en_first_name",
                  "src": "firstname_en",
                },
                {
                  "dest": "full_name_en_last_name",
                  "src": "lastname_en",
                },
              ],
              "builder": {
                "type": "ekyc_document_item",
              },
              "configs": {
                "check_ocr_fields": undefined,
                "check_warning": true,
                "enabled_vertical_experience": true,
              },
              "display": {
                "disabled_default_preview": true,
                "label": "",
              },
              "fields": {
                "country": "ekyc_document_country",
                "document_type": "ekyc_document_type",
                "preview": false,
              },
              "items": {
                "ekyc_document_country": {
                  "builder": {
                    "type": "country",
                  },
                  "display": {
                    "label": "ประเทศ",
                    "placeholder": "กรุณาเลือกประเทศของคุณ",
                    "searchable": false,
                  },
                  "enum": undefined,
                  "enum_final_filter_includes": [
                    "GRC",
                    "KHM",
                    "GTM",
                    "QAT",
                    "GHA",
                    "GAB",
                    "CPV",
                    "GUY",
                    "GIN",
                    "GNB",
                    "COG",
                    "CRI",
                    "COM",
                    "KAZ",
                    "CUB",
                    "KGZ",
                    "KWT",
                    "GEO",
                    "JOR",
                    "JAM",
                    "DJI",
                    "CHN",
                    "TCD",
                    "CHL",
                    "SMR",
                    "SAU",
                    "ZWE",
                    "SDN",
                    "SSD",
                    "SUR",
                    "JPN",
                    "TTO",
                    "TON",
                    "TLS",
                    "TUR",
                    "TUN",
                    "TJK",
                    "NOR",
                    "NAM",
                    "NIC",
                    "NZL",
                    "BRA",
                    "BWA",
                    "BIH",
                    "BGD",
                    "BGR",
                    "BRB",
                    "BHR",
                    "BHS",
                    "BFA",
                    "PAK",
                    "PAN",
                    "PNG",
                    "PRY",
                    "FRA",
                    "FIN",
                    "PHL",
                    "BTN",
                    "MNG",
                    "MNE",
                    "MRT",
                    "MLT",
                    "MDV",
                    "MDG",
                    "MLI",
                    "MYS",
                    "GIB",
                    "UGA",
                    "UKR",
                    "RWA",
                    "PSE",
                    "BOL",
                    "LUX",
                    "LVA",
                    "LIE",
                    "LTU",
                    "VUT",
                    "LKA",
                    "SWZ",
                    "CHE",
                    "SWE",
                    "RUS",
                    "NPL",
                    "ARE",
                    "USA",
                    "DOM",
                    "GBR",
                    "TZA",
                    "LAO",
                    "MDA",
                    "MKD",
                    "MWI",
                    "SYR",
                    "IRN",
                    "CZE",
                    "CAF",
                    "ZAF",
                    "VEN",
                    "POL",
                    "SGP",
                    "ESP",
                    "SVK",
                    "SVN",
                    "MHL",
                    "TCA",
                    "AUT",
                    "AUS",
                    "AFG",
                    "ARG",
                    "AZE",
                    "ARM",
                    "ITA",
                    "IND",
                    "IDN",
                    "IRQ",
                    "ISR",
                    "GNQ",
                    "EGY",
                    "UZB",
                    "URY",
                    "HND",
                    "HUN",
                    "HKG",
                    "KOR",
                    "KEN",
                    "LCA",
                    "VCT",
                    "KNA",
                    "SRB",
                    "STP",
                    "SLE",
                    "SYC",
                    "SEN",
                    "BRN",
                    "NLD",
                    "BEN",
                    "BLR",
                    "BLZ",
                    "BEL",
                    "BMU",
                    "PER",
                    "MUS",
                    "MEX",
                    "DEU",
                    "YEM",
                    "LBN",
                    "LSO",
                    "ESH",
                    "VNM",
                    "ECU",
                    "ETH",
                    "SLV",
                    "EST",
                    "HTI",
                    "GMB",
                    "CAN",
                    "CMR",
                    "ZMB",
                    "AGO",
                    "ATG",
                    "DZA",
                    "ALB",
                    "CIV",
                    "HRV",
                    "COL",
                    "SOM",
                    "DMA",
                    "TGO",
                    "PRT",
                    "MOZ",
                    "MCO",
                    "MAR",
                    "ROU",
                    "OMN",
                    "CYP",
                    "TWN",
                    "THA",
                    "NGA",
                    "FSM",
                    "LBR",
                    "IRL",
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "ekyc_document_country",
                  "prefill": {
                    "disabled": true,
                  },
                  "type": "CountrySelect",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "ekyc_document_type": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "disabled_default_preview": true,
                    "label": "ประเภทเอกสาร",
                    "reverse_direction": true,
                  },
                  "enum": [
                    {
                      "icon": "id-card",
                      "label": "บัตรประชาชน",
                      "value": "front_card",
                    },
                    {
                      "icon": "passport",
                      "label": "พาสปอร์ต",
                      "value": "passport",
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "ekyc_document_type",
                  "prefill": {
                    "value": "front_card",
                  },
                  "type": "InputRadioStaticIcon",
                  "validator_rule": undefined,
                  "visible": {
                    "ekyc_document_country": "required",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "max_attempt_warning": {
                "allow_max_attempt_pass": false,
                "max_attempt_count": 5,
              },
              "name": "ekyc_document",
              "selector_field": "ekyc_document_type",
              "type": "Ekyc.Document",
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "ekyc_max_attempt_warning": {
              "builder": {
                "type": "ekyc_max_attempt_warning",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "back_btn": "back_btn",
                "bring_navbar_front": "bring_navbar_front",
                "cross_device_content": "cross_device_content",
                "warning_content": "warning_content",
              },
              "items": {
                "back_btn": {
                  "display": {
                    "hide_label": true,
                    "label": "กลับหน้าหลัก",
                  },
                  "layout": "InputControl",
                  "name": "back_btn",
                  "target": "/page/test",
                  "type": "TargetButton",
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                },
                "bring_navbar_front": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<style>
.app__dynamic-form .navbar {
    z-index: 100000!important;
}
</style>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "bring_navbar_front",
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "cross_device_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{cross_device_content.title}}</div>
          <div class="warning-text subtitle">{{_,ekyc.recorder.error_card_max_attempt_desc_cross_device|toI18n}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "cross_device_content",
                  "prefill": {
                    "value": {
                      "title": "ไม่สามารถตรวจเอกสารยืนยันตัวตนได้",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "required|is:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{warning_content.title}}</div> 
          <div class="warning-text subtitle">{{warning_content.description}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_content",
                  "prefill": {
                    "value": {
                      "description": "กรุณาติดต่อฝ่ายบริการลูกค้า",
                      "title": "ไม่สามารถตรวจเอกสารยืนยันตัวตนได้",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_image": {
                  "builder": {
                    "type": "image_preset",
                  },
                  "display": {
                    "image_name": "telephone",
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_image",
                  "styling": {
                    "--alignment": "center",
                    "--height": "auto",
                    "--width": "50%",
                  },
                  "type": "StaticImage",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "FullscreenWrapper",
              "name": "ekyc_max_attempt_warning",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": {
                "ekyc_document_max_attempt": "required",
              },
              "visible_flag_invert": undefined,
            },
          },
          "label": "เลือกประเทศและประเภทเอกสาร",
          "name": "ekyc_document_2",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
        "ekyc_document_3": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "date_of_birth": {
              "builder": {
                "type": "date",
              },
              "display": {
                "date_order": "dmy",
                "label": "วันเกิด",
                "year_format": "be",
              },
              "fields": {
                "allow_no_day": undefined,
                "allow_no_month": undefined,
                "allow_no_year": undefined,
              },
              "layout": "InputControl",
              "name": "date_of_birth",
              "store_date_format": "yyyy-MM-dd",
              "type": "InputDate",
              "validator_rule": "required",
              "value_constraints": {
                "from": {
                  "exclusive": false,
                  "today": {
                    "years": -100,
                  },
                },
                "reversed": {
                  "year": true,
                },
                "to": {
                  "exclusive": false,
                  "today": {},
                },
              },
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "date_of_expiry": {
              "builder": {
                "type": "date",
              },
              "display": {
                "date_order": "dmy",
                "label": "วันบัตรหมดอายุ",
                "year_format": "be",
              },
              "fields": {
                "allow_no_day": undefined,
                "allow_no_month": undefined,
                "allow_no_year": undefined,
              },
              "layout": "InputControl",
              "name": "date_of_expiry",
              "store_date_format": "yyyy-MM-dd",
              "type": "InputDate",
              "validator_rule": "required",
              "value_constraints": {
                "from": {
                  "exclusive": false,
                  "today": {},
                },
                "reversed": {
                  "year": false,
                },
                "to": {
                  "exclusive": false,
                  "today": {
                    "years": 100,
                  },
                },
              },
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "date_of_issue": {
              "builder": {
                "type": "date",
              },
              "display": {
                "date_order": "dmy",
                "label": "วันออกบัตร",
                "year_format": "be",
              },
              "fields": {
                "allow_no_day": undefined,
                "allow_no_month": undefined,
                "allow_no_year": undefined,
              },
              "layout": "InputControl",
              "name": "date_of_issue",
              "store_date_format": "yyyy-MM-dd",
              "type": "InputDate",
              "validator_rule": "required",
              "value_constraints": {
                "from": {
                  "exclusive": false,
                  "today": {
                    "years": -100,
                  },
                },
                "reversed": {
                  "year": true,
                },
                "to": {
                  "exclusive": false,
                  "today": {},
                },
              },
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "document_numbers": {
              "builder": {
                "type": "document_numbers",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "document_number": "document_number",
                "nid": "nid",
              },
              "items": {
                "document_number": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "หมายเลขเอกสาร",
                    "mask": undefined,
                    "placeholder": "หมายเลขเอกสาร",
                  },
                  "layout": "InputControl",
                  "name": "document_number",
                  "props": {
                    "autocomplete": "off",
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required",
                  "visible": {
                    "ekyc_document_country": "required|is:THA",
                    "ekyc_document_type": "required|is:front_card",
                  },
                  "visible_flag_invert": true,
                },
                "nid": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "เลขบัตรประจำตัวประชาชน",
                    "mask": "national_id",
                    "placeholder": "เลขบัตรประจำตัวประชาชน",
                  },
                  "layout": "InputControl",
                  "name": "nid",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if_condition:ekyc_document_country,=,THA,ekyc_document_country,=,front_card|regex:/^[0-9]{13}$/i|pass_checksum",
                  "visible": {
                    "ekyc_document_country": "required|is:THA",
                    "ekyc_document_type": "required|is:front_card",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "document_numbers",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "full_name": {
              "builder": {
                "type": "full_name",
              },
              "display": {
                "hide_label": true,
                "label": "กรุณากรอกชื่อของคุณ",
              },
              "fields": {
                "first_name": "full_name_first_name",
                "full": "full_name_full",
                "last_name": "full_name_last_name",
                "middle_name": "full_name_middle_name",
                "prefix": false,
                "show_middle_name": "full_name_show_middle_name",
                "type": "full_name_type",
              },
              "items": {
                "full_name_first_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "ชื่อ",
                    "mask": undefined,
                    "placeholder": "กรอกคำตอบที่นี่",
                  },
                  "layout": "InputControl",
                  "name": "full_name_first_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_unless": "กรุณากรอกข้อมูลให้ถูกต้อง",
                  },
                  "validator_rule": "required_unless:full_name_type,full",
                  "visible": {
                    "full_name_type": "not_in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_full": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "ชื่อและนามสกุล",
                    "mask": undefined,
                    "placeholder": "กรุณากรอกชื่อและนามสกุล",
                  },
                  "layout": "InputControl",
                  "name": "full_name_full",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_if": "Please Enter Your Full Name",
                  },
                  "validator_rule": "required_if:full_name_type,full",
                  "visible": {
                    "full_name_type": "required|is:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_last_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "นามสกุล",
                    "mask": undefined,
                    "placeholder": "กรอกคำตอบที่นี่",
                  },
                  "layout": "InputControl",
                  "name": "full_name_last_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_unless": "กรุณากรอกข้อมูลให้ถูกต้อง",
                  },
                  "validator_rule": "required_unless:full_name_type,full",
                  "visible": {
                    "full_name_type": "not_in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_middle_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "ชื่อกลาง",
                    "mask": undefined,
                    "placeholder": "กรอกคำตอบที่นี่",
                  },
                  "layout": "InputControl",
                  "name": "full_name_middle_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required_if_condition": "กรุณากรอกข้อมูลให้ถูกต้อง",
                  },
                  "validator_rule": "required_if_condition:full_name_type,!=,full,full_name_show_middle_name,=,true",
                  "visible": {
                    "full_name_show_middle_name": "required:accepted",
                    "full_name_type": "not_in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_show_middle_name": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "label": "Show Middle Name",
                  },
                  "enum": [
                    {
                      "label": "ตัวเลือกที่ 1",
                      "value": true,
                    },
                    {
                      "label": "ตัวเลือกที่ 2",
                      "value": false,
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "full_name_show_middle_name",
                  "type": "InputRadio",
                  "validator_rule": undefined,
                  "visible": {
                    "show_middle_name_flag": "required|accepted",
                  },
                  "visible_flag_invert": undefined,
                },
                "full_name_type": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "label": "Name Type",
                  },
                  "enum": [
                    {
                      "label": "ตัวเลือกที่ 1",
                      "value": "parts",
                    },
                    {
                      "label": "ตัวเลือกที่ 2",
                      "value": "full",
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "full_name_type",
                  "type": "InputRadio",
                  "validator_rule": undefined,
                  "visible": {
                    "show_name_type": "required|accepted",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "full_name",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "full_name_en_first_name": {
              "builder": {
                "type": "data_field",
              },
              "display": {
                "hide_label": true,
                "label": "First name (EN)",
              },
              "layout": "DefaultWrapper",
              "name": "full_name_en_first_name",
              "type": "DataField",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "full_name_en_last_name": {
              "builder": {
                "type": "data_field",
              },
              "display": {
                "hide_label": true,
                "label": "Last name (EN)",
              },
              "layout": "DefaultWrapper",
              "name": "full_name_en_last_name",
              "type": "DataField",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "gender": {
              "boolean": undefined,
              "builder": {
                "type": "gender",
              },
              "display": {
                "label": "เพศ",
              },
              "enum": [
                {
                  "label": "ชาย",
                  "value": "M",
                },
                {
                  "label": "หญิง",
                  "value": "F",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "gender",
              "type": "SingleSelectButton",
              "validator_messages": {
                "required": "กรุณาเลือกตัวเลือกของคุณ",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "home_address": {
              "builder": {
                "type": "address",
              },
              "display": {
                "label": "ที่อยู่",
              },
              "fields": {
                "address": "home_address_address",
                "address_1_common": "home_address_address_1_common",
                "address_2_common": "home_address_address_2_common",
                "address_reference": false,
                "city_common": "home_address_city_common",
                "country": "home_address_country",
                "district": "home_address_district",
                "full": "home_address_full",
                "postal_code_common": "home_address_postal_code_common",
                "province": "home_address_province",
                "subdistrict": "home_address_subdistrict",
                "type": "address_type",
                "zipcode": "home_address_zipcode",
                "zone_common": "home_address_zone_common",
              },
              "items": {
                "address_type": {
                  "boolean": undefined,
                  "builder": {
                    "type": "choice",
                  },
                  "display": {
                    "label": "Address Type",
                  },
                  "enum": [
                    {
                      "label": "ตัวเลือกที่ 1",
                      "value": "thai",
                    },
                    {
                      "label": "ตัวเลือกที่ 2",
                      "value": "parts",
                    },
                    {
                      "label": "ตัวเลือกที่ 3",
                      "value": "full",
                    },
                    {
                      "label": "schema.item.choice.options.3.label",
                      "value": "empty",
                    },
                  ],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "address_type",
                  "type": "InputRadio",
                  "validator_rule": undefined,
                  "visible": {
                    "show_address_type": "required|accepted",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_address": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "บ้านเลขที่, ซอย, หมู่, ถนน",
                    "mask": undefined,
                    "placeholder": "บ้านเลขที่, ซอย, หมู่, ถนน",
                  },
                  "layout": "InputControl",
                  "name": "home_address_address",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_messages": {
                    "required_if": "กรุณาระบุ บ้านเลขที่, ซอย, หมู่, ถนน",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_address_1_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 1",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_address_1_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_address_2_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 2",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_address_2_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_city_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "City",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_city_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_country": {
                  "builder": {
                    "type": "country",
                  },
                  "display": {
                    "label": "ประเทศ",
                    "placeholder": "ประเทศ",
                    "searchable": true,
                  },
                  "enum": undefined,
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_country",
                  "prefill": {
                    "value": "THA",
                  },
                  "type": "CountrySelect",
                  "validator_rule": "required",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "home_address_district": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "เขต/อำเภอ",
                    "placeholder": "เลือกเขต/อำเภอ",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_district",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "กรุณาเลือกเขต/อำเภอ",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_full": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address (Full)",
                    "mask": undefined,
                    "placeholder": "กรุณากรอกที่อยู่",
                  },
                  "layout": "InputControl",
                  "name": "home_address_full",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_messages": {
                    "required_if": "กรุณากรอกที่อยู่",
                  },
                  "validator_rule": "required_if:address_type,full",
                  "visible": {
                    "address_type": "required|in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_postal_code_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Postal code",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_postal_code_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_province": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "จังหวัด",
                    "placeholder": "เลือกจังหวัด",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_province",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "กรุณาเลือกจังหวัด",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_subdistrict": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "แขวง/ตำบล",
                    "placeholder": "เลือกแขวง/ตำบล",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_subdistrict",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "กรุณาเลือกแขวง/ตำบล",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_zipcode": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "รหัสไปรษณีย์",
                    "placeholder": "รหัสไปรษณีย์",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "home_address_zipcode",
                  "type": "Select",
                  "validator_messages": {
                    "required_if": "กรุณาเลือกรหัสไปรษณีย์",
                  },
                  "validator_rule": "required_if:address_type,thai",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
                "home_address_zone_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Zone",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "home_address_zone_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": "required_if:address_type,parts",
                  "visible": {
                    "address_type": "not_in:full,empty",
                  },
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "home_address",
              "type": "AddressAutofill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "name_prefix": {
              "boolean": undefined,
              "builder": {
                "type": "name_prefix",
              },
              "display": {
                "label": "คำนำหน้าชื่อ",
              },
              "enum": [
                {
                  "label": "นาย",
                  "value": "Mr.",
                },
                {
                  "label": "นางสาว",
                  "value": "Miss",
                },
                {
                  "label": "นาง",
                  "value": "Mrs.",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "name_prefix",
              "type": "SingleSelectButton",
              "validator_messages": {
                "required": "กรุณาเลือกตัวเลือกของคุณ",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "รายละเอียดเอกสารยืนยันตัวตน",
          "name": "ekyc_document_3",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "Ekyc",
      "visible": undefined,
    },
    "ekyc_liveness": {
      "builder": {
        "type": "ekyc_liveness",
      },
      "fields": {
        "ekyc_liveness_item": "ekyc_liveness",
        "max_attempt_warning": "ekyc_max_attempt_warning",
      },
      "name": "ekyc_liveness",
      "sections": {
        "ekyc_liveness": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "ekyc_liveness": {
              "builder": {
                "type": "ekyc_liveness_item",
              },
              "display": {
                "label": "",
              },
              "layout": "InputControl",
              "liveness": {
                "actionIconSetName": "smiley",
                "enableFaceSize": true,
                "enableIdleOnly": true,
              },
              "max_attempt_warning": {
                "allow_max_attempt_pass": false,
                "max_attempt_count": 5,
              },
              "name": "ekyc_liveness",
              "override_messages": {
                "ekyc": {
                  "liveness": {
                    "guide_subtitle": "ถ่ายรูปใบหน้าเพื่อยืนยันตัวตน",
                  },
                  "video": {
                    "description": "ถ่ายภาพให้ใบหน้าของคุณให้อยู่ในกรอบ และสามารถเห็นใบหน้าได้อย่างชัดเจน",
                    "title": "",
                  },
                },
              },
              "type": "Ekyc.Liveness",
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "ekyc_max_attempt_warning": {
              "builder": {
                "type": "ekyc_max_attempt_warning",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "back_btn": "back_btn",
                "bring_navbar_front": "bring_navbar_front",
                "cross_device_content": "cross_device_content",
                "warning_content": "warning_content",
              },
              "items": {
                "back_btn": {
                  "display": {
                    "hide_label": true,
                    "label": "กลับหน้าหลัก",
                  },
                  "layout": "InputControl",
                  "name": "back_btn",
                  "target": "/page/test",
                  "type": "TargetButton",
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                },
                "bring_navbar_front": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<style>
.app__dynamic-form .navbar {
    z-index: 100000!important;
}
</style>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "bring_navbar_front",
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "cross_device_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{cross_device_content.title}}</div>
          <div class="warning-text subtitle">{{_,ekyc.recorder.error_card_max_attempt_desc_cross_device|toI18n}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "cross_device_content",
                  "prefill": {
                    "value": {
                      "title": "ไม่สามารถตรวจสอบการยืนยันตัวตนได้",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "required|is:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_content": {
                  "builder": {
                    "type": "paragraph",
                  },
                  "content": "<div class="warning-text title">{{warning_content.title}}</div> 
          <div class="warning-text subtitle">{{warning_content.description}}</div>",
                  "display": {
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_content",
                  "prefill": {
                    "value": {
                      "description": "กรุณาติดต่อฝ่ายบริการลูกค้า",
                      "title": "ไม่สามารถตรวจสอบการยืนยันตัวตนได้",
                    },
                  },
                  "sanitize": false,
                  "styling": {
                    "--background-color": undefined,
                    "--border-color": undefined,
                    "background-color": undefined,
                    "border": undefined,
                    "white-space": undefined,
                  },
                  "type": "StaticContent",
                  "validator_rule": undefined,
                  "visible": {
                    "_cross_device": "not_in:true",
                  },
                  "visible_flag_invert": undefined,
                },
                "warning_image": {
                  "builder": {
                    "type": "image_preset",
                  },
                  "display": {
                    "image_name": "telephone",
                    "label": "",
                  },
                  "layout": "InputControl",
                  "name": "warning_image",
                  "styling": {
                    "--alignment": "center",
                    "--height": "auto",
                    "--width": "50%",
                  },
                  "type": "StaticImage",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "FullscreenWrapper",
              "name": "ekyc_max_attempt_warning",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": {
                "ekyc_liveness_max_attempt": "required",
              },
              "visible_flag_invert": undefined,
            },
          },
          "label": "เตรียมถ่ายภาพเซลฟี่",
          "name": "ekyc_liveness",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "Ekyc",
      "visible": undefined,
    },
    "email": {
      "builder": {
        "type": "email",
      },
      "fields": {
        "otp_address": "email_otp_address",
        "otp_verify": "email_otp_verify",
      },
      "name": "email",
      "sections": {
        "email": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "email_image_1": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "email-verification",
                "label": "",
              },
              "layout": "InputControl",
              "name": "email_image_1",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "email_otp_address": {
              "builder": {
                "type": "email",
              },
              "display": {
                "hide_label": true,
                "label": "",
              },
              "fields": {
                "confirmation": false,
                "email": "email_otp_address_email",
              },
              "items": {
                "email_otp_address_email": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "configs": {
                    "transform_value": [
                      "toLowerCase",
                    ],
                  },
                  "display": {
                    "hide_label": true,
                    "icon_right": "feather:mail",
                    "label": "Email",
                    "mask": undefined,
                    "placeholder": "<EMAIL>",
                  },
                  "layout": "InputControl",
                  "name": "email_otp_address_email",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required": "กรุณากรอกอีเมลของคุณ",
                  },
                  "validator_rule": "required|email",
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "email_otp_address",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ยืนยันอีเมลของคุณ",
          "name": "email",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "ระบุอีเมลที่ต้องการใช้รับ <b>รหัสผ่าน</b>",
        },
        "email_2": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "email_image_2": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "email-verification-otp",
                "label": "",
              },
              "layout": "InputControl",
              "name": "email_image_2",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "email_otp_verify": {
              "builder": {
                "type": "otp_email",
              },
              "configs": {},
              "display": {
                "error_template": {
                  "item": false,
                  "section": false,
                },
                "label": "",
              },
              "email_field": "email_otp_address_email",
              "layout": "FullWidthWrapper",
              "name": "email_otp_verify",
              "override_heading": true,
              "override_subheading": true,
              "type": "OTP.VerifyEmail",
              "validator_messages": {
                "required": "Please enter OTP code",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ยืนยันอีเมลของคุณ",
          "name": "email_2",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "ระบุอีเมลที่ต้องการใช้รับ <b>รหัสผ่าน</b>",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "OTP",
      "visible": undefined,
    },
    "personal": {
      "name": "personal",
      "sections": {
        "personal": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "contact_address": {
              "builder": {
                "type": "address",
              },
              "display": {
                "label": "ที่อยู่",
              },
              "fields": {
                "address": "contact_address_address",
                "address_1_common": "contact_address_address_1_common",
                "address_2_common": "contact_address_address_2_common",
                "address_reference": false,
                "city_common": "contact_address_city_common",
                "country": "contact_address_country",
                "district": "contact_address_district",
                "full": "contact_address_full",
                "postal_code_common": "contact_address_postal_code_common",
                "province": "contact_address_province",
                "subdistrict": "contact_address_subdistrict",
                "type": false,
                "zipcode": "contact_address_zipcode",
                "zone_common": "contact_address_zone_common",
              },
              "items": {
                "contact_address_address": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "บ้านเลขที่, ซอย, หมู่, ถนน",
                    "mask": undefined,
                    "placeholder": "บ้านเลขที่, ซอย, หมู่, ถนน",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_address",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_address_1_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 1",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_address_1_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_address_2_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address 2",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_address_2_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_city_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "City",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_city_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_country": {
                  "builder": {
                    "type": "country",
                  },
                  "display": {
                    "label": "ประเทศ",
                    "placeholder": "ประเทศ",
                    "searchable": true,
                  },
                  "enum": undefined,
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_country",
                  "prefill": {
                    "value": "THA",
                  },
                  "type": "CountrySelect",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_district": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "เขต/อำเภอ",
                    "placeholder": "เลือกเขต/อำเภอ",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_district",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_full": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Address (Full)",
                    "mask": undefined,
                    "placeholder": "กรุณากรอกที่อยู่",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_full",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 1000,
                    "type": undefined,
                  },
                  "type": "Textarea",
                  "validator_rule": undefined,
                  "visible": {
                    "contact_address_type": "required|in:full",
                  },
                  "visible_flag_invert": undefined,
                },
                "contact_address_postal_code_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Postal code",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_postal_code_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_province": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "จังหวัด",
                    "placeholder": "เลือกจังหวัด",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_province",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_subdistrict": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "แขวง/ตำบล",
                    "placeholder": "เลือกแขวง/ตำบล",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_subdistrict",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_zipcode": {
                  "builder": {
                    "type": "dropdown",
                  },
                  "display": {
                    "label": "รหัสไปรษณีย์",
                    "placeholder": "รหัสไปรษณีย์",
                  },
                  "enum": [],
                  "enum_presets": undefined,
                  "layout": "InputControl",
                  "name": "contact_address_zipcode",
                  "type": "Select",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_address_zone_common": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "Zone",
                    "mask": undefined,
                    "placeholder": "",
                  },
                  "layout": "InputControl",
                  "name": "contact_address_zone_common",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "contact_address",
              "type": "AddressAutofill",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "contact_full_name": {
              "builder": {
                "type": "full_name",
              },
              "display": {
                "label": "กรุณากรอกชื่อของคุณ",
              },
              "fields": {
                "first_name": "contact_full_name_first_name",
                "full": false,
                "last_name": "contact_full_name_last_name",
                "middle_name": false,
                "prefix": false,
                "show_middle_name": false,
                "type": false,
              },
              "items": {
                "contact_full_name_first_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "ชื่อ",
                    "mask": undefined,
                    "placeholder": "ชื่อ",
                  },
                  "layout": "InputControl",
                  "name": "contact_full_name_first_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required": "กรุณากรอกชื่อ",
                  },
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
                "contact_full_name_last_name": {
                  "builder": {
                    "type": "short_long_answer",
                  },
                  "display": {
                    "label": "นามสกุล",
                    "mask": undefined,
                    "placeholder": "นามสกุล",
                  },
                  "layout": "InputControl",
                  "name": "contact_full_name_last_name",
                  "props": {
                    "autocomplete": undefined,
                    "maxlength": 280,
                    "type": undefined,
                  },
                  "type": "InputText",
                  "validator_messages": {
                    "required": "กรุณากรอกนามสกุล",
                  },
                  "validator_rule": undefined,
                  "visible": undefined,
                  "visible_flag_invert": undefined,
                },
              },
              "layout": "DefaultWrapper",
              "name": "contact_full_name",
              "type": "Fieldset",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ข้อมูลติดต่อ",
          "name": "personal",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "visible": undefined,
    },
    "telephone": {
      "builder": {
        "type": "telephone",
      },
      "fields": {
        "otp_address": "telephone_otp_address",
        "otp_verify": "telephone_otp_verify",
      },
      "name": "telephone",
      "sections": {
        "telephone": {
          "description": "Description",
          "hide_description": true,
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "telephone_image_1": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "mobile-verification",
                "label": "",
              },
              "layout": "InputControl",
              "name": "telephone_image_1",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "telephone_otp_address": {
              "builder": {
                "type": "telephone",
              },
              "configs": {
                "accepted_countries": [
                  "HK",
                  "ID",
                  "PH",
                  "TH",
                ],
              },
              "display": {
                "hide_label": true,
                "icon_right": "feather:smartphone",
                "label": "เบอร์โทรศัพท์",
                "placeholder": "เบอร์โทรศัพท์",
              },
              "layout": "InputControl",
              "name": "telephone_otp_address",
              "type": "InputTelephone",
              "validator_messages": {
                "required": "กรุณากรอกเบอร์โทรศัพท์",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ยืนยันเบอร์มือถือ",
          "name": "telephone",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "ใส่เบอร์ที่ถูกต้องเพื่อรับรหัส <b>OTP</b>",
        },
        "telephone_2": {
          "hide_label": false,
          "hide_subheading": false,
          "items": {
            "telephone_image_2": {
              "builder": {
                "type": "image_preset",
              },
              "display": {
                "image_name": "mobile-verification-otp",
                "label": "",
              },
              "layout": "InputControl",
              "name": "telephone_image_2",
              "styling": {
                "--alignment": "center",
                "--height": "auto",
                "--width": "50%",
              },
              "type": "StaticImage",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "telephone_otp_verify": {
              "builder": {
                "type": "otp_item",
              },
              "configs": {},
              "digits": 6,
              "display": {
                "error_template": {
                  "item": false,
                  "section": true,
                },
                "label": "",
              },
              "layout": "FullWidthWrapper",
              "name": "telephone_otp_verify",
              "override_heading": true,
              "override_subheading": true,
              "tel_field": "telephone_otp_address",
              "type": "OTP.Verify",
              "validator_messages": {
                "required": "Please enter OTP code",
              },
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "label": "ยืนยันเบอร์มือถือ",
          "name": "telephone_2",
          "post_save": undefined,
          "pre_save": undefined,
          "subheading": "ใส่เบอร์ที่ถูกต้องเพื่อรับรหัส <b>OTP</b>",
        },
      },
      "styling": {
        "--form-section-header-align": "center",
        "--form-step-header-align": "center",
        "--form-step-header-size": "1.375rem",
      },
      "type": "OTP",
      "visible": undefined,
    },
  },
}
`;
