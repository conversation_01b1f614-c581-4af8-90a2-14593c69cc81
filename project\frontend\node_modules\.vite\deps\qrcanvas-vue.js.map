{"version": 3, "sources": ["../../qrcode-generator/qrcode.js", "../../qrcanvas-vue/dist/qrcanvas-vue.esm.js", "../../@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../@babel/runtime/helpers/esm/extends.js", "../../qrcanvas/lib/qrcanvas.esm.js"], "sourcesContent": ["//---------------------------------------------------------------------\n//\n// QR Code Generator for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//  http://www.opensource.org/licenses/mit-license.php\n//\n// The word 'QR Code' is registered trademark of\n// DENSO WAVE INCORPORATED\n//  http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n\nvar qrcode = function() {\n\n  //---------------------------------------------------------------------\n  // qrcode\n  //---------------------------------------------------------------------\n\n  /**\n   * qrcode\n   * @param typeNumber 1 to 40\n   * @param errorCorrectionLevel 'L','M','Q','H'\n   */\n  var qrcode = function(typeNumber, errorCorrectionLevel) {\n\n    var PAD0 = 0xEC;\n    var PAD1 = 0x11;\n\n    var _typeNumber = typeNumber;\n    var _errorCorrectionLevel = QRErrorCorrectionLevel[errorCorrectionLevel];\n    var _modules = null;\n    var _moduleCount = 0;\n    var _dataCache = null;\n    var _dataList = [];\n\n    var _this = {};\n\n    var makeImpl = function(test, maskPattern) {\n\n      _moduleCount = _typeNumber * 4 + 17;\n      _modules = function(moduleCount) {\n        var modules = new Array(moduleCount);\n        for (var row = 0; row < moduleCount; row += 1) {\n          modules[row] = new Array(moduleCount);\n          for (var col = 0; col < moduleCount; col += 1) {\n            modules[row][col] = null;\n          }\n        }\n        return modules;\n      }(_moduleCount);\n\n      setupPositionProbePattern(0, 0);\n      setupPositionProbePattern(_moduleCount - 7, 0);\n      setupPositionProbePattern(0, _moduleCount - 7);\n      setupPositionAdjustPattern();\n      setupTimingPattern();\n      setupTypeInfo(test, maskPattern);\n\n      if (_typeNumber >= 7) {\n        setupTypeNumber(test);\n      }\n\n      if (_dataCache == null) {\n        _dataCache = createData(_typeNumber, _errorCorrectionLevel, _dataList);\n      }\n\n      mapData(_dataCache, maskPattern);\n    };\n\n    var setupPositionProbePattern = function(row, col) {\n\n      for (var r = -1; r <= 7; r += 1) {\n\n        if (row + r <= -1 || _moduleCount <= row + r) continue;\n\n        for (var c = -1; c <= 7; c += 1) {\n\n          if (col + c <= -1 || _moduleCount <= col + c) continue;\n\n          if ( (0 <= r && r <= 6 && (c == 0 || c == 6) )\n              || (0 <= c && c <= 6 && (r == 0 || r == 6) )\n              || (2 <= r && r <= 4 && 2 <= c && c <= 4) ) {\n            _modules[row + r][col + c] = true;\n          } else {\n            _modules[row + r][col + c] = false;\n          }\n        }\n      }\n    };\n\n    var getBestMaskPattern = function() {\n\n      var minLostPoint = 0;\n      var pattern = 0;\n\n      for (var i = 0; i < 8; i += 1) {\n\n        makeImpl(true, i);\n\n        var lostPoint = QRUtil.getLostPoint(_this);\n\n        if (i == 0 || minLostPoint > lostPoint) {\n          minLostPoint = lostPoint;\n          pattern = i;\n        }\n      }\n\n      return pattern;\n    };\n\n    var setupTimingPattern = function() {\n\n      for (var r = 8; r < _moduleCount - 8; r += 1) {\n        if (_modules[r][6] != null) {\n          continue;\n        }\n        _modules[r][6] = (r % 2 == 0);\n      }\n\n      for (var c = 8; c < _moduleCount - 8; c += 1) {\n        if (_modules[6][c] != null) {\n          continue;\n        }\n        _modules[6][c] = (c % 2 == 0);\n      }\n    };\n\n    var setupPositionAdjustPattern = function() {\n\n      var pos = QRUtil.getPatternPosition(_typeNumber);\n\n      for (var i = 0; i < pos.length; i += 1) {\n\n        for (var j = 0; j < pos.length; j += 1) {\n\n          var row = pos[i];\n          var col = pos[j];\n\n          if (_modules[row][col] != null) {\n            continue;\n          }\n\n          for (var r = -2; r <= 2; r += 1) {\n\n            for (var c = -2; c <= 2; c += 1) {\n\n              if (r == -2 || r == 2 || c == -2 || c == 2\n                  || (r == 0 && c == 0) ) {\n                _modules[row + r][col + c] = true;\n              } else {\n                _modules[row + r][col + c] = false;\n              }\n            }\n          }\n        }\n      }\n    };\n\n    var setupTypeNumber = function(test) {\n\n      var bits = QRUtil.getBCHTypeNumber(_typeNumber);\n\n      for (var i = 0; i < 18; i += 1) {\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n        _modules[Math.floor(i / 3)][i % 3 + _moduleCount - 8 - 3] = mod;\n      }\n\n      for (var i = 0; i < 18; i += 1) {\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n        _modules[i % 3 + _moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n      }\n    };\n\n    var setupTypeInfo = function(test, maskPattern) {\n\n      var data = (_errorCorrectionLevel << 3) | maskPattern;\n      var bits = QRUtil.getBCHTypeInfo(data);\n\n      // vertical\n      for (var i = 0; i < 15; i += 1) {\n\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n\n        if (i < 6) {\n          _modules[i][8] = mod;\n        } else if (i < 8) {\n          _modules[i + 1][8] = mod;\n        } else {\n          _modules[_moduleCount - 15 + i][8] = mod;\n        }\n      }\n\n      // horizontal\n      for (var i = 0; i < 15; i += 1) {\n\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n\n        if (i < 8) {\n          _modules[8][_moduleCount - i - 1] = mod;\n        } else if (i < 9) {\n          _modules[8][15 - i - 1 + 1] = mod;\n        } else {\n          _modules[8][15 - i - 1] = mod;\n        }\n      }\n\n      // fixed module\n      _modules[_moduleCount - 8][8] = (!test);\n    };\n\n    var mapData = function(data, maskPattern) {\n\n      var inc = -1;\n      var row = _moduleCount - 1;\n      var bitIndex = 7;\n      var byteIndex = 0;\n      var maskFunc = QRUtil.getMaskFunction(maskPattern);\n\n      for (var col = _moduleCount - 1; col > 0; col -= 2) {\n\n        if (col == 6) col -= 1;\n\n        while (true) {\n\n          for (var c = 0; c < 2; c += 1) {\n\n            if (_modules[row][col - c] == null) {\n\n              var dark = false;\n\n              if (byteIndex < data.length) {\n                dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);\n              }\n\n              var mask = maskFunc(row, col - c);\n\n              if (mask) {\n                dark = !dark;\n              }\n\n              _modules[row][col - c] = dark;\n              bitIndex -= 1;\n\n              if (bitIndex == -1) {\n                byteIndex += 1;\n                bitIndex = 7;\n              }\n            }\n          }\n\n          row += inc;\n\n          if (row < 0 || _moduleCount <= row) {\n            row -= inc;\n            inc = -inc;\n            break;\n          }\n        }\n      }\n    };\n\n    var createBytes = function(buffer, rsBlocks) {\n\n      var offset = 0;\n\n      var maxDcCount = 0;\n      var maxEcCount = 0;\n\n      var dcdata = new Array(rsBlocks.length);\n      var ecdata = new Array(rsBlocks.length);\n\n      for (var r = 0; r < rsBlocks.length; r += 1) {\n\n        var dcCount = rsBlocks[r].dataCount;\n        var ecCount = rsBlocks[r].totalCount - dcCount;\n\n        maxDcCount = Math.max(maxDcCount, dcCount);\n        maxEcCount = Math.max(maxEcCount, ecCount);\n\n        dcdata[r] = new Array(dcCount);\n\n        for (var i = 0; i < dcdata[r].length; i += 1) {\n          dcdata[r][i] = 0xff & buffer.getBuffer()[i + offset];\n        }\n        offset += dcCount;\n\n        var rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);\n        var rawPoly = qrPolynomial(dcdata[r], rsPoly.getLength() - 1);\n\n        var modPoly = rawPoly.mod(rsPoly);\n        ecdata[r] = new Array(rsPoly.getLength() - 1);\n        for (var i = 0; i < ecdata[r].length; i += 1) {\n          var modIndex = i + modPoly.getLength() - ecdata[r].length;\n          ecdata[r][i] = (modIndex >= 0)? modPoly.getAt(modIndex) : 0;\n        }\n      }\n\n      var totalCodeCount = 0;\n      for (var i = 0; i < rsBlocks.length; i += 1) {\n        totalCodeCount += rsBlocks[i].totalCount;\n      }\n\n      var data = new Array(totalCodeCount);\n      var index = 0;\n\n      for (var i = 0; i < maxDcCount; i += 1) {\n        for (var r = 0; r < rsBlocks.length; r += 1) {\n          if (i < dcdata[r].length) {\n            data[index] = dcdata[r][i];\n            index += 1;\n          }\n        }\n      }\n\n      for (var i = 0; i < maxEcCount; i += 1) {\n        for (var r = 0; r < rsBlocks.length; r += 1) {\n          if (i < ecdata[r].length) {\n            data[index] = ecdata[r][i];\n            index += 1;\n          }\n        }\n      }\n\n      return data;\n    };\n\n    var createData = function(typeNumber, errorCorrectionLevel, dataList) {\n\n      var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectionLevel);\n\n      var buffer = qrBitBuffer();\n\n      for (var i = 0; i < dataList.length; i += 1) {\n        var data = dataList[i];\n        buffer.put(data.getMode(), 4);\n        buffer.put(data.getLength(), QRUtil.getLengthInBits(data.getMode(), typeNumber) );\n        data.write(buffer);\n      }\n\n      // calc num max data.\n      var totalDataCount = 0;\n      for (var i = 0; i < rsBlocks.length; i += 1) {\n        totalDataCount += rsBlocks[i].dataCount;\n      }\n\n      if (buffer.getLengthInBits() > totalDataCount * 8) {\n        throw 'code length overflow. ('\n          + buffer.getLengthInBits()\n          + '>'\n          + totalDataCount * 8\n          + ')';\n      }\n\n      // end code\n      if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n        buffer.put(0, 4);\n      }\n\n      // padding\n      while (buffer.getLengthInBits() % 8 != 0) {\n        buffer.putBit(false);\n      }\n\n      // padding\n      while (true) {\n\n        if (buffer.getLengthInBits() >= totalDataCount * 8) {\n          break;\n        }\n        buffer.put(PAD0, 8);\n\n        if (buffer.getLengthInBits() >= totalDataCount * 8) {\n          break;\n        }\n        buffer.put(PAD1, 8);\n      }\n\n      return createBytes(buffer, rsBlocks);\n    };\n\n    _this.addData = function(data, mode) {\n\n      mode = mode || 'Byte';\n\n      var newData = null;\n\n      switch(mode) {\n      case 'Numeric' :\n        newData = qrNumber(data);\n        break;\n      case 'Alphanumeric' :\n        newData = qrAlphaNum(data);\n        break;\n      case 'Byte' :\n        newData = qr8BitByte(data);\n        break;\n      case 'Kanji' :\n        newData = qrKanji(data);\n        break;\n      default :\n        throw 'mode:' + mode;\n      }\n\n      _dataList.push(newData);\n      _dataCache = null;\n    };\n\n    _this.isDark = function(row, col) {\n      if (row < 0 || _moduleCount <= row || col < 0 || _moduleCount <= col) {\n        throw row + ',' + col;\n      }\n      return _modules[row][col];\n    };\n\n    _this.getModuleCount = function() {\n      return _moduleCount;\n    };\n\n    _this.make = function() {\n      if (_typeNumber < 1) {\n        var typeNumber = 1;\n\n        for (; typeNumber < 40; typeNumber++) {\n          var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, _errorCorrectionLevel);\n          var buffer = qrBitBuffer();\n\n          for (var i = 0; i < _dataList.length; i++) {\n            var data = _dataList[i];\n            buffer.put(data.getMode(), 4);\n            buffer.put(data.getLength(), QRUtil.getLengthInBits(data.getMode(), typeNumber) );\n            data.write(buffer);\n          }\n\n          var totalDataCount = 0;\n          for (var i = 0; i < rsBlocks.length; i++) {\n            totalDataCount += rsBlocks[i].dataCount;\n          }\n\n          if (buffer.getLengthInBits() <= totalDataCount * 8) {\n            break;\n          }\n        }\n\n        _typeNumber = typeNumber;\n      }\n\n      makeImpl(false, getBestMaskPattern() );\n    };\n\n    _this.createTableTag = function(cellSize, margin) {\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      var qrHtml = '';\n\n      qrHtml += '<table style=\"';\n      qrHtml += ' border-width: 0px; border-style: none;';\n      qrHtml += ' border-collapse: collapse;';\n      qrHtml += ' padding: 0px; margin: ' + margin + 'px;';\n      qrHtml += '\">';\n      qrHtml += '<tbody>';\n\n      for (var r = 0; r < _this.getModuleCount(); r += 1) {\n\n        qrHtml += '<tr>';\n\n        for (var c = 0; c < _this.getModuleCount(); c += 1) {\n          qrHtml += '<td style=\"';\n          qrHtml += ' border-width: 0px; border-style: none;';\n          qrHtml += ' border-collapse: collapse;';\n          qrHtml += ' padding: 0px; margin: 0px;';\n          qrHtml += ' width: ' + cellSize + 'px;';\n          qrHtml += ' height: ' + cellSize + 'px;';\n          qrHtml += ' background-color: ';\n          qrHtml += _this.isDark(r, c)? '#000000' : '#ffffff';\n          qrHtml += ';';\n          qrHtml += '\"/>';\n        }\n\n        qrHtml += '</tr>';\n      }\n\n      qrHtml += '</tbody>';\n      qrHtml += '</table>';\n\n      return qrHtml;\n    };\n\n    _this.createSvgTag = function(cellSize, margin, alt, title) {\n\n      var opts = {};\n      if (typeof arguments[0] == 'object') {\n        // Called by options.\n        opts = arguments[0];\n        // overwrite cellSize and margin.\n        cellSize = opts.cellSize;\n        margin = opts.margin;\n        alt = opts.alt;\n        title = opts.title;\n      }\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      // Compose alt property surrogate\n      alt = (typeof alt === 'string') ? {text: alt} : alt || {};\n      alt.text = alt.text || null;\n      alt.id = (alt.text) ? alt.id || 'qrcode-description' : null;\n\n      // Compose title property surrogate\n      title = (typeof title === 'string') ? {text: title} : title || {};\n      title.text = title.text || null;\n      title.id = (title.text) ? title.id || 'qrcode-title' : null;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var c, mc, r, mr, qrSvg='', rect;\n\n      rect = 'l' + cellSize + ',0 0,' + cellSize +\n        ' -' + cellSize + ',0 0,-' + cellSize + 'z ';\n\n      qrSvg += '<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"';\n      qrSvg += !opts.scalable ? ' width=\"' + size + 'px\" height=\"' + size + 'px\"' : '';\n      qrSvg += ' viewBox=\"0 0 ' + size + ' ' + size + '\" ';\n      qrSvg += ' preserveAspectRatio=\"xMinYMin meet\"';\n      qrSvg += (title.text || alt.text) ? ' role=\"img\" aria-labelledby=\"' +\n          escapeXml([title.id, alt.id].join(' ').trim() ) + '\"' : '';\n      qrSvg += '>';\n      qrSvg += (title.text) ? '<title id=\"' + escapeXml(title.id) + '\">' +\n          escapeXml(title.text) + '</title>' : '';\n      qrSvg += (alt.text) ? '<description id=\"' + escapeXml(alt.id) + '\">' +\n          escapeXml(alt.text) + '</description>' : '';\n      qrSvg += '<rect width=\"100%\" height=\"100%\" fill=\"white\" cx=\"0\" cy=\"0\"/>';\n      qrSvg += '<path d=\"';\n\n      for (r = 0; r < _this.getModuleCount(); r += 1) {\n        mr = r * cellSize + margin;\n        for (c = 0; c < _this.getModuleCount(); c += 1) {\n          if (_this.isDark(r, c) ) {\n            mc = c*cellSize+margin;\n            qrSvg += 'M' + mc + ',' + mr + rect;\n          }\n        }\n      }\n\n      qrSvg += '\" stroke=\"transparent\" fill=\"black\"/>';\n      qrSvg += '</svg>';\n\n      return qrSvg;\n    };\n\n    _this.createDataURL = function(cellSize, margin) {\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var min = margin;\n      var max = size - margin;\n\n      return createDataURL(size, size, function(x, y) {\n        if (min <= x && x < max && min <= y && y < max) {\n          var c = Math.floor( (x - min) / cellSize);\n          var r = Math.floor( (y - min) / cellSize);\n          return _this.isDark(r, c)? 0 : 1;\n        } else {\n          return 1;\n        }\n      } );\n    };\n\n    _this.createImgTag = function(cellSize, margin, alt) {\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n\n      var img = '';\n      img += '<img';\n      img += '\\u0020src=\"';\n      img += _this.createDataURL(cellSize, margin);\n      img += '\"';\n      img += '\\u0020width=\"';\n      img += size;\n      img += '\"';\n      img += '\\u0020height=\"';\n      img += size;\n      img += '\"';\n      if (alt) {\n        img += '\\u0020alt=\"';\n        img += escapeXml(alt);\n        img += '\"';\n      }\n      img += '/>';\n\n      return img;\n    };\n\n    var escapeXml = function(s) {\n      var escaped = '';\n      for (var i = 0; i < s.length; i += 1) {\n        var c = s.charAt(i);\n        switch(c) {\n        case '<': escaped += '&lt;'; break;\n        case '>': escaped += '&gt;'; break;\n        case '&': escaped += '&amp;'; break;\n        case '\"': escaped += '&quot;'; break;\n        default : escaped += c; break;\n        }\n      }\n      return escaped;\n    };\n\n    var _createHalfASCII = function(margin) {\n      var cellSize = 1;\n      margin = (typeof margin == 'undefined')? cellSize * 2 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var min = margin;\n      var max = size - margin;\n\n      var y, x, r1, r2, p;\n\n      var blocks = {\n        '██': '█',\n        '█ ': '▀',\n        ' █': '▄',\n        '  ': ' '\n      };\n\n      var blocksLastLineNoMargin = {\n        '██': '▀',\n        '█ ': '▀',\n        ' █': ' ',\n        '  ': ' '\n      };\n\n      var ascii = '';\n      for (y = 0; y < size; y += 2) {\n        r1 = Math.floor((y - min) / cellSize);\n        r2 = Math.floor((y + 1 - min) / cellSize);\n        for (x = 0; x < size; x += 1) {\n          p = '█';\n\n          if (min <= x && x < max && min <= y && y < max && _this.isDark(r1, Math.floor((x - min) / cellSize))) {\n            p = ' ';\n          }\n\n          if (min <= x && x < max && min <= y+1 && y+1 < max && _this.isDark(r2, Math.floor((x - min) / cellSize))) {\n            p += ' ';\n          }\n          else {\n            p += '█';\n          }\n\n          // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.\n          ascii += (margin < 1 && y+1 >= max) ? blocksLastLineNoMargin[p] : blocks[p];\n        }\n\n        ascii += '\\n';\n      }\n\n      if (size % 2 && margin > 0) {\n        return ascii.substring(0, ascii.length - size - 1) + Array(size+1).join('▀');\n      }\n\n      return ascii.substring(0, ascii.length-1);\n    };\n\n    _this.createASCII = function(cellSize, margin) {\n      cellSize = cellSize || 1;\n\n      if (cellSize < 2) {\n        return _createHalfASCII(margin);\n      }\n\n      cellSize -= 1;\n      margin = (typeof margin == 'undefined')? cellSize * 2 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var min = margin;\n      var max = size - margin;\n\n      var y, x, r, p;\n\n      var white = Array(cellSize+1).join('██');\n      var black = Array(cellSize+1).join('  ');\n\n      var ascii = '';\n      var line = '';\n      for (y = 0; y < size; y += 1) {\n        r = Math.floor( (y - min) / cellSize);\n        line = '';\n        for (x = 0; x < size; x += 1) {\n          p = 1;\n\n          if (min <= x && x < max && min <= y && y < max && _this.isDark(r, Math.floor((x - min) / cellSize))) {\n            p = 0;\n          }\n\n          // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.\n          line += p ? white : black;\n        }\n\n        for (r = 0; r < cellSize; r += 1) {\n          ascii += line + '\\n';\n        }\n      }\n\n      return ascii.substring(0, ascii.length-1);\n    };\n\n    _this.renderTo2dContext = function(context, cellSize) {\n      cellSize = cellSize || 2;\n      var length = _this.getModuleCount();\n      for (var row = 0; row < length; row++) {\n        for (var col = 0; col < length; col++) {\n          context.fillStyle = _this.isDark(row, col) ? 'black' : 'white';\n          context.fillRect(row * cellSize, col * cellSize, cellSize, cellSize);\n        }\n      }\n    }\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrcode.stringToBytes\n  //---------------------------------------------------------------------\n\n  qrcode.stringToBytesFuncs = {\n    'default' : function(s) {\n      var bytes = [];\n      for (var i = 0; i < s.length; i += 1) {\n        var c = s.charCodeAt(i);\n        bytes.push(c & 0xff);\n      }\n      return bytes;\n    }\n  };\n\n  qrcode.stringToBytes = qrcode.stringToBytesFuncs['default'];\n\n  //---------------------------------------------------------------------\n  // qrcode.createStringToBytes\n  //---------------------------------------------------------------------\n\n  /**\n   * @param unicodeData base64 string of byte array.\n   * [16bit Unicode],[16bit Bytes], ...\n   * @param numChars\n   */\n  qrcode.createStringToBytes = function(unicodeData, numChars) {\n\n    // create conversion map.\n\n    var unicodeMap = function() {\n\n      var bin = base64DecodeInputStream(unicodeData);\n      var read = function() {\n        var b = bin.read();\n        if (b == -1) throw 'eof';\n        return b;\n      };\n\n      var count = 0;\n      var unicodeMap = {};\n      while (true) {\n        var b0 = bin.read();\n        if (b0 == -1) break;\n        var b1 = read();\n        var b2 = read();\n        var b3 = read();\n        var k = String.fromCharCode( (b0 << 8) | b1);\n        var v = (b2 << 8) | b3;\n        unicodeMap[k] = v;\n        count += 1;\n      }\n      if (count != numChars) {\n        throw count + ' != ' + numChars;\n      }\n\n      return unicodeMap;\n    }();\n\n    var unknownChar = '?'.charCodeAt(0);\n\n    return function(s) {\n      var bytes = [];\n      for (var i = 0; i < s.length; i += 1) {\n        var c = s.charCodeAt(i);\n        if (c < 128) {\n          bytes.push(c);\n        } else {\n          var b = unicodeMap[s.charAt(i)];\n          if (typeof b == 'number') {\n            if ( (b & 0xff) == b) {\n              // 1byte\n              bytes.push(b);\n            } else {\n              // 2bytes\n              bytes.push(b >>> 8);\n              bytes.push(b & 0xff);\n            }\n          } else {\n            bytes.push(unknownChar);\n          }\n        }\n      }\n      return bytes;\n    };\n  };\n\n  //---------------------------------------------------------------------\n  // QRMode\n  //---------------------------------------------------------------------\n\n  var QRMode = {\n    MODE_NUMBER :    1 << 0,\n    MODE_ALPHA_NUM : 1 << 1,\n    MODE_8BIT_BYTE : 1 << 2,\n    MODE_KANJI :     1 << 3\n  };\n\n  //---------------------------------------------------------------------\n  // QRErrorCorrectionLevel\n  //---------------------------------------------------------------------\n\n  var QRErrorCorrectionLevel = {\n    L : 1,\n    M : 0,\n    Q : 3,\n    H : 2\n  };\n\n  //---------------------------------------------------------------------\n  // QRMaskPattern\n  //---------------------------------------------------------------------\n\n  var QRMaskPattern = {\n    PATTERN000 : 0,\n    PATTERN001 : 1,\n    PATTERN010 : 2,\n    PATTERN011 : 3,\n    PATTERN100 : 4,\n    PATTERN101 : 5,\n    PATTERN110 : 6,\n    PATTERN111 : 7\n  };\n\n  //---------------------------------------------------------------------\n  // QRUtil\n  //---------------------------------------------------------------------\n\n  var QRUtil = function() {\n\n    var PATTERN_POSITION_TABLE = [\n      [],\n      [6, 18],\n      [6, 22],\n      [6, 26],\n      [6, 30],\n      [6, 34],\n      [6, 22, 38],\n      [6, 24, 42],\n      [6, 26, 46],\n      [6, 28, 50],\n      [6, 30, 54],\n      [6, 32, 58],\n      [6, 34, 62],\n      [6, 26, 46, 66],\n      [6, 26, 48, 70],\n      [6, 26, 50, 74],\n      [6, 30, 54, 78],\n      [6, 30, 56, 82],\n      [6, 30, 58, 86],\n      [6, 34, 62, 90],\n      [6, 28, 50, 72, 94],\n      [6, 26, 50, 74, 98],\n      [6, 30, 54, 78, 102],\n      [6, 28, 54, 80, 106],\n      [6, 32, 58, 84, 110],\n      [6, 30, 58, 86, 114],\n      [6, 34, 62, 90, 118],\n      [6, 26, 50, 74, 98, 122],\n      [6, 30, 54, 78, 102, 126],\n      [6, 26, 52, 78, 104, 130],\n      [6, 30, 56, 82, 108, 134],\n      [6, 34, 60, 86, 112, 138],\n      [6, 30, 58, 86, 114, 142],\n      [6, 34, 62, 90, 118, 146],\n      [6, 30, 54, 78, 102, 126, 150],\n      [6, 24, 50, 76, 102, 128, 154],\n      [6, 28, 54, 80, 106, 132, 158],\n      [6, 32, 58, 84, 110, 136, 162],\n      [6, 26, 54, 82, 110, 138, 166],\n      [6, 30, 58, 86, 114, 142, 170]\n    ];\n    var G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0);\n    var G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0);\n    var G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1);\n\n    var _this = {};\n\n    var getBCHDigit = function(data) {\n      var digit = 0;\n      while (data != 0) {\n        digit += 1;\n        data >>>= 1;\n      }\n      return digit;\n    };\n\n    _this.getBCHTypeInfo = function(data) {\n      var d = data << 10;\n      while (getBCHDigit(d) - getBCHDigit(G15) >= 0) {\n        d ^= (G15 << (getBCHDigit(d) - getBCHDigit(G15) ) );\n      }\n      return ( (data << 10) | d) ^ G15_MASK;\n    };\n\n    _this.getBCHTypeNumber = function(data) {\n      var d = data << 12;\n      while (getBCHDigit(d) - getBCHDigit(G18) >= 0) {\n        d ^= (G18 << (getBCHDigit(d) - getBCHDigit(G18) ) );\n      }\n      return (data << 12) | d;\n    };\n\n    _this.getPatternPosition = function(typeNumber) {\n      return PATTERN_POSITION_TABLE[typeNumber - 1];\n    };\n\n    _this.getMaskFunction = function(maskPattern) {\n\n      switch (maskPattern) {\n\n      case QRMaskPattern.PATTERN000 :\n        return function(i, j) { return (i + j) % 2 == 0; };\n      case QRMaskPattern.PATTERN001 :\n        return function(i, j) { return i % 2 == 0; };\n      case QRMaskPattern.PATTERN010 :\n        return function(i, j) { return j % 3 == 0; };\n      case QRMaskPattern.PATTERN011 :\n        return function(i, j) { return (i + j) % 3 == 0; };\n      case QRMaskPattern.PATTERN100 :\n        return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0; };\n      case QRMaskPattern.PATTERN101 :\n        return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0; };\n      case QRMaskPattern.PATTERN110 :\n        return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0; };\n      case QRMaskPattern.PATTERN111 :\n        return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0; };\n\n      default :\n        throw 'bad maskPattern:' + maskPattern;\n      }\n    };\n\n    _this.getErrorCorrectPolynomial = function(errorCorrectLength) {\n      var a = qrPolynomial([1], 0);\n      for (var i = 0; i < errorCorrectLength; i += 1) {\n        a = a.multiply(qrPolynomial([1, QRMath.gexp(i)], 0) );\n      }\n      return a;\n    };\n\n    _this.getLengthInBits = function(mode, type) {\n\n      if (1 <= type && type < 10) {\n\n        // 1 - 9\n\n        switch(mode) {\n        case QRMode.MODE_NUMBER    : return 10;\n        case QRMode.MODE_ALPHA_NUM : return 9;\n        case QRMode.MODE_8BIT_BYTE : return 8;\n        case QRMode.MODE_KANJI     : return 8;\n        default :\n          throw 'mode:' + mode;\n        }\n\n      } else if (type < 27) {\n\n        // 10 - 26\n\n        switch(mode) {\n        case QRMode.MODE_NUMBER    : return 12;\n        case QRMode.MODE_ALPHA_NUM : return 11;\n        case QRMode.MODE_8BIT_BYTE : return 16;\n        case QRMode.MODE_KANJI     : return 10;\n        default :\n          throw 'mode:' + mode;\n        }\n\n      } else if (type < 41) {\n\n        // 27 - 40\n\n        switch(mode) {\n        case QRMode.MODE_NUMBER    : return 14;\n        case QRMode.MODE_ALPHA_NUM : return 13;\n        case QRMode.MODE_8BIT_BYTE : return 16;\n        case QRMode.MODE_KANJI     : return 12;\n        default :\n          throw 'mode:' + mode;\n        }\n\n      } else {\n        throw 'type:' + type;\n      }\n    };\n\n    _this.getLostPoint = function(qrcode) {\n\n      var moduleCount = qrcode.getModuleCount();\n\n      var lostPoint = 0;\n\n      // LEVEL1\n\n      for (var row = 0; row < moduleCount; row += 1) {\n        for (var col = 0; col < moduleCount; col += 1) {\n\n          var sameCount = 0;\n          var dark = qrcode.isDark(row, col);\n\n          for (var r = -1; r <= 1; r += 1) {\n\n            if (row + r < 0 || moduleCount <= row + r) {\n              continue;\n            }\n\n            for (var c = -1; c <= 1; c += 1) {\n\n              if (col + c < 0 || moduleCount <= col + c) {\n                continue;\n              }\n\n              if (r == 0 && c == 0) {\n                continue;\n              }\n\n              if (dark == qrcode.isDark(row + r, col + c) ) {\n                sameCount += 1;\n              }\n            }\n          }\n\n          if (sameCount > 5) {\n            lostPoint += (3 + sameCount - 5);\n          }\n        }\n      };\n\n      // LEVEL2\n\n      for (var row = 0; row < moduleCount - 1; row += 1) {\n        for (var col = 0; col < moduleCount - 1; col += 1) {\n          var count = 0;\n          if (qrcode.isDark(row, col) ) count += 1;\n          if (qrcode.isDark(row + 1, col) ) count += 1;\n          if (qrcode.isDark(row, col + 1) ) count += 1;\n          if (qrcode.isDark(row + 1, col + 1) ) count += 1;\n          if (count == 0 || count == 4) {\n            lostPoint += 3;\n          }\n        }\n      }\n\n      // LEVEL3\n\n      for (var row = 0; row < moduleCount; row += 1) {\n        for (var col = 0; col < moduleCount - 6; col += 1) {\n          if (qrcode.isDark(row, col)\n              && !qrcode.isDark(row, col + 1)\n              &&  qrcode.isDark(row, col + 2)\n              &&  qrcode.isDark(row, col + 3)\n              &&  qrcode.isDark(row, col + 4)\n              && !qrcode.isDark(row, col + 5)\n              &&  qrcode.isDark(row, col + 6) ) {\n            lostPoint += 40;\n          }\n        }\n      }\n\n      for (var col = 0; col < moduleCount; col += 1) {\n        for (var row = 0; row < moduleCount - 6; row += 1) {\n          if (qrcode.isDark(row, col)\n              && !qrcode.isDark(row + 1, col)\n              &&  qrcode.isDark(row + 2, col)\n              &&  qrcode.isDark(row + 3, col)\n              &&  qrcode.isDark(row + 4, col)\n              && !qrcode.isDark(row + 5, col)\n              &&  qrcode.isDark(row + 6, col) ) {\n            lostPoint += 40;\n          }\n        }\n      }\n\n      // LEVEL4\n\n      var darkCount = 0;\n\n      for (var col = 0; col < moduleCount; col += 1) {\n        for (var row = 0; row < moduleCount; row += 1) {\n          if (qrcode.isDark(row, col) ) {\n            darkCount += 1;\n          }\n        }\n      }\n\n      var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n      lostPoint += ratio * 10;\n\n      return lostPoint;\n    };\n\n    return _this;\n  }();\n\n  //---------------------------------------------------------------------\n  // QRMath\n  //---------------------------------------------------------------------\n\n  var QRMath = function() {\n\n    var EXP_TABLE = new Array(256);\n    var LOG_TABLE = new Array(256);\n\n    // initialize tables\n    for (var i = 0; i < 8; i += 1) {\n      EXP_TABLE[i] = 1 << i;\n    }\n    for (var i = 8; i < 256; i += 1) {\n      EXP_TABLE[i] = EXP_TABLE[i - 4]\n        ^ EXP_TABLE[i - 5]\n        ^ EXP_TABLE[i - 6]\n        ^ EXP_TABLE[i - 8];\n    }\n    for (var i = 0; i < 255; i += 1) {\n      LOG_TABLE[EXP_TABLE[i] ] = i;\n    }\n\n    var _this = {};\n\n    _this.glog = function(n) {\n\n      if (n < 1) {\n        throw 'glog(' + n + ')';\n      }\n\n      return LOG_TABLE[n];\n    };\n\n    _this.gexp = function(n) {\n\n      while (n < 0) {\n        n += 255;\n      }\n\n      while (n >= 256) {\n        n -= 255;\n      }\n\n      return EXP_TABLE[n];\n    };\n\n    return _this;\n  }();\n\n  //---------------------------------------------------------------------\n  // qrPolynomial\n  //---------------------------------------------------------------------\n\n  function qrPolynomial(num, shift) {\n\n    if (typeof num.length == 'undefined') {\n      throw num.length + '/' + shift;\n    }\n\n    var _num = function() {\n      var offset = 0;\n      while (offset < num.length && num[offset] == 0) {\n        offset += 1;\n      }\n      var _num = new Array(num.length - offset + shift);\n      for (var i = 0; i < num.length - offset; i += 1) {\n        _num[i] = num[i + offset];\n      }\n      return _num;\n    }();\n\n    var _this = {};\n\n    _this.getAt = function(index) {\n      return _num[index];\n    };\n\n    _this.getLength = function() {\n      return _num.length;\n    };\n\n    _this.multiply = function(e) {\n\n      var num = new Array(_this.getLength() + e.getLength() - 1);\n\n      for (var i = 0; i < _this.getLength(); i += 1) {\n        for (var j = 0; j < e.getLength(); j += 1) {\n          num[i + j] ^= QRMath.gexp(QRMath.glog(_this.getAt(i) ) + QRMath.glog(e.getAt(j) ) );\n        }\n      }\n\n      return qrPolynomial(num, 0);\n    };\n\n    _this.mod = function(e) {\n\n      if (_this.getLength() - e.getLength() < 0) {\n        return _this;\n      }\n\n      var ratio = QRMath.glog(_this.getAt(0) ) - QRMath.glog(e.getAt(0) );\n\n      var num = new Array(_this.getLength() );\n      for (var i = 0; i < _this.getLength(); i += 1) {\n        num[i] = _this.getAt(i);\n      }\n\n      for (var i = 0; i < e.getLength(); i += 1) {\n        num[i] ^= QRMath.gexp(QRMath.glog(e.getAt(i) ) + ratio);\n      }\n\n      // recursive call\n      return qrPolynomial(num, 0).mod(e);\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // QRRSBlock\n  //---------------------------------------------------------------------\n\n  var QRRSBlock = function() {\n\n    var RS_BLOCK_TABLE = [\n\n      // L\n      // M\n      // Q\n      // H\n\n      // 1\n      [1, 26, 19],\n      [1, 26, 16],\n      [1, 26, 13],\n      [1, 26, 9],\n\n      // 2\n      [1, 44, 34],\n      [1, 44, 28],\n      [1, 44, 22],\n      [1, 44, 16],\n\n      // 3\n      [1, 70, 55],\n      [1, 70, 44],\n      [2, 35, 17],\n      [2, 35, 13],\n\n      // 4\n      [1, 100, 80],\n      [2, 50, 32],\n      [2, 50, 24],\n      [4, 25, 9],\n\n      // 5\n      [1, 134, 108],\n      [2, 67, 43],\n      [2, 33, 15, 2, 34, 16],\n      [2, 33, 11, 2, 34, 12],\n\n      // 6\n      [2, 86, 68],\n      [4, 43, 27],\n      [4, 43, 19],\n      [4, 43, 15],\n\n      // 7\n      [2, 98, 78],\n      [4, 49, 31],\n      [2, 32, 14, 4, 33, 15],\n      [4, 39, 13, 1, 40, 14],\n\n      // 8\n      [2, 121, 97],\n      [2, 60, 38, 2, 61, 39],\n      [4, 40, 18, 2, 41, 19],\n      [4, 40, 14, 2, 41, 15],\n\n      // 9\n      [2, 146, 116],\n      [3, 58, 36, 2, 59, 37],\n      [4, 36, 16, 4, 37, 17],\n      [4, 36, 12, 4, 37, 13],\n\n      // 10\n      [2, 86, 68, 2, 87, 69],\n      [4, 69, 43, 1, 70, 44],\n      [6, 43, 19, 2, 44, 20],\n      [6, 43, 15, 2, 44, 16],\n\n      // 11\n      [4, 101, 81],\n      [1, 80, 50, 4, 81, 51],\n      [4, 50, 22, 4, 51, 23],\n      [3, 36, 12, 8, 37, 13],\n\n      // 12\n      [2, 116, 92, 2, 117, 93],\n      [6, 58, 36, 2, 59, 37],\n      [4, 46, 20, 6, 47, 21],\n      [7, 42, 14, 4, 43, 15],\n\n      // 13\n      [4, 133, 107],\n      [8, 59, 37, 1, 60, 38],\n      [8, 44, 20, 4, 45, 21],\n      [12, 33, 11, 4, 34, 12],\n\n      // 14\n      [3, 145, 115, 1, 146, 116],\n      [4, 64, 40, 5, 65, 41],\n      [11, 36, 16, 5, 37, 17],\n      [11, 36, 12, 5, 37, 13],\n\n      // 15\n      [5, 109, 87, 1, 110, 88],\n      [5, 65, 41, 5, 66, 42],\n      [5, 54, 24, 7, 55, 25],\n      [11, 36, 12, 7, 37, 13],\n\n      // 16\n      [5, 122, 98, 1, 123, 99],\n      [7, 73, 45, 3, 74, 46],\n      [15, 43, 19, 2, 44, 20],\n      [3, 45, 15, 13, 46, 16],\n\n      // 17\n      [1, 135, 107, 5, 136, 108],\n      [10, 74, 46, 1, 75, 47],\n      [1, 50, 22, 15, 51, 23],\n      [2, 42, 14, 17, 43, 15],\n\n      // 18\n      [5, 150, 120, 1, 151, 121],\n      [9, 69, 43, 4, 70, 44],\n      [17, 50, 22, 1, 51, 23],\n      [2, 42, 14, 19, 43, 15],\n\n      // 19\n      [3, 141, 113, 4, 142, 114],\n      [3, 70, 44, 11, 71, 45],\n      [17, 47, 21, 4, 48, 22],\n      [9, 39, 13, 16, 40, 14],\n\n      // 20\n      [3, 135, 107, 5, 136, 108],\n      [3, 67, 41, 13, 68, 42],\n      [15, 54, 24, 5, 55, 25],\n      [15, 43, 15, 10, 44, 16],\n\n      // 21\n      [4, 144, 116, 4, 145, 117],\n      [17, 68, 42],\n      [17, 50, 22, 6, 51, 23],\n      [19, 46, 16, 6, 47, 17],\n\n      // 22\n      [2, 139, 111, 7, 140, 112],\n      [17, 74, 46],\n      [7, 54, 24, 16, 55, 25],\n      [34, 37, 13],\n\n      // 23\n      [4, 151, 121, 5, 152, 122],\n      [4, 75, 47, 14, 76, 48],\n      [11, 54, 24, 14, 55, 25],\n      [16, 45, 15, 14, 46, 16],\n\n      // 24\n      [6, 147, 117, 4, 148, 118],\n      [6, 73, 45, 14, 74, 46],\n      [11, 54, 24, 16, 55, 25],\n      [30, 46, 16, 2, 47, 17],\n\n      // 25\n      [8, 132, 106, 4, 133, 107],\n      [8, 75, 47, 13, 76, 48],\n      [7, 54, 24, 22, 55, 25],\n      [22, 45, 15, 13, 46, 16],\n\n      // 26\n      [10, 142, 114, 2, 143, 115],\n      [19, 74, 46, 4, 75, 47],\n      [28, 50, 22, 6, 51, 23],\n      [33, 46, 16, 4, 47, 17],\n\n      // 27\n      [8, 152, 122, 4, 153, 123],\n      [22, 73, 45, 3, 74, 46],\n      [8, 53, 23, 26, 54, 24],\n      [12, 45, 15, 28, 46, 16],\n\n      // 28\n      [3, 147, 117, 10, 148, 118],\n      [3, 73, 45, 23, 74, 46],\n      [4, 54, 24, 31, 55, 25],\n      [11, 45, 15, 31, 46, 16],\n\n      // 29\n      [7, 146, 116, 7, 147, 117],\n      [21, 73, 45, 7, 74, 46],\n      [1, 53, 23, 37, 54, 24],\n      [19, 45, 15, 26, 46, 16],\n\n      // 30\n      [5, 145, 115, 10, 146, 116],\n      [19, 75, 47, 10, 76, 48],\n      [15, 54, 24, 25, 55, 25],\n      [23, 45, 15, 25, 46, 16],\n\n      // 31\n      [13, 145, 115, 3, 146, 116],\n      [2, 74, 46, 29, 75, 47],\n      [42, 54, 24, 1, 55, 25],\n      [23, 45, 15, 28, 46, 16],\n\n      // 32\n      [17, 145, 115],\n      [10, 74, 46, 23, 75, 47],\n      [10, 54, 24, 35, 55, 25],\n      [19, 45, 15, 35, 46, 16],\n\n      // 33\n      [17, 145, 115, 1, 146, 116],\n      [14, 74, 46, 21, 75, 47],\n      [29, 54, 24, 19, 55, 25],\n      [11, 45, 15, 46, 46, 16],\n\n      // 34\n      [13, 145, 115, 6, 146, 116],\n      [14, 74, 46, 23, 75, 47],\n      [44, 54, 24, 7, 55, 25],\n      [59, 46, 16, 1, 47, 17],\n\n      // 35\n      [12, 151, 121, 7, 152, 122],\n      [12, 75, 47, 26, 76, 48],\n      [39, 54, 24, 14, 55, 25],\n      [22, 45, 15, 41, 46, 16],\n\n      // 36\n      [6, 151, 121, 14, 152, 122],\n      [6, 75, 47, 34, 76, 48],\n      [46, 54, 24, 10, 55, 25],\n      [2, 45, 15, 64, 46, 16],\n\n      // 37\n      [17, 152, 122, 4, 153, 123],\n      [29, 74, 46, 14, 75, 47],\n      [49, 54, 24, 10, 55, 25],\n      [24, 45, 15, 46, 46, 16],\n\n      // 38\n      [4, 152, 122, 18, 153, 123],\n      [13, 74, 46, 32, 75, 47],\n      [48, 54, 24, 14, 55, 25],\n      [42, 45, 15, 32, 46, 16],\n\n      // 39\n      [20, 147, 117, 4, 148, 118],\n      [40, 75, 47, 7, 76, 48],\n      [43, 54, 24, 22, 55, 25],\n      [10, 45, 15, 67, 46, 16],\n\n      // 40\n      [19, 148, 118, 6, 149, 119],\n      [18, 75, 47, 31, 76, 48],\n      [34, 54, 24, 34, 55, 25],\n      [20, 45, 15, 61, 46, 16]\n    ];\n\n    var qrRSBlock = function(totalCount, dataCount) {\n      var _this = {};\n      _this.totalCount = totalCount;\n      _this.dataCount = dataCount;\n      return _this;\n    };\n\n    var _this = {};\n\n    var getRsBlockTable = function(typeNumber, errorCorrectionLevel) {\n\n      switch(errorCorrectionLevel) {\n      case QRErrorCorrectionLevel.L :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n      case QRErrorCorrectionLevel.M :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n      case QRErrorCorrectionLevel.Q :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n      case QRErrorCorrectionLevel.H :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n      default :\n        return undefined;\n      }\n    };\n\n    _this.getRSBlocks = function(typeNumber, errorCorrectionLevel) {\n\n      var rsBlock = getRsBlockTable(typeNumber, errorCorrectionLevel);\n\n      if (typeof rsBlock == 'undefined') {\n        throw 'bad rs block @ typeNumber:' + typeNumber +\n            '/errorCorrectionLevel:' + errorCorrectionLevel;\n      }\n\n      var length = rsBlock.length / 3;\n\n      var list = [];\n\n      for (var i = 0; i < length; i += 1) {\n\n        var count = rsBlock[i * 3 + 0];\n        var totalCount = rsBlock[i * 3 + 1];\n        var dataCount = rsBlock[i * 3 + 2];\n\n        for (var j = 0; j < count; j += 1) {\n          list.push(qrRSBlock(totalCount, dataCount) );\n        }\n      }\n\n      return list;\n    };\n\n    return _this;\n  }();\n\n  //---------------------------------------------------------------------\n  // qrBitBuffer\n  //---------------------------------------------------------------------\n\n  var qrBitBuffer = function() {\n\n    var _buffer = [];\n    var _length = 0;\n\n    var _this = {};\n\n    _this.getBuffer = function() {\n      return _buffer;\n    };\n\n    _this.getAt = function(index) {\n      var bufIndex = Math.floor(index / 8);\n      return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1;\n    };\n\n    _this.put = function(num, length) {\n      for (var i = 0; i < length; i += 1) {\n        _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);\n      }\n    };\n\n    _this.getLengthInBits = function() {\n      return _length;\n    };\n\n    _this.putBit = function(bit) {\n\n      var bufIndex = Math.floor(_length / 8);\n      if (_buffer.length <= bufIndex) {\n        _buffer.push(0);\n      }\n\n      if (bit) {\n        _buffer[bufIndex] |= (0x80 >>> (_length % 8) );\n      }\n\n      _length += 1;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrNumber\n  //---------------------------------------------------------------------\n\n  var qrNumber = function(data) {\n\n    var _mode = QRMode.MODE_NUMBER;\n    var _data = data;\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return _data.length;\n    };\n\n    _this.write = function(buffer) {\n\n      var data = _data;\n\n      var i = 0;\n\n      while (i + 2 < data.length) {\n        buffer.put(strToNum(data.substring(i, i + 3) ), 10);\n        i += 3;\n      }\n\n      if (i < data.length) {\n        if (data.length - i == 1) {\n          buffer.put(strToNum(data.substring(i, i + 1) ), 4);\n        } else if (data.length - i == 2) {\n          buffer.put(strToNum(data.substring(i, i + 2) ), 7);\n        }\n      }\n    };\n\n    var strToNum = function(s) {\n      var num = 0;\n      for (var i = 0; i < s.length; i += 1) {\n        num = num * 10 + chatToNum(s.charAt(i) );\n      }\n      return num;\n    };\n\n    var chatToNum = function(c) {\n      if ('0' <= c && c <= '9') {\n        return c.charCodeAt(0) - '0'.charCodeAt(0);\n      }\n      throw 'illegal char :' + c;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrAlphaNum\n  //---------------------------------------------------------------------\n\n  var qrAlphaNum = function(data) {\n\n    var _mode = QRMode.MODE_ALPHA_NUM;\n    var _data = data;\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return _data.length;\n    };\n\n    _this.write = function(buffer) {\n\n      var s = _data;\n\n      var i = 0;\n\n      while (i + 1 < s.length) {\n        buffer.put(\n          getCode(s.charAt(i) ) * 45 +\n          getCode(s.charAt(i + 1) ), 11);\n        i += 2;\n      }\n\n      if (i < s.length) {\n        buffer.put(getCode(s.charAt(i) ), 6);\n      }\n    };\n\n    var getCode = function(c) {\n\n      if ('0' <= c && c <= '9') {\n        return c.charCodeAt(0) - '0'.charCodeAt(0);\n      } else if ('A' <= c && c <= 'Z') {\n        return c.charCodeAt(0) - 'A'.charCodeAt(0) + 10;\n      } else {\n        switch (c) {\n        case ' ' : return 36;\n        case '$' : return 37;\n        case '%' : return 38;\n        case '*' : return 39;\n        case '+' : return 40;\n        case '-' : return 41;\n        case '.' : return 42;\n        case '/' : return 43;\n        case ':' : return 44;\n        default :\n          throw 'illegal char :' + c;\n        }\n      }\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qr8BitByte\n  //---------------------------------------------------------------------\n\n  var qr8BitByte = function(data) {\n\n    var _mode = QRMode.MODE_8BIT_BYTE;\n    var _data = data;\n    var _bytes = qrcode.stringToBytes(data);\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return _bytes.length;\n    };\n\n    _this.write = function(buffer) {\n      for (var i = 0; i < _bytes.length; i += 1) {\n        buffer.put(_bytes[i], 8);\n      }\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrKanji\n  //---------------------------------------------------------------------\n\n  var qrKanji = function(data) {\n\n    var _mode = QRMode.MODE_KANJI;\n    var _data = data;\n\n    var stringToBytes = qrcode.stringToBytesFuncs['SJIS'];\n    if (!stringToBytes) {\n      throw 'sjis not supported.';\n    }\n    !function(c, code) {\n      // self test for sjis support.\n      var test = stringToBytes(c);\n      if (test.length != 2 || ( (test[0] << 8) | test[1]) != code) {\n        throw 'sjis not supported.';\n      }\n    }('\\u53cb', 0x9746);\n\n    var _bytes = stringToBytes(data);\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return ~~(_bytes.length / 2);\n    };\n\n    _this.write = function(buffer) {\n\n      var data = _bytes;\n\n      var i = 0;\n\n      while (i + 1 < data.length) {\n\n        var c = ( (0xff & data[i]) << 8) | (0xff & data[i + 1]);\n\n        if (0x8140 <= c && c <= 0x9FFC) {\n          c -= 0x8140;\n        } else if (0xE040 <= c && c <= 0xEBBF) {\n          c -= 0xC140;\n        } else {\n          throw 'illegal char at ' + (i + 1) + '/' + c;\n        }\n\n        c = ( (c >>> 8) & 0xff) * 0xC0 + (c & 0xff);\n\n        buffer.put(c, 13);\n\n        i += 2;\n      }\n\n      if (i < data.length) {\n        throw 'illegal char at ' + (i + 1);\n      }\n    };\n\n    return _this;\n  };\n\n  //=====================================================================\n  // GIF Support etc.\n  //\n\n  //---------------------------------------------------------------------\n  // byteArrayOutputStream\n  //---------------------------------------------------------------------\n\n  var byteArrayOutputStream = function() {\n\n    var _bytes = [];\n\n    var _this = {};\n\n    _this.writeByte = function(b) {\n      _bytes.push(b & 0xff);\n    };\n\n    _this.writeShort = function(i) {\n      _this.writeByte(i);\n      _this.writeByte(i >>> 8);\n    };\n\n    _this.writeBytes = function(b, off, len) {\n      off = off || 0;\n      len = len || b.length;\n      for (var i = 0; i < len; i += 1) {\n        _this.writeByte(b[i + off]);\n      }\n    };\n\n    _this.writeString = function(s) {\n      for (var i = 0; i < s.length; i += 1) {\n        _this.writeByte(s.charCodeAt(i) );\n      }\n    };\n\n    _this.toByteArray = function() {\n      return _bytes;\n    };\n\n    _this.toString = function() {\n      var s = '';\n      s += '[';\n      for (var i = 0; i < _bytes.length; i += 1) {\n        if (i > 0) {\n          s += ',';\n        }\n        s += _bytes[i];\n      }\n      s += ']';\n      return s;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // base64EncodeOutputStream\n  //---------------------------------------------------------------------\n\n  var base64EncodeOutputStream = function() {\n\n    var _buffer = 0;\n    var _buflen = 0;\n    var _length = 0;\n    var _base64 = '';\n\n    var _this = {};\n\n    var writeEncoded = function(b) {\n      _base64 += String.fromCharCode(encode(b & 0x3f) );\n    };\n\n    var encode = function(n) {\n      if (n < 0) {\n        // error.\n      } else if (n < 26) {\n        return 0x41 + n;\n      } else if (n < 52) {\n        return 0x61 + (n - 26);\n      } else if (n < 62) {\n        return 0x30 + (n - 52);\n      } else if (n == 62) {\n        return 0x2b;\n      } else if (n == 63) {\n        return 0x2f;\n      }\n      throw 'n:' + n;\n    };\n\n    _this.writeByte = function(n) {\n\n      _buffer = (_buffer << 8) | (n & 0xff);\n      _buflen += 8;\n      _length += 1;\n\n      while (_buflen >= 6) {\n        writeEncoded(_buffer >>> (_buflen - 6) );\n        _buflen -= 6;\n      }\n    };\n\n    _this.flush = function() {\n\n      if (_buflen > 0) {\n        writeEncoded(_buffer << (6 - _buflen) );\n        _buffer = 0;\n        _buflen = 0;\n      }\n\n      if (_length % 3 != 0) {\n        // padding\n        var padlen = 3 - _length % 3;\n        for (var i = 0; i < padlen; i += 1) {\n          _base64 += '=';\n        }\n      }\n    };\n\n    _this.toString = function() {\n      return _base64;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // base64DecodeInputStream\n  //---------------------------------------------------------------------\n\n  var base64DecodeInputStream = function(str) {\n\n    var _str = str;\n    var _pos = 0;\n    var _buffer = 0;\n    var _buflen = 0;\n\n    var _this = {};\n\n    _this.read = function() {\n\n      while (_buflen < 8) {\n\n        if (_pos >= _str.length) {\n          if (_buflen == 0) {\n            return -1;\n          }\n          throw 'unexpected end of file./' + _buflen;\n        }\n\n        var c = _str.charAt(_pos);\n        _pos += 1;\n\n        if (c == '=') {\n          _buflen = 0;\n          return -1;\n        } else if (c.match(/^\\s$/) ) {\n          // ignore if whitespace.\n          continue;\n        }\n\n        _buffer = (_buffer << 6) | decode(c.charCodeAt(0) );\n        _buflen += 6;\n      }\n\n      var n = (_buffer >>> (_buflen - 8) ) & 0xff;\n      _buflen -= 8;\n      return n;\n    };\n\n    var decode = function(c) {\n      if (0x41 <= c && c <= 0x5a) {\n        return c - 0x41;\n      } else if (0x61 <= c && c <= 0x7a) {\n        return c - 0x61 + 26;\n      } else if (0x30 <= c && c <= 0x39) {\n        return c - 0x30 + 52;\n      } else if (c == 0x2b) {\n        return 62;\n      } else if (c == 0x2f) {\n        return 63;\n      } else {\n        throw 'c:' + c;\n      }\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // gifImage (B/W)\n  //---------------------------------------------------------------------\n\n  var gifImage = function(width, height) {\n\n    var _width = width;\n    var _height = height;\n    var _data = new Array(width * height);\n\n    var _this = {};\n\n    _this.setPixel = function(x, y, pixel) {\n      _data[y * _width + x] = pixel;\n    };\n\n    _this.write = function(out) {\n\n      //---------------------------------\n      // GIF Signature\n\n      out.writeString('GIF87a');\n\n      //---------------------------------\n      // Screen Descriptor\n\n      out.writeShort(_width);\n      out.writeShort(_height);\n\n      out.writeByte(0x80); // 2bit\n      out.writeByte(0);\n      out.writeByte(0);\n\n      //---------------------------------\n      // Global Color Map\n\n      // black\n      out.writeByte(0x00);\n      out.writeByte(0x00);\n      out.writeByte(0x00);\n\n      // white\n      out.writeByte(0xff);\n      out.writeByte(0xff);\n      out.writeByte(0xff);\n\n      //---------------------------------\n      // Image Descriptor\n\n      out.writeString(',');\n      out.writeShort(0);\n      out.writeShort(0);\n      out.writeShort(_width);\n      out.writeShort(_height);\n      out.writeByte(0);\n\n      //---------------------------------\n      // Local Color Map\n\n      //---------------------------------\n      // Raster Data\n\n      var lzwMinCodeSize = 2;\n      var raster = getLZWRaster(lzwMinCodeSize);\n\n      out.writeByte(lzwMinCodeSize);\n\n      var offset = 0;\n\n      while (raster.length - offset > 255) {\n        out.writeByte(255);\n        out.writeBytes(raster, offset, 255);\n        offset += 255;\n      }\n\n      out.writeByte(raster.length - offset);\n      out.writeBytes(raster, offset, raster.length - offset);\n      out.writeByte(0x00);\n\n      //---------------------------------\n      // GIF Terminator\n      out.writeString(';');\n    };\n\n    var bitOutputStream = function(out) {\n\n      var _out = out;\n      var _bitLength = 0;\n      var _bitBuffer = 0;\n\n      var _this = {};\n\n      _this.write = function(data, length) {\n\n        if ( (data >>> length) != 0) {\n          throw 'length over';\n        }\n\n        while (_bitLength + length >= 8) {\n          _out.writeByte(0xff & ( (data << _bitLength) | _bitBuffer) );\n          length -= (8 - _bitLength);\n          data >>>= (8 - _bitLength);\n          _bitBuffer = 0;\n          _bitLength = 0;\n        }\n\n        _bitBuffer = (data << _bitLength) | _bitBuffer;\n        _bitLength = _bitLength + length;\n      };\n\n      _this.flush = function() {\n        if (_bitLength > 0) {\n          _out.writeByte(_bitBuffer);\n        }\n      };\n\n      return _this;\n    };\n\n    var getLZWRaster = function(lzwMinCodeSize) {\n\n      var clearCode = 1 << lzwMinCodeSize;\n      var endCode = (1 << lzwMinCodeSize) + 1;\n      var bitLength = lzwMinCodeSize + 1;\n\n      // Setup LZWTable\n      var table = lzwTable();\n\n      for (var i = 0; i < clearCode; i += 1) {\n        table.add(String.fromCharCode(i) );\n      }\n      table.add(String.fromCharCode(clearCode) );\n      table.add(String.fromCharCode(endCode) );\n\n      var byteOut = byteArrayOutputStream();\n      var bitOut = bitOutputStream(byteOut);\n\n      // clear code\n      bitOut.write(clearCode, bitLength);\n\n      var dataIndex = 0;\n\n      var s = String.fromCharCode(_data[dataIndex]);\n      dataIndex += 1;\n\n      while (dataIndex < _data.length) {\n\n        var c = String.fromCharCode(_data[dataIndex]);\n        dataIndex += 1;\n\n        if (table.contains(s + c) ) {\n\n          s = s + c;\n\n        } else {\n\n          bitOut.write(table.indexOf(s), bitLength);\n\n          if (table.size() < 0xfff) {\n\n            if (table.size() == (1 << bitLength) ) {\n              bitLength += 1;\n            }\n\n            table.add(s + c);\n          }\n\n          s = c;\n        }\n      }\n\n      bitOut.write(table.indexOf(s), bitLength);\n\n      // end code\n      bitOut.write(endCode, bitLength);\n\n      bitOut.flush();\n\n      return byteOut.toByteArray();\n    };\n\n    var lzwTable = function() {\n\n      var _map = {};\n      var _size = 0;\n\n      var _this = {};\n\n      _this.add = function(key) {\n        if (_this.contains(key) ) {\n          throw 'dup key:' + key;\n        }\n        _map[key] = _size;\n        _size += 1;\n      };\n\n      _this.size = function() {\n        return _size;\n      };\n\n      _this.indexOf = function(key) {\n        return _map[key];\n      };\n\n      _this.contains = function(key) {\n        return typeof _map[key] != 'undefined';\n      };\n\n      return _this;\n    };\n\n    return _this;\n  };\n\n  var createDataURL = function(width, height, getPixel) {\n    var gif = gifImage(width, height);\n    for (var y = 0; y < height; y += 1) {\n      for (var x = 0; x < width; x += 1) {\n        gif.setPixel(x, y, getPixel(x, y) );\n      }\n    }\n\n    var b = byteArrayOutputStream();\n    gif.write(b);\n\n    var base64 = base64EncodeOutputStream();\n    var bytes = b.toByteArray();\n    for (var i = 0; i < bytes.length; i += 1) {\n      base64.writeByte(bytes[i]);\n    }\n    base64.flush();\n\n    return 'data:image/gif;base64,' + base64;\n  };\n\n  //---------------------------------------------------------------------\n  // returns qrcode function.\n\n  return qrcode;\n}();\n\n// multibyte support\n!function() {\n\n  qrcode.stringToBytesFuncs['UTF-8'] = function(s) {\n    // http://stackoverflow.com/questions/18729405/how-to-convert-utf8-string-to-byte-array\n    function toUTF8Array(str) {\n      var utf8 = [];\n      for (var i=0; i < str.length; i++) {\n        var charcode = str.charCodeAt(i);\n        if (charcode < 0x80) utf8.push(charcode);\n        else if (charcode < 0x800) {\n          utf8.push(0xc0 | (charcode >> 6),\n              0x80 | (charcode & 0x3f));\n        }\n        else if (charcode < 0xd800 || charcode >= 0xe000) {\n          utf8.push(0xe0 | (charcode >> 12),\n              0x80 | ((charcode>>6) & 0x3f),\n              0x80 | (charcode & 0x3f));\n        }\n        // surrogate pair\n        else {\n          i++;\n          // UTF-16 encodes 0x10000-0x10FFFF by\n          // subtracting 0x10000 and splitting the\n          // 20 bits of 0x0-0xFFFFF into two halves\n          charcode = 0x10000 + (((charcode & 0x3ff)<<10)\n            | (str.charCodeAt(i) & 0x3ff));\n          utf8.push(0xf0 | (charcode >>18),\n              0x80 | ((charcode>>12) & 0x3f),\n              0x80 | ((charcode>>6) & 0x3f),\n              0x80 | (charcode & 0x3f));\n        }\n      }\n      return utf8;\n    }\n    return toUTF8Array(s);\n  };\n\n}();\n\n(function (factory) {\n  if (typeof define === 'function' && define.amd) {\n      define([], factory);\n  } else if (typeof exports === 'object') {\n      module.exports = factory();\n  }\n}(function () {\n    return qrcode;\n}));\n", "/*! qrcanvas-vue v2.1.1 | ISC License */\nimport Vue from 'vue';\nimport { qrcanvas } from 'qrcanvas';\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar _excluded = [\"options\"];\nvar QRCanvas = Vue.extend({\n  props: {\n    options: Object\n  },\n  render: function render(h) {\n    var _this$$props = this.$props;\n        _this$$props.options;\n        var rest = _objectWithoutPropertiesLoose(_this$$props, _excluded);\n\n    return h('canvas', rest);\n  },\n  methods: {\n    update: function update(options) {\n      // Render only if mounted, skip SSR.\n      if (!this.mounted) return;\n      this.$emit('beforeUpdate', this.$el);\n      qrcanvas(_extends({}, options, {\n        canvas: this.$el\n      }));\n      this.$emit('updated', this.$el);\n    }\n  },\n  watch: {\n    options: 'update',\n    width: 'update',\n    height: 'update'\n  },\n  mounted: function mounted() {\n    this.mounted = true;\n    this.update(this.options);\n  }\n});\n\nexport { QRCanvas };\n", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "/*! qrcanvas v3.1.2 | ISC License */\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport qrcode from 'qrcode-generator';\n\nvar COLOR_BLACK = '#000';\nvar COLOR_WHITE = '#fff';\n\nvar helpers = {\n  createCanvas: createCanvas,\n  isCanvas: isCanvas,\n  isDrawable: isDrawable,\n  getCanvas: getCanvas,\n  updateCanvas: updateCanvas,\n  drawCanvas: drawCanvas,\n  drawText: drawText\n};\n\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n\nfunction isCanvas(el) {\n  return el instanceof HTMLCanvasElement;\n}\n\nfunction isDrawable(el) {\n  return isCanvas(el) || el instanceof HTMLImageElement;\n}\n/**\n * @desc Create a new canvas.\n * @param {Int} width Width of the canvas.\n * @param {Int} height Height of the canvas.\n * @return {Canvas}\n */\n\n\nfunction getCanvas(width, height) {\n  return helpers.createCanvas(width, height == null ? width : height);\n}\n\nfunction updateCanvas(canvas, width, height) {\n  if (canvas) {\n    canvas.width = width;\n    canvas.height = height == null ? width : height;\n    return canvas;\n  }\n\n  return getCanvas(width, height);\n}\n\n/**\n * @desc Draw to the canvas with given image or colors.\n * @param {Canvas} canvas The canvas to initialize.\n * @param {Image | String | Array} data\n * @param {Object} options\n *    cellSize: {Int}\n *    clear: {Boolean}\n */\nfunction drawCanvas(canvas, data, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      cellSize = _options.cellSize,\n      context = _options.context,\n      _options$clear = _options.clear,\n      clear = _options$clear === void 0 ? true : _options$clear;\n  var width = canvas.width,\n      height = canvas.height;\n  var queue = [data];\n  var ctx = context || canvas.getContext('2d');\n  if (clear) ctx.clearRect(0, 0, width, height);\n  ctx.globalCompositeOperation = 'source-over';\n\n  while (queue.length) {\n    var item = queue.shift();\n\n    if (Array.isArray(item)) {\n      queue = item.concat(queue);\n    } else if (item) {\n      var obj = void 0;\n\n      if (helpers.isDrawable(item)) {\n        obj = {\n          image: item\n        };\n      } else if (typeof item === 'string') {\n        obj = {\n          style: item\n        };\n      } else {\n        obj = item;\n      }\n\n      var x = (obj.col == null ? obj.x : obj.col * cellSize) || 0;\n      var y = (obj.row == null ? obj.y : obj.row * cellSize) || 0;\n      if (x < 0) x += width;\n      if (y < 0) y += width;\n      var w = ('cols' in obj ? obj.cols * cellSize : obj.w) || width;\n      var h = ('rows' in obj ? obj.rows * cellSize : obj.h) || width;\n\n      if (obj.image) {\n        ctx.drawImage(obj.image, x, y, w, h);\n      } else {\n        ctx.fillStyle = obj.style || 'black';\n        ctx.fillRect(x, y, w, h);\n      }\n    }\n  }\n\n  return canvas;\n}\n\nfunction drawText(text, options) {\n  var _ref = options || {},\n      _ref$fontSize = _ref.fontSize,\n      fontSize = _ref$fontSize === void 0 ? 64 : _ref$fontSize,\n      _ref$fontStyle = _ref.fontStyle,\n      fontStyle = _ref$fontStyle === void 0 ? '' : _ref$fontStyle,\n      _ref$fontFamily = _ref.fontFamily,\n      fontFamily = _ref$fontFamily === void 0 ? 'Cursive' : _ref$fontFamily,\n      _ref$color = _ref.color,\n      color = _ref$color === void 0 ? null : _ref$color,\n      _ref$pad = _ref.pad,\n      pad = _ref$pad === void 0 ? 8 : _ref$pad,\n      _ref$padColor = _ref.padColor,\n      padColor = _ref$padColor === void 0 ? COLOR_WHITE : _ref$padColor,\n      _ref$mode = _ref.mode,\n      mode = _ref$mode === void 0 ? 1 : _ref$mode;\n\n  var canvas = getCanvas(1);\n  var ctx = canvas.getContext('2d');\n  var padColorArr;\n\n  if (padColor) {\n    ctx.fillStyle = padColor;\n    ctx.fillRect(0, 0, 1, 1);\n\n    var _ctx$getImageData = ctx.getImageData(0, 0, 1, 1);\n\n    padColorArr = _ctx$getImageData.data;\n    if (!padColorArr[3]) padColorArr = null;\n  }\n\n  var height = fontSize + 2 * pad;\n  var font = [fontStyle, fontSize + \"px\", fontFamily].filter(Boolean).join(' ');\n\n  var resetContext = function resetContext() {\n    ctx.textAlign = 'center';\n    ctx.textBaseline = 'middle';\n    ctx.font = font;\n  };\n\n  resetContext();\n  var width = Math.ceil(ctx.measureText(text).width) + 2 * pad;\n  canvas.width = width;\n  canvas.height = height;\n  resetContext();\n\n  var fillText = function fillText() {\n    ctx.fillStyle = color || COLOR_BLACK;\n    ctx.fillText(text, width / 2, height / 2);\n  };\n\n  if (mode === 1) {\n    ctx.fillStyle = padColor;\n    ctx.fillRect(0, 0, width, height);\n    fillText();\n  } else {\n    fillText();\n\n    if (padColorArr) {\n      (function () {\n        var imageData = ctx.getImageData(0, 0, width, height);\n        var data = imageData.data;\n        var total = width * height;\n        var padded = [];\n        var offset = 0;\n\n        var _loop = function _loop(loop) {\n          var current = [];\n          var unique = {};\n          padded[offset] = current;\n          offset = 1 - offset;\n          var last = padded[offset];\n\n          if (!last) {\n            last = [];\n\n            for (var i = 0; i < total; i += 1) {\n              last.push(i);\n            }\n          }\n\n          last.forEach(function (i) {\n            if (data[4 * i + 3]) {\n              [i % width ? i - 1 : -1, (i + 1) % width ? i + 1 : -1, i - width, i + width].forEach(function (j) {\n                var k = 4 * j;\n\n                if (k >= 0 && k <= data.length && !unique[j]) {\n                  unique[j] = 1;\n                  current.push(j);\n                }\n              });\n            }\n          });\n          current.forEach(function (i) {\n            var j = 4 * i;\n\n            if (!data[j + 3]) {\n              for (var k = 0; k < 4; k += 1) {\n                data[j + k] = padColorArr[k];\n              }\n            }\n          });\n        };\n\n        for (var loop = 0; loop < pad; loop += 1) {\n          _loop();\n        }\n\n        ctx.putImageData(imageData, 0, 0);\n      })();\n    }\n  }\n\n  return canvas;\n}\n\nvar effects = {\n  \"default\": renderDefault,\n  round: renderRound,\n  fusion: renderFusion,\n  spot: renderSpot\n};\n\nfunction renderDefault(_ref) {\n  var foreground = _ref.foreground,\n      cellSize = _ref.cellSize,\n      isDark = _ref.isDark,\n      count = _ref.count;\n  var getCanvas = helpers.getCanvas,\n      drawCanvas = helpers.drawCanvas;\n  var width = cellSize * count;\n  var canvasMask = getCanvas(width);\n  var context = canvasMask.getContext('2d');\n  context.fillStyle = COLOR_BLACK;\n  drawCells({\n    cellSize: cellSize,\n    count: count\n  }, function (_ref2) {\n    var i = _ref2.i,\n        j = _ref2.j,\n        x = _ref2.x,\n        y = _ref2.y;\n\n    if (isDark(i, j)) {\n      context.fillRect(x, y, cellSize, cellSize);\n    }\n  });\n  var canvasFg = getCanvas(width);\n  drawCanvas(canvasFg, foreground, {\n    cellSize: cellSize\n  });\n  var ctx = canvasFg.getContext('2d');\n  ctx.globalCompositeOperation = 'destination-in';\n  ctx.drawImage(canvasMask, 0, 0);\n  return canvasFg;\n}\n\nfunction renderRound(_ref3, maskOptions) {\n  var foreground = _ref3.foreground,\n      cellSize = _ref3.cellSize,\n      isDark = _ref3.isDark,\n      count = _ref3.count;\n  var getCanvas = helpers.getCanvas,\n      drawCanvas = helpers.drawCanvas;\n  var width = cellSize * count;\n  var canvasMask = getCanvas(width);\n  var _maskOptions$value = maskOptions.value,\n      value = _maskOptions$value === void 0 ? 1 : _maskOptions$value;\n  var radius = value * cellSize / 2;\n  var context = canvasMask.getContext('2d');\n  context.fillStyle = COLOR_BLACK;\n  drawCells({\n    cellSize: cellSize,\n    count: count\n  }, function (_ref4) {\n    var i = _ref4.i,\n        j = _ref4.j,\n        x = _ref4.x,\n        y = _ref4.y;\n\n    if (isDark(i, j)) {\n      context.beginPath();\n      context.moveTo(x + 0.5 * cellSize, y);\n      drawCorner(context, x + cellSize, y, x + cellSize, y + 0.5 * cellSize, radius);\n      drawCorner(context, x + cellSize, y + cellSize, x + 0.5 * cellSize, y + cellSize, radius);\n      drawCorner(context, x, y + cellSize, x, y + 0.5 * cellSize, radius);\n      drawCorner(context, x, y, x + 0.5 * cellSize, y, radius); // context.closePath();\n\n      context.fill();\n    }\n  });\n  var canvasFg = getCanvas(width);\n  drawCanvas(canvasFg, foreground, {\n    cellSize: cellSize\n  });\n  var ctx = canvasFg.getContext('2d');\n  ctx.globalCompositeOperation = 'destination-in';\n  ctx.drawImage(canvasMask, 0, 0);\n  return canvasFg;\n}\n\nfunction renderFusion(_ref5, maskOptions) {\n  var foreground = _ref5.foreground,\n      cellSize = _ref5.cellSize,\n      isDark = _ref5.isDark,\n      count = _ref5.count;\n  var getCanvas = helpers.getCanvas,\n      drawCanvas = helpers.drawCanvas;\n  var width = cellSize * count;\n  var canvasMask = getCanvas(width);\n  var context = canvasMask.getContext('2d');\n  context.fillStyle = COLOR_BLACK;\n  var _maskOptions$value2 = maskOptions.value,\n      value = _maskOptions$value2 === void 0 ? 1 : _maskOptions$value2;\n  var radius = value * cellSize / 2;\n  drawCells({\n    cellSize: cellSize,\n    count: count\n  }, function (_ref6) {\n    var i = _ref6.i,\n        j = _ref6.j,\n        x = _ref6.x,\n        y = _ref6.y;\n    var corners = [0, 0, 0, 0]; // NW, NE, SE, SW\n\n    if (isDark(i - 1, j)) {\n      corners[0] += 1;\n      corners[1] += 1;\n    }\n\n    if (isDark(i + 1, j)) {\n      corners[2] += 1;\n      corners[3] += 1;\n    }\n\n    if (isDark(i, j - 1)) {\n      corners[0] += 1;\n      corners[3] += 1;\n    }\n\n    if (isDark(i, j + 1)) {\n      corners[1] += 1;\n      corners[2] += 1;\n    }\n\n    if (isDark(i, j)) {\n      if (isDark(i - 1, j - 1)) corners[0] += 1;\n      if (isDark(i - 1, j + 1)) corners[1] += 1;\n      if (isDark(i + 1, j + 1)) corners[2] += 1;\n      if (isDark(i + 1, j - 1)) corners[3] += 1;\n      context.beginPath();\n      context.moveTo(x + 0.5 * cellSize, y);\n      drawCorner(context, x + cellSize, y, x + cellSize, y + 0.5 * cellSize, corners[1] ? 0 : radius);\n      drawCorner(context, x + cellSize, y + cellSize, x + 0.5 * cellSize, y + cellSize, corners[2] ? 0 : radius);\n      drawCorner(context, x, y + cellSize, x, y + 0.5 * cellSize, corners[3] ? 0 : radius);\n      drawCorner(context, x, y, x + 0.5 * cellSize, y, corners[0] ? 0 : radius); // context.closePath();\n\n      context.fill();\n    } else {\n      if (corners[0] === 2) {\n        fillCorner(context, x, y + 0.5 * cellSize, x, y, x + 0.5 * cellSize, y, radius);\n      }\n\n      if (corners[1] === 2) {\n        fillCorner(context, x + 0.5 * cellSize, y, x + cellSize, y, x + cellSize, y + 0.5 * cellSize, radius);\n      }\n\n      if (corners[2] === 2) {\n        fillCorner(context, x + cellSize, y + 0.5 * cellSize, x + cellSize, y + cellSize, x + 0.5 * cellSize, y + cellSize, radius);\n      }\n\n      if (corners[3] === 2) {\n        fillCorner(context, x + 0.5 * cellSize, y + cellSize, x, y + cellSize, x, y + 0.5 * cellSize, radius);\n      }\n    }\n  });\n  var canvasFg = getCanvas(width);\n  drawCanvas(canvasFg, foreground, {\n    cellSize: cellSize\n  });\n  var ctx = canvasFg.getContext('2d');\n  ctx.globalCompositeOperation = 'destination-in';\n  ctx.drawImage(canvasMask, 0, 0);\n  return canvasFg;\n}\n\nfunction renderSpot(_ref7, maskOptions) {\n  var foreground = _ref7.foreground,\n      cellSize = _ref7.cellSize,\n      isDark = _ref7.isDark,\n      count = _ref7.count;\n  var getCanvas = helpers.getCanvas,\n      drawCanvas = helpers.drawCanvas;\n  var width = cellSize * count;\n  var canvasMask = getCanvas(width);\n  var value = maskOptions.value,\n      _maskOptions$foregrou = maskOptions.foregroundLight,\n      foregroundLight = _maskOptions$foregrou === void 0 ? COLOR_WHITE : _maskOptions$foregrou;\n  var context = canvasMask.getContext('2d');\n  var canvasLayer = getCanvas(width);\n  var canvasFg = getCanvas(width);\n  var ctxLayer = canvasLayer.getContext('2d');\n  [{\n    dark: true,\n    foreground: foreground\n  }, {\n    dark: false,\n    foreground: foregroundLight\n  }].forEach(function (item) {\n    context.fillStyle = COLOR_BLACK;\n    context.clearRect(0, 0, width, width);\n    drawCells({\n      cellSize: cellSize,\n      count: count\n    }, function (_ref8) {\n      var i = _ref8.i,\n          j = _ref8.j,\n          x = _ref8.x,\n          y = _ref8.y;\n\n      if (isDark(i, j) ^ +!item.dark) {\n        var fillSize;\n\n        if (i <= 7 && j <= 7 || i <= 7 && count - j - 1 <= 7 || count - i - 1 <= 7 && j <= 7 || i + 5 <= count && i + 9 >= count && j + 5 <= count && j + 9 >= count || i === 7 || j === 7) {\n          fillSize = 1 - 0.1 * value;\n        } else {\n          fillSize = 0.25;\n        }\n\n        var offset = (1 - fillSize) / 2;\n        context.fillRect(x + offset * cellSize, y + offset * cellSize, fillSize * cellSize, fillSize * cellSize);\n      }\n    });\n    drawCanvas(canvasLayer, item.foreground, {\n      cellSize: cellSize,\n      context: ctxLayer\n    });\n    ctxLayer.globalCompositeOperation = 'destination-in';\n    ctxLayer.drawImage(canvasMask, 0, 0);\n    drawCanvas(canvasFg, canvasLayer, {\n      cellSize: cellSize,\n      clear: false\n    });\n  });\n  return canvasFg;\n}\n\nfunction drawCells(_ref9, drawEach) {\n  var cellSize = _ref9.cellSize,\n      count = _ref9.count;\n\n  for (var i = 0; i < count; i += 1) {\n    for (var j = 0; j < count; j += 1) {\n      var x = j * cellSize;\n      var y = i * cellSize;\n      drawEach({\n        i: i,\n        j: j,\n        x: x,\n        y: y\n      });\n    }\n  }\n}\n\nfunction drawCorner(ctx, cornerX, cornerY, x, y, r) {\n  if (r) {\n    ctx.arcTo(cornerX, cornerY, x, y, r);\n  } else {\n    ctx.lineTo(cornerX, cornerY);\n    ctx.lineTo(x, y);\n  }\n}\n\nfunction fillCorner(context, startX, startY, cornerX, cornerY, destX, destY, radius) {\n  context.beginPath();\n  context.moveTo(startX, startY);\n  drawCorner(context, cornerX, cornerY, destX, destY, radius);\n  context.lineTo(cornerX, cornerY);\n  context.lineTo(startX, startY); // context.closePath();\n\n  context.fill();\n}\n\n// Enable UTF_8 support\nqrcode.stringToBytes = qrcode.stringToBytesFuncs['UTF-8'];\nvar DEFAULTS = {\n  background: 'white',\n  foreground: 'black',\n  typeNumber: 0,\n  correctLevel: 'L',\n  data: '',\n  padding: 0,\n  resize: true\n};\n\nvar QRCanvasRenderer = /*#__PURE__*/function () {\n  function QRCanvasRenderer(options) {\n    var _this = this;\n\n    this.options = _extends({}, DEFAULTS);\n    this.cache = {};\n\n    this.isDark = function (i, j) {\n      var _this$cache = _this.cache,\n          qr = _this$cache.qr,\n          count = _this$cache.count;\n      if (i < 0 || i >= count || j < 0 || j >= count) return false;\n      return qr.isDark(i, j);\n    };\n\n    this.setOptions(options);\n  }\n\n  var _proto = QRCanvasRenderer.prototype;\n\n  _proto.render = function render(canvas, config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    var _this$options = this.options,\n        background = _this$options.background,\n        foreground = _this$options.foreground,\n        padding = _this$options.padding,\n        effect = _this$options.effect,\n        logo = _this$options.logo,\n        resize = _this$options.resize;\n    var onRender = effects[effect.type] || effects[\"default\"];\n    var count = this.cache.count;\n    var drawCanvas = helpers.drawCanvas;\n    var _config = config,\n        size = _config.size;\n    var canvasOut;\n    var canvasBg;\n    var canvasFg; // Prepare output canvas, resize it if cellSize or size is provided.\n\n    {\n      var _config2 = config,\n          cellSize = _config2.cellSize;\n      if (!canvas && !cellSize && !size) cellSize = 6;\n      if (cellSize) size = count * cellSize + padding + padding;\n\n      if (size) {\n        canvasOut = resize || !canvas ? helpers.updateCanvas(canvas, size) : canvas;\n      } else {\n        size = canvas.width;\n        canvasOut = canvas;\n      }\n    }\n    var contentSize = size - padding - padding; // Create foreground and background layers on canvas\n\n    {\n      var _cellSize = Math.ceil(contentSize / count);\n\n      var sketchSize = _cellSize * count;\n      canvasBg = helpers.getCanvas(_cellSize * count);\n      drawCanvas(canvasBg, background, {\n        cellSize: _cellSize\n      });\n      canvasFg = onRender(_extends({\n        foreground: foreground,\n        cellSize: _cellSize,\n        isDark: this.isDark\n      }, this.cache), this.options.effect); // draw logo\n\n      if (logo) {\n        var logoLayer = _extends({}, logo);\n\n        if (!logo.w && !logo.h && !logo.cols && !logo.rows) {\n          var _ref = logo.image,\n              width = _ref.width,\n              height = _ref.height;\n          var imageRatio = width / height;\n          var posRatio = Math.min((count - 18) / count, 0.38);\n          var h = Math.min(height, sketchSize * posRatio, sketchSize * posRatio / imageRatio);\n          var w = h * imageRatio;\n          var x = (sketchSize - w) / 2;\n          var y = (sketchSize - h) / 2;\n          logoLayer.w = w;\n          logoLayer.h = h;\n          logoLayer.x = x;\n          logoLayer.y = y;\n        }\n\n        drawCanvas(canvasFg, logoLayer, {\n          clear: false\n        });\n      }\n    } // Combine the layers\n\n    drawCanvas(canvasOut, [{\n      image: canvasBg\n    }, {\n      image: canvasFg,\n      x: padding,\n      y: padding,\n      w: contentSize,\n      h: contentSize\n    }]);\n    return canvasOut;\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = _extends({}, this.options, options);\n    this.normalizeEffect();\n    this.normalizeLogo();\n    var _this$options2 = this.options,\n        typeNumber = _this$options2.typeNumber,\n        data = _this$options2.data,\n        logo = _this$options2.logo; // L / M / Q / H\n\n    var correctLevel = this.options.correctLevel;\n    if (logo && ['Q', 'H'].indexOf(correctLevel) < 0) correctLevel = 'H';\n    var qr = qrcode(typeNumber, correctLevel);\n    qr.addData(data || '');\n    qr.make();\n    var count = qr.getModuleCount();\n    this.cache = {\n      qr: qr,\n      count: count\n    };\n  };\n\n  _proto.normalizeEffect = function normalizeEffect() {\n    var effect = this.options.effect;\n\n    if (typeof effect === 'string') {\n      effect = {\n        type: effect\n      };\n    }\n\n    this.options.effect = effect || {};\n  };\n\n  _proto.normalizeLogo = function normalizeLogo() {\n    var isDrawable = helpers.isDrawable,\n        drawText = helpers.drawText;\n    var logo = this.options.logo;\n\n    if (logo) {\n      if (isDrawable(logo)) {\n        logo = {\n          image: logo\n        };\n      } else if (!isDrawable(logo.image)) {\n        if (typeof logo === 'string') {\n          logo = {\n            text: logo\n          };\n        }\n\n        if (typeof logo.text === 'string') {\n          logo = {\n            image: drawText(logo.text, logo.options)\n          };\n        } else {\n          logo = null;\n        }\n      }\n    }\n\n    this.options.logo = logo;\n  };\n\n  return QRCanvasRenderer;\n}();\n\nfunction qrcanvas(options) {\n  var canvas = options.canvas,\n      size = options.size,\n      cellSize = options.cellSize,\n      rest = _objectWithoutPropertiesLoose(options, [\"canvas\", \"size\", \"cellSize\"]);\n\n  var renderer = new QRCanvasRenderer(rest);\n  return renderer.render(canvas, {\n    size: size,\n    cellSize: cellSize\n  });\n}\n\nfunction setCanvasModule(canvasModule) {\n  var Canvas = canvasModule.Canvas,\n      Image = canvasModule.Image,\n      createCanvas = canvasModule.createCanvas;\n\n  var isCanvas = function isCanvas(el) {\n    return el instanceof Canvas;\n  };\n\n  var isDrawable = function isDrawable(el) {\n    return isCanvas(el) || el instanceof Image;\n  };\n\n  helpers.createCanvas = createCanvas;\n  helpers.isCanvas = isCanvas;\n  helpers.isDrawable = isDrawable;\n}\n\nexport { effects, helpers, qrcanvas, setCanvasModule };\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAiBA,QAAIA,UAAS,WAAW;AAWtB,UAAIA,UAAS,SAAS,YAAY,sBAAsB;AAEtD,YAAI,OAAO;AACX,YAAI,OAAO;AAEX,YAAI,cAAc;AAClB,YAAI,wBAAwB,uBAAuB,oBAAoB;AACvE,YAAI,WAAW;AACf,YAAI,eAAe;AACnB,YAAI,aAAa;AACjB,YAAI,YAAY,CAAC;AAEjB,YAAI,QAAQ,CAAC;AAEb,YAAI,WAAW,SAAS,MAAM,aAAa;AAEzC,yBAAe,cAAc,IAAI;AACjC,qBAAW,SAAS,aAAa;AAC/B,gBAAI,UAAU,IAAI,MAAM,WAAW;AACnC,qBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,sBAAQ,GAAG,IAAI,IAAI,MAAM,WAAW;AACpC,uBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,wBAAQ,GAAG,EAAE,GAAG,IAAI;AAAA,cACtB;AAAA,YACF;AACA,mBAAO;AAAA,UACT,EAAE,YAAY;AAEd,oCAA0B,GAAG,CAAC;AAC9B,oCAA0B,eAAe,GAAG,CAAC;AAC7C,oCAA0B,GAAG,eAAe,CAAC;AAC7C,qCAA2B;AAC3B,6BAAmB;AACnB,wBAAc,MAAM,WAAW;AAE/B,cAAI,eAAe,GAAG;AACpB,4BAAgB,IAAI;AAAA,UACtB;AAEA,cAAI,cAAc,MAAM;AACtB,yBAAa,WAAW,aAAa,uBAAuB,SAAS;AAAA,UACvE;AAEA,kBAAQ,YAAY,WAAW;AAAA,QACjC;AAEA,YAAI,4BAA4B,SAAS,KAAK,KAAK;AAEjD,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AAE/B,gBAAI,MAAM,KAAK,MAAM,gBAAgB,MAAM,EAAG;AAE9C,qBAAS,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AAE/B,kBAAI,MAAM,KAAK,MAAM,gBAAgB,MAAM,EAAG;AAE9C,kBAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAClC,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MACpC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAK;AAC9C,yBAAS,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,cAC/B,OAAO;AACL,yBAAS,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,qBAAqB,WAAW;AAElC,cAAI,eAAe;AACnB,cAAI,UAAU;AAEd,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAE7B,qBAAS,MAAM,CAAC;AAEhB,gBAAI,YAAY,OAAO,aAAa,KAAK;AAEzC,gBAAI,KAAK,KAAK,eAAe,WAAW;AACtC,6BAAe;AACf,wBAAU;AAAA,YACZ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,qBAAqB,WAAW;AAElC,mBAAS,IAAI,GAAG,IAAI,eAAe,GAAG,KAAK,GAAG;AAC5C,gBAAI,SAAS,CAAC,EAAE,CAAC,KAAK,MAAM;AAC1B;AAAA,YACF;AACA,qBAAS,CAAC,EAAE,CAAC,IAAK,IAAI,KAAK;AAAA,UAC7B;AAEA,mBAAS,IAAI,GAAG,IAAI,eAAe,GAAG,KAAK,GAAG;AAC5C,gBAAI,SAAS,CAAC,EAAE,CAAC,KAAK,MAAM;AAC1B;AAAA,YACF;AACA,qBAAS,CAAC,EAAE,CAAC,IAAK,IAAI,KAAK;AAAA,UAC7B;AAAA,QACF;AAEA,YAAI,6BAA6B,WAAW;AAE1C,cAAI,MAAM,OAAO,mBAAmB,WAAW;AAE/C,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAEtC,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAEtC,kBAAI,MAAM,IAAI,CAAC;AACf,kBAAI,MAAM,IAAI,CAAC;AAEf,kBAAI,SAAS,GAAG,EAAE,GAAG,KAAK,MAAM;AAC9B;AAAA,cACF;AAEA,uBAAS,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AAE/B,yBAAS,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AAE/B,sBAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KACjC,KAAK,KAAK,KAAK,GAAK;AAC1B,6BAAS,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,kBAC/B,OAAO;AACL,6BAAS,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,kBAC/B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,kBAAkB,SAAS,MAAM;AAEnC,cAAI,OAAO,OAAO,iBAAiB,WAAW;AAE9C,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,gBAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAC1C,qBAAS,KAAK,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,eAAe,IAAI,CAAC,IAAI;AAAA,UAC9D;AAEA,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,gBAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAC1C,qBAAS,IAAI,IAAI,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI;AAAA,UAC9D;AAAA,QACF;AAEA,YAAI,gBAAgB,SAAS,MAAM,aAAa;AAE9C,cAAI,OAAQ,yBAAyB,IAAK;AAC1C,cAAI,OAAO,OAAO,eAAe,IAAI;AAGrC,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAE9B,gBAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAE1C,gBAAI,IAAI,GAAG;AACT,uBAAS,CAAC,EAAE,CAAC,IAAI;AAAA,YACnB,WAAW,IAAI,GAAG;AAChB,uBAAS,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,YACvB,OAAO;AACL,uBAAS,eAAe,KAAK,CAAC,EAAE,CAAC,IAAI;AAAA,YACvC;AAAA,UACF;AAGA,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAE9B,gBAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAE1C,gBAAI,IAAI,GAAG;AACT,uBAAS,CAAC,EAAE,eAAe,IAAI,CAAC,IAAI;AAAA,YACtC,WAAW,IAAI,GAAG;AAChB,uBAAS,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,IAAI;AAAA,YAChC,OAAO;AACL,uBAAS,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI;AAAA,YAC5B;AAAA,UACF;AAGA,mBAAS,eAAe,CAAC,EAAE,CAAC,IAAK,CAAC;AAAA,QACpC;AAEA,YAAI,UAAU,SAAS,MAAM,aAAa;AAExC,cAAI,MAAM;AACV,cAAI,MAAM,eAAe;AACzB,cAAI,WAAW;AACf,cAAI,YAAY;AAChB,cAAI,WAAW,OAAO,gBAAgB,WAAW;AAEjD,mBAAS,MAAM,eAAe,GAAG,MAAM,GAAG,OAAO,GAAG;AAElD,gBAAI,OAAO,EAAG,QAAO;AAErB,mBAAO,MAAM;AAEX,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAE7B,oBAAI,SAAS,GAAG,EAAE,MAAM,CAAC,KAAK,MAAM;AAElC,sBAAI,OAAO;AAEX,sBAAI,YAAY,KAAK,QAAQ;AAC3B,4BAAY,KAAK,SAAS,MAAM,WAAY,MAAM;AAAA,kBACpD;AAEA,sBAAI,OAAO,SAAS,KAAK,MAAM,CAAC;AAEhC,sBAAI,MAAM;AACR,2BAAO,CAAC;AAAA,kBACV;AAEA,2BAAS,GAAG,EAAE,MAAM,CAAC,IAAI;AACzB,8BAAY;AAEZ,sBAAI,YAAY,IAAI;AAClB,iCAAa;AACb,+BAAW;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAEA,qBAAO;AAEP,kBAAI,MAAM,KAAK,gBAAgB,KAAK;AAClC,uBAAO;AACP,sBAAM,CAAC;AACP;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,cAAc,SAAS,QAAQ,UAAU;AAE3C,cAAI,SAAS;AAEb,cAAI,aAAa;AACjB,cAAI,aAAa;AAEjB,cAAI,SAAS,IAAI,MAAM,SAAS,MAAM;AACtC,cAAI,SAAS,IAAI,MAAM,SAAS,MAAM;AAEtC,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAE3C,gBAAI,UAAU,SAAS,CAAC,EAAE;AAC1B,gBAAI,UAAU,SAAS,CAAC,EAAE,aAAa;AAEvC,yBAAa,KAAK,IAAI,YAAY,OAAO;AACzC,yBAAa,KAAK,IAAI,YAAY,OAAO;AAEzC,mBAAO,CAAC,IAAI,IAAI,MAAM,OAAO;AAE7B,qBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK,GAAG;AAC5C,qBAAO,CAAC,EAAE,CAAC,IAAI,MAAO,OAAO,UAAU,EAAE,IAAI,MAAM;AAAA,YACrD;AACA,sBAAU;AAEV,gBAAI,SAAS,OAAO,0BAA0B,OAAO;AACrD,gBAAI,UAAU,aAAa,OAAO,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC;AAE5D,gBAAI,UAAU,QAAQ,IAAI,MAAM;AAChC,mBAAO,CAAC,IAAI,IAAI,MAAM,OAAO,UAAU,IAAI,CAAC;AAC5C,qBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK,GAAG;AAC5C,kBAAI,WAAW,IAAI,QAAQ,UAAU,IAAI,OAAO,CAAC,EAAE;AACnD,qBAAO,CAAC,EAAE,CAAC,IAAK,YAAY,IAAI,QAAQ,MAAM,QAAQ,IAAI;AAAA,YAC5D;AAAA,UACF;AAEA,cAAI,iBAAiB;AACrB,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,8BAAkB,SAAS,CAAC,EAAE;AAAA,UAChC;AAEA,cAAI,OAAO,IAAI,MAAM,cAAc;AACnC,cAAI,QAAQ;AAEZ,mBAAS,IAAI,GAAG,IAAI,YAAY,KAAK,GAAG;AACtC,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,kBAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACxB,qBAAK,KAAK,IAAI,OAAO,CAAC,EAAE,CAAC;AACzB,yBAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAEA,mBAAS,IAAI,GAAG,IAAI,YAAY,KAAK,GAAG;AACtC,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,kBAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACxB,qBAAK,KAAK,IAAI,OAAO,CAAC,EAAE,CAAC;AACzB,yBAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,aAAa,SAASC,aAAYC,uBAAsB,UAAU;AAEpE,cAAI,WAAW,UAAU,YAAYD,aAAYC,qBAAoB;AAErE,cAAI,SAAS,YAAY;AAEzB,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,gBAAI,OAAO,SAAS,CAAC;AACrB,mBAAO,IAAI,KAAK,QAAQ,GAAG,CAAC;AAC5B,mBAAO,IAAI,KAAK,UAAU,GAAG,OAAO,gBAAgB,KAAK,QAAQ,GAAGD,WAAU,CAAE;AAChF,iBAAK,MAAM,MAAM;AAAA,UACnB;AAGA,cAAI,iBAAiB;AACrB,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,8BAAkB,SAAS,CAAC,EAAE;AAAA,UAChC;AAEA,cAAI,OAAO,gBAAgB,IAAI,iBAAiB,GAAG;AACjD,kBAAM,4BACF,OAAO,gBAAgB,IACvB,MACA,iBAAiB,IACjB;AAAA,UACN;AAGA,cAAI,OAAO,gBAAgB,IAAI,KAAK,iBAAiB,GAAG;AACtD,mBAAO,IAAI,GAAG,CAAC;AAAA,UACjB;AAGA,iBAAO,OAAO,gBAAgB,IAAI,KAAK,GAAG;AACxC,mBAAO,OAAO,KAAK;AAAA,UACrB;AAGA,iBAAO,MAAM;AAEX,gBAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AAClD;AAAA,YACF;AACA,mBAAO,IAAI,MAAM,CAAC;AAElB,gBAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AAClD;AAAA,YACF;AACA,mBAAO,IAAI,MAAM,CAAC;AAAA,UACpB;AAEA,iBAAO,YAAY,QAAQ,QAAQ;AAAA,QACrC;AAEA,cAAM,UAAU,SAAS,MAAM,MAAM;AAEnC,iBAAO,QAAQ;AAEf,cAAI,UAAU;AAEd,kBAAO,MAAM;AAAA,YACb,KAAK;AACH,wBAAU,SAAS,IAAI;AACvB;AAAA,YACF,KAAK;AACH,wBAAU,WAAW,IAAI;AACzB;AAAA,YACF,KAAK;AACH,wBAAU,WAAW,IAAI;AACzB;AAAA,YACF,KAAK;AACH,wBAAU,QAAQ,IAAI;AACtB;AAAA,YACF;AACE,oBAAM,UAAU;AAAA,UAClB;AAEA,oBAAU,KAAK,OAAO;AACtB,uBAAa;AAAA,QACf;AAEA,cAAM,SAAS,SAAS,KAAK,KAAK;AAChC,cAAI,MAAM,KAAK,gBAAgB,OAAO,MAAM,KAAK,gBAAgB,KAAK;AACpE,kBAAM,MAAM,MAAM;AAAA,UACpB;AACA,iBAAO,SAAS,GAAG,EAAE,GAAG;AAAA,QAC1B;AAEA,cAAM,iBAAiB,WAAW;AAChC,iBAAO;AAAA,QACT;AAEA,cAAM,OAAO,WAAW;AACtB,cAAI,cAAc,GAAG;AACnB,gBAAIA,cAAa;AAEjB,mBAAOA,cAAa,IAAIA,eAAc;AACpC,kBAAI,WAAW,UAAU,YAAYA,aAAY,qBAAqB;AACtE,kBAAI,SAAS,YAAY;AAEzB,uBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,oBAAI,OAAO,UAAU,CAAC;AACtB,uBAAO,IAAI,KAAK,QAAQ,GAAG,CAAC;AAC5B,uBAAO,IAAI,KAAK,UAAU,GAAG,OAAO,gBAAgB,KAAK,QAAQ,GAAGA,WAAU,CAAE;AAChF,qBAAK,MAAM,MAAM;AAAA,cACnB;AAEA,kBAAI,iBAAiB;AACrB,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kCAAkB,SAAS,CAAC,EAAE;AAAA,cAChC;AAEA,kBAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AAClD;AAAA,cACF;AAAA,YACF;AAEA,0BAAcA;AAAA,UAChB;AAEA,mBAAS,OAAO,mBAAmB,CAAE;AAAA,QACvC;AAEA,cAAM,iBAAiB,SAAS,UAAU,QAAQ;AAEhD,qBAAW,YAAY;AACvB,mBAAU,OAAO,UAAU,cAAc,WAAW,IAAI;AAExD,cAAI,SAAS;AAEb,oBAAU;AACV,oBAAU;AACV,oBAAU;AACV,oBAAU,4BAA4B,SAAS;AAC/C,oBAAU;AACV,oBAAU;AAEV,mBAAS,IAAI,GAAG,IAAI,MAAM,eAAe,GAAG,KAAK,GAAG;AAElD,sBAAU;AAEV,qBAAS,IAAI,GAAG,IAAI,MAAM,eAAe,GAAG,KAAK,GAAG;AAClD,wBAAU;AACV,wBAAU;AACV,wBAAU;AACV,wBAAU;AACV,wBAAU,aAAa,WAAW;AAClC,wBAAU,cAAc,WAAW;AACnC,wBAAU;AACV,wBAAU,MAAM,OAAO,GAAG,CAAC,IAAG,YAAY;AAC1C,wBAAU;AACV,wBAAU;AAAA,YACZ;AAEA,sBAAU;AAAA,UACZ;AAEA,oBAAU;AACV,oBAAU;AAEV,iBAAO;AAAA,QACT;AAEA,cAAM,eAAe,SAAS,UAAU,QAAQ,KAAK,OAAO;AAE1D,cAAI,OAAO,CAAC;AACZ,cAAI,OAAO,UAAU,CAAC,KAAK,UAAU;AAEnC,mBAAO,UAAU,CAAC;AAElB,uBAAW,KAAK;AAChB,qBAAS,KAAK;AACd,kBAAM,KAAK;AACX,oBAAQ,KAAK;AAAA,UACf;AAEA,qBAAW,YAAY;AACvB,mBAAU,OAAO,UAAU,cAAc,WAAW,IAAI;AAGxD,gBAAO,OAAO,QAAQ,WAAY,EAAC,MAAM,IAAG,IAAI,OAAO,CAAC;AACxD,cAAI,OAAO,IAAI,QAAQ;AACvB,cAAI,KAAM,IAAI,OAAQ,IAAI,MAAM,uBAAuB;AAGvD,kBAAS,OAAO,UAAU,WAAY,EAAC,MAAM,MAAK,IAAI,SAAS,CAAC;AAChE,gBAAM,OAAO,MAAM,QAAQ;AAC3B,gBAAM,KAAM,MAAM,OAAQ,MAAM,MAAM,iBAAiB;AAEvD,cAAI,OAAO,MAAM,eAAe,IAAI,WAAW,SAAS;AACxD,cAAI,GAAG,IAAI,GAAG,IAAI,QAAM,IAAI;AAE5B,iBAAO,MAAM,WAAW,UAAU,WAChC,OAAO,WAAW,WAAW,WAAW;AAE1C,mBAAS;AACT,mBAAS,CAAC,KAAK,WAAW,aAAa,OAAO,iBAAiB,OAAO,QAAQ;AAC9E,mBAAS,mBAAmB,OAAO,MAAM,OAAO;AAChD,mBAAS;AACT,mBAAU,MAAM,QAAQ,IAAI,OAAQ,kCAChC,UAAU,CAAC,MAAM,IAAI,IAAI,EAAE,EAAE,KAAK,GAAG,EAAE,KAAK,CAAE,IAAI,MAAM;AAC5D,mBAAS;AACT,mBAAU,MAAM,OAAQ,gBAAgB,UAAU,MAAM,EAAE,IAAI,OAC1D,UAAU,MAAM,IAAI,IAAI,aAAa;AACzC,mBAAU,IAAI,OAAQ,sBAAsB,UAAU,IAAI,EAAE,IAAI,OAC5D,UAAU,IAAI,IAAI,IAAI,mBAAmB;AAC7C,mBAAS;AACT,mBAAS;AAET,eAAK,IAAI,GAAG,IAAI,MAAM,eAAe,GAAG,KAAK,GAAG;AAC9C,iBAAK,IAAI,WAAW;AACpB,iBAAK,IAAI,GAAG,IAAI,MAAM,eAAe,GAAG,KAAK,GAAG;AAC9C,kBAAI,MAAM,OAAO,GAAG,CAAC,GAAI;AACvB,qBAAK,IAAE,WAAS;AAChB,yBAAS,MAAM,KAAK,MAAM,KAAK;AAAA,cACjC;AAAA,YACF;AAAA,UACF;AAEA,mBAAS;AACT,mBAAS;AAET,iBAAO;AAAA,QACT;AAEA,cAAM,gBAAgB,SAAS,UAAU,QAAQ;AAE/C,qBAAW,YAAY;AACvB,mBAAU,OAAO,UAAU,cAAc,WAAW,IAAI;AAExD,cAAI,OAAO,MAAM,eAAe,IAAI,WAAW,SAAS;AACxD,cAAI,MAAM;AACV,cAAI,MAAM,OAAO;AAEjB,iBAAO,cAAc,MAAM,MAAM,SAAS,GAAG,GAAG;AAC9C,gBAAI,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,IAAI,KAAK;AAC9C,kBAAI,IAAI,KAAK,OAAQ,IAAI,OAAO,QAAQ;AACxC,kBAAI,IAAI,KAAK,OAAQ,IAAI,OAAO,QAAQ;AACxC,qBAAO,MAAM,OAAO,GAAG,CAAC,IAAG,IAAI;AAAA,YACjC,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF,CAAE;AAAA,QACJ;AAEA,cAAM,eAAe,SAAS,UAAU,QAAQ,KAAK;AAEnD,qBAAW,YAAY;AACvB,mBAAU,OAAO,UAAU,cAAc,WAAW,IAAI;AAExD,cAAI,OAAO,MAAM,eAAe,IAAI,WAAW,SAAS;AAExD,cAAI,MAAM;AACV,iBAAO;AACP,iBAAO;AACP,iBAAO,MAAM,cAAc,UAAU,MAAM;AAC3C,iBAAO;AACP,iBAAO;AACP,iBAAO;AACP,iBAAO;AACP,iBAAO;AACP,iBAAO;AACP,iBAAO;AACP,cAAI,KAAK;AACP,mBAAO;AACP,mBAAO,UAAU,GAAG;AACpB,mBAAO;AAAA,UACT;AACA,iBAAO;AAEP,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY,SAAS,GAAG;AAC1B,cAAI,UAAU;AACd,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,gBAAI,IAAI,EAAE,OAAO,CAAC;AAClB,oBAAO,GAAG;AAAA,cACV,KAAK;AAAK,2BAAW;AAAQ;AAAA,cAC7B,KAAK;AAAK,2BAAW;AAAQ;AAAA,cAC7B,KAAK;AAAK,2BAAW;AAAS;AAAA,cAC9B,KAAK;AAAK,2BAAW;AAAU;AAAA,cAC/B;AAAU,2BAAW;AAAG;AAAA,YACxB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,SAAS,QAAQ;AACtC,cAAI,WAAW;AACf,mBAAU,OAAO,UAAU,cAAc,WAAW,IAAI;AAExD,cAAI,OAAO,MAAM,eAAe,IAAI,WAAW,SAAS;AACxD,cAAI,MAAM;AACV,cAAI,MAAM,OAAO;AAEjB,cAAI,GAAG,GAAG,IAAI,IAAI;AAElB,cAAI,SAAS;AAAA,YACX,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACR;AAEA,cAAI,yBAAyB;AAAA,YAC3B,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACR;AAEA,cAAI,QAAQ;AACZ,eAAK,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC5B,iBAAK,KAAK,OAAO,IAAI,OAAO,QAAQ;AACpC,iBAAK,KAAK,OAAO,IAAI,IAAI,OAAO,QAAQ;AACxC,iBAAK,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC5B,kBAAI;AAEJ,kBAAI,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,IAAI,OAAO,MAAM,OAAO,IAAI,KAAK,OAAO,IAAI,OAAO,QAAQ,CAAC,GAAG;AACpG,oBAAI;AAAA,cACN;AAEA,kBAAI,OAAO,KAAK,IAAI,OAAO,OAAO,IAAE,KAAK,IAAE,IAAI,OAAO,MAAM,OAAO,IAAI,KAAK,OAAO,IAAI,OAAO,QAAQ,CAAC,GAAG;AACxG,qBAAK;AAAA,cACP,OACK;AACH,qBAAK;AAAA,cACP;AAGA,uBAAU,SAAS,KAAK,IAAE,KAAK,MAAO,uBAAuB,CAAC,IAAI,OAAO,CAAC;AAAA,YAC5E;AAEA,qBAAS;AAAA,UACX;AAEA,cAAI,OAAO,KAAK,SAAS,GAAG;AAC1B,mBAAO,MAAM,UAAU,GAAG,MAAM,SAAS,OAAO,CAAC,IAAI,MAAM,OAAK,CAAC,EAAE,KAAK,GAAG;AAAA,UAC7E;AAEA,iBAAO,MAAM,UAAU,GAAG,MAAM,SAAO,CAAC;AAAA,QAC1C;AAEA,cAAM,cAAc,SAAS,UAAU,QAAQ;AAC7C,qBAAW,YAAY;AAEvB,cAAI,WAAW,GAAG;AAChB,mBAAO,iBAAiB,MAAM;AAAA,UAChC;AAEA,sBAAY;AACZ,mBAAU,OAAO,UAAU,cAAc,WAAW,IAAI;AAExD,cAAI,OAAO,MAAM,eAAe,IAAI,WAAW,SAAS;AACxD,cAAI,MAAM;AACV,cAAI,MAAM,OAAO;AAEjB,cAAI,GAAG,GAAG,GAAG;AAEb,cAAI,QAAQ,MAAM,WAAS,CAAC,EAAE,KAAK,IAAI;AACvC,cAAI,QAAQ,MAAM,WAAS,CAAC,EAAE,KAAK,IAAI;AAEvC,cAAI,QAAQ;AACZ,cAAI,OAAO;AACX,eAAK,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC5B,gBAAI,KAAK,OAAQ,IAAI,OAAO,QAAQ;AACpC,mBAAO;AACP,iBAAK,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC5B,kBAAI;AAEJ,kBAAI,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,IAAI,OAAO,MAAM,OAAO,GAAG,KAAK,OAAO,IAAI,OAAO,QAAQ,CAAC,GAAG;AACnG,oBAAI;AAAA,cACN;AAGA,sBAAQ,IAAI,QAAQ;AAAA,YACtB;AAEA,iBAAK,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAChC,uBAAS,OAAO;AAAA,YAClB;AAAA,UACF;AAEA,iBAAO,MAAM,UAAU,GAAG,MAAM,SAAO,CAAC;AAAA,QAC1C;AAEA,cAAM,oBAAoB,SAAS,SAAS,UAAU;AACpD,qBAAW,YAAY;AACvB,cAAI,SAAS,MAAM,eAAe;AAClC,mBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,qBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,sBAAQ,YAAY,MAAM,OAAO,KAAK,GAAG,IAAI,UAAU;AACvD,sBAAQ,SAAS,MAAM,UAAU,MAAM,UAAU,UAAU,QAAQ;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAMA,MAAAD,QAAO,qBAAqB;AAAA,QAC1B,WAAY,SAAS,GAAG;AACtB,cAAI,QAAQ,CAAC;AACb,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,gBAAI,IAAI,EAAE,WAAW,CAAC;AACtB,kBAAM,KAAK,IAAI,GAAI;AAAA,UACrB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,MAAAA,QAAO,gBAAgBA,QAAO,mBAAmB,SAAS;AAW1D,MAAAA,QAAO,sBAAsB,SAAS,aAAa,UAAU;AAI3D,YAAI,aAAa,WAAW;AAE1B,cAAI,MAAM,wBAAwB,WAAW;AAC7C,cAAI,OAAO,WAAW;AACpB,gBAAI,IAAI,IAAI,KAAK;AACjB,gBAAI,KAAK,GAAI,OAAM;AACnB,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ;AACZ,cAAIG,cAAa,CAAC;AAClB,iBAAO,MAAM;AACX,gBAAI,KAAK,IAAI,KAAK;AAClB,gBAAI,MAAM,GAAI;AACd,gBAAI,KAAK,KAAK;AACd,gBAAI,KAAK,KAAK;AACd,gBAAI,KAAK,KAAK;AACd,gBAAI,IAAI,OAAO,aAAe,MAAM,IAAK,EAAE;AAC3C,gBAAI,IAAK,MAAM,IAAK;AACpB,YAAAA,YAAW,CAAC,IAAI;AAChB,qBAAS;AAAA,UACX;AACA,cAAI,SAAS,UAAU;AACrB,kBAAM,QAAQ,SAAS;AAAA,UACzB;AAEA,iBAAOA;AAAA,QACT,EAAE;AAEF,YAAI,cAAc,IAAI,WAAW,CAAC;AAElC,eAAO,SAAS,GAAG;AACjB,cAAI,QAAQ,CAAC;AACb,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,gBAAI,IAAI,EAAE,WAAW,CAAC;AACtB,gBAAI,IAAI,KAAK;AACX,oBAAM,KAAK,CAAC;AAAA,YACd,OAAO;AACL,kBAAI,IAAI,WAAW,EAAE,OAAO,CAAC,CAAC;AAC9B,kBAAI,OAAO,KAAK,UAAU;AACxB,qBAAM,IAAI,QAAS,GAAG;AAEpB,wBAAM,KAAK,CAAC;AAAA,gBACd,OAAO;AAEL,wBAAM,KAAK,MAAM,CAAC;AAClB,wBAAM,KAAK,IAAI,GAAI;AAAA,gBACrB;AAAA,cACF,OAAO;AACL,sBAAM,KAAK,WAAW;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAMA,UAAI,SAAS;AAAA,QACX,aAAiB,KAAK;AAAA,QACtB,gBAAiB,KAAK;AAAA,QACtB,gBAAiB,KAAK;AAAA,QACtB,YAAiB,KAAK;AAAA,MACxB;AAMA,UAAI,yBAAyB;AAAA,QAC3B,GAAI;AAAA,QACJ,GAAI;AAAA,QACJ,GAAI;AAAA,QACJ,GAAI;AAAA,MACN;AAMA,UAAI,gBAAgB;AAAA,QAClB,YAAa;AAAA,QACb,YAAa;AAAA,QACb,YAAa;AAAA,QACb,YAAa;AAAA,QACb,YAAa;AAAA,QACb,YAAa;AAAA,QACb,YAAa;AAAA,QACb,YAAa;AAAA,MACf;AAMA,UAAI,SAAS,WAAW;AAEtB,YAAI,yBAAyB;AAAA,UAC3B,CAAC;AAAA,UACD,CAAC,GAAG,EAAE;AAAA,UACN,CAAC,GAAG,EAAE;AAAA,UACN,CAAC,GAAG,EAAE;AAAA,UACN,CAAC,GAAG,EAAE;AAAA,UACN,CAAC,GAAG,EAAE;AAAA,UACN,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,UACd,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,UAClB,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,UAClB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,UACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,UACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,UACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,UACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,UACnB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,UACvB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,UACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,UACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,UACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,UACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,UACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,UACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,UAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,UAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,UAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,UAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,UAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC/B;AACA,YAAI,MAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AACnF,YAAI,MAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAChG,YAAI,WAAY,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AAEpE,YAAI,QAAQ,CAAC;AAEb,YAAI,cAAc,SAAS,MAAM;AAC/B,cAAI,QAAQ;AACZ,iBAAO,QAAQ,GAAG;AAChB,qBAAS;AACT,sBAAU;AAAA,UACZ;AACA,iBAAO;AAAA,QACT;AAEA,cAAM,iBAAiB,SAAS,MAAM;AACpC,cAAI,IAAI,QAAQ;AAChB,iBAAO,YAAY,CAAC,IAAI,YAAY,GAAG,KAAK,GAAG;AAC7C,iBAAM,OAAQ,YAAY,CAAC,IAAI,YAAY,GAAG;AAAA,UAChD;AACA,kBAAU,QAAQ,KAAM,KAAK;AAAA,QAC/B;AAEA,cAAM,mBAAmB,SAAS,MAAM;AACtC,cAAI,IAAI,QAAQ;AAChB,iBAAO,YAAY,CAAC,IAAI,YAAY,GAAG,KAAK,GAAG;AAC7C,iBAAM,OAAQ,YAAY,CAAC,IAAI,YAAY,GAAG;AAAA,UAChD;AACA,iBAAQ,QAAQ,KAAM;AAAA,QACxB;AAEA,cAAM,qBAAqB,SAAS,YAAY;AAC9C,iBAAO,uBAAuB,aAAa,CAAC;AAAA,QAC9C;AAEA,cAAM,kBAAkB,SAAS,aAAa;AAE5C,kBAAQ,aAAa;AAAA,YAErB,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,wBAAQ,IAAI,KAAK,KAAK;AAAA,cAAG;AAAA,YACnD,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,uBAAO,IAAI,KAAK;AAAA,cAAG;AAAA,YAC7C,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,uBAAO,IAAI,KAAK;AAAA,cAAG;AAAA,YAC7C,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,wBAAQ,IAAI,KAAK,KAAK;AAAA,cAAG;AAAA,YACnD,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,wBAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAM,KAAK;AAAA,cAAG;AAAA,YACpF,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,uBAAQ,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK;AAAA,cAAG;AAAA,YACjE,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,wBAAU,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK,KAAK;AAAA,cAAG;AAAA,YACxE,KAAK,cAAc;AACjB,qBAAO,SAAS,GAAG,GAAG;AAAE,wBAAU,IAAI,IAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,cAAG;AAAA,YAExE;AACE,oBAAM,qBAAqB;AAAA,UAC7B;AAAA,QACF;AAEA,cAAM,4BAA4B,SAAS,oBAAoB;AAC7D,cAAI,IAAI,aAAa,CAAC,CAAC,GAAG,CAAC;AAC3B,mBAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK,GAAG;AAC9C,gBAAI,EAAE,SAAS,aAAa,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAE;AAAA,UACtD;AACA,iBAAO;AAAA,QACT;AAEA,cAAM,kBAAkB,SAAS,MAAM,MAAM;AAE3C,cAAI,KAAK,QAAQ,OAAO,IAAI;AAI1B,oBAAO,MAAM;AAAA,cACb,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC;AACE,sBAAM,UAAU;AAAA,YAClB;AAAA,UAEF,WAAW,OAAO,IAAI;AAIpB,oBAAO,MAAM;AAAA,cACb,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC;AACE,sBAAM,UAAU;AAAA,YAClB;AAAA,UAEF,WAAW,OAAO,IAAI;AAIpB,oBAAO,MAAM;AAAA,cACb,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC,KAAK,OAAO;AAAiB,uBAAO;AAAA,cACpC;AACE,sBAAM,UAAU;AAAA,YAClB;AAAA,UAEF,OAAO;AACL,kBAAM,UAAU;AAAA,UAClB;AAAA,QACF;AAEA,cAAM,eAAe,SAASH,SAAQ;AAEpC,cAAI,cAAcA,QAAO,eAAe;AAExC,cAAI,YAAY;AAIhB,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,qBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAE7C,kBAAI,YAAY;AAChB,kBAAI,OAAOA,QAAO,OAAO,KAAK,GAAG;AAEjC,uBAAS,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AAE/B,oBAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AACzC;AAAA,gBACF;AAEA,yBAAS,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AAE/B,sBAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AACzC;AAAA,kBACF;AAEA,sBAAI,KAAK,KAAK,KAAK,GAAG;AACpB;AAAA,kBACF;AAEA,sBAAI,QAAQA,QAAO,OAAO,MAAM,GAAG,MAAM,CAAC,GAAI;AAC5C,iCAAa;AAAA,kBACf;AAAA,gBACF;AAAA,cACF;AAEA,kBAAI,YAAY,GAAG;AACjB,6BAAc,IAAI,YAAY;AAAA,cAChC;AAAA,YACF;AAAA,UACF;AAAC;AAID,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO,GAAG;AACjD,qBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO,GAAG;AACjD,kBAAI,QAAQ;AACZ,kBAAIA,QAAO,OAAO,KAAK,GAAG,EAAI,UAAS;AACvC,kBAAIA,QAAO,OAAO,MAAM,GAAG,GAAG,EAAI,UAAS;AAC3C,kBAAIA,QAAO,OAAO,KAAK,MAAM,CAAC,EAAI,UAAS;AAC3C,kBAAIA,QAAO,OAAO,MAAM,GAAG,MAAM,CAAC,EAAI,UAAS;AAC/C,kBAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,6BAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAIA,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,qBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO,GAAG;AACjD,kBAAIA,QAAO,OAAO,KAAK,GAAG,KACnB,CAACA,QAAO,OAAO,KAAK,MAAM,CAAC,KAC1BA,QAAO,OAAO,KAAK,MAAM,CAAC,KAC1BA,QAAO,OAAO,KAAK,MAAM,CAAC,KAC1BA,QAAO,OAAO,KAAK,MAAM,CAAC,KAC3B,CAACA,QAAO,OAAO,KAAK,MAAM,CAAC,KAC1BA,QAAO,OAAO,KAAK,MAAM,CAAC,GAAI;AACpC,6BAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAEA,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,qBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO,GAAG;AACjD,kBAAIA,QAAO,OAAO,KAAK,GAAG,KACnB,CAACA,QAAO,OAAO,MAAM,GAAG,GAAG,KAC1BA,QAAO,OAAO,MAAM,GAAG,GAAG,KAC1BA,QAAO,OAAO,MAAM,GAAG,GAAG,KAC1BA,QAAO,OAAO,MAAM,GAAG,GAAG,KAC3B,CAACA,QAAO,OAAO,MAAM,GAAG,GAAG,KAC1BA,QAAO,OAAO,MAAM,GAAG,GAAG,GAAI;AACpC,6BAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAIA,cAAI,YAAY;AAEhB,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,qBAAS,MAAM,GAAG,MAAM,aAAa,OAAO,GAAG;AAC7C,kBAAIA,QAAO,OAAO,KAAK,GAAG,GAAI;AAC5B,6BAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAEA,cAAI,QAAQ,KAAK,IAAI,MAAM,YAAY,cAAc,cAAc,EAAE,IAAI;AACzE,uBAAa,QAAQ;AAErB,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,EAAE;AAMF,UAAI,SAAS,WAAW;AAEtB,YAAI,YAAY,IAAI,MAAM,GAAG;AAC7B,YAAI,YAAY,IAAI,MAAM,GAAG;AAG7B,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,oBAAU,CAAC,IAAI,KAAK;AAAA,QACtB;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,oBAAU,CAAC,IAAI,UAAU,IAAI,CAAC,IAC1B,UAAU,IAAI,CAAC,IACf,UAAU,IAAI,CAAC,IACf,UAAU,IAAI,CAAC;AAAA,QACrB;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,oBAAU,UAAU,CAAC,CAAE,IAAI;AAAA,QAC7B;AAEA,YAAI,QAAQ,CAAC;AAEb,cAAM,OAAO,SAAS,GAAG;AAEvB,cAAI,IAAI,GAAG;AACT,kBAAM,UAAU,IAAI;AAAA,UACtB;AAEA,iBAAO,UAAU,CAAC;AAAA,QACpB;AAEA,cAAM,OAAO,SAAS,GAAG;AAEvB,iBAAO,IAAI,GAAG;AACZ,iBAAK;AAAA,UACP;AAEA,iBAAO,KAAK,KAAK;AACf,iBAAK;AAAA,UACP;AAEA,iBAAO,UAAU,CAAC;AAAA,QACpB;AAEA,eAAO;AAAA,MACT,EAAE;AAMF,eAAS,aAAa,KAAK,OAAO;AAEhC,YAAI,OAAO,IAAI,UAAU,aAAa;AACpC,gBAAM,IAAI,SAAS,MAAM;AAAA,QAC3B;AAEA,YAAI,OAAO,WAAW;AACpB,cAAI,SAAS;AACb,iBAAO,SAAS,IAAI,UAAU,IAAI,MAAM,KAAK,GAAG;AAC9C,sBAAU;AAAA,UACZ;AACA,cAAII,QAAO,IAAI,MAAM,IAAI,SAAS,SAAS,KAAK;AAChD,mBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC/C,YAAAA,MAAK,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,UAC1B;AACA,iBAAOA;AAAA,QACT,EAAE;AAEF,YAAI,QAAQ,CAAC;AAEb,cAAM,QAAQ,SAAS,OAAO;AAC5B,iBAAO,KAAK,KAAK;AAAA,QACnB;AAEA,cAAM,YAAY,WAAW;AAC3B,iBAAO,KAAK;AAAA,QACd;AAEA,cAAM,WAAW,SAAS,GAAG;AAE3B,cAAIC,OAAM,IAAI,MAAM,MAAM,UAAU,IAAI,EAAE,UAAU,IAAI,CAAC;AAEzD,mBAAS,IAAI,GAAG,IAAI,MAAM,UAAU,GAAG,KAAK,GAAG;AAC7C,qBAAS,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,KAAK,GAAG;AACzC,cAAAA,KAAI,IAAI,CAAC,KAAK,OAAO,KAAK,OAAO,KAAK,MAAM,MAAM,CAAC,CAAE,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,CAAE,CAAE;AAAA,YACpF;AAAA,UACF;AAEA,iBAAO,aAAaA,MAAK,CAAC;AAAA,QAC5B;AAEA,cAAM,MAAM,SAAS,GAAG;AAEtB,cAAI,MAAM,UAAU,IAAI,EAAE,UAAU,IAAI,GAAG;AACzC,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,OAAO,KAAK,MAAM,MAAM,CAAC,CAAE,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,CAAE;AAElE,cAAIA,OAAM,IAAI,MAAM,MAAM,UAAU,CAAE;AACtC,mBAAS,IAAI,GAAG,IAAI,MAAM,UAAU,GAAG,KAAK,GAAG;AAC7C,YAAAA,KAAI,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,UACxB;AAEA,mBAAS,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,KAAK,GAAG;AACzC,YAAAA,KAAI,CAAC,KAAK,OAAO,KAAK,OAAO,KAAK,EAAE,MAAM,CAAC,CAAE,IAAI,KAAK;AAAA,UACxD;AAGA,iBAAO,aAAaA,MAAK,CAAC,EAAE,IAAI,CAAC;AAAA,QACnC;AAEA,eAAO;AAAA,MACT;AAAC;AAMD,UAAI,YAAY,WAAW;AAEzB,YAAI,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQnB,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,CAAC;AAAA;AAAA,UAGT,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,UAGV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,UAGV,CAAC,GAAG,KAAK,EAAE;AAAA,UACX,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,CAAC;AAAA;AAAA,UAGT,CAAC,GAAG,KAAK,GAAG;AAAA,UACZ,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,UAGV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,EAAE;AAAA,UACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,KAAK,EAAE;AAAA,UACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,KAAK,GAAG;AAAA,UACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,KAAK,EAAE;AAAA,UACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,UACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGrB,CAAC,GAAG,KAAK,GAAG;AAAA,UACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,UACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,UACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,IAAI,IAAI,EAAE;AAAA,UACX,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,IAAI,IAAI,EAAE;AAAA,UACX,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,EAAE;AAAA;AAAA,UAGX,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,UAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,GAAG;AAAA,UACb,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,UAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,UACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,UAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,UAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,UACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,QACzB;AAEA,YAAI,YAAY,SAAS,YAAY,WAAW;AAC9C,cAAIC,SAAQ,CAAC;AACb,UAAAA,OAAM,aAAa;AACnB,UAAAA,OAAM,YAAY;AAClB,iBAAOA;AAAA,QACT;AAEA,YAAI,QAAQ,CAAC;AAEb,YAAI,kBAAkB,SAAS,YAAY,sBAAsB;AAE/D,kBAAO,sBAAsB;AAAA,YAC7B,KAAK,uBAAuB;AAC1B,qBAAO,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,YAChD,KAAK,uBAAuB;AAC1B,qBAAO,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,YAChD,KAAK,uBAAuB;AAC1B,qBAAO,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,YAChD,KAAK,uBAAuB;AAC1B,qBAAO,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,YAChD;AACE,qBAAO;AAAA,UACT;AAAA,QACF;AAEA,cAAM,cAAc,SAAS,YAAY,sBAAsB;AAE7D,cAAI,UAAU,gBAAgB,YAAY,oBAAoB;AAE9D,cAAI,OAAO,WAAW,aAAa;AACjC,kBAAM,+BAA+B,aACjC,2BAA2B;AAAA,UACjC;AAEA,cAAI,SAAS,QAAQ,SAAS;AAE9B,cAAI,OAAO,CAAC;AAEZ,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAElC,gBAAI,QAAQ,QAAQ,IAAI,IAAI,CAAC;AAC7B,gBAAI,aAAa,QAAQ,IAAI,IAAI,CAAC;AAClC,gBAAI,YAAY,QAAQ,IAAI,IAAI,CAAC;AAEjC,qBAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,mBAAK,KAAK,UAAU,YAAY,SAAS,CAAE;AAAA,YAC7C;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,EAAE;AAMF,UAAI,cAAc,WAAW;AAE3B,YAAI,UAAU,CAAC;AACf,YAAI,UAAU;AAEd,YAAI,QAAQ,CAAC;AAEb,cAAM,YAAY,WAAW;AAC3B,iBAAO;AAAA,QACT;AAEA,cAAM,QAAQ,SAAS,OAAO;AAC5B,cAAI,WAAW,KAAK,MAAM,QAAQ,CAAC;AACnC,kBAAU,QAAQ,QAAQ,MAAO,IAAI,QAAQ,IAAO,MAAM;AAAA,QAC5D;AAEA,cAAM,MAAM,SAAS,KAAK,QAAQ;AAChC,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,kBAAM,QAAW,QAAS,SAAS,IAAI,IAAO,MAAM,CAAC;AAAA,UACvD;AAAA,QACF;AAEA,cAAM,kBAAkB,WAAW;AACjC,iBAAO;AAAA,QACT;AAEA,cAAM,SAAS,SAAS,KAAK;AAE3B,cAAI,WAAW,KAAK,MAAM,UAAU,CAAC;AACrC,cAAI,QAAQ,UAAU,UAAU;AAC9B,oBAAQ,KAAK,CAAC;AAAA,UAChB;AAEA,cAAI,KAAK;AACP,oBAAQ,QAAQ,KAAM,QAAU,UAAU;AAAA,UAC5C;AAEA,qBAAW;AAAA,QACb;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,WAAW,SAAS,MAAM;AAE5B,YAAI,QAAQ,OAAO;AACnB,YAAI,QAAQ;AAEZ,YAAI,QAAQ,CAAC;AAEb,cAAM,UAAU,WAAW;AACzB,iBAAO;AAAA,QACT;AAEA,cAAM,YAAY,SAAS,QAAQ;AACjC,iBAAO,MAAM;AAAA,QACf;AAEA,cAAM,QAAQ,SAAS,QAAQ;AAE7B,cAAIC,QAAO;AAEX,cAAI,IAAI;AAER,iBAAO,IAAI,IAAIA,MAAK,QAAQ;AAC1B,mBAAO,IAAI,SAASA,MAAK,UAAU,GAAG,IAAI,CAAC,CAAE,GAAG,EAAE;AAClD,iBAAK;AAAA,UACP;AAEA,cAAI,IAAIA,MAAK,QAAQ;AACnB,gBAAIA,MAAK,SAAS,KAAK,GAAG;AACxB,qBAAO,IAAI,SAASA,MAAK,UAAU,GAAG,IAAI,CAAC,CAAE,GAAG,CAAC;AAAA,YACnD,WAAWA,MAAK,SAAS,KAAK,GAAG;AAC/B,qBAAO,IAAI,SAASA,MAAK,UAAU,GAAG,IAAI,CAAC,CAAE,GAAG,CAAC;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW,SAAS,GAAG;AACzB,cAAI,MAAM;AACV,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,kBAAM,MAAM,KAAK,UAAU,EAAE,OAAO,CAAC,CAAE;AAAA,UACzC;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY,SAAS,GAAG;AAC1B,cAAI,OAAO,KAAK,KAAK,KAAK;AACxB,mBAAO,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,UAC3C;AACA,gBAAM,mBAAmB;AAAA,QAC3B;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,aAAa,SAAS,MAAM;AAE9B,YAAI,QAAQ,OAAO;AACnB,YAAI,QAAQ;AAEZ,YAAI,QAAQ,CAAC;AAEb,cAAM,UAAU,WAAW;AACzB,iBAAO;AAAA,QACT;AAEA,cAAM,YAAY,SAAS,QAAQ;AACjC,iBAAO,MAAM;AAAA,QACf;AAEA,cAAM,QAAQ,SAAS,QAAQ;AAE7B,cAAI,IAAI;AAER,cAAI,IAAI;AAER,iBAAO,IAAI,IAAI,EAAE,QAAQ;AACvB,mBAAO;AAAA,cACL,QAAQ,EAAE,OAAO,CAAC,CAAE,IAAI,KACxB,QAAQ,EAAE,OAAO,IAAI,CAAC,CAAE;AAAA,cAAG;AAAA,YAAE;AAC/B,iBAAK;AAAA,UACP;AAEA,cAAI,IAAI,EAAE,QAAQ;AAChB,mBAAO,IAAI,QAAQ,EAAE,OAAO,CAAC,CAAE,GAAG,CAAC;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,UAAU,SAAS,GAAG;AAExB,cAAI,OAAO,KAAK,KAAK,KAAK;AACxB,mBAAO,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,UAC3C,WAAW,OAAO,KAAK,KAAK,KAAK;AAC/B,mBAAO,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI;AAAA,UAC/C,OAAO;AACL,oBAAQ,GAAG;AAAA,cACX,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB,KAAK;AAAM,uBAAO;AAAA,cAClB;AACE,sBAAM,mBAAmB;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,aAAa,SAAS,MAAM;AAE9B,YAAI,QAAQ,OAAO;AACnB,YAAI,QAAQ;AACZ,YAAI,SAASP,QAAO,cAAc,IAAI;AAEtC,YAAI,QAAQ,CAAC;AAEb,cAAM,UAAU,WAAW;AACzB,iBAAO;AAAA,QACT;AAEA,cAAM,YAAY,SAAS,QAAQ;AACjC,iBAAO,OAAO;AAAA,QAChB;AAEA,cAAM,QAAQ,SAAS,QAAQ;AAC7B,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,mBAAO,IAAI,OAAO,CAAC,GAAG,CAAC;AAAA,UACzB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,UAAU,SAAS,MAAM;AAE3B,YAAI,QAAQ,OAAO;AACnB,YAAI,QAAQ;AAEZ,YAAI,gBAAgBA,QAAO,mBAAmB,MAAM;AACpD,YAAI,CAAC,eAAe;AAClB,gBAAM;AAAA,QACR;AACA,SAAC,SAAS,GAAG,MAAM;AAEjB,cAAI,OAAO,cAAc,CAAC;AAC1B,cAAI,KAAK,UAAU,MAAQ,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC,MAAM,MAAM;AAC3D,kBAAM;AAAA,UACR;AAAA,QACF,EAAE,KAAU,KAAM;AAElB,YAAI,SAAS,cAAc,IAAI;AAE/B,YAAI,QAAQ,CAAC;AAEb,cAAM,UAAU,WAAW;AACzB,iBAAO;AAAA,QACT;AAEA,cAAM,YAAY,SAAS,QAAQ;AACjC,iBAAO,CAAC,EAAE,OAAO,SAAS;AAAA,QAC5B;AAEA,cAAM,QAAQ,SAAS,QAAQ;AAE7B,cAAIO,QAAO;AAEX,cAAI,IAAI;AAER,iBAAO,IAAI,IAAIA,MAAK,QAAQ;AAE1B,gBAAI,KAAO,MAAOA,MAAK,CAAC,MAAM,IAAM,MAAOA,MAAK,IAAI,CAAC;AAErD,gBAAI,SAAU,KAAK,KAAK,OAAQ;AAC9B,mBAAK;AAAA,YACP,WAAW,SAAU,KAAK,KAAK,OAAQ;AACrC,mBAAK;AAAA,YACP,OAAO;AACL,oBAAM,sBAAsB,IAAI,KAAK,MAAM;AAAA,YAC7C;AAEA,iBAAO,MAAM,IAAK,OAAQ,OAAQ,IAAI;AAEtC,mBAAO,IAAI,GAAG,EAAE;AAEhB,iBAAK;AAAA,UACP;AAEA,cAAI,IAAIA,MAAK,QAAQ;AACnB,kBAAM,sBAAsB,IAAI;AAAA,UAClC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAUA,UAAI,wBAAwB,WAAW;AAErC,YAAI,SAAS,CAAC;AAEd,YAAI,QAAQ,CAAC;AAEb,cAAM,YAAY,SAAS,GAAG;AAC5B,iBAAO,KAAK,IAAI,GAAI;AAAA,QACtB;AAEA,cAAM,aAAa,SAAS,GAAG;AAC7B,gBAAM,UAAU,CAAC;AACjB,gBAAM,UAAU,MAAM,CAAC;AAAA,QACzB;AAEA,cAAM,aAAa,SAAS,GAAG,KAAK,KAAK;AACvC,gBAAM,OAAO;AACb,gBAAM,OAAO,EAAE;AACf,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,kBAAM,UAAU,EAAE,IAAI,GAAG,CAAC;AAAA,UAC5B;AAAA,QACF;AAEA,cAAM,cAAc,SAAS,GAAG;AAC9B,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,kBAAM,UAAU,EAAE,WAAW,CAAC,CAAE;AAAA,UAClC;AAAA,QACF;AAEA,cAAM,cAAc,WAAW;AAC7B,iBAAO;AAAA,QACT;AAEA,cAAM,WAAW,WAAW;AAC1B,cAAI,IAAI;AACR,eAAK;AACL,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,gBAAI,IAAI,GAAG;AACT,mBAAK;AAAA,YACP;AACA,iBAAK,OAAO,CAAC;AAAA,UACf;AACA,eAAK;AACL,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,2BAA2B,WAAW;AAExC,YAAI,UAAU;AACd,YAAI,UAAU;AACd,YAAI,UAAU;AACd,YAAI,UAAU;AAEd,YAAI,QAAQ,CAAC;AAEb,YAAI,eAAe,SAAS,GAAG;AAC7B,qBAAW,OAAO,aAAa,OAAO,IAAI,EAAI,CAAE;AAAA,QAClD;AAEA,YAAI,SAAS,SAAS,GAAG;AACvB,cAAI,IAAI,GAAG;AAAA,UAEX,WAAW,IAAI,IAAI;AACjB,mBAAO,KAAO;AAAA,UAChB,WAAW,IAAI,IAAI;AACjB,mBAAO,MAAQ,IAAI;AAAA,UACrB,WAAW,IAAI,IAAI;AACjB,mBAAO,MAAQ,IAAI;AAAA,UACrB,WAAW,KAAK,IAAI;AAClB,mBAAO;AAAA,UACT,WAAW,KAAK,IAAI;AAClB,mBAAO;AAAA,UACT;AACA,gBAAM,OAAO;AAAA,QACf;AAEA,cAAM,YAAY,SAAS,GAAG;AAE5B,oBAAW,WAAW,IAAM,IAAI;AAChC,qBAAW;AACX,qBAAW;AAEX,iBAAO,WAAW,GAAG;AACnB,yBAAa,YAAa,UAAU,CAAG;AACvC,uBAAW;AAAA,UACb;AAAA,QACF;AAEA,cAAM,QAAQ,WAAW;AAEvB,cAAI,UAAU,GAAG;AACf,yBAAa,WAAY,IAAI,OAAS;AACtC,sBAAU;AACV,sBAAU;AAAA,UACZ;AAEA,cAAI,UAAU,KAAK,GAAG;AAEpB,gBAAI,SAAS,IAAI,UAAU;AAC3B,qBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,yBAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAEA,cAAM,WAAW,WAAW;AAC1B,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,0BAA0B,SAAS,KAAK;AAE1C,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,UAAU;AACd,YAAI,UAAU;AAEd,YAAI,QAAQ,CAAC;AAEb,cAAM,OAAO,WAAW;AAEtB,iBAAO,UAAU,GAAG;AAElB,gBAAI,QAAQ,KAAK,QAAQ;AACvB,kBAAI,WAAW,GAAG;AAChB,uBAAO;AAAA,cACT;AACA,oBAAM,6BAA6B;AAAA,YACrC;AAEA,gBAAI,IAAI,KAAK,OAAO,IAAI;AACxB,oBAAQ;AAER,gBAAI,KAAK,KAAK;AACZ,wBAAU;AACV,qBAAO;AAAA,YACT,WAAW,EAAE,MAAM,MAAM,GAAI;AAE3B;AAAA,YACF;AAEA,sBAAW,WAAW,IAAK,OAAO,EAAE,WAAW,CAAC,CAAE;AAClD,uBAAW;AAAA,UACb;AAEA,cAAI,IAAK,YAAa,UAAU,IAAO;AACvC,qBAAW;AACX,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,SAAS,GAAG;AACvB,cAAI,MAAQ,KAAK,KAAK,IAAM;AAC1B,mBAAO,IAAI;AAAA,UACb,WAAW,MAAQ,KAAK,KAAK,KAAM;AACjC,mBAAO,IAAI,KAAO;AAAA,UACpB,WAAW,MAAQ,KAAK,KAAK,IAAM;AACjC,mBAAO,IAAI,KAAO;AAAA,UACpB,WAAW,KAAK,IAAM;AACpB,mBAAO;AAAA,UACT,WAAW,KAAK,IAAM;AACpB,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,OAAO;AAAA,UACf;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAMA,UAAI,WAAW,SAAS,OAAO,QAAQ;AAErC,YAAI,SAAS;AACb,YAAI,UAAU;AACd,YAAI,QAAQ,IAAI,MAAM,QAAQ,MAAM;AAEpC,YAAI,QAAQ,CAAC;AAEb,cAAM,WAAW,SAAS,GAAG,GAAG,OAAO;AACrC,gBAAM,IAAI,SAAS,CAAC,IAAI;AAAA,QAC1B;AAEA,cAAM,QAAQ,SAAS,KAAK;AAK1B,cAAI,YAAY,QAAQ;AAKxB,cAAI,WAAW,MAAM;AACrB,cAAI,WAAW,OAAO;AAEtB,cAAI,UAAU,GAAI;AAClB,cAAI,UAAU,CAAC;AACf,cAAI,UAAU,CAAC;AAMf,cAAI,UAAU,CAAI;AAClB,cAAI,UAAU,CAAI;AAClB,cAAI,UAAU,CAAI;AAGlB,cAAI,UAAU,GAAI;AAClB,cAAI,UAAU,GAAI;AAClB,cAAI,UAAU,GAAI;AAKlB,cAAI,YAAY,GAAG;AACnB,cAAI,WAAW,CAAC;AAChB,cAAI,WAAW,CAAC;AAChB,cAAI,WAAW,MAAM;AACrB,cAAI,WAAW,OAAO;AACtB,cAAI,UAAU,CAAC;AAQf,cAAI,iBAAiB;AACrB,cAAI,SAAS,aAAa,cAAc;AAExC,cAAI,UAAU,cAAc;AAE5B,cAAI,SAAS;AAEb,iBAAO,OAAO,SAAS,SAAS,KAAK;AACnC,gBAAI,UAAU,GAAG;AACjB,gBAAI,WAAW,QAAQ,QAAQ,GAAG;AAClC,sBAAU;AAAA,UACZ;AAEA,cAAI,UAAU,OAAO,SAAS,MAAM;AACpC,cAAI,WAAW,QAAQ,QAAQ,OAAO,SAAS,MAAM;AACrD,cAAI,UAAU,CAAI;AAIlB,cAAI,YAAY,GAAG;AAAA,QACrB;AAEA,YAAI,kBAAkB,SAAS,KAAK;AAElC,cAAI,OAAO;AACX,cAAI,aAAa;AACjB,cAAI,aAAa;AAEjB,cAAID,SAAQ,CAAC;AAEb,UAAAA,OAAM,QAAQ,SAAS,MAAM,QAAQ;AAEnC,gBAAM,SAAS,UAAW,GAAG;AAC3B,oBAAM;AAAA,YACR;AAEA,mBAAO,aAAa,UAAU,GAAG;AAC/B,mBAAK,UAAU,OAAU,QAAQ,aAAc,WAAY;AAC3D,wBAAW,IAAI;AACf,wBAAW,IAAI;AACf,2BAAa;AACb,2BAAa;AAAA,YACf;AAEA,yBAAc,QAAQ,aAAc;AACpC,yBAAa,aAAa;AAAA,UAC5B;AAEA,UAAAA,OAAM,QAAQ,WAAW;AACvB,gBAAI,aAAa,GAAG;AAClB,mBAAK,UAAU,UAAU;AAAA,YAC3B;AAAA,UACF;AAEA,iBAAOA;AAAA,QACT;AAEA,YAAI,eAAe,SAAS,gBAAgB;AAE1C,cAAI,YAAY,KAAK;AACrB,cAAI,WAAW,KAAK,kBAAkB;AACtC,cAAI,YAAY,iBAAiB;AAGjC,cAAI,QAAQ,SAAS;AAErB,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK,GAAG;AACrC,kBAAM,IAAI,OAAO,aAAa,CAAC,CAAE;AAAA,UACnC;AACA,gBAAM,IAAI,OAAO,aAAa,SAAS,CAAE;AACzC,gBAAM,IAAI,OAAO,aAAa,OAAO,CAAE;AAEvC,cAAI,UAAU,sBAAsB;AACpC,cAAI,SAAS,gBAAgB,OAAO;AAGpC,iBAAO,MAAM,WAAW,SAAS;AAEjC,cAAI,YAAY;AAEhB,cAAI,IAAI,OAAO,aAAa,MAAM,SAAS,CAAC;AAC5C,uBAAa;AAEb,iBAAO,YAAY,MAAM,QAAQ;AAE/B,gBAAI,IAAI,OAAO,aAAa,MAAM,SAAS,CAAC;AAC5C,yBAAa;AAEb,gBAAI,MAAM,SAAS,IAAI,CAAC,GAAI;AAE1B,kBAAI,IAAI;AAAA,YAEV,OAAO;AAEL,qBAAO,MAAM,MAAM,QAAQ,CAAC,GAAG,SAAS;AAExC,kBAAI,MAAM,KAAK,IAAI,MAAO;AAExB,oBAAI,MAAM,KAAK,KAAM,KAAK,WAAa;AACrC,+BAAa;AAAA,gBACf;AAEA,sBAAM,IAAI,IAAI,CAAC;AAAA,cACjB;AAEA,kBAAI;AAAA,YACN;AAAA,UACF;AAEA,iBAAO,MAAM,MAAM,QAAQ,CAAC,GAAG,SAAS;AAGxC,iBAAO,MAAM,SAAS,SAAS;AAE/B,iBAAO,MAAM;AAEb,iBAAO,QAAQ,YAAY;AAAA,QAC7B;AAEA,YAAI,WAAW,WAAW;AAExB,cAAI,OAAO,CAAC;AACZ,cAAI,QAAQ;AAEZ,cAAIA,SAAQ,CAAC;AAEb,UAAAA,OAAM,MAAM,SAAS,KAAK;AACxB,gBAAIA,OAAM,SAAS,GAAG,GAAI;AACxB,oBAAM,aAAa;AAAA,YACrB;AACA,iBAAK,GAAG,IAAI;AACZ,qBAAS;AAAA,UACX;AAEA,UAAAA,OAAM,OAAO,WAAW;AACtB,mBAAO;AAAA,UACT;AAEA,UAAAA,OAAM,UAAU,SAAS,KAAK;AAC5B,mBAAO,KAAK,GAAG;AAAA,UACjB;AAEA,UAAAA,OAAM,WAAW,SAAS,KAAK;AAC7B,mBAAO,OAAO,KAAK,GAAG,KAAK;AAAA,UAC7B;AAEA,iBAAOA;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,gBAAgB,SAAS,OAAO,QAAQ,UAAU;AACpD,YAAI,MAAM,SAAS,OAAO,MAAM;AAChC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,mBAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,gBAAI,SAAS,GAAG,GAAG,SAAS,GAAG,CAAC,CAAE;AAAA,UACpC;AAAA,QACF;AAEA,YAAI,IAAI,sBAAsB;AAC9B,YAAI,MAAM,CAAC;AAEX,YAAI,SAAS,yBAAyB;AACtC,YAAI,QAAQ,EAAE,YAAY;AAC1B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,iBAAO,UAAU,MAAM,CAAC,CAAC;AAAA,QAC3B;AACA,eAAO,MAAM;AAEb,eAAO,2BAA2B;AAAA,MACpC;AAKA,aAAON;AAAA,IACT,EAAE;AAGF,KAAC,WAAW;AAEV,MAAAA,QAAO,mBAAmB,OAAO,IAAI,SAAS,GAAG;AAE/C,iBAAS,YAAY,KAAK;AACxB,cAAI,OAAO,CAAC;AACZ,mBAAS,IAAE,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,gBAAI,WAAW,IAAI,WAAW,CAAC;AAC/B,gBAAI,WAAW,IAAM,MAAK,KAAK,QAAQ;AAAA,qBAC9B,WAAW,MAAO;AACzB,mBAAK;AAAA,gBAAK,MAAQ,YAAY;AAAA,gBAC1B,MAAQ,WAAW;AAAA,cAAK;AAAA,YAC9B,WACS,WAAW,SAAU,YAAY,OAAQ;AAChD,mBAAK;AAAA,gBAAK,MAAQ,YAAY;AAAA,gBAC1B,MAAS,YAAU,IAAK;AAAA,gBACxB,MAAQ,WAAW;AAAA,cAAK;AAAA,YAC9B,OAEK;AACH;AAIA,yBAAW,UAAa,WAAW,SAAQ,KACtC,IAAI,WAAW,CAAC,IAAI;AACzB,mBAAK;AAAA,gBAAK,MAAQ,YAAW;AAAA,gBACzB,MAAS,YAAU,KAAM;AAAA,gBACzB,MAAS,YAAU,IAAK;AAAA,gBACxB,MAAQ,WAAW;AAAA,cAAK;AAAA,YAC9B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,YAAY,CAAC;AAAA,MACtB;AAAA,IAEF,EAAE;AAEF,KAAC,SAAU,SAAS;AAClB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,eAAO,CAAC,GAAG,OAAO;AAAA,MACtB,WAAW,OAAO,YAAY,UAAU;AACpC,eAAO,UAAU,QAAQ;AAAA,MAC7B;AAAA,IACF,GAAE,WAAY;AACV,aAAOA;AAAA,IACX,CAAC;AAAA;AAAA;;;ACvvED;;;ACDA,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,QAAI,OAAO,EAAE,QAAQ,CAAC,EAAG;AACzB,MAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACZ;AACA,SAAO;AACT;;;ACRA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACLA,8BAAmB;AAEnB,IAAI,cAAc;AAClB,IAAI,cAAc;AAElB,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,aAAa,OAAO,QAAQ;AACnC,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,SAAO;AACT;AAEA,SAAS,SAAS,IAAI;AACpB,SAAO,cAAc;AACvB;AAEA,SAAS,WAAW,IAAI;AACtB,SAAO,SAAS,EAAE,KAAK,cAAc;AACvC;AASA,SAAS,UAAU,OAAO,QAAQ;AAChC,SAAO,QAAQ,aAAa,OAAO,UAAU,OAAO,QAAQ,MAAM;AACpE;AAEA,SAAS,aAAa,QAAQ,OAAO,QAAQ;AAC3C,MAAI,QAAQ;AACV,WAAO,QAAQ;AACf,WAAO,SAAS,UAAU,OAAO,QAAQ;AACzC,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,OAAO,MAAM;AAChC;AAUA,SAAS,WAAW,QAAQ,MAAM,SAAS;AACzC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,WAAW,SAAS,UACpB,UAAU,SAAS,SACnB,iBAAiB,SAAS,OAC1B,QAAQ,mBAAmB,SAAS,OAAO;AAC/C,MAAI,QAAQ,OAAO,OACf,SAAS,OAAO;AACpB,MAAI,QAAQ,CAAC,IAAI;AACjB,MAAI,MAAM,WAAW,OAAO,WAAW,IAAI;AAC3C,MAAI,MAAO,KAAI,UAAU,GAAG,GAAG,OAAO,MAAM;AAC5C,MAAI,2BAA2B;AAE/B,SAAO,MAAM,QAAQ;AACnB,QAAI,OAAO,MAAM,MAAM;AAEvB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,cAAQ,KAAK,OAAO,KAAK;AAAA,IAC3B,WAAW,MAAM;AACf,UAAI,MAAM;AAEV,UAAI,QAAQ,WAAW,IAAI,GAAG;AAC5B,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,WAAW,OAAO,SAAS,UAAU;AACnC,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,cAAM;AAAA,MACR;AAEA,UAAI,KAAK,IAAI,OAAO,OAAO,IAAI,IAAI,IAAI,MAAM,aAAa;AAC1D,UAAI,KAAK,IAAI,OAAO,OAAO,IAAI,IAAI,IAAI,MAAM,aAAa;AAC1D,UAAI,IAAI,EAAG,MAAK;AAChB,UAAI,IAAI,EAAG,MAAK;AAChB,UAAI,KAAK,UAAU,MAAM,IAAI,OAAO,WAAW,IAAI,MAAM;AACzD,UAAI,KAAK,UAAU,MAAM,IAAI,OAAO,WAAW,IAAI,MAAM;AAEzD,UAAI,IAAI,OAAO;AACb,YAAI,UAAU,IAAI,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MACrC,OAAO;AACL,YAAI,YAAY,IAAI,SAAS;AAC7B,YAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,MAAM,SAAS;AAC/B,MAAI,OAAO,WAAW,CAAC,GACnB,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,KAAK,eAC3C,iBAAiB,KAAK,WACtB,YAAY,mBAAmB,SAAS,KAAK,gBAC7C,kBAAkB,KAAK,YACvB,aAAa,oBAAoB,SAAS,YAAY,iBACtD,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,OAAO,YACvC,WAAW,KAAK,KAChB,MAAM,aAAa,SAAS,IAAI,UAChC,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,cAAc,eACpD,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,IAAI;AAEtC,MAAI,SAAS,UAAU,CAAC;AACxB,MAAI,MAAM,OAAO,WAAW,IAAI;AAChC,MAAI;AAEJ,MAAI,UAAU;AACZ,QAAI,YAAY;AAChB,QAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AAEvB,QAAI,oBAAoB,IAAI,aAAa,GAAG,GAAG,GAAG,CAAC;AAEnD,kBAAc,kBAAkB;AAChC,QAAI,CAAC,YAAY,CAAC,EAAG,eAAc;AAAA,EACrC;AAEA,MAAI,SAAS,WAAW,IAAI;AAC5B,MAAI,OAAO,CAAC,WAAW,WAAW,MAAM,UAAU,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAE5E,MAAI,eAAe,SAASQ,gBAAe;AACzC,QAAI,YAAY;AAChB,QAAI,eAAe;AACnB,QAAI,OAAO;AAAA,EACb;AAEA,eAAa;AACb,MAAI,QAAQ,KAAK,KAAK,IAAI,YAAY,IAAI,EAAE,KAAK,IAAI,IAAI;AACzD,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,eAAa;AAEb,MAAI,WAAW,SAASC,YAAW;AACjC,QAAI,YAAY,SAAS;AACzB,QAAI,SAAS,MAAM,QAAQ,GAAG,SAAS,CAAC;AAAA,EAC1C;AAEA,MAAI,SAAS,GAAG;AACd,QAAI,YAAY;AAChB,QAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAChC,aAAS;AAAA,EACX,OAAO;AACL,aAAS;AAET,QAAI,aAAa;AACf,OAAC,WAAY;AACX,YAAI,YAAY,IAAI,aAAa,GAAG,GAAG,OAAO,MAAM;AACpD,YAAI,OAAO,UAAU;AACrB,YAAI,QAAQ,QAAQ;AACpB,YAAI,SAAS,CAAC;AACd,YAAI,SAAS;AAEb,YAAI,QAAQ,SAASC,OAAMC,OAAM;AAC/B,cAAI,UAAU,CAAC;AACf,cAAI,SAAS,CAAC;AACd,iBAAO,MAAM,IAAI;AACjB,mBAAS,IAAI;AACb,cAAI,OAAO,OAAO,MAAM;AAExB,cAAI,CAAC,MAAM;AACT,mBAAO,CAAC;AAER,qBAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,mBAAK,KAAK,CAAC;AAAA,YACb;AAAA,UACF;AAEA,eAAK,QAAQ,SAAUC,IAAG;AACxB,gBAAI,KAAK,IAAIA,KAAI,CAAC,GAAG;AACnB,eAACA,KAAI,QAAQA,KAAI,IAAI,KAAKA,KAAI,KAAK,QAAQA,KAAI,IAAI,IAAIA,KAAI,OAAOA,KAAI,KAAK,EAAE,QAAQ,SAAU,GAAG;AAChG,oBAAI,IAAI,IAAI;AAEZ,oBAAI,KAAK,KAAK,KAAK,KAAK,UAAU,CAAC,OAAO,CAAC,GAAG;AAC5C,yBAAO,CAAC,IAAI;AACZ,0BAAQ,KAAK,CAAC;AAAA,gBAChB;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AACD,kBAAQ,QAAQ,SAAUA,IAAG;AAC3B,gBAAI,IAAI,IAAIA;AAEZ,gBAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AAChB,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,qBAAK,IAAI,CAAC,IAAI,YAAY,CAAC;AAAA,cAC7B;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAEA,iBAAS,OAAO,GAAG,OAAO,KAAK,QAAQ,GAAG;AACxC,gBAAM;AAAA,QACR;AAEA,YAAI,aAAa,WAAW,GAAG,CAAC;AAAA,MAClC,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,UAAU;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,SAAS,KAAK,QACd,QAAQ,KAAK;AACjB,MAAIC,aAAY,QAAQ,WACpBC,cAAa,QAAQ;AACzB,MAAI,QAAQ,WAAW;AACvB,MAAI,aAAaD,WAAU,KAAK;AAChC,MAAI,UAAU,WAAW,WAAW,IAAI;AACxC,UAAQ,YAAY;AACpB,YAAU;AAAA,IACR;AAAA,IACA;AAAA,EACF,GAAG,SAAU,OAAO;AAClB,QAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM;AAEd,QAAI,OAAO,GAAG,CAAC,GAAG;AAChB,cAAQ,SAAS,GAAG,GAAG,UAAU,QAAQ;AAAA,IAC3C;AAAA,EACF,CAAC;AACD,MAAI,WAAWA,WAAU,KAAK;AAC9B,EAAAC,YAAW,UAAU,YAAY;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,MAAI,MAAM,SAAS,WAAW,IAAI;AAClC,MAAI,2BAA2B;AAC/B,MAAI,UAAU,YAAY,GAAG,CAAC;AAC9B,SAAO;AACT;AAEA,SAAS,YAAY,OAAO,aAAa;AACvC,MAAI,aAAa,MAAM,YACnB,WAAW,MAAM,UACjB,SAAS,MAAM,QACf,QAAQ,MAAM;AAClB,MAAID,aAAY,QAAQ,WACpBC,cAAa,QAAQ;AACzB,MAAI,QAAQ,WAAW;AACvB,MAAI,aAAaD,WAAU,KAAK;AAChC,MAAI,qBAAqB,YAAY,OACjC,QAAQ,uBAAuB,SAAS,IAAI;AAChD,MAAI,SAAS,QAAQ,WAAW;AAChC,MAAI,UAAU,WAAW,WAAW,IAAI;AACxC,UAAQ,YAAY;AACpB,YAAU;AAAA,IACR;AAAA,IACA;AAAA,EACF,GAAG,SAAU,OAAO;AAClB,QAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM;AAEd,QAAI,OAAO,GAAG,CAAC,GAAG;AAChB,cAAQ,UAAU;AAClB,cAAQ,OAAO,IAAI,MAAM,UAAU,CAAC;AACpC,iBAAW,SAAS,IAAI,UAAU,GAAG,IAAI,UAAU,IAAI,MAAM,UAAU,MAAM;AAC7E,iBAAW,SAAS,IAAI,UAAU,IAAI,UAAU,IAAI,MAAM,UAAU,IAAI,UAAU,MAAM;AACxF,iBAAW,SAAS,GAAG,IAAI,UAAU,GAAG,IAAI,MAAM,UAAU,MAAM;AAClE,iBAAW,SAAS,GAAG,GAAG,IAAI,MAAM,UAAU,GAAG,MAAM;AAEvD,cAAQ,KAAK;AAAA,IACf;AAAA,EACF,CAAC;AACD,MAAI,WAAWA,WAAU,KAAK;AAC9B,EAAAC,YAAW,UAAU,YAAY;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,MAAI,MAAM,SAAS,WAAW,IAAI;AAClC,MAAI,2BAA2B;AAC/B,MAAI,UAAU,YAAY,GAAG,CAAC;AAC9B,SAAO;AACT;AAEA,SAAS,aAAa,OAAO,aAAa;AACxC,MAAI,aAAa,MAAM,YACnB,WAAW,MAAM,UACjB,SAAS,MAAM,QACf,QAAQ,MAAM;AAClB,MAAID,aAAY,QAAQ,WACpBC,cAAa,QAAQ;AACzB,MAAI,QAAQ,WAAW;AACvB,MAAI,aAAaD,WAAU,KAAK;AAChC,MAAI,UAAU,WAAW,WAAW,IAAI;AACxC,UAAQ,YAAY;AACpB,MAAI,sBAAsB,YAAY,OAClC,QAAQ,wBAAwB,SAAS,IAAI;AACjD,MAAI,SAAS,QAAQ,WAAW;AAChC,YAAU;AAAA,IACR;AAAA,IACA;AAAA,EACF,GAAG,SAAU,OAAO;AAClB,QAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM;AACd,QAAI,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;AAEzB,QAAI,OAAO,IAAI,GAAG,CAAC,GAAG;AACpB,cAAQ,CAAC,KAAK;AACd,cAAQ,CAAC,KAAK;AAAA,IAChB;AAEA,QAAI,OAAO,IAAI,GAAG,CAAC,GAAG;AACpB,cAAQ,CAAC,KAAK;AACd,cAAQ,CAAC,KAAK;AAAA,IAChB;AAEA,QAAI,OAAO,GAAG,IAAI,CAAC,GAAG;AACpB,cAAQ,CAAC,KAAK;AACd,cAAQ,CAAC,KAAK;AAAA,IAChB;AAEA,QAAI,OAAO,GAAG,IAAI,CAAC,GAAG;AACpB,cAAQ,CAAC,KAAK;AACd,cAAQ,CAAC,KAAK;AAAA,IAChB;AAEA,QAAI,OAAO,GAAG,CAAC,GAAG;AAChB,UAAI,OAAO,IAAI,GAAG,IAAI,CAAC,EAAG,SAAQ,CAAC,KAAK;AACxC,UAAI,OAAO,IAAI,GAAG,IAAI,CAAC,EAAG,SAAQ,CAAC,KAAK;AACxC,UAAI,OAAO,IAAI,GAAG,IAAI,CAAC,EAAG,SAAQ,CAAC,KAAK;AACxC,UAAI,OAAO,IAAI,GAAG,IAAI,CAAC,EAAG,SAAQ,CAAC,KAAK;AACxC,cAAQ,UAAU;AAClB,cAAQ,OAAO,IAAI,MAAM,UAAU,CAAC;AACpC,iBAAW,SAAS,IAAI,UAAU,GAAG,IAAI,UAAU,IAAI,MAAM,UAAU,QAAQ,CAAC,IAAI,IAAI,MAAM;AAC9F,iBAAW,SAAS,IAAI,UAAU,IAAI,UAAU,IAAI,MAAM,UAAU,IAAI,UAAU,QAAQ,CAAC,IAAI,IAAI,MAAM;AACzG,iBAAW,SAAS,GAAG,IAAI,UAAU,GAAG,IAAI,MAAM,UAAU,QAAQ,CAAC,IAAI,IAAI,MAAM;AACnF,iBAAW,SAAS,GAAG,GAAG,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,IAAI,MAAM;AAExE,cAAQ,KAAK;AAAA,IACf,OAAO;AACL,UAAI,QAAQ,CAAC,MAAM,GAAG;AACpB,mBAAW,SAAS,GAAG,IAAI,MAAM,UAAU,GAAG,GAAG,IAAI,MAAM,UAAU,GAAG,MAAM;AAAA,MAChF;AAEA,UAAI,QAAQ,CAAC,MAAM,GAAG;AACpB,mBAAW,SAAS,IAAI,MAAM,UAAU,GAAG,IAAI,UAAU,GAAG,IAAI,UAAU,IAAI,MAAM,UAAU,MAAM;AAAA,MACtG;AAEA,UAAI,QAAQ,CAAC,MAAM,GAAG;AACpB,mBAAW,SAAS,IAAI,UAAU,IAAI,MAAM,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,MAAM,UAAU,IAAI,UAAU,MAAM;AAAA,MAC5H;AAEA,UAAI,QAAQ,CAAC,MAAM,GAAG;AACpB,mBAAW,SAAS,IAAI,MAAM,UAAU,IAAI,UAAU,GAAG,IAAI,UAAU,GAAG,IAAI,MAAM,UAAU,MAAM;AAAA,MACtG;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,WAAWA,WAAU,KAAK;AAC9B,EAAAC,YAAW,UAAU,YAAY;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,MAAI,MAAM,SAAS,WAAW,IAAI;AAClC,MAAI,2BAA2B;AAC/B,MAAI,UAAU,YAAY,GAAG,CAAC;AAC9B,SAAO;AACT;AAEA,SAAS,WAAW,OAAO,aAAa;AACtC,MAAI,aAAa,MAAM,YACnB,WAAW,MAAM,UACjB,SAAS,MAAM,QACf,QAAQ,MAAM;AAClB,MAAID,aAAY,QAAQ,WACpBC,cAAa,QAAQ;AACzB,MAAI,QAAQ,WAAW;AACvB,MAAI,aAAaD,WAAU,KAAK;AAChC,MAAI,QAAQ,YAAY,OACpB,wBAAwB,YAAY,iBACpC,kBAAkB,0BAA0B,SAAS,cAAc;AACvE,MAAI,UAAU,WAAW,WAAW,IAAI;AACxC,MAAI,cAAcA,WAAU,KAAK;AACjC,MAAI,WAAWA,WAAU,KAAK;AAC9B,MAAI,WAAW,YAAY,WAAW,IAAI;AAC1C,GAAC;AAAA,IACC,MAAM;AAAA,IACN;AAAA,EACF,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY;AAAA,EACd,CAAC,EAAE,QAAQ,SAAU,MAAM;AACzB,YAAQ,YAAY;AACpB,YAAQ,UAAU,GAAG,GAAG,OAAO,KAAK;AACpC,cAAU;AAAA,MACR;AAAA,MACA;AAAA,IACF,GAAG,SAAU,OAAO;AAClB,UAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,MAAM;AAEd,UAAI,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM;AAC9B,YAAI;AAEJ,YAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,MAAM,KAAK,MAAM,GAAG;AAClL,qBAAW,IAAI,MAAM;AAAA,QACvB,OAAO;AACL,qBAAW;AAAA,QACb;AAEA,YAAI,UAAU,IAAI,YAAY;AAC9B,gBAAQ,SAAS,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,WAAW,UAAU,WAAW,QAAQ;AAAA,MACzG;AAAA,IACF,CAAC;AACD,IAAAC,YAAW,aAAa,KAAK,YAAY;AAAA,MACvC;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AACD,aAAS,2BAA2B;AACpC,aAAS,UAAU,YAAY,GAAG,CAAC;AACnC,IAAAA,YAAW,UAAU,aAAa;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAEA,SAAS,UAAU,OAAO,UAAU;AAClC,MAAI,WAAW,MAAM,UACjB,QAAQ,MAAM;AAElB,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AACjC,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,WAAW,KAAK,SAAS,SAAS,GAAG,GAAG,GAAG;AAClD,MAAI,GAAG;AACL,QAAI,MAAM,SAAS,SAAS,GAAG,GAAG,CAAC;AAAA,EACrC,OAAO;AACL,QAAI,OAAO,SAAS,OAAO;AAC3B,QAAI,OAAO,GAAG,CAAC;AAAA,EACjB;AACF;AAEA,SAAS,WAAW,SAAS,QAAQ,QAAQ,SAAS,SAAS,OAAO,OAAO,QAAQ;AACnF,UAAQ,UAAU;AAClB,UAAQ,OAAO,QAAQ,MAAM;AAC7B,aAAW,SAAS,SAAS,SAAS,OAAO,OAAO,MAAM;AAC1D,UAAQ,OAAO,SAAS,OAAO;AAC/B,UAAQ,OAAO,QAAQ,MAAM;AAE7B,UAAQ,KAAK;AACf;AAGA,wBAAAC,QAAO,gBAAgB,wBAAAA,QAAO,mBAAmB,OAAO;AACxD,IAAI,WAAW;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AACV;AAEA,IAAI,mBAAgC,WAAY;AAC9C,WAASC,kBAAiB,SAAS;AACjC,QAAI,QAAQ;AAEZ,SAAK,UAAU,SAAS,CAAC,GAAG,QAAQ;AACpC,SAAK,QAAQ,CAAC;AAEd,SAAK,SAAS,SAAU,GAAG,GAAG;AAC5B,UAAI,cAAc,MAAM,OACpB,KAAK,YAAY,IACjB,QAAQ,YAAY;AACxB,UAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,MAAO,QAAO;AACvD,aAAO,GAAG,OAAO,GAAG,CAAC;AAAA,IACvB;AAEA,SAAK,WAAW,OAAO;AAAA,EACzB;AAEA,MAAI,SAASA,kBAAiB;AAE9B,SAAO,SAAS,SAASC,QAAO,QAAQ,QAAQ;AAC9C,QAAI,WAAW,QAAQ;AACrB,eAAS,CAAC;AAAA,IACZ;AAEA,QAAI,gBAAgB,KAAK,SACrB,aAAa,cAAc,YAC3B,aAAa,cAAc,YAC3B,UAAU,cAAc,SACxB,SAAS,cAAc,QACvB,OAAO,cAAc,MACrB,SAAS,cAAc;AAC3B,QAAI,WAAW,QAAQ,OAAO,IAAI,KAAK,QAAQ,SAAS;AACxD,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAIH,cAAa,QAAQ;AACzB,QAAI,UAAU,QACV,OAAO,QAAQ;AACnB,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ;AACE,UAAI,WAAW,QACX,WAAW,SAAS;AACxB,UAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAM,YAAW;AAC9C,UAAI,SAAU,QAAO,QAAQ,WAAW,UAAU;AAElD,UAAI,MAAM;AACR,oBAAY,UAAU,CAAC,SAAS,QAAQ,aAAa,QAAQ,IAAI,IAAI;AAAA,MACvE,OAAO;AACL,eAAO,OAAO;AACd,oBAAY;AAAA,MACd;AAAA,IACF;AACA,QAAI,cAAc,OAAO,UAAU;AAEnC;AACE,UAAI,YAAY,KAAK,KAAK,cAAc,KAAK;AAE7C,UAAI,aAAa,YAAY;AAC7B,iBAAW,QAAQ,UAAU,YAAY,KAAK;AAC9C,MAAAA,YAAW,UAAU,YAAY;AAAA,QAC/B,UAAU;AAAA,MACZ,CAAC;AACD,iBAAW,SAAS,SAAS;AAAA,QAC3B;AAAA,QACA,UAAU;AAAA,QACV,QAAQ,KAAK;AAAA,MACf,GAAG,KAAK,KAAK,GAAG,KAAK,QAAQ,MAAM;AAEnC,UAAI,MAAM;AACR,YAAI,YAAY,SAAS,CAAC,GAAG,IAAI;AAEjC,YAAI,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,QAAQ,CAAC,KAAK,MAAM;AAClD,cAAI,OAAO,KAAK,OACZ,QAAQ,KAAK,OACb,SAAS,KAAK;AAClB,cAAI,aAAa,QAAQ;AACzB,cAAI,WAAW,KAAK,KAAK,QAAQ,MAAM,OAAO,IAAI;AAClD,cAAI,IAAI,KAAK,IAAI,QAAQ,aAAa,UAAU,aAAa,WAAW,UAAU;AAClF,cAAI,IAAI,IAAI;AACZ,cAAI,KAAK,aAAa,KAAK;AAC3B,cAAI,KAAK,aAAa,KAAK;AAC3B,oBAAU,IAAI;AACd,oBAAU,IAAI;AACd,oBAAU,IAAI;AACd,oBAAU,IAAI;AAAA,QAChB;AAEA,QAAAA,YAAW,UAAU,WAAW;AAAA,UAC9B,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAEA,IAAAA,YAAW,WAAW,CAAC;AAAA,MACrB,OAAO;AAAA,IACT,GAAG;AAAA,MACD,OAAO;AAAA,MACP,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC,CAAC;AACF,WAAO;AAAA,EACT;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS;AAC/C,SAAK,UAAU,SAAS,CAAC,GAAG,KAAK,SAAS,OAAO;AACjD,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,QAAI,iBAAiB,KAAK,SACtB,aAAa,eAAe,YAC5B,OAAO,eAAe,MACtB,OAAO,eAAe;AAE1B,QAAI,eAAe,KAAK,QAAQ;AAChC,QAAI,QAAQ,CAAC,KAAK,GAAG,EAAE,QAAQ,YAAY,IAAI,EAAG,gBAAe;AACjE,QAAI,SAAK,wBAAAC,SAAO,YAAY,YAAY;AACxC,OAAG,QAAQ,QAAQ,EAAE;AACrB,OAAG,KAAK;AACR,QAAI,QAAQ,GAAG,eAAe;AAC9B,SAAK,QAAQ;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,kBAAkB,SAAS,kBAAkB;AAClD,QAAI,SAAS,KAAK,QAAQ;AAE1B,QAAI,OAAO,WAAW,UAAU;AAC9B,eAAS;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IACF;AAEA,SAAK,QAAQ,SAAS,UAAU,CAAC;AAAA,EACnC;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,QAAIG,cAAa,QAAQ,YACrBC,YAAW,QAAQ;AACvB,QAAI,OAAO,KAAK,QAAQ;AAExB,QAAI,MAAM;AACR,UAAID,YAAW,IAAI,GAAG;AACpB,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,MACF,WAAW,CAACA,YAAW,KAAK,KAAK,GAAG;AAClC,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAEA,YAAI,OAAO,KAAK,SAAS,UAAU;AACjC,iBAAO;AAAA,YACL,OAAOC,UAAS,KAAK,MAAM,KAAK,OAAO;AAAA,UACzC;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,SAAK,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAOH;AACT,EAAE;AAEF,SAAS,SAAS,SAAS;AACzB,MAAI,SAAS,QAAQ,QACjB,OAAO,QAAQ,MACf,WAAW,QAAQ,UACnB,OAAO,8BAA8B,SAAS,CAAC,UAAU,QAAQ,UAAU,CAAC;AAEhF,MAAI,WAAW,IAAI,iBAAiB,IAAI;AACxC,SAAO,SAAS,OAAO,QAAQ;AAAA,IAC7B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AHtrBA,SAASI,YAAW;AAClB,EAAAA,YAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACvC;AAEA,SAASC,+BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAI,YAAY,CAAC,SAAS;AAC1B,IAAI,WAAW,IAAI,OAAO;AAAA,EACxB,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,QAAQ,SAAS,OAAO,GAAG;AACzB,QAAI,eAAe,KAAK;AACpB,iBAAa;AACb,QAAI,OAAOA,+BAA8B,cAAc,SAAS;AAEpE,WAAO,EAAE,UAAU,IAAI;AAAA,EACzB;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,SAAS,OAAO,SAAS;AAE/B,UAAI,CAAC,KAAK,QAAS;AACnB,WAAK,MAAM,gBAAgB,KAAK,GAAG;AACnC,eAASD,UAAS,CAAC,GAAG,SAAS;AAAA,QAC7B,QAAQ,KAAK;AAAA,MACf,CAAC,CAAC;AACF,WAAK,MAAM,WAAW,KAAK,GAAG;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,SAAK,UAAU;AACf,SAAK,OAAO,KAAK,OAAO;AAAA,EAC1B;AACF,CAAC;", "names": ["qrcode", "typeNumber", "errorCorrectionLevel", "unicodeMap", "_num", "num", "_this", "data", "resetContext", "fillText", "_loop", "loop", "i", "get<PERSON>anvas", "draw<PERSON><PERSON>vas", "qrcode", "QRC<PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "isDrawable", "drawText", "_extends", "_objectWithoutPropertiesLoose"]}