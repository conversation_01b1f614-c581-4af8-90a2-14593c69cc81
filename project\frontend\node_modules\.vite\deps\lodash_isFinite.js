import {
  require_root
} from "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/isFinite.js
var require_isFinite = __commonJS({
  "node_modules/lodash/isFinite.js"(exports, module) {
    var root = require_root();
    var nativeIsFinite = root.isFinite;
    function isFinite(value) {
      return typeof value == "number" && nativeIsFinite(value);
    }
    module.exports = isFinite;
  }
});
export default require_isFinite();
//# sourceMappingURL=lodash_isFinite.js.map
