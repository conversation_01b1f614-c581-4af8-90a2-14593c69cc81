{"version": 3, "sources": ["../../lodash/_baseIntersection.js", "../../lodash/_castArrayLikeObject.js"], "sourcesContent": ["var SetCache = require('./_SetCache'),\n    arrayIncludes = require('./_arrayIncludes'),\n    arrayIncludesWith = require('./_arrayIncludesWith'),\n    arrayMap = require('./_arrayMap'),\n    baseUnary = require('./_baseUnary'),\n    cacheHas = require('./_cacheHas');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * The base implementation of methods like `_.intersection`, without support\n * for iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of shared values.\n */\nfunction baseIntersection(arrays, iteratee, comparator) {\n  var includes = comparator ? arrayIncludesWith : arrayIncludes,\n      length = arrays[0].length,\n      othLength = arrays.length,\n      othIndex = othLength,\n      caches = Array(othLength),\n      maxLength = Infinity,\n      result = [];\n\n  while (othIndex--) {\n    var array = arrays[othIndex];\n    if (othIndex && iteratee) {\n      array = arrayMap(array, baseUnary(iteratee));\n    }\n    maxLength = nativeMin(array.length, maxLength);\n    caches[othIndex] = !comparator && (iteratee || (length >= 120 && array.length >= 120))\n      ? new SetCache(othIndex && array)\n      : undefined;\n  }\n  array = arrays[0];\n\n  var index = -1,\n      seen = caches[0];\n\n  outer:\n  while (++index < length && result.length < maxLength) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (!(seen\n          ? cacheHas(seen, computed)\n          : includes(result, computed, comparator)\n        )) {\n      othIndex = othLength;\n      while (--othIndex) {\n        var cache = caches[othIndex];\n        if (!(cache\n              ? cacheHas(cache, computed)\n              : includes(arrays[othIndex], computed, comparator))\n            ) {\n          continue outer;\n        }\n      }\n      if (seen) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseIntersection;\n", "var isArrayLikeObject = require('./isArrayLikeObject');\n\n/**\n * Casts `value` to an empty array if it's not an array like object.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Array|Object} Returns the cast array-like object.\n */\nfunction castArrayLikeObject(value) {\n  return isArrayLikeObject(value) ? value : [];\n}\n\nmodule.exports = castArrayLikeObject;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,gBAAgB;AADpB,QAEI,oBAAoB;AAFxB,QAGI,WAAW;AAHf,QAII,YAAY;AAJhB,QAKI,WAAW;AAGf,QAAI,YAAY,KAAK;AAYrB,aAAS,iBAAiB,QAAQ,UAAU,YAAY;AACtD,UAAI,WAAW,aAAa,oBAAoB,eAC5C,SAAS,OAAO,CAAC,EAAE,QACnB,YAAY,OAAO,QACnB,WAAW,WACX,SAAS,MAAM,SAAS,GACxB,YAAY,UACZ,SAAS,CAAC;AAEd,aAAO,YAAY;AACjB,YAAI,QAAQ,OAAO,QAAQ;AAC3B,YAAI,YAAY,UAAU;AACxB,kBAAQ,SAAS,OAAO,UAAU,QAAQ,CAAC;AAAA,QAC7C;AACA,oBAAY,UAAU,MAAM,QAAQ,SAAS;AAC7C,eAAO,QAAQ,IAAI,CAAC,eAAe,YAAa,UAAU,OAAO,MAAM,UAAU,OAC7E,IAAI,SAAS,YAAY,KAAK,IAC9B;AAAA,MACN;AACA,cAAQ,OAAO,CAAC;AAEhB,UAAI,QAAQ,IACR,OAAO,OAAO,CAAC;AAEnB;AACA,eAAO,EAAE,QAAQ,UAAU,OAAO,SAAS,WAAW;AACpD,cAAI,QAAQ,MAAM,KAAK,GACnB,WAAW,WAAW,SAAS,KAAK,IAAI;AAE5C,kBAAS,cAAc,UAAU,IAAK,QAAQ;AAC9C,cAAI,EAAE,OACE,SAAS,MAAM,QAAQ,IACvB,SAAS,QAAQ,UAAU,UAAU,IACtC;AACL,uBAAW;AACX,mBAAO,EAAE,UAAU;AACjB,kBAAI,QAAQ,OAAO,QAAQ;AAC3B,kBAAI,EAAE,QACE,SAAS,OAAO,QAAQ,IACxB,SAAS,OAAO,QAAQ,GAAG,UAAU,UAAU,IACjD;AACJ,yBAAS;AAAA,cACX;AAAA,YACF;AACA,gBAAI,MAAM;AACR,mBAAK,KAAK,QAAQ;AAAA,YACpB;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzEjB;AAAA;AAAA,QAAI,oBAAoB;AASxB,aAAS,oBAAoB,OAAO;AAClC,aAAO,kBAAkB,KAAK,IAAI,QAAQ,CAAC;AAAA,IAC7C;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}