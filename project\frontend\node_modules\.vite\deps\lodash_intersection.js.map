{"version": 3, "sources": ["../../lodash/intersection.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap'),\n    baseIntersection = require('./_baseIntersection'),\n    baseRest = require('./_baseRest'),\n    castArrayLikeObject = require('./_castArrayLikeObject');\n\n/**\n * Creates an array of unique values that are included in all given arrays\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons. The order and references of result values are\n * determined by the first array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * _.intersection([2, 1], [2, 3]);\n * // => [2]\n */\nvar intersection = baseRest(function(arrays) {\n  var mapped = arrayMap(arrays, castArrayLikeObject);\n  return (mapped.length && mapped[0] === arrays[0])\n    ? baseIntersection(mapped)\n    : [];\n});\n\nmodule.exports = intersection;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,mBAAmB;AADvB,QAEI,WAAW;AAFf,QAGI,sBAAsB;AAmB1B,QAAI,eAAe,SAAS,SAAS,QAAQ;AAC3C,UAAI,SAAS,SAAS,QAAQ,mBAAmB;AACjD,aAAQ,OAAO,UAAU,OAAO,CAAC,MAAM,OAAO,CAAC,IAC3C,iBAAiB,MAAM,IACvB,CAAC;AAAA,IACP,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}