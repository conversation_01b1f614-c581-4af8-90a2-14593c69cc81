import {
  require_toInteger
} from "./chunk-ZXTARUDF.js";
import "./chunk-3R6DH2NC.js";
import "./chunk-YHXMOY7F.js";
import {
  require_baseToString,
  require_toString
} from "./chunk-VZITUV5G.js";
import "./chunk-CWSHORJK.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseClamp.js
var require_baseClamp = __commonJS({
  "node_modules/lodash/_baseClamp.js"(exports, module) {
    function baseClamp(number, lower, upper) {
      if (number === number) {
        if (upper !== void 0) {
          number = number <= upper ? number : upper;
        }
        if (lower !== void 0) {
          number = number >= lower ? number : lower;
        }
      }
      return number;
    }
    module.exports = baseClamp;
  }
});

// node_modules/lodash/startsWith.js
var require_startsWith = __commonJS({
  "node_modules/lodash/startsWith.js"(exports, module) {
    var baseClamp = require_baseClamp();
    var baseToString = require_baseToString();
    var toInteger = require_toInteger();
    var toString = require_toString();
    function startsWith(string, target, position) {
      string = toString(string);
      position = position == null ? 0 : baseClamp(toInteger(position), 0, string.length);
      target = baseToString(target);
      return string.slice(position, position + target.length) == target;
    }
    module.exports = startsWith;
  }
});
export default require_startsWith();
//# sourceMappingURL=lodash_startsWith.js.map
