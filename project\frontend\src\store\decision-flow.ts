import axios from 'axios';
import { defineStore } from 'pinia';

import { getEndpoint } from '@core/helpers/get-endpoint';

export const useDecisionFlowStore = defineStore('decisionFlow', () => {
  // Ref (states)
  const decisionFlows = ref<Types.PaginatedApiResult<Types.DecisionFlowItem>>(null);
  const decisionResult = ref<Types.PaginatedApiResult<Types.DecisionResult>>(null);
  const currentDecisionFlow = ref<Types.DecisionFlowItemDetail>();
  const modifiedDecisionFlowSchema = ref<Types.DecisionFlowItemDetail['schema']>([]);
  const currentEvent = ref<string>('');
  const showCreatePopup = ref<boolean>(false);
  const isCreatingDecisionFlow = ref<boolean>(false);
  const isDeletingDecisionFlow = ref<boolean>(false);
  const isLoadingDecisionFlow = ref<boolean>(false);
  const isUpdatingDecisionFlow = ref<boolean>(false);
  const isPublishingDecisionFlow = ref<boolean>(false);
  const isLoadingDecisionResult = ref<boolean>(false);
  const isRunningDecisionPipeline = ref<boolean>(false);
  const customStatusList = ref<Types.CustomStatus[]>([]);
  const events = ref<Types.DecisionFlowEvent[]>([]);
  const settingTreePosition = ref<boolean>(false);
  const datapoints = ref<Types.ConditionDataPointList>([]);

  // Function (actions)
  const createDecisionFlow = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW')}`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.post(url, params);
    return res.data;
  };

  const updateDecisionFlow = async ({
    workspaceSlug = '',
    formSlug = '',
    event = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW')}${event}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.put(url, params);
    return res.data;
  };

  const fetchDecisionFlow = async ({ workspaceSlug = '', formSlug = '', event = '' }) => {
    let url = `${getEndpoint('DECISION_FLOW')}${event}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url);
    return res.data;
  };

  const fetchDecisionFlowList = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW')}`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const deleteDecisionFlow = async ({ workspaceSlug = '', formSlug = '', event = '' }) => {
    let url = `${getEndpoint('DECISION_FLOW')}${event}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.delete(url);
    return res.data;
  };

  const publishDecisionFlow = async ({
    workspaceSlug = '',
    formSlug = '',
    event = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW')}${event}/publish/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.post(url, params);
    return res.data;
  };

  const fetchDecisionFlowResultList = async ({
    workspaceSlug = '',
    formSlug = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_PIPELINE')}`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const fetchDecisionFlowResult = async ({
    workspaceSlug = '',
    formSlug = '',
    id = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_PIPELINE')}${id}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const fetchMultipleDecisionFlowResult = async ({
    workspaceSlug = '',
    formSlug = '',
    ids = [],
    params = {},
  }) => {
    let baseUrl = getEndpoint('DECISION_FLOW_PIPELINE');
    baseUrl = baseUrl.replace(':workspaceSlug', workspaceSlug);
    baseUrl = baseUrl.replace(':formSlug', formSlug);
    const res = await axios.all(ids.map(id => axios.get(`${baseUrl}${id}/`, { params })));
    return res.map(r => r.data);
  };

  const reTriggerDecisionPipeline = async ({
    workspaceSlug = '',
    formSlug = '',
    id = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_PIPELINE')}${id}/re-trigger/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.post(url, params);
    return res.data;
  };

  const fetchDecisionFlowVersions = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW_OPTIONS')}decision-flows/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const getDecisionEvents = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW_OPTIONS')}events/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const getDataConnectionList = async ({
    workspaceSlug = '',
    formSlug = '',
    decisionFlowEvent = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_CONNECTION')}`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':decisionFlowEvent', decisionFlowEvent);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const getRequiredDataPoints = async ({
    workspaceSlug = '',
    formSlug = '',
    decisionFlowEvent = '',
    dataPointName = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_CONNECTION')}:dataPointName/required-data-point/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':decisionFlowEvent', decisionFlowEvent);
    url = url.replace(':dataPointName', dataPointName);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const connectToDataPoint = async ({
    workspaceSlug = '',
    formSlug = '',
    decisionFlowEvent = '',
    dataPointName = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_CONNECTION_MULTI_INPUT')}:dataPointName/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':decisionFlowEvent', decisionFlowEvent);
    url = url.replace(':dataPointName', dataPointName);
    const res = await axios.put(url, params);
    return res.data;
  };

  const disconnectDataPoint = async ({
    workspaceSlug = '',
    formSlug = '',
    decisionFlowEvent = '',
    dataPointName = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_CONNECTION_MULTI_INPUT')}:dataPointName/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':decisionFlowEvent', decisionFlowEvent);
    url = url.replace(':dataPointName', dataPointName);
    const res = await axios.delete(url, params);
    return res.data;
  };

  const getActionList = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW_OPTIONS')}actions/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  // Custom Status
  const fetchCustomStatusList = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = getEndpoint('DECISION_CUSTOM_STATUS_KEY');
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    customStatusList.value = res.data;
  };

  const createCustomStatusKey = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = getEndpoint('DECISION_CUSTOM_STATUS_KEY');
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.post(url, params);
    return res.data;
  };

  const batchUpdateCustomStatusKey = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = getEndpoint('DECISION_CUSTOM_STATUS_KEY');
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.patch(url, params);
    return res.data;
  };

  const updateCustomStatusKey = async ({
    workspaceSlug = '',
    formSlug = '',
    statusId = -1,
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_CUSTOM_STATUS_KEY')}${statusId}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.patch(url, params);
    return res.data;
  };

  const deleteCustomStatusKey = async ({ workspaceSlug = '', formSlug = '', statusId = -1 }) => {
    let url = `${getEndpoint('DECISION_CUSTOM_STATUS_KEY')}${statusId}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.delete(url);
    return res.data;
  };

  const createCustomStatusOption = async ({
    workspaceSlug = '',
    formSlug = '',
    statusId = -1,
    params = {},
  }) => {
    let url = getEndpoint('DECISION_CUSTOM_STATUS_OPTION');
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':statusId', statusId.toString());
    const res = await axios.post(url, params);
    return res.data;
  };

  const batchUpdateCustomStatusOption = async ({
    workspaceSlug = '',
    formSlug = '',
    statusId = -1,
    params = {},
  }) => {
    let url = getEndpoint('DECISION_CUSTOM_STATUS_OPTION');
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':statusId', statusId.toString());
    const res = await axios.patch(url, params);
    return res.data;
  };

  const updateCustomStatusOption = async ({
    workspaceSlug = '',
    formSlug = '',
    statusId = -1,
    optionId = -1,
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_CUSTOM_STATUS_OPTION')}${optionId}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':statusId', statusId.toString());
    const res = await axios.patch(url, params);
    return res.data;
  };

  const deleteCustomStatusOption = async ({
    workspaceSlug = '',
    formSlug = '',
    statusId = -1,
    optionId = -1,
  }) => {
    let url = `${getEndpoint('DECISION_CUSTOM_STATUS_OPTION')}${optionId}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    url = url.replace(':statusId', statusId.toString());
    const res = await axios.delete(url);
    return res.data;
  };

  const getDataPoints = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW_OPTIONS')}data-points/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const getDataPointConnectionList = async ({
    workspaceSlug = '',
    applicationSlug = '',
    params = {},
  }) => {
    let url = getEndpoint('DECISION_FLOW_RESULTS');
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':applicationSlug', applicationSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const getDataPointResult = async ({
    workspaceSlug = '',
    applicationSlug = '',
    datapointKey = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW_RESULTS')}${datapointKey}/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':applicationSlug', applicationSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  const updateRepeatRunConfig = async ({
    workspaceSlug = '',
    formSlug = '',
    event = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW')}${event}/repeat-run-config/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.post(url, params);
    return res.data;
  };

  const reRunDecisionFlow = async ({
    workspaceSlug = '',
    formSlug = '',
    event = '',
    params = {},
  }) => {
    let url = `${getEndpoint('DECISION_FLOW')}${event}/rerun/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.post(url, params);
    return res.data;
  };

  const getRepeatRunEvents = async ({ workspaceSlug = '', formSlug = '', params = {} }) => {
    let url = `${getEndpoint('DECISION_FLOW_OPTIONS')}repeat-run-events/`;
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url, { params });
    return res.data;
  };

  return {
    decisionFlows,
    decisionResult,
    currentDecisionFlow,
    modifiedDecisionFlowSchema,
    currentEvent,
    showCreatePopup,
    isCreatingDecisionFlow,
    isDeletingDecisionFlow,
    isLoadingDecisionFlow,
    isUpdatingDecisionFlow,
    isPublishingDecisionFlow,
    isLoadingDecisionResult,
    isRunningDecisionPipeline,
    events,
    customStatusList,
    settingTreePosition,
    datapoints,

    createDecisionFlow,
    updateDecisionFlow,
    fetchDecisionFlow,
    fetchDecisionFlowList,
    deleteDecisionFlow,
    publishDecisionFlow,
    fetchDecisionFlowResultList,
    fetchDecisionFlowResult,
    fetchMultipleDecisionFlowResult,
    getDecisionEvents,
    getActionList,

    fetchDecisionFlowVersions,
    reTriggerDecisionPipeline,

    getDataConnectionList,
    getRequiredDataPoints,
    connectToDataPoint,
    disconnectDataPoint,

    fetchCustomStatusList,
    createCustomStatusKey,
    batchUpdateCustomStatusKey,
    updateCustomStatusKey,
    deleteCustomStatusKey,

    createCustomStatusOption,
    batchUpdateCustomStatusOption,
    updateCustomStatusOption,
    deleteCustomStatusOption,

    getDataPoints,
    getDataPointConnectionList,
    getDataPointResult,

    updateRepeatRunConfig,
    reRunDecisionFlow,
    getRepeatRunEvents,
  };
});

export default { useDecisionFlowStore };
