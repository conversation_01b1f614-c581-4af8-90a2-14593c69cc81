{"version": 3, "sources": ["../../signature_pad/src/point.ts", "../../signature_pad/src/bezier.ts", "../../signature_pad/src/signature_event_target.ts", "../../signature_pad/src/throttle.ts", "../../signature_pad/src/signature_pad.ts"], "sourcesContent": ["// Interface for point data structure used e.g. in SignaturePad#fromData method\nexport interface BasicPoint {\n  x: number;\n  y: number;\n  pressure: number;\n  time: number;\n}\n\nexport class Point implements BasicPoint {\n  public x: number;\n  public y: number;\n  public pressure: number;\n  public time: number;\n\n  constructor(x: number, y: number, pressure?: number, time?: number) {\n    if (isNaN(x) || isNaN(y)) {\n      throw new Error(`Point is invalid: (${x}, ${y})`);\n    }\n    this.x = +x;\n    this.y = +y;\n    this.pressure = pressure || 0;\n    this.time = time || Date.now();\n  }\n\n  public distanceTo(start: BasicPoint): number {\n    return Math.sqrt(\n      Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2),\n    );\n  }\n\n  public equals(other: BasicPoint): boolean {\n    return (\n      this.x === other.x &&\n      this.y === other.y &&\n      this.pressure === other.pressure &&\n      this.time === other.time\n    );\n  }\n\n  public velocityFrom(start: BasicPoint): number {\n    return this.time !== start.time\n      ? this.distanceTo(start) / (this.time - start.time)\n      : 0;\n  }\n}\n", "import { BasicPoint, Point } from './point';\n\nexport class Bezier {\n  public static fromPoints(\n    points: Point[],\n    widths: { start: number; end: number },\n  ): Bezier {\n    const c2 = this.calculateControlPoints(points[0], points[1], points[2]).c2;\n    const c3 = this.calculateControlPoints(points[1], points[2], points[3]).c1;\n\n    return new Bezier(points[1], c2, c3, points[2], widths.start, widths.end);\n  }\n\n  private static calculateControlPoints(\n    s1: BasicPoint,\n    s2: BasicPoint,\n    s3: BasicPoint,\n  ): {\n    c1: BasicPoint;\n    c2: BasicPoint;\n  } {\n    const dx1 = s1.x - s2.x;\n    const dy1 = s1.y - s2.y;\n    const dx2 = s2.x - s3.x;\n    const dy2 = s2.y - s3.y;\n\n    const m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n    const m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n    const l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n    const l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n    const dxm = m1.x - m2.x;\n    const dym = m1.y - m2.y;\n\n    const k = l2 / (l1 + l2);\n    const cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n    const tx = s2.x - cm.x;\n    const ty = s2.y - cm.y;\n\n    return {\n      c1: new Point(m1.x + tx, m1.y + ty),\n      c2: new Point(m2.x + tx, m2.y + ty),\n    };\n  }\n\n  constructor(\n    public startPoint: Point,\n    public control2: BasicPoint,\n    public control1: BasicPoint,\n    public endPoint: Point,\n    public startWidth: number,\n    public endWidth: number,\n  ) {}\n\n  // Returns approximated length. Code taken from https://www.lemoda.net/maths/bezier-length/index.html.\n  public length(): number {\n    const steps = 10;\n    let length = 0;\n    let px;\n    let py;\n\n    for (let i = 0; i <= steps; i += 1) {\n      const t = i / steps;\n      const cx = this.point(\n        t,\n        this.startPoint.x,\n        this.control1.x,\n        this.control2.x,\n        this.endPoint.x,\n      );\n      const cy = this.point(\n        t,\n        this.startPoint.y,\n        this.control1.y,\n        this.control2.y,\n        this.endPoint.y,\n      );\n\n      if (i > 0) {\n        const xdiff = cx - (px as number);\n        const ydiff = cy - (py as number);\n\n        length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n      }\n\n      px = cx;\n      py = cy;\n    }\n\n    return length;\n  }\n\n  // Calculate parametric value of x or y given t and the four point coordinates of a cubic bezier curve.\n  private point(\n    t: number,\n    start: number,\n    c1: number,\n    c2: number,\n    end: number,\n  ): number {\n    // prettier-ignore\n    return (       start * (1.0 - t) * (1.0 - t)  * (1.0 - t))\n         + (3.0 *  c1    * (1.0 - t) * (1.0 - t)  * t)\n         + (3.0 *  c2    * (1.0 - t) * t          * t)\n         + (       end   * t         * t          * t);\n  }\n}\n", "export class SignatureEventTarget {\n  /* tslint:disable: variable-name */\n  private _et: EventTarget;\n  /* tslint:enable: variable-name */\n\n  constructor() {\n    try {\n      this._et = new EventTarget();\n    } catch (error) {\n      // Using document as EventTarget to support iOS 13 and older.\n      // Because EventTarget constructor just exists at iOS 14 and later.\n      this._et = document;\n    }\n  }\n\n  addEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject | null,\n    options?: boolean | AddEventListenerOptions,\n  ): void {\n    this._et.addEventListener(type, listener, options);\n  }\n\n  dispatchEvent(event: Event): boolean {\n    return this._et.dispatchEvent(event);\n  }\n\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject | null,\n    options?: boolean | EventListenerOptions,\n  ): void {\n    this._et.removeEventListener(type, callback, options);\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-this-alias */\n// Slightly simplified version of http://stackoverflow.com/a/27078401/815507\n\nexport function throttle(\n  fn: (...args: any[]) => any,\n  wait = 250,\n): (this: any, ...args: any[]) => any {\n  let previous = 0;\n  let timeout: number | null = null;\n  let result: any;\n  let storedContext: any;\n  let storedArgs: any[];\n\n  const later = (): void => {\n    previous = Date.now();\n    timeout = null;\n    result = fn.apply(storedContext, storedArgs);\n\n    if (!timeout) {\n      storedContext = null;\n      storedArgs = [];\n    }\n  };\n\n  return function wrapper(this: any, ...args: any[]): any {\n    const now = Date.now();\n    const remaining = wait - (now - previous);\n\n    storedContext = this;\n    storedArgs = args;\n\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n\n      previous = now;\n      result = fn.apply(storedContext, storedArgs);\n\n      if (!timeout) {\n        storedContext = null;\n        storedArgs = [];\n      }\n    } else if (!timeout) {\n      timeout = window.setTimeout(later, remaining);\n    }\n\n    return result;\n  };\n}\n", "/**\n * The main idea and some parts of the code (e.g. drawing variable width Bézier curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * https://web.archive.org/web/20160323213433/http://www.benknowscode.com/2012/09/path-interpolation-using-cubic-bezier_9742.html\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n */\n\nimport { Bezier } from './bezier';\nimport { BasicPoint, Point } from './point';\nimport { SignatureEventTarget } from './signature_event_target';\nimport { throttle } from './throttle';\n\ndeclare global {\n  interface CSSStyleDeclaration {\n    msTouchAction: string | null;\n  }\n}\n\nexport type SignatureEvent = MouseEvent | Touch | PointerEvent;\n\nexport interface FromDataOptions {\n  clear?: boolean;\n}\n\nexport interface ToSVGOptions {\n  includeBackgroundColor?: boolean;\n}\n\nexport interface PointGroupOptions {\n  dotSize: number;\n  minWidth: number;\n  maxWidth: number;\n  penColor: string;\n  velocityFilterWeight: number;\n  /**\n   * This is the globalCompositeOperation for the line.\n   * *default: 'source-over'*\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n   */\n  compositeOperation: GlobalCompositeOperation;\n}\n\nexport interface Options extends Partial<PointGroupOptions> {\n  minDistance?: number;\n  backgroundColor?: string;\n  throttle?: number;\n  canvasContextOptions?: CanvasRenderingContext2DSettings;\n}\n\nexport interface PointGroup extends PointGroupOptions {\n  points: BasicPoint[];\n}\n\nexport default class SignaturePad extends SignatureEventTarget {\n  // Public stuff\n  public dotSize: number;\n  public minWidth: number;\n  public maxWidth: number;\n  public penColor: string;\n  public minDistance: number;\n  public velocityFilterWeight: number;\n  public compositeOperation: GlobalCompositeOperation;\n  public backgroundColor: string;\n  public throttle: number;\n  public canvasContextOptions: CanvasRenderingContext2DSettings;\n\n  // Private stuff\n  /* tslint:disable: variable-name */\n  private _ctx: CanvasRenderingContext2D;\n  private _drawingStroke = false;\n  private _isEmpty = true;\n  private _lastPoints: Point[] = []; // Stores up to 4 most recent points; used to generate a new curve\n  private _data: PointGroup[] = []; // Stores all points in groups (one group per line or dot)\n  private _lastVelocity = 0;\n  private _lastWidth = 0;\n  private _strokeMoveUpdate: (event: SignatureEvent) => void;\n  /* tslint:enable: variable-name */\n\n  constructor(\n    private canvas: HTMLCanvasElement,\n    options: Options = {},\n  ) {\n    super();\n    this.velocityFilterWeight = options.velocityFilterWeight || 0.7;\n    this.minWidth = options.minWidth || 0.5;\n    this.maxWidth = options.maxWidth || 2.5;\n    this.throttle = ('throttle' in options ? options.throttle : 16) as number; // in milisecondss\n    this.minDistance = (\n      'minDistance' in options ? options.minDistance : 5\n    ) as number; // in pixels\n    this.dotSize = options.dotSize || 0;\n    this.penColor = options.penColor || 'black';\n    this.backgroundColor = options.backgroundColor || 'rgba(0,0,0,0)';\n    this.compositeOperation = options.compositeOperation || 'source-over';\n    this.canvasContextOptions = (\n      'canvasContextOptions' in options ? options.canvasContextOptions : {}\n    ) as CanvasRenderingContext2DSettings;\n\n    this._strokeMoveUpdate = this.throttle\n      ? throttle(SignaturePad.prototype._strokeUpdate, this.throttle)\n      : SignaturePad.prototype._strokeUpdate;\n    this._ctx = canvas.getContext(\n      '2d',\n      this.canvasContextOptions,\n    ) as CanvasRenderingContext2D;\n\n    this.clear();\n\n    // Enable mouse and touch event handlers\n    this.on();\n  }\n\n  public clear(): void {\n    const { _ctx: ctx, canvas } = this;\n\n    // Clear canvas using background color\n    ctx.fillStyle = this.backgroundColor;\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    this._data = [];\n    this._reset(this._getPointGroupOptions());\n    this._isEmpty = true;\n  }\n\n  public fromDataURL(\n    dataUrl: string,\n    options: {\n      ratio?: number;\n      width?: number;\n      height?: number;\n      xOffset?: number;\n      yOffset?: number;\n    } = {},\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const image = new Image();\n      const ratio = options.ratio || window.devicePixelRatio || 1;\n      const width = options.width || this.canvas.width / ratio;\n      const height = options.height || this.canvas.height / ratio;\n      const xOffset = options.xOffset || 0;\n      const yOffset = options.yOffset || 0;\n\n      this._reset(this._getPointGroupOptions());\n\n      image.onload = (): void => {\n        this._ctx.drawImage(image, xOffset, yOffset, width, height);\n        resolve();\n      };\n      image.onerror = (error): void => {\n        reject(error);\n      };\n      image.crossOrigin = 'anonymous';\n      image.src = dataUrl;\n\n      this._isEmpty = false;\n    });\n  }\n\n  public toDataURL(\n    type: 'image/svg+xml',\n    encoderOptions?: ToSVGOptions,\n  ): string;\n  public toDataURL(type?: string, encoderOptions?: number): string;\n  public toDataURL(\n    type = 'image/png',\n    encoderOptions?: number | ToSVGOptions | undefined,\n  ): string {\n    switch (type) {\n      case 'image/svg+xml':\n        if (typeof encoderOptions !== 'object') {\n          encoderOptions = undefined;\n        }\n        return `data:image/svg+xml;base64,${btoa(\n          this.toSVG(encoderOptions as ToSVGOptions),\n        )}`;\n      default:\n        if (typeof encoderOptions !== 'number') {\n          encoderOptions = undefined;\n        }\n        return this.canvas.toDataURL(type, encoderOptions);\n    }\n  }\n\n  public on(): void {\n    // Disable panning/zooming when touching canvas element\n    this.canvas.style.touchAction = 'none';\n    this.canvas.style.msTouchAction = 'none';\n    this.canvas.style.userSelect = 'none';\n\n    const isIOS =\n      /Macintosh/.test(navigator.userAgent) && 'ontouchstart' in document;\n\n    // The \"Scribble\" feature of iOS intercepts point events. So that we can lose some of them when tapping rapidly.\n    // Use touch events for iOS platforms to prevent it. See https://developer.apple.com/forums/thread/664108 for more information.\n    if (window.PointerEvent && !isIOS) {\n      this._handlePointerEvents();\n    } else {\n      this._handleMouseEvents();\n\n      if ('ontouchstart' in window) {\n        this._handleTouchEvents();\n      }\n    }\n  }\n\n  public off(): void {\n    // Enable panning/zooming when touching canvas element\n    this.canvas.style.touchAction = 'auto';\n    this.canvas.style.msTouchAction = 'auto';\n    this.canvas.style.userSelect = 'auto';\n\n    this.canvas.removeEventListener('pointerdown', this._handlePointerStart);\n    this.canvas.removeEventListener('pointermove', this._handlePointerMove);\n    this.canvas.ownerDocument.removeEventListener(\n      'pointerup',\n      this._handlePointerEnd,\n    );\n\n    this.canvas.removeEventListener('mousedown', this._handleMouseDown);\n    this.canvas.removeEventListener('mousemove', this._handleMouseMove);\n    this.canvas.ownerDocument.removeEventListener(\n      'mouseup',\n      this._handleMouseUp,\n    );\n\n    this.canvas.removeEventListener('touchstart', this._handleTouchStart);\n    this.canvas.removeEventListener('touchmove', this._handleTouchMove);\n    this.canvas.removeEventListener('touchend', this._handleTouchEnd);\n  }\n\n  public isEmpty(): boolean {\n    return this._isEmpty;\n  }\n\n  public fromData(\n    pointGroups: PointGroup[],\n    { clear = true }: FromDataOptions = {},\n  ): void {\n    if (clear) {\n      this.clear();\n    }\n\n    this._fromData(\n      pointGroups,\n      this._drawCurve.bind(this),\n      this._drawDot.bind(this),\n    );\n\n    this._data = this._data.concat(pointGroups);\n  }\n\n  public toData(): PointGroup[] {\n    return this._data;\n  }\n\n  // Event handlers\n  private _handleMouseDown = (event: MouseEvent): void => {\n    if (event.buttons === 1) {\n      this._strokeBegin(event);\n    }\n  };\n\n  private _handleMouseMove = (event: MouseEvent): void => {\n    this._strokeMoveUpdate(event);\n  };\n\n  private _handleMouseUp = (event: MouseEvent): void => {\n    if (event.buttons === 1) {\n      this._strokeEnd(event);\n    }\n  };\n\n  private _handleTouchStart = (event: TouchEvent): void => {\n    // Prevent scrolling.\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    if (event.targetTouches.length === 1) {\n      const touch = event.changedTouches[0];\n      this._strokeBegin(touch);\n    }\n  };\n\n  private _handleTouchMove = (event: TouchEvent): void => {\n    // Prevent scrolling.\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    const touch = event.targetTouches[0];\n    this._strokeMoveUpdate(touch);\n  };\n\n  private _handleTouchEnd = (event: TouchEvent): void => {\n    const wasCanvasTouched = event.target === this.canvas;\n    if (wasCanvasTouched) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      const touch = event.changedTouches[0];\n      this._strokeEnd(touch);\n    }\n  };\n\n  private _handlePointerStart = (event: PointerEvent): void => {\n    event.preventDefault();\n    this._strokeBegin(event);\n  };\n\n  private _handlePointerMove = (event: PointerEvent): void => {\n    this._strokeMoveUpdate(event);\n  };\n\n  private _handlePointerEnd = (event: PointerEvent): void => {\n    if (this._drawingStroke) {\n      event.preventDefault();\n      this._strokeEnd(event);\n    }\n  };\n\n  private _getPointGroupOptions(group?: PointGroup): PointGroupOptions {\n    return {\n      penColor: group && 'penColor' in group ? group.penColor : this.penColor,\n      dotSize: group && 'dotSize' in group ? group.dotSize : this.dotSize,\n      minWidth: group && 'minWidth' in group ? group.minWidth : this.minWidth,\n      maxWidth: group && 'maxWidth' in group ? group.maxWidth : this.maxWidth,\n      velocityFilterWeight:\n        group && 'velocityFilterWeight' in group\n          ? group.velocityFilterWeight\n          : this.velocityFilterWeight,\n      compositeOperation:\n        group && 'compositeOperation' in group\n          ? group.compositeOperation\n          : this.compositeOperation,\n    };\n  }\n\n  // Private methods\n  private _strokeBegin(event: SignatureEvent): void {\n    const cancelled = !this.dispatchEvent(\n      new CustomEvent('beginStroke', { detail: event, cancelable: true }),\n    );\n    if (cancelled) {\n      return;\n    }\n    this._drawingStroke = true;\n\n    const pointGroupOptions = this._getPointGroupOptions();\n\n    const newPointGroup: PointGroup = {\n      ...pointGroupOptions,\n      points: [],\n    };\n\n    this._data.push(newPointGroup);\n    this._reset(pointGroupOptions);\n    this._strokeUpdate(event);\n  }\n\n  private _strokeUpdate(event: SignatureEvent): void {\n    if (!this._drawingStroke) {\n      return;\n    }\n\n    if (this._data.length === 0) {\n      // This can happen if clear() was called while a signature is still in progress,\n      // or if there is a race condition between start/update events.\n      this._strokeBegin(event);\n      return;\n    }\n\n    this.dispatchEvent(\n      new CustomEvent('beforeUpdateStroke', { detail: event }),\n    );\n\n    const x = event.clientX;\n    const y = event.clientY;\n    const pressure =\n      (event as PointerEvent).pressure !== undefined\n        ? (event as PointerEvent).pressure\n        : (event as Touch).force !== undefined\n          ? (event as Touch).force\n          : 0;\n\n    const point = this._createPoint(x, y, pressure);\n    const lastPointGroup = this._data[this._data.length - 1];\n    const lastPoints = lastPointGroup.points;\n    const lastPoint =\n      lastPoints.length > 0 && lastPoints[lastPoints.length - 1];\n    const isLastPointTooClose = lastPoint\n      ? point.distanceTo(lastPoint) <= this.minDistance\n      : false;\n    const pointGroupOptions = this._getPointGroupOptions(lastPointGroup);\n\n    // Skip this point if it's too close to the previous one\n    if (!lastPoint || !(lastPoint && isLastPointTooClose)) {\n      const curve = this._addPoint(point, pointGroupOptions);\n\n      if (!lastPoint) {\n        this._drawDot(point, pointGroupOptions);\n      } else if (curve) {\n        this._drawCurve(curve, pointGroupOptions);\n      }\n\n      lastPoints.push({\n        time: point.time,\n        x: point.x,\n        y: point.y,\n        pressure: point.pressure,\n      });\n    }\n\n    this.dispatchEvent(new CustomEvent('afterUpdateStroke', { detail: event }));\n  }\n\n  private _strokeEnd(event: SignatureEvent): void {\n    if (!this._drawingStroke) {\n      return;\n    }\n\n    this._strokeUpdate(event);\n\n    this._drawingStroke = false;\n    this.dispatchEvent(new CustomEvent('endStroke', { detail: event }));\n  }\n\n  private _handlePointerEvents(): void {\n    this._drawingStroke = false;\n\n    this.canvas.addEventListener('pointerdown', this._handlePointerStart);\n    this.canvas.addEventListener('pointermove', this._handlePointerMove);\n    this.canvas.ownerDocument.addEventListener(\n      'pointerup',\n      this._handlePointerEnd,\n    );\n  }\n\n  private _handleMouseEvents(): void {\n    this._drawingStroke = false;\n\n    this.canvas.addEventListener('mousedown', this._handleMouseDown);\n    this.canvas.addEventListener('mousemove', this._handleMouseMove);\n    this.canvas.ownerDocument.addEventListener('mouseup', this._handleMouseUp);\n  }\n\n  private _handleTouchEvents(): void {\n    this.canvas.addEventListener('touchstart', this._handleTouchStart);\n    this.canvas.addEventListener('touchmove', this._handleTouchMove);\n    this.canvas.addEventListener('touchend', this._handleTouchEnd);\n  }\n\n  // Called when a new line is started\n  private _reset(options: PointGroupOptions): void {\n    this._lastPoints = [];\n    this._lastVelocity = 0;\n    this._lastWidth = (options.minWidth + options.maxWidth) / 2;\n    this._ctx.fillStyle = options.penColor;\n    this._ctx.globalCompositeOperation = options.compositeOperation;\n  }\n\n  private _createPoint(x: number, y: number, pressure: number): Point {\n    const rect = this.canvas.getBoundingClientRect();\n\n    return new Point(\n      x - rect.left,\n      y - rect.top,\n      pressure,\n      new Date().getTime(),\n    );\n  }\n\n  // Add point to _lastPoints array and generate a new curve if there are enough points (i.e. 3)\n  private _addPoint(point: Point, options: PointGroupOptions): Bezier | null {\n    const { _lastPoints } = this;\n\n    _lastPoints.push(point);\n\n    if (_lastPoints.length > 2) {\n      // To reduce the initial lag make it work with 3 points\n      // by copying the first point to the beginning.\n      if (_lastPoints.length === 3) {\n        _lastPoints.unshift(_lastPoints[0]);\n      }\n\n      // _points array will always have 4 points here.\n      const widths = this._calculateCurveWidths(\n        _lastPoints[1],\n        _lastPoints[2],\n        options,\n      );\n      const curve = Bezier.fromPoints(_lastPoints, widths);\n\n      // Remove the first element from the list, so that there are no more than 4 points at any time.\n      _lastPoints.shift();\n\n      return curve;\n    }\n\n    return null;\n  }\n\n  private _calculateCurveWidths(\n    startPoint: Point,\n    endPoint: Point,\n    options: PointGroupOptions,\n  ): { start: number; end: number } {\n    const velocity =\n      options.velocityFilterWeight * endPoint.velocityFrom(startPoint) +\n      (1 - options.velocityFilterWeight) * this._lastVelocity;\n\n    const newWidth = this._strokeWidth(velocity, options);\n\n    const widths = {\n      end: newWidth,\n      start: this._lastWidth,\n    };\n\n    this._lastVelocity = velocity;\n    this._lastWidth = newWidth;\n\n    return widths;\n  }\n\n  private _strokeWidth(velocity: number, options: PointGroupOptions): number {\n    return Math.max(options.maxWidth / (velocity + 1), options.minWidth);\n  }\n\n  private _drawCurveSegment(x: number, y: number, width: number): void {\n    const ctx = this._ctx;\n\n    ctx.moveTo(x, y);\n    ctx.arc(x, y, width, 0, 2 * Math.PI, false);\n    this._isEmpty = false;\n  }\n\n  private _drawCurve(curve: Bezier, options: PointGroupOptions): void {\n    const ctx = this._ctx;\n    const widthDelta = curve.endWidth - curve.startWidth;\n    // '2' is just an arbitrary number here. If only length is used, then\n    // there are gaps between curve segments :/\n    const drawSteps = Math.ceil(curve.length()) * 2;\n\n    ctx.beginPath();\n    ctx.fillStyle = options.penColor;\n\n    for (let i = 0; i < drawSteps; i += 1) {\n      // Calculate the Bezier (x, y) coordinate for this step.\n      const t = i / drawSteps;\n      const tt = t * t;\n      const ttt = tt * t;\n      const u = 1 - t;\n      const uu = u * u;\n      const uuu = uu * u;\n\n      let x = uuu * curve.startPoint.x;\n      x += 3 * uu * t * curve.control1.x;\n      x += 3 * u * tt * curve.control2.x;\n      x += ttt * curve.endPoint.x;\n\n      let y = uuu * curve.startPoint.y;\n      y += 3 * uu * t * curve.control1.y;\n      y += 3 * u * tt * curve.control2.y;\n      y += ttt * curve.endPoint.y;\n\n      const width = Math.min(\n        curve.startWidth + ttt * widthDelta,\n        options.maxWidth,\n      );\n      this._drawCurveSegment(x, y, width);\n    }\n\n    ctx.closePath();\n    ctx.fill();\n  }\n\n  private _drawDot(point: BasicPoint, options: PointGroupOptions): void {\n    const ctx = this._ctx;\n    const width =\n      options.dotSize > 0\n        ? options.dotSize\n        : (options.minWidth + options.maxWidth) / 2;\n\n    ctx.beginPath();\n    this._drawCurveSegment(point.x, point.y, width);\n    ctx.closePath();\n    ctx.fillStyle = options.penColor;\n    ctx.fill();\n  }\n\n  private _fromData(\n    pointGroups: PointGroup[],\n    drawCurve: SignaturePad['_drawCurve'],\n    drawDot: SignaturePad['_drawDot'],\n  ): void {\n    for (const group of pointGroups) {\n      const { points } = group;\n      const pointGroupOptions = this._getPointGroupOptions(group);\n\n      if (points.length > 1) {\n        for (let j = 0; j < points.length; j += 1) {\n          const basicPoint = points[j];\n          const point = new Point(\n            basicPoint.x,\n            basicPoint.y,\n            basicPoint.pressure,\n            basicPoint.time,\n          );\n\n          if (j === 0) {\n            this._reset(pointGroupOptions);\n          }\n\n          const curve = this._addPoint(point, pointGroupOptions);\n\n          if (curve) {\n            drawCurve(curve, pointGroupOptions);\n          }\n        }\n      } else {\n        this._reset(pointGroupOptions);\n\n        drawDot(points[0], pointGroupOptions);\n      }\n    }\n  }\n\n  public toSVG({ includeBackgroundColor = false }: ToSVGOptions = {}): string {\n    const pointGroups = this._data;\n    const ratio = Math.max(window.devicePixelRatio || 1, 1);\n    const minX = 0;\n    const minY = 0;\n    const maxX = this.canvas.width / ratio;\n    const maxY = this.canvas.height / ratio;\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n    svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\n    svg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');\n    svg.setAttribute('viewBox', `${minX} ${minY} ${maxX} ${maxY}`);\n    svg.setAttribute('width', maxX.toString());\n    svg.setAttribute('height', maxY.toString());\n\n    if (includeBackgroundColor && this.backgroundColor) {\n      const rect = document.createElement('rect');\n      rect.setAttribute('width', '100%');\n      rect.setAttribute('height', '100%');\n      rect.setAttribute('fill', this.backgroundColor);\n\n      svg.appendChild(rect);\n    }\n\n    this._fromData(\n      pointGroups,\n\n      (curve, { penColor }) => {\n        const path = document.createElement('path');\n\n        // Need to check curve for NaN values, these pop up when drawing\n        // lines on the canvas that are not continuous. E.g. Sharp corners\n        // or stopping mid-stroke and than continuing without lifting mouse.\n        /* eslint-disable no-restricted-globals */\n        if (\n          !isNaN(curve.control1.x) &&\n          !isNaN(curve.control1.y) &&\n          !isNaN(curve.control2.x) &&\n          !isNaN(curve.control2.y)\n        ) {\n          const attr =\n            `M ${curve.startPoint.x.toFixed(3)},${curve.startPoint.y.toFixed(\n              3,\n            )} ` +\n            `C ${curve.control1.x.toFixed(3)},${curve.control1.y.toFixed(3)} ` +\n            `${curve.control2.x.toFixed(3)},${curve.control2.y.toFixed(3)} ` +\n            `${curve.endPoint.x.toFixed(3)},${curve.endPoint.y.toFixed(3)}`;\n          path.setAttribute('d', attr);\n          path.setAttribute('stroke-width', (curve.endWidth * 2.25).toFixed(3));\n          path.setAttribute('stroke', penColor);\n          path.setAttribute('fill', 'none');\n          path.setAttribute('stroke-linecap', 'round');\n\n          svg.appendChild(path);\n        }\n        /* eslint-enable no-restricted-globals */\n      },\n\n      (point, { penColor, dotSize, minWidth, maxWidth }) => {\n        const circle = document.createElement('circle');\n        const size = dotSize > 0 ? dotSize : (minWidth + maxWidth) / 2;\n        circle.setAttribute('r', size.toString());\n        circle.setAttribute('cx', point.x.toString());\n        circle.setAttribute('cy', point.y.toString());\n        circle.setAttribute('fill', penColor);\n\n        svg.appendChild(circle);\n      },\n    );\n\n    return svg.outerHTML;\n  }\n}\n"], "mappings": ";;;IAQa,cAAK;EAMhB,YAAY,GAAW,GAAW,UAAmB,MAAa;AAChE,QAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACxB,YAAM,IAAI,MAAM,sBAAsB,CAAC,KAAK,CAAC,GAAG;IACjD;AACD,SAAK,IAAI,CAAC;AACV,SAAK,IAAI,CAAC;AACV,SAAK,WAAW,YAAY;AAC5B,SAAK,OAAO,QAAQ,KAAK,IAAG;;EAGvB,WAAW,OAAiB;AACjC,WAAO,KAAK,KACV,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;;EAI1D,OAAO,OAAiB;AAC7B,WACE,KAAK,MAAM,MAAM,KACjB,KAAK,MAAM,MAAM,KACjB,KAAK,aAAa,MAAM,YACxB,KAAK,SAAS,MAAM;;EAIjB,aAAa,OAAiB;AACnC,WAAO,KAAK,SAAS,MAAM,OACvB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,MAAM,QAC5C;;AAEP;IC1CY,eAAA,QAAM;EACV,OAAO,WACZ,QACA,QAAsC;AAEtC,UAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;AACxE,UAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;AAExE,WAAO,IAAI,QAAO,OAAO,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,GAAG,OAAO,OAAO,OAAO,GAAG;;EAGlE,OAAO,uBACb,IACA,IACA,IAAc;AAKd,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AAEtB,UAAM,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;AAC3D,UAAM,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;AAE3D,UAAM,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC1C,UAAM,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE1C,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AAEtB,UAAM,IAAI,MAAM,KAAK;AACrB,UAAM,KAAK,EAAE,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,EAAC;AAEjD,UAAM,KAAK,GAAG,IAAI,GAAG;AACrB,UAAM,KAAK,GAAG,IAAI,GAAG;AAErB,WAAO;MACL,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;MAClC,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;;EAItC,YACS,YACA,UACA,UACA,UACA,YACA,UAAgB;AALhB,SAAU,aAAV;AACA,SAAQ,WAAR;AACA,SAAQ,WAAR;AACA,SAAQ,WAAR;AACA,SAAU,aAAV;AACA,SAAQ,WAAR;;EAIF,SAAM;AACX,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,QAAI;AACJ,QAAI;AAEJ,aAAS,IAAI,GAAG,KAAK,OAAO,KAAK,GAAG;AAClC,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,KAAK,MACd,GACA,KAAK,WAAW,GAChB,KAAK,SAAS,GACd,KAAK,SAAS,GACd,KAAK,SAAS,CAAC;AAEjB,YAAM,KAAK,KAAK,MACd,GACA,KAAK,WAAW,GAChB,KAAK,SAAS,GACd,KAAK,SAAS,GACd,KAAK,SAAS,CAAC;AAGjB,UAAI,IAAI,GAAG;AACT,cAAM,QAAQ,KAAM;AACpB,cAAM,QAAQ,KAAM;AAEpB,kBAAU,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;MAClD;AAED,WAAK;AACL,WAAK;IACN;AAED,WAAO;;EAID,MACN,GACA,OACA,IACA,IACA,KAAW;AAGX,WAAe,SAAS,IAAM,MAAM,IAAM,MAAO,IAAM,KAC/C,IAAO,MAAS,IAAM,MAAM,IAAM,KAAM,IACxC,IAAO,MAAS,IAAM,KAAK,IAAa,IACjC,MAAQ,IAAY,IAAa;;AAEnD;IC5GY,6BAAoB;EAK/B,cAAA;AACE,QAAI;AACF,WAAK,MAAM,IAAI,YAAW;IAC3B,SAAQ,OAAO;AAGd,WAAK,MAAM;IACZ;;EAGH,iBACE,MACA,UACA,SAA2C;AAE3C,SAAK,IAAI,iBAAiB,MAAM,UAAU,OAAO;;EAGnD,cAAc,OAAY;AACxB,WAAO,KAAK,IAAI,cAAc,KAAK;;EAGrC,oBACE,MACA,UACA,SAAwC;AAExC,SAAK,IAAI,oBAAoB,MAAM,UAAU,OAAO;;AAEvD;SC/Be,SACd,IACA,OAAO,KAAG;AAEV,MAAI,WAAW;AACf,MAAI,UAAyB;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,QAAM,QAAQ,MAAW;AACvB,eAAW,KAAK,IAAG;AACnB,cAAU;AACV,aAAS,GAAG,MAAM,eAAe,UAAU;AAE3C,QAAI,CAAC,SAAS;AACZ,sBAAgB;AAChB,mBAAa,CAAA;IACd;EACH;AAEA,SAAO,SAAS,WAAsB,MAAW;AAC/C,UAAM,MAAM,KAAK,IAAG;AACpB,UAAM,YAAY,QAAQ,MAAM;AAEhC,oBAAgB;AAChB,iBAAa;AAEb,QAAI,aAAa,KAAK,YAAY,MAAM;AACtC,UAAI,SAAS;AACX,qBAAa,OAAO;AACpB,kBAAU;MACX;AAED,iBAAW;AACX,eAAS,GAAG,MAAM,eAAe,UAAU;AAE3C,UAAI,CAAC,SAAS;AACZ,wBAAgB;AAChB,qBAAa,CAAA;MACd;IACF,WAAU,CAAC,SAAS;AACnB,gBAAU,OAAO,WAAW,OAAO,SAAS;IAC7C;AAED,WAAO;EACT;AACF;ACOqB,IAAA,eAAA,MAAA,sBAAqB,qBAAoB;EAyB5D,YACU,QACR,UAAmB,CAAA,GAAE;AAErB,UAAK;AAHG,SAAM,SAAN;AAVF,SAAc,iBAAG;AACjB,SAAQ,WAAG;AACX,SAAW,cAAY,CAAA;AACvB,SAAK,QAAiB,CAAA;AACtB,SAAa,gBAAG;AAChB,SAAU,aAAG;AAuLb,SAAA,mBAAmB,CAAC,UAA2B;AACrD,UAAI,MAAM,YAAY,GAAG;AACvB,aAAK,aAAa,KAAK;MACxB;IACH;AAEQ,SAAA,mBAAmB,CAAC,UAA2B;AACrD,WAAK,kBAAkB,KAAK;IAC9B;AAEQ,SAAA,iBAAiB,CAAC,UAA2B;AACnD,UAAI,MAAM,YAAY,GAAG;AACvB,aAAK,WAAW,KAAK;MACtB;IACH;AAEQ,SAAA,oBAAoB,CAAC,UAA2B;AAEtD,UAAI,MAAM,YAAY;AACpB,cAAM,eAAc;MACrB;AAED,UAAI,MAAM,cAAc,WAAW,GAAG;AACpC,cAAM,QAAQ,MAAM,eAAe,CAAC;AACpC,aAAK,aAAa,KAAK;MACxB;IACH;AAEQ,SAAA,mBAAmB,CAAC,UAA2B;AAErD,UAAI,MAAM,YAAY;AACpB,cAAM,eAAc;MACrB;AAED,YAAM,QAAQ,MAAM,cAAc,CAAC;AACnC,WAAK,kBAAkB,KAAK;IAC9B;AAEQ,SAAA,kBAAkB,CAAC,UAA2B;AACpD,YAAM,mBAAmB,MAAM,WAAW,KAAK;AAC/C,UAAI,kBAAkB;AACpB,YAAI,MAAM,YAAY;AACpB,gBAAM,eAAc;QACrB;AACD,cAAM,QAAQ,MAAM,eAAe,CAAC;AACpC,aAAK,WAAW,KAAK;MACtB;IACH;AAEQ,SAAA,sBAAsB,CAAC,UAA6B;AAC1D,YAAM,eAAc;AACpB,WAAK,aAAa,KAAK;IACzB;AAEQ,SAAA,qBAAqB,CAAC,UAA6B;AACzD,WAAK,kBAAkB,KAAK;IAC9B;AAEQ,SAAA,oBAAoB,CAAC,UAA6B;AACxD,UAAI,KAAK,gBAAgB;AACvB,cAAM,eAAc;AACpB,aAAK,WAAW,KAAK;MACtB;IACH;AA7OE,SAAK,uBAAuB,QAAQ,wBAAwB;AAC5D,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,WAAY,cAAc,UAAU,QAAQ,WAAW;AAC5D,SAAK,cACH,iBAAiB,UAAU,QAAQ,cAAc;AAEnD,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,kBAAkB,QAAQ,mBAAmB;AAClD,SAAK,qBAAqB,QAAQ,sBAAsB;AACxD,SAAK,uBACH,0BAA0B,UAAU,QAAQ,uBAAuB,CAAA;AAGrE,SAAK,oBAAoB,KAAK,WAC1B,SAAS,cAAa,UAAU,eAAe,KAAK,QAAQ,IAC5D,cAAa,UAAU;AAC3B,SAAK,OAAO,OAAO,WACjB,MACA,KAAK,oBAAoB;AAG3B,SAAK,MAAK;AAGV,SAAK,GAAE;;EAGF,QAAK;AACV,UAAM,EAAE,MAAM,KAAK,OAAM,IAAK;AAG9B,QAAI,YAAY,KAAK;AACrB,QAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,QAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE9C,SAAK,QAAQ,CAAA;AACb,SAAK,OAAO,KAAK,sBAAqB,CAAE;AACxC,SAAK,WAAW;;EAGX,YACL,SACA,UAMI,CAAA,GAAE;AAEN,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,YAAM,QAAQ,IAAI,MAAK;AACvB,YAAM,QAAQ,QAAQ,SAAS,OAAO,oBAAoB;AAC1D,YAAM,QAAQ,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACnD,YAAM,SAAS,QAAQ,UAAU,KAAK,OAAO,SAAS;AACtD,YAAM,UAAU,QAAQ,WAAW;AACnC,YAAM,UAAU,QAAQ,WAAW;AAEnC,WAAK,OAAO,KAAK,sBAAqB,CAAE;AAExC,YAAM,SAAS,MAAW;AACxB,aAAK,KAAK,UAAU,OAAO,SAAS,SAAS,OAAO,MAAM;AAC1D,gBAAO;MACT;AACA,YAAM,UAAU,CAAC,UAAe;AAC9B,eAAO,KAAK;MACd;AACA,YAAM,cAAc;AACpB,YAAM,MAAM;AAEZ,WAAK,WAAW;IAClB,CAAC;;EAQI,UACL,OAAO,aACP,gBAAkD;AAElD,YAAQ,MAAI;MACV,KAAK;AACH,YAAI,OAAO,mBAAmB,UAAU;AACtC,2BAAiB;QAClB;AACD,eAAO,6BAA6B,KAClC,KAAK,MAAM,cAA8B,CAAC,CAC3C;MACH;AACE,YAAI,OAAO,mBAAmB,UAAU;AACtC,2BAAiB;QAClB;AACD,eAAO,KAAK,OAAO,UAAU,MAAM,cAAc;IACpD;;EAGI,KAAE;AAEP,SAAK,OAAO,MAAM,cAAc;AAChC,SAAK,OAAO,MAAM,gBAAgB;AAClC,SAAK,OAAO,MAAM,aAAa;AAE/B,UAAM,QACJ,YAAY,KAAK,UAAU,SAAS,KAAK,kBAAkB;AAI7D,QAAI,OAAO,gBAAgB,CAAC,OAAO;AACjC,WAAK,qBAAoB;IAC1B,OAAM;AACL,WAAK,mBAAkB;AAEvB,UAAI,kBAAkB,QAAQ;AAC5B,aAAK,mBAAkB;MACxB;IACF;;EAGI,MAAG;AAER,SAAK,OAAO,MAAM,cAAc;AAChC,SAAK,OAAO,MAAM,gBAAgB;AAClC,SAAK,OAAO,MAAM,aAAa;AAE/B,SAAK,OAAO,oBAAoB,eAAe,KAAK,mBAAmB;AACvE,SAAK,OAAO,oBAAoB,eAAe,KAAK,kBAAkB;AACtE,SAAK,OAAO,cAAc,oBACxB,aACA,KAAK,iBAAiB;AAGxB,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,SAAK,OAAO,cAAc,oBACxB,WACA,KAAK,cAAc;AAGrB,SAAK,OAAO,oBAAoB,cAAc,KAAK,iBAAiB;AACpE,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,SAAK,OAAO,oBAAoB,YAAY,KAAK,eAAe;;EAG3D,UAAO;AACZ,WAAO,KAAK;;EAGP,SACL,aACA,EAAE,QAAQ,KAAI,IAAsB,CAAA,GAAE;AAEtC,QAAI,OAAO;AACT,WAAK,MAAK;IACX;AAED,SAAK,UACH,aACA,KAAK,WAAW,KAAK,IAAI,GACzB,KAAK,SAAS,KAAK,IAAI,CAAC;AAG1B,SAAK,QAAQ,KAAK,MAAM,OAAO,WAAW;;EAGrC,SAAM;AACX,WAAO,KAAK;;EAqEN,sBAAsB,OAAkB;AAC9C,WAAO;MACL,UAAU,SAAS,cAAc,QAAQ,MAAM,WAAW,KAAK;MAC/D,SAAS,SAAS,aAAa,QAAQ,MAAM,UAAU,KAAK;MAC5D,UAAU,SAAS,cAAc,QAAQ,MAAM,WAAW,KAAK;MAC/D,UAAU,SAAS,cAAc,QAAQ,MAAM,WAAW,KAAK;MAC/D,sBACE,SAAS,0BAA0B,QAC/B,MAAM,uBACN,KAAK;MACX,oBACE,SAAS,wBAAwB,QAC7B,MAAM,qBACN,KAAK;;;EAKP,aAAa,OAAqB;AACxC,UAAM,YAAY,CAAC,KAAK,cACtB,IAAI,YAAY,eAAe,EAAE,QAAQ,OAAO,YAAY,KAAI,CAAE,CAAC;AAErE,QAAI,WAAW;AACb;IACD;AACD,SAAK,iBAAiB;AAEtB,UAAM,oBAAoB,KAAK,sBAAqB;AAEpD,UAAM,gBAAa,OAAA,OAAA,OAAA,OAAA,CAAA,GACd,iBAAiB,GAAA,EACpB,QAAQ,CAAA,EAAE,CAAA;AAGZ,SAAK,MAAM,KAAK,aAAa;AAC7B,SAAK,OAAO,iBAAiB;AAC7B,SAAK,cAAc,KAAK;;EAGlB,cAAc,OAAqB;AACzC,QAAI,CAAC,KAAK,gBAAgB;AACxB;IACD;AAED,QAAI,KAAK,MAAM,WAAW,GAAG;AAG3B,WAAK,aAAa,KAAK;AACvB;IACD;AAED,SAAK,cACH,IAAI,YAAY,sBAAsB,EAAE,QAAQ,MAAK,CAAE,CAAC;AAG1D,UAAM,IAAI,MAAM;AAChB,UAAM,IAAI,MAAM;AAChB,UAAM,WACH,MAAuB,aAAa,SAChC,MAAuB,WACvB,MAAgB,UAAU,SACxB,MAAgB,QACjB;AAER,UAAM,QAAQ,KAAK,aAAa,GAAG,GAAG,QAAQ;AAC9C,UAAM,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACvD,UAAM,aAAa,eAAe;AAClC,UAAM,YACJ,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC;AAC3D,UAAM,sBAAsB,YACxB,MAAM,WAAW,SAAS,KAAK,KAAK,cACpC;AACJ,UAAM,oBAAoB,KAAK,sBAAsB,cAAc;AAGnE,QAAI,CAAC,aAAa,EAAE,aAAa,sBAAsB;AACrD,YAAM,QAAQ,KAAK,UAAU,OAAO,iBAAiB;AAErD,UAAI,CAAC,WAAW;AACd,aAAK,SAAS,OAAO,iBAAiB;MACvC,WAAU,OAAO;AAChB,aAAK,WAAW,OAAO,iBAAiB;MACzC;AAED,iBAAW,KAAK;QACd,MAAM,MAAM;QACZ,GAAG,MAAM;QACT,GAAG,MAAM;QACT,UAAU,MAAM;MACjB,CAAA;IACF;AAED,SAAK,cAAc,IAAI,YAAY,qBAAqB,EAAE,QAAQ,MAAK,CAAE,CAAC;;EAGpE,WAAW,OAAqB;AACtC,QAAI,CAAC,KAAK,gBAAgB;AACxB;IACD;AAED,SAAK,cAAc,KAAK;AAExB,SAAK,iBAAiB;AACtB,SAAK,cAAc,IAAI,YAAY,aAAa,EAAE,QAAQ,MAAK,CAAE,CAAC;;EAG5D,uBAAoB;AAC1B,SAAK,iBAAiB;AAEtB,SAAK,OAAO,iBAAiB,eAAe,KAAK,mBAAmB;AACpE,SAAK,OAAO,iBAAiB,eAAe,KAAK,kBAAkB;AACnE,SAAK,OAAO,cAAc,iBACxB,aACA,KAAK,iBAAiB;;EAIlB,qBAAkB;AACxB,SAAK,iBAAiB;AAEtB,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAC/D,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAC/D,SAAK,OAAO,cAAc,iBAAiB,WAAW,KAAK,cAAc;;EAGnE,qBAAkB;AACxB,SAAK,OAAO,iBAAiB,cAAc,KAAK,iBAAiB;AACjE,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAC/D,SAAK,OAAO,iBAAiB,YAAY,KAAK,eAAe;;EAIvD,OAAO,SAA0B;AACvC,SAAK,cAAc,CAAA;AACnB,SAAK,gBAAgB;AACrB,SAAK,cAAc,QAAQ,WAAW,QAAQ,YAAY;AAC1D,SAAK,KAAK,YAAY,QAAQ;AAC9B,SAAK,KAAK,2BAA2B,QAAQ;;EAGvC,aAAa,GAAW,GAAW,UAAgB;AACzD,UAAM,OAAO,KAAK,OAAO,sBAAqB;AAE9C,WAAO,IAAI,MACT,IAAI,KAAK,MACT,IAAI,KAAK,KACT,WACA,oBAAI,KAAI,GAAG,QAAO,CAAE;;EAKhB,UAAU,OAAc,SAA0B;AACxD,UAAM,EAAE,YAAW,IAAK;AAExB,gBAAY,KAAK,KAAK;AAEtB,QAAI,YAAY,SAAS,GAAG;AAG1B,UAAI,YAAY,WAAW,GAAG;AAC5B,oBAAY,QAAQ,YAAY,CAAC,CAAC;MACnC;AAGD,YAAM,SAAS,KAAK,sBAClB,YAAY,CAAC,GACb,YAAY,CAAC,GACb,OAAO;AAET,YAAM,QAAQ,OAAO,WAAW,aAAa,MAAM;AAGnD,kBAAY,MAAK;AAEjB,aAAO;IACR;AAED,WAAO;;EAGD,sBACN,YACA,UACA,SAA0B;AAE1B,UAAM,WACJ,QAAQ,uBAAuB,SAAS,aAAa,UAAU,KAC9D,IAAI,QAAQ,wBAAwB,KAAK;AAE5C,UAAM,WAAW,KAAK,aAAa,UAAU,OAAO;AAEpD,UAAM,SAAS;MACb,KAAK;MACL,OAAO,KAAK;;AAGd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAElB,WAAO;;EAGD,aAAa,UAAkB,SAA0B;AAC/D,WAAO,KAAK,IAAI,QAAQ,YAAY,WAAW,IAAI,QAAQ,QAAQ;;EAG7D,kBAAkB,GAAW,GAAW,OAAa;AAC3D,UAAM,MAAM,KAAK;AAEjB,QAAI,OAAO,GAAG,CAAC;AACf,QAAI,IAAI,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,IAAI,KAAK;AAC1C,SAAK,WAAW;;EAGV,WAAW,OAAe,SAA0B;AAC1D,UAAM,MAAM,KAAK;AACjB,UAAM,aAAa,MAAM,WAAW,MAAM;AAG1C,UAAM,YAAY,KAAK,KAAK,MAAM,OAAM,CAAE,IAAI;AAE9C,QAAI,UAAS;AACb,QAAI,YAAY,QAAQ;AAExB,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK,GAAG;AAErC,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK;AACjB,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK;AAEjB,UAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,WAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,WAAK,MAAM,MAAM,SAAS;AAE1B,UAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,WAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,WAAK,MAAM,MAAM,SAAS;AAE1B,YAAM,QAAQ,KAAK,IACjB,MAAM,aAAa,MAAM,YACzB,QAAQ,QAAQ;AAElB,WAAK,kBAAkB,GAAG,GAAG,KAAK;IACnC;AAED,QAAI,UAAS;AACb,QAAI,KAAI;;EAGF,SAAS,OAAmB,SAA0B;AAC5D,UAAM,MAAM,KAAK;AACjB,UAAM,QACJ,QAAQ,UAAU,IACd,QAAQ,WACP,QAAQ,WAAW,QAAQ,YAAY;AAE9C,QAAI,UAAS;AACb,SAAK,kBAAkB,MAAM,GAAG,MAAM,GAAG,KAAK;AAC9C,QAAI,UAAS;AACb,QAAI,YAAY,QAAQ;AACxB,QAAI,KAAI;;EAGF,UACN,aACA,WACA,SAAiC;AAEjC,eAAW,SAAS,aAAa;AAC/B,YAAM,EAAE,OAAM,IAAK;AACnB,YAAM,oBAAoB,KAAK,sBAAsB,KAAK;AAE1D,UAAI,OAAO,SAAS,GAAG;AACrB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,gBAAM,aAAa,OAAO,CAAC;AAC3B,gBAAM,QAAQ,IAAI,MAChB,WAAW,GACX,WAAW,GACX,WAAW,UACX,WAAW,IAAI;AAGjB,cAAI,MAAM,GAAG;AACX,iBAAK,OAAO,iBAAiB;UAC9B;AAED,gBAAM,QAAQ,KAAK,UAAU,OAAO,iBAAiB;AAErD,cAAI,OAAO;AACT,sBAAU,OAAO,iBAAiB;UACnC;QACF;MACF,OAAM;AACL,aAAK,OAAO,iBAAiB;AAE7B,gBAAQ,OAAO,CAAC,GAAG,iBAAiB;MACrC;IACF;;EAGI,MAAM,EAAE,yBAAyB,MAAK,IAAmB,CAAA,GAAE;AAChE,UAAM,cAAc,KAAK;AACzB,UAAM,QAAQ,KAAK,IAAI,OAAO,oBAAoB,GAAG,CAAC;AACtD,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,UAAM,OAAO,KAAK,OAAO,SAAS;AAClC,UAAM,MAAM,SAAS,gBAAgB,8BAA8B,KAAK;AAExE,QAAI,aAAa,SAAS,4BAA4B;AACtD,QAAI,aAAa,eAAe,8BAA8B;AAC9D,QAAI,aAAa,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC7D,QAAI,aAAa,SAAS,KAAK,SAAQ,CAAE;AACzC,QAAI,aAAa,UAAU,KAAK,SAAQ,CAAE;AAE1C,QAAI,0BAA0B,KAAK,iBAAiB;AAClD,YAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,WAAK,aAAa,SAAS,MAAM;AACjC,WAAK,aAAa,UAAU,MAAM;AAClC,WAAK,aAAa,QAAQ,KAAK,eAAe;AAE9C,UAAI,YAAY,IAAI;IACrB;AAED,SAAK,UACH,aAEA,CAAC,OAAO,EAAE,SAAQ,MAAM;AACtB,YAAM,OAAO,SAAS,cAAc,MAAM;AAM1C,UACE,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,GACvB;AACA,cAAM,OACJ,KAAK,MAAM,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,WAAW,EAAE,QACvD,CAAC,CACF,MACI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAC5D,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAC1D,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC/D,aAAK,aAAa,KAAK,IAAI;AAC3B,aAAK,aAAa,iBAAiB,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC;AACpE,aAAK,aAAa,UAAU,QAAQ;AACpC,aAAK,aAAa,QAAQ,MAAM;AAChC,aAAK,aAAa,kBAAkB,OAAO;AAE3C,YAAI,YAAY,IAAI;MACrB;IAEH,GAEA,CAAC,OAAO,EAAE,UAAU,SAAS,UAAU,SAAQ,MAAM;AACnD,YAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,YAAM,OAAO,UAAU,IAAI,WAAW,WAAW,YAAY;AAC7D,aAAO,aAAa,KAAK,KAAK,SAAQ,CAAE;AACxC,aAAO,aAAa,MAAM,MAAM,EAAE,SAAQ,CAAE;AAC5C,aAAO,aAAa,MAAM,MAAM,EAAE,SAAQ,CAAE;AAC5C,aAAO,aAAa,QAAQ,QAAQ;AAEpC,UAAI,YAAY,MAAM;IACxB,CAAC;AAGH,WAAO,IAAI;;AAEd;", "names": []}