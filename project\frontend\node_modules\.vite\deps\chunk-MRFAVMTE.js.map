{"version": 3, "sources": ["../../lodash/_assignMergeValue.js", "../../lodash/_safeGet.js", "../../lodash/toPlainObject.js", "../../lodash/_baseMergeDeep.js", "../../lodash/_baseMerge.js", "../../lodash/_createAssigner.js"], "sourcesContent": ["var baseAssignValue = require('./_baseAssignValue'),\n    eq = require('./eq');\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if ((value !== undefined && !eq(object[key], value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nmodule.exports = assignMergeValue;\n", "/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n\n  if (key == '__proto__') {\n    return;\n  }\n\n  return object[key];\n}\n\nmodule.exports = safeGet;\n", "var copyObject = require('./_copyObject'),\n    keysIn = require('./keysIn');\n\n/**\n * Converts `value` to a plain object flattening inherited enumerable string\n * keyed properties of `value` to own properties of the plain object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Object} Returns the converted plain object.\n * @example\n *\n * function Foo() {\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.assign({ 'a': 1 }, new Foo);\n * // => { 'a': 1, 'b': 2 }\n *\n * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));\n * // => { 'a': 1, 'b': 2, 'c': 3 }\n */\nfunction toPlainObject(value) {\n  return copyObject(value, keysIn(value));\n}\n\nmodule.exports = toPlainObject;\n", "var assignMergeValue = require('./_assignMergeValue'),\n    cloneBuffer = require('./_cloneBuffer'),\n    cloneTypedArray = require('./_cloneTypedArray'),\n    copyArray = require('./_copyArray'),\n    initCloneObject = require('./_initCloneObject'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray'),\n    isArrayLikeObject = require('./isArrayLikeObject'),\n    isBuffer = require('./isBuffer'),\n    isFunction = require('./isFunction'),\n    isObject = require('./isObject'),\n    isPlainObject = require('./isPlainObject'),\n    isTypedArray = require('./isTypedArray'),\n    safeGet = require('./_safeGet'),\n    toPlainObject = require('./toPlainObject');\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n      srcValue = safeGet(source, key),\n      stacked = stack.get(srcValue);\n\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer\n    ? customizer(objValue, srcValue, (key + ''), object, source, stack)\n    : undefined;\n\n  var isCommon = newValue === undefined;\n\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n        isBuff = !isArr && isBuffer(srcValue),\n        isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      }\n      else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      }\n      else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      }\n      else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      }\n      else {\n        newValue = [];\n      }\n    }\n    else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      }\n      else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    }\n    else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\n\nmodule.exports = baseMergeDeep;\n", "var Stack = require('./_Stack'),\n    assignMergeValue = require('./_assignMergeValue'),\n    baseFor = require('./_baseFor'),\n    baseMergeDeep = require('./_baseMergeDeep'),\n    isObject = require('./isObject'),\n    keysIn = require('./keysIn'),\n    safeGet = require('./_safeGet');\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function(srcValue, key) {\n    stack || (stack = new Stack);\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    }\n    else {\n      var newValue = customizer\n        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)\n        : undefined;\n\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\n\nmodule.exports = baseMerge;\n", "var baseRest = require('./_baseRest'),\n    isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\nmodule.exports = createAssigner;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,KAAK;AAWT,aAAS,iBAAiB,QAAQ,KAAK,OAAO;AAC5C,UAAK,UAAU,UAAa,CAAC,GAAG,OAAO,GAAG,GAAG,KAAK,KAC7C,UAAU,UAAa,EAAE,OAAO,SAAU;AAC7C,wBAAgB,QAAQ,KAAK,KAAK;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAQA,aAAS,QAAQ,QAAQ,KAAK;AAC5B,UAAI,QAAQ,iBAAiB,OAAO,OAAO,GAAG,MAAM,YAAY;AAC9D;AAAA,MACF;AAEA,UAAI,OAAO,aAAa;AACtB;AAAA,MACF;AAEA,aAAO,OAAO,GAAG;AAAA,IACnB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,SAAS;AA0Bb,aAAS,cAAc,OAAO;AAC5B,aAAO,WAAW,OAAO,OAAO,KAAK,CAAC;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,mBAAmB;AAAvB,QACI,cAAc;AADlB,QAEI,kBAAkB;AAFtB,QAGI,YAAY;AAHhB,QAII,kBAAkB;AAJtB,QAKI,cAAc;AALlB,QAMI,UAAU;AANd,QAOI,oBAAoB;AAPxB,QAQI,WAAW;AARf,QASI,aAAa;AATjB,QAUI,WAAW;AAVf,QAWI,gBAAgB;AAXpB,QAYI,eAAe;AAZnB,QAaI,UAAU;AAbd,QAcI,gBAAgB;AAiBpB,aAAS,cAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY,OAAO;AAClF,UAAI,WAAW,QAAQ,QAAQ,GAAG,GAC9B,WAAW,QAAQ,QAAQ,GAAG,GAC9B,UAAU,MAAM,IAAI,QAAQ;AAEhC,UAAI,SAAS;AACX,yBAAiB,QAAQ,KAAK,OAAO;AACrC;AAAA,MACF;AACA,UAAI,WAAW,aACX,WAAW,UAAU,UAAW,MAAM,IAAK,QAAQ,QAAQ,KAAK,IAChE;AAEJ,UAAI,WAAW,aAAa;AAE5B,UAAI,UAAU;AACZ,YAAI,QAAQ,QAAQ,QAAQ,GACxB,SAAS,CAAC,SAAS,SAAS,QAAQ,GACpC,UAAU,CAAC,SAAS,CAAC,UAAU,aAAa,QAAQ;AAExD,mBAAW;AACX,YAAI,SAAS,UAAU,SAAS;AAC9B,cAAI,QAAQ,QAAQ,GAAG;AACrB,uBAAW;AAAA,UACb,WACS,kBAAkB,QAAQ,GAAG;AACpC,uBAAW,UAAU,QAAQ;AAAA,UAC/B,WACS,QAAQ;AACf,uBAAW;AACX,uBAAW,YAAY,UAAU,IAAI;AAAA,UACvC,WACS,SAAS;AAChB,uBAAW;AACX,uBAAW,gBAAgB,UAAU,IAAI;AAAA,UAC3C,OACK;AACH,uBAAW,CAAC;AAAA,UACd;AAAA,QACF,WACS,cAAc,QAAQ,KAAK,YAAY,QAAQ,GAAG;AACzD,qBAAW;AACX,cAAI,YAAY,QAAQ,GAAG;AACzB,uBAAW,cAAc,QAAQ;AAAA,UACnC,WACS,CAAC,SAAS,QAAQ,KAAK,WAAW,QAAQ,GAAG;AACpD,uBAAW,gBAAgB,QAAQ;AAAA,UACrC;AAAA,QACF,OACK;AACH,qBAAW;AAAA,QACb;AAAA,MACF;AACA,UAAI,UAAU;AAEZ,cAAM,IAAI,UAAU,QAAQ;AAC5B,kBAAU,UAAU,UAAU,UAAU,YAAY,KAAK;AACzD,cAAM,QAAQ,EAAE,QAAQ;AAAA,MAC1B;AACA,uBAAiB,QAAQ,KAAK,QAAQ;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7FjB;AAAA;AAAA,QAAI,QAAQ;AAAZ,QACI,mBAAmB;AADvB,QAEI,UAAU;AAFd,QAGI,gBAAgB;AAHpB,QAII,WAAW;AAJf,QAKI,SAAS;AALb,QAMI,UAAU;AAad,aAAS,UAAU,QAAQ,QAAQ,UAAU,YAAY,OAAO;AAC9D,UAAI,WAAW,QAAQ;AACrB;AAAA,MACF;AACA,cAAQ,QAAQ,SAAS,UAAU,KAAK;AACtC,kBAAU,QAAQ,IAAI;AACtB,YAAI,SAAS,QAAQ,GAAG;AACtB,wBAAc,QAAQ,QAAQ,KAAK,UAAU,WAAW,YAAY,KAAK;AAAA,QAC3E,OACK;AACH,cAAI,WAAW,aACX,WAAW,QAAQ,QAAQ,GAAG,GAAG,UAAW,MAAM,IAAK,QAAQ,QAAQ,KAAK,IAC5E;AAEJ,cAAI,aAAa,QAAW;AAC1B,uBAAW;AAAA,UACb;AACA,2BAAiB,QAAQ,KAAK,QAAQ;AAAA,QACxC;AAAA,MACF,GAAG,MAAM;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzCjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,iBAAiB;AASrB,aAAS,eAAe,UAAU;AAChC,aAAO,SAAS,SAAS,QAAQ,SAAS;AACxC,YAAI,QAAQ,IACR,SAAS,QAAQ,QACjB,aAAa,SAAS,IAAI,QAAQ,SAAS,CAAC,IAAI,QAChD,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAEtC,qBAAc,SAAS,SAAS,KAAK,OAAO,cAAc,cACrD,UAAU,cACX;AAEJ,YAAI,SAAS,eAAe,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG;AAC1D,uBAAa,SAAS,IAAI,SAAY;AACtC,mBAAS;AAAA,QACX;AACA,iBAAS,OAAO,MAAM;AACtB,eAAO,EAAE,QAAQ,QAAQ;AACvB,cAAI,SAAS,QAAQ,KAAK;AAC1B,cAAI,QAAQ;AACV,qBAAS,QAAQ,QAAQ,OAAO,UAAU;AAAA,UAC5C;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}