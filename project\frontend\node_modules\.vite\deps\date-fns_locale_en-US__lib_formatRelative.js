import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/date-fns/locale/en-US/_lib/formatRelative/index.js
var require_formatRelative = __commonJS({
  "node_modules/date-fns/locale/en-US/_lib/formatRelative/index.js"(exports, module) {
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var formatRelativeLocale = {
      lastWeek: "'last' eeee 'at' p",
      yesterday: "'yesterday at' p",
      today: "'today at' p",
      tomorrow: "'tomorrow at' p",
      nextWeek: "eeee 'at' p",
      other: "P"
    };
    var formatRelative = function formatRelative2(token, _date, _baseDate, _options) {
      return formatRelativeLocale[token];
    };
    var _default = formatRelative;
    exports.default = _default;
    module.exports = exports.default;
  }
});
export default require_formatRelative();
//# sourceMappingURL=date-fns_locale_en-US__lib_formatRelative.js.map
