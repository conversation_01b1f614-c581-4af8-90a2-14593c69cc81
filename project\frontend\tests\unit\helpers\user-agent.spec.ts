import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { isPocketDevice, userAgentLog } from '@helpers/helpers/user-agent';

import TEST_CASES from './user-agent.mock.json';

// Helper function to clear the log object before each test
const clearUserAgentLog = () => {
  for (const key in userAgentLog) {
    delete userAgentLog[key];
  }
};

describe('isPocketDevice', () => {
  // Store original objects to restore them after tests
  const originalNavigator = global.navigator;
  const originalWindow = global.window;
  const originalMatchMedia = global.matchMedia;

  beforeEach(() => {
    // Reset mocks and logs before each test
    vi.clearAllMocks();
    clearUserAgentLog();
  });

  afterEach(() => {
    // Clean up all mocks and restore original implementations after each test
    vi.unstubAllGlobals();
    global.navigator = originalNavigator;
    global.window = originalWindow;
    global.matchMedia = originalMatchMedia;
  });

  // Dynamically create a test for each case in the JSON file
  TEST_CASES.forEach((testCase, index) => {
    it(`should return ${testCase.expected} for case #${index + 1}: ${testCase.description}`, () => {
      // Mock navigator properties
      // We define a base object and spread the test case properties onto it
      // Ensure userAgent is properly set from either navigator.userAgent or window.userAgent
      const userAgentString = testCase.navigator?.userAgent || testCase.window?.userAgent || '';
      const mockNavigator = {
        userAgent: userAgentString,
        ...testCase.navigator,
      };
      vi.stubGlobal('navigator', mockNavigator);

      // Prepare and mock window properties
      const mockWindow = { matchMedia: vi.fn(), ...testCase.window };

      // If a matchMedia test case exists, replace the config object with a mock function
      if (testCase.window?.matchMedia) {
        const matchMediaConfig = testCase.window.matchMedia;
        mockWindow.matchMedia = vi.fn(query => {
          let matches = false;
          if (query === '(pointer:coarse)') {
            matches = matchMediaConfig.coarse;
          } else if (query === '(pointer:fine)') {
            matches = matchMediaConfig.fine;
          }
          return { matches, media: query };
        });
      }

      // Now stub the global window with the correctly prepared object
      vi.stubGlobal('window', mockWindow);

      // --- Execution Phase ---
      const result = isPocketDevice();

      // --- Assertion Phase ---
      expect(userAgentLog).toEqual(testCase.expectedLog);
      expect(result).toBe(testCase.expected);
    });
  });
});
