import {
  addMonths
} from "./chunk-7Q5JNRGT.js";
import {
  toInteger
} from "./chunk-SQMTRHET.js";
import {
  requiredArgs
} from "./chunk-Z43A42SM.js";

// node_modules/date-fns/esm/addYears/index.js
function addYears(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var amount = toInteger(dirtyAmount);
  return addMonths(dirtyDate, amount * 12);
}

export {
  addYears
};
//# sourceMappingURL=chunk-WKVP7Z2L.js.map
