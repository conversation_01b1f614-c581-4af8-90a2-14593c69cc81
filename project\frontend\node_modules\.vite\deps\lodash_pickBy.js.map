{"version": 3, "sources": ["../../lodash/pickBy.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap'),\n    baseIteratee = require('./_baseIteratee'),\n    basePickBy = require('./_basePickBy'),\n    getAllKeysIn = require('./_getAllKeysIn');\n\n/**\n * Creates an object composed of the `object` properties `predicate` returns\n * truthy for. The predicate is invoked with two arguments: (value, key).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The source object.\n * @param {Function} [predicate=_.identity] The function invoked per property.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pickBy(object, _.isNumber);\n * // => { 'a': 1, 'c': 3 }\n */\nfunction pickBy(object, predicate) {\n  if (object == null) {\n    return {};\n  }\n  var props = arrayMap(getAllKeysIn(object), function(prop) {\n    return [prop];\n  });\n  predicate = baseIteratee(predicate);\n  return basePickBy(object, props, function(value, path) {\n    return predicate(value, path[0]);\n  });\n}\n\nmodule.exports = pickBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,eAAe;AADnB,QAEI,aAAa;AAFjB,QAGI,eAAe;AAoBnB,aAAS,OAAO,QAAQ,WAAW;AACjC,UAAI,UAAU,MAAM;AAClB,eAAO,CAAC;AAAA,MACV;AACA,UAAI,QAAQ,SAAS,aAAa,MAAM,GAAG,SAAS,MAAM;AACxD,eAAO,CAAC,IAAI;AAAA,MACd,CAAC;AACD,kBAAY,aAAa,SAAS;AAClC,aAAO,WAAW,QAAQ,OAAO,SAAS,OAAO,MAAM;AACrD,eAAO,UAAU,OAAO,KAAK,CAAC,CAAC;AAAA,MACjC,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}