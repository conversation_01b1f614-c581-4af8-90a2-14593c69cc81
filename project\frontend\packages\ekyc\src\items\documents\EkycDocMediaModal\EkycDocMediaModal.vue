<script setup lang="ts">
import {
  promiseTimeout,
  templateRef,
  useSessionStorage,
  useVModels,
  useWindowSize,
} from '@vueuse/core';
import pick from 'lodash/pick';
import { useI18n } from 'vue-i18n-composable';

import ImageSelector from '@helpers/components/ImageSelector.vue';

import DebugCameraOverlay from '@ekyc/components/DebugCameraOverlay.vue';
import MovingPixel from '@ekyc/components/MovingPixel.vue';
import NfcModal from '@ekyc/components/NfcModal/NfcModal.vue';
import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useCamera } from '@ekyc/composables/use-camera';
import { DocumentProps, useCommonEkycItem } from '@ekyc/composables/use-common-ekyc-item';
import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { getAveragePixelColor3x3, getFrameBlob } from '@ekyc/helpers/image-utils';
import { makeid } from '@ekyc/helpers/utils';
import EkycBase from '@ekyc/items/base/EkycBase.vue';

/* Emits */
const emit = defineEmits<{
  (e: 'ready'): void;
  (e: 'camera-ready', val: any): void;
  (e: 'camera-check', val: any): void;
  (e: 'finish', val: object): void;
  (e: 'captured', val: any): void;
  (e: 'uploaded', val: Types.EkycDocumentApiResult): void;
  (e: 'fail', val: Types.EkycRecordFailPayload): void;
  (e: 'error', val: Types.EkycRecordErrorPayload): void;
  (e: 'close', val: Types.EkycRecordClosePayload): void;
  (e: 'loading', val: boolean): void;
  (e: 'set-preview', payload: Partial<Types.PreviewType>): void;
}>();

/* Props */
const props = defineProps({
  ...DocumentProps,
  media: {
    type: String as () => Types.EkycMediaTypes,
    required: true,
  },
  itemType: {
    type: String as () => Types.EkycDocumentItemTypes,
    required: true,
  },
  acceptedDocumentTypes: {
    type: Array as () => Types.EkycDocumentItemTypes[],
    required: true,
  },
});

/* Refs */

const { media, itemType, selectedCountry, acceptedDocumentTypes } = useVModels(props);

/* Variables */
const { t } = useI18n();
const workingConstraints = useSessionStorage('uppass-media-picture-constraints', {});
const { clickDebug, clickDebugCounter, debugMode, debugOverlay } = useDebug();
const { allSettings } = useEkycSettings();
const { width: screenWidth, height: screenHeight } = useWindowSize();

const ekyc = useCommonEkycItem({
  media,
  itemType,
  selectedCountry,
  acceptedDocumentTypes,
});

const {
  isDisplayMediaRecorder,
  uploadedCallbacks,
  onRecordFinished,
  stopChildPreview,
  upload,
  setMediaData,
  emitUploaded,
  emitError,
} = ekyc;

const videoRef = templateRef('videoRef');
const {
  deviceCapabilities,
  deviceSettings,
  isCameraReady,
  streamLoaded,
  applyConstraints,
  loadCamera,
  stopPreview,
  initOnResizeWatch,
  rotateCamera,
  zoomCamera,
  flipCamera,
} = useCamera({
  videoRef,
  highQuality: false,
  startFacingMode: 'environment',
  overrideConstraints: computed(() => props.setting.additional_constraints),
  workingConstraints,
});

const cardFrameRef = templateRef('cardFrameRef');
const cardFrameRect = reactive({
  camera: {
    x: null,
    y: null,
    width: null,
    height: null,
    video_width: null,
    video_height: null,
  },
  frame: {
    x: null,
    y: null,
    width: null,
    height: null,
  },
  card: {
    x: null,
    y: null,
    width: null,
    height: null,
  },
});

const isShowingConfirm = ref<boolean>(false);

const verifyingStage = ref(0);
const preCheckError = ref([]);

const previewUrl = ref<string>('');
const recordedData = ref<Types.RecordedData>(null);
const requestCameraLog = ref<Record<string, any>>({});
const currentState = ref<'preview' | 'selector' | 'camera'>('preview');

const nfcRef = templateRef('nfcRef');
const isInterfaceReady = ref(false);
const pageState = ref<'none' | 'app_popup' | 'permission_popup' | 'camera' | 'nfc'>('none');

let nonce = '';

/* Computed */
const lastImageToShow = computed<string>(() => previewUrl.value || recordedData.value?.url);

// Compute dynamic frame size based on screen dimensions
const holeStyle = computed(() => {
  const X_SCALE = allSettings.document?.frame_size?.mask_scale_x;
  const Y_SCALE = allSettings.document?.frame_size?.mask_scale_y;
  const X_MAX = allSettings.document?.frame_size?.max_mask_x;

  let width = screenHeight.value * X_SCALE;
  let height = screenHeight.value * Y_SCALE;
  const hwRatio = height / width;

  if (X_MAX > 0) {
    width = Math.min(width, screenWidth.value * X_MAX);
    height = width * hwRatio;
  }

  return {
    width: `${width}px`,
    height: `${height}px`,
  };
});

/* Functions */
const finish = async (payload: Types.EkycRecordFinishedPayload) => {
  await onRecordFinished(payload);
};

const getFrameBlobWithCurrentSettings = async () => {
  return getFrameBlob(videoRef.value, { maxWidth: 1080, minWidth: 1080 });
};

const refreshCardFrameRect = (canvas: HTMLCanvasElement) => {
  const frameRect = cardFrameRef.value?.getBoundingClientRect();
  if (frameRect) {
    cardFrameRect.frame = pick(frameRect, ['x', 'y', 'width', 'height']);
  }

  const videoRect = videoRef.value?.getBoundingClientRect();
  if (videoRect) {
    cardFrameRect.camera = {
      ...pick(videoRect, ['x', 'y', 'width', 'height']),
      video_width: videoRef.value.videoWidth,
      video_height: videoRef.value.videoHeight,
    };
  }

  if (!videoRect || !frameRect) {
    console.log('Rect not found, skip refreshCardFrameRect');
    return;
  }

  const scaleX = canvas.width / videoRect.width;
  const scaleY = canvas.height / videoRect.height;

  const cardRect = {
    x: (frameRect.x - videoRect.x) * scaleX,
    y: (frameRect.y - videoRect.y) * scaleY,
    width: frameRect.width * scaleX,
    height: frameRect.height * scaleY,
  };
  cardFrameRect.card = cardRect;
  console.log('Updated cardFrameRect:', JSON.parse(JSON.stringify(cardFrameRect)));
};

const processFrame = async () => {
  recordedData.value = await getFrameBlobWithCurrentSettings();
  refreshCardFrameRect(recordedData.value.canvas);
};

const takePicture = async () => {
  // If nonce already set because it's uploading, skip
  if (nonce) {
    return;
  }

  const currentType = itemType.value;

  verifyingStage.value = 1;
  preCheckError.value = [];
  await processFrame();

  // Loading
  setPreview({ url: lastImageToShow.value, isLoading: true, itemType: currentType });

  // Upload
  setMediaData([
    {
      ...recordedData.value,
      filename: 'card.jpg',
    },
  ]);

  emit('captured', media.value);

  goToPreviewState();

  // Set nonce and lock takePicture for uploading
  nonce = makeid(5);

  await upload({
    action: 'complete',
    nonce,
    logs: [
      {
        request_camera: requestCameraLog.value,
        camera_rect: cardFrameRect.camera,
        frame_rect: cardFrameRect.frame,
        card_rect: cardFrameRect.card,
      },
    ],
  });

  // Reset nonce
  nonce = '';

  // Done
  const includeNfc = props.setting.include_nfc && itemType.value === 'passport'; // if include nfc make it still loading
  setPreview({ isLoading: includeNfc, itemType: itemType.value });
};

const setPreview = (payload: Partial<Types.PreviewType>) => {
  emit('set-preview', payload);
};

const sendCameraReadyLog = async () => {
  if (!videoRef.value) return;
  const { canvas } = await getFrameBlobWithCurrentSettings();
  requestCameraLog.value.average_color = getAveragePixelColor3x3(canvas);
  emit('camera-ready', requestCameraLog.value);
};

const sendCameraCheckLog = async () => {
  if (!videoRef.value) return;
  const { canvas } = await getFrameBlobWithCurrentSettings();
  emit('camera-check', { average_color: getAveragePixelColor3x3(canvas) });
};

const startSendCameraCheckLogInterval = async () => {
  const DELAY = 500;
  const TIMES = 2_500 / DELAY;
  for (let i = 0; i < TIMES; i += 1) {
    await promiseTimeout(DELAY);
    sendCameraCheckLog();
  }
};

const goToCameraState = () => {
  currentState.value = 'camera';
};

const goToPreviewState = () => {
  currentState.value = 'preview';
};

const deselectDocumentType = async () => {
  currentState.value = 'selector';
};

const uploadedHandler = (res: Types.EkycDocumentApiResult) => {
  setPreview({ isLoading: false, itemType: itemType.value });
  emitUploaded(res);
};

/* onMounted */
onMounted(() => {
  emit('ready');
});

// Init by parent
const proceedNfcCheck = ({
  itemType,
  specificPayload,
  immediate,
}: {
  itemType: Types.EkycDocumentItemTypes;
  specificPayload?: Types.EkycDocumentApiResult;
  immediate?: boolean;
}) => {
  if (!props.setting.include_nfc || itemType !== 'passport') {
    console.log('checkNFC:', 'NFC not included');
    pageState.value = 'camera';
    uploadedCallbacks.length = 0;
    uploadedCallbacks.push(emitUploaded);
    return;
  }

  // NFC
  // Show app_popup by default, will be changed by callNfcReceiver
  pageState.value = 'app_popup';
  // NFC interface
  if (isInterfaceReady.value) {
    console.log('checkNFC:', 'init interface already');
  } else {
    const proceedNfcRequest = (payload: Types.EkycDocumentApiResult) => {
      setPreview({ isLoading: true, itemType });
      nfcRef.value?.sendInterfaceRequestNfc(payload);
    };
    if (props.setting?.auto_detect_document_type && immediate) {
      console.log('checkNFC:', 'init interface (immediate call)');
      proceedNfcRequest(specificPayload);
    } else {
      console.log('checkNFC:', 'init interface');

      uploadedCallbacks.length = 0;
      uploadedCallbacks.push(proceedNfcRequest);
    }

    isInterfaceReady.value = true;
  }

  nfcRef.value?.sendInterfaceRequestPermission();
};

const startInit = async () => {
  if (isDisplayMediaRecorder.value) {
    console.warn('startInit:', 'Already initialized');
    return;
  }

  isDisplayMediaRecorder.value = true;

  initOnResizeWatch();

  if (currentState.value === 'preview') {
    deselectDocumentType();
  }

  const { success, logs } = await loadCamera({
    reloadStream: media.value === 'document',
  });
  requestCameraLog.value = logs;
  if (success) {
    await promiseTimeout(100);
    sendCameraReadyLog();
    startSendCameraCheckLogInterval();
  } else {
    const errType =
      logs.error === 'error_open_camera_timeout'
        ? 'error_open_camera_timeout'
        : 'error_open_camera';
    const errText = `ekyc.recorder.${errType}`;
    finish({
      action: 'error',
      code: errType,
      status: errType,
      error: errText,
      error_type: errType,
      message: errType,
      logs: [{ request_camera: requestCameraLog.value }],
      from: 'init',
    });
    return;
  }

  // Init watcher later, to prevent NFC logic on max attempt
  watch(
    () => [media.value, itemType.value],
    async ([mediaVal, typeVal]) => {
      proceedNfcCheck({ itemType: typeVal as Types.EkycDocumentItemTypes });
    },
    { deep: true, immediate: true },
  );

  // If pageState changed to none by app/permission popup, reset document type
  watch(pageState, val => {
    if (val === 'none') {
      deselectDocumentType();
    }
  });
};

defineExpose({
  ...ekyc,
  selectedCountry,
  isDisplayMediaRecorder,
  currentState,
  cardFrameRef,
  debugMode,
  streamLoaded,
  proceedNfcCheck,

  startInit,
  deselectDocumentType,
  goToCameraState,

  stopChildPreview,
  stopPreview,

  rotateCamera,
  zoomCamera,
  flipCamera,
});
</script>

<template>
  <div>
    <EkycBase media="document">
      <template #modal v-if="isDisplayMediaRecorder">
        <div :class="`ekyc__modal-wrapper-${itemType}`" @click="clickDebug">
          <div class="modal is-active modal-full-screen mask-modal">
            <div class="modal-background" />
            <div
              class="modal-content modal-card camera-view mode-id-card vertical-experience-enabled"
            >
              <section class="modal-card-body">
                <div class="canvas-wrapper h-full" :class="{ 'image-preview': isShowingConfirm }">
                  <!-- Camera Background -->
                  <div class="flex flex-col justify-around h-full">
                    <div class="camera-view-wrapper">
                      <div class="camera-view-mask">
                        <video
                          id="camera-doc-media-modal"
                          ref="videoRef"
                          muted
                          playsinline
                          preload="auto"
                          poster="https://cdn.uppass.io/images/transparent.png"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Document & Country Selector template -->
                  <div v-if="currentState === 'selector'" class="selector-wrapper">
                    <slot name="selector"> </slot>
                  </div>

                  <!-- Camera template -->
                  <div v-else-if="currentState === 'camera'" class="flex flex-col full-screen-top">
                    <!-- Text & Actions content -->
                    <div class="flex flex-col items-center justify-center h-full pb-[150px] pt-6">
                      <!-- Card preview example -->
                      <div class="card-image-preview">
                        <ImageSelector
                          v-if="
                            itemType === 'passport' ||
                            itemType === 'ci_passport' ||
                            itemType === 'travel_document' ||
                            itemType === 'border_pass'
                          "
                          id="passport-preview"
                          name="passport-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'thai_alien_card'"
                          id="thai-alien-card-preview"
                          name="thai-alien-card-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'work_permit_card'"
                          id="work-permit-card-preview"
                          name="work-permit-card-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'work_permit_book'"
                          id="work-permit-book-preview"
                          name="work-permit-book-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'white_card'"
                          id="white-card-preview"
                          name="white-card-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'monk_card'"
                          id="monk-card-preview"
                          name="monk-card-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'immigration_card'"
                          id="immigration-card-preview"
                          name="immigration-card-preview"
                        />
                        <ImageSelector
                          v-else-if="itemType === 'other_document'"
                          id="other-document-preview"
                          name="other-document-preview"
                        />

                        <ImageSelector
                          v-else-if="media === 'document'"
                          id="front-card-preview"
                          name="front-card-preview"
                        />

                        <ImageSelector
                          v-else-if="media === 'backcard'"
                          id="back-card-preview"
                          name="back-card-preview"
                        />
                      </div>

                      <!-- Header -->
                      <div class="header-content-wrapper">
                        {{ t(`ekyc.${itemType || 'document'}.header`) }}
                      </div>

                      <!-- Overlay -->
                      <div class="id-card-overlay">
                        <div ref="cardFrameRef" class="hole" :style="holeStyle"></div>
                      </div>

                      <!-- Country & Document selector -->
                      <div class="selector">
                        <slot name="selector-trigger"></slot>
                      </div>
                    </div>

                    <!-- Capture button -->
                    <div class="camera-capture-wrapper">
                      <div id="capture-button" class="w-[80px] h-[80px] content-center">
                        <transition name="fade-slide" mode="out-in">
                          <a
                            v-if="isCameraReady"
                            id="btn-start"
                            @click="takePicture()"
                            key="button"
                          >
                            <ImageSelector id="capture-button-img" name="capture-button" />
                          </a>
                          <div v-else style="color: white" key="loader">
                            <div class="loading-spinner">
                              <div class="loader" />
                            </div>
                          </div>
                        </transition>
                      </div>
                    </div>
                  </div>

                  <div v-if="clickDebugCounter >= 7">
                    {{ 10 - clickDebugCounter }}
                  </div>
                </div>
              </section>
            </div>
          </div>

          <MovingPixel />

          <DebugCameraOverlay
            v-if="debugMode"
            ref="debugOverlay"
            :capabilities="deviceCapabilities"
            :settings="deviceSettings"
            :recorded-data="recordedData"
            :card-rect="cardFrameRect"
            @apply="applyConstraints"
          >
            <template #top>
              <button class="bg-blue-300" @click="processFrame">processFrame</button>
            </template>
          </DebugCameraOverlay>
        </div>
      </template>
      <template #loader><div></div></template>
    </EkycBase>

    <NfcModal
      ref="nfcRef"
      :page-state.sync="pageState"
      @uploaded="uploadedHandler"
      @error="emitError"
    />
  </div>
</template>

<style lang="scss" scoped>
.camera-view-wrapper {
  @apply top-0 left-0 w-full h-full;
  position: absolute !important;
  z-index: 1 !important;
  background-color: black;
}

.camera-view-mask {
  @apply h-full;
  border-radius: unset !important;
  padding: unset !important;
}

video#camera-doc-media-modal {
  @apply rounded-none;
  @apply max-w-[unset] max-h-[unset];
  @apply object-contain; // No clipping
  @apply left-0 right-0;
  @apply bg-black;
}

.camera-capture-wrapper {
  @apply flex justify-center w-full absolute bottom-[46px];
  z-index: 10 !important;
}

.preview-image-wrapper {
  border-radius: 8px;
  height: 260px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.preview-image {
  height: inherit;
  width: inherit;
  object-fit: cover;
}

.modal-card-body {
  padding: 0 !important;
}

.canvas-wrapper {
  height: 100%;
  width: 100%;
  background: black;
  display: flex;
  align-items: center;
  position: absolute !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  justify-content: center;
  align-content: center;
  padding: 0 !important;
  max-width: unset !important;
}

.selector-wrapper {
  @apply w-full h-full z-10 top-0 left-0 absolute text-white p-6 pt-[100px] overflow-hidden;
  background-color: rgba(42, 48, 73, 0.8);
  /* 
    This css class to solve invalid layer composition when camera open in safari
    set will-change and transform to make it fix and not re-arrange layer composition
  */
  will-change: transform;
  transform: translateZ(0);

  :deep(.header-title) {
    @apply text-[20px] font-medium leading-[32px];
  }

  :deep(#ok-dy__FormItem-ekyc_document_type) {
    @apply max-h-[60vh] overflow-auto;

    .choice-block {
      padding: 0 16px !important;
    }

    .image-selector {
      height: 48px !important;
      width: 48px !important;
    }

    .radio-item {
      height: 64px !important;
    }

    .radio-label > span > div > span:has(.image-selector) {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      padding-right: 16px !important;
    }

    .radio-label > span > div > span:not(:has(.image-selector)) {
      font-size: 14px;
      line-height: 24px;
      color: #353535;
      font-weight: 400;
    }

    .radio-label::before {
      width: 20px;
      height: 20px;
      display: none;
    }
  }
}

:deep(.button-text) {
  text-align: left !important;
}
:deep(.ok-dy__input_control .content-label) {
  @apply text-[13px] font-medium leading-6;
  color: white;
}

.full-screen-top {
  @apply h-full absolute top-0 z-10;
}

.card-image-preview {
  max-height: 120px !important;
  height: 100% !important;
  margin-bottom: 30px;
  & > figure {
    margin: unset !important;
    max-height: 120px;
    height: 100%;
  }
}

.header-content-wrapper {
  @apply font-extrabold text-2xl z-10 text-white leading-9 mb-[48px] mx-6 whitespace-pre-wrap;
}

.id-card-overlay {
  @apply w-full;
}

.hole {
  margin: auto;
  border-radius: 8px;
  background: transparent;
  justify-self: center;
  box-shadow: 0 0 0 9999px rgba(42, 48, 73, 0.8);
}

.selector {
  @apply px-6 mt-12;
}

@media (max-height: 786px) {
  .header-content-wrapper {
    @apply mb-6;
  }

  .selector {
    margin-top: 24px;
  }

  .card-image-preview {
    margin-bottom: 24px;
    max-height: 80px !important;
    & > figure {
      max-height: 80px;
    }
  }

  .camera-capture-wrapper {
    @apply bottom-[24px];
  }
}

// Handle small screen devices (iphone se1, iphone 5 etc.)
@media (max-height: 630px) {
  // Selector section
  :deep(.select:not(.is-multiple):not(.is-loading):after) {
    margin-top: -0.6em !important;
  }

  :deep(#ekycDocumentCountryField) {
    height: 40px !important;
    .button-text {
      font-size: 16px !important;
    }
  }

  :deep(.header-title) {
    font-size: 20px !important;
  }

  .selector-wrapper {
    padding-top: 10% !important;
    overflow-y: auto;

    :deep(.header-title) {
      @apply text-[20px] font-medium leading-[32px];
    }
  }

  :deep(#ok-dy__FormItem-ekyc_document_type) {
    .image-selector {
      height: 40px !important;
      width: 40px !important;
    }
    .radio-label > span > div > span:has(.image-selector) {
      padding-top: 4px !important;
      padding-bottom: 4px !important;
    }
    .radio-label::before {
      width: 20px;
      height: 20px;
    }
  }

  // Camera section
  .header-content-wrapper {
    font-size: 18px !important;
    margin-bottom: 0.5rem !important;
  }

  :deep(.selector-trigger) {
    height: 40px !important;
    .icon {
      height: 20px;
      width: 20px;
    }
  }

  :deep(.card-image-preview) {
    max-width: 60px !important;
    margin-bottom: 0.5rem !important;
    margin-top: 24px !important;
  }

  :deep(#capture-button) {
    height: 64px !important;
    width: 64px !important;
    .loader {
      height: 64px !important;
      width: 64px !important;
    }
  }

  .camera-capture-wrapper {
    bottom: 12px !important;
  }
}

.loading-spinner .loader {
  width: 80px !important;
  height: 80px !important;
  border-color: rgba(#fff, 0.2) rgba(#fff, 0.2) rgba(#fff, 0.2) #fff !important;
  border-width: 14px;
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}
.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>
