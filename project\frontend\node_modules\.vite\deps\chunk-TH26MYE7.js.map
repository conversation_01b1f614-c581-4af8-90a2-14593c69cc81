{"version": 3, "sources": ["../../lodash/_baseGt.js"], "sourcesContent": ["/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\n\nmodule.exports = baseGt;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,OAAO,OAAO;AAC5B,aAAO,QAAQ;AAAA,IACjB;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}