{"version": 3, "sources": ["../../lodash/maxBy.js"], "sourcesContent": ["var baseExtremum = require('./_baseExtremum'),\n    baseGt = require('./_baseGt'),\n    baseIteratee = require('./_baseIteratee');\n\n/**\n * This method is like `_.max` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.maxBy(objects, function(o) { return o.n; });\n * // => { 'n': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.maxBy(objects, 'n');\n * // => { 'n': 2 }\n */\nfunction maxBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseGt)\n    : undefined;\n}\n\nmodule.exports = maxBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,SAAS;AADb,QAEI,eAAe;AAyBnB,aAAS,MAAM,OAAO,UAAU;AAC9B,aAAQ,SAAS,MAAM,SACnB,aAAa,OAAO,aAAa,UAAU,CAAC,GAAG,MAAM,IACrD;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}