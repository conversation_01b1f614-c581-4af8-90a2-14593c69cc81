import {
  require_baseSum
} from "./chunk-3S6JOO2D.js";
import {
  require_identity
} from "./chunk-64Z5HK43.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseMean.js
var require_baseMean = __commonJS({
  "node_modules/lodash/_baseMean.js"(exports, module) {
    var baseSum = require_baseSum();
    var NAN = 0 / 0;
    function baseMean(array, iteratee) {
      var length = array == null ? 0 : array.length;
      return length ? baseSum(array, iteratee) / length : NAN;
    }
    module.exports = baseMean;
  }
});

// node_modules/lodash/mean.js
var require_mean = __commonJS({
  "node_modules/lodash/mean.js"(exports, module) {
    var baseMean = require_baseMean();
    var identity = require_identity();
    function mean(array) {
      return baseMean(array, identity);
    }
    module.exports = mean;
  }
});
export default require_mean();
//# sourceMappingURL=lodash_mean.js.map
