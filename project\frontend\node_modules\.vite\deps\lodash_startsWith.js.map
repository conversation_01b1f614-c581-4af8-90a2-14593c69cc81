{"version": 3, "sources": ["../../lodash/_baseClamp.js", "../../lodash/startsWith.js"], "sourcesContent": ["/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n  return number;\n}\n\nmodule.exports = baseClamp;\n", "var baseClamp = require('./_baseClamp'),\n    baseToString = require('./_baseToString'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/**\n * Checks if `string` starts with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=0] The position to search from.\n * @returns {boolean} Returns `true` if `string` starts with `target`,\n *  else `false`.\n * @example\n *\n * _.startsWith('abc', 'a');\n * // => true\n *\n * _.startsWith('abc', 'b');\n * // => false\n *\n * _.startsWith('abc', 'b', 1);\n * // => true\n */\nfunction startsWith(string, target, position) {\n  string = toString(string);\n  position = position == null\n    ? 0\n    : baseClamp(toInteger(position), 0, string.length);\n\n  target = baseToString(target);\n  return string.slice(position, position + target.length) == target;\n}\n\nmodule.exports = startsWith;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AASA,aAAS,UAAU,QAAQ,OAAO,OAAO;AACvC,UAAI,WAAW,QAAQ;AACrB,YAAI,UAAU,QAAW;AACvB,mBAAS,UAAU,QAAQ,SAAS;AAAA,QACtC;AACA,YAAI,UAAU,QAAW;AACvB,mBAAS,UAAU,QAAQ,SAAS;AAAA,QACtC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,eAAe;AADnB,QAEI,YAAY;AAFhB,QAGI,WAAW;AAyBf,aAAS,WAAW,QAAQ,QAAQ,UAAU;AAC5C,eAAS,SAAS,MAAM;AACxB,iBAAW,YAAY,OACnB,IACA,UAAU,UAAU,QAAQ,GAAG,GAAG,OAAO,MAAM;AAEnD,eAAS,aAAa,MAAM;AAC5B,aAAO,OAAO,MAAM,UAAU,WAAW,OAAO,MAAM,KAAK;AAAA,IAC7D;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}