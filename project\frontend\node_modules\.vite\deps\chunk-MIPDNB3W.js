import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/isObject.js
var require_isObject = __commonJS({
  "node_modules/lodash/isObject.js"(exports, module) {
    function isObject(value) {
      var type = typeof value;
      return value != null && (type == "object" || type == "function");
    }
    module.exports = isObject;
  }
});

export {
  require_isObject
};
//# sourceMappingURL=chunk-MIPDNB3W.js.map
