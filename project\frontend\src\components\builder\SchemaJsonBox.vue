<template>
  <div class="schema-json-box mt-2">
    <!-- Checkboxes -->
    <!-- <div v-if="!hideCheckbox" class="flex justify-between mb-2">
      <div class="flex gap-1">
        <button
          v-for="(option, index) in ALL_BOXES"
          :key="index"
          type="button"
          class="button !text-xs"
          :class="configs[option.key] ? 'is-primary' : 'is-outlined'"
          @click="configs[option.key] = !configs[option.key]"
        >
          <ReactiveIcon :icon="option.icon"></ReactiveIcon>
          <span>{{ option.label }}</span>
        </button>
      </div>
      <div class="flex gap-1">
        <div v-if="hasUnappliedChanges" class="flex gap-1 items-center text-sm">
          <span class="has-text-danger">
            * Changes will NOT effect the actual schema unless you apply them.
          </span>
          <button
            type="button"
            class="button is-danger is-outlined !text-sm"
            @click="discardSchema"
          >
            <ReactiveIcon icon="lucide:trash"></ReactiveIcon>
          </button>
          <button type="button" class="button is-success !text-xs" @click="applySchema">
            <ReactiveIcon icon="lucide:check"></ReactiveIcon>
            <span>Apply (Ctrl+S)</span>
          </button>
        </div>
        <button
          v-else
          type="button"
          class="button is-success is-outlined !text-xs"
          :disabled="true"
          @click="applySchema"
        >
          <ReactiveIcon icon="lucide:check-check"></ReactiveIcon>
          <span>Up to date!</span>
        </button>
      </div>
    </div> -->

    <!-- https://www.npmjs.com/package/@guolao/vue-monaco-editor -->
    <div class="schema-editor-wrapper" :style="{ height }">
      <!-- Json -->
      <VueMonacoEditor
        v-if="!configs.diff"
        :value.sync="modifiedCode"
        :options="monacoEditorOptions"
        language="json"
        :theme="configs.dark ? 'vs-dark' : 'vs'"
        width="100%"
        height="100%"
        @mount="onMonacoEditorMounted"
        @change="onMonacoEditorChanged"
      />
      <!-- Diff -->
      <VueMonacoDiffEditor
        v-else
        :original="originalCode"
        :modified.sync="modifiedCode"
        :options="monacoDiffOptions"
        language="json"
        :theme="configs.dark ? 'vs-dark' : 'vs'"
        width="100%"
        height="100%"
        @mount="onMonacoDiffMounted"
        @change="onMonacoEditorChanged"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDark, useVModels, watchDebounced } from '@vueuse/core';

import jsonFormSchema from '@/helpers/json-schemas/form.schema.json';
import jsonPageSchema from '@/helpers/json-schemas/page.schema.json';
import { VueMonacoDiffEditor, VueMonacoEditor, type monacoEditor } from '@/plugins/monaco-editor';

// Props
const props = defineProps({
  schema: {
    type: Object as () => any,
    default: () => {},
  },
  original: {
    type: Object as () => any,
    required: false,
    default: () => null,
  },
  disabled: {
    type: Boolean,
    required: false,
  },
  valid: {
    type: Boolean,
    required: false,
  },
  hasChanges: {
    type: Boolean,
    required: false,
  },
  hideCheckbox: {
    type: Boolean,
    default: false,
  },
  height: {
    type: String,
    required: false,
    default: '70vh',
  },
  editorJsonSchemaType: {
    type: String as () => 'form' | 'page',
    required: false,
    default: 'form',
  },
});

const emit = defineEmits<{
  (e: 'applied'): void;
  (e: 'update:schema', value: object): void;
  (e: 'update:valid', value: boolean): void;
  (e: 'update:hasChanges', value: boolean): void;
}>();

const { schema, valid, hasChanges } = useVModels(props, emit);

// Variable
const ALL_BOXES = [
  {
    label: 'Diff',
    icon: 'vscode-icons:file-type-diff',
    key: 'diff',
  },
  {
    label: 'Dark',
    icon: 'mdi:theme-light-dark',
    key: 'dark',
  },
];

const monacoEditorOptions = computed<monacoEditor.editor.IEditorOptions>(() => ({
  automaticLayout: true,
  formatOnType: true,
  formatOnPaste: true,
  stickyScroll: {
    enabled: true,
    maxLineCount: 5,
  },
  readOnly: props.disabled,
}));

const monacoDiffOptions = computed<monacoEditor.editor.IDiffEditorOptions>(() => ({
  ...monacoEditorOptions.value,
  originalEditable: false,
}));

const isDark = useDark();

const singleRef = shallowRef<monacoEditor.editor.IStandaloneCodeEditor>();
const diffRef = shallowRef<monacoEditor.editor.IStandaloneDiffEditor>();

const configs = reactive({ diff: false, dark: isDark });

const editorRef = computed(() =>
  configs.diff ? diffRef.value?.getModifiedEditor() : singleRef.value,
);

const modifiedCode = ref<string>();
const originalCode = ref<string>();
const lastAppliedVersionId = ref(1);
const currentVersionId = ref(1);

const hasUnappliedChanges = computed(() => currentVersionId.value !== lastAppliedVersionId.value);

function updateLastAppliedVersionId() {
  lastAppliedVersionId.value = editorRef.value?.getModel().getAlternativeVersionId();
}

function updateCurrentVersionId() {
  currentVersionId.value = editorRef.value?.getModel().getAlternativeVersionId();
}

function applySchema() {
  try {
    const finalVal = JSON.parse(modifiedCode.value);
    schema.value = finalVal;
    valid.value = true;
    emit('applied');
    updateLastAppliedVersionId();
    updateCurrentVersionId();
  } catch {
    valid.value = false;
  }
}

function discardSchema() {
  currentVersionId.value = lastAppliedVersionId.value;
}

function formatCode() {
  editorRef.value?.getAction('editor.action.formatDocument').run();
}

function syncFromSchemaProp() {
  const val = props.schema;
  let code = '';
  if (typeof val === 'object') {
    code = JSON.stringify(val, null, 2);
  } else {
    code = (val ?? '').toString();
  }
  if (modifiedCode.value === undefined) {
    modifiedCode.value = code;
  } else {
    editorRef.value?.executeEdits('', [
      {
        range: editorRef.value?.getModel().getFullModelRange(),
        text: code,
        // forceMoveMarkers: true
      },
    ]);
    updateLastAppliedVersionId();
    updateCurrentVersionId();
  }
}

function syncFromOriginalSchemaProp() {
  const val = props.original;
  if (typeof val === 'object') {
    originalCode.value = JSON.stringify(val, null, 2);
  } else {
    originalCode.value = val;
  }
}

function onMonacoMounted(
  editor: monacoEditor.editor.IStandaloneCodeEditor | monacoEditor.editor.IStandaloneDiffEditor,
  monaco: typeof monacoEditor,
) {
  syncFromSchemaProp();
  syncFromOriginalSchemaProp();
  updateLastAppliedVersionId();
  updateCurrentVersionId();
  // eslint-disable-next-line no-bitwise
  editor.addAction({
    label: 'Toggle Diff',
    id: 'toggleDiff',
    contextMenuGroupId: '1_uppass',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.Backslash],
    run: () => {
      configs.diff = !configs.diff;
    },
  });
  editor.addAction({
    label: 'Toggle Dark Mode',
    id: 'toggleDarkMode',
    contextMenuGroupId: '1_uppass',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyM],
    run: () => {
      configs.dark = !configs.dark;
    },
  });
  editor.addAction({
    label: 'Save & Apply schema',
    id: 'applySchema',
    contextMenuGroupId: '1_uppass',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
    run: () => {
      formatCode();
      applySchema();
    },
  });
  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: true,
    schemas: [
      props.editorJsonSchemaType === 'form'
        ? {
            uri: '@/helpers/json-schemas/schema.schema.json',
            fileMatch: ['*'],
            schema: jsonFormSchema,
          }
        : {
            uri: '@/helpers/json-schemas/page.schema.json',
            fileMatch: ['*'],
            schema: jsonPageSchema,
          },
    ],
  });
}

function onMonacoEditorMounted(
  editor: monacoEditor.editor.IStandaloneCodeEditor,
  monaco: typeof monacoEditor,
) {
  singleRef.value = editor;
  onMonacoMounted(editor, monaco);
}

function onMonacoDiffMounted(
  diff: monacoEditor.editor.IStandaloneDiffEditor,
  monaco: typeof monacoEditor,
) {
  diffRef.value = diff;
  onMonacoMounted(diff, monaco);
}

function onMonacoEditorChanged() {
  updateCurrentVersionId();
}

watch(() => props.schema, syncFromSchemaProp);

watch(() => props.original, syncFromOriginalSchemaProp);

watchDebounced(modifiedCode, applySchema, { debounce: 200 });

watch(
  () => currentVersionId.value !== lastAppliedVersionId.value,
  val => {
    hasChanges.value = val;
  },
);

// onMounted
onMounted(() => {});
</script>

<style lang="scss" scoped>
.schema-json-box {
  display: flex;
  flex-direction: column;
}

.schema-box-config-wrapper {
  margin-bottom: 0 !important;
  justify-content: flex-end;

  width: fit-content;
  float: right;
}

.schema-editor-wrapper {
  overflow: auto;
  resize: both;
}
</style>
