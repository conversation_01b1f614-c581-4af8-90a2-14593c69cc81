import axios from 'axios';
import cloneDeep from 'lodash/cloneDeep';
import forEach from 'lodash/forEach';
import get from 'lodash/get';
import merge from 'lodash/merge';
import throttle from 'lodash/throttle';
import unset from 'lodash/unset';

import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';
import { useDynamicFormStore } from '@core/store/modules/dynamic-form-module';

import { useUuidCookie } from '@helpers/composables/use-uuid-cookie';
import { getScreenInfo } from '@helpers/helpers/screen';
import { type UserAgentResult, getUserAgent } from '@helpers/helpers/user-agent';

const { dynamicFormApp, mainInstance } = useDynamicFormApp();
const { getUuid, uuidGetMethod } = useUuidCookie();
const dynamicFormStore = useDynamicFormStore();

const LOG_VERSION = '2023-03-24';

interface Position {
  readonly coords: {
    readonly accuracy: number;
    readonly altitude: number | null;
    readonly altitudeAccuracy: number | null;
    readonly heading: number | null;
    readonly latitude: number;
    readonly longitude: number;
    readonly speed: number | null;
  };
  readonly timestamp: number;
}

interface BasicExtractedEvent {
  form_slug: string;
  applied_form_slug: string;
  current_section: string;
  current_step: string;
  url: string;
  locale: string;
  timestamp: number;
  timezone: string;
  client: UserAgentResult & {
    uuid: string;
    uuid_get_method: string;
    geolocation: {
      latitude: any;
      longitude: any;
      altitude: any;
      accuracy: any;
      altitudeAccuracy: any;
      heading: any;
      speed: any;
    };
    language: string;
  };
  connection: {
    effective_type: string;
    rtt: number;
    download_speed: number;
    save_data: string;
  };
  screen: {
    physical: {
      availWidth: number;
      availHeight: number;
      width: number;
      height: number;
      colorDepth: number;
      pixelDepth: number;
      availLeft: number;
      availTop: number;
      orientation: {
        angle: number;
        type: OrientationType;
      };
    };
    page_width: number;
    page_height: number;
    client_width: number;
    client_height: number;
    scroll_x: number;
    scroll_y: number;
  };
  tab_id: number;
  page_visibility: DocumentVisibilityState;
  page_load_time: number;
  navigation_entries: any[];
  log_version: string;
  [key: string]: any;
}

interface FullExtractedEvent extends BasicExtractedEvent {
  event_type: any;
  event_key: any;
  data: any;
  input_type: string;
  uri: string;
  target: {
    bound_value: string;
    bound_value_length: number;
    name: any;
    type: any;
    tag_name: any;
    class_list: any;
    input_mode: any;
    id: any;
  };
}

const USER_AGENT = getUserAgent();

/**
 * Define tracking events and elements.
 */
const GLOBAL_DOM_EVENT: object = {
  MouseEvent: 'click contextmenu dblclick',
  ClipboardEvent: 'copy cut paste',
  DragEvent: 'dragend dragenter dragleave dragstart drop',
  HashChangeEvent: 'hashchange',
  TouchEvent: 'touchcancel touchend touchenter touchleave touchstart',
  WindowEvent: 'load unload beforeunload pagehide visibilitychange',
};
const DOM_EVENT: object = {
  UIEvent: 'select',
  Event: 'input submit',
  FocusEvent: 'blur focus',
  KeyboardEvent: 'keypress',
};
const TRACKABLE_ELEMENT: string[] = ['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON', 'A', 'FORM'];

// const NO_TRACk_Elements: string[] = [];

function geolocationToJson(geo) {
  const coords = get(geo, 'coords', {});
  return {
    latitude: coords.latitude,
    longitude: coords.latitude,
    altitude: coords.altitude,
    accuracy: coords.accuracy,
    altitudeAccuracy: coords.altitudeAccuracy,
    heading: coords.heading,
    speed: coords.speed,
  };
}

export class EventTrackerClass {
  geolocation: Position;

  uploading = false;

  userEvents = [];

  isGlobalEventInitialized = false;

  isTracking = false;

  url = '';

  tabStartTime = null;

  timezone: string;

  additionalData = {};

  start({ additionalData = {} }) {
    this.additionalData = additionalData;
    this.url = mainInstance.value?.formSchema.tracking.url;
    console.log('[Event tracker] Track to URL', this.url);
    if (!this.url) {
      console.warn('[Event tracker] No event tracking url defined, Canceling');
      return;
    }

    this.isTracking = true;
    this.tabStartTime = Date.now();
    this.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    this.submitCachedTrackingData();

    if (!this.isGlobalEventInitialized) {
      const eventLists = { ...GLOBAL_DOM_EVENT, ...DOM_EVENT };
      this.initializeEventTracker(eventLists, (events: string) => {
        window.addEventListener(events, this.DOM_EVENT_HANDLER.bind(this));
      });
      this.isGlobalEventInitialized = true;
    }

    // Save to localStorage when user leave the page
    window.onbeforeunload = () => {
      localStorage.setItem('userEvents', JSON.stringify(this.userEvents));
    };
  }

  stop() {
    this.submitTrackingData();
    this.isTracking = false;
  }

  public setUrl(newUrl: string) {
    this.url = newUrl;
  }

  public setData(data: any) {
    this.additionalData = data;
  }

  public addData(data: any) {
    merge(this.additionalData, data);
  }

  initializeEventTracker(eventGroup: object, fn: Function) {
    const trackLocation = get(this.additionalData, ['client', 'geolocation'], false);
    if (trackLocation === true) {
      unset(this.additionalData, ['client', 'geolocation']);
      navigator.geolocation.getCurrentPosition(loc => {
        // NOSONAR
        this.geolocation = loc;
      });
    }
    Object.keys(eventGroup).forEach((key: string) => {
      const eventList = eventGroup[key].split(' ');
      eventList.map((event: string[]) => fn(event));
    });

    dynamicFormStore.$onAction(({ name, after }) => {
      if (name === 'setErrors') {
        // this will trigger if the action succeeds and after it has fully run.
        // it waits for any returned promised
        after(() => {
          const sectionErrors = dynamicFormStore.sectionErrors;
          forEach(sectionErrors, (messages, field) => {
            const errorEvent = {
              event_type: 'error',
              data: {
                field,
                messages,
              },
              ...this.getBasicEventPayload(),
            };
            this.userEvents.push(errorEvent);
          });
          this.submitTrackingData();
        });
      }
    });
  }

  async submitCachedTrackingData() {
    const cachedUserEvents: BasicExtractedEvent[] = JSON.parse(localStorage.getItem('userEvents'));
    if (cachedUserEvents) {
      const filteredUserEvents = cachedUserEvents.filter(
        evt => evt.applied_form_slug === mainInstance.value?.appliedFormSlug,
      );
      this.userEvents.push(...filteredUserEvents);
      localStorage.setItem('userEvents', JSON.stringify([]));
      this.submitTrackingData({ cached: true });
    }
  }

  submitTrackingData = throttle(async (additionalPayload = {}) => {
    const eventsToSend = cloneDeep(this.userEvents);
    try {
      if (this.uploading) return false;
      if (this.userEvents.length === 0) return false;
      this.uploading = true;
      this.userEvents = [];
      await axios.post(this.url, { userEvents: eventsToSend, ...additionalPayload });
      return true;
    } catch {
      this.userEvents.push(...eventsToSend);
      return false;
    } finally {
      this.uploading = false;
    }
  }, 5000);

  getBasicEventPayload(): BasicExtractedEvent {
    const navigationEntries = performance.getEntriesByType('navigation');
    const navigator: any = window.navigator;
    const connection =
      navigator.connection || navigator.mozConnection || navigator.webkitConnection;

    const extractedEvent: BasicExtractedEvent = {
      form_slug: mainInstance.value?.formSlug,
      applied_form_slug: mainInstance.value?.appliedFormSlug,
      current_step: mainInstance.value?.currentStepName,
      current_section: mainInstance.value?.currentSectionName,
      url: window.location.href,
      locale: dynamicFormApp.locale,
      timestamp: new Date().getTime(),
      timezone: this.timezone,
      client: {
        ...USER_AGENT,
        uuid: getUuid(),
        uuid_get_method: uuidGetMethod.value,
        geolocation: geolocationToJson(this.geolocation),
        language: navigator.language,
      },
      connection: connection
        ? {
            effective_type: connection.effectiveType,
            rtt: connection.rtt,
            download_speed: connection.downlink,
            save_data: connection.saveData,
          }
        : null,
      screen: {
        physical: getScreenInfo(),
        page_width: document.body.scrollWidth,
        page_height: document.body.scrollHeight,
        client_width: window.innerWidth,
        client_height: window.innerHeight,
        scroll_x: window.scrollX,
        scroll_y: window.scrollY,
      },
      tab_id: this.tabStartTime,
      page_visibility: document.visibilityState,
      page_load_time: navigationEntries?.[0]?.duration,
      navigation_entries: (navigationEntries || []).map(ne => ne.toJSON()),
      log_version: LOG_VERSION,
    };
    merge(extractedEvent, this.additionalData);
    return extractedEvent;
  }

  getElementEventPayload(e): BasicExtractedEvent {
    let boundValue: string | null = null;
    let boundValueLength = 0;
    if (
      e.target.attributes &&
      (e.target.value || e.target.hasAttribute('value') || e.target.hasAttribute('data-value'))
    ) {
      boundValue =
        e.target.value ||
        e.target.getAttribute('value') ||
        e.target.getAttribute('data-value') ||
        null;
      if (typeof boundValue === 'string') {
        boundValueLength = boundValue.length;
        const trimHelper = (str: string, length: number) =>
          str.length > length ? `${str.substring(0, length)}...` : str;
        boundValue = trimHelper(boundValue, 100);
      }
    }

    const extractedEvent: FullExtractedEvent = {
      event_type: e.type,
      event_key: e.key,
      data:
        e.type === 'hashchange'
          ? {
              ...e.data,
              oldURL: e.oldURL,
              newURL: e.newURL,
            }
          : e.data,
      input_type: e.inputType,
      uri: e.target.baseURI,
      target: {
        bound_value: boundValue,
        bound_value_length: boundValueLength,
        name: e.target.name,
        type: e.target.type,
        tag_name: e.target.tagName,
        class_list: e.target.classList ? e.target.classList.value : '',
        input_mode: e.target.inputMode,
        id: e.target.id,
      },
      ...this.getBasicEventPayload(),
    };
    return extractedEvent;
  }

  DOM_EVENT_HANDLER(e: any) {
    // if (!TRACKABLE_ELEMENT.includes(e.target.tagName)) {
    //   return;
    // }
    // Stop same event being called in child elements
    e.stopPropagation();

    const isPasswordField = e.target.type === 'password';
    const isNotrackElement = e.target.getAttribute && e.target.getAttribute('notrack') !== null;

    if (isPasswordField || isNotrackElement) {
      return;
    }
    if (!this.isTracking) {
      return;
    }

    const eventData: BasicExtractedEvent = this.getElementEventPayload(e);
    this.userEvents.push(eventData);

    if (this.userEvents.length > 10) {
      this.submitTrackingData();
    }
  }
}

export default new EventTrackerClass();

// ================= Events
// let DOM_EVENT = {
// UIEvent: "abort DOMActivate error load resize scroll select unload",
// UIEvent: "select",
// ProgressEvent: "abort error load loadend loadstart progress progress timeout",
// Event: "abort afterprint beforeprint cached canplay
// canplaythrough change chargingchange chargingtimechange
// checking close dischargingtimechange DOMContentLoaded downloading durationchange
// emptied ended ended error error error error fullscreenchange fullscreenerror
// input invalid languagechange levelchange loadeddata loadedmetadata noupdate obsolete
// offline online open open orientationchange pause pointerlockchange pointerlockerror play
// playing ratechange readystatechange reset seeked seeking stalled submit success suspend
// timeupdate updateready visibilitychange volumechange waiting",
// AnimationEvent: "animationend animationiteration animationstart",
// AudioProcessingEvent: "audioprocess",
// Event: "input submit",
// BeforeUnloadEvent: "beforeunload",
// TimeEvent: "beginEvent endEvent repeatEvent",
// OtherEvent: "blocked complete upgradeneeded versionchange",
// FocusEvent: "blur focus",
// MouseEvent: "click contextmenu dblclick mousedown mouseenter
// mouseleave mousemove mouseout mouseover mouseup show",
// MouseEvent: "click contextmenu dblclick",
// SensorEvent: "compassneedscalibration Unimplemented userproximity",
// OfflineAudioCompletionEvent: "complete",
// CompositionEvent: "compositionend compositionstart compositionupdate",
// ClipboardEvent: "copy cut paste",
// DeviceLightEvent: "devicelight",
// DeviceMotionEvent: "devicemotion",
// DeviceOrientationEvent: "deviceorientation",
// DeviceProximityEvent: "deviceproximity",
// MutationNameEvent: "DOMAttributeNameChanged DOMElementNameChanged",
// MutationEvent: "DOMAttrModified DOMCharacterDataModified
// DOMNodeInserted DOMNodeInsertedIntoDocument DOMNodeRemoved DOMNodeRemovedFromDocument
// DOMSubtreeModified",
// DragEvent: "drag dragend dragenter dragleave dragover dragstart drop",
// DragEvent: "drag drop",
// GamepadEvent: "gamepadconnected gamepaddisconnected",
// HashChangeEvent: "hashchange",
// KeyboardEvent: "keypress",
// MessageEvent: "message",
// PageTransitionEvent: "pagehide pageshow",
// PopStateEvent: "popstate",
// StorageEvent: "storage",
// SVGEvent: "SVGAbort SVGError SVGLoad SVGResize SVGScroll SVGUnload",
// SVGZoomEvent: "SVGZoom",
// TouchEvent: "touchcancel touchend touchenter touchleave touchstart",
// TransitionEvent: "transitionend",
// WheelEvent: "wheel"
// }
