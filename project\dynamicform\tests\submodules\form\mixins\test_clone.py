# python -m pytest dynamicform/tests/submodules/form/mixins/test_clone.py -s
# python -m pytest dynamicform/tests/submodules/form/mixins/test_clone.py -s --cov=dynamicform/submodules/form/mixins --cov-report=term-missing
from django.contrib.auth import get_user_model
from pydash.objects import set_
from rest_framework import status
from helper.test.mixin import APITestsMixin
from dynamicform.models import (
    Form,
    FormSetting,
    Page
)
from dynamicform.submodules.decisionflow.serializer import DecisionFlowSerializer
from dynamicform.tests.submodules.form.mixins.mockup.decision_flow import (
    DECISIONFLOW_SCHEMA,
    EXPECTED_CLONED_DECISIONFLOW_FRONTEND_SCHEMA,
    EXPECTED_CLONED_DECISIONFLOW_BACKEND_SCHEMA
)
from webhook.models import (
    Trigger,
    Webhook
)
from workspace.models import (
    Workspace, 
    Form as WorkspaceForm,
)
from workspace.tests.mockup_frontend_schema import (
    MOCKUP_FORM_FRONTEND_SCHEMA_SIMPLE
)
import copy

User = get_user_model()

class FormCloneTests(APITestsMixin):
    def setUp(self):
        super().setUp()

        self.admin = User.objects.create_user(username="owner_admin", is_active=True)

        self.workspace = Workspace.create(slug="workspace_test", created_by=self.admin)

        # Form
        self.form = Form(
            name="test",
            slug="form_test_1",
            frontend_schema=MOCKUP_FORM_FRONTEND_SCHEMA_SIMPLE
        )
        self.form.save()
        WorkspaceForm.objects.create(
            workspace=self.workspace,
            form=self.form
        )

        self.form.update_lang("en")
        # Form settings
        self.form_setting = FormSetting.objects.create(settings={"drop_off": {}}, form=self.form)

        # Page
        self.page = Page.objects.create(
            name=self.form.slug,
            schema={"items": {
                "content": {
                    "name": "content",
                    "type": "StaticContent",
                    "content": "ABC"
                }
            }},
            category="landing"
        )
        self.page.form = self.form
        self.page.save()
        self.page.update_lang("en")

        # Custom Status
        self.custom_status_key = self.form.customstatuskey_set.create(
            value="status"
        )

        self.custom_status_value = self.custom_status_key.customstatusvalue_set.create(
            value="pass"
        )

        # Datapoint
        self.datapoint = self.form.formdatapoint_set.create(
            data_point_name="led",
            fields={
                "national_id": {
                    "fields": {},
                    "category": "answer",
                    "data_point": [{
                        "value": "short_long_answer",
                        "extra_fields": {}
                    }]
                }
            },
        )

        # Integrations
        self.integration = self.form.integration_set.create(
            name="Docusign",
            service_name="docusign",
            is_enabled=True,
            is_ready=True,
        )
        self.integration_docusign_user = self.integration.docusignuser_set.create(
            user_id="user-id-test",
            account_id="account-id-test",
            base_uri="http://base-url.test", # NOSONAR
        )
        self.integration_docusign_template = self.integration_docusign_user.docusigntemplate_set.create(
            template_id="template-test",
            data="template-data",
            version=1,
        )

        # Webhook
        self.webhook = Webhook.objects.create(
            name="name",
            event_trigger="onSubmit",
        )
        __method = {
            'authorization' : Trigger.AuthorizationChoice.BEARER_TOKEN,
            'url' : 'https://us-central1-credit-ok-testing.cloudfunctions.net/test_only',
            'url_get_token' : 'https://us-central1-credit-ok-testing.cloudfunctions.net/test_only',
            'username' : 'username',
            'password' : 'password'
        }
        self.webhook.add_method(
            method = Webhook.Method.CUSTOM, 
            key = self.form.secret_key,
            **__method
        )
        self.form_webhook = self.form.formwebhook_set.create(
            webhook=self.webhook
        )

    def __set_up_decision_flow(self):
        # Decision Flow
        decision_flow_schema = copy.deepcopy(DECISIONFLOW_SCHEMA)
        set_(decision_flow_schema, "2.param.0.groups.0.children.0.initial_data.value", self.custom_status_key.id)
        set_(decision_flow_schema, "2.param.0.groups.0.children.0.expected_data.value", f"custom_status.{self.custom_status_value.id}")
        set_(decision_flow_schema, "3.param.param.custom_status", self.custom_status_value.id)
        set_(decision_flow_schema, "4.param.param.form_webhook_id", self.form_webhook.id)
        set_(decision_flow_schema, "5.param.param.integration_id", self.integration.id)

        serializer = DecisionFlowSerializer(data={
            "name": "decision_flow",
            "event": "onSubmit",
            "description": "description",
            "active": True,
            "schema": decision_flow_schema,
        }, context={"form": self.form})
        serializer.is_valid(raise_exception=True)
        self.current_decision_flow = serializer.save()

    def test_clone(self):
        self.__set_up_decision_flow()

        cloned_form = self.form.clone(
            name="clone_name",
            workspace=self.workspace
        )
        # Form settings
        self.assertEqual(cloned_form.formsetting.settings, self.form_setting.settings)

        # Form Locale
        self.assertEqual(
            cloned_form.locale_set.first().language,
            self.form.locale_set.first().language
        )
        self.assertEqual(
            cloned_form.locale_set.first().key,
            self.form.locale_set.first().key
        )
        self.assertEqual(
            cloned_form.locale_set.first().text,
            self.form.locale_set.first().text
        )

        # Page
        cloned_page = cloned_form.page_set.filter(name=cloned_form.name).first()
        self.assertEqual(cloned_page._schema, self.page._schema)
        self.assertEqual(cloned_page.category, self.page.category)

        # Page Locale
        self.assertEqual(
            cloned_page.localepage_set.first().language,
            self.page.localepage_set.first().language
        )
        self.assertEqual(
            cloned_page.localepage_set.first().key,
            self.page.localepage_set.first().key
        )
        self.assertEqual(
            cloned_page.localepage_set.first().text,
            self.page.localepage_set.first().text
        )

        # Custom Status
        cloned_customstatus_key = cloned_form.customstatuskey_set.first()
        self.assertEqual(
            cloned_customstatus_key.value,
            self.custom_status_key.value
        )
        self.assertEqual(
            cloned_customstatus_key.customstatusvalue_set.first().value,
            self.custom_status_value.value
        )

        # Datapoint
        self.assertEqual(
            cloned_form.formdatapoint_set.filter(data_point_name="led").first().fields,
            self.datapoint.fields
        )

        # Integrations
        cloned_integration = cloned_form.integration_set.first()
        self.assertEqual(
            cloned_integration.service_name,
            self.integration.service_name
        )
        self.assertEqual(
            cloned_integration.name,
            self.integration.name
        )
        self.assertEqual(
            cloned_integration.is_enabled,
            self.integration.is_enabled
        )
        self.assertEqual(
            cloned_integration.is_ready,
            self.integration.is_ready
        )
        # Integrations Service
        cloned_docusign_user = cloned_integration.docusignuser_set.first()
        self.assertEqual(
            cloned_docusign_user.user_id,
            self.integration_docusign_user.user_id
        )
        self.assertEqual(
            cloned_docusign_user.account_id,
            self.integration_docusign_user.account_id
        )
        self.assertEqual(
            cloned_docusign_user.base_uri,
            self.integration_docusign_user.base_uri
        )
        cloned_docusign_template = cloned_docusign_user.docusigntemplate_set.first()
        self.assertEqual(
            cloned_docusign_template.template_id,
            self.integration_docusign_template.template_id
        )
        self.assertEqual(
            cloned_docusign_template.data,
            self.integration_docusign_template.data
        )
        self.assertEqual(
            cloned_docusign_template.version,
            self.integration_docusign_template.version
        )

        # Webhook
        cloned_form_webhook = cloned_form.formwebhook_set.first()
        self.assertEqual(
            cloned_form_webhook.enable,
            self.form_webhook.enable
        )
        self.assertEqual(
            cloned_form_webhook.enable_auto_trigger,
            self.form_webhook.enable_auto_trigger
        )
        self.assertEqual(
            cloned_form_webhook.json_template,
            self.form_webhook.json_template
        )
        self.assertEqual(
            cloned_form_webhook.props,
            self.form_webhook.props
        )
        self.assertEqual(
            cloned_form_webhook.version,
            self.form_webhook.version
        )
        self.assertEqual(
            cloned_form_webhook.webhook.name,
            self.form_webhook.webhook.name
        )
        self.assertEqual(
            cloned_form_webhook.webhook.event_trigger,
            self.form_webhook.webhook.event_trigger
        )
        self.assertEqual(
            cloned_form_webhook.webhook.method,
            self.form_webhook.webhook.method
        )
        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.authorization,
            self.form_webhook.webhook.custommethod.authorization
        )
        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.url,
            self.form_webhook.webhook.custommethod.url
        )
        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.url_get_token,
            self.form_webhook.webhook.custommethod.url_get_token
        )
        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.token_path,
            self.form_webhook.webhook.custommethod.token_path
        )
        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.username,
            self.form_webhook.webhook.custommethod.username
        )
        self.assertNotEqual(
            cloned_form_webhook.webhook.custommethod.password,
            self.form_webhook.webhook.custommethod.password
        )

        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.decrypt_password(key=cloned_form.secret_key),
            self.form_webhook.webhook.custommethod.decrypt_password(key=self.form.secret_key)
        )
        self.assertEqual(
            cloned_form_webhook.webhook.custommethod.props,
            self.form_webhook.webhook.custommethod.props
        )

        # Decision Flow
        cloned_current_decision_flow = cloned_form.currentdecisionflow_set.first()
        self.assertEqual(
            cloned_current_decision_flow.name,
            self.current_decision_flow.name
        )
        self.assertEqual(
            cloned_current_decision_flow.description,
            self.current_decision_flow.description
        )
        self.assertEqual(
            cloned_current_decision_flow.event,
            self.current_decision_flow.event
        )
        self.assertEqual(
            cloned_current_decision_flow.active,
            self.current_decision_flow.active
        )
        self.assertEqual(
            cloned_current_decision_flow.data_points,
            self.current_decision_flow.data_points
        )
        self.assertEqual(
            cloned_current_decision_flow.synchronous,
            self.current_decision_flow.synchronous
        )
        self.assertEqual(
            cloned_current_decision_flow.repeat_run_config_active,
            self.current_decision_flow.repeat_run_config_active
        )
        self.assertEqual(
            cloned_current_decision_flow.repeat_run_config_period,
            self.current_decision_flow.repeat_run_config_period
        )
        self.assertEqual(
            cloned_current_decision_flow.repeat_run_config_end_date,
            self.current_decision_flow.repeat_run_config_end_date
        )
        self.assertEqual(
            cloned_current_decision_flow.repeat_run_config_apply_all,
            self.current_decision_flow.repeat_run_config_apply_all
        )
        self.assertEqual(
            cloned_current_decision_flow.repeat_run_config_activated_at,
            self.current_decision_flow.repeat_run_config_activated_at
        )
        self.assertEqual(
            cloned_current_decision_flow.repeat_run_events,
            self.current_decision_flow.repeat_run_events
        )
        self.assertEqual(
            cloned_current_decision_flow.queue,
            self.current_decision_flow.queue
        )
        cloned_decision_flow = cloned_current_decision_flow.decision_flow
        self.assertEqual(
            cloned_decision_flow.event,
            self.current_decision_flow.decision_flow.event
        )
        self.assertEqual(
            cloned_decision_flow.publish_name,
            self.current_decision_flow.decision_flow.publish_name
        )
        self.assertEqual(
            cloned_decision_flow.note,
            self.current_decision_flow.decision_flow.note
        )
        self.assertEqual(
            cloned_decision_flow.publish_date,
            self.current_decision_flow.decision_flow.publish_date
        )

    def test_clone_decision_flow_schema(self):
        # Decision Flow
        serializer = DecisionFlowSerializer(data={
            "name": "decision_flow",
            "event": "onSubmit",
            "description": "description",
            "active": True,
            "schema": DECISIONFLOW_SCHEMA,
        }, context={"form": self.form})
        serializer.is_valid(raise_exception=True)
        self.current_decision_flow = serializer.save()

        from decision_flow.converter import Converter

        frontend_schema_copy = copy.deepcopy(DECISIONFLOW_SCHEMA)
        clone_to_form = self.form
        converter = Converter(
			frontend_schema=frontend_schema_copy,
			form=clone_to_form,
			is_cloning=True,
			clone_from_data={
                # object_type: {cloned_form_id:cloned_to_id}
                "webhooks": {1:2},
                "custom_status_keys": {3:4},
                "custom_status_values": {5:6},
                "integrations": {7:8},
            }
		)
        converter.convert()
        self.assertEqual(converter.get_cloned_frontend_schema(), EXPECTED_CLONED_DECISIONFLOW_FRONTEND_SCHEMA)
        self.assertEqual(converter.get_result(), EXPECTED_CLONED_DECISIONFLOW_BACKEND_SCHEMA)
    
    def test_clone_with_api(self):
        def call_clone_apis(data, expected_status_code):
            self.post(f"/th/api/forms/{self.form.slug}/clone/",
                data=data,
                status_code=expected_status_code
            )

        mock_form_name = "cloned form"
        mock_form_slug = "cloned-form"
        self.client.force_login(self.admin)
        call_clone_apis(
            data={
                "name": mock_form_name,
                "slug": mock_form_slug
            },
            expected_status_code=status.HTTP_201_CREATED
        )
        call_clone_apis(
            data={
                "name": mock_form_name,
                "slug": ""
            },
            expected_status_code=status.HTTP_201_CREATED
        )
        call_clone_apis(
            data={
                "name": mock_form_name,
            },
            expected_status_code=status.HTTP_201_CREATED
        )
        # duplicate slug
        call_clone_apis(
            data={
                "name": mock_form_name,
                "slug": mock_form_slug
            },
            expected_status_code=status.HTTP_201_CREATED
        )
