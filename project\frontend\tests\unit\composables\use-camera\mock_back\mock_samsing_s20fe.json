[{"user_agent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Mobile Safari/537.36 EdgA/122.0.0.0", "slug": "9THtK4ha", "created_at": null, "log_camera": {"screen": {"width": 360, "height": 800, "availTop": 0, "availLeft": 0, "availWidth": 360, "colorDepth": 24, "pixelDepth": 24, "availHeight": 800, "orientation": {"type": "portrait-primary", "angle": 0}}, "devices": [{"device": {"kind": "videoinput", "label": "camera2 1, facing front", "groupId": "0224c9089b794bdb593b2ae85fb25043e3a1a629eae6b6de21166030ca52c62f", "deviceId": "a50953600a5932b3abade75227487403b27e87851b1a2fd9fbd11bdbcdf62a7c"}, "tracks": [{"id": "416a3740-d20f-485a-a2fa-88be44bac89e", "kind": "video", "label": "camera2 1, facing front", "muted": false, "enabled": true, "settings": {"iso": 50, "zoom": 1, "width": 480, "height": 640, "groupId": "0224c9089b794bdb593b2ae85fb25043e3a1a629eae6b6de21166030ca52c62f", "deviceId": "a50953600a5932b3abade75227487403b27e87851b1a2fd9fbd11bdbcdf62a7c", "focusMode": "continuous", "frameRate": 30, "facingMode": "user", "resizeMode": "none", "aspectRatio": 0.75, "exposureMode": "continuous", "exposureTime": 666, "focusDistance": 0.4000000059604645, "colorTemperature": 0, "whiteBalanceMode": "continuous", "exposureCompensation": 0}, "readyState": "live", "constraints": {"deviceId": {"exact": "a50953600a5932b3abade75227487403b27e87851b1a2fd9fbd11bdbcdf62a7c"}}, "contentHint": "", "capabilities": {"iso": {"max": 100800, "min": 50, "step": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}, "width": {"max": 3264, "min": 1}, "height": {"max": 2448, "min": 1}, "groupId": "0224c9089b794bdb593b2ae85fb25043e3a1a629eae6b6de21166030ca52c62f", "deviceId": "a50953600a5932b3abade75227487403b27e87851b1a2fd9fbd11bdbcdf62a7c", "focusMode": ["manual"], "frameRate": {"max": 30, "min": 0}, "facingMode": ["user"], "resizeMode": ["none", "crop-and-scale"], "aspectRatio": {"max": 3264, "min": 0.0004084967320261438}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 2793, "min": 0, "step": 0}, "focusDistance": {"max": 0.2543646991252899, "min": 0, "step": 0.009999999776482582}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "whiteBalanceMode": ["continuous", "manual"], "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}}}], "success": true, "deviceConstraints": null}, {"device": {"kind": "videoinput", "label": "camera2 3, facing front", "groupId": "25a15440a5bb128773a2a8babae6d5de93a2ac50093646ec754f3d0240b8029c", "deviceId": "ae25d87e8c4e6a399df4a440adea6111aeaace2169fdd45cd50d5fc7d619165b"}, "tracks": [{"id": "ab7bd9a7-702d-47df-ab90-7e169cd4f8b7", "kind": "video", "label": "camera2 3, facing front", "muted": false, "enabled": true, "settings": {"iso": 50, "zoom": 1, "width": 480, "height": 640, "groupId": "25a15440a5bb128773a2a8babae6d5de93a2ac50093646ec754f3d0240b8029c", "deviceId": "ae25d87e8c4e6a399df4a440adea6111aeaace2169fdd45cd50d5fc7d619165b", "focusMode": "continuous", "frameRate": 30, "facingMode": "user", "resizeMode": "none", "aspectRatio": 0.75, "exposureMode": "continuous", "exposureTime": 416, "focusDistance": 0.4000000059604645, "colorTemperature": 0, "whiteBalanceMode": "continuous", "exposureCompensation": 0}, "readyState": "live", "constraints": {"deviceId": {"exact": "ae25d87e8c4e6a399df4a440adea6111aeaace2169fdd45cd50d5fc7d619165b"}}, "contentHint": "", "capabilities": {"iso": {"max": 3200, "min": 50, "step": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}, "width": {"max": 2640, "min": 1}, "height": {"max": 1980, "min": 1}, "groupId": "25a15440a5bb128773a2a8babae6d5de93a2ac50093646ec754f3d0240b8029c", "deviceId": "ae25d87e8c4e6a399df4a440adea6111aeaace2169fdd45cd50d5fc7d619165b", "focusMode": ["manual"], "frameRate": {"max": 30, "min": 0}, "facingMode": ["user"], "resizeMode": ["none", "crop-and-scale"], "aspectRatio": {"max": 2640, "min": 0.000505050505050505}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 2793, "min": 0, "step": 0}, "focusDistance": {"max": 0.2543646991252899, "min": 0, "step": 0.009999999776482582}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "whiteBalanceMode": ["continuous", "manual"], "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}}}], "success": true, "deviceConstraints": null}, {"device": {"kind": "videoinput", "label": "camera2 2, facing back", "groupId": "79ba1c5d3c0e0a81b31da99be99c3180ffc7814d64bf4699979635b029d03c75", "deviceId": "70e88029598a0b03dde13743a8b2e3a1f2b2d6b98ed787f9e154a65aef58bb4d"}, "tracks": [{"id": "605eb2cf-17f8-4181-9e44-c106ffc6c04a", "kind": "video", "label": "camera2 2, facing back", "muted": false, "enabled": true, "settings": {"iso": 50, "zoom": 1, "torch": false, "width": 480, "height": 640, "groupId": "79ba1c5d3c0e0a81b31da99be99c3180ffc7814d64bf4699979635b029d03c75", "deviceId": "70e88029598a0b03dde13743a8b2e3a1f2b2d6b98ed787f9e154a65aef58bb4d", "focusMode": "continuous", "frameRate": 30, "facingMode": "environment", "resizeMode": "none", "aspectRatio": 0.75, "exposureMode": "continuous", "exposureTime": 199, "focusDistance": 0.5, "colorTemperature": 0, "whiteBalanceMode": "continuous", "exposureCompensation": 0}, "readyState": "live", "constraints": {"deviceId": {"exact": "70e88029598a0b03dde13743a8b2e3a1f2b2d6b98ed787f9e154a65aef58bb4d"}}, "contentHint": "", "capabilities": {"iso": {"max": 2400, "min": 50, "step": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}, "torch": true, "width": {"max": 4000, "min": 1}, "height": {"max": 3000, "min": 1}, "groupId": "79ba1c5d3c0e0a81b31da99be99c3180ffc7814d64bf4699979635b029d03c75", "deviceId": "70e88029598a0b03dde13743a8b2e3a1f2b2d6b98ed787f9e154a65aef58bb4d", "focusMode": ["manual"], "frameRate": {"max": 30, "min": 0}, "facingMode": ["environment"], "resizeMode": ["none", "crop-and-scale"], "aspectRatio": {"max": 4000, "min": 0.0003333333333333333}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 6714, "min": 0, "step": 0}, "focusDistance": {"max": 1.6276918649673462, "min": 0, "step": 0.009999999776482582}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "whiteBalanceMode": ["continuous", "manual"], "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}}}], "success": true, "deviceConstraints": null}, {"device": {"kind": "videoinput", "label": "camera2 0, facing back", "groupId": "8688db5dbdc71f6654d06843fe554c49d2a88816afa3314aeb3a1b616679e708", "deviceId": "c8ece2334eae1438dcbbcf43c5bac51b841ddb13841330703de21039be3a5114"}, "tracks": [{"id": "0617e70c-0606-44eb-b7d0-77bb7a8da9fe", "kind": "video", "label": "camera2 0, facing back", "muted": false, "enabled": true, "settings": {"iso": 50, "zoom": 1, "torch": false, "width": 480, "height": 640, "groupId": "8688db5dbdc71f6654d06843fe554c49d2a88816afa3314aeb3a1b616679e708", "deviceId": "c8ece2334eae1438dcbbcf43c5bac51b841ddb13841330703de21039be3a5114", "focusMode": "continuous", "frameRate": 30, "facingMode": "environment", "resizeMode": "none", "aspectRatio": 0.75, "exposureMode": "continuous", "exposureTime": 299, "focusDistance": 0.10000000149011612, "colorTemperature": 0, "whiteBalanceMode": "continuous", "exposureCompensation": 0}, "readyState": "live", "constraints": {"deviceId": {"exact": "c8ece2334eae1438dcbbcf43c5bac51b841ddb13841330703de21039be3a5114"}}, "contentHint": "", "capabilities": {"iso": {"max": 3200, "min": 50, "step": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}, "torch": true, "width": {"max": 4032, "min": 1}, "height": {"max": 3024, "min": 1}, "groupId": "8688db5dbdc71f6654d06843fe554c49d2a88816afa3314aeb3a1b616679e708", "deviceId": "c8ece2334eae1438dcbbcf43c5bac51b841ddb13841330703de21039be3a5114", "focusMode": ["manual", "single-shot", "continuous"], "frameRate": {"max": 30, "min": 0}, "facingMode": ["environment"], "resizeMode": ["none", "crop-and-scale"], "aspectRatio": {"max": 4032, "min": 0.00033068783068783067}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 1467, "min": 0, "step": 0}, "focusDistance": {"max": 0.2222221940755844, "min": 0.10000000149011612, "step": 0.009999999776482582}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "whiteBalanceMode": ["continuous", "manual"], "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}}}], "success": true, "deviceConstraints": null}], "attempts": [{"endAt": "2024-03-20T08:31:15.101Z", "error": null, "video": {"loop": false, "ended": false, "error": null, "muted": true, "reIso": false, "width": 0, "height": 0, "paused": false, "played": {}, "poster": "https://cdn.uppass.io/images/transparent.png", "remote": {}, "volume": 1, "preload": "none", "seeking": false, "autoplay": false, "buffered": {}, "controls": false, "duration": null, "seekable": {}, "settings": {"iso": 50, "zoom": 1, "torch": false, "width": 1080, "height": 1920, "groupId": "8688db5dbdc71f6654d06843fe554c49d2a88816afa3314aeb3a1b616679e708", "deviceId": "c8ece2334eae1438dcbbcf43c5bac51b841ddb13841330703de21039be3a5114", "focusMode": "continuous", "frameRate": 30, "facingMode": "environment", "resizeMode": "none", "aspectRatio": 0.5625, "exposureMode": "continuous", "exposureTime": 299, "focusDistance": 0.10000000149011612, "colorTemperature": 0, "whiteBalanceMode": "continuous", "exposureCompensation": 0}, "mediaKeys": null, "reFocused": false, "currentSrc": "", "readyState": 4, "videoWidth": 1080, "clientWidth": 308, "crossOrigin": null, "currentTime": 1297144.693771, "offsetWidth": 308, "playsInline": true, "videoHeight": 1920, "clientHeight": 221, "defaultMuted": true, "networkState": 2, "offsetHeight": 221, "playbackRate": 1, "reConstraints": false, "preservesPitch": true, "playbackQuality": {}, "defaultPlaybackRate": 1, "disableRemotePlayback": false}, "method": "best", "startAt": "2024-03-20T08:31:14.337Z", "success": true, "constraints": {"audio": false, "video": {"width": {"min": 320, "ideal": 1920}, "height": {"min": 240, "ideal": 1080}, "advanced": [{"focusMode": "continuous"}], "deviceId": {"exact": "c8ece2334eae1438dcbbcf43c5bac51b841ddb13841330703de21039be3a5114"}, "facingMode": "environment"}}, "streamTracks": [{"id": "814f6b84-1b81-4d7f-acdb-370551814b5a", "label": "camera2 0, facing back"}]}], "supports": {"iso": true, "pan": true, "tilt": true, "zoom": true, "torch": true, "width": true, "height": true, "groupId": true, "latency": true, "contrast": true, "deviceId": true, "focusMode": true, "frameRate": true, "sharpness": true, "brightness": true, "facingMode": true, "resizeMode": true, "sampleRate": true, "sampleSize": true, "saturation": true, "aspectRatio": true, "faceFraming": true, "channelCount": true, "exposureMode": true, "exposureTime": true, "focusDistance": true, "backgroundBlur": true, "displaySurface": true, "voiceIsolation": true, "autoGainControl": true, "colorTemperature": true, "echoCancellation": true, "noiseSuppression": true, "pointsOfInterest": true, "whiteBalanceMode": true, "eyeGazeCorrection": true, "exposureCompensation": true, "suppressLocalAudioPlayback": true}, "facingMode": "environment", "log_version": "2023-06-15"}}]