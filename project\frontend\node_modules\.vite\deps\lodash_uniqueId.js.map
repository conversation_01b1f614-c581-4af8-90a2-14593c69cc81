{"version": 3, "sources": ["../../lodash/uniqueId.js"], "sourcesContent": ["var toString = require('./toString');\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nmodule.exports = uniqueId;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAGf,QAAI,YAAY;AAmBhB,aAAS,SAAS,QAAQ;AACxB,UAAI,KAAK,EAAE;AACX,aAAO,SAAS,MAAM,IAAI;AAAA,IAC5B;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}