{"version": 3, "sources": ["../../vue-i18n/dist/vue-i18n.esm.js"], "sourcesContent": ["/*!\n * vue-i18n v8.28.2 \n * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n * Released under the MIT License.\n */\n/*  */\n\n/**\n * constants\n */\n\nvar numberFormatKeys = [\n  'compactDisplay',\n  'currency',\n  'currencyDisplay',\n  'currencySign',\n  'localeMatcher',\n  'notation',\n  'numberingSystem',\n  'signDisplay',\n  'style',\n  'unit',\n  'unitDisplay',\n  'useGrouping',\n  'minimumIntegerDigits',\n  'minimumFractionDigits',\n  'maximumFractionDigits',\n  'minimumSignificantDigits',\n  'maximumSignificantDigits'\n];\n\nvar dateTimeFormatKeys = [\n  'dateStyle',\n  'timeStyle',\n  'calendar',\n  'localeMatcher',\n  \"hour12\",\n  \"hourCycle\",\n  \"timeZone\",\n  \"formatMatcher\",\n  'weekday',\n  'era',\n  'year',\n  'month',\n  'day',\n  'hour',\n  'minute',\n  'second',\n  'timeZoneName' ];\n\n/**\n * utilities\n */\n\nfunction warn (msg, err) {\n  if (typeof console !== 'undefined') {\n    console.warn('[vue-i18n] ' + msg);\n    /* istanbul ignore if */\n    if (err) {\n      console.warn(err.stack);\n    }\n  }\n}\n\nfunction error (msg, err) {\n  if (typeof console !== 'undefined') {\n    console.error('[vue-i18n] ' + msg);\n    /* istanbul ignore if */\n    if (err) {\n      console.error(err.stack);\n    }\n  }\n}\n\nvar isArray = Array.isArray;\n\nfunction isObject (obj) {\n  return obj !== null && typeof obj === 'object'\n}\n\nfunction isBoolean (val) {\n  return typeof val === 'boolean'\n}\n\nfunction isString (val) {\n  return typeof val === 'string'\n}\n\nvar toString = Object.prototype.toString;\nvar OBJECT_STRING = '[object Object]';\nfunction isPlainObject (obj) {\n  return toString.call(obj) === OBJECT_STRING\n}\n\nfunction isNull (val) {\n  return val === null || val === undefined\n}\n\nfunction isFunction (val) {\n  return typeof val === 'function'\n}\n\nfunction parseArgs () {\n  var args = [], len = arguments.length;\n  while ( len-- ) args[ len ] = arguments[ len ];\n\n  var locale = null;\n  var params = null;\n  if (args.length === 1) {\n    if (isObject(args[0]) || isArray(args[0])) {\n      params = args[0];\n    } else if (typeof args[0] === 'string') {\n      locale = args[0];\n    }\n  } else if (args.length === 2) {\n    if (typeof args[0] === 'string') {\n      locale = args[0];\n    }\n    /* istanbul ignore if */\n    if (isObject(args[1]) || isArray(args[1])) {\n      params = args[1];\n    }\n  }\n\n  return { locale: locale, params: params }\n}\n\nfunction looseClone (obj) {\n  return JSON.parse(JSON.stringify(obj))\n}\n\nfunction remove (arr, item) {\n  if (arr.delete(item)) {\n    return arr\n  }\n}\n\nfunction arrayFrom (arr) {\n  var ret = [];\n  arr.forEach(function (a) { return ret.push(a); });\n  return ret\n}\n\nfunction includes (arr, item) {\n  return !!~arr.indexOf(item)\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn (obj, key) {\n  return hasOwnProperty.call(obj, key)\n}\n\nfunction merge (target) {\n  var arguments$1 = arguments;\n\n  var output = Object(target);\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments$1[i];\n    if (source !== undefined && source !== null) {\n      var key = (void 0);\n      for (key in source) {\n        if (hasOwn(source, key)) {\n          if (isObject(source[key])) {\n            output[key] = merge(output[key], source[key]);\n          } else {\n            output[key] = source[key];\n          }\n        }\n      }\n    }\n  }\n  return output\n}\n\nfunction looseEqual (a, b) {\n  if (a === b) { return true }\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = isArray(a);\n      var isArrayB = isArray(b);\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i])\n        })\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key])\n        })\n      } else {\n        /* istanbul ignore next */\n        return false\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b)\n  } else {\n    return false\n  }\n}\n\n/**\n * Sanitizes html special characters from input strings. For mitigating risk of XSS attacks.\n * @param rawText The raw input from the user that should be escaped.\n */\nfunction escapeHtml(rawText) {\n  return rawText\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&apos;')\n}\n\n/**\n * Escapes html tags and special symbols from all provided params which were returned from parseArgs().params.\n * This method performs an in-place operation on the params object.\n *\n * @param {any} params Parameters as provided from `parseArgs().params`.\n *                     May be either an array of strings or a string->any map.\n *\n * @returns The manipulated `params` object.\n */\nfunction escapeParams(params) {\n  if(params != null) {\n    Object.keys(params).forEach(function (key) {\n      if(typeof(params[key]) == 'string') {\n        params[key] = escapeHtml(params[key]);\n      }\n    });\n  }\n  return params\n}\n\n/*  */\n\nfunction extend (Vue) {\n  if (!Vue.prototype.hasOwnProperty('$i18n')) {\n    // $FlowFixMe\n    Object.defineProperty(Vue.prototype, '$i18n', {\n      get: function get () { return this._i18n }\n    });\n  }\n\n  Vue.prototype.$t = function (key) {\n    var values = [], len = arguments.length - 1;\n    while ( len-- > 0 ) values[ len ] = arguments[ len + 1 ];\n\n    var i18n = this.$i18n;\n    return i18n._t.apply(i18n, [ key, i18n.locale, i18n._getMessages(), this ].concat( values ))\n  };\n\n  Vue.prototype.$tc = function (key, choice) {\n    var values = [], len = arguments.length - 2;\n    while ( len-- > 0 ) values[ len ] = arguments[ len + 2 ];\n\n    var i18n = this.$i18n;\n    return i18n._tc.apply(i18n, [ key, i18n.locale, i18n._getMessages(), this, choice ].concat( values ))\n  };\n\n  Vue.prototype.$te = function (key, locale) {\n    var i18n = this.$i18n;\n    return i18n._te(key, i18n.locale, i18n._getMessages(), locale)\n  };\n\n  Vue.prototype.$d = function (value) {\n    var ref;\n\n    var args = [], len = arguments.length - 1;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];\n    return (ref = this.$i18n).d.apply(ref, [ value ].concat( args ))\n  };\n\n  Vue.prototype.$n = function (value) {\n    var ref;\n\n    var args = [], len = arguments.length - 1;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];\n    return (ref = this.$i18n).n.apply(ref, [ value ].concat( args ))\n  };\n}\n\n/*  */\n\n/**\n * Mixin\n * \n * If `bridge` mode, empty mixin is returned,\n * else regulary mixin implementation is returned.\n */\nfunction defineMixin (bridge) {\n  if ( bridge === void 0 ) bridge = false;\n\n  function mounted () {\n    if (this !== this.$root && this.$options.__INTLIFY_META__ && this.$el) {\n      this.$el.setAttribute('data-intlify', this.$options.__INTLIFY_META__);\n    }\n  }\n\n  return bridge\n    ? { mounted: mounted } // delegate `vue-i18n-bridge` mixin implementation\n    : { // regulary \n    beforeCreate: function beforeCreate () {\n      var options = this.$options;\n      options.i18n = options.i18n || ((options.__i18nBridge || options.__i18n) ? {} : null);\n\n      if (options.i18n) {\n        if (options.i18n instanceof VueI18n) {\n          // init locale messages via custom blocks\n          if ((options.__i18nBridge || options.__i18n)) {\n            try {\n              var localeMessages = options.i18n && options.i18n.messages ? options.i18n.messages : {};\n              var _i18n = options.__i18nBridge || options.__i18n;\n              _i18n.forEach(function (resource) {\n                localeMessages = merge(localeMessages, JSON.parse(resource));\n              });\n              Object.keys(localeMessages).forEach(function (locale) {\n                options.i18n.mergeLocaleMessage(locale, localeMessages[locale]);\n              });\n            } catch (e) {\n              if (process.env.NODE_ENV !== 'production') {\n                error(\"Cannot parse locale messages via custom blocks.\", e);\n              }\n            }\n          }\n          this._i18n = options.i18n;\n          this._i18nWatcher = this._i18n.watchI18nData();\n        } else if (isPlainObject(options.i18n)) {\n          var rootI18n = this.$root && this.$root.$i18n && this.$root.$i18n instanceof VueI18n\n            ? this.$root.$i18n\n            : null;\n          // component local i18n\n          if (rootI18n) {\n            options.i18n.root = this.$root;\n            options.i18n.formatter = rootI18n.formatter;\n            options.i18n.fallbackLocale = rootI18n.fallbackLocale;\n            options.i18n.formatFallbackMessages = rootI18n.formatFallbackMessages;\n            options.i18n.silentTranslationWarn = rootI18n.silentTranslationWarn;\n            options.i18n.silentFallbackWarn = rootI18n.silentFallbackWarn;\n            options.i18n.pluralizationRules = rootI18n.pluralizationRules;\n            options.i18n.preserveDirectiveContent = rootI18n.preserveDirectiveContent;\n          }\n\n          // init locale messages via custom blocks\n          if ((options.__i18nBridge || options.__i18n)) {\n            try {\n              var localeMessages$1 = options.i18n && options.i18n.messages ? options.i18n.messages : {};\n              var _i18n$1 = options.__i18nBridge || options.__i18n;\n              _i18n$1.forEach(function (resource) {\n                localeMessages$1 = merge(localeMessages$1, JSON.parse(resource));\n              });\n              options.i18n.messages = localeMessages$1;\n            } catch (e) {\n              if (process.env.NODE_ENV !== 'production') {\n                warn(\"Cannot parse locale messages via custom blocks.\", e);\n              }\n            }\n          }\n\n          var ref = options.i18n;\n          var sharedMessages = ref.sharedMessages;\n          if (sharedMessages && isPlainObject(sharedMessages)) {\n            options.i18n.messages = merge(options.i18n.messages, sharedMessages);\n          }\n\n          this._i18n = new VueI18n(options.i18n);\n          this._i18nWatcher = this._i18n.watchI18nData();\n\n          if (options.i18n.sync === undefined || !!options.i18n.sync) {\n            this._localeWatcher = this.$i18n.watchLocale();\n          }\n\n          if (rootI18n) {\n            rootI18n.onComponentInstanceCreated(this._i18n);\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn(\"Cannot be interpreted 'i18n' option.\");\n          }\n        }\n      } else if (this.$root && this.$root.$i18n && this.$root.$i18n instanceof VueI18n) {\n        // root i18n\n        this._i18n = this.$root.$i18n;\n      } else if (options.parent && options.parent.$i18n && options.parent.$i18n instanceof VueI18n) {\n        // parent i18n\n        this._i18n = options.parent.$i18n;\n      }\n    },\n\n    beforeMount: function beforeMount () {\n      var options = this.$options;\n      options.i18n = options.i18n || ((options.__i18nBridge || options.__i18n) ? {} : null);\n\n      if (options.i18n) {\n        if (options.i18n instanceof VueI18n) {\n          // init locale messages via custom blocks\n          this._i18n.subscribeDataChanging(this);\n          this._subscribing = true;\n        } else if (isPlainObject(options.i18n)) {\n          this._i18n.subscribeDataChanging(this);\n          this._subscribing = true;\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn(\"Cannot be interpreted 'i18n' option.\");\n          }\n        }\n      } else if (this.$root && this.$root.$i18n && this.$root.$i18n instanceof VueI18n) {\n        this._i18n.subscribeDataChanging(this);\n        this._subscribing = true;\n      } else if (options.parent && options.parent.$i18n && options.parent.$i18n instanceof VueI18n) {\n        this._i18n.subscribeDataChanging(this);\n        this._subscribing = true;\n      }\n    },\n\n    mounted: mounted,\n\n    beforeDestroy: function beforeDestroy () {\n      if (!this._i18n) { return }\n\n      var self = this;\n      this.$nextTick(function () {\n        if (self._subscribing) {\n          self._i18n.unsubscribeDataChanging(self);\n          delete self._subscribing;\n        }\n\n        if (self._i18nWatcher) {\n          self._i18nWatcher();\n          self._i18n.destroyVM();\n          delete self._i18nWatcher;\n        }\n\n        if (self._localeWatcher) {\n          self._localeWatcher();\n          delete self._localeWatcher;\n        }\n      });\n    }\n  }\n}\n\n/*  */\n\nvar interpolationComponent = {\n  name: 'i18n',\n  functional: true,\n  props: {\n    tag: {\n      type: [String, Boolean, Object],\n      default: 'span'\n    },\n    path: {\n      type: String,\n      required: true\n    },\n    locale: {\n      type: String\n    },\n    places: {\n      type: [Array, Object]\n    }\n  },\n  render: function render (h, ref) {\n    var data = ref.data;\n    var parent = ref.parent;\n    var props = ref.props;\n    var slots = ref.slots;\n\n    var $i18n = parent.$i18n;\n    if (!$i18n) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Cannot find VueI18n instance!');\n      }\n      return\n    }\n\n    var path = props.path;\n    var locale = props.locale;\n    var places = props.places;\n    var params = slots();\n    var children = $i18n.i(\n      path,\n      locale,\n      onlyHasDefaultPlace(params) || places\n        ? useLegacyPlaces(params.default, places)\n        : params\n    );\n\n    var tag = (!!props.tag && props.tag !== true) || props.tag === false ? props.tag : 'span';\n    return tag ? h(tag, data, children) : children\n  }\n};\n\nfunction onlyHasDefaultPlace (params) {\n  var prop;\n  for (prop in params) {\n    if (prop !== 'default') { return false }\n  }\n  return Boolean(prop)\n}\n\nfunction useLegacyPlaces (children, places) {\n  var params = places ? createParamsFromPlaces(places) : {};\n\n  if (!children) { return params }\n\n  // Filter empty text nodes\n  children = children.filter(function (child) {\n    return child.tag || child.text.trim() !== ''\n  });\n\n  var everyPlace = children.every(vnodeHasPlaceAttribute);\n  if (process.env.NODE_ENV !== 'production' && everyPlace) {\n    warn('`place` attribute is deprecated in next major version. Please switch to Vue slots.');\n  }\n\n  return children.reduce(\n    everyPlace ? assignChildPlace : assignChildIndex,\n    params\n  )\n}\n\nfunction createParamsFromPlaces (places) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('`places` prop is deprecated in next major version. Please switch to Vue slots.');\n  }\n\n  return Array.isArray(places)\n    ? places.reduce(assignChildIndex, {})\n    : Object.assign({}, places)\n}\n\nfunction assignChildPlace (params, child) {\n  if (child.data && child.data.attrs && child.data.attrs.place) {\n    params[child.data.attrs.place] = child;\n  }\n  return params\n}\n\nfunction assignChildIndex (params, child, index) {\n  params[index] = child;\n  return params\n}\n\nfunction vnodeHasPlaceAttribute (vnode) {\n  return Boolean(vnode.data && vnode.data.attrs && vnode.data.attrs.place)\n}\n\n/*  */\n\nvar numberComponent = {\n  name: 'i18n-n',\n  functional: true,\n  props: {\n    tag: {\n      type: [String, Boolean, Object],\n      default: 'span'\n    },\n    value: {\n      type: Number,\n      required: true\n    },\n    format: {\n      type: [String, Object]\n    },\n    locale: {\n      type: String\n    }\n  },\n  render: function render (h, ref) {\n    var props = ref.props;\n    var parent = ref.parent;\n    var data = ref.data;\n\n    var i18n = parent.$i18n;\n\n    if (!i18n) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Cannot find VueI18n instance!');\n      }\n      return null\n    }\n\n    var key = null;\n    var options = null;\n\n    if (isString(props.format)) {\n      key = props.format;\n    } else if (isObject(props.format)) {\n      if (props.format.key) {\n        key = props.format.key;\n      }\n\n      // Filter out number format options only\n      options = Object.keys(props.format).reduce(function (acc, prop) {\n        var obj;\n\n        if (includes(numberFormatKeys, prop)) {\n          return Object.assign({}, acc, ( obj = {}, obj[prop] = props.format[prop], obj ))\n        }\n        return acc\n      }, null);\n    }\n\n    var locale = props.locale || i18n.locale;\n    var parts = i18n._ntp(props.value, locale, key, options);\n\n    var values = parts.map(function (part, index) {\n      var obj;\n\n      var slot = data.scopedSlots && data.scopedSlots[part.type];\n      return slot ? slot(( obj = {}, obj[part.type] = part.value, obj.index = index, obj.parts = parts, obj )) : part.value\n    });\n\n    var tag = (!!props.tag && props.tag !== true) || props.tag === false ? props.tag : 'span';\n    return tag\n      ? h(tag, {\n        attrs: data.attrs,\n        'class': data['class'],\n        staticClass: data.staticClass\n      }, values)\n      : values\n  }\n};\n\n/*  */\n\nfunction bind (el, binding, vnode) {\n  if (!assert(el, vnode)) { return }\n\n  t(el, binding, vnode);\n}\n\nfunction update (el, binding, vnode, oldVNode) {\n  if (!assert(el, vnode)) { return }\n\n  var i18n = vnode.context.$i18n;\n  if (localeEqual(el, vnode) &&\n    (looseEqual(binding.value, binding.oldValue) &&\n     looseEqual(el._localeMessage, i18n.getLocaleMessage(i18n.locale)))) { return }\n\n  t(el, binding, vnode);\n}\n\nfunction unbind (el, binding, vnode, oldVNode) {\n  var vm = vnode.context;\n  if (!vm) {\n    warn('Vue instance does not exists in VNode context');\n    return\n  }\n\n  var i18n = vnode.context.$i18n || {};\n  if (!binding.modifiers.preserve && !i18n.preserveDirectiveContent) {\n    el.textContent = '';\n  }\n  el._vt = undefined;\n  delete el['_vt'];\n  el._locale = undefined;\n  delete el['_locale'];\n  el._localeMessage = undefined;\n  delete el['_localeMessage'];\n}\n\nfunction assert (el, vnode) {\n  var vm = vnode.context;\n  if (!vm) {\n    warn('Vue instance does not exists in VNode context');\n    return false\n  }\n\n  if (!vm.$i18n) {\n    warn('VueI18n instance does not exists in Vue instance');\n    return false\n  }\n\n  return true\n}\n\nfunction localeEqual (el, vnode) {\n  var vm = vnode.context;\n  return el._locale === vm.$i18n.locale\n}\n\nfunction t (el, binding, vnode) {\n  var ref$1, ref$2;\n\n  var value = binding.value;\n\n  var ref = parseValue(value);\n  var path = ref.path;\n  var locale = ref.locale;\n  var args = ref.args;\n  var choice = ref.choice;\n  if (!path && !locale && !args) {\n    warn('value type not supported');\n    return\n  }\n\n  if (!path) {\n    warn('`path` is required in v-t directive');\n    return\n  }\n\n  var vm = vnode.context;\n  if (choice != null) {\n    el._vt = el.textContent = (ref$1 = vm.$i18n).tc.apply(ref$1, [ path, choice ].concat( makeParams(locale, args) ));\n  } else {\n    el._vt = el.textContent = (ref$2 = vm.$i18n).t.apply(ref$2, [ path ].concat( makeParams(locale, args) ));\n  }\n  el._locale = vm.$i18n.locale;\n  el._localeMessage = vm.$i18n.getLocaleMessage(vm.$i18n.locale);\n}\n\nfunction parseValue (value) {\n  var path;\n  var locale;\n  var args;\n  var choice;\n\n  if (isString(value)) {\n    path = value;\n  } else if (isPlainObject(value)) {\n    path = value.path;\n    locale = value.locale;\n    args = value.args;\n    choice = value.choice;\n  }\n\n  return { path: path, locale: locale, args: args, choice: choice }\n}\n\nfunction makeParams (locale, args) {\n  var params = [];\n\n  locale && params.push(locale);\n  if (args && (Array.isArray(args) || isPlainObject(args))) {\n    params.push(args);\n  }\n\n  return params\n}\n\nvar Vue;\n\nfunction install (_Vue, options) {\n  if ( options === void 0 ) options = { bridge: false };\n\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && install.installed && _Vue === Vue) {\n    warn('already installed.');\n    return\n  }\n  install.installed = true;\n\n  Vue = _Vue;\n\n  var version = (Vue.version && Number(Vue.version.split('.')[0])) || -1;\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && version < 2) {\n    warn((\"vue-i18n (\" + (install.version) + \") need to use Vue 2.0 or later (Vue: \" + (Vue.version) + \").\"));\n    return\n  }\n\n  extend(Vue);\n  Vue.mixin(defineMixin(options.bridge));\n  Vue.directive('t', { bind: bind, update: update, unbind: unbind });\n  Vue.component(interpolationComponent.name, interpolationComponent);\n  Vue.component(numberComponent.name, numberComponent);\n\n  // use simple mergeStrategies to prevent i18n instance lose '__proto__'\n  var strats = Vue.config.optionMergeStrategies;\n  strats.i18n = function (parentVal, childVal) {\n    return childVal === undefined\n      ? parentVal\n      : childVal\n  };\n}\n\n/*  */\n\nvar BaseFormatter = function BaseFormatter () {\n  this._caches = Object.create(null);\n};\n\nBaseFormatter.prototype.interpolate = function interpolate (message, values) {\n  if (!values) {\n    return [message]\n  }\n  var tokens = this._caches[message];\n  if (!tokens) {\n    tokens = parse(message);\n    this._caches[message] = tokens;\n  }\n  return compile(tokens, values)\n};\n\n\n\nvar RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\nvar RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\n\nfunction parse (format) {\n  var tokens = [];\n  var position = 0;\n\n  var text = '';\n  while (position < format.length) {\n    var char = format[position++];\n    if (char === '{') {\n      if (text) {\n        tokens.push({ type: 'text', value: text });\n      }\n\n      text = '';\n      var sub = '';\n      char = format[position++];\n      while (char !== undefined && char !== '}') {\n        sub += char;\n        char = format[position++];\n      }\n      var isClosed = char === '}';\n\n      var type = RE_TOKEN_LIST_VALUE.test(sub)\n        ? 'list'\n        : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\n          ? 'named'\n          : 'unknown';\n      tokens.push({ value: sub, type: type });\n    } else if (char === '%') {\n      // when found rails i18n syntax, skip text capture\n      if (format[(position)] !== '{') {\n        text += char;\n      }\n    } else {\n      text += char;\n    }\n  }\n\n  text && tokens.push({ type: 'text', value: text });\n\n  return tokens\n}\n\nfunction compile (tokens, values) {\n  var compiled = [];\n  var index = 0;\n\n  var mode = Array.isArray(values)\n    ? 'list'\n    : isObject(values)\n      ? 'named'\n      : 'unknown';\n  if (mode === 'unknown') { return compiled }\n\n  while (index < tokens.length) {\n    var token = tokens[index];\n    switch (token.type) {\n      case 'text':\n        compiled.push(token.value);\n        break\n      case 'list':\n        compiled.push(values[parseInt(token.value, 10)]);\n        break\n      case 'named':\n        if (mode === 'named') {\n          compiled.push((values)[token.value]);\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            warn((\"Type of token '\" + (token.type) + \"' and format of value '\" + mode + \"' don't match!\"));\n          }\n        }\n        break\n      case 'unknown':\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\"Detect 'unknown' type of token!\");\n        }\n        break\n    }\n    index++;\n  }\n\n  return compiled\n}\n\n/*  */\n\n/**\n *  Path parser\n *  - Inspired:\n *    Vue.js Path parser\n */\n\n// actions\nvar APPEND = 0;\nvar PUSH = 1;\nvar INC_SUB_PATH_DEPTH = 2;\nvar PUSH_SUB_PATH = 3;\n\n// states\nvar BEFORE_PATH = 0;\nvar IN_PATH = 1;\nvar BEFORE_IDENT = 2;\nvar IN_IDENT = 3;\nvar IN_SUB_PATH = 4;\nvar IN_SINGLE_QUOTE = 5;\nvar IN_DOUBLE_QUOTE = 6;\nvar AFTER_PATH = 7;\nvar ERROR = 8;\n\nvar pathStateMachine = [];\n\npathStateMachine[BEFORE_PATH] = {\n  'ws': [BEFORE_PATH],\n  'ident': [IN_IDENT, APPEND],\n  '[': [IN_SUB_PATH],\n  'eof': [AFTER_PATH]\n};\n\npathStateMachine[IN_PATH] = {\n  'ws': [IN_PATH],\n  '.': [BEFORE_IDENT],\n  '[': [IN_SUB_PATH],\n  'eof': [AFTER_PATH]\n};\n\npathStateMachine[BEFORE_IDENT] = {\n  'ws': [BEFORE_IDENT],\n  'ident': [IN_IDENT, APPEND],\n  '0': [IN_IDENT, APPEND],\n  'number': [IN_IDENT, APPEND]\n};\n\npathStateMachine[IN_IDENT] = {\n  'ident': [IN_IDENT, APPEND],\n  '0': [IN_IDENT, APPEND],\n  'number': [IN_IDENT, APPEND],\n  'ws': [IN_PATH, PUSH],\n  '.': [BEFORE_IDENT, PUSH],\n  '[': [IN_SUB_PATH, PUSH],\n  'eof': [AFTER_PATH, PUSH]\n};\n\npathStateMachine[IN_SUB_PATH] = {\n  \"'\": [IN_SINGLE_QUOTE, APPEND],\n  '\"': [IN_DOUBLE_QUOTE, APPEND],\n  '[': [IN_SUB_PATH, INC_SUB_PATH_DEPTH],\n  ']': [IN_PATH, PUSH_SUB_PATH],\n  'eof': ERROR,\n  'else': [IN_SUB_PATH, APPEND]\n};\n\npathStateMachine[IN_SINGLE_QUOTE] = {\n  \"'\": [IN_SUB_PATH, APPEND],\n  'eof': ERROR,\n  'else': [IN_SINGLE_QUOTE, APPEND]\n};\n\npathStateMachine[IN_DOUBLE_QUOTE] = {\n  '\"': [IN_SUB_PATH, APPEND],\n  'eof': ERROR,\n  'else': [IN_DOUBLE_QUOTE, APPEND]\n};\n\n/**\n * Check if an expression is a literal value.\n */\n\nvar literalValueRE = /^\\s?(?:true|false|-?[\\d.]+|'[^']*'|\"[^\"]*\")\\s?$/;\nfunction isLiteral (exp) {\n  return literalValueRE.test(exp)\n}\n\n/**\n * Strip quotes from a string\n */\n\nfunction stripQuotes (str) {\n  var a = str.charCodeAt(0);\n  var b = str.charCodeAt(str.length - 1);\n  return a === b && (a === 0x22 || a === 0x27)\n    ? str.slice(1, -1)\n    : str\n}\n\n/**\n * Determine the type of a character in a keypath.\n */\n\nfunction getPathCharType (ch) {\n  if (ch === undefined || ch === null) { return 'eof' }\n\n  var code = ch.charCodeAt(0);\n\n  switch (code) {\n    case 0x5B: // [\n    case 0x5D: // ]\n    case 0x2E: // .\n    case 0x22: // \"\n    case 0x27: // '\n      return ch\n\n    case 0x5F: // _\n    case 0x24: // $\n    case 0x2D: // -\n      return 'ident'\n\n    case 0x09: // Tab\n    case 0x0A: // Newline\n    case 0x0D: // Return\n    case 0xA0:  // No-break space\n    case 0xFEFF:  // Byte Order Mark\n    case 0x2028:  // Line Separator\n    case 0x2029:  // Paragraph Separator\n      return 'ws'\n  }\n\n  return 'ident'\n}\n\n/**\n * Format a subPath, return its plain form if it is\n * a literal string or number. Otherwise prepend the\n * dynamic indicator (*).\n */\n\nfunction formatSubPath (path) {\n  var trimmed = path.trim();\n  // invalid leading 0\n  if (path.charAt(0) === '0' && isNaN(path)) { return false }\n\n  return isLiteral(trimmed) ? stripQuotes(trimmed) : '*' + trimmed\n}\n\n/**\n * Parse a string path into an array of segments\n */\n\nfunction parse$1 (path) {\n  var keys = [];\n  var index = -1;\n  var mode = BEFORE_PATH;\n  var subPathDepth = 0;\n  var c;\n  var key;\n  var newChar;\n  var type;\n  var transition;\n  var action;\n  var typeMap;\n  var actions = [];\n\n  actions[PUSH] = function () {\n    if (key !== undefined) {\n      keys.push(key);\n      key = undefined;\n    }\n  };\n\n  actions[APPEND] = function () {\n    if (key === undefined) {\n      key = newChar;\n    } else {\n      key += newChar;\n    }\n  };\n\n  actions[INC_SUB_PATH_DEPTH] = function () {\n    actions[APPEND]();\n    subPathDepth++;\n  };\n\n  actions[PUSH_SUB_PATH] = function () {\n    if (subPathDepth > 0) {\n      subPathDepth--;\n      mode = IN_SUB_PATH;\n      actions[APPEND]();\n    } else {\n      subPathDepth = 0;\n      if (key === undefined) { return false }\n      key = formatSubPath(key);\n      if (key === false) {\n        return false\n      } else {\n        actions[PUSH]();\n      }\n    }\n  };\n\n  function maybeUnescapeQuote () {\n    var nextChar = path[index + 1];\n    if ((mode === IN_SINGLE_QUOTE && nextChar === \"'\") ||\n      (mode === IN_DOUBLE_QUOTE && nextChar === '\"')) {\n      index++;\n      newChar = '\\\\' + nextChar;\n      actions[APPEND]();\n      return true\n    }\n  }\n\n  while (mode !== null) {\n    index++;\n    c = path[index];\n\n    if (c === '\\\\' && maybeUnescapeQuote()) {\n      continue\n    }\n\n    type = getPathCharType(c);\n    typeMap = pathStateMachine[mode];\n    transition = typeMap[type] || typeMap['else'] || ERROR;\n\n    if (transition === ERROR) {\n      return // parse error\n    }\n\n    mode = transition[0];\n    action = actions[transition[1]];\n    if (action) {\n      newChar = transition[2];\n      newChar = newChar === undefined\n        ? c\n        : newChar;\n      if (action() === false) {\n        return\n      }\n    }\n\n    if (mode === AFTER_PATH) {\n      return keys\n    }\n  }\n}\n\n\n\n\n\nvar I18nPath = function I18nPath () {\n  this._cache = Object.create(null);\n};\n\n/**\n * External parse that check for a cache hit first\n */\nI18nPath.prototype.parsePath = function parsePath (path) {\n  var hit = this._cache[path];\n  if (!hit) {\n    hit = parse$1(path);\n    if (hit) {\n      this._cache[path] = hit;\n    }\n  }\n  return hit || []\n};\n\n/**\n * Get path value from path string\n */\nI18nPath.prototype.getPathValue = function getPathValue (obj, path) {\n  if (!isObject(obj)) { return null }\n\n  var paths = this.parsePath(path);\n  if (paths.length === 0) {\n    return null\n  } else {\n    var length = paths.length;\n    var last = obj;\n    var i = 0;\n    while (i < length) {\n      var value = last[paths[i]];\n      if (value === undefined || value === null) {\n        return null\n      }\n      last = value;\n      i++;\n    }\n\n    return last\n  }\n};\n\n/*  */\n\n\n\nvar htmlTagMatcher = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\nvar linkKeyMatcher = /(?:@(?:\\.[a-zA-Z]+)?:(?:[\\w\\-_|./]+|\\([\\w\\-_:|./]+\\)))/g;\nvar linkKeyPrefixMatcher = /^@(?:\\.([a-zA-Z]+))?:/;\nvar bracketsMatcher = /[()]/g;\nvar defaultModifiers = {\n  'upper': function (str) { return str.toLocaleUpperCase(); },\n  'lower': function (str) { return str.toLocaleLowerCase(); },\n  'capitalize': function (str) { return (\"\" + (str.charAt(0).toLocaleUpperCase()) + (str.substr(1))); }\n};\n\nvar defaultFormatter = new BaseFormatter();\n\nvar VueI18n = function VueI18n (options) {\n  var this$1 = this;\n  if ( options === void 0 ) options = {};\n\n  // Auto install if it is not done yet and `window` has `Vue`.\n  // To allow users to avoid auto-installation in some cases,\n  // this code should be placed here. See #290\n  /* istanbul ignore if */\n  if (!Vue && typeof window !== 'undefined' && window.Vue) {\n    install(window.Vue);\n  }\n\n  var locale = options.locale || 'en-US';\n  var fallbackLocale = options.fallbackLocale === false\n    ? false\n    : options.fallbackLocale || 'en-US';\n  var messages = options.messages || {};\n  var dateTimeFormats = options.dateTimeFormats || options.datetimeFormats || {};\n  var numberFormats = options.numberFormats || {};\n\n  this._vm = null;\n  this._formatter = options.formatter || defaultFormatter;\n  this._modifiers = options.modifiers || {};\n  this._missing = options.missing || null;\n  this._root = options.root || null;\n  this._sync = options.sync === undefined ? true : !!options.sync;\n  this._fallbackRoot = options.fallbackRoot === undefined\n    ? true\n    : !!options.fallbackRoot;\n  this._fallbackRootWithEmptyString = options.fallbackRootWithEmptyString === undefined\n    ? true\n    : !!options.fallbackRootWithEmptyString;\n  this._formatFallbackMessages = options.formatFallbackMessages === undefined\n    ? false\n    : !!options.formatFallbackMessages;\n  this._silentTranslationWarn = options.silentTranslationWarn === undefined\n    ? false\n    : options.silentTranslationWarn;\n  this._silentFallbackWarn = options.silentFallbackWarn === undefined\n    ? false\n    : !!options.silentFallbackWarn;\n  this._dateTimeFormatters = {};\n  this._numberFormatters = {};\n  this._path = new I18nPath();\n  this._dataListeners = new Set();\n  this._componentInstanceCreatedListener = options.componentInstanceCreatedListener || null;\n  this._preserveDirectiveContent = options.preserveDirectiveContent === undefined\n    ? false\n    : !!options.preserveDirectiveContent;\n  this.pluralizationRules = options.pluralizationRules || {};\n  this._warnHtmlInMessage = options.warnHtmlInMessage || 'off';\n  this._postTranslation = options.postTranslation || null;\n  this._escapeParameterHtml = options.escapeParameterHtml || false;\n\n  if ('__VUE_I18N_BRIDGE__' in options) {\n    this.__VUE_I18N_BRIDGE__ = options.__VUE_I18N_BRIDGE__;\n  }\n\n  /**\n   * @param choice {number} a choice index given by the input to $tc: `$tc('path.to.rule', choiceIndex)`\n   * @param choicesLength {number} an overall amount of available choices\n   * @returns a final choice index\n  */\n  this.getChoiceIndex = function (choice, choicesLength) {\n    var thisPrototype = Object.getPrototypeOf(this$1);\n    if (thisPrototype && thisPrototype.getChoiceIndex) {\n      var prototypeGetChoiceIndex = (thisPrototype.getChoiceIndex);\n      return (prototypeGetChoiceIndex).call(this$1, choice, choicesLength)\n    }\n\n    // Default (old) getChoiceIndex implementation - english-compatible\n    var defaultImpl = function (_choice, _choicesLength) {\n      _choice = Math.abs(_choice);\n\n      if (_choicesLength === 2) {\n        return _choice\n          ? _choice > 1\n            ? 1\n            : 0\n          : 1\n      }\n\n      return _choice ? Math.min(_choice, 2) : 0\n    };\n\n    if (this$1.locale in this$1.pluralizationRules) {\n      return this$1.pluralizationRules[this$1.locale].apply(this$1, [choice, choicesLength])\n    } else {\n      return defaultImpl(choice, choicesLength)\n    }\n  };\n\n\n  this._exist = function (message, key) {\n    if (!message || !key) { return false }\n    if (!isNull(this$1._path.getPathValue(message, key))) { return true }\n    // fallback for flat key\n    if (message[key]) { return true }\n    return false\n  };\n\n  if (this._warnHtmlInMessage === 'warn' || this._warnHtmlInMessage === 'error') {\n    Object.keys(messages).forEach(function (locale) {\n      this$1._checkLocaleMessage(locale, this$1._warnHtmlInMessage, messages[locale]);\n    });\n  }\n\n  this._initVM({\n    locale: locale,\n    fallbackLocale: fallbackLocale,\n    messages: messages,\n    dateTimeFormats: dateTimeFormats,\n    numberFormats: numberFormats\n  });\n};\n\nvar prototypeAccessors = { vm: { configurable: true },messages: { configurable: true },dateTimeFormats: { configurable: true },numberFormats: { configurable: true },availableLocales: { configurable: true },locale: { configurable: true },fallbackLocale: { configurable: true },formatFallbackMessages: { configurable: true },missing: { configurable: true },formatter: { configurable: true },silentTranslationWarn: { configurable: true },silentFallbackWarn: { configurable: true },preserveDirectiveContent: { configurable: true },warnHtmlInMessage: { configurable: true },postTranslation: { configurable: true },sync: { configurable: true } };\n\nVueI18n.prototype._checkLocaleMessage = function _checkLocaleMessage (locale, level, message) {\n  var paths = [];\n\n  var fn = function (level, locale, message, paths) {\n    if (isPlainObject(message)) {\n      Object.keys(message).forEach(function (key) {\n        var val = message[key];\n        if (isPlainObject(val)) {\n          paths.push(key);\n          paths.push('.');\n          fn(level, locale, val, paths);\n          paths.pop();\n          paths.pop();\n        } else {\n          paths.push(key);\n          fn(level, locale, val, paths);\n          paths.pop();\n        }\n      });\n    } else if (isArray(message)) {\n      message.forEach(function (item, index) {\n        if (isPlainObject(item)) {\n          paths.push((\"[\" + index + \"]\"));\n          paths.push('.');\n          fn(level, locale, item, paths);\n          paths.pop();\n          paths.pop();\n        } else {\n          paths.push((\"[\" + index + \"]\"));\n          fn(level, locale, item, paths);\n          paths.pop();\n        }\n      });\n    } else if (isString(message)) {\n      var ret = htmlTagMatcher.test(message);\n      if (ret) {\n        var msg = \"Detected HTML in message '\" + message + \"' of keypath '\" + (paths.join('')) + \"' at '\" + locale + \"'. Consider component interpolation with '<i18n>' to avoid XSS. See https://bit.ly/2ZqJzkp\";\n        if (level === 'warn') {\n          warn(msg);\n        } else if (level === 'error') {\n          error(msg);\n        }\n      }\n    }\n  };\n\n  fn(level, locale, message, paths);\n};\n\nVueI18n.prototype._initVM = function _initVM (data) {\n  var silent = Vue.config.silent;\n  Vue.config.silent = true;\n  this._vm = new Vue({ data: data, __VUE18N__INSTANCE__: true });\n  Vue.config.silent = silent;\n};\n\nVueI18n.prototype.destroyVM = function destroyVM () {\n  this._vm.$destroy();\n};\n\nVueI18n.prototype.subscribeDataChanging = function subscribeDataChanging (vm) {\n  this._dataListeners.add(vm);\n};\n\nVueI18n.prototype.unsubscribeDataChanging = function unsubscribeDataChanging (vm) {\n  remove(this._dataListeners, vm);\n};\n\nVueI18n.prototype.watchI18nData = function watchI18nData () {\n    var this$1 = this;\n  return this._vm.$watch('$data', function () {\n    var listeners = arrayFrom(this$1._dataListeners);\n    var i = listeners.length;\n    while(i--) {\n      Vue.nextTick(function () {\n        listeners[i] && listeners[i].$forceUpdate();\n      });\n    }\n  }, { deep: true })\n};\n\nVueI18n.prototype.watchLocale = function watchLocale (composer) {\n  if (!composer) {\n    /* istanbul ignore if */\n    if (!this._sync || !this._root) { return null }\n    var target = this._vm;\n    return this._root.$i18n.vm.$watch('locale', function (val) {\n      target.$set(target, 'locale', val);\n      target.$forceUpdate();\n    }, { immediate: true })\n  } else {\n    // deal with vue-i18n-bridge\n    if (!this.__VUE_I18N_BRIDGE__) { return null }\n    var self = this;\n    var target$1 = this._vm;\n    return this.vm.$watch('locale', function (val) {\n      target$1.$set(target$1, 'locale', val);\n      if (self.__VUE_I18N_BRIDGE__ && composer) {\n        composer.locale.value = val;\n      }\n      target$1.$forceUpdate();\n    }, { immediate: true })\n  }\n};\n\nVueI18n.prototype.onComponentInstanceCreated = function onComponentInstanceCreated (newI18n) {\n  if (this._componentInstanceCreatedListener) {\n    this._componentInstanceCreatedListener(newI18n, this);\n  }\n};\n\nprototypeAccessors.vm.get = function () { return this._vm };\n\nprototypeAccessors.messages.get = function () { return looseClone(this._getMessages()) };\nprototypeAccessors.dateTimeFormats.get = function () { return looseClone(this._getDateTimeFormats()) };\nprototypeAccessors.numberFormats.get = function () { return looseClone(this._getNumberFormats()) };\nprototypeAccessors.availableLocales.get = function () { return Object.keys(this.messages).sort() };\n\nprototypeAccessors.locale.get = function () { return this._vm.locale };\nprototypeAccessors.locale.set = function (locale) {\n  this._vm.$set(this._vm, 'locale', locale);\n};\n\nprototypeAccessors.fallbackLocale.get = function () { return this._vm.fallbackLocale };\nprototypeAccessors.fallbackLocale.set = function (locale) {\n  this._localeChainCache = {};\n  this._vm.$set(this._vm, 'fallbackLocale', locale);\n};\n\nprototypeAccessors.formatFallbackMessages.get = function () { return this._formatFallbackMessages };\nprototypeAccessors.formatFallbackMessages.set = function (fallback) { this._formatFallbackMessages = fallback; };\n\nprototypeAccessors.missing.get = function () { return this._missing };\nprototypeAccessors.missing.set = function (handler) { this._missing = handler; };\n\nprototypeAccessors.formatter.get = function () { return this._formatter };\nprototypeAccessors.formatter.set = function (formatter) { this._formatter = formatter; };\n\nprototypeAccessors.silentTranslationWarn.get = function () { return this._silentTranslationWarn };\nprototypeAccessors.silentTranslationWarn.set = function (silent) { this._silentTranslationWarn = silent; };\n\nprototypeAccessors.silentFallbackWarn.get = function () { return this._silentFallbackWarn };\nprototypeAccessors.silentFallbackWarn.set = function (silent) { this._silentFallbackWarn = silent; };\n\nprototypeAccessors.preserveDirectiveContent.get = function () { return this._preserveDirectiveContent };\nprototypeAccessors.preserveDirectiveContent.set = function (preserve) { this._preserveDirectiveContent = preserve; };\n\nprototypeAccessors.warnHtmlInMessage.get = function () { return this._warnHtmlInMessage };\nprototypeAccessors.warnHtmlInMessage.set = function (level) {\n    var this$1 = this;\n\n  var orgLevel = this._warnHtmlInMessage;\n  this._warnHtmlInMessage = level;\n  if (orgLevel !== level && (level === 'warn' || level === 'error')) {\n    var messages = this._getMessages();\n    Object.keys(messages).forEach(function (locale) {\n      this$1._checkLocaleMessage(locale, this$1._warnHtmlInMessage, messages[locale]);\n    });\n  }\n};\n\nprototypeAccessors.postTranslation.get = function () { return this._postTranslation };\nprototypeAccessors.postTranslation.set = function (handler) { this._postTranslation = handler; };\n\nprototypeAccessors.sync.get = function () { return this._sync };\nprototypeAccessors.sync.set = function (val) { this._sync = val; };\n\nVueI18n.prototype._getMessages = function _getMessages () { return this._vm.messages };\nVueI18n.prototype._getDateTimeFormats = function _getDateTimeFormats () { return this._vm.dateTimeFormats };\nVueI18n.prototype._getNumberFormats = function _getNumberFormats () { return this._vm.numberFormats };\n\nVueI18n.prototype._warnDefault = function _warnDefault (locale, key, result, vm, values, interpolateMode) {\n  if (!isNull(result)) { return result }\n  if (this._missing) {\n    var missingRet = this._missing.apply(null, [locale, key, vm, values]);\n    if (isString(missingRet)) {\n      return missingRet\n    }\n  } else {\n    if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key)) {\n      warn(\n        \"Cannot translate the value of keypath '\" + key + \"'. \" +\n        'Use the value of keypath as default.'\n      );\n    }\n  }\n\n  if (this._formatFallbackMessages) {\n    var parsedArgs = parseArgs.apply(void 0, values);\n    return this._render(key, interpolateMode, parsedArgs.params, key)\n  } else {\n    return key\n  }\n};\n\nVueI18n.prototype._isFallbackRoot = function _isFallbackRoot (val) {\n  return (this._fallbackRootWithEmptyString? !val : isNull(val)) && !isNull(this._root) && this._fallbackRoot\n};\n\nVueI18n.prototype._isSilentFallbackWarn = function _isSilentFallbackWarn (key) {\n  return this._silentFallbackWarn instanceof RegExp\n    ? this._silentFallbackWarn.test(key)\n    : this._silentFallbackWarn\n};\n\nVueI18n.prototype._isSilentFallback = function _isSilentFallback (locale, key) {\n  return this._isSilentFallbackWarn(key) && (this._isFallbackRoot() || locale !== this.fallbackLocale)\n};\n\nVueI18n.prototype._isSilentTranslationWarn = function _isSilentTranslationWarn (key) {\n  return this._silentTranslationWarn instanceof RegExp\n    ? this._silentTranslationWarn.test(key)\n    : this._silentTranslationWarn\n};\n\nVueI18n.prototype._interpolate = function _interpolate (\n  locale,\n  message,\n  key,\n  host,\n  interpolateMode,\n  values,\n  visitedLinkStack\n) {\n  if (!message) { return null }\n\n  var pathRet = this._path.getPathValue(message, key);\n  if (isArray(pathRet) || isPlainObject(pathRet)) { return pathRet }\n\n  var ret;\n  if (isNull(pathRet)) {\n    /* istanbul ignore else */\n    if (isPlainObject(message)) {\n      ret = message[key];\n      if (!(isString(ret) || isFunction(ret))) {\n        if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallback(locale, key)) {\n          warn((\"Value of key '\" + key + \"' is not a string or function !\"));\n        }\n        return null\n      }\n    } else {\n      return null\n    }\n  } else {\n    /* istanbul ignore else */\n    if (isString(pathRet) || isFunction(pathRet)) {\n      ret = pathRet;\n    } else {\n      if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallback(locale, key)) {\n        warn((\"Value of key '\" + key + \"' is not a string or function!\"));\n      }\n      return null\n    }\n  }\n\n  // Check for the existence of links within the translated string\n  if (isString(ret) && (ret.indexOf('@:') >= 0 || ret.indexOf('@.') >= 0)) {\n    ret = this._link(locale, message, ret, host, 'raw', values, visitedLinkStack);\n  }\n\n  return this._render(ret, interpolateMode, values, key)\n};\n\nVueI18n.prototype._link = function _link (\n  locale,\n  message,\n  str,\n  host,\n  interpolateMode,\n  values,\n  visitedLinkStack\n) {\n  var ret = str;\n\n  // Match all the links within the local\n  // We are going to replace each of\n  // them with its translation\n  var matches = ret.match(linkKeyMatcher);\n\n  // eslint-disable-next-line no-autofix/prefer-const\n  for (var idx in matches) {\n    // ie compatible: filter custom array\n    // prototype method\n    if (!matches.hasOwnProperty(idx)) {\n      continue\n    }\n    var link = matches[idx];\n    var linkKeyPrefixMatches = link.match(linkKeyPrefixMatcher);\n    var linkPrefix = linkKeyPrefixMatches[0];\n      var formatterName = linkKeyPrefixMatches[1];\n\n    // Remove the leading @:, @.case: and the brackets\n    var linkPlaceholder = link.replace(linkPrefix, '').replace(bracketsMatcher, '');\n\n    if (includes(visitedLinkStack, linkPlaceholder)) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn((\"Circular reference found. \\\"\" + link + \"\\\" is already visited in the chain of \" + (visitedLinkStack.reverse().join(' <- '))));\n      }\n      return ret\n    }\n    visitedLinkStack.push(linkPlaceholder);\n\n    // Translate the link\n    var translated = this._interpolate(\n      locale, message, linkPlaceholder, host,\n      interpolateMode === 'raw' ? 'string' : interpolateMode,\n      interpolateMode === 'raw' ? undefined : values,\n      visitedLinkStack\n    );\n\n    if (this._isFallbackRoot(translated)) {\n      if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(linkPlaceholder)) {\n        warn((\"Fall back to translate the link placeholder '\" + linkPlaceholder + \"' with root locale.\"));\n      }\n      /* istanbul ignore if */\n      if (!this._root) { throw Error('unexpected error') }\n      var root = this._root.$i18n;\n      translated = root._translate(\n        root._getMessages(), root.locale, root.fallbackLocale,\n        linkPlaceholder, host, interpolateMode, values\n      );\n    }\n    translated = this._warnDefault(\n      locale, linkPlaceholder, translated, host,\n      isArray(values) ? values : [values],\n      interpolateMode\n    );\n\n    if (this._modifiers.hasOwnProperty(formatterName)) {\n      translated = this._modifiers[formatterName](translated);\n    } else if (defaultModifiers.hasOwnProperty(formatterName)) {\n      translated = defaultModifiers[formatterName](translated);\n    }\n\n    visitedLinkStack.pop();\n\n    // Replace the link with the translated\n    ret = !translated ? ret : ret.replace(link, translated);\n  }\n\n  return ret\n};\n\nVueI18n.prototype._createMessageContext = function _createMessageContext (values, formatter, path, interpolateMode) {\n    var this$1 = this;\n\n  var _list = isArray(values) ? values : [];\n  var _named = isObject(values) ? values : {};\n  var list = function (index) { return _list[index]; };\n  var named = function (key) { return _named[key]; };\n  var messages = this._getMessages();\n  var locale = this.locale;\n\n  return {\n    list: list,\n    named: named,\n    values: values,\n    formatter: formatter,\n    path: path,\n    messages: messages,\n    locale: locale,\n    linked: function (linkedKey) { return this$1._interpolate(locale, messages[locale] || {}, linkedKey, null, interpolateMode, undefined, [linkedKey]); }\n  }\n};\n\nVueI18n.prototype._render = function _render (message, interpolateMode, values, path) {\n  if (isFunction(message)) {\n    return message(\n      this._createMessageContext(values, this._formatter || defaultFormatter, path, interpolateMode)\n    )\n  }\n\n  var ret = this._formatter.interpolate(message, values, path);\n\n  // If the custom formatter refuses to work - apply the default one\n  if (!ret) {\n    ret = defaultFormatter.interpolate(message, values, path);\n  }\n\n  // if interpolateMode is **not** 'string' ('row'),\n  // return the compiled data (e.g. ['foo', VNode, 'bar']) with formatter\n  return interpolateMode === 'string' && !isString(ret) ? ret.join('') : ret\n};\n\nVueI18n.prototype._appendItemToChain = function _appendItemToChain (chain, item, blocks) {\n  var follow = false;\n  if (!includes(chain, item)) {\n    follow = true;\n    if (item) {\n      follow = item[item.length - 1] !== '!';\n      item = item.replace(/!/g, '');\n      chain.push(item);\n      if (blocks && blocks[item]) {\n        follow = blocks[item];\n      }\n    }\n  }\n  return follow\n};\n\nVueI18n.prototype._appendLocaleToChain = function _appendLocaleToChain (chain, locale, blocks) {\n  var follow;\n  var tokens = locale.split('-');\n  do {\n    var item = tokens.join('-');\n    follow = this._appendItemToChain(chain, item, blocks);\n    tokens.splice(-1, 1);\n  } while (tokens.length && (follow === true))\n  return follow\n};\n\nVueI18n.prototype._appendBlockToChain = function _appendBlockToChain (chain, block, blocks) {\n  var follow = true;\n  for (var i = 0; (i < block.length) && (isBoolean(follow)); i++) {\n    var locale = block[i];\n    if (isString(locale)) {\n      follow = this._appendLocaleToChain(chain, locale, blocks);\n    }\n  }\n  return follow\n};\n\nVueI18n.prototype._getLocaleChain = function _getLocaleChain (start, fallbackLocale) {\n  if (start === '') { return [] }\n\n  if (!this._localeChainCache) {\n    this._localeChainCache = {};\n  }\n\n  var chain = this._localeChainCache[start];\n  if (!chain) {\n    if (!fallbackLocale) {\n      fallbackLocale = this.fallbackLocale;\n    }\n    chain = [];\n\n    // first block defined by start\n    var block = [start];\n\n    // while any intervening block found\n    while (isArray(block)) {\n      block = this._appendBlockToChain(\n        chain,\n        block,\n        fallbackLocale\n      );\n    }\n\n    // last block defined by default\n    var defaults;\n    if (isArray(fallbackLocale)) {\n      defaults = fallbackLocale;\n    } else if (isObject(fallbackLocale)) {\n      /* $FlowFixMe */\n      if (fallbackLocale['default']) {\n        defaults = fallbackLocale['default'];\n      } else {\n        defaults = null;\n      }\n    } else {\n      defaults = fallbackLocale;\n    }\n\n    // convert defaults to array\n    if (isString(defaults)) {\n      block = [defaults];\n    } else {\n      block = defaults;\n    }\n    if (block) {\n      this._appendBlockToChain(\n        chain,\n        block,\n        null\n      );\n    }\n    this._localeChainCache[start] = chain;\n  }\n  return chain\n};\n\nVueI18n.prototype._translate = function _translate (\n  messages,\n  locale,\n  fallback,\n  key,\n  host,\n  interpolateMode,\n  args\n) {\n  var chain = this._getLocaleChain(locale, fallback);\n  var res;\n  for (var i = 0; i < chain.length; i++) {\n    var step = chain[i];\n    res =\n      this._interpolate(step, messages[step], key, host, interpolateMode, args, [key]);\n    if (!isNull(res)) {\n      if (step !== locale && process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallbackWarn(key)) {\n        warn((\"Fall back to translate the keypath '\" + key + \"' with '\" + step + \"' locale.\"));\n      }\n      return res\n    }\n  }\n  return null\n};\n\nVueI18n.prototype._t = function _t (key, _locale, messages, host) {\n    var ref;\n\n    var values = [], len = arguments.length - 4;\n    while ( len-- > 0 ) values[ len ] = arguments[ len + 4 ];\n  if (!key) { return '' }\n\n  var parsedArgs = parseArgs.apply(void 0, values);\n  if(this._escapeParameterHtml) {\n    parsedArgs.params = escapeParams(parsedArgs.params);\n  }\n\n  var locale = parsedArgs.locale || _locale;\n\n  var ret = this._translate(\n    messages, locale, this.fallbackLocale, key,\n    host, 'string', parsedArgs.params\n  );\n  if (this._isFallbackRoot(ret)) {\n    if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallbackWarn(key)) {\n      warn((\"Fall back to translate the keypath '\" + key + \"' with root locale.\"));\n    }\n    /* istanbul ignore if */\n    if (!this._root) { throw Error('unexpected error') }\n    return (ref = this._root).$t.apply(ref, [ key ].concat( values ))\n  } else {\n    ret = this._warnDefault(locale, key, ret, host, values, 'string');\n    if (this._postTranslation && ret !== null && ret !== undefined) {\n      ret = this._postTranslation(ret, key);\n    }\n    return ret\n  }\n};\n\nVueI18n.prototype.t = function t (key) {\n    var ref;\n\n    var values = [], len = arguments.length - 1;\n    while ( len-- > 0 ) values[ len ] = arguments[ len + 1 ];\n  return (ref = this)._t.apply(ref, [ key, this.locale, this._getMessages(), null ].concat( values ))\n};\n\nVueI18n.prototype._i = function _i (key, locale, messages, host, values) {\n  var ret =\n    this._translate(messages, locale, this.fallbackLocale, key, host, 'raw', values);\n  if (this._isFallbackRoot(ret)) {\n    if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key)) {\n      warn((\"Fall back to interpolate the keypath '\" + key + \"' with root locale.\"));\n    }\n    if (!this._root) { throw Error('unexpected error') }\n    return this._root.$i18n.i(key, locale, values)\n  } else {\n    return this._warnDefault(locale, key, ret, host, [values], 'raw')\n  }\n};\n\nVueI18n.prototype.i = function i (key, locale, values) {\n  /* istanbul ignore if */\n  if (!key) { return '' }\n\n  if (!isString(locale)) {\n    locale = this.locale;\n  }\n\n  return this._i(key, locale, this._getMessages(), null, values)\n};\n\nVueI18n.prototype._tc = function _tc (\n  key,\n  _locale,\n  messages,\n  host,\n  choice\n) {\n    var ref;\n\n    var values = [], len = arguments.length - 5;\n    while ( len-- > 0 ) values[ len ] = arguments[ len + 5 ];\n  if (!key) { return '' }\n  if (choice === undefined) {\n    choice = 1;\n  }\n\n  var predefined = { 'count': choice, 'n': choice };\n  var parsedArgs = parseArgs.apply(void 0, values);\n  parsedArgs.params = Object.assign(predefined, parsedArgs.params);\n  values = parsedArgs.locale === null ? [parsedArgs.params] : [parsedArgs.locale, parsedArgs.params];\n  return this.fetchChoice((ref = this)._t.apply(ref, [ key, _locale, messages, host ].concat( values )), choice)\n};\n\nVueI18n.prototype.fetchChoice = function fetchChoice (message, choice) {\n  /* istanbul ignore if */\n  if (!message || !isString(message)) { return null }\n  var choices = message.split('|');\n\n  choice = this.getChoiceIndex(choice, choices.length);\n  if (!choices[choice]) { return message }\n  return choices[choice].trim()\n};\n\nVueI18n.prototype.tc = function tc (key, choice) {\n    var ref;\n\n    var values = [], len = arguments.length - 2;\n    while ( len-- > 0 ) values[ len ] = arguments[ len + 2 ];\n  return (ref = this)._tc.apply(ref, [ key, this.locale, this._getMessages(), null, choice ].concat( values ))\n};\n\nVueI18n.prototype._te = function _te (key, locale, messages) {\n    var args = [], len = arguments.length - 3;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 3 ];\n\n  var _locale = parseArgs.apply(void 0, args).locale || locale;\n  return this._exist(messages[_locale], key)\n};\n\nVueI18n.prototype.te = function te (key, locale) {\n  return this._te(key, this.locale, this._getMessages(), locale)\n};\n\nVueI18n.prototype.getLocaleMessage = function getLocaleMessage (locale) {\n  return looseClone(this._vm.messages[locale] || {})\n};\n\nVueI18n.prototype.setLocaleMessage = function setLocaleMessage (locale, message) {\n  if (this._warnHtmlInMessage === 'warn' || this._warnHtmlInMessage === 'error') {\n    this._checkLocaleMessage(locale, this._warnHtmlInMessage, message);\n  }\n  this._vm.$set(this._vm.messages, locale, message);\n};\n\nVueI18n.prototype.mergeLocaleMessage = function mergeLocaleMessage (locale, message) {\n  if (this._warnHtmlInMessage === 'warn' || this._warnHtmlInMessage === 'error') {\n    this._checkLocaleMessage(locale, this._warnHtmlInMessage, message);\n  }\n  this._vm.$set(this._vm.messages, locale, merge(\n    typeof this._vm.messages[locale] !== 'undefined' && Object.keys(this._vm.messages[locale]).length\n      ? Object.assign({}, this._vm.messages[locale])\n      : {},\n    message\n  ));\n};\n\nVueI18n.prototype.getDateTimeFormat = function getDateTimeFormat (locale) {\n  return looseClone(this._vm.dateTimeFormats[locale] || {})\n};\n\nVueI18n.prototype.setDateTimeFormat = function setDateTimeFormat (locale, format) {\n  this._vm.$set(this._vm.dateTimeFormats, locale, format);\n  this._clearDateTimeFormat(locale, format);\n};\n\nVueI18n.prototype.mergeDateTimeFormat = function mergeDateTimeFormat (locale, format) {\n  this._vm.$set(this._vm.dateTimeFormats, locale, merge(this._vm.dateTimeFormats[locale] || {}, format));\n  this._clearDateTimeFormat(locale, format);\n};\n\nVueI18n.prototype._clearDateTimeFormat = function _clearDateTimeFormat (locale, format) {\n  // eslint-disable-next-line no-autofix/prefer-const\n  for (var key in format) {\n    var id = locale + \"__\" + key;\n\n    if (!this._dateTimeFormatters.hasOwnProperty(id)) {\n      continue\n    }\n\n    delete this._dateTimeFormatters[id];\n  }\n};\n\nVueI18n.prototype._localizeDateTime = function _localizeDateTime (\n  value,\n  locale,\n  fallback,\n  dateTimeFormats,\n  key,\n  options\n) {\n  var _locale = locale;\n  var formats = dateTimeFormats[_locale];\n\n  var chain = this._getLocaleChain(locale, fallback);\n  for (var i = 0; i < chain.length; i++) {\n    var current = _locale;\n    var step = chain[i];\n    formats = dateTimeFormats[step];\n    _locale = step;\n    // fallback locale\n    if (isNull(formats) || isNull(formats[key])) {\n      if (step !== locale && process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallbackWarn(key)) {\n        warn((\"Fall back to '\" + step + \"' datetime formats from '\" + current + \"' datetime formats.\"));\n      }\n    } else {\n      break\n    }\n  }\n\n  if (isNull(formats) || isNull(formats[key])) {\n    return null\n  } else {\n    var format = formats[key];\n\n    var formatter;\n    if (options) {\n      formatter = new Intl.DateTimeFormat(_locale, Object.assign({}, format, options));\n    } else {\n      var id = _locale + \"__\" + key;\n      formatter = this._dateTimeFormatters[id];\n      if (!formatter) {\n        formatter = this._dateTimeFormatters[id] = new Intl.DateTimeFormat(_locale, format);\n      }\n    }\n\n    return formatter.format(value)\n  }\n};\n\nVueI18n.prototype._d = function _d (value, locale, key, options) {\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && !VueI18n.availabilities.dateTimeFormat) {\n    warn('Cannot format a Date value due to not supported Intl.DateTimeFormat.');\n    return ''\n  }\n\n  if (!key) {\n    var dtf = !options ? new Intl.DateTimeFormat(locale) : new Intl.DateTimeFormat(locale, options);\n    return dtf.format(value)\n  }\n\n  var ret =\n    this._localizeDateTime(value, locale, this.fallbackLocale, this._getDateTimeFormats(), key, options);\n  if (this._isFallbackRoot(ret)) {\n    if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallbackWarn(key)) {\n      warn((\"Fall back to datetime localization of root: key '\" + key + \"'.\"));\n    }\n    /* istanbul ignore if */\n    if (!this._root) { throw Error('unexpected error') }\n    return this._root.$i18n.d(value, key, locale)\n  } else {\n    return ret || ''\n  }\n};\n\nVueI18n.prototype.d = function d (value) {\n    var args = [], len = arguments.length - 1;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];\n\n  var locale = this.locale;\n  var key = null;\n  var options = null;\n\n  if (args.length === 1) {\n    if (isString(args[0])) {\n      key = args[0];\n    } else if (isObject(args[0])) {\n      if (args[0].locale) {\n        locale = args[0].locale;\n      }\n      if (args[0].key) {\n        key = args[0].key;\n      }\n    }\n\n    options = Object.keys(args[0]).reduce(function (acc, key) {\n        var obj;\n\n      if (includes(dateTimeFormatKeys, key)) {\n        return Object.assign({}, acc, ( obj = {}, obj[key] = args[0][key], obj ))\n      }\n      return acc\n    }, null);\n\n  } else if (args.length === 2) {\n    if (isString(args[0])) {\n      key = args[0];\n    }\n    if (isString(args[1])) {\n      locale = args[1];\n    }\n  }\n\n  return this._d(value, locale, key, options)\n};\n\nVueI18n.prototype.getNumberFormat = function getNumberFormat (locale) {\n  return looseClone(this._vm.numberFormats[locale] || {})\n};\n\nVueI18n.prototype.setNumberFormat = function setNumberFormat (locale, format) {\n  this._vm.$set(this._vm.numberFormats, locale, format);\n  this._clearNumberFormat(locale, format);\n};\n\nVueI18n.prototype.mergeNumberFormat = function mergeNumberFormat (locale, format) {\n  this._vm.$set(this._vm.numberFormats, locale, merge(this._vm.numberFormats[locale] || {}, format));\n  this._clearNumberFormat(locale, format);\n};\n\nVueI18n.prototype._clearNumberFormat = function _clearNumberFormat (locale, format) {\n  // eslint-disable-next-line no-autofix/prefer-const\n  for (var key in format) {\n    var id = locale + \"__\" + key;\n\n    if (!this._numberFormatters.hasOwnProperty(id)) {\n      continue\n    }\n\n    delete this._numberFormatters[id];\n  }\n};\n\nVueI18n.prototype._getNumberFormatter = function _getNumberFormatter (\n  value,\n  locale,\n  fallback,\n  numberFormats,\n  key,\n  options\n) {\n  var _locale = locale;\n  var formats = numberFormats[_locale];\n\n  var chain = this._getLocaleChain(locale, fallback);\n  for (var i = 0; i < chain.length; i++) {\n    var current = _locale;\n    var step = chain[i];\n    formats = numberFormats[step];\n    _locale = step;\n    // fallback locale\n    if (isNull(formats) || isNull(formats[key])) {\n      if (step !== locale && process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallbackWarn(key)) {\n        warn((\"Fall back to '\" + step + \"' number formats from '\" + current + \"' number formats.\"));\n      }\n    } else {\n      break\n    }\n  }\n\n  if (isNull(formats) || isNull(formats[key])) {\n    return null\n  } else {\n    var format = formats[key];\n\n    var formatter;\n    if (options) {\n      // If options specified - create one time number formatter\n      formatter = new Intl.NumberFormat(_locale, Object.assign({}, format, options));\n    } else {\n      var id = _locale + \"__\" + key;\n      formatter = this._numberFormatters[id];\n      if (!formatter) {\n        formatter = this._numberFormatters[id] = new Intl.NumberFormat(_locale, format);\n      }\n    }\n    return formatter\n  }\n};\n\nVueI18n.prototype._n = function _n (value, locale, key, options) {\n  /* istanbul ignore if */\n  if (!VueI18n.availabilities.numberFormat) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn('Cannot format a Number value due to not supported Intl.NumberFormat.');\n    }\n    return ''\n  }\n\n  if (!key) {\n    var nf = !options ? new Intl.NumberFormat(locale) : new Intl.NumberFormat(locale, options);\n    return nf.format(value)\n  }\n\n  var formatter = this._getNumberFormatter(value, locale, this.fallbackLocale, this._getNumberFormats(), key, options);\n  var ret = formatter && formatter.format(value);\n  if (this._isFallbackRoot(ret)) {\n    if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key) && !this._isSilentFallbackWarn(key)) {\n      warn((\"Fall back to number localization of root: key '\" + key + \"'.\"));\n    }\n    /* istanbul ignore if */\n    if (!this._root) { throw Error('unexpected error') }\n    return this._root.$i18n.n(value, Object.assign({}, { key: key, locale: locale }, options))\n  } else {\n    return ret || ''\n  }\n};\n\nVueI18n.prototype.n = function n (value) {\n    var args = [], len = arguments.length - 1;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];\n\n  var locale = this.locale;\n  var key = null;\n  var options = null;\n\n  if (args.length === 1) {\n    if (isString(args[0])) {\n      key = args[0];\n    } else if (isObject(args[0])) {\n      if (args[0].locale) {\n        locale = args[0].locale;\n      }\n      if (args[0].key) {\n        key = args[0].key;\n      }\n\n      // Filter out number format options only\n      options = Object.keys(args[0]).reduce(function (acc, key) {\n          var obj;\n\n        if (includes(numberFormatKeys, key)) {\n          return Object.assign({}, acc, ( obj = {}, obj[key] = args[0][key], obj ))\n        }\n        return acc\n      }, null);\n    }\n  } else if (args.length === 2) {\n    if (isString(args[0])) {\n      key = args[0];\n    }\n    if (isString(args[1])) {\n      locale = args[1];\n    }\n  }\n\n  return this._n(value, locale, key, options)\n};\n\nVueI18n.prototype._ntp = function _ntp (value, locale, key, options) {\n  /* istanbul ignore if */\n  if (!VueI18n.availabilities.numberFormat) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn('Cannot format to parts a Number value due to not supported Intl.NumberFormat.');\n    }\n    return []\n  }\n\n  if (!key) {\n    var nf = !options ? new Intl.NumberFormat(locale) : new Intl.NumberFormat(locale, options);\n    return nf.formatToParts(value)\n  }\n\n  var formatter = this._getNumberFormatter(value, locale, this.fallbackLocale, this._getNumberFormats(), key, options);\n  var ret = formatter && formatter.formatToParts(value);\n  if (this._isFallbackRoot(ret)) {\n    if (process.env.NODE_ENV !== 'production' && !this._isSilentTranslationWarn(key)) {\n      warn((\"Fall back to format number to parts of root: key '\" + key + \"' .\"));\n    }\n    /* istanbul ignore if */\n    if (!this._root) { throw Error('unexpected error') }\n    return this._root.$i18n._ntp(value, locale, key, options)\n  } else {\n    return ret || []\n  }\n};\n\nObject.defineProperties( VueI18n.prototype, prototypeAccessors );\n\nvar availabilities;\n// $FlowFixMe\nObject.defineProperty(VueI18n, 'availabilities', {\n  get: function get () {\n    if (!availabilities) {\n      var intlDefined = typeof Intl !== 'undefined';\n      availabilities = {\n        dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',\n        numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'\n      };\n    }\n\n    return availabilities\n  }\n});\n\nVueI18n.install = install;\nVueI18n.version = '8.28.2';\n\nexport default VueI18n;\n"], "mappings": ";;;AAWA,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAe;AAMjB,SAAS,KAAM,KAAK,KAAK;AACvB,MAAI,OAAO,YAAY,aAAa;AAClC,YAAQ,KAAK,gBAAgB,GAAG;AAEhC,QAAI,KAAK;AACP,cAAQ,KAAK,IAAI,KAAK;AAAA,IACxB;AAAA,EACF;AACF;AAEA,SAAS,MAAO,KAAK,KAAK;AACxB,MAAI,OAAO,YAAY,aAAa;AAClC,YAAQ,MAAM,gBAAgB,GAAG;AAEjC,QAAI,KAAK;AACP,cAAQ,MAAM,IAAI,KAAK;AAAA,IACzB;AAAA,EACF;AACF;AAEA,IAAI,UAAU,MAAM;AAEpB,SAAS,SAAU,KAAK;AACtB,SAAO,QAAQ,QAAQ,OAAO,QAAQ;AACxC;AAEA,SAAS,UAAW,KAAK;AACvB,SAAO,OAAO,QAAQ;AACxB;AAEA,SAAS,SAAU,KAAK;AACtB,SAAO,OAAO,QAAQ;AACxB;AAEA,IAAI,WAAW,OAAO,UAAU;AAChC,IAAI,gBAAgB;AACpB,SAAS,cAAe,KAAK;AAC3B,SAAO,SAAS,KAAK,GAAG,MAAM;AAChC;AAEA,SAAS,OAAQ,KAAK;AACpB,SAAO,QAAQ,QAAQ,QAAQ;AACjC;AAEA,SAAS,WAAY,KAAK;AACxB,SAAO,OAAO,QAAQ;AACxB;AAEA,SAAS,YAAa;AACpB,MAAI,OAAO,CAAC,GAAG,MAAM,UAAU;AAC/B,SAAQ,MAAQ,MAAM,GAAI,IAAI,UAAW,GAAI;AAE7C,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,SAAS,KAAK,CAAC,CAAC,KAAK,QAAQ,KAAK,CAAC,CAAC,GAAG;AACzC,eAAS,KAAK,CAAC;AAAA,IACjB,WAAW,OAAO,KAAK,CAAC,MAAM,UAAU;AACtC,eAAS,KAAK,CAAC;AAAA,IACjB;AAAA,EACF,WAAW,KAAK,WAAW,GAAG;AAC5B,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,eAAS,KAAK,CAAC;AAAA,IACjB;AAEA,QAAI,SAAS,KAAK,CAAC,CAAC,KAAK,QAAQ,KAAK,CAAC,CAAC,GAAG;AACzC,eAAS,KAAK,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,EAAE,QAAgB,OAAe;AAC1C;AAEA,SAAS,WAAY,KAAK;AACxB,SAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AACvC;AAEA,SAAS,OAAQ,KAAK,MAAM;AAC1B,MAAI,IAAI,OAAO,IAAI,GAAG;AACpB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAW,KAAK;AACvB,MAAI,MAAM,CAAC;AACX,MAAI,QAAQ,SAAU,GAAG;AAAE,WAAO,IAAI,KAAK,CAAC;AAAA,EAAG,CAAC;AAChD,SAAO;AACT;AAEA,SAAS,SAAU,KAAK,MAAM;AAC5B,SAAO,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI;AAC5B;AAEA,IAAI,iBAAiB,OAAO,UAAU;AACtC,SAAS,OAAQ,KAAK,KAAK;AACzB,SAAO,eAAe,KAAK,KAAK,GAAG;AACrC;AAEA,SAAS,MAAO,QAAQ;AACtB,MAAI,cAAc;AAElB,MAAI,SAAS,OAAO,MAAM;AAC1B,WAASA,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,QAAI,SAAS,YAAYA,EAAC;AAC1B,QAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,UAAI,MAAO;AACX,WAAK,OAAO,QAAQ;AAClB,YAAI,OAAO,QAAQ,GAAG,GAAG;AACvB,cAAI,SAAS,OAAO,GAAG,CAAC,GAAG;AACzB,mBAAO,GAAG,IAAI,MAAM,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,UAC9C,OAAO;AACL,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAY,GAAG,GAAG;AACzB,MAAI,MAAM,GAAG;AAAE,WAAO;AAAA,EAAK;AAC3B,MAAI,YAAY,SAAS,CAAC;AAC1B,MAAI,YAAY,SAAS,CAAC;AAC1B,MAAI,aAAa,WAAW;AAC1B,QAAI;AACF,UAAI,WAAW,QAAQ,CAAC;AACxB,UAAI,WAAW,QAAQ,CAAC;AACxB,UAAI,YAAY,UAAU;AACxB,eAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,SAAU,GAAGA,IAAG;AACtD,iBAAO,WAAW,GAAG,EAAEA,EAAC,CAAC;AAAA,QAC3B,CAAC;AAAA,MACH,WAAW,CAAC,YAAY,CAAC,UAAU;AACjC,YAAI,QAAQ,OAAO,KAAK,CAAC;AACzB,YAAI,QAAQ,OAAO,KAAK,CAAC;AACzB,eAAO,MAAM,WAAW,MAAM,UAAU,MAAM,MAAM,SAAU,KAAK;AACjE,iBAAO,WAAW,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,QAClC,CAAC;AAAA,MACH,OAAO;AAEL,eAAO;AAAA,MACT;AAAA,IACF,SAAS,GAAG;AAEV,aAAO;AAAA,IACT;AAAA,EACF,WAAW,CAAC,aAAa,CAAC,WAAW;AACnC,WAAO,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EAC/B,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAMA,SAAS,WAAW,SAAS;AAC3B,SAAO,QACJ,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAC3B;AAWA,SAAS,aAAa,QAAQ;AAC5B,MAAG,UAAU,MAAM;AACjB,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,UAAG,OAAO,OAAO,GAAG,KAAM,UAAU;AAClC,eAAO,GAAG,IAAI,WAAW,OAAO,GAAG,CAAC;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,SAAS,OAAQC,MAAK;AACpB,MAAI,CAACA,KAAI,UAAU,eAAe,OAAO,GAAG;AAE1C,WAAO,eAAeA,KAAI,WAAW,SAAS;AAAA,MAC5C,KAAK,SAASC,OAAO;AAAE,eAAO,KAAK;AAAA,MAAM;AAAA,IAC3C,CAAC;AAAA,EACH;AAEA,EAAAD,KAAI,UAAU,KAAK,SAAU,KAAK;AAChC,QAAI,SAAS,CAAC,GAAG,MAAM,UAAU,SAAS;AAC1C,WAAQ,QAAQ,EAAI,QAAQ,GAAI,IAAI,UAAW,MAAM,CAAE;AAEvD,QAAI,OAAO,KAAK;AAChB,WAAO,KAAK,GAAG,MAAM,MAAM,CAAE,KAAK,KAAK,QAAQ,KAAK,aAAa,GAAG,IAAK,EAAE,OAAQ,MAAO,CAAC;AAAA,EAC7F;AAEA,EAAAA,KAAI,UAAU,MAAM,SAAU,KAAK,QAAQ;AACzC,QAAI,SAAS,CAAC,GAAG,MAAM,UAAU,SAAS;AAC1C,WAAQ,QAAQ,EAAI,QAAQ,GAAI,IAAI,UAAW,MAAM,CAAE;AAEvD,QAAI,OAAO,KAAK;AAChB,WAAO,KAAK,IAAI,MAAM,MAAM,CAAE,KAAK,KAAK,QAAQ,KAAK,aAAa,GAAG,MAAM,MAAO,EAAE,OAAQ,MAAO,CAAC;AAAA,EACtG;AAEA,EAAAA,KAAI,UAAU,MAAM,SAAU,KAAK,QAAQ;AACzC,QAAI,OAAO,KAAK;AAChB,WAAO,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,aAAa,GAAG,MAAM;AAAA,EAC/D;AAEA,EAAAA,KAAI,UAAU,KAAK,SAAU,OAAO;AAClC,QAAI;AAEJ,QAAI,OAAO,CAAC,GAAG,MAAM,UAAU,SAAS;AACxC,WAAQ,QAAQ,EAAI,MAAM,GAAI,IAAI,UAAW,MAAM,CAAE;AACrD,YAAQ,MAAM,KAAK,OAAO,EAAE,MAAM,KAAK,CAAE,KAAM,EAAE,OAAQ,IAAK,CAAC;AAAA,EACjE;AAEA,EAAAA,KAAI,UAAU,KAAK,SAAU,OAAO;AAClC,QAAI;AAEJ,QAAI,OAAO,CAAC,GAAG,MAAM,UAAU,SAAS;AACxC,WAAQ,QAAQ,EAAI,MAAM,GAAI,IAAI,UAAW,MAAM,CAAE;AACrD,YAAQ,MAAM,KAAK,OAAO,EAAE,MAAM,KAAK,CAAE,KAAM,EAAE,OAAQ,IAAK,CAAC;AAAA,EACjE;AACF;AAUA,SAAS,YAAa,QAAQ;AAC5B,MAAK,WAAW,OAAS,UAAS;AAElC,WAAS,UAAW;AAClB,QAAI,SAAS,KAAK,SAAS,KAAK,SAAS,oBAAoB,KAAK,KAAK;AACrE,WAAK,IAAI,aAAa,gBAAgB,KAAK,SAAS,gBAAgB;AAAA,IACtE;AAAA,EACF;AAEA,SAAO,SACH,EAAE,QAAiB,IACnB;AAAA;AAAA,IACF,cAAc,SAAS,eAAgB;AACrC,UAAI,UAAU,KAAK;AACnB,cAAQ,OAAO,QAAQ,SAAU,QAAQ,gBAAgB,QAAQ,SAAU,CAAC,IAAI;AAEhF,UAAI,QAAQ,MAAM;AAChB,YAAI,QAAQ,gBAAgB,SAAS;AAEnC,cAAK,QAAQ,gBAAgB,QAAQ,QAAS;AAC5C,gBAAI;AACF,kBAAI,iBAAiB,QAAQ,QAAQ,QAAQ,KAAK,WAAW,QAAQ,KAAK,WAAW,CAAC;AACtF,kBAAI,QAAQ,QAAQ,gBAAgB,QAAQ;AAC5C,oBAAM,QAAQ,SAAU,UAAU;AAChC,iCAAiB,MAAM,gBAAgB,KAAK,MAAM,QAAQ,CAAC;AAAA,cAC7D,CAAC;AACD,qBAAO,KAAK,cAAc,EAAE,QAAQ,SAAU,QAAQ;AACpD,wBAAQ,KAAK,mBAAmB,QAAQ,eAAe,MAAM,CAAC;AAAA,cAChE,CAAC;AAAA,YACH,SAAS,GAAG;AACV,kBAAI,MAAuC;AACzC,sBAAM,mDAAmD,CAAC;AAAA,cAC5D;AAAA,YACF;AAAA,UACF;AACA,eAAK,QAAQ,QAAQ;AACrB,eAAK,eAAe,KAAK,MAAM,cAAc;AAAA,QAC/C,WAAW,cAAc,QAAQ,IAAI,GAAG;AACtC,cAAI,WAAW,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,iBAAiB,UACzE,KAAK,MAAM,QACX;AAEJ,cAAI,UAAU;AACZ,oBAAQ,KAAK,OAAO,KAAK;AACzB,oBAAQ,KAAK,YAAY,SAAS;AAClC,oBAAQ,KAAK,iBAAiB,SAAS;AACvC,oBAAQ,KAAK,yBAAyB,SAAS;AAC/C,oBAAQ,KAAK,wBAAwB,SAAS;AAC9C,oBAAQ,KAAK,qBAAqB,SAAS;AAC3C,oBAAQ,KAAK,qBAAqB,SAAS;AAC3C,oBAAQ,KAAK,2BAA2B,SAAS;AAAA,UACnD;AAGA,cAAK,QAAQ,gBAAgB,QAAQ,QAAS;AAC5C,gBAAI;AACF,kBAAI,mBAAmB,QAAQ,QAAQ,QAAQ,KAAK,WAAW,QAAQ,KAAK,WAAW,CAAC;AACxF,kBAAI,UAAU,QAAQ,gBAAgB,QAAQ;AAC9C,sBAAQ,QAAQ,SAAU,UAAU;AAClC,mCAAmB,MAAM,kBAAkB,KAAK,MAAM,QAAQ,CAAC;AAAA,cACjE,CAAC;AACD,sBAAQ,KAAK,WAAW;AAAA,YAC1B,SAAS,GAAG;AACV,kBAAI,MAAuC;AACzC,qBAAK,mDAAmD,CAAC;AAAA,cAC3D;AAAA,YACF;AAAA,UACF;AAEA,cAAI,MAAM,QAAQ;AAClB,cAAI,iBAAiB,IAAI;AACzB,cAAI,kBAAkB,cAAc,cAAc,GAAG;AACnD,oBAAQ,KAAK,WAAW,MAAM,QAAQ,KAAK,UAAU,cAAc;AAAA,UACrE;AAEA,eAAK,QAAQ,IAAI,QAAQ,QAAQ,IAAI;AACrC,eAAK,eAAe,KAAK,MAAM,cAAc;AAE7C,cAAI,QAAQ,KAAK,SAAS,UAAa,CAAC,CAAC,QAAQ,KAAK,MAAM;AAC1D,iBAAK,iBAAiB,KAAK,MAAM,YAAY;AAAA,UAC/C;AAEA,cAAI,UAAU;AACZ,qBAAS,2BAA2B,KAAK,KAAK;AAAA,UAChD;AAAA,QACF,OAAO;AACL,cAAI,MAAuC;AACzC,iBAAK,sCAAsC;AAAA,UAC7C;AAAA,QACF;AAAA,MACF,WAAW,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,iBAAiB,SAAS;AAEhF,aAAK,QAAQ,KAAK,MAAM;AAAA,MAC1B,WAAW,QAAQ,UAAU,QAAQ,OAAO,SAAS,QAAQ,OAAO,iBAAiB,SAAS;AAE5F,aAAK,QAAQ,QAAQ,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,IAEA,aAAa,SAAS,cAAe;AACnC,UAAI,UAAU,KAAK;AACnB,cAAQ,OAAO,QAAQ,SAAU,QAAQ,gBAAgB,QAAQ,SAAU,CAAC,IAAI;AAEhF,UAAI,QAAQ,MAAM;AAChB,YAAI,QAAQ,gBAAgB,SAAS;AAEnC,eAAK,MAAM,sBAAsB,IAAI;AACrC,eAAK,eAAe;AAAA,QACtB,WAAW,cAAc,QAAQ,IAAI,GAAG;AACtC,eAAK,MAAM,sBAAsB,IAAI;AACrC,eAAK,eAAe;AAAA,QACtB,OAAO;AACL,cAAI,MAAuC;AACzC,iBAAK,sCAAsC;AAAA,UAC7C;AAAA,QACF;AAAA,MACF,WAAW,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,iBAAiB,SAAS;AAChF,aAAK,MAAM,sBAAsB,IAAI;AACrC,aAAK,eAAe;AAAA,MACtB,WAAW,QAAQ,UAAU,QAAQ,OAAO,SAAS,QAAQ,OAAO,iBAAiB,SAAS;AAC5F,aAAK,MAAM,sBAAsB,IAAI;AACrC,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,IAEA;AAAA,IAEA,eAAe,SAAS,gBAAiB;AACvC,UAAI,CAAC,KAAK,OAAO;AAAE;AAAA,MAAO;AAE1B,UAAI,OAAO;AACX,WAAK,UAAU,WAAY;AACzB,YAAI,KAAK,cAAc;AACrB,eAAK,MAAM,wBAAwB,IAAI;AACvC,iBAAO,KAAK;AAAA,QACd;AAEA,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa;AAClB,eAAK,MAAM,UAAU;AACrB,iBAAO,KAAK;AAAA,QACd;AAEA,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe;AACpB,iBAAO,KAAK;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAIA,IAAI,yBAAyB;AAAA,EAC3B,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,IACL,KAAK;AAAA,MACH,MAAM,CAAC,QAAQ,SAAS,MAAM;AAAA,MAC9B,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,OAAO,MAAM;AAAA,IACtB;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,OAAQ,GAAG,KAAK;AAC/B,QAAI,OAAO,IAAI;AACf,QAAI,SAAS,IAAI;AACjB,QAAI,QAAQ,IAAI;AAChB,QAAI,QAAQ,IAAI;AAEhB,QAAI,QAAQ,OAAO;AACnB,QAAI,CAAC,OAAO;AACV,UAAI,MAAuC;AACzC,aAAK,+BAA+B;AAAA,MACtC;AACA;AAAA,IACF;AAEA,QAAI,OAAO,MAAM;AACjB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAI,SAAS,MAAM;AACnB,QAAI,WAAW,MAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA,oBAAoB,MAAM,KAAK,SAC3B,gBAAgB,OAAO,SAAS,MAAM,IACtC;AAAA,IACN;AAEA,QAAI,MAAO,CAAC,CAAC,MAAM,OAAO,MAAM,QAAQ,QAAS,MAAM,QAAQ,QAAQ,MAAM,MAAM;AACnF,WAAO,MAAM,EAAE,KAAK,MAAM,QAAQ,IAAI;AAAA,EACxC;AACF;AAEA,SAAS,oBAAqB,QAAQ;AACpC,MAAI;AACJ,OAAK,QAAQ,QAAQ;AACnB,QAAI,SAAS,WAAW;AAAE,aAAO;AAAA,IAAM;AAAA,EACzC;AACA,SAAO,QAAQ,IAAI;AACrB;AAEA,SAAS,gBAAiB,UAAU,QAAQ;AAC1C,MAAI,SAAS,SAAS,uBAAuB,MAAM,IAAI,CAAC;AAExD,MAAI,CAAC,UAAU;AAAE,WAAO;AAAA,EAAO;AAG/B,aAAW,SAAS,OAAO,SAAU,OAAO;AAC1C,WAAO,MAAM,OAAO,MAAM,KAAK,KAAK,MAAM;AAAA,EAC5C,CAAC;AAED,MAAI,aAAa,SAAS,MAAM,sBAAsB;AACtD,MAA6C,YAAY;AACvD,SAAK,oFAAoF;AAAA,EAC3F;AAEA,SAAO,SAAS;AAAA,IACd,aAAa,mBAAmB;AAAA,IAChC;AAAA,EACF;AACF;AAEA,SAAS,uBAAwB,QAAQ;AACvC,MAAI,MAAuC;AACzC,SAAK,gFAAgF;AAAA,EACvF;AAEA,SAAO,MAAM,QAAQ,MAAM,IACvB,OAAO,OAAO,kBAAkB,CAAC,CAAC,IAClC,OAAO,OAAO,CAAC,GAAG,MAAM;AAC9B;AAEA,SAAS,iBAAkB,QAAQ,OAAO;AACxC,MAAI,MAAM,QAAQ,MAAM,KAAK,SAAS,MAAM,KAAK,MAAM,OAAO;AAC5D,WAAO,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,EACnC;AACA,SAAO;AACT;AAEA,SAAS,iBAAkB,QAAQ,OAAO,OAAO;AAC/C,SAAO,KAAK,IAAI;AAChB,SAAO;AACT;AAEA,SAAS,uBAAwB,OAAO;AACtC,SAAO,QAAQ,MAAM,QAAQ,MAAM,KAAK,SAAS,MAAM,KAAK,MAAM,KAAK;AACzE;AAIA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,IACL,KAAK;AAAA,MACH,MAAM,CAAC,QAAQ,SAAS,MAAM;AAAA,MAC9B,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,IACvB;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,QAAQ,SAASE,QAAQ,GAAG,KAAK;AAC/B,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,IAAI;AACjB,QAAI,OAAO,IAAI;AAEf,QAAI,OAAO,OAAO;AAElB,QAAI,CAAC,MAAM;AACT,UAAI,MAAuC;AACzC,aAAK,+BAA+B;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AAEA,QAAI,MAAM;AACV,QAAI,UAAU;AAEd,QAAI,SAAS,MAAM,MAAM,GAAG;AAC1B,YAAM,MAAM;AAAA,IACd,WAAW,SAAS,MAAM,MAAM,GAAG;AACjC,UAAI,MAAM,OAAO,KAAK;AACpB,cAAM,MAAM,OAAO;AAAA,MACrB;AAGA,gBAAU,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,SAAU,KAAK,MAAM;AAC9D,YAAI;AAEJ,YAAI,SAAS,kBAAkB,IAAI,GAAG;AACpC,iBAAO,OAAO,OAAO,CAAC,GAAG,MAAO,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM,OAAO,IAAI,GAAG,IAAK;AAAA,QACjF;AACA,eAAO;AAAA,MACT,GAAG,IAAI;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,UAAU,KAAK;AAClC,QAAI,QAAQ,KAAK,KAAK,MAAM,OAAO,QAAQ,KAAK,OAAO;AAEvD,QAAI,SAAS,MAAM,IAAI,SAAU,MAAM,OAAO;AAC5C,UAAI;AAEJ,UAAI,OAAO,KAAK,eAAe,KAAK,YAAY,KAAK,IAAI;AACzD,aAAO,OAAO,MAAO,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAK,IAAI,KAAK;AAAA,IAClH,CAAC;AAED,QAAI,MAAO,CAAC,CAAC,MAAM,OAAO,MAAM,QAAQ,QAAS,MAAM,QAAQ,QAAQ,MAAM,MAAM;AACnF,WAAO,MACH,EAAE,KAAK;AAAA,MACP,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK,OAAO;AAAA,MACrB,aAAa,KAAK;AAAA,IACpB,GAAG,MAAM,IACP;AAAA,EACN;AACF;AAIA,SAAS,KAAM,IAAI,SAAS,OAAO;AACjC,MAAI,CAAC,OAAO,IAAI,KAAK,GAAG;AAAE;AAAA,EAAO;AAEjC,IAAE,IAAI,SAAS,KAAK;AACtB;AAEA,SAAS,OAAQ,IAAI,SAAS,OAAO,UAAU;AAC7C,MAAI,CAAC,OAAO,IAAI,KAAK,GAAG;AAAE;AAAA,EAAO;AAEjC,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,YAAY,IAAI,KAAK,MACtB,WAAW,QAAQ,OAAO,QAAQ,QAAQ,KAC1C,WAAW,GAAG,gBAAgB,KAAK,iBAAiB,KAAK,MAAM,CAAC,IAAI;AAAE;AAAA,EAAO;AAEhF,IAAE,IAAI,SAAS,KAAK;AACtB;AAEA,SAAS,OAAQ,IAAI,SAAS,OAAO,UAAU;AAC7C,MAAI,KAAK,MAAM;AACf,MAAI,CAAC,IAAI;AACP,SAAK,+CAA+C;AACpD;AAAA,EACF;AAEA,MAAI,OAAO,MAAM,QAAQ,SAAS,CAAC;AACnC,MAAI,CAAC,QAAQ,UAAU,YAAY,CAAC,KAAK,0BAA0B;AACjE,OAAG,cAAc;AAAA,EACnB;AACA,KAAG,MAAM;AACT,SAAO,GAAG,KAAK;AACf,KAAG,UAAU;AACb,SAAO,GAAG,SAAS;AACnB,KAAG,iBAAiB;AACpB,SAAO,GAAG,gBAAgB;AAC5B;AAEA,SAAS,OAAQ,IAAI,OAAO;AAC1B,MAAI,KAAK,MAAM;AACf,MAAI,CAAC,IAAI;AACP,SAAK,+CAA+C;AACpD,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,GAAG,OAAO;AACb,SAAK,kDAAkD;AACvD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,YAAa,IAAI,OAAO;AAC/B,MAAI,KAAK,MAAM;AACf,SAAO,GAAG,YAAY,GAAG,MAAM;AACjC;AAEA,SAAS,EAAG,IAAI,SAAS,OAAO;AAC9B,MAAI,OAAO;AAEX,MAAI,QAAQ,QAAQ;AAEpB,MAAI,MAAM,WAAW,KAAK;AAC1B,MAAI,OAAO,IAAI;AACf,MAAI,SAAS,IAAI;AACjB,MAAI,OAAO,IAAI;AACf,MAAI,SAAS,IAAI;AACjB,MAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM;AAC7B,SAAK,0BAA0B;AAC/B;AAAA,EACF;AAEA,MAAI,CAAC,MAAM;AACT,SAAK,qCAAqC;AAC1C;AAAA,EACF;AAEA,MAAI,KAAK,MAAM;AACf,MAAI,UAAU,MAAM;AAClB,OAAG,MAAM,GAAG,eAAe,QAAQ,GAAG,OAAO,GAAG,MAAM,OAAO,CAAE,MAAM,MAAO,EAAE,OAAQ,WAAW,QAAQ,IAAI,CAAE,CAAC;AAAA,EAClH,OAAO;AACL,OAAG,MAAM,GAAG,eAAe,QAAQ,GAAG,OAAO,EAAE,MAAM,OAAO,CAAE,IAAK,EAAE,OAAQ,WAAW,QAAQ,IAAI,CAAE,CAAC;AAAA,EACzG;AACA,KAAG,UAAU,GAAG,MAAM;AACtB,KAAG,iBAAiB,GAAG,MAAM,iBAAiB,GAAG,MAAM,MAAM;AAC/D;AAEA,SAAS,WAAY,OAAO;AAC1B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,SAAS,KAAK,GAAG;AACnB,WAAO;AAAA,EACT,WAAW,cAAc,KAAK,GAAG;AAC/B,WAAO,MAAM;AACb,aAAS,MAAM;AACf,WAAO,MAAM;AACb,aAAS,MAAM;AAAA,EACjB;AAEA,SAAO,EAAE,MAAY,QAAgB,MAAY,OAAe;AAClE;AAEA,SAAS,WAAY,QAAQ,MAAM;AACjC,MAAI,SAAS,CAAC;AAEd,YAAU,OAAO,KAAK,MAAM;AAC5B,MAAI,SAAS,MAAM,QAAQ,IAAI,KAAK,cAAc,IAAI,IAAI;AACxD,WAAO,KAAK,IAAI;AAAA,EAClB;AAEA,SAAO;AACT;AAEA,IAAI;AAEJ,SAAS,QAAS,MAAM,SAAS;AAC/B,MAAK,YAAY,OAAS,WAAU,EAAE,QAAQ,MAAM;AAGpD,MAA6C,QAAQ,aAAa,SAAS,KAAK;AAC9E,SAAK,oBAAoB;AACzB;AAAA,EACF;AACA,UAAQ,YAAY;AAEpB,QAAM;AAEN,MAAI,UAAW,IAAI,WAAW,OAAO,IAAI,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC,KAAM;AAEpE,MAA6C,UAAU,GAAG;AACxD,SAAM,eAAgB,QAAQ,UAAW,0CAA2C,IAAI,UAAW,IAAK;AACxG;AAAA,EACF;AAEA,SAAO,GAAG;AACV,MAAI,MAAM,YAAY,QAAQ,MAAM,CAAC;AACrC,MAAI,UAAU,KAAK,EAAE,MAAY,QAAgB,OAAe,CAAC;AACjE,MAAI,UAAU,uBAAuB,MAAM,sBAAsB;AACjE,MAAI,UAAU,gBAAgB,MAAM,eAAe;AAGnD,MAAI,SAAS,IAAI,OAAO;AACxB,SAAO,OAAO,SAAU,WAAW,UAAU;AAC3C,WAAO,aAAa,SAChB,YACA;AAAA,EACN;AACF;AAIA,IAAI,gBAAgB,SAASC,iBAAiB;AAC5C,OAAK,UAAU,uBAAO,OAAO,IAAI;AACnC;AAEA,cAAc,UAAU,cAAc,SAAS,YAAa,SAAS,QAAQ;AAC3E,MAAI,CAAC,QAAQ;AACX,WAAO,CAAC,OAAO;AAAA,EACjB;AACA,MAAI,SAAS,KAAK,QAAQ,OAAO;AACjC,MAAI,CAAC,QAAQ;AACX,aAAS,MAAM,OAAO;AACtB,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC1B;AACA,SAAO,QAAQ,QAAQ,MAAM;AAC/B;AAIA,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAE3B,SAAS,MAAO,QAAQ;AACtB,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AAEf,MAAI,OAAO;AACX,SAAO,WAAW,OAAO,QAAQ;AAC/B,QAAI,OAAO,OAAO,UAAU;AAC5B,QAAI,SAAS,KAAK;AAChB,UAAI,MAAM;AACR,eAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,KAAK,CAAC;AAAA,MAC3C;AAEA,aAAO;AACP,UAAI,MAAM;AACV,aAAO,OAAO,UAAU;AACxB,aAAO,SAAS,UAAa,SAAS,KAAK;AACzC,eAAO;AACP,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,UAAI,WAAW,SAAS;AAExB,UAAI,OAAO,oBAAoB,KAAK,GAAG,IACnC,SACA,YAAY,qBAAqB,KAAK,GAAG,IACvC,UACA;AACN,aAAO,KAAK,EAAE,OAAO,KAAK,KAAW,CAAC;AAAA,IACxC,WAAW,SAAS,KAAK;AAEvB,UAAI,OAAQ,QAAS,MAAM,KAAK;AAC9B,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,UAAQ,OAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,KAAK,CAAC;AAEjD,SAAO;AACT;AAEA,SAAS,QAAS,QAAQ,QAAQ;AAChC,MAAI,WAAW,CAAC;AAChB,MAAI,QAAQ;AAEZ,MAAI,OAAO,MAAM,QAAQ,MAAM,IAC3B,SACA,SAAS,MAAM,IACb,UACA;AACN,MAAI,SAAS,WAAW;AAAE,WAAO;AAAA,EAAS;AAE1C,SAAO,QAAQ,OAAO,QAAQ;AAC5B,QAAI,QAAQ,OAAO,KAAK;AACxB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,iBAAS,KAAK,MAAM,KAAK;AACzB;AAAA,MACF,KAAK;AACH,iBAAS,KAAK,OAAO,SAAS,MAAM,OAAO,EAAE,CAAC,CAAC;AAC/C;AAAA,MACF,KAAK;AACH,YAAI,SAAS,SAAS;AACpB,mBAAS,KAAM,OAAQ,MAAM,KAAK,CAAC;AAAA,QACrC,OAAO;AACL,cAAI,MAAuC;AACzC,iBAAM,oBAAqB,MAAM,OAAQ,4BAA4B,OAAO,gBAAiB;AAAA,UAC/F;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,YAAI,MAAuC;AACzC,eAAK,iCAAiC;AAAA,QACxC;AACA;AAAA,IACJ;AACA;AAAA,EACF;AAEA,SAAO;AACT;AAWA,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AAGpB,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,QAAQ;AAEZ,IAAI,mBAAmB,CAAC;AAExB,iBAAiB,WAAW,IAAI;AAAA,EAC9B,MAAM,CAAC,WAAW;AAAA,EAClB,SAAS,CAAC,UAAU,MAAM;AAAA,EAC1B,KAAK,CAAC,WAAW;AAAA,EACjB,OAAO,CAAC,UAAU;AACpB;AAEA,iBAAiB,OAAO,IAAI;AAAA,EAC1B,MAAM,CAAC,OAAO;AAAA,EACd,KAAK,CAAC,YAAY;AAAA,EAClB,KAAK,CAAC,WAAW;AAAA,EACjB,OAAO,CAAC,UAAU;AACpB;AAEA,iBAAiB,YAAY,IAAI;AAAA,EAC/B,MAAM,CAAC,YAAY;AAAA,EACnB,SAAS,CAAC,UAAU,MAAM;AAAA,EAC1B,KAAK,CAAC,UAAU,MAAM;AAAA,EACtB,UAAU,CAAC,UAAU,MAAM;AAC7B;AAEA,iBAAiB,QAAQ,IAAI;AAAA,EAC3B,SAAS,CAAC,UAAU,MAAM;AAAA,EAC1B,KAAK,CAAC,UAAU,MAAM;AAAA,EACtB,UAAU,CAAC,UAAU,MAAM;AAAA,EAC3B,MAAM,CAAC,SAAS,IAAI;AAAA,EACpB,KAAK,CAAC,cAAc,IAAI;AAAA,EACxB,KAAK,CAAC,aAAa,IAAI;AAAA,EACvB,OAAO,CAAC,YAAY,IAAI;AAC1B;AAEA,iBAAiB,WAAW,IAAI;AAAA,EAC9B,KAAK,CAAC,iBAAiB,MAAM;AAAA,EAC7B,KAAK,CAAC,iBAAiB,MAAM;AAAA,EAC7B,KAAK,CAAC,aAAa,kBAAkB;AAAA,EACrC,KAAK,CAAC,SAAS,aAAa;AAAA,EAC5B,OAAO;AAAA,EACP,QAAQ,CAAC,aAAa,MAAM;AAC9B;AAEA,iBAAiB,eAAe,IAAI;AAAA,EAClC,KAAK,CAAC,aAAa,MAAM;AAAA,EACzB,OAAO;AAAA,EACP,QAAQ,CAAC,iBAAiB,MAAM;AAClC;AAEA,iBAAiB,eAAe,IAAI;AAAA,EAClC,KAAK,CAAC,aAAa,MAAM;AAAA,EACzB,OAAO;AAAA,EACP,QAAQ,CAAC,iBAAiB,MAAM;AAClC;AAMA,IAAI,iBAAiB;AACrB,SAAS,UAAW,KAAK;AACvB,SAAO,eAAe,KAAK,GAAG;AAChC;AAMA,SAAS,YAAa,KAAK;AACzB,MAAI,IAAI,IAAI,WAAW,CAAC;AACxB,MAAI,IAAI,IAAI,WAAW,IAAI,SAAS,CAAC;AACrC,SAAO,MAAM,MAAM,MAAM,MAAQ,MAAM,MACnC,IAAI,MAAM,GAAG,EAAE,IACf;AACN;AAMA,SAAS,gBAAiB,IAAI;AAC5B,MAAI,OAAO,UAAa,OAAO,MAAM;AAAE,WAAO;AAAA,EAAM;AAEpD,MAAI,OAAO,GAAG,WAAW,CAAC;AAE1B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IAET,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IAET,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AAAA;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AAEA,SAAO;AACT;AAQA,SAAS,cAAe,MAAM;AAC5B,MAAI,UAAU,KAAK,KAAK;AAExB,MAAI,KAAK,OAAO,CAAC,MAAM,OAAO,MAAM,IAAI,GAAG;AAAE,WAAO;AAAA,EAAM;AAE1D,SAAO,UAAU,OAAO,IAAI,YAAY,OAAO,IAAI,MAAM;AAC3D;AAMA,SAAS,QAAS,MAAM;AACtB,MAAI,OAAO,CAAC;AACZ,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,eAAe;AACnB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,CAAC;AAEf,UAAQ,IAAI,IAAI,WAAY;AAC1B,QAAI,QAAQ,QAAW;AACrB,WAAK,KAAK,GAAG;AACb,YAAM;AAAA,IACR;AAAA,EACF;AAEA,UAAQ,MAAM,IAAI,WAAY;AAC5B,QAAI,QAAQ,QAAW;AACrB,YAAM;AAAA,IACR,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,UAAQ,kBAAkB,IAAI,WAAY;AACxC,YAAQ,MAAM,EAAE;AAChB;AAAA,EACF;AAEA,UAAQ,aAAa,IAAI,WAAY;AACnC,QAAI,eAAe,GAAG;AACpB;AACA,aAAO;AACP,cAAQ,MAAM,EAAE;AAAA,IAClB,OAAO;AACL,qBAAe;AACf,UAAI,QAAQ,QAAW;AAAE,eAAO;AAAA,MAAM;AACtC,YAAM,cAAc,GAAG;AACvB,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ,IAAI,EAAE;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,qBAAsB;AAC7B,QAAI,WAAW,KAAK,QAAQ,CAAC;AAC7B,QAAK,SAAS,mBAAmB,aAAa,OAC3C,SAAS,mBAAmB,aAAa,KAAM;AAChD;AACA,gBAAU,OAAO;AACjB,cAAQ,MAAM,EAAE;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,SAAS,MAAM;AACpB;AACA,QAAI,KAAK,KAAK;AAEd,QAAI,MAAM,QAAQ,mBAAmB,GAAG;AACtC;AAAA,IACF;AAEA,WAAO,gBAAgB,CAAC;AACxB,cAAU,iBAAiB,IAAI;AAC/B,iBAAa,QAAQ,IAAI,KAAK,QAAQ,MAAM,KAAK;AAEjD,QAAI,eAAe,OAAO;AACxB;AAAA,IACF;AAEA,WAAO,WAAW,CAAC;AACnB,aAAS,QAAQ,WAAW,CAAC,CAAC;AAC9B,QAAI,QAAQ;AACV,gBAAU,WAAW,CAAC;AACtB,gBAAU,YAAY,SAClB,IACA;AACJ,UAAI,OAAO,MAAM,OAAO;AACtB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,YAAY;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAMA,IAAI,WAAW,SAASC,YAAY;AAClC,OAAK,SAAS,uBAAO,OAAO,IAAI;AAClC;AAKA,SAAS,UAAU,YAAY,SAAS,UAAW,MAAM;AACvD,MAAI,MAAM,KAAK,OAAO,IAAI;AAC1B,MAAI,CAAC,KAAK;AACR,UAAM,QAAQ,IAAI;AAClB,QAAI,KAAK;AACP,WAAK,OAAO,IAAI,IAAI;AAAA,IACtB;AAAA,EACF;AACA,SAAO,OAAO,CAAC;AACjB;AAKA,SAAS,UAAU,eAAe,SAAS,aAAc,KAAK,MAAM;AAClE,MAAI,CAAC,SAAS,GAAG,GAAG;AAAE,WAAO;AAAA,EAAK;AAElC,MAAI,QAAQ,KAAK,UAAU,IAAI;AAC/B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT,OAAO;AACL,QAAI,SAAS,MAAM;AACnB,QAAI,OAAO;AACX,QAAIL,KAAI;AACR,WAAOA,KAAI,QAAQ;AACjB,UAAI,QAAQ,KAAK,MAAMA,EAAC,CAAC;AACzB,UAAI,UAAU,UAAa,UAAU,MAAM;AACzC,eAAO;AAAA,MACT;AACA,aAAO;AACP,MAAAA;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAMA,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AAAA,EACrB,SAAS,SAAU,KAAK;AAAE,WAAO,IAAI,kBAAkB;AAAA,EAAG;AAAA,EAC1D,SAAS,SAAU,KAAK;AAAE,WAAO,IAAI,kBAAkB;AAAA,EAAG;AAAA,EAC1D,cAAc,SAAU,KAAK;AAAE,WAAQ,KAAM,IAAI,OAAO,CAAC,EAAE,kBAAkB,IAAM,IAAI,OAAO,CAAC;AAAA,EAAK;AACtG;AAEA,IAAI,mBAAmB,IAAI,cAAc;AAEzC,IAAI,UAAU,SAASM,SAAS,SAAS;AACvC,MAAI,SAAS;AACb,MAAK,YAAY,OAAS,WAAU,CAAC;AAMrC,MAAI,CAAC,OAAO,OAAO,WAAW,eAAe,OAAO,KAAK;AACvD,YAAQ,OAAO,GAAG;AAAA,EACpB;AAEA,MAAI,SAAS,QAAQ,UAAU;AAC/B,MAAI,iBAAiB,QAAQ,mBAAmB,QAC5C,QACA,QAAQ,kBAAkB;AAC9B,MAAI,WAAW,QAAQ,YAAY,CAAC;AACpC,MAAI,kBAAkB,QAAQ,mBAAmB,QAAQ,mBAAmB,CAAC;AAC7E,MAAI,gBAAgB,QAAQ,iBAAiB,CAAC;AAE9C,OAAK,MAAM;AACX,OAAK,aAAa,QAAQ,aAAa;AACvC,OAAK,aAAa,QAAQ,aAAa,CAAC;AACxC,OAAK,WAAW,QAAQ,WAAW;AACnC,OAAK,QAAQ,QAAQ,QAAQ;AAC7B,OAAK,QAAQ,QAAQ,SAAS,SAAY,OAAO,CAAC,CAAC,QAAQ;AAC3D,OAAK,gBAAgB,QAAQ,iBAAiB,SAC1C,OACA,CAAC,CAAC,QAAQ;AACd,OAAK,+BAA+B,QAAQ,gCAAgC,SACxE,OACA,CAAC,CAAC,QAAQ;AACd,OAAK,0BAA0B,QAAQ,2BAA2B,SAC9D,QACA,CAAC,CAAC,QAAQ;AACd,OAAK,yBAAyB,QAAQ,0BAA0B,SAC5D,QACA,QAAQ;AACZ,OAAK,sBAAsB,QAAQ,uBAAuB,SACtD,QACA,CAAC,CAAC,QAAQ;AACd,OAAK,sBAAsB,CAAC;AAC5B,OAAK,oBAAoB,CAAC;AAC1B,OAAK,QAAQ,IAAI,SAAS;AAC1B,OAAK,iBAAiB,oBAAI,IAAI;AAC9B,OAAK,oCAAoC,QAAQ,oCAAoC;AACrF,OAAK,4BAA4B,QAAQ,6BAA6B,SAClE,QACA,CAAC,CAAC,QAAQ;AACd,OAAK,qBAAqB,QAAQ,sBAAsB,CAAC;AACzD,OAAK,qBAAqB,QAAQ,qBAAqB;AACvD,OAAK,mBAAmB,QAAQ,mBAAmB;AACnD,OAAK,uBAAuB,QAAQ,uBAAuB;AAE3D,MAAI,yBAAyB,SAAS;AACpC,SAAK,sBAAsB,QAAQ;AAAA,EACrC;AAOA,OAAK,iBAAiB,SAAU,QAAQ,eAAe;AACrD,QAAI,gBAAgB,OAAO,eAAe,MAAM;AAChD,QAAI,iBAAiB,cAAc,gBAAgB;AACjD,UAAI,0BAA2B,cAAc;AAC7C,aAAQ,wBAAyB,KAAK,QAAQ,QAAQ,aAAa;AAAA,IACrE;AAGA,QAAI,cAAc,SAAU,SAAS,gBAAgB;AACnD,gBAAU,KAAK,IAAI,OAAO;AAE1B,UAAI,mBAAmB,GAAG;AACxB,eAAO,UACH,UAAU,IACR,IACA,IACF;AAAA,MACN;AAEA,aAAO,UAAU,KAAK,IAAI,SAAS,CAAC,IAAI;AAAA,IAC1C;AAEA,QAAI,OAAO,UAAU,OAAO,oBAAoB;AAC9C,aAAO,OAAO,mBAAmB,OAAO,MAAM,EAAE,MAAM,QAAQ,CAAC,QAAQ,aAAa,CAAC;AAAA,IACvF,OAAO;AACL,aAAO,YAAY,QAAQ,aAAa;AAAA,IAC1C;AAAA,EACF;AAGA,OAAK,SAAS,SAAU,SAAS,KAAK;AACpC,QAAI,CAAC,WAAW,CAAC,KAAK;AAAE,aAAO;AAAA,IAAM;AACrC,QAAI,CAAC,OAAO,OAAO,MAAM,aAAa,SAAS,GAAG,CAAC,GAAG;AAAE,aAAO;AAAA,IAAK;AAEpE,QAAI,QAAQ,GAAG,GAAG;AAAE,aAAO;AAAA,IAAK;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,uBAAuB,UAAU,KAAK,uBAAuB,SAAS;AAC7E,WAAO,KAAK,QAAQ,EAAE,QAAQ,SAAUC,SAAQ;AAC9C,aAAO,oBAAoBA,SAAQ,OAAO,oBAAoB,SAASA,OAAM,CAAC;AAAA,IAChF,CAAC;AAAA,EACH;AAEA,OAAK,QAAQ;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,qBAAqB,EAAE,IAAI,EAAE,cAAc,KAAK,GAAE,UAAU,EAAE,cAAc,KAAK,GAAE,iBAAiB,EAAE,cAAc,KAAK,GAAE,eAAe,EAAE,cAAc,KAAK,GAAE,kBAAkB,EAAE,cAAc,KAAK,GAAE,QAAQ,EAAE,cAAc,KAAK,GAAE,gBAAgB,EAAE,cAAc,KAAK,GAAE,wBAAwB,EAAE,cAAc,KAAK,GAAE,SAAS,EAAE,cAAc,KAAK,GAAE,WAAW,EAAE,cAAc,KAAK,GAAE,uBAAuB,EAAE,cAAc,KAAK,GAAE,oBAAoB,EAAE,cAAc,KAAK,GAAE,0BAA0B,EAAE,cAAc,KAAK,GAAE,mBAAmB,EAAE,cAAc,KAAK,GAAE,iBAAiB,EAAE,cAAc,KAAK,GAAE,MAAM,EAAE,cAAc,KAAK,EAAE;AAE9nB,QAAQ,UAAU,sBAAsB,SAAS,oBAAqB,QAAQ,OAAO,SAAS;AAC5F,MAAI,QAAQ,CAAC;AAEb,MAAI,KAAK,SAAUC,QAAOD,SAAQE,UAASC,QAAO;AAChD,QAAI,cAAcD,QAAO,GAAG;AAC1B,aAAO,KAAKA,QAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,YAAI,MAAMA,SAAQ,GAAG;AACrB,YAAI,cAAc,GAAG,GAAG;AACtB,UAAAC,OAAM,KAAK,GAAG;AACd,UAAAA,OAAM,KAAK,GAAG;AACd,aAAGF,QAAOD,SAAQ,KAAKG,MAAK;AAC5B,UAAAA,OAAM,IAAI;AACV,UAAAA,OAAM,IAAI;AAAA,QACZ,OAAO;AACL,UAAAA,OAAM,KAAK,GAAG;AACd,aAAGF,QAAOD,SAAQ,KAAKG,MAAK;AAC5B,UAAAA,OAAM,IAAI;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH,WAAW,QAAQD,QAAO,GAAG;AAC3B,MAAAA,SAAQ,QAAQ,SAAU,MAAM,OAAO;AACrC,YAAI,cAAc,IAAI,GAAG;AACvB,UAAAC,OAAM,KAAM,MAAM,QAAQ,GAAI;AAC9B,UAAAA,OAAM,KAAK,GAAG;AACd,aAAGF,QAAOD,SAAQ,MAAMG,MAAK;AAC7B,UAAAA,OAAM,IAAI;AACV,UAAAA,OAAM,IAAI;AAAA,QACZ,OAAO;AACL,UAAAA,OAAM,KAAM,MAAM,QAAQ,GAAI;AAC9B,aAAGF,QAAOD,SAAQ,MAAMG,MAAK;AAC7B,UAAAA,OAAM,IAAI;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH,WAAW,SAASD,QAAO,GAAG;AAC5B,UAAI,MAAM,eAAe,KAAKA,QAAO;AACrC,UAAI,KAAK;AACP,YAAI,MAAM,+BAA+BA,WAAU,mBAAoBC,OAAM,KAAK,EAAE,IAAK,WAAWH,UAAS;AAC7G,YAAIC,WAAU,QAAQ;AACpB,eAAK,GAAG;AAAA,QACV,WAAWA,WAAU,SAAS;AAC5B,gBAAM,GAAG;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,KAAG,OAAO,QAAQ,SAAS,KAAK;AAClC;AAEA,QAAQ,UAAU,UAAU,SAAS,QAAS,MAAM;AAClD,MAAI,SAAS,IAAI,OAAO;AACxB,MAAI,OAAO,SAAS;AACpB,OAAK,MAAM,IAAI,IAAI,EAAE,MAAY,sBAAsB,KAAK,CAAC;AAC7D,MAAI,OAAO,SAAS;AACtB;AAEA,QAAQ,UAAU,YAAY,SAAS,YAAa;AAClD,OAAK,IAAI,SAAS;AACpB;AAEA,QAAQ,UAAU,wBAAwB,SAAS,sBAAuB,IAAI;AAC5E,OAAK,eAAe,IAAI,EAAE;AAC5B;AAEA,QAAQ,UAAU,0BAA0B,SAAS,wBAAyB,IAAI;AAChF,SAAO,KAAK,gBAAgB,EAAE;AAChC;AAEA,QAAQ,UAAU,gBAAgB,SAAS,gBAAiB;AACxD,MAAI,SAAS;AACf,SAAO,KAAK,IAAI,OAAO,SAAS,WAAY;AAC1C,QAAI,YAAY,UAAU,OAAO,cAAc;AAC/C,QAAIR,KAAI,UAAU;AAClB,WAAMA,MAAK;AACT,UAAI,SAAS,WAAY;AACvB,kBAAUA,EAAC,KAAK,UAAUA,EAAC,EAAE,aAAa;AAAA,MAC5C,CAAC;AAAA,IACH;AAAA,EACF,GAAG,EAAE,MAAM,KAAK,CAAC;AACnB;AAEA,QAAQ,UAAU,cAAc,SAAS,YAAa,UAAU;AAC9D,MAAI,CAAC,UAAU;AAEb,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,OAAO;AAAE,aAAO;AAAA,IAAK;AAC9C,QAAI,SAAS,KAAK;AAClB,WAAO,KAAK,MAAM,MAAM,GAAG,OAAO,UAAU,SAAU,KAAK;AACzD,aAAO,KAAK,QAAQ,UAAU,GAAG;AACjC,aAAO,aAAa;AAAA,IACtB,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA,EACxB,OAAO;AAEL,QAAI,CAAC,KAAK,qBAAqB;AAAE,aAAO;AAAA,IAAK;AAC7C,QAAI,OAAO;AACX,QAAI,WAAW,KAAK;AACpB,WAAO,KAAK,GAAG,OAAO,UAAU,SAAU,KAAK;AAC7C,eAAS,KAAK,UAAU,UAAU,GAAG;AACrC,UAAI,KAAK,uBAAuB,UAAU;AACxC,iBAAS,OAAO,QAAQ;AAAA,MAC1B;AACA,eAAS,aAAa;AAAA,IACxB,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA,EACxB;AACF;AAEA,QAAQ,UAAU,6BAA6B,SAAS,2BAA4B,SAAS;AAC3F,MAAI,KAAK,mCAAmC;AAC1C,SAAK,kCAAkC,SAAS,IAAI;AAAA,EACtD;AACF;AAEA,mBAAmB,GAAG,MAAM,WAAY;AAAE,SAAO,KAAK;AAAI;AAE1D,mBAAmB,SAAS,MAAM,WAAY;AAAE,SAAO,WAAW,KAAK,aAAa,CAAC;AAAE;AACvF,mBAAmB,gBAAgB,MAAM,WAAY;AAAE,SAAO,WAAW,KAAK,oBAAoB,CAAC;AAAE;AACrG,mBAAmB,cAAc,MAAM,WAAY;AAAE,SAAO,WAAW,KAAK,kBAAkB,CAAC;AAAE;AACjG,mBAAmB,iBAAiB,MAAM,WAAY;AAAE,SAAO,OAAO,KAAK,KAAK,QAAQ,EAAE,KAAK;AAAE;AAEjG,mBAAmB,OAAO,MAAM,WAAY;AAAE,SAAO,KAAK,IAAI;AAAO;AACrE,mBAAmB,OAAO,MAAM,SAAU,QAAQ;AAChD,OAAK,IAAI,KAAK,KAAK,KAAK,UAAU,MAAM;AAC1C;AAEA,mBAAmB,eAAe,MAAM,WAAY;AAAE,SAAO,KAAK,IAAI;AAAe;AACrF,mBAAmB,eAAe,MAAM,SAAU,QAAQ;AACxD,OAAK,oBAAoB,CAAC;AAC1B,OAAK,IAAI,KAAK,KAAK,KAAK,kBAAkB,MAAM;AAClD;AAEA,mBAAmB,uBAAuB,MAAM,WAAY;AAAE,SAAO,KAAK;AAAwB;AAClG,mBAAmB,uBAAuB,MAAM,SAAU,UAAU;AAAE,OAAK,0BAA0B;AAAU;AAE/G,mBAAmB,QAAQ,MAAM,WAAY;AAAE,SAAO,KAAK;AAAS;AACpE,mBAAmB,QAAQ,MAAM,SAAU,SAAS;AAAE,OAAK,WAAW;AAAS;AAE/E,mBAAmB,UAAU,MAAM,WAAY;AAAE,SAAO,KAAK;AAAW;AACxE,mBAAmB,UAAU,MAAM,SAAU,WAAW;AAAE,OAAK,aAAa;AAAW;AAEvF,mBAAmB,sBAAsB,MAAM,WAAY;AAAE,SAAO,KAAK;AAAuB;AAChG,mBAAmB,sBAAsB,MAAM,SAAU,QAAQ;AAAE,OAAK,yBAAyB;AAAQ;AAEzG,mBAAmB,mBAAmB,MAAM,WAAY;AAAE,SAAO,KAAK;AAAoB;AAC1F,mBAAmB,mBAAmB,MAAM,SAAU,QAAQ;AAAE,OAAK,sBAAsB;AAAQ;AAEnG,mBAAmB,yBAAyB,MAAM,WAAY;AAAE,SAAO,KAAK;AAA0B;AACtG,mBAAmB,yBAAyB,MAAM,SAAU,UAAU;AAAE,OAAK,4BAA4B;AAAU;AAEnH,mBAAmB,kBAAkB,MAAM,WAAY;AAAE,SAAO,KAAK;AAAmB;AACxF,mBAAmB,kBAAkB,MAAM,SAAU,OAAO;AACxD,MAAI,SAAS;AAEf,MAAI,WAAW,KAAK;AACpB,OAAK,qBAAqB;AAC1B,MAAI,aAAa,UAAU,UAAU,UAAU,UAAU,UAAU;AACjE,QAAI,WAAW,KAAK,aAAa;AACjC,WAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,QAAQ;AAC9C,aAAO,oBAAoB,QAAQ,OAAO,oBAAoB,SAAS,MAAM,CAAC;AAAA,IAChF,CAAC;AAAA,EACH;AACF;AAEA,mBAAmB,gBAAgB,MAAM,WAAY;AAAE,SAAO,KAAK;AAAiB;AACpF,mBAAmB,gBAAgB,MAAM,SAAU,SAAS;AAAE,OAAK,mBAAmB;AAAS;AAE/F,mBAAmB,KAAK,MAAM,WAAY;AAAE,SAAO,KAAK;AAAM;AAC9D,mBAAmB,KAAK,MAAM,SAAU,KAAK;AAAE,OAAK,QAAQ;AAAK;AAEjE,QAAQ,UAAU,eAAe,SAAS,eAAgB;AAAE,SAAO,KAAK,IAAI;AAAS;AACrF,QAAQ,UAAU,sBAAsB,SAAS,sBAAuB;AAAE,SAAO,KAAK,IAAI;AAAgB;AAC1G,QAAQ,UAAU,oBAAoB,SAAS,oBAAqB;AAAE,SAAO,KAAK,IAAI;AAAc;AAEpG,QAAQ,UAAU,eAAe,SAAS,aAAc,QAAQ,KAAK,QAAQ,IAAI,QAAQ,iBAAiB;AACxG,MAAI,CAAC,OAAO,MAAM,GAAG;AAAE,WAAO;AAAA,EAAO;AACrC,MAAI,KAAK,UAAU;AACjB,QAAI,aAAa,KAAK,SAAS,MAAM,MAAM,CAAC,QAAQ,KAAK,IAAI,MAAM,CAAC;AACpE,QAAI,SAAS,UAAU,GAAG;AACxB,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,QAA6C,CAAC,KAAK,yBAAyB,GAAG,GAAG;AAChF;AAAA,QACE,4CAA4C,MAAM;AAAA,MAEpD;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAK,yBAAyB;AAChC,QAAI,aAAa,UAAU,MAAM,QAAQ,MAAM;AAC/C,WAAO,KAAK,QAAQ,KAAK,iBAAiB,WAAW,QAAQ,GAAG;AAAA,EAClE,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,QAAQ,UAAU,kBAAkB,SAAS,gBAAiB,KAAK;AACjE,UAAQ,KAAK,+BAA8B,CAAC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,KAAK,KAAK,KAAK,KAAK;AAChG;AAEA,QAAQ,UAAU,wBAAwB,SAAS,sBAAuB,KAAK;AAC7E,SAAO,KAAK,+BAA+B,SACvC,KAAK,oBAAoB,KAAK,GAAG,IACjC,KAAK;AACX;AAEA,QAAQ,UAAU,oBAAoB,SAAS,kBAAmB,QAAQ,KAAK;AAC7E,SAAO,KAAK,sBAAsB,GAAG,MAAM,KAAK,gBAAgB,KAAK,WAAW,KAAK;AACvF;AAEA,QAAQ,UAAU,2BAA2B,SAAS,yBAA0B,KAAK;AACnF,SAAO,KAAK,kCAAkC,SAC1C,KAAK,uBAAuB,KAAK,GAAG,IACpC,KAAK;AACX;AAEA,QAAQ,UAAU,eAAe,SAAS,aACxC,QACA,SACA,KACA,MACA,iBACA,QACA,kBACA;AACA,MAAI,CAAC,SAAS;AAAE,WAAO;AAAA,EAAK;AAE5B,MAAI,UAAU,KAAK,MAAM,aAAa,SAAS,GAAG;AAClD,MAAI,QAAQ,OAAO,KAAK,cAAc,OAAO,GAAG;AAAE,WAAO;AAAA,EAAQ;AAEjE,MAAI;AACJ,MAAI,OAAO,OAAO,GAAG;AAEnB,QAAI,cAAc,OAAO,GAAG;AAC1B,YAAM,QAAQ,GAAG;AACjB,UAAI,EAAE,SAAS,GAAG,KAAK,WAAW,GAAG,IAAI;AACvC,YAA6C,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,kBAAkB,QAAQ,GAAG,GAAG;AACxH,eAAM,mBAAmB,MAAM,iCAAkC;AAAA,QACnE;AACA,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,QAAI,SAAS,OAAO,KAAK,WAAW,OAAO,GAAG;AAC5C,YAAM;AAAA,IACR,OAAO;AACL,UAA6C,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,kBAAkB,QAAQ,GAAG,GAAG;AACxH,aAAM,mBAAmB,MAAM,gCAAiC;AAAA,MAClE;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,SAAS,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI;AACvE,UAAM,KAAK,MAAM,QAAQ,SAAS,KAAK,MAAM,OAAO,QAAQ,gBAAgB;AAAA,EAC9E;AAEA,SAAO,KAAK,QAAQ,KAAK,iBAAiB,QAAQ,GAAG;AACvD;AAEA,QAAQ,UAAU,QAAQ,SAAS,MACjC,QACA,SACA,KACA,MACA,iBACA,QACA,kBACA;AACA,MAAI,MAAM;AAKV,MAAI,UAAU,IAAI,MAAM,cAAc;AAGtC,WAAS,OAAO,SAAS;AAGvB,QAAI,CAAC,QAAQ,eAAe,GAAG,GAAG;AAChC;AAAA,IACF;AACA,QAAI,OAAO,QAAQ,GAAG;AACtB,QAAI,uBAAuB,KAAK,MAAM,oBAAoB;AAC1D,QAAI,aAAa,qBAAqB,CAAC;AACrC,QAAI,gBAAgB,qBAAqB,CAAC;AAG5C,QAAI,kBAAkB,KAAK,QAAQ,YAAY,EAAE,EAAE,QAAQ,iBAAiB,EAAE;AAE9E,QAAI,SAAS,kBAAkB,eAAe,GAAG;AAC/C,UAAI,MAAuC;AACzC,aAAM,gCAAiC,OAAO,0CAA4C,iBAAiB,QAAQ,EAAE,KAAK,MAAM,CAAG;AAAA,MACrI;AACA,aAAO;AAAA,IACT;AACA,qBAAiB,KAAK,eAAe;AAGrC,QAAI,aAAa,KAAK;AAAA,MACpB;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAiB;AAAA,MAClC,oBAAoB,QAAQ,WAAW;AAAA,MACvC,oBAAoB,QAAQ,SAAY;AAAA,MACxC;AAAA,IACF;AAEA,QAAI,KAAK,gBAAgB,UAAU,GAAG;AACpC,UAA6C,CAAC,KAAK,yBAAyB,eAAe,GAAG;AAC5F,aAAM,kDAAkD,kBAAkB,qBAAsB;AAAA,MAClG;AAEA,UAAI,CAAC,KAAK,OAAO;AAAE,cAAM,MAAM,kBAAkB;AAAA,MAAE;AACnD,UAAI,OAAO,KAAK,MAAM;AACtB,mBAAa,KAAK;AAAA,QAChB,KAAK,aAAa;AAAA,QAAG,KAAK;AAAA,QAAQ,KAAK;AAAA,QACvC;AAAA,QAAiB;AAAA,QAAM;AAAA,QAAiB;AAAA,MAC1C;AAAA,IACF;AACA,iBAAa,KAAK;AAAA,MAChB;AAAA,MAAQ;AAAA,MAAiB;AAAA,MAAY;AAAA,MACrC,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAAA,MAClC;AAAA,IACF;AAEA,QAAI,KAAK,WAAW,eAAe,aAAa,GAAG;AACjD,mBAAa,KAAK,WAAW,aAAa,EAAE,UAAU;AAAA,IACxD,WAAW,iBAAiB,eAAe,aAAa,GAAG;AACzD,mBAAa,iBAAiB,aAAa,EAAE,UAAU;AAAA,IACzD;AAEA,qBAAiB,IAAI;AAGrB,UAAM,CAAC,aAAa,MAAM,IAAI,QAAQ,MAAM,UAAU;AAAA,EACxD;AAEA,SAAO;AACT;AAEA,QAAQ,UAAU,wBAAwB,SAAS,sBAAuB,QAAQ,WAAW,MAAM,iBAAiB;AAChH,MAAI,SAAS;AAEf,MAAI,QAAQ,QAAQ,MAAM,IAAI,SAAS,CAAC;AACxC,MAAI,SAAS,SAAS,MAAM,IAAI,SAAS,CAAC;AAC1C,MAAI,OAAO,SAAU,OAAO;AAAE,WAAO,MAAM,KAAK;AAAA,EAAG;AACnD,MAAI,QAAQ,SAAU,KAAK;AAAE,WAAO,OAAO,GAAG;AAAA,EAAG;AACjD,MAAI,WAAW,KAAK,aAAa;AACjC,MAAI,SAAS,KAAK;AAElB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,SAAU,WAAW;AAAE,aAAO,OAAO,aAAa,QAAQ,SAAS,MAAM,KAAK,CAAC,GAAG,WAAW,MAAM,iBAAiB,QAAW,CAAC,SAAS,CAAC;AAAA,IAAG;AAAA,EACvJ;AACF;AAEA,QAAQ,UAAU,UAAU,SAAS,QAAS,SAAS,iBAAiB,QAAQ,MAAM;AACpF,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,MACL,KAAK,sBAAsB,QAAQ,KAAK,cAAc,kBAAkB,MAAM,eAAe;AAAA,IAC/F;AAAA,EACF;AAEA,MAAI,MAAM,KAAK,WAAW,YAAY,SAAS,QAAQ,IAAI;AAG3D,MAAI,CAAC,KAAK;AACR,UAAM,iBAAiB,YAAY,SAAS,QAAQ,IAAI;AAAA,EAC1D;AAIA,SAAO,oBAAoB,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,KAAK,EAAE,IAAI;AACzE;AAEA,QAAQ,UAAU,qBAAqB,SAAS,mBAAoB,OAAO,MAAM,QAAQ;AACvF,MAAI,SAAS;AACb,MAAI,CAAC,SAAS,OAAO,IAAI,GAAG;AAC1B,aAAS;AACT,QAAI,MAAM;AACR,eAAS,KAAK,KAAK,SAAS,CAAC,MAAM;AACnC,aAAO,KAAK,QAAQ,MAAM,EAAE;AAC5B,YAAM,KAAK,IAAI;AACf,UAAI,UAAU,OAAO,IAAI,GAAG;AAC1B,iBAAS,OAAO,IAAI;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,QAAQ,UAAU,uBAAuB,SAAS,qBAAsB,OAAO,QAAQ,QAAQ;AAC7F,MAAI;AACJ,MAAI,SAAS,OAAO,MAAM,GAAG;AAC7B,KAAG;AACD,QAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,aAAS,KAAK,mBAAmB,OAAO,MAAM,MAAM;AACpD,WAAO,OAAO,IAAI,CAAC;AAAA,EACrB,SAAS,OAAO,UAAW,WAAW;AACtC,SAAO;AACT;AAEA,QAAQ,UAAU,sBAAsB,SAAS,oBAAqB,OAAO,OAAO,QAAQ;AAC1F,MAAI,SAAS;AACb,WAASA,KAAI,GAAIA,KAAI,MAAM,UAAY,UAAU,MAAM,GAAIA,MAAK;AAC9D,QAAI,SAAS,MAAMA,EAAC;AACpB,QAAI,SAAS,MAAM,GAAG;AACpB,eAAS,KAAK,qBAAqB,OAAO,QAAQ,MAAM;AAAA,IAC1D;AAAA,EACF;AACA,SAAO;AACT;AAEA,QAAQ,UAAU,kBAAkB,SAAS,gBAAiB,OAAO,gBAAgB;AACnF,MAAI,UAAU,IAAI;AAAE,WAAO,CAAC;AAAA,EAAE;AAE9B,MAAI,CAAC,KAAK,mBAAmB;AAC3B,SAAK,oBAAoB,CAAC;AAAA,EAC5B;AAEA,MAAI,QAAQ,KAAK,kBAAkB,KAAK;AACxC,MAAI,CAAC,OAAO;AACV,QAAI,CAAC,gBAAgB;AACnB,uBAAiB,KAAK;AAAA,IACxB;AACA,YAAQ,CAAC;AAGT,QAAI,QAAQ,CAAC,KAAK;AAGlB,WAAO,QAAQ,KAAK,GAAG;AACrB,cAAQ,KAAK;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAGA,QAAI;AACJ,QAAI,QAAQ,cAAc,GAAG;AAC3B,iBAAW;AAAA,IACb,WAAW,SAAS,cAAc,GAAG;AAEnC,UAAI,eAAe,SAAS,GAAG;AAC7B,mBAAW,eAAe,SAAS;AAAA,MACrC,OAAO;AACL,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,iBAAW;AAAA,IACb;AAGA,QAAI,SAAS,QAAQ,GAAG;AACtB,cAAQ,CAAC,QAAQ;AAAA,IACnB,OAAO;AACL,cAAQ;AAAA,IACV;AACA,QAAI,OAAO;AACT,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,SAAK,kBAAkB,KAAK,IAAI;AAAA,EAClC;AACA,SAAO;AACT;AAEA,QAAQ,UAAU,aAAa,SAAS,WACtC,UACA,QACA,UACA,KACA,MACA,iBACA,MACA;AACA,MAAI,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ;AACjD,MAAI;AACJ,WAASA,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,QAAI,OAAO,MAAMA,EAAC;AAClB,UACE,KAAK,aAAa,MAAM,SAAS,IAAI,GAAG,KAAK,MAAM,iBAAiB,MAAM,CAAC,GAAG,CAAC;AACjF,QAAI,CAAC,OAAO,GAAG,GAAG;AAChB,UAAI,SAAS,UAAU,QAAyC,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,sBAAsB,GAAG,GAAG;AACvI,aAAM,yCAAyC,MAAM,aAAa,OAAO,WAAY;AAAA,MACvF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,QAAQ,UAAU,KAAK,SAAS,GAAI,KAAK,SAAS,UAAU,MAAM;AAC9D,MAAI;AAEJ,MAAI,SAAS,CAAC,GAAG,MAAM,UAAU,SAAS;AAC1C,SAAQ,QAAQ,EAAI,QAAQ,GAAI,IAAI,UAAW,MAAM,CAAE;AACzD,MAAI,CAAC,KAAK;AAAE,WAAO;AAAA,EAAG;AAEtB,MAAI,aAAa,UAAU,MAAM,QAAQ,MAAM;AAC/C,MAAG,KAAK,sBAAsB;AAC5B,eAAW,SAAS,aAAa,WAAW,MAAM;AAAA,EACpD;AAEA,MAAI,SAAS,WAAW,UAAU;AAElC,MAAI,MAAM,KAAK;AAAA,IACb;AAAA,IAAU;AAAA,IAAQ,KAAK;AAAA,IAAgB;AAAA,IACvC;AAAA,IAAM;AAAA,IAAU,WAAW;AAAA,EAC7B;AACA,MAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,QAA6C,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,sBAAsB,GAAG,GAAG;AACpH,WAAM,yCAAyC,MAAM,qBAAsB;AAAA,IAC7E;AAEA,QAAI,CAAC,KAAK,OAAO;AAAE,YAAM,MAAM,kBAAkB;AAAA,IAAE;AACnD,YAAQ,MAAM,KAAK,OAAO,GAAG,MAAM,KAAK,CAAE,GAAI,EAAE,OAAQ,MAAO,CAAC;AAAA,EAClE,OAAO;AACL,UAAM,KAAK,aAAa,QAAQ,KAAK,KAAK,MAAM,QAAQ,QAAQ;AAChE,QAAI,KAAK,oBAAoB,QAAQ,QAAQ,QAAQ,QAAW;AAC9D,YAAM,KAAK,iBAAiB,KAAK,GAAG;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AACF;AAEA,QAAQ,UAAU,IAAI,SAASW,GAAG,KAAK;AACnC,MAAI;AAEJ,MAAI,SAAS,CAAC,GAAG,MAAM,UAAU,SAAS;AAC1C,SAAQ,QAAQ,EAAI,QAAQ,GAAI,IAAI,UAAW,MAAM,CAAE;AACzD,UAAQ,MAAM,MAAM,GAAG,MAAM,KAAK,CAAE,KAAK,KAAK,QAAQ,KAAK,aAAa,GAAG,IAAK,EAAE,OAAQ,MAAO,CAAC;AACpG;AAEA,QAAQ,UAAU,KAAK,SAAS,GAAI,KAAK,QAAQ,UAAU,MAAM,QAAQ;AACvE,MAAI,MACF,KAAK,WAAW,UAAU,QAAQ,KAAK,gBAAgB,KAAK,MAAM,OAAO,MAAM;AACjF,MAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,QAA6C,CAAC,KAAK,yBAAyB,GAAG,GAAG;AAChF,WAAM,2CAA2C,MAAM,qBAAsB;AAAA,IAC/E;AACA,QAAI,CAAC,KAAK,OAAO;AAAE,YAAM,MAAM,kBAAkB;AAAA,IAAE;AACnD,WAAO,KAAK,MAAM,MAAM,EAAE,KAAK,QAAQ,MAAM;AAAA,EAC/C,OAAO;AACL,WAAO,KAAK,aAAa,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG,KAAK;AAAA,EAClE;AACF;AAEA,QAAQ,UAAU,IAAI,SAAS,EAAG,KAAK,QAAQ,QAAQ;AAErD,MAAI,CAAC,KAAK;AAAE,WAAO;AAAA,EAAG;AAEtB,MAAI,CAAC,SAAS,MAAM,GAAG;AACrB,aAAS,KAAK;AAAA,EAChB;AAEA,SAAO,KAAK,GAAG,KAAK,QAAQ,KAAK,aAAa,GAAG,MAAM,MAAM;AAC/D;AAEA,QAAQ,UAAU,MAAM,SAAS,IAC/B,KACA,SACA,UACA,MACA,QACA;AACE,MAAI;AAEJ,MAAI,SAAS,CAAC,GAAG,MAAM,UAAU,SAAS;AAC1C,SAAQ,QAAQ,EAAI,QAAQ,GAAI,IAAI,UAAW,MAAM,CAAE;AACzD,MAAI,CAAC,KAAK;AAAE,WAAO;AAAA,EAAG;AACtB,MAAI,WAAW,QAAW;AACxB,aAAS;AAAA,EACX;AAEA,MAAI,aAAa,EAAE,SAAS,QAAQ,KAAK,OAAO;AAChD,MAAI,aAAa,UAAU,MAAM,QAAQ,MAAM;AAC/C,aAAW,SAAS,OAAO,OAAO,YAAY,WAAW,MAAM;AAC/D,WAAS,WAAW,WAAW,OAAO,CAAC,WAAW,MAAM,IAAI,CAAC,WAAW,QAAQ,WAAW,MAAM;AACjG,SAAO,KAAK,aAAa,MAAM,MAAM,GAAG,MAAM,KAAK,CAAE,KAAK,SAAS,UAAU,IAAK,EAAE,OAAQ,MAAO,CAAC,GAAG,MAAM;AAC/G;AAEA,QAAQ,UAAU,cAAc,SAAS,YAAa,SAAS,QAAQ;AAErE,MAAI,CAAC,WAAW,CAAC,SAAS,OAAO,GAAG;AAAE,WAAO;AAAA,EAAK;AAClD,MAAI,UAAU,QAAQ,MAAM,GAAG;AAE/B,WAAS,KAAK,eAAe,QAAQ,QAAQ,MAAM;AACnD,MAAI,CAAC,QAAQ,MAAM,GAAG;AAAE,WAAO;AAAA,EAAQ;AACvC,SAAO,QAAQ,MAAM,EAAE,KAAK;AAC9B;AAEA,QAAQ,UAAU,KAAK,SAAS,GAAI,KAAK,QAAQ;AAC7C,MAAI;AAEJ,MAAI,SAAS,CAAC,GAAG,MAAM,UAAU,SAAS;AAC1C,SAAQ,QAAQ,EAAI,QAAQ,GAAI,IAAI,UAAW,MAAM,CAAE;AACzD,UAAQ,MAAM,MAAM,IAAI,MAAM,KAAK,CAAE,KAAK,KAAK,QAAQ,KAAK,aAAa,GAAG,MAAM,MAAO,EAAE,OAAQ,MAAO,CAAC;AAC7G;AAEA,QAAQ,UAAU,MAAM,SAAS,IAAK,KAAK,QAAQ,UAAU;AACzD,MAAI,OAAO,CAAC,GAAG,MAAM,UAAU,SAAS;AACxC,SAAQ,QAAQ,EAAI,MAAM,GAAI,IAAI,UAAW,MAAM,CAAE;AAEvD,MAAI,UAAU,UAAU,MAAM,QAAQ,IAAI,EAAE,UAAU;AACtD,SAAO,KAAK,OAAO,SAAS,OAAO,GAAG,GAAG;AAC3C;AAEA,QAAQ,UAAU,KAAK,SAAS,GAAI,KAAK,QAAQ;AAC/C,SAAO,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,aAAa,GAAG,MAAM;AAC/D;AAEA,QAAQ,UAAU,mBAAmB,SAAS,iBAAkB,QAAQ;AACtE,SAAO,WAAW,KAAK,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC;AACnD;AAEA,QAAQ,UAAU,mBAAmB,SAAS,iBAAkB,QAAQ,SAAS;AAC/E,MAAI,KAAK,uBAAuB,UAAU,KAAK,uBAAuB,SAAS;AAC7E,SAAK,oBAAoB,QAAQ,KAAK,oBAAoB,OAAO;AAAA,EACnE;AACA,OAAK,IAAI,KAAK,KAAK,IAAI,UAAU,QAAQ,OAAO;AAClD;AAEA,QAAQ,UAAU,qBAAqB,SAAS,mBAAoB,QAAQ,SAAS;AACnF,MAAI,KAAK,uBAAuB,UAAU,KAAK,uBAAuB,SAAS;AAC7E,SAAK,oBAAoB,QAAQ,KAAK,oBAAoB,OAAO;AAAA,EACnE;AACA,OAAK,IAAI,KAAK,KAAK,IAAI,UAAU,QAAQ;AAAA,IACvC,OAAO,KAAK,IAAI,SAAS,MAAM,MAAM,eAAe,OAAO,KAAK,KAAK,IAAI,SAAS,MAAM,CAAC,EAAE,SACvF,OAAO,OAAO,CAAC,GAAG,KAAK,IAAI,SAAS,MAAM,CAAC,IAC3C,CAAC;AAAA,IACL;AAAA,EACF,CAAC;AACH;AAEA,QAAQ,UAAU,oBAAoB,SAAS,kBAAmB,QAAQ;AACxE,SAAO,WAAW,KAAK,IAAI,gBAAgB,MAAM,KAAK,CAAC,CAAC;AAC1D;AAEA,QAAQ,UAAU,oBAAoB,SAAS,kBAAmB,QAAQ,QAAQ;AAChF,OAAK,IAAI,KAAK,KAAK,IAAI,iBAAiB,QAAQ,MAAM;AACtD,OAAK,qBAAqB,QAAQ,MAAM;AAC1C;AAEA,QAAQ,UAAU,sBAAsB,SAAS,oBAAqB,QAAQ,QAAQ;AACpF,OAAK,IAAI,KAAK,KAAK,IAAI,iBAAiB,QAAQ,MAAM,KAAK,IAAI,gBAAgB,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC;AACrG,OAAK,qBAAqB,QAAQ,MAAM;AAC1C;AAEA,QAAQ,UAAU,uBAAuB,SAAS,qBAAsB,QAAQ,QAAQ;AAEtF,WAAS,OAAO,QAAQ;AACtB,QAAI,KAAK,SAAS,OAAO;AAEzB,QAAI,CAAC,KAAK,oBAAoB,eAAe,EAAE,GAAG;AAChD;AAAA,IACF;AAEA,WAAO,KAAK,oBAAoB,EAAE;AAAA,EACpC;AACF;AAEA,QAAQ,UAAU,oBAAoB,SAAS,kBAC7C,OACA,QACA,UACA,iBACA,KACA,SACA;AACA,MAAI,UAAU;AACd,MAAI,UAAU,gBAAgB,OAAO;AAErC,MAAI,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ;AACjD,WAASX,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,QAAI,UAAU;AACd,QAAI,OAAO,MAAMA,EAAC;AAClB,cAAU,gBAAgB,IAAI;AAC9B,cAAU;AAEV,QAAI,OAAO,OAAO,KAAK,OAAO,QAAQ,GAAG,CAAC,GAAG;AAC3C,UAAI,SAAS,UAAU,QAAyC,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,sBAAsB,GAAG,GAAG;AACvI,aAAM,mBAAmB,OAAO,8BAA8B,UAAU,qBAAsB;AAAA,MAChG;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,OAAO,KAAK,OAAO,QAAQ,GAAG,CAAC,GAAG;AAC3C,WAAO;AAAA,EACT,OAAO;AACL,QAAI,SAAS,QAAQ,GAAG;AAExB,QAAI;AACJ,QAAI,SAAS;AACX,kBAAY,IAAI,KAAK,eAAe,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO,CAAC;AAAA,IACjF,OAAO;AACL,UAAI,KAAK,UAAU,OAAO;AAC1B,kBAAY,KAAK,oBAAoB,EAAE;AACvC,UAAI,CAAC,WAAW;AACd,oBAAY,KAAK,oBAAoB,EAAE,IAAI,IAAI,KAAK,eAAe,SAAS,MAAM;AAAA,MACpF;AAAA,IACF;AAEA,WAAO,UAAU,OAAO,KAAK;AAAA,EAC/B;AACF;AAEA,QAAQ,UAAU,KAAK,SAAS,GAAI,OAAO,QAAQ,KAAK,SAAS;AAE/D,MAA6C,CAAC,QAAQ,eAAe,gBAAgB;AACnF,SAAK,sEAAsE;AAC3E,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,KAAK;AACR,QAAI,MAAM,CAAC,UAAU,IAAI,KAAK,eAAe,MAAM,IAAI,IAAI,KAAK,eAAe,QAAQ,OAAO;AAC9F,WAAO,IAAI,OAAO,KAAK;AAAA,EACzB;AAEA,MAAI,MACF,KAAK,kBAAkB,OAAO,QAAQ,KAAK,gBAAgB,KAAK,oBAAoB,GAAG,KAAK,OAAO;AACrG,MAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,QAA6C,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,sBAAsB,GAAG,GAAG;AACpH,WAAM,sDAAsD,MAAM,IAAK;AAAA,IACzE;AAEA,QAAI,CAAC,KAAK,OAAO;AAAE,YAAM,MAAM,kBAAkB;AAAA,IAAE;AACnD,WAAO,KAAK,MAAM,MAAM,EAAE,OAAO,KAAK,MAAM;AAAA,EAC9C,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;AAEA,QAAQ,UAAU,IAAI,SAAS,EAAG,OAAO;AACrC,MAAI,OAAO,CAAC,GAAG,MAAM,UAAU,SAAS;AACxC,SAAQ,QAAQ,EAAI,MAAM,GAAI,IAAI,UAAW,MAAM,CAAE;AAEvD,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM;AACV,MAAI,UAAU;AAEd,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,YAAM,KAAK,CAAC;AAAA,IACd,WAAW,SAAS,KAAK,CAAC,CAAC,GAAG;AAC5B,UAAI,KAAK,CAAC,EAAE,QAAQ;AAClB,iBAAS,KAAK,CAAC,EAAE;AAAA,MACnB;AACA,UAAI,KAAK,CAAC,EAAE,KAAK;AACf,cAAM,KAAK,CAAC,EAAE;AAAA,MAChB;AAAA,IACF;AAEA,cAAU,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,SAAU,KAAKY,MAAK;AACtD,UAAI;AAEN,UAAI,SAAS,oBAAoBA,IAAG,GAAG;AACrC,eAAO,OAAO,OAAO,CAAC,GAAG,MAAO,MAAM,CAAC,GAAG,IAAIA,IAAG,IAAI,KAAK,CAAC,EAAEA,IAAG,GAAG,IAAK;AAAA,MAC1E;AACA,aAAO;AAAA,IACT,GAAG,IAAI;AAAA,EAET,WAAW,KAAK,WAAW,GAAG;AAC5B,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,YAAM,KAAK,CAAC;AAAA,IACd;AACA,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,eAAS,KAAK,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,KAAK,GAAG,OAAO,QAAQ,KAAK,OAAO;AAC5C;AAEA,QAAQ,UAAU,kBAAkB,SAAS,gBAAiB,QAAQ;AACpE,SAAO,WAAW,KAAK,IAAI,cAAc,MAAM,KAAK,CAAC,CAAC;AACxD;AAEA,QAAQ,UAAU,kBAAkB,SAAS,gBAAiB,QAAQ,QAAQ;AAC5E,OAAK,IAAI,KAAK,KAAK,IAAI,eAAe,QAAQ,MAAM;AACpD,OAAK,mBAAmB,QAAQ,MAAM;AACxC;AAEA,QAAQ,UAAU,oBAAoB,SAAS,kBAAmB,QAAQ,QAAQ;AAChF,OAAK,IAAI,KAAK,KAAK,IAAI,eAAe,QAAQ,MAAM,KAAK,IAAI,cAAc,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC;AACjG,OAAK,mBAAmB,QAAQ,MAAM;AACxC;AAEA,QAAQ,UAAU,qBAAqB,SAAS,mBAAoB,QAAQ,QAAQ;AAElF,WAAS,OAAO,QAAQ;AACtB,QAAI,KAAK,SAAS,OAAO;AAEzB,QAAI,CAAC,KAAK,kBAAkB,eAAe,EAAE,GAAG;AAC9C;AAAA,IACF;AAEA,WAAO,KAAK,kBAAkB,EAAE;AAAA,EAClC;AACF;AAEA,QAAQ,UAAU,sBAAsB,SAAS,oBAC/C,OACA,QACA,UACA,eACA,KACA,SACA;AACA,MAAI,UAAU;AACd,MAAI,UAAU,cAAc,OAAO;AAEnC,MAAI,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ;AACjD,WAASZ,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,QAAI,UAAU;AACd,QAAI,OAAO,MAAMA,EAAC;AAClB,cAAU,cAAc,IAAI;AAC5B,cAAU;AAEV,QAAI,OAAO,OAAO,KAAK,OAAO,QAAQ,GAAG,CAAC,GAAG;AAC3C,UAAI,SAAS,UAAU,QAAyC,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,sBAAsB,GAAG,GAAG;AACvI,aAAM,mBAAmB,OAAO,4BAA4B,UAAU,mBAAoB;AAAA,MAC5F;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,OAAO,KAAK,OAAO,QAAQ,GAAG,CAAC,GAAG;AAC3C,WAAO;AAAA,EACT,OAAO;AACL,QAAI,SAAS,QAAQ,GAAG;AAExB,QAAI;AACJ,QAAI,SAAS;AAEX,kBAAY,IAAI,KAAK,aAAa,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO,CAAC;AAAA,IAC/E,OAAO;AACL,UAAI,KAAK,UAAU,OAAO;AAC1B,kBAAY,KAAK,kBAAkB,EAAE;AACrC,UAAI,CAAC,WAAW;AACd,oBAAY,KAAK,kBAAkB,EAAE,IAAI,IAAI,KAAK,aAAa,SAAS,MAAM;AAAA,MAChF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,QAAQ,UAAU,KAAK,SAAS,GAAI,OAAO,QAAQ,KAAK,SAAS;AAE/D,MAAI,CAAC,QAAQ,eAAe,cAAc;AACxC,QAAI,MAAuC;AACzC,WAAK,sEAAsE;AAAA,IAC7E;AACA,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,KAAK;AACR,QAAI,KAAK,CAAC,UAAU,IAAI,KAAK,aAAa,MAAM,IAAI,IAAI,KAAK,aAAa,QAAQ,OAAO;AACzF,WAAO,GAAG,OAAO,KAAK;AAAA,EACxB;AAEA,MAAI,YAAY,KAAK,oBAAoB,OAAO,QAAQ,KAAK,gBAAgB,KAAK,kBAAkB,GAAG,KAAK,OAAO;AACnH,MAAI,MAAM,aAAa,UAAU,OAAO,KAAK;AAC7C,MAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,QAA6C,CAAC,KAAK,yBAAyB,GAAG,KAAK,CAAC,KAAK,sBAAsB,GAAG,GAAG;AACpH,WAAM,oDAAoD,MAAM,IAAK;AAAA,IACvE;AAEA,QAAI,CAAC,KAAK,OAAO;AAAE,YAAM,MAAM,kBAAkB;AAAA,IAAE;AACnD,WAAO,KAAK,MAAM,MAAM,EAAE,OAAO,OAAO,OAAO,CAAC,GAAG,EAAE,KAAU,OAAe,GAAG,OAAO,CAAC;AAAA,EAC3F,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;AAEA,QAAQ,UAAU,IAAI,SAAS,EAAG,OAAO;AACrC,MAAI,OAAO,CAAC,GAAG,MAAM,UAAU,SAAS;AACxC,SAAQ,QAAQ,EAAI,MAAM,GAAI,IAAI,UAAW,MAAM,CAAE;AAEvD,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM;AACV,MAAI,UAAU;AAEd,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,YAAM,KAAK,CAAC;AAAA,IACd,WAAW,SAAS,KAAK,CAAC,CAAC,GAAG;AAC5B,UAAI,KAAK,CAAC,EAAE,QAAQ;AAClB,iBAAS,KAAK,CAAC,EAAE;AAAA,MACnB;AACA,UAAI,KAAK,CAAC,EAAE,KAAK;AACf,cAAM,KAAK,CAAC,EAAE;AAAA,MAChB;AAGA,gBAAU,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,SAAU,KAAKY,MAAK;AACtD,YAAI;AAEN,YAAI,SAAS,kBAAkBA,IAAG,GAAG;AACnC,iBAAO,OAAO,OAAO,CAAC,GAAG,MAAO,MAAM,CAAC,GAAG,IAAIA,IAAG,IAAI,KAAK,CAAC,EAAEA,IAAG,GAAG,IAAK;AAAA,QAC1E;AACA,eAAO;AAAA,MACT,GAAG,IAAI;AAAA,IACT;AAAA,EACF,WAAW,KAAK,WAAW,GAAG;AAC5B,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,YAAM,KAAK,CAAC;AAAA,IACd;AACA,QAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,eAAS,KAAK,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,KAAK,GAAG,OAAO,QAAQ,KAAK,OAAO;AAC5C;AAEA,QAAQ,UAAU,OAAO,SAAS,KAAM,OAAO,QAAQ,KAAK,SAAS;AAEnE,MAAI,CAAC,QAAQ,eAAe,cAAc;AACxC,QAAI,MAAuC;AACzC,WAAK,+EAA+E;AAAA,IACtF;AACA,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,CAAC,KAAK;AACR,QAAI,KAAK,CAAC,UAAU,IAAI,KAAK,aAAa,MAAM,IAAI,IAAI,KAAK,aAAa,QAAQ,OAAO;AACzF,WAAO,GAAG,cAAc,KAAK;AAAA,EAC/B;AAEA,MAAI,YAAY,KAAK,oBAAoB,OAAO,QAAQ,KAAK,gBAAgB,KAAK,kBAAkB,GAAG,KAAK,OAAO;AACnH,MAAI,MAAM,aAAa,UAAU,cAAc,KAAK;AACpD,MAAI,KAAK,gBAAgB,GAAG,GAAG;AAC7B,QAA6C,CAAC,KAAK,yBAAyB,GAAG,GAAG;AAChF,WAAM,uDAAuD,MAAM,KAAM;AAAA,IAC3E;AAEA,QAAI,CAAC,KAAK,OAAO;AAAE,YAAM,MAAM,kBAAkB;AAAA,IAAE;AACnD,WAAO,KAAK,MAAM,MAAM,KAAK,OAAO,QAAQ,KAAK,OAAO;AAAA,EAC1D,OAAO;AACL,WAAO,OAAO,CAAC;AAAA,EACjB;AACF;AAEA,OAAO,iBAAkB,QAAQ,WAAW,kBAAmB;AAE/D,IAAI;AAEJ,OAAO,eAAe,SAAS,kBAAkB;AAAA,EAC/C,KAAK,SAAS,MAAO;AACnB,QAAI,CAAC,gBAAgB;AACnB,UAAI,cAAc,OAAO,SAAS;AAClC,uBAAiB;AAAA,QACf,gBAAgB,eAAe,OAAO,KAAK,mBAAmB;AAAA,QAC9D,cAAc,eAAe,OAAO,KAAK,iBAAiB;AAAA,MAC5D;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF,CAAC;AAED,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAElB,IAAO,uBAAQ;", "names": ["i", "<PERSON><PERSON>", "get", "render", "BaseFormatter", "I18nPath", "VueI18n", "locale", "level", "message", "paths", "t", "key"]}