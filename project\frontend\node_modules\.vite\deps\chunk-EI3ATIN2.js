import {
  require_isLength
} from "./chunk-MYGK6PJD.js";
import {
  require_isFunction
} from "./chunk-DAWEUZO3.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/isArrayLike.js
var require_isArrayLike = __commonJS({
  "node_modules/lodash/isArrayLike.js"(exports, module) {
    var isFunction = require_isFunction();
    var isLength = require_isLength();
    function isArrayLike(value) {
      return value != null && isLength(value.length) && !isFunction(value);
    }
    module.exports = isArrayLike;
  }
});

export {
  require_isArrayLike
};
//# sourceMappingURL=chunk-EI3ATIN2.js.map
