import "./chunk-FMF7AJZE.js";
import {
  Extension
} from "./chunk-55TMZW6M.js";
import "./chunk-L55O3UNQ.js";
import "./chunk-KHEZUDY3.js";
import "./chunk-PLDDJCW6.js";

// node_modules/@tiptap/extension-font-family/dist/index.js
var FontFamily = Extension.create({
  name: "fontFamily",
  addOptions() {
    return {
      types: ["textStyle"]
    };
  },
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontFamily: {
            default: null,
            parseHTML: (element) => element.style.fontFamily,
            renderHTML: (attributes) => {
              if (!attributes.fontFamily) {
                return {};
              }
              return {
                style: `font-family: ${attributes.fontFamily}`
              };
            }
          }
        }
      }
    ];
  },
  addCommands() {
    return {
      setFontFamily: (fontFamily) => ({ chain }) => {
        return chain().setMark("textStyle", { fontFamily }).run();
      },
      unsetFontFamily: () => ({ chain }) => {
        return chain().setMark("textStyle", { fontFamily: null }).removeEmptyTextStyle().run();
      }
    };
  }
});
export {
  FontFamily,
  FontFamily as default
};
//# sourceMappingURL=@tiptap_extension-font-family.js.map
