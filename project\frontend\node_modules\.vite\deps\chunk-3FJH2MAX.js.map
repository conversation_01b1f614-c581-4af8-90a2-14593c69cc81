{"version": 3, "sources": ["../../lodash/_hasUnicode.js"], "sourcesContent": ["/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsZWJ = '\\\\u200d';\n\n/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */\nvar reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboRange + rsVarRange + ']');\n\n/**\n * Checks if `string` contains Unicode symbols.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {boolean} Returns `true` if a symbol is found, else `false`.\n */\nfunction hasUnicode(string) {\n  return reHasUnicode.test(string);\n}\n\nmodule.exports = hasUnicode;\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AAApB,QACI,oBAAoB;AADxB,QAEI,wBAAwB;AAF5B,QAGI,sBAAsB;AAH1B,QAII,eAAe,oBAAoB,wBAAwB;AAJ/D,QAKI,aAAa;AAGjB,QAAI,QAAQ;AAGZ,QAAI,eAAe,OAAO,MAAM,QAAQ,gBAAiB,eAAe,aAAa,GAAG;AASxF,aAAS,WAAW,QAAQ;AAC1B,aAAO,aAAa,KAAK,MAAM;AAAA,IACjC;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}