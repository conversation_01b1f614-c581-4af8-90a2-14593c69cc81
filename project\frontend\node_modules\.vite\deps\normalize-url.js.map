{"version": 3, "sources": ["../../normalize-url/index.js"], "sourcesContent": ["// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\nconst DATA_URL_DEFAULT_MIME_TYPE = 'text/plain';\nconst DATA_URL_DEFAULT_CHARSET = 'us-ascii';\n\nconst testParameter = (name, filters) => filters.some(filter => filter instanceof RegExp ? filter.test(name) : filter === name);\n\nconst supportedProtocols = new Set([\n\t'https:',\n\t'http:',\n\t'file:',\n]);\n\nconst hasCustomProtocol = urlString => {\n\ttry {\n\t\tconst {protocol} = new URL(urlString);\n\n\t\treturn protocol.endsWith(':')\n\t\t\t&& !protocol.includes('.')\n\t\t\t&& !supportedProtocols.has(protocol);\n\t} catch {\n\t\treturn false;\n\t}\n};\n\nconst normalizeDataURL = (urlString, {stripHash}) => {\n\tconst match = /^data:(?<type>[^,]*?),(?<data>[^#]*?)(?:#(?<hash>.*))?$/.exec(urlString);\n\n\tif (!match) {\n\t\tthrow new Error(`Invalid URL: ${urlString}`);\n\t}\n\n\tlet {type, data, hash} = match.groups;\n\tconst mediaType = type.split(';');\n\thash = stripHash ? '' : hash;\n\n\tlet isBase64 = false;\n\tif (mediaType[mediaType.length - 1] === 'base64') {\n\t\tmediaType.pop();\n\t\tisBase64 = true;\n\t}\n\n\t// Lowercase MIME type\n\tconst mimeType = mediaType.shift()?.toLowerCase() ?? '';\n\tconst attributes = mediaType\n\t\t.map(attribute => {\n\t\t\tlet [key, value = ''] = attribute.split('=').map(string => string.trim());\n\n\t\t\t// Lowercase `charset`\n\t\t\tif (key === 'charset') {\n\t\t\t\tvalue = value.toLowerCase();\n\n\t\t\t\tif (value === DATA_URL_DEFAULT_CHARSET) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn `${key}${value ? `=${value}` : ''}`;\n\t\t})\n\t\t.filter(Boolean);\n\n\tconst normalizedMediaType = [\n\t\t...attributes,\n\t];\n\n\tif (isBase64) {\n\t\tnormalizedMediaType.push('base64');\n\t}\n\n\tif (normalizedMediaType.length > 0 || (mimeType && mimeType !== DATA_URL_DEFAULT_MIME_TYPE)) {\n\t\tnormalizedMediaType.unshift(mimeType);\n\t}\n\n\treturn `data:${normalizedMediaType.join(';')},${isBase64 ? data.trim() : data}${hash ? `#${hash}` : ''}`;\n};\n\nexport default function normalizeUrl(urlString, options) {\n\toptions = {\n\t\tdefaultProtocol: 'http',\n\t\tnormalizeProtocol: true,\n\t\tforceHttp: false,\n\t\tforceHttps: false,\n\t\tstripAuthentication: true,\n\t\tstripHash: false,\n\t\tstripTextFragment: true,\n\t\tstripWWW: true,\n\t\tremoveQueryParameters: [/^utm_\\w+/i],\n\t\tremoveTrailingSlash: true,\n\t\tremoveSingleSlash: true,\n\t\tremoveDirectoryIndex: false,\n\t\tremoveExplicitPort: false,\n\t\tsortQueryParameters: true,\n\t\t...options,\n\t};\n\n\t// Legacy: Append `:` to the protocol if missing.\n\tif (typeof options.defaultProtocol === 'string' && !options.defaultProtocol.endsWith(':')) {\n\t\toptions.defaultProtocol = `${options.defaultProtocol}:`;\n\t}\n\n\turlString = urlString.trim();\n\n\t// Data URL\n\tif (/^data:/i.test(urlString)) {\n\t\treturn normalizeDataURL(urlString, options);\n\t}\n\n\tif (hasCustomProtocol(urlString)) {\n\t\treturn urlString;\n\t}\n\n\tconst hasRelativeProtocol = urlString.startsWith('//');\n\tconst isRelativeUrl = !hasRelativeProtocol && /^\\.*\\//.test(urlString);\n\n\t// Prepend protocol\n\tif (!isRelativeUrl) {\n\t\turlString = urlString.replace(/^(?!(?:\\w+:)?\\/\\/)|^\\/\\//, options.defaultProtocol);\n\t}\n\n\tconst urlObject = new URL(urlString);\n\n\tif (options.forceHttp && options.forceHttps) {\n\t\tthrow new Error('The `forceHttp` and `forceHttps` options cannot be used together');\n\t}\n\n\tif (options.forceHttp && urlObject.protocol === 'https:') {\n\t\turlObject.protocol = 'http:';\n\t}\n\n\tif (options.forceHttps && urlObject.protocol === 'http:') {\n\t\turlObject.protocol = 'https:';\n\t}\n\n\t// Remove auth\n\tif (options.stripAuthentication) {\n\t\turlObject.username = '';\n\t\turlObject.password = '';\n\t}\n\n\t// Remove hash\n\tif (options.stripHash) {\n\t\turlObject.hash = '';\n\t} else if (options.stripTextFragment) {\n\t\turlObject.hash = urlObject.hash.replace(/#?:~:text.*?$/i, '');\n\t}\n\n\t// Remove duplicate slashes if not preceded by a protocol\n\t// NOTE: This could be implemented using a single negative lookbehind\n\t// regex, but we avoid that to maintain compatibility with older js engines\n\t// which do not have support for that feature.\n\tif (urlObject.pathname) {\n\t\t// TODO: Replace everything below with `urlObject.pathname = urlObject.pathname.replace(/(?<!\\b[a-z][a-z\\d+\\-.]{1,50}:)\\/{2,}/g, '/');` when Safari supports negative lookbehind.\n\n\t\t// Split the string by occurrences of this protocol regex, and perform\n\t\t// duplicate-slash replacement on the strings between those occurrences\n\t\t// (if any).\n\t\tconst protocolRegex = /\\b[a-z][a-z\\d+\\-.]{1,50}:\\/\\//g;\n\n\t\tlet lastIndex = 0;\n\t\tlet result = '';\n\t\tfor (;;) {\n\t\t\tconst match = protocolRegex.exec(urlObject.pathname);\n\t\t\tif (!match) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst protocol = match[0];\n\t\t\tconst protocolAtIndex = match.index;\n\t\t\tconst intermediate = urlObject.pathname.slice(lastIndex, protocolAtIndex);\n\n\t\t\tresult += intermediate.replace(/\\/{2,}/g, '/');\n\t\t\tresult += protocol;\n\t\t\tlastIndex = protocolAtIndex + protocol.length;\n\t\t}\n\n\t\tconst remnant = urlObject.pathname.slice(lastIndex, urlObject.pathname.length);\n\t\tresult += remnant.replace(/\\/{2,}/g, '/');\n\n\t\turlObject.pathname = result;\n\t}\n\n\t// Decode URI octets\n\tif (urlObject.pathname) {\n\t\ttry {\n\t\t\turlObject.pathname = decodeURI(urlObject.pathname).replace(/\\\\/g, '%5C');\n\t\t} catch {}\n\t}\n\n\t// Remove directory index\n\tif (options.removeDirectoryIndex === true) {\n\t\toptions.removeDirectoryIndex = [/^index\\.[a-z]+$/];\n\t}\n\n\tif (Array.isArray(options.removeDirectoryIndex) && options.removeDirectoryIndex.length > 0) {\n\t\tlet pathComponents = urlObject.pathname.split('/');\n\t\tconst lastComponent = pathComponents[pathComponents.length - 1];\n\n\t\tif (testParameter(lastComponent, options.removeDirectoryIndex)) {\n\t\t\tpathComponents = pathComponents.slice(0, -1);\n\t\t\turlObject.pathname = pathComponents.slice(1).join('/') + '/';\n\t\t}\n\t}\n\n\tif (urlObject.hostname) {\n\t\t// Remove trailing dot\n\t\turlObject.hostname = urlObject.hostname.replace(/\\.$/, '');\n\n\t\t// Remove `www.`\n\t\tif (options.stripWWW && /^www\\.(?!www\\.)[a-z\\-\\d]{1,63}\\.[a-z.\\-\\d]{2,63}$/.test(urlObject.hostname)) {\n\t\t\t// Each label should be max 63 at length (min: 1).\n\t\t\t// Source: https://en.wikipedia.org/wiki/Hostname#Restrictions_on_valid_host_names\n\t\t\t// Each TLD should be up to 63 characters long (min: 2).\n\t\t\t// It is technically possible to have a single character TLD, but none currently exist.\n\t\t\turlObject.hostname = urlObject.hostname.replace(/^www\\./, '');\n\t\t}\n\t}\n\n\t// Remove query unwanted parameters\n\tif (Array.isArray(options.removeQueryParameters)) {\n\t\t// eslint-disable-next-line unicorn/no-useless-spread -- We are intentionally spreading to get a copy.\n\t\tfor (const key of [...urlObject.searchParams.keys()]) {\n\t\t\tif (testParameter(key, options.removeQueryParameters)) {\n\t\t\t\turlObject.searchParams.delete(key);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!Array.isArray(options.keepQueryParameters) && options.removeQueryParameters === true) {\n\t\turlObject.search = '';\n\t}\n\n\t// Keep wanted query parameters\n\tif (Array.isArray(options.keepQueryParameters) && options.keepQueryParameters.length > 0) {\n\t\t// eslint-disable-next-line unicorn/no-useless-spread -- We are intentionally spreading to get a copy.\n\t\tfor (const key of [...urlObject.searchParams.keys()]) {\n\t\t\tif (!testParameter(key, options.keepQueryParameters)) {\n\t\t\t\turlObject.searchParams.delete(key);\n\t\t\t}\n\t\t}\n\t}\n\n\t// Sort query parameters\n\tif (options.sortQueryParameters) {\n\t\turlObject.searchParams.sort();\n\n\t\t// Calling `.sort()` encodes the search parameters, so we need to decode them again.\n\t\ttry {\n\t\t\turlObject.search = decodeURIComponent(urlObject.search);\n\t\t} catch {}\n\t}\n\n\tif (options.removeTrailingSlash) {\n\t\turlObject.pathname = urlObject.pathname.replace(/\\/$/, '');\n\t}\n\n\t// Remove an explicit port number, excluding a default port number, if applicable\n\tif (options.removeExplicitPort && urlObject.port) {\n\t\turlObject.port = '';\n\t}\n\n\tconst oldUrlString = urlString;\n\n\t// Take advantage of many of the Node `url` normalizations\n\turlString = urlObject.toString();\n\n\tif (!options.removeSingleSlash && urlObject.pathname === '/' && !oldUrlString.endsWith('/') && urlObject.hash === '') {\n\t\turlString = urlString.replace(/\\/$/, '');\n\t}\n\n\t// Remove ending `/` unless removeSingleSlash is false\n\tif ((options.removeTrailingSlash || urlObject.pathname === '/') && urlObject.hash === '' && options.removeSingleSlash) {\n\t\turlString = urlString.replace(/\\/$/, '');\n\t}\n\n\t// Restore relative protocol, if applicable\n\tif (hasRelativeProtocol && !options.normalizeProtocol) {\n\t\turlString = urlString.replace(/^http:\\/\\//, '//');\n\t}\n\n\t// Remove http/https\n\tif (options.stripProtocol) {\n\t\turlString = urlString.replace(/^(?:https?:)?\\/\\//, '');\n\t}\n\n\treturn urlString;\n}\n"], "mappings": ";;;AACA,IAAM,6BAA6B;AACnC,IAAM,2BAA2B;AAEjC,IAAM,gBAAgB,CAAC,MAAM,YAAY,QAAQ,KAAK,YAAU,kBAAkB,SAAS,OAAO,KAAK,IAAI,IAAI,WAAW,IAAI;AAE9H,IAAM,qBAAqB,oBAAI,IAAI;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAED,IAAM,oBAAoB,eAAa;AACtC,MAAI;AACH,UAAM,EAAC,SAAQ,IAAI,IAAI,IAAI,SAAS;AAEpC,WAAO,SAAS,SAAS,GAAG,KACxB,CAAC,SAAS,SAAS,GAAG,KACtB,CAAC,mBAAmB,IAAI,QAAQ;AAAA,EACrC,QAAQ;AACP,WAAO;AAAA,EACR;AACD;AAEA,IAAM,mBAAmB,CAAC,WAAW,EAAC,UAAS,MAAM;AACpD,QAAM,QAAQ,0DAA0D,KAAK,SAAS;AAEtF,MAAI,CAAC,OAAO;AACX,UAAM,IAAI,MAAM,gBAAgB,SAAS,EAAE;AAAA,EAC5C;AAEA,MAAI,EAAC,MAAM,MAAM,KAAI,IAAI,MAAM;AAC/B,QAAM,YAAY,KAAK,MAAM,GAAG;AAChC,SAAO,YAAY,KAAK;AAExB,MAAI,WAAW;AACf,MAAI,UAAU,UAAU,SAAS,CAAC,MAAM,UAAU;AACjD,cAAU,IAAI;AACd,eAAW;AAAA,EACZ;AAGA,QAAM,WAAW,UAAU,MAAM,GAAG,YAAY,KAAK;AACrD,QAAM,aAAa,UACjB,IAAI,eAAa;AACjB,QAAI,CAAC,KAAK,QAAQ,EAAE,IAAI,UAAU,MAAM,GAAG,EAAE,IAAI,YAAU,OAAO,KAAK,CAAC;AAGxE,QAAI,QAAQ,WAAW;AACtB,cAAQ,MAAM,YAAY;AAE1B,UAAI,UAAU,0BAA0B;AACvC,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,GAAG,GAAG,GAAG,QAAQ,IAAI,KAAK,KAAK,EAAE;AAAA,EACzC,CAAC,EACA,OAAO,OAAO;AAEhB,QAAM,sBAAsB;AAAA,IAC3B,GAAG;AAAA,EACJ;AAEA,MAAI,UAAU;AACb,wBAAoB,KAAK,QAAQ;AAAA,EAClC;AAEA,MAAI,oBAAoB,SAAS,KAAM,YAAY,aAAa,4BAA6B;AAC5F,wBAAoB,QAAQ,QAAQ;AAAA,EACrC;AAEA,SAAO,QAAQ,oBAAoB,KAAK,GAAG,CAAC,IAAI,WAAW,KAAK,KAAK,IAAI,IAAI,GAAG,OAAO,IAAI,IAAI,KAAK,EAAE;AACvG;AAEe,SAAR,aAA8B,WAAW,SAAS;AACxD,YAAU;AAAA,IACT,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,uBAAuB,CAAC,WAAW;AAAA,IACnC,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,sBAAsB;AAAA,IACtB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,GAAG;AAAA,EACJ;AAGA,MAAI,OAAO,QAAQ,oBAAoB,YAAY,CAAC,QAAQ,gBAAgB,SAAS,GAAG,GAAG;AAC1F,YAAQ,kBAAkB,GAAG,QAAQ,eAAe;AAAA,EACrD;AAEA,cAAY,UAAU,KAAK;AAG3B,MAAI,UAAU,KAAK,SAAS,GAAG;AAC9B,WAAO,iBAAiB,WAAW,OAAO;AAAA,EAC3C;AAEA,MAAI,kBAAkB,SAAS,GAAG;AACjC,WAAO;AAAA,EACR;AAEA,QAAM,sBAAsB,UAAU,WAAW,IAAI;AACrD,QAAM,gBAAgB,CAAC,uBAAuB,SAAS,KAAK,SAAS;AAGrE,MAAI,CAAC,eAAe;AACnB,gBAAY,UAAU,QAAQ,4BAA4B,QAAQ,eAAe;AAAA,EAClF;AAEA,QAAM,YAAY,IAAI,IAAI,SAAS;AAEnC,MAAI,QAAQ,aAAa,QAAQ,YAAY;AAC5C,UAAM,IAAI,MAAM,kEAAkE;AAAA,EACnF;AAEA,MAAI,QAAQ,aAAa,UAAU,aAAa,UAAU;AACzD,cAAU,WAAW;AAAA,EACtB;AAEA,MAAI,QAAQ,cAAc,UAAU,aAAa,SAAS;AACzD,cAAU,WAAW;AAAA,EACtB;AAGA,MAAI,QAAQ,qBAAqB;AAChC,cAAU,WAAW;AACrB,cAAU,WAAW;AAAA,EACtB;AAGA,MAAI,QAAQ,WAAW;AACtB,cAAU,OAAO;AAAA,EAClB,WAAW,QAAQ,mBAAmB;AACrC,cAAU,OAAO,UAAU,KAAK,QAAQ,kBAAkB,EAAE;AAAA,EAC7D;AAMA,MAAI,UAAU,UAAU;AAMvB,UAAM,gBAAgB;AAEtB,QAAI,YAAY;AAChB,QAAI,SAAS;AACb,eAAS;AACR,YAAM,QAAQ,cAAc,KAAK,UAAU,QAAQ;AACnD,UAAI,CAAC,OAAO;AACX;AAAA,MACD;AAEA,YAAM,WAAW,MAAM,CAAC;AACxB,YAAM,kBAAkB,MAAM;AAC9B,YAAM,eAAe,UAAU,SAAS,MAAM,WAAW,eAAe;AAExE,gBAAU,aAAa,QAAQ,WAAW,GAAG;AAC7C,gBAAU;AACV,kBAAY,kBAAkB,SAAS;AAAA,IACxC;AAEA,UAAM,UAAU,UAAU,SAAS,MAAM,WAAW,UAAU,SAAS,MAAM;AAC7E,cAAU,QAAQ,QAAQ,WAAW,GAAG;AAExC,cAAU,WAAW;AAAA,EACtB;AAGA,MAAI,UAAU,UAAU;AACvB,QAAI;AACH,gBAAU,WAAW,UAAU,UAAU,QAAQ,EAAE,QAAQ,OAAO,KAAK;AAAA,IACxE,QAAQ;AAAA,IAAC;AAAA,EACV;AAGA,MAAI,QAAQ,yBAAyB,MAAM;AAC1C,YAAQ,uBAAuB,CAAC,iBAAiB;AAAA,EAClD;AAEA,MAAI,MAAM,QAAQ,QAAQ,oBAAoB,KAAK,QAAQ,qBAAqB,SAAS,GAAG;AAC3F,QAAI,iBAAiB,UAAU,SAAS,MAAM,GAAG;AACjD,UAAM,gBAAgB,eAAe,eAAe,SAAS,CAAC;AAE9D,QAAI,cAAc,eAAe,QAAQ,oBAAoB,GAAG;AAC/D,uBAAiB,eAAe,MAAM,GAAG,EAAE;AAC3C,gBAAU,WAAW,eAAe,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;AAAA,IAC1D;AAAA,EACD;AAEA,MAAI,UAAU,UAAU;AAEvB,cAAU,WAAW,UAAU,SAAS,QAAQ,OAAO,EAAE;AAGzD,QAAI,QAAQ,YAAY,oDAAoD,KAAK,UAAU,QAAQ,GAAG;AAKrG,gBAAU,WAAW,UAAU,SAAS,QAAQ,UAAU,EAAE;AAAA,IAC7D;AAAA,EACD;AAGA,MAAI,MAAM,QAAQ,QAAQ,qBAAqB,GAAG;AAEjD,eAAW,OAAO,CAAC,GAAG,UAAU,aAAa,KAAK,CAAC,GAAG;AACrD,UAAI,cAAc,KAAK,QAAQ,qBAAqB,GAAG;AACtD,kBAAU,aAAa,OAAO,GAAG;AAAA,MAClC;AAAA,IACD;AAAA,EACD;AAEA,MAAI,CAAC,MAAM,QAAQ,QAAQ,mBAAmB,KAAK,QAAQ,0BAA0B,MAAM;AAC1F,cAAU,SAAS;AAAA,EACpB;AAGA,MAAI,MAAM,QAAQ,QAAQ,mBAAmB,KAAK,QAAQ,oBAAoB,SAAS,GAAG;AAEzF,eAAW,OAAO,CAAC,GAAG,UAAU,aAAa,KAAK,CAAC,GAAG;AACrD,UAAI,CAAC,cAAc,KAAK,QAAQ,mBAAmB,GAAG;AACrD,kBAAU,aAAa,OAAO,GAAG;AAAA,MAClC;AAAA,IACD;AAAA,EACD;AAGA,MAAI,QAAQ,qBAAqB;AAChC,cAAU,aAAa,KAAK;AAG5B,QAAI;AACH,gBAAU,SAAS,mBAAmB,UAAU,MAAM;AAAA,IACvD,QAAQ;AAAA,IAAC;AAAA,EACV;AAEA,MAAI,QAAQ,qBAAqB;AAChC,cAAU,WAAW,UAAU,SAAS,QAAQ,OAAO,EAAE;AAAA,EAC1D;AAGA,MAAI,QAAQ,sBAAsB,UAAU,MAAM;AACjD,cAAU,OAAO;AAAA,EAClB;AAEA,QAAM,eAAe;AAGrB,cAAY,UAAU,SAAS;AAE/B,MAAI,CAAC,QAAQ,qBAAqB,UAAU,aAAa,OAAO,CAAC,aAAa,SAAS,GAAG,KAAK,UAAU,SAAS,IAAI;AACrH,gBAAY,UAAU,QAAQ,OAAO,EAAE;AAAA,EACxC;AAGA,OAAK,QAAQ,uBAAuB,UAAU,aAAa,QAAQ,UAAU,SAAS,MAAM,QAAQ,mBAAmB;AACtH,gBAAY,UAAU,QAAQ,OAAO,EAAE;AAAA,EACxC;AAGA,MAAI,uBAAuB,CAAC,QAAQ,mBAAmB;AACtD,gBAAY,UAAU,QAAQ,cAAc,IAAI;AAAA,EACjD;AAGA,MAAI,QAAQ,eAAe;AAC1B,gBAAY,UAAU,QAAQ,qBAAqB,EAAE;AAAA,EACtD;AAEA,SAAO;AACR;", "names": []}