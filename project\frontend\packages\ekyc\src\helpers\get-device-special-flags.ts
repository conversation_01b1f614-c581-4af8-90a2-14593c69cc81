import type { UserAgentResult } from '@helpers/helpers/user-agent';
import { validateConditionObject } from '@helpers/helpers/validation-condition-utils';

export function getDeviceSpecialFlags(
  conditions: Types.DeviceSpecialFlagCondition[],
  ua: UserAgentResult,
) {
  const deviceFlags: Types.DeviceSpecialFlags = {};
  conditions.forEach(flagConfig => {
    // Check if disabled
    if (flagConfig.enabled === false) {
      return;
    }

    // Check if all conditions pass for this flag configuration
    const conditionsPassed = flagConfig.conditions.every(condition =>
      validateConditionObject(condition, ua),
    );

    if (conditionsPassed) {
      Object.assign(deviceFlags, flagConfig.flags);
    }
  });
  return deviceFlags;
}

export function getDeviceSpecialFlagsForLivenessModel(ua: UserAgentResult) {
  return getDeviceSpecialFlags(DEVICE_SPECIAL_LIVENESS_MODEL_FLAGS_CONDITIONS, ua);
}

export function getDeviceSpecialFlagsForCamera(ua: UserAgentResult) {
  return getDeviceSpecialFlags(DEVICE_SPECIAL_FLAGS_CAMERA_CONDITIONS, ua);
}

export const DEVICE_SPECIAL_LIVENESS_MODEL_FLAGS_CONDITIONS: Types.DeviceSpecialFlagCondition[] = [
  {
    name: 'ios_16_skip_mediapipe',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
        },
        op: '==',
        expected_data: {
          value: 'iOS',
          category: 'string',
        },
      },
      {
        initial_data: {
          value: 'os.version',
          category: 'answer',
        },
        op: 'starts_with',
        expected_data: {
          value: '16',
          category: 'string',
        },
      },
    ],
    flags: {
      skipGpu: true,
      skipCpu: true,
    },
  },
  {
    name: 'android_10_skip_gpu',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
        },
        op: '<=',
        expected_data: {
          value: 'Android',
          category: 'string',
        },
      },
      {
        initial_data: {
          value: 'os.version',
          category: 'answer',
        },
        op: '<=',
        expected_data: {
          value: '10',
          category: 'string',
        },
      },
    ],
    flags: {
      skipGpu: true,
    },
  },
  {
    name: 'samsung_s25_skip_gpu',
    conditions: [
      {
        initial_data: {
          value: 'device.model',
          category: 'answer',
        },
        op: 'starts_with',
        expected_data: {
          value: 'SM-S93',
          category: 'string',
        },
      },
    ],
    flags: {
      skipGpu: true,
    },
  },
  {
    name: 'ios_skip_test_check',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
        },
        op: '==',
        expected_data: {
          value: 'iOS',
          category: 'string',
        },
      },
      {
        initial_data: {
          value: 'os.version',
          category: 'answer',
        },
        not: true,
        op: 'starts_with',
        expected_data: {
          value: '16',
          category: 'string',
        },
      },
    ],
    flags: {
      skipTestCheck: true,
    },
  },
  {
    name: 'mac_skip_test_check',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
        },
        op: '==',
        expected_data: {
          value: 'macOS',
          category: 'string',
        },
      },
    ],
    flags: {
      skipTestCheck: true,
    },
  },
];

export const DEVICE_SPECIAL_FLAGS_CAMERA_CONDITIONS: Types.DeviceSpecialFlagCondition[] = [];
