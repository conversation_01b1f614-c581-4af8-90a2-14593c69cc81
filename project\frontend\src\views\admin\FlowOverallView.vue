<template>
  <div class="flow-builder__config-wrapper flow-detail-wrapper">
    <div class="flow-builder__dashboard-content">
      <div class="application-dashboard-view">
        <div>
          <ApplicationFilters :applications="applications" />
          <ApplicationList :applications="applications" />
        </div>
        <!-- Application Detail Modal -->
        <b-modal
          :active="!!viewingAppliedFormSlug"
          :destroy-on-hide="false"
          :can-cancel="false"
          has-modal-card
          trap-focus
          :full-screen="true"
          class="application-report-modal"
          :class="`viewing-tab-${viewingTabName}`"
          style="position: fixed; z-index: 10000"
        >
          <template #default>
            <div class="modal-card">
              <header class="modal-card-head has-border">
                <p class="modal-card-title">Form Detail</p>
                <button type="button" class="delete" @click="viewingAppliedFormSlug = ''" />
              </header>
              <section class="modal-card-body" style="height: 80vh">
                <ApplicationDetail @change-tab="onChangeTab" />
              </section>
            </div>
          </template>
        </b-modal>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { debouncedWatch } from '@vueuse/core';
import format from 'date-fns/format';
import parse from 'date-fns/parse';
import pickBy from 'lodash/pickBy';
import { storeToRefs } from 'pinia';

import ApplicationDetail from '@/components/admin/dashboard/ApplicationDetail.vue';
import ApplicationFilters from '@/components/admin/dashboard/ApplicationFilters/ApplicationFilters.vue';
import ApplicationList from '@/components/admin/dashboard/ApplicationList/ApplicationList.vue';
import { useDecisionFlowManager } from '@/composables/decision-flow/use-decision-flow-manager';
import { useDashboardFormSettings } from '@/composables/dynamic-form-settings/use-dashboard-form-settings';
import { useAdminFormManager } from '@/composables/use-admin-form-manager';
import { useAdminReportManager } from '@/composables/use-admin-report-manager';
import { useFlowPublisher } from '@/composables/use-flow-publisher';
import { useRouter } from '@/composables/use-router';
import { useApplicationStore } from '@/store/application';

const props = defineProps({
  formSlug: {
    type: String,
    required: true,
  },
});

const { router, replaceQuery } = useRouter();
const {
  filteredStatus: rawFilteredStatus,
  timeSpan,
  customTimeStart,
  customTimeEnd,
  filteredDateField,
} = useAdminFormManager();

const applicationStore = useApplicationStore();
const {
  applications,
  currentPage,
  searchText,
  searchAnswers,
  queryAnswers,
  perPage,
  sorting,
  customStatus,
  isLoadingApplicationList,
} = storeToRefs(applicationStore);

const { setCustomStatus, fetchApplications, setSorting, resetSorting, resetPage } =
  applicationStore;

const { viewingAppliedFormSlug, viewingFormSlug } = useAdminReportManager();
const { customStatusList, fetchCustomStatusList } = useDecisionFlowManager();
const { selectedFormSlug, getDashboard } = useFlowPublisher();

const { dashboardSetting } = useDashboardFormSettings(selectedFormSlug);

const viewingTabName = ref('');

const filteredStatus = computed({
  get() {
    return rawFilteredStatus.value;
  },
  set(val) {
    rawFilteredStatus.value = val;
    if (['incomplete'].includes(val)) {
      filteredDateField.value = 'created_at';
    }
  },
});

async function fetchDashboardSettings() {
  if (selectedFormSlug.value) {
    await getDashboard();
    if (dashboardSetting.value?.query_answers) {
      queryAnswers.value = dashboardSetting.value.query_answers;
      // const defaultQueryAnswer = dashboardSetting.value?.query_answers?.find(
      //   ans =>
      //     ans.includes('first_name') &&
      //     dashboardSetting.value?.query_choices?.find(choice =>
      //       choice?.value?.includes('first_name'),
      //     ),
      // );
      // searchAnswers.value = [defaultQueryAnswer || 'slug'];
    }
  }
}

function getQuery() {
  const query = {
    ...(router.currentRoute.query as Record<string, string>),
  };
  if (query.page) {
    currentPage.value = +query.page;
  }
  if (query.search) {
    searchText.value = query.search;
  }
  if (query.answer) {
    searchAnswers.value = [query.answer];
  }
  if (query.sort) {
    const queryParts = query.sort.split(',');
    const pairs = queryParts.map(s => s.split('.'));
    setSorting(pairs.reduce((prev, [key, val]) => ({ ...prev, [key]: val }), {}));
  }
  if (query.status) {
    filteredStatus.value = query.status as Types.ApplicationStatusType;
  }
  if (query.custom) {
    try {
      const queryParts = JSON.parse(query.custom);
      setCustomStatus(queryParts);
    } catch {
      console.warn(`Invalid 'custom' query param`);
    }
  }
  if (query.dateField) {
    filteredDateField.value = query.dateField as Types.FilteredDateFieldType;
  }
  if (query.timeSpan) {
    timeSpan.value = query.timeSpan as Types.TimeSpanType;
  }
  if (query.timeRangeStart) {
    customTimeStart.value = parse(query.timeRangeStart, 'yyyy-MM-dd', new Date());
  }
  if (query.timeRangeEnd) {
    customTimeEnd.value = parse(query.timeRangeEnd, 'yyyy-MM-dd', new Date());
  }
}

function onChangeTab(tabName) {
  viewingTabName.value = tabName;
}

const delay = (ms: number) =>
  new Promise(resolve => {
    setTimeout(resolve, ms);
  });

watch([() => props.formSlug], (val, oldVal) => {
  if (oldVal !== val) {
    selectedFormSlug.value = props.formSlug;
  }
});

const isLoadedApplicationOnce = ref(false);
debouncedWatch(
  [
    currentPage,
    searchText,
    searchAnswers,
    sorting,
    filteredStatus,
    customStatus,
    filteredDateField,
    timeSpan,
    customTimeStart,
    customTimeEnd,
    // vvv Wont change query
    perPage,
  ],
  async (filter, oldFilter) => {
    const [
      newPage,
      newSearchText,
      newSearchAnswer,
      newSorting,
      newStatus,
      newCustom,
      newDateField,
      newTimeSpan,
      newCustomTimeStart,
      newCustomTimeEnd,
    ] = filter;

    const getValueOrUndefined = (val: any) => val || undefined;

    const [oldPage, , oldSearchAnswer] = oldFilter;
    const qPage = getValueOrUndefined(newPage);
    const qSearchText = getValueOrUndefined(newSearchText);
    const qSearchAnswer = getValueOrUndefined(newSearchAnswer);
    const qSorting = newSorting;
    const qStatus = getValueOrUndefined(newStatus);
    const qCustom = pickBy(newCustom || {}, q => !!q);
    const qDateField = getValueOrUndefined(newDateField);
    const qTimeSpan = getValueOrUndefined(newTimeSpan);
    const qCustomTimeStart = getValueOrUndefined(newCustomTimeStart);
    const qCustomTimeEnd = getValueOrUndefined(newCustomTimeEnd);

    // If searchAnswer changed but no searchText, just skip
    if (isLoadedApplicationOnce.value && oldSearchAnswer !== newSearchAnswer && !qSearchText) {
      return;
    }

    const query: Record<string, string> = {
      page: `${qPage}`,
      search: qSearchText,
      answer: qSearchAnswer?.length === 1 ? qSearchAnswer[0] : undefined,
      sort: Object.entries(qSorting)
        .map(([k, v]) => `${k}.${v}`)
        .join(','),
      status: qStatus,
      custom: Object.keys(qCustom).length ? JSON.stringify(qCustom) : undefined,
      dateField: qDateField,
      timeSpan: qTimeSpan,
      timeRangeStart: undefined,
      timeRangeEnd: undefined,
    };
    if (newTimeSpan === 'custom') {
      query.timeRangeStart = getValueOrUndefined(format(qCustomTimeStart, 'yyyy-MM-dd'));
      query.timeRangeEnd = getValueOrUndefined(format(qCustomTimeEnd, 'yyyy-MM-dd'));
    }

    if (newPage !== oldPage || newPage === 1) {
      isLoadingApplicationList.value = true;

      const promises = [delay(500), replaceQuery(query), fetchApplications()];
      await Promise.all(promises);

      isLoadingApplicationList.value = false;
      isLoadedApplicationOnce.value = true;
    } else {
      currentPage.value = 1;
    }
  },
  { debounce: 400 },
);

watch(selectedFormSlug, async () => {
  isLoadingApplicationList.value = true;
  await fetchDashboardSettings();
  resetSorting();
  resetPage();
});

onMounted(async () => {
  isLoadingApplicationList.value = true;
  selectedFormSlug.value = props.formSlug;
  await fetchCustomStatusList();
  await fetchDashboardSettings();
  searchAnswers.value = ['slug'];
  getQuery();
});

onBeforeUnmount(() => {
  isLoadingApplicationList.value = false;
});
</script>

<style lang="scss" scoped>
.flow-builder__config-wrapper.flow-detail-wrapper .application-dashboard-view {
  padding-top: 1.5rem !important;
}
.application-dashboard-view ::v-deep .pagination .pagination-list .pagination-link {
  margin-left: 0.1rem;
  margin-right: 0.1rem;
}
</style>
