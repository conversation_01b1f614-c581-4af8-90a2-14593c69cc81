{"version": 3, "sources": ["../../lodash/_isPrototype.js", "../../lodash/_overArg.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\nmodule.exports = isPrototype;\n", "/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\nmodule.exports = overArg;\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,cAAc,OAAO;AASzB,aAAS,YAAY,OAAO;AAC1B,UAAI,OAAO,SAAS,MAAM,aACtB,QAAS,OAAO,QAAQ,cAAc,KAAK,aAAc;AAE7D,aAAO,UAAU;AAAA,IACnB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAQA,aAAS,QAAQ,MAAM,WAAW;AAChC,aAAO,SAAS,KAAK;AACnB,eAAO,KAAK,UAAU,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}