{"version": 3, "sources": ["../../lodash/toLower.js"], "sourcesContent": ["var toString = require('./toString');\n\n/**\n * Converts `string`, as a whole, to lower case just like\n * [String#toLowerCase](https://mdn.io/toLowerCase).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the lower cased string.\n * @example\n *\n * _.toLower('--Foo-Bar--');\n * // => '--foo-bar--'\n *\n * _.toLower('fooBar');\n * // => 'foobar'\n *\n * _.toLower('__FOO_BAR__');\n * // => '__foo_bar__'\n */\nfunction toLower(value) {\n  return toString(value).toLowerCase();\n}\n\nmodule.exports = toLower;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAuBf,aAAS,QAAQ,OAAO;AACtB,aAAO,SAAS,KAAK,EAAE,YAAY;AAAA,IACrC;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}