{"version": 3, "sources": ["../../lodash/intersectionBy.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap'),\n    baseIntersection = require('./_baseIntersection'),\n    baseIteratee = require('./_baseIteratee'),\n    baseRest = require('./_baseRest'),\n    castArrayLikeObject = require('./_castArrayLikeObject'),\n    last = require('./last');\n\n/**\n * This method is like `_.intersection` except that it accepts `iteratee`\n * which is invoked for each element of each `arrays` to generate the criterion\n * by which they're compared. The order and references of result values are\n * determined by the first array. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * _.intersectionBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [2.1]\n *\n * // The `_.property` iteratee shorthand.\n * _.intersectionBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }]\n */\nvar intersectionBy = baseRest(function(arrays) {\n  var iteratee = last(arrays),\n      mapped = arrayMap(arrays, castArrayLikeObject);\n\n  if (iteratee === last(mapped)) {\n    iteratee = undefined;\n  } else {\n    mapped.pop();\n  }\n  return (mapped.length && mapped[0] === arrays[0])\n    ? baseIntersection(mapped, baseIteratee(iteratee, 2))\n    : [];\n});\n\nmodule.exports = intersectionBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,mBAAmB;AADvB,QAEI,eAAe;AAFnB,QAGI,WAAW;AAHf,QAII,sBAAsB;AAJ1B,QAKI,OAAO;AAyBX,QAAI,iBAAiB,SAAS,SAAS,QAAQ;AAC7C,UAAI,WAAW,KAAK,MAAM,GACtB,SAAS,SAAS,QAAQ,mBAAmB;AAEjD,UAAI,aAAa,KAAK,MAAM,GAAG;AAC7B,mBAAW;AAAA,MACb,OAAO;AACL,eAAO,IAAI;AAAA,MACb;AACA,aAAQ,OAAO,UAAU,OAAO,CAAC,MAAM,OAAO,CAAC,IAC3C,iBAAiB,QAAQ,aAAa,UAAU,CAAC,CAAC,IAClD,CAAC;AAAA,IACP,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}