{"version": 3, "sources": ["../../@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "../../state-local/lib/es/state-local.js", "../../@monaco-editor/loader/lib/es/config/index.js", "../../@monaco-editor/loader/lib/es/utils/curry.js", "../../@monaco-editor/loader/lib/es/utils/isObject.js", "../../@monaco-editor/loader/lib/es/validators/index.js", "../../@monaco-editor/loader/lib/es/utils/compose.js", "../../@monaco-editor/loader/lib/es/utils/deepMerge.js", "../../@monaco-editor/loader/lib/es/utils/makeCancelable.js", "../../@monaco-editor/loader/lib/es/loader/index.js", "../../@guolao/vue-monaco-editor/lib/es/index.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n", "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n", "function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n", "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n", "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n", "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n", "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n", "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n", "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n", "import loader from '@monaco-editor/loader';\nexport { default as loader } from '@monaco-editor/loader';\nimport { computed, shallowRef, ref, onMounted, defineComponent, onUnmounted, watch, h, nextTick } from 'vue-demi';\n\nvar __defProp$2 = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols$2 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$2 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$2 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$2 = (obj, key, value) => key in obj ? __defProp$2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$2 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$2.call(b, prop))\n      __defNormalProp$2(a, prop, b[prop]);\n  if (__getOwnPropSymbols$2)\n    for (var prop of __getOwnPropSymbols$2(b)) {\n      if (__propIsEnum$2.call(b, prop))\n        __defNormalProp$2(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nconst styles = {\n  wrapper: {\n    display: \"flex\",\n    position: \"relative\",\n    textAlign: \"initial\"\n  },\n  fullWidth: {\n    width: \"100%\"\n  },\n  hide: {\n    display: \"none\"\n  }\n};\nfunction useContainer(props, isEditorReady) {\n  const wrapperStyle = computed(() => {\n    const { width, height } = props;\n    return __spreadProps(__spreadValues$2({}, styles.wrapper), {\n      width,\n      height\n    });\n  });\n  const containerStyle = computed(() => {\n    return __spreadValues$2(__spreadValues$2({}, styles.fullWidth), !isEditorReady.value && styles.hide);\n  });\n  return { wrapperStyle, containerStyle };\n}\n\nfunction useMonaco() {\n  const monacoRef = shallowRef(loader.__getMonacoInstance());\n  const isLoadFailed = ref(false);\n  let promise;\n  onMounted(() => {\n    if (monacoRef.value)\n      return;\n    promise = loader.init();\n    promise.then((monacoInstance) => monacoRef.value = monacoInstance).catch((error) => {\n      if ((error == null ? void 0 : error.type) !== \"cancelation\") {\n        isLoadFailed.value = true;\n        console.error(\"Monaco initialization error:\", error);\n      }\n    });\n  });\n  const unload = () => promise == null ? void 0 : promise.cancel();\n  return {\n    monacoRef,\n    unload,\n    isLoadFailed\n  };\n}\n\nfunction slotHelper(slot) {\n  return typeof slot == \"function\" ? slot() : slot;\n}\nfunction isUndefined(v) {\n  return v === void 0;\n}\nfunction getOrCreateModel(monaco, value, language, path) {\n  return getModel(monaco, path) || createModel(monaco, value, language, path);\n}\nfunction getModel(monaco, path) {\n  return monaco.editor.getModel(createModelUri(monaco, path));\n}\nfunction createModel(monaco, value, language, path) {\n  return monaco.editor.createModel(value, language, path ? createModelUri(monaco, path) : void 0);\n}\nfunction createModelUri(monaco, path) {\n  return monaco.Uri.parse(path);\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;\nvar __hasOwnProp$1 = Object.prototype.hasOwnProperty;\nvar __propIsEnum$1 = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues$1 = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp$1.call(b, prop))\n      __defNormalProp$1(a, prop, b[prop]);\n  if (__getOwnPropSymbols$1)\n    for (var prop of __getOwnPropSymbols$1(b)) {\n      if (__propIsEnum$1.call(b, prop))\n        __defNormalProp$1(a, prop, b[prop]);\n    }\n  return a;\n};\nconst loadingStyle$1 = {\n  display: \"flex\",\n  height: \"100%\",\n  width: \"100%\",\n  justifyContent: \"center\",\n  alignItems: \"center\"\n};\nvar VueMonacoEditor = defineComponent({\n  name: \"VueMonacoEditor\",\n  // TODO: vue3 use modelValue, vue2 use value\n  model: {\n    prop: \"value\",\n    event: \"update:value\"\n  },\n  props: {\n    defaultValue: String,\n    defaultPath: String,\n    defaultLanguage: String,\n    value: String,\n    language: String,\n    path: String,\n    /* === */\n    theme: {\n      type: String,\n      default: \"vs\"\n    },\n    line: Number,\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    overrideServices: {\n      type: Object,\n      default: () => ({})\n    },\n    saveViewState: {\n      type: Boolean,\n      default: true\n    },\n    /* === */\n    width: {\n      type: [Number, String],\n      default: \"100%\"\n    },\n    height: {\n      type: [Number, String],\n      default: \"100%\"\n    },\n    className: String\n  },\n  emits: [\"update:value\", \"beforeMount\", \"mount\", \"change\", \"validate\"],\n  setup(props, ctx) {\n    const viewStates = /* @__PURE__ */ new Map();\n    const containerRef = shallowRef(null);\n    const { monacoRef, unload, isLoadFailed } = useMonaco();\n    const { editorRef } = useEditor(ctx, props, monacoRef, containerRef);\n    const { disposeValidator } = useValidator(ctx, props, monacoRef, editorRef);\n    const isEditorReady = computed(() => !!monacoRef.value && !!editorRef.value);\n    const { wrapperStyle, containerStyle } = useContainer(props, isEditorReady);\n    onUnmounted(() => {\n      var _a, _b;\n      (_a = disposeValidator.value) == null ? void 0 : _a.call(disposeValidator);\n      if (editorRef.value) {\n        (_b = editorRef.value.getModel()) == null ? void 0 : _b.dispose();\n        editorRef.value.dispose();\n      } else {\n        unload();\n      }\n    });\n    watch(\n      [() => props.path, () => props.value, () => props.language, () => props.line],\n      ([newPath, newValue, newLanguage, newLine], [oldPath, oldValue, oldLanguage, oldLine]) => {\n        if (!isEditorReady.value) {\n          return;\n        }\n        if (newPath !== oldPath) {\n          const newModel = getOrCreateModel(\n            monacoRef.value,\n            newValue || props.defaultValue || \"\",\n            newLanguage || props.defaultLanguage || \"\",\n            newPath || props.defaultPath || \"\"\n          );\n          props.saveViewState && viewStates.set(oldPath, editorRef.value.saveViewState());\n          editorRef.value.setModel(newModel);\n          props.saveViewState && editorRef.value.restoreViewState(viewStates.get(newPath));\n          if (!isUndefined(newLine)) {\n            editorRef.value.revealLine(newLine);\n          }\n          return;\n        }\n        if (editorRef.value.getValue() !== newValue) {\n          editorRef.value.setValue(newValue);\n        }\n        if (newLanguage !== oldLanguage) {\n          monacoRef.value.editor.setModelLanguage(editorRef.value.getModel(), newLanguage);\n        }\n        if (!isUndefined(newLine) && newLine !== oldLine) {\n          editorRef.value.revealLine(newLine);\n        }\n      }\n    );\n    watch(\n      () => props.options,\n      (options) => editorRef.value && editorRef.value.updateOptions(options),\n      { deep: true }\n    );\n    watch(\n      () => props.theme,\n      (theme) => monacoRef.value && monacoRef.value.editor.setTheme(theme)\n    );\n    return {\n      containerRef,\n      isEditorReady,\n      isLoadFailed,\n      wrapperStyle,\n      containerStyle\n    };\n  },\n  render() {\n    const {\n      $slots,\n      isEditorReady,\n      isLoadFailed,\n      wrapperStyle,\n      containerStyle,\n      // TODO: need remove, add `@deprecated` flag\n      className\n    } = this;\n    return h(\n      \"div\",\n      {\n        style: wrapperStyle\n      },\n      [\n        !isEditorReady && h(\n          \"div\",\n          {\n            style: loadingStyle$1\n          },\n          isLoadFailed ? $slots.failure ? slotHelper($slots.failure) : \"load failed\" : $slots.default ? slotHelper($slots.default) : \"loading...\"\n        ),\n        h(\"div\", {\n          ref: \"containerRef\",\n          key: \"monaco_editor_container\",\n          style: containerStyle,\n          class: className\n        })\n      ]\n    );\n  }\n});\nfunction useEditor({ emit }, props, monacoRef, containerRef) {\n  const editorRef = shallowRef(null);\n  onMounted(() => {\n    const stop = watch(\n      monacoRef,\n      () => {\n        if (containerRef.value && monacoRef.value) {\n          nextTick(() => stop());\n          createEditor();\n        }\n      },\n      { immediate: true }\n    );\n  });\n  function createEditor() {\n    var _a;\n    if (!containerRef.value || !monacoRef.value || editorRef.value) {\n      return;\n    }\n    emit(\"beforeMount\", monacoRef.value);\n    const autoCreatedModelPath = props.path || props.defaultPath;\n    const defaultModel = getOrCreateModel(\n      monacoRef.value,\n      props.value || props.defaultValue || \"\",\n      props.language || props.defaultLanguage || \"\",\n      autoCreatedModelPath || \"\"\n    );\n    editorRef.value = monacoRef.value.editor.create(\n      containerRef.value,\n      __spreadValues$1({\n        model: defaultModel,\n        theme: props.theme,\n        automaticLayout: true,\n        autoIndent: \"brackets\",\n        formatOnPaste: true,\n        formatOnType: true\n      }, props.options),\n      props.overrideServices\n    );\n    (_a = editorRef.value) == null ? void 0 : _a.onDidChangeModelContent((event) => {\n      const value = editorRef.value.getValue();\n      if (value !== props.value) {\n        emit(\"update:value\", value);\n        emit(\"change\", value, event);\n      }\n    });\n    if (editorRef.value && !isUndefined(props.line)) {\n      editorRef.value.revealLine(props.line);\n    }\n    emit(\"mount\", editorRef.value, monacoRef.value);\n  }\n  return { editorRef };\n}\nfunction useValidator({ emit }, props, monacoRef, editorRef) {\n  const disposeValidator = ref(null);\n  const stop = watch([monacoRef, editorRef], () => {\n    if (monacoRef.value && editorRef.value) {\n      nextTick(() => stop());\n      const changeMarkersListener = monacoRef.value.editor.onDidChangeMarkers((uris) => {\n        var _a, _b;\n        const editorUri = (_b = (_a = editorRef.value) == null ? void 0 : _a.getModel()) == null ? void 0 : _b.uri;\n        if (editorUri) {\n          const currentEditorHasMarkerChanges = uris.find((uri) => uri.path === editorUri.path);\n          if (currentEditorHasMarkerChanges) {\n            const markers = monacoRef.value.editor.getModelMarkers({\n              resource: editorUri\n            });\n            emit(\"validate\", markers);\n          }\n        }\n      });\n      disposeValidator.value = () => changeMarkersListener == null ? void 0 : changeMarkersListener.dispose();\n    }\n  });\n  return { disposeValidator };\n}\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nconst loadingStyle = {\n  display: \"flex\",\n  height: \"100%\",\n  width: \"100%\",\n  justifyContent: \"center\",\n  alignItems: \"center\"\n};\nvar VueMonacoDiffEditor = defineComponent({\n  name: \"VueMonacoDiffEditor\",\n  props: {\n    original: String,\n    modified: String,\n    language: String,\n    originalLanguage: String,\n    modifiedLanguage: String,\n    originalModelPath: String,\n    modifiedModelPath: String,\n    /* == */\n    theme: {\n      type: String,\n      default: \"vs\"\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    /* == */\n    width: {\n      type: [Number, String],\n      default: \"100%\"\n    },\n    height: {\n      type: [Number, String],\n      default: \"100%\"\n    },\n    className: String\n  },\n  setup(props, ctx) {\n    const containerRef = shallowRef(null);\n    const { monacoRef, unload, isLoadFailed } = useMonaco();\n    const { diffEditorRef } = useDiffEditor(ctx, props, monacoRef, containerRef);\n    const isDiffEditorReady = computed(() => !!monacoRef.value && !!diffEditorRef.value);\n    const { wrapperStyle, containerStyle } = useContainer(props, isDiffEditorReady);\n    onUnmounted(() => {\n      var _a, _b, _c, _d, _e, _f, _g, _h;\n      !monacoRef.value && unload();\n      const models = (_b = (_a = diffEditorRef.value) == null ? void 0 : _a.getModel) == null ? void 0 : _b.call(_a);\n      (_d = (_c = models == null ? void 0 : models.original) == null ? void 0 : _c.dispose) == null ? void 0 : _d.call(_c);\n      (_f = (_e = models == null ? void 0 : models.modified) == null ? void 0 : _e.dispose) == null ? void 0 : _f.call(_e);\n      (_h = (_g = diffEditorRef.value) == null ? void 0 : _g.dispose) == null ? void 0 : _h.call(_g);\n    });\n    watch(\n      [() => props.originalModelPath, () => props.original, () => props.originalLanguage, () => props.language],\n      ([newOriginalModelPath, newOriginal, newOriginalLanguage, newLanguage], [oldOriginalModelPath, oldOriginal, oldOriginalLanguage, oldLanguage]) => {\n        if (!isDiffEditorReady.value) {\n          return;\n        }\n        const originalEditor = diffEditorRef.value.getOriginalEditor();\n        if (newOriginalModelPath !== oldOriginalModelPath) {\n          const newOriginModel = getOrCreateModel(\n            monacoRef.value,\n            newOriginal || \"\",\n            newOriginalLanguage || newLanguage || \"text\",\n            newOriginalModelPath || \"\"\n          );\n          originalEditor.setModel(newOriginModel);\n          return;\n        }\n        if (newOriginal !== originalEditor.getValue()) {\n          originalEditor.setValue(newOriginal || \"\");\n        }\n        if (newOriginalLanguage !== oldOriginalLanguage || newLanguage !== oldLanguage) {\n          monacoRef.value.editor.setModelLanguage(\n            diffEditorRef.value.getModel().original,\n            newOriginalLanguage || newLanguage || \"text\"\n          );\n        }\n      }\n    );\n    watch(\n      [() => props.modifiedModelPath, () => props.modified, () => props.modifiedLanguage, () => props.language],\n      ([newModifiedModelPath, newModified, newModifiedLanguage, newLanguage], [oldModifiedModelPath, oldModified, oldModifiedLanguage, oldLanguage]) => {\n        if (!isDiffEditorReady.value) {\n          return;\n        }\n        const modifiedEditor = diffEditorRef.value.getModifiedEditor();\n        if (oldModifiedModelPath !== newModifiedModelPath) {\n          const newModifiedModel = getOrCreateModel(\n            monacoRef.value,\n            newModified || \"\",\n            newModifiedLanguage || newLanguage || \"text\",\n            newModifiedModelPath || \"\"\n          );\n          modifiedEditor.setModel(newModifiedModel);\n          return;\n        }\n        if (newModified !== modifiedEditor.getValue()) {\n          const readOnlyCode = monacoRef.value.editor.EditorOption.readOnly;\n          if (modifiedEditor.getOption(readOnlyCode)) {\n            modifiedEditor.setValue(newModified || \"\");\n          } else {\n            modifiedEditor.executeEdits(\"\", [\n              {\n                range: modifiedEditor.getModel().getFullModelRange(),\n                text: newModified || \"\",\n                forceMoveMarkers: true\n              }\n            ]);\n            modifiedEditor.pushUndoStop();\n          }\n        }\n        if (newModifiedLanguage !== oldModifiedLanguage || newLanguage !== oldLanguage) {\n          monacoRef.value.editor.setModelLanguage(\n            diffEditorRef.value.getModel().modified,\n            newModifiedLanguage || newLanguage || \"text\"\n          );\n        }\n      }\n    );\n    watch(\n      () => props.theme,\n      () => {\n        var _a;\n        return (_a = monacoRef.value) == null ? void 0 : _a.editor.setTheme(props.theme);\n      }\n    );\n    watch(\n      () => props.options,\n      () => {\n        var _a;\n        return (_a = diffEditorRef.value) == null ? void 0 : _a.updateOptions(props.options);\n      },\n      { deep: true }\n    );\n    return {\n      containerRef,\n      isDiffEditorReady,\n      isLoadFailed,\n      wrapperStyle,\n      containerStyle\n    };\n  },\n  render() {\n    const { $slots, isDiffEditorReady, isLoadFailed, wrapperStyle, containerStyle, className } = this;\n    return h(\n      \"div\",\n      {\n        style: wrapperStyle\n      },\n      [\n        !isDiffEditorReady && h(\n          \"div\",\n          {\n            style: loadingStyle\n          },\n          isLoadFailed ? $slots.failure ? slotHelper($slots.failure) : \"load failed\" : $slots.default ? slotHelper($slots.default) : \"loading...\"\n        ),\n        h(\"div\", {\n          ref: \"containerRef\",\n          key: \"monaco_diff_editor_container\",\n          style: containerStyle,\n          class: className\n        })\n      ]\n    );\n  }\n});\nfunction useDiffEditor({ emit }, props, monacoRef, containerRef) {\n  const diffEditorRef = shallowRef(null);\n  onMounted(() => {\n    const stop = watch(\n      monacoRef,\n      () => {\n        if (containerRef.value && monacoRef.value) {\n          nextTick(() => stop());\n          createDiffEditor();\n        }\n      },\n      { immediate: true }\n    );\n  });\n  function createDiffEditor() {\n    var _a;\n    if (!containerRef.value || !monacoRef.value || diffEditorRef.value) {\n      return;\n    }\n    emit(\"beforeMount\", monacoRef.value);\n    diffEditorRef.value = monacoRef.value.editor.createDiffEditor(containerRef.value, __spreadValues({\n      automaticLayout: true,\n      autoIndent: \"brackets\",\n      theme: props.theme,\n      formatOnPaste: true,\n      formatOnType: true\n    }, props.options));\n    const originalModel = getOrCreateModel(\n      monacoRef.value,\n      props.original || \"\",\n      props.originalLanguage || props.language || \"text\",\n      props.originalModelPath || \"\"\n    );\n    const modifiedModel = getOrCreateModel(\n      monacoRef.value,\n      props.modified || \"\",\n      props.modifiedLanguage || props.language || \"text\",\n      props.modifiedModelPath || \"\"\n    );\n    (_a = diffEditorRef.value) == null ? void 0 : _a.setModel({\n      original: originalModel,\n      modified: modifiedModel\n    });\n    emit(\"mount\", diffEditorRef.value, monacoRef.value);\n  }\n  return { diffEditorRef };\n}\n\nfunction install(app, options) {\n  if (options) {\n    loader.config(options);\n  }\n  app.component(VueMonacoEditor.name, VueMonacoEditor);\n  app.component(VueMonacoDiffEditor.name, VueMonacoDiffEditor);\n}\n\nexport { VueMonacoDiffEditor as DiffEditor, VueMonacoEditor as Editor, VueMonacoDiffEditor, VueMonacoEditor, VueMonacoEditor as default, install, useMonaco };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI,eAAgB,WAAU,QAAQ,OAAO,SAAU,KAAK;AAC1D,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC;AACD,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,OAAO,WAAW,eAAe,EAAE,OAAO,YAAY,OAAO,GAAG,GAAI;AACxE,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI;AACF,aAAS,KAAK,IAAI,OAAO,QAAQ,EAAE,GAAG,IAAI,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAClF,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;;;AC3IA,SAASA,iBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAASC,SAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI,eAAgB,WAAU,QAAQ,OAAO,SAAU,KAAK;AAC1D,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC;AACD,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAASC,gBAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,MAAAD,SAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,QAAAD,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,MAAAC,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU;AACjB,WAAS,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACtF,QAAI,IAAI,IAAI,UAAU,IAAI;AAAA,EAC5B;AAEA,SAAO,SAAU,GAAG;AAClB,WAAO,IAAI,YAAY,SAAU,GAAG,GAAG;AACrC,aAAO,EAAE,CAAC;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AACF;AAEA,SAAS,MAAM,IAAI;AACjB,SAAO,SAAS,UAAU;AACxB,QAAI,QAAQ;AAEZ,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AAEA,WAAO,KAAK,UAAU,GAAG,SAAS,GAAG,MAAM,MAAM,IAAI,IAAI,WAAY;AACnE,eAAS,QAAQ,UAAU,QAAQ,WAAW,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjG,iBAAS,KAAK,IAAI,UAAU,KAAK;AAAA,MACnC;AAEA,aAAO,QAAQ,MAAM,OAAO,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,CAAC,EAAE,SAAS,KAAK,KAAK,EAAE,SAAS,QAAQ;AAClD;AAEA,SAAS,QAAQ,KAAK;AACpB,SAAO,CAAC,OAAO,KAAK,GAAG,EAAE;AAC3B;AAEA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,eAAe,QAAQ,UAAU;AACxC,SAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ;AAC9D;AAEA,SAAS,gBAAgB,SAAS,SAAS;AACzC,MAAI,CAAC,SAAS,OAAO,EAAG,cAAa,YAAY;AACjD,MAAI,OAAO,KAAK,OAAO,EAAE,KAAK,SAAU,OAAO;AAC7C,WAAO,CAAC,eAAe,SAAS,KAAK;AAAA,EACvC,CAAC,EAAG,cAAa,aAAa;AAC9B,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU;AAClC,MAAI,CAAC,WAAW,QAAQ,EAAG,cAAa,cAAc;AACxD;AAEA,SAAS,gBAAgB,SAAS;AAChC,MAAI,EAAE,WAAW,OAAO,KAAK,SAAS,OAAO,GAAI,cAAa,aAAa;AAC3E,MAAI,SAAS,OAAO,KAAK,OAAO,OAAO,OAAO,EAAE,KAAK,SAAU,UAAU;AACvE,WAAO,CAAC,WAAW,QAAQ;AAAA,EAC7B,CAAC,EAAG,cAAa,cAAc;AACjC;AAEA,SAAS,gBAAgB,SAAS;AAChC,MAAI,CAAC,QAAS,cAAa,mBAAmB;AAC9C,MAAI,CAAC,SAAS,OAAO,EAAG,cAAa,aAAa;AAClD,MAAI,QAAQ,OAAO,EAAG,cAAa,gBAAgB;AACrD;AAEA,SAAS,WAAWE,gBAAe,MAAM;AACvC,QAAM,IAAI,MAAMA,eAAc,IAAI,KAAKA,eAAc,SAAS,CAAC;AACjE;AAEA,IAAI,gBAAgB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AACb;AACA,IAAI,eAAe,MAAM,UAAU,EAAE,aAAa;AAClD,IAAI,aAAa;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AACX;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,aAAW,QAAQ,OAAO;AAC1B,aAAW,QAAQ,OAAO;AAC1B,MAAI,QAAQ;AAAA,IACV,SAAS;AAAA,EACX;AACA,MAAI,YAAY,MAAM,cAAc,EAAE,OAAO,OAAO;AACpD,MAAI,SAAS,MAAM,WAAW,EAAE,KAAK;AACrC,MAAI,WAAW,MAAM,WAAW,OAAO,EAAE,OAAO;AAChD,MAAI,aAAa,MAAM,cAAc,EAAE,KAAK;AAE5C,WAASC,YAAW;AAClB,QAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,SAAUC,QAAO;AAClG,aAAOA;AAAA,IACT;AACA,eAAW,SAAS,QAAQ;AAC5B,WAAO,SAAS,MAAM,OAAO;AAAA,EAC/B;AAEA,WAASC,UAAS,eAAe;AAC/B,YAAQ,WAAW,QAAQ,UAAU,UAAU,EAAE,aAAa;AAAA,EAChE;AAEA,SAAO,CAACF,WAAUE,SAAQ;AAC5B;AAEA,SAAS,eAAe,OAAO,eAAe;AAC5C,SAAO,WAAW,aAAa,IAAI,cAAc,MAAM,OAAO,IAAI;AACpE;AAEA,SAAS,YAAY,OAAO,SAAS;AACnC,QAAM,UAAUJ,gBAAeA,gBAAe,CAAC,GAAG,MAAM,OAAO,GAAG,OAAO;AACzE,SAAO;AACT;AAEA,SAAS,eAAe,OAAO,SAAS,SAAS;AAC/C,aAAW,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,OAAO;AAC3F,QAAI;AAEJ,YAAQ,iBAAiB,QAAQ,KAAK,OAAO,QAAQ,mBAAmB,SAAS,SAAS,eAAe,KAAK,SAAS,MAAM,QAAQ,KAAK,CAAC;AAAA,EAC7I,CAAC;AACD,SAAO;AACT;AAEA,IAAI,QAAQ;AAAA,EACV;AACF;AAEA,IAAO,sBAAQ;;;AChMf,IAAI,SAAS;AAAA,EACX,OAAO;AAAA,IACL,IAAI;AAAA,EACN;AACF;AAEA,IAAO,iBAAQ;;;ACNf,SAASK,OAAM,IAAI;AACjB,SAAO,SAAS,UAAU;AACxB,QAAI,QAAQ;AAEZ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,WAAO,KAAK,UAAU,GAAG,SAAS,GAAG,MAAM,MAAM,IAAI,IAAI,WAAY;AACnE,eAAS,QAAQ,UAAU,QAAQ,WAAW,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjG,iBAAS,KAAK,IAAI,UAAU,KAAK;AAAA,MACnC;AAEA,aAAO,QAAQ,MAAM,OAAO,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AACF;AAEA,IAAO,gBAAQA;;;AClBf,SAASC,UAAS,OAAO;AACvB,SAAO,CAAC,EAAE,SAAS,KAAK,KAAK,EAAE,SAAS,QAAQ;AAClD;AAEA,IAAO,mBAAQA;;;ACKf,SAAS,eAAeC,SAAQ;AAC9B,MAAI,CAACA,QAAQ,CAAAC,cAAa,kBAAkB;AAC5C,MAAI,CAAC,iBAASD,OAAM,EAAG,CAAAC,cAAa,YAAY;AAEhD,MAAID,QAAO,MAAM;AACf,2BAAuB;AACvB,WAAO;AAAA,MACL,OAAO;AAAA,QACL,IAAIA,QAAO,KAAK;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAEA,SAAOA;AACT;AAMA,SAAS,yBAAyB;AAChC,UAAQ,KAAKE,eAAc,WAAW;AACxC;AAEA,SAASC,YAAWD,gBAAe,MAAM;AACvC,QAAM,IAAI,MAAMA,eAAc,IAAI,KAAKA,eAAc,SAAS,CAAC;AACjE;AAEA,IAAIA,iBAAgB;AAAA,EAClB,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AACf;AACA,IAAID,gBAAe,cAAME,WAAU,EAAED,cAAa;AAClD,IAAIE,cAAa;AAAA,EACf,QAAQ;AACV;AAEA,IAAO,qBAAQA;;;AChDf,IAAIC,WAAU,SAASA,WAAU;AAC/B,WAAS,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACtF,QAAI,IAAI,IAAI,UAAU,IAAI;AAAA,EAC5B;AAEA,SAAO,SAAU,GAAG;AAClB,WAAO,IAAI,YAAY,SAAU,GAAG,GAAG;AACrC,aAAO,EAAE,CAAC;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AACF;AAEA,IAAO,kBAAQA;;;ACVf,SAAS,MAAM,QAAQ,QAAQ;AAC7B,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,QAAI,OAAO,GAAG,aAAa,QAAQ;AACjC,UAAI,OAAO,GAAG,GAAG;AACf,eAAO,OAAO,OAAO,GAAG,GAAG,MAAM,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC,CAAC;AAAA,MAC5D;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,eAAe,eAAe,CAAC,GAAG,MAAM,GAAG,MAAM;AAC1D;AAEA,IAAO,oBAAQ;;;ACZf,IAAI,sBAAsB;AAAA,EACxB,MAAM;AAAA,EACN,KAAK;AACP;AAEA,SAAS,eAAe,SAAS;AAC/B,MAAI,eAAe;AACnB,MAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1D,YAAQ,KAAK,SAAU,KAAK;AAC1B,aAAO,eAAe,OAAO,mBAAmB,IAAI,QAAQ,GAAG;AAAA,IACjE,CAAC;AACD,YAAQ,OAAO,EAAE,MAAM;AAAA,EACzB,CAAC;AACD,SAAO,eAAe,SAAS,WAAY;AACzC,WAAO,eAAe;AAAA,EACxB,GAAG;AACL;AAEA,IAAO,yBAAQ;;;ACTf,IAAI,gBAAgB,oBAAM,OAAO;AAAA,EAC/B,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AACV,CAAC;AAND,IAOI,iBAAiB,eAAe,eAAe,CAAC;AAPpD,IAQI,WAAW,eAAe,CAAC;AAR/B,IASI,WAAW,eAAe,CAAC;AAO/B,SAASC,QAAO,cAAc;AAC5B,MAAI,qBAAqB,mBAAW,OAAO,YAAY,GACnD,SAAS,mBAAmB,QAC5BA,UAAS,yBAAyB,oBAAoB,CAAC,QAAQ,CAAC;AAEpE,WAAS,SAAU,OAAO;AACxB,WAAO;AAAA,MACL,QAAQ,kBAAM,MAAM,QAAQA,OAAM;AAAA,MAClC;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAOA,SAAS,OAAO;AACd,MAAI,QAAQ,SAAS,SAAU,MAAM;AACnC,QAAI,SAAS,KAAK,QACd,gBAAgB,KAAK,eACrB,UAAU,KAAK;AACnB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,CAAC,MAAM,eAAe;AACxB,aAAS;AAAA,MACP,eAAe;AAAA,IACjB,CAAC;AAED,QAAI,MAAM,QAAQ;AAChB,YAAM,QAAQ,MAAM,MAAM;AAC1B,aAAO,uBAAe,cAAc;AAAA,IACtC;AAEA,QAAI,OAAO,UAAU,OAAO,OAAO,QAAQ;AACzC,0BAAoB,OAAO,MAAM;AACjC,YAAM,QAAQ,OAAO,MAAM;AAC3B,aAAO,uBAAe,cAAc;AAAA,IACtC;AAEA,oBAAQ,eAAe,qBAAqB,EAAE,eAAe;AAAA,EAC/D;AAEA,SAAO,uBAAe,cAAc;AACtC;AAQA,SAAS,cAAc,QAAQ;AAC7B,SAAO,SAAS,KAAK,YAAY,MAAM;AACzC;AAQA,SAAS,aAAa,KAAK;AACzB,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,SAAO,QAAQ,OAAO,MAAM,MAAM;AACpC;AAOA,SAAS,sBAAsBC,kBAAiB;AAC9C,MAAI,QAAQ,SAAS,SAAU,OAAO;AACpC,QAAID,UAAS,MAAM,QACf,SAAS,MAAM;AACnB,WAAO;AAAA,MACL,QAAQA;AAAA,MACR;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,eAAe,aAAa,GAAG,OAAO,MAAM,OAAO,MAAM,IAAI,YAAY,CAAC;AAE9E,eAAa,SAAS,WAAY;AAChC,WAAOC,iBAAgB;AAAA,EACzB;AAEA,eAAa,UAAU,MAAM;AAC7B,SAAO;AACT;AAMA,SAAS,kBAAkB;AACzB,MAAI,QAAQ,SAAS,SAAU,OAAO;AACpC,QAAID,UAAS,MAAM,QACf,UAAU,MAAM,SAChB,SAAS,MAAM;AACnB,WAAO;AAAA,MACL,QAAQA;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAIE,WAAU,OAAO;AAErB,EAAAA,SAAQ,OAAO,MAAM,MAAM;AAE3B,EAAAA,SAAQ,CAAC,uBAAuB,GAAG,SAAU,QAAQ;AACnD,wBAAoB,MAAM;AAC1B,UAAM,QAAQ,MAAM;AAAA,EACtB,GAAG,SAAU,OAAO;AAClB,UAAM,OAAO,KAAK;AAAA,EACpB,CAAC;AACH;AAMA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,CAAC,SAAS,EAAE,QAAQ;AACtB,aAAS;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQA,SAAS,sBAAsB;AAC7B,SAAO,SAAS,SAAU,OAAO;AAC/B,QAAI,SAAS,MAAM;AACnB,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1D,SAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAI,SAAS;AAAA,EACX,QAAQF;AAAA,EACR;AAAA,EACA;AACF;AAEA,IAAO,iBAAQ;;;ACvLf,IAAI,cAAc,OAAO;AACzB,IAAI,aAAa,OAAO;AACxB,IAAI,oBAAoB,OAAO;AAC/B,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAI,gBAAgB,CAAC,GAAG,MAAM,WAAW,GAAG,kBAAkB,CAAC,CAAC;AAChE,IAAM,SAAS;AAAA,EACb,SAAS;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,SAAS;AAAA,EACX;AACF;AACA,SAAS,aAAa,OAAO,eAAe;AAC1C,QAAM,eAAe,SAAS,MAAM;AAClC,UAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,WAAO,cAAc,iBAAiB,CAAC,GAAG,OAAO,OAAO,GAAG;AAAA,MACzD;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,QAAM,iBAAiB,SAAS,MAAM;AACpC,WAAO,iBAAiB,iBAAiB,CAAC,GAAG,OAAO,SAAS,GAAG,CAAC,cAAc,SAAS,OAAO,IAAI;AAAA,EACrG,CAAC;AACD,SAAO,EAAE,cAAc,eAAe;AACxC;AAEA,SAAS,YAAY;AACnB,QAAM,YAAY,WAAW,eAAO,oBAAoB,CAAC;AACzD,QAAM,eAAe,MAAI,KAAK;AAC9B,MAAI;AACJ,YAAU,MAAM;AACd,QAAI,UAAU;AACZ;AACF,cAAU,eAAO,KAAK;AACtB,YAAQ,KAAK,CAAC,mBAAmB,UAAU,QAAQ,cAAc,EAAE,MAAM,CAAC,UAAU;AAClF,WAAK,SAAS,OAAO,SAAS,MAAM,UAAU,eAAe;AAC3D,qBAAa,QAAQ;AACrB,gBAAQ,MAAM,gCAAgC,KAAK;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,QAAM,SAAS,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,MAAM;AACxB,SAAO,OAAO,QAAQ,aAAa,KAAK,IAAI;AAC9C;AACA,SAAS,YAAY,GAAG;AACtB,SAAO,MAAM;AACf;AACA,SAAS,iBAAiB,QAAQ,OAAO,UAAU,MAAM;AACvD,SAAO,SAAS,QAAQ,IAAI,KAAK,YAAY,QAAQ,OAAO,UAAU,IAAI;AAC5E;AACA,SAAS,SAAS,QAAQ,MAAM;AAC9B,SAAO,OAAO,OAAO,SAAS,eAAe,QAAQ,IAAI,CAAC;AAC5D;AACA,SAAS,YAAY,QAAQ,OAAO,UAAU,MAAM;AAClD,SAAO,OAAO,OAAO,YAAY,OAAO,UAAU,OAAO,eAAe,QAAQ,IAAI,IAAI,MAAM;AAChG;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,SAAO,OAAO,IAAI,MAAM,IAAI;AAC9B;AAEA,IAAI,cAAc,OAAO;AACzB,IAAI,wBAAwB,OAAO;AACnC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,mBAAmB,CAAC,GAAG,MAAM;AAC/B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,wBAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AACtC,MAAI;AACF,aAAS,QAAQ,sBAAsB,CAAC,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG,IAAI;AAC7B,0BAAkB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACtC;AACF,SAAO;AACT;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,YAAY;AACd;AACA,IAAI,kBAAkB,gBAAgB;AAAA,EACpC,MAAM;AAAA;AAAA,EAEN,OAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,cAAc;AAAA,IACd,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA;AAAA,IAEN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA,OAAO,CAAC,gBAAgB,eAAe,SAAS,UAAU,UAAU;AAAA,EACpE,MAAM,OAAO,KAAK;AAChB,UAAM,aAA6B,oBAAI,IAAI;AAC3C,UAAM,eAAe,WAAW,IAAI;AACpC,UAAM,EAAE,WAAW,QAAQ,aAAa,IAAI,UAAU;AACtD,UAAM,EAAE,UAAU,IAAI,UAAU,KAAK,OAAO,WAAW,YAAY;AACnE,UAAM,EAAE,iBAAiB,IAAI,aAAa,KAAK,OAAO,WAAW,SAAS;AAC1E,UAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,UAAU,SAAS,CAAC,CAAC,UAAU,KAAK;AAC3E,UAAM,EAAE,cAAc,eAAe,IAAI,aAAa,OAAO,aAAa;AAC1E,gBAAY,MAAM;AAChB,UAAI,IAAI;AACR,OAAC,KAAK,iBAAiB,UAAU,OAAO,SAAS,GAAG,KAAK,gBAAgB;AACzE,UAAI,UAAU,OAAO;AACnB,SAAC,KAAK,UAAU,MAAM,SAAS,MAAM,OAAO,SAAS,GAAG,QAAQ;AAChE,kBAAU,MAAM,QAAQ;AAAA,MAC1B,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD;AAAA,MACE,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,UAAU,MAAM,MAAM,IAAI;AAAA,MAC5E,CAAC,CAAC,SAAS,UAAU,aAAa,OAAO,GAAG,CAAC,SAAS,UAAU,aAAa,OAAO,MAAM;AACxF,YAAI,CAAC,cAAc,OAAO;AACxB;AAAA,QACF;AACA,YAAI,YAAY,SAAS;AACvB,gBAAM,WAAW;AAAA,YACf,UAAU;AAAA,YACV,YAAY,MAAM,gBAAgB;AAAA,YAClC,eAAe,MAAM,mBAAmB;AAAA,YACxC,WAAW,MAAM,eAAe;AAAA,UAClC;AACA,gBAAM,iBAAiB,WAAW,IAAI,SAAS,UAAU,MAAM,cAAc,CAAC;AAC9E,oBAAU,MAAM,SAAS,QAAQ;AACjC,gBAAM,iBAAiB,UAAU,MAAM,iBAAiB,WAAW,IAAI,OAAO,CAAC;AAC/E,cAAI,CAAC,YAAY,OAAO,GAAG;AACzB,sBAAU,MAAM,WAAW,OAAO;AAAA,UACpC;AACA;AAAA,QACF;AACA,YAAI,UAAU,MAAM,SAAS,MAAM,UAAU;AAC3C,oBAAU,MAAM,SAAS,QAAQ;AAAA,QACnC;AACA,YAAI,gBAAgB,aAAa;AAC/B,oBAAU,MAAM,OAAO,iBAAiB,UAAU,MAAM,SAAS,GAAG,WAAW;AAAA,QACjF;AACA,YAAI,CAAC,YAAY,OAAO,KAAK,YAAY,SAAS;AAChD,oBAAU,MAAM,WAAW,OAAO;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AACA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,YAAY,UAAU,SAAS,UAAU,MAAM,cAAc,OAAO;AAAA,MACrE,EAAE,MAAM,KAAK;AAAA,IACf;AACA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,CAAC,UAAU,UAAU,SAAS,UAAU,MAAM,OAAO,SAAS,KAAK;AAAA,IACrE;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,IACF,IAAI;AACJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,QACE,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,CAAC,iBAAiB;AAAA,UAChB;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA,eAAe,OAAO,UAAU,WAAW,OAAO,OAAO,IAAI,gBAAgB,OAAO,UAAU,WAAW,OAAO,OAAO,IAAI;AAAA,QAC7H;AAAA,QACA,EAAE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,SAAS,UAAU,EAAE,KAAK,GAAG,OAAO,WAAW,cAAc;AAC3D,QAAM,YAAY,WAAW,IAAI;AACjC,YAAU,MAAM;AACd,UAAM,OAAO;AAAA,MACX;AAAA,MACA,MAAM;AACJ,YAAI,aAAa,SAAS,UAAU,OAAO;AACzC,mBAAS,MAAM,KAAK,CAAC;AACrB,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAAA,EACF,CAAC;AACD,WAAS,eAAe;AACtB,QAAI;AACJ,QAAI,CAAC,aAAa,SAAS,CAAC,UAAU,SAAS,UAAU,OAAO;AAC9D;AAAA,IACF;AACA,SAAK,eAAe,UAAU,KAAK;AACnC,UAAM,uBAAuB,MAAM,QAAQ,MAAM;AACjD,UAAM,eAAe;AAAA,MACnB,UAAU;AAAA,MACV,MAAM,SAAS,MAAM,gBAAgB;AAAA,MACrC,MAAM,YAAY,MAAM,mBAAmB;AAAA,MAC3C,wBAAwB;AAAA,IAC1B;AACA,cAAU,QAAQ,UAAU,MAAM,OAAO;AAAA,MACvC,aAAa;AAAA,MACb,iBAAiB;AAAA,QACf,OAAO;AAAA,QACP,OAAO,MAAM;AAAA,QACb,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,GAAG,MAAM,OAAO;AAAA,MAChB,MAAM;AAAA,IACR;AACA,KAAC,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,wBAAwB,CAAC,UAAU;AAC9E,YAAM,QAAQ,UAAU,MAAM,SAAS;AACvC,UAAI,UAAU,MAAM,OAAO;AACzB,aAAK,gBAAgB,KAAK;AAC1B,aAAK,UAAU,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,QAAI,UAAU,SAAS,CAAC,YAAY,MAAM,IAAI,GAAG;AAC/C,gBAAU,MAAM,WAAW,MAAM,IAAI;AAAA,IACvC;AACA,SAAK,SAAS,UAAU,OAAO,UAAU,KAAK;AAAA,EAChD;AACA,SAAO,EAAE,UAAU;AACrB;AACA,SAAS,aAAa,EAAE,KAAK,GAAG,OAAO,WAAW,WAAW;AAC3D,QAAM,mBAAmB,MAAI,IAAI;AACjC,QAAM,OAAO,MAAM,CAAC,WAAW,SAAS,GAAG,MAAM;AAC/C,QAAI,UAAU,SAAS,UAAU,OAAO;AACtC,eAAS,MAAM,KAAK,CAAC;AACrB,YAAM,wBAAwB,UAAU,MAAM,OAAO,mBAAmB,CAAC,SAAS;AAChF,YAAI,IAAI;AACR,cAAM,aAAa,MAAM,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,SAAS,MAAM,OAAO,SAAS,GAAG;AACvG,YAAI,WAAW;AACb,gBAAM,gCAAgC,KAAK,KAAK,CAAC,QAAQ,IAAI,SAAS,UAAU,IAAI;AACpF,cAAI,+BAA+B;AACjC,kBAAM,UAAU,UAAU,MAAM,OAAO,gBAAgB;AAAA,cACrD,UAAU;AAAA,YACZ,CAAC;AACD,iBAAK,YAAY,OAAO;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,CAAC;AACD,uBAAiB,QAAQ,MAAM,yBAAyB,OAAO,SAAS,sBAAsB,QAAQ;AAAA,IACxG;AAAA,EACF,CAAC;AACD,SAAO,EAAE,iBAAiB;AAC5B;AAEA,IAAI,YAAY,OAAO;AACvB,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAM,eAAe;AAAA,EACnB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,YAAY;AACd;AACA,IAAI,sBAAsB,gBAAgB;AAAA,EACxC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA;AAAA,IAEnB,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA,MAAM,OAAO,KAAK;AAChB,UAAM,eAAe,WAAW,IAAI;AACpC,UAAM,EAAE,WAAW,QAAQ,aAAa,IAAI,UAAU;AACtD,UAAM,EAAE,cAAc,IAAI,cAAc,KAAK,OAAO,WAAW,YAAY;AAC3E,UAAM,oBAAoB,SAAS,MAAM,CAAC,CAAC,UAAU,SAAS,CAAC,CAAC,cAAc,KAAK;AACnF,UAAM,EAAE,cAAc,eAAe,IAAI,aAAa,OAAO,iBAAiB;AAC9E,gBAAY,MAAM;AAChB,UAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChC,OAAC,UAAU,SAAS,OAAO;AAC3B,YAAM,UAAU,MAAM,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,aAAa,OAAO,SAAS,GAAG,KAAK,EAAE;AAC7G,OAAC,MAAM,KAAK,UAAU,OAAO,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,YAAY,OAAO,SAAS,GAAG,KAAK,EAAE;AACnH,OAAC,MAAM,KAAK,UAAU,OAAO,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,YAAY,OAAO,SAAS,GAAG,KAAK,EAAE;AACnH,OAAC,MAAM,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,YAAY,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,IAC/F,CAAC;AACD;AAAA,MACE,CAAC,MAAM,MAAM,mBAAmB,MAAM,MAAM,UAAU,MAAM,MAAM,kBAAkB,MAAM,MAAM,QAAQ;AAAA,MACxG,CAAC,CAAC,sBAAsB,aAAa,qBAAqB,WAAW,GAAG,CAAC,sBAAsB,aAAa,qBAAqB,WAAW,MAAM;AAChJ,YAAI,CAAC,kBAAkB,OAAO;AAC5B;AAAA,QACF;AACA,cAAM,iBAAiB,cAAc,MAAM,kBAAkB;AAC7D,YAAI,yBAAyB,sBAAsB;AACjD,gBAAM,iBAAiB;AAAA,YACrB,UAAU;AAAA,YACV,eAAe;AAAA,YACf,uBAAuB,eAAe;AAAA,YACtC,wBAAwB;AAAA,UAC1B;AACA,yBAAe,SAAS,cAAc;AACtC;AAAA,QACF;AACA,YAAI,gBAAgB,eAAe,SAAS,GAAG;AAC7C,yBAAe,SAAS,eAAe,EAAE;AAAA,QAC3C;AACA,YAAI,wBAAwB,uBAAuB,gBAAgB,aAAa;AAC9E,oBAAU,MAAM,OAAO;AAAA,YACrB,cAAc,MAAM,SAAS,EAAE;AAAA,YAC/B,uBAAuB,eAAe;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA;AAAA,MACE,CAAC,MAAM,MAAM,mBAAmB,MAAM,MAAM,UAAU,MAAM,MAAM,kBAAkB,MAAM,MAAM,QAAQ;AAAA,MACxG,CAAC,CAAC,sBAAsB,aAAa,qBAAqB,WAAW,GAAG,CAAC,sBAAsB,aAAa,qBAAqB,WAAW,MAAM;AAChJ,YAAI,CAAC,kBAAkB,OAAO;AAC5B;AAAA,QACF;AACA,cAAM,iBAAiB,cAAc,MAAM,kBAAkB;AAC7D,YAAI,yBAAyB,sBAAsB;AACjD,gBAAM,mBAAmB;AAAA,YACvB,UAAU;AAAA,YACV,eAAe;AAAA,YACf,uBAAuB,eAAe;AAAA,YACtC,wBAAwB;AAAA,UAC1B;AACA,yBAAe,SAAS,gBAAgB;AACxC;AAAA,QACF;AACA,YAAI,gBAAgB,eAAe,SAAS,GAAG;AAC7C,gBAAM,eAAe,UAAU,MAAM,OAAO,aAAa;AACzD,cAAI,eAAe,UAAU,YAAY,GAAG;AAC1C,2BAAe,SAAS,eAAe,EAAE;AAAA,UAC3C,OAAO;AACL,2BAAe,aAAa,IAAI;AAAA,cAC9B;AAAA,gBACE,OAAO,eAAe,SAAS,EAAE,kBAAkB;AAAA,gBACnD,MAAM,eAAe;AAAA,gBACrB,kBAAkB;AAAA,cACpB;AAAA,YACF,CAAC;AACD,2BAAe,aAAa;AAAA,UAC9B;AAAA,QACF;AACA,YAAI,wBAAwB,uBAAuB,gBAAgB,aAAa;AAC9E,oBAAU,MAAM,OAAO;AAAA,YACrB,cAAc,MAAM,SAAS,EAAE;AAAA,YAC/B,uBAAuB,eAAe;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACJ,YAAI;AACJ,gBAAQ,KAAK,UAAU,UAAU,OAAO,SAAS,GAAG,OAAO,SAAS,MAAM,KAAK;AAAA,MACjF;AAAA,IACF;AACA;AAAA,MACE,MAAM,MAAM;AAAA,MACZ,MAAM;AACJ,YAAI;AACJ,gBAAQ,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,cAAc,MAAM,OAAO;AAAA,MACrF;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IACf;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,EAAE,QAAQ,mBAAmB,cAAc,cAAc,gBAAgB,UAAU,IAAI;AAC7F,WAAO;AAAA,MACL;AAAA,MACA;AAAA,QACE,OAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,CAAC,qBAAqB;AAAA,UACpB;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA,eAAe,OAAO,UAAU,WAAW,OAAO,OAAO,IAAI,gBAAgB,OAAO,UAAU,WAAW,OAAO,OAAO,IAAI;AAAA,QAC7H;AAAA,QACA,EAAE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,SAAS,cAAc,EAAE,KAAK,GAAG,OAAO,WAAW,cAAc;AAC/D,QAAM,gBAAgB,WAAW,IAAI;AACrC,YAAU,MAAM;AACd,UAAM,OAAO;AAAA,MACX;AAAA,MACA,MAAM;AACJ,YAAI,aAAa,SAAS,UAAU,OAAO;AACzC,mBAAS,MAAM,KAAK,CAAC;AACrB,2BAAiB;AAAA,QACnB;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAAA,EACF,CAAC;AACD,WAAS,mBAAmB;AAC1B,QAAI;AACJ,QAAI,CAAC,aAAa,SAAS,CAAC,UAAU,SAAS,cAAc,OAAO;AAClE;AAAA,IACF;AACA,SAAK,eAAe,UAAU,KAAK;AACnC,kBAAc,QAAQ,UAAU,MAAM,OAAO,iBAAiB,aAAa,OAAO,eAAe;AAAA,MAC/F,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,OAAO,MAAM;AAAA,MACb,eAAe;AAAA,MACf,cAAc;AAAA,IAChB,GAAG,MAAM,OAAO,CAAC;AACjB,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM,YAAY;AAAA,MAClB,MAAM,oBAAoB,MAAM,YAAY;AAAA,MAC5C,MAAM,qBAAqB;AAAA,IAC7B;AACA,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM,YAAY;AAAA,MAClB,MAAM,oBAAoB,MAAM,YAAY;AAAA,MAC5C,MAAM,qBAAqB;AAAA,IAC7B;AACA,KAAC,KAAK,cAAc,UAAU,OAAO,SAAS,GAAG,SAAS;AAAA,MACxD,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS,cAAc,OAAO,UAAU,KAAK;AAAA,EACpD;AACA,SAAO,EAAE,cAAc;AACzB;AAEA,SAAS,QAAQ,KAAK,SAAS;AAC7B,MAAI,SAAS;AACX,mBAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,UAAU,gBAAgB,MAAM,eAAe;AACnD,MAAI,UAAU,oBAAoB,MAAM,mBAAmB;AAC7D;", "names": ["_defineProperty", "ownKeys", "_objectSpread2", "errorMessages", "getState", "state", "setState", "curry", "isObject", "config", "<PERSON><PERSON><PERSON><PERSON>", "errorMessages", "throwError", "validators", "compose", "config", "configure<PERSON><PERSON><PERSON>", "require"]}