import {
  init_polyfills,
  polyfills_exports
} from "./chunk-NITV7DIR.js";
import {
  __toCommonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/vue-svg-inline-plugin/package.json
var package_default = {
  name: "vue-svg-inline-plugin",
  version: "2.2.3",
  description: "Vue plugin for inline replacement of SVG images with actual content of SVG files.",
  main: "src/index.js",
  types: "src/index.d.ts",
  unpkg: "dist/vue-svg-inline-plugin.min.js",
  jsdelivr: "dist/vue-svg-inline-plugin.min.js",
  files: [
    "index.js",
    "dist/*.js",
    "src/*.{js,d.ts}"
  ],
  directories: {
    example: "examples"
  },
  scripts: {
    build: "npm run remove && npm run build:modern && npm run build:default",
    "build:default": "npm run remove:default && npx cross-env IMPORT_POLYFILLS=1 npm run webpack",
    "build:modern": "npm run remove:modern && npm run webpack",
    remove: "npm run remove:default && npm run remove:modern",
    "remove:default": "npx rimraf dist/vue-svg-inline-plugin.min.js",
    "remove:modern": "npx rimraf dist/vue-svg-inline-plugin-modern.min.js",
    webpack: "npx webpack --progress --color",
    test: 'echo "Error: no test specified" && exit 1'
  },
  repository: {
    type: "git",
    url: "git+https://github.com/oliverfindl/vue-svg-inline-plugin.git"
  },
  keywords: [
    "vue",
    "vuejs",
    "plugin",
    "vue-plugin",
    "svg",
    "inline",
    "sprites",
    "symbols",
    "vue-svg-inline-plugin"
  ],
  author: "Oliver Findl",
  license: "MIT",
  bugs: {
    url: "https://github.com/oliverfindl/vue-svg-inline-plugin/issues"
  },
  homepage: "https://github.com/oliverfindl/vue-svg-inline-plugin#readme",
  dependencies: {
    "core-js": "^3.25.2",
    "intersection-observer": "^0.12.2",
    "whatwg-fetch": "^3.6.2"
  },
  devDependencies: {
    "@babel/core": "^7.19.1",
    "@babel/eslint-parser": "^7.19.1",
    "@babel/preset-env": "^7.19.1",
    "babel-loader": "^8.2.5",
    "babel-plugin-remove-template-literals-whitespace": "^1.0.4",
    "cross-env": "^7.0.3",
    eslint: "^8.23.1",
    "eslint-plugin-vue": "^9.5.1",
    "eslint-webpack-plugin": "^3.2.0",
    rimraf: "^3.0.2",
    "terser-webpack-plugin": "^5.3.6",
    webpack: "^5.74.0",
    "webpack-bundle-analyzer": "^4.6.1",
    "webpack-cli": "^4.10.0"
  }
};

// node_modules/vue-svg-inline-plugin/src/index.js
var PACKAGE_NAME = package_default.name;
var PACKAGE_VERSION = package_default.version;
if (typeof IMPORT_POLYFILLS !== "undefined" && !!IMPORT_POLYFILLS) init_polyfills();
var DEFAULT_OPTIONS = {
  directive: {
    name: "v-svg-inline",
    spriteModifierName: "sprite"
  },
  attributes: {
    clone: ["viewbox"],
    merge: ["class", "style"],
    add: [{
      name: "focusable",
      value: false
    }, {
      name: "role",
      value: "presentation"
    }, {
      name: "tabindex",
      value: -1
    }],
    data: [],
    remove: ["alt", "src", "data-src"]
  },
  cache: {
    version: PACKAGE_VERSION,
    persistent: true,
    removeRevisions: true
  },
  intersectionObserverOptions: {},
  axios: null,
  xhtml: false
};
var OBSERVER_REF_ID = "observer";
var CONTAINER_REF_ID = "container";
var FLAGS_ID = `${PACKAGE_NAME}-flags`;
var SYMBOL_ID = `${PACKAGE_NAME}-sprite`;
var CONTAINER_ID = `${SYMBOL_ID}-${CONTAINER_REF_ID}`;
var REGEXP_SVG_FILENAME = /.+\.svg(?:[?#].*)?$/i;
var REGEXP_SVG_CONTENT = /<svg(\s+[^>]+)?>([\s\S]+)<\/svg>/i;
var REGEXP_ATTRIBUTES = /\s*([^\s=]+)[\s=]+(?:"([^"]*)"|'([^']*)')?\s*/g;
var REGEXP_ATTRIBUTE_NAME = /^[a-z](?:[a-z0-9-:]*[a-z0-9])?$/i;
var REGEXP_VUE_DIRECTIVE = /^v-/i;
var REGEXP_WHITESPACE = /\s+/g;
var REGEXP_TEMPLATE_LITERALS_WHITESPACE = /[\n\t]+/g;
var CORRECT_RESPONSE_STATUSES = /* @__PURE__ */ new Set([
  200,
  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/200
  304
  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/304
]);
var install = (VueOrApp = null, options = {}) => {
  const _str = "string";
  const _fnc = "function";
  const _obj = "object";
  if (!VueOrApp) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [VueOrApp]`);
  if (![_fnc, _obj].includes(typeof VueOrApp)) throw new TypeError(`[${PACKAGE_NAME}] Required argument is not valid! [VueOrApp]`);
  if (!VueOrApp.directive) throw new Error(`[${PACKAGE_NAME}] Required method is missing! [VueOrApp.directive]`);
  if (typeof VueOrApp.directive !== _fnc) throw new TypeError(`[${PACKAGE_NAME}] Required method is not valid! [VueOrApp.directive]`);
  if (!VueOrApp.version) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [VueOrApp.version]`);
  if (typeof VueOrApp.version !== _str) throw new TypeError(`[${PACKAGE_NAME}] Required property is not valid! [VueOrApp.version]`);
  if (VueOrApp.version.startsWith("1.")) throw new Error(`[${PACKAGE_NAME}] Vue@1 is not supported!`);
  ["directive", "attributes", "cache", "intersectionObserverOptions"].forEach((option) => options[option] = Object.assign({}, DEFAULT_OPTIONS[option], options[option] || {}));
  options = Object.assign({}, DEFAULT_OPTIONS, options);
  for (const option in options.directive) {
    options.directive[option] = options.directive[option].toString().trim().toLowerCase();
    if (!options.directive[option] || option === "name" && !REGEXP_ATTRIBUTE_NAME.test(options.directive[option])) throw new TypeError(`[${PACKAGE_NAME}] Option is not valid! [options.directives.${option}="${options.directives[option]}"]`);
  }
  options.directive.name = options.directive.name.replace(REGEXP_VUE_DIRECTIVE, "");
  for (const option in options.attributes) {
    if (!Array.isArray(options.attributes[option])) throw new TypeError(`[${PACKAGE_NAME}] Option is not valid! [options.attributes.${option}=${JSON.stringify(options.attributes[option])}]`);
    options.attributes[option] = option === "add" ? options.attributes[option].map((attribute) => ({
      name: attribute.name.toString().trim().toLowerCase(),
      value: attribute.value.toString().trim()
    })) : options.attributes[option].map((attribute) => attribute.toString().trim().toLowerCase());
    options.attributes[option] = new Set(options.attributes[option]);
  }
  for (const option in options.cache) {
    options.cache[option] = option === "version" ? options.cache[option].toString().trim().toLowerCase() : !!options.cache[option];
  }
  options.xhtml = !!options.xhtml;
  const isVue3 = (
    /* !(VueOrApp instanceof Function) && */
    VueOrApp.version.startsWith("3.")
  );
  options._fetch = "fetch" in window && typeof fetch === _fnc;
  options._axios = "axios" in window && typeof axios === _fnc;
  const validateAxiosGetMethod = (axios2 = null) => !!axios2 && typeof axios2 === _fnc && "get" in axios2 && typeof axios2.get === _fnc;
  let axiosIsValid = false;
  options.axios = ((axiosIsValid = validateAxiosGetMethod(options.axios)) ? options.axios : null) || (options._axios && "create" in axios && typeof axios.create === _fnc ? axios.create() : null);
  options._axios = axiosIsValid || validateAxiosGetMethod(options.axios);
  if (!options._fetch && !options._axios) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [fetch || axios]`);
  options._observer = "IntersectionObserver" in window;
  if (!options._observer) console.error(`[${PACKAGE_NAME}] Feature is not supported by browser! Disabling lazy processing of image nodes. [IntersectionObserver]`);
  options._storage = "localStorage" in window;
  if (!options._storage && options.cache.persistent) console.error(`[${PACKAGE_NAME}] Feature is not supported by browser! Disabling persistent cache of SVG files. [localStorage]`);
  const CACHE_ID = `${PACKAGE_NAME}:${options.cache.version}`;
  if (options._storage && options.cache.removeRevisions) Object.entries(localStorage).map((item) => item.shift()).filter((item) => item.startsWith(`${PACKAGE_NAME}:`) && !item.endsWith(`:${options.cache.version}`)).forEach((item) => localStorage.removeItem(item));
  const cache = options._storage && options.cache.persistent ? new Map(JSON.parse(localStorage.getItem(CACHE_ID) || "[]")) : /* @__PURE__ */ new Map();
  const symbols = /* @__PURE__ */ new Set();
  const refs = /* @__PURE__ */ new Map();
  const createImageNodeIntersectionObserver = () => {
    if (!options._observer) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [IntersectionObserver]`);
    if (refs.has(OBSERVER_REF_ID)) throw new Error(`[${PACKAGE_NAME}] Can not create image node intersection observer, intersection observer already exists!`);
    const observer = new IntersectionObserver((entries, observer2) => {
      for (const entry of entries) {
        if (!entry.isIntersecting) continue;
        const node = entry.target;
        processImageNode(node);
        observer2.unobserve(node);
      }
    }, options.intersectionObserverOptions);
    refs.set(OBSERVER_REF_ID, observer);
    return observer;
  };
  const getImageNodeIntersectionObserver = () => {
    return refs.has(OBSERVER_REF_ID) ? refs.get(OBSERVER_REF_ID) : createImageNodeIntersectionObserver();
  };
  const createSvgSymbolContainer = () => {
    if (refs.has(CONTAINER_REF_ID)) throw new Error(`[${PACKAGE_NAME}] Can not create SVG symbol container node, container node already exists!`);
    let container = createNode(`<svg xmlns="http://www.w3.org/2000/svg" id="${CONTAINER_ID}" style="display: none !important;"></svg>`);
    document.body.appendChild(container);
    refs.set(CONTAINER_REF_ID, container = document.getElementById(CONTAINER_ID));
    return container;
  };
  const getSvgSymbolContainer = () => {
    return refs.has(CONTAINER_REF_ID) ? refs.get(CONTAINER_REF_ID) : createSvgSymbolContainer();
  };
  const createNode = (string = "") => {
    if (!string) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [string]`);
    string = string.toString().trim();
    if (!string.startsWith("<") || !string.endsWith(">")) throw new TypeError(`[${PACKAGE_NAME}] Argument is not valid! [string="${string}"]`);
    string = string.replace(REGEXP_TEMPLATE_LITERALS_WHITESPACE, "");
    return document.createRange().createContextualFragment(string);
  };
  const replaceNode = (node = null, newNode = null) => {
    if (!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);
    if (!newNode) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [newNode]`);
    if (!node.parentNode) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [node.parentNode]`);
    node.parentNode.replaceChild(newNode, node);
  };
  const createAttributeMapFromString = (string = "") => {
    if (!string) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [string]`);
    string = string.toString().trim();
    const attributes = /* @__PURE__ */ new Map();
    REGEXP_ATTRIBUTES.lastIndex = 0;
    let attribute;
    while (attribute = REGEXP_ATTRIBUTES.exec(string)) {
      if (attribute.index === REGEXP_ATTRIBUTES.lastIndex) REGEXP_ATTRIBUTES.lastIndex++;
      const name = (attribute[1] || "").trim().toLowerCase();
      if (!name || name.startsWith("<") || name.endsWith(">")) continue;
      if (!REGEXP_ATTRIBUTE_NAME.test(name)) throw new TypeError(`[${PACKAGE_NAME}] Attribute name is not valid! [attribute="${name}"]`);
      const value = (attribute[2] || attribute[3] || "").trim();
      attributes.set(name, value ? value : options.xhtml ? name : "");
    }
    return attributes;
  };
  const createAttributeMapFromNamedNodeMap = (namedNodeAttributeMap = null) => {
    if (!namedNodeAttributeMap) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [namedNodeAttributeMap]`);
    if (!(namedNodeAttributeMap instanceof NamedNodeMap)) throw new TypeError(`[${PACKAGE_NAME}] Argument is not valid! [namedNodeAttributeMap]`);
    const attributes = new Map([...namedNodeAttributeMap].map(({ name, value }) => {
      name = (name || "").trim().toLowerCase();
      if (!REGEXP_ATTRIBUTE_NAME.test(name)) throw new TypeError(`[${PACKAGE_NAME}] Attribute name is not valid! [attribute="${name}"]`);
      value = (value || "").trim();
      return [name, value ? value : options.xhtml ? name : ""];
    }));
    return attributes;
  };
  const fetchSvgFile = (path = "") => {
    if (!options._fetch && !options._axios) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [fetch || axios]`);
    if (!path) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [path]`);
    path = path.toString().trim();
    if (!REGEXP_SVG_FILENAME.test(path)) throw new TypeError(`[${PACKAGE_NAME}] Argument is not valid! [path="${path}"]`);
    return new Promise((resolve, reject) => {
      const file = { path };
      if (cache.has(file.path)) {
        file.content = cache.get(file.path);
        return resolve(file);
      }
      (options._axios ? options.axios.get : fetch)(file.path).then((response) => {
        if (!CORRECT_RESPONSE_STATUSES.has(response.status | 0)) throw new Error(`Wrong response status! [response.status=${response.status}]`);
        return options._axios ? response.data.toString() : response.text();
      }).then((content) => {
        file.content = content.trim();
        cache.set(file.path, file.content);
        if (options._storage && options.cache.persistent) localStorage.setItem(CACHE_ID, JSON.stringify([...cache]));
        return resolve(file);
      }).catch(reject);
    });
  };
  const parseSvgFile = (file = null, node = null) => {
    if (!file) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [file]`);
    if (!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);
    if (!file.path) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [file.path]`);
    file.path = file.path.toString().trim();
    if (!REGEXP_SVG_FILENAME.test(file.path)) throw new TypeError(`[${PACKAGE_NAME}] Argument property is not valid! [file.path="${file.path}"]`);
    if (!file.content) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [file.content]`);
    file.content = file.content.toString().trim();
    if (!REGEXP_SVG_CONTENT.test(file.content)) throw new TypeError(`[${PACKAGE_NAME}] Argument property is not valid! [file.content="${file.content}"]`);
    if (!node.outerHTML) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [node.outerHTML]`);
    if (node[FLAGS_ID].has("sprite")) {
      file.content = file.content.replace(REGEXP_SVG_CONTENT, (svg, attributes, symbol) => {
        const symbolAlreadyDefined = symbols.has(file.path);
        const id = `${SYMBOL_ID}-${symbolAlreadyDefined ? [...symbols].indexOf(file.path) : symbols.size}`;
        if (!symbolAlreadyDefined) {
          const symbolNode = createNode(`
						<svg xmlns="http://www.w3.org/2000/svg">
							<symbol id="${id}"${attributes}>
								${symbol}
							</symbol>
						</svg>
					`);
          getSvgSymbolContainer().appendChild(symbolNode.firstChild.firstChild);
          symbols.add(file.path);
        }
        return `
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"${options.attributes.clone.size && (attributes = createAttributeMapFromString(attributes)) ? ` ${[...options.attributes.clone].filter((attribute) => !!attribute && attributes.has(attribute)).map((attribute) => `${attribute}="${attributes.get(attribute)}"`).join(" ")}` : ""}>
						<use xlink:href="#${id}" href="#${id}"></use>
					</svg>
				`;
      });
    }
    return file.content.replace(REGEXP_SVG_CONTENT, (svg, attributes, symbol) => {
      const fileAttributes = createAttributeMapFromString(attributes);
      const nodeAttributes = createAttributeMapFromNamedNodeMap(node.attributes);
      attributes = new Map([...fileAttributes, ...nodeAttributes]);
      const uniqueAttributeValues = /* @__PURE__ */ new Set(["class"]);
      for (const attribute of options.attributes.merge) {
        const fileValues = fileAttributes.has(attribute) ? fileAttributes.get(attribute).split(REGEXP_WHITESPACE).filter((value) => !!value) : [];
        const nodeValues = nodeAttributes.has(attribute) ? nodeAttributes.get(attribute).split(REGEXP_WHITESPACE).filter((value) => !!value) : [];
        if (options.xhtml && !fileValues.length && !nodeValues.length) continue;
        const values = [...fileValues, ...nodeValues];
        attributes.set(attribute, (uniqueAttributeValues.has(attribute) ? [...new Set(values)] : values).join(" ").trim());
      }
      for (const attribute of options.attributes.add) {
        let values = attribute.value.split(REGEXP_WHITESPACE).filter((value) => !!value);
        if (attributes.has(attribute.name)) {
          if (!options.attributes.merge.has(attribute.name)) throw new Error(`[${PACKAGE_NAME}] Can not add attribute, attribute already exists. [${attribute.name}]`);
          const oldValues = attributes.get(attribute.name).split(REGEXP_WHITESPACE).filter((value) => !!value);
          if (options.xhtml && !values.length && !oldValues.length) continue;
          values = [...oldValues, ...values];
        }
        attributes.set(attribute.name, (uniqueAttributeValues.has(attribute.name) ? [...new Set(values)] : values).join(" ").trim());
      }
      for (const attribute of options.attributes.data) {
        if (!attributes.has(attribute)) continue;
        let values = attributes.get(attribute).split(REGEXP_WHITESPACE).filter((value) => !!value);
        const dataAttribute = `data-${attribute}`;
        if (attributes.has(dataAttribute)) {
          if (!options.attributes.merge.has(dataAttribute)) throw new Error(`[${PACKAGE_NAME}] Can not transform attribute to data-attribute, data-attribute already exists. [${attribute}]`);
          const oldValues = attributes.get(dataAttribute).split(REGEXP_WHITESPACE).filter((value) => !!value);
          if (options.xhtml && !values.length && !oldValues.length) continue;
          values = [...oldValues, ...values];
        }
        attributes.set(dataAttribute, (uniqueAttributeValues.has(attribute) ? [...new Set(values)] : values).join(" ").trim());
        if (!options.attributes.remove.has(attribute)) options.attributes.remove.add(attribute);
      }
      for (const attribute of options.attributes.remove) {
        if (!attributes.has(attribute)) continue;
        attributes.delete(attribute);
      }
      return `
				<svg${attributes.size ? ` ${[...attributes.keys()].filter((attribute) => !!attribute).map((attribute) => `${attribute}="${attributes.get(attribute)}"`).join(" ")}` : ""}>
					${symbol}
				</svg>
			`;
    });
  };
  const processImageNode = (node = null) => {
    if (!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);
    if (!node.dataset.src && !node.src) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [node.data-src || node.src]`);
    if (node.dataset.src) node.dataset.src = node.dataset.src.toString().trim();
    if (node.src) node.src = node.src.toString().trim();
    fetchSvgFile(node.dataset.src || node.src).then((file) => {
      const svgString = parseSvgFile(file, node);
      const svgNode = createNode(svgString);
      replaceNode(node, svgNode);
    }).catch((error) => console.error(`[${PACKAGE_NAME}] ${error.toString()}`));
  };
  const beforeMount = (node = null, binding = null, vnode = null) => {
    if (!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);
    if (node.tagName !== "IMG") throw new Error(`[${PACKAGE_NAME}] Required argument is not valid! [node]`);
    if (!vnode) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [vnode]`);
    if (!node[FLAGS_ID]) node[FLAGS_ID] = /* @__PURE__ */ new Set();
    if (node[FLAGS_ID].has("processed")) return;
    node[FLAGS_ID].add("processed");
    const directives = isVue3 ? vnode.dirs : vnode.data.directives;
    if (directives.length > 1) throw new Error(`[${PACKAGE_NAME}] Node has more than 1 directive! [${isVue3 ? "vnode.dirs" : "vnode.data.directives"}]`);
    if (!!directives[0].modifiers[options.directive.spriteModifierName]) node[FLAGS_ID].add("sprite");
    if (!options._observer && node.dataset.src) {
      node.src = node.dataset.src;
      delete node.dataset.src;
    }
    if (node.dataset.src) getImageNodeIntersectionObserver().observe(node);
    else processImageNode(node);
  };
  VueOrApp.directive(options.directive.name, isVue3 ? { beforeMount } : { bind: beforeMount });
};
var src_default = { install };
export {
  src_default as default
};
/*! Bundled license information:

vue-svg-inline-plugin/src/index.js:
  (**
   * <AUTHOR> Findl
   * @version 2.2.3
   * @license MIT
   *)
*/
//# sourceMappingURL=vue-svg-inline-plugin.js.map
