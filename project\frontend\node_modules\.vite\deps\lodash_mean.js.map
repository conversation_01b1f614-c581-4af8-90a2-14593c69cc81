{"version": 3, "sources": ["../../lodash/_baseMean.js", "../../lodash/mean.js"], "sourcesContent": ["var baseSum = require('./_baseSum');\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/**\n * The base implementation of `_.mean` and `_.meanBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the mean.\n */\nfunction baseMean(array, iteratee) {\n  var length = array == null ? 0 : array.length;\n  return length ? (baseSum(array, iteratee) / length) : NAN;\n}\n\nmodule.exports = baseMean;\n", "var baseMean = require('./_baseMean'),\n    identity = require('./identity');\n\n/**\n * Computes the mean of the values in `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {number} Returns the mean.\n * @example\n *\n * _.mean([4, 2, 8, 6]);\n * // => 5\n */\nfunction mean(array) {\n  return baseMean(array, identity);\n}\n\nmodule.exports = mean;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AAGd,QAAI,MAAM,IAAI;AAWd,aAAS,SAAS,OAAO,UAAU;AACjC,UAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,aAAO,SAAU,QAAQ,OAAO,QAAQ,IAAI,SAAU;AAAA,IACxD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,WAAW;AAgBf,aAAS,KAAK,OAAO;AACnB,aAAO,SAAS,OAAO,QAAQ;AAAA,IACjC;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}