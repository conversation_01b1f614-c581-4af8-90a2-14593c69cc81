{"version": 3, "sources": ["../../vue-router/dist/vue-router.esm.js"], "sourcesContent": ["/*!\n  * vue-router v3.6.5\n  * (c) 2022 Evan You\n  * @license MIT\n  */\n/*  */\n\nfunction assert (condition, message) {\n  if (!condition) {\n    throw new Error((\"[vue-router] \" + message))\n  }\n}\n\nfunction warn (condition, message) {\n  if (!condition) {\n    typeof console !== 'undefined' && console.warn((\"[vue-router] \" + message));\n  }\n}\n\nfunction extend (a, b) {\n  for (var key in b) {\n    a[key] = b[key];\n  }\n  return a\n}\n\n/*  */\n\nvar encodeReserveRE = /[!'()*]/g;\nvar encodeReserveReplacer = function (c) { return '%' + c.charCodeAt(0).toString(16); };\nvar commaRE = /%2C/g;\n\n// fixed encodeURIComponent which is more conformant to RFC3986:\n// - escapes [!'()*]\n// - preserve commas\nvar encode = function (str) { return encodeURIComponent(str)\n    .replace(encodeReserveRE, encodeReserveReplacer)\n    .replace(commaRE, ','); };\n\nfunction decode (str) {\n  try {\n    return decodeURIComponent(str)\n  } catch (err) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(false, (\"Error decoding \\\"\" + str + \"\\\". Leaving it intact.\"));\n    }\n  }\n  return str\n}\n\nfunction resolveQuery (\n  query,\n  extraQuery,\n  _parseQuery\n) {\n  if ( extraQuery === void 0 ) extraQuery = {};\n\n  var parse = _parseQuery || parseQuery;\n  var parsedQuery;\n  try {\n    parsedQuery = parse(query || '');\n  } catch (e) {\n    process.env.NODE_ENV !== 'production' && warn(false, e.message);\n    parsedQuery = {};\n  }\n  for (var key in extraQuery) {\n    var value = extraQuery[key];\n    parsedQuery[key] = Array.isArray(value)\n      ? value.map(castQueryParamValue)\n      : castQueryParamValue(value);\n  }\n  return parsedQuery\n}\n\nvar castQueryParamValue = function (value) { return (value == null || typeof value === 'object' ? value : String(value)); };\n\nfunction parseQuery (query) {\n  var res = {};\n\n  query = query.trim().replace(/^(\\?|#|&)/, '');\n\n  if (!query) {\n    return res\n  }\n\n  query.split('&').forEach(function (param) {\n    var parts = param.replace(/\\+/g, ' ').split('=');\n    var key = decode(parts.shift());\n    var val = parts.length > 0 ? decode(parts.join('=')) : null;\n\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n\n  return res\n}\n\nfunction stringifyQuery (obj) {\n  var res = obj\n    ? Object.keys(obj)\n      .map(function (key) {\n        var val = obj[key];\n\n        if (val === undefined) {\n          return ''\n        }\n\n        if (val === null) {\n          return encode(key)\n        }\n\n        if (Array.isArray(val)) {\n          var result = [];\n          val.forEach(function (val2) {\n            if (val2 === undefined) {\n              return\n            }\n            if (val2 === null) {\n              result.push(encode(key));\n            } else {\n              result.push(encode(key) + '=' + encode(val2));\n            }\n          });\n          return result.join('&')\n        }\n\n        return encode(key) + '=' + encode(val)\n      })\n      .filter(function (x) { return x.length > 0; })\n      .join('&')\n    : null;\n  return res ? (\"?\" + res) : ''\n}\n\n/*  */\n\nvar trailingSlashRE = /\\/?$/;\n\nfunction createRoute (\n  record,\n  location,\n  redirectedFrom,\n  router\n) {\n  var stringifyQuery = router && router.options.stringifyQuery;\n\n  var query = location.query || {};\n  try {\n    query = clone(query);\n  } catch (e) {}\n\n  var route = {\n    name: location.name || (record && record.name),\n    meta: (record && record.meta) || {},\n    path: location.path || '/',\n    hash: location.hash || '',\n    query: query,\n    params: location.params || {},\n    fullPath: getFullPath(location, stringifyQuery),\n    matched: record ? formatMatch(record) : []\n  };\n  if (redirectedFrom) {\n    route.redirectedFrom = getFullPath(redirectedFrom, stringifyQuery);\n  }\n  return Object.freeze(route)\n}\n\nfunction clone (value) {\n  if (Array.isArray(value)) {\n    return value.map(clone)\n  } else if (value && typeof value === 'object') {\n    var res = {};\n    for (var key in value) {\n      res[key] = clone(value[key]);\n    }\n    return res\n  } else {\n    return value\n  }\n}\n\n// the starting route that represents the initial state\nvar START = createRoute(null, {\n  path: '/'\n});\n\nfunction formatMatch (record) {\n  var res = [];\n  while (record) {\n    res.unshift(record);\n    record = record.parent;\n  }\n  return res\n}\n\nfunction getFullPath (\n  ref,\n  _stringifyQuery\n) {\n  var path = ref.path;\n  var query = ref.query; if ( query === void 0 ) query = {};\n  var hash = ref.hash; if ( hash === void 0 ) hash = '';\n\n  var stringify = _stringifyQuery || stringifyQuery;\n  return (path || '/') + stringify(query) + hash\n}\n\nfunction isSameRoute (a, b, onlyPath) {\n  if (b === START) {\n    return a === b\n  } else if (!b) {\n    return false\n  } else if (a.path && b.path) {\n    return a.path.replace(trailingSlashRE, '') === b.path.replace(trailingSlashRE, '') && (onlyPath ||\n      a.hash === b.hash &&\n      isObjectEqual(a.query, b.query))\n  } else if (a.name && b.name) {\n    return (\n      a.name === b.name &&\n      (onlyPath || (\n        a.hash === b.hash &&\n      isObjectEqual(a.query, b.query) &&\n      isObjectEqual(a.params, b.params))\n      )\n    )\n  } else {\n    return false\n  }\n}\n\nfunction isObjectEqual (a, b) {\n  if ( a === void 0 ) a = {};\n  if ( b === void 0 ) b = {};\n\n  // handle null value #1566\n  if (!a || !b) { return a === b }\n  var aKeys = Object.keys(a).sort();\n  var bKeys = Object.keys(b).sort();\n  if (aKeys.length !== bKeys.length) {\n    return false\n  }\n  return aKeys.every(function (key, i) {\n    var aVal = a[key];\n    var bKey = bKeys[i];\n    if (bKey !== key) { return false }\n    var bVal = b[key];\n    // query values can be null and undefined\n    if (aVal == null || bVal == null) { return aVal === bVal }\n    // check nested equality\n    if (typeof aVal === 'object' && typeof bVal === 'object') {\n      return isObjectEqual(aVal, bVal)\n    }\n    return String(aVal) === String(bVal)\n  })\n}\n\nfunction isIncludedRoute (current, target) {\n  return (\n    current.path.replace(trailingSlashRE, '/').indexOf(\n      target.path.replace(trailingSlashRE, '/')\n    ) === 0 &&\n    (!target.hash || current.hash === target.hash) &&\n    queryIncludes(current.query, target.query)\n  )\n}\n\nfunction queryIncludes (current, target) {\n  for (var key in target) {\n    if (!(key in current)) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction handleRouteEntered (route) {\n  for (var i = 0; i < route.matched.length; i++) {\n    var record = route.matched[i];\n    for (var name in record.instances) {\n      var instance = record.instances[name];\n      var cbs = record.enteredCbs[name];\n      if (!instance || !cbs) { continue }\n      delete record.enteredCbs[name];\n      for (var i$1 = 0; i$1 < cbs.length; i$1++) {\n        if (!instance._isBeingDestroyed) { cbs[i$1](instance); }\n      }\n    }\n  }\n}\n\nvar View = {\n  name: 'RouterView',\n  functional: true,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    }\n  },\n  render: function render (_, ref) {\n    var props = ref.props;\n    var children = ref.children;\n    var parent = ref.parent;\n    var data = ref.data;\n\n    // used by devtools to display a router-view badge\n    data.routerView = true;\n\n    // directly use parent context's createElement() function\n    // so that components rendered by router-view can resolve named slots\n    var h = parent.$createElement;\n    var name = props.name;\n    var route = parent.$route;\n    var cache = parent._routerViewCache || (parent._routerViewCache = {});\n\n    // determine current view depth, also check to see if the tree\n    // has been toggled inactive but kept-alive.\n    var depth = 0;\n    var inactive = false;\n    while (parent && parent._routerRoot !== parent) {\n      var vnodeData = parent.$vnode ? parent.$vnode.data : {};\n      if (vnodeData.routerView) {\n        depth++;\n      }\n      if (vnodeData.keepAlive && parent._directInactive && parent._inactive) {\n        inactive = true;\n      }\n      parent = parent.$parent;\n    }\n    data.routerViewDepth = depth;\n\n    // render previous view if the tree is inactive and kept-alive\n    if (inactive) {\n      var cachedData = cache[name];\n      var cachedComponent = cachedData && cachedData.component;\n      if (cachedComponent) {\n        // #2301\n        // pass props\n        if (cachedData.configProps) {\n          fillPropsinData(cachedComponent, data, cachedData.route, cachedData.configProps);\n        }\n        return h(cachedComponent, data, children)\n      } else {\n        // render previous empty view\n        return h()\n      }\n    }\n\n    var matched = route.matched[depth];\n    var component = matched && matched.components[name];\n\n    // render empty node if no matched route or no config component\n    if (!matched || !component) {\n      cache[name] = null;\n      return h()\n    }\n\n    // cache component\n    cache[name] = { component: component };\n\n    // attach instance registration hook\n    // this will be called in the instance's injected lifecycle hooks\n    data.registerRouteInstance = function (vm, val) {\n      // val could be undefined for unregistration\n      var current = matched.instances[name];\n      if (\n        (val && current !== vm) ||\n        (!val && current === vm)\n      ) {\n        matched.instances[name] = val;\n      }\n    }\n\n    // also register instance in prepatch hook\n    // in case the same component instance is reused across different routes\n    ;(data.hook || (data.hook = {})).prepatch = function (_, vnode) {\n      matched.instances[name] = vnode.componentInstance;\n    };\n\n    // register instance in init hook\n    // in case kept-alive component be actived when routes changed\n    data.hook.init = function (vnode) {\n      if (vnode.data.keepAlive &&\n        vnode.componentInstance &&\n        vnode.componentInstance !== matched.instances[name]\n      ) {\n        matched.instances[name] = vnode.componentInstance;\n      }\n\n      // if the route transition has already been confirmed then we weren't\n      // able to call the cbs during confirmation as the component was not\n      // registered yet, so we call it here.\n      handleRouteEntered(route);\n    };\n\n    var configProps = matched.props && matched.props[name];\n    // save route and configProps in cache\n    if (configProps) {\n      extend(cache[name], {\n        route: route,\n        configProps: configProps\n      });\n      fillPropsinData(component, data, route, configProps);\n    }\n\n    return h(component, data, children)\n  }\n};\n\nfunction fillPropsinData (component, data, route, configProps) {\n  // resolve props\n  var propsToPass = data.props = resolveProps(route, configProps);\n  if (propsToPass) {\n    // clone to prevent mutation\n    propsToPass = data.props = extend({}, propsToPass);\n    // pass non-declared props as attrs\n    var attrs = data.attrs = data.attrs || {};\n    for (var key in propsToPass) {\n      if (!component.props || !(key in component.props)) {\n        attrs[key] = propsToPass[key];\n        delete propsToPass[key];\n      }\n    }\n  }\n}\n\nfunction resolveProps (route, config) {\n  switch (typeof config) {\n    case 'undefined':\n      return\n    case 'object':\n      return config\n    case 'function':\n      return config(route)\n    case 'boolean':\n      return config ? route.params : undefined\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        warn(\n          false,\n          \"props in \\\"\" + (route.path) + \"\\\" is a \" + (typeof config) + \", \" +\n          \"expecting an object, function or boolean.\"\n        );\n      }\n  }\n}\n\n/*  */\n\nfunction resolvePath (\n  relative,\n  base,\n  append\n) {\n  var firstChar = relative.charAt(0);\n  if (firstChar === '/') {\n    return relative\n  }\n\n  if (firstChar === '?' || firstChar === '#') {\n    return base + relative\n  }\n\n  var stack = base.split('/');\n\n  // remove trailing segment if:\n  // - not appending\n  // - appending to trailing slash (last segment is empty)\n  if (!append || !stack[stack.length - 1]) {\n    stack.pop();\n  }\n\n  // resolve relative path\n  var segments = relative.replace(/^\\//, '').split('/');\n  for (var i = 0; i < segments.length; i++) {\n    var segment = segments[i];\n    if (segment === '..') {\n      stack.pop();\n    } else if (segment !== '.') {\n      stack.push(segment);\n    }\n  }\n\n  // ensure leading slash\n  if (stack[0] !== '') {\n    stack.unshift('');\n  }\n\n  return stack.join('/')\n}\n\nfunction parsePath (path) {\n  var hash = '';\n  var query = '';\n\n  var hashIndex = path.indexOf('#');\n  if (hashIndex >= 0) {\n    hash = path.slice(hashIndex);\n    path = path.slice(0, hashIndex);\n  }\n\n  var queryIndex = path.indexOf('?');\n  if (queryIndex >= 0) {\n    query = path.slice(queryIndex + 1);\n    path = path.slice(0, queryIndex);\n  }\n\n  return {\n    path: path,\n    query: query,\n    hash: hash\n  }\n}\n\nfunction cleanPath (path) {\n  return path.replace(/\\/(?:\\s*\\/)+/g, '/')\n}\n\nvar isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n\n/**\n * Expose `pathToRegexp`.\n */\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n  // Match escaped characters that would otherwise appear in future matches.\n  // This allows the user to escape special characters that won't transform.\n  '(\\\\\\\\.)',\n  // Match Express-style parameters and un-named parameters with a prefix\n  // and optional suffixes. Matches appear as:\n  //\n  // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n  // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n  // \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n  '([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'\n].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse (str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue\n    }\n\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7];\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = res[2] || defaultDelimiter;\n    var pattern = capture || group;\n\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : (asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?')\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n\n  return tokens\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile (str, options) {\n  return tokensToFunction(parse(str, options), options)\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty (str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk (str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction (tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n\n      if (typeof token === 'string') {\n        path += token;\n\n        continue\n      }\n\n      var value = data[token.name];\n      var segment;\n\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n\n          continue\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined')\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`')\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty')\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`')\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n\n        continue\n      }\n\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n      }\n\n      path += token.prefix + segment;\n    }\n\n    return path\n  }\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString (str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1')\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup (group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1')\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys (re, keys) {\n  re.keys = keys;\n  return re\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags (options) {\n  return options && options.sensitive ? '' : 'i'\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp (path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n\n  return attachKeys(path, keys)\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp (path, keys, options) {\n  var parts = [];\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n\n  return attachKeys(regexp, keys)\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp (path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options)\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp (tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options);\n    keys = [];\n  }\n\n  options = options || {};\n\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n\n      keys.push(token);\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n\n      route += capture;\n    }\n  }\n\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter;\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n\n  return attachKeys(new RegExp('^' + route, flags(options)), keys)\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp (path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options);\n    keys = [];\n  }\n\n  options = options || {};\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */ (keys))\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */ (path), /** @type {!Array} */ (keys), options)\n  }\n\n  return stringToRegexp(/** @type {string} */ (path), /** @type {!Array} */ (keys), options)\n}\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n\n/*  */\n\n// $flow-disable-line\nvar regexpCompileCache = Object.create(null);\n\nfunction fillParams (\n  path,\n  params,\n  routeMsg\n) {\n  params = params || {};\n  try {\n    var filler =\n      regexpCompileCache[path] ||\n      (regexpCompileCache[path] = pathToRegexp_1.compile(path));\n\n    // Fix #2505 resolving asterisk routes { name: 'not-found', params: { pathMatch: '/not-found' }}\n    // and fix #3106 so that you can work with location descriptor object having params.pathMatch equal to empty string\n    if (typeof params.pathMatch === 'string') { params[0] = params.pathMatch; }\n\n    return filler(params, { pretty: true })\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      // Fix #3072 no warn if `pathMatch` is string\n      warn(typeof params.pathMatch === 'string', (\"missing param for \" + routeMsg + \": \" + (e.message)));\n    }\n    return ''\n  } finally {\n    // delete the 0 if it was added\n    delete params[0];\n  }\n}\n\n/*  */\n\nfunction normalizeLocation (\n  raw,\n  current,\n  append,\n  router\n) {\n  var next = typeof raw === 'string' ? { path: raw } : raw;\n  // named target\n  if (next._normalized) {\n    return next\n  } else if (next.name) {\n    next = extend({}, raw);\n    var params = next.params;\n    if (params && typeof params === 'object') {\n      next.params = extend({}, params);\n    }\n    return next\n  }\n\n  // relative params\n  if (!next.path && next.params && current) {\n    next = extend({}, next);\n    next._normalized = true;\n    var params$1 = extend(extend({}, current.params), next.params);\n    if (current.name) {\n      next.name = current.name;\n      next.params = params$1;\n    } else if (current.matched.length) {\n      var rawPath = current.matched[current.matched.length - 1].path;\n      next.path = fillParams(rawPath, params$1, (\"path \" + (current.path)));\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"relative params navigation requires a current route.\");\n    }\n    return next\n  }\n\n  var parsedPath = parsePath(next.path || '');\n  var basePath = (current && current.path) || '/';\n  var path = parsedPath.path\n    ? resolvePath(parsedPath.path, basePath, append || next.append)\n    : basePath;\n\n  var query = resolveQuery(\n    parsedPath.query,\n    next.query,\n    router && router.options.parseQuery\n  );\n\n  var hash = next.hash || parsedPath.hash;\n  if (hash && hash.charAt(0) !== '#') {\n    hash = \"#\" + hash;\n  }\n\n  return {\n    _normalized: true,\n    path: path,\n    query: query,\n    hash: hash\n  }\n}\n\n/*  */\n\n// work around weird flow bug\nvar toTypes = [String, Object];\nvar eventTypes = [String, Array];\n\nvar noop = function () {};\n\nvar warnedCustomSlot;\nvar warnedTagProp;\nvar warnedEventProp;\n\nvar Link = {\n  name: 'RouterLink',\n  props: {\n    to: {\n      type: toTypes,\n      required: true\n    },\n    tag: {\n      type: String,\n      default: 'a'\n    },\n    custom: Boolean,\n    exact: Boolean,\n    exactPath: Boolean,\n    append: Boolean,\n    replace: Boolean,\n    activeClass: String,\n    exactActiveClass: String,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    event: {\n      type: eventTypes,\n      default: 'click'\n    }\n  },\n  render: function render (h) {\n    var this$1$1 = this;\n\n    var router = this.$router;\n    var current = this.$route;\n    var ref = router.resolve(\n      this.to,\n      current,\n      this.append\n    );\n    var location = ref.location;\n    var route = ref.route;\n    var href = ref.href;\n\n    var classes = {};\n    var globalActiveClass = router.options.linkActiveClass;\n    var globalExactActiveClass = router.options.linkExactActiveClass;\n    // Support global empty active class\n    var activeClassFallback =\n      globalActiveClass == null ? 'router-link-active' : globalActiveClass;\n    var exactActiveClassFallback =\n      globalExactActiveClass == null\n        ? 'router-link-exact-active'\n        : globalExactActiveClass;\n    var activeClass =\n      this.activeClass == null ? activeClassFallback : this.activeClass;\n    var exactActiveClass =\n      this.exactActiveClass == null\n        ? exactActiveClassFallback\n        : this.exactActiveClass;\n\n    var compareTarget = route.redirectedFrom\n      ? createRoute(null, normalizeLocation(route.redirectedFrom), null, router)\n      : route;\n\n    classes[exactActiveClass] = isSameRoute(current, compareTarget, this.exactPath);\n    classes[activeClass] = this.exact || this.exactPath\n      ? classes[exactActiveClass]\n      : isIncludedRoute(current, compareTarget);\n\n    var ariaCurrentValue = classes[exactActiveClass] ? this.ariaCurrentValue : null;\n\n    var handler = function (e) {\n      if (guardEvent(e)) {\n        if (this$1$1.replace) {\n          router.replace(location, noop);\n        } else {\n          router.push(location, noop);\n        }\n      }\n    };\n\n    var on = { click: guardEvent };\n    if (Array.isArray(this.event)) {\n      this.event.forEach(function (e) {\n        on[e] = handler;\n      });\n    } else {\n      on[this.event] = handler;\n    }\n\n    var data = { class: classes };\n\n    var scopedSlot =\n      !this.$scopedSlots.$hasNormal &&\n      this.$scopedSlots.default &&\n      this.$scopedSlots.default({\n        href: href,\n        route: route,\n        navigate: handler,\n        isActive: classes[activeClass],\n        isExactActive: classes[exactActiveClass]\n      });\n\n    if (scopedSlot) {\n      if (process.env.NODE_ENV !== 'production' && !this.custom) {\n        !warnedCustomSlot && warn(false, 'In Vue Router 4, the v-slot API will by default wrap its content with an <a> element. Use the custom prop to remove this warning:\\n<router-link v-slot=\"{ navigate, href }\" custom></router-link>\\n');\n        warnedCustomSlot = true;\n      }\n      if (scopedSlot.length === 1) {\n        return scopedSlot[0]\n      } else if (scopedSlot.length > 1 || !scopedSlot.length) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\n            false,\n            (\"<router-link> with to=\\\"\" + (this.to) + \"\\\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element.\")\n          );\n        }\n        return scopedSlot.length === 0 ? h() : h('span', {}, scopedSlot)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if ('tag' in this.$options.propsData && !warnedTagProp) {\n        warn(\n          false,\n          \"<router-link>'s tag prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\"\n        );\n        warnedTagProp = true;\n      }\n      if ('event' in this.$options.propsData && !warnedEventProp) {\n        warn(\n          false,\n          \"<router-link>'s event prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\"\n        );\n        warnedEventProp = true;\n      }\n    }\n\n    if (this.tag === 'a') {\n      data.on = on;\n      data.attrs = { href: href, 'aria-current': ariaCurrentValue };\n    } else {\n      // find the first <a> child and apply listener and href\n      var a = findAnchor(this.$slots.default);\n      if (a) {\n        // in case the <a> is a static node\n        a.isStatic = false;\n        var aData = (a.data = extend({}, a.data));\n        aData.on = aData.on || {};\n        // transform existing events in both objects into arrays so we can push later\n        for (var event in aData.on) {\n          var handler$1 = aData.on[event];\n          if (event in on) {\n            aData.on[event] = Array.isArray(handler$1) ? handler$1 : [handler$1];\n          }\n        }\n        // append new listeners for router-link\n        for (var event$1 in on) {\n          if (event$1 in aData.on) {\n            // on[event] is always a function\n            aData.on[event$1].push(on[event$1]);\n          } else {\n            aData.on[event$1] = handler;\n          }\n        }\n\n        var aAttrs = (a.data.attrs = extend({}, a.data.attrs));\n        aAttrs.href = href;\n        aAttrs['aria-current'] = ariaCurrentValue;\n      } else {\n        // doesn't have <a> child, apply listener to self\n        data.on = on;\n      }\n    }\n\n    return h(this.tag, data, this.$slots.default)\n  }\n};\n\nfunction guardEvent (e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) { return }\n  // don't redirect when preventDefault called\n  if (e.defaultPrevented) { return }\n  // don't redirect on right click\n  if (e.button !== undefined && e.button !== 0) { return }\n  // don't redirect if `target=\"_blank\"`\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    var target = e.currentTarget.getAttribute('target');\n    if (/\\b_blank\\b/i.test(target)) { return }\n  }\n  // this may be a Weex event which doesn't have this method\n  if (e.preventDefault) {\n    e.preventDefault();\n  }\n  return true\n}\n\nfunction findAnchor (children) {\n  if (children) {\n    var child;\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      if (child.tag === 'a') {\n        return child\n      }\n      if (child.children && (child = findAnchor(child.children))) {\n        return child\n      }\n    }\n  }\n}\n\nvar _Vue;\n\nfunction install (Vue) {\n  if (install.installed && _Vue === Vue) { return }\n  install.installed = true;\n\n  _Vue = Vue;\n\n  var isDef = function (v) { return v !== undefined; };\n\n  var registerInstance = function (vm, callVal) {\n    var i = vm.$options._parentVnode;\n    if (isDef(i) && isDef(i = i.data) && isDef(i = i.registerRouteInstance)) {\n      i(vm, callVal);\n    }\n  };\n\n  Vue.mixin({\n    beforeCreate: function beforeCreate () {\n      if (isDef(this.$options.router)) {\n        this._routerRoot = this;\n        this._router = this.$options.router;\n        this._router.init(this);\n        Vue.util.defineReactive(this, '_route', this._router.history.current);\n      } else {\n        this._routerRoot = (this.$parent && this.$parent._routerRoot) || this;\n      }\n      registerInstance(this, this);\n    },\n    destroyed: function destroyed () {\n      registerInstance(this);\n    }\n  });\n\n  Object.defineProperty(Vue.prototype, '$router', {\n    get: function get () { return this._routerRoot._router }\n  });\n\n  Object.defineProperty(Vue.prototype, '$route', {\n    get: function get () { return this._routerRoot._route }\n  });\n\n  Vue.component('RouterView', View);\n  Vue.component('RouterLink', Link);\n\n  var strats = Vue.config.optionMergeStrategies;\n  // use the same hook merging strategy for route hooks\n  strats.beforeRouteEnter = strats.beforeRouteLeave = strats.beforeRouteUpdate = strats.created;\n}\n\n/*  */\n\nvar inBrowser = typeof window !== 'undefined';\n\n/*  */\n\nfunction createRouteMap (\n  routes,\n  oldPathList,\n  oldPathMap,\n  oldNameMap,\n  parentRoute\n) {\n  // the path list is used to control path matching priority\n  var pathList = oldPathList || [];\n  // $flow-disable-line\n  var pathMap = oldPathMap || Object.create(null);\n  // $flow-disable-line\n  var nameMap = oldNameMap || Object.create(null);\n\n  routes.forEach(function (route) {\n    addRouteRecord(pathList, pathMap, nameMap, route, parentRoute);\n  });\n\n  // ensure wildcard routes are always at the end\n  for (var i = 0, l = pathList.length; i < l; i++) {\n    if (pathList[i] === '*') {\n      pathList.push(pathList.splice(i, 1)[0]);\n      l--;\n      i--;\n    }\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // warn if routes do not include leading slashes\n    var found = pathList\n    // check for missing leading slash\n      .filter(function (path) { return path && path.charAt(0) !== '*' && path.charAt(0) !== '/'; });\n\n    if (found.length > 0) {\n      var pathNames = found.map(function (path) { return (\"- \" + path); }).join('\\n');\n      warn(false, (\"Non-nested routes must include a leading slash character. Fix the following routes: \\n\" + pathNames));\n    }\n  }\n\n  return {\n    pathList: pathList,\n    pathMap: pathMap,\n    nameMap: nameMap\n  }\n}\n\nfunction addRouteRecord (\n  pathList,\n  pathMap,\n  nameMap,\n  route,\n  parent,\n  matchAs\n) {\n  var path = route.path;\n  var name = route.name;\n  if (process.env.NODE_ENV !== 'production') {\n    assert(path != null, \"\\\"path\\\" is required in a route configuration.\");\n    assert(\n      typeof route.component !== 'string',\n      \"route config \\\"component\\\" for path: \" + (String(\n        path || name\n      )) + \" cannot be a \" + \"string id. Use an actual component instead.\"\n    );\n\n    warn(\n      // eslint-disable-next-line no-control-regex\n      !/[^\\u0000-\\u007F]+/.test(path),\n      \"Route with path \\\"\" + path + \"\\\" contains unencoded characters, make sure \" +\n        \"your path is correctly encoded before passing it to the router. Use \" +\n        \"encodeURI to encode static segments of your path.\"\n    );\n  }\n\n  var pathToRegexpOptions =\n    route.pathToRegexpOptions || {};\n  var normalizedPath = normalizePath(path, parent, pathToRegexpOptions.strict);\n\n  if (typeof route.caseSensitive === 'boolean') {\n    pathToRegexpOptions.sensitive = route.caseSensitive;\n  }\n\n  var record = {\n    path: normalizedPath,\n    regex: compileRouteRegex(normalizedPath, pathToRegexpOptions),\n    components: route.components || { default: route.component },\n    alias: route.alias\n      ? typeof route.alias === 'string'\n        ? [route.alias]\n        : route.alias\n      : [],\n    instances: {},\n    enteredCbs: {},\n    name: name,\n    parent: parent,\n    matchAs: matchAs,\n    redirect: route.redirect,\n    beforeEnter: route.beforeEnter,\n    meta: route.meta || {},\n    props:\n      route.props == null\n        ? {}\n        : route.components\n          ? route.props\n          : { default: route.props }\n  };\n\n  if (route.children) {\n    // Warn if route is named, does not redirect and has a default child route.\n    // If users navigate to this route by name, the default child will\n    // not be rendered (GH Issue #629)\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        route.name &&\n        !route.redirect &&\n        route.children.some(function (child) { return /^\\/?$/.test(child.path); })\n      ) {\n        warn(\n          false,\n          \"Named Route '\" + (route.name) + \"' has a default child route. \" +\n            \"When navigating to this named route (:to=\\\"{name: '\" + (route.name) + \"'}\\\"), \" +\n            \"the default child route will not be rendered. Remove the name from \" +\n            \"this route and use the name of the default child route for named \" +\n            \"links instead.\"\n        );\n      }\n    }\n    route.children.forEach(function (child) {\n      var childMatchAs = matchAs\n        ? cleanPath((matchAs + \"/\" + (child.path)))\n        : undefined;\n      addRouteRecord(pathList, pathMap, nameMap, child, record, childMatchAs);\n    });\n  }\n\n  if (!pathMap[record.path]) {\n    pathList.push(record.path);\n    pathMap[record.path] = record;\n  }\n\n  if (route.alias !== undefined) {\n    var aliases = Array.isArray(route.alias) ? route.alias : [route.alias];\n    for (var i = 0; i < aliases.length; ++i) {\n      var alias = aliases[i];\n      if (process.env.NODE_ENV !== 'production' && alias === path) {\n        warn(\n          false,\n          (\"Found an alias with the same value as the path: \\\"\" + path + \"\\\". You have to remove that alias. It will be ignored in development.\")\n        );\n        // skip in dev to make it work\n        continue\n      }\n\n      var aliasRoute = {\n        path: alias,\n        children: route.children\n      };\n      addRouteRecord(\n        pathList,\n        pathMap,\n        nameMap,\n        aliasRoute,\n        parent,\n        record.path || '/' // matchAs\n      );\n    }\n  }\n\n  if (name) {\n    if (!nameMap[name]) {\n      nameMap[name] = record;\n    } else if (process.env.NODE_ENV !== 'production' && !matchAs) {\n      warn(\n        false,\n        \"Duplicate named routes definition: \" +\n          \"{ name: \\\"\" + name + \"\\\", path: \\\"\" + (record.path) + \"\\\" }\"\n      );\n    }\n  }\n}\n\nfunction compileRouteRegex (\n  path,\n  pathToRegexpOptions\n) {\n  var regex = pathToRegexp_1(path, [], pathToRegexpOptions);\n  if (process.env.NODE_ENV !== 'production') {\n    var keys = Object.create(null);\n    regex.keys.forEach(function (key) {\n      warn(\n        !keys[key.name],\n        (\"Duplicate param keys in route with path: \\\"\" + path + \"\\\"\")\n      );\n      keys[key.name] = true;\n    });\n  }\n  return regex\n}\n\nfunction normalizePath (\n  path,\n  parent,\n  strict\n) {\n  if (!strict) { path = path.replace(/\\/$/, ''); }\n  if (path[0] === '/') { return path }\n  if (parent == null) { return path }\n  return cleanPath(((parent.path) + \"/\" + path))\n}\n\n/*  */\n\n\n\nfunction createMatcher (\n  routes,\n  router\n) {\n  var ref = createRouteMap(routes);\n  var pathList = ref.pathList;\n  var pathMap = ref.pathMap;\n  var nameMap = ref.nameMap;\n\n  function addRoutes (routes) {\n    createRouteMap(routes, pathList, pathMap, nameMap);\n  }\n\n  function addRoute (parentOrRoute, route) {\n    var parent = (typeof parentOrRoute !== 'object') ? nameMap[parentOrRoute] : undefined;\n    // $flow-disable-line\n    createRouteMap([route || parentOrRoute], pathList, pathMap, nameMap, parent);\n\n    // add aliases of parent\n    if (parent && parent.alias.length) {\n      createRouteMap(\n        // $flow-disable-line route is defined if parent is\n        parent.alias.map(function (alias) { return ({ path: alias, children: [route] }); }),\n        pathList,\n        pathMap,\n        nameMap,\n        parent\n      );\n    }\n  }\n\n  function getRoutes () {\n    return pathList.map(function (path) { return pathMap[path]; })\n  }\n\n  function match (\n    raw,\n    currentRoute,\n    redirectedFrom\n  ) {\n    var location = normalizeLocation(raw, currentRoute, false, router);\n    var name = location.name;\n\n    if (name) {\n      var record = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        warn(record, (\"Route with name '\" + name + \"' does not exist\"));\n      }\n      if (!record) { return _createRoute(null, location) }\n      var paramNames = record.regex.keys\n        .filter(function (key) { return !key.optional; })\n        .map(function (key) { return key.name; });\n\n      if (typeof location.params !== 'object') {\n        location.params = {};\n      }\n\n      if (currentRoute && typeof currentRoute.params === 'object') {\n        for (var key in currentRoute.params) {\n          if (!(key in location.params) && paramNames.indexOf(key) > -1) {\n            location.params[key] = currentRoute.params[key];\n          }\n        }\n      }\n\n      location.path = fillParams(record.path, location.params, (\"named route \\\"\" + name + \"\\\"\"));\n      return _createRoute(record, location, redirectedFrom)\n    } else if (location.path) {\n      location.params = {};\n      for (var i = 0; i < pathList.length; i++) {\n        var path = pathList[i];\n        var record$1 = pathMap[path];\n        if (matchRoute(record$1.regex, location.path, location.params)) {\n          return _createRoute(record$1, location, redirectedFrom)\n        }\n      }\n    }\n    // no match\n    return _createRoute(null, location)\n  }\n\n  function redirect (\n    record,\n    location\n  ) {\n    var originalRedirect = record.redirect;\n    var redirect = typeof originalRedirect === 'function'\n      ? originalRedirect(createRoute(record, location, null, router))\n      : originalRedirect;\n\n    if (typeof redirect === 'string') {\n      redirect = { path: redirect };\n    }\n\n    if (!redirect || typeof redirect !== 'object') {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(\n          false, (\"invalid redirect option: \" + (JSON.stringify(redirect)))\n        );\n      }\n      return _createRoute(null, location)\n    }\n\n    var re = redirect;\n    var name = re.name;\n    var path = re.path;\n    var query = location.query;\n    var hash = location.hash;\n    var params = location.params;\n    query = re.hasOwnProperty('query') ? re.query : query;\n    hash = re.hasOwnProperty('hash') ? re.hash : hash;\n    params = re.hasOwnProperty('params') ? re.params : params;\n\n    if (name) {\n      // resolved named direct\n      var targetRecord = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(targetRecord, (\"redirect failed: named route \\\"\" + name + \"\\\" not found.\"));\n      }\n      return match({\n        _normalized: true,\n        name: name,\n        query: query,\n        hash: hash,\n        params: params\n      }, undefined, location)\n    } else if (path) {\n      // 1. resolve relative redirect\n      var rawPath = resolveRecordPath(path, record);\n      // 2. resolve params\n      var resolvedPath = fillParams(rawPath, params, (\"redirect route with path \\\"\" + rawPath + \"\\\"\"));\n      // 3. rematch with existing query and hash\n      return match({\n        _normalized: true,\n        path: resolvedPath,\n        query: query,\n        hash: hash\n      }, undefined, location)\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, (\"invalid redirect option: \" + (JSON.stringify(redirect))));\n      }\n      return _createRoute(null, location)\n    }\n  }\n\n  function alias (\n    record,\n    location,\n    matchAs\n  ) {\n    var aliasedPath = fillParams(matchAs, location.params, (\"aliased route with path \\\"\" + matchAs + \"\\\"\"));\n    var aliasedMatch = match({\n      _normalized: true,\n      path: aliasedPath\n    });\n    if (aliasedMatch) {\n      var matched = aliasedMatch.matched;\n      var aliasedRecord = matched[matched.length - 1];\n      location.params = aliasedMatch.params;\n      return _createRoute(aliasedRecord, location)\n    }\n    return _createRoute(null, location)\n  }\n\n  function _createRoute (\n    record,\n    location,\n    redirectedFrom\n  ) {\n    if (record && record.redirect) {\n      return redirect(record, redirectedFrom || location)\n    }\n    if (record && record.matchAs) {\n      return alias(record, location, record.matchAs)\n    }\n    return createRoute(record, location, redirectedFrom, router)\n  }\n\n  return {\n    match: match,\n    addRoute: addRoute,\n    getRoutes: getRoutes,\n    addRoutes: addRoutes\n  }\n}\n\nfunction matchRoute (\n  regex,\n  path,\n  params\n) {\n  var m = path.match(regex);\n\n  if (!m) {\n    return false\n  } else if (!params) {\n    return true\n  }\n\n  for (var i = 1, len = m.length; i < len; ++i) {\n    var key = regex.keys[i - 1];\n    if (key) {\n      // Fix #1994: using * with props: true generates a param named 0\n      params[key.name || 'pathMatch'] = typeof m[i] === 'string' ? decode(m[i]) : m[i];\n    }\n  }\n\n  return true\n}\n\nfunction resolveRecordPath (path, record) {\n  return resolvePath(path, record.parent ? record.parent.path : '/', true)\n}\n\n/*  */\n\n// use User Timing api (if present) for more accurate key precision\nvar Time =\n  inBrowser && window.performance && window.performance.now\n    ? window.performance\n    : Date;\n\nfunction genStateKey () {\n  return Time.now().toFixed(3)\n}\n\nvar _key = genStateKey();\n\nfunction getStateKey () {\n  return _key\n}\n\nfunction setStateKey (key) {\n  return (_key = key)\n}\n\n/*  */\n\nvar positionStore = Object.create(null);\n\nfunction setupScroll () {\n  // Prevent browser scroll behavior on History popstate\n  if ('scrollRestoration' in window.history) {\n    window.history.scrollRestoration = 'manual';\n  }\n  // Fix for #1585 for Firefox\n  // Fix for #2195 Add optional third attribute to workaround a bug in safari https://bugs.webkit.org/show_bug.cgi?id=182678\n  // Fix for #2774 Support for apps loaded from Windows file shares not mapped to network drives: replaced location.origin with\n  // window.location.protocol + '//' + window.location.host\n  // location.host contains the port and location.hostname doesn't\n  var protocolAndPath = window.location.protocol + '//' + window.location.host;\n  var absolutePath = window.location.href.replace(protocolAndPath, '');\n  // preserve existing history state as it could be overriden by the user\n  var stateCopy = extend({}, window.history.state);\n  stateCopy.key = getStateKey();\n  window.history.replaceState(stateCopy, '', absolutePath);\n  window.addEventListener('popstate', handlePopState);\n  return function () {\n    window.removeEventListener('popstate', handlePopState);\n  }\n}\n\nfunction handleScroll (\n  router,\n  to,\n  from,\n  isPop\n) {\n  if (!router.app) {\n    return\n  }\n\n  var behavior = router.options.scrollBehavior;\n  if (!behavior) {\n    return\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    assert(typeof behavior === 'function', \"scrollBehavior must be a function\");\n  }\n\n  // wait until re-render finishes before scrolling\n  router.app.$nextTick(function () {\n    var position = getScrollPosition();\n    var shouldScroll = behavior.call(\n      router,\n      to,\n      from,\n      isPop ? position : null\n    );\n\n    if (!shouldScroll) {\n      return\n    }\n\n    if (typeof shouldScroll.then === 'function') {\n      shouldScroll\n        .then(function (shouldScroll) {\n          scrollToPosition((shouldScroll), position);\n        })\n        .catch(function (err) {\n          if (process.env.NODE_ENV !== 'production') {\n            assert(false, err.toString());\n          }\n        });\n    } else {\n      scrollToPosition(shouldScroll, position);\n    }\n  });\n}\n\nfunction saveScrollPosition () {\n  var key = getStateKey();\n  if (key) {\n    positionStore[key] = {\n      x: window.pageXOffset,\n      y: window.pageYOffset\n    };\n  }\n}\n\nfunction handlePopState (e) {\n  saveScrollPosition();\n  if (e.state && e.state.key) {\n    setStateKey(e.state.key);\n  }\n}\n\nfunction getScrollPosition () {\n  var key = getStateKey();\n  if (key) {\n    return positionStore[key]\n  }\n}\n\nfunction getElementPosition (el, offset) {\n  var docEl = document.documentElement;\n  var docRect = docEl.getBoundingClientRect();\n  var elRect = el.getBoundingClientRect();\n  return {\n    x: elRect.left - docRect.left - offset.x,\n    y: elRect.top - docRect.top - offset.y\n  }\n}\n\nfunction isValidPosition (obj) {\n  return isNumber(obj.x) || isNumber(obj.y)\n}\n\nfunction normalizePosition (obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : window.pageXOffset,\n    y: isNumber(obj.y) ? obj.y : window.pageYOffset\n  }\n}\n\nfunction normalizeOffset (obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : 0,\n    y: isNumber(obj.y) ? obj.y : 0\n  }\n}\n\nfunction isNumber (v) {\n  return typeof v === 'number'\n}\n\nvar hashStartsWithNumberRE = /^#\\d/;\n\nfunction scrollToPosition (shouldScroll, position) {\n  var isObject = typeof shouldScroll === 'object';\n  if (isObject && typeof shouldScroll.selector === 'string') {\n    // getElementById would still fail if the selector contains a more complicated query like #main[data-attr]\n    // but at the same time, it doesn't make much sense to select an element with an id and an extra selector\n    var el = hashStartsWithNumberRE.test(shouldScroll.selector) // $flow-disable-line\n      ? document.getElementById(shouldScroll.selector.slice(1)) // $flow-disable-line\n      : document.querySelector(shouldScroll.selector);\n\n    if (el) {\n      var offset =\n        shouldScroll.offset && typeof shouldScroll.offset === 'object'\n          ? shouldScroll.offset\n          : {};\n      offset = normalizeOffset(offset);\n      position = getElementPosition(el, offset);\n    } else if (isValidPosition(shouldScroll)) {\n      position = normalizePosition(shouldScroll);\n    }\n  } else if (isObject && isValidPosition(shouldScroll)) {\n    position = normalizePosition(shouldScroll);\n  }\n\n  if (position) {\n    // $flow-disable-line\n    if ('scrollBehavior' in document.documentElement.style) {\n      window.scrollTo({\n        left: position.x,\n        top: position.y,\n        // $flow-disable-line\n        behavior: shouldScroll.behavior\n      });\n    } else {\n      window.scrollTo(position.x, position.y);\n    }\n  }\n}\n\n/*  */\n\nvar supportsPushState =\n  inBrowser &&\n  (function () {\n    var ua = window.navigator.userAgent;\n\n    if (\n      (ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) &&\n      ua.indexOf('Mobile Safari') !== -1 &&\n      ua.indexOf('Chrome') === -1 &&\n      ua.indexOf('Windows Phone') === -1\n    ) {\n      return false\n    }\n\n    return window.history && typeof window.history.pushState === 'function'\n  })();\n\nfunction pushState (url, replace) {\n  saveScrollPosition();\n  // try...catch the pushState call to get around Safari\n  // DOM Exception 18 where it limits to 100 pushState calls\n  var history = window.history;\n  try {\n    if (replace) {\n      // preserve existing history state as it could be overriden by the user\n      var stateCopy = extend({}, history.state);\n      stateCopy.key = getStateKey();\n      history.replaceState(stateCopy, '', url);\n    } else {\n      history.pushState({ key: setStateKey(genStateKey()) }, '', url);\n    }\n  } catch (e) {\n    window.location[replace ? 'replace' : 'assign'](url);\n  }\n}\n\nfunction replaceState (url) {\n  pushState(url, true);\n}\n\n// When changing thing, also edit router.d.ts\nvar NavigationFailureType = {\n  redirected: 2,\n  aborted: 4,\n  cancelled: 8,\n  duplicated: 16\n};\n\nfunction createNavigationRedirectedError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.redirected,\n    (\"Redirected when going from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (stringifyRoute(\n      to\n    )) + \"\\\" via a navigation guard.\")\n  )\n}\n\nfunction createNavigationDuplicatedError (from, to) {\n  var error = createRouterError(\n    from,\n    to,\n    NavigationFailureType.duplicated,\n    (\"Avoided redundant navigation to current location: \\\"\" + (from.fullPath) + \"\\\".\")\n  );\n  // backwards compatible with the first introduction of Errors\n  error.name = 'NavigationDuplicated';\n  return error\n}\n\nfunction createNavigationCancelledError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.cancelled,\n    (\"Navigation cancelled from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (to.fullPath) + \"\\\" with a new navigation.\")\n  )\n}\n\nfunction createNavigationAbortedError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.aborted,\n    (\"Navigation aborted from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (to.fullPath) + \"\\\" via a navigation guard.\")\n  )\n}\n\nfunction createRouterError (from, to, type, message) {\n  var error = new Error(message);\n  error._isRouter = true;\n  error.from = from;\n  error.to = to;\n  error.type = type;\n\n  return error\n}\n\nvar propertiesToLog = ['params', 'query', 'hash'];\n\nfunction stringifyRoute (to) {\n  if (typeof to === 'string') { return to }\n  if ('path' in to) { return to.path }\n  var location = {};\n  propertiesToLog.forEach(function (key) {\n    if (key in to) { location[key] = to[key]; }\n  });\n  return JSON.stringify(location, null, 2)\n}\n\nfunction isError (err) {\n  return Object.prototype.toString.call(err).indexOf('Error') > -1\n}\n\nfunction isNavigationFailure (err, errorType) {\n  return (\n    isError(err) &&\n    err._isRouter &&\n    (errorType == null || err.type === errorType)\n  )\n}\n\n/*  */\n\nfunction runQueue (queue, fn, cb) {\n  var step = function (index) {\n    if (index >= queue.length) {\n      cb();\n    } else {\n      if (queue[index]) {\n        fn(queue[index], function () {\n          step(index + 1);\n        });\n      } else {\n        step(index + 1);\n      }\n    }\n  };\n  step(0);\n}\n\n/*  */\n\nfunction resolveAsyncComponents (matched) {\n  return function (to, from, next) {\n    var hasAsync = false;\n    var pending = 0;\n    var error = null;\n\n    flatMapComponents(matched, function (def, _, match, key) {\n      // if it's a function and doesn't have cid attached,\n      // assume it's an async component resolve function.\n      // we are not using Vue's default async resolving mechanism because\n      // we want to halt the navigation until the incoming component has been\n      // resolved.\n      if (typeof def === 'function' && def.cid === undefined) {\n        hasAsync = true;\n        pending++;\n\n        var resolve = once(function (resolvedDef) {\n          if (isESModule(resolvedDef)) {\n            resolvedDef = resolvedDef.default;\n          }\n          // save resolved on async factory in case it's used elsewhere\n          def.resolved = typeof resolvedDef === 'function'\n            ? resolvedDef\n            : _Vue.extend(resolvedDef);\n          match.components[key] = resolvedDef;\n          pending--;\n          if (pending <= 0) {\n            next();\n          }\n        });\n\n        var reject = once(function (reason) {\n          var msg = \"Failed to resolve async component \" + key + \": \" + reason;\n          process.env.NODE_ENV !== 'production' && warn(false, msg);\n          if (!error) {\n            error = isError(reason)\n              ? reason\n              : new Error(msg);\n            next(error);\n          }\n        });\n\n        var res;\n        try {\n          res = def(resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n        if (res) {\n          if (typeof res.then === 'function') {\n            res.then(resolve, reject);\n          } else {\n            // new syntax in Vue 2.3\n            var comp = res.component;\n            if (comp && typeof comp.then === 'function') {\n              comp.then(resolve, reject);\n            }\n          }\n        }\n      }\n    });\n\n    if (!hasAsync) { next(); }\n  }\n}\n\nfunction flatMapComponents (\n  matched,\n  fn\n) {\n  return flatten(matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) { return fn(\n      m.components[key],\n      m.instances[key],\n      m, key\n    ); })\n  }))\n}\n\nfunction flatten (arr) {\n  return Array.prototype.concat.apply([], arr)\n}\n\nvar hasSymbol =\n  typeof Symbol === 'function' &&\n  typeof Symbol.toStringTag === 'symbol';\n\nfunction isESModule (obj) {\n  return obj.__esModule || (hasSymbol && obj[Symbol.toStringTag] === 'Module')\n}\n\n// in Webpack 2, require.ensure now also returns a Promise\n// so the resolve/reject functions may get called an extra time\n// if the user uses an arrow function shorthand that happens to\n// return that Promise.\nfunction once (fn) {\n  var called = false;\n  return function () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    if (called) { return }\n    called = true;\n    return fn.apply(this, args)\n  }\n}\n\n/*  */\n\nvar History = function History (router, base) {\n  this.router = router;\n  this.base = normalizeBase(base);\n  // start with a route object that stands for \"nowhere\"\n  this.current = START;\n  this.pending = null;\n  this.ready = false;\n  this.readyCbs = [];\n  this.readyErrorCbs = [];\n  this.errorCbs = [];\n  this.listeners = [];\n};\n\nHistory.prototype.listen = function listen (cb) {\n  this.cb = cb;\n};\n\nHistory.prototype.onReady = function onReady (cb, errorCb) {\n  if (this.ready) {\n    cb();\n  } else {\n    this.readyCbs.push(cb);\n    if (errorCb) {\n      this.readyErrorCbs.push(errorCb);\n    }\n  }\n};\n\nHistory.prototype.onError = function onError (errorCb) {\n  this.errorCbs.push(errorCb);\n};\n\nHistory.prototype.transitionTo = function transitionTo (\n  location,\n  onComplete,\n  onAbort\n) {\n    var this$1$1 = this;\n\n  var route;\n  // catch redirect option https://github.com/vuejs/vue-router/issues/3201\n  try {\n    route = this.router.match(location, this.current);\n  } catch (e) {\n    this.errorCbs.forEach(function (cb) {\n      cb(e);\n    });\n    // Exception should still be thrown\n    throw e\n  }\n  var prev = this.current;\n  this.confirmTransition(\n    route,\n    function () {\n      this$1$1.updateRoute(route);\n      onComplete && onComplete(route);\n      this$1$1.ensureURL();\n      this$1$1.router.afterHooks.forEach(function (hook) {\n        hook && hook(route, prev);\n      });\n\n      // fire ready cbs once\n      if (!this$1$1.ready) {\n        this$1$1.ready = true;\n        this$1$1.readyCbs.forEach(function (cb) {\n          cb(route);\n        });\n      }\n    },\n    function (err) {\n      if (onAbort) {\n        onAbort(err);\n      }\n      if (err && !this$1$1.ready) {\n        // Initial redirection should not mark the history as ready yet\n        // because it's triggered by the redirection instead\n        // https://github.com/vuejs/vue-router/issues/3225\n        // https://github.com/vuejs/vue-router/issues/3331\n        if (!isNavigationFailure(err, NavigationFailureType.redirected) || prev !== START) {\n          this$1$1.ready = true;\n          this$1$1.readyErrorCbs.forEach(function (cb) {\n            cb(err);\n          });\n        }\n      }\n    }\n  );\n};\n\nHistory.prototype.confirmTransition = function confirmTransition (route, onComplete, onAbort) {\n    var this$1$1 = this;\n\n  var current = this.current;\n  this.pending = route;\n  var abort = function (err) {\n    // changed after adding errors with\n    // https://github.com/vuejs/vue-router/pull/3047 before that change,\n    // redirect and aborted navigation would produce an err == null\n    if (!isNavigationFailure(err) && isError(err)) {\n      if (this$1$1.errorCbs.length) {\n        this$1$1.errorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(false, 'uncaught error during route navigation:');\n        }\n        console.error(err);\n      }\n    }\n    onAbort && onAbort(err);\n  };\n  var lastRouteIndex = route.matched.length - 1;\n  var lastCurrentIndex = current.matched.length - 1;\n  if (\n    isSameRoute(route, current) &&\n    // in the case the route map has been dynamically appended to\n    lastRouteIndex === lastCurrentIndex &&\n    route.matched[lastRouteIndex] === current.matched[lastCurrentIndex]\n  ) {\n    this.ensureURL();\n    if (route.hash) {\n      handleScroll(this.router, current, route, false);\n    }\n    return abort(createNavigationDuplicatedError(current, route))\n  }\n\n  var ref = resolveQueue(\n    this.current.matched,\n    route.matched\n  );\n    var updated = ref.updated;\n    var deactivated = ref.deactivated;\n    var activated = ref.activated;\n\n  var queue = [].concat(\n    // in-component leave guards\n    extractLeaveGuards(deactivated),\n    // global before hooks\n    this.router.beforeHooks,\n    // in-component update hooks\n    extractUpdateHooks(updated),\n    // in-config enter guards\n    activated.map(function (m) { return m.beforeEnter; }),\n    // async components\n    resolveAsyncComponents(activated)\n  );\n\n  var iterator = function (hook, next) {\n    if (this$1$1.pending !== route) {\n      return abort(createNavigationCancelledError(current, route))\n    }\n    try {\n      hook(route, current, function (to) {\n        if (to === false) {\n          // next(false) -> abort navigation, ensure current URL\n          this$1$1.ensureURL(true);\n          abort(createNavigationAbortedError(current, route));\n        } else if (isError(to)) {\n          this$1$1.ensureURL(true);\n          abort(to);\n        } else if (\n          typeof to === 'string' ||\n          (typeof to === 'object' &&\n            (typeof to.path === 'string' || typeof to.name === 'string'))\n        ) {\n          // next('/') or next({ path: '/' }) -> redirect\n          abort(createNavigationRedirectedError(current, route));\n          if (typeof to === 'object' && to.replace) {\n            this$1$1.replace(to);\n          } else {\n            this$1$1.push(to);\n          }\n        } else {\n          // confirm transition and pass on the value\n          next(to);\n        }\n      });\n    } catch (e) {\n      abort(e);\n    }\n  };\n\n  runQueue(queue, iterator, function () {\n    // wait until async components are resolved before\n    // extracting in-component enter guards\n    var enterGuards = extractEnterGuards(activated);\n    var queue = enterGuards.concat(this$1$1.router.resolveHooks);\n    runQueue(queue, iterator, function () {\n      if (this$1$1.pending !== route) {\n        return abort(createNavigationCancelledError(current, route))\n      }\n      this$1$1.pending = null;\n      onComplete(route);\n      if (this$1$1.router.app) {\n        this$1$1.router.app.$nextTick(function () {\n          handleRouteEntered(route);\n        });\n      }\n    });\n  });\n};\n\nHistory.prototype.updateRoute = function updateRoute (route) {\n  this.current = route;\n  this.cb && this.cb(route);\n};\n\nHistory.prototype.setupListeners = function setupListeners () {\n  // Default implementation is empty\n};\n\nHistory.prototype.teardown = function teardown () {\n  // clean up event listeners\n  // https://github.com/vuejs/vue-router/issues/2341\n  this.listeners.forEach(function (cleanupListener) {\n    cleanupListener();\n  });\n  this.listeners = [];\n\n  // reset current history route\n  // https://github.com/vuejs/vue-router/issues/3294\n  this.current = START;\n  this.pending = null;\n};\n\nfunction normalizeBase (base) {\n  if (!base) {\n    if (inBrowser) {\n      // respect <base> tag\n      var baseEl = document.querySelector('base');\n      base = (baseEl && baseEl.getAttribute('href')) || '/';\n      // strip full URL origin\n      base = base.replace(/^https?:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  }\n  // make sure there's the starting slash\n  if (base.charAt(0) !== '/') {\n    base = '/' + base;\n  }\n  // remove trailing slash\n  return base.replace(/\\/$/, '')\n}\n\nfunction resolveQueue (\n  current,\n  next\n) {\n  var i;\n  var max = Math.max(current.length, next.length);\n  for (i = 0; i < max; i++) {\n    if (current[i] !== next[i]) {\n      break\n    }\n  }\n  return {\n    updated: next.slice(0, i),\n    activated: next.slice(i),\n    deactivated: current.slice(i)\n  }\n}\n\nfunction extractGuards (\n  records,\n  name,\n  bind,\n  reverse\n) {\n  var guards = flatMapComponents(records, function (def, instance, match, key) {\n    var guard = extractGuard(def, name);\n    if (guard) {\n      return Array.isArray(guard)\n        ? guard.map(function (guard) { return bind(guard, instance, match, key); })\n        : bind(guard, instance, match, key)\n    }\n  });\n  return flatten(reverse ? guards.reverse() : guards)\n}\n\nfunction extractGuard (\n  def,\n  key\n) {\n  if (typeof def !== 'function') {\n    // extend now so that global mixins are applied.\n    def = _Vue.extend(def);\n  }\n  return def.options[key]\n}\n\nfunction extractLeaveGuards (deactivated) {\n  return extractGuards(deactivated, 'beforeRouteLeave', bindGuard, true)\n}\n\nfunction extractUpdateHooks (updated) {\n  return extractGuards(updated, 'beforeRouteUpdate', bindGuard)\n}\n\nfunction bindGuard (guard, instance) {\n  if (instance) {\n    return function boundRouteGuard () {\n      return guard.apply(instance, arguments)\n    }\n  }\n}\n\nfunction extractEnterGuards (\n  activated\n) {\n  return extractGuards(\n    activated,\n    'beforeRouteEnter',\n    function (guard, _, match, key) {\n      return bindEnterGuard(guard, match, key)\n    }\n  )\n}\n\nfunction bindEnterGuard (\n  guard,\n  match,\n  key\n) {\n  return function routeEnterGuard (to, from, next) {\n    return guard(to, from, function (cb) {\n      if (typeof cb === 'function') {\n        if (!match.enteredCbs[key]) {\n          match.enteredCbs[key] = [];\n        }\n        match.enteredCbs[key].push(cb);\n      }\n      next(cb);\n    })\n  }\n}\n\n/*  */\n\nvar HTML5History = /*@__PURE__*/(function (History) {\n  function HTML5History (router, base) {\n    History.call(this, router, base);\n\n    this._startLocation = getLocation(this.base);\n  }\n\n  if ( History ) HTML5History.__proto__ = History;\n  HTML5History.prototype = Object.create( History && History.prototype );\n  HTML5History.prototype.constructor = HTML5History;\n\n  HTML5History.prototype.setupListeners = function setupListeners () {\n    var this$1$1 = this;\n\n    if (this.listeners.length > 0) {\n      return\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1$1.current;\n\n      // Avoiding first `popstate` event dispatched in some browsers but first\n      // history route not updated since async guard at the same time.\n      var location = getLocation(this$1$1.base);\n      if (this$1$1.current === START && location === this$1$1._startLocation) {\n        return\n      }\n\n      this$1$1.transitionTo(location, function (route) {\n        if (supportsScroll) {\n          handleScroll(router, route, current, true);\n        }\n      });\n    };\n    window.addEventListener('popstate', handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener('popstate', handleRoutingEvent);\n    });\n  };\n\n  HTML5History.prototype.go = function go (n) {\n    window.history.go(n);\n  };\n\n  HTML5History.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushState(cleanPath(this$1$1.base + route.fullPath));\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceState(cleanPath(this$1$1.base + route.fullPath));\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.ensureURL = function ensureURL (push) {\n    if (getLocation(this.base) !== this.current.fullPath) {\n      var current = cleanPath(this.base + this.current.fullPath);\n      push ? pushState(current) : replaceState(current);\n    }\n  };\n\n  HTML5History.prototype.getCurrentLocation = function getCurrentLocation () {\n    return getLocation(this.base)\n  };\n\n  return HTML5History;\n}(History));\n\nfunction getLocation (base) {\n  var path = window.location.pathname;\n  var pathLowerCase = path.toLowerCase();\n  var baseLowerCase = base.toLowerCase();\n  // base=\"/a\" shouldn't turn path=\"/app\" into \"/a/pp\"\n  // https://github.com/vuejs/vue-router/issues/3555\n  // so we ensure the trailing slash in the base\n  if (base && ((pathLowerCase === baseLowerCase) ||\n    (pathLowerCase.indexOf(cleanPath(baseLowerCase + '/')) === 0))) {\n    path = path.slice(base.length);\n  }\n  return (path || '/') + window.location.search + window.location.hash\n}\n\n/*  */\n\nvar HashHistory = /*@__PURE__*/(function (History) {\n  function HashHistory (router, base, fallback) {\n    History.call(this, router, base);\n    // check history fallback deeplinking\n    if (fallback && checkFallback(this.base)) {\n      return\n    }\n    ensureSlash();\n  }\n\n  if ( History ) HashHistory.__proto__ = History;\n  HashHistory.prototype = Object.create( History && History.prototype );\n  HashHistory.prototype.constructor = HashHistory;\n\n  // this is delayed until the app mounts\n  // to avoid the hashchange listener being fired too early\n  HashHistory.prototype.setupListeners = function setupListeners () {\n    var this$1$1 = this;\n\n    if (this.listeners.length > 0) {\n      return\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1$1.current;\n      if (!ensureSlash()) {\n        return\n      }\n      this$1$1.transitionTo(getHash(), function (route) {\n        if (supportsScroll) {\n          handleScroll(this$1$1.router, route, current, true);\n        }\n        if (!supportsPushState) {\n          replaceHash(route.fullPath);\n        }\n      });\n    };\n    var eventType = supportsPushState ? 'popstate' : 'hashchange';\n    window.addEventListener(\n      eventType,\n      handleRoutingEvent\n    );\n    this.listeners.push(function () {\n      window.removeEventListener(eventType, handleRoutingEvent);\n    });\n  };\n\n  HashHistory.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(\n      location,\n      function (route) {\n        pushHash(route.fullPath);\n        handleScroll(this$1$1.router, route, fromRoute, false);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  HashHistory.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(\n      location,\n      function (route) {\n        replaceHash(route.fullPath);\n        handleScroll(this$1$1.router, route, fromRoute, false);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  HashHistory.prototype.go = function go (n) {\n    window.history.go(n);\n  };\n\n  HashHistory.prototype.ensureURL = function ensureURL (push) {\n    var current = this.current.fullPath;\n    if (getHash() !== current) {\n      push ? pushHash(current) : replaceHash(current);\n    }\n  };\n\n  HashHistory.prototype.getCurrentLocation = function getCurrentLocation () {\n    return getHash()\n  };\n\n  return HashHistory;\n}(History));\n\nfunction checkFallback (base) {\n  var location = getLocation(base);\n  if (!/^\\/#/.test(location)) {\n    window.location.replace(cleanPath(base + '/#' + location));\n    return true\n  }\n}\n\nfunction ensureSlash () {\n  var path = getHash();\n  if (path.charAt(0) === '/') {\n    return true\n  }\n  replaceHash('/' + path);\n  return false\n}\n\nfunction getHash () {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var index = href.indexOf('#');\n  // empty path\n  if (index < 0) { return '' }\n\n  href = href.slice(index + 1);\n\n  return href\n}\n\nfunction getUrl (path) {\n  var href = window.location.href;\n  var i = href.indexOf('#');\n  var base = i >= 0 ? href.slice(0, i) : href;\n  return (base + \"#\" + path)\n}\n\nfunction pushHash (path) {\n  if (supportsPushState) {\n    pushState(getUrl(path));\n  } else {\n    window.location.hash = path;\n  }\n}\n\nfunction replaceHash (path) {\n  if (supportsPushState) {\n    replaceState(getUrl(path));\n  } else {\n    window.location.replace(getUrl(path));\n  }\n}\n\n/*  */\n\nvar AbstractHistory = /*@__PURE__*/(function (History) {\n  function AbstractHistory (router, base) {\n    History.call(this, router, base);\n    this.stack = [];\n    this.index = -1;\n  }\n\n  if ( History ) AbstractHistory.__proto__ = History;\n  AbstractHistory.prototype = Object.create( History && History.prototype );\n  AbstractHistory.prototype.constructor = AbstractHistory;\n\n  AbstractHistory.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    this.transitionTo(\n      location,\n      function (route) {\n        this$1$1.stack = this$1$1.stack.slice(0, this$1$1.index + 1).concat(route);\n        this$1$1.index++;\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  AbstractHistory.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    this.transitionTo(\n      location,\n      function (route) {\n        this$1$1.stack = this$1$1.stack.slice(0, this$1$1.index).concat(route);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  AbstractHistory.prototype.go = function go (n) {\n    var this$1$1 = this;\n\n    var targetIndex = this.index + n;\n    if (targetIndex < 0 || targetIndex >= this.stack.length) {\n      return\n    }\n    var route = this.stack[targetIndex];\n    this.confirmTransition(\n      route,\n      function () {\n        var prev = this$1$1.current;\n        this$1$1.index = targetIndex;\n        this$1$1.updateRoute(route);\n        this$1$1.router.afterHooks.forEach(function (hook) {\n          hook && hook(route, prev);\n        });\n      },\n      function (err) {\n        if (isNavigationFailure(err, NavigationFailureType.duplicated)) {\n          this$1$1.index = targetIndex;\n        }\n      }\n    );\n  };\n\n  AbstractHistory.prototype.getCurrentLocation = function getCurrentLocation () {\n    var current = this.stack[this.stack.length - 1];\n    return current ? current.fullPath : '/'\n  };\n\n  AbstractHistory.prototype.ensureURL = function ensureURL () {\n    // noop\n  };\n\n  return AbstractHistory;\n}(History));\n\n/*  */\n\n\n\nvar VueRouter = function VueRouter (options) {\n  if ( options === void 0 ) options = {};\n\n  if (process.env.NODE_ENV !== 'production') {\n    warn(this instanceof VueRouter, \"Router must be called with the new operator.\");\n  }\n  this.app = null;\n  this.apps = [];\n  this.options = options;\n  this.beforeHooks = [];\n  this.resolveHooks = [];\n  this.afterHooks = [];\n  this.matcher = createMatcher(options.routes || [], this);\n\n  var mode = options.mode || 'hash';\n  this.fallback =\n    mode === 'history' && !supportsPushState && options.fallback !== false;\n  if (this.fallback) {\n    mode = 'hash';\n  }\n  if (!inBrowser) {\n    mode = 'abstract';\n  }\n  this.mode = mode;\n\n  switch (mode) {\n    case 'history':\n      this.history = new HTML5History(this, options.base);\n      break\n    case 'hash':\n      this.history = new HashHistory(this, options.base, this.fallback);\n      break\n    case 'abstract':\n      this.history = new AbstractHistory(this, options.base);\n      break\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        assert(false, (\"invalid mode: \" + mode));\n      }\n  }\n};\n\nvar prototypeAccessors = { currentRoute: { configurable: true } };\n\nVueRouter.prototype.match = function match (raw, current, redirectedFrom) {\n  return this.matcher.match(raw, current, redirectedFrom)\n};\n\nprototypeAccessors.currentRoute.get = function () {\n  return this.history && this.history.current\n};\n\nVueRouter.prototype.init = function init (app /* Vue component instance */) {\n    var this$1$1 = this;\n\n  process.env.NODE_ENV !== 'production' &&\n    assert(\n      install.installed,\n      \"not installed. Make sure to call `Vue.use(VueRouter)` \" +\n        \"before creating root instance.\"\n    );\n\n  this.apps.push(app);\n\n  // set up app destroyed handler\n  // https://github.com/vuejs/vue-router/issues/2639\n  app.$once('hook:destroyed', function () {\n    // clean out app from this.apps array once destroyed\n    var index = this$1$1.apps.indexOf(app);\n    if (index > -1) { this$1$1.apps.splice(index, 1); }\n    // ensure we still have a main app or null if no apps\n    // we do not release the router so it can be reused\n    if (this$1$1.app === app) { this$1$1.app = this$1$1.apps[0] || null; }\n\n    if (!this$1$1.app) { this$1$1.history.teardown(); }\n  });\n\n  // main app previously initialized\n  // return as we don't need to set up new history listener\n  if (this.app) {\n    return\n  }\n\n  this.app = app;\n\n  var history = this.history;\n\n  if (history instanceof HTML5History || history instanceof HashHistory) {\n    var handleInitialScroll = function (routeOrError) {\n      var from = history.current;\n      var expectScroll = this$1$1.options.scrollBehavior;\n      var supportsScroll = supportsPushState && expectScroll;\n\n      if (supportsScroll && 'fullPath' in routeOrError) {\n        handleScroll(this$1$1, routeOrError, from, false);\n      }\n    };\n    var setupListeners = function (routeOrError) {\n      history.setupListeners();\n      handleInitialScroll(routeOrError);\n    };\n    history.transitionTo(\n      history.getCurrentLocation(),\n      setupListeners,\n      setupListeners\n    );\n  }\n\n  history.listen(function (route) {\n    this$1$1.apps.forEach(function (app) {\n      app._route = route;\n    });\n  });\n};\n\nVueRouter.prototype.beforeEach = function beforeEach (fn) {\n  return registerHook(this.beforeHooks, fn)\n};\n\nVueRouter.prototype.beforeResolve = function beforeResolve (fn) {\n  return registerHook(this.resolveHooks, fn)\n};\n\nVueRouter.prototype.afterEach = function afterEach (fn) {\n  return registerHook(this.afterHooks, fn)\n};\n\nVueRouter.prototype.onReady = function onReady (cb, errorCb) {\n  this.history.onReady(cb, errorCb);\n};\n\nVueRouter.prototype.onError = function onError (errorCb) {\n  this.history.onError(errorCb);\n};\n\nVueRouter.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1$1.history.push(location, resolve, reject);\n    })\n  } else {\n    this.history.push(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1$1.history.replace(location, resolve, reject);\n    })\n  } else {\n    this.history.replace(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.go = function go (n) {\n  this.history.go(n);\n};\n\nVueRouter.prototype.back = function back () {\n  this.go(-1);\n};\n\nVueRouter.prototype.forward = function forward () {\n  this.go(1);\n};\n\nVueRouter.prototype.getMatchedComponents = function getMatchedComponents (to) {\n  var route = to\n    ? to.matched\n      ? to\n      : this.resolve(to).route\n    : this.currentRoute;\n  if (!route) {\n    return []\n  }\n  return [].concat.apply(\n    [],\n    route.matched.map(function (m) {\n      return Object.keys(m.components).map(function (key) {\n        return m.components[key]\n      })\n    })\n  )\n};\n\nVueRouter.prototype.resolve = function resolve (\n  to,\n  current,\n  append\n) {\n  current = current || this.history.current;\n  var location = normalizeLocation(to, current, append, this);\n  var route = this.match(location, current);\n  var fullPath = route.redirectedFrom || route.fullPath;\n  var base = this.history.base;\n  var href = createHref(base, fullPath, this.mode);\n  return {\n    location: location,\n    route: route,\n    href: href,\n    // for backwards compat\n    normalizedTo: location,\n    resolved: route\n  }\n};\n\nVueRouter.prototype.getRoutes = function getRoutes () {\n  return this.matcher.getRoutes()\n};\n\nVueRouter.prototype.addRoute = function addRoute (parentOrRoute, route) {\n  this.matcher.addRoute(parentOrRoute, route);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nVueRouter.prototype.addRoutes = function addRoutes (routes) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn(false, 'router.addRoutes() is deprecated and has been removed in Vue Router 4. Use router.addRoute() instead.');\n  }\n  this.matcher.addRoutes(routes);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nObject.defineProperties( VueRouter.prototype, prototypeAccessors );\n\nvar VueRouter$1 = VueRouter;\n\nfunction registerHook (list, fn) {\n  list.push(fn);\n  return function () {\n    var i = list.indexOf(fn);\n    if (i > -1) { list.splice(i, 1); }\n  }\n}\n\nfunction createHref (base, fullPath, mode) {\n  var path = mode === 'hash' ? '#' + fullPath : fullPath;\n  return base ? cleanPath(base + '/' + path) : path\n}\n\n// We cannot remove this as it would be a breaking change\nVueRouter.install = install;\nVueRouter.version = '3.6.5';\nVueRouter.isNavigationFailure = isNavigationFailure;\nVueRouter.NavigationFailureType = NavigationFailureType;\nVueRouter.START_LOCATION = START;\n\nif (inBrowser && window.Vue) {\n  window.Vue.use(VueRouter);\n}\n\nvar version = '3.6.5';\n\nexport { NavigationFailureType, Link as RouterLink, View as RouterView, START as START_LOCATION, VueRouter$1 as default, isNavigationFailure, version };\n"], "mappings": ";;;AAOA,SAAS,OAAQ,WAAW,SAAS;AACnC,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAO,kBAAkB,OAAQ;AAAA,EAC7C;AACF;AAEA,SAAS,KAAM,WAAW,SAAS;AACjC,MAAI,CAAC,WAAW;AACd,WAAO,YAAY,eAAe,QAAQ,KAAM,kBAAkB,OAAQ;AAAA,EAC5E;AACF;AAEA,SAAS,OAAQ,GAAG,GAAG;AACrB,WAAS,OAAO,GAAG;AACjB,MAAE,GAAG,IAAI,EAAE,GAAG;AAAA,EAChB;AACA,SAAO;AACT;AAIA,IAAI,kBAAkB;AACtB,IAAI,wBAAwB,SAAU,GAAG;AAAE,SAAO,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AAAG;AACtF,IAAI,UAAU;AAKd,IAAI,SAAS,SAAU,KAAK;AAAE,SAAO,mBAAmB,GAAG,EACtD,QAAQ,iBAAiB,qBAAqB,EAC9C,QAAQ,SAAS,GAAG;AAAG;AAE5B,SAAS,OAAQ,KAAK;AACpB,MAAI;AACF,WAAO,mBAAmB,GAAG;AAAA,EAC/B,SAAS,KAAK;AACZ,QAAI,MAAuC;AACzC,WAAK,OAAQ,qBAAsB,MAAM,uBAAyB;AAAA,IACpE;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aACP,OACA,YACA,aACA;AACA,MAAK,eAAe,OAAS,cAAa,CAAC;AAE3C,MAAIA,SAAQ,eAAe;AAC3B,MAAI;AACJ,MAAI;AACF,kBAAcA,OAAM,SAAS,EAAE;AAAA,EACjC,SAAS,GAAG;AACV,IAAyC,KAAK,OAAO,EAAE,OAAO;AAC9D,kBAAc,CAAC;AAAA,EACjB;AACA,WAAS,OAAO,YAAY;AAC1B,QAAI,QAAQ,WAAW,GAAG;AAC1B,gBAAY,GAAG,IAAI,MAAM,QAAQ,KAAK,IAClC,MAAM,IAAI,mBAAmB,IAC7B,oBAAoB,KAAK;AAAA,EAC/B;AACA,SAAO;AACT;AAEA,IAAI,sBAAsB,SAAU,OAAO;AAAE,SAAQ,SAAS,QAAQ,OAAO,UAAU,WAAW,QAAQ,OAAO,KAAK;AAAI;AAE1H,SAAS,WAAY,OAAO;AAC1B,MAAI,MAAM,CAAC;AAEX,UAAQ,MAAM,KAAK,EAAE,QAAQ,aAAa,EAAE;AAE5C,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,QAAM,MAAM,GAAG,EAAE,QAAQ,SAAU,OAAO;AACxC,QAAI,QAAQ,MAAM,QAAQ,OAAO,GAAG,EAAE,MAAM,GAAG;AAC/C,QAAI,MAAM,OAAO,MAAM,MAAM,CAAC;AAC9B,QAAI,MAAM,MAAM,SAAS,IAAI,OAAO,MAAM,KAAK,GAAG,CAAC,IAAI;AAEvD,QAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,UAAI,GAAG,IAAI;AAAA,IACb,WAAW,MAAM,QAAQ,IAAI,GAAG,CAAC,GAAG;AAClC,UAAI,GAAG,EAAE,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,UAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG;AAAA,IAC3B;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,eAAgB,KAAK;AAC5B,MAAI,MAAM,MACN,OAAO,KAAK,GAAG,EACd,IAAI,SAAU,KAAK;AAClB,QAAI,MAAM,IAAI,GAAG;AAEjB,QAAI,QAAQ,QAAW;AACrB,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,MAAM;AAChB,aAAO,OAAO,GAAG;AAAA,IACnB;AAEA,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,UAAI,SAAS,CAAC;AACd,UAAI,QAAQ,SAAU,MAAM;AAC1B,YAAI,SAAS,QAAW;AACtB;AAAA,QACF;AACA,YAAI,SAAS,MAAM;AACjB,iBAAO,KAAK,OAAO,GAAG,CAAC;AAAA,QACzB,OAAO;AACL,iBAAO,KAAK,OAAO,GAAG,IAAI,MAAM,OAAO,IAAI,CAAC;AAAA,QAC9C;AAAA,MACF,CAAC;AACD,aAAO,OAAO,KAAK,GAAG;AAAA,IACxB;AAEA,WAAO,OAAO,GAAG,IAAI,MAAM,OAAO,GAAG;AAAA,EACvC,CAAC,EACA,OAAO,SAAU,GAAG;AAAE,WAAO,EAAE,SAAS;AAAA,EAAG,CAAC,EAC5C,KAAK,GAAG,IACT;AACJ,SAAO,MAAO,MAAM,MAAO;AAC7B;AAIA,IAAI,kBAAkB;AAEtB,SAAS,YACP,QACA,UACA,gBACA,QACA;AACA,MAAIC,kBAAiB,UAAU,OAAO,QAAQ;AAE9C,MAAI,QAAQ,SAAS,SAAS,CAAC;AAC/B,MAAI;AACF,YAAQ,MAAM,KAAK;AAAA,EACrB,SAAS,GAAG;AAAA,EAAC;AAEb,MAAI,QAAQ;AAAA,IACV,MAAM,SAAS,QAAS,UAAU,OAAO;AAAA,IACzC,MAAO,UAAU,OAAO,QAAS,CAAC;AAAA,IAClC,MAAM,SAAS,QAAQ;AAAA,IACvB,MAAM,SAAS,QAAQ;AAAA,IACvB;AAAA,IACA,QAAQ,SAAS,UAAU,CAAC;AAAA,IAC5B,UAAU,YAAY,UAAUA,eAAc;AAAA,IAC9C,SAAS,SAAS,YAAY,MAAM,IAAI,CAAC;AAAA,EAC3C;AACA,MAAI,gBAAgB;AAClB,UAAM,iBAAiB,YAAY,gBAAgBA,eAAc;AAAA,EACnE;AACA,SAAO,OAAO,OAAO,KAAK;AAC5B;AAEA,SAAS,MAAO,OAAO;AACrB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,KAAK;AAAA,EACxB,WAAW,SAAS,OAAO,UAAU,UAAU;AAC7C,QAAI,MAAM,CAAC;AACX,aAAS,OAAO,OAAO;AACrB,UAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAGA,IAAI,QAAQ,YAAY,MAAM;AAAA,EAC5B,MAAM;AACR,CAAC;AAED,SAAS,YAAa,QAAQ;AAC5B,MAAI,MAAM,CAAC;AACX,SAAO,QAAQ;AACb,QAAI,QAAQ,MAAM;AAClB,aAAS,OAAO;AAAA,EAClB;AACA,SAAO;AACT;AAEA,SAAS,YACP,KACA,iBACA;AACA,MAAI,OAAO,IAAI;AACf,MAAI,QAAQ,IAAI;AAAO,MAAK,UAAU,OAAS,SAAQ,CAAC;AACxD,MAAI,OAAO,IAAI;AAAM,MAAK,SAAS,OAAS,QAAO;AAEnD,MAAI,YAAY,mBAAmB;AACnC,UAAQ,QAAQ,OAAO,UAAU,KAAK,IAAI;AAC5C;AAEA,SAAS,YAAa,GAAG,GAAG,UAAU;AACpC,MAAI,MAAM,OAAO;AACf,WAAO,MAAM;AAAA,EACf,WAAW,CAAC,GAAG;AACb,WAAO;AAAA,EACT,WAAW,EAAE,QAAQ,EAAE,MAAM;AAC3B,WAAO,EAAE,KAAK,QAAQ,iBAAiB,EAAE,MAAM,EAAE,KAAK,QAAQ,iBAAiB,EAAE,MAAM,YACrF,EAAE,SAAS,EAAE,QACb,cAAc,EAAE,OAAO,EAAE,KAAK;AAAA,EAClC,WAAW,EAAE,QAAQ,EAAE,MAAM;AAC3B,WACE,EAAE,SAAS,EAAE,SACZ,YACC,EAAE,SAAS,EAAE,QACf,cAAc,EAAE,OAAO,EAAE,KAAK,KAC9B,cAAc,EAAE,QAAQ,EAAE,MAAM;AAAA,EAGpC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAe,GAAG,GAAG;AAC5B,MAAK,MAAM,OAAS,KAAI,CAAC;AACzB,MAAK,MAAM,OAAS,KAAI,CAAC;AAGzB,MAAI,CAAC,KAAK,CAAC,GAAG;AAAE,WAAO,MAAM;AAAA,EAAE;AAC/B,MAAI,QAAQ,OAAO,KAAK,CAAC,EAAE,KAAK;AAChC,MAAI,QAAQ,OAAO,KAAK,CAAC,EAAE,KAAK;AAChC,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;AAAA,EACT;AACA,SAAO,MAAM,MAAM,SAAU,KAAK,GAAG;AACnC,QAAI,OAAO,EAAE,GAAG;AAChB,QAAI,OAAO,MAAM,CAAC;AAClB,QAAI,SAAS,KAAK;AAAE,aAAO;AAAA,IAAM;AACjC,QAAI,OAAO,EAAE,GAAG;AAEhB,QAAI,QAAQ,QAAQ,QAAQ,MAAM;AAAE,aAAO,SAAS;AAAA,IAAK;AAEzD,QAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,aAAO,cAAc,MAAM,IAAI;AAAA,IACjC;AACA,WAAO,OAAO,IAAI,MAAM,OAAO,IAAI;AAAA,EACrC,CAAC;AACH;AAEA,SAAS,gBAAiB,SAAS,QAAQ;AACzC,SACE,QAAQ,KAAK,QAAQ,iBAAiB,GAAG,EAAE;AAAA,IACzC,OAAO,KAAK,QAAQ,iBAAiB,GAAG;AAAA,EAC1C,MAAM,MACL,CAAC,OAAO,QAAQ,QAAQ,SAAS,OAAO,SACzC,cAAc,QAAQ,OAAO,OAAO,KAAK;AAE7C;AAEA,SAAS,cAAe,SAAS,QAAQ;AACvC,WAAS,OAAO,QAAQ;AACtB,QAAI,EAAE,OAAO,UAAU;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,mBAAoB,OAAO;AAClC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,KAAK;AAC7C,QAAI,SAAS,MAAM,QAAQ,CAAC;AAC5B,aAAS,QAAQ,OAAO,WAAW;AACjC,UAAI,WAAW,OAAO,UAAU,IAAI;AACpC,UAAI,MAAM,OAAO,WAAW,IAAI;AAChC,UAAI,CAAC,YAAY,CAAC,KAAK;AAAE;AAAA,MAAS;AAClC,aAAO,OAAO,WAAW,IAAI;AAC7B,eAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,OAAO;AACzC,YAAI,CAAC,SAAS,mBAAmB;AAAE,cAAI,GAAG,EAAE,QAAQ;AAAA,QAAG;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,OAAQ,GAAG,KAAK;AAC/B,QAAI,QAAQ,IAAI;AAChB,QAAI,WAAW,IAAI;AACnB,QAAI,SAAS,IAAI;AACjB,QAAI,OAAO,IAAI;AAGf,SAAK,aAAa;AAIlB,QAAI,IAAI,OAAO;AACf,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,OAAO;AACnB,QAAI,QAAQ,OAAO,qBAAqB,OAAO,mBAAmB,CAAC;AAInE,QAAI,QAAQ;AACZ,QAAI,WAAW;AACf,WAAO,UAAU,OAAO,gBAAgB,QAAQ;AAC9C,UAAI,YAAY,OAAO,SAAS,OAAO,OAAO,OAAO,CAAC;AACtD,UAAI,UAAU,YAAY;AACxB;AAAA,MACF;AACA,UAAI,UAAU,aAAa,OAAO,mBAAmB,OAAO,WAAW;AACrE,mBAAW;AAAA,MACb;AACA,eAAS,OAAO;AAAA,IAClB;AACA,SAAK,kBAAkB;AAGvB,QAAI,UAAU;AACZ,UAAI,aAAa,MAAM,IAAI;AAC3B,UAAI,kBAAkB,cAAc,WAAW;AAC/C,UAAI,iBAAiB;AAGnB,YAAI,WAAW,aAAa;AAC1B,0BAAgB,iBAAiB,MAAM,WAAW,OAAO,WAAW,WAAW;AAAA,QACjF;AACA,eAAO,EAAE,iBAAiB,MAAM,QAAQ;AAAA,MAC1C,OAAO;AAEL,eAAO,EAAE;AAAA,MACX;AAAA,IACF;AAEA,QAAI,UAAU,MAAM,QAAQ,KAAK;AACjC,QAAI,YAAY,WAAW,QAAQ,WAAW,IAAI;AAGlD,QAAI,CAAC,WAAW,CAAC,WAAW;AAC1B,YAAM,IAAI,IAAI;AACd,aAAO,EAAE;AAAA,IACX;AAGA,UAAM,IAAI,IAAI,EAAE,UAAqB;AAIrC,SAAK,wBAAwB,SAAU,IAAI,KAAK;AAE9C,UAAI,UAAU,QAAQ,UAAU,IAAI;AACpC,UACG,OAAO,YAAY,MACnB,CAAC,OAAO,YAAY,IACrB;AACA,gBAAQ,UAAU,IAAI,IAAI;AAAA,MAC5B;AAAA,IACF;AAIC,KAAC,KAAK,SAAS,KAAK,OAAO,CAAC,IAAI,WAAW,SAAUC,IAAG,OAAO;AAC9D,cAAQ,UAAU,IAAI,IAAI,MAAM;AAAA,IAClC;AAIA,SAAK,KAAK,OAAO,SAAU,OAAO;AAChC,UAAI,MAAM,KAAK,aACb,MAAM,qBACN,MAAM,sBAAsB,QAAQ,UAAU,IAAI,GAClD;AACA,gBAAQ,UAAU,IAAI,IAAI,MAAM;AAAA,MAClC;AAKA,yBAAmB,KAAK;AAAA,IAC1B;AAEA,QAAI,cAAc,QAAQ,SAAS,QAAQ,MAAM,IAAI;AAErD,QAAI,aAAa;AACf,aAAO,MAAM,IAAI,GAAG;AAAA,QAClB;AAAA,QACA;AAAA,MACF,CAAC;AACD,sBAAgB,WAAW,MAAM,OAAO,WAAW;AAAA,IACrD;AAEA,WAAO,EAAE,WAAW,MAAM,QAAQ;AAAA,EACpC;AACF;AAEA,SAAS,gBAAiB,WAAW,MAAM,OAAO,aAAa;AAE7D,MAAI,cAAc,KAAK,QAAQ,aAAa,OAAO,WAAW;AAC9D,MAAI,aAAa;AAEf,kBAAc,KAAK,QAAQ,OAAO,CAAC,GAAG,WAAW;AAEjD,QAAI,QAAQ,KAAK,QAAQ,KAAK,SAAS,CAAC;AACxC,aAAS,OAAO,aAAa;AAC3B,UAAI,CAAC,UAAU,SAAS,EAAE,OAAO,UAAU,QAAQ;AACjD,cAAM,GAAG,IAAI,YAAY,GAAG;AAC5B,eAAO,YAAY,GAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,aAAc,OAAO,QAAQ;AACpC,UAAQ,OAAO,QAAQ;AAAA,IACrB,KAAK;AACH;AAAA,IACF,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,OAAO,KAAK;AAAA,IACrB,KAAK;AACH,aAAO,SAAS,MAAM,SAAS;AAAA,IACjC;AACE,UAAI,MAAuC;AACzC;AAAA,UACE;AAAA,UACA,eAAiB,MAAM,OAAQ,YAAc,OAAO,SAAU;AAAA,QAEhE;AAAA,MACF;AAAA,EACJ;AACF;AAIA,SAAS,YACP,UACA,MACA,QACA;AACA,MAAI,YAAY,SAAS,OAAO,CAAC;AACjC,MAAI,cAAc,KAAK;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,OAAO,cAAc,KAAK;AAC1C,WAAO,OAAO;AAAA,EAChB;AAEA,MAAI,QAAQ,KAAK,MAAM,GAAG;AAK1B,MAAI,CAAC,UAAU,CAAC,MAAM,MAAM,SAAS,CAAC,GAAG;AACvC,UAAM,IAAI;AAAA,EACZ;AAGA,MAAI,WAAW,SAAS,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AACpD,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,UAAU,SAAS,CAAC;AACxB,QAAI,YAAY,MAAM;AACpB,YAAM,IAAI;AAAA,IACZ,WAAW,YAAY,KAAK;AAC1B,YAAM,KAAK,OAAO;AAAA,IACpB;AAAA,EACF;AAGA,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,QAAQ,EAAE;AAAA,EAClB;AAEA,SAAO,MAAM,KAAK,GAAG;AACvB;AAEA,SAAS,UAAW,MAAM;AACxB,MAAI,OAAO;AACX,MAAI,QAAQ;AAEZ,MAAI,YAAY,KAAK,QAAQ,GAAG;AAChC,MAAI,aAAa,GAAG;AAClB,WAAO,KAAK,MAAM,SAAS;AAC3B,WAAO,KAAK,MAAM,GAAG,SAAS;AAAA,EAChC;AAEA,MAAI,aAAa,KAAK,QAAQ,GAAG;AACjC,MAAI,cAAc,GAAG;AACnB,YAAQ,KAAK,MAAM,aAAa,CAAC;AACjC,WAAO,KAAK,MAAM,GAAG,UAAU;AAAA,EACjC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAW,MAAM;AACxB,SAAO,KAAK,QAAQ,iBAAiB,GAAG;AAC1C;AAEA,IAAI,UAAU,MAAM,WAAW,SAAU,KAAK;AAC5C,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,KAAK;AAChD;AAKA,IAAI,iBAAiB;AACrB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AAOvB,IAAI,cAAc,IAAI,OAAO;AAAA;AAAA;AAAA,EAG3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AACF,EAAE,KAAK,GAAG,GAAG,GAAG;AAShB,SAAS,MAAO,KAAK,SAAS;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,mBAAmB,WAAW,QAAQ,aAAa;AACvD,MAAI;AAEJ,UAAQ,MAAM,YAAY,KAAK,GAAG,MAAM,MAAM;AAC5C,QAAI,IAAI,IAAI,CAAC;AACb,QAAI,UAAU,IAAI,CAAC;AACnB,QAAI,SAAS,IAAI;AACjB,YAAQ,IAAI,MAAM,OAAO,MAAM;AAC/B,YAAQ,SAAS,EAAE;AAGnB,QAAI,SAAS;AACX,cAAQ,QAAQ,CAAC;AACjB;AAAA,IACF;AAEA,QAAI,OAAO,IAAI,KAAK;AACpB,QAAI,SAAS,IAAI,CAAC;AAClB,QAAI,OAAO,IAAI,CAAC;AAChB,QAAI,UAAU,IAAI,CAAC;AACnB,QAAI,QAAQ,IAAI,CAAC;AACjB,QAAI,WAAW,IAAI,CAAC;AACpB,QAAI,WAAW,IAAI,CAAC;AAGpB,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,UAAU,QAAQ,QAAQ,QAAQ,SAAS;AACzD,QAAI,SAAS,aAAa,OAAO,aAAa;AAC9C,QAAI,WAAW,aAAa,OAAO,aAAa;AAChD,QAAI,YAAY,IAAI,CAAC,KAAK;AAC1B,QAAI,UAAU,WAAW;AAEzB,WAAO,KAAK;AAAA,MACV,MAAM,QAAQ;AAAA,MACd,QAAQ,UAAU;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,CAAC,CAAC;AAAA,MACZ,SAAS,UAAU,YAAY,OAAO,IAAK,WAAW,OAAO,OAAO,aAAa,SAAS,IAAI;AAAA,IAChG,CAAC;AAAA,EACH;AAGA,MAAI,QAAQ,IAAI,QAAQ;AACtB,YAAQ,IAAI,OAAO,KAAK;AAAA,EAC1B;AAGA,MAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAEA,SAAO;AACT;AASA,SAAS,QAAS,KAAK,SAAS;AAC9B,SAAO,iBAAiB,MAAM,KAAK,OAAO,GAAG,OAAO;AACtD;AAQA,SAAS,yBAA0B,KAAK;AACtC,SAAO,UAAU,GAAG,EAAE,QAAQ,WAAW,SAAU,GAAG;AACpD,WAAO,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EACxD,CAAC;AACH;AAQA,SAAS,eAAgB,KAAK;AAC5B,SAAO,UAAU,GAAG,EAAE,QAAQ,SAAS,SAAU,GAAG;AAClD,WAAO,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EACxD,CAAC;AACH;AAKA,SAAS,iBAAkB,QAAQ,SAAS;AAE1C,MAAI,UAAU,IAAI,MAAM,OAAO,MAAM;AAGrC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AACjC,cAAQ,CAAC,IAAI,IAAI,OAAO,SAAS,OAAO,CAAC,EAAE,UAAU,MAAM,MAAM,OAAO,CAAC;AAAA,IAC3E;AAAA,EACF;AAEA,SAAO,SAAU,KAAK,MAAM;AAC1B,QAAI,OAAO;AACX,QAAI,OAAO,OAAO,CAAC;AACnB,QAAIC,WAAU,QAAQ,CAAC;AACvB,QAAIC,UAASD,SAAQ,SAAS,2BAA2B;AAEzD,aAASE,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,UAAI,QAAQ,OAAOA,EAAC;AAEpB,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ;AAER;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,MAAM,IAAI;AAC3B,UAAI;AAEJ,UAAI,SAAS,MAAM;AACjB,YAAI,MAAM,UAAU;AAElB,cAAI,MAAM,SAAS;AACjB,oBAAQ,MAAM;AAAA,UAChB;AAEA;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,UAAU,eAAe,MAAM,OAAO,iBAAiB;AAAA,QACnE;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,GAAG;AAClB,YAAI,CAAC,MAAM,QAAQ;AACjB,gBAAM,IAAI,UAAU,eAAe,MAAM,OAAO,oCAAoC,KAAK,UAAU,KAAK,IAAI,GAAG;AAAA,QACjH;AAEA,YAAI,MAAM,WAAW,GAAG;AACtB,cAAI,MAAM,UAAU;AAClB;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,UAAU,eAAe,MAAM,OAAO,mBAAmB;AAAA,UACrE;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAUD,QAAO,MAAM,CAAC,CAAC;AAEzB,cAAI,CAAC,QAAQC,EAAC,EAAE,KAAK,OAAO,GAAG;AAC7B,kBAAM,IAAI,UAAU,mBAAmB,MAAM,OAAO,iBAAiB,MAAM,UAAU,sBAAsB,KAAK,UAAU,OAAO,IAAI,GAAG;AAAA,UAC1I;AAEA,mBAAS,MAAM,IAAI,MAAM,SAAS,MAAM,aAAa;AAAA,QACvD;AAEA;AAAA,MACF;AAEA,gBAAU,MAAM,WAAW,eAAe,KAAK,IAAID,QAAO,KAAK;AAE/D,UAAI,CAAC,QAAQC,EAAC,EAAE,KAAK,OAAO,GAAG;AAC7B,cAAM,IAAI,UAAU,eAAe,MAAM,OAAO,iBAAiB,MAAM,UAAU,sBAAsB,UAAU,GAAG;AAAA,MACtH;AAEA,cAAQ,MAAM,SAAS;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,aAAc,KAAK;AAC1B,SAAO,IAAI,QAAQ,8BAA8B,MAAM;AACzD;AAQA,SAAS,YAAa,OAAO;AAC3B,SAAO,MAAM,QAAQ,iBAAiB,MAAM;AAC9C;AASA,SAAS,WAAY,IAAI,MAAM;AAC7B,KAAG,OAAO;AACV,SAAO;AACT;AAQA,SAAS,MAAO,SAAS;AACvB,SAAO,WAAW,QAAQ,YAAY,KAAK;AAC7C;AASA,SAAS,eAAgB,MAAM,MAAM;AAEnC,MAAI,SAAS,KAAK,OAAO,MAAM,WAAW;AAE1C,MAAI,QAAQ;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,KAAK;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,WAAW,MAAM,IAAI;AAC9B;AAUA,SAAS,cAAe,MAAM,MAAM,SAAS;AAC3C,MAAI,QAAQ,CAAC;AAEb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,KAAK,aAAa,KAAK,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM;AAAA,EACxD;AAEA,MAAI,SAAS,IAAI,OAAO,QAAQ,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM,OAAO,CAAC;AAErE,SAAO,WAAW,QAAQ,IAAI;AAChC;AAUA,SAAS,eAAgB,MAAM,MAAM,SAAS;AAC5C,SAAO,eAAe,MAAM,MAAM,OAAO,GAAG,MAAM,OAAO;AAC3D;AAUA,SAAS,eAAgB,QAAQ,MAAM,SAAS;AAC9C,MAAI,CAAC,QAAQ,IAAI,GAAG;AAClB;AAAA,IAAkC,QAAQ;AAC1C,WAAO,CAAC;AAAA,EACV;AAEA,YAAU,WAAW,CAAC;AAEtB,MAAI,SAAS,QAAQ;AACrB,MAAI,MAAM,QAAQ,QAAQ;AAC1B,MAAI,QAAQ;AAGZ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,QAAQ,OAAO,CAAC;AAEpB,QAAI,OAAO,UAAU,UAAU;AAC7B,eAAS,aAAa,KAAK;AAAA,IAC7B,OAAO;AACL,UAAI,SAAS,aAAa,MAAM,MAAM;AACtC,UAAI,UAAU,QAAQ,MAAM,UAAU;AAEtC,WAAK,KAAK,KAAK;AAEf,UAAI,MAAM,QAAQ;AAChB,mBAAW,QAAQ,SAAS,UAAU;AAAA,MACxC;AAEA,UAAI,MAAM,UAAU;AAClB,YAAI,CAAC,MAAM,SAAS;AAClB,oBAAU,QAAQ,SAAS,MAAM,UAAU;AAAA,QAC7C,OAAO;AACL,oBAAU,SAAS,MAAM,UAAU;AAAA,QACrC;AAAA,MACF,OAAO;AACL,kBAAU,SAAS,MAAM,UAAU;AAAA,MACrC;AAEA,eAAS;AAAA,IACX;AAAA,EACF;AAEA,MAAI,YAAY,aAAa,QAAQ,aAAa,GAAG;AACrD,MAAI,oBAAoB,MAAM,MAAM,CAAC,UAAU,MAAM,MAAM;AAM3D,MAAI,CAAC,QAAQ;AACX,aAAS,oBAAoB,MAAM,MAAM,GAAG,CAAC,UAAU,MAAM,IAAI,SAAS,QAAQ,YAAY;AAAA,EAChG;AAEA,MAAI,KAAK;AACP,aAAS;AAAA,EACX,OAAO;AAGL,aAAS,UAAU,oBAAoB,KAAK,QAAQ,YAAY;AAAA,EAClE;AAEA,SAAO,WAAW,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,CAAC,GAAG,IAAI;AACjE;AAcA,SAAS,aAAc,MAAM,MAAM,SAAS;AAC1C,MAAI,CAAC,QAAQ,IAAI,GAAG;AAClB;AAAA,IAAkC,QAAQ;AAC1C,WAAO,CAAC;AAAA,EACV;AAEA,YAAU,WAAW,CAAC;AAEtB,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;AAAA,MAAe;AAAA;AAAA,MAA6B;AAAA,IAAK;AAAA,EAC1D;AAEA,MAAI,QAAQ,IAAI,GAAG;AACjB,WAAO;AAAA;AAAA,MAAqC;AAAA;AAAA,MAA8B;AAAA,MAAO;AAAA,IAAO;AAAA,EAC1F;AAEA,SAAO;AAAA;AAAA,IAAsC;AAAA;AAAA,IAA8B;AAAA,IAAO;AAAA,EAAO;AAC3F;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,mBAAmB;AAClC,eAAe,iBAAiB;AAKhC,IAAI,qBAAqB,uBAAO,OAAO,IAAI;AAE3C,SAAS,WACP,MACA,QACA,UACA;AACA,WAAS,UAAU,CAAC;AACpB,MAAI;AACF,QAAI,SACF,mBAAmB,IAAI,MACtB,mBAAmB,IAAI,IAAI,eAAe,QAAQ,IAAI;AAIzD,QAAI,OAAO,OAAO,cAAc,UAAU;AAAE,aAAO,CAAC,IAAI,OAAO;AAAA,IAAW;AAE1E,WAAO,OAAO,QAAQ,EAAE,QAAQ,KAAK,CAAC;AAAA,EACxC,SAAS,GAAG;AACV,QAAI,MAAuC;AAEzC,WAAK,OAAO,OAAO,cAAc,UAAW,uBAAuB,WAAW,OAAQ,EAAE,OAAS;AAAA,IACnG;AACA,WAAO;AAAA,EACT,UAAE;AAEA,WAAO,OAAO,CAAC;AAAA,EACjB;AACF;AAIA,SAAS,kBACP,KACA,SACA,QACA,QACA;AACA,MAAI,OAAO,OAAO,QAAQ,WAAW,EAAE,MAAM,IAAI,IAAI;AAErD,MAAI,KAAK,aAAa;AACpB,WAAO;AAAA,EACT,WAAW,KAAK,MAAM;AACpB,WAAO,OAAO,CAAC,GAAG,GAAG;AACrB,QAAI,SAAS,KAAK;AAClB,QAAI,UAAU,OAAO,WAAW,UAAU;AACxC,WAAK,SAAS,OAAO,CAAC,GAAG,MAAM;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,KAAK,QAAQ,KAAK,UAAU,SAAS;AACxC,WAAO,OAAO,CAAC,GAAG,IAAI;AACtB,SAAK,cAAc;AACnB,QAAI,WAAW,OAAO,OAAO,CAAC,GAAG,QAAQ,MAAM,GAAG,KAAK,MAAM;AAC7D,QAAI,QAAQ,MAAM;AAChB,WAAK,OAAO,QAAQ;AACpB,WAAK,SAAS;AAAA,IAChB,WAAW,QAAQ,QAAQ,QAAQ;AACjC,UAAI,UAAU,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,CAAC,EAAE;AAC1D,WAAK,OAAO,WAAW,SAAS,UAAW,UAAW,QAAQ,IAAM;AAAA,IACtE,WAAW,MAAuC;AAChD,WAAK,OAAO,sDAAsD;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,UAAU,KAAK,QAAQ,EAAE;AAC1C,MAAI,WAAY,WAAW,QAAQ,QAAS;AAC5C,MAAI,OAAO,WAAW,OAClB,YAAY,WAAW,MAAM,UAAU,UAAU,KAAK,MAAM,IAC5D;AAEJ,MAAI,QAAQ;AAAA,IACV,WAAW;AAAA,IACX,KAAK;AAAA,IACL,UAAU,OAAO,QAAQ;AAAA,EAC3B;AAEA,MAAI,OAAO,KAAK,QAAQ,WAAW;AACnC,MAAI,QAAQ,KAAK,OAAO,CAAC,MAAM,KAAK;AAClC,WAAO,MAAM;AAAA,EACf;AAEA,SAAO;AAAA,IACL,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,IAAI,UAAU,CAAC,QAAQ,MAAM;AAC7B,IAAI,aAAa,CAAC,QAAQ,KAAK;AAE/B,IAAI,OAAO,WAAY;AAAC;AAExB,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,IACL,IAAI;AAAA,MACF,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,QAAQ,SAASC,QAAQ,GAAG;AAC1B,QAAI,WAAW;AAEf,QAAI,SAAS,KAAK;AAClB,QAAI,UAAU,KAAK;AACnB,QAAI,MAAM,OAAO;AAAA,MACf,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACP;AACA,QAAI,WAAW,IAAI;AACnB,QAAI,QAAQ,IAAI;AAChB,QAAI,OAAO,IAAI;AAEf,QAAI,UAAU,CAAC;AACf,QAAI,oBAAoB,OAAO,QAAQ;AACvC,QAAI,yBAAyB,OAAO,QAAQ;AAE5C,QAAI,sBACF,qBAAqB,OAAO,uBAAuB;AACrD,QAAI,2BACF,0BAA0B,OACtB,6BACA;AACN,QAAI,cACF,KAAK,eAAe,OAAO,sBAAsB,KAAK;AACxD,QAAI,mBACF,KAAK,oBAAoB,OACrB,2BACA,KAAK;AAEX,QAAI,gBAAgB,MAAM,iBACtB,YAAY,MAAM,kBAAkB,MAAM,cAAc,GAAG,MAAM,MAAM,IACvE;AAEJ,YAAQ,gBAAgB,IAAI,YAAY,SAAS,eAAe,KAAK,SAAS;AAC9E,YAAQ,WAAW,IAAI,KAAK,SAAS,KAAK,YACtC,QAAQ,gBAAgB,IACxB,gBAAgB,SAAS,aAAa;AAE1C,QAAI,mBAAmB,QAAQ,gBAAgB,IAAI,KAAK,mBAAmB;AAE3E,QAAI,UAAU,SAAU,GAAG;AACzB,UAAI,WAAW,CAAC,GAAG;AACjB,YAAI,SAAS,SAAS;AACpB,iBAAO,QAAQ,UAAU,IAAI;AAAA,QAC/B,OAAO;AACL,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,EAAE,OAAO,WAAW;AAC7B,QAAI,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC7B,WAAK,MAAM,QAAQ,SAAU,GAAG;AAC9B,WAAG,CAAC,IAAI;AAAA,MACV,CAAC;AAAA,IACH,OAAO;AACL,SAAG,KAAK,KAAK,IAAI;AAAA,IACnB;AAEA,QAAI,OAAO,EAAE,OAAO,QAAQ;AAE5B,QAAI,aACF,CAAC,KAAK,aAAa,cACnB,KAAK,aAAa,WAClB,KAAK,aAAa,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,UAAU,QAAQ,WAAW;AAAA,MAC7B,eAAe,QAAQ,gBAAgB;AAAA,IACzC,CAAC;AAEH,QAAI,YAAY;AACd,UAA6C,CAAC,KAAK,QAAQ;AACzD,SAAC,oBAAoB,KAAK,OAAO,qMAAqM;AACtO,2BAAmB;AAAA,MACrB;AACA,UAAI,WAAW,WAAW,GAAG;AAC3B,eAAO,WAAW,CAAC;AAAA,MACrB,WAAW,WAAW,SAAS,KAAK,CAAC,WAAW,QAAQ;AACtD,YAAI,MAAuC;AACzC;AAAA,YACE;AAAA,YACC,4BAA8B,KAAK,KAAM;AAAA,UAC5C;AAAA,QACF;AACA,eAAO,WAAW,WAAW,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,UAAU;AAAA,MACjE;AAAA,IACF;AAEA,QAAI,MAAuC;AACzC,UAAI,SAAS,KAAK,SAAS,aAAa,CAAC,eAAe;AACtD;AAAA,UACE;AAAA,UACA;AAAA,QACF;AACA,wBAAgB;AAAA,MAClB;AACA,UAAI,WAAW,KAAK,SAAS,aAAa,CAAC,iBAAiB;AAC1D;AAAA,UACE;AAAA,UACA;AAAA,QACF;AACA,0BAAkB;AAAA,MACpB;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,KAAK;AACpB,WAAK,KAAK;AACV,WAAK,QAAQ,EAAE,MAAY,gBAAgB,iBAAiB;AAAA,IAC9D,OAAO;AAEL,UAAI,IAAI,WAAW,KAAK,OAAO,OAAO;AACtC,UAAI,GAAG;AAEL,UAAE,WAAW;AACb,YAAI,QAAS,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,IAAI;AACvC,cAAM,KAAK,MAAM,MAAM,CAAC;AAExB,iBAAS,SAAS,MAAM,IAAI;AAC1B,cAAI,YAAY,MAAM,GAAG,KAAK;AAC9B,cAAI,SAAS,IAAI;AACf,kBAAM,GAAG,KAAK,IAAI,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAAA,UACrE;AAAA,QACF;AAEA,iBAAS,WAAW,IAAI;AACtB,cAAI,WAAW,MAAM,IAAI;AAEvB,kBAAM,GAAG,OAAO,EAAE,KAAK,GAAG,OAAO,CAAC;AAAA,UACpC,OAAO;AACL,kBAAM,GAAG,OAAO,IAAI;AAAA,UACtB;AAAA,QACF;AAEA,YAAI,SAAU,EAAE,KAAK,QAAQ,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK;AACpD,eAAO,OAAO;AACd,eAAO,cAAc,IAAI;AAAA,MAC3B,OAAO;AAEL,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,EAAE,KAAK,KAAK,MAAM,KAAK,OAAO,OAAO;AAAA,EAC9C;AACF;AAEA,SAAS,WAAY,GAAG;AAEtB,MAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU;AAAE;AAAA,EAAO;AAE/D,MAAI,EAAE,kBAAkB;AAAE;AAAA,EAAO;AAEjC,MAAI,EAAE,WAAW,UAAa,EAAE,WAAW,GAAG;AAAE;AAAA,EAAO;AAEvD,MAAI,EAAE,iBAAiB,EAAE,cAAc,cAAc;AACnD,QAAI,SAAS,EAAE,cAAc,aAAa,QAAQ;AAClD,QAAI,cAAc,KAAK,MAAM,GAAG;AAAE;AAAA,IAAO;AAAA,EAC3C;AAEA,MAAI,EAAE,gBAAgB;AACpB,MAAE,eAAe;AAAA,EACnB;AACA,SAAO;AACT;AAEA,SAAS,WAAY,UAAU;AAC7B,MAAI,UAAU;AACZ,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,cAAQ,SAAS,CAAC;AAClB,UAAI,MAAM,QAAQ,KAAK;AACrB,eAAO;AAAA,MACT;AACA,UAAI,MAAM,aAAa,QAAQ,WAAW,MAAM,QAAQ,IAAI;AAC1D,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI;AAEJ,SAAS,QAAS,KAAK;AACrB,MAAI,QAAQ,aAAa,SAAS,KAAK;AAAE;AAAA,EAAO;AAChD,UAAQ,YAAY;AAEpB,SAAO;AAEP,MAAI,QAAQ,SAAU,GAAG;AAAE,WAAO,MAAM;AAAA,EAAW;AAEnD,MAAI,mBAAmB,SAAU,IAAI,SAAS;AAC5C,QAAI,IAAI,GAAG,SAAS;AACpB,QAAI,MAAM,CAAC,KAAK,MAAM,IAAI,EAAE,IAAI,KAAK,MAAM,IAAI,EAAE,qBAAqB,GAAG;AACvE,QAAE,IAAI,OAAO;AAAA,IACf;AAAA,EACF;AAEA,MAAI,MAAM;AAAA,IACR,cAAc,SAAS,eAAgB;AACrC,UAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AAC/B,aAAK,cAAc;AACnB,aAAK,UAAU,KAAK,SAAS;AAC7B,aAAK,QAAQ,KAAK,IAAI;AACtB,YAAI,KAAK,eAAe,MAAM,UAAU,KAAK,QAAQ,QAAQ,OAAO;AAAA,MACtE,OAAO;AACL,aAAK,cAAe,KAAK,WAAW,KAAK,QAAQ,eAAgB;AAAA,MACnE;AACA,uBAAiB,MAAM,IAAI;AAAA,IAC7B;AAAA,IACA,WAAW,SAAS,YAAa;AAC/B,uBAAiB,IAAI;AAAA,IACvB;AAAA,EACF,CAAC;AAED,SAAO,eAAe,IAAI,WAAW,WAAW;AAAA,IAC9C,KAAK,SAAS,MAAO;AAAE,aAAO,KAAK,YAAY;AAAA,IAAQ;AAAA,EACzD,CAAC;AAED,SAAO,eAAe,IAAI,WAAW,UAAU;AAAA,IAC7C,KAAK,SAAS,MAAO;AAAE,aAAO,KAAK,YAAY;AAAA,IAAO;AAAA,EACxD,CAAC;AAED,MAAI,UAAU,cAAc,IAAI;AAChC,MAAI,UAAU,cAAc,IAAI;AAEhC,MAAI,SAAS,IAAI,OAAO;AAExB,SAAO,mBAAmB,OAAO,mBAAmB,OAAO,oBAAoB,OAAO;AACxF;AAIA,IAAI,YAAY,OAAO,WAAW;AAIlC,SAAS,eACP,QACA,aACA,YACA,YACA,aACA;AAEA,MAAI,WAAW,eAAe,CAAC;AAE/B,MAAI,UAAU,cAAc,uBAAO,OAAO,IAAI;AAE9C,MAAI,UAAU,cAAc,uBAAO,OAAO,IAAI;AAE9C,SAAO,QAAQ,SAAU,OAAO;AAC9B,mBAAe,UAAU,SAAS,SAAS,OAAO,WAAW;AAAA,EAC/D,CAAC;AAGD,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,QAAI,SAAS,CAAC,MAAM,KAAK;AACvB,eAAS,KAAK,SAAS,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AACtC;AACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAwC;AAE1C,QAAI,QAAQ,SAET,OAAO,SAAU,MAAM;AAAE,aAAO,QAAQ,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,CAAC,MAAM;AAAA,IAAK,CAAC;AAE9F,QAAI,MAAM,SAAS,GAAG;AACpB,UAAI,YAAY,MAAM,IAAI,SAAU,MAAM;AAAE,eAAQ,OAAO;AAAA,MAAO,CAAC,EAAE,KAAK,IAAI;AAC9E,WAAK,OAAQ,2FAA2F,SAAU;AAAA,IACpH;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eACP,UACA,SACA,SACA,OACA,QACA,SACA;AACA,MAAI,OAAO,MAAM;AACjB,MAAI,OAAO,MAAM;AACjB,MAAI,MAAuC;AACzC,WAAO,QAAQ,MAAM,8CAAgD;AACrE;AAAA,MACE,OAAO,MAAM,cAAc;AAAA,MAC3B,wCAA2C;AAAA,QACzC,QAAQ;AAAA,MACV,IAAK;AAAA,IACP;AAEA;AAAA;AAAA,MAEE,CAAC,oBAAoB,KAAK,IAAI;AAAA,MAC9B,sBAAuB,OAAO;AAAA,IAGhC;AAAA,EACF;AAEA,MAAI,sBACF,MAAM,uBAAuB,CAAC;AAChC,MAAI,iBAAiB,cAAc,MAAM,QAAQ,oBAAoB,MAAM;AAE3E,MAAI,OAAO,MAAM,kBAAkB,WAAW;AAC5C,wBAAoB,YAAY,MAAM;AAAA,EACxC;AAEA,MAAI,SAAS;AAAA,IACX,MAAM;AAAA,IACN,OAAO,kBAAkB,gBAAgB,mBAAmB;AAAA,IAC5D,YAAY,MAAM,cAAc,EAAE,SAAS,MAAM,UAAU;AAAA,IAC3D,OAAO,MAAM,QACT,OAAO,MAAM,UAAU,WACrB,CAAC,MAAM,KAAK,IACZ,MAAM,QACR,CAAC;AAAA,IACL,WAAW,CAAC;AAAA,IACZ,YAAY,CAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,aAAa,MAAM;AAAA,IACnB,MAAM,MAAM,QAAQ,CAAC;AAAA,IACrB,OACE,MAAM,SAAS,OACX,CAAC,IACD,MAAM,aACJ,MAAM,QACN,EAAE,SAAS,MAAM,MAAM;AAAA,EACjC;AAEA,MAAI,MAAM,UAAU;AAIlB,QAAI,MAAuC;AACzC,UACE,MAAM,QACN,CAAC,MAAM,YACP,MAAM,SAAS,KAAK,SAAU,OAAO;AAAE,eAAO,QAAQ,KAAK,MAAM,IAAI;AAAA,MAAG,CAAC,GACzE;AACA;AAAA,UACE;AAAA,UACA,kBAAmB,MAAM,OAAQ,oFAC0B,MAAM,OAAQ;AAAA,QAI3E;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,SAAU,OAAO;AACtC,UAAI,eAAe,UACf,UAAW,UAAU,MAAO,MAAM,IAAM,IACxC;AACJ,qBAAe,UAAU,SAAS,SAAS,OAAO,QAAQ,YAAY;AAAA,IACxE,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,QAAQ,OAAO,IAAI,GAAG;AACzB,aAAS,KAAK,OAAO,IAAI;AACzB,YAAQ,OAAO,IAAI,IAAI;AAAA,EACzB;AAEA,MAAI,MAAM,UAAU,QAAW;AAC7B,QAAI,UAAU,MAAM,QAAQ,MAAM,KAAK,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK;AACrE,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,UAAI,QAAQ,QAAQ,CAAC;AACrB,UAA6C,UAAU,MAAM;AAC3D;AAAA,UACE;AAAA,UACC,sDAAuD,OAAO;AAAA,QACjE;AAEA;AAAA,MACF;AAEA,UAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,UAAU,MAAM;AAAA,MAClB;AACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,QAAQ;AAAA;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM;AACR,QAAI,CAAC,QAAQ,IAAI,GAAG;AAClB,cAAQ,IAAI,IAAI;AAAA,IAClB,WAAoD,CAAC,SAAS;AAC5D;AAAA,QACE;AAAA,QACA,iDACiB,OAAO,eAAkB,OAAO,OAAQ;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBACP,MACA,qBACA;AACA,MAAI,QAAQ,eAAe,MAAM,CAAC,GAAG,mBAAmB;AACxD,MAAI,MAAuC;AACzC,QAAI,OAAO,uBAAO,OAAO,IAAI;AAC7B,UAAM,KAAK,QAAQ,SAAU,KAAK;AAChC;AAAA,QACE,CAAC,KAAK,IAAI,IAAI;AAAA,QACb,+CAAgD,OAAO;AAAA,MAC1D;AACA,WAAK,IAAI,IAAI,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,cACP,MACA,QACA,QACA;AACA,MAAI,CAAC,QAAQ;AAAE,WAAO,KAAK,QAAQ,OAAO,EAAE;AAAA,EAAG;AAC/C,MAAI,KAAK,CAAC,MAAM,KAAK;AAAE,WAAO;AAAA,EAAK;AACnC,MAAI,UAAU,MAAM;AAAE,WAAO;AAAA,EAAK;AAClC,SAAO,UAAY,OAAO,OAAQ,MAAM,IAAK;AAC/C;AAMA,SAAS,cACP,QACA,QACA;AACA,MAAI,MAAM,eAAe,MAAM;AAC/B,MAAI,WAAW,IAAI;AACnB,MAAI,UAAU,IAAI;AAClB,MAAI,UAAU,IAAI;AAElB,WAASC,WAAWC,SAAQ;AAC1B,mBAAeA,SAAQ,UAAU,SAAS,OAAO;AAAA,EACnD;AAEA,WAASC,UAAU,eAAe,OAAO;AACvC,QAAI,SAAU,OAAO,kBAAkB,WAAY,QAAQ,aAAa,IAAI;AAE5E,mBAAe,CAAC,SAAS,aAAa,GAAG,UAAU,SAAS,SAAS,MAAM;AAG3E,QAAI,UAAU,OAAO,MAAM,QAAQ;AACjC;AAAA;AAAA,QAEE,OAAO,MAAM,IAAI,SAAUC,QAAO;AAAE,iBAAQ,EAAE,MAAMA,QAAO,UAAU,CAAC,KAAK,EAAE;AAAA,QAAI,CAAC;AAAA,QAClF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAASC,aAAa;AACpB,WAAO,SAAS,IAAI,SAAU,MAAM;AAAE,aAAO,QAAQ,IAAI;AAAA,IAAG,CAAC;AAAA,EAC/D;AAEA,WAASC,OACP,KACA,cACA,gBACA;AACA,QAAI,WAAW,kBAAkB,KAAK,cAAc,OAAO,MAAM;AACjE,QAAI,OAAO,SAAS;AAEpB,QAAI,MAAM;AACR,UAAI,SAAS,QAAQ,IAAI;AACzB,UAAI,MAAuC;AACzC,aAAK,QAAS,sBAAsB,OAAO,kBAAmB;AAAA,MAChE;AACA,UAAI,CAAC,QAAQ;AAAE,eAAO,aAAa,MAAM,QAAQ;AAAA,MAAE;AACnD,UAAI,aAAa,OAAO,MAAM,KAC3B,OAAO,SAAUC,MAAK;AAAE,eAAO,CAACA,KAAI;AAAA,MAAU,CAAC,EAC/C,IAAI,SAAUA,MAAK;AAAE,eAAOA,KAAI;AAAA,MAAM,CAAC;AAE1C,UAAI,OAAO,SAAS,WAAW,UAAU;AACvC,iBAAS,SAAS,CAAC;AAAA,MACrB;AAEA,UAAI,gBAAgB,OAAO,aAAa,WAAW,UAAU;AAC3D,iBAAS,OAAO,aAAa,QAAQ;AACnC,cAAI,EAAE,OAAO,SAAS,WAAW,WAAW,QAAQ,GAAG,IAAI,IAAI;AAC7D,qBAAS,OAAO,GAAG,IAAI,aAAa,OAAO,GAAG;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAEA,eAAS,OAAO,WAAW,OAAO,MAAM,SAAS,QAAS,kBAAmB,OAAO,GAAK;AACzF,aAAO,aAAa,QAAQ,UAAU,cAAc;AAAA,IACtD,WAAW,SAAS,MAAM;AACxB,eAAS,SAAS,CAAC;AACnB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,OAAO,SAAS,CAAC;AACrB,YAAI,WAAW,QAAQ,IAAI;AAC3B,YAAI,WAAW,SAAS,OAAO,SAAS,MAAM,SAAS,MAAM,GAAG;AAC9D,iBAAO,aAAa,UAAU,UAAU,cAAc;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAEA,WAAO,aAAa,MAAM,QAAQ;AAAA,EACpC;AAEA,WAAS,SACP,QACA,UACA;AACA,QAAI,mBAAmB,OAAO;AAC9B,QAAIC,YAAW,OAAO,qBAAqB,aACvC,iBAAiB,YAAY,QAAQ,UAAU,MAAM,MAAM,CAAC,IAC5D;AAEJ,QAAI,OAAOA,cAAa,UAAU;AAChC,MAAAA,YAAW,EAAE,MAAMA,UAAS;AAAA,IAC9B;AAEA,QAAI,CAACA,aAAY,OAAOA,cAAa,UAAU;AAC7C,UAAI,MAAuC;AACzC;AAAA,UACE;AAAA,UAAQ,8BAA+B,KAAK,UAAUA,SAAQ;AAAA,QAChE;AAAA,MACF;AACA,aAAO,aAAa,MAAM,QAAQ;AAAA,IACpC;AAEA,QAAI,KAAKA;AACT,QAAI,OAAO,GAAG;AACd,QAAI,OAAO,GAAG;AACd,QAAI,QAAQ,SAAS;AACrB,QAAI,OAAO,SAAS;AACpB,QAAI,SAAS,SAAS;AACtB,YAAQ,GAAG,eAAe,OAAO,IAAI,GAAG,QAAQ;AAChD,WAAO,GAAG,eAAe,MAAM,IAAI,GAAG,OAAO;AAC7C,aAAS,GAAG,eAAe,QAAQ,IAAI,GAAG,SAAS;AAEnD,QAAI,MAAM;AAER,UAAI,eAAe,QAAQ,IAAI;AAC/B,UAAI,MAAuC;AACzC,eAAO,cAAe,mCAAoC,OAAO,cAAgB;AAAA,MACnF;AACA,aAAOF,OAAM;AAAA,QACX,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,QAAW,QAAQ;AAAA,IACxB,WAAW,MAAM;AAEf,UAAI,UAAU,kBAAkB,MAAM,MAAM;AAE5C,UAAI,eAAe,WAAW,SAAS,QAAS,+BAAgC,UAAU,GAAK;AAE/F,aAAOA,OAAM;AAAA,QACX,aAAa;AAAA,QACb,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,GAAG,QAAW,QAAQ;AAAA,IACxB,OAAO;AACL,UAAI,MAAuC;AACzC,aAAK,OAAQ,8BAA+B,KAAK,UAAUE,SAAQ,CAAG;AAAA,MACxE;AACA,aAAO,aAAa,MAAM,QAAQ;AAAA,IACpC;AAAA,EACF;AAEA,WAAS,MACP,QACA,UACA,SACA;AACA,QAAI,cAAc,WAAW,SAAS,SAAS,QAAS,8BAA+B,UAAU,GAAK;AACtG,QAAI,eAAeF,OAAM;AAAA,MACvB,aAAa;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AACD,QAAI,cAAc;AAChB,UAAI,UAAU,aAAa;AAC3B,UAAI,gBAAgB,QAAQ,QAAQ,SAAS,CAAC;AAC9C,eAAS,SAAS,aAAa;AAC/B,aAAO,aAAa,eAAe,QAAQ;AAAA,IAC7C;AACA,WAAO,aAAa,MAAM,QAAQ;AAAA,EACpC;AAEA,WAAS,aACP,QACA,UACA,gBACA;AACA,QAAI,UAAU,OAAO,UAAU;AAC7B,aAAO,SAAS,QAAQ,kBAAkB,QAAQ;AAAA,IACpD;AACA,QAAI,UAAU,OAAO,SAAS;AAC5B,aAAO,MAAM,QAAQ,UAAU,OAAO,OAAO;AAAA,IAC/C;AACA,WAAO,YAAY,QAAQ,UAAU,gBAAgB,MAAM;AAAA,EAC7D;AAEA,SAAO;AAAA,IACL,OAAOA;AAAA,IACP,UAAUH;AAAA,IACV,WAAWE;AAAA,IACX,WAAWJ;AAAA,EACb;AACF;AAEA,SAAS,WACP,OACA,MACA,QACA;AACA,MAAI,IAAI,KAAK,MAAM,KAAK;AAExB,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT,WAAW,CAAC,QAAQ;AAClB,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,MAAM,EAAE,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC5C,QAAI,MAAM,MAAM,KAAK,IAAI,CAAC;AAC1B,QAAI,KAAK;AAEP,aAAO,IAAI,QAAQ,WAAW,IAAI,OAAO,EAAE,CAAC,MAAM,WAAW,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,IACjF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,kBAAmB,MAAM,QAAQ;AACxC,SAAO,YAAY,MAAM,OAAO,SAAS,OAAO,OAAO,OAAO,KAAK,IAAI;AACzE;AAKA,IAAI,OACF,aAAa,OAAO,eAAe,OAAO,YAAY,MAClD,OAAO,cACP;AAEN,SAAS,cAAe;AACtB,SAAO,KAAK,IAAI,EAAE,QAAQ,CAAC;AAC7B;AAEA,IAAI,OAAO,YAAY;AAEvB,SAAS,cAAe;AACtB,SAAO;AACT;AAEA,SAAS,YAAa,KAAK;AACzB,SAAQ,OAAO;AACjB;AAIA,IAAI,gBAAgB,uBAAO,OAAO,IAAI;AAEtC,SAAS,cAAe;AAEtB,MAAI,uBAAuB,OAAO,SAAS;AACzC,WAAO,QAAQ,oBAAoB;AAAA,EACrC;AAMA,MAAI,kBAAkB,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS;AACxE,MAAI,eAAe,OAAO,SAAS,KAAK,QAAQ,iBAAiB,EAAE;AAEnE,MAAI,YAAY,OAAO,CAAC,GAAG,OAAO,QAAQ,KAAK;AAC/C,YAAU,MAAM,YAAY;AAC5B,SAAO,QAAQ,aAAa,WAAW,IAAI,YAAY;AACvD,SAAO,iBAAiB,YAAY,cAAc;AAClD,SAAO,WAAY;AACjB,WAAO,oBAAoB,YAAY,cAAc;AAAA,EACvD;AACF;AAEA,SAAS,aACP,QACA,IACA,MACA,OACA;AACA,MAAI,CAAC,OAAO,KAAK;AACf;AAAA,EACF;AAEA,MAAI,WAAW,OAAO,QAAQ;AAC9B,MAAI,CAAC,UAAU;AACb;AAAA,EACF;AAEA,MAAI,MAAuC;AACzC,WAAO,OAAO,aAAa,YAAY,mCAAmC;AAAA,EAC5E;AAGA,SAAO,IAAI,UAAU,WAAY;AAC/B,QAAI,WAAW,kBAAkB;AACjC,QAAI,eAAe,SAAS;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,WAAW;AAAA,IACrB;AAEA,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAEA,QAAI,OAAO,aAAa,SAAS,YAAY;AAC3C,mBACG,KAAK,SAAUQ,eAAc;AAC5B,yBAAkBA,eAAe,QAAQ;AAAA,MAC3C,CAAC,EACA,MAAM,SAAU,KAAK;AACpB,YAAI,MAAuC;AACzC,iBAAO,OAAO,IAAI,SAAS,CAAC;AAAA,QAC9B;AAAA,MACF,CAAC;AAAA,IACL,OAAO;AACL,uBAAiB,cAAc,QAAQ;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AAEA,SAAS,qBAAsB;AAC7B,MAAI,MAAM,YAAY;AACtB,MAAI,KAAK;AACP,kBAAc,GAAG,IAAI;AAAA,MACnB,GAAG,OAAO;AAAA,MACV,GAAG,OAAO;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,eAAgB,GAAG;AAC1B,qBAAmB;AACnB,MAAI,EAAE,SAAS,EAAE,MAAM,KAAK;AAC1B,gBAAY,EAAE,MAAM,GAAG;AAAA,EACzB;AACF;AAEA,SAAS,oBAAqB;AAC5B,MAAI,MAAM,YAAY;AACtB,MAAI,KAAK;AACP,WAAO,cAAc,GAAG;AAAA,EAC1B;AACF;AAEA,SAAS,mBAAoB,IAAI,QAAQ;AACvC,MAAI,QAAQ,SAAS;AACrB,MAAI,UAAU,MAAM,sBAAsB;AAC1C,MAAI,SAAS,GAAG,sBAAsB;AACtC,SAAO;AAAA,IACL,GAAG,OAAO,OAAO,QAAQ,OAAO,OAAO;AAAA,IACvC,GAAG,OAAO,MAAM,QAAQ,MAAM,OAAO;AAAA,EACvC;AACF;AAEA,SAAS,gBAAiB,KAAK;AAC7B,SAAO,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;AAC1C;AAEA,SAAS,kBAAmB,KAAK;AAC/B,SAAO;AAAA,IACL,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO;AAAA,IACpC,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO;AAAA,EACtC;AACF;AAEA,SAAS,gBAAiB,KAAK;AAC7B,SAAO;AAAA,IACL,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI;AAAA,IAC7B,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI;AAAA,EAC/B;AACF;AAEA,SAAS,SAAU,GAAG;AACpB,SAAO,OAAO,MAAM;AACtB;AAEA,IAAI,yBAAyB;AAE7B,SAAS,iBAAkB,cAAc,UAAU;AACjD,MAAI,WAAW,OAAO,iBAAiB;AACvC,MAAI,YAAY,OAAO,aAAa,aAAa,UAAU;AAGzD,QAAI,KAAK,uBAAuB,KAAK,aAAa,QAAQ,IACtD,SAAS,eAAe,aAAa,SAAS,MAAM,CAAC,CAAC,IACtD,SAAS,cAAc,aAAa,QAAQ;AAEhD,QAAI,IAAI;AACN,UAAI,SACF,aAAa,UAAU,OAAO,aAAa,WAAW,WAClD,aAAa,SACb,CAAC;AACP,eAAS,gBAAgB,MAAM;AAC/B,iBAAW,mBAAmB,IAAI,MAAM;AAAA,IAC1C,WAAW,gBAAgB,YAAY,GAAG;AACxC,iBAAW,kBAAkB,YAAY;AAAA,IAC3C;AAAA,EACF,WAAW,YAAY,gBAAgB,YAAY,GAAG;AACpD,eAAW,kBAAkB,YAAY;AAAA,EAC3C;AAEA,MAAI,UAAU;AAEZ,QAAI,oBAAoB,SAAS,gBAAgB,OAAO;AACtD,aAAO,SAAS;AAAA,QACd,MAAM,SAAS;AAAA,QACf,KAAK,SAAS;AAAA;AAAA,QAEd,UAAU,aAAa;AAAA,MACzB,CAAC;AAAA,IACH,OAAO;AACL,aAAO,SAAS,SAAS,GAAG,SAAS,CAAC;AAAA,IACxC;AAAA,EACF;AACF;AAIA,IAAI,oBACF,aACC,WAAY;AACX,MAAI,KAAK,OAAO,UAAU;AAE1B,OACG,GAAG,QAAQ,YAAY,MAAM,MAAM,GAAG,QAAQ,aAAa,MAAM,OAClE,GAAG,QAAQ,eAAe,MAAM,MAChC,GAAG,QAAQ,QAAQ,MAAM,MACzB,GAAG,QAAQ,eAAe,MAAM,IAChC;AACA,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,cAAc;AAC/D,EAAG;AAEL,SAAS,UAAW,KAAKC,UAAS;AAChC,qBAAmB;AAGnB,MAAI,UAAU,OAAO;AACrB,MAAI;AACF,QAAIA,UAAS;AAEX,UAAI,YAAY,OAAO,CAAC,GAAG,QAAQ,KAAK;AACxC,gBAAU,MAAM,YAAY;AAC5B,cAAQ,aAAa,WAAW,IAAI,GAAG;AAAA,IACzC,OAAO;AACL,cAAQ,UAAU,EAAE,KAAK,YAAY,YAAY,CAAC,EAAE,GAAG,IAAI,GAAG;AAAA,IAChE;AAAA,EACF,SAAS,GAAG;AACV,WAAO,SAASA,WAAU,YAAY,QAAQ,EAAE,GAAG;AAAA,EACrD;AACF;AAEA,SAAS,aAAc,KAAK;AAC1B,YAAU,KAAK,IAAI;AACrB;AAGA,IAAI,wBAAwB;AAAA,EAC1B,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AACd;AAEA,SAAS,gCAAiC,MAAM,IAAI;AAClD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,sBAAsB;AAAA,IACrB,iCAAmC,KAAK,WAAY,WAAc;AAAA,MACjE;AAAA,IACF,IAAK;AAAA,EACP;AACF;AAEA,SAAS,gCAAiC,MAAM,IAAI;AAClD,MAAI,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,IACA,sBAAsB;AAAA,IACrB,wDAA0D,KAAK,WAAY;AAAA,EAC9E;AAEA,QAAM,OAAO;AACb,SAAO;AACT;AAEA,SAAS,+BAAgC,MAAM,IAAI;AACjD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,sBAAsB;AAAA,IACrB,gCAAkC,KAAK,WAAY,WAAc,GAAG,WAAY;AAAA,EACnF;AACF;AAEA,SAAS,6BAA8B,MAAM,IAAI;AAC/C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,sBAAsB;AAAA,IACrB,8BAAgC,KAAK,WAAY,WAAc,GAAG,WAAY;AAAA,EACjF;AACF;AAEA,SAAS,kBAAmB,MAAM,IAAI,MAAM,SAAS;AACnD,MAAI,QAAQ,IAAI,MAAM,OAAO;AAC7B,QAAM,YAAY;AAClB,QAAM,OAAO;AACb,QAAM,KAAK;AACX,QAAM,OAAO;AAEb,SAAO;AACT;AAEA,IAAI,kBAAkB,CAAC,UAAU,SAAS,MAAM;AAEhD,SAAS,eAAgB,IAAI;AAC3B,MAAI,OAAO,OAAO,UAAU;AAAE,WAAO;AAAA,EAAG;AACxC,MAAI,UAAU,IAAI;AAAE,WAAO,GAAG;AAAA,EAAK;AACnC,MAAI,WAAW,CAAC;AAChB,kBAAgB,QAAQ,SAAU,KAAK;AACrC,QAAI,OAAO,IAAI;AAAE,eAAS,GAAG,IAAI,GAAG,GAAG;AAAA,IAAG;AAAA,EAC5C,CAAC;AACD,SAAO,KAAK,UAAU,UAAU,MAAM,CAAC;AACzC;AAEA,SAAS,QAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,EAAE,QAAQ,OAAO,IAAI;AAChE;AAEA,SAAS,oBAAqB,KAAK,WAAW;AAC5C,SACE,QAAQ,GAAG,KACX,IAAI,cACH,aAAa,QAAQ,IAAI,SAAS;AAEvC;AAIA,SAAS,SAAU,OAAO,IAAI,IAAI;AAChC,MAAI,OAAO,SAAU,OAAO;AAC1B,QAAI,SAAS,MAAM,QAAQ;AACzB,SAAG;AAAA,IACL,OAAO;AACL,UAAI,MAAM,KAAK,GAAG;AAChB,WAAG,MAAM,KAAK,GAAG,WAAY;AAC3B,eAAK,QAAQ,CAAC;AAAA,QAChB,CAAC;AAAA,MACH,OAAO;AACL,aAAK,QAAQ,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,OAAK,CAAC;AACR;AAIA,SAAS,uBAAwB,SAAS;AACxC,SAAO,SAAU,IAAI,MAAM,MAAM;AAC/B,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,QAAQ;AAEZ,sBAAkB,SAAS,SAAU,KAAK,GAAGJ,QAAO,KAAK;AAMvD,UAAI,OAAO,QAAQ,cAAc,IAAI,QAAQ,QAAW;AACtD,mBAAW;AACX;AAEA,YAAIK,WAAU,KAAK,SAAU,aAAa;AACxC,cAAI,WAAW,WAAW,GAAG;AAC3B,0BAAc,YAAY;AAAA,UAC5B;AAEA,cAAI,WAAW,OAAO,gBAAgB,aAClC,cACA,KAAK,OAAO,WAAW;AAC3B,UAAAL,OAAM,WAAW,GAAG,IAAI;AACxB;AACA,cAAI,WAAW,GAAG;AAChB,iBAAK;AAAA,UACP;AAAA,QACF,CAAC;AAED,YAAI,SAAS,KAAK,SAAU,QAAQ;AAClC,cAAI,MAAM,uCAAuC,MAAM,OAAO;AAC9D,UAAyC,KAAK,OAAO,GAAG;AACxD,cAAI,CAAC,OAAO;AACV,oBAAQ,QAAQ,MAAM,IAClB,SACA,IAAI,MAAM,GAAG;AACjB,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AAED,YAAI;AACJ,YAAI;AACF,gBAAM,IAAIK,UAAS,MAAM;AAAA,QAC3B,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,KAAK;AACP,cAAI,OAAO,IAAI,SAAS,YAAY;AAClC,gBAAI,KAAKA,UAAS,MAAM;AAAA,UAC1B,OAAO;AAEL,gBAAI,OAAO,IAAI;AACf,gBAAI,QAAQ,OAAO,KAAK,SAAS,YAAY;AAC3C,mBAAK,KAAKA,UAAS,MAAM;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,CAAC,UAAU;AAAE,WAAK;AAAA,IAAG;AAAA,EAC3B;AACF;AAEA,SAAS,kBACP,SACA,IACA;AACA,SAAO,QAAQ,QAAQ,IAAI,SAAU,GAAG;AACtC,WAAO,OAAO,KAAK,EAAE,UAAU,EAAE,IAAI,SAAU,KAAK;AAAE,aAAO;AAAA,QAC3D,EAAE,WAAW,GAAG;AAAA,QAChB,EAAE,UAAU,GAAG;AAAA,QACf;AAAA,QAAG;AAAA,MACL;AAAA,IAAG,CAAC;AAAA,EACN,CAAC,CAAC;AACJ;AAEA,SAAS,QAAS,KAAK;AACrB,SAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,GAAG;AAC7C;AAEA,IAAI,YACF,OAAO,WAAW,cAClB,OAAO,OAAO,gBAAgB;AAEhC,SAAS,WAAY,KAAK;AACxB,SAAO,IAAI,cAAe,aAAa,IAAI,OAAO,WAAW,MAAM;AACrE;AAMA,SAAS,KAAM,IAAI;AACjB,MAAI,SAAS;AACb,SAAO,WAAY;AACjB,QAAI,OAAO,CAAC,GAAG,MAAM,UAAU;AAC/B,WAAQ,MAAQ,MAAM,GAAI,IAAI,UAAW,GAAI;AAE7C,QAAI,QAAQ;AAAE;AAAA,IAAO;AACrB,aAAS;AACT,WAAO,GAAG,MAAM,MAAM,IAAI;AAAA,EAC5B;AACF;AAIA,IAAI,UAAU,SAASC,SAAS,QAAQ,MAAM;AAC5C,OAAK,SAAS;AACd,OAAK,OAAO,cAAc,IAAI;AAE9B,OAAK,UAAU;AACf,OAAK,UAAU;AACf,OAAK,QAAQ;AACb,OAAK,WAAW,CAAC;AACjB,OAAK,gBAAgB,CAAC;AACtB,OAAK,WAAW,CAAC;AACjB,OAAK,YAAY,CAAC;AACpB;AAEA,QAAQ,UAAU,SAAS,SAAS,OAAQ,IAAI;AAC9C,OAAK,KAAK;AACZ;AAEA,QAAQ,UAAU,UAAU,SAAS,QAAS,IAAI,SAAS;AACzD,MAAI,KAAK,OAAO;AACd,OAAG;AAAA,EACL,OAAO;AACL,SAAK,SAAS,KAAK,EAAE;AACrB,QAAI,SAAS;AACX,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC;AAAA,EACF;AACF;AAEA,QAAQ,UAAU,UAAU,SAAS,QAAS,SAAS;AACrD,OAAK,SAAS,KAAK,OAAO;AAC5B;AAEA,QAAQ,UAAU,eAAe,SAAS,aACxC,UACA,YACA,SACA;AACE,MAAI,WAAW;AAEjB,MAAI;AAEJ,MAAI;AACF,YAAQ,KAAK,OAAO,MAAM,UAAU,KAAK,OAAO;AAAA,EAClD,SAAS,GAAG;AACV,SAAK,SAAS,QAAQ,SAAU,IAAI;AAClC,SAAG,CAAC;AAAA,IACN,CAAC;AAED,UAAM;AAAA,EACR;AACA,MAAI,OAAO,KAAK;AAChB,OAAK;AAAA,IACH;AAAA,IACA,WAAY;AACV,eAAS,YAAY,KAAK;AAC1B,oBAAc,WAAW,KAAK;AAC9B,eAAS,UAAU;AACnB,eAAS,OAAO,WAAW,QAAQ,SAAU,MAAM;AACjD,gBAAQ,KAAK,OAAO,IAAI;AAAA,MAC1B,CAAC;AAGD,UAAI,CAAC,SAAS,OAAO;AACnB,iBAAS,QAAQ;AACjB,iBAAS,SAAS,QAAQ,SAAU,IAAI;AACtC,aAAG,KAAK;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,SAAU,KAAK;AACb,UAAI,SAAS;AACX,gBAAQ,GAAG;AAAA,MACb;AACA,UAAI,OAAO,CAAC,SAAS,OAAO;AAK1B,YAAI,CAAC,oBAAoB,KAAK,sBAAsB,UAAU,KAAK,SAAS,OAAO;AACjF,mBAAS,QAAQ;AACjB,mBAAS,cAAc,QAAQ,SAAU,IAAI;AAC3C,eAAG,GAAG;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,QAAQ,UAAU,oBAAoB,SAAS,kBAAmB,OAAO,YAAY,SAAS;AAC1F,MAAI,WAAW;AAEjB,MAAI,UAAU,KAAK;AACnB,OAAK,UAAU;AACf,MAAI,QAAQ,SAAU,KAAK;AAIzB,QAAI,CAAC,oBAAoB,GAAG,KAAK,QAAQ,GAAG,GAAG;AAC7C,UAAI,SAAS,SAAS,QAAQ;AAC5B,iBAAS,SAAS,QAAQ,SAAU,IAAI;AACtC,aAAG,GAAG;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AACL,YAAI,MAAuC;AACzC,eAAK,OAAO,yCAAyC;AAAA,QACvD;AACA,gBAAQ,MAAM,GAAG;AAAA,MACnB;AAAA,IACF;AACA,eAAW,QAAQ,GAAG;AAAA,EACxB;AACA,MAAI,iBAAiB,MAAM,QAAQ,SAAS;AAC5C,MAAI,mBAAmB,QAAQ,QAAQ,SAAS;AAChD,MACE,YAAY,OAAO,OAAO;AAAA,EAE1B,mBAAmB,oBACnB,MAAM,QAAQ,cAAc,MAAM,QAAQ,QAAQ,gBAAgB,GAClE;AACA,SAAK,UAAU;AACf,QAAI,MAAM,MAAM;AACd,mBAAa,KAAK,QAAQ,SAAS,OAAO,KAAK;AAAA,IACjD;AACA,WAAO,MAAM,gCAAgC,SAAS,KAAK,CAAC;AAAA,EAC9D;AAEA,MAAI,MAAM;AAAA,IACR,KAAK,QAAQ;AAAA,IACb,MAAM;AAAA,EACR;AACE,MAAI,UAAU,IAAI;AAClB,MAAI,cAAc,IAAI;AACtB,MAAI,YAAY,IAAI;AAEtB,MAAI,QAAQ,CAAC,EAAE;AAAA;AAAA,IAEb,mBAAmB,WAAW;AAAA;AAAA,IAE9B,KAAK,OAAO;AAAA;AAAA,IAEZ,mBAAmB,OAAO;AAAA;AAAA,IAE1B,UAAU,IAAI,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAa,CAAC;AAAA;AAAA,IAEpD,uBAAuB,SAAS;AAAA,EAClC;AAEA,MAAI,WAAW,SAAU,MAAM,MAAM;AACnC,QAAI,SAAS,YAAY,OAAO;AAC9B,aAAO,MAAM,+BAA+B,SAAS,KAAK,CAAC;AAAA,IAC7D;AACA,QAAI;AACF,WAAK,OAAO,SAAS,SAAU,IAAI;AACjC,YAAI,OAAO,OAAO;AAEhB,mBAAS,UAAU,IAAI;AACvB,gBAAM,6BAA6B,SAAS,KAAK,CAAC;AAAA,QACpD,WAAW,QAAQ,EAAE,GAAG;AACtB,mBAAS,UAAU,IAAI;AACvB,gBAAM,EAAE;AAAA,QACV,WACE,OAAO,OAAO,YACb,OAAO,OAAO,aACZ,OAAO,GAAG,SAAS,YAAY,OAAO,GAAG,SAAS,WACrD;AAEA,gBAAM,gCAAgC,SAAS,KAAK,CAAC;AACrD,cAAI,OAAO,OAAO,YAAY,GAAG,SAAS;AACxC,qBAAS,QAAQ,EAAE;AAAA,UACrB,OAAO;AACL,qBAAS,KAAK,EAAE;AAAA,UAClB;AAAA,QACF,OAAO;AAEL,eAAK,EAAE;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH,SAAS,GAAG;AACV,YAAM,CAAC;AAAA,IACT;AAAA,EACF;AAEA,WAAS,OAAO,UAAU,WAAY;AAGpC,QAAI,cAAc,mBAAmB,SAAS;AAC9C,QAAIC,SAAQ,YAAY,OAAO,SAAS,OAAO,YAAY;AAC3D,aAASA,QAAO,UAAU,WAAY;AACpC,UAAI,SAAS,YAAY,OAAO;AAC9B,eAAO,MAAM,+BAA+B,SAAS,KAAK,CAAC;AAAA,MAC7D;AACA,eAAS,UAAU;AACnB,iBAAW,KAAK;AAChB,UAAI,SAAS,OAAO,KAAK;AACvB,iBAAS,OAAO,IAAI,UAAU,WAAY;AACxC,6BAAmB,KAAK;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,QAAQ,UAAU,cAAc,SAAS,YAAa,OAAO;AAC3D,OAAK,UAAU;AACf,OAAK,MAAM,KAAK,GAAG,KAAK;AAC1B;AAEA,QAAQ,UAAU,iBAAiB,SAAS,iBAAkB;AAE9D;AAEA,QAAQ,UAAU,WAAW,SAAS,WAAY;AAGhD,OAAK,UAAU,QAAQ,SAAU,iBAAiB;AAChD,oBAAgB;AAAA,EAClB,CAAC;AACD,OAAK,YAAY,CAAC;AAIlB,OAAK,UAAU;AACf,OAAK,UAAU;AACjB;AAEA,SAAS,cAAe,MAAM;AAC5B,MAAI,CAAC,MAAM;AACT,QAAI,WAAW;AAEb,UAAI,SAAS,SAAS,cAAc,MAAM;AAC1C,aAAQ,UAAU,OAAO,aAAa,MAAM,KAAM;AAElD,aAAO,KAAK,QAAQ,sBAAsB,EAAE;AAAA,IAC9C,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,WAAO,MAAM;AAAA,EACf;AAEA,SAAO,KAAK,QAAQ,OAAO,EAAE;AAC/B;AAEA,SAAS,aACP,SACA,MACA;AACA,MAAI;AACJ,MAAI,MAAM,KAAK,IAAI,QAAQ,QAAQ,KAAK,MAAM;AAC9C,OAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,QAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG;AAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,SAAS,KAAK,MAAM,GAAG,CAAC;AAAA,IACxB,WAAW,KAAK,MAAM,CAAC;AAAA,IACvB,aAAa,QAAQ,MAAM,CAAC;AAAA,EAC9B;AACF;AAEA,SAAS,cACP,SACA,MACA,MACA,SACA;AACA,MAAI,SAAS,kBAAkB,SAAS,SAAU,KAAK,UAAUP,QAAO,KAAK;AAC3E,QAAI,QAAQ,aAAa,KAAK,IAAI;AAClC,QAAI,OAAO;AACT,aAAO,MAAM,QAAQ,KAAK,IACtB,MAAM,IAAI,SAAUQ,QAAO;AAAE,eAAO,KAAKA,QAAO,UAAUR,QAAO,GAAG;AAAA,MAAG,CAAC,IACxE,KAAK,OAAO,UAAUA,QAAO,GAAG;AAAA,IACtC;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,UAAU,OAAO,QAAQ,IAAI,MAAM;AACpD;AAEA,SAAS,aACP,KACA,KACA;AACA,MAAI,OAAO,QAAQ,YAAY;AAE7B,UAAM,KAAK,OAAO,GAAG;AAAA,EACvB;AACA,SAAO,IAAI,QAAQ,GAAG;AACxB;AAEA,SAAS,mBAAoB,aAAa;AACxC,SAAO,cAAc,aAAa,oBAAoB,WAAW,IAAI;AACvE;AAEA,SAAS,mBAAoB,SAAS;AACpC,SAAO,cAAc,SAAS,qBAAqB,SAAS;AAC9D;AAEA,SAAS,UAAW,OAAO,UAAU;AACnC,MAAI,UAAU;AACZ,WAAO,SAAS,kBAAmB;AACjC,aAAO,MAAM,MAAM,UAAU,SAAS;AAAA,IACxC;AAAA,EACF;AACF;AAEA,SAAS,mBACP,WACA;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,SAAU,OAAO,GAAGA,QAAO,KAAK;AAC9B,aAAO,eAAe,OAAOA,QAAO,GAAG;AAAA,IACzC;AAAA,EACF;AACF;AAEA,SAAS,eACP,OACAA,QACA,KACA;AACA,SAAO,SAAS,gBAAiB,IAAI,MAAM,MAAM;AAC/C,WAAO,MAAM,IAAI,MAAM,SAAU,IAAI;AACnC,UAAI,OAAO,OAAO,YAAY;AAC5B,YAAI,CAACA,OAAM,WAAW,GAAG,GAAG;AAC1B,UAAAA,OAAM,WAAW,GAAG,IAAI,CAAC;AAAA,QAC3B;AACA,QAAAA,OAAM,WAAW,GAAG,EAAE,KAAK,EAAE;AAAA,MAC/B;AACA,WAAK,EAAE;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAIA,IAAI,eAA6B,SAAUM,UAAS;AAClD,WAASG,cAAc,QAAQ,MAAM;AACnC,IAAAH,SAAQ,KAAK,MAAM,QAAQ,IAAI;AAE/B,SAAK,iBAAiB,YAAY,KAAK,IAAI;AAAA,EAC7C;AAEA,MAAKA,SAAU,CAAAG,cAAa,YAAYH;AACxC,EAAAG,cAAa,YAAY,OAAO,OAAQH,YAAWA,SAAQ,SAAU;AACrE,EAAAG,cAAa,UAAU,cAAcA;AAErC,EAAAA,cAAa,UAAU,iBAAiB,SAASC,kBAAkB;AACjE,QAAI,WAAW;AAEf,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B;AAAA,IACF;AAEA,QAAI,SAAS,KAAK;AAClB,QAAI,eAAe,OAAO,QAAQ;AAClC,QAAI,iBAAiB,qBAAqB;AAE1C,QAAI,gBAAgB;AAClB,WAAK,UAAU,KAAK,YAAY,CAAC;AAAA,IACnC;AAEA,QAAI,qBAAqB,WAAY;AACnC,UAAI,UAAU,SAAS;AAIvB,UAAI,WAAW,YAAY,SAAS,IAAI;AACxC,UAAI,SAAS,YAAY,SAAS,aAAa,SAAS,gBAAgB;AACtE;AAAA,MACF;AAEA,eAAS,aAAa,UAAU,SAAU,OAAO;AAC/C,YAAI,gBAAgB;AAClB,uBAAa,QAAQ,OAAO,SAAS,IAAI;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,iBAAiB,YAAY,kBAAkB;AACtD,SAAK,UAAU,KAAK,WAAY;AAC9B,aAAO,oBAAoB,YAAY,kBAAkB;AAAA,IAC3D,CAAC;AAAA,EACH;AAEA,EAAAD,cAAa,UAAU,KAAK,SAASE,IAAI,GAAG;AAC1C,WAAO,QAAQ,GAAG,CAAC;AAAA,EACrB;AAEA,EAAAF,cAAa,UAAU,OAAO,SAASG,MAAM,UAAU,YAAY,SAAS;AAC1E,QAAI,WAAW;AAEf,QAAI,MAAM;AACV,QAAI,YAAY,IAAI;AACpB,SAAK,aAAa,UAAU,SAAU,OAAO;AAC3C,gBAAU,UAAU,SAAS,OAAO,MAAM,QAAQ,CAAC;AACnD,mBAAa,SAAS,QAAQ,OAAO,WAAW,KAAK;AACrD,oBAAc,WAAW,KAAK;AAAA,IAChC,GAAG,OAAO;AAAA,EACZ;AAEA,EAAAH,cAAa,UAAU,UAAU,SAASL,SAAS,UAAU,YAAY,SAAS;AAChF,QAAI,WAAW;AAEf,QAAI,MAAM;AACV,QAAI,YAAY,IAAI;AACpB,SAAK,aAAa,UAAU,SAAU,OAAO;AAC3C,mBAAa,UAAU,SAAS,OAAO,MAAM,QAAQ,CAAC;AACtD,mBAAa,SAAS,QAAQ,OAAO,WAAW,KAAK;AACrD,oBAAc,WAAW,KAAK;AAAA,IAChC,GAAG,OAAO;AAAA,EACZ;AAEA,EAAAK,cAAa,UAAU,YAAY,SAAS,UAAWG,OAAM;AAC3D,QAAI,YAAY,KAAK,IAAI,MAAM,KAAK,QAAQ,UAAU;AACpD,UAAI,UAAU,UAAU,KAAK,OAAO,KAAK,QAAQ,QAAQ;AACzD,MAAAA,QAAO,UAAU,OAAO,IAAI,aAAa,OAAO;AAAA,IAClD;AAAA,EACF;AAEA,EAAAH,cAAa,UAAU,qBAAqB,SAAS,qBAAsB;AACzE,WAAO,YAAY,KAAK,IAAI;AAAA,EAC9B;AAEA,SAAOA;AACT,EAAE,OAAO;AAET,SAAS,YAAa,MAAM;AAC1B,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,gBAAgB,KAAK,YAAY;AACrC,MAAI,gBAAgB,KAAK,YAAY;AAIrC,MAAI,SAAU,kBAAkB,iBAC7B,cAAc,QAAQ,UAAU,gBAAgB,GAAG,CAAC,MAAM,IAAK;AAChE,WAAO,KAAK,MAAM,KAAK,MAAM;AAAA,EAC/B;AACA,UAAQ,QAAQ,OAAO,OAAO,SAAS,SAAS,OAAO,SAAS;AAClE;AAIA,IAAI,cAA4B,SAAUH,UAAS;AACjD,WAASO,aAAa,QAAQ,MAAM,UAAU;AAC5C,IAAAP,SAAQ,KAAK,MAAM,QAAQ,IAAI;AAE/B,QAAI,YAAY,cAAc,KAAK,IAAI,GAAG;AACxC;AAAA,IACF;AACA,gBAAY;AAAA,EACd;AAEA,MAAKA,SAAU,CAAAO,aAAY,YAAYP;AACvC,EAAAO,aAAY,YAAY,OAAO,OAAQP,YAAWA,SAAQ,SAAU;AACpE,EAAAO,aAAY,UAAU,cAAcA;AAIpC,EAAAA,aAAY,UAAU,iBAAiB,SAASH,kBAAkB;AAChE,QAAI,WAAW;AAEf,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B;AAAA,IACF;AAEA,QAAI,SAAS,KAAK;AAClB,QAAI,eAAe,OAAO,QAAQ;AAClC,QAAI,iBAAiB,qBAAqB;AAE1C,QAAI,gBAAgB;AAClB,WAAK,UAAU,KAAK,YAAY,CAAC;AAAA,IACnC;AAEA,QAAI,qBAAqB,WAAY;AACnC,UAAI,UAAU,SAAS;AACvB,UAAI,CAAC,YAAY,GAAG;AAClB;AAAA,MACF;AACA,eAAS,aAAa,QAAQ,GAAG,SAAU,OAAO;AAChD,YAAI,gBAAgB;AAClB,uBAAa,SAAS,QAAQ,OAAO,SAAS,IAAI;AAAA,QACpD;AACA,YAAI,CAAC,mBAAmB;AACtB,sBAAY,MAAM,QAAQ;AAAA,QAC5B;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,YAAY,oBAAoB,aAAa;AACjD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,SAAK,UAAU,KAAK,WAAY;AAC9B,aAAO,oBAAoB,WAAW,kBAAkB;AAAA,IAC1D,CAAC;AAAA,EACH;AAEA,EAAAG,aAAY,UAAU,OAAO,SAASD,MAAM,UAAU,YAAY,SAAS;AACzE,QAAI,WAAW;AAEf,QAAI,MAAM;AACV,QAAI,YAAY,IAAI;AACpB,SAAK;AAAA,MACH;AAAA,MACA,SAAU,OAAO;AACf,iBAAS,MAAM,QAAQ;AACvB,qBAAa,SAAS,QAAQ,OAAO,WAAW,KAAK;AACrD,sBAAc,WAAW,KAAK;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,EAAAC,aAAY,UAAU,UAAU,SAAST,SAAS,UAAU,YAAY,SAAS;AAC/E,QAAI,WAAW;AAEf,QAAI,MAAM;AACV,QAAI,YAAY,IAAI;AACpB,SAAK;AAAA,MACH;AAAA,MACA,SAAU,OAAO;AACf,oBAAY,MAAM,QAAQ;AAC1B,qBAAa,SAAS,QAAQ,OAAO,WAAW,KAAK;AACrD,sBAAc,WAAW,KAAK;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,EAAAS,aAAY,UAAU,KAAK,SAASF,IAAI,GAAG;AACzC,WAAO,QAAQ,GAAG,CAAC;AAAA,EACrB;AAEA,EAAAE,aAAY,UAAU,YAAY,SAAS,UAAWD,OAAM;AAC1D,QAAI,UAAU,KAAK,QAAQ;AAC3B,QAAI,QAAQ,MAAM,SAAS;AACzB,MAAAA,QAAO,SAAS,OAAO,IAAI,YAAY,OAAO;AAAA,IAChD;AAAA,EACF;AAEA,EAAAC,aAAY,UAAU,qBAAqB,SAAS,qBAAsB;AACxE,WAAO,QAAQ;AAAA,EACjB;AAEA,SAAOA;AACT,EAAE,OAAO;AAET,SAAS,cAAe,MAAM;AAC5B,MAAI,WAAW,YAAY,IAAI;AAC/B,MAAI,CAAC,OAAO,KAAK,QAAQ,GAAG;AAC1B,WAAO,SAAS,QAAQ,UAAU,OAAO,OAAO,QAAQ,CAAC;AACzD,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAe;AACtB,MAAI,OAAO,QAAQ;AACnB,MAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,WAAO;AAAA,EACT;AACA,cAAY,MAAM,IAAI;AACtB,SAAO;AACT;AAEA,SAAS,UAAW;AAGlB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,QAAQ,KAAK,QAAQ,GAAG;AAE5B,MAAI,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAG;AAE3B,SAAO,KAAK,MAAM,QAAQ,CAAC;AAE3B,SAAO;AACT;AAEA,SAAS,OAAQ,MAAM;AACrB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,IAAI,KAAK,QAAQ,GAAG;AACxB,MAAI,OAAO,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI;AACvC,SAAQ,OAAO,MAAM;AACvB;AAEA,SAAS,SAAU,MAAM;AACvB,MAAI,mBAAmB;AACrB,cAAU,OAAO,IAAI,CAAC;AAAA,EACxB,OAAO;AACL,WAAO,SAAS,OAAO;AAAA,EACzB;AACF;AAEA,SAAS,YAAa,MAAM;AAC1B,MAAI,mBAAmB;AACrB,iBAAa,OAAO,IAAI,CAAC;AAAA,EAC3B,OAAO;AACL,WAAO,SAAS,QAAQ,OAAO,IAAI,CAAC;AAAA,EACtC;AACF;AAIA,IAAI,kBAAgC,SAAUP,UAAS;AACrD,WAASQ,iBAAiB,QAAQ,MAAM;AACtC,IAAAR,SAAQ,KAAK,MAAM,QAAQ,IAAI;AAC/B,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ;AAAA,EACf;AAEA,MAAKA,SAAU,CAAAQ,iBAAgB,YAAYR;AAC3C,EAAAQ,iBAAgB,YAAY,OAAO,OAAQR,YAAWA,SAAQ,SAAU;AACxE,EAAAQ,iBAAgB,UAAU,cAAcA;AAExC,EAAAA,iBAAgB,UAAU,OAAO,SAASF,MAAM,UAAU,YAAY,SAAS;AAC7E,QAAI,WAAW;AAEf,SAAK;AAAA,MACH;AAAA,MACA,SAAU,OAAO;AACf,iBAAS,QAAQ,SAAS,MAAM,MAAM,GAAG,SAAS,QAAQ,CAAC,EAAE,OAAO,KAAK;AACzE,iBAAS;AACT,sBAAc,WAAW,KAAK;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,EAAAE,iBAAgB,UAAU,UAAU,SAASV,SAAS,UAAU,YAAY,SAAS;AACnF,QAAI,WAAW;AAEf,SAAK;AAAA,MACH;AAAA,MACA,SAAU,OAAO;AACf,iBAAS,QAAQ,SAAS,MAAM,MAAM,GAAG,SAAS,KAAK,EAAE,OAAO,KAAK;AACrE,sBAAc,WAAW,KAAK;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,EAAAU,iBAAgB,UAAU,KAAK,SAASH,IAAI,GAAG;AAC7C,QAAI,WAAW;AAEf,QAAI,cAAc,KAAK,QAAQ;AAC/B,QAAI,cAAc,KAAK,eAAe,KAAK,MAAM,QAAQ;AACvD;AAAA,IACF;AACA,QAAI,QAAQ,KAAK,MAAM,WAAW;AAClC,SAAK;AAAA,MACH;AAAA,MACA,WAAY;AACV,YAAI,OAAO,SAAS;AACpB,iBAAS,QAAQ;AACjB,iBAAS,YAAY,KAAK;AAC1B,iBAAS,OAAO,WAAW,QAAQ,SAAU,MAAM;AACjD,kBAAQ,KAAK,OAAO,IAAI;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MACA,SAAU,KAAK;AACb,YAAI,oBAAoB,KAAK,sBAAsB,UAAU,GAAG;AAC9D,mBAAS,QAAQ;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,EAAAG,iBAAgB,UAAU,qBAAqB,SAAS,qBAAsB;AAC5E,QAAI,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC9C,WAAO,UAAU,QAAQ,WAAW;AAAA,EACtC;AAEA,EAAAA,iBAAgB,UAAU,YAAY,SAAS,YAAa;AAAA,EAE5D;AAEA,SAAOA;AACT,EAAE,OAAO;AAMT,IAAI,YAAY,SAASC,WAAW,SAAS;AAC3C,MAAK,YAAY,OAAS,WAAU,CAAC;AAErC,MAAI,MAAuC;AACzC,SAAK,gBAAgBA,YAAW,8CAA8C;AAAA,EAChF;AACA,OAAK,MAAM;AACX,OAAK,OAAO,CAAC;AACb,OAAK,UAAU;AACf,OAAK,cAAc,CAAC;AACpB,OAAK,eAAe,CAAC;AACrB,OAAK,aAAa,CAAC;AACnB,OAAK,UAAU,cAAc,QAAQ,UAAU,CAAC,GAAG,IAAI;AAEvD,MAAI,OAAO,QAAQ,QAAQ;AAC3B,OAAK,WACH,SAAS,aAAa,CAAC,qBAAqB,QAAQ,aAAa;AACnE,MAAI,KAAK,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,OAAK,OAAO;AAEZ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,WAAK,UAAU,IAAI,aAAa,MAAM,QAAQ,IAAI;AAClD;AAAA,IACF,KAAK;AACH,WAAK,UAAU,IAAI,YAAY,MAAM,QAAQ,MAAM,KAAK,QAAQ;AAChE;AAAA,IACF,KAAK;AACH,WAAK,UAAU,IAAI,gBAAgB,MAAM,QAAQ,IAAI;AACrD;AAAA,IACF;AACE,UAAI,MAAuC;AACzC,eAAO,OAAQ,mBAAmB,IAAK;AAAA,MACzC;AAAA,EACJ;AACF;AAEA,IAAI,qBAAqB,EAAE,cAAc,EAAE,cAAc,KAAK,EAAE;AAEhE,UAAU,UAAU,QAAQ,SAAS,MAAO,KAAK,SAAS,gBAAgB;AACxE,SAAO,KAAK,QAAQ,MAAM,KAAK,SAAS,cAAc;AACxD;AAEA,mBAAmB,aAAa,MAAM,WAAY;AAChD,SAAO,KAAK,WAAW,KAAK,QAAQ;AACtC;AAEA,UAAU,UAAU,OAAO,SAAS,KAAM,KAAkC;AACxE,MAAI,WAAW;AAEjB,EACE;AAAA,IACE,QAAQ;AAAA,IACR;AAAA,EAEF;AAEF,OAAK,KAAK,KAAK,GAAG;AAIlB,MAAI,MAAM,kBAAkB,WAAY;AAEtC,QAAI,QAAQ,SAAS,KAAK,QAAQ,GAAG;AACrC,QAAI,QAAQ,IAAI;AAAE,eAAS,KAAK,OAAO,OAAO,CAAC;AAAA,IAAG;AAGlD,QAAI,SAAS,QAAQ,KAAK;AAAE,eAAS,MAAM,SAAS,KAAK,CAAC,KAAK;AAAA,IAAM;AAErE,QAAI,CAAC,SAAS,KAAK;AAAE,eAAS,QAAQ,SAAS;AAAA,IAAG;AAAA,EACpD,CAAC;AAID,MAAI,KAAK,KAAK;AACZ;AAAA,EACF;AAEA,OAAK,MAAM;AAEX,MAAI,UAAU,KAAK;AAEnB,MAAI,mBAAmB,gBAAgB,mBAAmB,aAAa;AACrE,QAAI,sBAAsB,SAAU,cAAc;AAChD,UAAI,OAAO,QAAQ;AACnB,UAAI,eAAe,SAAS,QAAQ;AACpC,UAAI,iBAAiB,qBAAqB;AAE1C,UAAI,kBAAkB,cAAc,cAAc;AAChD,qBAAa,UAAU,cAAc,MAAM,KAAK;AAAA,MAClD;AAAA,IACF;AACA,QAAIL,kBAAiB,SAAU,cAAc;AAC3C,cAAQ,eAAe;AACvB,0BAAoB,YAAY;AAAA,IAClC;AACA,YAAQ;AAAA,MACN,QAAQ,mBAAmB;AAAA,MAC3BA;AAAA,MACAA;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,OAAO,SAAU,OAAO;AAC9B,aAAS,KAAK,QAAQ,SAAUM,MAAK;AACnC,MAAAA,KAAI,SAAS;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AACH;AAEA,UAAU,UAAU,aAAa,SAAS,WAAY,IAAI;AACxD,SAAO,aAAa,KAAK,aAAa,EAAE;AAC1C;AAEA,UAAU,UAAU,gBAAgB,SAAS,cAAe,IAAI;AAC9D,SAAO,aAAa,KAAK,cAAc,EAAE;AAC3C;AAEA,UAAU,UAAU,YAAY,SAAS,UAAW,IAAI;AACtD,SAAO,aAAa,KAAK,YAAY,EAAE;AACzC;AAEA,UAAU,UAAU,UAAU,SAASC,SAAS,IAAI,SAAS;AAC3D,OAAK,QAAQ,QAAQ,IAAI,OAAO;AAClC;AAEA,UAAU,UAAU,UAAU,SAASC,SAAS,SAAS;AACvD,OAAK,QAAQ,QAAQ,OAAO;AAC9B;AAEA,UAAU,UAAU,OAAO,SAAS,KAAM,UAAU,YAAY,SAAS;AACrE,MAAI,WAAW;AAGjB,MAAI,CAAC,cAAc,CAAC,WAAW,OAAO,YAAY,aAAa;AAC7D,WAAO,IAAI,QAAQ,SAAUb,UAAS,QAAQ;AAC5C,eAAS,QAAQ,KAAK,UAAUA,UAAS,MAAM;AAAA,IACjD,CAAC;AAAA,EACH,OAAO;AACL,SAAK,QAAQ,KAAK,UAAU,YAAY,OAAO;AAAA,EACjD;AACF;AAEA,UAAU,UAAU,UAAU,SAAS,QAAS,UAAU,YAAY,SAAS;AAC3E,MAAI,WAAW;AAGjB,MAAI,CAAC,cAAc,CAAC,WAAW,OAAO,YAAY,aAAa;AAC7D,WAAO,IAAI,QAAQ,SAAUA,UAAS,QAAQ;AAC5C,eAAS,QAAQ,QAAQ,UAAUA,UAAS,MAAM;AAAA,IACpD,CAAC;AAAA,EACH,OAAO;AACL,SAAK,QAAQ,QAAQ,UAAU,YAAY,OAAO;AAAA,EACpD;AACF;AAEA,UAAU,UAAU,KAAK,SAAS,GAAI,GAAG;AACvC,OAAK,QAAQ,GAAG,CAAC;AACnB;AAEA,UAAU,UAAU,OAAO,SAAS,OAAQ;AAC1C,OAAK,GAAG,EAAE;AACZ;AAEA,UAAU,UAAU,UAAU,SAAS,UAAW;AAChD,OAAK,GAAG,CAAC;AACX;AAEA,UAAU,UAAU,uBAAuB,SAAS,qBAAsB,IAAI;AAC5E,MAAI,QAAQ,KACR,GAAG,UACD,KACA,KAAK,QAAQ,EAAE,EAAE,QACnB,KAAK;AACT,MAAI,CAAC,OAAO;AACV,WAAO,CAAC;AAAA,EACV;AACA,SAAO,CAAC,EAAE,OAAO;AAAA,IACf,CAAC;AAAA,IACD,MAAM,QAAQ,IAAI,SAAU,GAAG;AAC7B,aAAO,OAAO,KAAK,EAAE,UAAU,EAAE,IAAI,SAAU,KAAK;AAClD,eAAO,EAAE,WAAW,GAAG;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAEA,UAAU,UAAU,UAAU,SAAS,QACrC,IACA,SACA,QACA;AACA,YAAU,WAAW,KAAK,QAAQ;AAClC,MAAI,WAAW,kBAAkB,IAAI,SAAS,QAAQ,IAAI;AAC1D,MAAI,QAAQ,KAAK,MAAM,UAAU,OAAO;AACxC,MAAI,WAAW,MAAM,kBAAkB,MAAM;AAC7C,MAAI,OAAO,KAAK,QAAQ;AACxB,MAAI,OAAO,WAAW,MAAM,UAAU,KAAK,IAAI;AAC/C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,cAAc;AAAA,IACd,UAAU;AAAA,EACZ;AACF;AAEA,UAAU,UAAU,YAAY,SAAS,YAAa;AACpD,SAAO,KAAK,QAAQ,UAAU;AAChC;AAEA,UAAU,UAAU,WAAW,SAAS,SAAU,eAAe,OAAO;AACtE,OAAK,QAAQ,SAAS,eAAe,KAAK;AAC1C,MAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,SAAK,QAAQ,aAAa,KAAK,QAAQ,mBAAmB,CAAC;AAAA,EAC7D;AACF;AAEA,UAAU,UAAU,YAAY,SAAS,UAAW,QAAQ;AAC1D,MAAI,MAAuC;AACzC,SAAK,OAAO,uGAAuG;AAAA,EACrH;AACA,OAAK,QAAQ,UAAU,MAAM;AAC7B,MAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,SAAK,QAAQ,aAAa,KAAK,QAAQ,mBAAmB,CAAC;AAAA,EAC7D;AACF;AAEA,OAAO,iBAAkB,UAAU,WAAW,kBAAmB;AAEjE,IAAI,cAAc;AAElB,SAAS,aAAc,MAAM,IAAI;AAC/B,OAAK,KAAK,EAAE;AACZ,SAAO,WAAY;AACjB,QAAI,IAAI,KAAK,QAAQ,EAAE;AACvB,QAAI,IAAI,IAAI;AAAE,WAAK,OAAO,GAAG,CAAC;AAAA,IAAG;AAAA,EACnC;AACF;AAEA,SAAS,WAAY,MAAM,UAAU,MAAM;AACzC,MAAI,OAAO,SAAS,SAAS,MAAM,WAAW;AAC9C,SAAO,OAAO,UAAU,OAAO,MAAM,IAAI,IAAI;AAC/C;AAGA,UAAU,UAAU;AACpB,UAAU,UAAU;AACpB,UAAU,sBAAsB;AAChC,UAAU,wBAAwB;AAClC,UAAU,iBAAiB;AAE3B,IAAI,aAAa,OAAO,KAAK;AAC3B,SAAO,IAAI,IAAI,SAAS;AAC1B;AAEA,IAAI,UAAU;", "names": ["parse", "stringifyQuery", "_", "options", "encode", "i", "render", "addRoutes", "routes", "addRoute", "alias", "getRoutes", "match", "key", "redirect", "shouldScroll", "replace", "resolve", "History", "queue", "guard", "HTML5History", "setupListeners", "go", "push", "HashHistory", "AbstractHistory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app", "onReady", "onError"]}