import {
  addYears
} from "./chunk-WKVP7Z2L.js";
import "./chunk-7Q5JNRGT.js";
import {
  toInteger
} from "./chunk-SQMTRHET.js";
import "./chunk-HUOGXHLH.js";
import {
  requiredArgs
} from "./chunk-Z43A42SM.js";
import "./chunk-PLDDJCW6.js";

// node_modules/date-fns/esm/subYears/index.js
function subYears(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var amount = toInteger(dirtyAmount);
  return addYears(dirtyDate, -amount);
}
export {
  subYears as default
};
//# sourceMappingURL=date-fns_subYears.js.map
