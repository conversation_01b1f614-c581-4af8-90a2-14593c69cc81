{"version": 3, "sources": ["../../lodash/chunk.js"], "sourcesContent": ["var baseSlice = require('./_baseSlice'),\n    isIterateeCall = require('./_isIterateeCall'),\n    toInteger = require('./toInteger');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * Creates an array of elements split into groups the length of `size`.\n * If `array` can't be split evenly, the final chunk will be the remaining\n * elements.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to process.\n * @param {number} [size=1] The length of each chunk\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the new array of chunks.\n * @example\n *\n * _.chunk(['a', 'b', 'c', 'd'], 2);\n * // => [['a', 'b'], ['c', 'd']]\n *\n * _.chunk(['a', 'b', 'c', 'd'], 3);\n * // => [['a', 'b', 'c'], ['d']]\n */\nfunction chunk(array, size, guard) {\n  if ((guard ? isIterateeCall(array, size, guard) : size === undefined)) {\n    size = 1;\n  } else {\n    size = nativeMax(toInteger(size), 0);\n  }\n  var length = array == null ? 0 : array.length;\n  if (!length || size < 1) {\n    return [];\n  }\n  var index = 0,\n      resIndex = 0,\n      result = Array(nativeCeil(length / size));\n\n  while (index < length) {\n    result[resIndex++] = baseSlice(array, index, (index += size));\n  }\n  return result;\n}\n\nmodule.exports = chunk;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,iBAAiB;AADrB,QAEI,YAAY;AAGhB,QAAI,aAAa,KAAK;AAAtB,QACI,YAAY,KAAK;AAuBrB,aAAS,MAAM,OAAO,MAAM,OAAO;AACjC,UAAK,QAAQ,eAAe,OAAO,MAAM,KAAK,IAAI,SAAS,QAAY;AACrE,eAAO;AAAA,MACT,OAAO;AACL,eAAO,UAAU,UAAU,IAAI,GAAG,CAAC;AAAA,MACrC;AACA,UAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,UAAI,CAAC,UAAU,OAAO,GAAG;AACvB,eAAO,CAAC;AAAA,MACV;AACA,UAAI,QAAQ,GACR,WAAW,GACX,SAAS,MAAM,WAAW,SAAS,IAAI,CAAC;AAE5C,aAAO,QAAQ,QAAQ;AACrB,eAAO,UAAU,IAAI,UAAU,OAAO,OAAQ,SAAS,IAAK;AAAA,MAC9D;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}