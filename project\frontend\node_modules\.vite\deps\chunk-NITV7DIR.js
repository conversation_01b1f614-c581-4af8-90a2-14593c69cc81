import {
  __esm
} from "./chunk-PLDDJCW6.js";

// node_modules/whatwg-fetch/fetch.js
function isDataView(obj) {
  return obj && DataView.prototype.isPrototypeOf(obj);
}
function normalizeName(name) {
  if (typeof name !== "string") {
    name = String(name);
  }
  if (/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(name) || name === "") {
    throw new TypeError('Invalid character in header field name: "' + name + '"');
  }
  return name.toLowerCase();
}
function normalizeValue(value) {
  if (typeof value !== "string") {
    value = String(value);
  }
  return value;
}
function iteratorFor(items) {
  var iterator = {
    next: function() {
      var value = items.shift();
      return { done: value === void 0, value };
    }
  };
  if (support.iterable) {
    iterator[Symbol.iterator] = function() {
      return iterator;
    };
  }
  return iterator;
}
function Headers(headers) {
  this.map = {};
  if (headers instanceof Headers) {
    headers.forEach(function(value, name) {
      this.append(name, value);
    }, this);
  } else if (Array.isArray(headers)) {
    headers.forEach(function(header) {
      if (header.length != 2) {
        throw new TypeError("Headers constructor: expected name/value pair to be length 2, found" + header.length);
      }
      this.append(header[0], header[1]);
    }, this);
  } else if (headers) {
    Object.getOwnPropertyNames(headers).forEach(function(name) {
      this.append(name, headers[name]);
    }, this);
  }
}
function consumed(body) {
  if (body._noBody) return;
  if (body.bodyUsed) {
    return Promise.reject(new TypeError("Already read"));
  }
  body.bodyUsed = true;
}
function fileReaderReady(reader) {
  return new Promise(function(resolve, reject) {
    reader.onload = function() {
      resolve(reader.result);
    };
    reader.onerror = function() {
      reject(reader.error);
    };
  });
}
function readBlobAsArrayBuffer(blob) {
  var reader = new FileReader();
  var promise = fileReaderReady(reader);
  reader.readAsArrayBuffer(blob);
  return promise;
}
function readBlobAsText(blob) {
  var reader = new FileReader();
  var promise = fileReaderReady(reader);
  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type);
  var encoding = match ? match[1] : "utf-8";
  reader.readAsText(blob, encoding);
  return promise;
}
function readArrayBufferAsText(buf) {
  var view = new Uint8Array(buf);
  var chars = new Array(view.length);
  for (var i = 0; i < view.length; i++) {
    chars[i] = String.fromCharCode(view[i]);
  }
  return chars.join("");
}
function bufferClone(buf) {
  if (buf.slice) {
    return buf.slice(0);
  } else {
    var view = new Uint8Array(buf.byteLength);
    view.set(new Uint8Array(buf));
    return view.buffer;
  }
}
function Body() {
  this.bodyUsed = false;
  this._initBody = function(body) {
    this.bodyUsed = this.bodyUsed;
    this._bodyInit = body;
    if (!body) {
      this._noBody = true;
      this._bodyText = "";
    } else if (typeof body === "string") {
      this._bodyText = body;
    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {
      this._bodyBlob = body;
    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {
      this._bodyFormData = body;
    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {
      this._bodyText = body.toString();
    } else if (support.arrayBuffer && support.blob && isDataView(body)) {
      this._bodyArrayBuffer = bufferClone(body.buffer);
      this._bodyInit = new Blob([this._bodyArrayBuffer]);
    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {
      this._bodyArrayBuffer = bufferClone(body);
    } else {
      this._bodyText = body = Object.prototype.toString.call(body);
    }
    if (!this.headers.get("content-type")) {
      if (typeof body === "string") {
        this.headers.set("content-type", "text/plain;charset=UTF-8");
      } else if (this._bodyBlob && this._bodyBlob.type) {
        this.headers.set("content-type", this._bodyBlob.type);
      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {
        this.headers.set("content-type", "application/x-www-form-urlencoded;charset=UTF-8");
      }
    }
  };
  if (support.blob) {
    this.blob = function() {
      var rejected = consumed(this);
      if (rejected) {
        return rejected;
      }
      if (this._bodyBlob) {
        return Promise.resolve(this._bodyBlob);
      } else if (this._bodyArrayBuffer) {
        return Promise.resolve(new Blob([this._bodyArrayBuffer]));
      } else if (this._bodyFormData) {
        throw new Error("could not read FormData body as blob");
      } else {
        return Promise.resolve(new Blob([this._bodyText]));
      }
    };
  }
  this.arrayBuffer = function() {
    if (this._bodyArrayBuffer) {
      var isConsumed = consumed(this);
      if (isConsumed) {
        return isConsumed;
      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {
        return Promise.resolve(
          this._bodyArrayBuffer.buffer.slice(
            this._bodyArrayBuffer.byteOffset,
            this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength
          )
        );
      } else {
        return Promise.resolve(this._bodyArrayBuffer);
      }
    } else if (support.blob) {
      return this.blob().then(readBlobAsArrayBuffer);
    } else {
      throw new Error("could not read as ArrayBuffer");
    }
  };
  this.text = function() {
    var rejected = consumed(this);
    if (rejected) {
      return rejected;
    }
    if (this._bodyBlob) {
      return readBlobAsText(this._bodyBlob);
    } else if (this._bodyArrayBuffer) {
      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer));
    } else if (this._bodyFormData) {
      throw new Error("could not read FormData body as text");
    } else {
      return Promise.resolve(this._bodyText);
    }
  };
  if (support.formData) {
    this.formData = function() {
      return this.text().then(decode);
    };
  }
  this.json = function() {
    return this.text().then(JSON.parse);
  };
  return this;
}
function normalizeMethod(method) {
  var upcased = method.toUpperCase();
  return methods.indexOf(upcased) > -1 ? upcased : method;
}
function Request(input, options) {
  if (!(this instanceof Request)) {
    throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');
  }
  options = options || {};
  var body = options.body;
  if (input instanceof Request) {
    if (input.bodyUsed) {
      throw new TypeError("Already read");
    }
    this.url = input.url;
    this.credentials = input.credentials;
    if (!options.headers) {
      this.headers = new Headers(input.headers);
    }
    this.method = input.method;
    this.mode = input.mode;
    this.signal = input.signal;
    if (!body && input._bodyInit != null) {
      body = input._bodyInit;
      input.bodyUsed = true;
    }
  } else {
    this.url = String(input);
  }
  this.credentials = options.credentials || this.credentials || "same-origin";
  if (options.headers || !this.headers) {
    this.headers = new Headers(options.headers);
  }
  this.method = normalizeMethod(options.method || this.method || "GET");
  this.mode = options.mode || this.mode || null;
  this.signal = options.signal || this.signal || function() {
    if ("AbortController" in g) {
      var ctrl = new AbortController();
      return ctrl.signal;
    }
  }();
  this.referrer = null;
  if ((this.method === "GET" || this.method === "HEAD") && body) {
    throw new TypeError("Body not allowed for GET or HEAD requests");
  }
  this._initBody(body);
  if (this.method === "GET" || this.method === "HEAD") {
    if (options.cache === "no-store" || options.cache === "no-cache") {
      var reParamSearch = /([?&])_=[^&]*/;
      if (reParamSearch.test(this.url)) {
        this.url = this.url.replace(reParamSearch, "$1_=" + (/* @__PURE__ */ new Date()).getTime());
      } else {
        var reQueryString = /\?/;
        this.url += (reQueryString.test(this.url) ? "&" : "?") + "_=" + (/* @__PURE__ */ new Date()).getTime();
      }
    }
  }
}
function decode(body) {
  var form = new FormData();
  body.trim().split("&").forEach(function(bytes) {
    if (bytes) {
      var split = bytes.split("=");
      var name = split.shift().replace(/\+/g, " ");
      var value = split.join("=").replace(/\+/g, " ");
      form.append(decodeURIComponent(name), decodeURIComponent(value));
    }
  });
  return form;
}
function parseHeaders(rawHeaders) {
  var headers = new Headers();
  var preProcessedHeaders = rawHeaders.replace(/\r?\n[\t ]+/g, " ");
  preProcessedHeaders.split("\r").map(function(header) {
    return header.indexOf("\n") === 0 ? header.substr(1, header.length) : header;
  }).forEach(function(line) {
    var parts = line.split(":");
    var key = parts.shift().trim();
    if (key) {
      var value = parts.join(":").trim();
      try {
        headers.append(key, value);
      } catch (error) {
        console.warn("Response " + error.message);
      }
    }
  });
  return headers;
}
function Response(bodyInit, options) {
  if (!(this instanceof Response)) {
    throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');
  }
  if (!options) {
    options = {};
  }
  this.type = "default";
  this.status = options.status === void 0 ? 200 : options.status;
  if (this.status < 200 || this.status > 599) {
    throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");
  }
  this.ok = this.status >= 200 && this.status < 300;
  this.statusText = options.statusText === void 0 ? "" : "" + options.statusText;
  this.headers = new Headers(options.headers);
  this.url = options.url || "";
  this._initBody(bodyInit);
}
function fetch(input, init) {
  return new Promise(function(resolve, reject) {
    var request = new Request(input, init);
    if (request.signal && request.signal.aborted) {
      return reject(new DOMException("Aborted", "AbortError"));
    }
    var xhr = new XMLHttpRequest();
    function abortXhr() {
      xhr.abort();
    }
    xhr.onload = function() {
      var options = {
        statusText: xhr.statusText,
        headers: parseHeaders(xhr.getAllResponseHeaders() || "")
      };
      if (request.url.indexOf("file://") === 0 && (xhr.status < 200 || xhr.status > 599)) {
        options.status = 200;
      } else {
        options.status = xhr.status;
      }
      options.url = "responseURL" in xhr ? xhr.responseURL : options.headers.get("X-Request-URL");
      var body = "response" in xhr ? xhr.response : xhr.responseText;
      setTimeout(function() {
        resolve(new Response(body, options));
      }, 0);
    };
    xhr.onerror = function() {
      setTimeout(function() {
        reject(new TypeError("Network request failed"));
      }, 0);
    };
    xhr.ontimeout = function() {
      setTimeout(function() {
        reject(new TypeError("Network request timed out"));
      }, 0);
    };
    xhr.onabort = function() {
      setTimeout(function() {
        reject(new DOMException("Aborted", "AbortError"));
      }, 0);
    };
    function fixUrl(url) {
      try {
        return url === "" && g.location.href ? g.location.href : url;
      } catch (e) {
        return url;
      }
    }
    xhr.open(request.method, fixUrl(request.url), true);
    if (request.credentials === "include") {
      xhr.withCredentials = true;
    } else if (request.credentials === "omit") {
      xhr.withCredentials = false;
    }
    if ("responseType" in xhr) {
      if (support.blob) {
        xhr.responseType = "blob";
      } else if (support.arrayBuffer) {
        xhr.responseType = "arraybuffer";
      }
    }
    if (init && typeof init.headers === "object" && !(init.headers instanceof Headers || g.Headers && init.headers instanceof g.Headers)) {
      var names = [];
      Object.getOwnPropertyNames(init.headers).forEach(function(name) {
        names.push(normalizeName(name));
        xhr.setRequestHeader(name, normalizeValue(init.headers[name]));
      });
      request.headers.forEach(function(value, name) {
        if (names.indexOf(name) === -1) {
          xhr.setRequestHeader(name, value);
        }
      });
    } else {
      request.headers.forEach(function(value, name) {
        xhr.setRequestHeader(name, value);
      });
    }
    if (request.signal) {
      request.signal.addEventListener("abort", abortXhr);
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          request.signal.removeEventListener("abort", abortXhr);
        }
      };
    }
    xhr.send(typeof request._bodyInit === "undefined" ? null : request._bodyInit);
  });
}
var g, support, viewClasses, isArrayBufferView, methods, redirectStatuses, DOMException;
var init_fetch = __esm({
  "node_modules/whatwg-fetch/fetch.js"() {
    g = typeof globalThis !== "undefined" && globalThis || typeof self !== "undefined" && self || // eslint-disable-next-line no-undef
    typeof global !== "undefined" && global || {};
    support = {
      searchParams: "URLSearchParams" in g,
      iterable: "Symbol" in g && "iterator" in Symbol,
      blob: "FileReader" in g && "Blob" in g && function() {
        try {
          new Blob();
          return true;
        } catch (e) {
          return false;
        }
      }(),
      formData: "FormData" in g,
      arrayBuffer: "ArrayBuffer" in g
    };
    if (support.arrayBuffer) {
      viewClasses = [
        "[object Int8Array]",
        "[object Uint8Array]",
        "[object Uint8ClampedArray]",
        "[object Int16Array]",
        "[object Uint16Array]",
        "[object Int32Array]",
        "[object Uint32Array]",
        "[object Float32Array]",
        "[object Float64Array]"
      ];
      isArrayBufferView = ArrayBuffer.isView || function(obj) {
        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1;
      };
    }
    Headers.prototype.append = function(name, value) {
      name = normalizeName(name);
      value = normalizeValue(value);
      var oldValue = this.map[name];
      this.map[name] = oldValue ? oldValue + ", " + value : value;
    };
    Headers.prototype["delete"] = function(name) {
      delete this.map[normalizeName(name)];
    };
    Headers.prototype.get = function(name) {
      name = normalizeName(name);
      return this.has(name) ? this.map[name] : null;
    };
    Headers.prototype.has = function(name) {
      return this.map.hasOwnProperty(normalizeName(name));
    };
    Headers.prototype.set = function(name, value) {
      this.map[normalizeName(name)] = normalizeValue(value);
    };
    Headers.prototype.forEach = function(callback, thisArg) {
      for (var name in this.map) {
        if (this.map.hasOwnProperty(name)) {
          callback.call(thisArg, this.map[name], name, this);
        }
      }
    };
    Headers.prototype.keys = function() {
      var items = [];
      this.forEach(function(value, name) {
        items.push(name);
      });
      return iteratorFor(items);
    };
    Headers.prototype.values = function() {
      var items = [];
      this.forEach(function(value) {
        items.push(value);
      });
      return iteratorFor(items);
    };
    Headers.prototype.entries = function() {
      var items = [];
      this.forEach(function(value, name) {
        items.push([name, value]);
      });
      return iteratorFor(items);
    };
    if (support.iterable) {
      Headers.prototype[Symbol.iterator] = Headers.prototype.entries;
    }
    methods = ["CONNECT", "DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "TRACE"];
    Request.prototype.clone = function() {
      return new Request(this, { body: this._bodyInit });
    };
    Body.call(Request.prototype);
    Body.call(Response.prototype);
    Response.prototype.clone = function() {
      return new Response(this._bodyInit, {
        status: this.status,
        statusText: this.statusText,
        headers: new Headers(this.headers),
        url: this.url
      });
    };
    Response.error = function() {
      var response = new Response(null, { status: 200, statusText: "" });
      response.ok = false;
      response.status = 0;
      response.type = "error";
      return response;
    };
    redirectStatuses = [301, 302, 303, 307, 308];
    Response.redirect = function(url, status) {
      if (redirectStatuses.indexOf(status) === -1) {
        throw new RangeError("Invalid status code");
      }
      return new Response(null, { status, headers: { location: url } });
    };
    DOMException = g.DOMException;
    try {
      new DOMException();
    } catch (err) {
      DOMException = function(message, name) {
        this.message = message;
        this.name = name;
        var error = Error(message);
        this.stack = error.stack;
      };
      DOMException.prototype = Object.create(Error.prototype);
      DOMException.prototype.constructor = DOMException;
    }
    fetch.polyfill = true;
    if (!g.fetch) {
      g.fetch = fetch;
      g.Headers = Headers;
      g.Request = Request;
      g.Response = Response;
    }
  }
});

// node_modules/intersection-observer/intersection-observer.js
var init_intersection_observer = __esm({
  "node_modules/intersection-observer/intersection-observer.js"() {
    (function() {
      "use strict";
      if (typeof window !== "object") {
        return;
      }
      if ("IntersectionObserver" in window && "IntersectionObserverEntry" in window && "intersectionRatio" in window.IntersectionObserverEntry.prototype) {
        if (!("isIntersecting" in window.IntersectionObserverEntry.prototype)) {
          Object.defineProperty(
            window.IntersectionObserverEntry.prototype,
            "isIntersecting",
            {
              get: function() {
                return this.intersectionRatio > 0;
              }
            }
          );
        }
        return;
      }
      function getFrameElement(doc) {
        try {
          return doc.defaultView && doc.defaultView.frameElement || null;
        } catch (e) {
          return null;
        }
      }
      var document = function(startDoc) {
        var doc = startDoc;
        var frame = getFrameElement(doc);
        while (frame) {
          doc = frame.ownerDocument;
          frame = getFrameElement(doc);
        }
        return doc;
      }(window.document);
      var registry = [];
      var crossOriginUpdater = null;
      var crossOriginRect = null;
      function IntersectionObserverEntry(entry) {
        this.time = entry.time;
        this.target = entry.target;
        this.rootBounds = ensureDOMRect(entry.rootBounds);
        this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);
        this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());
        this.isIntersecting = !!entry.intersectionRect;
        var targetRect = this.boundingClientRect;
        var targetArea = targetRect.width * targetRect.height;
        var intersectionRect = this.intersectionRect;
        var intersectionArea = intersectionRect.width * intersectionRect.height;
        if (targetArea) {
          this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));
        } else {
          this.intersectionRatio = this.isIntersecting ? 1 : 0;
        }
      }
      function IntersectionObserver(callback, opt_options) {
        var options = opt_options || {};
        if (typeof callback != "function") {
          throw new Error("callback must be a function");
        }
        if (options.root && options.root.nodeType != 1 && options.root.nodeType != 9) {
          throw new Error("root must be a Document or Element");
        }
        this._checkForIntersections = throttle(
          this._checkForIntersections.bind(this),
          this.THROTTLE_TIMEOUT
        );
        this._callback = callback;
        this._observationTargets = [];
        this._queuedEntries = [];
        this._rootMarginValues = this._parseRootMargin(options.rootMargin);
        this.thresholds = this._initThresholds(options.threshold);
        this.root = options.root || null;
        this.rootMargin = this._rootMarginValues.map(function(margin) {
          return margin.value + margin.unit;
        }).join(" ");
        this._monitoringDocuments = [];
        this._monitoringUnsubscribes = [];
      }
      IntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;
      IntersectionObserver.prototype.POLL_INTERVAL = null;
      IntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;
      IntersectionObserver._setupCrossOriginUpdater = function() {
        if (!crossOriginUpdater) {
          crossOriginUpdater = function(boundingClientRect, intersectionRect) {
            if (!boundingClientRect || !intersectionRect) {
              crossOriginRect = getEmptyRect();
            } else {
              crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);
            }
            registry.forEach(function(observer) {
              observer._checkForIntersections();
            });
          };
        }
        return crossOriginUpdater;
      };
      IntersectionObserver._resetCrossOriginUpdater = function() {
        crossOriginUpdater = null;
        crossOriginRect = null;
      };
      IntersectionObserver.prototype.observe = function(target) {
        var isTargetAlreadyObserved = this._observationTargets.some(function(item) {
          return item.element == target;
        });
        if (isTargetAlreadyObserved) {
          return;
        }
        if (!(target && target.nodeType == 1)) {
          throw new Error("target must be an Element");
        }
        this._registerInstance();
        this._observationTargets.push({ element: target, entry: null });
        this._monitorIntersections(target.ownerDocument);
        this._checkForIntersections();
      };
      IntersectionObserver.prototype.unobserve = function(target) {
        this._observationTargets = this._observationTargets.filter(function(item) {
          return item.element != target;
        });
        this._unmonitorIntersections(target.ownerDocument);
        if (this._observationTargets.length == 0) {
          this._unregisterInstance();
        }
      };
      IntersectionObserver.prototype.disconnect = function() {
        this._observationTargets = [];
        this._unmonitorAllIntersections();
        this._unregisterInstance();
      };
      IntersectionObserver.prototype.takeRecords = function() {
        var records = this._queuedEntries.slice();
        this._queuedEntries = [];
        return records;
      };
      IntersectionObserver.prototype._initThresholds = function(opt_threshold) {
        var threshold = opt_threshold || [0];
        if (!Array.isArray(threshold)) threshold = [threshold];
        return threshold.sort().filter(function(t, i, a) {
          if (typeof t != "number" || isNaN(t) || t < 0 || t > 1) {
            throw new Error("threshold must be a number between 0 and 1 inclusively");
          }
          return t !== a[i - 1];
        });
      };
      IntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {
        var marginString = opt_rootMargin || "0px";
        var margins = marginString.split(/\s+/).map(function(margin) {
          var parts = /^(-?\d*\.?\d+)(px|%)$/.exec(margin);
          if (!parts) {
            throw new Error("rootMargin must be specified in pixels or percent");
          }
          return { value: parseFloat(parts[1]), unit: parts[2] };
        });
        margins[1] = margins[1] || margins[0];
        margins[2] = margins[2] || margins[0];
        margins[3] = margins[3] || margins[1];
        return margins;
      };
      IntersectionObserver.prototype._monitorIntersections = function(doc) {
        var win = doc.defaultView;
        if (!win) {
          return;
        }
        if (this._monitoringDocuments.indexOf(doc) != -1) {
          return;
        }
        var callback = this._checkForIntersections;
        var monitoringInterval = null;
        var domObserver = null;
        if (this.POLL_INTERVAL) {
          monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);
        } else {
          addEvent(win, "resize", callback, true);
          addEvent(doc, "scroll", callback, true);
          if (this.USE_MUTATION_OBSERVER && "MutationObserver" in win) {
            domObserver = new win.MutationObserver(callback);
            domObserver.observe(doc, {
              attributes: true,
              childList: true,
              characterData: true,
              subtree: true
            });
          }
        }
        this._monitoringDocuments.push(doc);
        this._monitoringUnsubscribes.push(function() {
          var win2 = doc.defaultView;
          if (win2) {
            if (monitoringInterval) {
              win2.clearInterval(monitoringInterval);
            }
            removeEvent(win2, "resize", callback, true);
          }
          removeEvent(doc, "scroll", callback, true);
          if (domObserver) {
            domObserver.disconnect();
          }
        });
        var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;
        if (doc != rootDoc) {
          var frame = getFrameElement(doc);
          if (frame) {
            this._monitorIntersections(frame.ownerDocument);
          }
        }
      };
      IntersectionObserver.prototype._unmonitorIntersections = function(doc) {
        var index = this._monitoringDocuments.indexOf(doc);
        if (index == -1) {
          return;
        }
        var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;
        var hasDependentTargets = this._observationTargets.some(function(item) {
          var itemDoc = item.element.ownerDocument;
          if (itemDoc == doc) {
            return true;
          }
          while (itemDoc && itemDoc != rootDoc) {
            var frame2 = getFrameElement(itemDoc);
            itemDoc = frame2 && frame2.ownerDocument;
            if (itemDoc == doc) {
              return true;
            }
          }
          return false;
        });
        if (hasDependentTargets) {
          return;
        }
        var unsubscribe = this._monitoringUnsubscribes[index];
        this._monitoringDocuments.splice(index, 1);
        this._monitoringUnsubscribes.splice(index, 1);
        unsubscribe();
        if (doc != rootDoc) {
          var frame = getFrameElement(doc);
          if (frame) {
            this._unmonitorIntersections(frame.ownerDocument);
          }
        }
      };
      IntersectionObserver.prototype._unmonitorAllIntersections = function() {
        var unsubscribes = this._monitoringUnsubscribes.slice(0);
        this._monitoringDocuments.length = 0;
        this._monitoringUnsubscribes.length = 0;
        for (var i = 0; i < unsubscribes.length; i++) {
          unsubscribes[i]();
        }
      };
      IntersectionObserver.prototype._checkForIntersections = function() {
        if (!this.root && crossOriginUpdater && !crossOriginRect) {
          return;
        }
        var rootIsInDom = this._rootIsInDom();
        var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();
        this._observationTargets.forEach(function(item) {
          var target = item.element;
          var targetRect = getBoundingClientRect(target);
          var rootContainsTarget = this._rootContainsTarget(target);
          var oldEntry = item.entry;
          var intersectionRect = rootIsInDom && rootContainsTarget && this._computeTargetAndRootIntersection(target, targetRect, rootRect);
          var rootBounds = null;
          if (!this._rootContainsTarget(target)) {
            rootBounds = getEmptyRect();
          } else if (!crossOriginUpdater || this.root) {
            rootBounds = rootRect;
          }
          var newEntry = item.entry = new IntersectionObserverEntry({
            time: now(),
            target,
            boundingClientRect: targetRect,
            rootBounds,
            intersectionRect
          });
          if (!oldEntry) {
            this._queuedEntries.push(newEntry);
          } else if (rootIsInDom && rootContainsTarget) {
            if (this._hasCrossedThreshold(oldEntry, newEntry)) {
              this._queuedEntries.push(newEntry);
            }
          } else {
            if (oldEntry && oldEntry.isIntersecting) {
              this._queuedEntries.push(newEntry);
            }
          }
        }, this);
        if (this._queuedEntries.length) {
          this._callback(this.takeRecords(), this);
        }
      };
      IntersectionObserver.prototype._computeTargetAndRootIntersection = function(target, targetRect, rootRect) {
        if (window.getComputedStyle(target).display == "none") return;
        var intersectionRect = targetRect;
        var parent = getParentNode(target);
        var atRoot = false;
        while (!atRoot && parent) {
          var parentRect = null;
          var parentComputedStyle = parent.nodeType == 1 ? window.getComputedStyle(parent) : {};
          if (parentComputedStyle.display == "none") return null;
          if (parent == this.root || parent.nodeType == /* DOCUMENT */
          9) {
            atRoot = true;
            if (parent == this.root || parent == document) {
              if (crossOriginUpdater && !this.root) {
                if (!crossOriginRect || crossOriginRect.width == 0 && crossOriginRect.height == 0) {
                  parent = null;
                  parentRect = null;
                  intersectionRect = null;
                } else {
                  parentRect = crossOriginRect;
                }
              } else {
                parentRect = rootRect;
              }
            } else {
              var frame = getParentNode(parent);
              var frameRect = frame && getBoundingClientRect(frame);
              var frameIntersect = frame && this._computeTargetAndRootIntersection(frame, frameRect, rootRect);
              if (frameRect && frameIntersect) {
                parent = frame;
                parentRect = convertFromParentRect(frameRect, frameIntersect);
              } else {
                parent = null;
                intersectionRect = null;
              }
            }
          } else {
            var doc = parent.ownerDocument;
            if (parent != doc.body && parent != doc.documentElement && parentComputedStyle.overflow != "visible") {
              parentRect = getBoundingClientRect(parent);
            }
          }
          if (parentRect) {
            intersectionRect = computeRectIntersection(parentRect, intersectionRect);
          }
          if (!intersectionRect) break;
          parent = parent && getParentNode(parent);
        }
        return intersectionRect;
      };
      IntersectionObserver.prototype._getRootRect = function() {
        var rootRect;
        if (this.root && !isDoc(this.root)) {
          rootRect = getBoundingClientRect(this.root);
        } else {
          var doc = isDoc(this.root) ? this.root : document;
          var html = doc.documentElement;
          var body = doc.body;
          rootRect = {
            top: 0,
            left: 0,
            right: html.clientWidth || body.clientWidth,
            width: html.clientWidth || body.clientWidth,
            bottom: html.clientHeight || body.clientHeight,
            height: html.clientHeight || body.clientHeight
          };
        }
        return this._expandRectByRootMargin(rootRect);
      };
      IntersectionObserver.prototype._expandRectByRootMargin = function(rect) {
        var margins = this._rootMarginValues.map(function(margin, i) {
          return margin.unit == "px" ? margin.value : margin.value * (i % 2 ? rect.width : rect.height) / 100;
        });
        var newRect = {
          top: rect.top - margins[0],
          right: rect.right + margins[1],
          bottom: rect.bottom + margins[2],
          left: rect.left - margins[3]
        };
        newRect.width = newRect.right - newRect.left;
        newRect.height = newRect.bottom - newRect.top;
        return newRect;
      };
      IntersectionObserver.prototype._hasCrossedThreshold = function(oldEntry, newEntry) {
        var oldRatio = oldEntry && oldEntry.isIntersecting ? oldEntry.intersectionRatio || 0 : -1;
        var newRatio = newEntry.isIntersecting ? newEntry.intersectionRatio || 0 : -1;
        if (oldRatio === newRatio) return;
        for (var i = 0; i < this.thresholds.length; i++) {
          var threshold = this.thresholds[i];
          if (threshold == oldRatio || threshold == newRatio || threshold < oldRatio !== threshold < newRatio) {
            return true;
          }
        }
      };
      IntersectionObserver.prototype._rootIsInDom = function() {
        return !this.root || containsDeep(document, this.root);
      };
      IntersectionObserver.prototype._rootContainsTarget = function(target) {
        var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;
        return containsDeep(rootDoc, target) && (!this.root || rootDoc == target.ownerDocument);
      };
      IntersectionObserver.prototype._registerInstance = function() {
        if (registry.indexOf(this) < 0) {
          registry.push(this);
        }
      };
      IntersectionObserver.prototype._unregisterInstance = function() {
        var index = registry.indexOf(this);
        if (index != -1) registry.splice(index, 1);
      };
      function now() {
        return window.performance && performance.now && performance.now();
      }
      function throttle(fn, timeout) {
        var timer = null;
        return function() {
          if (!timer) {
            timer = setTimeout(function() {
              fn();
              timer = null;
            }, timeout);
          }
        };
      }
      function addEvent(node, event, fn, opt_useCapture) {
        if (typeof node.addEventListener == "function") {
          node.addEventListener(event, fn, opt_useCapture || false);
        } else if (typeof node.attachEvent == "function") {
          node.attachEvent("on" + event, fn);
        }
      }
      function removeEvent(node, event, fn, opt_useCapture) {
        if (typeof node.removeEventListener == "function") {
          node.removeEventListener(event, fn, opt_useCapture || false);
        } else if (typeof node.detachEvent == "function") {
          node.detachEvent("on" + event, fn);
        }
      }
      function computeRectIntersection(rect1, rect2) {
        var top = Math.max(rect1.top, rect2.top);
        var bottom = Math.min(rect1.bottom, rect2.bottom);
        var left = Math.max(rect1.left, rect2.left);
        var right = Math.min(rect1.right, rect2.right);
        var width = right - left;
        var height = bottom - top;
        return width >= 0 && height >= 0 && {
          top,
          bottom,
          left,
          right,
          width,
          height
        } || null;
      }
      function getBoundingClientRect(el) {
        var rect;
        try {
          rect = el.getBoundingClientRect();
        } catch (err) {
        }
        if (!rect) return getEmptyRect();
        if (!(rect.width && rect.height)) {
          rect = {
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom,
            left: rect.left,
            width: rect.right - rect.left,
            height: rect.bottom - rect.top
          };
        }
        return rect;
      }
      function getEmptyRect() {
        return {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
          width: 0,
          height: 0
        };
      }
      function ensureDOMRect(rect) {
        if (!rect || "x" in rect) {
          return rect;
        }
        return {
          top: rect.top,
          y: rect.top,
          bottom: rect.bottom,
          left: rect.left,
          x: rect.left,
          right: rect.right,
          width: rect.width,
          height: rect.height
        };
      }
      function convertFromParentRect(parentBoundingRect, parentIntersectionRect) {
        var top = parentIntersectionRect.top - parentBoundingRect.top;
        var left = parentIntersectionRect.left - parentBoundingRect.left;
        return {
          top,
          left,
          height: parentIntersectionRect.height,
          width: parentIntersectionRect.width,
          bottom: top + parentIntersectionRect.height,
          right: left + parentIntersectionRect.width
        };
      }
      function containsDeep(parent, child) {
        var node = child;
        while (node) {
          if (node == parent) return true;
          node = getParentNode(node);
        }
        return false;
      }
      function getParentNode(node) {
        var parent = node.parentNode;
        if (node.nodeType == /* DOCUMENT */
        9 && node != document) {
          return getFrameElement(node);
        }
        if (parent && parent.assignedSlot) {
          parent = parent.assignedSlot.parentNode;
        }
        if (parent && parent.nodeType == 11 && parent.host) {
          return parent.host;
        }
        return parent;
      }
      function isDoc(node) {
        return node && node.nodeType === 9;
      }
      window.IntersectionObserver = IntersectionObserver;
      window.IntersectionObserverEntry = IntersectionObserverEntry;
    })();
  }
});

// node_modules/vue-svg-inline-plugin/src/polyfills.js
var polyfills_exports = {};
var init_polyfills = __esm({
  "node_modules/vue-svg-inline-plugin/src/polyfills.js"() {
    init_fetch();
    init_intersection_observer();
  }
});

export {
  polyfills_exports,
  init_polyfills
};
//# sourceMappingURL=chunk-NITV7DIR.js.map
