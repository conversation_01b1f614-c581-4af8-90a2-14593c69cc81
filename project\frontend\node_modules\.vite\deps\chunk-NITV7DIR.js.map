{"version": 3, "sources": ["../../whatwg-fetch/fetch.js", "../../intersection-observer/intersection-observer.js", "../../vue-svg-inline-plugin/src/polyfills.js"], "sourcesContent": ["/* eslint-disable no-prototype-builtins */\nvar g =\n  (typeof globalThis !== 'undefined' && globalThis) ||\n  (typeof self !== 'undefined' && self) ||\n  // eslint-disable-next-line no-undef\n  (typeof global !== 'undefined' && global) ||\n  {}\n\nvar support = {\n  searchParams: 'URLSearchParams' in g,\n  iterable: 'Symbol' in g && 'iterator' in Symbol,\n  blob:\n    'FileReader' in g &&\n    'Blob' in g &&\n    (function() {\n      try {\n        new Blob()\n        return true\n      } catch (e) {\n        return false\n      }\n    })(),\n  formData: 'FormData' in g,\n  arrayBuffer: 'ArrayBuffer' in g\n}\n\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj)\n}\n\nif (support.arrayBuffer) {\n  var viewClasses = [\n    '[object Int8Array]',\n    '[object Uint8Array]',\n    '[object Uint8ClampedArray]',\n    '[object Int16Array]',\n    '[object Uint16Array]',\n    '[object Int32Array]',\n    '[object Uint32Array]',\n    '[object Float32Array]',\n    '[object Float64Array]'\n  ]\n\n  var isArrayBufferView =\n    ArrayBuffer.isView ||\n    function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name)\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n    throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n  }\n  return name.toLowerCase()\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value)\n  }\n  return value\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function() {\n      var value = items.shift()\n      return {done: value === undefined, value: value}\n    }\n  }\n\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function() {\n      return iterator\n    }\n  }\n\n  return iterator\n}\n\nexport function Headers(headers) {\n  this.map = {}\n\n  if (headers instanceof Headers) {\n    headers.forEach(function(value, name) {\n      this.append(name, value)\n    }, this)\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function(header) {\n      if (header.length != 2) {\n        throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n      }\n      this.append(header[0], header[1])\n    }, this)\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function(name) {\n      this.append(name, headers[name])\n    }, this)\n  }\n}\n\nHeaders.prototype.append = function(name, value) {\n  name = normalizeName(name)\n  value = normalizeValue(value)\n  var oldValue = this.map[name]\n  this.map[name] = oldValue ? oldValue + ', ' + value : value\n}\n\nHeaders.prototype['delete'] = function(name) {\n  delete this.map[normalizeName(name)]\n}\n\nHeaders.prototype.get = function(name) {\n  name = normalizeName(name)\n  return this.has(name) ? this.map[name] : null\n}\n\nHeaders.prototype.has = function(name) {\n  return this.map.hasOwnProperty(normalizeName(name))\n}\n\nHeaders.prototype.set = function(name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value)\n}\n\nHeaders.prototype.forEach = function(callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this)\n    }\n  }\n}\n\nHeaders.prototype.keys = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push(name)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.values = function() {\n  var items = []\n  this.forEach(function(value) {\n    items.push(value)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.entries = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push([name, value])\n  })\n  return iteratorFor(items)\n}\n\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n}\n\nfunction consumed(body) {\n  if (body._noBody) return\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'))\n  }\n  body.bodyUsed = true\n}\n\nfunction fileReaderReady(reader) {\n  return new Promise(function(resolve, reject) {\n    reader.onload = function() {\n      resolve(reader.result)\n    }\n    reader.onerror = function() {\n      reject(reader.error)\n    }\n  })\n}\n\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsArrayBuffer(blob)\n  return promise\n}\n\nfunction readBlobAsText(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type)\n  var encoding = match ? match[1] : 'utf-8'\n  reader.readAsText(blob, encoding)\n  return promise\n}\n\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf)\n  var chars = new Array(view.length)\n\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i])\n  }\n  return chars.join('')\n}\n\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0)\n  } else {\n    var view = new Uint8Array(buf.byteLength)\n    view.set(new Uint8Array(buf))\n    return view.buffer\n  }\n}\n\nfunction Body() {\n  this.bodyUsed = false\n\n  this._initBody = function(body) {\n    /*\n      fetch-mock wraps the Response object in an ES6 Proxy to\n      provide useful test harness features such as flush. However, on\n      ES5 browsers without fetch or Proxy support pollyfills must be used;\n      the proxy-pollyfill is unable to proxy an attribute unless it exists\n      on the object before the Proxy is created. This change ensures\n      Response.bodyUsed exists on the instance, while maintaining the\n      semantic of setting Request.bodyUsed in the constructor before\n      _initBody is called.\n    */\n    // eslint-disable-next-line no-self-assign\n    this.bodyUsed = this.bodyUsed\n    this._bodyInit = body\n    if (!body) {\n      this._noBody = true;\n      this._bodyText = ''\n    } else if (typeof body === 'string') {\n      this._bodyText = body\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString()\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer)\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer])\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body)\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body)\n    }\n\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8')\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type)\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n      }\n    }\n  }\n\n  if (support.blob) {\n    this.blob = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob')\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]))\n      }\n    }\n  }\n\n  this.arrayBuffer = function() {\n    if (this._bodyArrayBuffer) {\n      var isConsumed = consumed(this)\n      if (isConsumed) {\n        return isConsumed\n      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n        return Promise.resolve(\n          this._bodyArrayBuffer.buffer.slice(\n            this._bodyArrayBuffer.byteOffset,\n            this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n          )\n        )\n      } else {\n        return Promise.resolve(this._bodyArrayBuffer)\n      }\n    } else if (support.blob) {\n      return this.blob().then(readBlobAsArrayBuffer)\n    } else {\n      throw new Error('could not read as ArrayBuffer')\n    }\n  }\n\n  this.text = function() {\n    var rejected = consumed(this)\n    if (rejected) {\n      return rejected\n    }\n\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob)\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text')\n    } else {\n      return Promise.resolve(this._bodyText)\n    }\n  }\n\n  if (support.formData) {\n    this.formData = function() {\n      return this.text().then(decode)\n    }\n  }\n\n  this.json = function() {\n    return this.text().then(JSON.parse)\n  }\n\n  return this\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE']\n\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase()\n  return methods.indexOf(upcased) > -1 ? upcased : method\n}\n\nexport function Request(input, options) {\n  if (!(this instanceof Request)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n\n  options = options || {}\n  var body = options.body\n\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read')\n    }\n    this.url = input.url\n    this.credentials = input.credentials\n    if (!options.headers) {\n      this.headers = new Headers(input.headers)\n    }\n    this.method = input.method\n    this.mode = input.mode\n    this.signal = input.signal\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit\n      input.bodyUsed = true\n    }\n  } else {\n    this.url = String(input)\n  }\n\n  this.credentials = options.credentials || this.credentials || 'same-origin'\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers)\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET')\n  this.mode = options.mode || this.mode || null\n  this.signal = options.signal || this.signal || (function () {\n    if ('AbortController' in g) {\n      var ctrl = new AbortController();\n      return ctrl.signal;\n    }\n  }());\n  this.referrer = null\n\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests')\n  }\n  this._initBody(body)\n\n  if (this.method === 'GET' || this.method === 'HEAD') {\n    if (options.cache === 'no-store' || options.cache === 'no-cache') {\n      // Search for a '_' parameter in the query string\n      var reParamSearch = /([?&])_=[^&]*/\n      if (reParamSearch.test(this.url)) {\n        // If it already exists then set the value with the current time\n        this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime())\n      } else {\n        // Otherwise add a new '_' parameter to the end with the current time\n        var reQueryString = /\\?/\n        this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime()\n      }\n    }\n  }\n}\n\nRequest.prototype.clone = function() {\n  return new Request(this, {body: this._bodyInit})\n}\n\nfunction decode(body) {\n  var form = new FormData()\n  body\n    .trim()\n    .split('&')\n    .forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n  return form\n}\n\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers()\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n  // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n  // https://github.com/github/fetch/issues/748\n  // https://github.com/zloirock/core-js/issues/751\n  preProcessedHeaders\n    .split('\\r')\n    .map(function(header) {\n      return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n    })\n    .forEach(function(line) {\n      var parts = line.split(':')\n      var key = parts.shift().trim()\n      if (key) {\n        var value = parts.join(':').trim()\n        try {\n          headers.append(key, value)\n        } catch (error) {\n          console.warn('Response ' + error.message)\n        }\n      }\n    })\n  return headers\n}\n\nBody.call(Request.prototype)\n\nexport function Response(bodyInit, options) {\n  if (!(this instanceof Response)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n  if (!options) {\n    options = {}\n  }\n\n  this.type = 'default'\n  this.status = options.status === undefined ? 200 : options.status\n  if (this.status < 200 || this.status > 599) {\n    throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n  }\n  this.ok = this.status >= 200 && this.status < 300\n  this.statusText = options.statusText === undefined ? '' : '' + options.statusText\n  this.headers = new Headers(options.headers)\n  this.url = options.url || ''\n  this._initBody(bodyInit)\n}\n\nBody.call(Response.prototype)\n\nResponse.prototype.clone = function() {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  })\n}\n\nResponse.error = function() {\n  var response = new Response(null, {status: 200, statusText: ''})\n  response.ok = false\n  response.status = 0\n  response.type = 'error'\n  return response\n}\n\nvar redirectStatuses = [301, 302, 303, 307, 308]\n\nResponse.redirect = function(url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code')\n  }\n\n  return new Response(null, {status: status, headers: {location: url}})\n}\n\nexport var DOMException = g.DOMException\ntry {\n  new DOMException()\n} catch (err) {\n  DOMException = function(message, name) {\n    this.message = message\n    this.name = name\n    var error = Error(message)\n    this.stack = error.stack\n  }\n  DOMException.prototype = Object.create(Error.prototype)\n  DOMException.prototype.constructor = DOMException\n}\n\nexport function fetch(input, init) {\n  return new Promise(function(resolve, reject) {\n    var request = new Request(input, init)\n\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    var xhr = new XMLHttpRequest()\n\n    function abortXhr() {\n      xhr.abort()\n    }\n\n    xhr.onload = function() {\n      var options = {\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      }\n      // This check if specifically for when a user fetches a file locally from the file system\n      // Only if the status is out of a normal range\n      if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n        options.status = 200;\n      } else {\n        options.status = xhr.status;\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n      var body = 'response' in xhr ? xhr.response : xhr.responseText\n      setTimeout(function() {\n        resolve(new Response(body, options))\n      }, 0)\n    }\n\n    xhr.onerror = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request failed'))\n      }, 0)\n    }\n\n    xhr.ontimeout = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request timed out'))\n      }, 0)\n    }\n\n    xhr.onabort = function() {\n      setTimeout(function() {\n        reject(new DOMException('Aborted', 'AbortError'))\n      }, 0)\n    }\n\n    function fixUrl(url) {\n      try {\n        return url === '' && g.location.href ? g.location.href : url\n      } catch (e) {\n        return url\n      }\n    }\n\n    xhr.open(request.method, fixUrl(request.url), true)\n\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false\n    }\n\n    if ('responseType' in xhr) {\n      if (support.blob) {\n        xhr.responseType = 'blob'\n      } else if (\n        support.arrayBuffer\n      ) {\n        xhr.responseType = 'arraybuffer'\n      }\n    }\n\n    if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n      var names = [];\n      Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n        names.push(normalizeName(name))\n        xhr.setRequestHeader(name, normalizeValue(init.headers[name]))\n      })\n      request.headers.forEach(function(value, name) {\n        if (names.indexOf(name) === -1) {\n          xhr.setRequestHeader(name, value)\n        }\n      })\n    } else {\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value)\n      })\n    }\n\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr)\n\n      xhr.onreadystatechange = function() {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr)\n        }\n      }\n    }\n\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n  })\n}\n\nfetch.polyfill = true\n\nif (!g.fetch) {\n  g.fetch = fetch\n  g.Headers = Headers\n  g.Request = Request\n  g.Response = Response\n}\n", "/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function() {\n'use strict';\n\n// Exit early if we're not running in a browser.\nif (typeof window !== 'object') {\n  return;\n}\n\n// Exit early if all IntersectionObserver and IntersectionObserverEntry\n// features are natively supported.\nif ('IntersectionObserver' in window &&\n    'IntersectionObserverEntry' in window &&\n    'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n\n  // Minimal polyfill for Edge 15's lack of `isIntersecting`\n  // See: https://github.com/w3c/IntersectionObserver/issues/211\n  if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n    Object.defineProperty(window.IntersectionObserverEntry.prototype,\n      'isIntersecting', {\n      get: function () {\n        return this.intersectionRatio > 0;\n      }\n    });\n  }\n  return;\n}\n\n/**\n * Returns the embedding frame element, if any.\n * @param {!Document} doc\n * @return {!Element}\n */\nfunction getFrameElement(doc) {\n  try {\n    return doc.defaultView && doc.defaultView.frameElement || null;\n  } catch (e) {\n    // Ignore the error.\n    return null;\n  }\n}\n\n/**\n * A local reference to the root document.\n */\nvar document = (function(startDoc) {\n  var doc = startDoc;\n  var frame = getFrameElement(doc);\n  while (frame) {\n    doc = frame.ownerDocument;\n    frame = getFrameElement(doc);\n  }\n  return doc;\n})(window.document);\n\n/**\n * An IntersectionObserver registry. This registry exists to hold a strong\n * reference to IntersectionObserver instances currently observing a target\n * element. Without this registry, instances without another reference may be\n * garbage collected.\n */\nvar registry = [];\n\n/**\n * The signal updater for cross-origin intersection. When not null, it means\n * that the polyfill is configured to work in a cross-origin mode.\n * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nvar crossOriginUpdater = null;\n\n/**\n * The current cross-origin intersection. Only used in the cross-origin mode.\n * @type {DOMRect|ClientRect}\n */\nvar crossOriginRect = null;\n\n\n/**\n * Creates the global IntersectionObserverEntry constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n * @param {Object} entry A dictionary of instance properties.\n * @constructor\n */\nfunction IntersectionObserverEntry(entry) {\n  this.time = entry.time;\n  this.target = entry.target;\n  this.rootBounds = ensureDOMRect(entry.rootBounds);\n  this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n  this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n  this.isIntersecting = !!entry.intersectionRect;\n\n  // Calculates the intersection ratio.\n  var targetRect = this.boundingClientRect;\n  var targetArea = targetRect.width * targetRect.height;\n  var intersectionRect = this.intersectionRect;\n  var intersectionArea = intersectionRect.width * intersectionRect.height;\n\n  // Sets intersection ratio.\n  if (targetArea) {\n    // Round the intersection ratio to avoid floating point math issues:\n    // https://github.com/w3c/IntersectionObserver/issues/324\n    this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n  } else {\n    // If area is zero and is intersecting, sets to 1, otherwise to 0\n    this.intersectionRatio = this.isIntersecting ? 1 : 0;\n  }\n}\n\n\n/**\n * Creates the global IntersectionObserver constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n * @param {Function} callback The function to be invoked after intersection\n *     changes have queued. The function is not invoked if the queue has\n *     been emptied by calling the `takeRecords` method.\n * @param {Object=} opt_options Optional configuration options.\n * @constructor\n */\nfunction IntersectionObserver(callback, opt_options) {\n\n  var options = opt_options || {};\n\n  if (typeof callback != 'function') {\n    throw new Error('callback must be a function');\n  }\n\n  if (\n    options.root &&\n    options.root.nodeType != 1 &&\n    options.root.nodeType != 9\n  ) {\n    throw new Error('root must be a Document or Element');\n  }\n\n  // Binds and throttles `this._checkForIntersections`.\n  this._checkForIntersections = throttle(\n      this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);\n\n  // Private properties.\n  this._callback = callback;\n  this._observationTargets = [];\n  this._queuedEntries = [];\n  this._rootMarginValues = this._parseRootMargin(options.rootMargin);\n\n  // Public properties.\n  this.thresholds = this._initThresholds(options.threshold);\n  this.root = options.root || null;\n  this.rootMargin = this._rootMarginValues.map(function(margin) {\n    return margin.value + margin.unit;\n  }).join(' ');\n\n  /** @private @const {!Array<!Document>} */\n  this._monitoringDocuments = [];\n  /** @private @const {!Array<function()>} */\n  this._monitoringUnsubscribes = [];\n}\n\n\n/**\n * The minimum interval within which the document will be checked for\n * intersection changes.\n */\nIntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n\n\n/**\n * The frequency in which the polyfill polls for intersection changes.\n * this can be updated on a per instance basis and must be set prior to\n * calling `observe` on the first target.\n */\nIntersectionObserver.prototype.POLL_INTERVAL = null;\n\n/**\n * Use a mutation observer on the root element\n * to detect intersection changes.\n */\nIntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n\n\n/**\n * Sets up the polyfill in the cross-origin mode. The result is the\n * updater function that accepts two arguments: `boundingClientRect` and\n * `intersectionRect` - just as these fields would be available to the\n * parent via `IntersectionObserverEntry`. This function should be called\n * each time the iframe receives intersection information from the parent\n * window, e.g. via messaging.\n * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nIntersectionObserver._setupCrossOriginUpdater = function() {\n  if (!crossOriginUpdater) {\n    /**\n     * @param {DOMRect|ClientRect} boundingClientRect\n     * @param {DOMRect|ClientRect} intersectionRect\n     */\n    crossOriginUpdater = function(boundingClientRect, intersectionRect) {\n      if (!boundingClientRect || !intersectionRect) {\n        crossOriginRect = getEmptyRect();\n      } else {\n        crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n      }\n      registry.forEach(function(observer) {\n        observer._checkForIntersections();\n      });\n    };\n  }\n  return crossOriginUpdater;\n};\n\n\n/**\n * Resets the cross-origin mode.\n */\nIntersectionObserver._resetCrossOriginUpdater = function() {\n  crossOriginUpdater = null;\n  crossOriginRect = null;\n};\n\n\n/**\n * Starts observing a target element for intersection changes based on\n * the thresholds values.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.observe = function(target) {\n  var isTargetAlreadyObserved = this._observationTargets.some(function(item) {\n    return item.element == target;\n  });\n\n  if (isTargetAlreadyObserved) {\n    return;\n  }\n\n  if (!(target && target.nodeType == 1)) {\n    throw new Error('target must be an Element');\n  }\n\n  this._registerInstance();\n  this._observationTargets.push({element: target, entry: null});\n  this._monitorIntersections(target.ownerDocument);\n  this._checkForIntersections();\n};\n\n\n/**\n * Stops observing a target element for intersection changes.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.unobserve = function(target) {\n  this._observationTargets =\n      this._observationTargets.filter(function(item) {\n        return item.element != target;\n      });\n  this._unmonitorIntersections(target.ownerDocument);\n  if (this._observationTargets.length == 0) {\n    this._unregisterInstance();\n  }\n};\n\n\n/**\n * Stops observing all target elements for intersection changes.\n */\nIntersectionObserver.prototype.disconnect = function() {\n  this._observationTargets = [];\n  this._unmonitorAllIntersections();\n  this._unregisterInstance();\n};\n\n\n/**\n * Returns any queue entries that have not yet been reported to the\n * callback and clears the queue. This can be used in conjunction with the\n * callback to obtain the absolute most up-to-date intersection information.\n * @return {Array} The currently queued entries.\n */\nIntersectionObserver.prototype.takeRecords = function() {\n  var records = this._queuedEntries.slice();\n  this._queuedEntries = [];\n  return records;\n};\n\n\n/**\n * Accepts the threshold value from the user configuration object and\n * returns a sorted array of unique threshold values. If a value is not\n * between 0 and 1 and error is thrown.\n * @private\n * @param {Array|number=} opt_threshold An optional threshold value or\n *     a list of threshold values, defaulting to [0].\n * @return {Array} A sorted list of unique and valid threshold values.\n */\nIntersectionObserver.prototype._initThresholds = function(opt_threshold) {\n  var threshold = opt_threshold || [0];\n  if (!Array.isArray(threshold)) threshold = [threshold];\n\n  return threshold.sort().filter(function(t, i, a) {\n    if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n      throw new Error('threshold must be a number between 0 and 1 inclusively');\n    }\n    return t !== a[i - 1];\n  });\n};\n\n\n/**\n * Accepts the rootMargin value from the user configuration object\n * and returns an array of the four margin values as an object containing\n * the value and unit properties. If any of the values are not properly\n * formatted or use a unit other than px or %, and error is thrown.\n * @private\n * @param {string=} opt_rootMargin An optional rootMargin value,\n *     defaulting to '0px'.\n * @return {Array<Object>} An array of margin objects with the keys\n *     value and unit.\n */\nIntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {\n  var marginString = opt_rootMargin || '0px';\n  var margins = marginString.split(/\\s+/).map(function(margin) {\n    var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n    if (!parts) {\n      throw new Error('rootMargin must be specified in pixels or percent');\n    }\n    return {value: parseFloat(parts[1]), unit: parts[2]};\n  });\n\n  // Handles shorthand.\n  margins[1] = margins[1] || margins[0];\n  margins[2] = margins[2] || margins[0];\n  margins[3] = margins[3] || margins[1];\n\n  return margins;\n};\n\n\n/**\n * Starts polling for intersection changes if the polling is not already\n * happening, and if the page's visibility state is visible.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._monitorIntersections = function(doc) {\n  var win = doc.defaultView;\n  if (!win) {\n    // Already destroyed.\n    return;\n  }\n  if (this._monitoringDocuments.indexOf(doc) != -1) {\n    // Already monitoring.\n    return;\n  }\n\n  // Private state for monitoring.\n  var callback = this._checkForIntersections;\n  var monitoringInterval = null;\n  var domObserver = null;\n\n  // If a poll interval is set, use polling instead of listening to\n  // resize and scroll events or DOM mutations.\n  if (this.POLL_INTERVAL) {\n    monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n  } else {\n    addEvent(win, 'resize', callback, true);\n    addEvent(doc, 'scroll', callback, true);\n    if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n      domObserver = new win.MutationObserver(callback);\n      domObserver.observe(doc, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    }\n  }\n\n  this._monitoringDocuments.push(doc);\n  this._monitoringUnsubscribes.push(function() {\n    // Get the window object again. When a friendly iframe is destroyed, it\n    // will be null.\n    var win = doc.defaultView;\n\n    if (win) {\n      if (monitoringInterval) {\n        win.clearInterval(monitoringInterval);\n      }\n      removeEvent(win, 'resize', callback, true);\n    }\n\n    removeEvent(doc, 'scroll', callback, true);\n    if (domObserver) {\n      domObserver.disconnect();\n    }\n  });\n\n  // Also monitor the parent.\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._monitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorIntersections = function(doc) {\n  var index = this._monitoringDocuments.indexOf(doc);\n  if (index == -1) {\n    return;\n  }\n\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n\n  // Check if any dependent targets are still remaining.\n  var hasDependentTargets =\n      this._observationTargets.some(function(item) {\n        var itemDoc = item.element.ownerDocument;\n        // Target is in this context.\n        if (itemDoc == doc) {\n          return true;\n        }\n        // Target is nested in this context.\n        while (itemDoc && itemDoc != rootDoc) {\n          var frame = getFrameElement(itemDoc);\n          itemDoc = frame && frame.ownerDocument;\n          if (itemDoc == doc) {\n            return true;\n          }\n        }\n        return false;\n      });\n  if (hasDependentTargets) {\n    return;\n  }\n\n  // Unsubscribe.\n  var unsubscribe = this._monitoringUnsubscribes[index];\n  this._monitoringDocuments.splice(index, 1);\n  this._monitoringUnsubscribes.splice(index, 1);\n  unsubscribe();\n\n  // Also unmonitor the parent.\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._unmonitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorAllIntersections = function() {\n  var unsubscribes = this._monitoringUnsubscribes.slice(0);\n  this._monitoringDocuments.length = 0;\n  this._monitoringUnsubscribes.length = 0;\n  for (var i = 0; i < unsubscribes.length; i++) {\n    unsubscribes[i]();\n  }\n};\n\n\n/**\n * Scans each observation target for intersection changes and adds them\n * to the internal entries queue. If new entries are found, it\n * schedules the callback to be invoked.\n * @private\n */\nIntersectionObserver.prototype._checkForIntersections = function() {\n  if (!this.root && crossOriginUpdater && !crossOriginRect) {\n    // Cross origin monitoring, but no initial data available yet.\n    return;\n  }\n\n  var rootIsInDom = this._rootIsInDom();\n  var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n\n  this._observationTargets.forEach(function(item) {\n    var target = item.element;\n    var targetRect = getBoundingClientRect(target);\n    var rootContainsTarget = this._rootContainsTarget(target);\n    var oldEntry = item.entry;\n    var intersectionRect = rootIsInDom && rootContainsTarget &&\n        this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n\n    var rootBounds = null;\n    if (!this._rootContainsTarget(target)) {\n      rootBounds = getEmptyRect();\n    } else if (!crossOriginUpdater || this.root) {\n      rootBounds = rootRect;\n    }\n\n    var newEntry = item.entry = new IntersectionObserverEntry({\n      time: now(),\n      target: target,\n      boundingClientRect: targetRect,\n      rootBounds: rootBounds,\n      intersectionRect: intersectionRect\n    });\n\n    if (!oldEntry) {\n      this._queuedEntries.push(newEntry);\n    } else if (rootIsInDom && rootContainsTarget) {\n      // If the new entry intersection ratio has crossed any of the\n      // thresholds, add a new entry.\n      if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n        this._queuedEntries.push(newEntry);\n      }\n    } else {\n      // If the root is not in the DOM or target is not contained within\n      // root but the previous entry for this target had an intersection,\n      // add a new record indicating removal.\n      if (oldEntry && oldEntry.isIntersecting) {\n        this._queuedEntries.push(newEntry);\n      }\n    }\n  }, this);\n\n  if (this._queuedEntries.length) {\n    this._callback(this.takeRecords(), this);\n  }\n};\n\n\n/**\n * Accepts a target and root rect computes the intersection between then\n * following the algorithm in the spec.\n * TODO(philipwalton): at this time clip-path is not considered.\n * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n * @param {Element} target The target DOM element\n * @param {Object} targetRect The bounding rect of the target.\n * @param {Object} rootRect The bounding rect of the root after being\n *     expanded by the rootMargin value.\n * @return {?Object} The final intersection rect object or undefined if no\n *     intersection is found.\n * @private\n */\nIntersectionObserver.prototype._computeTargetAndRootIntersection =\n    function(target, targetRect, rootRect) {\n  // If the element isn't displayed, an intersection can't happen.\n  if (window.getComputedStyle(target).display == 'none') return;\n\n  var intersectionRect = targetRect;\n  var parent = getParentNode(target);\n  var atRoot = false;\n\n  while (!atRoot && parent) {\n    var parentRect = null;\n    var parentComputedStyle = parent.nodeType == 1 ?\n        window.getComputedStyle(parent) : {};\n\n    // If the parent isn't displayed, an intersection can't happen.\n    if (parentComputedStyle.display == 'none') return null;\n\n    if (parent == this.root || parent.nodeType == /* DOCUMENT */ 9) {\n      atRoot = true;\n      if (parent == this.root || parent == document) {\n        if (crossOriginUpdater && !this.root) {\n          if (!crossOriginRect ||\n              crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n            // A 0-size cross-origin intersection means no-intersection.\n            parent = null;\n            parentRect = null;\n            intersectionRect = null;\n          } else {\n            parentRect = crossOriginRect;\n          }\n        } else {\n          parentRect = rootRect;\n        }\n      } else {\n        // Check if there's a frame that can be navigated to.\n        var frame = getParentNode(parent);\n        var frameRect = frame && getBoundingClientRect(frame);\n        var frameIntersect =\n            frame &&\n            this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n        if (frameRect && frameIntersect) {\n          parent = frame;\n          parentRect = convertFromParentRect(frameRect, frameIntersect);\n        } else {\n          parent = null;\n          intersectionRect = null;\n        }\n      }\n    } else {\n      // If the element has a non-visible overflow, and it's not the <body>\n      // or <html> element, update the intersection rect.\n      // Note: <body> and <html> cannot be clipped to a rect that's not also\n      // the document rect, so no need to compute a new intersection.\n      var doc = parent.ownerDocument;\n      if (parent != doc.body &&\n          parent != doc.documentElement &&\n          parentComputedStyle.overflow != 'visible') {\n        parentRect = getBoundingClientRect(parent);\n      }\n    }\n\n    // If either of the above conditionals set a new parentRect,\n    // calculate new intersection data.\n    if (parentRect) {\n      intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n    }\n    if (!intersectionRect) break;\n    parent = parent && getParentNode(parent);\n  }\n  return intersectionRect;\n};\n\n\n/**\n * Returns the root rect after being expanded by the rootMargin value.\n * @return {ClientRect} The expanded root rect.\n * @private\n */\nIntersectionObserver.prototype._getRootRect = function() {\n  var rootRect;\n  if (this.root && !isDoc(this.root)) {\n    rootRect = getBoundingClientRect(this.root);\n  } else {\n    // Use <html>/<body> instead of window since scroll bars affect size.\n    var doc = isDoc(this.root) ? this.root : document;\n    var html = doc.documentElement;\n    var body = doc.body;\n    rootRect = {\n      top: 0,\n      left: 0,\n      right: html.clientWidth || body.clientWidth,\n      width: html.clientWidth || body.clientWidth,\n      bottom: html.clientHeight || body.clientHeight,\n      height: html.clientHeight || body.clientHeight\n    };\n  }\n  return this._expandRectByRootMargin(rootRect);\n};\n\n\n/**\n * Accepts a rect and expands it by the rootMargin value.\n * @param {DOMRect|ClientRect} rect The rect object to expand.\n * @return {ClientRect} The expanded rect.\n * @private\n */\nIntersectionObserver.prototype._expandRectByRootMargin = function(rect) {\n  var margins = this._rootMarginValues.map(function(margin, i) {\n    return margin.unit == 'px' ? margin.value :\n        margin.value * (i % 2 ? rect.width : rect.height) / 100;\n  });\n  var newRect = {\n    top: rect.top - margins[0],\n    right: rect.right + margins[1],\n    bottom: rect.bottom + margins[2],\n    left: rect.left - margins[3]\n  };\n  newRect.width = newRect.right - newRect.left;\n  newRect.height = newRect.bottom - newRect.top;\n\n  return newRect;\n};\n\n\n/**\n * Accepts an old and new entry and returns true if at least one of the\n * threshold values has been crossed.\n * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n *    particular target element or null if no previous entry exists.\n * @param {IntersectionObserverEntry} newEntry The current entry for a\n *    particular target element.\n * @return {boolean} Returns true if a any threshold has been crossed.\n * @private\n */\nIntersectionObserver.prototype._hasCrossedThreshold =\n    function(oldEntry, newEntry) {\n\n  // To make comparing easier, an entry that has a ratio of 0\n  // but does not actually intersect is given a value of -1\n  var oldRatio = oldEntry && oldEntry.isIntersecting ?\n      oldEntry.intersectionRatio || 0 : -1;\n  var newRatio = newEntry.isIntersecting ?\n      newEntry.intersectionRatio || 0 : -1;\n\n  // Ignore unchanged ratios\n  if (oldRatio === newRatio) return;\n\n  for (var i = 0; i < this.thresholds.length; i++) {\n    var threshold = this.thresholds[i];\n\n    // Return true if an entry matches a threshold or if the new ratio\n    // and the old ratio are on the opposite sides of a threshold.\n    if (threshold == oldRatio || threshold == newRatio ||\n        threshold < oldRatio !== threshold < newRatio) {\n      return true;\n    }\n  }\n};\n\n\n/**\n * Returns whether or not the root element is an element and is in the DOM.\n * @return {boolean} True if the root element is an element and is in the DOM.\n * @private\n */\nIntersectionObserver.prototype._rootIsInDom = function() {\n  return !this.root || containsDeep(document, this.root);\n};\n\n\n/**\n * Returns whether or not the target element is a child of root.\n * @param {Element} target The target element to check.\n * @return {boolean} True if the target element is a child of root.\n * @private\n */\nIntersectionObserver.prototype._rootContainsTarget = function(target) {\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  return (\n    containsDeep(rootDoc, target) &&\n    (!this.root || rootDoc == target.ownerDocument)\n  );\n};\n\n\n/**\n * Adds the instance to the global IntersectionObserver registry if it isn't\n * already present.\n * @private\n */\nIntersectionObserver.prototype._registerInstance = function() {\n  if (registry.indexOf(this) < 0) {\n    registry.push(this);\n  }\n};\n\n\n/**\n * Removes the instance from the global IntersectionObserver registry.\n * @private\n */\nIntersectionObserver.prototype._unregisterInstance = function() {\n  var index = registry.indexOf(this);\n  if (index != -1) registry.splice(index, 1);\n};\n\n\n/**\n * Returns the result of the performance.now() method or null in browsers\n * that don't support the API.\n * @return {number} The elapsed time since the page was requested.\n */\nfunction now() {\n  return window.performance && performance.now && performance.now();\n}\n\n\n/**\n * Throttles a function and delays its execution, so it's only called at most\n * once within a given time period.\n * @param {Function} fn The function to throttle.\n * @param {number} timeout The amount of time that must pass before the\n *     function can be called again.\n * @return {Function} The throttled function.\n */\nfunction throttle(fn, timeout) {\n  var timer = null;\n  return function () {\n    if (!timer) {\n      timer = setTimeout(function() {\n        fn();\n        timer = null;\n      }, timeout);\n    }\n  };\n}\n\n\n/**\n * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n * @param {Node} node The DOM node to add the event handler to.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to add.\n * @param {boolean} opt_useCapture Optionally adds the even to the capture\n *     phase. Note: this only works in modern browsers.\n */\nfunction addEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.addEventListener == 'function') {\n    node.addEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.attachEvent == 'function') {\n    node.attachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Removes a previously added event handler from a DOM node.\n * @param {Node} node The DOM node to remove the event handler from.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to remove.\n * @param {boolean} opt_useCapture If the event handler was added with this\n *     flag set to true, it should be set to true here in order to remove it.\n */\nfunction removeEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.removeEventListener == 'function') {\n    node.removeEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.detachEvent == 'function') {\n    node.detachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Returns the intersection between two rect objects.\n * @param {Object} rect1 The first rect.\n * @param {Object} rect2 The second rect.\n * @return {?Object|?ClientRect} The intersection rect or undefined if no\n *     intersection is found.\n */\nfunction computeRectIntersection(rect1, rect2) {\n  var top = Math.max(rect1.top, rect2.top);\n  var bottom = Math.min(rect1.bottom, rect2.bottom);\n  var left = Math.max(rect1.left, rect2.left);\n  var right = Math.min(rect1.right, rect2.right);\n  var width = right - left;\n  var height = bottom - top;\n\n  return (width >= 0 && height >= 0) && {\n    top: top,\n    bottom: bottom,\n    left: left,\n    right: right,\n    width: width,\n    height: height\n  } || null;\n}\n\n\n/**\n * Shims the native getBoundingClientRect for compatibility with older IE.\n * @param {Element} el The element whose bounding rect to get.\n * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n */\nfunction getBoundingClientRect(el) {\n  var rect;\n\n  try {\n    rect = el.getBoundingClientRect();\n  } catch (err) {\n    // Ignore Windows 7 IE11 \"Unspecified error\"\n    // https://github.com/w3c/IntersectionObserver/pull/205\n  }\n\n  if (!rect) return getEmptyRect();\n\n  // Older IE\n  if (!(rect.width && rect.height)) {\n    rect = {\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom,\n      left: rect.left,\n      width: rect.right - rect.left,\n      height: rect.bottom - rect.top\n    };\n  }\n  return rect;\n}\n\n\n/**\n * Returns an empty rect object. An empty rect is returned when an element\n * is not in the DOM.\n * @return {ClientRect} The empty rect.\n */\nfunction getEmptyRect() {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n    width: 0,\n    height: 0\n  };\n}\n\n\n/**\n * Ensure that the result has all of the necessary fields of the DOMRect.\n * Specifically this ensures that `x` and `y` fields are set.\n *\n * @param {?DOMRect|?ClientRect} rect\n * @return {?DOMRect}\n */\nfunction ensureDOMRect(rect) {\n  // A `DOMRect` object has `x` and `y` fields.\n  if (!rect || 'x' in rect) {\n    return rect;\n  }\n  // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n  // for internally calculated Rect objects. For the purposes of\n  // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n  // for these fields.\n  return {\n    top: rect.top,\n    y: rect.top,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    right: rect.right,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n\n/**\n * Inverts the intersection and bounding rect from the parent (frame) BCR to\n * the local BCR space.\n * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n * @return {ClientRect} The local root bounding rect for the parent's children.\n */\nfunction convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n  var top = parentIntersectionRect.top - parentBoundingRect.top;\n  var left = parentIntersectionRect.left - parentBoundingRect.left;\n  return {\n    top: top,\n    left: left,\n    height: parentIntersectionRect.height,\n    width: parentIntersectionRect.width,\n    bottom: top + parentIntersectionRect.height,\n    right: left + parentIntersectionRect.width\n  };\n}\n\n\n/**\n * Checks to see if a parent element contains a child element (including inside\n * shadow DOM).\n * @param {Node} parent The parent element.\n * @param {Node} child The child element.\n * @return {boolean} True if the parent node contains the child node.\n */\nfunction containsDeep(parent, child) {\n  var node = child;\n  while (node) {\n    if (node == parent) return true;\n\n    node = getParentNode(node);\n  }\n  return false;\n}\n\n\n/**\n * Gets the parent node of an element or its host element if the parent node\n * is a shadow root.\n * @param {Node} node The node whose parent to get.\n * @return {Node|null} The parent node or null if no parent exists.\n */\nfunction getParentNode(node) {\n  var parent = node.parentNode;\n\n  if (node.nodeType == /* DOCUMENT */ 9 && node != document) {\n    // If this node is a document node, look for the embedding frame.\n    return getFrameElement(node);\n  }\n\n  // If the parent has element that is assigned through shadow root slot\n  if (parent && parent.assignedSlot) {\n    parent = parent.assignedSlot.parentNode\n  }\n\n  if (parent && parent.nodeType == 11 && parent.host) {\n    // If the parent is a shadow root, return the host element.\n    return parent.host;\n  }\n\n  return parent;\n}\n\n/**\n * Returns true if `node` is a Document.\n * @param {!Node} node\n * @returns {boolean}\n */\nfunction isDoc(node) {\n  return node && node.nodeType === 9;\n}\n\n\n// Exposes the constructors globally.\nwindow.IntersectionObserver = IntersectionObserver;\nwindow.IntersectionObserverEntry = IntersectionObserverEntry;\n\n}());\n", "\"use strict\";\n\n/* import polyfills */\nimport \"whatwg-fetch\"; // https://github.com/github/fetch\nimport \"intersection-observer\"; // https://github.com/w3c/IntersectionObserver\n"], "mappings": ";;;;;AA0BA,SAAS,WAAW,KAAK;AACvB,SAAO,OAAO,SAAS,UAAU,cAAc,GAAG;AACpD;AAsBA,SAAS,cAAc,MAAM;AAC3B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,OAAO,IAAI;AAAA,EACpB;AACA,MAAI,6BAA6B,KAAK,IAAI,KAAK,SAAS,IAAI;AAC1D,UAAM,IAAI,UAAU,8CAA8C,OAAO,GAAG;AAAA,EAC9E;AACA,SAAO,KAAK,YAAY;AAC1B;AAEA,SAAS,eAAe,OAAO;AAC7B,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,OAAO,KAAK;AAAA,EACtB;AACA,SAAO;AACT;AAGA,SAAS,YAAY,OAAO;AAC1B,MAAI,WAAW;AAAA,IACb,MAAM,WAAW;AACf,UAAI,QAAQ,MAAM,MAAM;AACxB,aAAO,EAAC,MAAM,UAAU,QAAW,MAAY;AAAA,IACjD;AAAA,EACF;AAEA,MAAI,QAAQ,UAAU;AACpB,aAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ,SAAS;AAC/B,OAAK,MAAM,CAAC;AAEZ,MAAI,mBAAmB,SAAS;AAC9B,YAAQ,QAAQ,SAAS,OAAO,MAAM;AACpC,WAAK,OAAO,MAAM,KAAK;AAAA,IACzB,GAAG,IAAI;AAAA,EACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,YAAQ,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,UAAU,GAAG;AACtB,cAAM,IAAI,UAAU,wEAAwE,OAAO,MAAM;AAAA,MAC3G;AACA,WAAK,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IAClC,GAAG,IAAI;AAAA,EACT,WAAW,SAAS;AAClB,WAAO,oBAAoB,OAAO,EAAE,QAAQ,SAAS,MAAM;AACzD,WAAK,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,IACjC,GAAG,IAAI;AAAA,EACT;AACF;AA8DA,SAAS,SAAS,MAAM;AACtB,MAAI,KAAK,QAAS;AAClB,MAAI,KAAK,UAAU;AACjB,WAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC;AAAA,EACrD;AACA,OAAK,WAAW;AAClB;AAEA,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,WAAO,SAAS,WAAW;AACzB,cAAQ,OAAO,MAAM;AAAA,IACvB;AACA,WAAO,UAAU,WAAW;AAC1B,aAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF,CAAC;AACH;AAEA,SAAS,sBAAsB,MAAM;AACnC,MAAI,SAAS,IAAI,WAAW;AAC5B,MAAI,UAAU,gBAAgB,MAAM;AACpC,SAAO,kBAAkB,IAAI;AAC7B,SAAO;AACT;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI,SAAS,IAAI,WAAW;AAC5B,MAAI,UAAU,gBAAgB,MAAM;AACpC,MAAI,QAAQ,2BAA2B,KAAK,KAAK,IAAI;AACrD,MAAI,WAAW,QAAQ,MAAM,CAAC,IAAI;AAClC,SAAO,WAAW,MAAM,QAAQ;AAChC,SAAO;AACT;AAEA,SAAS,sBAAsB,KAAK;AAClC,MAAI,OAAO,IAAI,WAAW,GAAG;AAC7B,MAAI,QAAQ,IAAI,MAAM,KAAK,MAAM;AAEjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,CAAC,IAAI,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,KAAK,EAAE;AACtB;AAEA,SAAS,YAAY,KAAK;AACxB,MAAI,IAAI,OAAO;AACb,WAAO,IAAI,MAAM,CAAC;AAAA,EACpB,OAAO;AACL,QAAI,OAAO,IAAI,WAAW,IAAI,UAAU;AACxC,SAAK,IAAI,IAAI,WAAW,GAAG,CAAC;AAC5B,WAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,OAAO;AACd,OAAK,WAAW;AAEhB,OAAK,YAAY,SAAS,MAAM;AAY9B,SAAK,WAAW,KAAK;AACrB,SAAK,YAAY;AACjB,QAAI,CAAC,MAAM;AACT,WAAK,UAAU;AACf,WAAK,YAAY;AAAA,IACnB,WAAW,OAAO,SAAS,UAAU;AACnC,WAAK,YAAY;AAAA,IACnB,WAAW,QAAQ,QAAQ,KAAK,UAAU,cAAc,IAAI,GAAG;AAC7D,WAAK,YAAY;AAAA,IACnB,WAAW,QAAQ,YAAY,SAAS,UAAU,cAAc,IAAI,GAAG;AACrE,WAAK,gBAAgB;AAAA,IACvB,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,WAAK,YAAY,KAAK,SAAS;AAAA,IACjC,WAAW,QAAQ,eAAe,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAClE,WAAK,mBAAmB,YAAY,KAAK,MAAM;AAE/C,WAAK,YAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,IACnD,WAAW,QAAQ,gBAAgB,YAAY,UAAU,cAAc,IAAI,KAAK,kBAAkB,IAAI,IAAI;AACxG,WAAK,mBAAmB,YAAY,IAAI;AAAA,IAC1C,OAAO;AACL,WAAK,YAAY,OAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,IAC7D;AAEA,QAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,GAAG;AACrC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,MAC7D,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,aAAK,QAAQ,IAAI,gBAAgB,KAAK,UAAU,IAAI;AAAA,MACtD,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,aAAK,QAAQ,IAAI,gBAAgB,iDAAiD;AAAA,MACpF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,SAAK,OAAO,WAAW;AACrB,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,UAAU;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,WAAW;AAClB,eAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,MACvC,WAAW,KAAK,kBAAkB;AAChC,eAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,MAC1D,WAAW,KAAK,eAAe;AAC7B,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD,OAAO;AACL,eAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAEA,OAAK,cAAc,WAAW;AAC5B,QAAI,KAAK,kBAAkB;AACzB,UAAI,aAAa,SAAS,IAAI;AAC9B,UAAI,YAAY;AACd,eAAO;AAAA,MACT,WAAW,YAAY,OAAO,KAAK,gBAAgB,GAAG;AACpD,eAAO,QAAQ;AAAA,UACb,KAAK,iBAAiB,OAAO;AAAA,YAC3B,KAAK,iBAAiB;AAAA,YACtB,KAAK,iBAAiB,aAAa,KAAK,iBAAiB;AAAA,UAC3D;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,MAC9C;AAAA,IACF,WAAW,QAAQ,MAAM;AACvB,aAAO,KAAK,KAAK,EAAE,KAAK,qBAAqB;AAAA,IAC/C,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAAA,EACF;AAEA,OAAK,OAAO,WAAW;AACrB,QAAI,WAAW,SAAS,IAAI;AAC5B,QAAI,UAAU;AACZ,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,WAAW;AAClB,aAAO,eAAe,KAAK,SAAS;AAAA,IACtC,WAAW,KAAK,kBAAkB;AAChC,aAAO,QAAQ,QAAQ,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,IACrE,WAAW,KAAK,eAAe;AAC7B,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD,OAAO;AACL,aAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,IACvC;AAAA,EACF;AAEA,MAAI,QAAQ,UAAU;AACpB,SAAK,WAAW,WAAW;AACzB,aAAO,KAAK,KAAK,EAAE,KAAK,MAAM;AAAA,IAChC;AAAA,EACF;AAEA,OAAK,OAAO,WAAW;AACrB,WAAO,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAKA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,UAAU,OAAO,YAAY;AACjC,SAAO,QAAQ,QAAQ,OAAO,IAAI,KAAK,UAAU;AACnD;AAEO,SAAS,QAAQ,OAAO,SAAS;AACtC,MAAI,EAAE,gBAAgB,UAAU;AAC9B,UAAM,IAAI,UAAU,4FAA4F;AAAA,EAClH;AAEA,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,QAAQ;AAEnB,MAAI,iBAAiB,SAAS;AAC5B,QAAI,MAAM,UAAU;AAClB,YAAM,IAAI,UAAU,cAAc;AAAA,IACpC;AACA,SAAK,MAAM,MAAM;AACjB,SAAK,cAAc,MAAM;AACzB,QAAI,CAAC,QAAQ,SAAS;AACpB,WAAK,UAAU,IAAI,QAAQ,MAAM,OAAO;AAAA,IAC1C;AACA,SAAK,SAAS,MAAM;AACpB,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,MAAM;AACpB,QAAI,CAAC,QAAQ,MAAM,aAAa,MAAM;AACpC,aAAO,MAAM;AACb,YAAM,WAAW;AAAA,IACnB;AAAA,EACF,OAAO;AACL,SAAK,MAAM,OAAO,KAAK;AAAA,EACzB;AAEA,OAAK,cAAc,QAAQ,eAAe,KAAK,eAAe;AAC9D,MAAI,QAAQ,WAAW,CAAC,KAAK,SAAS;AACpC,SAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAAA,EAC5C;AACA,OAAK,SAAS,gBAAgB,QAAQ,UAAU,KAAK,UAAU,KAAK;AACpE,OAAK,OAAO,QAAQ,QAAQ,KAAK,QAAQ;AACzC,OAAK,SAAS,QAAQ,UAAU,KAAK,UAAW,WAAY;AAC1D,QAAI,qBAAqB,GAAG;AAC1B,UAAI,OAAO,IAAI,gBAAgB;AAC/B,aAAO,KAAK;AAAA,IACd;AAAA,EACF,EAAE;AACF,OAAK,WAAW;AAEhB,OAAK,KAAK,WAAW,SAAS,KAAK,WAAW,WAAW,MAAM;AAC7D,UAAM,IAAI,UAAU,2CAA2C;AAAA,EACjE;AACA,OAAK,UAAU,IAAI;AAEnB,MAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,QAAI,QAAQ,UAAU,cAAc,QAAQ,UAAU,YAAY;AAEhE,UAAI,gBAAgB;AACpB,UAAI,cAAc,KAAK,KAAK,GAAG,GAAG;AAEhC,aAAK,MAAM,KAAK,IAAI,QAAQ,eAAe,UAAS,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,MAC1E,OAAO;AAEL,YAAI,gBAAgB;AACpB,aAAK,QAAQ,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM,OAAO,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AACF;AAMA,SAAS,OAAO,MAAM;AACpB,MAAI,OAAO,IAAI,SAAS;AACxB,OACG,KAAK,EACL,MAAM,GAAG,EACT,QAAQ,SAAS,OAAO;AACvB,QAAI,OAAO;AACT,UAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,UAAI,OAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG;AAC3C,UAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9C,WAAK,OAAO,mBAAmB,IAAI,GAAG,mBAAmB,KAAK,CAAC;AAAA,IACjE;AAAA,EACF,CAAC;AACH,SAAO;AACT;AAEA,SAAS,aAAa,YAAY;AAChC,MAAI,UAAU,IAAI,QAAQ;AAG1B,MAAI,sBAAsB,WAAW,QAAQ,gBAAgB,GAAG;AAIhE,sBACG,MAAM,IAAI,EACV,IAAI,SAAS,QAAQ;AACpB,WAAO,OAAO,QAAQ,IAAI,MAAM,IAAI,OAAO,OAAO,GAAG,OAAO,MAAM,IAAI;AAAA,EACxE,CAAC,EACA,QAAQ,SAAS,MAAM;AACtB,QAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,QAAI,MAAM,MAAM,MAAM,EAAE,KAAK;AAC7B,QAAI,KAAK;AACP,UAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK;AACjC,UAAI;AACF,gBAAQ,OAAO,KAAK,KAAK;AAAA,MAC3B,SAAS,OAAO;AACd,gBAAQ,KAAK,cAAc,MAAM,OAAO;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,CAAC;AACH,SAAO;AACT;AAIO,SAAS,SAAS,UAAU,SAAS;AAC1C,MAAI,EAAE,gBAAgB,WAAW;AAC/B,UAAM,IAAI,UAAU,4FAA4F;AAAA,EAClH;AACA,MAAI,CAAC,SAAS;AACZ,cAAU,CAAC;AAAA,EACb;AAEA,OAAK,OAAO;AACZ,OAAK,SAAS,QAAQ,WAAW,SAAY,MAAM,QAAQ;AAC3D,MAAI,KAAK,SAAS,OAAO,KAAK,SAAS,KAAK;AAC1C,UAAM,IAAI,WAAW,0FAA0F;AAAA,EACjH;AACA,OAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,OAAK,aAAa,QAAQ,eAAe,SAAY,KAAK,KAAK,QAAQ;AACvE,OAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAC1C,OAAK,MAAM,QAAQ,OAAO;AAC1B,OAAK,UAAU,QAAQ;AACzB;AA6CO,SAAS,MAAM,OAAO,MAAM;AACjC,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,UAAU,IAAI,QAAQ,OAAO,IAAI;AAErC,QAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS;AAC5C,aAAO,OAAO,IAAI,aAAa,WAAW,YAAY,CAAC;AAAA,IACzD;AAEA,QAAI,MAAM,IAAI,eAAe;AAE7B,aAAS,WAAW;AAClB,UAAI,MAAM;AAAA,IACZ;AAEA,QAAI,SAAS,WAAW;AACtB,UAAI,UAAU;AAAA,QACZ,YAAY,IAAI;AAAA,QAChB,SAAS,aAAa,IAAI,sBAAsB,KAAK,EAAE;AAAA,MACzD;AAGA,UAAI,QAAQ,IAAI,QAAQ,SAAS,MAAM,MAAM,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM;AAClF,gBAAQ,SAAS;AAAA,MACnB,OAAO;AACL,gBAAQ,SAAS,IAAI;AAAA,MACvB;AACA,cAAQ,MAAM,iBAAiB,MAAM,IAAI,cAAc,QAAQ,QAAQ,IAAI,eAAe;AAC1F,UAAI,OAAO,cAAc,MAAM,IAAI,WAAW,IAAI;AAClD,iBAAW,WAAW;AACpB,gBAAQ,IAAI,SAAS,MAAM,OAAO,CAAC;AAAA,MACrC,GAAG,CAAC;AAAA,IACN;AAEA,QAAI,UAAU,WAAW;AACvB,iBAAW,WAAW;AACpB,eAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,MAChD,GAAG,CAAC;AAAA,IACN;AAEA,QAAI,YAAY,WAAW;AACzB,iBAAW,WAAW;AACpB,eAAO,IAAI,UAAU,2BAA2B,CAAC;AAAA,MACnD,GAAG,CAAC;AAAA,IACN;AAEA,QAAI,UAAU,WAAW;AACvB,iBAAW,WAAW;AACpB,eAAO,IAAI,aAAa,WAAW,YAAY,CAAC;AAAA,MAClD,GAAG,CAAC;AAAA,IACN;AAEA,aAAS,OAAO,KAAK;AACnB,UAAI;AACF,eAAO,QAAQ,MAAM,EAAE,SAAS,OAAO,EAAE,SAAS,OAAO;AAAA,MAC3D,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,QAAQ,OAAO,QAAQ,GAAG,GAAG,IAAI;AAElD,QAAI,QAAQ,gBAAgB,WAAW;AACrC,UAAI,kBAAkB;AAAA,IACxB,WAAW,QAAQ,gBAAgB,QAAQ;AACzC,UAAI,kBAAkB;AAAA,IACxB;AAEA,QAAI,kBAAkB,KAAK;AACzB,UAAI,QAAQ,MAAM;AAChB,YAAI,eAAe;AAAA,MACrB,WACE,QAAQ,aACR;AACA,YAAI,eAAe;AAAA,MACrB;AAAA,IACF;AAEA,QAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,EAAE,KAAK,mBAAmB,WAAY,EAAE,WAAW,KAAK,mBAAmB,EAAE,UAAW;AACtI,UAAI,QAAQ,CAAC;AACb,aAAO,oBAAoB,KAAK,OAAO,EAAE,QAAQ,SAAS,MAAM;AAC9D,cAAM,KAAK,cAAc,IAAI,CAAC;AAC9B,YAAI,iBAAiB,MAAM,eAAe,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,MAC/D,CAAC;AACD,cAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,YAAI,MAAM,QAAQ,IAAI,MAAM,IAAI;AAC9B,cAAI,iBAAiB,MAAM,KAAK;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,YAAI,iBAAiB,MAAM,KAAK;AAAA,MAClC,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,QAAQ;AAClB,cAAQ,OAAO,iBAAiB,SAAS,QAAQ;AAEjD,UAAI,qBAAqB,WAAW;AAElC,YAAI,IAAI,eAAe,GAAG;AACxB,kBAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,OAAO,QAAQ,cAAc,cAAc,OAAO,QAAQ,SAAS;AAAA,EAC9E,CAAC;AACH;AAxnBA,IACI,GAOA,SAuBE,aAYA,mBA2SF,SA+JA,kBAUO;AA/fX;AAAA;AACA,IAAI,IACD,OAAO,eAAe,eAAe,cACrC,OAAO,SAAS,eAAe;AAAA,IAE/B,OAAO,WAAW,eAAe,UAClC,CAAC;AAEH,IAAI,UAAU;AAAA,MACZ,cAAc,qBAAqB;AAAA,MACnC,UAAU,YAAY,KAAK,cAAc;AAAA,MACzC,MACE,gBAAgB,KAChB,UAAU,KACT,WAAW;AACV,YAAI;AACF,cAAI,KAAK;AACT,iBAAO;AAAA,QACT,SAAS,GAAG;AACV,iBAAO;AAAA,QACT;AAAA,MACF,EAAG;AAAA,MACL,UAAU,cAAc;AAAA,MACxB,aAAa,iBAAiB;AAAA,IAChC;AAMA,QAAI,QAAQ,aAAa;AACnB,oBAAc;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEI,0BACF,YAAY,UACZ,SAAS,KAAK;AACZ,eAAO,OAAO,YAAY,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI;AAAA,MAC3E;AAAA,IACJ;AA0DA,YAAQ,UAAU,SAAS,SAAS,MAAM,OAAO;AAC/C,aAAO,cAAc,IAAI;AACzB,cAAQ,eAAe,KAAK;AAC5B,UAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,WAAK,IAAI,IAAI,IAAI,WAAW,WAAW,OAAO,QAAQ;AAAA,IACxD;AAEA,YAAQ,UAAU,QAAQ,IAAI,SAAS,MAAM;AAC3C,aAAO,KAAK,IAAI,cAAc,IAAI,CAAC;AAAA,IACrC;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,aAAO,cAAc,IAAI;AACzB,aAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,IAC3C;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,aAAO,KAAK,IAAI,eAAe,cAAc,IAAI,CAAC;AAAA,IACpD;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM,OAAO;AAC5C,WAAK,IAAI,cAAc,IAAI,CAAC,IAAI,eAAe,KAAK;AAAA,IACtD;AAEA,YAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACtD,eAAS,QAAQ,KAAK,KAAK;AACzB,YAAI,KAAK,IAAI,eAAe,IAAI,GAAG;AACjC,mBAAS,KAAK,SAAS,KAAK,IAAI,IAAI,GAAG,MAAM,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,UAAU,OAAO,WAAW;AAClC,UAAI,QAAQ,CAAC;AACb,WAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,cAAM,KAAK,IAAI;AAAA,MACjB,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B;AAEA,YAAQ,UAAU,SAAS,WAAW;AACpC,UAAI,QAAQ,CAAC;AACb,WAAK,QAAQ,SAAS,OAAO;AAC3B,cAAM,KAAK,KAAK;AAAA,MAClB,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B;AAEA,YAAQ,UAAU,UAAU,WAAW;AACrC,UAAI,QAAQ,CAAC;AACb,WAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,cAAM,KAAK,CAAC,MAAM,KAAK,CAAC;AAAA,MAC1B,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B;AAEA,QAAI,QAAQ,UAAU;AACpB,cAAQ,UAAU,OAAO,QAAQ,IAAI,QAAQ,UAAU;AAAA,IACzD;AAkLA,IAAI,UAAU,CAAC,WAAW,UAAU,OAAO,QAAQ,WAAW,SAAS,QAAQ,OAAO,OAAO;AAsE7F,YAAQ,UAAU,QAAQ,WAAW;AACnC,aAAO,IAAI,QAAQ,MAAM,EAAC,MAAM,KAAK,UAAS,CAAC;AAAA,IACjD;AA8CA,SAAK,KAAK,QAAQ,SAAS;AAsB3B,SAAK,KAAK,SAAS,SAAS;AAE5B,aAAS,UAAU,QAAQ,WAAW;AACpC,aAAO,IAAI,SAAS,KAAK,WAAW;AAAA,QAClC,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK;AAAA,QACjB,SAAS,IAAI,QAAQ,KAAK,OAAO;AAAA,QACjC,KAAK,KAAK;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,aAAS,QAAQ,WAAW;AAC1B,UAAI,WAAW,IAAI,SAAS,MAAM,EAAC,QAAQ,KAAK,YAAY,GAAE,CAAC;AAC/D,eAAS,KAAK;AACd,eAAS,SAAS;AAClB,eAAS,OAAO;AAChB,aAAO;AAAA,IACT;AAEA,IAAI,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAE/C,aAAS,WAAW,SAAS,KAAK,QAAQ;AACxC,UAAI,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAC3C,cAAM,IAAI,WAAW,qBAAqB;AAAA,MAC5C;AAEA,aAAO,IAAI,SAAS,MAAM,EAAC,QAAgB,SAAS,EAAC,UAAU,IAAG,EAAC,CAAC;AAAA,IACtE;AAEO,IAAI,eAAe,EAAE;AAC5B,QAAI;AACF,UAAI,aAAa;AAAA,IACnB,SAAS,KAAK;AACZ,qBAAe,SAAS,SAAS,MAAM;AACrC,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,YAAI,QAAQ,MAAM,OAAO;AACzB,aAAK,QAAQ,MAAM;AAAA,MACrB;AACA,mBAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AACtD,mBAAa,UAAU,cAAc;AAAA,IACvC;AA+GA,UAAM,WAAW;AAEjB,QAAI,CAAC,EAAE,OAAO;AACZ,QAAE,QAAQ;AACV,QAAE,UAAU;AACZ,QAAE,UAAU;AACZ,QAAE,WAAW;AAAA,IACf;AAAA;AAAA;;;ACjoBA;AAAA;AAQA,KAAC,WAAW;AACZ;AAGA,UAAI,OAAO,WAAW,UAAU;AAC9B;AAAA,MACF;AAIA,UAAI,0BAA0B,UAC1B,+BAA+B,UAC/B,uBAAuB,OAAO,0BAA0B,WAAW;AAIrE,YAAI,EAAE,oBAAoB,OAAO,0BAA0B,YAAY;AACrE,iBAAO;AAAA,YAAe,OAAO,0BAA0B;AAAA,YACrD;AAAA,YAAkB;AAAA,cAClB,KAAK,WAAY;AACf,uBAAO,KAAK,oBAAoB;AAAA,cAClC;AAAA,YACF;AAAA,UAAC;AAAA,QACH;AACA;AAAA,MACF;AAOA,eAAS,gBAAgB,KAAK;AAC5B,YAAI;AACF,iBAAO,IAAI,eAAe,IAAI,YAAY,gBAAgB;AAAA,QAC5D,SAAS,GAAG;AAEV,iBAAO;AAAA,QACT;AAAA,MACF;AAKA,UAAI,WAAY,SAAS,UAAU;AACjC,YAAI,MAAM;AACV,YAAI,QAAQ,gBAAgB,GAAG;AAC/B,eAAO,OAAO;AACZ,gBAAM,MAAM;AACZ,kBAAQ,gBAAgB,GAAG;AAAA,QAC7B;AACA,eAAO;AAAA,MACT,EAAG,OAAO,QAAQ;AAQlB,UAAI,WAAW,CAAC;AAOhB,UAAI,qBAAqB;AAMzB,UAAI,kBAAkB;AAStB,eAAS,0BAA0B,OAAO;AACxC,aAAK,OAAO,MAAM;AAClB,aAAK,SAAS,MAAM;AACpB,aAAK,aAAa,cAAc,MAAM,UAAU;AAChD,aAAK,qBAAqB,cAAc,MAAM,kBAAkB;AAChE,aAAK,mBAAmB,cAAc,MAAM,oBAAoB,aAAa,CAAC;AAC9E,aAAK,iBAAiB,CAAC,CAAC,MAAM;AAG9B,YAAI,aAAa,KAAK;AACtB,YAAI,aAAa,WAAW,QAAQ,WAAW;AAC/C,YAAI,mBAAmB,KAAK;AAC5B,YAAI,mBAAmB,iBAAiB,QAAQ,iBAAiB;AAGjE,YAAI,YAAY;AAGd,eAAK,oBAAoB,QAAQ,mBAAmB,YAAY,QAAQ,CAAC,CAAC;AAAA,QAC5E,OAAO;AAEL,eAAK,oBAAoB,KAAK,iBAAiB,IAAI;AAAA,QACrD;AAAA,MACF;AAYA,eAAS,qBAAqB,UAAU,aAAa;AAEnD,YAAI,UAAU,eAAe,CAAC;AAE9B,YAAI,OAAO,YAAY,YAAY;AACjC,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QAC/C;AAEA,YACE,QAAQ,QACR,QAAQ,KAAK,YAAY,KACzB,QAAQ,KAAK,YAAY,GACzB;AACA,gBAAM,IAAI,MAAM,oCAAoC;AAAA,QACtD;AAGA,aAAK,yBAAyB;AAAA,UAC1B,KAAK,uBAAuB,KAAK,IAAI;AAAA,UAAG,KAAK;AAAA,QAAgB;AAGjE,aAAK,YAAY;AACjB,aAAK,sBAAsB,CAAC;AAC5B,aAAK,iBAAiB,CAAC;AACvB,aAAK,oBAAoB,KAAK,iBAAiB,QAAQ,UAAU;AAGjE,aAAK,aAAa,KAAK,gBAAgB,QAAQ,SAAS;AACxD,aAAK,OAAO,QAAQ,QAAQ;AAC5B,aAAK,aAAa,KAAK,kBAAkB,IAAI,SAAS,QAAQ;AAC5D,iBAAO,OAAO,QAAQ,OAAO;AAAA,QAC/B,CAAC,EAAE,KAAK,GAAG;AAGX,aAAK,uBAAuB,CAAC;AAE7B,aAAK,0BAA0B,CAAC;AAAA,MAClC;AAOA,2BAAqB,UAAU,mBAAmB;AAQlD,2BAAqB,UAAU,gBAAgB;AAM/C,2BAAqB,UAAU,wBAAwB;AAYvD,2BAAqB,2BAA2B,WAAW;AACzD,YAAI,CAAC,oBAAoB;AAKvB,+BAAqB,SAAS,oBAAoB,kBAAkB;AAClE,gBAAI,CAAC,sBAAsB,CAAC,kBAAkB;AAC5C,gCAAkB,aAAa;AAAA,YACjC,OAAO;AACL,gCAAkB,sBAAsB,oBAAoB,gBAAgB;AAAA,YAC9E;AACA,qBAAS,QAAQ,SAAS,UAAU;AAClC,uBAAS,uBAAuB;AAAA,YAClC,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAMA,2BAAqB,2BAA2B,WAAW;AACzD,6BAAqB;AACrB,0BAAkB;AAAA,MACpB;AAQA,2BAAqB,UAAU,UAAU,SAAS,QAAQ;AACxD,YAAI,0BAA0B,KAAK,oBAAoB,KAAK,SAAS,MAAM;AACzE,iBAAO,KAAK,WAAW;AAAA,QACzB,CAAC;AAED,YAAI,yBAAyB;AAC3B;AAAA,QACF;AAEA,YAAI,EAAE,UAAU,OAAO,YAAY,IAAI;AACrC,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AAEA,aAAK,kBAAkB;AACvB,aAAK,oBAAoB,KAAK,EAAC,SAAS,QAAQ,OAAO,KAAI,CAAC;AAC5D,aAAK,sBAAsB,OAAO,aAAa;AAC/C,aAAK,uBAAuB;AAAA,MAC9B;AAOA,2BAAqB,UAAU,YAAY,SAAS,QAAQ;AAC1D,aAAK,sBACD,KAAK,oBAAoB,OAAO,SAAS,MAAM;AAC7C,iBAAO,KAAK,WAAW;AAAA,QACzB,CAAC;AACL,aAAK,wBAAwB,OAAO,aAAa;AACjD,YAAI,KAAK,oBAAoB,UAAU,GAAG;AACxC,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF;AAMA,2BAAqB,UAAU,aAAa,WAAW;AACrD,aAAK,sBAAsB,CAAC;AAC5B,aAAK,2BAA2B;AAChC,aAAK,oBAAoB;AAAA,MAC3B;AASA,2BAAqB,UAAU,cAAc,WAAW;AACtD,YAAI,UAAU,KAAK,eAAe,MAAM;AACxC,aAAK,iBAAiB,CAAC;AACvB,eAAO;AAAA,MACT;AAYA,2BAAqB,UAAU,kBAAkB,SAAS,eAAe;AACvE,YAAI,YAAY,iBAAiB,CAAC,CAAC;AACnC,YAAI,CAAC,MAAM,QAAQ,SAAS,EAAG,aAAY,CAAC,SAAS;AAErD,eAAO,UAAU,KAAK,EAAE,OAAO,SAAS,GAAG,GAAG,GAAG;AAC/C,cAAI,OAAO,KAAK,YAAY,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AACtD,kBAAM,IAAI,MAAM,wDAAwD;AAAA,UAC1E;AACA,iBAAO,MAAM,EAAE,IAAI,CAAC;AAAA,QACtB,CAAC;AAAA,MACH;AAcA,2BAAqB,UAAU,mBAAmB,SAAS,gBAAgB;AACzE,YAAI,eAAe,kBAAkB;AACrC,YAAI,UAAU,aAAa,MAAM,KAAK,EAAE,IAAI,SAAS,QAAQ;AAC3D,cAAI,QAAQ,wBAAwB,KAAK,MAAM;AAC/C,cAAI,CAAC,OAAO;AACV,kBAAM,IAAI,MAAM,mDAAmD;AAAA,UACrE;AACA,iBAAO,EAAC,OAAO,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,EAAC;AAAA,QACrD,CAAC;AAGD,gBAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACpC,gBAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACpC,gBAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAEpC,eAAO;AAAA,MACT;AASA,2BAAqB,UAAU,wBAAwB,SAAS,KAAK;AACnE,YAAI,MAAM,IAAI;AACd,YAAI,CAAC,KAAK;AAER;AAAA,QACF;AACA,YAAI,KAAK,qBAAqB,QAAQ,GAAG,KAAK,IAAI;AAEhD;AAAA,QACF;AAGA,YAAI,WAAW,KAAK;AACpB,YAAI,qBAAqB;AACzB,YAAI,cAAc;AAIlB,YAAI,KAAK,eAAe;AACtB,+BAAqB,IAAI,YAAY,UAAU,KAAK,aAAa;AAAA,QACnE,OAAO;AACL,mBAAS,KAAK,UAAU,UAAU,IAAI;AACtC,mBAAS,KAAK,UAAU,UAAU,IAAI;AACtC,cAAI,KAAK,yBAAyB,sBAAsB,KAAK;AAC3D,0BAAc,IAAI,IAAI,iBAAiB,QAAQ;AAC/C,wBAAY,QAAQ,KAAK;AAAA,cACvB,YAAY;AAAA,cACZ,WAAW;AAAA,cACX,eAAe;AAAA,cACf,SAAS;AAAA,YACX,CAAC;AAAA,UACH;AAAA,QACF;AAEA,aAAK,qBAAqB,KAAK,GAAG;AAClC,aAAK,wBAAwB,KAAK,WAAW;AAG3C,cAAIA,OAAM,IAAI;AAEd,cAAIA,MAAK;AACP,gBAAI,oBAAoB;AACtB,cAAAA,KAAI,cAAc,kBAAkB;AAAA,YACtC;AACA,wBAAYA,MAAK,UAAU,UAAU,IAAI;AAAA,UAC3C;AAEA,sBAAY,KAAK,UAAU,UAAU,IAAI;AACzC,cAAI,aAAa;AACf,wBAAY,WAAW;AAAA,UACzB;AAAA,QACF,CAAC;AAGD,YAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAU;AAC3D,YAAI,OAAO,SAAS;AAClB,cAAI,QAAQ,gBAAgB,GAAG;AAC/B,cAAI,OAAO;AACT,iBAAK,sBAAsB,MAAM,aAAa;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAQA,2BAAqB,UAAU,0BAA0B,SAAS,KAAK;AACrE,YAAI,QAAQ,KAAK,qBAAqB,QAAQ,GAAG;AACjD,YAAI,SAAS,IAAI;AACf;AAAA,QACF;AAEA,YAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAU;AAG3D,YAAI,sBACA,KAAK,oBAAoB,KAAK,SAAS,MAAM;AAC3C,cAAI,UAAU,KAAK,QAAQ;AAE3B,cAAI,WAAW,KAAK;AAClB,mBAAO;AAAA,UACT;AAEA,iBAAO,WAAW,WAAW,SAAS;AACpC,gBAAIC,SAAQ,gBAAgB,OAAO;AACnC,sBAAUA,UAASA,OAAM;AACzB,gBAAI,WAAW,KAAK;AAClB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AACL,YAAI,qBAAqB;AACvB;AAAA,QACF;AAGA,YAAI,cAAc,KAAK,wBAAwB,KAAK;AACpD,aAAK,qBAAqB,OAAO,OAAO,CAAC;AACzC,aAAK,wBAAwB,OAAO,OAAO,CAAC;AAC5C,oBAAY;AAGZ,YAAI,OAAO,SAAS;AAClB,cAAI,QAAQ,gBAAgB,GAAG;AAC/B,cAAI,OAAO;AACT,iBAAK,wBAAwB,MAAM,aAAa;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAQA,2BAAqB,UAAU,6BAA6B,WAAW;AACrE,YAAI,eAAe,KAAK,wBAAwB,MAAM,CAAC;AACvD,aAAK,qBAAqB,SAAS;AACnC,aAAK,wBAAwB,SAAS;AACtC,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,uBAAa,CAAC,EAAE;AAAA,QAClB;AAAA,MACF;AASA,2BAAqB,UAAU,yBAAyB,WAAW;AACjE,YAAI,CAAC,KAAK,QAAQ,sBAAsB,CAAC,iBAAiB;AAExD;AAAA,QACF;AAEA,YAAI,cAAc,KAAK,aAAa;AACpC,YAAI,WAAW,cAAc,KAAK,aAAa,IAAI,aAAa;AAEhE,aAAK,oBAAoB,QAAQ,SAAS,MAAM;AAC9C,cAAI,SAAS,KAAK;AAClB,cAAI,aAAa,sBAAsB,MAAM;AAC7C,cAAI,qBAAqB,KAAK,oBAAoB,MAAM;AACxD,cAAI,WAAW,KAAK;AACpB,cAAI,mBAAmB,eAAe,sBAClC,KAAK,kCAAkC,QAAQ,YAAY,QAAQ;AAEvE,cAAI,aAAa;AACjB,cAAI,CAAC,KAAK,oBAAoB,MAAM,GAAG;AACrC,yBAAa,aAAa;AAAA,UAC5B,WAAW,CAAC,sBAAsB,KAAK,MAAM;AAC3C,yBAAa;AAAA,UACf;AAEA,cAAI,WAAW,KAAK,QAAQ,IAAI,0BAA0B;AAAA,YACxD,MAAM,IAAI;AAAA,YACV;AAAA,YACA,oBAAoB;AAAA,YACpB;AAAA,YACA;AAAA,UACF,CAAC;AAED,cAAI,CAAC,UAAU;AACb,iBAAK,eAAe,KAAK,QAAQ;AAAA,UACnC,WAAW,eAAe,oBAAoB;AAG5C,gBAAI,KAAK,qBAAqB,UAAU,QAAQ,GAAG;AACjD,mBAAK,eAAe,KAAK,QAAQ;AAAA,YACnC;AAAA,UACF,OAAO;AAIL,gBAAI,YAAY,SAAS,gBAAgB;AACvC,mBAAK,eAAe,KAAK,QAAQ;AAAA,YACnC;AAAA,UACF;AAAA,QACF,GAAG,IAAI;AAEP,YAAI,KAAK,eAAe,QAAQ;AAC9B,eAAK,UAAU,KAAK,YAAY,GAAG,IAAI;AAAA,QACzC;AAAA,MACF;AAgBA,2BAAqB,UAAU,oCAC3B,SAAS,QAAQ,YAAY,UAAU;AAEzC,YAAI,OAAO,iBAAiB,MAAM,EAAE,WAAW,OAAQ;AAEvD,YAAI,mBAAmB;AACvB,YAAI,SAAS,cAAc,MAAM;AACjC,YAAI,SAAS;AAEb,eAAO,CAAC,UAAU,QAAQ;AACxB,cAAI,aAAa;AACjB,cAAI,sBAAsB,OAAO,YAAY,IACzC,OAAO,iBAAiB,MAAM,IAAI,CAAC;AAGvC,cAAI,oBAAoB,WAAW,OAAQ,QAAO;AAElD,cAAI,UAAU,KAAK,QAAQ,OAAO;AAAA,UAA2B,GAAG;AAC9D,qBAAS;AACT,gBAAI,UAAU,KAAK,QAAQ,UAAU,UAAU;AAC7C,kBAAI,sBAAsB,CAAC,KAAK,MAAM;AACpC,oBAAI,CAAC,mBACD,gBAAgB,SAAS,KAAK,gBAAgB,UAAU,GAAG;AAE7D,2BAAS;AACT,+BAAa;AACb,qCAAmB;AAAA,gBACrB,OAAO;AACL,+BAAa;AAAA,gBACf;AAAA,cACF,OAAO;AACL,6BAAa;AAAA,cACf;AAAA,YACF,OAAO;AAEL,kBAAI,QAAQ,cAAc,MAAM;AAChC,kBAAI,YAAY,SAAS,sBAAsB,KAAK;AACpD,kBAAI,iBACA,SACA,KAAK,kCAAkC,OAAO,WAAW,QAAQ;AACrE,kBAAI,aAAa,gBAAgB;AAC/B,yBAAS;AACT,6BAAa,sBAAsB,WAAW,cAAc;AAAA,cAC9D,OAAO;AACL,yBAAS;AACT,mCAAmB;AAAA,cACrB;AAAA,YACF;AAAA,UACF,OAAO;AAKL,gBAAI,MAAM,OAAO;AACjB,gBAAI,UAAU,IAAI,QACd,UAAU,IAAI,mBACd,oBAAoB,YAAY,WAAW;AAC7C,2BAAa,sBAAsB,MAAM;AAAA,YAC3C;AAAA,UACF;AAIA,cAAI,YAAY;AACd,+BAAmB,wBAAwB,YAAY,gBAAgB;AAAA,UACzE;AACA,cAAI,CAAC,iBAAkB;AACvB,mBAAS,UAAU,cAAc,MAAM;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AAQA,2BAAqB,UAAU,eAAe,WAAW;AACvD,YAAI;AACJ,YAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,GAAG;AAClC,qBAAW,sBAAsB,KAAK,IAAI;AAAA,QAC5C,OAAO;AAEL,cAAI,MAAM,MAAM,KAAK,IAAI,IAAI,KAAK,OAAO;AACzC,cAAI,OAAO,IAAI;AACf,cAAI,OAAO,IAAI;AACf,qBAAW;AAAA,YACT,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO,KAAK,eAAe,KAAK;AAAA,YAChC,OAAO,KAAK,eAAe,KAAK;AAAA,YAChC,QAAQ,KAAK,gBAAgB,KAAK;AAAA,YAClC,QAAQ,KAAK,gBAAgB,KAAK;AAAA,UACpC;AAAA,QACF;AACA,eAAO,KAAK,wBAAwB,QAAQ;AAAA,MAC9C;AASA,2BAAqB,UAAU,0BAA0B,SAAS,MAAM;AACtE,YAAI,UAAU,KAAK,kBAAkB,IAAI,SAAS,QAAQ,GAAG;AAC3D,iBAAO,OAAO,QAAQ,OAAO,OAAO,QAChC,OAAO,SAAS,IAAI,IAAI,KAAK,QAAQ,KAAK,UAAU;AAAA,QAC1D,CAAC;AACD,YAAI,UAAU;AAAA,UACZ,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,UACzB,OAAO,KAAK,QAAQ,QAAQ,CAAC;AAAA,UAC7B,QAAQ,KAAK,SAAS,QAAQ,CAAC;AAAA,UAC/B,MAAM,KAAK,OAAO,QAAQ,CAAC;AAAA,QAC7B;AACA,gBAAQ,QAAQ,QAAQ,QAAQ,QAAQ;AACxC,gBAAQ,SAAS,QAAQ,SAAS,QAAQ;AAE1C,eAAO;AAAA,MACT;AAaA,2BAAqB,UAAU,uBAC3B,SAAS,UAAU,UAAU;AAI/B,YAAI,WAAW,YAAY,SAAS,iBAChC,SAAS,qBAAqB,IAAI;AACtC,YAAI,WAAW,SAAS,iBACpB,SAAS,qBAAqB,IAAI;AAGtC,YAAI,aAAa,SAAU;AAE3B,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,cAAI,YAAY,KAAK,WAAW,CAAC;AAIjC,cAAI,aAAa,YAAY,aAAa,YACtC,YAAY,aAAa,YAAY,UAAU;AACjD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAQA,2BAAqB,UAAU,eAAe,WAAW;AACvD,eAAO,CAAC,KAAK,QAAQ,aAAa,UAAU,KAAK,IAAI;AAAA,MACvD;AASA,2BAAqB,UAAU,sBAAsB,SAAS,QAAQ;AACpE,YAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAU;AAC3D,eACE,aAAa,SAAS,MAAM,MAC3B,CAAC,KAAK,QAAQ,WAAW,OAAO;AAAA,MAErC;AAQA,2BAAqB,UAAU,oBAAoB,WAAW;AAC5D,YAAI,SAAS,QAAQ,IAAI,IAAI,GAAG;AAC9B,mBAAS,KAAK,IAAI;AAAA,QACpB;AAAA,MACF;AAOA,2BAAqB,UAAU,sBAAsB,WAAW;AAC9D,YAAI,QAAQ,SAAS,QAAQ,IAAI;AACjC,YAAI,SAAS,GAAI,UAAS,OAAO,OAAO,CAAC;AAAA,MAC3C;AAQA,eAAS,MAAM;AACb,eAAO,OAAO,eAAe,YAAY,OAAO,YAAY,IAAI;AAAA,MAClE;AAWA,eAAS,SAAS,IAAI,SAAS;AAC7B,YAAI,QAAQ;AACZ,eAAO,WAAY;AACjB,cAAI,CAAC,OAAO;AACV,oBAAQ,WAAW,WAAW;AAC5B,iBAAG;AACH,sBAAQ;AAAA,YACV,GAAG,OAAO;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAWA,eAAS,SAAS,MAAM,OAAO,IAAI,gBAAgB;AACjD,YAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,eAAK,iBAAiB,OAAO,IAAI,kBAAkB,KAAK;AAAA,QAC1D,WACS,OAAO,KAAK,eAAe,YAAY;AAC9C,eAAK,YAAY,OAAO,OAAO,EAAE;AAAA,QACnC;AAAA,MACF;AAWA,eAAS,YAAY,MAAM,OAAO,IAAI,gBAAgB;AACpD,YAAI,OAAO,KAAK,uBAAuB,YAAY;AACjD,eAAK,oBAAoB,OAAO,IAAI,kBAAkB,KAAK;AAAA,QAC7D,WACS,OAAO,KAAK,eAAe,YAAY;AAC9C,eAAK,YAAY,OAAO,OAAO,EAAE;AAAA,QACnC;AAAA,MACF;AAUA,eAAS,wBAAwB,OAAO,OAAO;AAC7C,YAAI,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,GAAG;AACvC,YAAI,SAAS,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAChD,YAAI,OAAO,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI;AAC1C,YAAI,QAAQ,KAAK,IAAI,MAAM,OAAO,MAAM,KAAK;AAC7C,YAAI,QAAQ,QAAQ;AACpB,YAAI,SAAS,SAAS;AAEtB,eAAQ,SAAS,KAAK,UAAU,KAAM;AAAA,UACpC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,KAAK;AAAA,MACP;AAQA,eAAS,sBAAsB,IAAI;AACjC,YAAI;AAEJ,YAAI;AACF,iBAAO,GAAG,sBAAsB;AAAA,QAClC,SAAS,KAAK;AAAA,QAGd;AAEA,YAAI,CAAC,KAAM,QAAO,aAAa;AAG/B,YAAI,EAAE,KAAK,SAAS,KAAK,SAAS;AAChC,iBAAO;AAAA,YACL,KAAK,KAAK;AAAA,YACV,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,MAAM,KAAK;AAAA,YACX,OAAO,KAAK,QAAQ,KAAK;AAAA,YACzB,QAAQ,KAAK,SAAS,KAAK;AAAA,UAC7B;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAQA,eAAS,eAAe;AACtB,eAAO;AAAA,UACL,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACF;AAUA,eAAS,cAAc,MAAM;AAE3B,YAAI,CAAC,QAAQ,OAAO,MAAM;AACxB,iBAAO;AAAA,QACT;AAKA,eAAO;AAAA,UACL,KAAK,KAAK;AAAA,UACV,GAAG,KAAK;AAAA,UACR,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,GAAG,KAAK;AAAA,UACR,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,QACf;AAAA,MACF;AAUA,eAAS,sBAAsB,oBAAoB,wBAAwB;AACzE,YAAI,MAAM,uBAAuB,MAAM,mBAAmB;AAC1D,YAAI,OAAO,uBAAuB,OAAO,mBAAmB;AAC5D,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,uBAAuB;AAAA,UAC/B,OAAO,uBAAuB;AAAA,UAC9B,QAAQ,MAAM,uBAAuB;AAAA,UACrC,OAAO,OAAO,uBAAuB;AAAA,QACvC;AAAA,MACF;AAUA,eAAS,aAAa,QAAQ,OAAO;AACnC,YAAI,OAAO;AACX,eAAO,MAAM;AACX,cAAI,QAAQ,OAAQ,QAAO;AAE3B,iBAAO,cAAc,IAAI;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,MAAM;AAC3B,YAAI,SAAS,KAAK;AAElB,YAAI,KAAK;AAAA,QAA2B,KAAK,QAAQ,UAAU;AAEzD,iBAAO,gBAAgB,IAAI;AAAA,QAC7B;AAGA,YAAI,UAAU,OAAO,cAAc;AACjC,mBAAS,OAAO,aAAa;AAAA,QAC/B;AAEA,YAAI,UAAU,OAAO,YAAY,MAAM,OAAO,MAAM;AAElD,iBAAO,OAAO;AAAA,QAChB;AAEA,eAAO;AAAA,MACT;AAOA,eAAS,MAAM,MAAM;AACnB,eAAO,QAAQ,KAAK,aAAa;AAAA,MACnC;AAIA,aAAO,uBAAuB;AAC9B,aAAO,4BAA4B;AAAA,IAEnC,GAAE;AAAA;AAAA;;;ACr/BF;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;", "names": ["win", "frame"]}