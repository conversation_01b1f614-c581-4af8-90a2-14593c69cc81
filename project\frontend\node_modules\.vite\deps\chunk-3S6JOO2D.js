import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseSum.js
var require_baseSum = __commonJS({
  "node_modules/lodash/_baseSum.js"(exports, module) {
    function baseSum(array, iteratee) {
      var result, index = -1, length = array.length;
      while (++index < length) {
        var current = iteratee(array[index]);
        if (current !== void 0) {
          result = result === void 0 ? current : result + current;
        }
      }
      return result;
    }
    module.exports = baseSum;
  }
});

export {
  require_baseSum
};
//# sourceMappingURL=chunk-3S6JOO2D.js.map
