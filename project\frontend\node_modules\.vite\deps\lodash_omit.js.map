{"version": 3, "sources": ["../../lodash/_customOmitClone.js", "../../lodash/omit.js"], "sourcesContent": ["var isPlainObject = require('./isPlainObject');\n\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\n\nmodule.exports = customOmitClone;\n", "var arrayMap = require('./_arrayMap'),\n    baseClone = require('./_baseClone'),\n    baseUnset = require('./_baseUnset'),\n    castPath = require('./_castPath'),\n    copyObject = require('./_copyObject'),\n    customOmitClone = require('./_customOmitClone'),\n    flatRest = require('./_flatRest'),\n    getAllKeysIn = require('./_getAllKeysIn');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_FLAT_FLAG = 2,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * The opposite of `_.pick`; this method creates an object composed of the\n * own and inherited enumerable property paths of `object` that are not omitted.\n *\n * **Note:** This method is considerably slower than `_.pick`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to omit.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omit(object, ['a', 'c']);\n * // => { 'b': '2' }\n */\nvar omit = flatRest(function(object, paths) {\n  var result = {};\n  if (object == null) {\n    return result;\n  }\n  var isDeep = false;\n  paths = arrayMap(paths, function(path) {\n    path = castPath(path, object);\n    isDeep || (isDeep = path.length > 1);\n    return path;\n  });\n  copyObject(object, getAllKeysIn(object), result);\n  if (isDeep) {\n    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);\n  }\n  var length = paths.length;\n  while (length--) {\n    baseUnset(result, paths[length]);\n  }\n  return result;\n});\n\nmodule.exports = omit;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,gBAAgB;AAWpB,aAAS,gBAAgB,OAAO;AAC9B,aAAO,cAAc,KAAK,IAAI,SAAY;AAAA,IAC5C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,YAAY;AADhB,QAEI,YAAY;AAFhB,QAGI,WAAW;AAHf,QAII,aAAa;AAJjB,QAKI,kBAAkB;AALtB,QAMI,WAAW;AANf,QAOI,eAAe;AAGnB,QAAI,kBAAkB;AAAtB,QACI,kBAAkB;AADtB,QAEI,qBAAqB;AAsBzB,QAAI,OAAO,SAAS,SAAS,QAAQ,OAAO;AAC1C,UAAI,SAAS,CAAC;AACd,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,UAAI,SAAS;AACb,cAAQ,SAAS,OAAO,SAAS,MAAM;AACrC,eAAO,SAAS,MAAM,MAAM;AAC5B,mBAAW,SAAS,KAAK,SAAS;AAClC,eAAO;AAAA,MACT,CAAC;AACD,iBAAW,QAAQ,aAAa,MAAM,GAAG,MAAM;AAC/C,UAAI,QAAQ;AACV,iBAAS,UAAU,QAAQ,kBAAkB,kBAAkB,oBAAoB,eAAe;AAAA,MACpG;AACA,UAAI,SAAS,MAAM;AACnB,aAAO,UAAU;AACf,kBAAU,QAAQ,MAAM,MAAM,CAAC;AAAA,MACjC;AACA,aAAO;AAAA,IACT,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}