{"version": 3, "sources": ["../../lodash/isEmpty.js"], "sourcesContent": ["var baseKeys = require('./_baseKeys'),\n    getTag = require('./_getTag'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray'),\n    isArrayLike = require('./isArrayLike'),\n    isBuffer = require('./isBuffer'),\n    isPrototype = require('./_isPrototype'),\n    isTypedArray = require('./isTypedArray');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if `value` is an empty object, collection, map, or set.\n *\n * Objects are considered empty if they have no own enumerable string keyed\n * properties.\n *\n * Array-like values such as `arguments` objects, arrays, buffers, strings, or\n * jQuery-like collections are considered empty if they have a `length` of `0`.\n * Similarly, maps and sets are considered empty if they have a `size` of `0`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is empty, else `false`.\n * @example\n *\n * _.isEmpty(null);\n * // => true\n *\n * _.isEmpty(true);\n * // => true\n *\n * _.isEmpty(1);\n * // => true\n *\n * _.isEmpty([1, 2, 3]);\n * // => false\n *\n * _.isEmpty({ 'a': 1 });\n * // => false\n */\nfunction isEmpty(value) {\n  if (value == null) {\n    return true;\n  }\n  if (isArrayLike(value) &&\n      (isArray(value) || typeof value == 'string' || typeof value.splice == 'function' ||\n        isBuffer(value) || isTypedArray(value) || isArguments(value))) {\n    return !value.length;\n  }\n  var tag = getTag(value);\n  if (tag == mapTag || tag == setTag) {\n    return !value.size;\n  }\n  if (isPrototype(value)) {\n    return !baseKeys(value).length;\n  }\n  for (var key in value) {\n    if (hasOwnProperty.call(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nmodule.exports = isEmpty;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,SAAS;AADb,QAEI,cAAc;AAFlB,QAGI,UAAU;AAHd,QAII,cAAc;AAJlB,QAKI,WAAW;AALf,QAMI,cAAc;AANlB,QAOI,eAAe;AAGnB,QAAI,SAAS;AAAb,QACI,SAAS;AAGb,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAmCjC,aAAS,QAAQ,OAAO;AACtB,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAK,MAChB,QAAQ,KAAK,KAAK,OAAO,SAAS,YAAY,OAAO,MAAM,UAAU,cACpE,SAAS,KAAK,KAAK,aAAa,KAAK,KAAK,YAAY,KAAK,IAAI;AACnE,eAAO,CAAC,MAAM;AAAA,MAChB;AACA,UAAI,MAAM,OAAO,KAAK;AACtB,UAAI,OAAO,UAAU,OAAO,QAAQ;AAClC,eAAO,CAAC,MAAM;AAAA,MAChB;AACA,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,CAAC,SAAS,KAAK,EAAE;AAAA,MAC1B;AACA,eAAS,OAAO,OAAO;AACrB,YAAI,eAAe,KAAK,OAAO,GAAG,GAAG;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}