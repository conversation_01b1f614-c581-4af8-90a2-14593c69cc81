export function sanitizeImageUrl(url: string) {
  const defaultUrl = '';
  try {
    const urlObj = new URL(url);

    // Ensure protocol is http or https only
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return defaultUrl;
    }

    // Remove any dangerous protocols or javascript:
    if (
      url.toLowerCase().includes('javascript:') || // NOSONAR
      url.toLowerCase().includes('data:') ||
      url.toLowerCase().includes('vbscript:')
    ) {
      return defaultUrl;
    }

    return urlObj.toString(); // Returns sanitized URL
  } catch {
    return defaultUrl; // Invalid URL
  }
}
