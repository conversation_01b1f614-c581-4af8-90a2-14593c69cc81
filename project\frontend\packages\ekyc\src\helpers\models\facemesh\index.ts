import {
  FaceLandmarker,
  type FaceLandmarkerOptions,
  FilesetResolver,
} from '@mediapipe/tasks-vision';

import { getResizedResolution, resizeCanvas } from '@helpers/helpers/image-utils';

import { getFrameCanvas } from '@ekyc/helpers/image-utils';

const runningMode: FaceLandmarkerOptions['runningMode'] = 'VIDEO';
let lastVideoTime = -1;

export async function loadDetector(
  fileset: Awaited<ReturnType<typeof FilesetResolver.forVisionTasks>>,
  delegate: FaceLandmarkerOptions['baseOptions']['delegate'],
) {
  const detector = await FaceLandmarker.createFromOptions(fileset, {
    baseOptions: {
      modelAssetPath: `https://cdn.uppass.io/ekyc/models/mediapipe-models/face_landmarker.task`,
      delegate,
    },
    runningMode,
    numFaces: 2,
    outputFaceBlendshapes: true,
  });

  return {
    detector,
    runningMode,
  };
}

export async function loadDetectorTf(backendType: 'wasm' | 'cpu') {
  const log: Record<string, any> = {};

  const [mp, faceLandmarksDetection, tf] = await Promise.all([
    import('@mediapipe/face_mesh').then(module => module.default),
    import('@tensorflow-models/face-landmarks-detection'),
    import('@tensorflow/tfjs-core'),
  ]);

  if (backendType === 'wasm') {
    const tfjsWasm = await import('@tensorflow/tfjs-backend-wasm');
    const versionWasm = tfjsWasm.version_wasm;
    const wasmSrc = 'https://cdn.uppass.io/ekyc/models/@tensorflow/tfjs-backend-wasm';
    const wasmUrl = `${wasmSrc}/${versionWasm}/`;
    tfjsWasm.setWasmPaths(wasmUrl);

    log.version_wasm = versionWasm;
    log.mp_version = mp.VERSION;
    log.flags = tf.ENV.getFlags();
  } else {
    const tfjsCpu = await import('@tensorflow/tfjs-backend-cpu');
    log.version_cpu = tfjsCpu.version_cpu;
    log.mp_version = mp.VERSION;
    log.flags = tf.ENV.getFlags();
  }

  await tf.setBackend(backendType);
  await tf.ready();

  const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh;
  const detector = await faceLandmarksDetection.createDetector(model, {
    runtime: 'tfjs',
    refineLandmarks: true,
    maxFaces: 2,
    detectorModelUrl:
      // https://www.kaggle.com/models/mediapipe/face-detection/tfJs/short/1/model.json
      'https://cdn.uppass.io/ekyc/models/tfjs-model/face-detection-short/model.json',
    // https://www.kaggle.com/models/mediapipe/face-landmarks-detection/tfJs/attention-mesh/1/model.json
    landmarkModelUrl: 'https://cdn.uppass.io/ekyc/models/tfjs-model/attention-mesh/model.json',
  });

  return {
    detector,
    ...log,
  };
}

export function getDetectFunction(detector: Awaited<ReturnType<typeof loadDetector>>['detector']) {
  return (
    src: HTMLVideoElement | HTMLCanvasElement | ImageBitmap,
  ): Promise<Types.DetectFunctionResult[]> => {
    if (!src) {
      console.warn('Video not loaded');
      return Promise.resolve(null);
    }

    if (!detector) throw new Error('Detector not loaded');

    const startTimeMs = performance.now();

    let isUpdated = false;
    if ('currentTime' in src) {
      isUpdated = src.currentTime !== lastVideoTime;
      lastVideoTime = src.currentTime;
      src = getFrameCanvas(src);
    }

    if (!isUpdated) return null;

    const result = detector.detectForVideo(src, startTimeMs);

    const formatted = result.faceLandmarks.map((lm, i) => ({
      landmarks: null as any,
      mesh: lm,
      blendshape: Object.values(result.faceBlendshapes[i]?.categories ?? {}).reduce(
        (prev, cur) => ({
          ...prev,
          [cur.categoryName]: cur.score,
        }),
        {} as Record<Types.PossibleBlendshapeName, number>,
      ),
      matrix: result.facialTransformationMatrixes[i],
      runningMode,
      resized_width: 'width' in src ? src.width : null,
      resized_height: 'height' in src ? src.height : null,
    }));

    return Promise.resolve(formatted);
  };
}

export function getDetectFunctionTf(
  detector: Awaited<ReturnType<typeof loadDetectorTf>>['detector'],
) {
  return async (video: HTMLVideoElement): Promise<Types.DetectFunctionResult[]> => {
    if (!video) {
      console.warn('Video not loaded');
      return Promise.resolve(null);
    }

    if (!detector) throw new Error('Detector not loaded');
    let canvas: HTMLCanvasElement = null;

    if (video instanceof HTMLVideoElement) {
      canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d', { alpha: false });
      ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
    } else {
      canvas = video;
    }

    const { resizedWidth, resizedHeight } = getResizedResolution(
      { inputHeight: canvas.height, inputWidth: canvas.width },
      { maxHeight: 320 },
    );
    const resized = resizeCanvas(canvas, { width: resizedWidth, height: resizedHeight });

    const faces = await detector.estimateFaces(resized);

    const formatted = faces.map((lm, i) => ({
      landmarks: null as any,
      mesh: lm.keypoints.map(p => ({
        x: p.x,
        y: p.y,
        z: p.z,
        visibility: 1,
      })),
      runningMode,
    }));

    return formatted;
  };
}

export function getTestDetectFunction(
  detector: Awaited<ReturnType<typeof loadDetector>>['detector'],
) {
  return async (input: ImageBitmap) => {
    const result = detector.detectForVideo(input, 0);
    const points = result.faceLandmarks[0]?.map(p => [p.x, p.y, p.z]);

    return points;
  };
}

export function getTestDetectFunctionTf(
  detector: Awaited<ReturnType<typeof loadDetectorTf>>['detector'],
) {
  return async (input: ImageBitmap) => {
    const results = await detector.estimateFaces(input);
    const points = results[0]?.keypoints?.map(p => [p.x, p.y, p.z]);
    return Promise.resolve(points);
  };
}
