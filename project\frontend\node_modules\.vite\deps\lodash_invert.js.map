{"version": 3, "sources": ["../../lodash/_baseInverter.js", "../../lodash/_createInverter.js", "../../lodash/invert.js"], "sourcesContent": ["var baseForOwn = require('./_baseForOwn');\n\n/**\n * The base implementation of `_.invert` and `_.invertBy` which inverts\n * `object` with values transformed by `iteratee` and set by `setter`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform values.\n * @param {Object} accumulator The initial inverted object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseInverter(object, setter, iteratee, accumulator) {\n  baseForOwn(object, function(value, key, object) {\n    setter(accumulator, iteratee(value), key, object);\n  });\n  return accumulator;\n}\n\nmodule.exports = baseInverter;\n", "var baseInverter = require('./_baseInverter');\n\n/**\n * Creates a function like `_.invertBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} toIteratee The function to resolve iteratees.\n * @returns {Function} Returns the new inverter function.\n */\nfunction createInverter(setter, toIteratee) {\n  return function(object, iteratee) {\n    return baseInverter(object, setter, toIteratee(iteratee), {});\n  };\n}\n\nmodule.exports = createInverter;\n", "var constant = require('./constant'),\n    createInverter = require('./_createInverter'),\n    identity = require('./identity');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Creates an object composed of the inverted keys and values of `object`.\n * If `object` contains duplicate values, subsequent values overwrite\n * property assignments of previous values.\n *\n * @static\n * @memberOf _\n * @since 0.7.0\n * @category Object\n * @param {Object} object The object to invert.\n * @returns {Object} Returns the new inverted object.\n * @example\n *\n * var object = { 'a': 1, 'b': 2, 'c': 1 };\n *\n * _.invert(object);\n * // => { '1': 'c', '2': 'b' }\n */\nvar invert = createInverter(function(result, value, key) {\n  if (value != null &&\n      typeof value.toString != 'function') {\n    value = nativeObjectToString.call(value);\n  }\n\n  result[value] = key;\n}, constant(identity));\n\nmodule.exports = invert;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,aAAa;AAajB,aAAS,aAAa,QAAQ,QAAQ,UAAU,aAAa;AAC3D,iBAAW,QAAQ,SAAS,OAAO,KAAKA,SAAQ;AAC9C,eAAO,aAAa,SAAS,KAAK,GAAG,KAAKA,OAAM;AAAA,MAClD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,eAAe;AAUnB,aAAS,eAAe,QAAQ,YAAY;AAC1C,aAAO,SAAS,QAAQ,UAAU;AAChC,eAAO,aAAa,QAAQ,QAAQ,WAAW,QAAQ,GAAG,CAAC,CAAC;AAAA,MAC9D;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,iBAAiB;AADrB,QAEI,WAAW;AAGf,QAAI,cAAc,OAAO;AAOzB,QAAI,uBAAuB,YAAY;AAoBvC,QAAI,SAAS,eAAe,SAAS,QAAQ,OAAO,KAAK;AACvD,UAAI,SAAS,QACT,OAAO,MAAM,YAAY,YAAY;AACvC,gBAAQ,qBAAqB,KAAK,KAAK;AAAA,MACzC;AAEA,aAAO,KAAK,IAAI;AAAA,IAClB,GAAG,SAAS,QAAQ,CAAC;AAErB,WAAO,UAAU;AAAA;AAAA;", "names": ["object"]}