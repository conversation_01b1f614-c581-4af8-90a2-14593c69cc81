{"version": 3, "sources": ["../../lodash/_basePickBy.js"], "sourcesContent": ["var baseGet = require('./_baseGet'),\n    baseSet = require('./_baseSet'),\n    castPath = require('./_castPath');\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = basePickBy;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,UAAU;AADd,QAEI,WAAW;AAWf,aAAS,WAAW,QAAQ,OAAO,WAAW;AAC5C,UAAI,QAAQ,IACR,SAAS,MAAM,QACf,SAAS,CAAC;AAEd,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,OAAO,MAAM,KAAK,GAClB,QAAQ,QAAQ,QAAQ,IAAI;AAEhC,YAAI,UAAU,OAAO,IAAI,GAAG;AAC1B,kBAAQ,QAAQ,SAAS,MAAM,MAAM,GAAG,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}