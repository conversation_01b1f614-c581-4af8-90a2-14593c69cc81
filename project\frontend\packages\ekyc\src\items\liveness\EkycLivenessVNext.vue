<script setup lang="ts">
import { promiseTimeout, templateRef, until, useSessionStorage } from '@vueuse/core';
import DOMPurify from 'dompurify';
import cloneDeep from 'lodash/cloneDeep';
import get from 'lodash/get';
import throttle from 'lodash/throttle';
import { animate } from 'motion';

import ImageSelector from '@helpers/components/ImageSelector.vue';
import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';
import { getUserAgent } from '@helpers/helpers/user-agent';
import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import DebugLivenessOverlay from '@ekyc/components/DebugLivenessOverlay.vue';
import EkycLoadingOverlay from '@ekyc/components/EkycLoadingOverlay/EkycLoadingOverlay.vue';
import MovingPixel from '@ekyc/components/MovingPixel.vue';
import FaceMaskSvgNext from '@ekyc/components/liveness-modal/FaceMaskSvgNext.vue';
import LivenessModalActionProgressionNext from '@ekyc/components/liveness-modal/LivenessModalActionProgressionNext.vue';
import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useFaceAction } from '@ekyc/composables/liveness/use-face-action';
import { useFramesSequences } from '@ekyc/composables/liveness/use-frames-sequences';
import { useHistory } from '@ekyc/composables/liveness/use-history';
import { type LivenessLog, useLivenessLog } from '@ekyc/composables/liveness/use-liveness-log';
import { useLivenessState } from '@ekyc/composables/liveness/use-liveness-state';
import { useRecordedData } from '@ekyc/composables/liveness/use-recorded-data';
import { useCamera } from '@ekyc/composables/use-camera';
import { LivenessProps, useCommonEkycItem } from '@ekyc/composables/use-common-ekyc-item';
import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { useSwitchAppWatcher } from '@ekyc/composables/use-switch-app-watcher';
import {
  forcedShowTarget,
  playAnimation,
  playProgressLivenessDonutReverse,
  showIndicator,
  skipStartInstruction,
} from '@ekyc/helpers/animations';
import { calculateFaceMaskRect } from '@ekyc/helpers/face-mask-rect';
import { checkFaceSizeFromLandmark, checkFaceSizeFromMesh } from '@ekyc/helpers/face-size-checker';
import { getFrameBlob } from '@ekyc/helpers/image-utils';
import { detect, loadModel } from '@ekyc/helpers/models';
import EkycBase from '@ekyc/items/base/EkycBase.vue';
import { sendLivenessStart } from '@ekyc/services/ekyc';
import { useFaceApiStore } from '@ekyc/store/modules/faceAPI';

const props = defineProps({
  ...LivenessProps,
  isFailedState: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const emit = defineEmits<{
  (e: 'ready', value?: any): void;
  (e: 'complete', value: Types.EkycRecordCompletePayload): void;
  (e: 'error', value: Types.EkycRecordErrorPayload): void;
  (e: 'fail', value: Types.EkycRecordFailPayload): void;
}>();

// Override default settings first
const faceApiStore = useFaceApiStore();

const ekyc = useCommonEkycItem({
  media: 'liveness',
  itemType: 'liveness',
});

const { mediaData, onRecordFinished } = ekyc;

const workingConstraints = useSessionStorage('uppass-media-liveness-constraints', {});

const { allSettings } = useEkycSettings();

const { initSwitchAppWatcher, deinitSwitchAppWatcher } = useSwitchAppWatcher();

const videoRef = templateRef('videoRef');
const actionProgressionRef = templateRef('actionProgressionRef');
const faceMaskRef = templateRef('faceMaskRef');
const uploadingOverlayRef = templateRef('uploadingOverlayRef');

const sanitizedPrepareTopContent = computed(() => {
  const raw = allSettings.liveness.prepare_top_content;
  if (!raw) return '';
  return DOMPurify.sanitize(raw);
});

const {
  facingMode,
  isFlipped,
  initOnResizeWatch,
  loadCamera,
  stopPreview,
  rotateCamera,
  zoomCamera,
  flipCamera,
} = useCamera({
  videoRef,
  highQuality: false,
  startFacingMode: 'user',
  overrideConstraints: computed(() => allSettings.liveness.additional_constraints),
  workingConstraints,
});

function exitPreview() {
  stopPreview({ tryRequestAndStopExternal: false });
}

const { t } = useI18n();
const { snapFrameNumber, livenessTimeStart, recordingState, isRecording, resetLivenessState } =
  useLivenessState();
const {
  allLogs,
  addLog,
  createLivenessLogWithCurrentState,
  resetAllLogs,
  updateLatestLog,
  updateLogWithFaceDetection,
  updateLogWithCameraSample,
  updateLogWithRecordedData,
} = useLivenessLog();
const { recordedDataList, newRecordedData, addRecordedData, resetRecordedData } = useRecordedData();
const { detectionHistory, currentHistory, addDetectionHistory, clearDetectionHistories } =
  useHistory();
const {
  currentAction,
  actionListIndex,
  sequenceListIndex,
  shouldSaveSnapshot,
  state,
  logs,
  actionProgress,
  sequenceList,
  setToNotPassState,
  checkAction,
  getNextAction,
  restartCurrentProgress,
  restartFaceAction,
  resetFaceAction,
  loadActionList,
  ...faceAction
} = useFaceAction();
const {
  actionFramesSequences,
  missingFramesSequences,
  processActionFrame,
  processMissingFrameByFaceResults,
} = useFramesSequences();
const { debugMode, debugOverlay, dontPass, forcePass, freezeTime, setDebugInfo, setCanvasSize } =
  useDebug();

const recordTimeLeft = ref(0);
const errorMessage = ref('');
const canClickSkipInstruction = ref(false);
const shouldShowLivenessModal = ref(false);
const isLivenessSessionStarted = ref(false);
const shouldShowUploadProgress = ref(false);
const reachingMaxAttempt = ref(false);
const isFullFrame = ref(false);

// For manual snap logic
const CAN_MANUAL_SNAP_AFTER_MS = 1000 * 3; // 8 sec
const canManualSnap = ref(false);

const showSelfTimer = ref<boolean>(false);
const selfTimerCount = ref(0);
const selfTimer = computed(() => props.setting?.self_timer);

function doEnableManualSnap() {
  if (canManualSnap.value) return;
  canManualSnap.value = true;
  playAnimation('manualSnap');
  updateLatestLog({ can_manual_snap: true });
  startChecking();
}

function doEmit(payload: Types.EkycRecordFinishedPayload) {
  updateLatestLog({
    action_sequences: actionFramesSequences.value,
    missing_frames_sequences: missingFramesSequences.value,
    time_left: recordTimeLeft.value,
  });
  return onRecordFinished({
    ...payload,
    logs: allLogs.value,
    isRecording: isRecording.value,
    data: recordedDataList.value,
    facingMode: facingMode.value,
    flipped: isFlipped.value,
  });
}

function setErrorMessage(errMsg: string) {
  if (recordingState.value === 'recording') {
    errorMessage.value = errMsg;
  }
}

async function saveLatestImage(success: boolean) {
  newRecordedData.value = await getFrameBlob(videoRef.value, { maxHeight: 1080 });

  const latestToSave = cloneDeep(newRecordedData.value);
  latestToSave.filename = `${currentAction.value}${success ? '' : '_fail'}_0.jpg`;
  addRecordedData(latestToSave);
}

function resetTimer() {
  recordTimeLeft.value = allSettings.liveness.liveness.recordTimeMax[currentAction.value];
}

function doCheckAction() {
  if (forcePass.value) {
    forcePass.value = false;
    setDebugInfo('status', 'Force pass...');
    return true;
  }

  const { passed, state, logs: checkActionLogs } = checkAction();

  setDebugInfo('actionLogs', checkActionLogs);
  setDebugInfo('status', state);

  return passed;
}

function checkFaceDetection(
  faceResults: Types.DetectFunctionResult[],
  rects: Types.FaceMaskRectInfo,
) {
  let faceSizeRecord: Types.FaceSizeRecord = null;
  /* **** Detection Check **** */

  /** Check if Not started yet (detect for model warmup) */
  const checkIsNotStarted = () => recordingState.value === 'loading';

  const checkIsNoFace = () => {
    if (faceResults.length === 0) {
      setErrorMessage(t('ekyc.recorder.error_no_face').toString());
      return true;
    }
    return false;
  };

  const checkIsMultipleFace = () => {
    if (faceResults.length > 1) {
      setErrorMessage(t('ekyc.recorder.error_multiple_faces').toString());
      return true;
    }
    return false;
  };

  if (checkIsNotStarted() || checkIsNoFace() || checkIsMultipleFace()) {
    restartFaceAction();
    resetRecordedData();
    return { passed: false, faceSizeRecord: null };
  }

  /* **** Single Face Check **** */
  const { isFaceTooBig, isFaceTooSmall, record } = faceResults[0].landmarks
    ? checkFaceSizeFromLandmark(faceResults[0].landmarks, rects)
    : checkFaceSizeFromMesh(
        faceResults[0].mesh,
        faceResults[0].resized_width,
        faceResults[0].resized_height,
        rects,
      );
  faceSizeRecord = record;

  if (
    allSettings.liveness.liveness.enableFaceSize &&
    currentAction.value === 'idle' &&
    (isFaceTooSmall || isFaceTooBig)
  ) {
    if (isFaceTooSmall) {
      setErrorMessage(t('ekyc.recorder.error_small_face').toString());
    }
    if (isFaceTooBig) {
      setErrorMessage(t('ekyc.recorder.error_big_face').toString());
    }
    return { passed: false, faceSizeRecord };
  }

  return { passed: true, faceSizeRecord };
}

function detectAndCheckAction(faceResults: Types.DetectFunctionResult[]) {
  /* **** Face Recognition **** */
  setDebugInfo('status', 'Detecting...');

  const currentDetectStartedAt = Date.now();
  const lastDetectedAt = currentHistory.value?.detectStartedAt;
  const frameDeltaMs = lastDetectedAt ? currentDetectStartedAt - lastDetectedAt : 0;

  if (freezeTime.value !== true) {
    recordTimeLeft.value -= frameDeltaMs / 1000;
  }

  const faceResult = faceResults[0];
  const newHistory: Types.FaceDetectionResult = {
    ...faceResult,
    detectionPassed: false,
    detectStartedAt: currentDetectStartedAt,
    deltaMs: frameDeltaMs,
  };
  addDetectionHistory(newHistory);

  logs.value = [];

  const rects = calculateFaceMaskRect(videoRef.value, faceMaskRef.value);

  /* **** Face Detection Check (Count, Size) **** */
  const { passed: faceDetectionPassed, faceSizeRecord } = checkFaceDetection(faceResults, rects);

  setDebugInfo('faceMaskRect', rects);
  setDebugInfo('faceResults', faceResults);
  setDebugInfo('faceSizeRecord', faceSizeRecord);

  if (faceDetectionPassed && recordingState.value !== 'recording') {
    startChecking();
  }

  if (!faceDetectionPassed) {
    restartCurrentProgress();

    return {
      detections: [...detectionHistory.value] as Types.FaceDetectionResult[],
      action: currentAction.value,
      passed: false,
      frame_delta_ms: frameDeltaMs,
      state: state.value,
      checker_logs: logs.value,
      face_mask_rect: rects,
      face_size_record: faceSizeRecord,
    };
  }

  currentHistory.value.detectionPassed = true;

  /* **** Liveness Check **** */
  const passed = doCheckAction();

  if (!passed && actionProgress.value <= 0) {
    // Not idle error message
    if (shouldSaveSnapshot.value) {
      setErrorMessage(t(`ekyc.recorder.error_not_idle`).toString());
    }
    // Show action indicator
    else {
      playAnimation('actionIndicator');
    }
  }

  const record: Types.FaceDetectionRecord = {
    detections: [...detectionHistory.value] as Types.FaceDetectionResult[],
    action: currentAction.value,
    passed,
    frame_delta_ms: frameDeltaMs,
    state: state.value,
    checker_logs: logs.value,
    face_mask_rect: rects,
    face_size_record: faceSizeRecord,
    action_progress: actionProgress.value,
  };

  return record;
}

function doVibrate(pattern: number | number[] = 200) {
  window.navigator.vibrate?.(pattern);
}

const logSnap = throttle((livenessLog: LivenessLog) => {
  console.group(
    `[Liveness] %cSnap ${snapFrameNumber.value} (${currentAction.value})`,
    'font-size: 20px;',
  );
  console.log(
    `[Liveness] Passed: %c${livenessLog.result.passed} ${livenessLog.result.action_progress}`,
    `background-color: ${livenessLog.result.passed ? 'green' : 'red'};`,
  );
  console.log(`Delta: ${currentHistory.value?.deltaMs}`, `LivenessLog:`, livenessLog);
  console.log(`CheckerLogs:`, livenessLog.result.checker_logs);
  console.groupEnd();
}, 1000);

function createLivenessLogOnInitSnap() {
  const livenessLog = createLivenessLogWithCurrentState();
  addLog(livenessLog);
  return livenessLog;
}

function hideManualSnapButton() {
  const el = document.querySelector('#manual-take-btn');
  return animate(el, { opacity: 0 }, { duration: 0 }).then(() => {});
}

const delay = (ms: number) =>
  new Promise(resolve => {
    setTimeout(resolve, ms);
  });

async function doSelfTimer() {
  forcedShowTarget.value.push('#action-progression');
  showSelfTimer.value = true;
  selfTimerCount.value = selfTimer.value;
  await hideManualSnapButton();
  await showIndicator('action-progression');

  playProgressLivenessDonutReverse(selfTimer.value);
  await new Promise<void>(async resolve => {
    console.log(`Starting countdown: ${selfTimerCount.value} seconds`);

    const timerInterval = setInterval(async () => {
      selfTimerCount.value--;
      if (selfTimerCount.value > 0) {
        console.log(`Time left: ${selfTimerCount.value} seconds`);
      } else {
        clearInterval(timerInterval);
        console.log('Timer finished!');
        showSelfTimer.value = false;

        const targetIndex = forcedShowTarget.value.indexOf('#action-progression');
        forcedShowTarget.value.splice(targetIndex, 1);
        await delay(100);
        resolve();
      }
    }, 1_000);
  });
}

async function manualSnap() {
  try {
    if (selfTimer.value > 0) {
      // If already counting down, skip this one
      if (showSelfTimer.value) {
        return;
      }
      // Start self timer
      await doSelfTimer();
    }

    shouldShowUploadProgress.value = true;

    recordingState.value = 'uploading';

    doVibrate();

    if (!allSettings.liveness.start_url) {
      await loadActionList({ log: {} });
    }

    getNextAction();

    const livenessLog = createLivenessLogOnInitSnap();

    const detectionRecord = detectAndCheckAction([]);
    updateLogWithFaceDetection(livenessLog, detectionRecord);

    await saveLatestImage(true);

    await until(isLivenessSessionStarted).toBe(true);

    doEmit({
      action: 'complete',
      submit_log: { is_auto_snap: false },
    }).then(async () => {
      await uploadingOverlayRef.value?.complete?.(); // wait for progress to fill
    });
  } catch {
    doEmit({
      action: 'error',
      code: 'error_not_supported',
      error: 'ekyc.recorder.error_not_supported',
    });
  }
}

/* Liveness snap loop */
async function snapFunction(
  results: Types.DetectFunctionResult[],
): Promise<'complete' | 'continue' | 'timeout' | 'error'> {
  try {
    // CHECK timeout
    if (recordTimeLeft.value <= 0) {
      console.log('[Liveness] Timeout...');
      playAnimation('failIndicator');
      canManualSnap.value = false;
      return 'timeout';
    }

    // INIT loop info
    const hasOneAction = sequenceList.value.length <= 0;
    snapFrameNumber.value += 1;
    const livenessLog = createLivenessLogOnInitSnap();
    processActionFrame(currentAction.value);

    // DETECT
    const detectionRecord = detectAndCheckAction(results);
    updateLogWithFaceDetection(livenessLog, detectionRecord);
    processMissingFrameByFaceResults(results);

    // DEBUG
    logSnap(livenessLog);
    setDebugInfo('state', detectionRecord.state);

    // Sample
    if (snapFrameNumber.value % 50 === 0) {
      const frameBlob = await getFrameBlob(videoRef.value, { maxHeight: 1080 });
      updateLogWithCameraSample(livenessLog, frameBlob.canvas);
    }

    /** ======================= STEP 1: CHECKING STATE ======================= */
    // DEBUG: Don't pass
    if (dontPass.value) {
      detectionRecord.passed = false;
      detectionRecord.state = 'checking';
      actionProgress.value = Math.min(99, actionProgress.value);
      setToNotPassState();
    }
    // Enable Manual Snap
    const livenessTimeSpent = livenessTimeStart.value
      ? new Date().valueOf() - livenessTimeStart.value.valueOf()
      : 0;
    if (
      hasOneAction &&
      actionProgress.value <= 0 &&
      livenessTimeSpent >= CAN_MANUAL_SNAP_AFTER_MS
    ) {
      doEnableManualSnap();
    } else {
      canManualSnap.value = false;
    }

    /** ======================= STEP 2: NOT FINISHED ======================= */
    if (hasOneAction) {
      if (actionProgress.value > 0) {
        playAnimation('progressLiveness');
        errorMessage.value = '';
      } else {
        playAnimation('stopProgressLiveness');
      }
    }

    if (actionProgress.value < 100) {
      return 'continue';
    }

    /** ======================= STEP 3: FINISHED ======================= */
    console.log('[Liveness] PASSED:', currentAction.value);

    // SNAP
    doVibrate();
    if (shouldSaveSnapshot.value) {
      await saveLatestImage(true);
    }
    updateLogWithRecordedData(livenessLog, newRecordedData.value);

    /** ======================= STEP 4: NEXT ACTION ======================= */
    getNextAction();
    if (currentAction.value) {
      clearDetectionHistories();
      resetTimer();
      return 'continue';
    }

    /** ======================= STEP 5: COMPLETE ======================= */
    return 'complete';
  } catch (err) {
    console.error('snapFunction error:', err);
    updateLatestLog({
      error_message: typeof err === 'object' && err.stack ? err.stack : err,
    });
    return 'error';
  }
}

async function onDetected(results: Types.DetectFunctionResult[]): Promise<boolean> {
  const snapOp = await snapFunction(results);

  // Snap finish Log
  const livenessTimeSpent = livenessTimeStart.value
    ? new Date().valueOf() - livenessTimeStart.value.valueOf()
    : 0;
  updateLatestLog({
    liveness_time_spent: livenessTimeSpent,
    time_left: recordTimeLeft.value,
  });

  if (props.isFailedState) {
    recordingState.value = 'uploading';
    return false;
  }

  // Snap next operation
  switch (snapOp) {
    case 'complete':
      console.log(
        `[Liveness] %cLiveness Time spent ${livenessTimeSpent}/${recordTimeLeft.value}`,
        'font-size: 20px;',
      );
      recordingState.value = 'uploading';

      // Play success first
      playAnimation('successIndicator');

      // Play upload later
      promiseTimeout(2_000).then(() => {
        shouldShowUploadProgress.value = true;
      });

      // Wait for session to start
      await until(isLivenessSessionStarted).toBe(true);

      // Uploading
      doEmit({
        action: 'complete',
        submit_log: { is_auto_snap: true },
      }).then(async () => {
        shouldShowUploadProgress.value = true;
        await uploadingOverlayRef.value?.complete?.(); // wait for progress to fill
      });
      return true;
    case 'timeout':
      updateLatestLog({
        passed: false,
        error_message: t(`ekyc.recorder.error_action_time_out`) as string,
      });

      await saveLatestImage(false);
      doEmit({
        action: 'fail',
        code: 'error_action_time_out',
        error: `ekyc.recorder.error_action_time_out`,
        reaching_max_attempt: reachingMaxAttempt.value,
      });
      recordingState.value = 'uploading';
      return false;
    case 'error':
      doEmit({
        action: 'error',
        code: 'error_not_supported',
        error: 'ekyc.recorder.error_not_supported',
        reaching_max_attempt: reachingMaxAttempt.value,
      });
      return false;
    default:
      requestAnimationFrame(nextSnap);
      return false;
  }
}

async function nextSnap() {
  if (showSelfTimer.value) {
    return;
  }

  if (recordingState.value !== 'early-recording' && recordingState.value !== 'recording') {
    return;
  }

  const results = await detect(videoRef.value);
  if (results) {
    onDetected(results);
  } else {
    requestAnimationFrame(nextSnap);
  }
}

const finishedInit = ref(false);
const finishedInstruction = ref(false);

const doLoadModel = async () => {
  try {
    if (allSettings.liveness.dummy) {
      return Promise.resolve({}) as ReturnType<typeof loadModel>;
    }
    const lm = await loadModel();
    return lm;
  } catch (err) {
    faceApiStore.setCanLoadModel(false);
    throw new Error('error_liveness_load_models', { cause: err });
  }
};

const doLoadCameraAndWaitUntilReady = async () => {
  if (allSettings.liveness.dummy) {
    startInstruction();
    return Promise.resolve({ success: true }) as ReturnType<typeof loadCamera>;
  }

  startInstruction();
  const lc = await loadCamera();
  canClickSkipInstruction.value = true;
  // Minimum 3 sec. after instruction
  await promiseTimeout(1_000);
  return lc;
};

const doLoadActionList = async (initLog: any) => {
  const LOG_PREFIX = [
    '%c[LIVENESS doLoadActionList]',
    'background: #000; color: #ff0; border: 1px solid #ff0;',
  ];

  if (allSettings.liveness.dummy) {
    return Promise.resolve({}) as unknown as ReturnType<typeof loadActionList>;
  }
  return loadActionList({ log: initLog }).catch(err => {
    console.error(...LOG_PREFIX, 'loadActionList error:', err);
    if (get(err, 'response.data.error_type') === 'max_attempt') {
      throw new Error('error_liveness_max_attempt');
    } else {
      throw new Error('error_liveness_get_action');
    }
  });
};

const handleFirstAttempt = async (initLog: any) => {
  const LOG_PREFIX = [
    '%c[LIVENESS handleFirstAttempt]',
    'background: #000; color: #ff0; border: 1px solid #ff0;',
  ];

  setDebugInfo('status', 'Initializing...');

  const [lc, lm] = await Promise.all([doLoadCameraAndWaitUntilReady(), doLoadModel()]);

  setDebugInfo('video', videoRef.value);

  initLog.load_model = lm;
  initLog.load_camera = lc;

  setDebugInfo('initLog', initLog);

  try {
    initLog.performance = { ...JSON.parse(JSON.stringify(window.performance)) };
  } catch (error) {
    console.warn(...LOG_PREFIX, 'Can not get performance log.', error);
  }

  if (!lc.success) {
    if (lc.logs.error === 'error_open_camera_timeout') {
      throw new Error('error_open_camera_timeout');
    }
    throw new Error('error_open_camera');
  }

  try {
    initLog.performance = { ...JSON.parse(JSON.stringify(window.performance)) };
  } catch (error) {
    console.warn(...LOG_PREFIX, 'Can not get performance log.', error);
  }
};

async function startInit({ isFirstAttempt = true } = {}) {
  const LOG_PREFIX = [
    '%c[LIVENESS startInit]',
    'background: #000; color: #ff0; border: 1px solid #ff0;',
  ];

  const initLog = {
    version: '2025-06-17-next',
    init_time_spent: -1,
    load_model: {},
    load_camera: {},
    ua: getUserAgent(),
    performance: {},
  };

  const initStartTime = new Date();
  const logStopTime = () => {
    const initStopTime = new Date();
    const initTimeSpent = initStopTime.valueOf() - initStartTime.valueOf();
    initLog.init_time_spent = initTimeSpent;
  };

  try {
    console.log(...LOG_PREFIX, 'Initializing', allSettings.liveness.dummy ? '(dummy)' : '');

    shouldShowLivenessModal.value = true;
    shouldShowUploadProgress.value = false;
    isLivenessSessionStarted.value = false;
    canManualSnap.value = false;
    errorMessage.value = '';

    initOnResizeWatch();
    resetRecordedData();
    resetLivenessState();
    resetFaceAction();
    clearDetectionHistories();
    resetAllLogs();

    faceMaskRef.value.draw();

    if (isFirstAttempt) {
      await handleFirstAttempt(initLog);
    }

    setDebugInfo('initLog', initLog);
    setDebugInfo('status', 'Getting action list...');
    setCanvasSize(videoRef.value.videoWidth, videoRef.value.videoHeight);

    logStopTime();

    await doLoadActionList(initLog);
    setDebugInfo('status', 'Ready to start!');

    finishedInit.value = true;
    startEarlyRecording();

    console.log(...LOG_PREFIX, 'Initializing done!');
  } catch (err) {
    console.error(...LOG_PREFIX, 'Initializing error:', err);
    logStopTime();

    const VALID_ERR = [
      'error_open_camera',
      'error_open_camera_timeout',
      'error_liveness_max_attempt',
      'error_liveness_get_action',
      'error_liveness_load_models',
    ] as const;
    const errType: (typeof VALID_ERR)[number] = VALID_ERR.includes(err.message)
      ? err.message
      : 'error_liveness_init_function';
    const errText = `ekyc.recorder.${errType}`;

    if (
      ['error_open_camera', 'error_open_camera_timeout', 'error_liveness_max_attempt'].includes(
        errType,
      )
    ) {
      // Delay modal close
      setTimeout(() => {
        doEmit({
          action: 'error',
          code: errType,
          status: errType,
          error: errText,
          error_type: errType,
          message: err.message,
          detail: err?.toString(),
          init_log: initLog,
          from: 'init',
        });
        exitPreview();
      }, 1000);
    } else {
      // Start & Allow manual snap if camera is on
      finishedInit.value = true;
      startEarlyRecording();
    }
  }
}

async function startInstruction() {
  const LOG_PREFIX = [
    '%c[LIVENESS startInstruction]',
    'background: #000; color: #0ff; border: 1px solid #0ff;',
  ];

  console.log(...LOG_PREFIX, 'Instructing...');

  await playAnimation('startInstruction');

  finishedInstruction.value = true;
  startEarlyRecording();

  console.log(...LOG_PREFIX, 'Instructing done!');
}

function startEarlyRecording() {
  if (!finishedInit.value) {
    return;
  }

  if (!finishedInstruction.value) {
    return;
  }

  if (recordingState.value === 'early-recording') {
    return;
  }

  if (allSettings.liveness.dummy) {
    canClickSkipInstruction.value = true;
    return;
  }

  const LOG_PREFIX = [
    '%c[LIVENESS startEarlyRecording]',
    'background: #000; color: #0f0; border: 1px solid #0f0;',
  ];

  skipStartInstruction();
  recordingState.value = 'early-recording';
  livenessTimeStart.value = new Date();
  getNextAction();
  resetTimer();
  nextSnap();
  console.log(...LOG_PREFIX, 'Started early recording!');

  // Waiting for button to be clicked OR face found
  // If not, allow manual snap after 3 sec.
  setTimeout(() => {
    const hasOneAction = sequenceList.value.length <= 0;
    if (hasOneAction && actionProgress.value <= 0) {
      doEnableManualSnap();
    }
  }, CAN_MANUAL_SNAP_AFTER_MS);
}

async function startChecking() {
  const LOG_PREFIX = [
    '%c[LIVENESS startChecking]',
    'background: #000; color: #fa0; border: 1px solid #fa0;',
  ];

  if (recordingState.value === 'recording') {
    return;
  }

  recordingState.value = 'recording';

  playAnimation('startChecking');

  if (allSettings.liveness.dummy) {
    return;
  }

  if (!isLivenessSessionStarted.value) {
    const { data } = await sendLivenessStart({ logs: allLogs.value });
    if (data.max_attempt) {
      console.log(...LOG_PREFIX, 'Reaching max attempt!');
      reachingMaxAttempt.value = true;
    }
    isLivenessSessionStarted.value = true;
  }
}

async function retryLiveness() {
  finishedInstruction.value = true;
  playAnimation('retryLiveness');
  await startInit({ isFirstAttempt: false });
  await startChecking();
}

function doSkipInstruction() {
  finishedInstruction.value = true;
  canClickSkipInstruction.value = false;
  skipStartInstruction();
}

function openVideoCamera() {
  startInit();
}

watch(errorMessage, val => {
  if (val && !canManualSnap.value) {
    // If manual snap, dont display error
    playAnimation('warnIndicator');
  }
});

watch(shouldShowUploadProgress, val => {
  if (val) {
    uploadingOverlayRef.value?.start?.();
  }
});

onMounted(() => {
  emit('ready');

  isFullFrame.value =
    allSettings.liveness.liveness.face_size.mask_scale_x < 0 &&
    allSettings.liveness.liveness.face_size.mask_scale_y < 0;

  initSwitchAppWatcher(() => {
    doEmit({
      action: 'close',
      unfocus: true,
    });
    recordingState.value = 'uploading';
  });
});

onBeforeUnmount(() => {
  deinitSwitchAppWatcher();
});

defineExpose({
  ...ekyc,
  ...faceAction,
  exitPreview,
  startInit,
  openVideoCamera,
  retryLiveness,
  snapFunction,
  onDetected,
  rotateCamera,
  zoomCamera,
  flipCamera,
  videoRef,
  recordingState,
  actionProgress,
  errorMessage,
  shouldShowUploadProgress,
  shouldShowLivenessModal,
  uploadingOverlayRef,
  recordTimeLeft,
});
</script>

<template>
  <div>
    <EkycBase media="liveness">
      <template #modal>
        <div
          class="modal modal-full-screen mask-modal"
          :class="{ 'is-active': shouldShowLivenessModal }"
        >
          <div class="modal-background" />
          <div class="modal-content modal-card camera-view mode-liveness">
            <DebugLivenessOverlay v-if="debugMode" ref="debugOverlay" />

            <div class="modal-card-body" :class="{ 'is-full-frame': isFullFrame }">
              <!-- Top Text -->
              <div id="top-texts-container">
                <div id="prepare-liveness">
                  <!-- Top Prepare-icon Animation -->
                  <div
                    v-if="sanitizedPrepareTopContent"
                    v-html="sanitizedPrepareTopContent"
                    class="prepare-top-content-container"
                  />
                  {{ t('ekyc.liveness.prepare_liveness') }}
                </div>
                <div id="detecting-face-position">
                  {{ t('ekyc.liveness.detecting_face_position') }}
                  <span class="dot dot-1">.</span>
                  <span class="dot dot-2">.</span>
                  <span class="dot dot-3">.</span>
                </div>
                <div id="align-your-face">
                  {{
                    {
                      idle: t('ekyc.liveness.align_your_face'),
                      blink_twice: t('ekyc.liveness.blink_twice'),
                      idle_background: t('ekyc.liveness.align_your_face'),
                    }[currentAction] || t('ekyc.liveness.align_your_face')
                  }}
                </div>
                <div id="press-button">
                  {{ t('ekyc.liveness.press_button') }}
                </div>
                <div id="scan-complete">
                  {{ t('ekyc.recorder.scan_complete') }}
                </div>
                <div id="action-failed">
                  {{ t('ekyc.recorder.action_failed') }}
                </div>
                <div id="error-message">
                  {{ errorMessage }}
                </div>
              </div>

              <!-- Self timer - for manual snap -->
              <div
                v-if="
                  canManualSnap && !shouldShowUploadProgress && showSelfTimer && selfTimerCount > 0
                "
                class="self-timer-display"
                :key="`timer-${selfTimerCount}`"
              >
                {{ selfTimerCount }}
              </div>

              <!-- Instruction -->
              <div id="start-instructions-container">
                <div class="start-instruction do">
                  <ImageSelector name="liveness-instruction-light-good" class="instruction-image" />
                  <span>{{ t('ekyc.liveness.instructions.0') }}</span>
                  <ReactiveIcon icon="lucide:check" />
                </div>
                <hr />
                <div class="start-instruction dont">
                  <ImageSelector name="liveness-instruction-glasses" class="instruction-image" />
                  <span>{{ t('ekyc.liveness.instructions.1') }}</span>
                  <ReactiveIcon icon="lucide:x" />
                </div>
                <div class="start-instruction dont">
                  <ImageSelector name="liveness-instruction-mask" class="instruction-image" />
                  <span>{{ t('ekyc.liveness.instructions.2') }}</span>
                  <ReactiveIcon icon="lucide:x" />
                </div>
                <div class="start-instruction dont">
                  <ImageSelector name="liveness-instruction-light-bad" class="instruction-image" />
                  <span>{{ t('ekyc.liveness.instructions.3') }}</span>
                  <ReactiveIcon icon="lucide:x" />
                </div>
                <div class="start-instruction dont">
                  <ImageSelector name="liveness-instruction-multi-face" class="instruction-image" />
                  <span>{{ t('ekyc.liveness.instructions.4') }}</span>
                  <ReactiveIcon icon="lucide:x" />
                </div>
              </div>

              <!-- Instruction Dim BG -->
              <div
                id="instruction-dim-bg"
                class="absolute w-full h-full left-0 top-0 bg-black/80 z-10"
              />

              <!-- Skip instruction button -->
              <button
                id="skip-instruction-btn"
                class="button"
                :class="canClickSkipInstruction ? '' : 'is-loading'"
                @click="doSkipInstruction"
              >
                <div>{{ t('ekyc.video.start') }}</div>
              </button>

              <!-- Start Btn -->
              <div v-if="canManualSnap && !shouldShowUploadProgress" id="manual-take-btn">
                <button class="inner-circle" type="button" @click="manualSnap" />
              </div>

              <!--  Fail Indicator -->
              <div id="fail-indicator">
                <ReactiveIcon icon="lucide:x" />
                <div class="inner-circle" />
              </div>

              <!--  Warn Indicator -->
              <div id="warn-indicator">
                <ReactiveIcon icon="ion:alert" />
                <div class="inner-circle" />
              </div>

              <!--  Success Indicator -->
              <div id="success-indicator">
                <ReactiveIcon icon="lucide:check" />
                <div class="inner-circle" />
              </div>

              <!--  Sequence Action Indicator -->
              <div id="action-indicator">
                <ReactiveIcon icon="lucide:eye" />
                <div class="inner-circle" />
              </div>

              <!-- Action Progression -->
              <LivenessModalActionProgressionNext
                id="action-progression"
                ref="actionProgressionRef"
              />

              <!-- State 4: Success & Uploading... -->
              <EkycLoadingOverlay
                ref="uploadingOverlayRef"
                v-show="shouldShowUploadProgress"
                :progress="mediaData.progress"
                class="bg-white"
              />

              <!-- Camera -->
              <video
                id="camera"
                ref="videoRef"
                muted
                playsinline
                preload="auto"
                poster="https://cdn.uppass.io/images/transparent.png"
              />
              <FaceMaskSvgNext
                ref="faceMaskRef"
                id="face-mask-svg"
                class="opacity-0"
                :is-full-frame="isFullFrame"
              />
              <MovingPixel />
            </div>
          </div>
        </div>
      </template>
    </EkycBase>
  </div>
</template>

<style lang="scss" scoped>
.modal.mask-modal .modal-card.camera-view.mode-liveness {
  .modal-card-body {
    @apply flex items-center justify-center;
    @apply absolute w-full h-full;
    @apply bg-[#26336d];
  }

  .ekyc-action-result {
    .title {
      margin-top: 1.5rem;
    }
  }

  #camera,
  :deep(.liveness-debug-overlay .face-landmarks) {
    position: fixed;

    object-fit: cover;
    max-height: unset;
    max-width: unset;
  }
}

.close-btn {
  top: 1%;
  right: 2%;
  z-index: 4;
  font-size: 2rem;
  display: block;
  position: absolute;
  color: #fff;
}

.ekyc-action-result.is-animated {
  .icon,
  .image {
    animation: come-to-middle 1s;
    animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
  }
  .title,
  .subtitle {
    animation: fade-in 1s;
    animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
  }
}

#top-texts-container {
  @apply absolute top-12 z-30 left-0 right-0;
  @apply pointer-events-none min-h-40;
  @apply font-extrabold text-2xl text-white;
  > * {
    @apply text-inherit px-2 opacity-0;
    @apply absolute left-0 right-0;
  }
  .dot {
    @apply opacity-0;
  }
}

#start-instructions-container {
  @apply flex flex-col gap-4 w-11/12 text-left;
  @apply text-lg font-medium text-white;
  @apply absolute top-36;
  @apply bg-slate-800/60 p-6 rounded-3xl opacity-0;
  @apply z-20;

  > hr {
    @apply m-0 h-px;
    @apply hidden;
  }

  > .start-instruction {
    @apply flex items-center justify-start gap-6;
    @apply hidden;

    .instruction-image {
      @apply w-16 h-16 min-w-16 rounded-lg m-0;
    }

    span {
      @apply w-1/2;
    }

    :deep(.icon) {
      @apply min-w-9 w-9 h-9 text-white ml-auto;
    }

    &.do {
      :deep(.icon) {
        @apply rounded-full bg-[var(--color-success)];
      }
    }

    &.dont {
      :deep(.icon) {
        @apply rounded-full bg-[#ED6898];
      }
    }
  }
}

#top-texts-container:has(.prepare-top-content-container) + #start-instructions-container {
  @apply top-60;
}

#skip-instruction-btn {
  @apply absolute z-50 bottom-4;
  @apply w-full !max-w-[unset];
  @apply text-[var(--app-button-text-color)];
  @apply bg-[var(--app-button-background-color)];
  @apply border-none;
  @apply opacity-0;
  width: calc(100% - 3rem);
}

#manual-take-btn {
  @apply absolute z-50 max-w-[90vw];
  @apply flex flex-col gap-4 items-center justify-center;
  @apply bottom-12;
  @apply font-extrabold text-2xl text-white;
  text-shadow: 0px 0px 2px #000;

  .inner-circle {
    @apply w-14 h-14 rounded-full;
    @apply bg-white hover:bg-gray-100;
    @apply outline outline-4 outline-white outline-offset-4;
    --tw-ring-offset-color: transparent;
  }
}

#success-indicator,
#fail-indicator,
#warn-indicator,
#action-progression {
  @apply absolute z-40;
  @apply bottom-12;
  @apply pointer-events-none;
  @apply opacity-0;

  .inner-circle {
    @apply w-16 h-16 m-auto rounded-full;
  }
  .icon {
    @apply translate-y-[60px] text-6xl text-[#2a3049];
  }
}

#action-indicator {
  @apply absolute z-40;
  @apply bottom-12;
  @apply opacity-0;
  animation: sync-animation 3s infinite ease-in-out;

  .icon {
    @apply w-16 h-16 text-6xl text-white;
    transform: scaleY(var(--scale-y));
  }

  // :deep(circle) {
  //   r: var(--radius);
  // }
}

@property --scale-y {
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}

@property --radius {
  syntax: '<number>';
  inherits: true;
  initial-value: 3;
}

@keyframes sync-animation {
  0%,
  10%,
  20%,
  30%,
  40%,
  100% {
    --scale-y: 1;
    // --radius: 3;
  }
  15%,
  35% {
    --scale-y: 0.1;
    // --radius: 0;
  }
}

#success-indicator {
  .inner-circle {
    @apply bg-[var(--color-success,#19CEB0)];
  }
}

#fail-indicator {
  .inner-circle {
    @apply bg-[var(--color-danger,#ED6898)];
  }
}

#warn-indicator {
  .inner-circle {
    @apply bg-[var(--color-warning,#FFE27C)];
  }
}

.is-full-frame {
  #top-texts-container {
    top: 3%;
  }

  #manual-take-btn,
  #success-indicator,
  #fail-indicator,
  #warn-indicator,
  #action-indicator,
  #action-progression {
    bottom: 4%;
  }
}

.self-timer-display {
  position: absolute;
  top: 15%;
  z-index: 1;
  font-weight: 500;
  font-size: 8.75rem;
  line-height: 150px;
  text-align: center;
  color: white;

  animation: fadeOutAndIn 1s forwards;
}

@keyframes come-to-middle {
  0% {
    transform: translateY(-50vh) scale(1);
  }
  50% {
    transform: translateY(0px) scale(1.5);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  50% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-height: 630px) {
  #top-texts-container {
    font-size: 20px !important;
    top: 2rem;
  }

  #start-instructions-container {
    top: 6rem;
    overflow: auto;
    max-height: calc(95vh - 5rem);
    padding: 1rem 1.5rem;

    .start-instruction {
      font-size: 14px !important;

      .instruction-image {
        min-width: 40px !important;
        width: 40px !important;
        height: 40px !important;
      }

      :deep(.icon) {
        min-width: 24px !important;
        width: 24px !important;
        height: 24px !important;
        font-size: 20px;
      }
    }
  }
}

.prepare-top-content-container {
  @apply flex justify-around justify-items-center text-center mt-8 mb-8;
}

.prepare-top-content-container > :deep(*) {
  /* Animation properties:
      1. 'appear' runs once for 0.8s, then holds its final state.
      2. 'bob' starts after 0.8s and loops infinitely.
  */
  animation:
    appear 0.8s cubic-bezier(0.25, 0.1, 0.25, 1) 0.05s forwards,
    bob 2.5s cubic-bezier(0.25, 0.1, 0.25, 1) 0.8s infinite;
}

/*
  - The "appear" animation runs once to fade in and swoop up.
  - The "forwards" fill mode makes the prepare-icon retain its final state.
*/
@keyframes appear {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/*
  - The "bob" animation loops infinitely after "appear" is finished.
  - This creates the continuous up-and-down motion.
*/
@keyframes bob {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px); /* Bob up */
  }
  100% {
    transform: translateY(0); /* Bob down */
  }
}

/*
  - The "fadeOutAndIn" animation runs once to pop up and fade out 
*/
@keyframes fadeOutAndIn {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
