{"version": 3, "sources": ["../../lodash/_arrayAggregator.js", "../../lodash/_baseAggregator.js", "../../lodash/_createAggregator.js"], "sourcesContent": ["/**\n * A specialized version of `baseAggregator` for arrays.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction arrayAggregator(array, setter, iteratee, accumulator) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    var value = array[index];\n    setter(accumulator, value, iteratee(value), array);\n  }\n  return accumulator;\n}\n\nmodule.exports = arrayAggregator;\n", "var baseEach = require('./_baseEach');\n\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function(value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\n\nmodule.exports = baseAggregator;\n", "var arrayAggregator = require('./_arrayAggregator'),\n    baseAggregator = require('./_baseAggregator'),\n    baseIteratee = require('./_baseIteratee'),\n    isArray = require('./isArray');\n\n/**\n * Creates a function like `_.groupBy`.\n *\n * @private\n * @param {Function} setter The function to set accumulator values.\n * @param {Function} [initializer] The accumulator object initializer.\n * @returns {Function} Returns the new aggregator function.\n */\nfunction createAggregator(setter, initializer) {\n  return function(collection, iteratee) {\n    var func = isArray(collection) ? arrayAggregator : baseAggregator,\n        accumulator = initializer ? initializer() : {};\n\n    return func(collection, setter, baseIteratee(iteratee, 2), accumulator);\n  };\n}\n\nmodule.exports = createAggregator;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAUA,aAAS,gBAAgB,OAAO,QAAQ,UAAU,aAAa;AAC7D,UAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM;AAEvC,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,MAAM,KAAK;AACvB,eAAO,aAAa,OAAO,SAAS,KAAK,GAAG,KAAK;AAAA,MACnD;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAI,WAAW;AAaf,aAAS,eAAe,YAAY,QAAQ,UAAU,aAAa;AACjE,eAAS,YAAY,SAAS,OAAO,KAAKA,aAAY;AACpD,eAAO,aAAa,OAAO,SAAS,KAAK,GAAGA,WAAU;AAAA,MACxD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,iBAAiB;AADrB,QAEI,eAAe;AAFnB,QAGI,UAAU;AAUd,aAAS,iBAAiB,QAAQ,aAAa;AAC7C,aAAO,SAAS,YAAY,UAAU;AACpC,YAAI,OAAO,QAAQ,UAAU,IAAI,kBAAkB,gBAC/C,cAAc,cAAc,YAAY,IAAI,CAAC;AAEjD,eAAO,KAAK,YAAY,QAAQ,aAAa,UAAU,CAAC,GAAG,WAAW;AAAA,MACxE;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["collection"]}