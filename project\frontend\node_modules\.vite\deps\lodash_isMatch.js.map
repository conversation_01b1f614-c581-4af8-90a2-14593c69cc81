{"version": 3, "sources": ["../../lodash/isMatch.js"], "sourcesContent": ["var baseIsMatch = require('./_baseIsMatch'),\n    getMatchData = require('./_getMatchData');\n\n/**\n * Performs a partial deep comparison between `object` and `source` to\n * determine if `object` contains equivalent property values.\n *\n * **Note:** This method is equivalent to `_.matches` when `source` is\n * partially applied.\n *\n * Partial comparisons will match empty array and empty object `source`\n * values against any array or object value, respectively. See `_.isEqual`\n * for a list of supported value comparisons.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n *\n * _.isMatch(object, { 'b': 2 });\n * // => true\n *\n * _.isMatch(object, { 'b': 1 });\n * // => false\n */\nfunction isMatch(object, source) {\n  return object === source || baseIsMatch(object, source, getMatchData(source));\n}\n\nmodule.exports = isMatch;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,eAAe;AA8BnB,aAAS,QAAQ,QAAQ,QAAQ;AAC/B,aAAO,WAAW,UAAU,YAAY,QAAQ,QAAQ,aAAa,MAAM,CAAC;AAAA,IAC9E;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}