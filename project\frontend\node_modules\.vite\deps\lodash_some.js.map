{"version": 3, "sources": ["../../lodash/_baseSome.js", "../../lodash/some.js"], "sourcesContent": ["var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.some` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction baseSome(collection, predicate) {\n  var result;\n\n  baseEach(collection, function(value, index, collection) {\n    result = predicate(value, index, collection);\n    return !result;\n  });\n  return !!result;\n}\n\nmodule.exports = baseSome;\n", "var arraySome = require('./_arraySome'),\n    baseIteratee = require('./_baseIteratee'),\n    baseSome = require('./_baseSome'),\n    isArray = require('./isArray'),\n    isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Checks if `predicate` returns truthy for **any** element of `collection`.\n * Iteration is stopped once `predicate` returns truthy. The predicate is\n * invoked with three arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n * @example\n *\n * _.some([null, 0, 'yes', false], Boolean);\n * // => true\n *\n * var users = [\n *   { 'user': 'barney', 'active': true },\n *   { 'user': 'fred',   'active': false }\n * ];\n *\n * // The `_.matches` iteratee shorthand.\n * _.some(users, { 'user': 'barney', 'active': false });\n * // => false\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.some(users, ['active', false]);\n * // => true\n *\n * // The `_.property` iteratee shorthand.\n * _.some(users, 'active');\n * // => true\n */\nfunction some(collection, predicate, guard) {\n  var func = isArray(collection) ? arraySome : baseSome;\n  if (guard && isIterateeCall(collection, predicate, guard)) {\n    predicate = undefined;\n  }\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nmodule.exports = some;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAWf,aAAS,SAAS,YAAY,WAAW;AACvC,UAAI;AAEJ,eAAS,YAAY,SAAS,OAAO,OAAOA,aAAY;AACtD,iBAAS,UAAU,OAAO,OAAOA,WAAU;AAC3C,eAAO,CAAC;AAAA,MACV,CAAC;AACD,aAAO,CAAC,CAAC;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,eAAe;AADnB,QAEI,WAAW;AAFf,QAGI,UAAU;AAHd,QAII,iBAAiB;AAsCrB,aAAS,KAAK,YAAY,WAAW,OAAO;AAC1C,UAAI,OAAO,QAAQ,UAAU,IAAI,YAAY;AAC7C,UAAI,SAAS,eAAe,YAAY,WAAW,KAAK,GAAG;AACzD,oBAAY;AAAA,MACd;AACA,aAAO,KAAK,YAAY,aAAa,WAAW,CAAC,CAAC;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["collection"]}