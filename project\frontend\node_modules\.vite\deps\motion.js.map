{"version": 3, "sources": ["../../motion-utils/dist/es/array.mjs", "../../motion-utils/dist/es/clamp.mjs", "../../motion-utils/dist/es/format-error-message.mjs", "../../motion-utils/dist/es/errors.mjs", "../../motion-utils/dist/es/global-config.mjs", "../../motion-utils/dist/es/is-numerical-string.mjs", "../../motion-utils/dist/es/is-object.mjs", "../../motion-utils/dist/es/is-zero-value-string.mjs", "../../motion-utils/dist/es/memo.mjs", "../../motion-utils/dist/es/noop.mjs", "../../motion-utils/dist/es/pipe.mjs", "../../motion-utils/dist/es/progress.mjs", "../../motion-utils/dist/es/subscription-manager.mjs", "../../motion-utils/dist/es/time-conversion.mjs", "../../motion-utils/dist/es/velocity-per-second.mjs", "../../motion-utils/dist/es/warn-once.mjs", "../../motion-utils/dist/es/wrap.mjs", "../../motion-utils/dist/es/easing/cubic-bezier.mjs", "../../motion-utils/dist/es/easing/modifiers/mirror.mjs", "../../motion-utils/dist/es/easing/modifiers/reverse.mjs", "../../motion-utils/dist/es/easing/back.mjs", "../../motion-utils/dist/es/easing/anticipate.mjs", "../../motion-utils/dist/es/easing/circ.mjs", "../../motion-utils/dist/es/easing/ease.mjs", "../../motion-utils/dist/es/easing/steps.mjs", "../../motion-utils/dist/es/easing/utils/is-easing-array.mjs", "../../motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs", "../../motion-utils/dist/es/easing/utils/is-bezier-definition.mjs", "../../motion-utils/dist/es/easing/utils/map.mjs", "../../motion-dom/dist/es/frameloop/order.mjs", "../../motion-dom/dist/es/stats/buffer.mjs", "../../motion-dom/dist/es/frameloop/render-step.mjs", "../../motion-dom/dist/es/frameloop/batcher.mjs", "../../motion-dom/dist/es/frameloop/frame.mjs", "../../motion-dom/dist/es/frameloop/sync-time.mjs", "../../motion-dom/dist/es/stats/animation-count.mjs", "../../motion-dom/dist/es/animation/utils/is-css-variable.mjs", "../../motion-dom/dist/es/value/types/numbers/index.mjs", "../../motion-dom/dist/es/value/types/utils/sanitize.mjs", "../../motion-dom/dist/es/value/types/utils/float-regex.mjs", "../../motion-dom/dist/es/value/types/utils/is-nullish.mjs", "../../motion-dom/dist/es/value/types/utils/single-color-regex.mjs", "../../motion-dom/dist/es/value/types/color/utils.mjs", "../../motion-dom/dist/es/value/types/color/rgba.mjs", "../../motion-dom/dist/es/value/types/color/hex.mjs", "../../motion-dom/dist/es/value/types/numbers/units.mjs", "../../motion-dom/dist/es/value/types/color/hsla.mjs", "../../motion-dom/dist/es/value/types/color/index.mjs", "../../motion-dom/dist/es/value/types/utils/color-regex.mjs", "../../motion-dom/dist/es/value/types/complex/index.mjs", "../../motion-dom/dist/es/value/types/color/hsla-to-rgba.mjs", "../../motion-dom/dist/es/utils/mix/immediate.mjs", "../../motion-dom/dist/es/utils/mix/number.mjs", "../../motion-dom/dist/es/utils/mix/color.mjs", "../../motion-dom/dist/es/utils/mix/visibility.mjs", "../../motion-dom/dist/es/utils/mix/complex.mjs", "../../motion-dom/dist/es/utils/mix/index.mjs", "../../motion-dom/dist/es/animation/drivers/frame.mjs", "../../motion-dom/dist/es/animation/waapi/utils/linear.mjs", "../../motion-dom/dist/es/animation/generators/utils/calc-duration.mjs", "../../motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs", "../../motion-dom/dist/es/animation/generators/utils/velocity.mjs", "../../motion-dom/dist/es/animation/generators/spring/defaults.mjs", "../../motion-dom/dist/es/animation/generators/spring/find.mjs", "../../motion-dom/dist/es/animation/generators/spring/index.mjs", "../../motion-dom/dist/es/animation/generators/inertia.mjs", "../../motion-dom/dist/es/utils/interpolate.mjs", "../../motion-dom/dist/es/animation/keyframes/offsets/fill.mjs", "../../motion-dom/dist/es/animation/keyframes/offsets/default.mjs", "../../motion-dom/dist/es/animation/keyframes/offsets/time.mjs", "../../motion-dom/dist/es/animation/generators/keyframes.mjs", "../../motion-dom/dist/es/animation/keyframes/get-final.mjs", "../../motion-dom/dist/es/animation/utils/replace-transition-type.mjs", "../../motion-dom/dist/es/animation/utils/WithPromise.mjs", "../../motion-dom/dist/es/animation/JSAnimation.mjs", "../../motion-dom/dist/es/animation/keyframes/utils/fill-wildcards.mjs", "../../motion-dom/dist/es/render/dom/parse-transform.mjs", "../../motion-dom/dist/es/render/utils/keys-transform.mjs", "../../motion-dom/dist/es/animation/keyframes/utils/unit-conversion.mjs", "../../motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs", "../../motion-dom/dist/es/render/dom/is-css-var.mjs", "../../motion-dom/dist/es/render/dom/style-set.mjs", "../../motion-dom/dist/es/utils/supports/scroll-timeline.mjs", "../../motion-dom/dist/es/utils/supports/flags.mjs", "../../motion-dom/dist/es/utils/supports/memo.mjs", "../../motion-dom/dist/es/utils/supports/linear-easing.mjs", "../../motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs", "../../motion-dom/dist/es/animation/waapi/easing/supported.mjs", "../../motion-dom/dist/es/animation/waapi/easing/map-easing.mjs", "../../motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs", "../../motion-dom/dist/es/animation/generators/utils/is-generator.mjs", "../../motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs", "../../motion-dom/dist/es/animation/NativeAnimation.mjs", "../../motion-dom/dist/es/animation/waapi/utils/unsupported-easing.mjs", "../../motion-dom/dist/es/animation/NativeAnimationExtended.mjs", "../../motion-dom/dist/es/animation/utils/is-animatable.mjs", "../../motion-dom/dist/es/animation/utils/can-animate.mjs", "../../motion-dom/dist/es/animation/utils/make-animation-instant.mjs", "../../motion-dom/dist/es/animation/waapi/supports/waapi.mjs", "../../motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs", "../../motion-dom/dist/es/animation/GroupAnimation.mjs", "../../motion-dom/dist/es/animation/GroupAnimationWithThen.mjs", "../../motion-dom/dist/es/animation/NativeAnimationWrapper.mjs", "../../motion-dom/dist/es/animation/utils/active-animations.mjs", "../../motion-dom/dist/es/animation/utils/css-variables-conversion.mjs", "../../motion-dom/dist/es/animation/utils/get-value-transition.mjs", "../../motion-dom/dist/es/render/utils/keys-position.mjs", "../../motion-dom/dist/es/value/types/auto.mjs", "../../motion-dom/dist/es/value/types/test.mjs", "../../motion-dom/dist/es/value/types/dimensions.mjs", "../../motion-dom/dist/es/animation/keyframes/utils/is-none.mjs", "../../motion-dom/dist/es/value/types/complex/filter.mjs", "../../motion-dom/dist/es/value/types/int.mjs", "../../motion-dom/dist/es/value/types/maps/transform.mjs", "../../motion-dom/dist/es/value/types/maps/number.mjs", "../../motion-dom/dist/es/value/types/maps/defaults.mjs", "../../motion-dom/dist/es/value/types/utils/animatable-none.mjs", "../../motion-dom/dist/es/animation/keyframes/utils/make-none-animatable.mjs", "../../motion-dom/dist/es/animation/keyframes/DOMKeyframesResolver.mjs", "../../motion-dom/dist/es/animation/waapi/utils/px-values.mjs", "../../motion-dom/dist/es/animation/keyframes/utils/apply-px-defaults.mjs", "../../motion-dom/dist/es/animation/waapi/easing/is-supported.mjs", "../../motion-dom/dist/es/animation/waapi/supports/partial-keyframes.mjs", "../../motion-dom/dist/es/animation/waapi/utils/accelerated-values.mjs", "../../motion-dom/dist/es/render/dom/utils/camel-to-dash.mjs", "../../motion-dom/dist/es/utils/resolve-elements.mjs", "../../motion-dom/dist/es/effects/utils/create-dom-effect.mjs", "../../motion-dom/dist/es/value/types/utils/get-as-type.mjs", "../../motion-dom/dist/es/effects/MotionValueState.mjs", "../../motion-dom/dist/es/effects/utils/create-effect.mjs", "../../motion-dom/dist/es/effects/attr/index.mjs", "../../motion-dom/dist/es/effects/prop/index.mjs", "../../motion-dom/dist/es/utils/is-html-element.mjs", "../../motion-dom/dist/es/value/index.mjs", "../../motion-dom/dist/es/effects/style/transform.mjs", "../../motion-dom/dist/es/effects/style/index.mjs", "../../motion-dom/dist/es/effects/svg/index.mjs", "../../motion-dom/dist/es/frameloop/microtask.mjs", "../../motion-dom/dist/es/gestures/drag/state/is-active.mjs", "../../motion-dom/dist/es/gestures/drag/state/set-active.mjs", "../../motion-dom/dist/es/gestures/utils/setup.mjs", "../../motion-dom/dist/es/gestures/hover.mjs", "../../motion-dom/dist/es/gestures/utils/is-node-or-child.mjs", "../../motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs", "../../motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs", "../../motion-dom/dist/es/gestures/press/utils/state.mjs", "../../motion-dom/dist/es/gestures/press/utils/keyboard.mjs", "../../motion-dom/dist/es/gestures/press/index.mjs", "../../motion-dom/dist/es/render/dom/style-computed.mjs", "../../motion-dom/dist/es/utils/is-svg-element.mjs", "../../motion-dom/dist/es/resize/handle-element.mjs", "../../motion-dom/dist/es/resize/handle-window.mjs", "../../motion-dom/dist/es/resize/index.mjs", "../../motion-dom/dist/es/scroll/observe.mjs", "../../motion-dom/dist/es/stats/index.mjs", "../../motion-dom/dist/es/utils/is-svg-svg-element.mjs", "../../motion-dom/dist/es/utils/stagger.mjs", "../../motion-dom/dist/es/utils/transform.mjs", "../../motion-dom/dist/es/value/subscribe-value.mjs", "../../motion-dom/dist/es/value/transform-value.mjs", "../../motion-dom/dist/es/value/map-value.mjs", "../../motion-dom/dist/es/value/utils/is-motion-value.mjs", "../../motion-dom/dist/es/value/spring-value.mjs", "../../motion-dom/dist/es/value/types/utils/find.mjs", "../../motion-dom/dist/es/view/utils/choose-layer-type.mjs", "../../motion-dom/dist/es/view/utils/css.mjs", "../../motion-dom/dist/es/view/utils/get-layer-info.mjs", "../../motion-dom/dist/es/view/utils/get-view-animations.mjs", "../../motion-dom/dist/es/view/utils/has-target.mjs", "../../motion-dom/dist/es/view/start.mjs", "../../motion-dom/dist/es/view/queue.mjs", "../../motion-dom/dist/es/view/index.mjs", "../../motion-dom/dist/es/frameloop/index-legacy.mjs", "../../framer-motion/dist/es/animation/utils/is-dom-keyframes.mjs", "../../framer-motion/dist/es/animation/animate/resolve-subjects.mjs", "../../framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs", "../../framer-motion/dist/es/animation/sequence/utils/calc-time.mjs", "../../framer-motion/dist/es/animation/sequence/utils/edit.mjs", "../../framer-motion/dist/es/animation/sequence/utils/normalize-times.mjs", "../../framer-motion/dist/es/animation/sequence/utils/sort.mjs", "../../framer-motion/dist/es/animation/sequence/create.mjs", "../../framer-motion/dist/es/render/store.mjs", "../../framer-motion/dist/es/animation/utils/is-keyframes-target.mjs", "../../framer-motion/dist/es/render/utils/resolve-variants.mjs", "../../framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs", "../../framer-motion/dist/es/render/utils/setters.mjs", "../../framer-motion/dist/es/value/use-will-change/is.mjs", "../../framer-motion/dist/es/value/use-will-change/add-will-change.mjs", "../../framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs", "../../framer-motion/dist/es/animation/optimized-appear/data-id.mjs", "../../framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs", "../../framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs", "../../framer-motion/dist/es/animation/utils/default-transitions.mjs", "../../framer-motion/dist/es/animation/utils/is-transition-defined.mjs", "../../framer-motion/dist/es/animation/interfaces/motion-value.mjs", "../../framer-motion/dist/es/animation/interfaces/visual-element-target.mjs", "../../framer-motion/dist/es/projection/geometry/conversion.mjs", "../../framer-motion/dist/es/projection/utils/measure.mjs", "../../framer-motion/dist/es/motion/features/definitions.mjs", "../../framer-motion/dist/es/projection/geometry/models.mjs", "../../framer-motion/dist/es/utils/is-browser.mjs", "../../framer-motion/dist/es/utils/reduced-motion/state.mjs", "../../framer-motion/dist/es/utils/reduced-motion/index.mjs", "../../framer-motion/dist/es/animation/utils/is-animation-controls.mjs", "../../framer-motion/dist/es/render/utils/is-variant-label.mjs", "../../framer-motion/dist/es/render/utils/variant-props.mjs", "../../framer-motion/dist/es/render/utils/is-controlling-variants.mjs", "../../framer-motion/dist/es/render/utils/motion-values.mjs", "../../framer-motion/dist/es/render/VisualElement.mjs", "../../framer-motion/dist/es/render/dom/DOMVisualElement.mjs", "../../framer-motion/dist/es/render/html/utils/build-transform.mjs", "../../framer-motion/dist/es/render/html/utils/build-styles.mjs", "../../framer-motion/dist/es/render/html/utils/render.mjs", "../../framer-motion/dist/es/projection/styles/scale-correction.mjs", "../../framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs", "../../framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs", "../../framer-motion/dist/es/render/html/HTMLVisualElement.mjs", "../../framer-motion/dist/es/render/object/ObjectVisualElement.mjs", "../../framer-motion/dist/es/render/svg/utils/path.mjs", "../../framer-motion/dist/es/render/svg/utils/build-attrs.mjs", "../../framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs", "../../framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs", "../../framer-motion/dist/es/render/svg/utils/render.mjs", "../../framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs", "../../framer-motion/dist/es/render/svg/SVGVisualElement.mjs", "../../framer-motion/dist/es/animation/utils/create-visual-element.mjs", "../../framer-motion/dist/es/animation/animate/single-value.mjs", "../../framer-motion/dist/es/animation/animate/subject.mjs", "../../framer-motion/dist/es/animation/animate/sequence.mjs", "../../framer-motion/dist/es/animation/animate/index.mjs", "../../framer-motion/dist/es/animation/animators/waapi/animate-elements.mjs", "../../framer-motion/dist/es/animation/animators/waapi/animate-style.mjs", "../../framer-motion/dist/es/render/dom/scroll/info.mjs", "../../framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs", "../../framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs", "../../framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs", "../../framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs", "../../framer-motion/dist/es/render/dom/scroll/offsets/index.mjs", "../../framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs", "../../framer-motion/dist/es/render/dom/scroll/track.mjs", "../../framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs", "../../framer-motion/dist/es/render/dom/scroll/attach-animation.mjs", "../../framer-motion/dist/es/render/dom/scroll/attach-function.mjs", "../../framer-motion/dist/es/render/dom/scroll/index.mjs", "../../framer-motion/dist/es/render/dom/viewport/index.mjs", "../../framer-motion/dist/es/utils/delay.mjs", "../../framer-motion/dist/es/utils/distance.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n", "const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n", "function formatErrorMessage(message, errorCode) {\n    return errorCode\n        ? `${message}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${errorCode}`\n        : message;\n}\n\nexport { formatErrorMessage };\n", "import { formatErrorMessage } from './format-error-message.mjs';\n\nlet warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message, errorCode) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(formatErrorMessage(message, errorCode));\n        }\n    };\n    invariant = (check, message, errorCode) => {\n        if (!check) {\n            throw new Error(formatErrorMessage(message, errorCode));\n        }\n    };\n}\n\nexport { invariant, warning };\n", "const MotionGlobalConfig = {};\n\nexport { MotionGlobalConfig };\n", "/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n", "function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n", "/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n", "/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n", "/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n", "/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n", "/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n", "import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n", "/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n", "/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n", "import { formatErrorMessage } from './format-error-message.mjs';\n\nconst warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, errorCode) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(formatErrorMessage(message, errorCode));\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n", "const wrap = (min, max, v) => {\n    const rangeSize = max - min;\n    return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\nexport { wrap };\n", "import { noop } from '../noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from Gaë<PERSON>eau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticeably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n", "// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n", "// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n", "import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n", "import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n", "import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n", "import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = /*@__PURE__*/ cubicBezier(0.42, 0, 1, 1);\nconst easeOut = /*@__PURE__*/ cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = /*@__PURE__*/ cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n", "import { clamp } from '../clamp.mjs';\n\nfunction steps(numSteps, direction = \"end\") {\n    return (progress) => {\n        progress =\n            direction === \"end\"\n                ? Math.min(progress, 0.999)\n                : Math.max(progress, 0.001);\n        const expanded = progress * numSteps;\n        const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n        return clamp(0, 1, rounded / numSteps);\n    };\n}\n\nexport { steps };\n", "const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n", "import { wrap } from '../../wrap.mjs';\nimport { isEasingArray } from './is-easing-array.mjs';\n\nfunction getEasingForSegment(easing, i) {\n    return isEasingArray(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\n\nexport { getEasingForSegment };\n", "const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n", "import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`, \"cubic-bezier-length\");\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`, \"invalid-easing-type\");\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n", "const stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\nexport { stepsOrder };\n", "const statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\nexport { statsBuffer };\n", "import { statsBuffer } from '../stats/buffer.mjs';\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && statsBuffer.value) {\n                statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher };\n", "import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, frameSteps };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming\n                ? frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\nexport { time };\n", "const activeAnimations = {\n    layout: 0,\n    mainThread: 0,\n    waapi: 0,\n};\n\nexport { activeAnimations };\n", "const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = \n/*@__PURE__*/ checkStringStartsWith(\"--\");\nconst startsAsVariableToken = \n/*@__PURE__*/ checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\nexport { isCSSVariableName, isCSSVariableToken };\n", "import { clamp } from 'motion-utils';\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => clamp(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\nexport { alpha, number, scale };\n", "// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\n\nexport { sanitize };\n", "const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\nexport { floatRegex };\n", "function isNullish(v) {\n    return v == null;\n}\n\nexport { isNullish };\n", "const singleColorRegex = /^(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))$/iu;\n\nexport { singleColorRegex };\n", "import { floatRegex } from '../utils/float-regex.mjs';\nimport { isNullish } from '../utils/is-nullish.mjs';\nimport { singleColorRegex } from '../utils/single-color-regex.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((typeof v === \"string\" &&\n        singleColorRegex.test(v) &&\n        v.startsWith(type)) ||\n        (testProp &&\n            !isNullish(v) &&\n            Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (typeof v !== \"string\")\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n", "import { clamp } from 'motion-utils';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = (v) => clamp(0, 255, v);\nconst rgbUnit = {\n    ...number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: /*@__PURE__*/ isColorString(\"rgb\", \"red\"),\n    parse: /*@__PURE__*/ splitColor(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        sanitize(alpha.transform(alpha$1)) +\n        \")\",\n};\n\nexport { rgbUnit, rgba };\n", "import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\n\nfunction parseHex(v) {\n    let r = \"\";\n    let g = \"\";\n    let b = \"\";\n    let a = \"\";\n    // If we have 6 characters, ie #FF0000\n    if (v.length > 5) {\n        r = v.substring(1, 3);\n        g = v.substring(3, 5);\n        b = v.substring(5, 7);\n        a = v.substring(7, 9);\n        // Or we have 3 characters, ie #F00\n    }\n    else {\n        r = v.substring(1, 2);\n        g = v.substring(2, 3);\n        b = v.substring(3, 4);\n        a = v.substring(4, 5);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: /*@__PURE__*/ isColorString(\"#\"),\n    parse: parseHex,\n    transform: rgba.transform,\n};\n\nexport { hex };\n", "/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/ (() => ({\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n}))();\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n", "import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: /*@__PURE__*/ isColorString(\"hsl\", \"hue\"),\n    parse: /*@__PURE__*/ splitColor(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            percent.transform(sanitize(saturation)) +\n            \", \" +\n            percent.transform(sanitize(lightness)) +\n            \", \" +\n            sanitize(alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\nexport { hsla };\n", "import { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\n\nconst color = {\n    test: (v) => rgba.test(v) || hex.test(v) || hsla.test(v),\n    parse: (v) => {\n        if (rgba.test(v)) {\n            return rgba.parse(v);\n        }\n        else if (hsla.test(v)) {\n            return hsla.parse(v);\n        }\n        else {\n            return hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return typeof v === \"string\"\n            ? v\n            : v.hasOwnProperty(\"red\")\n                ? rgba.transform(v)\n                : hsla.transform(v);\n    },\n    getAnimatableNone: (v) => {\n        const parsed = color.parse(v);\n        parsed.alpha = 0;\n        return color.transform(parsed);\n    },\n};\n\nexport { color };\n", "const colorRegex = /(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))/giu;\n\nexport { colorRegex };\n", "import { color } from '../color/index.mjs';\nimport { colorRegex } from '../utils/color-regex.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\n\nfunction test(v) {\n    return (isNaN(v) &&\n        typeof v === \"string\" &&\n        (v.match(floatRegex)?.length || 0) +\n            (v.match(colorRegex)?.length || 0) >\n            0);\n}\nconst NUMBER_TOKEN = \"number\";\nconst COLOR_TOKEN = \"color\";\nconst VAR_TOKEN = \"var\";\nconst VAR_FUNCTION_TOKEN = \"var(\";\nconst SPLIT_TOKEN = \"${}\";\n// this regex consists of the `singleCssVariableRegex|rgbHSLValueRegex|digitRegex`\nconst complexRegex = /var\\s*\\(\\s*--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)|#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\)|-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/giu;\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const values = [];\n    const indexes = {\n        color: [],\n        number: [],\n        var: [],\n    };\n    const types = [];\n    let i = 0;\n    const tokenised = originalValue.replace(complexRegex, (parsedValue) => {\n        if (color.test(parsedValue)) {\n            indexes.color.push(i);\n            types.push(COLOR_TOKEN);\n            values.push(color.parse(parsedValue));\n        }\n        else if (parsedValue.startsWith(VAR_FUNCTION_TOKEN)) {\n            indexes.var.push(i);\n            types.push(VAR_TOKEN);\n            values.push(parsedValue);\n        }\n        else {\n            indexes.number.push(i);\n            types.push(NUMBER_TOKEN);\n            values.push(parseFloat(parsedValue));\n        }\n        ++i;\n        return SPLIT_TOKEN;\n    });\n    const split = tokenised.split(SPLIT_TOKEN);\n    return { values, split, indexes, types };\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { split, types } = analyseComplexValue(source);\n    const numSections = split.length;\n    return (v) => {\n        let output = \"\";\n        for (let i = 0; i < numSections; i++) {\n            output += split[i];\n            if (v[i] !== undefined) {\n                const type = types[i];\n                if (type === NUMBER_TOKEN) {\n                    output += sanitize(v[i]);\n                }\n                else if (type === COLOR_TOKEN) {\n                    output += color.transform(v[i]);\n                }\n                else {\n                    output += v[i];\n                }\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : color.test(v) ? color.getAnimatableNone(v) : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\nexport { analyseComplexValue, complex };\n", "// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\nexport { hslaToRgba };\n", "function mixImmediate(a, b) {\n    return (p) => (p > 0 ? b : a);\n}\n\nexport { mixImmediate };\n", "/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mixNumber = (from, to, progress) => {\n    return from + (to - from) * progress;\n};\n\nexport { mixNumber };\n", "import { warning } from 'motion-utils';\nimport { hex } from '../../value/types/color/hex.mjs';\nimport { hsla } from '../../value/types/color/hsla.mjs';\nimport { hslaToRgba } from '../../value/types/color/hsla-to-rgba.mjs';\nimport { rgba } from '../../value/types/color/rgba.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber } from './number.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const expo = v * (to * to - fromExpo) + fromExpo;\n    return expo < 0 ? 0 : Math.sqrt(expo);\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    warning(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`, \"color-not-animatable\");\n    if (!Boolean(type))\n        return false;\n    let model = type.parse(color);\n    if (type === hsla) {\n        // TODO Remove this cast - needed since Motion's stricter typing\n        model = hslaToRgba(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    if (!fromRGBA || !toRGBA) {\n        return mixImmediate(from, to);\n    }\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = mixNumber(fromRGBA.alpha, toRGBA.alpha, v);\n        return rgba.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n", "const invisibleValues = new Set([\"none\", \"hidden\"]);\n/**\n * Returns a function that, when provided a progress value between 0 and 1,\n * will return the \"none\" or \"hidden\" string only when the progress is that of\n * the origin or target.\n */\nfunction mixVisibility(origin, target) {\n    if (invisibleValues.has(origin)) {\n        return (p) => (p <= 0 ? origin : target);\n    }\n    else {\n        return (p) => (p >= 1 ? target : origin);\n    }\n}\n\nexport { invisibleValues, mixVisibility };\n", "import { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\n\nfunction mixNumber(a, b) {\n    return (p) => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n    if (typeof a === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof a === \"string\") {\n        return isCSSVariableToken(a)\n            ? mixImmediate\n            : color.test(a)\n                ? mixColor\n                : mixComplex;\n    }\n    else if (Array.isArray(a)) {\n        return mixArray;\n    }\n    else if (typeof a === \"object\") {\n        return color.test(a) ? mixColor : mixObject;\n    }\n    return mixImmediate;\n}\nfunction mixArray(a, b) {\n    const output = [...a];\n    const numValues = output.length;\n    const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n    return (p) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](p);\n        }\n        return output;\n    };\n}\nfunction mixObject(a, b) {\n    const output = { ...a, ...b };\n    const blendValue = {};\n    for (const key in output) {\n        if (a[key] !== undefined && b[key] !== undefined) {\n            blendValue[key] = getMixer(a[key])(a[key], b[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n}\nfunction matchOrder(origin, target) {\n    const orderedOrigin = [];\n    const pointers = { color: 0, var: 0, number: 0 };\n    for (let i = 0; i < target.values.length; i++) {\n        const type = target.types[i];\n        const originIndex = origin.indexes[type][pointers[type]];\n        const originValue = origin.values[originIndex] ?? 0;\n        orderedOrigin[i] = originValue;\n        pointers[type]++;\n    }\n    return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length &&\n        originStats.indexes.color.length === targetStats.indexes.color.length &&\n        originStats.indexes.number.length >= targetStats.indexes.number.length;\n    if (canInterpolate) {\n        if ((invisibleValues.has(origin) &&\n            !targetStats.values.length) ||\n            (invisibleValues.has(target) &&\n                !originStats.values.length)) {\n            return mixVisibility(origin, target);\n        }\n        return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`, \"complex-values-different\");\n        return mixImmediate(origin, target);\n    }\n};\n\nexport { getMixer, mixArray, mixComplex, mixObject };\n", "import { getMixer } from './complex.mjs';\nimport { mixNumber } from './number.mjs';\n\nfunction mix(from, to, p) {\n    if (typeof from === \"number\" &&\n        typeof to === \"number\" &&\n        typeof p === \"number\") {\n        return mixNumber(from, to, p);\n    }\n    const mixer = getMixer(from);\n    return mixer(from, to);\n}\n\nexport { mix };\n", "import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: (keepAlive = true) => frame.update(passTimestamp, keepAlive),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => (frameData.isProcessing ? frameData.timestamp : time.now()),\n    };\n};\n\nexport { frameloopDriver };\n", "const generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += Math.round(easing(i / (numPoints - 1)) * 10000) / 10000 + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n", "/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\nexport { calcGeneratorDuration, maxGeneratorDuration };\n", "import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n", "import { velocityPerSecond } from 'motion-utils';\n\nconst velocitySampleDuration = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - velocitySampleDuration, 0);\n    return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\n\nexport { calcGeneratorVelocity };\n", "const springDefaults = {\n    // Default spring physics\n    stiffness: 100,\n    damping: 10,\n    mass: 1.0,\n    velocity: 0.0,\n    // Default duration/bounce-based options\n    duration: 800, // in ms\n    bounce: 0.3,\n    visualDuration: 0.3, // in seconds\n    // Rest thresholds\n    restSpeed: {\n        granular: 0.01,\n        default: 2,\n    },\n    restDelta: {\n        granular: 0.005,\n        default: 0.5,\n    },\n    // Limits\n    minDuration: 0.01, // in seconds\n    maxDuration: 10.0, // in seconds\n    minDamping: 0.05,\n    maxDamping: 1,\n};\n\nexport { springDefaults };\n", "import { warning, secondsToMilliseconds, clamp, millisecondsToSeconds } from 'motion-utils';\nimport { springDefaults } from './defaults.mjs';\n\nconst safeMin = 0.001;\nfunction findSpring({ duration = springDefaults.duration, bounce = springDefaults.bounce, velocity = springDefaults.velocity, mass = springDefaults.mass, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= secondsToMilliseconds(springDefaults.maxDuration), \"Spring duration must be 10 seconds or less\", \"spring-duration-limit\");\n    let dampingRatio = 1 - bounce;\n    /**\n     * Restrict dampingRatio and duration to within acceptable ranges.\n     */\n    dampingRatio = clamp(springDefaults.minDamping, springDefaults.maxDamping, dampingRatio);\n    duration = clamp(springDefaults.minDuration, springDefaults.maxDuration, millisecondsToSeconds(duration));\n    if (dampingRatio < 1) {\n        /**\n         * Underdamped spring\n         */\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        /**\n         * Critically-damped spring\n         */\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = secondsToMilliseconds(duration);\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: springDefaults.stiffness,\n            damping: springDefaults.damping,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring };\n", "import { millisecondsToSeconds, secondsToMilliseconds, clamp } from 'motion-utils';\nimport { generateLinearEasing } from '../../waapi/utils/linear.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../utils/calc-duration.mjs';\nimport { createGeneratorEasing } from '../utils/create-generator-easing.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { springDefaults } from './defaults.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = {\n        velocity: springDefaults.velocity,\n        stiffness: springDefaults.stiffness,\n        damping: springDefaults.damping,\n        mass: springDefaults.mass,\n        isResolvedFromDuration: false,\n        ...options,\n    };\n    // stiffness/damping/mass overrides duration/bounce\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        if (options.visualDuration) {\n            const visualDuration = options.visualDuration;\n            const root = (2 * Math.PI) / (visualDuration * 1.2);\n            const stiffness = root * root;\n            const damping = 2 *\n                clamp(0.05, 1, 1 - (options.bounce || 0)) *\n                Math.sqrt(stiffness);\n            springOptions = {\n                ...springOptions,\n                mass: springDefaults.mass,\n                stiffness,\n                damping,\n            };\n        }\n        else {\n            const derived = findSpring(options);\n            springOptions = {\n                ...springOptions,\n                ...derived,\n                mass: springDefaults.mass,\n            };\n            springOptions.isResolvedFromDuration = true;\n        }\n    }\n    return springOptions;\n}\nfunction spring(optionsOrVisualDuration = springDefaults.visualDuration, bounce = springDefaults.bounce) {\n    const options = typeof optionsOrVisualDuration !== \"object\"\n        ? {\n            visualDuration: optionsOrVisualDuration,\n            keyframes: [0, 1],\n            bounce,\n        }\n        : optionsOrVisualDuration;\n    let { restSpeed, restDelta } = options;\n    const origin = options.keyframes[0];\n    const target = options.keyframes[options.keyframes.length - 1];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    const { stiffness, damping, mass, duration, velocity, isResolvedFromDuration, } = getSpringOptions({\n        ...options,\n        velocity: -millisecondsToSeconds(options.velocity || 0),\n    });\n    const initialVelocity = velocity || 0.0;\n    const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n    const initialDelta = target - origin;\n    const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n    /**\n     * If we're working on a granular scale, use smaller defaults for determining\n     * when the spring is finished.\n     *\n     * These defaults have been selected emprically based on what strikes a good\n     * ratio between feeling good and finishing as soon as changes are imperceptible.\n     */\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale\n        ? springDefaults.restSpeed.granular\n        : springDefaults.restSpeed.default);\n    restDelta || (restDelta = isGranularScale\n        ? springDefaults.restDelta.granular\n        : springDefaults.restDelta.default);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n        // Underdamped spring\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            return (target -\n                envelope *\n                    (((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) /\n                        angularFreq) *\n                        Math.sin(angularFreq * t) +\n                        initialDelta * Math.cos(angularFreq * t)));\n        };\n    }\n    else if (dampingRatio === 1) {\n        // Critically damped spring\n        resolveSpring = (t) => target -\n            Math.exp(-undampedAngularFreq * t) *\n                (initialDelta +\n                    (initialVelocity + undampedAngularFreq * initialDelta) * t);\n    }\n    else {\n        // Overdamped spring\n        const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            // When performing sinh or cosh values can hit Infinity so we cap them here\n            const freqForT = Math.min(dampedAngularFreq * t, 300);\n            return (target -\n                (envelope *\n                    ((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) *\n                        Math.sinh(freqForT) +\n                        dampedAngularFreq *\n                            initialDelta *\n                            Math.cosh(freqForT))) /\n                    dampedAngularFreq);\n        };\n    }\n    const generator = {\n        calculatedDuration: isResolvedFromDuration ? duration || null : null,\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                let currentVelocity = t === 0 ? initialVelocity : 0.0;\n                /**\n                 * We only need to calculate velocity for under-damped springs\n                 * as over- and critically-damped springs can't overshoot, so\n                 * checking only for displacement is enough.\n                 */\n                if (dampingRatio < 1) {\n                    currentVelocity =\n                        t === 0\n                            ? secondsToMilliseconds(initialVelocity)\n                            : calcGeneratorVelocity(resolveSpring, t, current);\n                }\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? target : current;\n            return state;\n        },\n        toString: () => {\n            const calculatedDuration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n            const easing = generateLinearEasing((progress) => generator.next(calculatedDuration * progress).value, calculatedDuration, 30);\n            return calculatedDuration + \"ms \" + easing;\n        },\n        toTransition: () => { },\n    };\n    return generator;\n}\nspring.applyToOptions = (options) => {\n    const generatorOptions = createGeneratorEasing(options, 100, spring);\n    options.ease = generatorOptions.ease;\n    options.duration = secondsToMilliseconds(generatorOptions.duration);\n    options.type = \"keyframes\";\n    return options;\n};\n\nexport { spring };\n", "import { spring } from './spring/index.mjs';\nimport { calcGeneratorVelocity } from './utils/velocity.mjs';\n\nfunction inertia({ keyframes, velocity = 0.0, power = 0.8, timeConstant = 325, bounceDamping = 10, bounceStiffness = 500, modifyTarget, min, max, restDelta = 0.5, restSpeed, }) {\n    const origin = keyframes[0];\n    const state = {\n        done: false,\n        value: origin,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = origin + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - origin;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / timeConstant);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDelta;\n        state.value = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.value))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = spring({\n            keyframes: [state.value, nearestBoundary(state.value)],\n            velocity: calcGeneratorVelocity(calcLatest, t, state.value), // TODO: This should be passing * 1000\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDelta,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return {\n        calculatedDuration: null,\n        next: (t) => {\n            /**\n             * We need to resolve the friction to figure out if we need a\n             * spring but we don't want to do this twice per frame. So here\n             * we flag if we updated for this frame and later if we did\n             * we can skip doing it again.\n             */\n            let hasUpdatedFrame = false;\n            if (!spring$1 && timeReachedBoundary === undefined) {\n                hasUpdatedFrame = true;\n                applyFriction(t);\n                checkCatchBoundary(t);\n            }\n            /**\n             * If we have a spring and the provided t is beyond the moment the friction\n             * animation crossed the min/max boundary, use the spring.\n             */\n            if (timeReachedBoundary !== undefined && t >= timeReachedBoundary) {\n                return spring$1.next(t - timeReachedBoundary);\n            }\n            else {\n                !hasUpdatedFrame && applyFriction(t);\n                return state;\n            }\n        },\n    };\n}\n\nexport { inertia };\n", "import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\", \"range-length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && output[0] === output[1])\n        return () => output[1];\n    const isZeroDeltaRange = input[0] === input[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        if (isZeroDeltaRange && v < input[0])\n            return output[0];\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n", "import { progress } from 'motion-utils';\nimport { mixNumber } from '../../../utils/mix/number.mjs';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mixNumber(min, 1, offsetProgress));\n    }\n}\n\nexport { fillOffset };\n", "import { fillOffset } from './fill.mjs';\n\nfunction defaultOffset(arr) {\n    const offset = [0];\n    fillOffset(offset, arr.length - 1);\n    return offset;\n}\n\nexport { defaultOffset };\n", "function convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\n\nexport { convertOffsetToTimes };\n", "import { easeInOut, isEasingArray, easingDefinitionToFunction } from 'motion-utils';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { defaultOffset } from '../keyframes/offsets/default.mjs';\nimport { convertOffsetToTimes } from '../keyframes/offsets/time.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction keyframes({ duration = 300, keyframes: keyframeValues, times, ease = \"easeInOut\", }) {\n    /**\n     * Easing functions can be externally defined as strings. Here we convert them\n     * into actual functions.\n     */\n    const easingFunctions = isEasingArray(ease)\n        ? ease.map(easingDefinitionToFunction)\n        : easingDefinitionToFunction(ease);\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = {\n        done: false,\n        value: keyframeValues[0],\n    };\n    /**\n     * Create a times array based on the provided 0-1 offsets\n     */\n    const absoluteTimes = convertOffsetToTimes(\n    // Only use the provided offsets if they're the correct length\n    // TODO Maybe we should warn here if there's a length mismatch\n    times && times.length === keyframeValues.length\n        ? times\n        : defaultOffset(keyframeValues), duration);\n    const mapTimeToKeyframe = interpolate(absoluteTimes, keyframeValues, {\n        ease: Array.isArray(easingFunctions)\n            ? easingFunctions\n            : defaultEasing(keyframeValues, easingFunctions),\n    });\n    return {\n        calculatedDuration: duration,\n        next: (t) => {\n            state.value = mapTimeToKeyframe(t);\n            state.done = t >= duration;\n            return state;\n        },\n    };\n}\n\nexport { defaultEasing, keyframes };\n", "const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe, speed = 1) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const useFirstKeyframe = speed < 0 || (repeat && repeatType !== \"loop\" && repeat % 2 === 1);\n    const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n", "import { inertia } from '../generators/inertia.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { spring } from '../generators/spring/index.mjs';\n\nconst transitionTypeMap = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\nfunction replaceTransitionType(transition) {\n    if (typeof transition.type === \"string\") {\n        transition.type = transitionTypeMap[transition.type];\n    }\n}\n\nexport { replaceTransitionType };\n", "class WithPromise {\n    constructor() {\n        this.updateFinished();\n    }\n    get finished() {\n        return this._finished;\n    }\n    updateFinished() {\n        this._finished = new Promise((resolve) => {\n            this.resolve = resolve;\n        });\n    }\n    notifyFinished() {\n        this.resolve();\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve, onReject);\n    }\n}\n\nexport { WithPromise };\n", "import { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\n\nconst percentToProgress = (percent) => percent / 100;\nclass JSAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.state = \"idle\";\n        this.startTime = null;\n        this.isStopped = false;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            const { motionValue } = this.options;\n            if (motionValue && motionValue.updatedAt !== time.now()) {\n                this.tick(time.now());\n            }\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            this.options.onStop?.();\n        };\n        activeAnimations.mainThread++;\n        this.options = options;\n        this.initAnimation();\n        this.play();\n        if (options.autoplay === false)\n            this.pause();\n    }\n    initAnimation() {\n        const { options } = this;\n        replaceTransitionType(options);\n        const { type = keyframes, repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = options;\n        let { keyframes: keyframes$1 } = options;\n        const generatorFactory = type || keyframes;\n        if (process.env.NODE_ENV !== \"production\" &&\n            generatorFactory !== keyframes) {\n            invariant(keyframes$1.length <= 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`, \"spring-two-frames\");\n        }\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            this.mirroredGenerator = generatorFactory({\n                ...options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        this.calculatedDuration = calculatedDuration;\n        this.resolvedDuration = calculatedDuration + repeatDelay;\n        this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n        this.generator = generator;\n    }\n    updateTime(timestamp) {\n        const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n        // Update currentTime\n        if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime = animationTime;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { generator, totalDuration, mixKeyframes, mirroredGenerator, resolvedDuration, calculatedDuration, } = this;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay = 0, keyframes, repeat, repeatType, repeatDelay, type, onUpdate, finalKeyframe, } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else {\n            this.updateTime(timestamp);\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.playbackSpeed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mixKeyframes) {\n            state.value = mixKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.playbackSpeed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        // TODO: The exception for inertia could be cleaner here\n        if (isAnimationFinished && type !== inertia) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    /**\n     * Allows the returned animation to be awaited or promise-chained. Currently\n     * resolves when the animation finishes at all but in a future update could/should\n     * reject if its cancels.\n     */\n    then(resolve, reject) {\n        return this.finished.then(resolve, reject);\n    }\n    get duration() {\n        return millisecondsToSeconds(this.calculatedDuration);\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.startTime === null ||\n            this.holdTime !== null ||\n            this.playbackSpeed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n        }\n        this.driver?.start(false);\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        this.updateTime(time.now());\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, startTime } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        this.options.onPlay?.();\n        const now = this.driver.now();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n            this.startTime = now;\n        }\n        else if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = startTime ?? now;\n        }\n        if (this.state === \"finished\" && this.speed < 0) {\n            this.startTime += this.calculatedDuration;\n        }\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        this.state = \"paused\";\n        this.updateTime(time.now());\n        this.holdTime = this.currentTime;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.notifyFinished();\n        this.teardown();\n        this.state = \"finished\";\n        this.options.onComplete?.();\n    }\n    cancel() {\n        this.holdTime = null;\n        this.startTime = 0;\n        this.tick(0);\n        this.teardown();\n        this.options.onCancel?.();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.startTime = this.holdTime = null;\n        activeAnimations.mainThread--;\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(sampleTime) {\n        this.startTime = 0;\n        return this.tick(sampleTime, true);\n    }\n    attachTimeline(timeline) {\n        if (this.options.allowFlatten) {\n            this.options.type = \"keyframes\";\n            this.options.ease = \"linear\";\n            this.initAnimation();\n        }\n        this.driver?.stop();\n        return timeline.observe(this);\n    }\n}\n// Legacy function support\nfunction animateValue(options) {\n    return new JSAnimation(options);\n}\n\nexport { JSAnimation, animateValue };\n", "function fillWildcards(keyframes) {\n    for (let i = 1; i < keyframes.length; i++) {\n        keyframes[i] ?? (keyframes[i] = keyframes[i - 1]);\n    }\n}\n\nexport { fillWildcards };\n", "const radToDeg = (rad) => (rad * 180) / Math.PI;\nconst rotate = (v) => {\n    const angle = radToDeg(Math.atan2(v[1], v[0]));\n    return rebaseAngle(angle);\n};\nconst matrix2dParsers = {\n    x: 4,\n    y: 5,\n    translateX: 4,\n    translateY: 5,\n    scaleX: 0,\n    scaleY: 3,\n    scale: (v) => (Math.abs(v[0]) + Math.abs(v[3])) / 2,\n    rotate,\n    rotateZ: rotate,\n    skewX: (v) => radToDeg(Math.atan(v[1])),\n    skewY: (v) => radToDeg(Math.atan(v[2])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[2])) / 2,\n};\nconst rebaseAngle = (angle) => {\n    angle = angle % 360;\n    if (angle < 0)\n        angle += 360;\n    return angle;\n};\nconst rotateZ = rotate;\nconst scaleX = (v) => Math.sqrt(v[0] * v[0] + v[1] * v[1]);\nconst scaleY = (v) => Math.sqrt(v[4] * v[4] + v[5] * v[5]);\nconst matrix3dParsers = {\n    x: 12,\n    y: 13,\n    z: 14,\n    translateX: 12,\n    translateY: 13,\n    translateZ: 14,\n    scaleX,\n    scaleY,\n    scale: (v) => (scaleX(v) + scaleY(v)) / 2,\n    rotateX: (v) => rebaseAngle(radToDeg(Math.atan2(v[6], v[5]))),\n    rotateY: (v) => rebaseAngle(radToDeg(Math.atan2(-v[2], v[0]))),\n    rotateZ,\n    rotate: rotateZ,\n    skewX: (v) => radToDeg(Math.atan(v[4])),\n    skewY: (v) => radToDeg(Math.atan(v[1])),\n    skew: (v) => (Math.abs(v[1]) + Math.abs(v[4])) / 2,\n};\nfunction defaultTransformValue(name) {\n    return name.includes(\"scale\") ? 1 : 0;\n}\nfunction parseValueFromTransform(transform, name) {\n    if (!transform || transform === \"none\") {\n        return defaultTransformValue(name);\n    }\n    const matrix3dMatch = transform.match(/^matrix3d\\(([-\\d.e\\s,]+)\\)$/u);\n    let parsers;\n    let match;\n    if (matrix3dMatch) {\n        parsers = matrix3dParsers;\n        match = matrix3dMatch;\n    }\n    else {\n        const matrix2dMatch = transform.match(/^matrix\\(([-\\d.e\\s,]+)\\)$/u);\n        parsers = matrix2dParsers;\n        match = matrix2dMatch;\n    }\n    if (!match) {\n        return defaultTransformValue(name);\n    }\n    const valueParser = parsers[name];\n    const values = match[1].split(\",\").map(convertTransformToNumber);\n    return typeof valueParser === \"function\"\n        ? valueParser(values)\n        : values[valueParser];\n}\nconst readTransformValue = (instance, name) => {\n    const { transform = \"none\" } = getComputedStyle(instance);\n    return parseValueFromTransform(transform, name);\n};\nfunction convertTransformToNumber(value) {\n    return parseFloat(value.trim());\n}\n\nexport { defaultTransformValue, parseValueFromTransform, readTransformValue };\n", "/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\n    \"transformPerspective\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"translateX\",\n    \"translateY\",\n    \"translateZ\",\n    \"scale\",\n    \"scaleX\",\n    \"scaleY\",\n    \"rotate\",\n    \"rotateX\",\n    \"rotateY\",\n    \"rotateZ\",\n    \"skew\",\n    \"skewX\",\n    \"skewY\",\n];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = /*@__PURE__*/ (() => new Set(transformPropOrder))();\n\nexport { transformPropOrder, transformProps };\n", "import { parseValueFromTransform } from '../../../render/dom/parse-transform.mjs';\nimport { transformPropOrder } from '../../../render/utils/keys-transform.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst isNumOrPxType = (v) => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: (_bbox, { transform }) => parseValueFromTransform(transform, \"x\"),\n    y: (_bbox, { transform }) => parseValueFromTransform(transform, \"y\"),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\n\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };\n", "import { fillWildcards } from './utils/fill-wildcards.mjs';\nimport { removeNonTranslationalTransform } from './utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nlet isForced = false;\nfunction measureAllKeyframes() {\n    if (anyNeedsMeasurement) {\n        const resolversToMeasure = Array.from(toResolve).filter((resolver) => resolver.needsMeasurement);\n        const elementsToMeasure = new Set(resolversToMeasure.map((resolver) => resolver.element));\n        const transformsToRestore = new Map();\n        /**\n         * Write pass\n         * If we're measuring elements we want to remove bounding box-changing transforms.\n         */\n        elementsToMeasure.forEach((element) => {\n            const removedTransforms = removeNonTranslationalTransform(element);\n            if (!removedTransforms.length)\n                return;\n            transformsToRestore.set(element, removedTransforms);\n            element.render();\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureInitialState());\n        // Write\n        elementsToMeasure.forEach((element) => {\n            element.render();\n            const restore = transformsToRestore.get(element);\n            if (restore) {\n                restore.forEach(([key, value]) => {\n                    element.getValue(key)?.set(value);\n                });\n            }\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureEndState());\n        // Write\n        resolversToMeasure.forEach((resolver) => {\n            if (resolver.suspendedScrollY !== undefined) {\n                window.scrollTo(0, resolver.suspendedScrollY);\n            }\n        });\n    }\n    anyNeedsMeasurement = false;\n    isScheduled = false;\n    toResolve.forEach((resolver) => resolver.complete(isForced));\n    toResolve.clear();\n}\nfunction readAllKeyframes() {\n    toResolve.forEach((resolver) => {\n        resolver.readKeyframes();\n        if (resolver.needsMeasurement) {\n            anyNeedsMeasurement = true;\n        }\n    });\n}\nfunction flushKeyframeResolvers() {\n    isForced = true;\n    readAllKeyframes();\n    measureAllKeyframes();\n    isForced = false;\n}\nclass KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n        this.state = \"pending\";\n        /**\n         * Track whether this resolver is async. If it is, it'll be added to the\n         * resolver queue and flushed in the next frame. Resolvers that aren't going\n         * to trigger read/write thrashing don't need to be async.\n         */\n        this.isAsync = false;\n        /**\n         * Track whether this resolver needs to perform a measurement\n         * to resolve its keyframes.\n         */\n        this.needsMeasurement = false;\n        this.unresolvedKeyframes = [...unresolvedKeyframes];\n        this.onComplete = onComplete;\n        this.name = name;\n        this.motionValue = motionValue;\n        this.element = element;\n        this.isAsync = isAsync;\n    }\n    scheduleResolve() {\n        this.state = \"scheduled\";\n        if (this.isAsync) {\n            toResolve.add(this);\n            if (!isScheduled) {\n                isScheduled = true;\n                frame.read(readAllKeyframes);\n                frame.resolveKeyframes(measureAllKeyframes);\n            }\n        }\n        else {\n            this.readKeyframes();\n            this.complete();\n        }\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, name, element, motionValue } = this;\n        // If initial keyframe is null we need to read it from the DOM\n        if (unresolvedKeyframes[0] === null) {\n            const currentValue = motionValue?.get();\n            // TODO: This doesn't work if the final keyframe is a wildcard\n            const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n            if (currentValue !== undefined) {\n                unresolvedKeyframes[0] = currentValue;\n            }\n            else if (element && name) {\n                const valueAsRead = element.readValue(name, finalKeyframe);\n                if (valueAsRead !== undefined && valueAsRead !== null) {\n                    unresolvedKeyframes[0] = valueAsRead;\n                }\n            }\n            if (unresolvedKeyframes[0] === undefined) {\n                unresolvedKeyframes[0] = finalKeyframe;\n            }\n            if (motionValue && currentValue === undefined) {\n                motionValue.set(unresolvedKeyframes[0]);\n            }\n        }\n        fillWildcards(unresolvedKeyframes);\n    }\n    setFinalKeyframe() { }\n    measureInitialState() { }\n    renderEndStyles() { }\n    measureEndState() { }\n    complete(isForcedComplete = false) {\n        this.state = \"complete\";\n        this.onComplete(this.unresolvedKeyframes, this.finalKeyframe, isForcedComplete);\n        toResolve.delete(this);\n    }\n    cancel() {\n        if (this.state === \"scheduled\") {\n            toResolve.delete(this);\n            this.state = \"pending\";\n        }\n    }\n    resume() {\n        if (this.state === \"pending\")\n            this.scheduleResolve();\n    }\n}\n\nexport { KeyframeResolver, flushKeyframeResolvers };\n", "const isCSSVar = (name) => name.startsWith(\"--\");\n\nexport { isCSSVar };\n", "import { isCSSVar } from './is-css-var.mjs';\n\nfunction setStyle(element, name, value) {\n    isCSSVar(name)\n        ? element.style.setProperty(name, value)\n        : (element.style[name] = value);\n}\n\nexport { setStyle };\n", "import { memo } from 'motion-utils';\n\nconst supportsScrollTimeline = /* @__PURE__ */ memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n", "/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\nexport { supportsFlags };\n", "import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = memo(callback);\n    return () => supportsFlags[supportsFlag] ?? memoized();\n}\n\nexport { memoSupports };\n", "import { memoSupports } from './memo.mjs';\n\nconst supportsLinearEasing = /*@__PURE__*/ memoSupports(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\nexport { supportsLinearEasing };\n", "const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n", "import { cubicBezierAsString } from './cubic-bezier.mjs';\n\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\n\nexport { supportedWaapiEasing };\n", "import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from '../utils/linear.mjs';\nimport { cubicBezierAsString } from './cubic-bezier.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\") {\n        return supportsLinearEasing()\n            ? generateLinearEasing(easing, duration)\n            : \"ease-out\";\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { mapEasingToNativeEasing };\n", "import { activeAnimations } from '../../stats/animation-count.mjs';\nimport { statsBuffer } from '../../stats/buffer.mjs';\nimport { mapEasingToNativeEasing } from './easing/map-easing.mjs';\n\nfunction startWaapiAnimation(element, valueName, keyframes, { delay = 0, duration = 300, repeat = 0, repeatType = \"loop\", ease = \"easeOut\", times, } = {}, pseudoElement = undefined) {\n    const keyframeOptions = {\n        [valueName]: keyframes,\n    };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease, duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    if (statsBuffer.value) {\n        activeAnimations.waapi++;\n    }\n    const options = {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    };\n    if (pseudoElement)\n        options.pseudoElement = pseudoElement;\n    const animation = element.animate(keyframeOptions, options);\n    if (statsBuffer.value) {\n        animation.finished.finally(() => {\n            activeAnimations.waapi--;\n        });\n    }\n    return animation;\n}\n\nexport { startWaapiAnimation };\n", "function isGenerator(type) {\n    return typeof type === \"function\" && \"applyToOptions\" in type;\n}\n\nexport { isGenerator };\n", "import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type) && supportsLinearEasing()) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n", "import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { setStyle } from '../render/dom/style-set.mjs';\nimport { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.finishedTime = null;\n        this.isStopped = false;\n        if (!options)\n            return;\n        const { element, name, keyframes, pseudoElement, allowFlatten = false, finalKeyframe, onComplete, } = options;\n        this.isPseudoElement = Boolean(pseudoElement);\n        this.allowFlatten = allowFlatten;\n        this.options = options;\n        invariant(typeof options.type !== \"string\", `Mini animate() doesn't support \"type\" as a string.`, \"mini-spring\");\n        const transition = applyGeneratorOptions(options);\n        this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.animation.onfinish = () => {\n            this.finishedTime = this.time;\n            if (!pseudoElement) {\n                const keyframe = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n                if (this.updateMotionValue) {\n                    this.updateMotionValue(keyframe);\n                }\n                else {\n                    /**\n                     * If we can, we want to commit the final style as set by the user,\n                     * rather than the computed keyframe value supplied by the animation.\n                     */\n                    setStyle(element, name, keyframe);\n                }\n                this.animation.cancel();\n            }\n            onComplete?.();\n            this.notifyFinished();\n        };\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        this.animation.play();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n        }\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish?.();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n    }\n    stop() {\n        if (this.isStopped)\n            return;\n        this.isStopped = true;\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        if (this.updateMotionValue) {\n            this.updateMotionValue();\n        }\n        else {\n            this.commitStyles();\n        }\n        if (!this.isPseudoElement)\n            this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        if (!this.isPseudoElement) {\n            this.animation.commitStyles?.();\n        }\n    }\n    get duration() {\n        const duration = this.animation.effect?.getComputedTiming?.().duration || 0;\n        return millisecondsToSeconds(Number(duration));\n    }\n    get time() {\n        return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.finishedTime = null;\n        this.animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        // Allow backwards playback after finishing\n        if (newSpeed < 0)\n            this.finishedTime = null;\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.finishedTime !== null\n            ? \"finished\"\n            : this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    set startTime(newStartTime) {\n        this.animation.startTime = newStartTime;\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline({ timeline, observe }) {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n        this.animation.onfinish = null;\n        if (timeline && supportsScrollTimeline()) {\n            this.animation.timeline = timeline;\n            return noop;\n        }\n        else {\n            return observe(this);\n        }\n    }\n}\n\nexport { NativeAnimation };\n", "import { anticipate, backInOut, circInOut } from 'motion-utils';\n\nconst unsupportedEasingFunctions = {\n    anticipate,\n    backInOut,\n    circInOut,\n};\nfunction isUnsupportedEase(key) {\n    return key in unsupportedEasingFunctions;\n}\nfunction replaceStringEasing(transition) {\n    if (typeof transition.ease === \"string\" &&\n        isUnsupportedEase(transition.ease)) {\n        transition.ease = unsupportedEasingFunctions[transition.ease];\n    }\n}\n\nexport { replaceStringEasing };\n", "import { secondsToMilliseconds } from 'motion-utils';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { NativeAnimation } from './NativeAnimation.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { replaceStringEasing } from './waapi/utils/unsupported-easing.mjs';\n\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\nclass NativeAnimationExtended extends NativeAnimation {\n    constructor(options) {\n        /**\n         * The base NativeAnimation function only supports a subset\n         * of Motion easings, and WAAPI also only supports some\n         * easing functions via string/cubic-bezier definitions.\n         *\n         * This function replaces those unsupported easing functions\n         * with a JS easing function. This will later get compiled\n         * to a linear() easing function.\n         */\n        replaceStringEasing(options);\n        /**\n         * Ensure we replace the transition type with a generator function\n         * before passing to WAAPI.\n         *\n         * TODO: Does this have a better home? It could be shared with\n         * JSAnimation.\n         */\n        replaceTransitionType(options);\n        super(options);\n        if (options.startTime) {\n            this.startTime = options.startTime;\n        }\n        this.options = options;\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * Rather than read commited styles back out of the DOM, we can\n     * create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to calculate velocity for any subsequent animation.\n     */\n    updateMotionValue(value) {\n        const { motionValue, onUpdate, onComplete, element, ...options } = this.options;\n        if (!motionValue)\n            return;\n        if (value !== undefined) {\n            motionValue.set(value);\n            return;\n        }\n        const sampleAnimation = new JSAnimation({\n            ...options,\n            autoplay: false,\n        });\n        const sampleTime = secondsToMilliseconds(this.finishedTime ?? this.time);\n        motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n        sampleAnimation.stop();\n    }\n}\n\nexport { NativeAnimationExtended };\n", "import { complex } from '../../value/types/complex/index.mjs';\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (value, name) => {\n    // If the list of keys that might be non-animatable grows, replace with Set\n    if (name === \"zIndex\")\n        return false;\n    // If it's a number or a keyframes array, we can animate it. We might at some point\n    // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n    // but for now lets leave it like this for performance reasons\n    if (typeof value === \"number\" || Array.isArray(value))\n        return true;\n    if (typeof value === \"string\" && // It's animatable if we have a string\n        (complex.test(value) || value === \"0\") && // And it contains numbers and/or colors\n        !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n    ) {\n        return true;\n    }\n    return false;\n};\n\nexport { isAnimatable };\n", "import { warning } from 'motion-utils';\nimport { isGenerator } from '../generators/utils/is-generator.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\n\nfunction hasKeyframesChanged(keyframes) {\n    const current = keyframes[0];\n    if (keyframes.length === 1)\n        return true;\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] !== current)\n            return true;\n    }\n}\nfunction canAnimate(keyframes, name, type, velocity) {\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    if (originKeyframe === null)\n        return false;\n    /**\n     * These aren't traditionally animatable but we do support them.\n     * In future we could look into making this more generic or replacing\n     * this function with mix() === mixImmediate\n     */\n    if (name === \"display\" || name === \"visibility\")\n        return true;\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(originKeyframe, name);\n    const isTargetAnimatable = isAnimatable(targetKeyframe, name);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${name} from \"${originKeyframe}\" to \"${targetKeyframe}\". \"${isOriginAnimatable ? targetKeyframe : originKeyframe}\" is not an animatable value.`, \"value-not-animatable\");\n    // Always skip if any of these are true\n    if (!isOriginAnimatable || !isTargetAnimatable) {\n        return false;\n    }\n    return (hasKeyframesChanged(keyframes) ||\n        ((type === \"spring\" || isGenerator(type)) && velocity));\n}\n\nexport { canAnimate };\n", "function makeAnimationInstant(options) {\n    options.duration = 0;\n    options.type === \"keyframes\";\n}\n\nexport { makeAnimationInstant };\n", "import { memo } from 'motion-utils';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Could be re-enabled now we have support for linear() easing\n    // \"background-color\"\n]);\nconst supportsWaapi = /*@__PURE__*/ memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\nfunction supportsBrowserAnimation(options) {\n    const { motionValue, name, repeatDelay, repeatType, damping, type } = options;\n    const subject = motionValue?.owner?.current;\n    /**\n     * We use this check instead of isHTMLElement() because we explicitly\n     * **don't** want elements in different timing contexts (i.e. popups)\n     * to be accelerated, as it's not possible to sync these animations\n     * properly with those driven from the main window frameloop.\n     */\n    if (!(subject instanceof HTMLElement)) {\n        return false;\n    }\n    const { onUpdate, transformTemplate } = motionValue.owner.getProps();\n    return (supportsWaapi() &&\n        name &&\n        acceleratedValues.has(name) &&\n        (name !== \"transform\" || !transformTemplate) &&\n        /**\n         * If we're outputting values to onUpdate then we can't use WAAPI as there's\n         * no way to read the value from WAAPI every frame.\n         */\n        !onUpdate &&\n        !repeatDelay &&\n        repeatType !== \"mirror\" &&\n        damping !== 0 &&\n        type !== \"inertia\");\n}\n\nexport { supportsBrowserAnimation };\n", "import { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { makeAnimationInstant } from './utils/make-animation-instant.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n    constructor({ autoplay = true, delay = 0, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", keyframes, name, motionValue, element, ...options }) {\n        super();\n        /**\n         * Bound to support return animation.stop pattern\n         */\n        this.stop = () => {\n            if (this._animation) {\n                this._animation.stop();\n                this.stopTimeline?.();\n            }\n            this.keyframeResolver?.cancel();\n        };\n        this.createdAt = time.now();\n        const optionsWithDefaults = {\n            autoplay,\n            delay,\n            type,\n            repeat,\n            repeatDelay,\n            repeatType,\n            name,\n            motionValue,\n            element,\n            ...options,\n        };\n        const KeyframeResolver$1 = element?.KeyframeResolver || KeyframeResolver;\n        this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n        this.keyframeResolver?.scheduleResolve();\n    }\n    onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n        this.keyframeResolver = undefined;\n        const { name, type, velocity, delay, isHandoff, onUpdate } = options;\n        this.resolvedAt = time.now();\n        /**\n         * If we can't animate this value with the resolved keyframes\n         * then we should complete it immediately.\n         */\n        if (!canAnimate(keyframes, name, type, velocity)) {\n            if (MotionGlobalConfig.instantAnimations || !delay) {\n                onUpdate?.(getFinalKeyframe(keyframes, options, finalKeyframe));\n            }\n            keyframes[0] = keyframes[keyframes.length - 1];\n            makeAnimationInstant(options);\n            options.repeat = 0;\n        }\n        /**\n         * Resolve startTime for the animation.\n         *\n         * This method uses the createdAt and resolvedAt to calculate the\n         * animation startTime. *Ideally*, we would use the createdAt time as t=0\n         * as the following frame would then be the first frame of the animation in\n         * progress, which would feel snappier.\n         *\n         * However, if there's a delay (main thread work) between the creation of\n         * the animation and the first commited frame, we prefer to use resolvedAt\n         * to avoid a sudden jump into the animation.\n         */\n        const startTime = sync\n            ? !this.resolvedAt\n                ? this.createdAt\n                : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY\n                    ? this.resolvedAt\n                    : this.createdAt\n            : undefined;\n        const resolvedOptions = {\n            startTime,\n            finalKeyframe,\n            ...options,\n            keyframes,\n        };\n        /**\n         * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions)\n            ? new NativeAnimationExtended({\n                ...resolvedOptions,\n                element: resolvedOptions.motionValue.owner.current,\n            })\n            : new JSAnimation(resolvedOptions);\n        animation.finished.then(() => this.notifyFinished()).catch(noop);\n        if (this.pendingTimeline) {\n            this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n            this.pendingTimeline = undefined;\n        }\n        this._animation = animation;\n    }\n    get finished() {\n        if (!this._animation) {\n            return this._finished;\n        }\n        else {\n            return this.animation.finished;\n        }\n    }\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n    get animation() {\n        if (!this._animation) {\n            this.keyframeResolver?.resume();\n            flushKeyframeResolvers();\n        }\n        return this._animation;\n    }\n    get duration() {\n        return this.animation.duration;\n    }\n    get time() {\n        return this.animation.time;\n    }\n    set time(newTime) {\n        this.animation.time = newTime;\n    }\n    get speed() {\n        return this.animation.speed;\n    }\n    get state() {\n        return this.animation.state;\n    }\n    set speed(newSpeed) {\n        this.animation.speed = newSpeed;\n    }\n    get startTime() {\n        return this.animation.startTime;\n    }\n    attachTimeline(timeline) {\n        if (this._animation) {\n            this.stopTimeline = this.animation.attachTimeline(timeline);\n        }\n        else {\n            this.pendingTimeline = timeline;\n        }\n        return () => this.stop();\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.complete();\n    }\n    cancel() {\n        if (this._animation) {\n            this.animation.cancel();\n        }\n        this.keyframeResolver?.cancel();\n    }\n}\n\nexport { AsyncMotionValueAnimation };\n", "class GroupAnimation {\n    constructor(animations) {\n        // Bound to accomadate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline) {\n        const subscriptions = this.animations.map((animation) => animation.attachTimeline(timeline));\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get state() {\n        return this.getAll(\"state\");\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\nexport { GroupAnimation };\n", "import { GroupAnimation } from './GroupAnimation.mjs';\n\nclass GroupAnimationWithThen extends GroupAnimation {\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n}\n\nexport { GroupAnimationWithThen };\n", "import { NativeAnimation } from './NativeAnimation.mjs';\n\nclass NativeAnimationWrapper extends NativeAnimation {\n    constructor(animation) {\n        super();\n        this.animation = animation;\n        animation.onfinish = () => {\n            this.finishedTime = this.time;\n            this.notifyFinished();\n        };\n    }\n}\n\nexport { NativeAnimationWrapper };\n", "const animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement = \"\") => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n\nexport { animationMapKey, getAnimationMap };\n", "import { invariant, isNumericalString } from 'motion-utils';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON><PERSON>'s special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = \n// eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive, as it can match a lot of words\n/^var\\(--(?:([\\w-]+)|([\\w-]+), ?([a-zA-Z\\d ()%#.,-]+))\\)/u;\nfunction parseCSSVariable(current) {\n    const match = splitCSSVariableRegex.exec(current);\n    if (!match)\n        return [,];\n    const [, token1, token2, fallback] = match;\n    return [`--${token1 ?? token2}`, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n    invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`, \"max-css-var-depth\");\n    const [token, fallback] = parseCSSVariable(current);\n    // No CSS variable detected\n    if (!token)\n        return;\n    // Attempt to read this CSS variable off the element\n    const resolved = window.getComputedStyle(element).getPropertyValue(token);\n    if (resolved) {\n        const trimmed = resolved.trim();\n        return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n    }\n    return isCSSVariableToken(fallback)\n        ? getVariableValue(fallback, element, depth + 1)\n        : fallback;\n}\n\nexport { getVariableValue, parseCSSVariable };\n", "function getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\nexport { getValueTransition };\n", "import { transformPropOrder } from './keys-transform.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    ...transformPropOrder,\n]);\n\nexport { positionalKeys };\n", "/**\n * ValueType for \"auto\"\n */\nconst auto = {\n    test: (v) => v === \"auto\",\n    parse: (v) => v,\n};\n\nexport { auto };\n", "/**\n * Tests a provided value against a ValueType\n */\nconst testValueType = (v) => (type) => type.test(v);\n\nexport { testValueType };\n", "import { auto } from './auto.mjs';\nimport { number } from './numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from './numbers/units.mjs';\nimport { testValueType } from './test.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = (v) => dimensionValueTypes.find(testValueType(v));\n\nexport { dimensionValueTypes, findDimensionValueType };\n", "import { isZeroValueString } from 'motion-utils';\n\nfunction isNone(value) {\n    if (typeof value === \"number\") {\n        return value === 0;\n    }\n    else if (value !== null) {\n        return value === \"none\" || value === \"0\" || isZeroValueString(value);\n    }\n    else {\n        return true;\n    }\n}\n\nexport { isNone };\n", "import { complex } from './index.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\n\n/**\n * Properties that should default to 1 or 100%\n */\nconst maxDefaults = new Set([\"brightness\", \"contrast\", \"saturate\", \"opacity\"]);\nfunction applyDefaultFilter(v) {\n    const [name, value] = v.slice(0, -1).split(\"(\");\n    if (name === \"drop-shadow\")\n        return v;\n    const [number] = value.match(floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, \"\");\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + \"(\" + defaultValue + unit + \")\";\n}\nconst functionRegex = /\\b([a-z-]*)\\(.*?\\)/gu;\nconst filter = {\n    ...complex,\n    getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(\" \") : v;\n    },\n};\n\nexport { filter };\n", "import { number } from './numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n", "import { scale, alpha } from '../numbers/index.mjs';\nimport { degrees, px, progressPercentage } from '../numbers/units.mjs';\n\nconst transformValueTypes = {\n    rotate: degrees,\n    rotateX: degrees,\n    rotateY: degrees,\n    rotateZ: degrees,\n    scale,\n    scaleX: scale,\n    scaleY: scale,\n    scaleZ: scale,\n    skew: degrees,\n    skewX: degrees,\n    skewY: degrees,\n    distance: px,\n    translateX: px,\n    translateY: px,\n    translateZ: px,\n    x: px,\n    y: px,\n    z: px,\n    perspective: px,\n    transformPerspective: px,\n    opacity: alpha,\n    originX: progressPercentage,\n    originY: progressPercentage,\n    originZ: px,\n};\n\nexport { transformValueTypes };\n", "import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    ...transformValueTypes,\n    zIndex: int,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n", "import { color } from '../color/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n    ...numberValueTypes,\n    // Color props\n    color,\n    backgroundColor: color,\n    outlineColor: color,\n    fill: color,\n    stroke: color,\n    // Border props\n    borderColor: color,\n    borderTopColor: color,\n    borderRightColor: color,\n    borderBottomColor: color,\n    borderLeftColor: color,\n    filter,\n    WebkitFilter: filter,\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = (key) => defaultValueTypes[key];\n\nexport { defaultValueTypes, getDefaultValueType };\n", "import { complex } from '../complex/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { getDefaultValueType } from '../maps/defaults.mjs';\n\nfunction getAnimatableNone(key, value) {\n    let defaultValueType = getDefaultValueType(key);\n    if (defaultValueType !== filter)\n        defaultValueType = complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return defaultValueType.getAnimatableNone\n        ? defaultValueType.getAnimatableNone(value)\n        : undefined;\n}\n\nexport { getAnimatableNone };\n", "import { analyseComplexValue } from '../../../value/types/complex/index.mjs';\nimport { getAnimatableNone } from '../../../value/types/utils/animatable-none.mjs';\n\n/**\n * If we encounter keyframes like \"none\" or \"0\" and we also have keyframes like\n * \"#fff\" or \"200px 200px\" we want to find a keyframe to serve as a template for\n * the \"none\" keyframes. In this case \"#fff\" or \"200px 200px\" - then these get turned into\n * zero equivalents, i.e. \"#fff0\" or \"0px 0px\".\n */\nconst invalidTemplates = new Set([\"auto\", \"none\", \"0\"]);\nfunction makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name) {\n    let i = 0;\n    let animatableTemplate = undefined;\n    while (i < unresolvedKeyframes.length && !animatableTemplate) {\n        const keyframe = unresolvedKeyframes[i];\n        if (typeof keyframe === \"string\" &&\n            !invalidTemplates.has(keyframe) &&\n            analyseComplexValue(keyframe).values.length) {\n            animatableTemplate = unresolvedKeyframes[i];\n        }\n        i++;\n    }\n    if (animatableTemplate && name) {\n        for (const noneIndex of noneKeyframeIndexes) {\n            unresolvedKeyframes[noneIndex] = getAnimatableNone(name, animatableTemplate);\n        }\n    }\n}\n\nexport { makeNoneKeyframesAnimatable };\n", "import { positionalKeys } from '../../render/utils/keys-position.mjs';\nimport { findDimensionValueType } from '../../value/types/dimensions.mjs';\nimport { getVariableValue } from '../utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from '../utils/is-css-variable.mjs';\nimport { KeyframeResolver } from './KeyframesResolver.mjs';\nimport { isNone } from './utils/is-none.mjs';\nimport { makeNoneKeyframesAnimatable } from './utils/make-none-animatable.mjs';\nimport { isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\n\nclass DOMKeyframesResolver extends KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element) {\n        super(unresolvedKeyframes, onComplete, name, motionValue, element, true);\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, element, name } = this;\n        if (!element || !element.current)\n            return;\n        super.readKeyframes();\n        /**\n         * If any keyframe is a CSS variable, we need to find its value by sampling the element\n         */\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            let keyframe = unresolvedKeyframes[i];\n            if (typeof keyframe === \"string\") {\n                keyframe = keyframe.trim();\n                if (isCSSVariableToken(keyframe)) {\n                    const resolved = getVariableValue(keyframe, element.current);\n                    if (resolved !== undefined) {\n                        unresolvedKeyframes[i] = resolved;\n                    }\n                    if (i === unresolvedKeyframes.length - 1) {\n                        this.finalKeyframe = keyframe;\n                    }\n                }\n            }\n        }\n        /**\n         * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n         * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n         * have a far bigger performance impact.\n         */\n        this.resolveNoneKeyframes();\n        /**\n         * Check to see if unit type has changed. If so schedule jobs that will\n         * temporarily set styles to the destination keyframes.\n         * Skip if we have more than two keyframes or this isn't a positional value.\n         * TODO: We can throw if there are multiple keyframes and the value type changes.\n         */\n        if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n            return;\n        }\n        const [origin, target] = unresolvedKeyframes;\n        const originType = findDimensionValueType(origin);\n        const targetType = findDimensionValueType(target);\n        /**\n         * Either we don't recognise these value types or we can animate between them.\n         */\n        if (originType === targetType)\n            return;\n        /**\n         * If both values are numbers or pixels, we can animate between them by\n         * converting them to numbers.\n         */\n        if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n            for (let i = 0; i < unresolvedKeyframes.length; i++) {\n                const value = unresolvedKeyframes[i];\n                if (typeof value === \"string\") {\n                    unresolvedKeyframes[i] = parseFloat(value);\n                }\n            }\n        }\n        else if (positionalValues[name]) {\n            /**\n             * Else, the only way to resolve this is by measuring the element.\n             */\n            this.needsMeasurement = true;\n        }\n    }\n    resolveNoneKeyframes() {\n        const { unresolvedKeyframes, name } = this;\n        const noneKeyframeIndexes = [];\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            if (unresolvedKeyframes[i] === null ||\n                isNone(unresolvedKeyframes[i])) {\n                noneKeyframeIndexes.push(i);\n            }\n        }\n        if (noneKeyframeIndexes.length) {\n            makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n        }\n    }\n    measureInitialState() {\n        const { element, unresolvedKeyframes, name } = this;\n        if (!element || !element.current)\n            return;\n        if (name === \"height\") {\n            this.suspendedScrollY = window.pageYOffset;\n        }\n        this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        unresolvedKeyframes[0] = this.measuredOrigin;\n        // Set final key frame to measure after next render\n        const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n        if (measureKeyframe !== undefined) {\n            element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n        }\n    }\n    measureEndState() {\n        const { element, name, unresolvedKeyframes } = this;\n        if (!element || !element.current)\n            return;\n        const value = element.getValue(name);\n        value && value.jump(this.measuredOrigin, false);\n        const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n        const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n        unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n            this.finalKeyframe = finalKeyframe;\n        }\n        // If we removed transform values, reapply them before the next render\n        if (this.removedTransforms?.length) {\n            this.removedTransforms.forEach(([unsetTransformName, unsetTransformValue]) => {\n                element\n                    .getValue(unsetTransformName)\n                    .set(unsetTransformValue);\n            });\n        }\n        this.resolveNoneKeyframes();\n    }\n}\n\nexport { DOMKeyframesResolver };\n", "const pxValues = new Set([\n    // Border props\n    \"borderWidth\",\n    \"borderTopWidth\",\n    \"borderRightWidth\",\n    \"borderBottomWidth\",\n    \"borderLeftWidth\",\n    \"borderRadius\",\n    \"radius\",\n    \"borderTopLeftRadius\",\n    \"borderTopRightRadius\",\n    \"borderBottomRightRadius\",\n    \"borderBottomLeftRadius\",\n    // Positioning props\n    \"width\",\n    \"maxWidth\",\n    \"height\",\n    \"maxHeight\",\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\",\n    // Spacing props\n    \"padding\",\n    \"paddingTop\",\n    \"paddingRight\",\n    \"paddingBottom\",\n    \"paddingLeft\",\n    \"margin\",\n    \"marginTop\",\n    \"marginRight\",\n    \"marginBottom\",\n    \"marginLeft\",\n    // Misc\n    \"backgroundPositionX\",\n    \"backgroundPositionY\",\n]);\n\nexport { pxValues };\n", "import { pxValues } from '../../waapi/utils/px-values.mjs';\n\nfunction applyPxDefaults(keyframes, name) {\n    for (let i = 0; i < keyframes.length; i++) {\n        if (typeof keyframes[i] === \"number\" && pxValues.has(name)) {\n            keyframes[i] = keyframes[i] + \"px\";\n        }\n    }\n}\n\nexport { applyPxDefaults };\n", "import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && supportsLinearEasing()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in supportedWaapiEasing || supportsLinearEasing())) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\n\nexport { isWaapiSupportedEasing };\n", "import { memo } from 'motion-utils';\n\nconst supportsPartialKeyframes = /*@__PURE__*/ memo(() => {\n    try {\n        document.createElement(\"div\").animate({ opacity: [1] });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n});\n\nexport { supportsPartialKeyframes };\n", "/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Can be accelerated but currently disabled until https://issues.chromium.org/issues/41491098 is resolved\n    // or until we implement support for linear() easing.\n    // \"background-color\"\n]);\n\nexport { acceleratedValues };\n", "function camelToDash(str) {\n    return str.replace(/([A-Z])/g, (match) => `-${match.toLowerCase()}`);\n}\n\nexport { camelToDash };\n", "function resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n", "import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction createSelectorEffect(subjectEffect) {\n    return (subject, values) => {\n        const elements = resolveElements(subject);\n        const subscriptions = [];\n        for (const element of elements) {\n            const remove = subjectEffect(element, values);\n            subscriptions.push(remove);\n        }\n        return () => {\n            for (const remove of subscriptions)\n                remove();\n        };\n    };\n}\n\nexport { createSelectorEffect };\n", "/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n    return type && typeof value === \"number\"\n        ? type.transform(value)\n        : value;\n};\n\nexport { getValueAsType };\n", "import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\n\nclass MotionValueState {\n    constructor() {\n        this.latest = {};\n        this.values = new Map();\n    }\n    set(name, value, render, computed, useDefaultValueType = true) {\n        const existingValue = this.values.get(name);\n        if (existingValue) {\n            existingValue.onRemove();\n        }\n        const onChange = () => {\n            const v = value.get();\n            if (useDefaultValueType) {\n                this.latest[name] = getValueAsType(v, numberValueTypes[name]);\n            }\n            else {\n                this.latest[name] = v;\n            }\n            render && frame.render(render);\n        };\n        onChange();\n        const cancelOnChange = value.on(\"change\", onChange);\n        computed && value.addDependent(computed);\n        const remove = () => {\n            cancelOnChange();\n            render && cancelFrame(render);\n            this.values.delete(name);\n            computed && value.removeDependent(computed);\n        };\n        this.values.set(name, { value, onRemove: remove });\n        return remove;\n    }\n    get(name) {\n        return this.values.get(name)?.value;\n    }\n    destroy() {\n        for (const value of this.values.values()) {\n            value.onRemove();\n        }\n    }\n}\n\nexport { MotionValueState };\n", "import { MotionValueState } from '../MotionValueState.mjs';\n\nfunction createEffect(addValue) {\n    const stateCache = new WeakMap();\n    const subscriptions = [];\n    return (subject, values) => {\n        const state = stateCache.get(subject) ?? new MotionValueState();\n        stateCache.set(subject, state);\n        for (const key in values) {\n            const value = values[key];\n            const remove = addValue(subject, state, key, value);\n            subscriptions.push(remove);\n        }\n        return () => {\n            for (const cancel of subscriptions)\n                cancel();\n        };\n    };\n}\n\nexport { createEffect };\n", "import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\n\nfunction canSetAsProperty(element, name) {\n    if (!(name in element))\n        return false;\n    const descriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(element), name) ||\n        Object.getOwnPropertyDescriptor(element, name);\n    // Check if it has a setter\n    return descriptor && typeof descriptor.set === \"function\";\n}\nconst addAttrValue = (element, state, key, value) => {\n    const isProp = canSetAsProperty(element, key);\n    const name = isProp\n        ? key\n        : key.startsWith(\"data\") || key.startsWith(\"aria\")\n            ? camelToDash(key)\n            : key;\n    /**\n     * Set attribute directly via property if available\n     */\n    const render = isProp\n        ? () => {\n            element[name] = state.latest[key];\n        }\n        : () => {\n            const v = state.latest[key];\n            if (v === null || v === undefined) {\n                element.removeAttribute(name);\n            }\n            else {\n                element.setAttribute(name, String(v));\n            }\n        };\n    return state.set(key, value, render);\n};\nconst attrEffect = /*@__PURE__*/ createSelectorEffect(\n/*@__PURE__*/ createEffect(addAttrValue));\n\nexport { addAttrValue, attrEffect };\n", "import { createEffect } from '../utils/create-effect.mjs';\n\nconst propEffect = /*@__PURE__*/ createEffect((subject, state, key, value) => {\n    return state.set(key, value, () => {\n        subject[key] = state.latest[key];\n    }, undefined, false);\n});\n\nexport { propEffect };\n", "import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an HTML element in a way\n * that works across iframes\n */\nfunction isHTMLElement(element) {\n    return isObject(element) && \"offsetHeight\" in element;\n}\n\nexport { isHTMLElement };\n", "import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev) {\n                this.events.change?.notify(this.current);\n                if (this.dependents) {\n                    for (const dependent of this.dependents) {\n                        dependent.dirty();\n                    }\n                }\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v) {\n        if (!this.passiveEffect) {\n            this.updateAndNotify(v);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    dirty() {\n        this.events.change?.notify(this.current);\n    }\n    addDependent(dependent) {\n        if (!this.dependents) {\n            this.dependents = new Set();\n        }\n        this.dependents.add(dependent);\n    }\n    removeDependent(dependent) {\n        if (this.dependents) {\n            this.dependents.delete(dependent);\n        }\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.dependents?.clear();\n        this.events.destroy?.notify();\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n", "import { transformPropOrder } from '../../render/utils/keys-transform.mjs';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nfunction buildTransform(state) {\n    let transform = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < transformPropOrder.length; i++) {\n        const key = transformPropOrder[i];\n        const value = state.latest[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault) {\n            transformIsDefault = false;\n            const transformName = translateAlias[key] || key;\n            const valueToRender = state.latest[key];\n            transform += `${transformName}(${valueToRender}) `;\n        }\n    }\n    return transformIsDefault ? \"none\" : transform.trim();\n}\n\nexport { buildTransform };\n", "import { isCSSVar } from '../../render/dom/is-css-var.mjs';\nimport { transformProps } from '../../render/utils/keys-transform.mjs';\nimport { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { MotionValue } from '../../value/index.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nimport { buildTransform } from './transform.mjs';\n\nconst originProps = new Set([\"originX\", \"originY\", \"originZ\"]);\nconst addStyleValue = (element, state, key, value) => {\n    let render = undefined;\n    let computed = undefined;\n    if (transformProps.has(key)) {\n        if (!state.get(\"transform\")) {\n            // If this is an HTML element, we need to set the transform-box to fill-box\n            // to normalise the transform relative to the element's bounding box\n            if (!isHTMLElement(element) && !state.get(\"transformBox\")) {\n                addStyleValue(element, state, \"transformBox\", new MotionValue(\"fill-box\"));\n            }\n            state.set(\"transform\", new MotionValue(\"none\"), () => {\n                element.style.transform = buildTransform(state);\n            });\n        }\n        computed = state.get(\"transform\");\n    }\n    else if (originProps.has(key)) {\n        if (!state.get(\"transformOrigin\")) {\n            state.set(\"transformOrigin\", new MotionValue(\"\"), () => {\n                const originX = state.latest.originX ?? \"50%\";\n                const originY = state.latest.originY ?? \"50%\";\n                const originZ = state.latest.originZ ?? 0;\n                element.style.transformOrigin = `${originX} ${originY} ${originZ}`;\n            });\n        }\n        computed = state.get(\"transformOrigin\");\n    }\n    else if (isCSSVar(key)) {\n        render = () => {\n            element.style.setProperty(key, state.latest[key]);\n        };\n    }\n    else {\n        render = () => {\n            element.style[key] = state.latest[key];\n        };\n    }\n    return state.set(key, value, render, computed);\n};\nconst styleEffect = /*@__PURE__*/ createSelectorEffect(\n/*@__PURE__*/ createEffect(addStyleValue));\n\nexport { addStyleValue, styleEffect };\n", "import { MotionValue } from '../../value/index.mjs';\nimport { px } from '../../value/types/numbers/units.mjs';\nimport { addAttrValue } from '../attr/index.mjs';\nimport { addStyleValue } from '../style/index.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toPx = px.transform;\nfunction addSVGPathValue(element, state, key, value) {\n    frame.render(() => element.setAttribute(\"pathLength\", \"1\"));\n    if (key === \"pathOffset\") {\n        return state.set(key, value, () => element.setAttribute(\"stroke-dashoffset\", toPx(-state.latest[key])));\n    }\n    else {\n        if (!state.get(\"stroke-dasharray\")) {\n            state.set(\"stroke-dasharray\", new MotionValue(\"1 1\"), () => {\n                const { pathLength = 1, pathSpacing } = state.latest;\n                element.setAttribute(\"stroke-dasharray\", `${toPx(pathLength)} ${toPx(pathSpacing ?? 1 - Number(pathLength))}`);\n            });\n        }\n        return state.set(key, value, undefined, state.get(\"stroke-dasharray\"));\n    }\n}\nconst addSVGValue = (element, state, key, value) => {\n    if (key.startsWith(\"path\")) {\n        return addSVGPathValue(element, state, key, value);\n    }\n    else if (key.startsWith(\"attr\")) {\n        return addAttrValue(element, state, convertAttrKey(key), value);\n    }\n    const handler = key in element.style ? addStyleValue : addAttrValue;\n    return handler(element, state, key, value);\n};\nconst svgEffect = /*@__PURE__*/ createSelectorEffect(\n/*@__PURE__*/ createEffect(addSVGValue));\nfunction convertAttrKey(key) {\n    return key.replace(/^attr([A-Z])/, (_, firstChar) => firstChar.toLowerCase());\n}\n\nexport { svgEffect };\n", "import { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ createRenderBatcher(queueMicrotask, false);\n\nexport { cancelMicrotask, microtask };\n", "const isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\nexport { isDragActive, isDragging };\n", "import { isDragging } from './is-active.mjs';\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (isDragging[axis]) {\n            return null;\n        }\n        else {\n            isDragging[axis] = true;\n            return () => {\n                isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (isDragging.x || isDragging.y) {\n            return null;\n        }\n        else {\n            isDragging.x = isDragging.y = true;\n            return () => {\n                isDragging.x = isDragging.y = false;\n            };\n        }\n    }\n}\n\nexport { setDragLock };\n", "import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = resolveElements(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\nexport { setupGesture };\n", "import { isDragActive } from './drag/state/is-active.mjs';\nimport { setupGesture } from './utils/setup.mjs';\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || isDragActive());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = setupGesture(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\nexport { hover };\n", "/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\nexport { isNodeOrChild };\n", "const isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\nexport { isPrimaryPointer };\n", "const focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\nexport { isElementKeyboardAccessible };\n", "const isPressing = new WeakSet();\n\nexport { isPressing };\n", "import { isPressing } from './state.mjs';\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\nexport { enableKeyboardPress };\n", "import { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent))\n            return;\n        isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (isPressing.has(target)) {\n                isPressing.delete(target);\n            }\n            if (!isValidPressEvent(endEvent)) {\n                return;\n            }\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                isNodeOrChild(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (isHTMLElement(target)) {\n            target.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions));\n            if (!isElementKeyboardAccessible(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\nexport { press };\n", "import { isCSSVar } from './is-css-var.mjs';\n\nfunction getComputedStyle(element, name) {\n    const computedStyle = window.getComputedStyle(element);\n    return isCSSVar(name)\n        ? computedStyle.getPropertyValue(name)\n        : computedStyle[name];\n}\n\nexport { getComputedStyle };\n", "import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an SVG element in a way\n * that works across iframes\n */\nfunction isSVGElement(element) {\n    return isObject(element) && \"ownerSVGElement\" in element;\n}\n\nexport { isSVGElement };\n", "import { isSVGElement } from '../utils/is-svg-element.mjs';\nimport { resolveElements } from '../utils/resolve-elements.mjs';\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n    if (borderBoxSize && borderBoxSize[0]) {\n        return borderBoxSize[0][(borderBoxAxis + \"Size\")];\n    }\n    else if (isSVGElement(target) && \"getBBox\" in target) {\n        return target.getBBox()[svgAxis];\n    }\n    else {\n        return target[htmlAxis];\n    }\n};\nconst getWidth = /*@__PURE__*/ getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/ getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget({ target, borderBoxSize }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler(target, {\n            get width() {\n                return getWidth(target, borderBoxSize);\n            },\n            get height() {\n                return getHeight(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = resolveElements(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\nexport { resizeElement };\n", "const windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const info = {\n            get width() {\n                return window.innerWidth;\n            },\n            get height() {\n                return window.innerHeight;\n            },\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size &&\n            typeof windowResizeHandler === \"function\") {\n            window.removeEventListener(\"resize\", windowResizeHandler);\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\nexport { resizeWindow };\n", "import { resizeElement } from './handle-element.mjs';\nimport { resizeWindow } from './handle-window.mjs';\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? resizeWindow(a) : resizeElement(a, b);\n}\n\nexport { resize };\n", "import { frame, cancelFrame } from '../frameloop/frame.mjs';\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    frame.preUpdate(onFrame, true);\n    return () => cancelFrame(onFrame);\n}\n\nexport { observeTimeline };\n", "import { activeAnimations } from './animation-count.mjs';\nimport { statsBuffer } from './buffer.mjs';\nimport { frame, cancelFrame, frameData } from '../frameloop/frame.mjs';\n\nfunction record() {\n    const { value } = statsBuffer;\n    if (value === null) {\n        cancelFrame(record);\n        return;\n    }\n    value.frameloop.rate.push(frameData.delta);\n    value.animations.mainThread.push(activeAnimations.mainThread);\n    value.animations.waapi.push(activeAnimations.waapi);\n    value.animations.layout.push(activeAnimations.layout);\n}\nfunction mean(values) {\n    return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n    if (values.length === 0) {\n        return {\n            min: 0,\n            max: 0,\n            avg: 0,\n        };\n    }\n    return {\n        min: Math.min(...values),\n        max: Math.max(...values),\n        avg: calcAverage(values),\n    };\n}\nconst msToFps = (ms) => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n    statsBuffer.value = null;\n    statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n    const { value } = statsBuffer;\n    if (!value) {\n        throw new Error(\"Stats are not being measured\");\n    }\n    clearStatsBuffer();\n    cancelFrame(record);\n    const summary = {\n        frameloop: {\n            setup: summarise(value.frameloop.setup),\n            rate: summarise(value.frameloop.rate),\n            read: summarise(value.frameloop.read),\n            resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n            preUpdate: summarise(value.frameloop.preUpdate),\n            update: summarise(value.frameloop.update),\n            preRender: summarise(value.frameloop.preRender),\n            render: summarise(value.frameloop.render),\n            postRender: summarise(value.frameloop.postRender),\n        },\n        animations: {\n            mainThread: summarise(value.animations.mainThread),\n            waapi: summarise(value.animations.waapi),\n            layout: summarise(value.animations.layout),\n        },\n        layoutProjection: {\n            nodes: summarise(value.layoutProjection.nodes),\n            calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n            calculatedProjections: summarise(value.layoutProjection.calculatedProjections),\n        },\n    };\n    /**\n     * Convert the rate to FPS\n     */\n    const { rate } = summary.frameloop;\n    rate.min = msToFps(rate.min);\n    rate.max = msToFps(rate.max);\n    rate.avg = msToFps(rate.avg);\n    [rate.min, rate.max] = [rate.max, rate.min];\n    return summary;\n}\nfunction recordStats() {\n    if (statsBuffer.value) {\n        clearStatsBuffer();\n        throw new Error(\"Stats are already being measured\");\n    }\n    const newStatsBuffer = statsBuffer;\n    newStatsBuffer.value = {\n        frameloop: {\n            setup: [],\n            rate: [],\n            read: [],\n            resolveKeyframes: [],\n            preUpdate: [],\n            update: [],\n            preRender: [],\n            render: [],\n            postRender: [],\n        },\n        animations: {\n            mainThread: [],\n            waapi: [],\n            layout: [],\n        },\n        layoutProjection: {\n            nodes: [],\n            calculatedTargetDeltas: [],\n            calculatedProjections: [],\n        },\n    };\n    newStatsBuffer.addProjectionMetrics = (metrics) => {\n        const { layoutProjection } = newStatsBuffer.value;\n        layoutProjection.nodes.push(metrics.nodes);\n        layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n        layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n    };\n    frame.postRender(record, true);\n    return reportStats;\n}\n\nexport { recordStats };\n", "import { isSVGElement } from './is-svg-element.mjs';\n\n/**\n * Checks if an element is specifically an SVGSVGElement (the root SVG element)\n * in a way that works across iframes\n */\nfunction isSVGSVGElement(element) {\n    return isSVGElement(element) && element.tagName === \"svg\";\n}\n\nexport { isSVGSVGElement };\n", "import { easingDefinitionToFunction } from 'motion-utils';\n\nfunction getOriginIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction stagger(duration = 0.1, { startDelay = 0, from = 0, ease } = {}) {\n    return (i, total) => {\n        const fromIndex = typeof from === \"number\" ? from : getOriginIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (ease) {\n            const maxDelay = total * duration;\n            const easingFunction = easingDefinitionToFunction(ease);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return startDelay + delay;\n    };\n}\n\nexport { getOriginIndex, stagger };\n", "import { interpolate } from './interpolate.mjs';\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = interpolate(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\nexport { transform };\n", "import { cancelFrame, frame } from '../frameloop/frame.mjs';\n\nfunction subscribeValue(inputValues, outputValue, getLatest) {\n    const update = () => outputValue.set(getLatest());\n    const scheduleUpdate = () => frame.preRender(update, false, true);\n    const subscriptions = inputValues.map((v) => v.on(\"change\", scheduleUpdate));\n    outputValue.on(\"destroy\", () => {\n        subscriptions.forEach((unsubscribe) => unsubscribe());\n        cancelFrame(update);\n    });\n}\n\nexport { subscribeValue };\n", "import { collectMotionValues, motionValue } from './index.mjs';\nimport { subscribeValue } from './subscribe-value.mjs';\n\n/**\n * Create a `MotionValue` that transforms the output of other `MotionValue`s by\n * passing their latest values through a transform function.\n *\n * Whenever a `MotionValue` referred to in the provided function is updated,\n * it will be re-evaluated.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction transformValue(transform) {\n    const collectedValues = [];\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * inside transform will be saved into this array.\n     */\n    collectMotionValues.current = collectedValues;\n    const initialValue = transform();\n    collectMotionValues.current = undefined;\n    const value = motionValue(initialValue);\n    subscribeValue(collectedValues, value, transform);\n    return value;\n}\n\nexport { transformValue };\n", "import { transform } from '../utils/transform.mjs';\nimport { transformValue } from './transform-value.mjs';\n\n/**\n * Create a `MotionValue` that maps the output of another `MotionValue` by\n * mapping it from one range of values into another.\n *\n * @remarks\n *\n * Given an input range of `[-200, -100, 100, 200]` and an output range of\n * `[0, 1, 1, 0]`, the returned `MotionValue` will:\n *\n * - When provided a value between `-200` and `-100`, will return a value between `0` and  `1`.\n * - When provided a value between `-100` and `100`, will return `1`.\n * - When provided a value between `100` and `200`, will return a value between `1` and  `0`\n *\n * The input range must be a linear series of numbers. The output range\n * can be any value type supported by Motion: numbers, colors, shadows, etc.\n *\n * Every value in the output range must be of the same type and in the same format.\n *\n * ```jsx\n * const x = motionValue(0)\n * const xRange = [-200, -100, 100, 200]\n * const opacityRange = [0, 1, 1, 0]\n * const opacity = mapValue(x, xRange, opacityRange)\n * ```\n *\n * @param inputValue - `MotionValue`\n * @param inputRange - A linear series of numbers (either all increasing or decreasing)\n * @param outputRange - A series of numbers, colors or strings. Must be the same length as `inputRange`.\n * @param options -\n *\n *  - clamp: boolean. Clamp values to within the given range. Defaults to `true`\n *  - ease: EasingFunction[]. Easing functions to use on the interpolations between each value in the input and output ranges. If provided as an array, the array must be one item shorter than the input and output ranges, as the easings apply to the transition between each.\n *\n * @returns `MotionValue`\n *\n * @public\n */\nfunction mapValue(inputValue, inputRange, outputRange, options) {\n    const map = transform(inputRange, outputRange, options);\n    return transformValue(() => map(inputValue.get()));\n}\n\nexport { mapValue };\n", "const isMotionValue = (value) => Boolean(value && value.getVelocity);\n\nexport { isMotionValue };\n", "import { motionValue } from './index.mjs';\nimport { JSAnimation } from '../animation/JSAnimation.mjs';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Create a `MotionValue` that animates to its latest value using a spring.\n * Can either be a value or track another `MotionValue`.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction springValue(source, options) {\n    const initialValue = isMotionValue(source) ? source.get() : source;\n    const value = motionValue(initialValue);\n    attachSpring(value, source, options);\n    return value;\n}\nfunction attachSpring(value, source, options) {\n    const initialValue = value.get();\n    let activeAnimation = null;\n    let latestValue = initialValue;\n    let latestSetter;\n    const unit = typeof initialValue === \"string\"\n        ? initialValue.replace(/[\\d.-]/g, \"\")\n        : undefined;\n    const stopAnimation = () => {\n        if (activeAnimation) {\n            activeAnimation.stop();\n            activeAnimation = null;\n        }\n    };\n    const startAnimation = () => {\n        stopAnimation();\n        activeAnimation = new JSAnimation({\n            keyframes: [asNumber(value.get()), asNumber(latestValue)],\n            velocity: value.getVelocity(),\n            type: \"spring\",\n            restDelta: 0.001,\n            restSpeed: 0.01,\n            ...options,\n            onUpdate: latestSetter,\n        });\n    };\n    value.attach((v, set) => {\n        latestValue = v;\n        latestSetter = (latest) => set(parseValue(latest, unit));\n        frame.postRender(startAnimation);\n        return value.get();\n    }, stopAnimation);\n    if (isMotionValue(source)) {\n        const removeSourceOnChange = source.on(\"change\", (v) => value.set(parseValue(v, unit)));\n        const removeValueOnDestroy = value.on(\"destroy\", removeSourceOnChange);\n        return () => {\n            removeSourceOnChange();\n            removeValueOnDestroy();\n        };\n    }\n    return stopAnimation;\n}\nfunction parseValue(v, unit) {\n    return unit ? v + unit : v;\n}\nfunction asNumber(v) {\n    return typeof v === \"number\" ? v : parseFloat(v);\n}\n\nexport { attachSpring, springValue };\n", "import { color } from '../color/index.mjs';\nimport { complex } from '../complex/index.mjs';\nimport { dimensionValueTypes } from '../dimensions.mjs';\nimport { testValueType } from '../test.mjs';\n\n/**\n * A list of all ValueTypes\n */\nconst valueTypes = [...dimensionValueTypes, color, complex];\n/**\n * Tests a value against the list of ValueTypes\n */\nconst findValueType = (v) => valueTypes.find(testValueType(v));\n\nexport { findValueType };\n", "function chooseLayerType(valueName) {\n    if (valueName === \"layout\")\n        return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\")\n        return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\")\n        return \"old\";\n    return \"group\";\n}\n\nexport { chooseLayerType };\n", "let pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values) => {\n        pendingRules[selector] = values;\n    },\n    commit: () => {\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for (const selector in pendingRules) {\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)) {\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: () => {\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    },\n};\n\nexport { css };\n", "function getViewAnimationLayerInfo(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\nexport { getViewAnimationLayerInfo };\n", "function filterViewAnimations(animation) {\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        effect.pseudoElement?.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\nexport { getViewAnimations };\n", "function hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\nexport { hasTarget };\n", "import { secondsToMilliseconds } from 'motion-utils';\nimport { GroupAnimation } from '../animation/GroupAnimation.mjs';\nimport { NativeAnimation } from '../animation/NativeAnimation.mjs';\nimport { NativeAnimationWrapper } from '../animation/NativeAnimationWrapper.mjs';\nimport { getValueTransition } from '../animation/utils/get-value-transition.mjs';\nimport { mapEasingToNativeEasing } from '../animation/waapi/easing/map-easing.mjs';\nimport { applyGeneratorOptions } from '../animation/waapi/utils/apply-generator.mjs';\nimport { chooseLayerType } from './utils/choose-layer-type.mjs';\nimport { css } from './utils/css.mjs';\nimport { getViewAnimationLayerInfo } from './utils/get-layer-info.mjs';\nimport { getViewAnimations } from './utils/get-view-animations.mjs';\nimport { hasTarget } from './utils/has-target.mjs';\n\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(builder) {\n    const { update, targets, options: defaultOptions } = builder;\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve) => {\n            await update();\n            resolve(new GroupAnimation([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */\n    if (!hasTarget(\"root\", targets)) {\n        css.set(\":root\", {\n            \"view-transition-name\": \"none\",\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */\n    css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", { \"animation-timing-function\": \"linear !important\" });\n    css.commit(); // Write\n    const transition = document.startViewTransition(async () => {\n        await update();\n        // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(() => {\n        css.remove(); // Write\n    });\n    return new Promise((resolve) => {\n        transition.ready.then(() => {\n            const generatedViewAnimations = getViewAnimations();\n            const animations = [];\n            /**\n             * Create animations for each of our explicitly-defined subjects.\n             */\n            targets.forEach((definition, target) => {\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames) {\n                    if (!definition[key])\n                        continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n                        if (!valueKeyframes)\n                            continue;\n                        const valueOptions = {\n                            ...getValueTransition(defaultOptions, valueName),\n                            ...getValueTransition(options, valueName),\n                        };\n                        const type = chooseLayerType(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */\n                        if (valueName === \"opacity\" &&\n                            !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [initialValue, valueKeyframes];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */\n                        if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n                        valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n                        const animation = new NativeAnimation({\n                            ...valueOptions,\n                            element: document.documentElement,\n                            name: valueName,\n                            pseudoElement: `::view-transition-${type}(${target})`,\n                            keyframes: valueKeyframes,\n                        });\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */\n            for (const animation of generatedViewAnimations) {\n                if (animation.playState === \"finished\")\n                    continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect))\n                    continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement)\n                    continue;\n                const name = getViewAnimationLayerInfo(pseudoElement);\n                if (!name)\n                    continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */\n                    const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    let animationTransition = {\n                        ...getValueTransition(defaultOptions, transitionName),\n                    };\n                    animationTransition.duration && (animationTransition.duration = secondsToMilliseconds(animationTransition.duration));\n                    animationTransition =\n                        applyGeneratorOptions(animationTransition);\n                    const easing = mapEasingToNativeEasing(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: secondsToMilliseconds(animationTransition.delay ?? 0),\n                        duration: animationTransition.duration,\n                        easing,\n                    });\n                    animations.push(new NativeAnimationWrapper(animation));\n                }\n                else if (hasOpacity(targetDefinition, \"enter\") &&\n                    hasOpacity(targetDefinition, \"exit\") &&\n                    effect\n                        .getKeyframes()\n                        .some((keyframe) => keyframe.mixBlendMode)) {\n                    animations.push(new NativeAnimationWrapper(animation));\n                }\n                else {\n                    animation.cancel();\n                }\n            }\n            resolve(new GroupAnimation(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    return target?.[key]?.keyframes.opacity;\n}\n\nexport { startViewAnimation };\n", "import { removeItem } from 'motion-utils';\nimport { microtask } from '../frameloop/microtask.mjs';\nimport { startViewAnimation } from './start.mjs';\n\nlet builders = [];\nlet current = null;\nfunction next() {\n    current = null;\n    const [nextBuilder] = builders;\n    if (nextBuilder)\n        start(nextBuilder);\n}\nfunction start(builder) {\n    removeItem(builders, builder);\n    current = builder;\n    startViewAnimation(builder).then((animation) => {\n        builder.notifyReady(animation);\n        animation.finished.finally(next);\n    });\n}\nfunction processQueue() {\n    /**\n     * Iterate backwards over the builders array. We can ignore the\n     * \"wait\" animations. If we have an interrupting animation in the\n     * queue then we need to batch all preceeding animations into it.\n     * Currently this only batches the update functions but will also\n     * need to batch the targets.\n     */\n    for (let i = builders.length - 1; i >= 0; i--) {\n        const builder = builders[i];\n        const { interrupt } = builder.options;\n        if (interrupt === \"immediate\") {\n            const batchedUpdates = builders.slice(0, i + 1).map((b) => b.update);\n            const remaining = builders.slice(i + 1);\n            builder.update = () => {\n                batchedUpdates.forEach((update) => update());\n            };\n            // Put the current builder at the front, followed by any \"wait\" builders\n            builders = [builder, ...remaining];\n            break;\n        }\n    }\n    if (!current || builders[0]?.options.interrupt === \"immediate\") {\n        next();\n    }\n}\nfunction addToQueue(builder) {\n    builders.push(builder);\n    microtask.render(processQueue);\n}\n\nexport { addToQueue };\n", "import { noop } from 'motion-utils';\nimport { addToQueue } from './queue.mjs';\n\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentSubject = \"root\";\n        this.targets = new Map();\n        this.notifyReady = noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        this.update = update;\n        this.options = {\n            interrupt: \"wait\",\n            ...options,\n        };\n        addToQueue(this);\n    }\n    get(subject) {\n        this.currentSubject = subject;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentSubject, targets } = this;\n        if (!targets.has(currentSubject)) {\n            targets.set(currentSubject, {});\n        }\n        const targetData = targets.get(currentSubject);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction animateView(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\nexport { ViewTransitionBuilder, animateView };\n", "import { stepsOrder } from './order.mjs';\nimport { frame, cancelFrame } from './frame.mjs';\n\n/**\n * @deprecated\n *\n * Import as `frame` instead.\n */\nconst sync = frame;\n/**\n * @deprecated\n *\n * Use cancelFrame(callback) instead.\n */\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = (process) => cancelFrame(process);\n    return acc;\n}, {});\n\nexport { cancelSync, sync };\n", "function isDOMKeyframes(keyframes) {\n    return typeof keyframes === \"object\" && !Array.isArray(keyframes);\n}\n\nexport { isDOMKeyframes };\n", "import { resolveElements } from 'motion-dom';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\n\nfunction resolveSubjects(subject, keyframes, scope, selectorCache) {\n    if (typeof subject === \"string\" && isDOMKeyframes(keyframes)) {\n        return resolveElements(subject, scope, selectorCache);\n    }\n    else if (subject instanceof NodeList) {\n        return Array.from(subject);\n    }\n    else if (Array.isArray(subject)) {\n        return subject;\n    }\n    else {\n        return [subject];\n    }\n}\n\nexport { resolveSubjects };\n", "function calculateRepeatDuration(duration, repeat, _repeatDelay) {\n    return duration * (repeat + 1);\n}\n\nexport { calculateRepeatDuration };\n", "/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n    if (typeof next === \"number\") {\n        return next;\n    }\n    else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n        return Math.max(0, current + parseFloat(next));\n    }\n    else if (next === \"<\") {\n        return prev;\n    }\n    else if (next.startsWith(\"<\")) {\n        return Math.max(0, prev + parseFloat(next.slice(1)));\n    }\n    else {\n        return labels.get(next) ?? current;\n    }\n}\n\nexport { calcNextTime };\n", "import { mixNumber } from 'motion-dom';\nimport { getEasingForSegment, removeItem } from 'motion-utils';\n\nfunction eraseKeyframes(sequence, startTime, endTime) {\n    for (let i = 0; i < sequence.length; i++) {\n        const keyframe = sequence[i];\n        if (keyframe.at > startTime && keyframe.at < endTime) {\n            removeItem(sequence, keyframe);\n            // If we remove this item we have to push the pointer back one\n            i--;\n        }\n    }\n}\nfunction addKeyframes(sequence, keyframes, easing, offset, startTime, endTime) {\n    /**\n     * Erase every existing value between currentTime and targetTime,\n     * this will essentially splice this timeline into any currently\n     * defined ones.\n     */\n    eraseKeyframes(sequence, startTime, endTime);\n    for (let i = 0; i < keyframes.length; i++) {\n        sequence.push({\n            value: keyframes[i],\n            at: mixNumber(startTime, endTime, offset[i]),\n            easing: getEasingForSegment(easing, i),\n        });\n    }\n}\n\nexport { addKeyframes, eraseKeyframes };\n", "/**\n * Take an array of times that represent repeated keyframes. For instance\n * if we have original times of [0, 0.5, 1] then our repeated times will\n * be [0, 0.5, 1, 1, 1.5, 2]. Loop over the times and scale them back\n * down to a 0-1 scale.\n */\nfunction normalizeTimes(times, repeat) {\n    for (let i = 0; i < times.length; i++) {\n        times[i] = times[i] / (repeat + 1);\n    }\n}\n\nexport { normalizeTimes };\n", "function compareByTime(a, b) {\n    if (a.at === b.at) {\n        if (a.value === null)\n            return 1;\n        if (b.value === null)\n            return -1;\n        return 0;\n    }\n    else {\n        return a.at - b.at;\n    }\n}\n\nexport { compareByTime };\n", "import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope, generators) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", repeat, repeatType, repeatDelay = 0, ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numSubjects)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            const createGenerator = isGenerator(type)\n                ? type\n                : generators?.[type || \"keyframes\"];\n            if (numKeyframes <= 2 && createGenerator) {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration ?? (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Handle repeat options\n             */\n            if (repeat) {\n                invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\", \"repeat-count-high\");\n                duration = calculateRepeatDuration(duration, repeat);\n                const originalKeyframes = [...valueKeyframesAsList];\n                const originalTimes = [...times];\n                ease = Array.isArray(ease) ? [...ease] : [ease];\n                const originalEase = [...ease];\n                for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n                    valueKeyframesAsList.push(...originalKeyframes);\n                    for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n                        times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n                        ease.push(keyframeIndex === 0\n                            ? \"linear\"\n                            : getEasingForSegment(originalEase, keyframeIndex - 1));\n                    }\n                }\n                normalizeTimes(times, repeat);\n            }\n            const targetTime = startTime + duration;\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n            const numSubjects = subjects.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const thisSubject = subjects[subjectIndex];\n                const subjectSequence = getSubjectSequence(thisSubject, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition && transition[key]\n        ? {\n            ...transition,\n            ...transition[key],\n        }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n", "const visualElementStore = new WeakMap();\n\nexport { visualElementStore };\n", "const isKeyframesTarget = (v) => {\n    return Array.isArray(v);\n};\n\nexport { isKeyframesTarget };\n", "function getValueState(visualElement) {\n    const state = [{}, {}];\n    visualElement?.values.forEach((value, key) => {\n        state[0][key] = value.get();\n        state[1][key] = value.getVelocity();\n    });\n    return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n", "import { resolveVariantFromProps } from './resolve-variants.mjs';\n\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\n\nexport { resolveVariant };\n", "import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\nexport { setTarget };\n", "import { isMotionValue } from 'motion-dom';\n\nfunction isWillChangeMotionValue(value) {\n    return Boolean(isMotionValue(value) && value.add);\n}\n\nexport { isWillChangeMotionValue };\n", "import { MotionGlobalConfig } from 'motion-utils';\nimport { isWillChangeMotionValue } from './is.mjs';\n\nfunction addValueToWillChange(visualElement, key) {\n    const willChange = visualElement.getValue(\"willChange\");\n    /**\n     * It could be that a user has set will<PERSON>hange to a regular MotionValue,\n     * in which case we can't add the value to it.\n     */\n    if (isWillChangeMotionValue(willChange)) {\n        return willChange.add(key);\n    }\n    else if (!willChange && MotionGlobalConfig.WillChange) {\n        const newWillChange = new MotionGlobalConfig.WillChange(\"auto\");\n        visualElement.addValue(\"willChange\", newWillChange);\n        newWillChange.add(key);\n    }\n}\n\nexport { addValueToWillChange };\n", "/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/gu, \"$1-$2\").toLowerCase();\n\nexport { camelToDash };\n", "import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\n\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + camelToDash(optimizedAppearDataId);\n\nexport { optimizedAppearDataAttribute, optimizedAppearDataId };\n", "import { optimizedAppearDataAttribute } from './data-id.mjs';\n\nfunction getOptimisedAppearId(visualElement) {\n    return visualElement.props[optimizedAppearDataAttribute];\n}\n\nexport { getOptimisedAppearId };\n", "const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n", "import { transformProps } from 'motion-dom';\n\nconst underDampedSpring = {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n};\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n    type: \"keyframes\",\n    ease: [0.25, 0.1, 0.35, 1],\n    duration: 0.3,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else if (transformProps.has(valueKey)) {\n        return valueKey.startsWith(\"scale\")\n            ? criticallyDampedSpring(keyframes[1])\n            : underDampedSpring;\n    }\n    return ease;\n};\n\nexport { getDefaultTransition };\n", "/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\n\nexport { isTransitionDefined };\n", "import { getValueTransition, makeAnimationInstant, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        makeAnimationInstant(options);\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (MotionGlobalConfig.instantAnimations ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        makeAnimationInstant(options);\n        options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    return valueTransition.isSync\n        ? new JSAnimation(options)\n        : new AsyncMotionValueAnimation(options);\n};\n\nexport { animateMotionValue };\n", "import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = targetAndTransition;\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n        const valueTarget = target[key];\n        if (valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If the value is already at the defined target, skip the animation.\n         */\n        const currentValue = value.get();\n        if (currentValue !== undefined &&\n            !value.isAnimating &&\n            !Array.isArray(valueTarget) &&\n            valueTarget === currentValue &&\n            !valueTransition.velocity) {\n            continue;\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        let isHandoff = false;\n        if (window.MotionHandoffAnimation) {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (appearId) {\n                const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n                if (startTime !== null) {\n                    valueTransition.startTime = startTime;\n                    isHandoff = true;\n                }\n            }\n        }\n        addValueToWillChange(visualElement, key);\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key)\n            ? { type: false }\n            : valueTransition, visualElement, isHandoff));\n        const animation = value.animation;\n        if (animation) {\n            animations.push(animation);\n        }\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            frame.update(() => {\n                transitionEnd && setTarget(visualElement, transitionEnd);\n            });\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n", "/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\nexport { convertBoundingBoxToBox, convertBoxToBoundingBox, transformBoxPoints };\n", "import { convertBoundingBoxToBox, transformBoxPoints } from '../geometry/conversion.mjs';\nimport { translateAxis } from '../geometry/delta-apply.mjs';\n\nfunction measureViewportBox(instance, transformPoint) {\n    return convertBoundingBoxToBox(transformBoxPoints(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n    const viewportBox = measureViewportBox(element, transformPagePoint);\n    const { scroll } = rootProjectionNode;\n    if (scroll) {\n        translateAxis(viewportBox.x, scroll.offset.x);\n        translateAxis(viewportBox.y, scroll.offset.y);\n    }\n    return viewportBox;\n}\n\nexport { measurePageBox, measureViewportBox };\n", "const featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\nexport { featureDefinitions };\n", "const createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\nexport { createAxis, createAxisDelta, createBox, createDelta };\n", "const isBrowser = typeof window !== \"undefined\";\n\nexport { isBrowser };\n", "// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = { current: null };\nconst hasReducedMotionListener = { current: false };\n\nexport { hasReducedMotionListener, prefersReducedMotion };\n", "import { isBrowser } from '../is-browser.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\nfunction initPrefersReducedMotion() {\n    hasReducedMotionListener.current = true;\n    if (!isBrowser)\n        return;\n    if (window.matchMedia) {\n        const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n        const setReducedMotionPreferences = () => (prefersReducedMotion.current = motionMediaQuery.matches);\n        motionMediaQuery.addEventListener(\"change\", setReducedMotionPreferences);\n        setReducedMotionPreferences();\n    }\n    else {\n        prefersReducedMotion.current = false;\n    }\n}\n\nexport { initPrefersReducedMotion };\n", "function isAnimationControls(v) {\n    return (v !== null &&\n        typeof v === \"object\" &&\n        typeof v.start === \"function\");\n}\n\nexport { isAnimationControls };\n", "/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\nexport { isVariantLabel };\n", "const variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\nexport { variantPriorityOrder, variantProps };\n", "import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nfunction isControllingVariants(props) {\n    return (isAnimationControls(props.animate) ||\n        variantProps.some((name) => isVariantLabel(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\nexport { isControllingVariants, isVariantNode };\n", "import { isMotionValue, motionValue } from 'motion-dom';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                if (existingValue.liveStyle === true) {\n                    existingValue.jump(nextValue);\n                }\n                else if (!existingValue.hasAnimated) {\n                    existingValue.set(nextValue);\n                }\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n", "import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone, microtask } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key]);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\", \"reduced-motion-disabled\");\n        }\n        this.parent?.addChild(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent?.removeChild(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    addChild(child) {\n        this.children.add(child);\n        this.enteringChildren ?? (this.enteringChildren = new Set());\n        this.enteringChildren.add(child);\n    }\n    removeChild(child) {\n        this.children.delete(child);\n        this.enteringChildren && this.enteringChildren.delete(child);\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n            this.scheduleRender();\n        });\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in featureDefinitions) {\n            const featureDefinition = featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n    scheduleRenderMicrotask() {\n        microtask.render(this.render);\n    }\n}\n\nexport { VisualElement };\n", "import { DOMKeyframesResolver, isMotionValue } from 'motion-dom';\nimport { VisualElement } from '../VisualElement.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if (isMotionValue(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current) {\n                    this.current.textContent = `${latest}`;\n                }\n            });\n        }\n    }\n}\n\nexport { DOMVisualElement };\n", "import { transformPropOrder, getValueAsType, numberValueTypes } from 'motion-dom';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = transformPropOrder[i];\n        const value = latestValues[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault || transformTemplate) {\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (!valueIsDefault) {\n                transformIsDefault = false;\n                const transformName = translateAlias[key] || key;\n                transformString += `${transformName}(${valueAsType}) `;\n            }\n            if (transformTemplate) {\n                transform[key] = valueAsType;\n            }\n        }\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n", "import { transformProps, isCSSVariableName, getValueAsType, numberValueTypes } from 'motion-dom';\nimport { buildTransform } from './build-transform.mjs';\n\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n    const { style, vars, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept separately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            continue;\n        }\n        else if (isCSSVariableName(key)) {\n            vars[key] = value;\n            continue;\n        }\n        else {\n            // Convert the value to its default value type, ie 0 -> \"0px\"\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (key.startsWith(\"origin\")) {\n                // If this is a transform origin, flag and enable further transform-origin processing\n                hasTransformOrigin = true;\n                transformOrigin[key] =\n                    valueAsType;\n            }\n            else {\n                style[key] = valueAsType;\n            }\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(latestValues, state.transform, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n", "function renderHTML(element, { style, vars }, styleProp, projection) {\n    const elementStyle = element.style;\n    let key;\n    for (key in style) {\n        // CSSStyleDeclaration has [index: number]: string; in the types, so we use that as key type.\n        elementStyle[key] = style[key];\n    }\n    // Write projection styles directly to element style\n    projection?.applyProjectionStyles(elementStyle, styleProp);\n    for (key in vars) {\n        // Loop over any CSS variables and assign those.\n        // They can only be assigned using `setProperty`.\n        elementStyle.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n", "import { isCSSVariableName } from 'motion-dom';\n\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    for (const key in correctors) {\n        scaleCorrectors[key] = correctors[key];\n        if (isCSSVariableName(key)) {\n            scaleCorrectors[key].isCSSVariable = true;\n        }\n    }\n}\n\nexport { addScaleCorrector, scaleCorrectors };\n", "import { transformProps } from 'motion-dom';\nimport { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!scaleCorrectors[key] || key === \"opacity\")));\n}\n\nexport { isForcedMotionValue };\n", "import { isMotionValue } from 'motion-dom';\nimport { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style &&\n                isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props) ||\n            visualElement?.getValue(key)?.liveStyle !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n", "import { transformProps, defaultTransformValue, readTransformValue, isCSSVariableName } from 'motion-dom';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            return this.projection?.isProjecting\n                ? defaultTransformValue(key)\n                : readTransformValue(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n", "import { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\n\nfunction isObjectKey(key, object) {\n    return key in object;\n}\nclass ObjectVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"object\";\n    }\n    readValueFromInstance(instance, key) {\n        if (isObjectKey(key, instance)) {\n            const value = instance[key];\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                return value;\n            }\n        }\n        return undefined;\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    removeValueFromRenderState(key, renderState) {\n        delete renderState.output[key];\n    }\n    measureInstanceViewportBox() {\n        return createBox();\n    }\n    build(renderState, latestValues) {\n        Object.assign(renderState.output, latestValues);\n    }\n    renderInstance(instance, { output }) {\n        Object.assign(instance, output);\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n}\n\nexport { ObjectVisualElement };\n", "import { px } from 'motion-dom';\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = px.transform(-offset);\n    // Build the dash array\n    const pathLength = px.transform(length);\n    const pathSpacing = px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\nexport { buildSVGPath };\n", "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n", "/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\nexport { camelCaseAttributes };\n", "const isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\nexport { isSVGTag };\n", "import { camelToDash } from '../../dom/utils/camel-to-dash.mjs';\nimport { renderHTML } from '../../html/utils/render.mjs';\nimport { camelCaseAttributes } from './camel-case-attrs.mjs';\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    renderHTML(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!camelCaseAttributes.has(key) ? camelToDash(key) : key, renderState.attrs[key]);\n    }\n}\n\nexport { renderSVG };\n", "import { isMotionValue, transformPropOrder } from 'motion-dom';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const newValues = scrapeMotionValuesFromProps$1(props, prevProps, visualElement);\n    for (const key in props) {\n        if (isMotionValue(props[key]) ||\n            isMotionValue(prevProps[key])) {\n            const targetKey = transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n", "import { transformProps, getDefaultValueType } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = createBox;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n    build(renderState, latestValues, props) {\n        buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n", "import { isSVGElement, isSVGSVGElement } from 'motion-dom';\nimport { HTMLVisualElement } from '../../render/html/HTMLVisualElement.mjs';\nimport { ObjectVisualElement } from '../../render/object/ObjectVisualElement.mjs';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { SVGVisualElement } from '../../render/svg/SVGVisualElement.mjs';\n\nfunction createDOMVisualElement(element) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                transform: {},\n                transformOrigin: {},\n                style: {},\n                vars: {},\n                attrs: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = isSVGElement(element) && !isSVGSVGElement(element)\n        ? new SVGVisualElement(options)\n        : new HTMLVisualElement(options);\n    node.mount(element);\n    visualElementStore.set(element, node);\n}\nfunction createObjectVisualElement(subject) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                output: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = new ObjectVisualElement(options);\n    node.mount(subject);\n    visualElementStore.set(subject, node);\n}\n\nexport { createDOMVisualElement, createObjectVisualElement };\n", "import { isMotionValue, motionValue } from 'motion-dom';\nimport { animateMotionValue } from '../interfaces/motion-value.mjs';\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n    motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\nexport { animateSingleValue };\n", "import { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { animateTarget } from '../interfaces/visual-element-target.mjs';\nimport { createDOMVisualElement, createObjectVisualElement } from '../utils/create-visual-element.mjs';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\nimport { resolveSubjects } from './resolve-subjects.mjs';\nimport { animateSingleValue } from './single-value.mjs';\n\nfunction isSingleValue(subject, keyframes) {\n    return (isMotionValue(subject) ||\n        typeof subject === \"number\" ||\n        (typeof subject === \"string\" && !isDOMKeyframes(keyframes)));\n}\n/**\n * Implementation\n */\nfunction animateSubject(subject, keyframes, options, scope) {\n    const animations = [];\n    if (isSingleValue(subject, keyframes)) {\n        animations.push(animateSingleValue(subject, isDOMKeyframes(keyframes)\n            ? keyframes.default || keyframes\n            : keyframes, options ? options.default || options : options));\n    }\n    else {\n        const subjects = resolveSubjects(subject, keyframes, scope);\n        const numSubjects = subjects.length;\n        invariant(Boolean(numSubjects), \"No valid elements provided.\", \"no-valid-elements\");\n        for (let i = 0; i < numSubjects; i++) {\n            const thisSubject = subjects[i];\n            invariant(thisSubject !== null, \"You're trying to perform an animation on null. Ensure that selectors are correctly finding elements and refs are correctly hydrated.\", \"animate-null\");\n            const createVisualElement = thisSubject instanceof Element\n                ? createDOMVisualElement\n                : createObjectVisualElement;\n            if (!visualElementStore.has(thisSubject)) {\n                createVisualElement(thisSubject);\n            }\n            const visualElement = visualElementStore.get(thisSubject);\n            const transition = { ...options };\n            /**\n             * Resolve stagger function if provided.\n             */\n            if (\"delay\" in transition &&\n                typeof transition.delay === \"function\") {\n                transition.delay = transition.delay(i, numSubjects);\n            }\n            animations.push(...animateTarget(visualElement, { ...keyframes, transition }, {}));\n        }\n    }\n    return animations;\n}\n\nexport { animateSubject };\n", "import { spring } from 'motion-dom';\nimport { createAnimationsFromSequence } from '../sequence/create.mjs';\nimport { animateSubject } from './subject.mjs';\n\nfunction animateSequence(sequence, options, scope) {\n    const animations = [];\n    const animationDefinitions = createAnimationsFromSequence(sequence, options, scope, { spring });\n    animationDefinitions.forEach(({ keyframes, transition }, subject) => {\n        animations.push(...animateSubject(subject, keyframes, transition));\n    });\n    return animations;\n}\n\nexport { animateSequence };\n", "import { GroupAnimationWithThen } from 'motion-dom';\nimport { removeItem } from 'motion-utils';\nimport { animateSequence } from './sequence.mjs';\nimport { animateSubject } from './subject.mjs';\n\nfunction isSequence(value) {\n    return Array.isArray(value) && value.some(Array.isArray);\n}\n/**\n * Creates an animation function that is optionally scoped\n * to a specific element.\n */\nfunction createScopedAnimate(scope) {\n    /**\n     * Implementation\n     */\n    function scopedAnimate(subjectOrSequence, optionsOrKeyframes, options) {\n        let animations = [];\n        if (isSequence(subjectOrSequence)) {\n            animations = animateSequence(subjectOrSequence, optionsOrKeyframes, scope);\n        }\n        else {\n            animations = animateSubject(subjectOrSequence, optionsOrKeyframes, options, scope);\n        }\n        const animation = new GroupAnimationWithThen(animations);\n        if (scope) {\n            scope.animations.push(animation);\n            animation.finished.then(() => {\n                removeItem(scope.animations, animation);\n            });\n        }\n        return animation;\n    }\n    return scopedAnimate;\n}\nconst animate = createScopedAnimate();\n\nexport { animate, createScopedAnimate };\n", "import { resolveElements, getValueTransition, getAnimationMap, animationMapKey, getComputedStyle, fillWildcards, applyPxDefaults, NativeAnimation } from 'motion-dom';\nimport { invariant, secondsToMilliseconds } from 'motion-utils';\n\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n    const elements = resolveElements(elementOrSelector, scope);\n    const numElements = elements.length;\n    invariant(Boolean(numElements), \"No valid elements provided.\", \"no-valid-elements\");\n    /**\n     * WAAPI doesn't support interrupting animations.\n     *\n     * Therefore, starting animations requires a three-step process:\n     * 1. Stop existing animations (write styles to DOM)\n     * 2. Resolve keyframes (read styles from DOM)\n     * 3. Create new animations (write styles to DOM)\n     *\n     * The hybrid `animate()` function uses AsyncAnimation to resolve\n     * keyframes before creating new animations, which removes style\n     * thrashing. Here, we have much stricter filesize constraints.\n     * Therefore we do this in a synchronous way that ensures that\n     * at least within `animate()` calls there is no style thrashing.\n     *\n     * In the motion-native-animate-mini-interrupt benchmark this\n     * was 80% faster than a single loop.\n     */\n    const animationDefinitions = [];\n    /**\n     * Step 1: Build options and stop existing animations (write)\n     */\n    for (let i = 0; i < numElements; i++) {\n        const element = elements[i];\n        const elementTransition = { ...options };\n        /**\n         * Resolve stagger function if provided.\n         */\n        if (typeof elementTransition.delay === \"function\") {\n            elementTransition.delay = elementTransition.delay(i, numElements);\n        }\n        for (const valueName in keyframes) {\n            let valueKeyframes = keyframes[valueName];\n            if (!Array.isArray(valueKeyframes)) {\n                valueKeyframes = [valueKeyframes];\n            }\n            const valueOptions = {\n                ...getValueTransition(elementTransition, valueName),\n            };\n            valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n            valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n            /**\n             * If there's an existing animation playing on this element then stop it\n             * before creating a new one.\n             */\n            const map = getAnimationMap(element);\n            const key = animationMapKey(valueName, valueOptions.pseudoElement || \"\");\n            const currentAnimation = map.get(key);\n            currentAnimation && currentAnimation.stop();\n            animationDefinitions.push({\n                map,\n                key,\n                unresolvedKeyframes: valueKeyframes,\n                options: {\n                    ...valueOptions,\n                    element,\n                    name: valueName,\n                    allowFlatten: !elementTransition.type && !elementTransition.ease,\n                },\n            });\n        }\n    }\n    /**\n     * Step 2: Resolve keyframes (read)\n     */\n    for (let i = 0; i < animationDefinitions.length; i++) {\n        const { unresolvedKeyframes, options: animationOptions } = animationDefinitions[i];\n        const { element, name, pseudoElement } = animationOptions;\n        if (!pseudoElement && unresolvedKeyframes[0] === null) {\n            unresolvedKeyframes[0] = getComputedStyle(element, name);\n        }\n        fillWildcards(unresolvedKeyframes);\n        applyPxDefaults(unresolvedKeyframes, name);\n        /**\n         * If we only have one keyframe, explicitly read the initial keyframe\n         * from the computed style. This is to ensure consistency with WAAPI behaviour\n         * for restarting animations, for instance .play() after finish, when it\n         * has one vs two keyframes.\n         */\n        if (!pseudoElement && unresolvedKeyframes.length < 2) {\n            unresolvedKeyframes.unshift(getComputedStyle(element, name));\n        }\n        animationOptions.keyframes = unresolvedKeyframes;\n    }\n    /**\n     * Step 3: Create new animations (write)\n     */\n    const animations = [];\n    for (let i = 0; i < animationDefinitions.length; i++) {\n        const { map, key, options: animationOptions } = animationDefinitions[i];\n        const animation = new NativeAnimation(animationOptions);\n        map.set(key, animation);\n        animation.finished.finally(() => map.delete(key));\n        animations.push(animation);\n    }\n    return animations;\n}\n\nexport { animateElements };\n", "import { GroupAnimationWithThen } from 'motion-dom';\nimport { animateElements } from './animate-elements.mjs';\n\nconst createScopedWaapiAnimate = (scope) => {\n    function scopedAnimate(elementOrSelector, keyframes, options) {\n        return new GroupAnimationWithThen(animateElements(elementOrSelector, keyframes, options, scope));\n    }\n    return scopedAnimate;\n};\nconst animateMini = /*@__PURE__*/ createScopedWaapiAnimate();\n\nexport { animateMini, createScopedWaapiAnimate };\n", "import { progress, velocityPerSecond } from 'motion-utils';\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = progress(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : velocityPerSecond(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\nexport { createScrollInfo, updateScrollInfo };\n", "import { isHTMLElement } from 'motion-dom';\n\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (isHTMLElement(current)) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\nexport { calcInset };\n", "const namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\nexport { namedEdges, resolveEdge };\n", "import { resolveEdge, namedEdges } from './edge.mjs';\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = resolveEdge(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = resolveEdge(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\nexport { resolveOffset };\n", "const ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\nexport { ScrollOffset };\n", "import { interpolate, defaultOffset } from 'motion-dom';\nimport { clamp } from 'motion-utils';\nimport { calcInset } from './inset.mjs';\nimport { resolveOffset } from './offset.mjs';\nimport { ScrollOffset } from './presets.mjs';\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? calcInset(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = resolveOffset(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = interpolate(info[axis].offset, defaultOffset(offsetDefinition), { clamp: false });\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = clamp(0, 1, info[axis].interpolate(info[axis].current));\n}\n\nexport { resolveOffsets };\n", "import { warnOnce } from 'motion-utils';\nimport { updateScrollInfo } from './info.mjs';\nimport { resolveOffsets } from './offsets/index.mjs';\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (process.env.NODE_ENV !== \"production\") {\n        if (container && target && target !== container) {\n            warnOnce(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: (time) => {\n            measure(element, options.target, info);\n            updateScrollInfo(element, info, time);\n            if (options.offset || options.target) {\n                resolveOffsets(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\nexport { createOnScrollHandler };\n", "import { resize, frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(frameData.timestamp);\n            }\n            frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n", "import { supportsScrollTimeline } from 'motion-dom';\nimport { scrollInfo } from '../track.mjs';\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = scrollInfo((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && supportsScrollTimeline()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\nexport { getTimeline };\n", "import { observeTimeline } from 'motion-dom';\nimport { getTimeline } from './utils/get-timeline.mjs';\n\nfunction attachToAnimation(animation, options) {\n    const timeline = getTimeline(options);\n    return animation.attachTimeline({\n        timeline: options.target ? undefined : timeline,\n        observe: (valueAnimation) => {\n            valueAnimation.pause();\n            return observeTimeline((progress) => {\n                valueAnimation.time = valueAnimation.duration * progress;\n            }, timeline);\n        },\n    });\n}\n\nexport { attachToAnimation };\n", "import { observeTimeline } from 'motion-dom';\nimport { scrollInfo } from './track.mjs';\nimport { getTimeline } from './utils/get-timeline.mjs';\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll)) {\n        return scrollInfo((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return observeTimeline(onScroll, getTimeline(options));\n    }\n}\n\nexport { attachToFunction };\n", "import { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? attachToFunction(onScroll, optionsWithDefaults)\n        : attachToAnimation(onScroll, optionsWithDefaults);\n}\n\nexport { scroll };\n", "import { resolveElements } from 'motion-dom';\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = resolveElements(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry.target, entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\nexport { inView };\n", "import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = time.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.setup(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n    return delay(callback, secondsToMilliseconds(timeout));\n}\n\nexport { delay, delayInSeconds };\n", "const distance = (a, b) => Math.abs(a - b);\nfunction distance2D(a, b) {\n    // Multi-dimensional\n    const xDelta = distance(a.x, b.x);\n    const yDelta = distance(a.y, b.y);\n    return Math.sqrt(xDelta ** 2 + yDelta ** 2);\n}\n\nexport { distance, distance2D };\n"], "mappings": ";;;AAAA,SAAS,cAAc,KAAK,MAAM;AAC9B,MAAI,IAAI,QAAQ,IAAI,MAAM;AACtB,QAAI,KAAK,IAAI;AACrB;AACA,SAAS,WAAW,KAAK,MAAM;AAC3B,QAAM,QAAQ,IAAI,QAAQ,IAAI;AAC9B,MAAI,QAAQ;AACR,QAAI,OAAO,OAAO,CAAC;AAC3B;AAEA,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,WAAW,SAAS;AAC5C,QAAM,aAAa,YAAY,IAAI,IAAI,SAAS,YAAY;AAC5D,MAAI,cAAc,KAAK,aAAa,IAAI,QAAQ;AAC5C,UAAM,WAAW,UAAU,IAAI,IAAI,SAAS,UAAU;AACtD,UAAM,CAAC,IAAI,IAAI,IAAI,OAAO,WAAW,CAAC;AACtC,QAAI,OAAO,UAAU,GAAG,IAAI;AAAA,EAChC;AACA,SAAO;AACX;;;AClBA,IAAM,QAAQ,CAAC,KAAK,KAAK,MAAM;AAC3B,MAAI,IAAI;AACJ,WAAO;AACX,MAAI,IAAI;AACJ,WAAO;AACX,SAAO;AACX;;;ACNA,SAAS,mBAAmB,SAAS,WAAW;AAC5C,SAAO,YACD,GAAG,OAAO,0FAA0F,SAAS,KAC7G;AACV;;;ACFA,IAAI,UAAU,MAAM;AAAE;AACtB,IAAI,YAAY,MAAM;AAAE;AACxB,IAAI,MAAuC;AACvC,YAAU,CAAC,OAAO,SAAS,cAAc;AACrC,QAAI,CAAC,SAAS,OAAO,YAAY,aAAa;AAC1C,cAAQ,KAAK,mBAAmB,SAAS,SAAS,CAAC;AAAA,IACvD;AAAA,EACJ;AACA,cAAY,CAAC,OAAO,SAAS,cAAc;AACvC,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,MAAM,mBAAmB,SAAS,SAAS,CAAC;AAAA,IAC1D;AAAA,EACJ;AACJ;;;ACfA,IAAM,qBAAqB,CAAC;;;ACG5B,IAAM,oBAAoB,CAAC,MAAM,+BAA+B,KAAK,CAAC;;;ACHtE,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;;;ACCA,IAAM,oBAAoB,CAAC,MAAM,cAAc,KAAK,CAAC;;;ACFrD,SAAS,KAAK,UAAU;AACpB,MAAI;AACJ,SAAO,MAAM;AACT,QAAI,WAAW;AACX,eAAS,SAAS;AACtB,WAAO;AAAA,EACX;AACJ;;;ACPA,IAAM,OAAO,CAAC,QAAQ;;;ACMtB,IAAM,mBAAmB,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAChD,IAAM,OAAO,IAAI,iBAAiB,aAAa,OAAO,gBAAgB;;;ACKtE,IAAM,WAAW,CAAC,MAAM,IAAI,UAAU;AAClC,QAAM,mBAAmB,KAAK;AAC9B,SAAO,qBAAqB,IAAI,KAAK,QAAQ,QAAQ;AACzD;;;ACdA,IAAM,sBAAN,MAA0B;AAAA,EACtB,cAAc;AACV,SAAK,gBAAgB,CAAC;AAAA,EAC1B;AAAA,EACA,IAAI,SAAS;AACT,kBAAc,KAAK,eAAe,OAAO;AACzC,WAAO,MAAM,WAAW,KAAK,eAAe,OAAO;AAAA,EACvD;AAAA,EACA,OAAO,GAAG,GAAG,GAAG;AACZ,UAAM,mBAAmB,KAAK,cAAc;AAC5C,QAAI,CAAC;AACD;AACJ,QAAI,qBAAqB,GAAG;AAIxB,WAAK,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC;AAAA,IACjC,OACK;AACD,eAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AAKvC,cAAM,UAAU,KAAK,cAAc,CAAC;AACpC,mBAAW,QAAQ,GAAG,GAAG,CAAC;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU;AACN,WAAO,KAAK,cAAc;AAAA,EAC9B;AAAA,EACA,QAAQ;AACJ,SAAK,cAAc,SAAS;AAAA,EAChC;AACJ;;;AC9BA,IAAM,wBAAwB,CAAC,YAAY,UAAU;AAErD,IAAM,wBAAwB,CAAC,iBAAiB,eAAe;;;ACH/D,SAAS,kBAAkB,UAAU,eAAe;AAChD,SAAO,gBAAgB,YAAY,MAAO,iBAAiB;AAC/D;;;ACNA,IAAM,SAAS,oBAAI,IAAI;AACvB,SAAS,UAAU,SAAS;AACxB,SAAO,OAAO,IAAI,OAAO;AAC7B;AACA,SAAS,SAAS,WAAW,SAAS,WAAW;AAC7C,MAAI,aAAa,OAAO,IAAI,OAAO;AAC/B;AACJ,UAAQ,KAAK,mBAAmB,SAAS,SAAS,CAAC;AACnD,SAAO,IAAI,OAAO;AACtB;;;ACXA,IAAM,OAAO,CAAC,KAAK,KAAK,MAAM;AAC1B,QAAM,YAAY,MAAM;AACxB,WAAW,IAAI,OAAO,YAAa,aAAa,YAAa;AACjE;;;ACiBA,IAAM,aAAa,CAAC,GAAG,IAAI,UAAU,IAAM,IAAM,KAAK,IAAM,MAAM,KAAK,IAAM,KAAK,IAAM,OAAO,IAAI,IAAM,MACrG;AACJ,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,SAAS,gBAAgB,GAAG,YAAY,YAAY,KAAK,KAAK;AAC1D,MAAI;AACJ,MAAI;AACJ,MAAI,IAAI;AACR,KAAG;AACC,eAAW,cAAc,aAAa,cAAc;AACpD,eAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,QAAI,WAAW,GAAK;AAChB,mBAAa;AAAA,IACjB,OACK;AACD,mBAAa;AAAA,IACjB;AAAA,EACJ,SAAS,KAAK,IAAI,QAAQ,IAAI,wBAC1B,EAAE,IAAI;AACV,SAAO;AACX;AACA,SAAS,YAAY,KAAK,KAAK,KAAK,KAAK;AAErC,MAAI,QAAQ,OAAO,QAAQ;AACvB,WAAO;AACX,QAAM,WAAW,CAAC,OAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK,GAAG;AAE3D,SAAO,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAC3E;;;AC9CA,IAAM,eAAe,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE,KAAK;;;ACAnG,IAAM,gBAAgB,CAAC,WAAW,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC;;;ACEzD,IAAM,UAAwB,YAAY,MAAM,MAAM,MAAM,IAAI;AAChE,IAAM,SAAuB,cAAc,OAAO;AAClD,IAAM,YAA0B,aAAa,MAAM;;;ACJnD,IAAM,aAAa,CAAC,OAAO,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE;;;ACC/F,IAAM,SAAS,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAC/C,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,MAAM;;;ACHrC,IAAM,SAAuB,YAAY,MAAM,GAAG,GAAG,CAAC;AACtD,IAAM,UAAwB,YAAY,GAAG,GAAG,MAAM,CAAC;AACvD,IAAM,YAA0B,YAAY,MAAM,GAAG,MAAM,CAAC;;;ACF5D,SAAS,MAAM,UAAU,YAAY,OAAO;AACxC,SAAO,CAACA,cAAa;AACjB,IAAAA,YACI,cAAc,QACR,KAAK,IAAIA,WAAU,KAAK,IACxB,KAAK,IAAIA,WAAU,IAAK;AAClC,UAAM,WAAWA,YAAW;AAC5B,UAAM,UAAU,cAAc,QAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC/E,WAAO,MAAM,GAAG,GAAG,UAAU,QAAQ;AAAA,EACzC;AACJ;;;ACZA,IAAM,gBAAgB,CAACC,UAAS;AAC5B,SAAO,MAAM,QAAQA,KAAI,KAAK,OAAOA,MAAK,CAAC,MAAM;AACrD;;;ACCA,SAAS,oBAAoB,QAAQ,GAAG;AACpC,SAAO,cAAc,MAAM,IAAI,OAAO,KAAK,GAAG,OAAO,QAAQ,CAAC,CAAC,IAAI;AACvE;;;ACLA,IAAM,qBAAqB,CAAC,WAAW,MAAM,QAAQ,MAAM,KAAK,OAAO,OAAO,CAAC,MAAM;;;ACSrF,IAAM,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,gBAAgB,CAAC,WAAW;AAC9B,SAAO,OAAO,WAAW;AAC7B;AACA,IAAM,6BAA6B,CAAC,eAAe;AAC/C,MAAI,mBAAmB,UAAU,GAAG;AAEhC,cAAU,WAAW,WAAW,GAAG,2DAA2D,qBAAqB;AACnH,UAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,WAAO,YAAY,IAAI,IAAI,IAAI,EAAE;AAAA,EACrC,WACS,cAAc,UAAU,GAAG;AAEhC,cAAU,aAAa,UAAU,MAAM,QAAW,wBAAwB,UAAU,KAAK,qBAAqB;AAC9G,WAAO,aAAa,UAAU;AAAA,EAClC;AACA,SAAO;AACX;;;ACtCA,IAAM,aAAa;AAAA,EACf;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AACJ;;;ACTA,IAAM,cAAc;AAAA,EAChB,OAAO;AAAA,EACP,sBAAsB;AAC1B;;;ACDA,SAAS,iBAAiB,cAAc,UAAU;AAK9C,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,YAAY,oBAAI,IAAI;AAKxB,MAAI,eAAe;AACnB,MAAI,iBAAiB;AAIrB,QAAM,cAAc,oBAAI,QAAQ;AAChC,MAAI,kBAAkB;AAAA,IAClB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,EAClB;AACA,MAAI,WAAW;AACf,WAAS,gBAAgB,UAAU;AAC/B,QAAI,YAAY,IAAI,QAAQ,GAAG;AAC3B,WAAK,SAAS,QAAQ;AACtB,mBAAa;AAAA,IACjB;AACA;AACA,aAAS,eAAe;AAAA,EAC5B;AACA,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA,IAIT,UAAU,CAAC,UAAU,YAAY,OAAO,YAAY,UAAU;AAC1D,YAAM,oBAAoB,aAAa;AACvC,YAAM,QAAQ,oBAAoB,YAAY;AAC9C,UAAI;AACA,oBAAY,IAAI,QAAQ;AAC5B,UAAI,CAAC,MAAM,IAAI,QAAQ;AACnB,cAAM,IAAI,QAAQ;AACtB,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA,IAIA,QAAQ,CAAC,aAAa;AAClB,gBAAU,OAAO,QAAQ;AACzB,kBAAY,OAAO,QAAQ;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS,CAACC,eAAc;AACpB,wBAAkBA;AAMlB,UAAI,cAAc;AACd,yBAAiB;AACjB;AAAA,MACJ;AACA,qBAAe;AACf,OAAC,WAAW,SAAS,IAAI,CAAC,WAAW,SAAS;AAE9C,gBAAU,QAAQ,eAAe;AAIjC,UAAI,YAAY,YAAY,OAAO;AAC/B,oBAAY,MAAM,UAAU,QAAQ,EAAE,KAAK,QAAQ;AAAA,MACvD;AACA,iBAAW;AAGX,gBAAU,MAAM;AAChB,qBAAe;AACf,UAAI,gBAAgB;AAChB,yBAAiB;AACjB,aAAK,QAAQA,UAAS;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;ACrFA,IAAM,aAAa;AACnB,SAAS,oBAAoB,mBAAmB,gBAAgB;AAC5D,MAAI,eAAe;AACnB,MAAI,oBAAoB;AACxB,QAAM,QAAQ;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,EAClB;AACA,QAAM,mBAAmB,MAAO,eAAe;AAC/C,QAAMC,SAAQ,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC1C,QAAI,GAAG,IAAI,iBAAiB,kBAAkB,iBAAiB,MAAM,MAAS;AAC9E,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,QAAM,EAAE,OAAO,MAAM,kBAAkB,WAAW,QAAQ,WAAW,QAAQ,WAAY,IAAIA;AAC7F,QAAM,eAAe,MAAM;AACvB,UAAM,YAAY,mBAAmB,kBAC/B,MAAM,YACN,YAAY,IAAI;AACtB,mBAAe;AACf,QAAI,CAAC,mBAAmB,iBAAiB;AACrC,YAAM,QAAQ,oBACR,MAAO,KACP,KAAK,IAAI,KAAK,IAAI,YAAY,MAAM,WAAW,UAAU,GAAG,CAAC;AAAA,IACvE;AACA,UAAM,YAAY;AAClB,UAAM,eAAe;AAErB,UAAM,QAAQ,KAAK;AACnB,SAAK,QAAQ,KAAK;AAClB,qBAAiB,QAAQ,KAAK;AAC9B,cAAU,QAAQ,KAAK;AACvB,WAAO,QAAQ,KAAK;AACpB,cAAU,QAAQ,KAAK;AACvB,WAAO,QAAQ,KAAK;AACpB,eAAW,QAAQ,KAAK;AACxB,UAAM,eAAe;AACrB,QAAI,gBAAgB,gBAAgB;AAChC,0BAAoB;AACpB,wBAAkB,YAAY;AAAA,IAClC;AAAA,EACJ;AACA,QAAM,OAAO,MAAM;AACf,mBAAe;AACf,wBAAoB;AACpB,QAAI,CAAC,MAAM,cAAc;AACrB,wBAAkB,YAAY;AAAA,IAClC;AAAA,EACJ;AACA,QAAM,WAAW,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC7C,UAAM,OAAOA,OAAM,GAAG;AACtB,QAAI,GAAG,IAAI,CAACC,UAAS,YAAY,OAAO,YAAY,UAAU;AAC1D,UAAI,CAAC;AACD,aAAK;AACT,aAAO,KAAK,SAASA,UAAS,WAAW,SAAS;AAAA,IACtD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,QAAM,SAAS,CAACA,aAAY;AACxB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,MAAAD,OAAM,WAAW,CAAC,CAAC,EAAE,OAAOC,QAAO;AAAA,IACvC;AAAA,EACJ;AACA,SAAO,EAAE,UAAU,QAAQ,OAAO,OAAAD,OAAM;AAC5C;;;ACjEA,IAAM,EAAE,UAAU,OAAO,QAAQ,aAAa,OAAO,WAAW,OAAO,WAAY,IAAoB,oBAAoB,OAAO,0BAA0B,cAAc,wBAAwB,MAAM,IAAI;;;ACA5M,IAAI;AACJ,SAAS,YAAY;AACjB,QAAM;AACV;AASA,IAAM,OAAO;AAAA,EACT,KAAK,MAAM;AACP,QAAI,QAAQ,QAAW;AACnB,WAAK,IAAI,UAAU,gBAAgB,mBAAmB,kBAChD,UAAU,YACV,YAAY,IAAI,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,CAAC,YAAY;AACd,UAAM;AACN,mBAAe,SAAS;AAAA,EAC5B;AACJ;;;AC5BA,IAAM,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AACX;;;ACJA,IAAM,wBAAwB,CAAC,UAAU,CAAC,QAAQ,OAAO,QAAQ,YAAY,IAAI,WAAW,KAAK;AACjG,IAAM,oBACQ,sBAAsB,IAAI;AACxC,IAAM,wBACQ,sBAAsB,QAAQ;AAC5C,IAAM,qBAAqB,CAAC,UAAU;AAClC,QAAM,kBAAkB,sBAAsB,KAAK;AACnD,MAAI,CAAC;AACD,WAAO;AAEX,SAAO,uBAAuB,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC;AAClE;AACA,IAAM,yBAAyB;;;ACV/B,IAAM,SAAS;AAAA,EACX,MAAM,CAAC,MAAM,OAAO,MAAM;AAAA,EAC1B,OAAO;AAAA,EACP,WAAW,CAAC,MAAM;AACtB;AACA,IAAM,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,WAAW,CAAC,MAAM,MAAM,GAAG,GAAG,CAAC;AACnC;AACA,IAAM,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,SAAS;AACb;;;ACZA,IAAM,WAAW,CAAC,MAAM,KAAK,MAAM,IAAI,GAAM,IAAI;;;ACFjD,IAAM,aAAa;;;ACAnB,SAAS,UAAU,GAAG;AAClB,SAAO,KAAK;AAChB;;;ACFA,IAAM,mBAAmB;;;ACQzB,IAAM,gBAAgB,CAAC,MAAM,aAAa,CAAC,MAAM;AAC7C,SAAO,QAAS,OAAO,MAAM,YACzB,iBAAiB,KAAK,CAAC,KACvB,EAAE,WAAW,IAAI,KAChB,YACG,CAAC,UAAU,CAAC,KACZ,OAAO,UAAU,eAAe,KAAK,GAAG,QAAQ,CAAE;AAC9D;AACA,IAAM,aAAa,CAAC,OAAO,OAAO,UAAU,CAAC,MAAM;AAC/C,MAAI,OAAO,MAAM;AACb,WAAO;AACX,QAAM,CAAC,GAAG,GAAG,GAAGE,MAAK,IAAI,EAAE,MAAM,UAAU;AAC3C,SAAO;AAAA,IACH,CAAC,KAAK,GAAG,WAAW,CAAC;AAAA,IACrB,CAAC,KAAK,GAAG,WAAW,CAAC;AAAA,IACrB,CAAC,KAAK,GAAG,WAAW,CAAC;AAAA,IACrB,OAAOA,WAAU,SAAY,WAAWA,MAAK,IAAI;AAAA,EACrD;AACJ;;;ACrBA,IAAM,eAAe,CAAC,MAAM,MAAM,GAAG,KAAK,CAAC;AAC3C,IAAM,UAAU;AAAA,EACZ,GAAG;AAAA,EACH,WAAW,CAAC,MAAM,KAAK,MAAM,aAAa,CAAC,CAAC;AAChD;AACA,IAAM,OAAO;AAAA,EACT,MAAoB,cAAc,OAAO,KAAK;AAAA,EAC9C,OAAqB,WAAW,OAAO,SAAS,MAAM;AAAA,EACtD,WAAW,CAAC,EAAE,KAAK,OAAO,MAAM,OAAO,UAAU,EAAE,MAAM,UACrD,QAAQ,UAAU,GAAG,IACrB,OACA,QAAQ,UAAU,KAAK,IACvB,OACA,QAAQ,UAAU,IAAI,IACtB,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AACR;;;ACnBA,SAAS,SAAS,GAAG;AACjB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,EAAE,SAAS,GAAG;AACd,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,QAAI,EAAE,UAAU,GAAG,CAAC;AAAA,EAExB,OACK;AACD,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,QAAI,EAAE,UAAU,GAAG,CAAC;AACpB,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACT;AACA,SAAO;AAAA,IACH,KAAK,SAAS,GAAG,EAAE;AAAA,IACnB,OAAO,SAAS,GAAG,EAAE;AAAA,IACrB,MAAM,SAAS,GAAG,EAAE;AAAA,IACpB,OAAO,IAAI,SAAS,GAAG,EAAE,IAAI,MAAM;AAAA,EACvC;AACJ;AACA,IAAM,MAAM;AAAA,EACR,MAAoB,cAAc,GAAG;AAAA,EACrC,OAAO;AAAA,EACP,WAAW,KAAK;AACpB;;;ACpCA,IAAM,iBAAiB,CAAC,UAAU;AAAA,EAC9B,MAAM,CAAC,MAAM,OAAO,MAAM,YAAY,EAAE,SAAS,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,WAAW;AAAA,EAClF,OAAO;AAAA,EACP,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI;AACjC;AACA,IAAM,UAAwB,eAAe,KAAK;AAClD,IAAM,UAAwB,eAAe,GAAG;AAChD,IAAM,KAAmB,eAAe,IAAI;AAC5C,IAAM,KAAmB,eAAe,IAAI;AAC5C,IAAM,KAAmB,eAAe,IAAI;AAC5C,IAAM,sBAAoC,OAAO;AAAA,EAC7C,GAAG;AAAA,EACH,OAAO,CAAC,MAAM,QAAQ,MAAM,CAAC,IAAI;AAAA,EACjC,WAAW,CAAC,MAAM,QAAQ,UAAU,IAAI,GAAG;AAC/C,IAAI;;;ACVJ,IAAM,OAAO;AAAA,EACT,MAAoB,cAAc,OAAO,KAAK;AAAA,EAC9C,OAAqB,WAAW,OAAO,cAAc,WAAW;AAAA,EAChE,WAAW,CAAC,EAAE,KAAK,YAAY,WAAW,OAAO,UAAU,EAAE,MAAM;AAC/D,WAAQ,UACJ,KAAK,MAAM,GAAG,IACd,OACA,QAAQ,UAAU,SAAS,UAAU,CAAC,IACtC,OACA,QAAQ,UAAU,SAAS,SAAS,CAAC,IACrC,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AAAA,EACR;AACJ;;;ACfA,IAAM,QAAQ;AAAA,EACV,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACvD,OAAO,CAAC,MAAM;AACV,QAAI,KAAK,KAAK,CAAC,GAAG;AACd,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,WACS,KAAK,KAAK,CAAC,GAAG;AACnB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,OACK;AACD,aAAO,IAAI,MAAM,CAAC;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,MAAM;AACd,WAAO,OAAO,MAAM,WACd,IACA,EAAE,eAAe,KAAK,IAClB,KAAK,UAAU,CAAC,IAChB,KAAK,UAAU,CAAC;AAAA,EAC9B;AAAA,EACA,mBAAmB,CAAC,MAAM;AACtB,UAAM,SAAS,MAAM,MAAM,CAAC;AAC5B,WAAO,QAAQ;AACf,WAAO,MAAM,UAAU,MAAM;AAAA,EACjC;AACJ;;;AC7BA,IAAM,aAAa;;;ACKnB,SAAS,KAAK,GAAG;AACb,SAAQ,MAAM,CAAC,KACX,OAAO,MAAM,aACZ,EAAE,MAAM,UAAU,GAAG,UAAU,MAC3B,EAAE,MAAM,UAAU,GAAG,UAAU,KAChC;AACZ;AACA,IAAM,eAAe;AACrB,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AAEpB,IAAM,eAAe;AACrB,SAAS,oBAAoB,OAAO;AAChC,QAAM,gBAAgB,MAAM,SAAS;AACrC,QAAM,SAAS,CAAC;AAChB,QAAM,UAAU;AAAA,IACZ,OAAO,CAAC;AAAA,IACR,QAAQ,CAAC;AAAA,IACT,KAAK,CAAC;AAAA,EACV;AACA,QAAM,QAAQ,CAAC;AACf,MAAI,IAAI;AACR,QAAM,YAAY,cAAc,QAAQ,cAAc,CAAC,gBAAgB;AACnE,QAAI,MAAM,KAAK,WAAW,GAAG;AACzB,cAAQ,MAAM,KAAK,CAAC;AACpB,YAAM,KAAK,WAAW;AACtB,aAAO,KAAK,MAAM,MAAM,WAAW,CAAC;AAAA,IACxC,WACS,YAAY,WAAW,kBAAkB,GAAG;AACjD,cAAQ,IAAI,KAAK,CAAC;AAClB,YAAM,KAAK,SAAS;AACpB,aAAO,KAAK,WAAW;AAAA,IAC3B,OACK;AACD,cAAQ,OAAO,KAAK,CAAC;AACrB,YAAM,KAAK,YAAY;AACvB,aAAO,KAAK,WAAW,WAAW,CAAC;AAAA,IACvC;AACA,MAAE;AACF,WAAO;AAAA,EACX,CAAC;AACD,QAAM,QAAQ,UAAU,MAAM,WAAW;AACzC,SAAO,EAAE,QAAQ,OAAO,SAAS,MAAM;AAC3C;AACA,SAAS,kBAAkB,GAAG;AAC1B,SAAO,oBAAoB,CAAC,EAAE;AAClC;AACA,SAAS,kBAAkB,QAAQ;AAC/B,QAAM,EAAE,OAAO,MAAM,IAAI,oBAAoB,MAAM;AACnD,QAAM,cAAc,MAAM;AAC1B,SAAO,CAAC,MAAM;AACV,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,gBAAU,MAAM,CAAC;AACjB,UAAI,EAAE,CAAC,MAAM,QAAW;AACpB,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,SAAS,cAAc;AACvB,oBAAU,SAAS,EAAE,CAAC,CAAC;AAAA,QAC3B,WACS,SAAS,aAAa;AAC3B,oBAAU,MAAM,UAAU,EAAE,CAAC,CAAC;AAAA,QAClC,OACK;AACD,oBAAU,EAAE,CAAC;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,uBAAuB,CAAC,MAAM,OAAO,MAAM,WAAW,IAAI,MAAM,KAAK,CAAC,IAAI,MAAM,kBAAkB,CAAC,IAAI;AAC7G,SAAS,kBAAkB,GAAG;AAC1B,QAAM,SAAS,kBAAkB,CAAC;AAClC,QAAM,cAAc,kBAAkB,CAAC;AACvC,SAAO,YAAY,OAAO,IAAI,oBAAoB,CAAC;AACvD;AACA,IAAM,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AACJ;;;ACvFA,SAAS,SAAS,GAAG,GAAG,GAAG;AACvB,MAAI,IAAI;AACJ,SAAK;AACT,MAAI,IAAI;AACJ,SAAK;AACT,MAAI,IAAI,IAAI;AACR,WAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,IAAI,IAAI;AACR,WAAO;AACX,MAAI,IAAI,IAAI;AACR,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AACvC,SAAO;AACX;AACA,SAAS,WAAW,EAAE,KAAK,YAAY,WAAW,OAAAC,OAAM,GAAG;AACvD,SAAO;AACP,gBAAc;AACd,eAAa;AACb,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,CAAC,YAAY;AACb,UAAM,QAAQ,OAAO;AAAA,EACzB,OACK;AACD,UAAM,IAAI,YAAY,MAChB,aAAa,IAAI,cACjB,YAAY,aAAa,YAAY;AAC3C,UAAM,IAAI,IAAI,YAAY;AAC1B,UAAM,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAChC,YAAQ,SAAS,GAAG,GAAG,GAAG;AAC1B,WAAO,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAAA,EACrC;AACA,SAAO;AAAA,IACH,KAAK,KAAK,MAAM,MAAM,GAAG;AAAA,IACzB,OAAO,KAAK,MAAM,QAAQ,GAAG;AAAA,IAC7B,MAAM,KAAK,MAAM,OAAO,GAAG;AAAA,IAC3B,OAAAA;AAAA,EACJ;AACJ;;;ACvCA,SAAS,aAAa,GAAG,GAAG;AACxB,SAAO,CAAC,MAAO,IAAI,IAAI,IAAI;AAC/B;;;ACmBA,IAAM,YAAY,CAAC,MAAM,IAAIC,cAAa;AACtC,SAAO,QAAQ,KAAK,QAAQA;AAChC;;;ACZA,IAAM,iBAAiB,CAAC,MAAM,IAAI,MAAM;AACpC,QAAM,WAAW,OAAO;AACxB,QAAM,OAAO,KAAK,KAAK,KAAK,YAAY;AACxC,SAAO,OAAO,IAAI,IAAI,KAAK,KAAK,IAAI;AACxC;AACA,IAAM,aAAa,CAAC,KAAK,MAAM,IAAI;AACnC,IAAM,eAAe,CAAC,MAAM,WAAW,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;AAClE,SAAS,OAAOC,QAAO;AACnB,QAAM,OAAO,aAAaA,MAAK;AAC/B,UAAQ,QAAQ,IAAI,GAAG,IAAIA,MAAK,wEAAwE,sBAAsB;AAC9H,MAAI,CAAC,QAAQ,IAAI;AACb,WAAO;AACX,MAAI,QAAQ,KAAK,MAAMA,MAAK;AAC5B,MAAI,SAAS,MAAM;AAEf,YAAQ,WAAW,KAAK;AAAA,EAC5B;AACA,SAAO;AACX;AACA,IAAM,WAAW,CAAC,MAAM,OAAO;AAC3B,QAAM,WAAW,OAAO,IAAI;AAC5B,QAAM,SAAS,OAAO,EAAE;AACxB,MAAI,CAAC,YAAY,CAAC,QAAQ;AACtB,WAAO,aAAa,MAAM,EAAE;AAAA,EAChC;AACA,QAAM,UAAU,EAAE,GAAG,SAAS;AAC9B,SAAO,CAAC,MAAM;AACV,YAAQ,MAAM,eAAe,SAAS,KAAK,OAAO,KAAK,CAAC;AACxD,YAAQ,QAAQ,eAAe,SAAS,OAAO,OAAO,OAAO,CAAC;AAC9D,YAAQ,OAAO,eAAe,SAAS,MAAM,OAAO,MAAM,CAAC;AAC3D,YAAQ,QAAQ,UAAU,SAAS,OAAO,OAAO,OAAO,CAAC;AACzD,WAAO,KAAK,UAAU,OAAO;AAAA,EACjC;AACJ;;;AC5CA,IAAM,kBAAkB,oBAAI,IAAI,CAAC,QAAQ,QAAQ,CAAC;AAMlD,SAAS,cAAc,QAAQ,QAAQ;AACnC,MAAI,gBAAgB,IAAI,MAAM,GAAG;AAC7B,WAAO,CAAC,MAAO,KAAK,IAAI,SAAS;AAAA,EACrC,OACK;AACD,WAAO,CAAC,MAAO,KAAK,IAAI,SAAS;AAAA,EACrC;AACJ;;;ACJA,SAASC,WAAU,GAAG,GAAG;AACrB,SAAO,CAAC,MAAM,UAAY,GAAG,GAAG,CAAC;AACrC;AACA,SAAS,SAAS,GAAG;AACjB,MAAI,OAAO,MAAM,UAAU;AACvB,WAAOA;AAAA,EACX,WACS,OAAO,MAAM,UAAU;AAC5B,WAAO,mBAAmB,CAAC,IACrB,eACA,MAAM,KAAK,CAAC,IACR,WACA;AAAA,EACd,WACS,MAAM,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,EACX,WACS,OAAO,MAAM,UAAU;AAC5B,WAAO,MAAM,KAAK,CAAC,IAAI,WAAW;AAAA,EACtC;AACA,SAAO;AACX;AACA,SAAS,SAAS,GAAG,GAAG;AACpB,QAAM,SAAS,CAAC,GAAG,CAAC;AACpB,QAAM,YAAY,OAAO;AACzB,QAAM,aAAa,EAAE,IAAI,CAAC,GAAG,MAAM,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD,SAAO,CAAC,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,aAAO,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,GAAG,GAAG;AACrB,QAAM,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE;AAC5B,QAAM,aAAa,CAAC;AACpB,aAAW,OAAO,QAAQ;AACtB,QAAI,EAAE,GAAG,MAAM,UAAa,EAAE,GAAG,MAAM,QAAW;AAC9C,iBAAW,GAAG,IAAI,SAAS,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,IACrD;AAAA,EACJ;AACA,SAAO,CAAC,MAAM;AACV,eAAW,OAAO,YAAY;AAC1B,aAAO,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,WAAW,QAAQ,QAAQ;AAChC,QAAM,gBAAgB,CAAC;AACvB,QAAM,WAAW,EAAE,OAAO,GAAG,KAAK,GAAG,QAAQ,EAAE;AAC/C,WAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAK;AAC3C,UAAM,OAAO,OAAO,MAAM,CAAC;AAC3B,UAAM,cAAc,OAAO,QAAQ,IAAI,EAAE,SAAS,IAAI,CAAC;AACvD,UAAM,cAAc,OAAO,OAAO,WAAW,KAAK;AAClD,kBAAc,CAAC,IAAI;AACnB,aAAS,IAAI;AAAA,EACjB;AACA,SAAO;AACX;AACA,IAAM,aAAa,CAAC,QAAQ,WAAW;AACnC,QAAM,WAAW,QAAQ,kBAAkB,MAAM;AACjD,QAAM,cAAc,oBAAoB,MAAM;AAC9C,QAAM,cAAc,oBAAoB,MAAM;AAC9C,QAAM,iBAAiB,YAAY,QAAQ,IAAI,WAAW,YAAY,QAAQ,IAAI,UAC9E,YAAY,QAAQ,MAAM,WAAW,YAAY,QAAQ,MAAM,UAC/D,YAAY,QAAQ,OAAO,UAAU,YAAY,QAAQ,OAAO;AACpE,MAAI,gBAAgB;AAChB,QAAK,gBAAgB,IAAI,MAAM,KAC3B,CAAC,YAAY,OAAO,UACnB,gBAAgB,IAAI,MAAM,KACvB,CAAC,YAAY,OAAO,QAAS;AACjC,aAAO,cAAc,QAAQ,MAAM;AAAA,IACvC;AACA,WAAO,KAAK,SAAS,WAAW,aAAa,WAAW,GAAG,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC5F,OACK;AACD,YAAQ,MAAM,mBAAmB,MAAM,UAAU,MAAM,4KAA4K,0BAA0B;AAC7P,WAAO,aAAa,QAAQ,MAAM;AAAA,EACtC;AACJ;;;ACtFA,SAAS,IAAI,MAAM,IAAI,GAAG;AACtB,MAAI,OAAO,SAAS,YAChB,OAAO,OAAO,YACd,OAAO,MAAM,UAAU;AACvB,WAAO,UAAU,MAAM,IAAI,CAAC;AAAA,EAChC;AACA,QAAM,QAAQ,SAAS,IAAI;AAC3B,SAAO,MAAM,MAAM,EAAE;AACzB;;;ACRA,IAAM,kBAAkB,CAAC,WAAW;AAChC,QAAM,gBAAgB,CAAC,EAAE,UAAU,MAAM,OAAO,SAAS;AACzD,SAAO;AAAA,IACH,OAAO,CAAC,YAAY,SAAS,MAAM,OAAO,eAAe,SAAS;AAAA,IAClE,MAAM,MAAM,YAAY,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,IAKrC,KAAK,MAAO,UAAU,eAAe,UAAU,YAAY,KAAK,IAAI;AAAA,EACxE;AACJ;;;ACdA,IAAM,uBAAuB,CAAC,QAAQ,UACtC,aAAa,OACR;AACD,MAAI,SAAS;AACb,QAAM,YAAY,KAAK,IAAI,KAAK,MAAM,WAAW,UAAU,GAAG,CAAC;AAC/D,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,cAAU,KAAK,MAAM,OAAO,KAAK,YAAY,EAAE,IAAI,GAAK,IAAI,MAAQ;AAAA,EACxE;AACA,SAAO,UAAU,OAAO,UAAU,GAAG,OAAO,SAAS,CAAC,CAAC;AAC3D;;;ACLA,IAAM,uBAAuB;AAC7B,SAAS,sBAAsB,WAAW;AACtC,MAAI,WAAW;AACf,QAAM,WAAW;AACjB,MAAI,QAAQ,UAAU,KAAK,QAAQ;AACnC,SAAO,CAAC,MAAM,QAAQ,WAAW,sBAAsB;AACnD,gBAAY;AACZ,YAAQ,UAAU,KAAK,QAAQ;AAAA,EACnC;AACA,SAAO,YAAY,uBAAuB,WAAW;AACzD;;;ACRA,SAAS,sBAAsB,SAASC,SAAQ,KAAK,iBAAiB;AAClE,QAAM,YAAY,gBAAgB,EAAE,GAAG,SAAS,WAAW,CAAC,GAAGA,MAAK,EAAE,CAAC;AACvE,QAAM,WAAW,KAAK,IAAI,sBAAsB,SAAS,GAAG,oBAAoB;AAChF,SAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAM,CAACC,cAAa;AAChB,aAAO,UAAU,KAAK,WAAWA,SAAQ,EAAE,QAAQD;AAAA,IACvD;AAAA,IACA,UAAU,sBAAsB,QAAQ;AAAA,EAC5C;AACJ;;;ACdA,IAAM,yBAAyB;AAC/B,SAAS,sBAAsB,cAAc,GAAGE,UAAS;AACrD,QAAM,QAAQ,KAAK,IAAI,IAAI,wBAAwB,CAAC;AACpD,SAAO,kBAAkBA,WAAU,aAAa,KAAK,GAAG,IAAI,KAAK;AACrE;;;ACNA,IAAM,iBAAiB;AAAA;AAAA,EAEnB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA;AAAA,EAEV,UAAU;AAAA;AAAA,EACV,QAAQ;AAAA,EACR,gBAAgB;AAAA;AAAA;AAAA,EAEhB,WAAW;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAAA,EACA,WAAW;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EACb,aAAa;AAAA;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAChB;;;ACrBA,IAAM,UAAU;AAChB,SAAS,WAAW,EAAE,WAAW,eAAe,UAAU,SAAS,eAAe,QAAQ,WAAW,eAAe,UAAU,OAAO,eAAe,KAAM,GAAG;AACzJ,MAAI;AACJ,MAAI;AACJ,UAAQ,YAAY,sBAAsB,eAAe,WAAW,GAAG,8CAA8C,uBAAuB;AAC5I,MAAI,eAAe,IAAI;AAIvB,iBAAe,MAAM,eAAe,YAAY,eAAe,YAAY,YAAY;AACvF,aAAW,MAAM,eAAe,aAAa,eAAe,aAAa,sBAAsB,QAAQ,CAAC;AACxG,MAAI,eAAe,GAAG;AAIlB,eAAW,CAACC,kBAAiB;AACzB,YAAM,mBAAmBA,gBAAe;AACxC,YAAM,QAAQ,mBAAmB;AACjC,YAAM,IAAI,mBAAmB;AAC7B,YAAM,IAAI,gBAAgBA,eAAc,YAAY;AACpD,YAAM,IAAI,KAAK,IAAI,CAAC,KAAK;AACzB,aAAO,UAAW,IAAI,IAAK;AAAA,IAC/B;AACA,iBAAa,CAACA,kBAAiB;AAC3B,YAAM,mBAAmBA,gBAAe;AACxC,YAAM,QAAQ,mBAAmB;AACjC,YAAM,IAAI,QAAQ,WAAW;AAC7B,YAAM,IAAI,KAAK,IAAI,cAAc,CAAC,IAAI,KAAK,IAAIA,eAAc,CAAC,IAAI;AAClE,YAAM,IAAI,KAAK,IAAI,CAAC,KAAK;AACzB,YAAM,IAAI,gBAAgB,KAAK,IAAIA,eAAc,CAAC,GAAG,YAAY;AACjE,YAAM,SAAS,CAAC,SAASA,aAAY,IAAI,UAAU,IAAI,KAAK;AAC5D,aAAQ,WAAW,IAAI,KAAK,KAAM;AAAA,IACtC;AAAA,EACJ,OACK;AAID,eAAW,CAACA,kBAAiB;AACzB,YAAM,IAAI,KAAK,IAAI,CAACA,gBAAe,QAAQ;AAC3C,YAAM,KAAKA,gBAAe,YAAY,WAAW;AACjD,aAAO,CAAC,UAAU,IAAI;AAAA,IAC1B;AACA,iBAAa,CAACA,kBAAiB;AAC3B,YAAM,IAAI,KAAK,IAAI,CAACA,gBAAe,QAAQ;AAC3C,YAAM,KAAK,WAAWA,kBAAiB,WAAW;AAClD,aAAO,IAAI;AAAA,IACf;AAAA,EACJ;AACA,QAAM,eAAe,IAAI;AACzB,QAAM,eAAe,gBAAgB,UAAU,YAAY,YAAY;AACvE,aAAW,sBAAsB,QAAQ;AACzC,MAAI,MAAM,YAAY,GAAG;AACrB,WAAO;AAAA,MACH,WAAW,eAAe;AAAA,MAC1B,SAAS,eAAe;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ,OACK;AACD,UAAM,YAAY,KAAK,IAAI,cAAc,CAAC,IAAI;AAC9C,WAAO;AAAA,MACH;AAAA,MACA,SAAS,eAAe,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,iBAAiB;AACvB,SAAS,gBAAgB,UAAU,YAAY,cAAc;AACzD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,aAAS,SAAS,SAAS,MAAM,IAAI,WAAW,MAAM;AAAA,EAC1D;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,cAAc,cAAc;AACjD,SAAO,eAAe,KAAK,KAAK,IAAI,eAAe,YAAY;AACnE;;;ACzEA,IAAM,eAAe,CAAC,YAAY,QAAQ;AAC1C,IAAM,cAAc,CAAC,aAAa,WAAW,MAAM;AACnD,SAAS,aAAa,SAASC,OAAM;AACjC,SAAOA,MAAK,KAAK,CAAC,QAAQ,QAAQ,GAAG,MAAM,MAAS;AACxD;AACA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,gBAAgB;AAAA,IAChB,UAAU,eAAe;AAAA,IACzB,WAAW,eAAe;AAAA,IAC1B,SAAS,eAAe;AAAA,IACxB,MAAM,eAAe;AAAA,IACrB,wBAAwB;AAAA,IACxB,GAAG;AAAA,EACP;AAEA,MAAI,CAAC,aAAa,SAAS,WAAW,KAClC,aAAa,SAAS,YAAY,GAAG;AACrC,QAAI,QAAQ,gBAAgB;AACxB,YAAM,iBAAiB,QAAQ;AAC/B,YAAM,OAAQ,IAAI,KAAK,MAAO,iBAAiB;AAC/C,YAAM,YAAY,OAAO;AACzB,YAAM,UAAU,IACZ,MAAM,MAAM,GAAG,KAAK,QAAQ,UAAU,EAAE,IACxC,KAAK,KAAK,SAAS;AACvB,sBAAgB;AAAA,QACZ,GAAG;AAAA,QACH,MAAM,eAAe;AAAA,QACrB;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,OACK;AACD,YAAM,UAAU,WAAW,OAAO;AAClC,sBAAgB;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,MAAM,eAAe;AAAA,MACzB;AACA,oBAAc,yBAAyB;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,OAAO,0BAA0B,eAAe,gBAAgB,SAAS,eAAe,QAAQ;AACrG,QAAM,UAAU,OAAO,4BAA4B,WAC7C;AAAA,IACE,gBAAgB;AAAA,IAChB,WAAW,CAAC,GAAG,CAAC;AAAA,IAChB;AAAA,EACJ,IACE;AACN,MAAI,EAAE,WAAW,UAAU,IAAI;AAC/B,QAAM,SAAS,QAAQ,UAAU,CAAC;AAClC,QAAM,SAAS,QAAQ,UAAU,QAAQ,UAAU,SAAS,CAAC;AAK7D,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,OAAO;AAC3C,QAAM,EAAE,WAAW,SAAS,MAAM,UAAU,UAAU,uBAAwB,IAAI,iBAAiB;AAAA,IAC/F,GAAG;AAAA,IACH,UAAU,CAAC,sBAAsB,QAAQ,YAAY,CAAC;AAAA,EAC1D,CAAC;AACD,QAAM,kBAAkB,YAAY;AACpC,QAAM,eAAe,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI;AAC9D,QAAM,eAAe,SAAS;AAC9B,QAAM,sBAAsB,sBAAsB,KAAK,KAAK,YAAY,IAAI,CAAC;AAQ7E,QAAM,kBAAkB,KAAK,IAAI,YAAY,IAAI;AACjD,gBAAc,YAAY,kBACpB,eAAe,UAAU,WACzB,eAAe,UAAU;AAC/B,gBAAc,YAAY,kBACpB,eAAe,UAAU,WACzB,eAAe,UAAU;AAC/B,MAAI;AACJ,MAAI,eAAe,GAAG;AAClB,UAAM,cAAc,gBAAgB,qBAAqB,YAAY;AAErE,oBAAgB,CAAC,MAAM;AACnB,YAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,aAAQ,SACJ,aACO,kBACC,eAAe,sBAAsB,gBACrC,cACA,KAAK,IAAI,cAAc,CAAC,IACxB,eAAe,KAAK,IAAI,cAAc,CAAC;AAAA,IACvD;AAAA,EACJ,WACS,iBAAiB,GAAG;AAEzB,oBAAgB,CAAC,MAAM,SACnB,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAC5B,gBACI,kBAAkB,sBAAsB,gBAAgB;AAAA,EACzE,OACK;AAED,UAAM,oBAAoB,sBAAsB,KAAK,KAAK,eAAe,eAAe,CAAC;AACzF,oBAAgB,CAAC,MAAM;AACnB,YAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AAEjE,YAAM,WAAW,KAAK,IAAI,oBAAoB,GAAG,GAAG;AACpD,aAAQ,SACH,aACK,kBACE,eAAe,sBAAsB,gBACrC,KAAK,KAAK,QAAQ,IAClB,oBACI,eACA,KAAK,KAAK,QAAQ,KAC1B;AAAA,IACZ;AAAA,EACJ;AACA,QAAM,YAAY;AAAA,IACd,oBAAoB,yBAAyB,YAAY,OAAO;AAAA,IAChE,MAAM,CAAC,MAAM;AACT,YAAMC,WAAU,cAAc,CAAC;AAC/B,UAAI,CAAC,wBAAwB;AACzB,YAAI,kBAAkB,MAAM,IAAI,kBAAkB;AAMlD,YAAI,eAAe,GAAG;AAClB,4BACI,MAAM,IACA,sBAAsB,eAAe,IACrC,sBAAsB,eAAe,GAAGA,QAAO;AAAA,QAC7D;AACA,cAAM,2BAA2B,KAAK,IAAI,eAAe,KAAK;AAC9D,cAAM,+BAA+B,KAAK,IAAI,SAASA,QAAO,KAAK;AACnE,cAAM,OACF,4BAA4B;AAAA,MACpC,OACK;AACD,cAAM,OAAO,KAAK;AAAA,MACtB;AACA,YAAM,QAAQ,MAAM,OAAO,SAASA;AACpC,aAAO;AAAA,IACX;AAAA,IACA,UAAU,MAAM;AACZ,YAAM,qBAAqB,KAAK,IAAI,sBAAsB,SAAS,GAAG,oBAAoB;AAC1F,YAAM,SAAS,qBAAqB,CAACC,cAAa,UAAU,KAAK,qBAAqBA,SAAQ,EAAE,OAAO,oBAAoB,EAAE;AAC7H,aAAO,qBAAqB,QAAQ;AAAA,IACxC;AAAA,IACA,cAAc,MAAM;AAAA,IAAE;AAAA,EAC1B;AACA,SAAO;AACX;AACA,OAAO,iBAAiB,CAAC,YAAY;AACjC,QAAM,mBAAmB,sBAAsB,SAAS,KAAK,MAAM;AACnE,UAAQ,OAAO,iBAAiB;AAChC,UAAQ,WAAW,sBAAsB,iBAAiB,QAAQ;AAClE,UAAQ,OAAO;AACf,SAAO;AACX;;;ACzKA,SAAS,QAAQ,EAAE,WAAAC,YAAW,WAAW,GAAK,QAAQ,KAAK,eAAe,KAAK,gBAAgB,IAAI,kBAAkB,KAAK,cAAc,KAAK,KAAK,YAAY,KAAK,UAAW,GAAG;AAC7K,QAAM,SAASA,WAAU,CAAC;AAC1B,QAAM,QAAQ;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,EACX;AACA,QAAM,gBAAgB,CAAC,MAAO,QAAQ,UAAa,IAAI,OAAS,QAAQ,UAAa,IAAI;AACzF,QAAM,kBAAkB,CAAC,MAAM;AAC3B,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,QAAQ;AACR,aAAO;AACX,WAAO,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM;AAAA,EACzD;AACA,MAAI,YAAY,QAAQ;AACxB,QAAM,QAAQ,SAAS;AACvB,QAAM,SAAS,iBAAiB,SAAY,QAAQ,aAAa,KAAK;AAKtE,MAAI,WAAW;AACX,gBAAY,SAAS;AACzB,QAAM,YAAY,CAAC,MAAM,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,YAAY;AAChE,QAAM,aAAa,CAAC,MAAM,SAAS,UAAU,CAAC;AAC9C,QAAM,gBAAgB,CAAC,MAAM;AACzB,UAAM,QAAQ,UAAU,CAAC;AACzB,UAAM,SAAS,WAAW,CAAC;AAC3B,UAAM,OAAO,KAAK,IAAI,KAAK,KAAK;AAChC,UAAM,QAAQ,MAAM,OAAO,SAAS;AAAA,EACxC;AAOA,MAAI;AACJ,MAAI;AACJ,QAAM,qBAAqB,CAAC,MAAM;AAC9B,QAAI,CAAC,cAAc,MAAM,KAAK;AAC1B;AACJ,0BAAsB;AACtB,eAAW,OAAO;AAAA,MACd,WAAW,CAAC,MAAM,OAAO,gBAAgB,MAAM,KAAK,CAAC;AAAA,MACrD,UAAU,sBAAsB,YAAY,GAAG,MAAM,KAAK;AAAA;AAAA,MAC1D,SAAS;AAAA,MACT,WAAW;AAAA,MACX;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACA,qBAAmB,CAAC;AACpB,SAAO;AAAA,IACH,oBAAoB;AAAA,IACpB,MAAM,CAAC,MAAM;AAOT,UAAI,kBAAkB;AACtB,UAAI,CAAC,YAAY,wBAAwB,QAAW;AAChD,0BAAkB;AAClB,sBAAc,CAAC;AACf,2BAAmB,CAAC;AAAA,MACxB;AAKA,UAAI,wBAAwB,UAAa,KAAK,qBAAqB;AAC/D,eAAO,SAAS,KAAK,IAAI,mBAAmB;AAAA,MAChD,OACK;AACD,SAAC,mBAAmB,cAAc,CAAC;AACnC,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACjFA,SAAS,aAAa,QAAQC,OAAM,aAAa;AAC7C,QAAM,SAAS,CAAC;AAChB,QAAM,eAAe,eAAe,mBAAmB,OAAO;AAC9D,QAAM,YAAY,OAAO,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,QAAI,QAAQ,aAAa,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACjD,QAAIA,OAAM;AACN,YAAM,iBAAiB,MAAM,QAAQA,KAAI,IAAIA,MAAK,CAAC,KAAK,OAAOA;AAC/D,cAAQ,KAAK,gBAAgB,KAAK;AAAA,IACtC;AACA,WAAO,KAAK,KAAK;AAAA,EACrB;AACA,SAAO;AACX;AAoBA,SAAS,YAAY,OAAO,QAAQ,EAAE,OAAO,UAAU,MAAM,MAAAA,OAAM,MAAM,IAAI,CAAC,GAAG;AAC7E,QAAM,cAAc,MAAM;AAC1B,YAAU,gBAAgB,OAAO,QAAQ,wDAAwD,cAAc;AAK/G,MAAI,gBAAgB;AAChB,WAAO,MAAM,OAAO,CAAC;AACzB,MAAI,gBAAgB,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC;AAC3C,WAAO,MAAM,OAAO,CAAC;AACzB,QAAM,mBAAmB,MAAM,CAAC,MAAM,MAAM,CAAC;AAE7C,MAAI,MAAM,CAAC,IAAI,MAAM,cAAc,CAAC,GAAG;AACnC,YAAQ,CAAC,GAAG,KAAK,EAAE,QAAQ;AAC3B,aAAS,CAAC,GAAG,MAAM,EAAE,QAAQ;AAAA,EACjC;AACA,QAAM,SAAS,aAAa,QAAQA,OAAM,KAAK;AAC/C,QAAM,YAAY,OAAO;AACzB,QAAM,eAAe,CAAC,MAAM;AACxB,QAAI,oBAAoB,IAAI,MAAM,CAAC;AAC/B,aAAO,OAAO,CAAC;AACnB,QAAI,IAAI;AACR,QAAI,YAAY,GAAG;AACf,aAAO,IAAI,MAAM,SAAS,GAAG,KAAK;AAC9B,YAAI,IAAI,MAAM,IAAI,CAAC;AACf;AAAA,MACR;AAAA,IACJ;AACA,UAAM,kBAAkB,SAAS,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC;AAC1D,WAAO,OAAO,CAAC,EAAE,eAAe;AAAA,EACpC;AACA,SAAO,UACD,CAAC,MAAM,aAAa,MAAM,MAAM,CAAC,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,IAC9D;AACV;;;ACpEA,SAAS,WAAW,QAAQ,WAAW;AACnC,QAAM,MAAM,OAAO,OAAO,SAAS,CAAC;AACpC,WAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACjC,UAAM,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC/C,WAAO,KAAK,UAAU,KAAK,GAAG,cAAc,CAAC;AAAA,EACjD;AACJ;;;ACPA,SAAS,cAAc,KAAK;AACxB,QAAM,SAAS,CAAC,CAAC;AACjB,aAAW,QAAQ,IAAI,SAAS,CAAC;AACjC,SAAO;AACX;;;ACNA,SAAS,qBAAqB,QAAQ,UAAU;AAC5C,SAAO,OAAO,IAAI,CAAC,MAAM,IAAI,QAAQ;AACzC;;;ACGA,SAAS,cAAc,QAAQ,QAAQ;AACnC,SAAO,OAAO,IAAI,MAAM,UAAU,SAAS,EAAE,OAAO,GAAG,OAAO,SAAS,CAAC;AAC5E;AACA,SAAS,UAAU,EAAE,WAAW,KAAK,WAAW,gBAAgB,OAAO,MAAAC,QAAO,YAAa,GAAG;AAK1F,QAAM,kBAAkB,cAAcA,KAAI,IACpCA,MAAK,IAAI,0BAA0B,IACnC,2BAA2BA,KAAI;AAKrC,QAAM,QAAQ;AAAA,IACV,MAAM;AAAA,IACN,OAAO,eAAe,CAAC;AAAA,EAC3B;AAIA,QAAM,gBAAgB;AAAA;AAAA;AAAA,IAGtB,SAAS,MAAM,WAAW,eAAe,SACnC,QACA,cAAc,cAAc;AAAA,IAAG;AAAA,EAAQ;AAC7C,QAAM,oBAAoB,YAAY,eAAe,gBAAgB;AAAA,IACjE,MAAM,MAAM,QAAQ,eAAe,IAC7B,kBACA,cAAc,gBAAgB,eAAe;AAAA,EACvD,CAAC;AACD,SAAO;AAAA,IACH,oBAAoB;AAAA,IACpB,MAAM,CAAC,MAAM;AACT,YAAM,QAAQ,kBAAkB,CAAC;AACjC,YAAM,OAAO,KAAK;AAClB,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;;;AC9CA,IAAM,YAAY,CAAC,UAAU,UAAU;AACvC,SAAS,iBAAiBC,YAAW,EAAE,QAAQ,aAAa,OAAO,GAAG,eAAe,QAAQ,GAAG;AAC5F,QAAM,oBAAoBA,WAAU,OAAO,SAAS;AACpD,QAAM,mBAAmB,QAAQ,KAAM,UAAU,eAAe,UAAU,SAAS,MAAM;AACzF,QAAM,QAAQ,mBAAmB,IAAI,kBAAkB,SAAS;AAChE,SAAO,CAAC,SAAS,kBAAkB,SAC7B,kBAAkB,KAAK,IACvB;AACV;;;ACJA,IAAM,oBAAoB;AAAA,EACtB,OAAO;AAAA,EACP;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AACJ;AACA,SAAS,sBAAsB,YAAY;AACvC,MAAI,OAAO,WAAW,SAAS,UAAU;AACrC,eAAW,OAAO,kBAAkB,WAAW,IAAI;AAAA,EACvD;AACJ;;;ACfA,IAAM,cAAN,MAAkB;AAAA,EACd,cAAc;AACV,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,iBAAiB;AACb,SAAK,YAAY,IAAI,QAAQ,CAAC,YAAY;AACtC,WAAK,UAAU;AAAA,IACnB,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB;AACb,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,WAAW,UAAU;AACtB,WAAO,KAAK,SAAS,KAAK,WAAW,QAAQ;AAAA,EACjD;AACJ;;;ACXA,IAAM,oBAAoB,CAACC,aAAYA,WAAU;AACjD,IAAM,cAAN,cAA0B,YAAY;AAAA,EAClC,YAAY,SAAS;AACjB,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAIjB,SAAK,cAAc;AAInB,SAAK,WAAW;AAIhB,SAAK,gBAAgB;AAKrB,SAAK,OAAO,MAAM;AACd,YAAM,EAAE,aAAAC,aAAY,IAAI,KAAK;AAC7B,UAAIA,gBAAeA,aAAY,cAAc,KAAK,IAAI,GAAG;AACrD,aAAK,KAAK,KAAK,IAAI,CAAC;AAAA,MACxB;AACA,WAAK,YAAY;AACjB,UAAI,KAAK,UAAU;AACf;AACJ,WAAK,SAAS;AACd,WAAK,QAAQ,SAAS;AAAA,IAC1B;AACA,qBAAiB;AACjB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,KAAK;AACV,QAAI,QAAQ,aAAa;AACrB,WAAK,MAAM;AAAA,EACnB;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,QAAQ,IAAI;AACpB,0BAAsB,OAAO;AAC7B,UAAM,EAAE,OAAO,WAAW,SAAS,GAAG,cAAc,GAAG,YAAY,WAAW,EAAG,IAAI;AACrF,QAAI,EAAE,WAAW,YAAY,IAAI;AACjC,UAAM,mBAAmB,QAAQ;AACjC,QACI,qBAAqB,WAAW;AAChC,gBAAU,YAAY,UAAU,GAAG,gGAAgG,WAAW,IAAI,mBAAmB;AAAA,IACzK;AACA,QAAI,qBAAqB,aACrB,OAAO,YAAY,CAAC,MAAM,UAAU;AACpC,WAAK,eAAe,KAAK,mBAAmB,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAC/E,oBAAc,CAAC,GAAG,GAAG;AAAA,IACzB;AACA,UAAM,YAAY,iBAAiB,EAAE,GAAG,SAAS,WAAW,YAAY,CAAC;AAKzE,QAAI,eAAe,UAAU;AACzB,WAAK,oBAAoB,iBAAiB;AAAA,QACtC,GAAG;AAAA,QACH,WAAW,CAAC,GAAG,WAAW,EAAE,QAAQ;AAAA,QACpC,UAAU,CAAC;AAAA,MACf,CAAC;AAAA,IACL;AASA,QAAI,UAAU,uBAAuB,MAAM;AACvC,gBAAU,qBAAqB,sBAAsB,SAAS;AAAA,IAClE;AACA,UAAM,EAAE,mBAAmB,IAAI;AAC/B,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB,qBAAqB;AAC7C,SAAK,gBAAgB,KAAK,oBAAoB,SAAS,KAAK;AAC5D,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,WAAW,WAAW;AAClB,UAAM,gBAAgB,KAAK,MAAM,YAAY,KAAK,SAAS,IAAI,KAAK;AAEpE,QAAI,KAAK,aAAa,MAAM;AACxB,WAAK,cAAc,KAAK;AAAA,IAC5B,OACK;AAID,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,KAAK,WAAW,SAAS,OAAO;AAC5B,UAAM,EAAE,WAAW,eAAe,cAAc,mBAAmB,kBAAkB,mBAAoB,IAAI;AAC7G,QAAI,KAAK,cAAc;AACnB,aAAO,UAAU,KAAK,CAAC;AAC3B,UAAM,EAAE,OAAAC,SAAQ,GAAG,WAAAC,YAAW,QAAQ,YAAY,aAAa,MAAM,UAAU,cAAe,IAAI,KAAK;AAOvG,QAAI,KAAK,QAAQ,GAAG;AAChB,WAAK,YAAY,KAAK,IAAI,KAAK,WAAW,SAAS;AAAA,IACvD,WACS,KAAK,QAAQ,GAAG;AACrB,WAAK,YAAY,KAAK,IAAI,YAAY,gBAAgB,KAAK,OAAO,KAAK,SAAS;AAAA,IACpF;AACA,QAAI,QAAQ;AACR,WAAK,cAAc;AAAA,IACvB,OACK;AACD,WAAK,WAAW,SAAS;AAAA,IAC7B;AAEA,UAAM,mBAAmB,KAAK,cAAcD,UAAS,KAAK,iBAAiB,IAAI,IAAI;AACnF,UAAM,iBAAiB,KAAK,iBAAiB,IACvC,mBAAmB,IACnB,mBAAmB;AACzB,SAAK,cAAc,KAAK,IAAI,kBAAkB,CAAC;AAE/C,QAAI,KAAK,UAAU,cAAc,KAAK,aAAa,MAAM;AACrD,WAAK,cAAc;AAAA,IACvB;AACA,QAAI,UAAU,KAAK;AACnB,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AAMR,YAAME,YAAW,KAAK,IAAI,KAAK,aAAa,aAAa,IAAI;AAK7D,UAAI,mBAAmB,KAAK,MAAMA,SAAQ;AAK1C,UAAI,oBAAoBA,YAAW;AAKnC,UAAI,CAAC,qBAAqBA,aAAY,GAAG;AACrC,4BAAoB;AAAA,MACxB;AACA,4BAAsB,KAAK;AAC3B,yBAAmB,KAAK,IAAI,kBAAkB,SAAS,CAAC;AAIxD,YAAM,iBAAiB,QAAQ,mBAAmB,CAAC;AACnD,UAAI,gBAAgB;AAChB,YAAI,eAAe,WAAW;AAC1B,8BAAoB,IAAI;AACxB,cAAI,aAAa;AACb,iCAAqB,cAAc;AAAA,UACvC;AAAA,QACJ,WACS,eAAe,UAAU;AAC9B,2BAAiB;AAAA,QACrB;AAAA,MACJ;AACA,gBAAU,MAAM,GAAG,GAAG,iBAAiB,IAAI;AAAA,IAC/C;AAMA,UAAM,QAAQ,iBACR,EAAE,MAAM,OAAO,OAAOD,WAAU,CAAC,EAAE,IACnC,eAAe,KAAK,OAAO;AACjC,QAAI,cAAc;AACd,YAAM,QAAQ,aAAa,MAAM,KAAK;AAAA,IAC1C;AACA,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,CAAC,kBAAkB,uBAAuB,MAAM;AAChD,aACI,KAAK,iBAAiB,IAChB,KAAK,eAAe,gBACpB,KAAK,eAAe;AAAA,IAClC;AACA,UAAM,sBAAsB,KAAK,aAAa,SACzC,KAAK,UAAU,cAAe,KAAK,UAAU,aAAa;AAE/D,QAAI,uBAAuB,SAAS,SAAS;AACzC,YAAM,QAAQ,iBAAiBA,YAAW,KAAK,SAAS,eAAe,KAAK,KAAK;AAAA,IACrF;AACA,QAAI,UAAU;AACV,eAAS,MAAM,KAAK;AAAA,IACxB;AACA,QAAI,qBAAqB;AACrB,WAAK,OAAO;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,SAAS,QAAQ;AAClB,WAAO,KAAK,SAAS,KAAK,SAAS,MAAM;AAAA,EAC7C;AAAA,EACA,IAAI,WAAW;AACX,WAAO,sBAAsB,KAAK,kBAAkB;AAAA,EACxD;AAAA,EACA,IAAI,OAAO;AACP,WAAO,sBAAsB,KAAK,WAAW;AAAA,EACjD;AAAA,EACA,IAAI,KAAK,SAAS;AACd,cAAU,sBAAsB,OAAO;AACvC,SAAK,cAAc;AACnB,QAAI,KAAK,cAAc,QACnB,KAAK,aAAa,QAClB,KAAK,kBAAkB,GAAG;AAC1B,WAAK,WAAW;AAAA,IACpB,WACS,KAAK,QAAQ;AAClB,WAAK,YAAY,KAAK,OAAO,IAAI,IAAI,UAAU,KAAK;AAAA,IACxD;AACA,SAAK,QAAQ,MAAM,KAAK;AAAA,EAC5B;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,MAAM,UAAU;AAChB,SAAK,WAAW,KAAK,IAAI,CAAC;AAC1B,UAAM,aAAa,KAAK,kBAAkB;AAC1C,SAAK,gBAAgB;AACrB,QAAI,YAAY;AACZ,WAAK,OAAO,sBAAsB,KAAK,WAAW;AAAA,IACtD;AAAA,EACJ;AAAA,EACA,OAAO;AACH,QAAI,KAAK;AACL;AACJ,UAAM,EAAE,SAAS,iBAAiB,UAAU,IAAI,KAAK;AACrD,QAAI,CAAC,KAAK,QAAQ;AACd,WAAK,SAAS,OAAO,CAAC,cAAc,KAAK,KAAK,SAAS,CAAC;AAAA,IAC5D;AACA,SAAK,QAAQ,SAAS;AACtB,UAAME,OAAM,KAAK,OAAO,IAAI;AAC5B,QAAI,KAAK,UAAU,YAAY;AAC3B,WAAK,eAAe;AACpB,WAAK,YAAYA;AAAA,IACrB,WACS,KAAK,aAAa,MAAM;AAC7B,WAAK,YAAYA,OAAM,KAAK;AAAA,IAChC,WACS,CAAC,KAAK,WAAW;AACtB,WAAK,YAAY,aAAaA;AAAA,IAClC;AACA,QAAI,KAAK,UAAU,cAAc,KAAK,QAAQ,GAAG;AAC7C,WAAK,aAAa,KAAK;AAAA,IAC3B;AACA,SAAK,WAAW;AAKhB,SAAK,QAAQ;AACb,SAAK,OAAO,MAAM;AAAA,EACtB;AAAA,EACA,QAAQ;AACJ,SAAK,QAAQ;AACb,SAAK,WAAW,KAAK,IAAI,CAAC;AAC1B,SAAK,WAAW,KAAK;AAAA,EACzB;AAAA,EACA,WAAW;AACP,QAAI,KAAK,UAAU,WAAW;AAC1B,WAAK,KAAK;AAAA,IACd;AACA,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,SAAS;AACL,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ,aAAa;AAAA,EAC9B;AAAA,EACA,SAAS;AACL,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,KAAK,CAAC;AACX,SAAK,SAAS;AACd,SAAK,QAAQ,WAAW;AAAA,EAC5B;AAAA,EACA,WAAW;AACP,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY,KAAK,WAAW;AACjC,qBAAiB;AAAA,EACrB;AAAA,EACA,aAAa;AACT,QAAI,CAAC,KAAK;AACN;AACJ,SAAK,OAAO,KAAK;AACjB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,OAAO,YAAY;AACf,SAAK,YAAY;AACjB,WAAO,KAAK,KAAK,YAAY,IAAI;AAAA,EACrC;AAAA,EACA,eAAe,UAAU;AACrB,QAAI,KAAK,QAAQ,cAAc;AAC3B,WAAK,QAAQ,OAAO;AACpB,WAAK,QAAQ,OAAO;AACpB,WAAK,cAAc;AAAA,IACvB;AACA,SAAK,QAAQ,KAAK;AAClB,WAAO,SAAS,QAAQ,IAAI;AAAA,EAChC;AACJ;AAEA,SAAS,aAAa,SAAS;AAC3B,SAAO,IAAI,YAAY,OAAO;AAClC;;;ACtVA,SAAS,cAAcC,YAAW;AAC9B,WAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,IAAAA,WAAU,CAAC,MAAMA,WAAU,CAAC,IAAIA,WAAU,IAAI,CAAC;AAAA,EACnD;AACJ;;;ACJA,IAAM,WAAW,CAAC,QAAS,MAAM,MAAO,KAAK;AAC7C,IAAM,SAAS,CAAC,MAAM;AAClB,QAAM,QAAQ,SAAS,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC7C,SAAO,YAAY,KAAK;AAC5B;AACA,IAAM,kBAAkB;AAAA,EACpB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK;AAAA,EAClD;AAAA,EACA,SAAS;AAAA,EACT,OAAO,CAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;AAAA,EACtC,OAAO,CAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;AAAA,EACtC,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK;AACrD;AACA,IAAM,cAAc,CAAC,UAAU;AAC3B,UAAQ,QAAQ;AAChB,MAAI,QAAQ;AACR,aAAS;AACb,SAAO;AACX;AACA,IAAM,UAAU;AAChB,IAAM,SAAS,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACzD,IAAM,SAAS,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACzD,IAAM,kBAAkB;AAAA,EACpB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,OAAO,CAAC,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK;AAAA,EACxC,SAAS,CAAC,MAAM,YAAY,SAAS,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,EAC5D,SAAS,CAAC,MAAM,YAAY,SAAS,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,EAC7D;AAAA,EACA,QAAQ;AAAA,EACR,OAAO,CAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;AAAA,EACtC,OAAO,CAAC,MAAM,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;AAAA,EACtC,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,KAAK;AACrD;AACA,SAAS,sBAAsB,MAAM;AACjC,SAAO,KAAK,SAAS,OAAO,IAAI,IAAI;AACxC;AACA,SAAS,wBAAwBC,YAAW,MAAM;AAC9C,MAAI,CAACA,cAAaA,eAAc,QAAQ;AACpC,WAAO,sBAAsB,IAAI;AAAA,EACrC;AACA,QAAM,gBAAgBA,WAAU,MAAM,8BAA8B;AACpE,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACf,cAAU;AACV,YAAQ;AAAA,EACZ,OACK;AACD,UAAM,gBAAgBA,WAAU,MAAM,4BAA4B;AAClE,cAAU;AACV,YAAQ;AAAA,EACZ;AACA,MAAI,CAAC,OAAO;AACR,WAAO,sBAAsB,IAAI;AAAA,EACrC;AACA,QAAM,cAAc,QAAQ,IAAI;AAChC,QAAM,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,wBAAwB;AAC/D,SAAO,OAAO,gBAAgB,aACxB,YAAY,MAAM,IAClB,OAAO,WAAW;AAC5B;AACA,IAAM,qBAAqB,CAAC,UAAU,SAAS;AAC3C,QAAM,EAAE,WAAAA,aAAY,OAAO,IAAI,iBAAiB,QAAQ;AACxD,SAAO,wBAAwBA,YAAW,IAAI;AAClD;AACA,SAAS,yBAAyB,OAAO;AACrC,SAAO,WAAW,MAAM,KAAK,CAAC;AAClC;;;AC7EA,IAAM,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAIA,IAAM,kBAAgC,MAAM,IAAI,IAAI,kBAAkB,GAAG;;;ACpBzE,IAAM,gBAAgB,CAAC,MAAM,MAAM,UAAU,MAAM;AACnD,IAAM,gBAAgB,oBAAI,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7C,IAAM,gCAAgC,mBAAmB,OAAO,CAAC,QAAQ,CAAC,cAAc,IAAI,GAAG,CAAC;AAChG,SAAS,gCAAgC,eAAe;AACpD,QAAM,oBAAoB,CAAC;AAC3B,gCAA8B,QAAQ,CAAC,QAAQ;AAC3C,UAAM,QAAQ,cAAc,SAAS,GAAG;AACxC,QAAI,UAAU,QAAW;AACrB,wBAAkB,KAAK,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC;AACzC,YAAM,IAAI,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,IAC7C;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAM,mBAAmB;AAAA;AAAA,EAErB,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,cAAc,KAAK,eAAe,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,WAAW,IAAI,WAAW,YAAY;AAAA,EAC9H,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,aAAa,KAAK,gBAAgB,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,UAAU,IAAI,WAAW,aAAa;AAAA,EAC/H,KAAK,CAAC,OAAO,EAAE,IAAI,MAAM,WAAW,GAAG;AAAA,EACvC,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,WAAW,IAAI;AAAA,EAC1C,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,MAAM,WAAW,GAAG,KAAK,EAAE,MAAM,EAAE;AAAA,EACzD,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,MAAM,WAAW,IAAI,KAAK,EAAE,MAAM,EAAE;AAAA;AAAA,EAE1D,GAAG,CAAC,OAAO,EAAE,WAAAC,WAAU,MAAM,wBAAwBA,YAAW,GAAG;AAAA,EACnE,GAAG,CAAC,OAAO,EAAE,WAAAA,WAAU,MAAM,wBAAwBA,YAAW,GAAG;AACvE;AAEA,iBAAiB,aAAa,iBAAiB;AAC/C,iBAAiB,aAAa,iBAAiB;;;AC7B/C,IAAM,YAAY,oBAAI,IAAI;AAC1B,IAAI,cAAc;AAClB,IAAI,sBAAsB;AAC1B,IAAI,WAAW;AACf,SAAS,sBAAsB;AAC3B,MAAI,qBAAqB;AACrB,UAAM,qBAAqB,MAAM,KAAK,SAAS,EAAE,OAAO,CAAC,aAAa,SAAS,gBAAgB;AAC/F,UAAM,oBAAoB,IAAI,IAAI,mBAAmB,IAAI,CAAC,aAAa,SAAS,OAAO,CAAC;AACxF,UAAM,sBAAsB,oBAAI,IAAI;AAKpC,sBAAkB,QAAQ,CAAC,YAAY;AACnC,YAAM,oBAAoB,gCAAgC,OAAO;AACjE,UAAI,CAAC,kBAAkB;AACnB;AACJ,0BAAoB,IAAI,SAAS,iBAAiB;AAClD,cAAQ,OAAO;AAAA,IACnB,CAAC;AAED,uBAAmB,QAAQ,CAAC,aAAa,SAAS,oBAAoB,CAAC;AAEvE,sBAAkB,QAAQ,CAAC,YAAY;AACnC,cAAQ,OAAO;AACf,YAAM,UAAU,oBAAoB,IAAI,OAAO;AAC/C,UAAI,SAAS;AACT,gBAAQ,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC9B,kBAAQ,SAAS,GAAG,GAAG,IAAI,KAAK;AAAA,QACpC,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAED,uBAAmB,QAAQ,CAAC,aAAa,SAAS,gBAAgB,CAAC;AAEnE,uBAAmB,QAAQ,CAAC,aAAa;AACrC,UAAI,SAAS,qBAAqB,QAAW;AACzC,eAAO,SAAS,GAAG,SAAS,gBAAgB;AAAA,MAChD;AAAA,IACJ,CAAC;AAAA,EACL;AACA,wBAAsB;AACtB,gBAAc;AACd,YAAU,QAAQ,CAAC,aAAa,SAAS,SAAS,QAAQ,CAAC;AAC3D,YAAU,MAAM;AACpB;AACA,SAAS,mBAAmB;AACxB,YAAU,QAAQ,CAAC,aAAa;AAC5B,aAAS,cAAc;AACvB,QAAI,SAAS,kBAAkB;AAC3B,4BAAsB;AAAA,IAC1B;AAAA,EACJ,CAAC;AACL;AACA,SAAS,yBAAyB;AAC9B,aAAW;AACX,mBAAiB;AACjB,sBAAoB;AACpB,aAAW;AACf;AACA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,qBAAqB,YAAY,MAAMC,cAAa,SAAS,UAAU,OAAO;AACtF,SAAK,QAAQ;AAMb,SAAK,UAAU;AAKf,SAAK,mBAAmB;AACxB,SAAK,sBAAsB,CAAC,GAAG,mBAAmB;AAClD,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,cAAcA;AACnB,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,kBAAkB;AACd,SAAK,QAAQ;AACb,QAAI,KAAK,SAAS;AACd,gBAAU,IAAI,IAAI;AAClB,UAAI,CAAC,aAAa;AACd,sBAAc;AACd,cAAM,KAAK,gBAAgB;AAC3B,cAAM,iBAAiB,mBAAmB;AAAA,MAC9C;AAAA,IACJ,OACK;AACD,WAAK,cAAc;AACnB,WAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,qBAAqB,MAAM,SAAS,aAAAA,aAAY,IAAI;AAE5D,QAAI,oBAAoB,CAAC,MAAM,MAAM;AACjC,YAAM,eAAeA,cAAa,IAAI;AAEtC,YAAM,gBAAgB,oBAAoB,oBAAoB,SAAS,CAAC;AACxE,UAAI,iBAAiB,QAAW;AAC5B,4BAAoB,CAAC,IAAI;AAAA,MAC7B,WACS,WAAW,MAAM;AACtB,cAAM,cAAc,QAAQ,UAAU,MAAM,aAAa;AACzD,YAAI,gBAAgB,UAAa,gBAAgB,MAAM;AACnD,8BAAoB,CAAC,IAAI;AAAA,QAC7B;AAAA,MACJ;AACA,UAAI,oBAAoB,CAAC,MAAM,QAAW;AACtC,4BAAoB,CAAC,IAAI;AAAA,MAC7B;AACA,UAAIA,gBAAe,iBAAiB,QAAW;AAC3C,QAAAA,aAAY,IAAI,oBAAoB,CAAC,CAAC;AAAA,MAC1C;AAAA,IACJ;AACA,kBAAc,mBAAmB;AAAA,EACrC;AAAA,EACA,mBAAmB;AAAA,EAAE;AAAA,EACrB,sBAAsB;AAAA,EAAE;AAAA,EACxB,kBAAkB;AAAA,EAAE;AAAA,EACpB,kBAAkB;AAAA,EAAE;AAAA,EACpB,SAAS,mBAAmB,OAAO;AAC/B,SAAK,QAAQ;AACb,SAAK,WAAW,KAAK,qBAAqB,KAAK,eAAe,gBAAgB;AAC9E,cAAU,OAAO,IAAI;AAAA,EACzB;AAAA,EACA,SAAS;AACL,QAAI,KAAK,UAAU,aAAa;AAC5B,gBAAU,OAAO,IAAI;AACrB,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,KAAK,UAAU;AACf,WAAK,gBAAgB;AAAA,EAC7B;AACJ;;;AChJA,IAAM,WAAW,CAAC,SAAS,KAAK,WAAW,IAAI;;;ACE/C,SAAS,SAAS,SAAS,MAAM,OAAO;AACpC,WAAS,IAAI,IACP,QAAQ,MAAM,YAAY,MAAM,KAAK,IACpC,QAAQ,MAAM,IAAI,IAAI;AACjC;;;ACJA,IAAM,yBAAyC,KAAK,MAAM,OAAO,mBAAmB,MAAS;;;ACE7F,IAAM,gBAAgB,CAAC;;;ACDvB,SAAS,aAAa,UAAU,cAAc;AAC1C,QAAM,WAAW,KAAK,QAAQ;AAC9B,SAAO,MAAM,cAAc,YAAY,KAAK,SAAS;AACzD;;;ACJA,IAAM,uBAAqC,aAAa,MAAM;AAC1D,MAAI;AACA,aACK,cAAc,KAAK,EACnB,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,EAC3D,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACA,SAAO;AACX,GAAG,cAAc;;;ACZjB,IAAM,sBAAsB,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;;;ACEjF,IAAM,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAsB,oBAAoB,CAAC,GAAG,MAAM,MAAM,CAAC,CAAC;AAAA,EAC5D,SAAuB,oBAAoB,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,EAC7D,QAAsB,oBAAoB,CAAC,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,EACnE,SAAuB,oBAAoB,CAAC,MAAM,MAAM,MAAM,IAAI,CAAC;AACvE;;;ACNA,SAAS,wBAAwB,QAAQ,UAAU;AAC/C,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX,WACS,OAAO,WAAW,YAAY;AACnC,WAAO,qBAAqB,IACtB,qBAAqB,QAAQ,QAAQ,IACrC;AAAA,EACV,WACS,mBAAmB,MAAM,GAAG;AACjC,WAAO,oBAAoB,MAAM;AAAA,EACrC,WACS,MAAM,QAAQ,MAAM,GAAG;AAC5B,WAAO,OAAO,IAAI,CAAC,kBAAkB,wBAAwB,eAAe,QAAQ,KAChF,qBAAqB,OAAO;AAAA,EACpC,OACK;AACD,WAAO,qBAAqB,MAAM;AAAA,EACtC;AACJ;;;ACrBA,SAAS,oBAAoB,SAAS,WAAWC,YAAW,EAAE,OAAAC,SAAQ,GAAG,WAAW,KAAK,SAAS,GAAG,aAAa,QAAQ,MAAAC,QAAO,WAAW,MAAO,IAAI,CAAC,GAAG,gBAAgB,QAAW;AAClL,QAAM,kBAAkB;AAAA,IACpB,CAAC,SAAS,GAAGF;AAAA,EACjB;AACA,MAAI;AACA,oBAAgB,SAAS;AAC7B,QAAM,SAAS,wBAAwBE,OAAM,QAAQ;AAIrD,MAAI,MAAM,QAAQ,MAAM;AACpB,oBAAgB,SAAS;AAC7B,MAAI,YAAY,OAAO;AACnB,qBAAiB;AAAA,EACrB;AACA,QAAM,UAAU;AAAA,IACZ,OAAAD;AAAA,IACA;AAAA,IACA,QAAQ,CAAC,MAAM,QAAQ,MAAM,IAAI,SAAS;AAAA,IAC1C,MAAM;AAAA,IACN,YAAY,SAAS;AAAA,IACrB,WAAW,eAAe,YAAY,cAAc;AAAA,EACxD;AACA,MAAI;AACA,YAAQ,gBAAgB;AAC5B,QAAM,YAAY,QAAQ,QAAQ,iBAAiB,OAAO;AAC1D,MAAI,YAAY,OAAO;AACnB,cAAU,SAAS,QAAQ,MAAM;AAC7B,uBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,SAAO;AACX;;;ACpCA,SAAS,YAAY,MAAM;AACvB,SAAO,OAAO,SAAS,cAAc,oBAAoB;AAC7D;;;ACCA,SAAS,sBAAsB,EAAE,MAAM,GAAG,QAAQ,GAAG;AACjD,MAAI,YAAY,IAAI,KAAK,qBAAqB,GAAG;AAC7C,WAAO,KAAK,eAAe,OAAO;AAAA,EACtC,OACK;AACD,YAAQ,aAAa,QAAQ,WAAW;AACxC,YAAQ,SAAS,QAAQ,OAAO;AAAA,EACpC;AACA,SAAO;AACX;;;ACDA,IAAM,kBAAN,cAA8B,YAAY;AAAA,EACtC,YAAY,SAAS;AACjB,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,QAAI,CAAC;AACD;AACJ,UAAM,EAAE,SAAS,MAAM,WAAAE,YAAW,eAAe,eAAe,OAAO,eAAe,WAAY,IAAI;AACtG,SAAK,kBAAkB,QAAQ,aAAa;AAC5C,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,cAAU,OAAO,QAAQ,SAAS,UAAU,sDAAsD,aAAa;AAC/G,UAAM,aAAa,sBAAsB,OAAO;AAChD,SAAK,YAAY,oBAAoB,SAAS,MAAMA,YAAW,YAAY,aAAa;AACxF,QAAI,WAAW,aAAa,OAAO;AAC/B,WAAK,UAAU,MAAM;AAAA,IACzB;AACA,SAAK,UAAU,WAAW,MAAM;AAC5B,WAAK,eAAe,KAAK;AACzB,UAAI,CAAC,eAAe;AAChB,cAAM,WAAW,iBAAiBA,YAAW,KAAK,SAAS,eAAe,KAAK,KAAK;AACpF,YAAI,KAAK,mBAAmB;AACxB,eAAK,kBAAkB,QAAQ;AAAA,QACnC,OACK;AAKD,mBAAS,SAAS,MAAM,QAAQ;AAAA,QACpC;AACA,aAAK,UAAU,OAAO;AAAA,MAC1B;AACA,mBAAa;AACb,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,OAAO;AACH,QAAI,KAAK;AACL;AACJ,SAAK,UAAU,KAAK;AACpB,QAAI,KAAK,UAAU,YAAY;AAC3B,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,SAAK,UAAU,MAAM;AAAA,EACzB;AAAA,EACA,WAAW;AACP,SAAK,UAAU,SAAS;AAAA,EAC5B;AAAA,EACA,SAAS;AACL,QAAI;AACA,WAAK,UAAU,OAAO;AAAA,IAC1B,SACO,GAAG;AAAA,IAAE;AAAA,EAChB;AAAA,EACA,OAAO;AACH,QAAI,KAAK;AACL;AACJ,SAAK,YAAY;AACjB,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,UAAU,UAAU,UAAU,YAAY;AAC1C;AAAA,IACJ;AACA,QAAI,KAAK,mBAAmB;AACxB,WAAK,kBAAkB;AAAA,IAC3B,OACK;AACD,WAAK,aAAa;AAAA,IACtB;AACA,QAAI,CAAC,KAAK;AACN,WAAK,OAAO;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,eAAe;AACX,QAAI,CAAC,KAAK,iBAAiB;AACvB,WAAK,UAAU,eAAe;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,IAAI,WAAW;AACX,UAAM,WAAW,KAAK,UAAU,QAAQ,oBAAoB,EAAE,YAAY;AAC1E,WAAO,sBAAsB,OAAO,QAAQ,CAAC;AAAA,EACjD;AAAA,EACA,IAAI,OAAO;AACP,WAAO,sBAAsB,OAAO,KAAK,UAAU,WAAW,KAAK,CAAC;AAAA,EACxE;AAAA,EACA,IAAI,KAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,UAAU,cAAc,sBAAsB,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACR,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,IAAI,MAAM,UAAU;AAEhB,QAAI,WAAW;AACX,WAAK,eAAe;AACxB,SAAK,UAAU,eAAe;AAAA,EAClC;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,iBAAiB,OACvB,aACA,KAAK,UAAU;AAAA,EACzB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,OAAO,KAAK,UAAU,SAAS;AAAA,EAC1C;AAAA,EACA,IAAI,UAAU,cAAc;AACxB,SAAK,UAAU,YAAY;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,EAAE,UAAU,QAAQ,GAAG;AAClC,QAAI,KAAK,cAAc;AACnB,WAAK,UAAU,QAAQ,aAAa,EAAE,QAAQ,SAAS,CAAC;AAAA,IAC5D;AACA,SAAK,UAAU,WAAW;AAC1B,QAAI,YAAY,uBAAuB,GAAG;AACtC,WAAK,UAAU,WAAW;AAC1B,aAAO;AAAA,IACX,OACK;AACD,aAAO,QAAQ,IAAI;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACvJA,IAAM,6BAA6B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,kBAAkB,KAAK;AAC5B,SAAO,OAAO;AAClB;AACA,SAAS,oBAAoB,YAAY;AACrC,MAAI,OAAO,WAAW,SAAS,YAC3B,kBAAkB,WAAW,IAAI,GAAG;AACpC,eAAW,OAAO,2BAA2B,WAAW,IAAI;AAAA,EAChE;AACJ;;;ACJA,IAAM,cAAc;AACpB,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EAClD,YAAY,SAAS;AAUjB,wBAAoB,OAAO;AAQ3B,0BAAsB,OAAO;AAC7B,UAAM,OAAO;AACb,QAAI,QAAQ,WAAW;AACnB,WAAK,YAAY,QAAQ;AAAA,IAC7B;AACA,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB,OAAO;AACrB,UAAM,EAAE,aAAAC,cAAa,UAAU,YAAY,SAAS,GAAG,QAAQ,IAAI,KAAK;AACxE,QAAI,CAACA;AACD;AACJ,QAAI,UAAU,QAAW;AACrB,MAAAA,aAAY,IAAI,KAAK;AACrB;AAAA,IACJ;AACA,UAAM,kBAAkB,IAAI,YAAY;AAAA,MACpC,GAAG;AAAA,MACH,UAAU;AAAA,IACd,CAAC;AACD,UAAM,aAAa,sBAAsB,KAAK,gBAAgB,KAAK,IAAI;AACvE,IAAAA,aAAY,gBAAgB,gBAAgB,OAAO,aAAa,WAAW,EAAE,OAAO,gBAAgB,OAAO,UAAU,EAAE,OAAO,WAAW;AACzI,oBAAgB,KAAK;AAAA,EACzB;AACJ;;;ACnDA,IAAM,eAAe,CAAC,OAAO,SAAS;AAElC,MAAI,SAAS;AACT,WAAO;AAIX,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK;AAChD,WAAO;AACX,MAAI,OAAO,UAAU;AAAA,GAChB,QAAQ,KAAK,KAAK,KAAK,UAAU;AAAA,EAClC,CAAC,MAAM,WAAW,MAAM,GAC1B;AACE,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACvBA,SAAS,oBAAoBC,YAAW;AACpC,QAAMC,WAAUD,WAAU,CAAC;AAC3B,MAAIA,WAAU,WAAW;AACrB,WAAO;AACX,WAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,QAAIA,WAAU,CAAC,MAAMC;AACjB,aAAO;AAAA,EACf;AACJ;AACA,SAAS,WAAWD,YAAW,MAAM,MAAM,UAAU;AAMjD,QAAM,iBAAiBA,WAAU,CAAC;AAClC,MAAI,mBAAmB;AACnB,WAAO;AAMX,MAAI,SAAS,aAAa,SAAS;AAC/B,WAAO;AACX,QAAM,iBAAiBA,WAAUA,WAAU,SAAS,CAAC;AACrD,QAAM,qBAAqB,aAAa,gBAAgB,IAAI;AAC5D,QAAM,qBAAqB,aAAa,gBAAgB,IAAI;AAC5D,UAAQ,uBAAuB,oBAAoB,6BAA6B,IAAI,UAAU,cAAc,SAAS,cAAc,OAAO,qBAAqB,iBAAiB,cAAc,iCAAiC,sBAAsB;AAErP,MAAI,CAAC,sBAAsB,CAAC,oBAAoB;AAC5C,WAAO;AAAA,EACX;AACA,SAAQ,oBAAoBA,UAAS,MAC/B,SAAS,YAAY,YAAY,IAAI,MAAM;AACrD;;;ACvCA,SAAS,qBAAqB,SAAS;AACnC,UAAQ,WAAW;AACnB,UAAQ,SAAS;AACrB;;;ACEA,IAAM,oBAAoB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAGJ,CAAC;AACD,IAAM,gBAA8B,KAAK,MAAM,OAAO,eAAe,KAAK,QAAQ,WAAW,SAAS,CAAC;AACvG,SAAS,yBAAyB,SAAS;AACvC,QAAM,EAAE,aAAAE,cAAa,MAAM,aAAa,YAAY,SAAS,KAAK,IAAI;AACtE,QAAM,UAAUA,cAAa,OAAO;AAOpC,MAAI,EAAE,mBAAmB,cAAc;AACnC,WAAO;AAAA,EACX;AACA,QAAM,EAAE,UAAU,kBAAkB,IAAIA,aAAY,MAAM,SAAS;AACnE,SAAQ,cAAc,KAClB,QACA,kBAAkB,IAAI,IAAI,MACzB,SAAS,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA,EAK1B,CAAC,YACD,CAAC,eACD,eAAe,YACf,YAAY,KACZ,SAAS;AACjB;;;ACrBA,IAAM,oBAAoB;AAC1B,IAAM,4BAAN,cAAwC,YAAY;AAAA,EAChD,YAAY,EAAE,WAAW,MAAM,OAAAC,SAAQ,GAAG,OAAO,aAAa,SAAS,GAAG,cAAc,GAAG,aAAa,QAAQ,WAAAC,YAAW,MAAM,aAAAC,cAAa,SAAS,GAAG,QAAQ,GAAG;AACjK,UAAM;AAIN,SAAK,OAAO,MAAM;AACd,UAAI,KAAK,YAAY;AACjB,aAAK,WAAW,KAAK;AACrB,aAAK,eAAe;AAAA,MACxB;AACA,WAAK,kBAAkB,OAAO;AAAA,IAClC;AACA,SAAK,YAAY,KAAK,IAAI;AAC1B,UAAM,sBAAsB;AAAA,MACxB;AAAA,MACA,OAAAF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAAE;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP;AACA,UAAM,qBAAqB,SAAS,oBAAoB;AACxD,SAAK,mBAAmB,IAAI,mBAAmBD,YAAW,CAAC,mBAAmB,eAAe,WAAW,KAAK,oBAAoB,mBAAmB,eAAe,qBAAqB,CAAC,MAAM,GAAG,MAAMC,cAAa,OAAO;AAC5N,SAAK,kBAAkB,gBAAgB;AAAA,EAC3C;AAAA,EACA,oBAAoBD,YAAW,eAAe,SAASE,OAAM;AACzD,SAAK,mBAAmB;AACxB,UAAM,EAAE,MAAM,MAAM,UAAU,OAAAH,QAAO,WAAW,SAAS,IAAI;AAC7D,SAAK,aAAa,KAAK,IAAI;AAK3B,QAAI,CAAC,WAAWC,YAAW,MAAM,MAAM,QAAQ,GAAG;AAC9C,UAAI,mBAAmB,qBAAqB,CAACD,QAAO;AAChD,mBAAW,iBAAiBC,YAAW,SAAS,aAAa,CAAC;AAAA,MAClE;AACA,MAAAA,WAAU,CAAC,IAAIA,WAAUA,WAAU,SAAS,CAAC;AAC7C,2BAAqB,OAAO;AAC5B,cAAQ,SAAS;AAAA,IACrB;AAaA,UAAM,YAAYE,QACZ,CAAC,KAAK,aACF,KAAK,YACL,KAAK,aAAa,KAAK,YAAY,oBAC/B,KAAK,aACL,KAAK,YACb;AACN,UAAM,kBAAkB;AAAA,MACpB;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,WAAAF;AAAA,IACJ;AAMA,UAAM,YAAY,CAAC,aAAa,yBAAyB,eAAe,IAClE,IAAI,wBAAwB;AAAA,MAC1B,GAAG;AAAA,MACH,SAAS,gBAAgB,YAAY,MAAM;AAAA,IAC/C,CAAC,IACC,IAAI,YAAY,eAAe;AACrC,cAAU,SAAS,KAAK,MAAM,KAAK,eAAe,CAAC,EAAE,MAAM,IAAI;AAC/D,QAAI,KAAK,iBAAiB;AACtB,WAAK,eAAe,UAAU,eAAe,KAAK,eAAe;AACjE,WAAK,kBAAkB;AAAA,IAC3B;AACA,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,IAAI,WAAW;AACX,QAAI,CAAC,KAAK,YAAY;AAClB,aAAO,KAAK;AAAA,IAChB,OACK;AACD,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,KAAK,WAAW,WAAW;AACvB,WAAO,KAAK,SAAS,QAAQ,SAAS,EAAE,KAAK,MAAM;AAAA,IAAE,CAAC;AAAA,EAC1D;AAAA,EACA,IAAI,YAAY;AACZ,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,kBAAkB,OAAO;AAC9B,6BAAuB;AAAA,IAC3B;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,IAAI,KAAK,SAAS;AACd,SAAK,UAAU,OAAO;AAAA,EAC1B;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,IAAI,MAAM,UAAU;AAChB,SAAK,UAAU,QAAQ;AAAA,EAC3B;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,eAAe,UAAU;AACrB,QAAI,KAAK,YAAY;AACjB,WAAK,eAAe,KAAK,UAAU,eAAe,QAAQ;AAAA,IAC9D,OACK;AACD,WAAK,kBAAkB;AAAA,IAC3B;AACA,WAAO,MAAM,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO;AACH,SAAK,UAAU,KAAK;AAAA,EACxB;AAAA,EACA,QAAQ;AACJ,SAAK,UAAU,MAAM;AAAA,EACzB;AAAA,EACA,WAAW;AACP,SAAK,UAAU,SAAS;AAAA,EAC5B;AAAA,EACA,SAAS;AACL,QAAI,KAAK,YAAY;AACjB,WAAK,UAAU,OAAO;AAAA,IAC1B;AACA,SAAK,kBAAkB,OAAO;AAAA,EAClC;AACJ;;;AC5KA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,YAAY;AAEpB,SAAK,OAAO,MAAM,KAAK,OAAO,MAAM;AACpC,SAAK,aAAa,WAAW,OAAO,OAAO;AAAA,EAC/C;AAAA,EACA,IAAI,WAAW;AACX,WAAO,QAAQ,IAAI,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,QAAQ,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,UAAU;AACb,WAAO,KAAK,WAAW,CAAC,EAAE,QAAQ;AAAA,EACtC;AAAA,EACA,OAAO,UAAU,UAAU;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,WAAK,WAAW,CAAC,EAAE,QAAQ,IAAI;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,eAAe,UAAU;AACrB,UAAM,gBAAgB,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,eAAe,QAAQ,CAAC;AAC3F,WAAO,MAAM;AACT,oBAAc,QAAQ,CAAC,QAAQ,MAAM;AACjC,kBAAU,OAAO;AACjB,aAAK,WAAW,CAAC,EAAE,KAAK;AAAA,MAC5B,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK,OAAO,MAAM;AAAA,EAC7B;AAAA,EACA,IAAI,KAAKG,OAAM;AACX,SAAK,OAAO,QAAQA,KAAI;AAAA,EAC5B;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,OAAO,OAAO;AAAA,EAC9B;AAAA,EACA,IAAI,MAAM,OAAO;AACb,SAAK,OAAO,SAAS,KAAK;AAAA,EAC9B;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,OAAO,OAAO;AAAA,EAC9B;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,OAAO,WAAW;AAAA,EAClC;AAAA,EACA,IAAI,WAAW;AACX,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,YAAM,KAAK,IAAI,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ;AAAA,IACnD;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,YAAY;AACf,SAAK,WAAW,QAAQ,CAAC,aAAa,SAAS,UAAU,EAAE,CAAC;AAAA,EAChE;AAAA,EACA,OAAO;AACH,SAAK,OAAO,MAAM;AAAA,EACtB;AAAA,EACA,QAAQ;AACJ,SAAK,OAAO,OAAO;AAAA,EACvB;AAAA,EACA,SAAS;AACL,SAAK,OAAO,QAAQ;AAAA,EACxB;AAAA,EACA,WAAW;AACP,SAAK,OAAO,UAAU;AAAA,EAC1B;AACJ;;;ACnEA,IAAM,yBAAN,cAAqC,eAAe;AAAA,EAChD,KAAK,WAAW,WAAW;AACvB,WAAO,KAAK,SAAS,QAAQ,SAAS,EAAE,KAAK,MAAM;AAAA,IAAE,CAAC;AAAA,EAC1D;AACJ;;;ACJA,IAAM,yBAAN,cAAqC,gBAAgB;AAAA,EACjD,YAAY,WAAW;AACnB,UAAM;AACN,SAAK,YAAY;AACjB,cAAU,WAAW,MAAM;AACvB,WAAK,eAAe,KAAK;AACzB,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACXA,IAAM,gBAAgB,oBAAI,QAAQ;AAClC,IAAM,kBAAkB,CAAC,MAAM,gBAAgB,OAAO,GAAG,IAAI,IAAI,aAAa;AAC9E,SAAS,gBAAgB,SAAS;AAC9B,QAAM,MAAM,cAAc,IAAI,OAAO,KAAK,oBAAI,IAAI;AAClD,gBAAc,IAAI,SAAS,GAAG;AAC9B,SAAO;AACX;;;ACMA,IAAM;AAAA;AAAA,EAEN;AAAA;AACA,SAAS,iBAAiBC,UAAS;AAC/B,QAAM,QAAQ,sBAAsB,KAAKA,QAAO;AAChD,MAAI,CAAC;AACD,WAAO,CAAC,CAAC;AACb,QAAM,CAAC,EAAE,QAAQ,QAAQ,QAAQ,IAAI;AACrC,SAAO,CAAC,KAAK,UAAU,MAAM,IAAI,QAAQ;AAC7C;AACA,IAAM,WAAW;AACjB,SAAS,iBAAiBA,UAAS,SAAS,QAAQ,GAAG;AACnD,YAAU,SAAS,UAAU,yDAAyDA,QAAO,wDAAwD,mBAAmB;AACxK,QAAM,CAAC,OAAO,QAAQ,IAAI,iBAAiBA,QAAO;AAElD,MAAI,CAAC;AACD;AAEJ,QAAM,WAAW,OAAO,iBAAiB,OAAO,EAAE,iBAAiB,KAAK;AACxE,MAAI,UAAU;AACV,UAAM,UAAU,SAAS,KAAK;AAC9B,WAAO,kBAAkB,OAAO,IAAI,WAAW,OAAO,IAAI;AAAA,EAC9D;AACA,SAAO,mBAAmB,QAAQ,IAC5B,iBAAiB,UAAU,SAAS,QAAQ,CAAC,IAC7C;AACV;;;ACtCA,SAAS,mBAAmB,YAAY,KAAK;AACzC,SAAQ,aAAa,GAAG,KACpB,aAAa,SAAS,KACtB;AACR;;;ACFA,IAAM,iBAAiB,oBAAI,IAAI;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACP,CAAC;;;ACPD,IAAM,OAAO;AAAA,EACT,MAAM,CAAC,MAAM,MAAM;AAAA,EACnB,OAAO,CAAC,MAAM;AAClB;;;ACHA,IAAM,gBAAgB,CAAC,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC;;;ACKlD,IAAM,sBAAsB,CAAC,QAAQ,IAAI,SAAS,SAAS,IAAI,IAAI,IAAI;AAIvE,IAAM,yBAAyB,CAAC,MAAM,oBAAoB,KAAK,cAAc,CAAC,CAAC;;;ACV/E,SAAS,OAAO,OAAO;AACnB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,UAAU;AAAA,EACrB,WACS,UAAU,MAAM;AACrB,WAAO,UAAU,UAAU,UAAU,OAAO,kBAAkB,KAAK;AAAA,EACvE,OACK;AACD,WAAO;AAAA,EACX;AACJ;;;ACNA,IAAM,cAAc,oBAAI,IAAI,CAAC,cAAc,YAAY,YAAY,SAAS,CAAC;AAC7E,SAAS,mBAAmB,GAAG;AAC3B,QAAM,CAAC,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC9C,MAAI,SAAS;AACT,WAAO;AACX,QAAM,CAACC,OAAM,IAAI,MAAM,MAAM,UAAU,KAAK,CAAC;AAC7C,MAAI,CAACA;AACD,WAAO;AACX,QAAM,OAAO,MAAM,QAAQA,SAAQ,EAAE;AACrC,MAAI,eAAe,YAAY,IAAI,IAAI,IAAI,IAAI;AAC/C,MAAIA,YAAW;AACX,oBAAgB;AACpB,SAAO,OAAO,MAAM,eAAe,OAAO;AAC9C;AACA,IAAM,gBAAgB;AACtB,IAAM,SAAS;AAAA,EACX,GAAG;AAAA,EACH,mBAAmB,CAAC,MAAM;AACtB,UAAM,YAAY,EAAE,MAAM,aAAa;AACvC,WAAO,YAAY,UAAU,IAAI,kBAAkB,EAAE,KAAK,GAAG,IAAI;AAAA,EACrE;AACJ;;;ACzBA,IAAM,MAAM;AAAA,EACR,GAAG;AAAA,EACH,WAAW,KAAK;AACpB;;;ACFA,IAAM,sBAAsB;AAAA,EACxB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,aAAa;AAAA,EACb,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACb;;;ACvBA,IAAM,mBAAmB;AAAA;AAAA,EAErB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,wBAAwB;AAAA;AAAA,EAExB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA;AAAA,EAEZ,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,GAAG;AAAA,EACH,QAAQ;AAAA;AAAA,EAER,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AAChB;;;ACxCA,IAAM,oBAAoB;AAAA,EACtB,GAAG;AAAA;AAAA,EAEH;AAAA,EACA,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA;AAAA,EAER,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB;AAAA,EACA,cAAc;AAClB;AAIA,IAAM,sBAAsB,CAAC,QAAQ,kBAAkB,GAAG;;;ACvB1D,SAASC,mBAAkB,KAAK,OAAO;AACnC,MAAI,mBAAmB,oBAAoB,GAAG;AAC9C,MAAI,qBAAqB;AACrB,uBAAmB;AAEvB,SAAO,iBAAiB,oBAClB,iBAAiB,kBAAkB,KAAK,IACxC;AACV;;;ACHA,IAAM,mBAAmB,oBAAI,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAC;AACtD,SAAS,4BAA4B,qBAAqB,qBAAqB,MAAM;AACjF,MAAI,IAAI;AACR,MAAI,qBAAqB;AACzB,SAAO,IAAI,oBAAoB,UAAU,CAAC,oBAAoB;AAC1D,UAAM,WAAW,oBAAoB,CAAC;AACtC,QAAI,OAAO,aAAa,YACpB,CAAC,iBAAiB,IAAI,QAAQ,KAC9B,oBAAoB,QAAQ,EAAE,OAAO,QAAQ;AAC7C,2BAAqB,oBAAoB,CAAC;AAAA,IAC9C;AACA;AAAA,EACJ;AACA,MAAI,sBAAsB,MAAM;AAC5B,eAAW,aAAa,qBAAqB;AACzC,0BAAoB,SAAS,IAAIC,mBAAkB,MAAM,kBAAkB;AAAA,IAC/E;AAAA,EACJ;AACJ;;;AClBA,IAAM,uBAAN,cAAmC,iBAAiB;AAAA,EAChD,YAAY,qBAAqB,YAAY,MAAMC,cAAa,SAAS;AACrE,UAAM,qBAAqB,YAAY,MAAMA,cAAa,SAAS,IAAI;AAAA,EAC3E;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,qBAAqB,SAAS,KAAK,IAAI;AAC/C,QAAI,CAAC,WAAW,CAAC,QAAQ;AACrB;AACJ,UAAM,cAAc;AAIpB,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,UAAI,WAAW,oBAAoB,CAAC;AACpC,UAAI,OAAO,aAAa,UAAU;AAC9B,mBAAW,SAAS,KAAK;AACzB,YAAI,mBAAmB,QAAQ,GAAG;AAC9B,gBAAM,WAAW,iBAAiB,UAAU,QAAQ,OAAO;AAC3D,cAAI,aAAa,QAAW;AACxB,gCAAoB,CAAC,IAAI;AAAA,UAC7B;AACA,cAAI,MAAM,oBAAoB,SAAS,GAAG;AACtC,iBAAK,gBAAgB;AAAA,UACzB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAMA,SAAK,qBAAqB;AAO1B,QAAI,CAAC,eAAe,IAAI,IAAI,KAAK,oBAAoB,WAAW,GAAG;AAC/D;AAAA,IACJ;AACA,UAAM,CAAC,QAAQ,MAAM,IAAI;AACzB,UAAM,aAAa,uBAAuB,MAAM;AAChD,UAAM,aAAa,uBAAuB,MAAM;AAIhD,QAAI,eAAe;AACf;AAKJ,QAAI,cAAc,UAAU,KAAK,cAAc,UAAU,GAAG;AACxD,eAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,cAAM,QAAQ,oBAAoB,CAAC;AACnC,YAAI,OAAO,UAAU,UAAU;AAC3B,8BAAoB,CAAC,IAAI,WAAW,KAAK;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ,WACS,iBAAiB,IAAI,GAAG;AAI7B,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,UAAM,EAAE,qBAAqB,KAAK,IAAI;AACtC,UAAM,sBAAsB,CAAC;AAC7B,aAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACjD,UAAI,oBAAoB,CAAC,MAAM,QAC3B,OAAO,oBAAoB,CAAC,CAAC,GAAG;AAChC,4BAAoB,KAAK,CAAC;AAAA,MAC9B;AAAA,IACJ;AACA,QAAI,oBAAoB,QAAQ;AAC5B,kCAA4B,qBAAqB,qBAAqB,IAAI;AAAA,IAC9E;AAAA,EACJ;AAAA,EACA,sBAAsB;AAClB,UAAM,EAAE,SAAS,qBAAqB,KAAK,IAAI;AAC/C,QAAI,CAAC,WAAW,CAAC,QAAQ;AACrB;AACJ,QAAI,SAAS,UAAU;AACnB,WAAK,mBAAmB,OAAO;AAAA,IACnC;AACA,SAAK,iBAAiB,iBAAiB,IAAI,EAAE,QAAQ,mBAAmB,GAAG,OAAO,iBAAiB,QAAQ,OAAO,CAAC;AACnH,wBAAoB,CAAC,IAAI,KAAK;AAE9B,UAAM,kBAAkB,oBAAoB,oBAAoB,SAAS,CAAC;AAC1E,QAAI,oBAAoB,QAAW;AAC/B,cAAQ,SAAS,MAAM,eAAe,EAAE,KAAK,iBAAiB,KAAK;AAAA,IACvE;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,UAAM,EAAE,SAAS,MAAM,oBAAoB,IAAI;AAC/C,QAAI,CAAC,WAAW,CAAC,QAAQ;AACrB;AACJ,UAAM,QAAQ,QAAQ,SAAS,IAAI;AACnC,aAAS,MAAM,KAAK,KAAK,gBAAgB,KAAK;AAC9C,UAAM,qBAAqB,oBAAoB,SAAS;AACxD,UAAM,gBAAgB,oBAAoB,kBAAkB;AAC5D,wBAAoB,kBAAkB,IAAI,iBAAiB,IAAI,EAAE,QAAQ,mBAAmB,GAAG,OAAO,iBAAiB,QAAQ,OAAO,CAAC;AACvI,QAAI,kBAAkB,QAAQ,KAAK,kBAAkB,QAAW;AAC5D,WAAK,gBAAgB;AAAA,IACzB;AAEA,QAAI,KAAK,mBAAmB,QAAQ;AAChC,WAAK,kBAAkB,QAAQ,CAAC,CAAC,oBAAoB,mBAAmB,MAAM;AAC1E,gBACK,SAAS,kBAAkB,EAC3B,IAAI,mBAAmB;AAAA,MAChC,CAAC;AAAA,IACL;AACA,SAAK,qBAAqB;AAAA,EAC9B;AACJ;;;AChIA,IAAM,WAAW,oBAAI,IAAI;AAAA;AAAA,EAErB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AACJ,CAAC;;;AClCD,SAAS,gBAAgBC,YAAW,MAAM;AACtC,WAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,QAAI,OAAOA,WAAU,CAAC,MAAM,YAAY,SAAS,IAAI,IAAI,GAAG;AACxD,MAAAA,WAAU,CAAC,IAAIA,WAAU,CAAC,IAAI;AAAA,IAClC;AAAA,EACJ;AACJ;;;ACJA,SAAS,uBAAuB,QAAQ;AACpC,SAAO,QAAS,OAAO,WAAW,cAAc,qBAAqB,KACjE,CAAC,UACA,OAAO,WAAW,aACd,UAAU,wBAAwB,qBAAqB,MAC5D,mBAAmB,MAAM,KACxB,MAAM,QAAQ,MAAM,KAAK,OAAO,MAAM,sBAAsB,CAAE;AACvE;;;ACTA,IAAM,2BAAyC,KAAK,MAAM;AACtD,MAAI;AACA,aAAS,cAAc,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;AAAA,EAC1D,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACA,SAAO;AACX,CAAC;;;ACPD,IAAMC,qBAAoB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAIJ,CAAC;;;ACXD,SAAS,YAAY,KAAK;AACtB,SAAO,IAAI,QAAQ,YAAY,CAAC,UAAU,IAAI,MAAM,YAAY,CAAC,EAAE;AACvE;;;ACFA,SAAS,gBAAgB,mBAAmB,OAAO,eAAe;AAC9D,MAAI,6BAA6B,aAAa;AAC1C,WAAO,CAAC,iBAAiB;AAAA,EAC7B,WACS,OAAO,sBAAsB,UAAU;AAC5C,QAAI,OAAO;AACX,QAAI,OAAO;AACP,aAAO,MAAM;AAAA,IACjB;AACA,UAAM,WAAW,gBAAgB,iBAAiB,KAC9C,KAAK,iBAAiB,iBAAiB;AAC3C,WAAO,WAAW,MAAM,KAAK,QAAQ,IAAI,CAAC;AAAA,EAC9C;AACA,SAAO,MAAM,KAAK,iBAAiB;AACvC;;;ACZA,SAAS,qBAAqB,eAAe;AACzC,SAAO,CAAC,SAAS,WAAW;AACxB,UAAM,WAAW,gBAAgB,OAAO;AACxC,UAAM,gBAAgB,CAAC;AACvB,eAAW,WAAW,UAAU;AAC5B,YAAM,SAAS,cAAc,SAAS,MAAM;AAC5C,oBAAc,KAAK,MAAM;AAAA,IAC7B;AACA,WAAO,MAAM;AACT,iBAAW,UAAU;AACjB,eAAO;AAAA,IACf;AAAA,EACJ;AACJ;;;ACZA,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACpC,SAAO,QAAQ,OAAO,UAAU,WAC1B,KAAK,UAAU,KAAK,IACpB;AACV;;;ACHA,IAAM,mBAAN,MAAuB;AAAA,EACnB,cAAc;AACV,SAAK,SAAS,CAAC;AACf,SAAK,SAAS,oBAAI,IAAI;AAAA,EAC1B;AAAA,EACA,IAAI,MAAM,OAAO,QAAQ,UAAU,sBAAsB,MAAM;AAC3D,UAAM,gBAAgB,KAAK,OAAO,IAAI,IAAI;AAC1C,QAAI,eAAe;AACf,oBAAc,SAAS;AAAA,IAC3B;AACA,UAAM,WAAW,MAAM;AACnB,YAAM,IAAI,MAAM,IAAI;AACpB,UAAI,qBAAqB;AACrB,aAAK,OAAO,IAAI,IAAI,eAAe,GAAG,iBAAiB,IAAI,CAAC;AAAA,MAChE,OACK;AACD,aAAK,OAAO,IAAI,IAAI;AAAA,MACxB;AACA,gBAAU,MAAM,OAAO,MAAM;AAAA,IACjC;AACA,aAAS;AACT,UAAM,iBAAiB,MAAM,GAAG,UAAU,QAAQ;AAClD,gBAAY,MAAM,aAAa,QAAQ;AACvC,UAAM,SAAS,MAAM;AACjB,qBAAe;AACf,gBAAU,YAAY,MAAM;AAC5B,WAAK,OAAO,OAAO,IAAI;AACvB,kBAAY,MAAM,gBAAgB,QAAQ;AAAA,IAC9C;AACA,SAAK,OAAO,IAAI,MAAM,EAAE,OAAO,UAAU,OAAO,CAAC;AACjD,WAAO;AAAA,EACX;AAAA,EACA,IAAI,MAAM;AACN,WAAO,KAAK,OAAO,IAAI,IAAI,GAAG;AAAA,EAClC;AAAA,EACA,UAAU;AACN,eAAW,SAAS,KAAK,OAAO,OAAO,GAAG;AACtC,YAAM,SAAS;AAAA,IACnB;AAAA,EACJ;AACJ;;;AC1CA,SAAS,aAAa,UAAU;AAC5B,QAAM,aAAa,oBAAI,QAAQ;AAC/B,QAAM,gBAAgB,CAAC;AACvB,SAAO,CAAC,SAAS,WAAW;AACxB,UAAM,QAAQ,WAAW,IAAI,OAAO,KAAK,IAAI,iBAAiB;AAC9D,eAAW,IAAI,SAAS,KAAK;AAC7B,eAAW,OAAO,QAAQ;AACtB,YAAM,QAAQ,OAAO,GAAG;AACxB,YAAM,SAAS,SAAS,SAAS,OAAO,KAAK,KAAK;AAClD,oBAAc,KAAK,MAAM;AAAA,IAC7B;AACA,WAAO,MAAM;AACT,iBAAW,UAAU;AACjB,eAAO;AAAA,IACf;AAAA,EACJ;AACJ;;;ACdA,SAAS,iBAAiB,SAAS,MAAM;AACrC,MAAI,EAAE,QAAQ;AACV,WAAO;AACX,QAAM,aAAa,OAAO,yBAAyB,OAAO,eAAe,OAAO,GAAG,IAAI,KACnF,OAAO,yBAAyB,SAAS,IAAI;AAEjD,SAAO,cAAc,OAAO,WAAW,QAAQ;AACnD;AACA,IAAM,eAAe,CAAC,SAAS,OAAO,KAAK,UAAU;AACjD,QAAM,SAAS,iBAAiB,SAAS,GAAG;AAC5C,QAAM,OAAO,SACP,MACA,IAAI,WAAW,MAAM,KAAK,IAAI,WAAW,MAAM,IAC3C,YAAY,GAAG,IACf;AAIV,QAAM,SAAS,SACT,MAAM;AACJ,YAAQ,IAAI,IAAI,MAAM,OAAO,GAAG;AAAA,EACpC,IACE,MAAM;AACJ,UAAM,IAAI,MAAM,OAAO,GAAG;AAC1B,QAAI,MAAM,QAAQ,MAAM,QAAW;AAC/B,cAAQ,gBAAgB,IAAI;AAAA,IAChC,OACK;AACD,cAAQ,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,IACxC;AAAA,EACJ;AACJ,SAAO,MAAM,IAAI,KAAK,OAAO,MAAM;AACvC;AACA,IAAM,aAA2B;AAAA,EACnB,aAAa,YAAY;AAAC;;;ACpCxC,IAAM,aAA2B,aAAa,CAAC,SAAS,OAAO,KAAK,UAAU;AAC1E,SAAO,MAAM,IAAI,KAAK,OAAO,MAAM;AAC/B,YAAQ,GAAG,IAAI,MAAM,OAAO,GAAG;AAAA,EACnC,GAAG,QAAW,KAAK;AACvB,CAAC;;;ACAD,SAAS,cAAc,SAAS;AAC5B,SAAO,SAAS,OAAO,KAAK,kBAAkB;AAClD;;;ACAA,IAAM,qBAAqB;AAC3B,IAAM,UAAU,CAAC,UAAU;AACvB,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC;AACnC;AACA,IAAM,sBAAsB;AAAA,EACxB,SAAS;AACb;AAMA,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,YAAY,MAAM,UAAU,CAAC,GAAG;AAQ5B,SAAK,mBAAmB;AAIxB,SAAK,SAAS,CAAC;AACf,SAAK,kBAAkB,CAAC,MAAM;AAC1B,YAAM,cAAc,KAAK,IAAI;AAM7B,UAAI,KAAK,cAAc,aAAa;AAChC,aAAK,kBAAkB;AAAA,MAC3B;AACA,WAAK,OAAO,KAAK;AACjB,WAAK,WAAW,CAAC;AAEjB,UAAI,KAAK,YAAY,KAAK,MAAM;AAC5B,aAAK,OAAO,QAAQ,OAAO,KAAK,OAAO;AACvC,YAAI,KAAK,YAAY;AACjB,qBAAW,aAAa,KAAK,YAAY;AACrC,sBAAU,MAAM;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,cAAc;AACnB,SAAK,WAAW,IAAI;AACpB,SAAK,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,WAAWC,UAAS;AAChB,SAAK,UAAUA;AACf,SAAK,YAAY,KAAK,IAAI;AAC1B,QAAI,KAAK,qBAAqB,QAAQA,aAAY,QAAW;AACzD,WAAK,mBAAmB,QAAQ,KAAK,OAAO;AAAA,IAChD;AAAA,EACJ;AAAA,EACA,kBAAkB,iBAAiB,KAAK,SAAS;AAC7C,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyCA,SAAS,cAAc;AACnB,QAAI,MAAuC;AACvC,eAAS,OAAO,iFAAiF;AAAA,IACrG;AACA,WAAO,KAAK,GAAG,UAAU,YAAY;AAAA,EACzC;AAAA,EACA,GAAG,WAAW,UAAU;AACpB,QAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AACzB,WAAK,OAAO,SAAS,IAAI,IAAI,oBAAoB;AAAA,IACrD;AACA,UAAM,cAAc,KAAK,OAAO,SAAS,EAAE,IAAI,QAAQ;AACvD,QAAI,cAAc,UAAU;AACxB,aAAO,MAAM;AACT,oBAAY;AAKZ,cAAM,KAAK,MAAM;AACb,cAAI,CAAC,KAAK,OAAO,OAAO,QAAQ,GAAG;AAC/B,iBAAK,KAAK;AAAA,UACd;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB;AACb,eAAW,iBAAiB,KAAK,QAAQ;AACrC,WAAK,OAAO,aAAa,EAAE,MAAM;AAAA,IACrC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,eAAe,mBAAmB;AACrC,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,IAAI,GAAG;AACH,QAAI,CAAC,KAAK,eAAe;AACrB,WAAK,gBAAgB,CAAC;AAAA,IAC1B,OACK;AACD,WAAK,cAAc,GAAG,KAAK,eAAe;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,gBAAgB,MAAMA,UAAS,OAAO;AAClC,SAAK,IAAIA,QAAO;AAChB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,KAAK,YAAY;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,GAAG,eAAe,MAAM;AACzB,SAAK,gBAAgB,CAAC;AACtB,SAAK,OAAO;AACZ,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,oBAAgB,KAAK,KAAK;AAC1B,QAAI,KAAK;AACL,WAAK,kBAAkB;AAAA,EAC/B;AAAA,EACA,QAAQ;AACJ,SAAK,OAAO,QAAQ,OAAO,KAAK,OAAO;AAAA,EAC3C;AAAA,EACA,aAAa,WAAW;AACpB,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa,oBAAI,IAAI;AAAA,IAC9B;AACA,SAAK,WAAW,IAAI,SAAS;AAAA,EACjC;AAAA,EACA,gBAAgB,WAAW;AACvB,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,OAAO,SAAS;AAAA,IACpC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM;AACF,QAAI,oBAAoB,SAAS;AAC7B,0BAAoB,QAAQ,KAAK,IAAI;AAAA,IACzC;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACV,UAAM,cAAc,KAAK,IAAI;AAC7B,QAAI,CAAC,KAAK,oBACN,KAAK,mBAAmB,UACxB,cAAc,KAAK,YAAY,oBAAoB;AACnD,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,KAAK,IAAI,KAAK,YAAY,KAAK,eAAe,kBAAkB;AAE9E,WAAO,kBAAkB,WAAW,KAAK,OAAO,IAC5C,WAAW,KAAK,cAAc,GAAG,KAAK;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,SAAK,KAAK;AACV,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,WAAK,cAAc;AACnB,WAAK,YAAY,eAAe,OAAO;AACvC,UAAI,KAAK,OAAO,gBAAgB;AAC5B,aAAK,OAAO,eAAe,OAAO;AAAA,MACtC;AAAA,IACJ,CAAC,EAAE,KAAK,MAAM;AACV,UAAI,KAAK,OAAO,mBAAmB;AAC/B,aAAK,OAAO,kBAAkB,OAAO;AAAA,MACzC;AACA,WAAK,eAAe;AAAA,IACxB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,KAAK;AACpB,UAAI,KAAK,OAAO,iBAAiB;AAC7B,aAAK,OAAO,gBAAgB,OAAO;AAAA,MACvC;AAAA,IACJ;AACA,SAAK,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACV,WAAO,CAAC,CAAC,KAAK;AAAA,EAClB;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU;AACN,SAAK,YAAY,MAAM;AACvB,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,eAAe;AACpB,SAAK,KAAK;AACV,QAAI,KAAK,mBAAmB;AACxB,WAAK,kBAAkB;AAAA,IAC3B;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,MAAM,SAAS;AAChC,SAAO,IAAI,YAAY,MAAM,OAAO;AACxC;;;AC9TA,IAAM,iBAAiB;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,sBAAsB;AAC1B;AACA,SAAS,eAAe,OAAO;AAC3B,MAAIC,aAAY;AAChB,MAAI,qBAAqB;AAKzB,WAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAChD,UAAM,MAAM,mBAAmB,CAAC;AAChC,UAAM,QAAQ,MAAM,OAAO,GAAG;AAC9B,QAAI,UAAU;AACV;AACJ,QAAI,iBAAiB;AACrB,QAAI,OAAO,UAAU,UAAU;AAC3B,uBAAiB,WAAW,IAAI,WAAW,OAAO,IAAI,IAAI;AAAA,IAC9D,OACK;AACD,uBAAiB,WAAW,KAAK,MAAM;AAAA,IAC3C;AACA,QAAI,CAAC,gBAAgB;AACjB,2BAAqB;AACrB,YAAM,gBAAgB,eAAe,GAAG,KAAK;AAC7C,YAAM,gBAAgB,MAAM,OAAO,GAAG;AACtC,MAAAA,cAAa,GAAG,aAAa,IAAI,aAAa;AAAA,IAClD;AAAA,EACJ;AACA,SAAO,qBAAqB,SAASA,WAAU,KAAK;AACxD;;;AC3BA,IAAM,cAAc,oBAAI,IAAI,CAAC,WAAW,WAAW,SAAS,CAAC;AAC7D,IAAM,gBAAgB,CAAC,SAAS,OAAO,KAAK,UAAU;AAClD,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,eAAe,IAAI,GAAG,GAAG;AACzB,QAAI,CAAC,MAAM,IAAI,WAAW,GAAG;AAGzB,UAAI,CAAC,cAAc,OAAO,KAAK,CAAC,MAAM,IAAI,cAAc,GAAG;AACvD,sBAAc,SAAS,OAAO,gBAAgB,IAAI,YAAY,UAAU,CAAC;AAAA,MAC7E;AACA,YAAM,IAAI,aAAa,IAAI,YAAY,MAAM,GAAG,MAAM;AAClD,gBAAQ,MAAM,YAAY,eAAe,KAAK;AAAA,MAClD,CAAC;AAAA,IACL;AACA,eAAW,MAAM,IAAI,WAAW;AAAA,EACpC,WACS,YAAY,IAAI,GAAG,GAAG;AAC3B,QAAI,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAC/B,YAAM,IAAI,mBAAmB,IAAI,YAAY,EAAE,GAAG,MAAM;AACpD,cAAM,UAAU,MAAM,OAAO,WAAW;AACxC,cAAM,UAAU,MAAM,OAAO,WAAW;AACxC,cAAM,UAAU,MAAM,OAAO,WAAW;AACxC,gBAAQ,MAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,MACpE,CAAC;AAAA,IACL;AACA,eAAW,MAAM,IAAI,iBAAiB;AAAA,EAC1C,WACS,SAAS,GAAG,GAAG;AACpB,aAAS,MAAM;AACX,cAAQ,MAAM,YAAY,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,IACpD;AAAA,EACJ,OACK;AACD,aAAS,MAAM;AACX,cAAQ,MAAM,GAAG,IAAI,MAAM,OAAO,GAAG;AAAA,IACzC;AAAA,EACJ;AACA,SAAO,MAAM,IAAI,KAAK,OAAO,QAAQ,QAAQ;AACjD;AACA,IAAM,cAA4B;AAAA,EACpB,aAAa,aAAa;AAAC;;;ACzCzC,IAAM,OAAO,GAAG;AAChB,SAAS,gBAAgB,SAAS,OAAO,KAAK,OAAO;AACjD,QAAM,OAAO,MAAM,QAAQ,aAAa,cAAc,GAAG,CAAC;AAC1D,MAAI,QAAQ,cAAc;AACtB,WAAO,MAAM,IAAI,KAAK,OAAO,MAAM,QAAQ,aAAa,qBAAqB,KAAK,CAAC,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;AAAA,EAC1G,OACK;AACD,QAAI,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAChC,YAAM,IAAI,oBAAoB,IAAI,YAAY,KAAK,GAAG,MAAM;AACxD,cAAM,EAAE,aAAa,GAAG,YAAY,IAAI,MAAM;AAC9C,gBAAQ,aAAa,oBAAoB,GAAG,KAAK,UAAU,CAAC,IAAI,KAAK,eAAe,IAAI,OAAO,UAAU,CAAC,CAAC,EAAE;AAAA,MACjH,CAAC;AAAA,IACL;AACA,WAAO,MAAM,IAAI,KAAK,OAAO,QAAW,MAAM,IAAI,kBAAkB,CAAC;AAAA,EACzE;AACJ;AACA,IAAM,cAAc,CAAC,SAAS,OAAO,KAAK,UAAU;AAChD,MAAI,IAAI,WAAW,MAAM,GAAG;AACxB,WAAO,gBAAgB,SAAS,OAAO,KAAK,KAAK;AAAA,EACrD,WACS,IAAI,WAAW,MAAM,GAAG;AAC7B,WAAO,aAAa,SAAS,OAAO,eAAe,GAAG,GAAG,KAAK;AAAA,EAClE;AACA,QAAM,UAAU,OAAO,QAAQ,QAAQ,gBAAgB;AACvD,SAAO,QAAQ,SAAS,OAAO,KAAK,KAAK;AAC7C;AACA,IAAM,YAA0B;AAAA,EAClB,aAAa,WAAW;AAAC;AACvC,SAAS,eAAe,KAAK;AACzB,SAAO,IAAI,QAAQ,gBAAgB,CAAC,GAAG,cAAc,UAAU,YAAY,CAAC;AAChF;;;ACpCA,IAAM,EAAE,UAAU,WAAW,QAAQ,gBAAgB,IACrC,oBAAoB,gBAAgB,KAAK;;;ACHzD,IAAM,aAAa;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AACP;AACA,SAAS,eAAe;AACpB,SAAO,WAAW,KAAK,WAAW;AACtC;;;ACJA,SAAS,YAAY,MAAM;AACvB,MAAI,SAAS,OAAO,SAAS,KAAK;AAC9B,QAAI,WAAW,IAAI,GAAG;AAClB,aAAO;AAAA,IACX,OACK;AACD,iBAAW,IAAI,IAAI;AACnB,aAAO,MAAM;AACT,mBAAW,IAAI,IAAI;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ,OACK;AACD,QAAI,WAAW,KAAK,WAAW,GAAG;AAC9B,aAAO;AAAA,IACX,OACK;AACD,iBAAW,IAAI,WAAW,IAAI;AAC9B,aAAO,MAAM;AACT,mBAAW,IAAI,WAAW,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACvBA,SAAS,aAAa,mBAAmB,SAAS;AAC9C,QAAM,WAAW,gBAAgB,iBAAiB;AAClD,QAAM,yBAAyB,IAAI,gBAAgB;AACnD,QAAM,eAAe;AAAA,IACjB,SAAS;AAAA,IACT,GAAG;AAAA,IACH,QAAQ,uBAAuB;AAAA,EACnC;AACA,QAAM,SAAS,MAAM,uBAAuB,MAAM;AAClD,SAAO,CAAC,UAAU,cAAc,MAAM;AAC1C;;;ACTA,SAAS,aAAa,OAAO;AACzB,SAAO,EAAE,MAAM,gBAAgB,WAAW,aAAa;AAC3D;AAQA,SAAS,MAAM,mBAAmB,cAAc,UAAU,CAAC,GAAG;AAC1D,QAAM,CAAC,UAAU,cAAc,MAAM,IAAI,aAAa,mBAAmB,OAAO;AAChF,QAAM,iBAAiB,CAAC,eAAe;AACnC,QAAI,CAAC,aAAa,UAAU;AACxB;AACJ,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,aAAa,aAAa,QAAQ,UAAU;AAClD,QAAI,OAAO,eAAe,cAAc,CAAC;AACrC;AACJ,UAAM,iBAAiB,CAAC,eAAe;AACnC,UAAI,CAAC,aAAa,UAAU;AACxB;AACJ,iBAAW,UAAU;AACrB,aAAO,oBAAoB,gBAAgB,cAAc;AAAA,IAC7D;AACA,WAAO,iBAAiB,gBAAgB,gBAAgB,YAAY;AAAA,EACxE;AACA,WAAS,QAAQ,CAAC,YAAY;AAC1B,YAAQ,iBAAiB,gBAAgB,gBAAgB,YAAY;AAAA,EACzE,CAAC;AACD,SAAO;AACX;;;AC3BA,IAAM,gBAAgB,CAAC,QAAQ,UAAU;AACrC,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX,WACS,WAAW,OAAO;AACvB,WAAO;AAAA,EACX,OACK;AACD,WAAO,cAAc,QAAQ,MAAM,aAAa;AAAA,EACpD;AACJ;;;ACjBA,IAAM,mBAAmB,CAAC,UAAU;AAChC,MAAI,MAAM,gBAAgB,SAAS;AAC/B,WAAO,OAAO,MAAM,WAAW,YAAY,MAAM,UAAU;AAAA,EAC/D,OACK;AASD,WAAO,MAAM,cAAc;AAAA,EAC/B;AACJ;;;ACfA,IAAM,oBAAoB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AACD,SAAS,4BAA4B,SAAS;AAC1C,SAAQ,kBAAkB,IAAI,QAAQ,OAAO,KACzC,QAAQ,aAAa;AAC7B;;;ACVA,IAAM,aAAa,oBAAI,QAAQ;;;ACK/B,SAAS,aAAa,UAAU;AAC5B,SAAO,CAAC,UAAU;AACd,QAAI,MAAM,QAAQ;AACd;AACJ,aAAS,KAAK;AAAA,EAClB;AACJ;AACA,SAAS,iBAAiB,QAAQ,MAAM;AACpC,SAAO,cAAc,IAAI,aAAa,YAAY,MAAM,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC,CAAC;AAC/F;AACA,IAAM,sBAAsB,CAAC,YAAY,iBAAiB;AACtD,QAAM,UAAU,WAAW;AAC3B,MAAI,CAAC;AACD;AACJ,QAAM,gBAAgB,aAAa,MAAM;AACrC,QAAI,WAAW,IAAI,OAAO;AACtB;AACJ,qBAAiB,SAAS,MAAM;AAChC,UAAM,cAAc,aAAa,MAAM;AACnC,uBAAiB,SAAS,IAAI;AAAA,IAClC,CAAC;AACD,UAAM,aAAa,MAAM,iBAAiB,SAAS,QAAQ;AAC3D,YAAQ,iBAAiB,SAAS,aAAa,YAAY;AAC3D,YAAQ,iBAAiB,QAAQ,YAAY,YAAY;AAAA,EAC7D,CAAC;AACD,UAAQ,iBAAiB,WAAW,eAAe,YAAY;AAI/D,UAAQ,iBAAiB,QAAQ,MAAM,QAAQ,oBAAoB,WAAW,aAAa,GAAG,YAAY;AAC9G;;;ACtBA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,iBAAiB,KAAK,KAAK,CAAC,aAAa;AACpD;AAoBA,SAAS,MAAM,kBAAkB,cAAc,UAAU,CAAC,GAAG;AACzD,QAAM,CAAC,SAAS,cAAc,YAAY,IAAI,aAAa,kBAAkB,OAAO;AACpF,QAAM,aAAa,CAAC,eAAe;AAC/B,UAAM,SAAS,WAAW;AAC1B,QAAI,CAAC,kBAAkB,UAAU;AAC7B;AACJ,eAAW,IAAI,MAAM;AACrB,UAAM,aAAa,aAAa,QAAQ,UAAU;AAClD,UAAM,eAAe,CAAC,UAAU,YAAY;AACxC,aAAO,oBAAoB,aAAa,WAAW;AACnD,aAAO,oBAAoB,iBAAiB,eAAe;AAC3D,UAAI,WAAW,IAAI,MAAM,GAAG;AACxB,mBAAW,OAAO,MAAM;AAAA,MAC5B;AACA,UAAI,CAAC,kBAAkB,QAAQ,GAAG;AAC9B;AAAA,MACJ;AACA,UAAI,OAAO,eAAe,YAAY;AAClC,mBAAW,UAAU,EAAE,QAAQ,CAAC;AAAA,MACpC;AAAA,IACJ;AACA,UAAM,cAAc,CAAC,YAAY;AAC7B,mBAAa,SAAS,WAAW,UAC7B,WAAW,YACX,QAAQ,mBACR,cAAc,QAAQ,QAAQ,MAAM,CAAC;AAAA,IAC7C;AACA,UAAM,kBAAkB,CAAC,gBAAgB;AACrC,mBAAa,aAAa,KAAK;AAAA,IACnC;AACA,WAAO,iBAAiB,aAAa,aAAa,YAAY;AAC9D,WAAO,iBAAiB,iBAAiB,iBAAiB,YAAY;AAAA,EAC1E;AACA,UAAQ,QAAQ,CAAC,WAAW;AACxB,UAAM,oBAAoB,QAAQ,kBAAkB,SAAS;AAC7D,sBAAkB,iBAAiB,eAAe,YAAY,YAAY;AAC1E,QAAI,cAAc,MAAM,GAAG;AACvB,aAAO,iBAAiB,SAAS,CAAC,UAAU,oBAAoB,OAAO,YAAY,CAAC;AACpF,UAAI,CAAC,4BAA4B,MAAM,KACnC,CAAC,OAAO,aAAa,UAAU,GAAG;AAClC,eAAO,WAAW;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;AC9EA,SAASC,kBAAiB,SAAS,MAAM;AACrC,QAAM,gBAAgB,OAAO,iBAAiB,OAAO;AACrD,SAAO,SAAS,IAAI,IACd,cAAc,iBAAiB,IAAI,IACnC,cAAc,IAAI;AAC5B;;;ACDA,SAAS,aAAa,SAAS;AAC3B,SAAO,SAAS,OAAO,KAAK,qBAAqB;AACrD;;;ACLA,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAI;AACJ,IAAM,UAAU,CAAC,eAAe,SAAS,aAAa,CAAC,QAAQ,kBAAkB;AAC7E,MAAI,iBAAiB,cAAc,CAAC,GAAG;AACnC,WAAO,cAAc,CAAC,EAAG,gBAAgB,MAAO;AAAA,EACpD,WACS,aAAa,MAAM,KAAK,aAAa,QAAQ;AAClD,WAAO,OAAO,QAAQ,EAAE,OAAO;AAAA,EACnC,OACK;AACD,WAAO,OAAO,QAAQ;AAAA,EAC1B;AACJ;AACA,IAAM,WAAyB,QAAQ,UAAU,SAAS,aAAa;AACvE,IAAM,YAA0B,QAAQ,SAAS,UAAU,cAAc;AACzE,SAAS,aAAa,EAAE,QAAQ,cAAc,GAAG;AAC7C,iBAAe,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY;AAC7C,YAAQ,QAAQ;AAAA,MACZ,IAAI,QAAQ;AACR,eAAO,SAAS,QAAQ,aAAa;AAAA,MACzC;AAAA,MACA,IAAI,SAAS;AACT,eAAO,UAAU,QAAQ,aAAa;AAAA,MAC1C;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,UAAU,SAAS;AACxB,UAAQ,QAAQ,YAAY;AAChC;AACA,SAAS,uBAAuB;AAC5B,MAAI,OAAO,mBAAmB;AAC1B;AACJ,aAAW,IAAI,eAAe,SAAS;AAC3C;AACA,SAAS,cAAc,QAAQ,SAAS;AACpC,MAAI,CAAC;AACD,yBAAqB;AACzB,QAAM,WAAW,gBAAgB,MAAM;AACvC,WAAS,QAAQ,CAAC,YAAY;AAC1B,QAAI,kBAAkB,eAAe,IAAI,OAAO;AAChD,QAAI,CAAC,iBAAiB;AAClB,wBAAkB,oBAAI,IAAI;AAC1B,qBAAe,IAAI,SAAS,eAAe;AAAA,IAC/C;AACA,oBAAgB,IAAI,OAAO;AAC3B,cAAU,QAAQ,OAAO;AAAA,EAC7B,CAAC;AACD,SAAO,MAAM;AACT,aAAS,QAAQ,CAAC,YAAY;AAC1B,YAAM,kBAAkB,eAAe,IAAI,OAAO;AAClD,uBAAiB,OAAO,OAAO;AAC/B,UAAI,CAAC,iBAAiB,MAAM;AACxB,kBAAU,UAAU,OAAO;AAAA,MAC/B;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;;;AC5DA,IAAM,kBAAkB,oBAAI,IAAI;AAChC,IAAI;AACJ,SAAS,4BAA4B;AACjC,wBAAsB,MAAM;AACxB,UAAM,OAAO;AAAA,MACT,IAAI,QAAQ;AACR,eAAO,OAAO;AAAA,MAClB;AAAA,MACA,IAAI,SAAS;AACT,eAAO,OAAO;AAAA,MAClB;AAAA,IACJ;AACA,oBAAgB,QAAQ,CAAC,aAAa,SAAS,IAAI,CAAC;AAAA,EACxD;AACA,SAAO,iBAAiB,UAAU,mBAAmB;AACzD;AACA,SAAS,aAAa,UAAU;AAC5B,kBAAgB,IAAI,QAAQ;AAC5B,MAAI,CAAC;AACD,8BAA0B;AAC9B,SAAO,MAAM;AACT,oBAAgB,OAAO,QAAQ;AAC/B,QAAI,CAAC,gBAAgB,QACjB,OAAO,wBAAwB,YAAY;AAC3C,aAAO,oBAAoB,UAAU,mBAAmB;AACxD,4BAAsB;AAAA,IAC1B;AAAA,EACJ;AACJ;;;ACzBA,SAAS,OAAO,GAAG,GAAG;AAClB,SAAO,OAAO,MAAM,aAAa,aAAa,CAAC,IAAI,cAAc,GAAG,CAAC;AACzE;;;ACHA,SAAS,gBAAgB,QAAQ,UAAU;AACvC,MAAI;AACJ,QAAM,UAAU,MAAM;AAClB,UAAM,EAAE,YAAY,IAAI;AACxB,UAAM,aAAa,gBAAgB,OAAO,IAAI,YAAY;AAC1D,UAAMC,YAAW,aAAa;AAC9B,QAAI,iBAAiBA,WAAU;AAC3B,aAAOA,SAAQ;AAAA,IACnB;AACA,mBAAeA;AAAA,EACnB;AACA,QAAM,UAAU,SAAS,IAAI;AAC7B,SAAO,MAAM,YAAY,OAAO;AACpC;;;ACXA,SAAS,SAAS;AACd,QAAM,EAAE,MAAM,IAAI;AAClB,MAAI,UAAU,MAAM;AAChB,gBAAY,MAAM;AAClB;AAAA,EACJ;AACA,QAAM,UAAU,KAAK,KAAK,UAAU,KAAK;AACzC,QAAM,WAAW,WAAW,KAAK,iBAAiB,UAAU;AAC5D,QAAM,WAAW,MAAM,KAAK,iBAAiB,KAAK;AAClD,QAAM,WAAW,OAAO,KAAK,iBAAiB,MAAM;AACxD;AACA,SAAS,KAAK,QAAQ;AAClB,SAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,OAAO;AAClE;AACA,SAAS,UAAU,QAAQ,cAAc,MAAM;AAC3C,MAAI,OAAO,WAAW,GAAG;AACrB,WAAO;AAAA,MACH,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AAAA,EACJ;AACA,SAAO;AAAA,IACH,KAAK,KAAK,IAAI,GAAG,MAAM;AAAA,IACvB,KAAK,KAAK,IAAI,GAAG,MAAM;AAAA,IACvB,KAAK,YAAY,MAAM;AAAA,EAC3B;AACJ;AACA,IAAM,UAAU,CAAC,OAAO,KAAK,MAAM,MAAO,EAAE;AAC5C,SAAS,mBAAmB;AACxB,cAAY,QAAQ;AACpB,cAAY,uBAAuB;AACvC;AACA,SAAS,cAAc;AACnB,QAAM,EAAE,MAAM,IAAI;AAClB,MAAI,CAAC,OAAO;AACR,UAAM,IAAI,MAAM,8BAA8B;AAAA,EAClD;AACA,mBAAiB;AACjB,cAAY,MAAM;AAClB,QAAM,UAAU;AAAA,IACZ,WAAW;AAAA,MACP,OAAO,UAAU,MAAM,UAAU,KAAK;AAAA,MACtC,MAAM,UAAU,MAAM,UAAU,IAAI;AAAA,MACpC,MAAM,UAAU,MAAM,UAAU,IAAI;AAAA,MACpC,kBAAkB,UAAU,MAAM,UAAU,gBAAgB;AAAA,MAC5D,WAAW,UAAU,MAAM,UAAU,SAAS;AAAA,MAC9C,QAAQ,UAAU,MAAM,UAAU,MAAM;AAAA,MACxC,WAAW,UAAU,MAAM,UAAU,SAAS;AAAA,MAC9C,QAAQ,UAAU,MAAM,UAAU,MAAM;AAAA,MACxC,YAAY,UAAU,MAAM,UAAU,UAAU;AAAA,IACpD;AAAA,IACA,YAAY;AAAA,MACR,YAAY,UAAU,MAAM,WAAW,UAAU;AAAA,MACjD,OAAO,UAAU,MAAM,WAAW,KAAK;AAAA,MACvC,QAAQ,UAAU,MAAM,WAAW,MAAM;AAAA,IAC7C;AAAA,IACA,kBAAkB;AAAA,MACd,OAAO,UAAU,MAAM,iBAAiB,KAAK;AAAA,MAC7C,wBAAwB,UAAU,MAAM,iBAAiB,sBAAsB;AAAA,MAC/E,uBAAuB,UAAU,MAAM,iBAAiB,qBAAqB;AAAA,IACjF;AAAA,EACJ;AAIA,QAAM,EAAE,KAAK,IAAI,QAAQ;AACzB,OAAK,MAAM,QAAQ,KAAK,GAAG;AAC3B,OAAK,MAAM,QAAQ,KAAK,GAAG;AAC3B,OAAK,MAAM,QAAQ,KAAK,GAAG;AAC3B,GAAC,KAAK,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG;AAC1C,SAAO;AACX;AACA,SAAS,cAAc;AACnB,MAAI,YAAY,OAAO;AACnB,qBAAiB;AACjB,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACtD;AACA,QAAM,iBAAiB;AACvB,iBAAe,QAAQ;AAAA,IACnB,WAAW;AAAA,MACP,OAAO,CAAC;AAAA,MACR,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,MACP,kBAAkB,CAAC;AAAA,MACnB,WAAW,CAAC;AAAA,MACZ,QAAQ,CAAC;AAAA,MACT,WAAW,CAAC;AAAA,MACZ,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,IACjB;AAAA,IACA,YAAY;AAAA,MACR,YAAY,CAAC;AAAA,MACb,OAAO,CAAC;AAAA,MACR,QAAQ,CAAC;AAAA,IACb;AAAA,IACA,kBAAkB;AAAA,MACd,OAAO,CAAC;AAAA,MACR,wBAAwB,CAAC;AAAA,MACzB,uBAAuB,CAAC;AAAA,IAC5B;AAAA,EACJ;AACA,iBAAe,uBAAuB,CAAC,YAAY;AAC/C,UAAM,EAAE,iBAAiB,IAAI,eAAe;AAC5C,qBAAiB,MAAM,KAAK,QAAQ,KAAK;AACzC,qBAAiB,uBAAuB,KAAK,QAAQ,sBAAsB;AAC3E,qBAAiB,sBAAsB,KAAK,QAAQ,qBAAqB;AAAA,EAC7E;AACA,QAAM,WAAW,QAAQ,IAAI;AAC7B,SAAO;AACX;;;AC5GA,SAAS,gBAAgB,SAAS;AAC9B,SAAO,aAAa,OAAO,KAAK,QAAQ,YAAY;AACxD;;;ACNA,SAAS,eAAe,MAAM,OAAO;AACjC,MAAI,SAAS,SAAS;AAClB,WAAO;AAAA,EACX,OACK;AACD,UAAM,YAAY,QAAQ;AAC1B,WAAO,SAAS,SAAS,YAAY,YAAY;AAAA,EACrD;AACJ;AACA,SAAS,QAAQ,WAAW,KAAK,EAAE,aAAa,GAAG,OAAO,GAAG,MAAAC,MAAK,IAAI,CAAC,GAAG;AACtE,SAAO,CAAC,GAAG,UAAU;AACjB,UAAM,YAAY,OAAO,SAAS,WAAW,OAAO,eAAe,MAAM,KAAK;AAC9E,UAAMC,YAAW,KAAK,IAAI,YAAY,CAAC;AACvC,QAAIC,SAAQ,WAAWD;AACvB,QAAID,OAAM;AACN,YAAM,WAAW,QAAQ;AACzB,YAAM,iBAAiB,2BAA2BA,KAAI;AACtD,MAAAE,SAAQ,eAAeA,SAAQ,QAAQ,IAAI;AAAA,IAC/C;AACA,WAAO,aAAaA;AAAA,EACxB;AACJ;;;ACrBA,SAAS,aAAa,MAAM;AACxB,QAAM,eAAe,CAAC,MAAM,QAAQ,KAAK,CAAC,CAAC;AAC3C,QAAM,YAAY,eAAe,IAAI;AACrC,QAAM,aAAa,KAAK,IAAI,SAAS;AACrC,QAAM,aAAa,KAAK,IAAI,SAAS;AACrC,QAAM,cAAc,KAAK,IAAI,SAAS;AACtC,QAAM,UAAU,KAAK,IAAI,SAAS;AAClC,QAAM,eAAe,YAAY,YAAY,aAAa,OAAO;AACjE,SAAO,eAAe,aAAa,UAAU,IAAI;AACrD;;;ACTA,SAAS,eAAe,aAAa,aAAa,WAAW;AACzD,QAAM,SAAS,MAAM,YAAY,IAAI,UAAU,CAAC;AAChD,QAAM,iBAAiB,MAAM,MAAM,UAAU,QAAQ,OAAO,IAAI;AAChE,QAAM,gBAAgB,YAAY,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,cAAc,CAAC;AAC3E,cAAY,GAAG,WAAW,MAAM;AAC5B,kBAAc,QAAQ,CAAC,gBAAgB,YAAY,CAAC;AACpD,gBAAY,MAAM;AAAA,EACtB,CAAC;AACL;;;ACUA,SAAS,eAAeC,YAAW;AAC/B,QAAM,kBAAkB,CAAC;AAKzB,sBAAoB,UAAU;AAC9B,QAAM,eAAeA,WAAU;AAC/B,sBAAoB,UAAU;AAC9B,QAAM,QAAQ,YAAY,YAAY;AACtC,iBAAe,iBAAiB,OAAOA,UAAS;AAChD,SAAO;AACX;;;ACQA,SAAS,SAAS,YAAY,YAAY,aAAa,SAAS;AAC5D,QAAM,MAAM,UAAU,YAAY,aAAa,OAAO;AACtD,SAAO,eAAe,MAAM,IAAI,WAAW,IAAI,CAAC,CAAC;AACrD;;;AC3CA,IAAM,gBAAgB,CAAC,UAAU,QAAQ,SAAS,MAAM,WAAW;;;ACmBnE,SAAS,YAAY,QAAQ,SAAS;AAClC,QAAM,eAAe,cAAc,MAAM,IAAI,OAAO,IAAI,IAAI;AAC5D,QAAM,QAAQ,YAAY,YAAY;AACtC,eAAa,OAAO,QAAQ,OAAO;AACnC,SAAO;AACX;AACA,SAAS,aAAa,OAAO,QAAQ,SAAS;AAC1C,QAAM,eAAe,MAAM,IAAI;AAC/B,MAAI,kBAAkB;AACtB,MAAI,cAAc;AAClB,MAAI;AACJ,QAAM,OAAO,OAAO,iBAAiB,WAC/B,aAAa,QAAQ,WAAW,EAAE,IAClC;AACN,QAAM,gBAAgB,MAAM;AACxB,QAAI,iBAAiB;AACjB,sBAAgB,KAAK;AACrB,wBAAkB;AAAA,IACtB;AAAA,EACJ;AACA,QAAM,iBAAiB,MAAM;AACzB,kBAAc;AACd,sBAAkB,IAAI,YAAY;AAAA,MAC9B,WAAW,CAAC,SAAS,MAAM,IAAI,CAAC,GAAG,SAAS,WAAW,CAAC;AAAA,MACxD,UAAU,MAAM,YAAY;AAAA,MAC5B,MAAM;AAAA,MACN,WAAW;AAAA,MACX,WAAW;AAAA,MACX,GAAG;AAAA,MACH,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AACA,QAAM,OAAO,CAAC,GAAG,QAAQ;AACrB,kBAAc;AACd,mBAAe,CAAC,WAAW,IAAI,WAAW,QAAQ,IAAI,CAAC;AACvD,UAAM,WAAW,cAAc;AAC/B,WAAO,MAAM,IAAI;AAAA,EACrB,GAAG,aAAa;AAChB,MAAI,cAAc,MAAM,GAAG;AACvB,UAAM,uBAAuB,OAAO,GAAG,UAAU,CAAC,MAAM,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC;AACtF,UAAM,uBAAuB,MAAM,GAAG,WAAW,oBAAoB;AACrE,WAAO,MAAM;AACT,2BAAqB;AACrB,2BAAqB;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,GAAG,MAAM;AACzB,SAAO,OAAO,IAAI,OAAO;AAC7B;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,OAAO,MAAM,WAAW,IAAI,WAAW,CAAC;AACnD;;;AChEA,IAAM,aAAa,CAAC,GAAG,qBAAqB,OAAO,OAAO;AAI1D,IAAM,gBAAgB,CAAC,MAAM,WAAW,KAAK,cAAc,CAAC,CAAC;;;ACZ7D,SAAS,gBAAgB,WAAW;AAChC,MAAI,cAAc;AACd,WAAO;AACX,MAAI,cAAc,WAAW,cAAc;AACvC,WAAO;AACX,MAAI,cAAc,UAAU,cAAc;AACtC,WAAO;AACX,SAAO;AACX;;;ACRA,IAAI,eAAe,CAAC;AACpB,IAAI,QAAQ;AACZ,IAAM,MAAM;AAAA,EACR,KAAK,CAAC,UAAU,WAAW;AACvB,iBAAa,QAAQ,IAAI;AAAA,EAC7B;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,CAAC,OAAO;AACR,cAAQ,SAAS,cAAc,OAAO;AACtC,YAAM,KAAK;AAAA,IACf;AACA,QAAI,UAAU;AACd,eAAW,YAAY,cAAc;AACjC,YAAM,OAAO,aAAa,QAAQ;AAClC,iBAAW,GAAG,QAAQ;AAAA;AACtB,iBAAW,CAAC,UAAU,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAClD,mBAAW,KAAK,QAAQ,KAAK,KAAK;AAAA;AAAA,MACtC;AACA,iBAAW;AAAA,IACf;AACA,UAAM,cAAc;AACpB,aAAS,KAAK,YAAY,KAAK;AAC/B,mBAAe,CAAC;AAAA,EACpB;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,SAAS,MAAM,eAAe;AAC9B,YAAM,cAAc,YAAY,KAAK;AAAA,IACzC;AAAA,EACJ;AACJ;;;AC7BA,SAAS,0BAA0B,eAAe;AAC9C,QAAM,QAAQ,cAAc,MAAM,uDAAuD;AACzF,MAAI,CAAC;AACD,WAAO;AACX,SAAO,EAAE,OAAO,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE;AAC7C;;;ACLA,SAAS,qBAAqB,WAAW;AACrC,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,CAAC;AACD,WAAO;AACX,SAAQ,OAAO,WAAW,SAAS,mBAC/B,OAAO,eAAe,WAAW,mBAAmB;AAC5D;AACA,SAAS,oBAAoB;AACzB,SAAO,SAAS,cAAc,EAAE,OAAO,oBAAoB;AAC/D;;;ACTA,SAAS,UAAU,QAAQ,SAAS;AAChC,SAAO,QAAQ,IAAI,MAAM,KAAK,OAAO,KAAK,QAAQ,IAAI,MAAM,CAAC,EAAE,SAAS;AAC5E;;;ACWA,IAAM,kBAAkB,CAAC,UAAU,SAAS,QAAQ,OAAO,KAAK;AAChE,SAAS,mBAAmB,SAAS;AACjC,QAAM,EAAE,QAAQ,SAAS,SAAS,eAAe,IAAI;AACrD,MAAI,CAAC,SAAS,qBAAqB;AAC/B,WAAO,IAAI,QAAQ,OAAO,YAAY;AAClC,YAAM,OAAO;AACb,cAAQ,IAAI,eAAe,CAAC,CAAC,CAAC;AAAA,IAClC,CAAC;AAAA,EACL;AAMA,MAAI,CAAC,UAAU,QAAQ,OAAO,GAAG;AAC7B,QAAI,IAAI,SAAS;AAAA,MACb,wBAAwB;AAAA,IAC5B,CAAC;AAAA,EACL;AAQA,MAAI,IAAI,kFAAkF,EAAE,6BAA6B,oBAAoB,CAAC;AAC9I,MAAI,OAAO;AACX,QAAM,aAAa,SAAS,oBAAoB,YAAY;AACxD,UAAM,OAAO;AAAA,EAEjB,CAAC;AACD,aAAW,SAAS,QAAQ,MAAM;AAC9B,QAAI,OAAO;AAAA,EACf,CAAC;AACD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,eAAW,MAAM,KAAK,MAAM;AACxB,YAAM,0BAA0B,kBAAkB;AAClD,YAAM,aAAa,CAAC;AAIpB,cAAQ,QAAQ,CAAC,YAAY,WAAW;AAGpC,mBAAW,OAAO,iBAAiB;AAC/B,cAAI,CAAC,WAAW,GAAG;AACf;AACJ,gBAAM,EAAE,WAAAC,YAAW,QAAQ,IAAI,WAAW,GAAG;AAC7C,mBAAS,CAAC,WAAW,cAAc,KAAK,OAAO,QAAQA,UAAS,GAAG;AAC/D,gBAAI,CAAC;AACD;AACJ,kBAAM,eAAe;AAAA,cACjB,GAAG,mBAAmB,gBAAgB,SAAS;AAAA,cAC/C,GAAG,mBAAmB,SAAS,SAAS;AAAA,YAC5C;AACA,kBAAM,OAAO,gBAAgB,GAAG;AAKhC,gBAAI,cAAc,aACd,CAAC,MAAM,QAAQ,cAAc,GAAG;AAChC,oBAAM,eAAe,SAAS,QAAQ,IAAI;AAC1C,+BAAiB,CAAC,cAAc,cAAc;AAAA,YAClD;AAIA,gBAAI,OAAO,aAAa,UAAU,YAAY;AAC1C,2BAAa,QAAQ,aAAa,MAAM,GAAG,CAAC;AAAA,YAChD;AACA,yBAAa,aAAa,aAAa,WAAW,sBAAsB,aAAa,QAAQ;AAC7F,yBAAa,UAAU,aAAa,QAAQ,sBAAsB,aAAa,KAAK;AACpF,kBAAM,YAAY,IAAI,gBAAgB;AAAA,cAClC,GAAG;AAAA,cACH,SAAS,SAAS;AAAA,cAClB,MAAM;AAAA,cACN,eAAe,qBAAqB,IAAI,IAAI,MAAM;AAAA,cAClD,WAAW;AAAA,YACf,CAAC;AACD,uBAAW,KAAK,SAAS;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ,CAAC;AAID,iBAAW,aAAa,yBAAyB;AAC7C,YAAI,UAAU,cAAc;AACxB;AACJ,cAAM,EAAE,OAAO,IAAI;AACnB,YAAI,CAAC,UAAU,EAAE,kBAAkB;AAC/B;AACJ,cAAM,EAAE,cAAc,IAAI;AAC1B,YAAI,CAAC;AACD;AACJ,cAAM,OAAO,0BAA0B,aAAa;AACpD,YAAI,CAAC;AACD;AACJ,cAAM,mBAAmB,QAAQ,IAAI,KAAK,KAAK;AAC/C,YAAI,CAAC,kBAAkB;AAMnB,gBAAM,iBAAiB,KAAK,SAAS,UAAU,WAAW;AAC1D,cAAI,sBAAsB;AAAA,YACtB,GAAG,mBAAmB,gBAAgB,cAAc;AAAA,UACxD;AACA,8BAAoB,aAAa,oBAAoB,WAAW,sBAAsB,oBAAoB,QAAQ;AAClH,gCACI,sBAAsB,mBAAmB;AAC7C,gBAAM,SAAS,wBAAwB,oBAAoB,MAAM,oBAAoB,QAAQ;AAC7F,iBAAO,aAAa;AAAA,YAChB,OAAO,sBAAsB,oBAAoB,SAAS,CAAC;AAAA,YAC3D,UAAU,oBAAoB;AAAA,YAC9B;AAAA,UACJ,CAAC;AACD,qBAAW,KAAK,IAAI,uBAAuB,SAAS,CAAC;AAAA,QACzD,WACS,WAAW,kBAAkB,OAAO,KACzC,WAAW,kBAAkB,MAAM,KACnC,OACK,aAAa,EACb,KAAK,CAAC,aAAa,SAAS,YAAY,GAAG;AAChD,qBAAW,KAAK,IAAI,uBAAuB,SAAS,CAAC;AAAA,QACzD,OACK;AACD,oBAAU,OAAO;AAAA,QACrB;AAAA,MACJ;AACA,cAAQ,IAAI,eAAe,UAAU,CAAC;AAAA,IAC1C,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,WAAW,QAAQ,KAAK;AAC7B,SAAO,SAAS,GAAG,GAAG,UAAU;AACpC;;;ACpJA,IAAI,WAAW,CAAC;AAChB,IAAI,UAAU;AACd,SAAS,OAAO;AACZ,YAAU;AACV,QAAM,CAAC,WAAW,IAAI;AACtB,MAAI;AACA,UAAM,WAAW;AACzB;AACA,SAAS,MAAM,SAAS;AACpB,aAAW,UAAU,OAAO;AAC5B,YAAU;AACV,qBAAmB,OAAO,EAAE,KAAK,CAAC,cAAc;AAC5C,YAAQ,YAAY,SAAS;AAC7B,cAAU,SAAS,QAAQ,IAAI;AAAA,EACnC,CAAC;AACL;AACA,SAAS,eAAe;AAQpB,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,UAAU,SAAS,CAAC;AAC1B,UAAM,EAAE,UAAU,IAAI,QAAQ;AAC9B,QAAI,cAAc,aAAa;AAC3B,YAAM,iBAAiB,SAAS,MAAM,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM;AACnE,YAAM,YAAY,SAAS,MAAM,IAAI,CAAC;AACtC,cAAQ,SAAS,MAAM;AACnB,uBAAe,QAAQ,CAAC,WAAW,OAAO,CAAC;AAAA,MAC/C;AAEA,iBAAW,CAAC,SAAS,GAAG,SAAS;AACjC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,CAAC,WAAW,SAAS,CAAC,GAAG,QAAQ,cAAc,aAAa;AAC5D,SAAK;AAAA,EACT;AACJ;AACA,SAAS,WAAW,SAAS;AACzB,WAAS,KAAK,OAAO;AACrB,YAAU,OAAO,YAAY;AACjC;;;AC9CA,IAAM,wBAAN,MAA4B;AAAA,EACxB,YAAY,QAAQ,UAAU,CAAC,GAAG;AAC9B,SAAK,iBAAiB;AACtB,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,cAAc;AACnB,SAAK,eAAe,IAAI,QAAQ,CAAC,YAAY;AACzC,WAAK,cAAc;AAAA,IACvB,CAAC;AACD,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,MACX,WAAW;AAAA,MACX,GAAG;AAAA,IACP;AACA,eAAW,IAAI;AAAA,EACnB;AAAA,EACA,IAAI,SAAS;AACT,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA,EACA,OAAOC,YAAW,SAAS;AACvB,SAAK,aAAa,UAAUA,YAAW,OAAO;AAC9C,WAAO;AAAA,EACX;AAAA,EACA,IAAIA,YAAW,SAAS;AACpB,SAAK,aAAa,OAAOA,YAAW,OAAO;AAC3C,WAAO;AAAA,EACX;AAAA,EACA,IAAIA,YAAW,SAAS;AACpB,SAAK,aAAa,OAAOA,YAAW,OAAO;AAC3C,WAAO;AAAA,EACX;AAAA,EACA,MAAMA,YAAW,SAAS;AACtB,SAAK,aAAa,SAASA,YAAW,OAAO;AAC7C,WAAO;AAAA,EACX;AAAA,EACA,KAAKA,YAAW,SAAS;AACrB,SAAK,aAAa,QAAQA,YAAW,OAAO;AAC5C,WAAO;AAAA,EACX;AAAA,EACA,UAAU,SAAS;AACf,SAAK,aAAa,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO;AAClD,SAAK,aAAa,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;AACjD,WAAO;AAAA,EACX;AAAA,EACA,aAAa,QAAQA,YAAW,UAAU,CAAC,GAAG;AAC1C,UAAM,EAAE,gBAAgB,QAAQ,IAAI;AACpC,QAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AAC9B,cAAQ,IAAI,gBAAgB,CAAC,CAAC;AAAA,IAClC;AACA,UAAM,aAAa,QAAQ,IAAI,cAAc;AAC7C,eAAW,MAAM,IAAI,EAAE,WAAAA,YAAW,QAAQ;AAAA,EAC9C;AAAA,EACA,KAAK,SAAS,QAAQ;AAClB,WAAO,KAAK,aAAa,KAAK,SAAS,MAAM;AAAA,EACjD;AACJ;AACA,SAAS,YAAY,QAAQ,iBAAiB,CAAC,GAAG;AAC9C,SAAO,IAAI,sBAAsB,QAAQ,cAAc;AAC3D;;;ACrDA,IAAM,OAAO;AAMb,IAAM,aAAa,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC/C,MAAI,GAAG,IAAI,CAACC,aAAY,YAAYA,QAAO;AAC3C,SAAO;AACX,GAAG,CAAC,CAAC;;;ACjBL,SAAS,eAAeC,YAAW;AAC/B,SAAO,OAAOA,eAAc,YAAY,CAAC,MAAM,QAAQA,UAAS;AACpE;;;ACCA,SAAS,gBAAgB,SAASC,YAAW,OAAO,eAAe;AAC/D,MAAI,OAAO,YAAY,YAAY,eAAeA,UAAS,GAAG;AAC1D,WAAO,gBAAgB,SAAS,OAAO,aAAa;AAAA,EACxD,WACS,mBAAmB,UAAU;AAClC,WAAO,MAAM,KAAK,OAAO;AAAA,EAC7B,WACS,MAAM,QAAQ,OAAO,GAAG;AAC7B,WAAO;AAAA,EACX,OACK;AACD,WAAO,CAAC,OAAO;AAAA,EACnB;AACJ;;;AChBA,SAAS,wBAAwB,UAAU,QAAQ,cAAc;AAC7D,SAAO,YAAY,SAAS;AAChC;;;ACEA,SAAS,aAAaC,UAASC,OAAM,MAAM,QAAQ;AAC/C,MAAI,OAAOA,UAAS,UAAU;AAC1B,WAAOA;AAAA,EACX,WACSA,MAAK,WAAW,GAAG,KAAKA,MAAK,WAAW,GAAG,GAAG;AACnD,WAAO,KAAK,IAAI,GAAGD,WAAU,WAAWC,KAAI,CAAC;AAAA,EACjD,WACSA,UAAS,KAAK;AACnB,WAAO;AAAA,EACX,WACSA,MAAK,WAAW,GAAG,GAAG;AAC3B,WAAO,KAAK,IAAI,GAAG,OAAO,WAAWA,MAAK,MAAM,CAAC,CAAC,CAAC;AAAA,EACvD,OACK;AACD,WAAO,OAAO,IAAIA,KAAI,KAAKD;AAAA,EAC/B;AACJ;;;ACjBA,SAAS,eAAe,UAAU,WAAW,SAAS;AAClD,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAM,WAAW,SAAS,CAAC;AAC3B,QAAI,SAAS,KAAK,aAAa,SAAS,KAAK,SAAS;AAClD,iBAAW,UAAU,QAAQ;AAE7B;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,UAAUE,YAAW,QAAQ,QAAQ,WAAW,SAAS;AAM3E,iBAAe,UAAU,WAAW,OAAO;AAC3C,WAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACvC,aAAS,KAAK;AAAA,MACV,OAAOA,WAAU,CAAC;AAAA,MAClB,IAAI,UAAU,WAAW,SAAS,OAAO,CAAC,CAAC;AAAA,MAC3C,QAAQ,oBAAoB,QAAQ,CAAC;AAAA,IACzC,CAAC;AAAA,EACL;AACJ;;;ACrBA,SAAS,eAAe,OAAO,QAAQ;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,CAAC,IAAI,MAAM,CAAC,KAAK,SAAS;AAAA,EACpC;AACJ;;;ACVA,SAAS,cAAc,GAAG,GAAG;AACzB,MAAI,EAAE,OAAO,EAAE,IAAI;AACf,QAAI,EAAE,UAAU;AACZ,aAAO;AACX,QAAI,EAAE,UAAU;AACZ,aAAO;AACX,WAAO;AAAA,EACX,OACK;AACD,WAAO,EAAE,KAAK,EAAE;AAAA,EACpB;AACJ;;;ACFA,IAAM,uBAAuB;AAC7B,IAAM,aAAa;AACnB,SAAS,6BAA6B,UAAU,EAAE,oBAAoB,CAAC,GAAG,GAAG,mBAAmB,IAAI,CAAC,GAAG,OAAO,YAAY;AACvH,QAAM,kBAAkB,kBAAkB,YAAY;AACtD,QAAM,uBAAuB,oBAAI,IAAI;AACrC,QAAM,YAAY,oBAAI,IAAI;AAC1B,QAAM,eAAe,CAAC;AACtB,QAAM,aAAa,oBAAI,IAAI;AAC3B,MAAI,WAAW;AACf,MAAI,cAAc;AAClB,MAAI,gBAAgB;AAMpB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAM,UAAU,SAAS,CAAC;AAI1B,QAAI,OAAO,YAAY,UAAU;AAC7B,iBAAW,IAAI,SAAS,WAAW;AACnC;AAAA,IACJ,WACS,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC9B,iBAAW,IAAI,QAAQ,MAAM,aAAa,aAAa,QAAQ,IAAI,UAAU,UAAU,CAAC;AACxF;AAAA,IACJ;AACA,QAAI,CAAC,SAASC,YAAW,aAAa,CAAC,CAAC,IAAI;AAK5C,QAAI,WAAW,OAAO,QAAW;AAC7B,oBAAc,aAAa,aAAa,WAAW,IAAI,UAAU,UAAU;AAAA,IAC/E;AAKA,QAAI,cAAc;AAClB,UAAM,uBAAuB,CAAC,gBAAgB,iBAAiB,eAAe,eAAe,GAAG,cAAc,MAAM;AAChH,YAAM,uBAAuB,gBAAgB,cAAc;AAC3D,YAAM,EAAE,OAAAC,SAAQ,GAAG,QAAQ,cAAc,oBAAoB,GAAG,OAAO,aAAa,QAAQ,YAAY,cAAc,GAAG,GAAG,oBAAoB,IAAI;AACpJ,UAAI,EAAE,MAAAC,QAAO,kBAAkB,QAAQ,WAAW,SAAS,IAAI;AAI/D,YAAM,kBAAkB,OAAOD,WAAU,aACnCA,OAAM,cAAc,WAAW,IAC/BA;AAIN,YAAM,eAAe,qBAAqB;AAC1C,YAAM,kBAAkB,YAAY,IAAI,IAClC,OACA,aAAa,QAAQ,WAAW;AACtC,UAAI,gBAAgB,KAAK,iBAAiB;AAOtC,YAAI,gBAAgB;AACpB,YAAI,iBAAiB,KACjB,uBAAuB,oBAAoB,GAAG;AAC9C,gBAAM,QAAQ,qBAAqB,CAAC,IAAI,qBAAqB,CAAC;AAC9D,0BAAgB,KAAK,IAAI,KAAK;AAAA,QAClC;AACA,cAAM,mBAAmB,EAAE,GAAG,oBAAoB;AAClD,YAAI,aAAa,QAAW;AACxB,2BAAiB,WAAW,sBAAsB,QAAQ;AAAA,QAC9D;AACA,cAAM,eAAe,sBAAsB,kBAAkB,eAAe,eAAe;AAC3F,QAAAC,QAAO,aAAa;AACpB,mBAAW,aAAa;AAAA,MAC5B;AACA,mBAAa,WAAW;AACxB,YAAM,YAAY,cAAc;AAIhC,UAAI,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,GAAG;AACtC,cAAM,CAAC,IAAI;AAAA,MACf;AAIA,YAAM,YAAY,MAAM,SAAS,qBAAqB;AACtD,kBAAY,KAAK,WAAW,OAAO,SAAS;AAM5C,2BAAqB,WAAW,KAC5B,qBAAqB,QAAQ,IAAI;AAIrC,UAAI,QAAQ;AACR,kBAAU,SAAS,YAAY,+CAA+C,mBAAmB;AACjG,mBAAW,wBAAwB,UAAU,MAAM;AACnD,cAAM,oBAAoB,CAAC,GAAG,oBAAoB;AAClD,cAAM,gBAAgB,CAAC,GAAG,KAAK;AAC/B,QAAAA,QAAO,MAAM,QAAQA,KAAI,IAAI,CAAC,GAAGA,KAAI,IAAI,CAACA,KAAI;AAC9C,cAAM,eAAe,CAAC,GAAGA,KAAI;AAC7B,iBAAS,cAAc,GAAG,cAAc,QAAQ,eAAe;AAC3D,+BAAqB,KAAK,GAAG,iBAAiB;AAC9C,mBAAS,gBAAgB,GAAG,gBAAgB,kBAAkB,QAAQ,iBAAiB;AACnF,kBAAM,KAAK,cAAc,aAAa,KAAK,cAAc,EAAE;AAC3D,YAAAA,MAAK,KAAK,kBAAkB,IACtB,WACA,oBAAoB,cAAc,gBAAgB,CAAC,CAAC;AAAA,UAC9D;AAAA,QACJ;AACA,uBAAe,OAAO,MAAM;AAAA,MAChC;AACA,YAAM,aAAa,YAAY;AAI/B,mBAAa,eAAe,sBAAsBA,OAAM,OAAO,WAAW,UAAU;AACpF,oBAAc,KAAK,IAAI,kBAAkB,UAAU,WAAW;AAC9D,sBAAgB,KAAK,IAAI,YAAY,aAAa;AAAA,IACtD;AACA,QAAI,cAAc,OAAO,GAAG;AACxB,YAAM,kBAAkB,mBAAmB,SAAS,SAAS;AAC7D,2BAAqBF,YAAW,YAAY,iBAAiB,WAAW,eAAe,CAAC;AAAA,IAC5F,OACK;AACD,YAAM,WAAW,gBAAgB,SAASA,YAAW,OAAO,YAAY;AACxE,YAAM,cAAc,SAAS;AAI7B,eAAS,eAAe,GAAG,eAAe,aAAa,gBAAgB;AAInE,QAAAA,aAAYA;AACZ,qBAAa;AACb,cAAM,cAAc,SAAS,YAAY;AACzC,cAAM,kBAAkB,mBAAmB,aAAa,SAAS;AACjE,mBAAW,OAAOA,YAAW;AACzB,+BAAqBA,WAAU,GAAG,GAAGG,oBAAmB,YAAY,GAAG,GAAG,iBAAiB,KAAK,eAAe,GAAG,cAAc,WAAW;AAAA,QAC/I;AAAA,MACJ;AAAA,IACJ;AACA,eAAW;AACX,mBAAe;AAAA,EACnB;AAIA,YAAU,QAAQ,CAAC,gBAAgB,YAAY;AAC3C,eAAW,OAAO,gBAAgB;AAC9B,YAAM,gBAAgB,eAAe,GAAG;AAIxC,oBAAc,KAAK,aAAa;AAChC,YAAMH,aAAY,CAAC;AACnB,YAAM,cAAc,CAAC;AACrB,YAAM,cAAc,CAAC;AAKrB,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,cAAM,EAAE,IAAI,OAAO,OAAO,IAAI,cAAc,CAAC;AAC7C,QAAAA,WAAU,KAAK,KAAK;AACpB,oBAAY,KAAK,SAAS,GAAG,eAAe,EAAE,CAAC;AAC/C,oBAAY,KAAK,UAAU,SAAS;AAAA,MACxC;AAMA,UAAI,YAAY,CAAC,MAAM,GAAG;AACtB,oBAAY,QAAQ,CAAC;AACrB,QAAAA,WAAU,QAAQA,WAAU,CAAC,CAAC;AAC9B,oBAAY,QAAQ,oBAAoB;AAAA,MAC5C;AAMA,UAAI,YAAY,YAAY,SAAS,CAAC,MAAM,GAAG;AAC3C,oBAAY,KAAK,CAAC;AAClB,QAAAA,WAAU,KAAK,IAAI;AAAA,MACvB;AACA,UAAI,CAAC,qBAAqB,IAAI,OAAO,GAAG;AACpC,6BAAqB,IAAI,SAAS;AAAA,UAC9B,WAAW,CAAC;AAAA,UACZ,YAAY,CAAC;AAAA,QACjB,CAAC;AAAA,MACL;AACA,YAAM,aAAa,qBAAqB,IAAI,OAAO;AACnD,iBAAW,UAAU,GAAG,IAAIA;AAC5B,iBAAW,WAAW,GAAG,IAAI;AAAA,QACzB,GAAG;AAAA,QACH,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,GAAG;AAAA,MACP;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,SAAS,mBAAmB,SAAS,WAAW;AAC5C,GAAC,UAAU,IAAI,OAAO,KAAK,UAAU,IAAI,SAAS,CAAC,CAAC;AACpD,SAAO,UAAU,IAAI,OAAO;AAChC;AACA,SAAS,iBAAiB,MAAM,WAAW;AACvC,MAAI,CAAC,UAAU,IAAI;AACf,cAAU,IAAI,IAAI,CAAC;AACvB,SAAO,UAAU,IAAI;AACzB;AACA,SAAS,gBAAgBA,YAAW;AAChC,SAAO,MAAM,QAAQA,UAAS,IAAIA,aAAY,CAACA,UAAS;AAC5D;AACA,SAASG,oBAAmB,YAAY,KAAK;AACzC,SAAO,cAAc,WAAW,GAAG,IAC7B;AAAA,IACE,GAAG;AAAA,IACH,GAAG,WAAW,GAAG;AAAA,EACrB,IACE,EAAE,GAAG,WAAW;AAC1B;AACA,IAAM,WAAW,CAAC,aAAa,OAAO,aAAa;AACnD,IAAM,yBAAyB,CAACH,eAAcA,WAAU,MAAM,QAAQ;;;ACtPtE,IAAM,qBAAqB,oBAAI,QAAQ;;;ACAvC,IAAM,oBAAoB,CAAC,MAAM;AAC7B,SAAO,MAAM,QAAQ,CAAC;AAC1B;;;ACFA,SAAS,cAAc,eAAe;AAClC,QAAM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACrB,iBAAe,OAAO,QAAQ,CAAC,OAAO,QAAQ;AAC1C,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI;AAC1B,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,YAAY;AAAA,EACtC,CAAC;AACD,SAAO;AACX;AACA,SAAS,wBAAwB,OAAO,YAAY,QAAQ,eAAe;AAIvE,MAAI,OAAO,eAAe,YAAY;AAClC,UAAM,CAACI,UAAS,QAAQ,IAAI,cAAc,aAAa;AACvD,iBAAa,WAAW,WAAW,SAAY,SAAS,MAAM,QAAQA,UAAS,QAAQ;AAAA,EAC3F;AAKA,MAAI,OAAO,eAAe,UAAU;AAChC,iBAAa,MAAM,YAAY,MAAM,SAAS,UAAU;AAAA,EAC5D;AAMA,MAAI,OAAO,eAAe,YAAY;AAClC,UAAM,CAACA,UAAS,QAAQ,IAAI,cAAc,aAAa;AACvD,iBAAa,WAAW,WAAW,SAAY,SAAS,MAAM,QAAQA,UAAS,QAAQ;AAAA,EAC3F;AACA,SAAO;AACX;;;AC/BA,SAAS,eAAe,eAAe,YAAY,QAAQ;AACvD,QAAM,QAAQ,cAAc,SAAS;AACrC,SAAO,wBAAwB,OAAO,YAAY,WAAW,SAAY,SAAS,MAAM,QAAQ,aAAa;AACjH;;;ACGA,SAAS,eAAe,eAAe,KAAK,OAAO;AAC/C,MAAI,cAAc,SAAS,GAAG,GAAG;AAC7B,kBAAc,SAAS,GAAG,EAAE,IAAI,KAAK;AAAA,EACzC,OACK;AACD,kBAAc,SAAS,KAAK,YAAY,KAAK,CAAC;AAAA,EAClD;AACJ;AACA,SAAS,6BAA6B,GAAG;AAErC,SAAO,kBAAkB,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,KAAK,IAAI;AACzD;AACA,SAAS,UAAU,eAAe,YAAY;AAC1C,QAAM,WAAW,eAAe,eAAe,UAAU;AACzD,MAAI,EAAE,gBAAgB,CAAC,GAAG,aAAa,CAAC,GAAG,GAAG,OAAO,IAAI,YAAY,CAAC;AACtE,WAAS,EAAE,GAAG,QAAQ,GAAG,cAAc;AACvC,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,6BAA6B,OAAO,GAAG,CAAC;AACtD,mBAAe,eAAe,KAAK,KAAK;AAAA,EAC5C;AACJ;;;AC1BA,SAAS,wBAAwB,OAAO;AACpC,SAAO,QAAQ,cAAc,KAAK,KAAK,MAAM,GAAG;AACpD;;;ACDA,SAAS,qBAAqB,eAAe,KAAK;AAC9C,QAAM,aAAa,cAAc,SAAS,YAAY;AAKtD,MAAI,wBAAwB,UAAU,GAAG;AACrC,WAAO,WAAW,IAAI,GAAG;AAAA,EAC7B,WACS,CAAC,cAAc,mBAAmB,YAAY;AACnD,UAAM,gBAAgB,IAAI,mBAAmB,WAAW,MAAM;AAC9D,kBAAc,SAAS,cAAc,aAAa;AAClD,kBAAc,IAAI,GAAG;AAAA,EACzB;AACJ;;;ACdA,IAAMC,eAAc,CAAC,QAAQ,IAAI,QAAQ,oBAAoB,OAAO,EAAE,YAAY;;;ACDlF,IAAM,wBAAwB;AAC9B,IAAM,+BAA+B,UAAUC,aAAY,qBAAqB;;;ACDhF,SAAS,qBAAqB,eAAe;AACzC,SAAO,cAAc,MAAM,4BAA4B;AAC3D;;;ACJA,IAAMC,aAAY,CAAC,UAAU,UAAU;AACvC,SAASC,kBAAiBC,YAAW,EAAE,QAAQ,aAAa,OAAO,GAAG,eAAe;AACjF,QAAM,oBAAoBA,WAAU,OAAOF,UAAS;AACpD,QAAM,QAAQ,UAAU,eAAe,UAAU,SAAS,MAAM,IAC1D,IACA,kBAAkB,SAAS;AACjC,SAAO,CAAC,SAAS,kBAAkB,SAC7B,kBAAkB,KAAK,IACvB;AACV;;;ACPA,IAAM,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AACf;AACA,IAAM,yBAAyB,CAAC,YAAY;AAAA,EACxC,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS,WAAW,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AAAA,EAC7C,WAAW;AACf;AACA,IAAM,sBAAsB;AAAA,EACxB,MAAM;AAAA,EACN,UAAU;AACd;AAKA,IAAM,OAAO;AAAA,EACT,MAAM;AAAA,EACN,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AAAA,EACzB,UAAU;AACd;AACA,IAAM,uBAAuB,CAAC,UAAU,EAAE,WAAAG,WAAU,MAAM;AACtD,MAAIA,WAAU,SAAS,GAAG;AACtB,WAAO;AAAA,EACX,WACS,eAAe,IAAI,QAAQ,GAAG;AACnC,WAAO,SAAS,WAAW,OAAO,IAC5B,uBAAuBA,WAAU,CAAC,CAAC,IACnC;AAAA,EACV;AACA,SAAO;AACX;;;AChCA,SAAS,oBAAoB,EAAE,MAAM,OAAO,QAAQ,eAAe,iBAAiB,kBAAkB,QAAQ,YAAY,aAAa,MAAM,SAAS,GAAG,WAAW,GAAG;AACnK,SAAO,CAAC,CAAC,OAAO,KAAK,UAAU,EAAE;AACrC;;;ACDA,IAAM,qBAAqB,CAAC,MAAM,OAAO,QAAQ,aAAa,CAAC,GAAG,SAAS,cAAc,CAAC,eAAe;AACrG,QAAM,kBAAkB,mBAAmB,YAAY,IAAI,KAAK,CAAC;AAMjE,QAAMC,SAAQ,gBAAgB,SAAS,WAAW,SAAS;AAK3D,MAAI,EAAE,UAAU,EAAE,IAAI;AACtB,YAAU,UAAU,sBAAsBA,MAAK;AAC/C,QAAM,UAAU;AAAA,IACZ,WAAW,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,MAAM;AAAA,IACzD,MAAM;AAAA,IACN,UAAU,MAAM,YAAY;AAAA,IAC5B,GAAG;AAAA,IACH,OAAO,CAAC;AAAA,IACR,UAAU,CAAC,MAAM;AACb,YAAM,IAAI,CAAC;AACX,sBAAgB,YAAY,gBAAgB,SAAS,CAAC;AAAA,IAC1D;AAAA,IACA,YAAY,MAAM;AACd,iBAAW;AACX,sBAAgB,cAAc,gBAAgB,WAAW;AAAA,IAC7D;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,SAAS,YAAY,SAAY;AAAA,EACrC;AAKA,MAAI,CAAC,oBAAoB,eAAe,GAAG;AACvC,WAAO,OAAO,SAAS,qBAAqB,MAAM,OAAO,CAAC;AAAA,EAC9D;AAMA,UAAQ,aAAa,QAAQ,WAAW,sBAAsB,QAAQ,QAAQ;AAC9E,UAAQ,gBAAgB,QAAQ,cAAc,sBAAsB,QAAQ,WAAW;AAIvF,MAAI,QAAQ,SAAS,QAAW;AAC5B,YAAQ,UAAU,CAAC,IAAI,QAAQ;AAAA,EACnC;AACA,MAAI,aAAa;AACjB,MAAI,QAAQ,SAAS,SAChB,QAAQ,aAAa,KAAK,CAAC,QAAQ,aAAc;AAClD,yBAAqB,OAAO;AAC5B,QAAI,QAAQ,UAAU,GAAG;AACrB,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,MAAI,mBAAmB,qBACnB,mBAAmB,gBAAgB;AACnC,iBAAa;AACb,yBAAqB,OAAO;AAC5B,YAAQ,QAAQ;AAAA,EACpB;AAKA,UAAQ,eAAe,CAAC,gBAAgB,QAAQ,CAAC,gBAAgB;AAMjE,MAAI,cAAc,CAAC,aAAa,MAAM,IAAI,MAAM,QAAW;AACvD,UAAM,gBAAgBC,kBAAiB,QAAQ,WAAW,eAAe;AACzE,QAAI,kBAAkB,QAAW;AAC7B,YAAM,OAAO,MAAM;AACf,gBAAQ,SAAS,aAAa;AAC9B,gBAAQ,WAAW;AAAA,MACvB,CAAC;AACD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,gBAAgB,SACjB,IAAI,YAAY,OAAO,IACvB,IAAI,0BAA0B,OAAO;AAC/C;;;ACnFA,SAAS,qBAAqB,EAAE,eAAe,eAAe,GAAG,KAAK;AAClE,QAAM,cAAc,cAAc,eAAe,GAAG,KAAK,eAAe,GAAG,MAAM;AACjF,iBAAe,GAAG,IAAI;AACtB,SAAO;AACX;AACA,SAAS,cAAc,eAAe,qBAAqB,EAAE,OAAAC,SAAQ,GAAG,oBAAoB,KAAK,IAAI,CAAC,GAAG;AACrG,MAAI,EAAE,aAAa,cAAc,qBAAqB,GAAG,eAAe,GAAG,OAAO,IAAI;AACtF,MAAI;AACA,iBAAa;AACjB,QAAM,aAAa,CAAC;AACpB,QAAM,qBAAqB,QACvB,cAAc,kBACd,cAAc,eAAe,SAAS,EAAE,IAAI;AAChD,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,cAAc,SAAS,KAAK,cAAc,aAAa,GAAG,KAAK,IAAI;AACjF,UAAM,cAAc,OAAO,GAAG;AAC9B,QAAI,gBAAgB,UACf,sBACG,qBAAqB,oBAAoB,GAAG,GAAI;AACpD;AAAA,IACJ;AACA,UAAM,kBAAkB;AAAA,MACpB,OAAAA;AAAA,MACA,GAAG,mBAAmB,cAAc,CAAC,GAAG,GAAG;AAAA,IAC/C;AAIA,UAAM,eAAe,MAAM,IAAI;AAC/B,QAAI,iBAAiB,UACjB,CAAC,MAAM,eACP,CAAC,MAAM,QAAQ,WAAW,KAC1B,gBAAgB,gBAChB,CAAC,gBAAgB,UAAU;AAC3B;AAAA,IACJ;AAKA,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB;AAC/B,YAAM,WAAW,qBAAqB,aAAa;AACnD,UAAI,UAAU;AACV,cAAM,YAAY,OAAO,uBAAuB,UAAU,KAAK,KAAK;AACpE,YAAI,cAAc,MAAM;AACpB,0BAAgB,YAAY;AAC5B,sBAAY;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,yBAAqB,eAAe,GAAG;AACvC,UAAM,MAAM,mBAAmB,KAAK,OAAO,aAAa,cAAc,sBAAsB,eAAe,IAAI,GAAG,IAC5G,EAAE,MAAM,MAAM,IACd,iBAAiB,eAAe,SAAS,CAAC;AAChD,UAAM,YAAY,MAAM;AACxB,QAAI,WAAW;AACX,iBAAW,KAAK,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,MAAI,eAAe;AACf,YAAQ,IAAI,UAAU,EAAE,KAAK,MAAM;AAC/B,YAAM,OAAO,MAAM;AACf,yBAAiB,UAAU,eAAe,aAAa;AAAA,MAC3D,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACA,SAAO;AACX;;;AC3EA,SAAS,wBAAwB,EAAE,KAAK,MAAM,OAAO,OAAQ,GAAG;AAC5D,SAAO;AAAA,IACH,GAAG,EAAE,KAAK,MAAM,KAAK,MAAM;AAAA,IAC3B,GAAG,EAAE,KAAK,KAAK,KAAK,OAAO;AAAA,EAC/B;AACJ;AASA,SAAS,mBAAmBC,QAAO,gBAAgB;AAC/C,MAAI,CAAC;AACD,WAAOA;AACX,QAAM,UAAU,eAAe,EAAE,GAAGA,OAAM,MAAM,GAAGA,OAAM,IAAI,CAAC;AAC9D,QAAM,cAAc,eAAe,EAAE,GAAGA,OAAM,OAAO,GAAGA,OAAM,OAAO,CAAC;AACtE,SAAO;AAAA,IACH,KAAK,QAAQ;AAAA,IACb,MAAM,QAAQ;AAAA,IACd,QAAQ,YAAY;AAAA,IACpB,OAAO,YAAY;AAAA,EACvB;AACJ;;;AC3BA,SAAS,mBAAmB,UAAU,gBAAgB;AAClD,SAAO,wBAAwB,mBAAmB,SAAS,sBAAsB,GAAG,cAAc,CAAC;AACvG;;;ACLA,IAAM,eAAe;AAAA,EACjB,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM,CAAC,MAAM;AAAA,EACb,MAAM,CAAC,QAAQ,cAAc;AAAA,EAC7B,OAAO,CAAC,YAAY;AAAA,EACpB,OAAO,CAAC,cAAc,gBAAgB,YAAY;AAAA,EAClD,KAAK,CAAC,YAAY,SAAS,cAAc,aAAa;AAAA,EACtD,KAAK,CAAC,SAAS,cAAc,qBAAqB,UAAU;AAAA,EAC5D,QAAQ,CAAC,eAAe,mBAAmB,iBAAiB;AAAA,EAC5D,QAAQ,CAAC,UAAU,UAAU;AACjC;AACA,IAAM,qBAAqB,CAAC;AAC5B,WAAW,OAAO,cAAc;AAC5B,qBAAmB,GAAG,IAAI;AAAA,IACtB,WAAW,CAAC,UAAU,aAAa,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC;AAAA,EACxE;AACJ;;;ACfA,IAAM,aAAa,OAAO,EAAE,KAAK,GAAG,KAAK,EAAE;AAC3C,IAAM,YAAY,OAAO;AAAA,EACrB,GAAG,WAAW;AAAA,EACd,GAAG,WAAW;AAClB;;;ACdA,IAAM,YAAY,OAAO,WAAW;;;ACCpC,IAAM,uBAAuB,EAAE,SAAS,KAAK;AAC7C,IAAM,2BAA2B,EAAE,SAAS,MAAM;;;ACClD,SAAS,2BAA2B;AAChC,2BAAyB,UAAU;AACnC,MAAI,CAAC;AACD;AACJ,MAAI,OAAO,YAAY;AACnB,UAAM,mBAAmB,OAAO,WAAW,0BAA0B;AACrE,UAAM,8BAA8B,MAAO,qBAAqB,UAAU,iBAAiB;AAC3F,qBAAiB,iBAAiB,UAAU,2BAA2B;AACvE,gCAA4B;AAAA,EAChC,OACK;AACD,yBAAqB,UAAU;AAAA,EACnC;AACJ;;;AChBA,SAAS,oBAAoB,GAAG;AAC5B,SAAQ,MAAM,QACV,OAAO,MAAM,YACb,OAAO,EAAE,UAAU;AAC3B;;;ACDA,SAAS,eAAe,GAAG;AACvB,SAAO,OAAO,MAAM,YAAY,MAAM,QAAQ,CAAC;AACnD;;;ACLA,IAAM,uBAAuB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,eAAe,CAAC,WAAW,GAAG,oBAAoB;;;ACLxD,SAAS,sBAAsB,OAAO;AAClC,SAAQ,oBAAoB,MAAM,OAAO,KACrC,aAAa,KAAK,CAAC,SAAS,eAAe,MAAM,IAAI,CAAC,CAAC;AAC/D;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,QAAQ,sBAAsB,KAAK,KAAK,MAAM,QAAQ;AACjE;;;ACRA,SAAS,4BAA4B,SAASC,OAAM,MAAM;AACtD,aAAW,OAAOA,OAAM;AACpB,UAAM,YAAYA,MAAK,GAAG;AAC1B,UAAM,YAAY,KAAK,GAAG;AAC1B,QAAI,cAAc,SAAS,GAAG;AAK1B,cAAQ,SAAS,KAAK,SAAS;AAAA,IACnC,WACS,cAAc,SAAS,GAAG;AAK/B,cAAQ,SAAS,KAAK,YAAY,WAAW,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,IACpE,WACS,cAAc,WAAW;AAM9B,UAAI,QAAQ,SAAS,GAAG,GAAG;AACvB,cAAM,gBAAgB,QAAQ,SAAS,GAAG;AAC1C,YAAI,cAAc,cAAc,MAAM;AAClC,wBAAc,KAAK,SAAS;AAAA,QAChC,WACS,CAAC,cAAc,aAAa;AACjC,wBAAc,IAAI,SAAS;AAAA,QAC/B;AAAA,MACJ,OACK;AACD,cAAM,cAAc,QAAQ,eAAe,GAAG;AAC9C,gBAAQ,SAAS,KAAK,YAAY,gBAAgB,SAAY,cAAc,WAAW,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,MAC9G;AAAA,IACJ;AAAA,EACJ;AAEA,aAAW,OAAO,MAAM;AACpB,QAAIA,MAAK,GAAG,MAAM;AACd,cAAQ,YAAY,GAAG;AAAA,EAC/B;AACA,SAAOA;AACX;;;ACpCA,IAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAKA,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhB,4BAA4B,QAAQ,YAAY,gBAAgB;AAC5D,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,YAAY,EAAE,QAAQ,OAAO,iBAAiB,qBAAqB,uBAAuB,YAAa,GAAG,UAAU,CAAC,GAAG;AAKpH,SAAK,UAAU;AAIf,SAAK,WAAW,oBAAI,IAAI;AAIxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAQ7B,SAAK,qBAAqB;AAM1B,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,mBAAmB;AAIxB,SAAK,WAAW,CAAC;AAKjB,SAAK,qBAAqB,oBAAI,IAAI;AAMlC,SAAK,mBAAmB,CAAC;AAIzB,SAAK,SAAS,CAAC;AAMf,SAAK,yBAAyB,CAAC;AAC/B,SAAK,eAAe,MAAM,KAAK,OAAO,UAAU,KAAK,YAAY;AACjE,SAAK,SAAS,MAAM;AAChB,UAAI,CAAC,KAAK;AACN;AACJ,WAAK,aAAa;AAClB,WAAK,eAAe,KAAK,SAAS,KAAK,aAAa,KAAK,MAAM,OAAO,KAAK,UAAU;AAAA,IACzF;AACA,SAAK,oBAAoB;AACzB,SAAK,iBAAiB,MAAM;AACxB,YAAMC,OAAM,KAAK,IAAI;AACrB,UAAI,KAAK,oBAAoBA,MAAK;AAC9B,aAAK,oBAAoBA;AACzB,cAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AAAA,MACzC;AAAA,IACJ;AACA,UAAM,EAAE,cAAc,YAAY,IAAI;AACtC,SAAK,eAAe;AACpB,SAAK,aAAa,EAAE,GAAG,aAAa;AACpC,SAAK,gBAAgB,MAAM,UAAU,EAAE,GAAG,aAAa,IAAI,CAAC;AAC5D,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,QAAQ,SAAS,OAAO,QAAQ,IAAI;AACzC,SAAK,sBAAsB;AAC3B,SAAK,UAAU;AACf,SAAK,wBAAwB,QAAQ,qBAAqB;AAC1D,SAAK,wBAAwB,sBAAsB,KAAK;AACxD,SAAK,gBAAgB,cAAc,KAAK;AACxC,QAAI,KAAK,eAAe;AACpB,WAAK,kBAAkB,oBAAI,IAAI;AAAA,IACnC;AACA,SAAK,yBAAyB,QAAQ,UAAU,OAAO,OAAO;AAW9D,UAAM,EAAE,YAAY,GAAG,oBAAoB,IAAI,KAAK,4BAA4B,OAAO,CAAC,GAAG,IAAI;AAC/F,eAAW,OAAO,qBAAqB;AACnC,YAAM,QAAQ,oBAAoB,GAAG;AACrC,UAAI,aAAa,GAAG,MAAM,UAAa,cAAc,KAAK,GAAG;AACzD,cAAM,IAAI,aAAa,GAAG,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,UAAU;AACZ,SAAK,UAAU;AACf,uBAAmB,IAAI,UAAU,IAAI;AACrC,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,UAAU;AAC9C,WAAK,WAAW,MAAM,QAAQ;AAAA,IAClC;AACA,QAAI,KAAK,UAAU,KAAK,iBAAiB,CAAC,KAAK,uBAAuB;AAClE,WAAK,wBAAwB,KAAK,OAAO,gBAAgB,IAAI;AAAA,IACjE;AACA,SAAK,OAAO,QAAQ,CAAC,OAAO,QAAQ,KAAK,kBAAkB,KAAK,KAAK,CAAC;AACtE,QAAI,CAAC,yBAAyB,SAAS;AACnC,+BAAyB;AAAA,IAC7B;AACA,SAAK,qBACD,KAAK,wBAAwB,UACvB,QACA,KAAK,wBAAwB,WACzB,OACA,qBAAqB;AACnC,QAAI,MAAuC;AACvC,eAAS,KAAK,uBAAuB,MAAM,0FAA0F,yBAAyB;AAAA,IAClK;AACA,SAAK,QAAQ,SAAS,IAAI;AAC1B,SAAK,OAAO,KAAK,OAAO,KAAK,eAAe;AAAA,EAChD;AAAA,EACA,UAAU;AACN,SAAK,cAAc,KAAK,WAAW,QAAQ;AAC3C,gBAAY,KAAK,YAAY;AAC7B,gBAAY,KAAK,MAAM;AACvB,SAAK,mBAAmB,QAAQ,CAAC,WAAW,OAAO,CAAC;AACpD,SAAK,mBAAmB,MAAM;AAC9B,SAAK,yBAAyB,KAAK,sBAAsB;AACzD,SAAK,QAAQ,YAAY,IAAI;AAC7B,eAAW,OAAO,KAAK,QAAQ;AAC3B,WAAK,OAAO,GAAG,EAAE,MAAM;AAAA,IAC3B;AACA,eAAW,OAAO,KAAK,UAAU;AAC7B,YAAM,UAAU,KAAK,SAAS,GAAG;AACjC,UAAI,SAAS;AACT,gBAAQ,QAAQ;AAChB,gBAAQ,YAAY;AAAA,MACxB;AAAA,IACJ;AACA,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,SAAS,IAAI,KAAK;AACvB,SAAK,qBAAqB,KAAK,mBAAmB,oBAAI,IAAI;AAC1D,SAAK,iBAAiB,IAAI,KAAK;AAAA,EACnC;AAAA,EACA,YAAY,OAAO;AACf,SAAK,SAAS,OAAO,KAAK;AAC1B,SAAK,oBAAoB,KAAK,iBAAiB,OAAO,KAAK;AAAA,EAC/D;AAAA,EACA,kBAAkB,KAAK,OAAO;AAC1B,QAAI,KAAK,mBAAmB,IAAI,GAAG,GAAG;AAClC,WAAK,mBAAmB,IAAI,GAAG,EAAE;AAAA,IACrC;AACA,UAAM,mBAAmB,eAAe,IAAI,GAAG;AAC/C,QAAI,oBAAoB,KAAK,iBAAiB;AAC1C,WAAK,gBAAgB;AAAA,IACzB;AACA,UAAM,iBAAiB,MAAM,GAAG,UAAU,CAAC,gBAAgB;AACvD,WAAK,aAAa,GAAG,IAAI;AACzB,WAAK,MAAM,YAAY,MAAM,UAAU,KAAK,YAAY;AACxD,UAAI,oBAAoB,KAAK,YAAY;AACrC,aAAK,WAAW,mBAAmB;AAAA,MACvC;AACA,WAAK,eAAe;AAAA,IACxB,CAAC;AACD,QAAI;AACJ,QAAI,OAAO,uBAAuB;AAC9B,wBAAkB,OAAO,sBAAsB,MAAM,KAAK,KAAK;AAAA,IACnE;AACA,SAAK,mBAAmB,IAAI,KAAK,MAAM;AACnC,qBAAe;AACf,UAAI;AACA,wBAAgB;AACpB,UAAI,MAAM;AACN,cAAM,KAAK;AAAA,IACnB,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB,OAAO;AAIpB,QAAI,CAAC,KAAK,WACN,CAAC,KAAK,4BACN,KAAK,SAAS,MAAM,MAAM;AAC1B,aAAO;AAAA,IACX;AACA,WAAO,KAAK,yBAAyB,KAAK,SAAS,MAAM,OAAO;AAAA,EACpE;AAAA,EACA,iBAAiB;AACb,QAAI,MAAM;AACV,SAAK,OAAO,oBAAoB;AAC5B,YAAM,oBAAoB,mBAAmB,GAAG;AAChD,UAAI,CAAC;AACD;AACJ,YAAM,EAAE,WAAW,SAAS,mBAAmB,IAAI;AAInD,UAAI,CAAC,KAAK,SAAS,GAAG,KAClB,sBACA,UAAU,KAAK,KAAK,GAAG;AACvB,aAAK,SAAS,GAAG,IAAI,IAAI,mBAAmB,IAAI;AAAA,MACpD;AAIA,UAAI,KAAK,SAAS,GAAG,GAAG;AACpB,cAAM,UAAU,KAAK,SAAS,GAAG;AACjC,YAAI,QAAQ,WAAW;AACnB,kBAAQ,OAAO;AAAA,QACnB,OACK;AACD,kBAAQ,MAAM;AACd,kBAAQ,YAAY;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AACX,SAAK,MAAM,KAAK,aAAa,KAAK,cAAc,KAAK,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACjB,WAAO,KAAK,UACN,KAAK,2BAA2B,KAAK,SAAS,KAAK,KAAK,IACxD,UAAU;AAAA,EACpB;AAAA,EACA,eAAe,KAAK;AAChB,WAAO,KAAK,aAAa,GAAG;AAAA,EAChC;AAAA,EACA,eAAe,KAAK,OAAO;AACvB,SAAK,aAAa,GAAG,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,iBAAiB;AAC3B,QAAI,MAAM,qBAAqB,KAAK,MAAM,mBAAmB;AACzD,WAAK,eAAe;AAAA,IACxB;AACA,SAAK,YAAY,KAAK;AACtB,SAAK,QAAQ;AACb,SAAK,sBAAsB,KAAK;AAChC,SAAK,kBAAkB;AAIvB,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AAC/C,YAAM,MAAM,kBAAkB,CAAC;AAC/B,UAAI,KAAK,uBAAuB,GAAG,GAAG;AAClC,aAAK,uBAAuB,GAAG,EAAE;AACjC,eAAO,KAAK,uBAAuB,GAAG;AAAA,MAC1C;AACA,YAAM,eAAgB,OAAO;AAC7B,YAAM,WAAW,MAAM,YAAY;AACnC,UAAI,UAAU;AACV,aAAK,uBAAuB,GAAG,IAAI,KAAK,GAAG,KAAK,QAAQ;AAAA,MAC5D;AAAA,IACJ;AACA,SAAK,mBAAmB,4BAA4B,MAAM,KAAK,4BAA4B,OAAO,KAAK,WAAW,IAAI,GAAG,KAAK,gBAAgB;AAC9I,QAAI,KAAK,wBAAwB;AAC7B,WAAK,uBAAuB;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACb,WAAO,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACnB,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,wBAAwB;AACpB,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,wBAAwB;AACpB,WAAO,KAAK,gBACN,OACA,KAAK,SACD,KAAK,OAAO,sBAAsB,IAClC;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,OAAO;AACnB,UAAM,qBAAqB,KAAK,sBAAsB;AACtD,QAAI,oBAAoB;AACpB,yBAAmB,mBACf,mBAAmB,gBAAgB,IAAI,KAAK;AAChD,aAAO,MAAM,mBAAmB,gBAAgB,OAAO,KAAK;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,KAAK,OAAO;AAEjB,UAAM,gBAAgB,KAAK,OAAO,IAAI,GAAG;AACzC,QAAI,UAAU,eAAe;AACzB,UAAI;AACA,aAAK,YAAY,GAAG;AACxB,WAAK,kBAAkB,KAAK,KAAK;AACjC,WAAK,OAAO,IAAI,KAAK,KAAK;AAC1B,WAAK,aAAa,GAAG,IAAI,MAAM,IAAI;AAAA,IACvC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,KAAK;AACb,SAAK,OAAO,OAAO,GAAG;AACtB,UAAM,cAAc,KAAK,mBAAmB,IAAI,GAAG;AACnD,QAAI,aAAa;AACb,kBAAY;AACZ,WAAK,mBAAmB,OAAO,GAAG;AAAA,IACtC;AACA,WAAO,KAAK,aAAa,GAAG;AAC5B,SAAK,2BAA2B,KAAK,KAAK,WAAW;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,KAAK;AACV,WAAO,KAAK,OAAO,IAAI,GAAG;AAAA,EAC9B;AAAA,EACA,SAAS,KAAK,cAAc;AACxB,QAAI,KAAK,MAAM,UAAU,KAAK,MAAM,OAAO,GAAG,GAAG;AAC7C,aAAO,KAAK,MAAM,OAAO,GAAG;AAAA,IAChC;AACA,QAAI,QAAQ,KAAK,OAAO,IAAI,GAAG;AAC/B,QAAI,UAAU,UAAa,iBAAiB,QAAW;AACnD,cAAQ,YAAY,iBAAiB,OAAO,SAAY,cAAc,EAAE,OAAO,KAAK,CAAC;AACrF,WAAK,SAAS,KAAK,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK,QAAQ;AACnB,QAAI,QAAQ,KAAK,aAAa,GAAG,MAAM,UAAa,CAAC,KAAK,UACpD,KAAK,aAAa,GAAG,IACrB,KAAK,uBAAuB,KAAK,OAAO,GAAG,KACzC,KAAK,sBAAsB,KAAK,SAAS,KAAK,KAAK,OAAO;AAClE,QAAI,UAAU,UAAa,UAAU,MAAM;AACvC,UAAI,OAAO,UAAU,aAChB,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,IAAI;AAExD,gBAAQ,WAAW,KAAK;AAAA,MAC5B,WACS,CAAC,cAAc,KAAK,KAAK,QAAQ,KAAK,MAAM,GAAG;AACpD,gBAAQC,mBAAkB,KAAK,MAAM;AAAA,MACzC;AACA,WAAK,cAAc,KAAK,cAAc,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,IACtE;AACA,WAAO,cAAc,KAAK,IAAI,MAAM,IAAI,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK,OAAO;AACtB,SAAK,WAAW,GAAG,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK;AACf,UAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,QAAI;AACJ,QAAI,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU;AAC5D,YAAM,UAAU,wBAAwB,KAAK,OAAO,SAAS,KAAK,iBAAiB,MAAM;AACzF,UAAI,SAAS;AACT,2BAAmB,QAAQ,GAAG;AAAA,MAClC;AAAA,IACJ;AAIA,QAAI,WAAW,qBAAqB,QAAW;AAC3C,aAAO;AAAA,IACX;AAKA,UAAM,SAAS,KAAK,uBAAuB,KAAK,OAAO,GAAG;AAC1D,QAAI,WAAW,UAAa,CAAC,cAAc,MAAM;AAC7C,aAAO;AAKX,WAAO,KAAK,cAAc,GAAG,MAAM,UAC/B,qBAAqB,SACnB,SACA,KAAK,WAAW,GAAG;AAAA,EAC7B;AAAA,EACA,GAAG,WAAW,UAAU;AACpB,QAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AACzB,WAAK,OAAO,SAAS,IAAI,IAAI,oBAAoB;AAAA,IACrD;AACA,WAAO,KAAK,OAAO,SAAS,EAAE,IAAI,QAAQ;AAAA,EAC9C;AAAA,EACA,OAAO,cAAc,MAAM;AACvB,QAAI,KAAK,OAAO,SAAS,GAAG;AACxB,WAAK,OAAO,SAAS,EAAE,OAAO,GAAG,IAAI;AAAA,IACzC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACtB,cAAU,OAAO,KAAK,MAAM;AAAA,EAChC;AACJ;;;ACvdA,IAAM,mBAAN,cAA+B,cAAc;AAAA,EACzC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,yBAAyB,GAAG,GAAG;AAM3B,WAAO,EAAE,wBAAwB,CAAC,IAAI,IAAI,IAAI;AAAA,EAClD;AAAA,EACA,uBAAuB,OAAO,KAAK;AAC/B,WAAO,MAAM,QACP,MAAM,MAAM,GAAG,IACf;AAAA,EACV;AAAA,EACA,2BAA2B,KAAK,EAAE,MAAM,OAAAC,OAAM,GAAG;AAC7C,WAAO,KAAK,GAAG;AACf,WAAOA,OAAM,GAAG;AAAA,EACpB;AAAA,EACA,yBAAyB;AACrB,QAAI,KAAK,mBAAmB;AACxB,WAAK,kBAAkB;AACvB,aAAO,KAAK;AAAA,IAChB;AACA,UAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,QAAI,cAAc,QAAQ,GAAG;AACzB,WAAK,oBAAoB,SAAS,GAAG,UAAU,CAAC,WAAW;AACvD,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,cAAc,GAAG,MAAM;AAAA,QACxC;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;;;ACrCA,IAAMC,kBAAiB;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,sBAAsB;AAC1B;AACA,IAAM,gBAAgB,mBAAmB;AAOzC,SAASC,gBAAe,cAAcC,YAAW,mBAAmB;AAEhE,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AAKzB,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACpC,UAAM,MAAM,mBAAmB,CAAC;AAChC,UAAM,QAAQ,aAAa,GAAG;AAC9B,QAAI,UAAU;AACV;AACJ,QAAI,iBAAiB;AACrB,QAAI,OAAO,UAAU,UAAU;AAC3B,uBAAiB,WAAW,IAAI,WAAW,OAAO,IAAI,IAAI;AAAA,IAC9D,OACK;AACD,uBAAiB,WAAW,KAAK,MAAM;AAAA,IAC3C;AACA,QAAI,CAAC,kBAAkB,mBAAmB;AACtC,YAAM,cAAc,eAAe,OAAO,iBAAiB,GAAG,CAAC;AAC/D,UAAI,CAAC,gBAAgB;AACjB,6BAAqB;AACrB,cAAM,gBAAgBF,gBAAe,GAAG,KAAK;AAC7C,2BAAmB,GAAG,aAAa,IAAI,WAAW;AAAA,MACtD;AACA,UAAI,mBAAmB;AACnB,QAAAE,WAAU,GAAG,IAAI;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,oBAAkB,gBAAgB,KAAK;AAGvC,MAAI,mBAAmB;AACnB,sBAAkB,kBAAkBA,YAAW,qBAAqB,KAAK,eAAe;AAAA,EAC5F,WACS,oBAAoB;AACzB,sBAAkB;AAAA,EACtB;AACA,SAAO;AACX;;;ACtDA,SAAS,gBAAgB,OAAO,cAAc,mBAAmB;AAC7D,QAAM,EAAE,OAAAC,QAAO,MAAM,gBAAgB,IAAI;AAEzC,MAAIC,gBAAe;AACnB,MAAI,qBAAqB;AAOzB,aAAW,OAAO,cAAc;AAC5B,UAAM,QAAQ,aAAa,GAAG;AAC9B,QAAI,eAAe,IAAI,GAAG,GAAG;AAEzB,MAAAA,gBAAe;AACf;AAAA,IACJ,WACS,kBAAkB,GAAG,GAAG;AAC7B,WAAK,GAAG,IAAI;AACZ;AAAA,IACJ,OACK;AAED,YAAM,cAAc,eAAe,OAAO,iBAAiB,GAAG,CAAC;AAC/D,UAAI,IAAI,WAAW,QAAQ,GAAG;AAE1B,6BAAqB;AACrB,wBAAgB,GAAG,IACf;AAAA,MACR,OACK;AACD,QAAAD,OAAM,GAAG,IAAI;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,CAAC,aAAa,WAAW;AACzB,QAAIC,iBAAgB,mBAAmB;AACnC,MAAAD,OAAM,YAAYE,gBAAe,cAAc,MAAM,WAAW,iBAAiB;AAAA,IACrF,WACSF,OAAM,WAAW;AAKtB,MAAAA,OAAM,YAAY;AAAA,IACtB;AAAA,EACJ;AAKA,MAAI,oBAAoB;AACpB,UAAM,EAAE,UAAU,OAAO,UAAU,OAAO,UAAU,EAAG,IAAI;AAC3D,IAAAA,OAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,EAC5D;AACJ;;;AC3DA,SAAS,WAAW,SAAS,EAAE,OAAAG,QAAO,KAAK,GAAG,WAAW,YAAY;AACjE,QAAM,eAAe,QAAQ;AAC7B,MAAI;AACJ,OAAK,OAAOA,QAAO;AAEf,iBAAa,GAAG,IAAIA,OAAM,GAAG;AAAA,EACjC;AAEA,cAAY,sBAAsB,cAAc,SAAS;AACzD,OAAK,OAAO,MAAM;AAGd,iBAAa,YAAY,KAAK,KAAK,GAAG,CAAC;AAAA,EAC3C;AACJ;;;ACZA,IAAM,kBAAkB,CAAC;;;ACCzB,SAAS,oBAAoB,KAAK,EAAE,QAAQ,SAAS,GAAG;AACpD,SAAQ,eAAe,IAAI,GAAG,KAC1B,IAAI,WAAW,QAAQ,MACrB,UAAU,aAAa,YACpB,CAAC,CAAC,gBAAgB,GAAG,KAAK,QAAQ;AAC/C;;;ACLA,SAAS,4BAA4B,OAAO,WAAW,eAAe;AAClE,QAAM,EAAE,OAAAC,OAAM,IAAI;AAClB,QAAM,YAAY,CAAC;AACnB,aAAW,OAAOA,QAAO;AACrB,QAAI,cAAcA,OAAM,GAAG,CAAC,KACvB,UAAU,SACP,cAAc,UAAU,MAAM,GAAG,CAAC,KACtC,oBAAoB,KAAK,KAAK,KAC9B,eAAe,SAAS,GAAG,GAAG,cAAc,QAAW;AACvD,gBAAU,GAAG,IAAIA,OAAM,GAAG;AAAA,IAC9B;AAAA,EACJ;AACA,SAAO;AACX;;;ACTA,SAASC,kBAAiB,SAAS;AAC/B,SAAO,OAAO,iBAAiB,OAAO;AAC1C;AACA,IAAM,oBAAN,cAAgC,iBAAiB;AAAA,EAC7C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,sBAAsB,UAAU,KAAK;AACjC,QAAI,eAAe,IAAI,GAAG,GAAG;AACzB,aAAO,KAAK,YAAY,eAClB,sBAAsB,GAAG,IACzB,mBAAmB,UAAU,GAAG;AAAA,IAC1C,OACK;AACD,YAAM,gBAAgBA,kBAAiB,QAAQ;AAC/C,YAAM,SAAS,kBAAkB,GAAG,IAC9B,cAAc,iBAAiB,GAAG,IAClC,cAAc,GAAG,MAAM;AAC7B,aAAO,OAAO,UAAU,WAAW,MAAM,KAAK,IAAI;AAAA,IACtD;AAAA,EACJ;AAAA,EACA,2BAA2B,UAAU,EAAE,mBAAmB,GAAG;AACzD,WAAO,mBAAmB,UAAU,kBAAkB;AAAA,EAC1D;AAAA,EACA,MAAM,aAAa,cAAc,OAAO;AACpC,oBAAgB,aAAa,cAAc,MAAM,iBAAiB;AAAA,EACtE;AAAA,EACA,4BAA4B,OAAO,WAAW,eAAe;AACzD,WAAO,4BAA4B,OAAO,WAAW,aAAa;AAAA,EACtE;AACJ;;;ACpCA,SAAS,YAAY,KAAK,QAAQ;AAC9B,SAAO,OAAO;AAClB;AACA,IAAM,sBAAN,cAAkC,cAAc;AAAA,EAC5C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,sBAAsB,UAAU,KAAK;AACjC,QAAI,YAAY,KAAK,QAAQ,GAAG;AAC5B,YAAM,QAAQ,SAAS,GAAG;AAC1B,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AACxD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,yBAAyB;AACrB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,KAAK,aAAa;AACzC,WAAO,YAAY,OAAO,GAAG;AAAA,EACjC;AAAA,EACA,6BAA6B;AACzB,WAAO,UAAU;AAAA,EACrB;AAAA,EACA,MAAM,aAAa,cAAc;AAC7B,WAAO,OAAO,YAAY,QAAQ,YAAY;AAAA,EAClD;AAAA,EACA,eAAe,UAAU,EAAE,OAAO,GAAG;AACjC,WAAO,OAAO,UAAU,MAAM;AAAA,EAClC;AAAA,EACA,2BAA2B;AACvB,WAAO;AAAA,EACX;AACJ;;;ACpCA,IAAM,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AACX;AACA,IAAM,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,OAAO;AACX;AAQA,SAAS,aAAa,OAAO,QAAQ,UAAU,GAAG,SAAS,GAAG,cAAc,MAAM;AAE9E,QAAM,aAAa;AAGnB,QAAMC,QAAO,cAAc,WAAW;AAEtC,QAAMA,MAAK,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM;AAEzC,QAAM,aAAa,GAAG,UAAU,MAAM;AACtC,QAAM,cAAc,GAAG,UAAU,OAAO;AACxC,QAAMA,MAAK,KAAK,IAAI,GAAG,UAAU,IAAI,WAAW;AACpD;;;ACvBA,SAAS,cAAc,OAAO;AAAA,EAAE;AAAA,EAAO;AAAA,EAAO;AAAA,EAAW;AAAA,EAAY,cAAc;AAAA,EAAG,aAAa;AAAA;AAAA,EAEnG,GAAG;AAAO,GAAGC,WAAU,mBAAmB,WAAW;AACjD,kBAAgB,OAAO,QAAQ,iBAAiB;AAKhD,MAAIA,WAAU;AACV,QAAI,MAAM,MAAM,SAAS;AACrB,YAAM,MAAM,UAAU,MAAM,MAAM;AAAA,IACtC;AACA;AAAA,EACJ;AACA,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,CAAC;AACf,QAAM,EAAE,OAAO,OAAAC,OAAM,IAAI;AAKzB,MAAI,MAAM,WAAW;AACjB,IAAAA,OAAM,YAAY,MAAM;AACxB,WAAO,MAAM;AAAA,EACjB;AACA,MAAIA,OAAM,aAAa,MAAM,iBAAiB;AAC1C,IAAAA,OAAM,kBAAkB,MAAM,mBAAmB;AACjD,WAAO,MAAM;AAAA,EACjB;AACA,MAAIA,OAAM,WAAW;AAKjB,IAAAA,OAAM,eAAe,WAAW,gBAAgB;AAChD,WAAO,MAAM;AAAA,EACjB;AAEA,MAAI,UAAU;AACV,UAAM,IAAI;AACd,MAAI,UAAU;AACV,UAAM,IAAI;AACd,MAAI,cAAc;AACd,UAAM,QAAQ;AAElB,MAAI,eAAe,QAAW;AAC1B,iBAAa,OAAO,YAAY,aAAa,YAAY,KAAK;AAAA,EAClE;AACJ;;;ACnDA,IAAM,sBAAsB,oBAAI,IAAI;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;;;AC3BD,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,IAAI,YAAY,MAAM;;;ACI3E,SAAS,UAAU,SAAS,aAAa,YAAY,YAAY;AAC7D,aAAW,SAAS,aAAa,QAAW,UAAU;AACtD,aAAW,OAAO,YAAY,OAAO;AACjC,YAAQ,aAAa,CAAC,oBAAoB,IAAI,GAAG,IAAIC,aAAY,GAAG,IAAI,KAAK,YAAY,MAAM,GAAG,CAAC;AAAA,EACvG;AACJ;;;ACNA,SAASC,6BAA4B,OAAO,WAAW,eAAe;AAClE,QAAM,YAAY,4BAA8B,OAAO,WAAW,aAAa;AAC/E,aAAW,OAAO,OAAO;AACrB,QAAI,cAAc,MAAM,GAAG,CAAC,KACxB,cAAc,UAAU,GAAG,CAAC,GAAG;AAC/B,YAAM,YAAY,mBAAmB,QAAQ,GAAG,MAAM,KAChD,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC,IACtD;AACN,gBAAU,SAAS,IAAI,MAAM,GAAG;AAAA,IACpC;AAAA,EACJ;AACA,SAAO;AACX;;;ACLA,IAAM,mBAAN,cAA+B,iBAAiB;AAAA,EAC5C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,6BAA6B;AAAA,EACtC;AAAA,EACA,uBAAuB,OAAO,KAAK;AAC/B,WAAO,MAAM,GAAG;AAAA,EACpB;AAAA,EACA,sBAAsB,UAAU,KAAK;AACjC,QAAI,eAAe,IAAI,GAAG,GAAG;AACzB,YAAM,cAAc,oBAAoB,GAAG;AAC3C,aAAO,cAAc,YAAY,WAAW,IAAI;AAAA,IACpD;AACA,UAAM,CAAC,oBAAoB,IAAI,GAAG,IAAIC,aAAY,GAAG,IAAI;AACzD,WAAO,SAAS,aAAa,GAAG;AAAA,EACpC;AAAA,EACA,4BAA4B,OAAO,WAAW,eAAe;AACzD,WAAOC,6BAA4B,OAAO,WAAW,aAAa;AAAA,EACtE;AAAA,EACA,MAAM,aAAa,cAAc,OAAO;AACpC,kBAAc,aAAa,cAAc,KAAK,UAAU,MAAM,mBAAmB,MAAM,KAAK;AAAA,EAChG;AAAA,EACA,eAAe,UAAU,aAAa,WAAW,YAAY;AACzD,cAAU,UAAU,aAAa,WAAW,UAAU;AAAA,EAC1D;AAAA,EACA,MAAM,UAAU;AACZ,SAAK,WAAW,SAAS,SAAS,OAAO;AACzC,UAAM,MAAM,QAAQ;AAAA,EACxB;AACJ;;;ACnCA,SAAS,uBAAuB,SAAS;AACrC,QAAM,UAAU;AAAA,IACZ,iBAAiB;AAAA,IACjB,OAAO,CAAC;AAAA,IACR,aAAa;AAAA,MACT,aAAa;AAAA,QACT,WAAW,CAAC;AAAA,QACZ,iBAAiB,CAAC;AAAA,QAClB,OAAO,CAAC;AAAA,QACR,MAAM,CAAC;AAAA,QACP,OAAO,CAAC;AAAA,MACZ;AAAA,MACA,cAAc,CAAC;AAAA,IACnB;AAAA,EACJ;AACA,QAAM,OAAO,aAAa,OAAO,KAAK,CAAC,gBAAgB,OAAO,IACxD,IAAI,iBAAiB,OAAO,IAC5B,IAAI,kBAAkB,OAAO;AACnC,OAAK,MAAM,OAAO;AAClB,qBAAmB,IAAI,SAAS,IAAI;AACxC;AACA,SAAS,0BAA0B,SAAS;AACxC,QAAM,UAAU;AAAA,IACZ,iBAAiB;AAAA,IACjB,OAAO,CAAC;AAAA,IACR,aAAa;AAAA,MACT,aAAa;AAAA,QACT,QAAQ,CAAC;AAAA,MACb;AAAA,MACA,cAAc,CAAC;AAAA,IACnB;AAAA,EACJ;AACA,QAAM,OAAO,IAAI,oBAAoB,OAAO;AAC5C,OAAK,MAAM,OAAO;AAClB,qBAAmB,IAAI,SAAS,IAAI;AACxC;;;ACtCA,SAAS,mBAAmB,OAAOC,YAAW,SAAS;AACnD,QAAM,gBAAgB,cAAc,KAAK,IAAI,QAAQ,YAAY,KAAK;AACtE,gBAAc,MAAM,mBAAmB,IAAI,eAAeA,YAAW,OAAO,CAAC;AAC7E,SAAO,cAAc;AACzB;;;ACEA,SAAS,cAAc,SAASC,YAAW;AACvC,SAAQ,cAAc,OAAO,KACzB,OAAO,YAAY,YAClB,OAAO,YAAY,YAAY,CAAC,eAAeA,UAAS;AACjE;AAIA,SAAS,eAAe,SAASA,YAAW,SAAS,OAAO;AACxD,QAAM,aAAa,CAAC;AACpB,MAAI,cAAc,SAASA,UAAS,GAAG;AACnC,eAAW,KAAK,mBAAmB,SAAS,eAAeA,UAAS,IAC9DA,WAAU,WAAWA,aACrBA,YAAW,UAAU,QAAQ,WAAW,UAAU,OAAO,CAAC;AAAA,EACpE,OACK;AACD,UAAM,WAAW,gBAAgB,SAASA,YAAW,KAAK;AAC1D,UAAM,cAAc,SAAS;AAC7B,cAAU,QAAQ,WAAW,GAAG,+BAA+B,mBAAmB;AAClF,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,YAAM,cAAc,SAAS,CAAC;AAC9B,gBAAU,gBAAgB,MAAM,wIAAwI,cAAc;AACtL,YAAM,sBAAsB,uBAAuB,UAC7C,yBACA;AACN,UAAI,CAAC,mBAAmB,IAAI,WAAW,GAAG;AACtC,4BAAoB,WAAW;AAAA,MACnC;AACA,YAAM,gBAAgB,mBAAmB,IAAI,WAAW;AACxD,YAAM,aAAa,EAAE,GAAG,QAAQ;AAIhC,UAAI,WAAW,cACX,OAAO,WAAW,UAAU,YAAY;AACxC,mBAAW,QAAQ,WAAW,MAAM,GAAG,WAAW;AAAA,MACtD;AACA,iBAAW,KAAK,GAAG,cAAc,eAAe,EAAE,GAAGA,YAAW,WAAW,GAAG,CAAC,CAAC,CAAC;AAAA,IACrF;AAAA,EACJ;AACA,SAAO;AACX;;;AC9CA,SAAS,gBAAgB,UAAU,SAAS,OAAO;AAC/C,QAAM,aAAa,CAAC;AACpB,QAAM,uBAAuB,6BAA6B,UAAU,SAAS,OAAO,EAAE,OAAO,CAAC;AAC9F,uBAAqB,QAAQ,CAAC,EAAE,WAAAC,YAAW,WAAW,GAAG,YAAY;AACjE,eAAW,KAAK,GAAG,eAAe,SAASA,YAAW,UAAU,CAAC;AAAA,EACrE,CAAC;AACD,SAAO;AACX;;;ACNA,SAAS,WAAW,OAAO;AACvB,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,OAAO;AAC3D;AAKA,SAAS,oBAAoB,OAAO;AAIhC,WAAS,cAAc,mBAAmB,oBAAoB,SAAS;AACnE,QAAI,aAAa,CAAC;AAClB,QAAI,WAAW,iBAAiB,GAAG;AAC/B,mBAAa,gBAAgB,mBAAmB,oBAAoB,KAAK;AAAA,IAC7E,OACK;AACD,mBAAa,eAAe,mBAAmB,oBAAoB,SAAS,KAAK;AAAA,IACrF;AACA,UAAM,YAAY,IAAI,uBAAuB,UAAU;AACvD,QAAI,OAAO;AACP,YAAM,WAAW,KAAK,SAAS;AAC/B,gBAAU,SAAS,KAAK,MAAM;AAC1B,mBAAW,MAAM,YAAY,SAAS;AAAA,MAC1C,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAM,UAAU,oBAAoB;;;AChCpC,SAAS,gBAAgB,mBAAmBC,YAAW,SAAS,OAAO;AACnE,QAAM,WAAW,gBAAgB,mBAAmB,KAAK;AACzD,QAAM,cAAc,SAAS;AAC7B,YAAU,QAAQ,WAAW,GAAG,+BAA+B,mBAAmB;AAkBlF,QAAM,uBAAuB,CAAC;AAI9B,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,UAAM,UAAU,SAAS,CAAC;AAC1B,UAAM,oBAAoB,EAAE,GAAG,QAAQ;AAIvC,QAAI,OAAO,kBAAkB,UAAU,YAAY;AAC/C,wBAAkB,QAAQ,kBAAkB,MAAM,GAAG,WAAW;AAAA,IACpE;AACA,eAAW,aAAaA,YAAW;AAC/B,UAAI,iBAAiBA,WAAU,SAAS;AACxC,UAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAChC,yBAAiB,CAAC,cAAc;AAAA,MACpC;AACA,YAAM,eAAe;AAAA,QACjB,GAAG,mBAAmB,mBAAmB,SAAS;AAAA,MACtD;AACA,mBAAa,aAAa,aAAa,WAAW,sBAAsB,aAAa,QAAQ;AAC7F,mBAAa,UAAU,aAAa,QAAQ,sBAAsB,aAAa,KAAK;AAKpF,YAAM,MAAM,gBAAgB,OAAO;AACnC,YAAM,MAAM,gBAAgB,WAAW,aAAa,iBAAiB,EAAE;AACvE,YAAM,mBAAmB,IAAI,IAAI,GAAG;AACpC,0BAAoB,iBAAiB,KAAK;AAC1C,2BAAqB,KAAK;AAAA,QACtB;AAAA,QACA;AAAA,QACA,qBAAqB;AAAA,QACrB,SAAS;AAAA,UACL,GAAG;AAAA,UACH;AAAA,UACA,MAAM;AAAA,UACN,cAAc,CAAC,kBAAkB,QAAQ,CAAC,kBAAkB;AAAA,QAChE;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAIA,WAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AAClD,UAAM,EAAE,qBAAqB,SAAS,iBAAiB,IAAI,qBAAqB,CAAC;AACjF,UAAM,EAAE,SAAS,MAAM,cAAc,IAAI;AACzC,QAAI,CAAC,iBAAiB,oBAAoB,CAAC,MAAM,MAAM;AACnD,0BAAoB,CAAC,IAAIC,kBAAiB,SAAS,IAAI;AAAA,IAC3D;AACA,kBAAc,mBAAmB;AACjC,oBAAgB,qBAAqB,IAAI;AAOzC,QAAI,CAAC,iBAAiB,oBAAoB,SAAS,GAAG;AAClD,0BAAoB,QAAQA,kBAAiB,SAAS,IAAI,CAAC;AAAA,IAC/D;AACA,qBAAiB,YAAY;AAAA,EACjC;AAIA,QAAM,aAAa,CAAC;AACpB,WAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AAClD,UAAM,EAAE,KAAK,KAAK,SAAS,iBAAiB,IAAI,qBAAqB,CAAC;AACtE,UAAM,YAAY,IAAI,gBAAgB,gBAAgB;AACtD,QAAI,IAAI,KAAK,SAAS;AACtB,cAAU,SAAS,QAAQ,MAAM,IAAI,OAAO,GAAG,CAAC;AAChD,eAAW,KAAK,SAAS;AAAA,EAC7B;AACA,SAAO;AACX;;;ACnGA,IAAM,2BAA2B,CAAC,UAAU;AACxC,WAAS,cAAc,mBAAmBC,YAAW,SAAS;AAC1D,WAAO,IAAI,uBAAuB,gBAAgB,mBAAmBA,YAAW,SAAS,KAAK,CAAC;AAAA,EACnG;AACA,SAAO;AACX;AACA,IAAM,cAA4B,yBAAyB;;;ACJ3D,IAAMC,cAAa;AACnB,IAAM,iBAAiB,OAAO;AAAA,EAC1B,SAAS;AAAA,EACT,QAAQ,CAAC;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,UAAU;AACd;AACA,IAAM,mBAAmB,OAAO;AAAA,EAC5B,MAAM;AAAA,EACN,GAAG,eAAe;AAAA,EAClB,GAAG,eAAe;AACtB;AACA,IAAM,OAAO;AAAA,EACT,GAAG;AAAA,IACC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd;AAAA,EACA,GAAG;AAAA,IACC,QAAQ;AAAA,IACR,UAAU;AAAA,EACd;AACJ;AACA,SAAS,eAAe,SAAS,UAAU,MAAMC,OAAM;AACnD,QAAM,OAAO,KAAK,QAAQ;AAC1B,QAAM,EAAE,QAAQ,SAAS,IAAI,KAAK,QAAQ;AAC1C,QAAM,OAAO,KAAK;AAClB,QAAM,WAAW,KAAK;AACtB,OAAK,UAAU,QAAQ,SAAS,QAAQ,EAAE;AAC1C,OAAK,eAAe,QAAQ,SAAS,MAAM,EAAE,IAAI,QAAQ,SAAS,MAAM,EAAE;AAC1E,OAAK,OAAO,SAAS;AACrB,OAAK,OAAO,CAAC,IAAI;AACjB,OAAK,OAAO,CAAC,IAAI,KAAK;AACtB,OAAK,WAAW,SAAS,GAAG,KAAK,cAAc,KAAK,OAAO;AAC3D,QAAM,UAAUA,QAAO;AACvB,OAAK,WACD,UAAUD,cACJ,IACA,kBAAkB,KAAK,UAAU,MAAM,OAAO;AAC5D;AACA,SAAS,iBAAiB,SAAS,MAAMC,OAAM;AAC3C,iBAAe,SAAS,KAAK,MAAMA,KAAI;AACvC,iBAAe,SAAS,KAAK,MAAMA,KAAI;AACvC,OAAK,OAAOA;AAChB;;;AClDA,SAAS,UAAU,SAAS,WAAW;AACnC,QAAM,QAAQ,EAAE,GAAG,GAAG,GAAG,EAAE;AAC3B,MAAIC,WAAU;AACd,SAAOA,YAAWA,aAAY,WAAW;AACrC,QAAI,cAAcA,QAAO,GAAG;AACxB,YAAM,KAAKA,SAAQ;AACnB,YAAM,KAAKA,SAAQ;AACnB,MAAAA,WAAUA,SAAQ;AAAA,IACtB,WACSA,SAAQ,YAAY,OAAO;AAQhC,YAAM,iBAAiBA,SAAQ,sBAAsB;AACrD,MAAAA,WAAUA,SAAQ;AAClB,YAAM,oBAAoBA,SAAQ,sBAAsB;AACxD,YAAM,KAAK,eAAe,OAAO,kBAAkB;AACnD,YAAM,KAAK,eAAe,MAAM,kBAAkB;AAAA,IACtD,WACSA,oBAAmB,oBAAoB;AAC5C,YAAM,EAAE,GAAG,EAAE,IAAIA,SAAQ,QAAQ;AACjC,YAAM,KAAK;AACX,YAAM,KAAK;AACX,UAAI,MAAM;AACV,UAAI,SAASA,SAAQ;AACrB,aAAO,CAAC,KAAK;AACT,YAAI,OAAO,YAAY,OAAO;AAC1B,gBAAM;AAAA,QACV;AACA,iBAASA,SAAQ;AAAA,MACrB;AACA,MAAAA,WAAU;AAAA,IACd,OACK;AACD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AC5CA,IAAM,aAAa;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACT;AACA,SAAS,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAC1C,MAAI,QAAQ;AAKZ,MAAI,QAAQ,YAAY;AACpB,WAAO,WAAW,IAAI;AAAA,EAC1B;AAIA,MAAI,OAAO,SAAS,UAAU;AAC1B,UAAMC,YAAW,WAAW,IAAI;AAChC,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,cAAQA;AAAA,IACZ,WACS,KAAK,SAAS,GAAG,GAAG;AACzB,aAAOA,YAAW;AAAA,IACtB,WACS,KAAK,SAAS,IAAI,GAAG;AAC1B,cAASA,YAAW,MAAO,SAAS,gBAAgB;AAAA,IACxD,WACS,KAAK,SAAS,IAAI,GAAG;AAC1B,cAASA,YAAW,MAAO,SAAS,gBAAgB;AAAA,IACxD,OACK;AACD,aAAOA;AAAA,IACX;AAAA,EACJ;AAIA,MAAI,OAAO,SAAS,UAAU;AAC1B,YAAQ,SAAS;AAAA,EACrB;AACA,SAAO,QAAQ;AACnB;;;ACxCA,IAAMC,iBAAgB,CAAC,GAAG,CAAC;AAC3B,SAAS,cAAc,QAAQ,iBAAiB,cAAc,aAAa;AACvE,MAAI,mBAAmB,MAAM,QAAQ,MAAM,IAAI,SAASA;AACxD,MAAI,cAAc;AAClB,MAAI,iBAAiB;AACrB,MAAI,OAAO,WAAW,UAAU;AAM5B,uBAAmB,CAAC,QAAQ,MAAM;AAAA,EACtC,WACS,OAAO,WAAW,UAAU;AACjC,aAAS,OAAO,KAAK;AACrB,QAAI,OAAO,SAAS,GAAG,GAAG;AACtB,yBAAmB,OAAO,MAAM,GAAG;AAAA,IACvC,OACK;AAMD,yBAAmB,CAAC,QAAQ,WAAW,MAAM,IAAI,SAAS,GAAG;AAAA,IACjE;AAAA,EACJ;AACA,gBAAc,YAAY,iBAAiB,CAAC,GAAG,cAAc,WAAW;AACxE,mBAAiB,YAAY,iBAAiB,CAAC,GAAG,eAAe;AACjE,SAAO,cAAc;AACzB;;;AChCA,IAAM,eAAe;AAAA,EACjB,OAAO;AAAA,IACH,CAAC,GAAG,CAAC;AAAA,IACL,CAAC,GAAG,CAAC;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACF,CAAC,GAAG,CAAC;AAAA,IACL,CAAC,GAAG,CAAC;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACD,CAAC,GAAG,CAAC;AAAA,IACL,CAAC,GAAG,CAAC;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACD,CAAC,GAAG,CAAC;AAAA,IACL,CAAC,GAAG,CAAC;AAAA,EACT;AACJ;;;ACXA,IAAM,QAAQ,EAAE,GAAG,GAAG,GAAG,EAAE;AAC3B,SAAS,cAAc,QAAQ;AAC3B,SAAO,aAAa,UAAU,OAAO,YAAY,QAC3C,OAAO,QAAQ,IACf,EAAE,OAAO,OAAO,aAAa,QAAQ,OAAO,aAAa;AACnE;AACA,SAAS,eAAe,WAAW,MAAM,SAAS;AAC9C,QAAM,EAAE,QAAQ,mBAAmB,aAAa,IAAI,IAAI;AACxD,QAAM,EAAE,SAAS,WAAW,OAAO,IAAI,IAAI;AAC3C,QAAM,cAAc,SAAS,MAAM,WAAW;AAC9C,QAAM,QAAQ,WAAW,YAAY,UAAU,QAAQ,SAAS,IAAI;AAMpE,QAAM,aAAa,WAAW,YACxB,EAAE,OAAO,UAAU,aAAa,QAAQ,UAAU,aAAa,IAC/D,cAAc,MAAM;AAC1B,QAAM,gBAAgB;AAAA,IAClB,OAAO,UAAU;AAAA,IACjB,QAAQ,UAAU;AAAA,EACtB;AAKA,OAAK,IAAI,EAAE,OAAO,SAAS;AAK3B,MAAI,aAAa,CAAC,KAAK,IAAI,EAAE;AAC7B,QAAM,aAAa,iBAAiB;AACpC,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,UAAM,SAAS,cAAc,iBAAiB,CAAC,GAAG,cAAc,WAAW,GAAG,WAAW,WAAW,GAAG,MAAM,IAAI,CAAC;AAClH,QAAI,CAAC,cAAc,WAAW,KAAK,IAAI,EAAE,oBAAoB,CAAC,GAAG;AAC7D,mBAAa;AAAA,IACjB;AACA,SAAK,IAAI,EAAE,OAAO,CAAC,IAAI;AAAA,EAC3B;AAKA,MAAI,YAAY;AACZ,SAAK,IAAI,EAAE,cAAc,YAAY,KAAK,IAAI,EAAE,QAAQ,cAAc,gBAAgB,GAAG,EAAE,OAAO,MAAM,CAAC;AACzG,SAAK,IAAI,EAAE,sBAAsB,CAAC,GAAG,KAAK,IAAI,EAAE,MAAM;AAAA,EAC1D;AACA,OAAK,IAAI,EAAE,WAAW,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,YAAY,KAAK,IAAI,EAAE,OAAO,CAAC;AAChF;;;ACpDA,SAAS,QAAQ,WAAW,SAAS,WAAW,MAAM;AAIlD,OAAK,EAAE,eAAe;AACtB,OAAK,EAAE,eAAe;AACtB,MAAI,WAAW,WAAW;AACtB,QAAI,OAAO;AACX,WAAO,QAAQ,SAAS,WAAW;AAC/B,WAAK,EAAE,gBAAgB,KAAK;AAC5B,WAAK,EAAE,gBAAgB,KAAK;AAC5B,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACA,OAAK,EAAE,eACH,WAAW,YAAY,OAAO,cAAc,OAAO;AACvD,OAAK,EAAE,eACH,WAAW,YAAY,OAAO,eAAe,OAAO;AACxD,OAAK,EAAE,kBAAkB,UAAU;AACnC,OAAK,EAAE,kBAAkB,UAAU;AAKnC,MAAI,MAAuC;AACvC,QAAI,aAAa,UAAU,WAAW,WAAW;AAC7C,eAAS,iBAAiB,SAAS,EAAE,aAAa,UAAU,sJAAsJ;AAAA,IACtN;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,SAAS,UAAU,MAAM,UAAU,CAAC,GAAG;AAClE,SAAO;AAAA,IACH,SAAS,CAACC,UAAS;AACf,cAAQ,SAAS,QAAQ,QAAQ,IAAI;AACrC,uBAAiB,SAAS,MAAMA,KAAI;AACpC,UAAI,QAAQ,UAAU,QAAQ,QAAQ;AAClC,uBAAe,SAAS,MAAM,OAAO;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,QAAQ,MAAM,SAAS,IAAI;AAAA,EAC/B;AACJ;;;ACxCA,IAAM,kBAAkB,oBAAI,QAAQ;AACpC,IAAM,kBAAkB,oBAAI,QAAQ;AACpC,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,iBAAiB,CAAC,YAAY,YAAY,SAAS,mBAAmB,SAAS;AACrF,SAAS,WAAW,UAAU,EAAE,YAAY,SAAS,kBAAkB,GAAG,QAAQ,IAAI,CAAC,GAAG;AACtF,MAAI,CAAC;AACD,WAAO;AACX,MAAI,oBAAoB,iBAAiB,IAAI,SAAS;AAKtD,MAAI,CAAC,mBAAmB;AACpB,wBAAoB,oBAAI,IAAI;AAC5B,qBAAiB,IAAI,WAAW,iBAAiB;AAAA,EACrD;AAIA,QAAM,OAAO,iBAAiB;AAC9B,QAAM,mBAAmB,sBAAsB,WAAW,UAAU,MAAM,OAAO;AACjF,oBAAkB,IAAI,gBAAgB;AAKtC,MAAI,CAAC,gBAAgB,IAAI,SAAS,GAAG;AACjC,UAAM,aAAa,MAAM;AACrB,iBAAW,WAAW,mBAAmB;AACrC,gBAAQ,QAAQ,UAAU,SAAS;AAAA,MACvC;AACA,YAAM,UAAUC,UAAS;AAAA,IAC7B;AACA,UAAMA,aAAY,MAAM;AACpB,iBAAW,WAAW,mBAAmB;AACrC,gBAAQ,OAAO;AAAA,MACnB;AAAA,IACJ;AACA,UAAMC,YAAW,MAAM,MAAM,KAAK,UAAU;AAC5C,oBAAgB,IAAI,WAAWA,SAAQ;AACvC,UAAM,SAAS,eAAe,SAAS;AACvC,WAAO,iBAAiB,UAAUA,WAAU,EAAE,SAAS,KAAK,CAAC;AAC7D,QAAI,cAAc,SAAS,iBAAiB;AACxC,sBAAgB,IAAI,WAAW,OAAO,WAAWA,SAAQ,CAAC;AAAA,IAC9D;AACA,WAAO,iBAAiB,UAAUA,WAAU,EAAE,SAAS,KAAK,CAAC;AAC7D,IAAAA,UAAS;AAAA,EACb;AACA,QAAM,WAAW,gBAAgB,IAAI,SAAS;AAC9C,QAAM,KAAK,UAAU,OAAO,IAAI;AAChC,SAAO,MAAM;AACT,gBAAY,QAAQ;AAIpB,UAAM,kBAAkB,iBAAiB,IAAI,SAAS;AACtD,QAAI,CAAC;AACD;AACJ,oBAAgB,OAAO,gBAAgB;AACvC,QAAI,gBAAgB;AAChB;AAIJ,UAAM,iBAAiB,gBAAgB,IAAI,SAAS;AACpD,oBAAgB,OAAO,SAAS;AAChC,QAAI,gBAAgB;AAChB,qBAAe,SAAS,EAAE,oBAAoB,UAAU,cAAc;AACtE,sBAAgB,IAAI,SAAS,IAAI;AACjC,aAAO,oBAAoB,UAAU,cAAc;AAAA,IACvD;AAAA,EACJ;AACJ;;;AC1EA,IAAM,gBAAgB,oBAAI,IAAI;AAC9B,SAAS,uBAAuB,SAAS;AACrC,QAAM,cAAc,EAAE,OAAO,EAAE;AAC/B,QAAM,SAAS,WAAW,CAAC,SAAS;AAChC,gBAAY,QAAQ,KAAK,QAAQ,IAAI,EAAE,WAAW;AAAA,EACtD,GAAG,OAAO;AACV,SAAO,EAAE,aAAa,OAAO;AACjC;AACA,SAAS,YAAY,EAAE,QAAQ,WAAW,GAAG,QAAQ,GAAG;AACpD,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI;AACA,gBAAY;AAChB,QAAM,iBAAiB,cAAc,IAAI,SAAS,KAAK,oBAAI,IAAI;AAC/D,gBAAc,IAAI,WAAW,cAAc;AAC3C,QAAM,YAAY,QAAQ,UAAU;AACpC,QAAM,cAAc,eAAe,IAAI,SAAS,KAAK,CAAC;AACtD,QAAM,UAAU,QAAQ,QAAQ,UAAU,CAAC,GAAG,KAAK,GAAG;AACtD,MAAI,CAAC,YAAY,OAAO,GAAG;AACvB,gBAAY,OAAO,IACf,CAAC,QAAQ,UAAU,uBAAuB,IACpC,IAAI,eAAe,EAAE,QAAQ,WAAW,KAAK,CAAC,IAC9C,uBAAuB,EAAE,WAAW,GAAG,QAAQ,CAAC;AAAA,EAC9D;AACA,SAAO,YAAY,OAAO;AAC9B;;;ACxBA,SAAS,kBAAkB,WAAW,SAAS;AAC3C,QAAM,WAAW,YAAY,OAAO;AACpC,SAAO,UAAU,eAAe;AAAA,IAC5B,UAAU,QAAQ,SAAS,SAAY;AAAA,IACvC,SAAS,CAAC,mBAAmB;AACzB,qBAAe,MAAM;AACrB,aAAO,gBAAgB,CAACC,cAAa;AACjC,uBAAe,OAAO,eAAe,WAAWA;AAAA,MACpD,GAAG,QAAQ;AAAA,IACf;AAAA,EACJ,CAAC;AACL;;;ACNA,SAAS,mBAAmB,UAAU;AAClC,SAAO,SAAS,WAAW;AAC/B;AACA,SAAS,iBAAiB,UAAU,SAAS;AACzC,MAAI,mBAAmB,QAAQ,GAAG;AAC9B,WAAO,WAAW,CAAC,SAAS;AACxB,eAAS,KAAK,QAAQ,IAAI,EAAE,UAAU,IAAI;AAAA,IAC9C,GAAG,OAAO;AAAA,EACd,OACK;AACD,WAAO,gBAAgB,UAAU,YAAY,OAAO,CAAC;AAAA,EACzD;AACJ;;;AChBA,SAAS,OAAO,UAAU,EAAE,OAAO,KAAK,YAAY,SAAS,kBAAkB,GAAG,QAAQ,IAAI,CAAC,GAAG;AAC9F,MAAI,CAAC;AACD,WAAO;AACX,QAAM,sBAAsB,EAAE,MAAM,WAAW,GAAG,QAAQ;AAC1D,SAAO,OAAO,aAAa,aACrB,iBAAiB,UAAU,mBAAmB,IAC9C,kBAAkB,UAAU,mBAAmB;AACzD;;;ACTA,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EACN,KAAK;AACT;AACA,SAAS,OAAO,mBAAmB,SAAS,EAAE,MAAM,QAAQ,YAAY,SAAS,OAAO,IAAI,CAAC,GAAG;AAC5F,QAAM,WAAW,gBAAgB,iBAAiB;AAClD,QAAM,sBAAsB,oBAAI,QAAQ;AACxC,QAAM,uBAAuB,CAAC,YAAY;AACtC,YAAQ,QAAQ,CAAC,UAAU;AACvB,YAAM,QAAQ,oBAAoB,IAAI,MAAM,MAAM;AAKlD,UAAI,MAAM,mBAAmB,QAAQ,KAAK;AACtC;AACJ,UAAI,MAAM,gBAAgB;AACtB,cAAM,WAAW,QAAQ,MAAM,QAAQ,KAAK;AAC5C,YAAI,OAAO,aAAa,YAAY;AAChC,8BAAoB,IAAI,MAAM,QAAQ,QAAQ;AAAA,QAClD,OACK;AACD,UAAAC,UAAS,UAAU,MAAM,MAAM;AAAA,QACnC;AAAA,MACJ,WACS,OAAO,UAAU,YAAY;AAClC,cAAM,KAAK;AACX,4BAAoB,OAAO,MAAM,MAAM;AAAA,MAC3C;AAAA,IACJ,CAAC;AAAA,EACL;AACA,QAAMA,YAAW,IAAI,qBAAqB,sBAAsB;AAAA,IAC5D;AAAA,IACA;AAAA,IACA,WAAW,OAAO,WAAW,WAAW,SAAS,WAAW,MAAM;AAAA,EACtE,CAAC;AACD,WAAS,QAAQ,CAAC,YAAYA,UAAS,QAAQ,OAAO,CAAC;AACvD,SAAO,MAAMA,UAAS,WAAW;AACrC;;;AClCA,SAAS,MAAM,UAAU,SAAS;AAC9B,QAAMC,SAAQ,KAAK,IAAI;AACvB,QAAM,eAAe,CAAC,EAAE,UAAU,MAAM;AACpC,UAAM,UAAU,YAAYA;AAC5B,QAAI,WAAW,SAAS;AACpB,kBAAY,YAAY;AACxB,eAAS,UAAU,OAAO;AAAA,IAC9B;AAAA,EACJ;AACA,QAAM,MAAM,cAAc,IAAI;AAC9B,SAAO,MAAM,YAAY,YAAY;AACzC;AACA,SAAS,eAAe,UAAU,SAAS;AACvC,SAAO,MAAM,UAAU,sBAAsB,OAAO,CAAC;AACzD;;;ACpBA,IAAM,WAAW,CAAC,GAAG,MAAM,KAAK,IAAI,IAAI,CAAC;AACzC,SAAS,WAAW,GAAG,GAAG;AAEtB,QAAM,SAAS,SAAS,EAAE,GAAG,EAAE,CAAC;AAChC,QAAM,SAAS,SAAS,EAAE,GAAG,EAAE,CAAC;AAChC,SAAO,KAAK,KAAK,UAAU,IAAI,UAAU,CAAC;AAC9C;", "names": ["progress", "ease", "frameData", "steps", "process", "alpha", "alpha", "progress", "color", "mixNumber", "scale", "progress", "current", "undampedFreq", "keys", "current", "progress", "keyframes", "ease", "ease", "keyframes", "percent", "motionValue", "delay", "keyframes", "progress", "now", "keyframes", "transform", "transform", "motionValue", "keyframes", "delay", "ease", "keyframes", "motionValue", "keyframes", "current", "motionValue", "delay", "keyframes", "motionValue", "sync", "time", "current", "number", "getAnimatableNone", "getAnimatableNone", "motionValue", "keyframes", "acceleratedValues", "current", "transform", "getComputedStyle", "progress", "ease", "distance", "delay", "transform", "keyframes", "keyframes", "process", "keyframes", "keyframes", "current", "next", "keyframes", "keyframes", "delay", "ease", "getValueTransition", "current", "camelToDash", "camelToDash", "isNotNull", "getFinalKeyframe", "keyframes", "keyframes", "delay", "getFinalKeyframe", "delay", "point", "next", "now", "getAnimatableNone", "style", "<PERSON><PERSON><PERSON><PERSON>", "buildTransform", "transform", "style", "hasTransform", "buildTransform", "style", "style", "getComputedStyle", "keys", "isSVGTag", "style", "camelToDash", "scrapeMotionValuesFromProps", "camelToDash", "scrapeMotionValuesFromProps", "keyframes", "keyframes", "keyframes", "keyframes", "getComputedStyle", "keyframes", "maxElapsed", "time", "current", "asNumber", "defaultOffset", "time", "notifyAll", "listener", "progress", "observer", "start"]}