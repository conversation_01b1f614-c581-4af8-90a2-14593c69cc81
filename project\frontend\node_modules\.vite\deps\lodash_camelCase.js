import {
  require_upperFirst
} from "./chunk-WRHJBJWA.js";
import "./chunk-3FJH2MAX.js";
import {
  require_createCompounder
} from "./chunk-32OCQV76.js";
import "./chunk-TPPCP22B.js";
import "./chunk-WI7ETHBW.js";
import {
  require_toString
} from "./chunk-VZITUV5G.js";
import "./chunk-CWSHORJK.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/capitalize.js
var require_capitalize = __commonJS({
  "node_modules/lodash/capitalize.js"(exports, module) {
    var toString = require_toString();
    var upperFirst = require_upperFirst();
    function capitalize(string) {
      return upperFirst(toString(string).toLowerCase());
    }
    module.exports = capitalize;
  }
});

// node_modules/lodash/camelCase.js
var require_camelCase = __commonJS({
  "node_modules/lodash/camelCase.js"(exports, module) {
    var capitalize = require_capitalize();
    var createCompounder = require_createCompounder();
    var camelCase = createCompounder(function(result, word, index) {
      word = word.toLowerCase();
      return result + (index ? capitalize(word) : word);
    });
    module.exports = camelCase;
  }
});
export default require_camelCase();
//# sourceMappingURL=lodash_camelCase.js.map
