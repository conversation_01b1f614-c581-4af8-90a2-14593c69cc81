{"version": 3, "sources": ["../../lodash/uniq.js"], "sourcesContent": ["var baseUniq = require('./_baseUniq');\n\n/**\n * Creates a duplicate-free version of an array, using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons, in which only the first occurrence of each element\n * is kept. The order of result values is determined by the order they occur\n * in the array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.uniq([2, 1, 2]);\n * // => [2, 1]\n */\nfunction uniq(array) {\n  return (array && array.length) ? baseUniq(array) : [];\n}\n\nmodule.exports = uniq;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAoBf,aAAS,KAAK,OAAO;AACnB,aAAQ,SAAS,MAAM,SAAU,SAAS,KAAK,IAAI,CAAC;AAAA,IACtD;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}