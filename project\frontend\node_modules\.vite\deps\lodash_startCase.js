import {
  require_upperFirst
} from "./chunk-WRHJBJWA.js";
import "./chunk-3FJH2MAX.js";
import {
  require_createCompounder
} from "./chunk-32OCQV76.js";
import "./chunk-TPPCP22B.js";
import "./chunk-WI7ETHBW.js";
import "./chunk-VZITUV5G.js";
import "./chunk-CWSHORJK.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/startCase.js
var require_startCase = __commonJS({
  "node_modules/lodash/startCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var upperFirst = require_upperFirst();
    var startCase = createCompounder(function(result, word, index) {
      return result + (index ? " " : "") + upperFirst(word);
    });
    module.exports = startCase;
  }
});
export default require_startCase();
//# sourceMappingURL=lodash_startCase.js.map
