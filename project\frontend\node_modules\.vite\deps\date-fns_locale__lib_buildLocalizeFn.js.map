{"version": 3, "sources": ["../../date-fns/locale/_lib/buildLocalizeFn/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = buildLocalizeFn;\nfunction buildLocalizeFn(args) {\n  return function (dirtyIndex, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex;\n    // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n    return valuesArray[index];\n  };\n}\nmodule.exports = exports.default;"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,aAAS,gBAAgB,MAAM;AAC7B,aAAO,SAAU,YAAY,SAAS;AACpC,YAAI,UAAU,YAAY,QAAQ,YAAY,UAAU,QAAQ,UAAU,OAAO,QAAQ,OAAO,IAAI;AACpG,YAAI;AACJ,YAAI,YAAY,gBAAgB,KAAK,kBAAkB;AACrD,cAAI,eAAe,KAAK,0BAA0B,KAAK;AACvD,cAAI,QAAQ,YAAY,QAAQ,YAAY,UAAU,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI;AAC9F,wBAAc,KAAK,iBAAiB,KAAK,KAAK,KAAK,iBAAiB,YAAY;AAAA,QAClF,OAAO;AACL,cAAI,gBAAgB,KAAK;AACzB,cAAI,SAAS,YAAY,QAAQ,YAAY,UAAU,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI,KAAK;AACpG,wBAAc,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,aAAa;AAAA,QAChE;AACA,YAAI,QAAQ,KAAK,mBAAmB,KAAK,iBAAiB,UAAU,IAAI;AAExE,eAAO,YAAY,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,WAAO,UAAU,QAAQ;AAAA;AAAA;", "names": []}