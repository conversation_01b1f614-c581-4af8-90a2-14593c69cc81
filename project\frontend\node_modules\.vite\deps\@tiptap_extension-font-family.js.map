{"version": 3, "sources": ["../../@tiptap/extension-font-family/src/font-family.ts"], "sourcesContent": ["import '@tiptap/extension-text-style'\n\nimport { Extension } from '@tiptap/core'\n\nexport type FontFamilyOptions = {\n  /**\n   * A list of node names where the font family can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[],\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    fontFamily: {\n      /**\n       * Set the font family\n       * @param fontFamily The font family\n       * @example editor.commands.setFontFamily('Arial')\n       */\n      setFontFamily: (fontFamily: string) => ReturnType,\n      /**\n       * Unset the font family\n       * @example editor.commands.unsetFontFamily()\n       */\n      unsetFontFamily: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to set a font family for text.\n * @see https://www.tiptap.dev/api/extensions/font-family\n */\nexport const FontFamily = Extension.create<FontFamilyOptions>({\n  name: 'fontFamily',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          fontFamily: {\n            default: null,\n            parseHTML: element => element.style.fontFamily,\n            renderHTML: attributes => {\n              if (!attributes.fontFamily) {\n                return {}\n              }\n\n              return {\n                style: `font-family: ${attributes.fontFamily}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setFontFamily: fontFamily => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { fontFamily })\n          .run()\n      },\n      unsetFontFamily: () => ({ chain }) => {\n        return chain()\n          .setMark('textStyle', { fontFamily: null })\n          .removeEmptyTextStyle()\n          .run()\n      },\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;AAmCa,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO,CAAC,WAAW;;;EAIvB,sBAAmB;AACjB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,YAAY;YACV,SAAS;YACT,WAAW,aAAW,QAAQ,MAAM;YACpC,YAAY,gBAAa;AACvB,kBAAI,CAAC,WAAW,YAAY;AAC1B,uBAAO,CAAA;;AAGT,qBAAO;gBACL,OAAO,gBAAgB,WAAW,UAAU;;;UAGjD;QACF;MACF;;;EAIL,cAAW;AACT,WAAO;MACL,eAAe,gBAAc,CAAC,EAAE,MAAK,MAAM;AACzC,eAAO,MAAK,EACT,QAAQ,aAAa,EAAE,WAAU,CAAE,EACnC,IAAG;;MAER,iBAAiB,MAAM,CAAC,EAAE,MAAK,MAAM;AACnC,eAAO,MAAK,EACT,QAAQ,aAAa,EAAE,YAAY,KAAI,CAAE,EACzC,qBAAoB,EACpB,IAAG;;;;AAIb,CAAA;", "names": []}