import {
  _typeof,
  toDate
} from "./chunk-HUOGXHLH.js";
import {
  requiredArgs
} from "./chunk-Z43A42SM.js";

// node_modules/date-fns/esm/isDate/index.js
function isDate(value) {
  requiredArgs(1, arguments);
  return value instanceof Date || _typeof(value) === "object" && Object.prototype.toString.call(value) === "[object Date]";
}

// node_modules/date-fns/esm/isValid/index.js
function isValid(dirtyDate) {
  requiredArgs(1, arguments);
  if (!isDate(dirtyDate) && typeof dirtyDate !== "number") {
    return false;
  }
  var date = toDate(dirtyDate);
  return !isNaN(Number(date));
}

export {
  isValid
};
//# sourceMappingURL=chunk-JDVYNLU2.js.map
