// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CreatorItemBusinessInformation (builder) > should build schema to match the previous snapshot, locale=en 1`] = `
{
  "builder": {
    "type": "business_information",
  },
  "display": {
    "label": "Please enter your Business Information",
  },
  "fields": {
    "country": "business_information_country",
    "id": "business_information_id",
    "name": "business_information_name",
  },
  "items": {
    "business_information_country": {
      "builder": {
        "type": "country",
      },
      "display": {
        "label": "Country",
        "placeholder": undefined,
        "searchable": false,
      },
      "enum": undefined,
      "enum_final_filter_includes": [
        "THA",
        "JPN",
        "HKG",
        "MYS",
        "SGP",
        "PHL",
        "AUS",
      ],
      "enum_presets": undefined,
      "layout": "InputControl",
      "name": "business_information_country",
      "type": "CountrySelect",
      "validator_rule": undefined,
      "visible": undefined,
      "visible_flag_invert": undefined,
    },
    "business_information_id": {
      "builder": {
        "type": "short_long_answer",
      },
      "display": {
        "label": "Business ID",
        "mask": undefined,
        "placeholder": "Your Business ID",
      },
      "layout": "InputControl",
      "name": "business_information_id",
      "props": {
        "autocomplete": undefined,
        "maxlength": 280,
        "type": undefined,
      },
      "type": "InputText",
      "validator_rule": "business_id:business_information_country",
      "visible": undefined,
      "visible_flag_invert": undefined,
    },
    "business_information_name": {
      "builder": {
        "type": "short_long_answer",
      },
      "display": {
        "label": "Business Name",
        "mask": undefined,
        "placeholder": "Your Business Name",
      },
      "layout": "InputControl",
      "name": "business_information_name",
      "props": {
        "autocomplete": undefined,
        "maxlength": 280,
        "type": undefined,
      },
      "type": "InputText",
      "validator_rule": undefined,
      "visible": undefined,
      "visible_flag_invert": undefined,
    },
  },
  "layout": "DefaultWrapper",
  "name": "business_information",
  "type": "BusinessInformation",
  "validator_rule": undefined,
  "visible": undefined,
  "visible_flag_invert": undefined,
}
`;

exports[`CreatorItemBusinessInformation (builder) > should build schema to match the previous snapshot, locale=th 1`] = `
{
  "builder": {
    "type": "business_information",
  },
  "display": {
    "label": "กรุณากรอกข้อมูลธุรกิจของคุณ",
  },
  "fields": {
    "country": "business_information_country",
    "id": "business_information_id",
    "name": "business_information_name",
  },
  "items": {
    "business_information_country": {
      "builder": {
        "type": "country",
      },
      "display": {
        "label": "ประเทศ",
        "placeholder": undefined,
        "searchable": false,
      },
      "enum": undefined,
      "enum_final_filter_includes": [
        "THA",
        "JPN",
        "HKG",
        "MYS",
        "SGP",
        "PHL",
        "AUS",
      ],
      "enum_presets": undefined,
      "layout": "InputControl",
      "name": "business_information_country",
      "type": "CountrySelect",
      "validator_rule": undefined,
      "visible": undefined,
      "visible_flag_invert": undefined,
    },
    "business_information_id": {
      "builder": {
        "type": "short_long_answer",
      },
      "display": {
        "label": "เลขทะเบียนธุรกิจ",
        "mask": undefined,
        "placeholder": "เลขทะเบียนธุรกิจของคุณ",
      },
      "layout": "InputControl",
      "name": "business_information_id",
      "props": {
        "autocomplete": undefined,
        "maxlength": 280,
        "type": undefined,
      },
      "type": "InputText",
      "validator_rule": "business_id:business_information_country",
      "visible": undefined,
      "visible_flag_invert": undefined,
    },
    "business_information_name": {
      "builder": {
        "type": "short_long_answer",
      },
      "display": {
        "label": "ชื่อธุรกิจ",
        "mask": undefined,
        "placeholder": "ชื่อธุรกิจของคุณ",
      },
      "layout": "InputControl",
      "name": "business_information_name",
      "props": {
        "autocomplete": undefined,
        "maxlength": 280,
        "type": undefined,
      },
      "type": "InputText",
      "validator_rule": undefined,
      "visible": undefined,
      "visible_flag_invert": undefined,
    },
  },
  "layout": "DefaultWrapper",
  "name": "business_information",
  "type": "BusinessInformation",
  "validator_rule": undefined,
  "visible": undefined,
  "visible_flag_invert": undefined,
}
`;
