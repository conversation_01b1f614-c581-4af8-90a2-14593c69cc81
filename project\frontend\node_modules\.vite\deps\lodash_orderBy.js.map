{"version": 3, "sources": ["../../lodash/orderBy.js"], "sourcesContent": ["var baseOrderBy = require('./_baseOrderBy'),\n    isArray = require('./isArray');\n\n/**\n * This method is like `_.sortBy` except that it allows specifying the sort\n * orders of the iteratees to sort by. If `orders` is unspecified, all values\n * are sorted in ascending order. Otherwise, specify an order of \"desc\" for\n * descending or \"asc\" for ascending sort order of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Array[]|Function[]|Object[]|string[]} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @param {string[]} [orders] The sort orders of `iteratees`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 34 },\n *   { 'user': 'fred',   'age': 40 },\n *   { 'user': 'barney', 'age': 36 }\n * ];\n *\n * // Sort by `user` in ascending order and by `age` in descending order.\n * _.orderBy(users, ['user', 'age'], ['asc', 'desc']);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 40]]\n */\nfunction orderBy(collection, iteratees, orders, guard) {\n  if (collection == null) {\n    return [];\n  }\n  if (!isArray(iteratees)) {\n    iteratees = iteratees == null ? [] : [iteratees];\n  }\n  orders = guard ? undefined : orders;\n  if (!isArray(orders)) {\n    orders = orders == null ? [] : [orders];\n  }\n  return baseOrderBy(collection, iteratees, orders);\n}\n\nmodule.exports = orderBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,UAAU;AA+Bd,aAAS,QAAQ,YAAY,WAAW,QAAQ,OAAO;AACrD,UAAI,cAAc,MAAM;AACtB,eAAO,CAAC;AAAA,MACV;AACA,UAAI,CAAC,QAAQ,SAAS,GAAG;AACvB,oBAAY,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;AAAA,MACjD;AACA,eAAS,QAAQ,SAAY;AAC7B,UAAI,CAAC,QAAQ,MAAM,GAAG;AACpB,iBAAS,UAAU,OAAO,CAAC,IAAI,CAAC,MAAM;AAAA,MACxC;AACA,aAAO,YAAY,YAAY,WAAW,MAAM;AAAA,IAClD;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}