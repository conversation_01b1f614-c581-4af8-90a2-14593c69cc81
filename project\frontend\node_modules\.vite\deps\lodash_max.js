import {
  require_baseGt
} from "./chunk-TH26MYE7.js";
import {
  require_baseExtremum
} from "./chunk-DT54FW77.js";
import "./chunk-Z3AMIQTO.js";
import {
  require_identity
} from "./chunk-64Z5HK43.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/max.js
var require_max = __commonJS({
  "node_modules/lodash/max.js"(exports, module) {
    var baseExtremum = require_baseExtremum();
    var baseGt = require_baseGt();
    var identity = require_identity();
    function max(array) {
      return array && array.length ? baseExtremum(array, identity, baseGt) : void 0;
    }
    module.exports = max;
  }
});
export default require_max();
//# sourceMappingURL=lodash_max.js.map
