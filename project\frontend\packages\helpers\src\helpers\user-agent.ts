import { UAParser } from 'ua-parser-js';

type DeviceType = UAParser.IDevice['type'] | 'unknown_portrait' | 'unknown_landscape';
type OSType = 'ios' | 'android' | 'other_device';
type BrowserType = 'facebook' | 'line' | 'chrome' | 'safari' | 'firefox' | 'other_app';

export type UserAgentResult = Omit<UAParser.IResult, 'device'> & {
  device: Omit<UAParser.IDevice, 'type'> & { type: DeviceType };
};

// * https://developer.mozilla.org/en-US/docs/Web/HTTP/Browser_detection_using_the_user_agent
export const userAgentLog: Partial<{
  userAgentData: any;
  max_touch_points: any;
  ms_max_touch_points: any;
  has_pointer_coarse: any;
  has_pointer_fine: boolean;
  has_orientation: any;
  is_ua_mobile: boolean;
  is_pocket: boolean;
  is_webview: boolean;
  browser_type: BrowserType;
  browser_version: any;
  orientation: any;
  pocket_reason: string;
}> = {};

export function getManualOrientation() {
  if (window?.innerHeight > window?.innerWidth) {
    return 'portrait' as const;
  }
  return 'landscape' as const;
}

export function getUserAgent(uaString?: string) {
  // In ua-parser-js 2.x, we need to explicitly pass the user agent string
  // If no uaString is provided, use navigator.userAgent (which can be mocked in tests)
  const userAgentString = uaString || (typeof navigator !== 'undefined' ? navigator.userAgent : '');
  const uaparser = new UAParser(userAgentString);
  const uaResult = uaparser.getResult() as UserAgentResult;
  const uaRaw = uaResult.ua;
  if (uaResult.device.type === undefined) {
    if (
      /\b(BlackBerry|webOS|iPhone|IEMobile)\b/i.test(uaRaw) ||
      /\b(Android|Windows Phone|iPad|iPod)\b/i.test(uaRaw)
    ) {
      uaResult.device.type = 'mobile';
    } else if (/\b(Windows|Macintosh|Mac OS)\b/i.test(uaRaw)) {
      uaResult.device.type = 'desktop';
    } else {
      const mo = getManualOrientation();
      if (mo === 'portrait') {
        uaResult.device.type = 'unknown_portrait';
      } else {
        uaResult.device.type = 'unknown_landscape';
      }
    }
  }
  return uaResult;
}

/* eslint-disable */
const webviewRules = [
  // if it says it's a webview, let's go with that
  'WebView',
  // iOS webview will be the same as safari but missing "Safari"
  '(iPhone|iPod|iPad)(?!.*Safari)',
  // https://developer.chrome.com/docs/multidevice/user-agent/#webview_user_agent
  'Android.*Version/[0-9].[0-9]',
  // Also, we should save the wv detected for Lollipop
  // Android Lollipop and Above: webview will be the same as native but it will contain "wv"
  'Android.*wv',
  // old chrome android webview agent
  'Linux; U; Android',
  // FB
  '(Facebook|FBAN|FBAV/)',
  // Line
  'Line/',
];

export function getOsType(osName: string): OSType {
  return ['ios', 'android'].includes(osName) ? (osName as OSType) : 'other_device';
}

/* eslint-enable */
export function isWebview(_ua?: string) {
  const ua = _ua || getUserAgent().ua;
  const webviewRegExp = new RegExp(`(${webviewRules.join('|')})`, 'ig');
  const isWebview = !!RegExp(webviewRegExp).exec(ua);
  userAgentLog.is_webview = isWebview;
  return isWebview;
}
export function isLine(_ua?: string) {
  const ua = _ua || getUserAgent().ua;
  const webviewRegExp = /Line\//gi;
  return !!ua.match(webviewRegExp);
}
export function isFacebook(_ua?: string) {
  const ua = _ua || getUserAgent().ua;
  const webviewRegExp = /(Facebook|FBAN|FBAV\/)/gi;
  return !!ua.match(webviewRegExp);
}
export function isHuawei(_ua?: string) {
  const ua = _ua || getUserAgent().ua;
  const webviewRegExp = /Huawei/gi;
  return !!ua.match(webviewRegExp);
}
export function isIos() {
  const ua = getUserAgent();
  const osName = ua.os.name?.toLowerCase();
  return osName === 'ios';
}
export function isApple() {
  const ua = getUserAgent();
  const fullUa = ua.ua?.toLowerCase() ?? '';
  const isOsMatch =
    fullUa.includes('iphone') || fullUa.includes('ipad') || fullUa.includes('macintosh');
  return isOsMatch;
}

export function isBadBrowser(_ua?: UserAgentResult) {
  const ua = _ua || getUserAgent();
  const osName = ua.os?.name?.toLowerCase() ?? '';
  const browserName = ua.browser?.name?.toLowerCase() ?? '';
  const osType = getOsType(osName);
  return (
    isWebview(ua.ua) || (osType === 'ios' && !browserName.includes('safari'))
  ); /* || !isWebRtcSupport() */
}

export function isPocketDevice() {
  let yes = false;
  let maxTouchPoints = null;
  let msMaxTouchPoints = null;
  let orientation = null;

  // * 1. Explicit User Agent Data
  if ('userAgentData' in navigator) {
    const userAgentData = (navigator as any)?.userAgentData;
    userAgentLog.userAgentData = userAgentData;
    const check = !!userAgentData?.mobile;
    if (check) {
      yes = true;
      userAgentLog.pocket_reason = 'explicit';
    }
  }

  // * 2. Capability detection
  // A true "pocket device" has a coarse pointer (touch) but NOT a fine pointer (mouse/trackpad).
  // This correctly excludes touch-screen laptops, which have both.
  if ('matchMedia' in window) {
    const hasPointerCoarse = window.matchMedia?.('(pointer:coarse)')?.matches;
    const hasPointerFine = window.matchMedia?.('(pointer:fine)')?.matches;
    userAgentLog.has_pointer_coarse = hasPointerCoarse;
    userAgentLog.has_pointer_fine = hasPointerFine;
    const check = hasPointerCoarse && !hasPointerFine;
    if (check) {
      yes = true;
      userAgentLog.pocket_reason = 'pointer';
    }
  }

  // * User agent sniffing, will be used to co-check ALL below legacy checks.
  const userAgent = getUserAgent();
  console.log('ua:', userAgent);
  const isUaMobile = ['mobile', 'tablet', 'unknown_portrait'].includes(userAgent.device.type);
  userAgentLog.is_ua_mobile = isUaMobile;

  // * 3. Touch points.
  // False report because now it's also in laptops, but still a reasonable check.
  if ('maxTouchPoints' in navigator) {
    maxTouchPoints = navigator.maxTouchPoints;
    userAgentLog.max_touch_points = maxTouchPoints;
  }
  if ('msMaxTouchPoints' in navigator) {
    msMaxTouchPoints = (navigator as any).msMaxTouchPoints;
    userAgentLog.ms_max_touch_points = msMaxTouchPoints;
  }
  const check = isUaMobile && (maxTouchPoints > 0 || msMaxTouchPoints > 0);
  if (check) {
    yes = true;
    userAgentLog.pocket_reason = 'touch_points';
  }

  // * 4. Orientation (deprecated, but good fallback)
  if ('orientation' in window) {
    orientation = (window as any).orientation;
    userAgentLog.orientation = orientation;
    const check = isUaMobile && !!orientation;
    if (check) {
      yes = true;
      userAgentLog.pocket_reason = 'orientation';
    }
  }
  userAgentLog.is_pocket = yes;

  return yes;
}

export function isWebRtcSupport() {
  const md = navigator.mediaDevices;
  if (!md) return false;
  if (!md.enumerateDevices) return false;
  if (!md.getUserMedia) return false;
  return true;
}

function getBrowserType(ua: UserAgentResult): BrowserType {
  const browserName = ua.browser?.name?.toLowerCase();

  if (isLine(ua.ua)) {
    return 'line';
  }
  if (isFacebook(ua.ua)) {
    return 'facebook';
  }
  if (['chrome', 'chromium', 'edge'].includes(browserName)) {
    return 'chrome';
  }
  if (['firefox'].includes(browserName)) {
    return 'firefox';
  }
  if (browserName?.includes('safari')) {
    return 'safari';
  }

  return 'other_app';
}

export function checkBrowser() {
  const ua = getUserAgent();

  const deviceType = ua.device.type;
  const osName = ua.os.name?.toLowerCase() || '';
  const browserName = ua.browser.name || '';

  const osType: OSType = getOsType(osName);
  const isIOS = osType === 'ios';

  const badBrowser = isBadBrowser();

  const browserType: BrowserType = getBrowserType(ua);
  userAgentLog.browser_type = browserType;

  const browserVersion = parseFloat(ua.browser.version);
  userAgentLog.browser_version = browserVersion;

  return {
    deviceType,
    osType,
    browserName,
    browserType,
    browserVersion,
    badBrowser,
    ua,
    isIOS,
    isHuawei: isHuawei(ua.ua),
  };
}
