{"version": 3, "sources": ["../../@tensorflow-models/face-detection/dist/face-detection.esm.js"], "sourcesContent": ["/**\n    * @license\n    * Copyright 2024 Google LLC. All Rights Reserved.\n    * Licensed under the Apache License, Version 2.0 (the \"License\");\n    * you may not use this file except in compliance with the License.\n    * You may obtain a copy of the License at\n    *\n    * http://www.apache.org/licenses/LICENSE-2.0\n    *\n    * Unless required by applicable law or agreed to in writing, software\n    * distributed under the License is distributed on an \"AS IS\" BASIS,\n    * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n    * See the License for the specific language governing permissions and\n    * limitations under the License.\n    * =============================================================================\n    */\nimport{FaceDetection as e}from\"@mediapipe/face_detection\";import{Tensor as t,browser as n,util as i,tidy as o,add as r,mul as a,tensor2d as s,image as h,expandDims as u,cast as c,slice as l,squeeze as f,dispose as d,tensor1d as p,div as m,exp as x,sub as g,concat as y,reshape as v,clipByValue as w,sigmoid as M}from\"@tensorflow/tfjs-core\";import{loadGraphModel as S}from\"@tensorflow/tfjs-converter\";var b=function(){return b=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},b.apply(this,arguments)};function T(e,t,n,i){return new(n||(n=Promise))((function(o,r){function a(e){try{h(i.next(e))}catch(e){r(e)}}function s(e){try{h(i.throw(e))}catch(e){r(e)}}function h(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}h((i=i.apply(e,t||[])).next())}))}function C(e,t){var n,i,o,r,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},\"function\"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError(\"Generator is already executing.\");for(;a;)try{if(n=1,i&&(o=2&r[0]?i.return:r[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,r[1])).done)return o;switch(i=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,i=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){a.label=r[1];break}if(6===r[0]&&a.label<o[1]){a.label=o[1],o=r;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(r);break}o[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(e){r=[6,e],i=0}finally{n=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}}var O=[\"rightEye\",\"leftEye\",\"noseTip\",\"mouthCenter\",\"rightEarTragion\",\"leftEarTragion\"];var B={modelType:\"short\",runtime:\"mediapipe\",maxFaces:1};var z=function(){function i(t){var n=this;this.width=0,this.height=0,this.selfieMode=!1,this.faceDetectorSolution=new e({locateFile:function(e,n){if(t.solutionPath){var i=t.solutionPath.replace(/\\/+$/,\"\");return\"\".concat(i,\"/\").concat(e)}return\"\".concat(n,\"/\").concat(e)}}),this.faceDetectorSolution.setOptions({selfieMode:this.selfieMode,model:t.modelType}),this.faceDetectorSolution.onResults((function(e){if(n.height=e.image.height,n.width=e.image.width,n.faces=[],null!==e.detections)for(var t=0,i=e.detections;t<i.length;t++){var o=i[t];n.faces.push(n.normalizedToAbsolute(o.landmarks,(r=o.boundingBox,a=void 0,s=void 0,h=void 0,a=r.xCenter-r.width/2,s=a+r.width,h=r.yCenter-r.height/2,{xMin:a,xMax:s,yMin:h,yMax:h+r.height,width:r.width,height:r.height})))}var r,a,s,h}))}return i.prototype.normalizedToAbsolute=function(e,t){var n=this;return{keypoints:e.map((function(e,t){return{x:e.x*n.width,y:e.y*n.height,name:O[t]}})),box:{xMin:t.xMin*this.width,yMin:t.yMin*this.height,xMax:t.xMax*this.width,yMax:t.yMax*this.height,width:t.width*this.width,height:t.height*this.height}}},i.prototype.estimateFaces=function(e,i){return T(this,void 0,void 0,(function(){var o,r;return C(this,(function(a){switch(a.label){case 0:return i&&i.flipHorizontal&&i.flipHorizontal!==this.selfieMode&&(this.selfieMode=i.flipHorizontal,this.faceDetectorSolution.setOptions({selfieMode:this.selfieMode})),e instanceof t?(r=ImageData.bind,[4,n.toPixels(e)]):[3,2];case 1:return o=new(r.apply(ImageData,[void 0,a.sent(),e.shape[1],e.shape[0]])),[3,3];case 2:o=e,a.label=3;case 3:return e=o,[4,this.faceDetectorSolution.send({image:e})];case 4:return a.sent(),[2,this.faces]}}))}))},i.prototype.dispose=function(){this.faceDetectorSolution.close()},i.prototype.reset=function(){this.faceDetectorSolution.reset(),this.width=0,this.height=0,this.faces=null,this.selfieMode=!1},i.prototype.initialize=function(){return this.faceDetectorSolution.initialize()},i}();function D(e){return T(this,void 0,void 0,(function(){var t,n;return C(this,(function(i){switch(i.label){case 0:return t=function(e){if(null==e)return b({},B);var t=b({},e);return t.runtime=\"mediapipe\",null==t.modelType&&(t.modelType=B.modelType),null==t.maxFaces&&(t.maxFaces=B.maxFaces),t}(e),[4,(n=new z(t)).initialize()];case 1:return i.sent(),[2,n]}}))}))}function A(e,t,n,i){var o=e.width,r=e.height,a=i?-1:1,s=Math.cos(e.rotation),h=Math.sin(e.rotation),u=e.xCenter,c=e.yCenter,l=1/t,f=1/n,d=new Array(16);return d[0]=o*s*a*l,d[1]=-r*h*l,d[2]=0,d[3]=(-.5*o*s*a+.5*r*h+u)*l,d[4]=o*h*a*f,d[5]=r*s*f,d[6]=0,d[7]=(-.5*r*s-.5*o*h*a+c)*f,d[8]=0,d[9]=0,d[10]=o*l,d[11]=0,d[12]=0,d[13]=0,d[14]=0,d[15]=1,function(e){if(16!==e.length)throw new Error(\"Array length must be 16 but got \".concat(e.length));return[[e[0],e[1],e[2],e[3]],[e[4],e[5],e[6],e[7]],[e[8],e[9],e[10],e[11]],[e[12],e[13],e[14],e[15]]]}(d)}function F(e){return e instanceof t?{height:e.shape[0],width:e.shape[1]}:{height:e.height,width:e.width}}function E(e){return e instanceof t?e:n.fromPixels(e)}function R(e,t){i.assert(0!==e.width,(function(){return\"\".concat(t,\" width cannot be 0.\")})),i.assert(0!==e.height,(function(){return\"\".concat(t,\" height cannot be 0.\")}))}function L(e,t){var n=function(e,t,n,i){var o=t-e,r=i-n;if(0===o)throw new Error(\"Original min and max are both \".concat(e,\", range cannot be 0.\"));var a=r/o;return{scale:a,offset:n-e*a}}(0,255,t[0],t[1]);return o((function(){return r(a(e,n.scale),n.offset)}))}function K(e,t,n){var i=t.outputTensorSize,r=t.keepAspectRatio,a=t.borderMode,l=t.outputTensorFloatRange,f=F(e),d=function(e,t){return t?{xCenter:t.xCenter*e.width,yCenter:t.yCenter*e.height,width:t.width*e.width,height:t.height*e.height,rotation:t.rotation}:{xCenter:.5*e.width,yCenter:.5*e.height,width:e.width,height:e.height,rotation:0}}(f,n),p=function(e,t,n){if(void 0===n&&(n=!1),!n)return{top:0,left:0,right:0,bottom:0};var i=t.height,o=t.width;R(t,\"targetSize\"),R(e,\"roi\");var r,a,s=i/o,h=e.height/e.width,u=0,c=0;return s>h?(r=e.width,a=e.width*s,c=(1-h/s)/2):(r=e.height/s,a=e.height,u=(1-s/h)/2),e.width=r,e.height=a,{top:c,left:u,right:u,bottom:c}}(d,i,r),m=A(d,f.width,f.height,!1),x=o((function(){var t=E(e),n=s(function(e,t,n){return R(n,\"inputResolution\"),[1/n.width*e[0][0]*t.width,1/n.height*e[0][1]*t.width,e[0][3]*t.width,1/n.width*e[1][0]*t.height,1/n.height*e[1][1]*t.height,e[1][3]*t.height,0,0]}(m,f,i),[1,8]),o=\"zero\"===a?\"constant\":\"nearest\",r=h.transform(u(c(t,\"float32\")),n,\"bilinear\",o,0,[i.height,i.width]);return null!=l?L(r,l):r}));return{imageTensor:x,padding:p,transformationMatrix:m}}function k(e){null==e.reduceBoxesInLowestLayer&&(e.reduceBoxesInLowestLayer=!1),null==e.interpolatedScaleAspectRatio&&(e.interpolatedScaleAspectRatio=1),null==e.fixedAnchorSize&&(e.fixedAnchorSize=!1);for(var t=[],n=0;n<e.numLayers;){for(var i=[],o=[],r=[],a=[],s=n;s<e.strides.length&&e.strides[s]===e.strides[n];){var h=P(e.minScale,e.maxScale,s,e.strides.length);if(0===s&&e.reduceBoxesInLowestLayer)r.push(1),r.push(2),r.push(.5),a.push(.1),a.push(h),a.push(h);else{for(var u=0;u<e.aspectRatios.length;++u)r.push(e.aspectRatios[u]),a.push(h);if(e.interpolatedScaleAspectRatio>0){var c=s===e.strides.length-1?1:P(e.minScale,e.maxScale,s+1,e.strides.length);a.push(Math.sqrt(h*c)),r.push(e.interpolatedScaleAspectRatio)}}s++}for(var l=0;l<r.length;++l){var f=Math.sqrt(r[l]);i.push(a[l]/f),o.push(a[l]*f)}var d=0,p=0;if(e.featureMapHeight.length>0)d=e.featureMapHeight[n],p=e.featureMapWidth[n];else{var m=e.strides[n];d=Math.ceil(e.inputSizeHeight/m),p=Math.ceil(e.inputSizeWidth/m)}for(var x=0;x<d;++x)for(var g=0;g<p;++g)for(var y=0;y<i.length;++y){var v={xCenter:(g+e.anchorOffsetX)/p,yCenter:(x+e.anchorOffsetY)/d,width:0,height:0};e.fixedAnchorSize?(v.width=1,v.height=1):(v.width=o[y],v.height=i[y]),t.push(v)}n=s}return t}function P(e,t,n,i){return 1===i?.5*(e+t):e+(t-e)*n/(i-1)}function V(e,t){var n=t[0],i=t[1];return[n*e[0]+i*e[1]+e[3],n*e[4]+i*e[5]+e[7]]}function H(e){return o((function(){var t=function(e){return o((function(){return[l(e,[0,0,0],[1,-1,1]),l(e,[0,0,1],[1,-1,-1])]}))}(e),n=t[0],i=t[1];return{boxes:f(i),logits:f(n)}}))}function U(e,t,n,i){return T(this,void 0,void 0,(function(){var i,o,r,a,u;return C(this,(function(c){switch(c.label){case 0:return e.sort((function(e,t){return Math.max.apply(Math,t.score)-Math.max.apply(Math,e.score)})),i=s(e.map((function(e){return[e.locationData.relativeBoundingBox.yMin,e.locationData.relativeBoundingBox.xMin,e.locationData.relativeBoundingBox.yMax,e.locationData.relativeBoundingBox.xMax]}))),o=p(e.map((function(e){return e.score[0]}))),[4,h.nonMaxSuppressionAsync(i,o,t,n)];case 1:return[4,(r=c.sent()).array()];case 2:return a=c.sent(),u=e.filter((function(e,t){return a.indexOf(t)>-1})),d([i,o,r]),[2,u]}}))}))}function j(e,t,n){return T(this,void 0,void 0,(function(){var i,s,h,u,c;return C(this,(function(p){switch(p.label){case 0:return i=e[0],s=e[1],h=function(e,t,n){return o((function(){var i,o,s,h;n.reverseOutputOrder?(o=f(l(e,[0,n.boxCoordOffset+0],[-1,1])),i=f(l(e,[0,n.boxCoordOffset+1],[-1,1])),h=f(l(e,[0,n.boxCoordOffset+2],[-1,1])),s=f(l(e,[0,n.boxCoordOffset+3],[-1,1]))):(i=f(l(e,[0,n.boxCoordOffset+0],[-1,1])),o=f(l(e,[0,n.boxCoordOffset+1],[-1,1])),s=f(l(e,[0,n.boxCoordOffset+2],[-1,1])),h=f(l(e,[0,n.boxCoordOffset+3],[-1,1]))),o=r(a(m(o,n.xScale),t.w),t.x),i=r(a(m(i,n.yScale),t.h),t.y),n.applyExponentialOnBoxSize?(s=a(x(m(s,n.hScale)),t.h),h=a(x(m(h,n.wScale)),t.w)):(s=a(m(s,n.hScale),t.h),h=a(m(h,n.wScale),t.h));var u=g(i,m(s,2)),c=g(o,m(h,2)),d=r(i,m(s,2)),p=r(o,m(h,2)),w=y([v(u,[n.numBoxes,1]),v(c,[n.numBoxes,1]),v(d,[n.numBoxes,1]),v(p,[n.numBoxes,1])],1);if(n.numKeypoints)for(var M=0;M<n.numKeypoints;++M){var S=n.keypointCoordOffset+M*n.numValuesPerKeypoint,b=void 0,T=void 0;n.reverseOutputOrder?(b=f(l(e,[0,S],[-1,1])),T=f(l(e,[0,S+1],[-1,1]))):(T=f(l(e,[0,S],[-1,1])),b=f(l(e,[0,S+1],[-1,1])));var C=r(a(m(b,n.xScale),t.w),t.x),O=r(a(m(T,n.yScale),t.h),t.y);w=y([w,v(C,[n.numBoxes,1]),v(O,[n.numBoxes,1])],1)}return w}))}(s,t,n),u=o((function(){var e=i;return n.sigmoidScore?(null!=n.scoreClippingThresh&&(e=w(i,-n.scoreClippingThresh,n.scoreClippingThresh)),e=M(e)):e})),[4,I(h,u,n)];case 1:return c=p.sent(),d([h,u]),[2,c]}}))}))}function I(e,t,n){return T(this,void 0,void 0,(function(){var i,o,r,a,s,h,u,c,l,f,d,p;return C(this,(function(m){switch(m.label){case 0:return i=[],[4,e.data()];case 1:return o=m.sent(),[4,t.data()];case 2:for(r=m.sent(),a=0;a<n.numBoxes;++a)if(!(null!=n.minScoreThresh&&r[a]<n.minScoreThresh||(s=a*n.numCoords,h=_(o[s+0],o[s+1],o[s+2],o[s+3],r[a],n.flipVertically,a),(u=h.locationData.relativeBoundingBox).width<0||u.height<0))){if(n.numKeypoints>0)for((c=h.locationData).relativeKeypoints=[],l=n.numKeypoints*n.numValuesPerKeypoint,f=0;f<l;f+=n.numValuesPerKeypoint)d=s+n.keypointCoordOffset+f,p={x:o[d+0],y:n.flipVertically?1-o[d+1]:o[d+1]},c.relativeKeypoints.push(p);i.push(h)}return[2,i]}}))}))}function _(e,t,n,i,o,r,a){return{score:[o],ind:a,locationData:{relativeBoundingBox:{xMin:t,yMin:r?1-n:e,xMax:i,yMax:r?1-e:n,width:i-t,height:n-e}}}}var N={reduceBoxesInLowestLayer:!1,interpolatedScaleAspectRatio:1,featureMapHeight:[],featureMapWidth:[],numLayers:4,minScale:.1484375,maxScale:.75,inputSizeHeight:128,inputSizeWidth:128,anchorOffsetX:.5,anchorOffsetY:.5,strides:[8,16,16,16],aspectRatios:[1],fixedAnchorSize:!0},W={reduceBoxesInLowestLayer:!1,interpolatedScaleAspectRatio:0,featureMapHeight:[],featureMapWidth:[],numLayers:1,minScale:.1484375,maxScale:.75,inputSizeHeight:192,inputSizeWidth:192,anchorOffsetX:.5,anchorOffsetY:.5,strides:[4],aspectRatios:[1],fixedAnchorSize:!0},X={runtime:\"tfjs\",modelType:\"short\",maxFaces:1,detectorModelUrl:\"https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1\"},Y={applyExponentialOnBoxSize:!1,flipVertically:!1,ignoreClasses:[],numClasses:1,numBoxes:896,numCoords:16,boxCoordOffset:0,keypointCoordOffset:4,numKeypoints:6,numValuesPerKeypoint:2,sigmoidScore:!0,scoreClippingThresh:100,reverseOutputOrder:!0,xScale:128,yScale:128,hScale:128,wScale:128,minScoreThresh:.5},q={applyExponentialOnBoxSize:!1,flipVertically:!1,ignoreClasses:[],numClasses:1,numBoxes:2304,numCoords:16,boxCoordOffset:0,keypointCoordOffset:4,numKeypoints:6,numValuesPerKeypoint:2,sigmoidScore:!0,scoreClippingThresh:100,reverseOutputOrder:!0,xScale:192,yScale:192,hScale:192,wScale:192,minScoreThresh:.6},G=.3,$={outputTensorSize:{width:128,height:128},keepAspectRatio:!0,outputTensorFloatRange:[-1,1],borderMode:\"zero\"},J={outputTensorSize:{width:192,height:192},keepAspectRatio:!0,outputTensorFloatRange:[-1,1],borderMode:\"zero\"};var Q,Z=function(){function e(e,t,n){this.detectorModel=t,this.maxFaces=n,\"full\"===e?(this.imageToTensorConfig=J,this.tensorsToDetectionConfig=q,this.anchors=k(W)):(this.imageToTensorConfig=$,this.tensorsToDetectionConfig=Y,this.anchors=k(N));var i=p(this.anchors.map((function(e){return e.width}))),o=p(this.anchors.map((function(e){return e.height}))),r=p(this.anchors.map((function(e){return e.xCenter}))),a=p(this.anchors.map((function(e){return e.yCenter})));this.anchorTensor={x:r,y:a,w:i,h:o}}return e.prototype.dispose=function(){this.detectorModel.dispose(),d([this.anchorTensor.x,this.anchorTensor.y,this.anchorTensor.w,this.anchorTensor.h])},e.prototype.reset=function(){},e.prototype.detectFaces=function(e,t){return void 0===t&&(t=!1),T(this,void 0,void 0,(function(){var n,i,r,a,s,l,p,m,x,g,y;return C(this,(function(v){switch(v.label){case 0:return null==e?(this.reset(),[2,[]]):(n=o((function(){var n=c(E(e),\"float32\");if(t){n=f(h.flipLeftRight(u(n,0)),[0])}return n})),i=K(n,this.imageToTensorConfig),r=i.imageTensor,a=i.transformationMatrix,s=this.detectorModel.execute(r,\"Identity:0\"),l=H(s),p=l.boxes,[4,j([m=l.logits,p],this.anchorTensor,this.tensorsToDetectionConfig)]);case 1:return 0===(x=v.sent()).length?(d([n,r,s,m,p]),[2,x]):[4,U(x,this.maxFaces,G)];case 2:return g=v.sent(),y=function(e,t){void 0===e&&(e=[]);var n,i=(n=t,[].concat.apply([],n));return e.forEach((function(e){var t=e.locationData;t.relativeKeypoints.forEach((function(e){var t=V(i,[e.x,e.y]),n=t[0],o=t[1];e.x=n,e.y=o}));var n=t.relativeBoundingBox,o=Number.MAX_VALUE,r=Number.MAX_VALUE,a=Number.MIN_VALUE,s=Number.MIN_VALUE;[[n.xMin,n.yMin],[n.xMin+n.width,n.yMin],[n.xMin+n.width,n.yMin+n.height],[n.xMin,n.yMin+n.height]].forEach((function(e){var t=V(i,e),n=t[0],h=t[1];o=Math.min(o,n),a=Math.max(a,n),r=Math.min(r,h),s=Math.max(s,h)})),t.relativeBoundingBox={xMin:o,xMax:a,yMin:r,yMax:s,width:a-o,height:s-r}})),e}(g,a),d([n,r,s,m,p]),[2,y]}}))}))},e.prototype.estimateFaces=function(e,t){return T(this,void 0,void 0,(function(){var n,i;return C(this,(function(o){return n=F(e),i=!!t&&t.flipHorizontal,[2,this.detectFaces(e,i).then((function(e){return e.map((function(e){for(var t=e.locationData.relativeKeypoints.map((function(e,t){return b(b({},e),{x:e.x*n.width,y:e.y*n.height,name:O[t]})})),i=e.locationData.relativeBoundingBox,o=0,r=[\"width\",\"xMax\",\"xMin\"];o<r.length;o++){i[r[o]]*=n.width}for(var a=0,s=[\"height\",\"yMax\",\"yMin\"];a<s.length;a++){i[s[a]]*=n.height}return{keypoints:t,box:i}}))}))]}))}))},e}();function ee(e){return T(this,void 0,void 0,(function(){var t,n,i;return C(this,(function(o){switch(o.label){case 0:return t=function(e){if(null==e)return b({},X);var t=b({},e);null==t.modelType&&(t.modelType=X.modelType),null==t.maxFaces&&(t.maxFaces=X.maxFaces),null==t.detectorModelUrl&&(\"full\"===t.modelType?t.detectorModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/face_detection/full/1\":t.detectorModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1\");return t}(e),n=\"string\"==typeof t.detectorModelUrl&&t.detectorModelUrl.indexOf(\"https://tfhub.dev\")>-1,[4,S(t.detectorModelUrl,{fromTFHub:n})];case 1:return i=o.sent(),[2,new Z(t.modelType,i,t.maxFaces)]}}))}))}function te(e,t){return T(this,void 0,void 0,(function(){var n,i;return C(this,(function(o){if(e===Q.MediaPipeFaceDetector){if(i=void 0,null!=(n=t)){if(\"tfjs\"===n.runtime)return[2,ee(n)];if(\"mediapipe\"===n.runtime)return[2,D(n)];i=n.runtime}throw new Error(\"Expect modelConfig.runtime to be either 'tfjs' \"+\"or 'mediapipe', but got \".concat(i))}throw new Error(\"\".concat(e,\" is not a supported model name.\"))}))}))}!function(e){e.MediaPipeFaceDetector=\"MediaPipeFaceDetector\"}(Q||(Q={}));export{z as MediaPipeFaceDetectorMediaPipe,Z as MediaPipeFaceDetectorTfjs,Q as SupportedModels,te as createDetector};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,4BAA8B;AAAkX,IAAI,IAAE,WAAU;AAAC,SAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,aAAQ,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,UAAQ,KAAK,IAAE,UAAU,CAAC,EAAE,QAAO,UAAU,eAAe,KAAK,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,EAAE,CAAC;AAAG,WAAOA;AAAA,EAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAC;AAAE,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,KAAI,MAAI,IAAE,UAAW,SAAS,GAAE,GAAE;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,KAAKA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,MAAMA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAIC;AAAE,MAAAD,GAAE,OAAK,EAAEA,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAa,IAAEA,KAAE,IAAI,EAAG,SAASD,IAAE;AAAC,QAAAA,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAG,KAAK,GAAE,CAAC;AAAA,IAAC;AAAC,OAAG,IAAE,EAAE,MAAMD,IAAE,KAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,IAAE,EAAC,OAAM,GAAE,MAAK,WAAU;AAAC,QAAG,IAAE,EAAE,CAAC,EAAE,OAAM,EAAE,CAAC;AAAE,WAAO,EAAE,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC;AAAE,SAAO,IAAE,EAAC,MAAK,EAAE,CAAC,GAAE,OAAM,EAAE,CAAC,GAAE,QAAO,EAAE,CAAC,EAAC,GAAE,cAAY,OAAO,WAAS,EAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,WAAO;AAAA,EAAI,IAAG;AAAE,WAAS,EAAEE,IAAE;AAAC,WAAO,SAASC,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,YAAG,EAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,eAAK,IAAG,KAAG;AAAC,cAAG,IAAE,GAAE,MAAI,IAAE,IAAEA,GAAE,CAAC,IAAE,EAAE,SAAOA,GAAE,CAAC,IAAE,EAAE,WAAS,IAAE,EAAE,WAAS,EAAE,KAAK,CAAC,GAAE,KAAG,EAAE,SAAO,EAAE,IAAE,EAAE,KAAK,GAAEA,GAAE,CAAC,CAAC,GAAG,KAAK,QAAO;AAAE,kBAAO,IAAE,GAAE,MAAIA,KAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,KAAK,IAAGA,GAAE,CAAC,GAAE;AAAA,YAAC,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAEA;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAO,EAAE,SAAQ,EAAC,OAAMA,GAAE,CAAC,GAAE,MAAK,MAAE;AAAA,YAAE,KAAK;AAAE,gBAAE,SAAQ,IAAEA,GAAE,CAAC,GAAEA,KAAE,CAAC,CAAC;AAAE;AAAA,YAAS,KAAK;AAAE,cAAAA,KAAE,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,YAAS;AAAQ,kBAAG,EAAE,IAAE,EAAE,OAAM,IAAE,EAAE,SAAO,KAAG,EAAE,EAAE,SAAO,CAAC,MAAI,MAAIA,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,IAAG;AAAC,oBAAE;AAAE;AAAA,cAAQ;AAAC,kBAAG,MAAIA,GAAE,CAAC,MAAI,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,IAAG;AAAC,kBAAE,QAAMA,GAAE,CAAC;AAAE;AAAA,cAAK;AAAC,kBAAG,MAAIA,GAAE,CAAC,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,IAAEA;AAAE;AAAA,cAAK;AAAC,kBAAG,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,EAAE,IAAI,KAAKA,EAAC;AAAE;AAAA,cAAK;AAAC,gBAAE,CAAC,KAAG,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,UAAQ;AAAC,UAAAA,KAAE,EAAE,KAAKF,IAAE,CAAC;AAAA,QAAC,SAAOA,IAAE;AAAC,UAAAE,KAAE,CAAC,GAAEF,EAAC,GAAE,IAAE;AAAA,QAAC,UAAC;AAAQ,cAAE,IAAE;AAAA,QAAC;AAAC,YAAG,IAAEE,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,eAAM,EAAC,OAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,QAAO,MAAK,KAAE;AAAA,MAAC,EAAE,CAACA,IAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,CAAC,YAAW,WAAU,WAAU,eAAc,mBAAkB,gBAAgB;AAAE,IAAI,IAAE,EAAC,WAAU,SAAQ,SAAQ,aAAY,UAAS,EAAC;AAAE,IAAI,IAAE,WAAU;AAAC,WAAS,EAAE,GAAE;AAAC,QAAI,IAAE;AAAK,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,aAAW,OAAG,KAAK,uBAAqB,IAAI,sBAAAH,cAAE,EAAC,YAAW,SAASA,IAAEI,IAAE;AAAC,UAAG,EAAE,cAAa;AAAC,YAAIC,KAAE,EAAE,aAAa,QAAQ,QAAO,EAAE;AAAE,eAAM,GAAG,OAAOA,IAAE,GAAG,EAAE,OAAOL,EAAC;AAAA,MAAC;AAAC,aAAM,GAAG,OAAOI,IAAE,GAAG,EAAE,OAAOJ,EAAC;AAAA,IAAC,EAAC,CAAC,GAAE,KAAK,qBAAqB,WAAW,EAAC,YAAW,KAAK,YAAW,OAAM,EAAE,UAAS,CAAC,GAAE,KAAK,qBAAqB,UAAW,SAASA,IAAE;AAAC,UAAG,EAAE,SAAOA,GAAE,MAAM,QAAO,EAAE,QAAMA,GAAE,MAAM,OAAM,EAAE,QAAM,CAAC,GAAE,SAAOA,GAAE,WAAW,UAAQC,KAAE,GAAEI,KAAEL,GAAE,YAAWC,KAAEI,GAAE,QAAOJ,MAAI;AAAC,YAAI,IAAEI,GAAEJ,EAAC;AAAE,UAAE,MAAM,KAAK,EAAE,qBAAqB,EAAE,YAAW,IAAE,EAAE,aAAY,IAAE,QAAO,IAAE,QAAO,IAAE,QAAO,IAAE,EAAE,UAAQ,EAAE,QAAM,GAAE,IAAE,IAAE,EAAE,OAAM,IAAE,EAAE,UAAQ,EAAE,SAAO,GAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,IAAE,EAAE,QAAO,OAAM,EAAE,OAAM,QAAO,EAAE,OAAM,EAAE,CAAC;AAAA,MAAC;AAAC,UAAI,GAAE,GAAE,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO,EAAE,UAAU,uBAAqB,SAASD,IAAE,GAAE;AAAC,QAAI,IAAE;AAAK,WAAM,EAAC,WAAUA,GAAE,IAAK,SAASA,IAAEC,IAAE;AAAC,aAAM,EAAC,GAAED,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,QAAO,MAAK,EAAEC,EAAC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAI,EAAC,MAAK,EAAE,OAAK,KAAK,OAAM,MAAK,EAAE,OAAK,KAAK,QAAO,MAAK,EAAE,OAAK,KAAK,OAAM,MAAK,EAAE,OAAK,KAAK,QAAO,OAAM,EAAE,QAAM,KAAK,OAAM,QAAO,EAAE,SAAO,KAAK,OAAM,EAAC;AAAA,EAAC,GAAE,EAAE,UAAU,gBAAc,SAASD,IAAEK,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAOA,MAAGA,GAAE,kBAAgBA,GAAE,mBAAiB,KAAK,eAAa,KAAK,aAAWA,GAAE,gBAAe,KAAK,qBAAqB,WAAW,EAAC,YAAW,KAAK,WAAU,CAAC,IAAGL,cAAa,UAAG,IAAE,UAAU,MAAK,CAAC,GAAE,gBAAE,SAASA,EAAC,CAAC,KAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,KAAI,EAAE,MAAM,WAAU,CAAC,QAAO,EAAE,KAAK,GAAEA,GAAE,MAAM,CAAC,GAAEA,GAAE,MAAM,CAAC,CAAC,CAAC,MAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAEA,IAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAOA,KAAE,GAAE,CAAC,GAAE,KAAK,qBAAqB,KAAK,EAAC,OAAMA,GAAC,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,KAAK,KAAK;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,qBAAqB,MAAM;AAAA,EAAC,GAAE,EAAE,UAAU,QAAM,WAAU;AAAC,SAAK,qBAAqB,MAAM,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,MAAK,KAAK,aAAW;AAAA,EAAE,GAAE,EAAE,UAAU,aAAW,WAAU;AAAC,WAAO,KAAK,qBAAqB,WAAW;AAAA,EAAC,GAAE;AAAC,EAAE;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,CAAC;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,mBAAOC,GAAE,UAAQ,aAAY,QAAMA,GAAE,cAAYA,GAAE,YAAU,EAAE,YAAW,QAAMA,GAAE,aAAWA,GAAE,WAAS,EAAE,WAAUA;AAAA,UAAC,EAAED,EAAC,GAAE,CAAC,IAAG,IAAE,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAEA,GAAE,OAAM,IAAEA,GAAE,QAAO,IAAE,IAAE,KAAG,GAAE,IAAE,KAAK,IAAIA,GAAE,QAAQ,GAAE,IAAE,KAAK,IAAIA,GAAE,QAAQ,GAAE,IAAEA,GAAE,SAAQ,IAAEA,GAAE,SAAQ,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,IAAI,MAAM,EAAE;AAAE,SAAO,EAAE,CAAC,IAAE,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,CAAC,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI,IAAE,IAAE,IAAE,MAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI,IAAE,IAAE,MAAG,IAAE,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,EAAE,IAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,SAASA,IAAE;AAAC,QAAG,OAAKA,GAAE,OAAO,OAAM,IAAI,MAAM,mCAAmC,OAAOA,GAAE,MAAM,CAAC;AAAE,WAAM,CAAC,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,GAAE,CAACA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,CAAC;AAAA,EAAC,EAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa,SAAE,EAAC,QAAOA,GAAE,MAAM,CAAC,GAAE,OAAMA,GAAE,MAAM,CAAC,EAAC,IAAE,EAAC,QAAOA,GAAE,QAAO,OAAMA,GAAE,MAAK;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa,SAAEA,KAAE,gBAAE,WAAWA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,eAAE,OAAO,MAAIA,GAAE,OAAO,WAAU;AAAC,WAAM,GAAG,OAAO,GAAE,qBAAqB;AAAA,EAAC,CAAE,GAAE,aAAE,OAAO,MAAIA,GAAE,QAAQ,WAAU;AAAC,WAAM,GAAG,OAAO,GAAE,sBAAsB;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,IAAE,SAASA,IAAEC,IAAEG,IAAE,GAAE;AAAC,QAAI,IAAEH,KAAED,IAAE,IAAE,IAAEI;AAAE,QAAG,MAAI,EAAE,OAAM,IAAI,MAAM,iCAAiC,OAAOJ,IAAE,sBAAsB,CAAC;AAAE,QAAI,IAAE,IAAE;AAAE,WAAM,EAAC,OAAM,GAAE,QAAOI,KAAEJ,KAAE,EAAC;AAAA,EAAC,EAAE,GAAE,KAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,SAAO,KAAG,WAAU;AAAC,WAAO,IAAE,IAAEA,IAAE,EAAE,KAAK,GAAE,EAAE,MAAM;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,kBAAiB,IAAE,EAAE,iBAAgB,IAAE,EAAE,YAAW,IAAE,EAAE,wBAAuB,IAAE,EAAEA,EAAC,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,WAAOA,KAAE,EAAC,SAAQA,GAAE,UAAQD,GAAE,OAAM,SAAQC,GAAE,UAAQD,GAAE,QAAO,OAAMC,GAAE,QAAMD,GAAE,OAAM,QAAOC,GAAE,SAAOD,GAAE,QAAO,UAASC,GAAE,SAAQ,IAAE,EAAC,SAAQ,MAAGD,GAAE,OAAM,SAAQ,MAAGA,GAAE,QAAO,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,UAAS,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEG,IAAE;AAAC,QAAG,WAASA,OAAIA,KAAE,QAAI,CAACA,GAAE,QAAM,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,QAAIC,KAAEJ,GAAE,QAAO,IAAEA,GAAE;AAAM,MAAEA,IAAE,YAAY,GAAE,EAAED,IAAE,KAAK;AAAE,QAAIE,IAAEI,IAAE,IAAED,KAAE,GAAE,IAAEL,GAAE,SAAOA,GAAE,OAAM,IAAE,GAAE,IAAE;AAAE,WAAO,IAAE,KAAGE,KAAEF,GAAE,OAAMM,KAAEN,GAAE,QAAM,GAAE,KAAG,IAAE,IAAE,KAAG,MAAIE,KAAEF,GAAE,SAAO,GAAEM,KAAEN,GAAE,QAAO,KAAG,IAAE,IAAE,KAAG,IAAGA,GAAE,QAAME,IAAEF,GAAE,SAAOM,IAAE,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,EAAC;AAAA,EAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,EAAE,GAAE,EAAE,OAAM,EAAE,QAAO,KAAE,GAAE,IAAE,KAAG,WAAU;AAAC,QAAIL,KAAE,EAAED,EAAC,GAAEI,KAAE,SAAE,SAASJ,IAAEC,IAAEG,IAAE;AAAC,aAAO,EAAEA,IAAE,iBAAiB,GAAE,CAAC,IAAEA,GAAE,QAAMJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,OAAM,IAAEG,GAAE,SAAOJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,OAAMD,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,OAAM,IAAEG,GAAE,QAAMJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,QAAO,IAAEG,GAAE,SAAOJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,QAAOD,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,QAAO,GAAE,CAAC;AAAA,IAAC,EAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,IAAE,WAAS,IAAE,aAAW,WAAUC,KAAE,MAAE,UAAU,WAAE,KAAED,IAAE,SAAS,CAAC,GAAEG,IAAE,YAAW,GAAE,GAAE,CAAC,EAAE,QAAO,EAAE,KAAK,CAAC;AAAE,WAAO,QAAM,IAAE,EAAEF,IAAE,CAAC,IAAEA;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,aAAY,GAAE,SAAQ,GAAE,sBAAqB,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,UAAMA,GAAE,6BAA2BA,GAAE,2BAAyB,QAAI,QAAMA,GAAE,iCAA+BA,GAAE,+BAA6B,IAAG,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB;AAAI,WAAQ,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEA,GAAE,aAAW;AAAC,aAAQ,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEA,GAAE,QAAQ,UAAQA,GAAE,QAAQ,CAAC,MAAIA,GAAE,QAAQ,CAAC,KAAG;AAAC,UAAI,IAAE,EAAEA,GAAE,UAASA,GAAE,UAAS,GAAEA,GAAE,QAAQ,MAAM;AAAE,UAAG,MAAI,KAAGA,GAAE,yBAAyB,GAAE,KAAK,CAAC,GAAE,EAAE,KAAK,CAAC,GAAE,EAAE,KAAK,GAAE,GAAE,EAAE,KAAK,GAAE,GAAE,EAAE,KAAK,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,WAAM;AAAC,iBAAQ,IAAE,GAAE,IAAEA,GAAE,aAAa,QAAO,EAAE,EAAE,GAAE,KAAKA,GAAE,aAAa,CAAC,CAAC,GAAE,EAAE,KAAK,CAAC;AAAE,YAAGA,GAAE,+BAA6B,GAAE;AAAC,cAAI,IAAE,MAAIA,GAAE,QAAQ,SAAO,IAAE,IAAE,EAAEA,GAAE,UAASA,GAAE,UAAS,IAAE,GAAEA,GAAE,QAAQ,MAAM;AAAE,YAAE,KAAK,KAAK,KAAK,IAAE,CAAC,CAAC,GAAE,EAAE,KAAKA,GAAE,4BAA4B;AAAA,QAAC;AAAA,MAAC;AAAC;AAAA,IAAG;AAAC,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,UAAI,IAAE,KAAK,KAAK,EAAE,CAAC,CAAC;AAAE,QAAE,KAAK,EAAE,CAAC,IAAE,CAAC,GAAE,EAAE,KAAK,EAAE,CAAC,IAAE,CAAC;AAAA,IAAC;AAAC,QAAI,IAAE,GAAE,IAAE;AAAE,QAAGA,GAAE,iBAAiB,SAAO,EAAE,KAAEA,GAAE,iBAAiB,CAAC,GAAE,IAAEA,GAAE,gBAAgB,CAAC;AAAA,SAAM;AAAC,UAAI,IAAEA,GAAE,QAAQ,CAAC;AAAE,UAAE,KAAK,KAAKA,GAAE,kBAAgB,CAAC,GAAE,IAAE,KAAK,KAAKA,GAAE,iBAAe,CAAC;AAAA,IAAC;AAAC,aAAQ,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,UAAQ,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,UAAI,IAAE,EAAC,UAAS,IAAEA,GAAE,iBAAe,GAAE,UAAS,IAAEA,GAAE,iBAAe,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,MAAAA,GAAE,mBAAiB,EAAE,QAAM,GAAE,EAAE,SAAO,MAAI,EAAE,QAAM,EAAE,CAAC,GAAE,EAAE,SAAO,EAAE,CAAC,IAAG,EAAE,KAAK,CAAC;AAAA,IAAC;AAAC,QAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,MAAI,IAAE,OAAIA,KAAE,KAAGA,MAAG,IAAEA,MAAG,KAAG,IAAE;AAAE;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,SAAM,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,IAAE,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,KAAG,WAAU;AAAC,QAAI,IAAE,SAASA,IAAE;AAAC,aAAO,KAAG,WAAU;AAAC,eAAM,CAAC,MAAEA,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,CAAC,GAAE,MAAEA,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,EAAEA,EAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,WAAM,EAAC,OAAM,QAAE,CAAC,GAAE,QAAO,QAAE,CAAC,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAIK,IAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAOL,GAAE,KAAM,SAASA,IAAEC,IAAE;AAAC,mBAAO,KAAK,IAAI,MAAM,MAAKA,GAAE,KAAK,IAAE,KAAK,IAAI,MAAM,MAAKD,GAAE,KAAK;AAAA,UAAC,CAAE,GAAEK,KAAE,SAAEL,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAM,CAACA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,IAAI;AAAA,UAAC,CAAE,CAAC,GAAE,IAAE,SAAEA,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAOA,GAAE,MAAM,CAAC;AAAA,UAAC,CAAE,CAAC,GAAE,CAAC,GAAE,MAAE,uBAAuBK,IAAE,GAAE,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,IAAG,IAAE,EAAE,KAAK,GAAG,MAAM,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAEL,GAAE,OAAQ,SAASA,IAAEC,IAAE;AAAC,mBAAO,EAAE,QAAQA,EAAC,IAAE;AAAA,UAAE,CAAE,GAAE,QAAE,CAACI,IAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEL,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEG,IAAE;AAAC,mBAAO,KAAG,WAAU;AAAC,kBAAIC,IAAE,GAAEF,IAAEI;AAAE,cAAAH,GAAE,sBAAoB,IAAE,QAAE,MAAEJ,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEC,KAAE,QAAE,MAAEL,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEG,KAAE,QAAE,MAAEP,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAED,KAAE,QAAE,MAAEH,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,MAAIC,KAAE,QAAE,MAAEL,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEJ,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAED,KAAE,QAAE,MAAEH,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEG,KAAE,QAAE,MAAEP,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,IAAG,IAAE,IAAE,IAAE,IAAE,GAAEA,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEI,KAAE,IAAE,IAAE,IAAEA,IAAED,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,GAAE,6BAA2BD,KAAE,IAAE,IAAE,IAAEA,IAAEC,GAAE,MAAM,CAAC,GAAEH,GAAE,CAAC,GAAEM,KAAE,IAAE,IAAE,IAAEA,IAAEH,GAAE,MAAM,CAAC,GAAEH,GAAE,CAAC,MAAIE,KAAE,IAAE,IAAEA,IAAEC,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEM,KAAE,IAAE,IAAEA,IAAEH,GAAE,MAAM,GAAEH,GAAE,CAAC;AAAG,kBAAIO,KAAE,IAAEH,IAAE,IAAEF,IAAE,CAAC,CAAC,GAAEM,KAAE,IAAE,GAAE,IAAEF,IAAE,CAAC,CAAC,GAAE,IAAE,IAAEF,IAAE,IAAEF,IAAE,CAAC,CAAC,GAAEO,KAAE,IAAE,GAAE,IAAEH,IAAE,CAAC,CAAC,GAAE,IAAE,OAAE,CAAC,QAAEC,IAAE,CAACJ,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEK,IAAE,CAACL,GAAE,UAAS,CAAC,CAAC,GAAE,QAAE,GAAE,CAACA,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEM,IAAE,CAACN,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,CAAC;AAAE,kBAAGA,GAAE,aAAa,UAAQ,IAAE,GAAE,IAAEA,GAAE,cAAa,EAAE,GAAE;AAAC,oBAAI,IAAEA,GAAE,sBAAoB,IAAEA,GAAE,sBAAqBO,KAAE,QAAOC,KAAE;AAAO,gBAAAR,GAAE,sBAAoBO,KAAE,QAAE,MAAEX,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEY,KAAE,QAAE,MAAEZ,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,MAAIY,KAAE,QAAE,MAAEZ,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEW,KAAE,QAAE,MAAEX,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC;AAAG,oBAAIa,KAAE,IAAE,IAAE,IAAEF,IAAEP,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEa,KAAE,IAAE,IAAE,IAAEF,IAAER,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC;AAAE,oBAAE,OAAE,CAAC,GAAE,QAAEY,IAAE,CAACT,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEU,IAAE,CAACV,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,CAAC;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAC,CAAE;AAAA,UAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,KAAG,WAAU;AAAC,gBAAIJ,KAAE;AAAE,mBAAO,EAAE,gBAAc,QAAM,EAAE,wBAAsBA,KAAE,YAAE,GAAE,CAAC,EAAE,qBAAoB,EAAE,mBAAmB,IAAGA,KAAE,QAAEA,EAAC,KAAGA;AAAA,UAAC,CAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,QAAE,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,CAAC,GAAE,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,GAAE,IAAE,EAAE,UAAS,EAAE,EAAE,KAAG,EAAE,QAAM,EAAE,kBAAgB,EAAE,CAAC,IAAE,EAAE,mBAAiB,IAAE,IAAE,EAAE,WAAU,IAAE,EAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,gBAAe,CAAC,IAAG,IAAE,EAAE,aAAa,qBAAqB,QAAM,KAAG,EAAE,SAAO,KAAI;AAAC,gBAAG,EAAE,eAAa,EAAE,OAAK,IAAE,EAAE,cAAc,oBAAkB,CAAC,GAAE,IAAE,EAAE,eAAa,EAAE,sBAAqB,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,qBAAqB,KAAE,IAAE,EAAE,sBAAoB,GAAE,IAAE,EAAC,GAAE,EAAE,IAAE,CAAC,GAAE,GAAE,EAAE,iBAAe,IAAE,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,EAAC,GAAE,EAAE,kBAAkB,KAAK,CAAC;AAAE,cAAE,KAAK,CAAC;AAAA,UAAC;AAAC,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAM,EAAC,OAAM,CAAC,CAAC,GAAE,KAAI,GAAE,cAAa,EAAC,qBAAoB,EAAC,MAAK,GAAE,MAAK,IAAE,IAAE,IAAEA,IAAE,MAAK,GAAE,MAAK,IAAE,IAAEA,KAAE,GAAE,OAAM,IAAE,GAAE,QAAO,IAAEA,GAAC,EAAC,EAAC;AAAC;AAAC,IAAI,IAAE,EAAC,0BAAyB,OAAG,8BAA6B,GAAE,kBAAiB,CAAC,GAAE,iBAAgB,CAAC,GAAE,WAAU,GAAE,UAAS,WAAS,UAAS,MAAI,iBAAgB,KAAI,gBAAe,KAAI,eAAc,KAAG,eAAc,KAAG,SAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,cAAa,CAAC,CAAC,GAAE,iBAAgB,KAAE;AAArR,IAAuR,IAAE,EAAC,0BAAyB,OAAG,8BAA6B,GAAE,kBAAiB,CAAC,GAAE,iBAAgB,CAAC,GAAE,WAAU,GAAE,UAAS,WAAS,UAAS,MAAI,iBAAgB,KAAI,gBAAe,KAAI,eAAc,KAAG,eAAc,KAAG,SAAQ,CAAC,CAAC,GAAE,cAAa,CAAC,CAAC,GAAE,iBAAgB,KAAE;AAA/hB,IAAiiB,IAAE,EAAC,SAAQ,QAAO,WAAU,SAAQ,UAAS,GAAE,kBAAiB,gEAA+D;AAAhqB,IAAkqB,IAAE,EAAC,2BAA0B,OAAG,gBAAe,OAAG,eAAc,CAAC,GAAE,YAAW,GAAE,UAAS,KAAI,WAAU,IAAG,gBAAe,GAAE,qBAAoB,GAAE,cAAa,GAAE,sBAAqB,GAAE,cAAa,MAAG,qBAAoB,KAAI,oBAAmB,MAAG,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,gBAAe,IAAE;AAAp9B,IAAs9B,IAAE,EAAC,2BAA0B,OAAG,gBAAe,OAAG,eAAc,CAAC,GAAE,YAAW,GAAE,UAAS,MAAK,WAAU,IAAG,gBAAe,GAAE,qBAAoB,GAAE,cAAa,GAAE,sBAAqB,GAAE,cAAa,MAAG,qBAAoB,KAAI,oBAAmB,MAAG,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,gBAAe,IAAE;AAAzwC,IAA2wC,IAAE;AAA7wC,IAAgxC,IAAE,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,iBAAgB,MAAG,wBAAuB,CAAC,IAAG,CAAC,GAAE,YAAW,OAAM;AAA73C,IAA+3C,IAAE,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,iBAAgB,MAAG,wBAAuB,CAAC,IAAG,CAAC,GAAE,YAAW,OAAM;AAAE,IAAI;AAAJ,IAAM,IAAE,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE,GAAE;AAAC,SAAK,gBAAc,GAAE,KAAK,WAAS,GAAE,WAASA,MAAG,KAAK,sBAAoB,GAAE,KAAK,2BAAyB,GAAE,KAAK,UAAQ,EAAE,CAAC,MAAI,KAAK,sBAAoB,GAAE,KAAK,2BAAyB,GAAE,KAAK,UAAQ,EAAE,CAAC;AAAG,QAAI,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAK,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAM,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAO,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAO,CAAE,CAAC;AAAE,SAAK,eAAa,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,cAAc,QAAQ,GAAE,QAAE,CAAC,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa,CAAC,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,SAASA,IAAE,GAAE;AAAC,WAAO,WAAS,MAAI,IAAE,QAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,QAAMA,MAAG,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,IAAE,KAAG,WAAU;AAAC,kBAAII,KAAE,KAAE,EAAEJ,EAAC,GAAE,SAAS;AAAE,kBAAG,GAAE;AAAC,gBAAAI,KAAE,QAAE,MAAE,cAAc,WAAEA,IAAE,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC;AAAA,cAAC;AAAC,qBAAOA;AAAA,YAAC,CAAE,GAAE,IAAE,EAAE,GAAE,KAAK,mBAAmB,GAAE,IAAE,EAAE,aAAY,IAAE,EAAE,sBAAqB,IAAE,KAAK,cAAc,QAAQ,GAAE,YAAY,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,OAAM,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,QAAO,CAAC,GAAE,KAAK,cAAa,KAAK,wBAAwB,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAO,OAAK,IAAE,EAAE,KAAK,GAAG,UAAQ,QAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,KAAG,CAAC,GAAE,EAAE,GAAE,KAAK,UAAS,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,SAASJ,IAAEC,IAAE;AAAC,yBAASD,OAAIA,KAAE,CAAC;AAAG,kBAAII,IAAEC,MAAGD,KAAEH,IAAE,CAAC,EAAE,OAAO,MAAM,CAAC,GAAEG,EAAC;AAAG,qBAAOJ,GAAE,QAAS,SAASA,IAAE;AAAC,oBAAIC,KAAED,GAAE;AAAa,gBAAAC,GAAE,kBAAkB,QAAS,SAASD,IAAE;AAAC,sBAAIC,KAAE,EAAEI,IAAE,CAACL,GAAE,GAAEA,GAAE,CAAC,CAAC,GAAEI,KAAEH,GAAE,CAAC,GAAEc,KAAEd,GAAE,CAAC;AAAE,kBAAAD,GAAE,IAAEI,IAAEJ,GAAE,IAAEe;AAAA,gBAAC,CAAE;AAAE,oBAAIX,KAAEH,GAAE,qBAAoB,IAAE,OAAO,WAAUC,KAAE,OAAO,WAAUI,KAAE,OAAO,WAAUH,KAAE,OAAO;AAAU,iBAAC,CAACC,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,OAAKA,GAAE,OAAMA,GAAE,IAAI,GAAE,CAACA,GAAE,OAAKA,GAAE,OAAMA,GAAE,OAAKA,GAAE,MAAM,GAAE,CAACA,GAAE,MAAKA,GAAE,OAAKA,GAAE,MAAM,CAAC,EAAE,QAAS,SAASJ,IAAE;AAAC,sBAAIC,KAAE,EAAEI,IAAEL,EAAC,GAAEI,KAAEH,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC;AAAE,sBAAE,KAAK,IAAI,GAAEG,EAAC,GAAEE,KAAE,KAAK,IAAIA,IAAEF,EAAC,GAAEF,KAAE,KAAK,IAAIA,IAAE,CAAC,GAAEC,KAAE,KAAK,IAAIA,IAAE,CAAC;AAAA,gBAAC,CAAE,GAAEF,GAAE,sBAAoB,EAAC,MAAK,GAAE,MAAKK,IAAE,MAAKJ,IAAE,MAAKC,IAAE,OAAMG,KAAE,GAAE,QAAOH,KAAED,GAAC;AAAA,cAAC,CAAE,GAAEF;AAAA,YAAC,EAAE,GAAE,CAAC,GAAE,QAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,eAAO,IAAE,EAAEA,EAAC,GAAE,IAAE,CAAC,CAAC,KAAG,EAAE,gBAAe,CAAC,GAAE,KAAK,YAAYA,IAAE,CAAC,EAAE,KAAM,SAASA,IAAE;AAAC,iBAAOA,GAAE,IAAK,SAASA,IAAE;AAAC,qBAAQC,KAAED,GAAE,aAAa,kBAAkB,IAAK,SAASA,IAAEC,IAAE;AAAC,qBAAO,EAAE,EAAE,CAAC,GAAED,EAAC,GAAE,EAAC,GAAEA,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,QAAO,MAAK,EAAEC,EAAC,EAAC,CAAC;AAAA,YAAC,CAAE,GAAEI,KAAEL,GAAE,aAAa,qBAAoBe,KAAE,GAAE,IAAE,CAAC,SAAQ,QAAO,MAAM,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,cAAAV,GAAE,EAAEU,EAAC,CAAC,KAAG,EAAE;AAAA,YAAK;AAAC,qBAAQ,IAAE,GAAE,IAAE,CAAC,UAAS,QAAO,MAAM,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,cAAAV,GAAE,EAAE,CAAC,CAAC,KAAG,EAAE;AAAA,YAAM;AAAC,mBAAM,EAAC,WAAUJ,IAAE,KAAII,GAAC;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEL;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,CAAC;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,oBAAMC,GAAE,cAAYA,GAAE,YAAU,EAAE,YAAW,QAAMA,GAAE,aAAWA,GAAE,WAAS,EAAE,WAAU,QAAMA,GAAE,qBAAmB,WAASA,GAAE,YAAUA,GAAE,mBAAiB,iEAA+DA,GAAE,mBAAiB;AAAiE,mBAAOA;AAAA,UAAC,EAAED,EAAC,GAAE,IAAE,YAAU,OAAO,EAAE,oBAAkB,EAAE,iBAAiB,QAAQ,mBAAmB,IAAE,IAAG,CAAC,GAAE,eAAE,EAAE,kBAAiB,EAAC,WAAU,EAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,IAAI,EAAE,EAAE,WAAU,GAAE,EAAE,QAAQ,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,UAAGA,OAAI,EAAE,uBAAsB;AAAC,YAAG,IAAE,QAAO,SAAO,IAAE,IAAG;AAAC,cAAG,WAAS,EAAE,QAAQ,QAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAE,cAAG,gBAAc,EAAE,QAAQ,QAAM,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,cAAE,EAAE;AAAA,QAAO;AAAC,cAAM,IAAI,MAAM,oDAAkD,2BAA2B,OAAO,CAAC,CAAC;AAAA,MAAC;AAAC,YAAM,IAAI,MAAM,GAAG,OAAOA,IAAE,iCAAiC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAE,wBAAsB;AAAuB,EAAE,MAAI,IAAE,CAAC,EAAE;", "names": ["e", "t", "r", "s", "n", "i", "a", "h", "u", "c", "p", "b", "T", "C", "O", "o"]}