{"version": 3, "sources": ["../../lodash/_baseXor.js", "../../lodash/xorBy.js"], "sourcesContent": ["var baseDifference = require('./_baseDifference'),\n    baseFlatten = require('./_baseFlatten'),\n    baseUniq = require('./_baseUniq');\n\n/**\n * The base implementation of methods like `_.xor`, without support for\n * iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of values.\n */\nfunction baseXor(arrays, iteratee, comparator) {\n  var length = arrays.length;\n  if (length < 2) {\n    return length ? baseUniq(arrays[0]) : [];\n  }\n  var index = -1,\n      result = Array(length);\n\n  while (++index < length) {\n    var array = arrays[index],\n        othIndex = -1;\n\n    while (++othIndex < length) {\n      if (othIndex != index) {\n        result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);\n      }\n    }\n  }\n  return baseUniq(baseFlatten(result, 1), iteratee, comparator);\n}\n\nmodule.exports = baseXor;\n", "var arrayFilter = require('./_arrayFilter'),\n    baseIteratee = require('./_baseIteratee'),\n    baseRest = require('./_baseRest'),\n    baseXor = require('./_baseXor'),\n    isArrayLikeObject = require('./isArrayLikeObject'),\n    last = require('./last');\n\n/**\n * This method is like `_.xor` except that it accepts `iteratee` which is\n * invoked for each element of each `arrays` to generate the criterion by\n * which by which they're compared. The order of result values is determined\n * by the order they occur in the arrays. The iteratee is invoked with one\n * argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.xorBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [1.2, 3.4]\n *\n * // The `_.property` iteratee shorthand.\n * _.xorBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 2 }]\n */\nvar xorBy = baseRest(function(arrays) {\n  var iteratee = last(arrays);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return baseXor(arrayFilter(arrays, isArrayLikeObject), baseIteratee(iteratee, 2));\n});\n\nmodule.exports = xorBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,cAAc;AADlB,QAEI,WAAW;AAYf,aAAS,QAAQ,QAAQ,UAAU,YAAY;AAC7C,UAAI,SAAS,OAAO;AACpB,UAAI,SAAS,GAAG;AACd,eAAO,SAAS,SAAS,OAAO,CAAC,CAAC,IAAI,CAAC;AAAA,MACzC;AACA,UAAI,QAAQ,IACR,SAAS,MAAM,MAAM;AAEzB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,OAAO,KAAK,GACpB,WAAW;AAEf,eAAO,EAAE,WAAW,QAAQ;AAC1B,cAAI,YAAY,OAAO;AACrB,mBAAO,KAAK,IAAI,eAAe,OAAO,KAAK,KAAK,OAAO,OAAO,QAAQ,GAAG,UAAU,UAAU;AAAA,UAC/F;AAAA,QACF;AAAA,MACF;AACA,aAAO,SAAS,YAAY,QAAQ,CAAC,GAAG,UAAU,UAAU;AAAA,IAC9D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnCjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,eAAe;AADnB,QAEI,WAAW;AAFf,QAGI,UAAU;AAHd,QAII,oBAAoB;AAJxB,QAKI,OAAO;AAyBX,QAAI,QAAQ,SAAS,SAAS,QAAQ;AACpC,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,kBAAkB,QAAQ,GAAG;AAC/B,mBAAW;AAAA,MACb;AACA,aAAO,QAAQ,YAAY,QAAQ,iBAAiB,GAAG,aAAa,UAAU,CAAC,CAAC;AAAA,IAClF,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}