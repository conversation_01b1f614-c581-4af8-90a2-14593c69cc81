import {
  require_baseDifference
} from "./chunk-3SPSYUSI.js";
import {
  require_baseFlatten
} from "./chunk-ICXN6OJ6.js";
import "./chunk-TYXAQETL.js";
import {
  require_isArrayLikeObject
} from "./chunk-DOSSVMDF.js";
import {
  require_baseRest
} from "./chunk-SMZ2DSXW.js";
import "./chunk-TLVLGZ6X.js";
import "./chunk-JIR7Y6MV.js";
import "./chunk-3HWTEJRL.js";
import "./chunk-M2WBRPB3.js";
import "./chunk-CWSHORJK.js";
import "./chunk-U4XCUM7X.js";
import "./chunk-5PX5EJ7R.js";
import "./chunk-7I5PEIZF.js";
import "./chunk-MIX47OBP.js";
import "./chunk-64Z5HK43.js";
import "./chunk-IW7F5ZXM.js";
import "./chunk-F753BK7A.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/difference.js
var require_difference = __commonJS({
  "node_modules/lodash/difference.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseFlatten = require_baseFlatten();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var difference = baseRest(function(array, values) {
      return isArrayLikeObject(array) ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true)) : [];
    });
    module.exports = difference;
  }
});
export default require_difference();
//# sourceMappingURL=lodash_difference.js.map
