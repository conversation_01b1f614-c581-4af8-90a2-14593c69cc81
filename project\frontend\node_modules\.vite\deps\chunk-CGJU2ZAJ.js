import {
  Vue,
  getCurrentInstance,
  init_vue_runtime_esm
} from "./chunk-YO42JT3F.js";

// node_modules/vue-demi/lib/index.mjs
init_vue_runtime_esm();
init_vue_runtime_esm();
init_vue_runtime_esm();
var isVue2 = true;
var isVue3 = false;
var warn = Vue.util.warn;
function createMockComponent(name) {
  return {
    setup() {
      throw new Error("[vue-demi] " + name + " is not supported in Vue 2. It's provided to avoid compiler errors.");
    }
  };
}
var Fragment = createMockComponent("Fragment");
var Transition = createMockComponent("Transition");
var TransitionGroup = createMockComponent("TransitionGroup");
var Teleport = createMockComponent("Teleport");
var Suspense = createMockComponent("Suspense");
var KeepAlive = createMockComponent("KeepAlive");
function hasInjectionContext() {
  return !!getCurrentInstance();
}

export {
  isVue2,
  isVue3,
  Fragment,
  TransitionGroup,
  hasInjectionContext
};
//# sourceMappingURL=chunk-CGJU2ZAJ.js.map
