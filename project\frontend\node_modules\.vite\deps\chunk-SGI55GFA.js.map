{"version": 3, "sources": ["../../@gtm-support/core/src/assert-is-gtm-id.ts", "../../@gtm-support/core/src/utils.ts", "../../@gtm-support/core/src/gtm-support.ts", "../../@gtm-support/core/src/index.ts", "../../@gtm-support/vue2-gtm/dist/index.js"], "sourcesContent": ["/** GTM Container ID pattern. */\nexport const GTM_ID_PATTERN: RegExp = /^GTM-[0-9A-Z]+$/;\n\n/**\n * Assert that the given id is a valid GTM Container ID.\n *\n * Tested against pattern: `/^GTM-[0-9A-Z]+$/`.\n *\n * @param id A GTM Container ID.\n */\nexport function assertIsGtmId(id: string): asserts id {\n  if (typeof id !== 'string' || !GTM_ID_PATTERN.test(id)) {\n    throw new Error(`GTM-ID '${id}' is not valid`);\n  }\n}\n", "import type { GtmQueryParams } from './gtm-container';\n\n/**\n *  OnReadyOptions.\n */\nexport interface OnReadyOptions {\n  /**\n   * The GTM id.\n   */\n  id: string;\n  /**\n   * The script element.\n   */\n  script: HTMLScriptElement;\n}\n\n/**\n * Options for `loadScript` function.\n */\nexport interface LoadScriptOptions {\n  /**\n   * Add url query string when load gtm.js with GTM ID.\n   */\n  queryParams?: GtmQueryParams;\n  /**\n   * Script can be set to `defer` to speed up page load at the cost of less accurate results (in case visitor leaves before script is loaded, which is unlikely but possible).\n   */\n  defer: boolean;\n  /**\n   * Will add `async` and `defer` to the script tag to not block requests for old browsers that do not support `async`.\n   */\n  compatibility: boolean;\n  /**\n   * Will add `nonce` to the script tag.\n   *\n   * @see [Using Google Tag Manager with a Content Security Policy](https://developers.google.com/tag-manager/web/csp)\n   */\n  nonce?: string;\n  /**\n   * Where to append the script element.\n   *\n   * @default document.body\n   */\n  parentElement?: HTMLElement;\n  /**\n   * The URL of the script; useful for server-side GTM.\n   *\n   * @default https://www.googletagmanager.com/gtm.js\n   */\n  source?: string;\n  /**\n   * Will be called when the script is loaded.\n   *\n   * @param options Object containing container `id` and `script` element.\n   */\n  onReady?: (options: OnReadyOptions) => void;\n}\n\n/**\n * Load GTM script tag.\n *\n * @param id GTM ID.\n * @param config The config object.\n * @returns The script element.\n */\nexport function loadScript(\n  id: string,\n  config: LoadScriptOptions,\n): HTMLScriptElement {\n  const doc: Document = document;\n  const script: HTMLScriptElement = doc.createElement('script');\n\n  const scriptLoadListener: (event: Event) => void = (event) => {\n    config.onReady?.({ id, script });\n    script.removeEventListener('load', scriptLoadListener);\n  };\n\n  script.addEventListener('load', scriptLoadListener);\n\n  window.dataLayer = window.dataLayer ?? [];\n\n  window.dataLayer?.push({\n    event: 'gtm.js',\n    'gtm.start': new Date().getTime(),\n  });\n\n  if (!id) {\n    return script;\n  }\n\n  script.async = !config.defer;\n  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n  script.defer = Boolean(config.defer || config.compatibility);\n\n  if (config.nonce) {\n    script.nonce = config.nonce;\n  }\n\n  const queryString: URLSearchParams = new URLSearchParams({\n    id,\n    ...(config.queryParams ?? {}),\n  });\n\n  const source: string =\n    config.source ?? 'https://www.googletagmanager.com/gtm.js';\n\n  script.src = `${source}?${queryString}`;\n\n  const parentElement: HTMLElement = config.parentElement ?? doc.body;\n\n  if (typeof parentElement?.appendChild !== 'function') {\n    throw new Error('parentElement must be a DOM element');\n  }\n\n  parentElement.appendChild(script);\n\n  return script;\n}\n\n/**\n * Check if GTM script is in the document.\n *\n * @param source The URL of the script, if it differs from the default. Default: 'https://www.googletagmanager.com/gtm.js'.\n * @returns `true` if in the `document` is a `script` with `src` containing `'https://www.googletagmanager.com/gtm.js'` (or `source` if specified), otherwise `false`.\n */\nexport function hasScript(\n  source: string = 'https://www.googletagmanager.com/gtm.js',\n): boolean {\n  return Array.from(document.getElementsByTagName('script')).some((script) =>\n    script.src.includes(source),\n  );\n}\n", "import { assertIsGtmId } from './assert-is-gtm-id';\nimport type { DataLayerObject } from './data-layer-object';\nimport type { GtmIdContainer } from './gtm-container';\nimport type { GtmSupportOptions } from './options';\nimport type { LoadScriptOptions } from './utils';\nimport { hasScript, loadScript } from './utils';\n\n/**\n * Object definition for a track event.\n */\nexport interface TrackEventOptions {\n  [key: string]: any;\n  event?: string;\n  category?: any;\n  action?: any;\n  label?: any;\n  value?: any;\n  noninteraction?: boolean;\n}\n\n/**\n * The GTM Support main class.\n */\nexport class GtmSupport {\n  /** GTM Container ID. */\n  public readonly id: string | string[] | GtmIdContainer[];\n  /** GTM Support Options. */\n  public readonly options: Omit<GtmSupportOptions, 'id'>;\n\n  public readonly scriptElements: HTMLScriptElement[] = [];\n\n  /**\n   * Constructs a new `GtmSupport` instance.\n   *\n   * @param options Options.\n   */\n  public constructor(options: GtmSupportOptions) {\n    if (Array.isArray(options.id)) {\n      for (const idOrObject of options.id) {\n        if (typeof idOrObject === 'string') {\n          assertIsGtmId(idOrObject);\n        } else {\n          assertIsGtmId(idOrObject.id);\n        }\n      }\n    } else {\n      assertIsGtmId(options.id);\n    }\n\n    this.id = options.id;\n    this.options = {\n      enabled: true,\n      debug: false,\n      loadScript: true,\n      defer: false,\n      compatibility: false,\n      ...options,\n    };\n\n    // @ts-expect-error: Just remove the id from options\n    delete this.options.id;\n  }\n\n  /**\n   * Whether the script is running in a browser or not.\n   *\n   * You can override this function if you need to.\n   *\n   * @returns `true` if the script runs in browser context.\n   */\n  public isInBrowserContext: () => boolean = () =>\n    typeof window !== 'undefined';\n\n  /**\n   * Check if plugin is enabled.\n   *\n   * @returns `true` if the plugin is enabled, otherwise `false`.\n   */\n  public enabled(): boolean {\n    return this.options.enabled ?? true;\n  }\n\n  /**\n   * Enable or disable plugin.\n   *\n   * When enabling with this function, the script will be attached to the `document` if:\n   *\n   * - the script runs in browser context\n   * - the `document` doesn't have the script already attached\n   * - the `loadScript` option is set to `true`\n   *\n   * @param enabled `true` to enable, `false` to disable. Default: `true`.\n   * @param source The URL of the script, if it differs from the default. Default: 'https://www.googletagmanager.com/gtm.js'.\n   */\n  public enable(enabled: boolean = true, source?: string): void {\n    this.options.enabled = enabled;\n\n    if (\n      this.isInBrowserContext() &&\n      enabled &&\n      !hasScript(source) &&\n      this.options.loadScript\n    ) {\n      if (Array.isArray(this.id)) {\n        this.id.forEach((id: string | GtmIdContainer) => {\n          let scriptElement: HTMLScriptElement;\n          if (typeof id === 'string') {\n            scriptElement = loadScript(id, {\n              ...this.options,\n            } as LoadScriptOptions);\n          } else {\n            scriptElement = loadScript(id.id, {\n              ...this.options,\n              queryParams: id.queryParams,\n            } as LoadScriptOptions);\n          }\n          this.scriptElements.push(scriptElement);\n        });\n      } else {\n        const scriptElement: HTMLScriptElement = loadScript(this.id, {\n          ...this.options,\n        } as LoadScriptOptions);\n        this.scriptElements.push(scriptElement);\n      }\n    }\n  }\n\n  /**\n   * Check if plugin is in debug mode.\n   *\n   * @returns `true` if the plugin is in debug mode, otherwise `false`.\n   */\n  public debugEnabled(): boolean {\n    return this.options.debug ?? false;\n  }\n\n  /**\n   * Enable or disable debug mode.\n   *\n   * @param enable `true` to enable, `false` to disable.\n   */\n  public debug(enable: boolean): void {\n    this.options.debug = enable;\n  }\n\n  /**\n   * Returns the `window.dataLayer` array if the script is running in browser context and the plugin is enabled,\n   * otherwise `false`.\n   *\n   * @returns The `window.dataLayer` if script is running in browser context and plugin is enabled, otherwise `false`.\n   */\n  public dataLayer(): DataLayerObject[] | false {\n    if (this.isInBrowserContext() && this.options.enabled) {\n      return (window.dataLayer = window.dataLayer ?? []);\n    }\n    return false;\n  }\n\n  /**\n   * Track a view event with `event: \"content-view\"`.\n   *\n   * The event will only be send if the script runs in browser context and the if plugin is enabled.\n   *\n   * If debug mode is enabled, a \"Dispatching TrackView\" is logged,\n   * regardless of whether the plugin is enabled or the plugin is being executed in browser context.\n   *\n   * @param screenName Name of the screen passed as `\"content-view-name\"`.\n   * @param path Path passed as `\"content-name\"`.\n   * @param additionalEventData Additional data for the event object. `event`, `\"content-name\"` and `\"content-view-name\"` will always be overridden.\n   */\n  public trackView(\n    screenName: string,\n    path: string,\n    additionalEventData: Record<string, any> = {},\n  ): void {\n    const trigger: boolean =\n      this.isInBrowserContext() && (this.options.enabled ?? false);\n    if (this.options.debug) {\n      console.log(\n        `[GTM-Support${trigger ? '' : '(disabled)'}]: Dispatching TrackView`,\n        { screenName, path },\n      );\n    }\n\n    if (trigger) {\n      const dataLayer: DataLayerObject[] = (window.dataLayer =\n        window.dataLayer ?? []);\n      dataLayer.push({\n        ...additionalEventData,\n        event: 'content-view',\n        'content-name': path,\n        'content-view-name': screenName,\n      });\n    }\n  }\n\n  /**\n   * Track an event.\n   *\n   * The event will only be send if the script runs in browser context and the if plugin is enabled.\n   *\n   * If debug mode is enabled, a \"Dispatching event\" is logged,\n   * regardless of whether the plugin is enabled or the plugin is being executed in browser context.\n   *\n   * @param param0 Object that will be used for configuring the event object passed to GTM.\n   * @param param0.event `event`, default to `\"interaction\"` when pushed to `window.dataLayer`.\n   * @param param0.category Optional `category`, passed as `target`.\n   * @param param0.action Optional `action`, passed as `action`.\n   * @param param0.label Optional `label`, passed as `\"target-properties\"`.\n   * @param param0.value Optional `value`, passed as `value`.\n   * @param param0.noninteraction Optional `noninteraction`, passed as `\"interaction-type\"`.\n   */\n  public trackEvent({\n    event,\n    category = null,\n    action = null,\n    label = null,\n    value = null,\n    noninteraction = false,\n    ...rest\n  }: TrackEventOptions = {}): void {\n    const trigger: boolean =\n      this.isInBrowserContext() && (this.options.enabled ?? false);\n    if (this.options.debug) {\n      console.log(\n        `[GTM-Support${trigger ? '' : '(disabled)'}]: Dispatching event`,\n        {\n          event,\n          category,\n          action,\n          label,\n          value,\n          ...rest,\n        },\n      );\n    }\n\n    if (trigger) {\n      const dataLayer: DataLayerObject[] = (window.dataLayer =\n        window.dataLayer ?? []);\n      dataLayer.push({\n        event: event ?? 'interaction',\n        target: category,\n        action: action,\n        'target-properties': label,\n        value: value,\n        'interaction-type': noninteraction,\n        ...rest,\n      });\n    }\n  }\n}\n", "import type { DataLayerObject } from './data-layer-object';\n\ndeclare global {\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  interface Window {\n    /**\n     * `dataLayer` used by GTM.\n     *\n     * @see [developers.google.com/tag-manager/devguide](https://developers.google.com/tag-manager/devguide)\n     */\n    dataLayer?: DataLayerObject[];\n  }\n}\n\nexport { assertIsGtmId, GTM_ID_PATTERN } from './assert-is-gtm-id';\nexport type { DataLayerObject } from './data-layer-object';\nexport type { GtmIdContainer, GtmQueryParams } from './gtm-container';\nexport { GtmSupport } from './gtm-support';\nexport type { TrackEventOptions } from './gtm-support';\nexport type { GtmSupportOptions } from './options';\nexport { hasScript, loadScript } from './utils';\nexport type { LoadScriptOptions, OnReadyOptions } from './utils';\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useGtm = exports.GtmPlugin = exports.loadScript = exports.hasScript = exports.GtmSupport = exports.assertIsGtmId = void 0;\nvar core_1 = require(\"@gtm-support/core\");\nObject.defineProperty(exports, \"GtmPlugin\", { enumerable: true, get: function () { return core_1.GtmSupport; } });\nvar gtmPlugin;\n/**\n * Installation procedure.\n *\n * @param Vue The Vue instance.\n * @param options Configuration options.\n */\nfunction install(Vue, options) {\n    if (options === void 0) { options = { id: '' }; }\n    // Apply default configuration\n    options = __assign({ trackOnNextTick: false }, options);\n    // Add to vue prototype and also from globals\n    gtmPlugin = new core_1.GtmSupport(options);\n    Vue.prototype.$gtm = Vue.gtm = gtmPlugin;\n    // Check if plugin is running in a real browser or e.g. in SSG mode\n    if (gtmPlugin.isInBrowserContext()) {\n        // Handle vue-router if defined\n        if (options.vueRouter) {\n            initVueRouterGuard(Vue, options.vueRouter, options.ignoredViews, options.trackOnNextTick);\n        }\n        // Load GTM script when enabled\n        if (gtmPlugin.options.enabled && gtmPlugin.options.loadScript) {\n            if (Array.isArray(options.id)) {\n                options.id.forEach(function (id) {\n                    if (typeof id === 'string') {\n                        (0, core_1.loadScript)(id, options);\n                    }\n                    else {\n                        var newConf = __assign({}, options);\n                        if (id.queryParams != null) {\n                            newConf.queryParams = __assign(__assign({}, newConf.queryParams), id.queryParams);\n                        }\n                        (0, core_1.loadScript)(id.id, newConf);\n                    }\n                });\n            }\n            else {\n                (0, core_1.loadScript)(options.id, options);\n            }\n        }\n    }\n}\n/**\n * Initialize the router guard.\n *\n * @param Vue The Vue instance.\n * @param vueRouter The Vue router instance to attach the guard.\n * @param ignoredViews An array of route name that will be ignored.\n * @param trackOnNextTick Whether or not to call `trackView` in `Vue.nextTick`.\n * @param deriveAdditionalEventData Callback to derive additional event data.\n */\nfunction initVueRouterGuard(Vue, vueRouter, ignoredViews, trackOnNextTick, deriveAdditionalEventData) {\n    var _this = this;\n    if (ignoredViews === void 0) { ignoredViews = []; }\n    if (deriveAdditionalEventData === void 0) { deriveAdditionalEventData = function () { return ({}); }; }\n    if (!vueRouter) {\n        console.warn(\"[VueGtm]: You tried to register 'vueRouter' for vue-gtm, but 'vue-router' was not found.\");\n        return;\n    }\n    vueRouter.afterEach(function (to, from) { return __awaiter(_this, void 0, void 0, function () {\n        var name, additionalEventData, _a, baseUrl, fullUrl;\n        var _b, _c;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    // Ignore some routes\n                    if (typeof to.name !== 'string' ||\n                        (Array.isArray(ignoredViews) && ignoredViews.includes(to.name)) ||\n                        (typeof ignoredViews === 'function' && ignoredViews(to, from))) {\n                        return [2 /*return*/];\n                    }\n                    name = to.meta && typeof to.meta.gtm === 'string' && !!to.meta.gtm\n                        ? to.meta.gtm\n                        : to.name;\n                    _a = [{}];\n                    return [4 /*yield*/, deriveAdditionalEventData(to, from)];\n                case 1:\n                    additionalEventData = __assign.apply(void 0, [__assign.apply(void 0, _a.concat([(_d.sent())])), (_b = to.meta) === null || _b === void 0 ? void 0 : _b.gtmAdditionalEventData]);\n                    baseUrl = (_c = vueRouter.options.base) !== null && _c !== void 0 ? _c : '';\n                    fullUrl = baseUrl;\n                    if (!fullUrl.endsWith('/')) {\n                        fullUrl += '/';\n                    }\n                    fullUrl += to.fullPath.startsWith('/')\n                        ? to.fullPath.substr(1)\n                        : to.fullPath;\n                    if (trackOnNextTick) {\n                        Vue.nextTick(function () {\n                            gtmPlugin === null || gtmPlugin === void 0 ? void 0 : gtmPlugin.trackView(name, fullUrl, additionalEventData);\n                        });\n                    }\n                    else {\n                        gtmPlugin === null || gtmPlugin === void 0 ? void 0 : gtmPlugin.trackView(name, fullUrl, additionalEventData);\n                    }\n                    return [2 /*return*/];\n            }\n        });\n    }); });\n}\nvar _default = { install: install };\nvar core_2 = require(\"@gtm-support/core\");\nObject.defineProperty(exports, \"assertIsGtmId\", { enumerable: true, get: function () { return core_2.assertIsGtmId; } });\nObject.defineProperty(exports, \"GtmSupport\", { enumerable: true, get: function () { return core_2.GtmSupport; } });\nObject.defineProperty(exports, \"hasScript\", { enumerable: true, get: function () { return core_2.hasScript; } });\nObject.defineProperty(exports, \"loadScript\", { enumerable: true, get: function () { return core_2.loadScript; } });\nexports.default = _default;\n/**\n * Returns GTM plugin instance to be used via Composition API inside setup method.\n *\n * @returns The Vue GTM instance if the it was installed, otherwise `undefined`.\n */\nfunction useGtm() {\n    return gtmPlugin;\n}\nexports.useGtm = useGtm;\n"], "mappings": ";;;;;;;;;;AACa,YAAA,iBAAyB;AAStC,aAAgB,cAAc,IAAU;AACtC,UAAI,OAAO,OAAO,YAAY,CAAC,QAAA,eAAe,KAAK,EAAE,GAAG;AACtD,cAAM,IAAI,MAAM,WAAW,EAAE,gBAAgB;;IAEjD;AAJA,YAAA,gBAAA;;;;;;;;;;ACuDA,aAAgB,WACd,IACA,QAAyB;;AAEzB,YAAM,MAAgB;AACtB,YAAM,SAA4B,IAAI,cAAc,QAAQ;AAE5D,YAAM,qBAA6C,CAAC,UAAS;;AAC3D,SAAAA,MAAA,OAAO,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,QAAG,EAAE,IAAI,OAAM,CAAE;AAC/B,eAAO,oBAAoB,QAAQ,kBAAkB;MACvD;AAEA,aAAO,iBAAiB,QAAQ,kBAAkB;AAElD,aAAO,aAAY,KAAA,OAAO,eAAS,QAAA,OAAA,SAAA,KAAI,CAAA;AAEvC,OAAA,KAAA,OAAO,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK;QACrB,OAAO;QACP,cAAa,oBAAI,KAAI,GAAG,QAAO;OAChC;AAED,UAAI,CAAC,IAAI;AACP,eAAO;;AAGT,aAAO,QAAQ,CAAC,OAAO;AAEvB,aAAO,QAAQ,QAAQ,OAAO,SAAS,OAAO,aAAa;AAE3D,UAAI,OAAO,OAAO;AAChB,eAAO,QAAQ,OAAO;;AAGxB,YAAM,cAA+B,IAAI,gBAAgB;QACvD;QACA,IAAI,KAAA,OAAO,iBAAW,QAAA,OAAA,SAAA,KAAI,CAAA;OAC3B;AAED,YAAM,UACJ,KAAA,OAAO,YAAM,QAAA,OAAA,SAAA,KAAI;AAEnB,aAAO,MAAM,GAAG,MAAM,IAAI,WAAW;AAErC,YAAM,iBAA6B,KAAA,OAAO,mBAAa,QAAA,OAAA,SAAA,KAAI,IAAI;AAE/D,UAAI,QAAO,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,iBAAgB,YAAY;AACpD,cAAM,IAAI,MAAM,qCAAqC;;AAGvD,oBAAc,YAAY,MAAM;AAEhC,aAAO;IACT;AApDA,YAAA,aAAA;AA4DA,aAAgB,UACd,SAAiB,2CAAyC;AAE1D,aAAO,MAAM,KAAK,SAAS,qBAAqB,QAAQ,CAAC,EAAE,KAAK,CAAC,WAC/D,OAAO,IAAI,SAAS,MAAM,CAAC;IAE/B;AANA,YAAA,YAAA;;;;;;;;;;AC7HA,QAAA,qBAAA;AAKA,QAAA,UAAA;AAkBA,QAAa,aAAb,MAAuB;;;;;;MAarB,YAAmB,SAA0B;AAP7B,aAAA,iBAAsC,CAAA;AAyC/C,aAAA,qBAAoC,MACzC,OAAO,WAAW;AAlClB,YAAI,MAAM,QAAQ,QAAQ,EAAE,GAAG;AAC7B,qBAAW,cAAc,QAAQ,IAAI;AACnC,gBAAI,OAAO,eAAe,UAAU;AAClC,eAAA,GAAA,mBAAA,eAAc,UAAU;mBACnB;AACL,eAAA,GAAA,mBAAA,eAAc,WAAW,EAAE;;;eAG1B;AACL,WAAA,GAAA,mBAAA,eAAc,QAAQ,EAAE;;AAG1B,aAAK,KAAK,QAAQ;AAClB,aAAK,UAAU;UACb,SAAS;UACT,OAAO;UACP,YAAY;UACZ,OAAO;UACP,eAAe;UACf,GAAG;;AAIL,eAAO,KAAK,QAAQ;MACtB;;;;;;MAiBO,UAAO;;AACZ,gBAAO,KAAA,KAAK,QAAQ,aAAO,QAAA,OAAA,SAAA,KAAI;MACjC;;;;;;;;;;;;;MAcO,OAAO,UAAmB,MAAM,QAAe;AACpD,aAAK,QAAQ,UAAU;AAEvB,YACE,KAAK,mBAAkB,KACvB,WACA,EAAC,GAAA,QAAA,WAAU,MAAM,KACjB,KAAK,QAAQ,YACb;AACA,cAAI,MAAM,QAAQ,KAAK,EAAE,GAAG;AAC1B,iBAAK,GAAG,QAAQ,CAAC,OAA+B;AAC9C,kBAAI;AACJ,kBAAI,OAAO,OAAO,UAAU;AAC1B,iCAAgB,GAAA,QAAA,YAAW,IAAI;kBAC7B,GAAG,KAAK;iBACY;qBACjB;AACL,iCAAgB,GAAA,QAAA,YAAW,GAAG,IAAI;kBAChC,GAAG,KAAK;kBACR,aAAa,GAAG;iBACI;;AAExB,mBAAK,eAAe,KAAK,aAAa;YACxC,CAAC;iBACI;AACL,kBAAM,iBAAmC,GAAA,QAAA,YAAW,KAAK,IAAI;cAC3D,GAAG,KAAK;aACY;AACtB,iBAAK,eAAe,KAAK,aAAa;;;MAG5C;;;;;;MAOO,eAAY;;AACjB,gBAAO,KAAA,KAAK,QAAQ,WAAK,QAAA,OAAA,SAAA,KAAI;MAC/B;;;;;;MAOO,MAAM,QAAe;AAC1B,aAAK,QAAQ,QAAQ;MACvB;;;;;;;MAQO,YAAS;;AACd,YAAI,KAAK,mBAAkB,KAAM,KAAK,QAAQ,SAAS;AACrD,iBAAQ,OAAO,aAAY,KAAA,OAAO,eAAS,QAAA,OAAA,SAAA,KAAI,CAAA;;AAEjD,eAAO;MACT;;;;;;;;;;;;;MAcO,UACL,YACA,MACA,sBAA2C,CAAA,GAAE;;AAE7C,cAAM,UACJ,KAAK,mBAAkB,OAAO,KAAA,KAAK,QAAQ,aAAO,QAAA,OAAA,SAAA,KAAI;AACxD,YAAI,KAAK,QAAQ,OAAO;AACtB,kBAAQ,IACN,eAAe,UAAU,KAAK,YAAY,4BAC1C,EAAE,YAAY,KAAI,CAAE;;AAIxB,YAAI,SAAS;AACX,gBAAM,YAAgC,OAAO,aAC3C,KAAA,OAAO,eAAS,QAAA,OAAA,SAAA,KAAI,CAAA;AACtB,oBAAU,KAAK;YACb,GAAG;YACH,OAAO;YACP,gBAAgB;YAChB,qBAAqB;WACtB;;MAEL;;;;;;;;;;;;;;;;;MAkBO,WAAW,EAChB,OACA,WAAW,MACX,SAAS,MACT,QAAQ,MACR,QAAQ,MACR,iBAAiB,OACjB,GAAG,KAAI,IACc,CAAA,GAAE;;AACvB,cAAM,UACJ,KAAK,mBAAkB,OAAO,KAAA,KAAK,QAAQ,aAAO,QAAA,OAAA,SAAA,KAAI;AACxD,YAAI,KAAK,QAAQ,OAAO;AACtB,kBAAQ,IACN,eAAe,UAAU,KAAK,YAAY,wBAC1C;YACE;YACA;YACA;YACA;YACA;YACA,GAAG;WACJ;;AAIL,YAAI,SAAS;AACX,gBAAM,YAAgC,OAAO,aAC3C,KAAA,OAAO,eAAS,QAAA,OAAA,SAAA,KAAI,CAAA;AACtB,oBAAU,KAAK;YACb,OAAO,UAAK,QAAL,UAAK,SAAL,QAAS;YAChB,QAAQ;YACR;YACA,qBAAqB;YACrB;YACA,oBAAoB;YACpB,GAAG;WACJ;;MAEL;;AAnOF,YAAA,aAAA;;;;;;;;;;ACTA,QAAA,qBAAA;AAAS,WAAA,eAAA,SAAA,iBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,mBAAA;IAAa,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,kBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,mBAAA;IAAc,EAAA,CAAA;AAGtC,QAAA,gBAAA;AAAS,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,cAAA;IAAU,EAAA,CAAA;AAGnB,QAAA,UAAA;AAAS,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,QAAA;IAAS,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,QAAA;IAAU,EAAA,CAAA;;;;;ACpB9B;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,YAAa,WAAQ,QAAK,aAAc,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,eAAS,MAAM,OAAO;AAAE,eAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,kBAAQ,KAAK;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3G,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC1F,iBAAS,SAAS,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC7F,iBAAS,KAAK,QAAQ;AAAE,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QAAG;AAC7G,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACA,QAAI,cAAe,WAAQ,QAAK,eAAgB,SAAU,SAAS,MAAM;AACrE,UAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,YAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,eAAO,EAAE,CAAC;AAAA,MAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,aAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,eAAO;AAAA,MAAM,IAAI;AACvJ,eAAS,KAAK,GAAG;AAAE,eAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG;AAAA,MAAG;AACjE,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO,EAAG,KAAI;AACV,cAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,cAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,kBAAQ,GAAG,CAAC,GAAG;AAAA,YACX,KAAK;AAAA,YAAG,KAAK;AAAG,kBAAI;AAAI;AAAA,YACxB,KAAK;AAAG,gBAAE;AAAS,qBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,YACtD,KAAK;AAAG,gBAAE;AAAS,kBAAI,GAAG,CAAC;AAAG,mBAAK,CAAC,CAAC;AAAG;AAAA,YACxC,KAAK;AAAG,mBAAK,EAAE,IAAI,IAAI;AAAG,gBAAE,KAAK,IAAI;AAAG;AAAA,YACxC;AACI,kBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,oBAAI;AAAG;AAAA,cAAU;AAC3G,kBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,kBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,cAAO;AACrF,kBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,oBAAI;AAAI;AAAA,cAAO;AACpE,kBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,cAAO;AAClE,kBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,gBAAE,KAAK,IAAI;AAAG;AAAA,UACtB;AACA,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC7B,SAAS,GAAG;AAAE,eAAK,CAAC,GAAG,CAAC;AAAG,cAAI;AAAA,QAAG,UAAE;AAAU,cAAI,IAAI;AAAA,QAAG;AACzD,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACnF;AAAA,IACJ;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,SAAS,QAAQ,YAAY,QAAQ,aAAa,QAAQ,YAAY,QAAQ,aAAa,QAAQ,gBAAgB;AAC3H,QAAI,SAAS;AACb,WAAO,eAAe,SAAS,aAAa,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAY,EAAE,CAAC;AAChH,QAAI;AAOJ,aAAS,QAAQ,KAAK,SAAS;AAC3B,UAAI,YAAY,QAAQ;AAAE,kBAAU,EAAE,IAAI,GAAG;AAAA,MAAG;AAEhD,gBAAU,SAAS,EAAE,iBAAiB,MAAM,GAAG,OAAO;AAEtD,kBAAY,IAAI,OAAO,WAAW,OAAO;AACzC,UAAI,UAAU,OAAO,IAAI,MAAM;AAE/B,UAAI,UAAU,mBAAmB,GAAG;AAEhC,YAAI,QAAQ,WAAW;AACnB,6BAAmB,KAAK,QAAQ,WAAW,QAAQ,cAAc,QAAQ,eAAe;AAAA,QAC5F;AAEA,YAAI,UAAU,QAAQ,WAAW,UAAU,QAAQ,YAAY;AAC3D,cAAI,MAAM,QAAQ,QAAQ,EAAE,GAAG;AAC3B,oBAAQ,GAAG,QAAQ,SAAU,IAAI;AAC7B,kBAAI,OAAO,OAAO,UAAU;AACxB,iBAAC,GAAG,OAAO,YAAY,IAAI,OAAO;AAAA,cACtC,OACK;AACD,oBAAI,UAAU,SAAS,CAAC,GAAG,OAAO;AAClC,oBAAI,GAAG,eAAe,MAAM;AACxB,0BAAQ,cAAc,SAAS,SAAS,CAAC,GAAG,QAAQ,WAAW,GAAG,GAAG,WAAW;AAAA,gBACpF;AACA,iBAAC,GAAG,OAAO,YAAY,GAAG,IAAI,OAAO;AAAA,cACzC;AAAA,YACJ,CAAC;AAAA,UACL,OACK;AACD,aAAC,GAAG,OAAO,YAAY,QAAQ,IAAI,OAAO;AAAA,UAC9C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAUA,aAAS,mBAAmB,KAAK,WAAW,cAAc,iBAAiB,2BAA2B;AAClG,UAAI,QAAQ;AACZ,UAAI,iBAAiB,QAAQ;AAAE,uBAAe,CAAC;AAAA,MAAG;AAClD,UAAI,8BAA8B,QAAQ;AAAE,oCAA4B,WAAY;AAAE,iBAAQ,CAAC;AAAA,QAAI;AAAA,MAAG;AACtG,UAAI,CAAC,WAAW;AACZ,gBAAQ,KAAK,0FAA0F;AACvG;AAAA,MACJ;AACA,gBAAU,UAAU,SAAU,IAAI,MAAM;AAAE,eAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAC1F,cAAI,MAAM,qBAAqB,IAAI,SAAS;AAC5C,cAAI,IAAI;AACR,iBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,oBAAQ,GAAG,OAAO;AAAA,cACd,KAAK;AAED,oBAAI,OAAO,GAAG,SAAS,YAClB,MAAM,QAAQ,YAAY,KAAK,aAAa,SAAS,GAAG,IAAI,KAC5D,OAAO,iBAAiB,cAAc,aAAa,IAAI,IAAI,GAAI;AAChE,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,gBACxB;AACA,uBAAO,GAAG,QAAQ,OAAO,GAAG,KAAK,QAAQ,YAAY,CAAC,CAAC,GAAG,KAAK,MACzD,GAAG,KAAK,MACR,GAAG;AACT,qBAAK,CAAC,CAAC,CAAC;AACR,uBAAO,CAAC,GAAa,0BAA0B,IAAI,IAAI,CAAC;AAAA,cAC5D,KAAK;AACD,sCAAsB,SAAS,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,GAAG,OAAO,CAAE,GAAG,KAAK,CAAE,CAAC,CAAC,IAAI,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,sBAAsB,CAAC;AAC9K,2BAAW,KAAK,UAAU,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK;AACzE,0BAAU;AACV,oBAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AACxB,6BAAW;AAAA,gBACf;AACA,2BAAW,GAAG,SAAS,WAAW,GAAG,IAC/B,GAAG,SAAS,OAAO,CAAC,IACpB,GAAG;AACT,oBAAI,iBAAiB;AACjB,sBAAI,SAAS,WAAY;AACrB,kCAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU,MAAM,SAAS,mBAAmB;AAAA,kBAChH,CAAC;AAAA,gBACL,OACK;AACD,gCAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU,MAAM,SAAS,mBAAmB;AAAA,gBAChH;AACA,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,YAC5B;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MAAG,CAAC;AAAA,IACT;AACA,QAAI,WAAW,EAAE,QAAiB;AAClC,QAAI,SAAS;AACb,WAAO,eAAe,SAAS,iBAAiB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAe,EAAE,CAAC;AACvH,WAAO,eAAe,SAAS,cAAc,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAY,EAAE,CAAC;AACjH,WAAO,eAAe,SAAS,aAAa,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAW,EAAE,CAAC;AAC/G,WAAO,eAAe,SAAS,cAAc,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAY,EAAE,CAAC;AACjH,YAAQ,UAAU;AAMlB,aAAS,SAAS;AACd,aAAO;AAAA,IACX;AACA,YAAQ,SAAS;AAAA;AAAA;", "names": ["_a"]}