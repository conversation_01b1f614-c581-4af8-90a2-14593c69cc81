{"version": 3, "sources": ["../../lodash/difference.js"], "sourcesContent": ["var baseDifference = require('./_baseDifference'),\n    baseFlatten = require('./_baseFlatten'),\n    baseRest = require('./_baseRest'),\n    isArrayLikeObject = require('./isArrayLikeObject');\n\n/**\n * Creates an array of `array` values not included in the other given arrays\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons. The order and references of result values are\n * determined by the first array.\n *\n * **Note:** Unlike `_.pullAll`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...Array} [values] The values to exclude.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.without, _.xor\n * @example\n *\n * _.difference([2, 1], [2, 3]);\n * // => [1]\n */\nvar difference = baseRest(function(array, values) {\n  return isArrayLikeObject(array)\n    ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true))\n    : [];\n});\n\nmodule.exports = difference;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,cAAc;AADlB,QAEI,WAAW;AAFf,QAGI,oBAAoB;AAuBxB,QAAI,aAAa,SAAS,SAAS,OAAO,QAAQ;AAChD,aAAO,kBAAkB,KAAK,IAC1B,eAAe,OAAO,YAAY,QAAQ,GAAG,mBAAmB,IAAI,CAAC,IACrE,CAAC;AAAA,IACP,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}