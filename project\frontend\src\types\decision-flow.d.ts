/* eslint-disable camelcase, no-use-before-define */

namespace Types {
  declare type DataConnectionTabs = 'data-list' | 'data-point' | 'edit-data-point' | 'disconnect';
  declare type ConnectStatus = 'not-connected' | 'connecting' | 'connected';

  declare type DecisionRepeatRunConfig = {
    active: boolean;
    period: number;
    end_date: string;
    apply_all: boolean;
    has_end_date?: boolean;
    events?: string[];
  };

  declare type DecisionRepeatRunEvent = {
    value: string;
    label: string;
    logo: string;
    description: string;

    active?: boolean;
  };

  declare type DecisionFlowItem = {
    id: string;
    name: string;
    description: string;
    active: boolean;
    event: string;
    published: boolean;
    action_list?: string[];
    data_point?: Pick<DataConnection, 'title' | 'data_point_name' | 'icon'>[];
    synchronous: boolean;
    repeat_run_config?: DecisionRepeatRunConfig;
    publish_name?: string;
    note?: string;
    publish_date?: string;
  };

  declare type DecisionFlowItemDetail = DecisionFlowItem & {
    schema?: DecisionFlowSchema;
  };

  declare type DecisionFlowEvent = {
    label: string;
    value: string;
    is_activated: boolean;
  };

  declare type DecisionFlowSchema = (Edge | Node)[];

  declare type DecisionFlowTab = 'list' | 'edit';

  declare type DecisionItemLog = {
    id: string;
    value: any;
    success: boolean;
    raw_data?:
      | DeleteApplicationParam
      | LogParam
      | SubmitApplicationParam
      | TriggerWebhookParam
      | TriggerIntegrationParam
      | UpdateStatusParam;
    timestamp: string;
    base_unit_name: string;
    error_detail?: {
      [key: stirng]: string;
    };
  };

  declare type CommonDecisionComponent = {
    id: string;
    type?: string;
    entity:
      | 'nodeStart'
      | 'nodeEnd'
      | 'nodeStatic'
      | 'nodeCondition'
      | 'nodeAction'
      | 'nodeEmpty'
      | 'edge';
    label: string;
    class?: string;
    log?: DecisionItemLog;
  };

  declare type DataObjectLog = {
    base_unit_name: string;
    id?: number | null;
    raw_data: {
      category: string | null;
      data_connection_id: string | null;
      log_path: string;
      rendered_value?: any;
      value: any;
      value_type: string | null;
      [key: string]: any;
    };
    success: boolean;
    timestamp: string;
    value: any;
    [key: string]: any;
  };

  declare type DataObject = {
    category: string; // ['answer', 'manual', ...]
    value: any; // [...]
    value_type: string; // ['string', 'number', 'boolean', 'null', 'json']
    decision_flow_data_point_id?: number | null;
    log?: DataObjectLog;
  };

  declare type ActionParam = {
    label: string;
    method: string; // ['setEkycStatus', ...]
    param: {
      [key: string]: any;
    };
  };

  declare type DeleteApplicationParam = ActionParam & {
    method: 'deleteApplication';
  };

  declare type LogParam = ActionParam & {
    method: 'log';
    param: {
      section?: string | optional;
      detail: string;
      action: string;
    };
  };

  declare type SubmitApplicationParam = ActionParam & {
    method: 'submitApplication';
    param: {
      log_all_sections?: boolean;
      notify?: boolean;
      request_hook_submit?: boolean;
    };
  };

  declare type TriggerIntegrationParam = ActionParam & {
    method: 'triggerIntegration';
    param: {
      integration_id: nubmer;
      data?: Object;
    };
  };

  declare type TriggerWebhookParam = ActionParam & {
    method: 'triggerWebhook';
    param: {
      form_webhook_id: nubmer;
      collect_result?: boolean;
      data?: Object;
    };
  };

  declare type UpdateStatusParam = ActionParam & {
    method: 'updateStatus';
    param: {
      status?: string;
      other_status: {
        status_name: string;
        [key: string]: any;
      };
    };
  };

  declare type SetAnswerParam = ActionParam & {
    method: 'setAnswer';
    param: {
      answers: Record<string, any>;
    };
  };

  declare type RunActionParam = ActionParam & {
    method: 'runAction';
  };

  declare type SetErrorPopupParam = RunActionParam & {
    param: {
      entity: string;
      method: 'setErrorPopup';
      params: {
        error_header: string;
        error_body: string;
      };
    };
  };

  declare type ConditionItem = {
    op: string; // backend-allowed operations ['==', '!=', '<', '>', '<=', '>=', 'in', 'not in', 'is', 'is not']
    initial_data: DataObject;
    expected_data: DataObject;
  };

  declare type ConditionParam = {
    label?: string;
    link_operation?: string; // ['or', 'and', ...]
    groups?: {
      link_operation: string; // ['or', 'and', ...]
      children: ConditionItem[];
      log?: any;
    }[];
    value?: any;
    is_default?: boolean;
  };

  declare type Node = CommonDecisionComponent & {
    position: { x: number; y: number };
    param?: ActionParam | ConditionParam | ConditionParam[];
  };

  declare type ActionNode = Node & {
    param?: ActionParam;
  };

  declare type ConditionNode = Node & {
    is_ifelse_statement?: boolean;
    param?: ConditionParam[];
  };

  declare type EdgeParam = {
    source_value_match?: DataObject;
  };

  declare type Edge = CommonDecisionComponent & {
    source: string; // node_id
    target: string; // node_id
    param?: EdgeParam;
  };

  declare type DecisionResultCellCustom = {
    type?: 'custom';
    label: string;
    inline_label?: string;
    class?: string;
    value: string;
    sort_key?: string;
  };

  declare type DecisionResultCellSetting =
    | {
        type:
          | 'form'
          | 'id'
          | 'name'
          | 'status'
          | 'submitted_at'
          | 'created_at'
          | 'publish_name'
          | 'publish_date'
          | 'event'
          | 'repeat_run'
          | 'decision_flow_name';
      }
    | DecisionResultCellCustom;

  declare type DecisionResultColumnSetting = {
    label?: string;
    cells: DecisionResultCellSetting[];
    show_only?: string[];
  };

  declare type DecisionResultCell = {
    label: string;
    class?: string;
    value?: string | number;
    sortKey?: string;
    getClass?: (row: any) => string;
    getTooltip?: (row: any) => string;
  };

  declare type DecisionResultColumn = {
    label?: string;
    stylingCellType?: string;
    cells: DecisionResultCell[];
    sortKey?: string;
  };

  declare type DecisionResult = {
    id: number;
    applied_form: string;
    form: string;
    name: string;
    publish_name: string;
    status: string;
    created_at: string;
    updated_at: string;
    submitted_at: string;
    updated_at: string;
    publish_date: string;
    decision_flow_name: string;
    event: string;
    period?: number;
    note?: string;
    repeat_run?: string;
  };

  declare type RequiredDataPointOption = {
    value: any;
    label: string;
    type: string;
    builder_type: string;
    operations?: {
      label: string;
      value: string[];
    }[];
  };

  declare type RequiredDataPointItem = {
    label: string;
    type: string;
    category: string;
    is_array?: boolean;
    options: RequiredDataPointOption[];
    validation_rule?: string | string[];
    allowed_item_builder_types?: string[];
    split_fields?: boolean;
    extra_fields?: {
      [key: string]: {
        label: string;
        type: string;
        [key: string]: any;
      };
    };
  };

  declare type RequiredDataPoint = {
    [key: string]: RequiredDataPointItem;
  };

  declare type DataPointTemplate = RequiredDataPointItem & {
    value: any;
  };

  declare type DataPointPayloadValueObject = {
    value: string;
    extra_fields?: { [key: string]: any };
  };

  declare type DataPointPayloadValue = DataPointPayloadValueObject | string;

  declare type DataPointPayloadItem = {
    category: string;
    data_point: DataPointPayloadValue | DataPointPayloadValue[];
  };

  declare type DataPointPayload = {
    id: null | number;
    field: {
      [key: string]: DataPointPayloadItem;
    };
  };

  declare type DataConnection = {
    title: string;
    sub_title: string;
    description: string;
    icon?: string;
    connect: boolean;
    data_point_name: string;
    data_connect?: DataPointPayload[];
    is_multiple_input?: boolean;
  };

  declare type ActionOption = {
    value: string;
    label: string;
    synchronous: boolean;
  };

  declare type ActionList = ActionOption[];

  declare type CustomStatusOption = {
    id?: number;
    value: string;
    label?: string;
    editing?: boolean;
    color?: string;
    priority?: number;
  };

  declare type CustomStatus = {
    id?: number;
    value: string;
    description?: string;
    icon?: string;
    label?: string;
    options: CustomStatusOption[];
    editing?: boolean;
    priority?: number;
    read_only?: boolean;
  };

  declare type ConditionDataPointOption = {
    key?: string;
    label?: string;
    type: string;
    builder_type?: string;
    operations?: {
      label: string;
      value: string;
    }[];
    options?: {
      label?: string;
      value: any;
      type?: string;
      disabled?: boolean;
    }[];
  };

  declare type ConditionDataPoint = {
    category: {
      name: 'global_data_source' | 'data_from_submission' | 'data_type';
      label: string;
    };
    icon?: string;
    icon_name?: string;
    label?: string;
    options: { [key: string]: ConditionDataPointOption };
    is_connected: boolean;
    decision_flow_data_point_id?: number | null;
    data_point_name: string;
  };

  declare type ConditionDataPointList = ConditionDataPoint[];
  declare type ConditionItemPayload = {
    category: string;
    dataPoint: string;
    condition: string;
    valueType: string;
    value: any;
    decision_flow_data_point_id?: number | null;
  };

  declare type DataConnectionFooter = {
    disabled: boolean;
    connectStatus: string;
    button: any;
  };

  declare type DecisionConnectionResultList = {
    [key: string]: {
      has_result: boolean;
      is_connected: boolean;
    };
  };
}
