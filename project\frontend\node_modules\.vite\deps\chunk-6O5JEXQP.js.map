{"version": 3, "sources": ["../../../../tfjs-backend-cpu/src/shared.ts", "../../../../tfjs-backend-cpu/src/cpu_util.ts", "../../../../tfjs-backend-cpu/src/kernels/Abs.ts", "../../../../tfjs-backend-cpu/src/utils/binary_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Complex.ts", "../../../../tfjs-backend-cpu/src/utils/zeros_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Identity.ts", "../../../../tfjs-backend-cpu/src/kernels/Real.ts", "../../../../tfjs-backend-cpu/src/kernels/Cast.ts", "../../../../tfjs-backend-cpu/src/utils/binary_utils.ts", "../../../../tfjs-backend-cpu/src/kernels/Add.ts", "../../../../tfjs-backend-cpu/src/kernels/Bincount_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/BitwiseAnd.ts", "../../../../tfjs-backend-cpu/src/utils/unary_impl.ts", "../../../../tfjs-backend-cpu/src/utils/unary_utils.ts", "../../../../tfjs-backend-cpu/src/kernels/Ceil.ts", "../../../../tfjs-backend-cpu/src/kernels/Concat_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Equal.ts", "../../../../tfjs-backend-cpu/src/kernels/Exp.ts", "../../../../tfjs-backend-cpu/src/kernels/Expm1.ts", "../../../../tfjs-backend-cpu/src/kernels/Floor.ts", "../../../../tfjs-backend-cpu/src/kernels/FloorDiv.ts", "../../../../tfjs-backend-cpu/src/kernels/GatherNd_Impl.ts", "../../../../tfjs-backend-cpu/src/kernels/GatherV2_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Greater.ts", "../../../../tfjs-backend-cpu/src/kernels/GreaterEqual.ts", "../../../../tfjs-backend-cpu/src/kernels/Less.ts", "../../../../tfjs-backend-cpu/src/kernels/LessEqual.ts", "../../../../tfjs-backend-cpu/src/kernels/LinSpace_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Log.ts", "../../../../tfjs-backend-cpu/src/kernels/Max_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Maximum.ts", "../../../../tfjs-backend-cpu/src/kernels/Minimum.ts", "../../../../tfjs-backend-cpu/src/kernels/Multiply.ts", "../../../../tfjs-backend-cpu/src/kernels/Neg.ts", "../../../../tfjs-backend-cpu/src/kernels/NotEqual.ts", "../../../../tfjs-backend-cpu/src/kernels/Transpose_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Transpose.ts", "../../../../tfjs-backend-cpu/src/kernels/Prod.ts", "../../../../tfjs-backend-cpu/src/kernels/RaggedGather_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/RaggedRange_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/RaggedTensorToTensor_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Range_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Rsqrt.ts", "../../../../tfjs-backend-cpu/src/kernels/Scatter_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Sigmoid.ts", "../../../../tfjs-backend-cpu/src/kernels/Slice.ts", "../../../../tfjs-backend-cpu/src/kernels/SparseFillEmptyRows_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/SparseReshape_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/SparseSegmentReduction_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Sqrt.ts", "../../../../tfjs-backend-cpu/src/kernels/SquaredDifference.ts", "../../../../tfjs-backend-cpu/src/kernels/StaticRegexReplace.ts", "../../../../tfjs-backend-cpu/src/kernels/StridedSlice_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/StringNGrams_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/StringSplit_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/StringToHashBucketFast_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Sub.ts", "../../../../tfjs-backend-cpu/src/kernels/Tile_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/TopK_impl.ts", "../../../../tfjs-backend-cpu/src/kernels/Unique_impl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n// Shared functionality among backends.\nexport {simpleAbsImpl} from './kernels/Abs';\nexport {addImpl} from './kernels/Add';\nexport {bincountImpl, bincountReduceImpl} from './kernels/Bincount_impl';\nexport {bitwiseAndImpl} from './kernels/BitwiseAnd';\nexport {castImpl} from './kernels/Cast';\nexport {ceilImpl} from './kernels/Ceil';\nexport {concatImpl} from './kernels/Concat_impl';\nexport {equalImpl} from './kernels/Equal';\nexport {expImpl} from './kernels/Exp';\nexport {expm1Impl} from './kernels/Expm1';\nexport {floorImpl} from './kernels/Floor';\nexport {floorDivImpl} from './kernels/FloorDiv';\nexport {gatherNdImpl} from './kernels/GatherNd_Impl';\nexport {gatherV2Impl} from './kernels/GatherV2_impl';\nexport {greaterImpl} from './kernels/Greater';\nexport {greaterEqualImpl} from './kernels/GreaterEqual';\nexport {lessImpl} from './kernels/Less';\nexport {lessEqualImpl} from './kernels/LessEqual';\nexport {linSpaceImpl} from './kernels/LinSpace_impl';\nexport {logImpl} from './kernels/Log';\nexport {maxImpl} from './kernels/Max_impl';\nexport {maximumImpl} from './kernels/Maximum';\nexport {minimumImpl} from './kernels/Minimum';\nexport {multiplyImpl} from './kernels/Multiply';\nexport {negImpl} from './kernels/Neg';\nexport {notEqualImpl} from './kernels/NotEqual';\nexport {prodImpl} from './kernels/Prod';\nexport {raggedGatherImpl} from './kernels/RaggedGather_impl';\nexport {raggedRangeImpl} from './kernels/RaggedRange_impl';\nexport {raggedTensorToTensorImpl} from './kernels/RaggedTensorToTensor_impl';\nexport {rangeImpl} from './kernels/Range_impl';\nexport {rsqrtImpl} from './kernels/Rsqrt';\nexport {scatterImpl} from './kernels/Scatter_impl';\nexport {sigmoidImpl} from './kernels/Sigmoid';\nexport {sliceImpl} from './kernels/Slice';\nexport {sparseFillEmptyRowsImpl} from './kernels/SparseFillEmptyRows_impl';\nexport {sparseReshapeImpl} from './kernels/SparseReshape_impl';\nexport {sparseSegmentReductionImpl} from './kernels/SparseSegmentReduction_impl';\nexport {sqrtImpl} from './kernels/Sqrt';\nexport {squaredDifferenceImpl} from './kernels/SquaredDifference';\nexport {staticRegexReplaceImpl} from './kernels/StaticRegexReplace';\nexport {stridedSliceImpl} from './kernels/StridedSlice_impl';\nexport {stringNGramsImpl} from './kernels/StringNGrams_impl';\nexport {stringSplitImpl} from './kernels/StringSplit_impl';\nexport {stringToHashBucketFastImpl} from './kernels/StringToHashBucketFast_impl';\nexport {subImpl} from './kernels/Sub';\nexport {tileImpl} from './kernels/Tile_impl';\nexport {topKImpl} from './kernels/TopK_impl';\nexport {transposeImpl} from './kernels/Transpose_impl';\nexport {uniqueImpl} from './kernels/Unique_impl';\nexport {ComplexBinaryKernelImpl, SimpleBinaryKernelImpl} from './utils/binary_types';\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TensorInfo, util} from '@tensorflow/tfjs-core';\n\nexport function assertNotComplex(\n    tensor: TensorInfo|TensorInfo[], opName: string): void {\n  if (!Array.isArray(tensor)) {\n    tensor = [tensor];\n  }\n  tensor.forEach(t => {\n    if (t != null) {\n      util.assert(\n          t.dtype !== 'complex64',\n          () => `${\n              opName} does not support complex64 tensors in the CPU backend.`);\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Abs, AbsInputs, KernelConfig, KernelFunc, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function simpleAbsImpl(vals: TypedArray): Float32Array {\n  const resultValues = new Float32Array(vals.length);\n  for (let i = 0; i < vals.length; ++i) {\n    resultValues[i] = Math.abs(vals[i]);\n  }\n  return resultValues;\n}\n\nexport const abs = (args: {inputs: AbsInputs, backend: MathBackendCPU}) => {\n  const {x} = args.inputs;\n  const cpuBackend = args.backend;\n\n  assertNotComplex(x, 'abs');\n\n  let resultValues = new Float32Array(util.sizeFromShape(x.shape));\n  const values = cpuBackend.data.get(x.dataId).values as TypedArray;\n  resultValues = simpleAbsImpl(values);\n\n  return cpuBackend.makeOutput(resultValues, x.shape, x.dtype);\n};\n\nexport const absConfig: KernelConfig = {\n  kernelName: Abs,\n  backendName: 'cpu',\n  kernelFunc: abs as unknown as KernelFunc,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, DataValues, NumericDataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {SimpleBinaryKernelImpl, SimpleBinaryOperation} from './binary_types';\n\n/**\n * Template that creates implementation for binary ops. Supports broadcast.\n */\nexport function createSimpleBinaryKernelImpl(op: SimpleBinaryOperation):\n    SimpleBinaryKernelImpl {\n  return (aShape: number[], bShape: number[], aVals: DataValues,\n          bVals: DataValues, dtype: DataType): [TypedArray, number[]] => {\n    const newShape = backend_util.assertAndGetBroadcastShape(aShape, bShape);\n\n    const resultRank = newShape.length;\n    const resultStrides = util.computeStrides(newShape);\n    const resultSize = util.sizeFromShape(newShape);\n\n    const result =\n        util.getTypedArrayFromDType(dtype as NumericDataType, resultSize);\n\n    const aRank = aShape.length;\n    const bRank = bShape.length;\n\n    const aStrides = util.computeStrides(aShape);\n    const bStrides = util.computeStrides(bShape);\n\n    const aBroadcastDims = backend_util.getBroadcastDims(aShape, newShape);\n    const bBroadcastDims = backend_util.getBroadcastDims(bShape, newShape);\n\n    if (aBroadcastDims.length + bBroadcastDims.length === 0) {\n      for (let i = 0; i < result.length; ++i) {\n        result[i] = op(aVals[i % aVals.length], bVals[i % bVals.length]);\n      }\n    } else {\n      for (let i = 0; i < result.length; ++i) {\n        const loc = util.indexToLoc(i, resultRank, resultStrides);\n\n        const aLoc = loc.slice(-aRank);\n        aBroadcastDims.forEach(d => aLoc[d] = 0);\n        const aIndex = util.locToIndex(aLoc, aRank, aStrides);\n\n        const bLoc = loc.slice(-bRank);\n        bBroadcastDims.forEach(d => bLoc[d] = 0);\n        const bIndex = util.locToIndex(bLoc, bRank, bStrides);\n\n        result[i] = op(aVals[aIndex], bVals[bIndex]);\n      }\n    }\n\n    return [result, newShape];\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Complex, ComplexInputs, KernelConfig, KernelFunc, TensorInfo, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport function complex(args: {inputs: ComplexInputs, backend: MathBackendCPU}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {real, imag} = inputs;\n\n  const realVals = backend.data.get(real.dataId).values as TypedArray;\n  const imagVals = backend.data.get(imag.dataId).values as TypedArray;\n\n  const complexInfo = backend.makeTensorInfo(real.shape, 'complex64');\n\n  const complex = backend.data.get(complexInfo.dataId);\n\n  // The complex tensor owns the underlying real and imag tensorInfos, only the\n  // complex tensor tracks refCount, when complexData is disposed the\n  // underlying tensorData will be disposed.\n  complex.complexTensorInfos = {\n    real: backend.makeTensorInfo(real.shape, 'float32', realVals),\n    imag: backend.makeTensorInfo(imag.shape, 'float32', imagVals)\n  };\n\n  return complexInfo;\n}\n\nexport const complexConfig: KernelConfig = {\n  kernelName: Complex,\n  backendName: 'cpu',\n  kernelFunc: complex as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, TensorInfo, util} from '@tensorflow/tfjs-core';\nimport {MathBackendCPU} from '../backend_cpu';\nimport {complex} from '../kernels/Complex';\n\n/**\n * Generates a tensorInfo with all zeros value.\n * @param backend cpu backend.\n * @param shape Shape for the zeros tensor.\n * @param dtype Optional. If set, the result has this dtype.\n */\nexport function zeros(\n    backend: MathBackendCPU, shape: number[],\n    dtype: DataType = 'float32'): TensorInfo {\n  if (dtype === 'complex64') {\n    const real = zeros(backend, shape, 'float32');\n    const imag = zeros(backend, shape, 'float32');\n\n    return complex({inputs: {real, imag}, backend});\n  }\n\n  const values = util.makeZerosTypedArray(util.sizeFromShape(shape), dtype);\n\n  return backend.makeTensorInfo(shape, dtype, values);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Identity, IdentityInputs, KernelConfig, KernelFunc, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport function identity(\n    args: {inputs: IdentityInputs, backend: MathBackendCPU}): TensorInfo {\n  const {inputs, backend} = args;\n  const {x} = inputs;\n\n  backend.incRef(x.dataId);\n\n  return {dataId: x.dataId, shape: x.shape, dtype: x.dtype};\n}\n\nexport const identityConfig: KernelConfig = {\n  kernelName: Identity,\n  backendName: 'cpu',\n  kernelFunc: identity as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, Real, RealInputs, TensorInfo} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\n\nexport function real(args: {inputs: RealInputs, backend: MathBackendCPU}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {input} = inputs;\n\n  const real = backend.data.get(input.dataId).complexTensorInfos.real;\n  const realVal = backend.data.get(real.dataId).values;\n\n  // When complex tensor is disposed, its underlying parts will be disposed too.\n  // Make new tensor out of the real value of the complex. This makes sure the\n  // value is still accessible even if complex tensor is disposed.\n  return backend.makeTensorInfo(real.shape, real.dtype, realVal);\n}\n\nexport const realConfig: KernelConfig = {\n  kernelName: Real,\n  backendName: 'cpu',\n  kernelFunc: real as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {Cast, CastAttrs, CastInputs, DataType, KernelConfig, KernelFunc, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {zeros} from '../utils/zeros_impl';\n\nimport {complex} from './Complex';\nimport {identity} from './Identity';\nimport {real} from './Real';\n\nexport function castImpl(\n    values: TypedArray, shape: number[], inputType: DataType,\n    dtype: DataType): [number[], DataType, TypedArray] {\n  if (dtype === 'int32') {\n    const resultValues = Int32Array.from(values);\n    return [shape, 'int32', resultValues];\n  }\n\n  if (dtype === 'bool') {\n    // This is essentially the result of notEqual(x, 0). We avoid using\n    // kernel notEqual to avoid circular dependency, i.e. binary_utils ->\n    // cast -> notEqual -> binary_utils.\n    const zero = util.toTypedArray([0], inputType);\n\n    const [resultData, resultShape] = createSimpleBinaryKernelImpl(\n        (a, b) => (a !== b) ? 1 : 0)(shape, [], values, zero, 'bool');\n\n    return [resultShape, 'bool', resultData];\n  }\n  throw new Error(`Error in Cast: failed to cast ${inputType} to ${dtype}`);\n}\n\nexport function cast(\n    args: {inputs: CastInputs, backend: MathBackendCPU, attrs: CastAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {dtype} = attrs;\n\n  // Casting to complex64.\n  if (dtype === 'complex64') {\n    if (x.dtype === 'complex64') {\n      return identity({inputs: {x}, backend});\n    }\n\n    const zerosTensorInfo = zeros(backend, x.shape, x.dtype);\n    const floatX = cast({inputs: {x}, backend, attrs: {dtype: 'float32'}});\n\n    const result =\n        complex({inputs: {real: floatX, imag: zerosTensorInfo}, backend});\n\n    backend.disposeIntermediateTensorInfo(zerosTensorInfo);\n    backend.disposeIntermediateTensorInfo(floatX);\n\n    return result;\n  }\n\n  // Casting from complex64\n  if (x.dtype === 'complex64') {\n    const realPart = real({inputs: {input: x}, backend});\n    const result = cast({inputs: {x: realPart}, backend, attrs: {dtype}});\n\n    backend.disposeIntermediateTensorInfo(realPart);\n\n    return result;\n  }\n\n  if (!util.hasEncodingLoss(x.dtype, dtype)) {\n    // We don't change the underlying data, since we cast to higher\n    // precision.\n    const result = identity({inputs: {x}, backend});\n    return {dataId: result.dataId, shape: result.shape, dtype};\n  }\n\n  const values = backend.data.get(x.dataId).values as TypedArray;\n  const [resultShape, resultType, resultData] =\n      castImpl(values, x.shape, x.dtype, dtype);\n  return backend.makeTensorInfo(resultShape, resultType, resultData);\n}\n\nexport const castConfig: KernelConfig = {\n  kernelName: Cast,\n  backendName: 'cpu',\n  kernelFunc: cast as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, BinaryInputs, DataType, KernelFunc, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {cast} from '../kernels/Cast';\nimport {complex} from '../kernels/Complex';\n\nimport {ComplexBinaryKernelImpl, ComplexBinaryOperation, SimpleBinaryKernelImpl} from './binary_types';\n\n/**\n * Template that creates a `KernelFunc` for binary ops.\n * @param name Kernel name.\n * @param binaryKernelImpl A `SimpleBinaryKernelImpl` for the kernel.\n * @param binaryKernelComplexImpl Optional. If exists, represents a\n *     `ComplexBinaryKernelImpl` for the kernel, will be used when input dtype\n *     is `complex64`.\n * @param dtype Optional. If set, the result has this dtype. Otherwise, the\n *     result has the same dtype as the first input. This is mainly used in\n *     comparison kernels, such as Equal, Less, Greater, etc.\n */\nexport function binaryKernelFunc(\n    name: string, simpleImpl: SimpleBinaryKernelImpl,\n    complexImpl?: ComplexBinaryKernelImpl, dtype?: DataType): KernelFunc {\n  if (complexImpl == null) {\n    return ({inputs, backend}) => {\n      const {a, b} = inputs as BinaryInputs;\n      const cpuBackend = backend as MathBackendCPU;\n\n      assertNotComplex([a, b], name);\n\n      const aVals = cpuBackend.data.get(a.dataId).values as TypedArray;\n      const bVals = cpuBackend.data.get(b.dataId).values as TypedArray;\n\n      const decodedAVals = a.dtype === 'string' ?\n          // tslint:disable-next-line: no-any\n          backend_util.fromUint8ToStringArray(aVals as any as Uint8Array[]) :\n          aVals;\n      const decodedBVals = a.dtype === 'string' ?\n          // tslint:disable-next-line: no-any\n          backend_util.fromUint8ToStringArray(bVals as any as Uint8Array[]) :\n          bVals;\n      const $dtype = dtype || a.dtype;\n\n      const [resultData, resultShape] =\n          simpleImpl(a.shape, b.shape, decodedAVals, decodedBVals, $dtype);\n\n      return cpuBackend.makeTensorInfo(resultShape, $dtype, resultData);\n    };\n  }\n\n  return ({inputs, backend}) => {\n    const {a, b} = inputs as BinaryInputs;\n    const cpuBackend = backend as MathBackendCPU;\n\n    if (a.dtype === 'complex64' || b.dtype === 'complex64') {\n      const $aComplex = cast(\n          {inputs: {x: a}, backend: cpuBackend, attrs: {dtype: 'complex64'}});\n\n      const $aComplexVals = cpuBackend.data.get($aComplex.dataId);\n\n      const aReal = $aComplexVals.complexTensorInfos.real;\n      const aImag = $aComplexVals.complexTensorInfos.imag;\n\n      const aRealVals =\n          cpuBackend.data.get(aReal.dataId).values as Float32Array;\n      const aImagVals =\n          cpuBackend.data.get(aImag.dataId).values as Float32Array;\n\n      const $bComplex = cast(\n          {inputs: {x: b}, backend: cpuBackend, attrs: {dtype: 'complex64'}});\n\n      const $bComplexVals = cpuBackend.data.get($bComplex.dataId);\n\n      const bReal = $bComplexVals.complexTensorInfos.real;\n      const bImag = $bComplexVals.complexTensorInfos.imag;\n\n      const bRealVals =\n          cpuBackend.data.get(bReal.dataId).values as Float32Array;\n      const bImagVals =\n          cpuBackend.data.get(bImag.dataId).values as Float32Array;\n\n      const [resultRealData, resultImagData, resultShape] = complexImpl(\n          a.shape, b.shape, aRealVals, aImagVals, bRealVals, bImagVals);\n\n      const resultReal =\n          cpuBackend.makeTensorInfo(resultShape, 'float32', resultRealData);\n\n      const resultImag =\n          cpuBackend.makeTensorInfo(resultShape, 'float32', resultImagData);\n\n      const result = complex(\n          {inputs: {real: resultReal, imag: resultImag}, backend: cpuBackend});\n\n      cpuBackend.disposeIntermediateTensorInfo($aComplex);\n      cpuBackend.disposeIntermediateTensorInfo($bComplex);\n      cpuBackend.disposeIntermediateTensorInfo(resultReal);\n      cpuBackend.disposeIntermediateTensorInfo(resultImag);\n\n      return result;\n    } else {\n      const aVals = cpuBackend.data.get(a.dataId).values as TypedArray;\n      const bVals = cpuBackend.data.get(b.dataId).values as TypedArray;\n\n      const $dtype = dtype || a.dtype;\n\n      const [resultData, resultShape] =\n          simpleImpl(a.shape, b.shape, aVals, bVals, $dtype);\n\n      return cpuBackend.makeTensorInfo(resultShape, $dtype, resultData);\n    }\n  };\n}\n\n/**\n * Template that creates the complex type implementation for binary ops.\n * Supports broadcast.\n */\nexport function createComplexBinaryKernelImpl(op: ComplexBinaryOperation):\n    ComplexBinaryKernelImpl {\n  return (aShape: number[], bShape: number[], aRealVals: Float32Array,\n          aImagVals: Float32Array, bRealVals: Float32Array,\n          bImagVals: Float32Array): [TypedArray, TypedArray, number[]] => {\n    const resultShape = backend_util.assertAndGetBroadcastShape(aShape, bShape);\n    const resultSize = util.sizeFromShape(resultShape);\n    const resultRank = resultShape.length;\n    const resultStrides = util.computeStrides(resultShape);\n\n    const resultRealVals = util.getTypedArrayFromDType('float32', resultSize);\n    const resultImagVals = util.getTypedArrayFromDType('float32', resultSize);\n\n    const aBroadcastDims = backend_util.getBroadcastDims(aShape, resultShape);\n    const bBroadcastDims = backend_util.getBroadcastDims(bShape, resultShape);\n\n    const aVals = backend_util.mergeRealAndImagArrays(aRealVals, aImagVals);\n    const bVals = backend_util.mergeRealAndImagArrays(bRealVals, bImagVals);\n\n    const aRank = aShape.length;\n    const aStrides = util.computeStrides(aShape);\n\n    const bRank = bShape.length;\n    const bStrides = util.computeStrides(bShape);\n\n    if (aBroadcastDims.length + bBroadcastDims.length === 0) {\n      for (let i = 0; i < resultRealVals.length; i++) {\n        const aIdx = i % aVals.length;\n        const bIdx = i % bVals.length;\n\n        const result =\n            op(aVals[aIdx * 2], aVals[aIdx * 2 + 1], bVals[bIdx * 2],\n               bVals[bIdx * 2 + 1]);\n\n        resultRealVals[i] = result.real;\n        resultImagVals[i] = result.imag;\n      }\n    } else {\n      for (let i = 0; i < resultRealVals.length; i++) {\n        const loc = util.indexToLoc(i, resultRank, resultStrides);\n\n        const aLoc = loc.slice(-aRank);\n        aBroadcastDims.forEach(d => aLoc[d] = 0);\n        const aIndex = util.locToIndex(aLoc, aRank, aStrides);\n\n        const bLoc = loc.slice(-bRank);\n        bBroadcastDims.forEach(d => bLoc[d] = 0);\n        const bIndex = util.locToIndex(bLoc, bRank, bStrides);\n\n        const opResult =\n            op(aVals[aIndex * 2], aVals[aIndex * 2 + 1], bVals[bIndex * 2],\n               bVals[bIndex * 2 + 1]);\n\n        resultRealVals[i] = opResult.real;\n        resultImagVals[i] = opResult.imag;\n      }\n    }\n    return [resultRealVals, resultImagVals, resultShape];\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Add, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc, createComplexBinaryKernelImpl} from '../utils/binary_utils';\n\nexport const addImpl =\n    createSimpleBinaryKernelImpl(((a: number, b: number) => a + b));\nexport const addComplexImpl =\n    createComplexBinaryKernelImpl(((aReal, aImag, bReal, bImag) => {\n      return {real: aReal + bReal, imag: aImag + bImag};\n    }));\n\nexport const add = binaryKernelFunc(Add, addImpl, addComplexImpl);\n\nexport const addConfig: KernelConfig = {\n  kernelName: Add,\n  backendName: 'cpu',\n  kernelFunc: add\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, DataType, Rank, TensorBuffer, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function bincountImpl(\n    xVals: TypedArray, weightsVals: TypedArray, weightsDtype: DataType,\n    weightsShape: number[], size: number): TypedArray {\n  const weightsSize = util.sizeFromShape(weightsShape);\n  const outVals = util.makeZerosTypedArray(size, weightsDtype) as TypedArray;\n\n  for (let i = 0; i < xVals.length; i++) {\n    const value = xVals[i];\n    if (value < 0) {\n      throw new Error('Input x must be non-negative!');\n    }\n\n    if (value >= size) {\n      continue;\n    }\n\n    if (weightsSize > 0) {\n      outVals[value] += weightsVals[i];\n    } else {\n      outVals[value] += 1;\n    }\n  }\n\n  return outVals;\n}\n\nexport function bincountReduceImpl<R extends Rank>(\n    xBuf: TensorBuffer<R>, weightsBuf: TensorBuffer<R>, size: number,\n    binaryOutput = false): TensorBuffer<R> {\n  const numRows = xBuf.shape[0];\n  const numCols = xBuf.shape[1];\n\n  const outBuf = buffer([numRows, size], weightsBuf.dtype);\n\n  for (let i = 0; i < numRows; i++) {\n    for (let j = 0; j < numCols; j++) {\n      const value = xBuf.get(i, j);\n      if (value < 0) {\n        throw new Error('Input x must be non-negative!');\n      }\n\n      if (value >= size) {\n        continue;\n      }\n\n      if (binaryOutput) {\n        outBuf.set(1, i, value);\n      } else {\n        if (weightsBuf.size > 0) {\n          outBuf.set(outBuf.get(i, value) + weightsBuf.get(i, j), i, value);\n        } else {\n          outBuf.set(outBuf.get(i, value) + 1, i, value);\n        }\n      }\n    }\n  }\n\n  return outBuf as TensorBuffer<R>;\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BitwiseAnd, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const bitwiseAndImpl =\n    createSimpleBinaryKernelImpl(((a: number, b: number) => a & b));\n\nexport const bitwiseAnd = binaryKernelFunc(BitwiseAnd, bitwiseAndImpl);\n\nexport const bitwiseAndConfig: KernelConfig = {\n  kernelName: BitwiseAnd,\n  backendName: 'cpu',\n  kernelFunc: bitwiseAnd\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\n\nimport {SimpleUnaryImpl, SimpleUnaryOperation} from './unary_types';\n\n/**\n * Template that creates implementation for unary op.\n */\nexport function createSimpleUnaryImpl<I extends number | string = number,\n  O extends number | string = number>(op: SimpleUnaryOperation<I, O>):\n    SimpleUnaryImpl<I, O> {\n  return (values, dtype, attrs) => {\n    const newValues =\n        util.getArrayFromDType(dtype, values.length);\n    for (let i = 0; i < values.length; ++i) {\n      newValues[i] = op(values[i], attrs);\n    }\n    return newValues;\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataTypeFor, KernelFunc, UnaryInputs} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {createSimpleUnaryImpl} from './unary_impl';\n\nimport {SimpleUnaryImpl, SimpleUnaryOperation} from './unary_types';\n\n/**\n * Template that creates a `KernelFunc` for unary ops.\n * @param name Kernel name.\n * @param op A `SimpleUnaryOperation` for the kernel.\n * @param dtype Optional. If set, the result has this dtype. Otherwise, the\n *     result has the same dtype as the input. This is mainly used in certain\n *     kernels that return bool type, such as isFinite, isInf, etc.\n */\nexport function unaryKernelFunc<I extends number | string = number,\n  O extends number | string = number>(\n  name: string, op: SimpleUnaryOperation<I, O>,\n  dtype?: DataTypeFor<O>): KernelFunc {\n\n  const impl = createSimpleUnaryImpl<I, O>(op);\n\n  return unaryKernelFuncFromImpl<I, O>(name, impl, dtype);\n}\n\n/**\n * Template that creates a `KernelFunc` for unary ops from the given\n * `SimpleUnaryImpl`..\n * @param name Kernel name.\n * @param unaryImpl A `SimpleUnaryImpl` that implements the op.\n * @param dtype Optional. If set, the result has this dtype. Otherwise, the\n *     result has the same dtype as the input. This is mainly used in certain\n *     kernels that return bool type, such as isFinite, isInf, etc.\n */\nexport function unaryKernelFuncFromImpl<I extends number | string = number,\n  O extends number | string = number>(\n  name: string, unaryImpl: SimpleUnaryImpl<I, O>,\n  dtype?: DataTypeFor<O>): KernelFunc {\n\n  return ({inputs, attrs, backend}) => {\n    const {x} = inputs as UnaryInputs;\n    assertNotComplex(x, name);\n\n    const cpuBackend = backend as MathBackendCPU;\n    const values = cpuBackend.data.get(x.dataId).values;\n    let decoded: ArrayLike<I>;\n    if (x.dtype === 'string') {\n      if (!Array.isArray(values)) {\n        throw new Error('String tensor\\'s value was not an instance of Array');\n      }\n      decoded = backend_util.fromUint8ToStringArray(values) as unknown as\n        ArrayLike<I>;\n    } else {\n      decoded = values as unknown as ArrayLike<I>;\n    }\n\n    const $dtype = dtype || x.dtype as DataTypeFor<O>;\n    const newValues = unaryImpl(decoded, $dtype, attrs);\n    return cpuBackend.makeTensorInfo(x.shape, $dtype, newValues);\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Ceil, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const ceilImpl = createSimpleUnaryImpl((xi) => Math.ceil(xi));\nexport const ceil = unaryKernelFuncFromImpl(Ceil, ceilImpl);\n\nexport const ceilConfig: KernelConfig = {\n  kernelName: Ceil,\n  backendName: 'cpu',\n  kernelFunc: ceil,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, BackendValues, DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function concatImpl(\n    inputs: Array<{vals: BackendValues, shape: number[]}>, outShape: number[],\n    dtype: DataType, simplyConcat: boolean): TypedArray|string[] {\n  const outVals = util.getArrayFromDType(dtype, util.sizeFromShape(outShape));\n\n  if (simplyConcat && dtype !== 'string') {\n    // Use built-in TypedArray.set() method for speed.\n    let offset = 0;\n    inputs.forEach(input => {\n      const size = util.sizeFromShape(input.shape);\n\n      (outVals as TypedArray).set(input.vals as TypedArray, offset);\n      offset += size;\n    });\n  } else {\n    let colOffset = 0;\n\n    inputs.forEach(input => {\n      const decodedData = dtype === 'string' ?\n          backend_util.fromUint8ToStringArray(input.vals as Uint8Array[]) :\n          input.vals as TypedArray;\n\n      let tIdx = 0;\n\n      for (let row = 0; row < input.shape[0]; ++row) {\n        const resIdx = row * outShape[1] + colOffset;\n        for (let col = 0; col < input.shape[1]; ++col) {\n          outVals[resIdx + col] = decodedData[tIdx++];\n        }\n      }\n\n      colOffset += input.shape[1];\n    });\n  }\n\n  return outVals;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Equal, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const equalImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a === b) ? 1 : 0);\nexport const equal =\n    binaryKernelFunc(Equal, equalImpl, null /* complexImpl */, 'bool');\n\nexport const equalConfig: KernelConfig = {\n  kernelName: Equal,\n  backendName: 'cpu',\n  kernelFunc: equal\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Exp, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const expImpl = createSimpleUnaryImpl((xi) => Math.exp(xi));\nexport const exp = unaryKernelFuncFromImpl(Exp, expImpl, 'float32');\n\nexport const expConfig: KernelConfig = {\n  kernelName: Exp,\n  backendName: 'cpu',\n  kernelFunc: exp,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Expm1, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const expm1Impl = createSimpleUnaryImpl((xi) => Math.expm1(xi));\nexport const expm1 = unaryKernelFuncFromImpl(Expm1, expm1Impl);\n\nexport const expm1Config: KernelConfig = {\n  kernelName: Expm1,\n  backendName: 'cpu',\n  kernelFunc: expm1,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Floor, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const floorImpl = createSimpleUnaryImpl((xi) => Math.floor(xi));\nexport const floor = unaryKernelFuncFromImpl(Floor, floorImpl);\n\nexport const floorConfig: KernelConfig = {\n  kernelName: Floor,\n  backendName: 'cpu',\n  kernelFunc: floor,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {FloorDiv, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const floorDivImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => Math.floor(a / b));\nexport const floorDiv =\n    binaryKernelFunc(FloorDiv, floorDivImpl, null /* complexImpl */, 'int32');\n\nexport const floorDivConfig: KernelConfig = {\n  kernelName: FloorDiv,\n  backendName: 'cpu',\n  kernelFunc: floorDiv\n};\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, DataType, Rank, TensorBuffer, TypedArray} from '@tensorflow/tfjs-core';\n\nexport function gatherNdImpl<R extends Rank>(\n    indicesData: TypedArray, paramsBuf: TensorBuffer<R>, dtype: DataType,\n    numSlices: number, sliceRank: number, sliceSize: number, strides: number[],\n    paramsShape: number[], paramsSize: number): TensorBuffer<R> {\n  const outBuf = buffer([numSlices, sliceSize], dtype);\n\n  for (let i = 0; i < numSlices; i++) {\n    const index = [];\n    let flattenIndex = 0;\n    for (let j = 0; j < sliceRank; j++) {\n      const dim = indicesData[i * sliceRank + j];\n      flattenIndex += dim * strides[j];\n      index.push(dim);\n    }\n    if (flattenIndex < 0 || flattenIndex >= paramsSize / sliceSize) {\n      throw new Error(\n          `Invalid indices: ${index} does not index into ${paramsShape}`);\n    }\n\n    for (let k = 0; k < sliceSize; k++) {\n      outBuf.values[i * sliceSize + k] =\n          paramsBuf.get(...paramsBuf.indexToLoc(flattenIndex * sliceSize + k));\n    }\n  }\n\n  return outBuf as TensorBuffer<R>;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, DataType, Rank, TensorBuffer} from '@tensorflow/tfjs-core';\n\nexport function gatherV2Impl<R extends Rank, D extends DataType>(\n    xBuf: TensorBuffer<R, D>, indicesBuf: TensorBuffer<R, D>,\n    flattenOutputShape: number[]): TensorBuffer<R, D> {\n  const outBuf = buffer(flattenOutputShape, xBuf.dtype);\n  for (let i = 0; i < outBuf.size; ++i) {\n    const newLoc = outBuf.indexToLoc(i);\n\n    const originalLoc: number[] = newLoc.slice();\n    const batchIdx = originalLoc[0];\n    const indicesIdx = originalLoc[2];\n    const indicesIndex = indicesBuf.locToIndex([batchIdx, indicesIdx]);\n    originalLoc[2] = indicesBuf.values[indicesIndex] as number;\n\n    const originalIndex = xBuf.locToIndex(originalLoc);\n\n    if (0 <= originalIndex && originalIndex < xBuf.values.length) {\n      outBuf.values[i] = xBuf.values[originalIndex];\n    } // Else, index is out of bounds, so leave the default zero val in outBuf.\n  }\n\n  return outBuf as TensorBuffer<R, D>;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {Greater, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const greaterImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a > b) ? 1 : 0);\nexport const greater =\n    binaryKernelFunc(Greater, greaterImpl, null /* complexImpl */, 'bool');\n\nexport const greaterConfig: KernelConfig = {\n  kernelName: Greater,\n  backendName: 'cpu',\n  kernelFunc: greater\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {GreaterEqual, KernelConfig} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const greaterEqualImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a >= b) ? 1 : 0);\nexport const greaterEqual = binaryKernelFunc(\n    GreaterEqual, greaterEqualImpl, null /* complexImpl */, 'bool');\n\nexport const greaterEqualConfig: KernelConfig = {\n  kernelName: GreaterEqual,\n  backendName: 'cpu',\n  kernelFunc: greaterEqual\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Less} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const lessImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a < b) ? 1 : 0);\nexport const less =\n    binaryKernelFunc(Less, lessImpl, null /* complexImpl */, 'bool');\n\nexport const lessConfig: KernelConfig = {\n  kernelName: Less,\n  backendName: 'cpu',\n  kernelFunc: less\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, LessEqual} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const lessEqualImpl =\n    createSimpleBinaryKernelImpl((a: number, b: number) => (a <= b) ? 1 : 0);\nexport const lessEqual =\n    binaryKernelFunc(LessEqual, lessEqualImpl, null /* complexImpl */, 'bool');\n\nexport const lessEqualConfig: KernelConfig = {\n  kernelName: LessEqual,\n  backendName: 'cpu',\n  kernelFunc: lessEqual\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function linSpaceImpl(\n    start: number, stop: number, num: number): TypedArray {\n  const step = (stop - start) / (num - 1);\n\n  const values = util.makeZerosTypedArray(num, 'float32');\n  values[0] = start;\n  for (let i = 1; i < values.length; i++) {\n    values[i] = values[i - 1] + step;\n  }\n\n  return values;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Log} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const logImpl = createSimpleUnaryImpl((xi) => Math.log(xi));\nexport const log = unaryKernelFuncFromImpl(Log, logImpl);\n\nexport const logConfig: KernelConfig = {\n  kernelName: Log,\n  backendName: 'cpu',\n  kernelFunc: log,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, NumericDataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function maxImpl(\n    aVals: TypedArray, reduceSize: number, outShape: number[],\n    dtype: DataType): TypedArray {\n  const vals = util.getTypedArrayFromDType(\n      dtype as NumericDataType, util.sizeFromShape(outShape));\n\n  for (let i = 0; i < vals.length; ++i) {\n    const offset = i * reduceSize;\n    let max = aVals[offset];\n    for (let j = 0; j < reduceSize; ++j) {\n      const value = aVals[offset + j];\n      if (Number.isNaN(value) ||\n          value > max) {  // comparison with NaN always return false\n        max = value;\n      }\n    }\n    vals[i] = max;\n  }\n  return vals;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Maximum} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const maximumImpl = createSimpleBinaryKernelImpl(\n    ((aValue, bValue) => Math.max(aValue as number, bValue as number)));\nexport const maximum = binaryKernelFunc(Maximum, maximumImpl);\n\nexport const maximumConfig: KernelConfig = {\n  kernelName: Maximum,\n  backendName: 'cpu',\n  kernelFunc: maximum\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Minimum} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const minimumImpl = createSimpleBinaryKernelImpl(\n    ((aValue, bValue) => Math.min(aValue as number, bValue as number)));\nexport const minimum = binaryKernelFunc(Minimum, minimumImpl);\n\nexport const minimumConfig: KernelConfig = {\n  kernelName: Minimum,\n  backendName: 'cpu',\n  kernelFunc: minimum\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Multiply} from '@tensorflow/tfjs-core';\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc, createComplexBinaryKernelImpl} from '../utils/binary_utils';\n\nexport const multiplyImpl = createSimpleBinaryKernelImpl(\n    ((aValue: number, bValue: number) => aValue * bValue));\nexport const multiplyComplexImpl =\n    createComplexBinaryKernelImpl(((aReal, aImag, bReal, bImag) => {\n      return {\n        real: aReal * bReal - aImag * bImag,\n        imag: aReal * bImag + aImag * bReal\n      };\n    }));\n\nexport const multiply =\n    binaryKernelFunc(Multiply, multiplyImpl, multiplyComplexImpl);\n\nexport const multiplyConfig: KernelConfig = {\n  kernelName: Multiply,\n  backendName: 'cpu',\n  kernelFunc: multiply\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, KernelConfig, KernelFunc, Neg, TensorInfo, TypedArray, UnaryInputs, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {multiplyImpl} from './Multiply';\n\nexport function negImpl(xVals: TypedArray, xShape: number[], xDtype: DataType):\n    [TypedArray, number[]] {\n  const minusOne =\n      util.createScalarValue(-1 as unknown as 'float32', xDtype) as TypedArray;\n  return multiplyImpl([], xShape, minusOne, xVals, xDtype);\n}\n\nexport function neg(args: {inputs: UnaryInputs, backend: MathBackendCPU}):\n    TensorInfo {\n  const {inputs, backend} = args;\n  const {x} = inputs;\n\n  assertNotComplex(x, 'neg');\n\n  const xVals = backend.data.get(x.dataId).values as TypedArray;\n  const [res, newShape] = negImpl(xVals, x.shape, x.dtype);\n\n  return backend.makeTensorInfo(newShape, x.dtype, res);\n}\n\nexport const negConfig: KernelConfig = {\n  kernelName: Neg,\n  backendName: 'cpu',\n  kernelFunc: neg as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, NotEqual} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const notEqualImpl =\n    createSimpleBinaryKernelImpl(((a, b) => (a !== b) ? 1 : 0));\nexport const notEqual =\n    binaryKernelFunc(NotEqual, notEqualImpl, null /* complexOp */, 'bool');\n\nexport const notEqualConfig: KernelConfig = {\n  kernelName: NotEqual,\n  backendName: 'cpu',\n  kernelFunc: notEqual\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, NumericDataType, TypedArray} from '@tensorflow/tfjs-core';\nimport {util} from '@tensorflow/tfjs-core';\n\nexport function transposeImpl(\n    xVals: TypedArray, xShape: number[], dtype: DataType, perm: number[],\n    newShape: number[]): TypedArray {\n  const xRank = xShape.length;\n  const xSize = util.sizeFromShape(xShape);\n  const xStrides = util.computeStrides(xShape);\n  const newStrides = util.computeStrides(newShape);\n\n  const result = util.getTypedArrayFromDType(\n      dtype as NumericDataType, util.sizeFromShape(newShape));\n\n  for (let i = 0; i < xSize; ++i) {\n    const loc = util.indexToLoc(i, xRank, xStrides);\n\n    // Permute location.\n    const newLoc: number[] = new Array(loc.length);\n    for (let i = 0; i < newLoc.length; i++) {\n      newLoc[i] = loc[perm[i]];\n    }\n\n    const newIndex = util.locToIndex(newLoc, xRank, newStrides);\n    result[newIndex] = xVals[i];\n  }\n  return result;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, KernelFunc, TensorInfo, Transpose, TransposeAttrs, TransposeInputs, TypedArray} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nimport {transposeImpl} from './Transpose_impl';\n\nexport function transpose(args: {\n  inputs: TransposeInputs,\n  attrs: TransposeAttrs,\n  backend: MathBackendCPU\n}): TensorInfo {\n  const {inputs, attrs, backend} = args;\n  const {x} = inputs;\n  const {perm} = attrs;\n\n  assertNotComplex(x, 'transpose');\n\n  const xRank = x.shape.length;\n\n  const newShape: number[] = new Array(xRank);\n  for (let i = 0; i < newShape.length; i++) {\n    newShape[i] = x.shape[perm[i]];\n  }\n\n  const values = backend.data.get(x.dataId).values as TypedArray;\n  const result = transposeImpl(values, x.shape, x.dtype, perm, newShape);\n\n  const dataId = backend.write(result, newShape, x.dtype);\n  return {dataId, shape: newShape, dtype: x.dtype};\n}\n\nexport const transposeConfig: KernelConfig = {\n  kernelName: Transpose,\n  backendName: 'cpu',\n  kernelFunc: transpose as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, KernelConfig, KernelFunc, Prod, ProdAttrs, ProdInputs, TensorInfo, TypedArray, upcastType, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\nimport {transpose} from './Transpose';\n\nexport function prodImpl(\n    xShape: number[], xDtype: DataType, xVals: TypedArray,\n    reductionAxes: number[]):\n    {outVals: TypedArray, outShape: number[], outDtype: DataType} {\n  const [outShape, reduceShape] =\n      backend_util.computeOutAndReduceShapes(xShape, reductionAxes);\n  const outDtype = upcastType(xDtype, 'int32');\n  const outVals = util.makeZerosTypedArray(\n                      util.sizeFromShape(outShape), outDtype) as TypedArray;\n  const reduceSize = util.sizeFromShape(reduceShape);\n\n  for (let i = 0; i < outVals.length; ++i) {\n    const offset = i * reduceSize;\n    let prod = 1;\n    for (let j = 0; j < reduceSize; ++j) {\n      prod *= xVals[offset + j];\n    }\n    outVals[i] = prod;\n  }\n\n  return {outVals, outShape, outDtype};\n}\n\nexport function prod(\n    args: {inputs: ProdInputs, backend: MathBackendCPU, attrs: ProdAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {axis, keepDims} = attrs;\n\n  assertNotComplex(x, 'prod');\n\n  const xRank = x.shape.length;\n  const axes = util.parseAxisParam(axis, x.shape);\n\n  const permutation = backend_util.getAxesPermutation(axes, xRank);\n  let reductionAxes = axes;\n  let permutedX = x;\n  const intermediateTensorInfos = [];\n  if (permutation != null) {\n    permutedX = transpose({inputs: {x}, backend, attrs: {perm: permutation}});\n    intermediateTensorInfos.push(permutedX);\n    reductionAxes = backend_util.getInnerMostAxes(reductionAxes.length, xRank);\n  }\n\n  const xVals = backend.data.get(permutedX.dataId).values as TypedArray;\n  const {outVals, outShape, outDtype} =\n      prodImpl(permutedX.shape, permutedX.dtype, xVals, reductionAxes);\n\n  let resultShape = outShape;\n  if (keepDims) {\n    resultShape = backend_util.expandShapeToKeepDim(outShape, axes);\n  }\n\n  intermediateTensorInfos.forEach(\n      t => backend.disposeIntermediateTensorInfo(t));\n\n  return backend.makeTensorInfo(resultShape, outDtype, outVals);\n}\n\nexport const prodConfig: KernelConfig = {\n  kernelName: Prod,\n  backendName: 'cpu',\n  kernelFunc: prod as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nfunction validateIndices(\n    indices: TypedArray, indicesShape: number[], numParams: number) {\n  indices.forEach((index: number, i: number) => {\n    if (index < 0 || index >= numParams) {\n      const locString =\n          util.indexToLoc(\n                  i, indicesShape.length, util.computeStrides(indicesShape))\n              .join(',');\n      throw new Error(\n          `indices[${locString}] = ${index} is not in [0, ${numParams})`);\n    }\n  });\n}\n\nfunction validateSplits(\n    paramsNestedSplits: TypedArray[], numParamsDenseValues: number) {\n  // Validate\n  for (let dim = 0; dim < paramsNestedSplits.length; ++dim) {\n    const splits = paramsNestedSplits[dim];\n    const lastSplit = (dim === paramsNestedSplits.length - 1) ?\n        numParamsDenseValues :\n        paramsNestedSplits[dim + 1].length;\n    if (splits.length === 0) {\n      throw new Error('Ragged splits may not be empty');\n    }\n    if (splits[0] < 0) {\n      throw new Error('Ragged splits must be non-negative');\n    }\n    if (splits[splits.length - 1] > lastSplit) {\n      throw new Error('Ragged splits must not point past values');\n    }\n    for (let i = 1; i < splits.length; ++i) {\n      if (splits[i - 1] > splits[i]) {\n        throw new Error('Ragged splits must be sorted in ascending order');\n      }\n    }\n  }\n}\n\n// Construct the `splits` output tensors, encoded using a nested vector.\n// Also find the slices of values that need to be copied, and store them\n// in `valueSlices`.  The total number of values that will be copied (which\n// we need for allocating the output values tensor) is stored in `numValues`.\nfunction makeSplits(\n    indices: TypedArray, indicesShape: number[],\n    paramsNestedSplits: TypedArray[], numParamsDenseValues: number) {\n  const valueSlices: Array<[number, number]> = [];\n  let numValues = 0;\n\n  const numSplits = indicesShape.length - 1 + paramsNestedSplits.length;\n  const outSplits = new Array(numSplits).fill(null).map(() => [0]);\n\n  validateSplits(paramsNestedSplits, numParamsDenseValues);\n\n  // Add `splits` that come from all but the last dimension of the dense\n  // Tensor `indices`.  In particular, for each dimension D, we add a\n  // splits tensor whose values are:\n  //   range(reduceProd(splits.shape[:D]) + 1) * splits.shape[D+1]\n  // E.g., if indices.shape=[2, 3, 4] then we will add splits tensors:\n  //   [0, 3, 6]                    # length=2+1, stride=3\n  //   [0, 4, 8, 12, 16, 20, 24]    # length=2*3+1, stride=4\n  let nrows = 1;\n  for (let dim = 0; dim < indicesShape.length - 1; ++dim) {\n    nrows *= indicesShape[dim];\n    const rowLength = indicesShape[dim + 1];\n    for (let i = 1; i < nrows + 1; ++i) {\n      outSplits[dim].push(i * rowLength);\n    }\n  }\n\n  // Add `splits` that come from `paramsNestedSplits`.  Starting with the\n  // outermost ragged dimension (i.e., the first `splits` tensor), we work\n  // our way in, finding the range of values that should be copied.  As we\n  // go, we update the output `splits` for each dimension with the appropriate\n  // values.  In particular, the *lengths* of the slices from `param_splits`\n  // should be copied to generate corresponding slice lengths in the output\n  // splits.  E.g., if we are copying a ragged row with length 4, then we\n  // should add a new split point to outSplits that is 4 greater than the\n  // previous split point in outSplits.\n  for (let i = 0; i < indices.length; ++i) {\n    let start = indices[i];\n    let limit = indices[i] + 1;\n\n    // Copy splits.\n    for (let dim = 0; dim < paramsNestedSplits.length; ++dim) {\n      const splits = paramsNestedSplits[dim];\n      const outDim = dim + indicesShape.length - 1;\n      if (outDim >= 0) {\n        const outSplitsOutDim = outSplits[outDim];\n        const delta =\n            outSplitsOutDim[outSplitsOutDim.length - 1] - splits[start];\n        for (let j = start; j < limit; ++j) {\n          outSplits[outDim].push(splits[j + 1] + delta);\n        }\n      }\n      start = splits[start];\n      limit = splits[limit];\n    }\n    if (limit !== start) {\n      valueSlices.push([start, limit]);\n      numValues += limit - start;\n    }\n  }\n\n  return {outSplits, valueSlices, numValues};\n}\n\nfunction getSplits(outSplits: number[][]) {\n  const splitsOut: TypedArray[] = [];\n  for (let i = 0; i < outSplits.length; ++i) {\n    const numSplits = outSplits[i].length;\n    const splits = util.getArrayFromDType('int32', numSplits) as TypedArray;\n    splitsOut.push(splits);\n\n    outSplits[i].forEach((value, j: number) => splits[j] = value);\n  }\n\n  return splitsOut;\n}\n\nfunction computeFlatOuterDims(orig: number[], numOutDims: number) {\n  const outDims = orig.slice(0, numOutDims);\n  while (outDims.length < numOutDims) {\n    outDims.push(1);\n  }\n\n  for (let inDim = numOutDims; inDim < orig.length; inDim++) {\n    outDims[numOutDims - 1] *= orig[inDim];\n  }\n\n  return outDims;\n}\n// For each slice in `(start, limit)` in `valueSlices`, append\n// `paramsDenseValues[start,...,limit] to `values`.  `valueSize` indicates\n// the number of scalars contained in each value paramsDenseValues[i].\nfunction writeValueSlices(\n    paramsDenseValues: TypedArray, paramsDenseValuesShape: number[],\n    valueSlices: Array<[number, number]>, valueSize: number, values: TypedArray,\n    valuesShape: number[]) {\n  const denseM = computeFlatOuterDims(paramsDenseValuesShape, 2)[1];\n  const valuesM = computeFlatOuterDims(valuesShape, 2)[1];\n\n  let outPos = 0;\n  for (const slice of valueSlices) {\n    for (let i = slice[0]; i < slice[1]; ++i) {\n      for (let j = 0; j < valueSize; ++j) {\n        values[outPos * valuesM + j] = paramsDenseValues[i * denseM + j];\n      }\n      ++outPos;\n    }\n  }\n}\n\nfunction getValues(\n    paramsDenseValues: TypedArray, paramsDenseValuesShape: number[],\n    paramsDenseValuesDType: DataType, valueSlices: Array<[number, number]>,\n    numValues: number): [TypedArray, number[]] {\n  const valuesShape = paramsDenseValuesShape.slice();\n  valuesShape[0] = numValues;\n\n  const valuesOut = util.getArrayFromDType(\n                        paramsDenseValuesDType,\n                        util.sizeFromShape(valuesShape)) as TypedArray;\n\n  const numElements = paramsDenseValues.length;\n  const valueSize =\n      numElements === 0 ? 0 : (numElements / paramsDenseValuesShape[0]);\n  writeValueSlices(\n      paramsDenseValues, paramsDenseValuesShape, valueSlices, valueSize,\n      valuesOut, valuesShape);\n\n  return [valuesOut, valuesShape];\n}\nexport function raggedGatherImpl(\n    paramsNestedSplits: TypedArray[], paramsNestedSplitsShapes: number[][],\n    paramsDenseValues: TypedArray, paramsDenseValuesShape: number[],\n    paramsDenseValuesDType: DataType, indices: TypedArray,\n    indicesShape: number[],\n    outputRaggedRank: number): [TypedArray[], TypedArray, number[]] {\n  if (paramsNestedSplits.length === 0) {\n    throw new Error('paramsNestedSplits must be non empty');\n  }\n\n  if (paramsNestedSplitsShapes[0].length === 0) {\n    throw new Error('Split tensors must not be scalars');\n  }\n  const numParams = paramsNestedSplitsShapes[0][0] - 1;\n  validateIndices(indices, indicesShape, numParams);\n\n  if (paramsDenseValuesShape.length === 0) {\n    throw new Error('params.rank must be nonzero');\n  }\n  const numParamsDenseValues = paramsDenseValuesShape[0];\n\n  // Calculate the `splits`, and store the value slices that we need to\n  // copy in `valueSlices`.\n  const {outSplits, valueSlices, numValues} = makeSplits(\n      indices, indicesShape, paramsNestedSplits, numParamsDenseValues);\n\n  // Write the output tensors.\n  const outputNestedSplits = getSplits(outSplits);\n  const outputDenseValues = getValues(\n      paramsDenseValues, paramsDenseValuesShape, paramsDenseValuesDType,\n      valueSlices, numValues);\n\n  return [outputNestedSplits, outputDenseValues[0], outputDenseValues[1]];\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nconst INT32_MAX = **********;\n\nexport function raggedRangeImpl(\n    starts: TypedArray, startsShape: number[], startsDType: DataType,\n    limits: TypedArray, limitsShape: number[], deltas: TypedArray,\n    deltasShape: number[]): [TypedArray, TypedArray] {\n  // Check input tensor shapes.\n  if (startsShape.length > 1) {\n    throw new Error('starts must be a scalar or vector');\n  }\n  if (limitsShape.length > 1) {\n    throw new Error('limits must be a scalar or vector');\n  }\n  if (deltasShape.length > 1) {\n    throw new Error('deltas must be a scalar or vector');\n  }\n\n  // Determine which tensors we need to broadcast.\n  const broadcastStarts = startsShape.length === 0;\n  const broadcastLimits = limitsShape.length === 0;\n  const broadcastDeltas = deltasShape.length === 0;\n\n  // nRows (number of output rows) is the size of the non-broadcast inputs,\n  // or 1 if all inputs are scalars.\n  const inSizes: number[] = [];\n  if (!broadcastStarts) {\n    inSizes.push(startsShape[0]);\n  }\n  if (!broadcastLimits) {\n    inSizes.push(limitsShape[0]);\n  }\n  if (!broadcastDeltas) {\n    inSizes.push(deltasShape[0]);\n  }\n\n  for (let i = 1; i < inSizes.length; ++i) {\n    if (inSizes[i] !== inSizes[i - 1]) {\n      throw new Error('starts, limits, and deltas must have the same shape');\n    }\n  }\n  const nRows = inSizes.length === 0 ? 1 : inSizes[0];\n\n  // Construct the rtNestedSplits tensor.\n  const rtNestedSplits =\n      util.getArrayFromDType('int32', nRows + 1) as TypedArray;\n  rtNestedSplits[0] = 0;\n  for (let row = 0; row < nRows; ++row) {\n    const start = broadcastStarts ? starts[0] : starts[row];\n    const limit = broadcastLimits ? limits[0] : limits[row];\n    const delta = broadcastDeltas ? deltas[0] : deltas[row];\n    if (delta === 0) {\n      throw new Error('Requires delta != 0');\n    }\n    let size: number;  // The number of elements in the specified range.\n    if (((delta > 0) && (limit < start)) || ((delta < 0) && (limit > start))) {\n      size = 0;\n    } else {\n      size = Math.ceil(Math.abs((limit - start) / delta));\n\n      if (size > INT32_MAX) {\n        throw new Error(`Requires ((limit - start) / delta) <= ${INT32_MAX}`);\n      }\n    }\n    rtNestedSplits[row + 1] = rtNestedSplits[row] + size;\n  }\n\n  const nVals = rtNestedSplits[nRows];\n\n  // Construct the rtDenseValues tensor.\n  const rtDenseValues =\n      util.getArrayFromDType(startsDType, nVals) as TypedArray;\n\n  let valueIndex = 0;\n  for (let row = 0; row < nRows; ++row) {\n    const rowSize = rtNestedSplits[row + 1] - rtNestedSplits[row];\n    let value = broadcastStarts ? starts[0] : starts[row];\n    const delta = broadcastDeltas ? deltas[0] : deltas[row];\n    for (let i = 0; i < rowSize; ++i) {\n      rtDenseValues[valueIndex++] = value;\n      value += delta;\n    }\n  }\n\n  return [rtNestedSplits, rtDenseValues];\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, broadcastTo, DataType, reshape, tidy, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport RowPartitionType = backend_util.RowPartitionType;\n// Based on\n// https://github.com/tensorflow/tensorflow/blob/master/tensorflow/core/kernels/ragged_tensor_to_tensor_op.cc\nclass RaggedTensorToTensorOp {\n  private readonly rowPartitionTypes: RowPartitionType[];\n  private readonly raggedRank: number;\n  constructor(\n      private shape: TypedArray, private shapeShape: number[],\n      private values: TypedArray, private valuesShape: number[],\n      private valuesDType: DataType, private defaultValue: TypedArray,\n      private defaultValueShape: number[],\n      private readonly rowPartitionValues: TypedArray[],\n      private readonly rowPartitionValuesShapes: number[][],\n      rowPartitionTypeStrings: string[]) {\n    this.rowPartitionTypes =\n        backend_util.getRowPartitionTypesHelper(rowPartitionTypeStrings);\n    this.raggedRank = backend_util.getRaggedRank(this.rowPartitionTypes);\n  }\n\n  private getRowPartitionTypeByDimension(dimension: number) {\n    if (this.rowPartitionTypes[0] === RowPartitionType.FIRST_DIM_SIZE) {\n      return this.rowPartitionTypes[dimension + 1];\n    } else {\n      return this.rowPartitionTypes[dimension];\n    }\n  }\n\n  // Returns the relationship between dimension and dimension + 1.\n  private getRowPartitionTensor(dimension: number) {\n    if (this.rowPartitionTypes[0] === RowPartitionType.FIRST_DIM_SIZE) {\n      return this.rowPartitionValues[dimension + 1];\n    } else {\n      return this.rowPartitionValues[dimension];\n    }\n  }\n\n  private getMaxWidth(dimension: number) {\n    const rowPartitionTensor = this.getRowPartitionTensor(dimension - 1);\n    switch (this.getRowPartitionTypeByDimension(dimension - 1)) {\n      case RowPartitionType.VALUE_ROWIDS:\n        return RaggedTensorToTensorOp.getMaxWidthValueRowID(rowPartitionTensor);\n      case RowPartitionType.ROW_SPLITS:\n        return RaggedTensorToTensorOp.getMaxWidthRowSplit(rowPartitionTensor);\n      default:\n        throw new Error(`Cannot handle partition type ${\n            RowPartitionType[this.getRowPartitionTypeByDimension(\n                dimension - 1)]}`);\n    }\n  }\n\n  static getMaxWidthRowSplit(rowSplit: TypedArray) {\n    const tensorLength = rowSplit.length;\n    if (tensorLength === 0 || tensorLength === 1) {\n      return 0;\n    }\n    let maxWidth = 0;\n    for (let i = 0; i < tensorLength - 1; ++i) {\n      const currentWidth = rowSplit[i + 1] - rowSplit[i];\n      if (currentWidth > maxWidth) {\n        maxWidth = currentWidth;\n      }\n    }\n    return maxWidth;\n  }\n\n  static getMaxWidthValueRowID(valueRowIds: TypedArray) {\n    const indexLength = valueRowIds.length;\n    if (indexLength === 0) {\n      return 0;\n    }\n    let firstEqualIndex = 0;\n    let firstEqualIndexValue = valueRowIds[0];\n    let maxWidth = 0;\n    for (let i = 1; i < indexLength; ++i) {\n      const value = valueRowIds[i];\n      if (value !== firstEqualIndexValue) {\n        firstEqualIndexValue = value;\n        maxWidth = Math.max(i - firstEqualIndex, maxWidth);\n        firstEqualIndex = i;\n      }\n    }\n    return Math.max(indexLength - firstEqualIndex, maxWidth);\n  }\n\n  private tensorShapeFromTensor(\n      t: TypedArray, tShape: number[], isPartial = true) {\n    if (tShape.length === 0) {\n      if (t[0] === -1) {\n        return [];\n      }\n      throw new Error(\n          `The only valid scalar shape tensor is the fully unknown shape specified as -1.`);\n    }\n    // MakePartialShape/MakeShapeHelper.\n    return makeShape(t, isPartial);\n  }\n\n  private calculateOutputSize(firstDim: number) {\n    const valueShape = this.valuesShape;\n    const defaultValueShape = this.defaultValueShape;\n\n    backend_util.validateDefaultValueShape(defaultValueShape, valueShape);\n\n    const shape = this.tensorShapeFromTensor(this.shape, this.shapeShape);\n    const outputShape = backend_util.combineRaggedTensorToTensorShapes(\n        this.raggedRank, shape, valueShape);\n\n    const result = outputShape;\n\n    if (result[0] < 0) {\n      result[0] = firstDim;\n    }\n    for (let i = 1; i <= this.raggedRank; ++i) {\n      if (result[i] < 0) {\n        result[i] = this.getMaxWidth(i);\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * The outputIndex represents the index in the output tensor\n   * where the first element of a particular dimension would be written.\n   * If it is -1, it indicates that the index is out of scope.\n   * Example, given firstDimension = 10, firstDimensionOutput = 6,\n   * and outputIndexMultiplier = 100:\n   * result = [0 100 200 300 400 500 -1 -1 -1 -1]\n   * If firstDimensionOutput = 11 instead, then:\n   * result = [0 100 200 300 400 500 600 700 800 900]\n   */\n  private calculateFirstParentOutputIndex(\n      firstDimension: number, outputIndexMultiplier: number,\n      firstDimensionOutput: number) {\n    const minDimension = Math.min(firstDimension, firstDimensionOutput);\n    const result: number[] = [];\n    let currentOutputIndex = 0;\n    for (let i = 0; i < minDimension;\n         ++i, currentOutputIndex += outputIndexMultiplier) {\n      result.push(currentOutputIndex);\n    }\n    for (let i = minDimension; i < firstDimension; ++i) {\n      result.push(-1);\n    }\n    util.assert(\n        result.length === firstDimension,\n        () => 'Final length of result must be equal to firstDimension.');\n\n    return result;\n  }\n\n  private calculateOutputIndexRowSplit(\n      rowSplit: TypedArray, parentOutputIndex: number[],\n      outputIndexMultiplier: number, outputSize: number) {\n    const rowSplitSize = rowSplit.length;\n    const result: number[] = [];\n    for (let i = 0; i < rowSplitSize - 1; ++i) {\n      const rowLength = rowSplit[i + 1] - rowSplit[i];\n      let realLength = Math.min(outputSize, rowLength);\n      let parentOutputIndexCurrent = parentOutputIndex[i];\n\n      if (parentOutputIndexCurrent === -1) {\n        realLength = 0;\n      }\n      for (let j = 0; j < realLength; ++j) {\n        result.push(parentOutputIndexCurrent);\n        parentOutputIndexCurrent += outputIndexMultiplier;\n      }\n      for (let j = 0; j < rowLength - realLength; ++j) {\n        result.push(-1);\n      }\n    }\n    if (rowSplitSize > 0 && result.length !== rowSplit[rowSplitSize - 1]) {\n      throw new Error('Invalid row split size.');\n    }\n\n    return result;\n  }\n\n  // Calculate the output index of the first element of a list.\n  // The parentOutputIndex is the same computation for the previous list.\n  // -1 indicates an element or list that is out of range.\n  // The outputIndexMultiplier is the number of output indices one moves\n  // forward for each column.\n  // E.g., given:\n  // valueRowIds:[0 1 2 2 2 3 5 5 6]\n  // parentOutputIndex:[1000 1100 2000 2100 -1 3000 4000]\n  // outputIndexMultiplier: 10\n  // outputSize: 2\n  // You get:\n  // result = [1000 1100 2000 2010 -1 2100 -1 -1 3000]\n  // result[0] = parentOutputIndex[valueRowIds[0]]\n  // result[1] = parentOutputIndex[valueRowIds[1]]\n  // result[2] = parentOutputIndex[valueRowIds[2]]\n  // result[3] = parentOutputIndex[valueRowIds[2] + 10]\n  // result[4] = -1 because it is the third element the size is 2.\n  // result[5] = parentOutputIndex[valueRowIds[3]]\n  // result[6] = -1 because parentOutputIndex[valueRowIds[6]] == -1\n  // result[7] = -1 because parentOutputIndex[valueRowIds[6]] == -1\n  // result[8] = parentOutputIndex[valueRowIds[7]]\n  private calculateOutputIndexValueRowID(\n      valueRowIds: TypedArray, parentOutputIndex: number[],\n      outputIndexMultiplier: number, outputSize: number) {\n    const indexSize = valueRowIds.length;\n    const result: number[] = [];\n    if (indexSize === 0) {\n      return [];\n    }\n\n    let currentOutputColumn = 0;\n    let currentValueRowId = valueRowIds[0];\n\n    if (currentValueRowId >= parentOutputIndex.length) {\n      throw new Error(\n          `Got currentValueRowId=${currentValueRowId}, which is not less than ${\n              parentOutputIndex.length}`);\n    }\n\n    let currentOutputIndex = parentOutputIndex[currentValueRowId];\n    result.push(currentOutputIndex);\n    for (let i = 1; i < indexSize; ++i) {\n      const nextValueRowId = valueRowIds[i];\n      if (nextValueRowId === currentValueRowId) {\n        if (currentOutputIndex >= 0) {\n          ++currentOutputColumn;\n          if (currentOutputColumn < outputSize) {\n            currentOutputIndex += outputIndexMultiplier;\n          } else {\n            currentOutputIndex = -1;\n          }\n        }\n      } else {\n        currentOutputColumn = 0;\n        currentValueRowId = nextValueRowId;\n\n        if (nextValueRowId >= parentOutputIndex.length) {\n          throw new Error(\n              `Got nextValueRowId=${nextValueRowId} which is not less than ${\n                  parentOutputIndex.length}`);\n        }\n\n        currentOutputIndex = parentOutputIndex[nextValueRowId];\n      }\n      result.push(currentOutputIndex);\n    }\n\n    if (result.length !== valueRowIds.length) {\n      throw new Error('Invalid row ids.');\n    }\n\n    return result;\n  }\n\n  private calculateOutputIndex(\n      dimension: number, parentOutputIndex: number[],\n      outputIndexMultiplier: number, outputSize: number) {\n    const rowPartitionTensor = this.getRowPartitionTensor(dimension);\n    const partitionType = this.getRowPartitionTypeByDimension(dimension);\n    switch (partitionType) {\n      case RowPartitionType.VALUE_ROWIDS:\n        return this.calculateOutputIndexValueRowID(\n            rowPartitionTensor, parentOutputIndex, outputIndexMultiplier,\n            outputSize);\n      case RowPartitionType.ROW_SPLITS:\n        if (rowPartitionTensor.length - 1 > parentOutputIndex.length) {\n          throw new Error(`Row partition size is greater than output size: ${\n              rowPartitionTensor.length - 1} > ${parentOutputIndex.length}`);\n        }\n        return this.calculateOutputIndexRowSplit(\n            rowPartitionTensor, parentOutputIndex, outputIndexMultiplier,\n            outputSize);\n      default:\n        throw new Error(\n            `Unsupported partition type: ${RowPartitionType[partitionType]}`);\n    }\n  }\n\n  private getFirstDimensionSize() {\n    const firstPartitionTensor = this.rowPartitionValues[0];\n    if (this.rowPartitionTypes.length === 0) {\n      throw new Error('No row_partition_types given.');\n    }\n    const firstPartitionType = this.rowPartitionTypes[0];\n    switch (firstPartitionType) {\n      case RowPartitionType.FIRST_DIM_SIZE:\n        return firstPartitionTensor[0];\n      case RowPartitionType.VALUE_ROWIDS:\n        throw new Error('Cannot handle VALUE_ROWIDS in first dimension.');\n      case RowPartitionType.ROW_SPLITS:\n        return this.rowPartitionValuesShapes[0][0] - 1;\n      default:\n        throw new Error(\n            `Cannot handle type ${RowPartitionType[firstPartitionType]}`);\n    }\n  }\n\n  compute(): [number[], TypedArray] {\n    const firstPartitionTensor = this.rowPartitionValues[0];\n    if (firstPartitionTensor.length <= 0) {\n      throw new Error(\n          'Invalid first partition input. ' +\n          'Tensor requires at least one element.');\n    }\n    const firstDimension = this.getFirstDimensionSize();\n    const outputSize = this.calculateOutputSize(firstDimension);\n    const multiplier: number[] = new Array(this.raggedRank + 1);\n\n    multiplier[multiplier.length - 1] = 1;\n    for (let i = multiplier.length - 2; i >= 0; --i) {\n      multiplier[i] = multiplier[i + 1] * outputSize[i + 1];\n    }\n    // Full size of the tensor.\n    const outputShape: number[] = makeShape(outputSize, false);\n    const outputTensor =\n        util.getArrayFromDType(\n            this.valuesDType, util.sizeFromShape(outputShape)) as TypedArray;\n\n    const fullSize = multiplier[0] * outputSize[0];\n    if (fullSize > 0) {\n      let outputIndex = this.calculateFirstParentOutputIndex(\n          firstDimension, multiplier[0], outputSize[0]);\n      for (let i = 1; i <= this.raggedRank; ++i) {\n        const newOutputIndex = this.calculateOutputIndex(\n            i - 1, outputIndex, multiplier[i], outputSize[i]);\n        outputIndex = newOutputIndex;\n      }\n\n      this.setOutput(this.raggedRank, outputIndex, outputTensor, outputShape);\n    }\n\n    return [outputShape, outputTensor];\n  }\n  setOutput(\n      raggedRank: number, outputIndex: number[], outputTensor: TypedArray,\n      outputShape: number[]) {\n    if (outputTensor.length === 0) {\n      return;\n    }\n\n    const valuesBase = this.values;\n    const outputBase = outputTensor;\n\n    let elementShape = outputShape.slice();\n    elementShape = elementShape.slice(raggedRank + 1);\n    const valueElementSize = util.sizeFromShape(elementShape);\n    const outputIndexSize = outputIndex.length;\n\n    // Broadcast the default value to value_element_size.  (We can skip this\n    // if defaultValueTensor.size == 1, since we use fill when that's true.)\n    let defaultValue = this.defaultValue;\n    if (defaultValue.length !== valueElementSize && defaultValue.length !== 1) {\n      const srcShape = this.defaultValueShape;\n      tidy(() => {\n        const defaultValueTensor = reshape(defaultValue, srcShape);\n        const bCastDefault = broadcastTo(defaultValueTensor, elementShape);\n        defaultValue = bCastDefault.dataSync();\n      });\n    }\n\n    // Loop through the outputIndex array, finding contiguous regions that\n    // should be copied.  Once we find the end of a contiguous region, copy it\n    // and add any necessary padding (with defaultValue).\n    let srcStart = 0;  // Start of contiguous region (in values)\n    let dstStart = 0;  // Destination for contiguous region (in output)\n    let dstEnd = 0;    // Destination for contiguous region (in output)\n    for (let srcI = 0; srcI <= outputIndexSize; ++srcI) {\n      // dstI is the destination where the value at srcI should be copied.\n      let dstI = srcI < outputIndexSize ? outputIndex[srcI] : -1;\n\n      // If we're still in a contiguous region, then update dstEnd go to the\n      // next srcI.\n      if (dstI === dstEnd) {\n        ++dstEnd;\n        continue;\n      }\n\n      // We found the end of contiguous region.  This can be because we found\n      // a gap (dstI > dstEnd), or a source value that shouldn't be copied\n      // because it's out-of-bounds (dstI == -1), or the end of the tensor\n      // (dstI === -1).\n      if (dstStart < dstEnd) {\n        // Copy the contiguous region.\n        const src = valuesBase.subarray(srcStart * valueElementSize);\n        const dst = outputBase.subarray(dstStart * valueElementSize);\n        const nVals = (dstEnd - dstStart) * valueElementSize;\n        copyArray(dst, src, nVals);\n      }\n\n      // Add any necessary padding (w/ defaultValue).\n      if (srcI >= outputIndexSize) {\n        // We reached the end of values: pad to the end of output.\n        const outputSize = outputTensor.length;\n        dstI = Math.floor(outputSize / valueElementSize);\n      }\n      if (dstI > dstEnd) {\n        if (this.defaultValue.length === 1) {\n          outputBase\n              .subarray(dstEnd * valueElementSize, dstI * valueElementSize)\n              .fill(this.defaultValue[0]);\n          dstEnd = dstI;\n        } else {\n          while (dstI > dstEnd) {\n            const dst = outputBase.slice(dstEnd * valueElementSize);\n            copyArray(dst, defaultValue, valueElementSize);\n            ++dstEnd;\n          }\n        }\n      }\n\n      // Update indices.\n      if (dstI < 0) {\n        // srcI should be skipped -- leave it out of the contiguous region.\n        srcStart = srcI + 1;\n        dstStart = dstEnd;\n      } else {\n        // srcI should be copied -- include it in the contiguous region.\n        srcStart = srcI;\n        dstStart = dstEnd;\n        dstEnd = dstStart + 1;\n      }\n    }\n  }\n}\n\nfunction copyArray(dst: TypedArray, src: TypedArray, size: number) {\n  for (let i = 0; i < size; i++) {\n    dst[i] = src[i];\n  }\n}\n\nfunction makeShape(shape: number[]|TypedArray, isPartial: boolean) {\n  const out: number[] = [];\n  for (let dim of shape) {\n    if (dim < 0) {\n      if (!isPartial) {\n        throw new Error(`Dimension ${dim} must be >= 0`);\n      }\n      if (dim < -1) {\n        throw new Error(`Dimension ${dim} must be >= -1`);\n      }\n      dim = -1;\n    }\n    out.push(dim);\n  }\n\n  return out;\n}\n\nexport function raggedTensorToTensorImpl(\n    shape: TypedArray, shapesShape: number[], values: TypedArray,\n    valuesShape: number[], valuesDType: DataType, defaultValue: TypedArray,\n    defaultValueShape: number[], rowPartitionValues: TypedArray[],\n    rowPartitionValuesShapes: number[][],\n    rowPartitionTypes: string[]): [number[], TypedArray] {\n  return new RaggedTensorToTensorOp(\n             shape, shapesShape, values, valuesShape, valuesDType, defaultValue,\n             defaultValueShape, rowPartitionValues, rowPartitionValuesShapes,\n             rowPartitionTypes)\n      .compute();\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {DataTypeMap, util} from '@tensorflow/tfjs-core';\n\nexport function rangeImpl(\n    start: number, stop: number, step: number,\n    dtype: 'float32'|'int32'): DataTypeMap['float32' | 'int32'] {\n  const sameStartStop = start === stop;\n  const increasingRangeNegativeStep = start < stop && step < 0;\n  const decreasingRangePositiveStep = stop < start && step > 1;\n\n  if (sameStartStop || increasingRangeNegativeStep ||\n      decreasingRangePositiveStep) {\n    return util.makeZerosTypedArray(0, dtype);\n  }\n\n  const numElements = Math.abs(Math.ceil((stop - start) / step));\n  const values = util.makeZerosTypedArray(numElements, dtype);\n\n  if (stop < start && step === 1) {\n    // Auto adjust the step's sign if it hasn't been set\n    // (or was set to 1)\n    step = -1;\n  }\n\n  values[0] = start;\n  for (let i = 1; i < values.length; i++) {\n    values[i] = values[i - 1] + step;\n  }\n  return values;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Rsqrt} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const rsqrtImpl = createSimpleUnaryImpl((xi) => 1 / Math.sqrt(xi));\nexport const rsqrt = unaryKernelFuncFromImpl(Rsqrt, rsqrtImpl);\n\nexport const rsqrtConfig: KernelConfig = {\n  kernelName: Rsqrt,\n  backendName: 'cpu',\n  kernelFunc: rsqrt,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\nimport {buffer, Rank, ShapeMap, TensorBuffer, TypedArray} from '@tensorflow/tfjs-core';\n\ninterface DefaultValueTypeMap {\n  bool: boolean;\n  int32: number;\n  float32: number;\n  string: string;\n}\n\nexport function\nscatterImpl<R extends Rank, D extends 'float32'|'int32'|'bool'|'string'>(\n    indices: TensorBuffer<R, 'int32'>, updates: TensorBuffer<R, D>,\n    shape: number[], outputSize: number, sliceSize: number, numUpdates: number,\n    sliceRank: number, strides: number[],\n    defaultValue: TensorBuffer<R, D>|DefaultValueTypeMap[D],\n    sumDupeIndices: boolean): TensorBuffer<R, D> {\n  const flattenShape = [outputSize / sliceSize, sliceSize];\n\n  const indicesData = indices.values as TypedArray;\n  const updatesData = updates.values;\n\n  if (outputSize === 0) {\n    return buffer(shape as ShapeMap[R], updates.dtype);\n  }\n\n  const outBuf = (defaultValue instanceof TensorBuffer) ?\n      defaultValue :\n      buffer(flattenShape, updates.dtype);\n  if (typeof defaultValue === 'string') {\n    (outBuf.values as string[]).fill(defaultValue);\n  } else if (typeof defaultValue === 'number') {\n    (outBuf.values as TypedArray).fill(defaultValue);\n  } else if (typeof defaultValue === 'boolean') {\n    (outBuf.values as TypedArray).fill(+defaultValue);\n  }\n\n  for (let i = 0; i < numUpdates; i++) {\n    const index = [];\n    let flattenIndex = 0;\n    for (let j = 0; j < sliceRank; j++) {\n      const dim = indicesData[i * sliceRank + j];\n      index.push(dim);\n      flattenIndex += dim * strides[j];\n    }\n\n    if (flattenIndex < 0 || flattenIndex >= outputSize / sliceSize) {\n      throw new Error(`Invalid indices: ${index} does not index into ${shape}`);\n    }\n\n    for (let k = 0; k < sliceSize; k++) {\n      if (sumDupeIndices) {\n        (outBuf.values as TypedArray)[flattenIndex * sliceSize + k] +=\n            (updatesData as TypedArray)[i * sliceSize + k];\n      } else {\n        outBuf.values[flattenIndex * sliceSize + k] = updates.rank === 0 ?\n            updatesData[0] :\n            updatesData[i * sliceSize + k];\n      }\n    }\n  }\n\n  return outBuf as TensorBuffer<R, D>;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Sigmoid} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const sigmoidImpl =\n    createSimpleUnaryImpl((xi) => 1 / (1 + Math.exp(-xi)));\nexport const sigmoid =\n    unaryKernelFunc(Sigmoid, (xi) => 1 / (1 + Math.exp(-xi)));\n\nexport const sigmoidConfig: KernelConfig = {\n  kernelName: Sigmoid,\n  backendName: 'cpu',\n  kernelFunc: sigmoid,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, BackendValues, buffer, DataType, KernelConfig, KernelFunc, Slice, slice_util, SliceAttrs, SliceInputs, TensorInfo, TypedArray, util} from '@tensorflow/tfjs-core';\n\nimport {MathBackendCPU} from '../backend_cpu';\nimport {assertNotComplex} from '../cpu_util';\n\nexport function sliceImpl(\n    vals: BackendValues, begin: number[], size: number[], shape: number[],\n    dtype: DataType): BackendValues {\n  const isContinous = slice_util.isSliceContinous(shape, begin, size);\n  const length = util.sizeFromShape(size);\n  const xStrides = util.computeStrides(shape);\n\n  if (isContinous) {\n    const flatOffset = slice_util.computeFlatOffset(begin, xStrides);\n\n    if (dtype === 'string') {\n      return (vals as Uint8Array[]).slice(flatOffset, flatOffset + length);\n    }\n\n    return (vals as TypedArray).subarray(flatOffset, flatOffset + length);\n  }\n\n  const decodedData = dtype === 'string' ?\n      backend_util.fromUint8ToStringArray(vals as Uint8Array[]) :\n      vals as TypedArray;\n\n  const inBuf = buffer(shape, dtype, decodedData);\n  const outBuf = buffer(size, dtype);\n  for (let i = 0; i < outBuf.size; ++i) {\n    const outLoc = outBuf.indexToLoc(i);\n    const inLoc = outLoc.map((idx: number, j) => idx + begin[j]);\n    outBuf.set(inBuf.get(...inLoc), ...outLoc);\n  }\n\n  if (dtype === 'string') {\n    return backend_util.fromStringArrayToUint8(outBuf.values as string[]);\n  }\n  return outBuf.values as TypedArray;\n}\n\nexport function slice(\n    args: {inputs: SliceInputs, backend: MathBackendCPU, attrs: SliceAttrs}):\n    TensorInfo {\n  const {inputs, backend, attrs} = args;\n  const {x} = inputs;\n  const {begin, size} = attrs;\n\n  assertNotComplex(x, 'slice');\n\n  const [$begin, $size] = slice_util.parseSliceParams(x, begin, size);\n  slice_util.assertParamsValid(x, $begin, $size);\n\n  const vals = backend.data.get(x.dataId).values;\n  const outVals = sliceImpl(vals, $begin, $size, x.shape, x.dtype);\n  return backend.makeTensorInfo($size, x.dtype, outVals);\n}\n\nexport const sliceConfig: KernelConfig = {\n  kernelName: Slice,\n  backendName: 'cpu',\n  kernelFunc: slice as unknown as KernelFunc\n};\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function sparseFillEmptyRowsImpl(\n    indices: TypedArray, indicesShape: number[], indicesDType: DataType,\n    values: TypedArray, valuesDType: DataType, denseShape: TypedArray,\n    defaultValue: number):\n    [TypedArray, number[], TypedArray, boolean[], number[]] {\n  const indicesCount = indicesShape[0];\n  const denseRows = denseShape[0];\n\n  const emptyRowIndicator: boolean[] = new Array(denseRows);\n  const reverseIndexMap: number[] = new Array(indicesCount);\n\n  const rank = indicesShape[1];\n\n  if (denseRows === 0) {\n    if (indicesCount !== 0) {\n      throw new Error(\n          backend_util.getSparseFillEmptyRowsIndicesDenseShapeMismatch(\n              indicesCount));\n    }\n    const outputIndices = util.getArrayFromDType(indicesDType, 0) as TypedArray;\n    const outputValues = util.getArrayFromDType(valuesDType, 0) as TypedArray;\n    return [\n      outputIndices, [0, rank], outputValues, emptyRowIndicator, reverseIndexMap\n    ];\n  }\n\n  let rowsAreOrdered = true;\n  let lastIndicesRow = 0;\n  const csrOffset: number[] = new Array(denseRows).fill(0);\n\n  for (let i = 0; i < indicesCount; ++i) {\n    // indices is a 2d tensor with shape of [N, rank]\n    const row = indices[i * rank];\n    if (row < 0) {\n      throw new Error(\n          backend_util.getSparseFillEmptyRowsNegativeIndexErrorMessage(i, row));\n    }\n    if (row >= denseRows) {\n      throw new Error(\n          backend_util.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(\n              i, row, denseRows));\n    }\n    ++csrOffset[row];\n    rowsAreOrdered = rowsAreOrdered && (row >= lastIndicesRow);\n    lastIndicesRow = row;\n  }\n\n  let allRowsFull = true;\n  for (let row = 0; row < denseRows; ++row) {\n    // csrOffset here describes the number of elements in this dense row\n    const rowEmpty = (csrOffset[row] === 0);\n    emptyRowIndicator[row] = rowEmpty;\n    allRowsFull = allRowsFull && !rowEmpty;\n    // In filled version, each row has at least one element.\n    csrOffset[row] = Math.max(csrOffset[row], 1);\n    // Update csrOffset to represent the number of elements up to and\n    // including denseRows + 1:\n    //  csrOffset[0] == #{elements of row 0}\n    //  csrOffset[1] == #{elements of row 1} + #{elements of row 0}\n    //  ..\n    //  csrOffset[i] == starting index for elements in row i + 1.\n    if (row > 0) {\n      csrOffset[row] += csrOffset[row - 1];\n    }\n  }\n\n  if (allRowsFull && rowsAreOrdered) {\n    const outputIndices: TypedArray = indices;\n    const outputValues: TypedArray = values;\n    for (let i = 0; i < indicesCount; ++i) {\n      reverseIndexMap[i] = i;\n    }\n    return [\n      outputIndices, [indicesCount, rank], outputValues, emptyRowIndicator,\n      reverseIndexMap\n    ];\n  } else {\n    const fullIndicesCount = csrOffset[denseRows - 1];\n    const outputIndices =\n        util.getArrayFromDType(indicesDType, fullIndicesCount * rank) as\n        TypedArray;\n    const outputValues =\n        util.getArrayFromDType(valuesDType, fullIndicesCount) as TypedArray;\n    const filledCount: number[] = new Array(denseRows).fill(0);\n\n    // Fill in values for rows that are not missing\n    for (let i = 0; i < indicesCount; ++i) {\n      // indices is a 2d tensor with shape of [N, rank]\n      const row = indices[i * rank];\n      const offset = filledCount[row];\n      const outputI = ((row === 0) ? 0 : csrOffset[row - 1]) + offset;\n      filledCount[row]++;  // Increment the filled count for this row.\n      for (let j = 0; j < rank; ++j) {\n        // indices and outputIndices are 2d tensors with shape of [N, rank]\n        outputIndices[outputI * rank + j] = indices[i * rank + j];\n      }\n      outputValues[outputI] = values[i];\n      // We'll need this reverse index map to backprop correctly.\n      reverseIndexMap[i] = outputI;\n    }\n\n    // Fill in values for rows that are missing\n    for (let row = 0; row < denseRows; ++row) {\n      const rowCount = filledCount[row];\n      if (rowCount === 0) {  // We haven't filled this row\n        const startingIndex = (row === 0) ? 0 : csrOffset[row - 1];\n        // Remaining index values were set to zero already.\n        // Just need to set the row index in the right location.\n        // outputIndices is a 2d tensor with shape of [N, rank]\n        outputIndices[startingIndex * rank + 0] = row;\n        for (let col = 1; col < rank; ++col) {\n          outputIndices[startingIndex * rank + col] = 0;\n        }\n        outputValues[startingIndex] = defaultValue;\n      }\n    }\n    return [\n      outputIndices, [fullIndicesCount, rank], outputValues, emptyRowIndicator,\n      reverseIndexMap\n    ];\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function sparseReshapeImpl(\n    inputIndices: TypedArray, inputIndicesShape: number[], inputDType: DataType,\n    inputShape: number[],\n    targetShape: number[]): [TypedArray, number[], number[]] {\n  const denseSize = util.sizeFromShape(inputShape);\n  const nnz = inputIndicesShape[0];\n  const outputRank = targetShape.length;\n\n  // Compute the output shape. Determine product of specified dimensions, and\n  // find the index of the unspecified one.\n  const outputShape: number[] = [];\n  let product = 1;\n  let unknownIndex = -1;\n  for (let d = 0; d < outputRank; ++d) {\n    const size = targetShape[d];\n    if (size === -1) {\n      if (unknownIndex !== -1) {\n        throw new Error(\n            backend_util\n                .getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(\n                    unknownIndex, d));\n      }\n      unknownIndex = d;\n      outputShape.push(1);\n    } else {\n      if (size < 0) {\n        throw new Error(\n            backend_util.getSparseReshapeNegativeOutputDimErrorMessage(\n                d, size));\n      }\n      product *= size;\n      outputShape.push(size);\n    }\n  }\n  if (unknownIndex !== -1) {\n    if (product <= 0) {\n      throw new Error(\n          backend_util.getSparseReshapeEmptyTensorZeroOutputDimErrorMessage());\n    }\n    const missing = Math.trunc(denseSize / product);\n    if (product * missing !== denseSize) {\n      throw new Error(\n          backend_util.getSparseReshapeInputOutputMultipleErrorMessage(\n              inputShape, outputShape));\n    }\n\n    outputShape[unknownIndex] = missing;\n  }\n  const outputSize = util.sizeFromShape(outputShape);\n  if (outputSize !== denseSize) {\n    throw new Error(\n        backend_util.getSparseReshapeInputOutputMismatchErrorMessage(\n            inputShape, outputShape));\n  }\n\n  const inputRank = inputShape.length;\n  const inputStrides: number[] = [];\n  if (inputRank > 0) {\n    inputStrides[inputRank - 1] = 1;\n    for (let d = inputRank - 2; d >= 0; --d) {\n      inputStrides[d] = inputStrides[d + 1] * inputShape[d + 1];\n    }\n  }\n\n  const outputStrides: number[] = [];\n  if (outputRank > 0) {\n    outputStrides[outputRank - 1] = 1;\n    for (let d = outputRank - 2; d >= 0; --d) {\n      outputStrides[d] = outputStrides[d + 1] * outputShape[d + 1];\n    }\n  }\n\n  const newIndices =\n      util.getArrayFromDType(inputDType, nnz * outputRank) as TypedArray;\n  for (let i = 0; i < nnz; ++i) {\n    let id = 0;\n    for (let j = 0; j < inputRank; ++j) {\n      // inputIndices is a 2d tensor with shape of [nnz, inputRank]\n      id += inputIndices[i * inputRank + j] * inputStrides[j];\n    }\n    for (let j = 0; j < outputRank; ++j) {\n      // newIndices is a 2d tensor with shape of [nnz, outputRank]\n      newIndices[i * outputRank + j] = Math.trunc(id / outputStrides[j]);\n      id %= outputStrides[j];\n    }\n  }\n  return [newIndices, [nnz, outputRank], outputShape];\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {backend_util, DataType, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function sparseSegmentReductionImpl(\n    input: TypedArray, inputShape: number[], inputDType: DataType,\n    indices: TypedArray, segmentIds: TypedArray, isMean = false,\n    defaultValue = 0): [TypedArray, number[]] {\n  const numIndices = indices.length;\n\n  // Flatten the array to two dimensions\n  const inputFlat: number[] = [inputShape[0], input.length / inputShape[0]];\n  const numCol = inputFlat[1];\n  // Note that the current implementation assumes that segmentIds values are\n  // sorted.\n  const lastSegmentIdPlusOne =\n      numIndices > 0 ? segmentIds[numIndices - 1] + 1 : 0;\n  const outputRows = lastSegmentIdPlusOne;\n\n  if (outputRows < 0) {\n    throw new Error(\n        backend_util.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());\n  }\n\n  const outputShape = inputShape.slice();\n  outputShape[0] = outputRows;\n\n  const outputLength =\n      outputShape.reduce((product, value) => product * value, 1);\n  // Output array is initialized with the value 0 by default.\n  const output = util.getArrayFromDType(inputDType, outputLength) as TypedArray;\n\n  // Note that we do not initialize the output buffer with a default value, so\n  // we need to explicitly set missing indices to the default value.\n  if (numIndices === 0) {\n    if (outputRows > 0) {\n      output.fill(defaultValue);\n    }\n    return [output, outputShape];\n  }\n\n  if (outputRows <= 0) {\n    throw new Error(\n        backend_util.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());\n  }\n\n  let start = 0, end = 1;\n  // Index from which the output is not initialized.\n  let uninitializedIndex = 0;\n  let outIndex = segmentIds[start];\n\n  while (true) {\n    // We initialize nextIndex to 0 to avoid may be uninitialized warning\n    let nextIndex = 0;\n    if (end < numIndices) {\n      nextIndex = segmentIds[end];\n      if (outIndex === nextIndex) {\n        ++end;\n        continue;\n      }\n      // We have a new segment here.  Verify that the segment ids are growing.\n      if (outIndex >= nextIndex) {\n        throw new Error(backend_util\n            .getSparseSegmentReductionNonIncreasingSegmentIdsErrorMessage());\n      }\n    }\n\n    if (outIndex < 0 || outIndex >= outputRows) {\n      throw new Error(\n          backend_util.getSparseSegmentReductionSegmentIdOutOfRangeErrorMessage(\n              outIndex, outputRows));\n    }\n\n    // If there is a gap between two indices, we need to set that gap to the\n    // default value.\n    if (outIndex > uninitializedIndex) {\n      output.fill(defaultValue, uninitializedIndex * numCol, outIndex * numCol);\n    }\n\n    for (let i = start; i < end; ++i) {\n      const index = indices[i];\n      if (index < 0 || index >= inputFlat[0]) {\n        throw new Error(\n            backend_util.getSparseSegmentReductionIndicesOutOfRangeErrorMessage(\n                i, indices[i], inputFlat[0]));\n      }\n      for (let j = 0; j < numCol; j++) {\n        output[outIndex * numCol + j] += input[index * numCol + j];\n      }\n    }\n\n    if (isMean) {\n      for (let j = 0; j < numCol; j++) {\n        output[outIndex * numCol + j] /= end - start;\n      }\n    }\n\n    start = end;\n    ++end;\n    uninitializedIndex = outIndex + 1;\n    outIndex = nextIndex;\n    if (end > numIndices) {\n      break;\n    }\n  }\n\n  // Fill the gap at the end with the default value.\n  if (uninitializedIndex < outputRows) {\n    output.fill(defaultValue, uninitializedIndex * numCol, outputRows * numCol);\n  }\n\n  return [output, outputShape];\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the License);\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an AS IS BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Sqrt} from '@tensorflow/tfjs-core';\n\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFunc} from '../utils/unary_utils';\n\nexport const sqrtImpl = createSimpleUnaryImpl((xi) => Math.sqrt(xi));\nexport const sqrt = unaryKernelFunc(Sqrt, (xi) => Math.sqrt(xi));\n\nexport const sqrtConfig: KernelConfig = {\n  kernelName: Sqrt,\n  backendName: 'cpu',\n  kernelFunc: sqrt,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, SquaredDifference} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc} from '../utils/binary_utils';\n\nexport const squaredDifferenceImpl =\n    createSimpleBinaryKernelImpl(((a: number, b: number) => {\n      const diff = a - b;\n      return diff * diff;\n    }));\nexport const squaredDifference =\n    binaryKernelFunc(SquaredDifference, squaredDifferenceImpl);\n\nexport const squaredDifferenceConfig: KernelConfig = {\n  kernelName: SquaredDifference,\n  backendName: 'cpu',\n  kernelFunc: squaredDifference\n};\n", "/**\n * @license\n * Copyright 2023 Google LLC.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, StaticRegexReplace, StaticRegexReplaceAttrs} from '@tensorflow/tfjs-core';\nimport {createSimpleUnaryImpl} from '../utils/unary_impl';\nimport {unaryKernelFuncFromImpl} from '../utils/unary_utils';\n\nexport const staticRegexReplaceImpl = createSimpleUnaryImpl<string,\n  string>((x: string, attrs) => {\n    const {pattern, replaceGlobal, rewrite} =\n      attrs as unknown as StaticRegexReplaceAttrs;\n    // TODO(mattSoulanille): Don't create a regex each time.\n    return x.replace(new RegExp(pattern, replaceGlobal ? 'g' : ''), rewrite);\n});\n\nconst staticRegexReplace =\n  unaryKernelFuncFromImpl(StaticRegexReplace, staticRegexReplaceImpl);\n\nexport const staticRegexReplaceConfig: KernelConfig = {\n  kernelName: StaticRegexReplace,\n  backendName: 'cpu',\n  kernelFunc: staticRegexReplace,\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, Rank, TensorBuffer} from '@tensorflow/tfjs-core';\n\nexport function stridedSliceImpl<R extends Rank>(\n    outShape: number[], xBuf: TensorBuffer<R>, strides: number[],\n    begin: number[]): TensorBuffer<R> {\n  const outBuf = buffer(outShape, xBuf.dtype);\n\n  for (let i = 0; i < outBuf.size; i++) {\n    const loc = outBuf.indexToLoc(i);\n\n    const newLoc: number[] = new Array(loc.length);\n    for (let j = 0; j < newLoc.length; j++) {\n      newLoc[j] = loc[j] * strides[j] + begin[j];\n    }\n    outBuf.set(xBuf.get(...newLoc), ...loc);\n  }\n\n  return outBuf as TensorBuffer<R>;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\n\n/**\n * The StringNGramsOp class creates ngrams from ragged string data.\n * The constructor contains all attributes related to the operation such as\n * padding widths and strings, and the compute function can be used to\n * compute the ngrams for different ragged tensor inputs.\n */\nclass StringNGramsOp {\n  private separator: Uint8Array;\n  private nGramWidths: number[];\n  private padWidth: number;\n  private leftPad: Uint8Array;\n  private rightPad: Uint8Array;\n  private preserveShort: boolean;\n\n  constructor(\n      separator: string, nGramWidths: number[], leftPad: string,\n      rightPad: string, padWidth: number, preserveShortSequences: boolean) {\n    this.separator = util.encodeString(separator);\n    this.nGramWidths = nGramWidths;\n    this.leftPad = util.encodeString(leftPad);\n    this.rightPad = util.encodeString(rightPad);\n    this.padWidth = padWidth;\n    this.preserveShort = preserveShortSequences;\n  }\n\n  private getPadWidth(nGramWidth: number) {\n    // Ngrams can be padded with either a fixed pad width or a dynamic pad\n    // width depending on the 'padWidth' arg, but in no case should the padding\n    // ever be wider than 'nGramWidth' - 1.\n    return Math.min(\n        this.padWidth < 0 ? nGramWidth - 1 : this.padWidth, nGramWidth - 1);\n  }\n\n  private getNumNGrams(length: number, nGramWidth: number) {\n    const padWidth = this.getPadWidth(nGramWidth);\n    return Math.max(0, ((length + 2 * padWidth) - nGramWidth) + 1);\n  }\n\n  private createNGrams(\n      data: Uint8Array[], splitIndex: number, output: Uint8Array[],\n      outputStartIndex: number, numNGrams: number, nGramWidth: number) {\n    for (let nGramIndex = 0; nGramIndex < numNGrams; ++nGramIndex) {\n      const padWidth = this.getPadWidth(nGramWidth);\n      const leftPadding = Math.max(0, padWidth - nGramIndex);\n      const rightPadding =\n          Math.max(0, padWidth - (numNGrams - (nGramIndex + 1)));\n      const numTokens = nGramWidth - (leftPadding + rightPadding);\n      const dataStartIndex =\n          splitIndex + (leftPadding > 0 ? 0 : nGramIndex - padWidth);\n\n      // Calculate the total expected size of the nGram so we can reserve the\n      // correct amount of space in the string.\n      let nGramSize = 0;\n      // Size of the left padding.\n      nGramSize += leftPadding * this.leftPad.length;\n      // Size of the tokens.\n      for (let n = 0; n < numTokens; ++n) {\n        nGramSize += data[dataStartIndex + n].length;\n      }\n      // Size of the right padding.\n      nGramSize += rightPadding * this.rightPad.length;\n      // Size of the separators.\n      const numSeparators = leftPadding + rightPadding + numTokens - 1;\n      nGramSize += numSeparators * this.separator.length;\n\n      // Build the nGram.\n      output[outputStartIndex + nGramIndex] = new Uint8Array(nGramSize);\n      const nGram = output[outputStartIndex + nGramIndex];\n\n      let nextNGramIndex = 0;\n      const appendToNGram = (str: Uint8Array) =>\n          str.forEach((value) => nGram[nextNGramIndex++] = value);\n\n      for (let n = 0; n < leftPadding; ++n) {\n        appendToNGram(this.leftPad);\n        appendToNGram(this.separator);\n      }\n      // Only output first numTokens - 1 pairs of data and separator\n      for (let n = 0; n < numTokens - 1; ++n) {\n        appendToNGram(data[dataStartIndex + n]);\n        appendToNGram(this.separator);\n      }\n      // Handle case when there are no tokens or no right padding as these\n      // can result in consecutive separators.\n      if (numTokens > 0) {\n        // If we have tokens, then output last and then pair each separator\n        // with the right padding that follows, to ensure nGram ends either with\n        // the token or with the right pad.\n        appendToNGram(data[dataStartIndex + numTokens - 1]);\n        for (let n = 0; n < rightPadding; ++n) {\n          appendToNGram(this.separator);\n          appendToNGram(this.rightPad);\n        }\n      } else {\n        // If we don't have tokens, then the last item inserted into the nGram\n        // has been the separator from the left padding loop above. Hence,\n        // output right pad and separator and make sure to finish with a\n        // padding, not a separator.\n        for (let n = 0; n < rightPadding - 1; ++n) {\n          appendToNGram(this.rightPad);\n          appendToNGram(this.separator);\n        }\n        appendToNGram(this.rightPad);\n      }\n    }\n  }\n\n  // Data and splits together form the definition of the ragged tensor,\n  // where data is 1 dimensional and contains the values of the tensor\n  // and splits denotes the indices at which each row starts.\n  public compute(data: Uint8Array[], splits: Int32Array):\n      [Uint8Array[], Int32Array] {\n    // Validate that the splits are valid indices into data, only if there are\n    // splits specified.\n    const inputDataSize = data.length;\n    const splitsSize = splits.length;\n    if (splitsSize > 0) {\n      let prevSplit = splits[0];\n      if (prevSplit !== 0) {\n        throw new Error(`First split value must be 0, got ${prevSplit}`);\n      }\n      for (let i = 1; i < splitsSize; ++i) {\n        let validSplits = splits[i] >= prevSplit;\n        validSplits = validSplits && (splits[i] <= inputDataSize);\n        if (!validSplits) {\n          throw new Error(`Invalid split value ${splits[i]}, must be in [${\n              prevSplit}, ${inputDataSize}]`);\n        }\n        prevSplit = splits[i];\n      }\n      if (prevSplit !== inputDataSize) {\n        throw new Error(`Last split value must be data size. Expected ${\n            inputDataSize}, got ${prevSplit}`);\n      }\n    }\n\n    const numBatchItems = splitsSize - 1;\n    const nGramsSplits = util.getArrayFromDType('int32', splitsSize);\n    // If there is no data or size, return an empty ragged tensor.\n    if (inputDataSize === 0 || splitsSize === 0) {\n      const empty: Uint8Array[] = new Array(inputDataSize);\n      for (let i = 0; i <= numBatchItems; ++i) {\n        nGramsSplits[i] = 0;\n      }\n      return [empty, nGramsSplits];\n    }\n\n    nGramsSplits[0] = 0;\n    for (let i = 1; i <= numBatchItems; ++i) {\n      const length = splits[i] - splits[i - 1];\n      let numNGrams = 0;\n      this.nGramWidths.forEach((nGramWidth) => {\n        numNGrams += this.getNumNGrams(length, nGramWidth);\n      });\n      if (this.preserveShort && length > 0 && numNGrams === 0) {\n        numNGrams = 1;\n      }\n      nGramsSplits[i] = nGramsSplits[i - 1] + numNGrams;\n    }\n\n    const nGrams: Uint8Array[] = new Array(nGramsSplits[numBatchItems]);\n\n    for (let i = 0; i < numBatchItems; ++i) {\n      const splitIndex = splits[i];\n      let outputStartIdx = nGramsSplits[i];\n      this.nGramWidths.forEach((nGramWidth) => {\n        const length = splits[i + 1] - splits[i];\n        const numNGrams = this.getNumNGrams(length, nGramWidth);\n        this.createNGrams(\n            data, splitIndex, nGrams, outputStartIdx, numNGrams, nGramWidth);\n        outputStartIdx += numNGrams;\n      });\n      // If we're preserving short sequences, check to see if no sequence was\n      // generated by comparing the current output start idx to the original\n      // one (nGramSplitsdata). If no ngrams were generated, then they will\n      // be equal (since we increment outputStartIdx by numNGrams every\n      // time we create a set of ngrams.)\n      if (this.preserveShort && outputStartIdx === nGramsSplits[i]) {\n        const dataLength = splits[i + 1] - splits[i];\n        // One legitimate reason to not have any ngrams when this.preserveShort\n        // is true is if the sequence itself is empty. In that case, move on.\n        if (dataLength === 0) {\n          continue;\n        }\n        // We don't have to worry about dynamic padding sizes here: if padding\n        // was dynamic, every sequence would have had sufficient padding to\n        // generate at least one nGram.\n        const nGramWidth = dataLength + 2 * this.padWidth;\n        const numNGrams = 1;\n        this.createNGrams(\n            data, splitIndex, nGrams, outputStartIdx, numNGrams, nGramWidth);\n      }\n    }\n    return [nGrams, nGramsSplits];\n  }\n}\n\nexport function stringNGramsImpl(\n    data: Uint8Array[], dataSplits: Int32Array, separator: string,\n    nGramWidths: number[], leftPad: string, rightPad: string, padWidth: number,\n    preserveShortSequences: boolean): [Uint8Array[], Int32Array] {\n  return new StringNGramsOp(\n             separator, nGramWidths, leftPad, rightPad, padWidth,\n             preserveShortSequences)\n      .compute(data, dataSplits);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TypedArray, util} from '@tensorflow/tfjs-core';\n\nfunction split(\n    str: Uint8Array, delimiters: Uint8Array, skipEmpty: boolean,\n    result: Uint8Array[]): void {\n  if (!str.length) {\n    return;\n  }\n  // When the delimiter is empty, the input is split into individual characters.\n  if (delimiters.length === 0) {\n    for (let i = 0; i < str.length; ++i) {\n      result.push(str.subarray(i, i + 1));\n    }\n    return;\n  }\n  // When there is one delimiter, the input is split only at that delimiter.\n  if (delimiters.length === 1) {\n    const delimiter = delimiters[0];\n    let f = str.indexOf(delimiter);\n    while (f !== -1) {\n      const token = str.subarray(0, f);\n      if (!skipEmpty || token.length !== 0) {\n        result.push(token);\n      }\n      str = str.subarray(f + 1);\n      f = str.indexOf(delimiter);\n    }\n    if (!skipEmpty || str.length !== 0) {\n      result.push(str);\n    }\n    return;\n  }\n  // When there are multiple delimiters, the input is split at every instance\n  // one of the delimiters appears.\n  let tokenStart = 0;\n  for (let i = 0; i < str.length + 1; i++) {\n    if ((i === str.length) || (delimiters.indexOf(str[i]) !== -1)) {\n      const token = str.subarray(tokenStart, i);\n      if (!skipEmpty || token.length !== 0) {\n        result.push(token);\n      }\n      tokenStart = i + 1;\n    }\n  }\n}\n\nexport function stringSplitImpl(\n    input: Uint8Array[], delimiter: Uint8Array,\n    skipEmpty: boolean): [TypedArray, Uint8Array[], [number, number]] {\n  const batchSize = input.length;\n\n  // Empty delimiter means split the input character by character.\n  const tokens: Uint8Array[] = [];\n\n  let outputSize = 0;\n  let maxNumEntries = 0;\n  const numIndices: number[] = new Array(batchSize);\n  for (let i = 0; i < batchSize; ++i) {\n    const prevTokensLength = tokens.length;\n    split(input[i], delimiter, skipEmpty, tokens);\n    const nEntries = tokens.length - prevTokensLength;\n    numIndices[i] = nEntries;\n    outputSize += nEntries;\n    maxNumEntries = Math.max(maxNumEntries, nEntries);\n  }\n\n  const indices = util.getArrayFromDType('int32', outputSize * 2) as TypedArray;\n  const values: Uint8Array[] = new Array(outputSize);\n  const shape: [number, number] = [batchSize, maxNumEntries];\n\n  let c = 0;\n  for (let i = 0; i < batchSize; ++i) {\n    for (let j = 0; j < numIndices[i]; ++j) {\n      // indices is a 2d tensor with shape of [outputSize, 2]\n      indices[c * 2] = i;\n      indices[c * 2 + 1] = j;\n      values[c] = tokens[c];\n      ++c;\n    }\n  }\n\n  return [indices, values, shape];\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function stringToHashBucketFastImpl(\n    input: Uint8Array[], numBuckets: number): TypedArray {\n  const output = util.getArrayFromDType('int32', input.length) as TypedArray;\n\n  for (let i = 0; i < input.length; ++i) {\n    output[i] =\n        util.fingerPrint64(input[i]).modulo(numBuckets).getLowBitsUnsigned();\n  }\n\n  return output;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {KernelConfig, Sub} from '@tensorflow/tfjs-core';\n\nimport {createSimpleBinaryKernelImpl} from '../utils/binary_impl';\nimport {binaryKernelFunc, createComplexBinaryKernelImpl} from '../utils/binary_utils';\n\nexport const subImpl = createSimpleBinaryKernelImpl(\n    ((aValue: number, bValue: number) => aValue - bValue));\nexport const subComplexImpl =\n    createComplexBinaryKernelImpl(((aReal, aImag, bReal, bImag) => {\n      return {real: aReal - bReal, imag: aImag - bImag};\n    }));\nexport const sub = binaryKernelFunc(Sub, subImpl, subComplexImpl);\n\nexport const subConfig: KernelConfig = {\n  kernelName: Sub,\n  backendName: 'cpu',\n  kernelFunc: sub\n};\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {buffer, DataType, Rank, TensorBuffer} from '@tensorflow/tfjs-core';\n\n/**\n * An implementation of the tile kernel shared between webgl and cpu for string\n * tensors only.\n */\n\nexport function tileImpl<R extends Rank>(\n    xBuf: TensorBuffer<R, DataType>,\n    reps: number[]): TensorBuffer<R, DataType> {\n  const newShape: number[] = new Array(xBuf.rank);\n  for (let i = 0; i < newShape.length; i++) {\n    newShape[i] = xBuf.shape[i] * reps[i];\n  }\n  const result = buffer(newShape, xBuf.dtype);\n  for (let i = 0; i < result.values.length; ++i) {\n    const newLoc = result.indexToLoc(i);\n\n    const originalLoc: number[] = new Array(xBuf.rank);\n    for (let j = 0; j < originalLoc.length; j++) {\n      originalLoc[j] = newLoc[j] % xBuf.shape[j];\n    }\n\n    const originalIndex = xBuf.locToIndex(originalLoc);\n\n    result.values[i] = xBuf.values[originalIndex];\n  }\n  return result as TensorBuffer<R, DataType>;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\n/** An implementation of the TopK kernel shared between webgl and cpu. */\n\nimport {buffer, NumericDataType, Rank, ShapeMap, Tensor, TensorBuffer, TypedArray, util} from '@tensorflow/tfjs-core';\n\ntype Pair = {\n  value: number,\n  index: number\n};\n\nconst comparePair = (a: Pair, b: Pair) => {\n  const valueDiff = b.value - a.value;\n  return valueDiff === 0 ? a.index - b.index : valueDiff;\n};\n\n/**\n * Partitions array where all elements smaller than the (k+1) smallest element\n * are found to the left of it, and all larger to the right of it.\n * Based on the Floyd-Rivest Algorithm, ref:\n * https://en.wikipedia.org/wiki/Floyd%E2%80%93Rivest_algorithm\n * @param array: Array to partition\n * @param left: Left index for the interval\n * @param right: Right index for the interval\n * @param k: Desired index value, where array[k] is the (k+1)th smallest element\n *           when left = 0\n */\nfunction select(array: Pair[], k: number, left = 0, right = array.length - 1) {\n  while (right > left) {\n    // Use select recursively to sample a smaller set of size s\n    // the arbitrary constants 600 and 0.5 are used in the original\n    // version to minimize execution time.\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const i = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * Math.sign(i - n / 2);\n      const newLeft = Math.max(left, Math.floor(k - i * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - i) * s / n + sd));\n      select(array, k, newLeft, newRight);\n    }\n    // partition the elements between left and right around t\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    util.swap(array, left, k);\n\n    if (comparePair(array[right], t) > 0) {\n      util.swap(array, left, right);\n    }\n    while (i < j) {\n      util.swap(array, i, j);\n      i++;\n      j--;\n      while (comparePair(array[i], t) < 0) {\n        i = i + 1;\n      }\n      while (comparePair(array[j], t) > 0) {\n        j = j - 1;\n      }\n    }\n    if (comparePair(array[left], t) === 0) {\n      util.swap(array, left, j);\n    } else {\n      j = j + 1;\n      util.swap(array, j, right);\n    }\n    // Adjust left and right towards the boundaries of the subset\n    // containing the (k - left + 1)th smallest element.\n    if (j <= k) {\n      left = j + 1;\n    }\n    if (k <= j) {\n      right = j - 1;\n    }\n  }\n}\n\nexport function topKImpl<T extends Tensor, R extends Rank>(\n    x: TypedArray, xShape: number[], xDtype: NumericDataType, k: number,\n    sorted: boolean):\n    [TensorBuffer<R, NumericDataType>, TensorBuffer<R, 'int32'>] {\n  // Reshape into a 2d tensor [batch, lastDim] and compute topk along lastDim.\n  const lastDim = xShape[xShape.length - 1];\n  const [batch, size] = [x.length / lastDim, lastDim];\n  const allTopKVals = util.getTypedArrayFromDType(xDtype, batch * k);\n  const allTopKIndices = util.getTypedArrayFromDType('int32', batch * k);\n\n  for (let b = 0; b < batch; b++) {\n    const offset = b * size;\n    const vals = x.subarray(offset, offset + size);\n\n    let valAndInd: Pair[] = new Array(vals.length);\n    vals.forEach(\n        (value: number, index: number) => valAndInd[index] = {value, index});\n\n    if (k < valAndInd.length) {\n      select(valAndInd, k);\n      valAndInd = valAndInd.slice(0, k);\n    }\n\n    if (sorted) {\n      valAndInd.sort(comparePair);\n    }\n    \n    const outOffset = b * k;\n    const topKVals = allTopKVals.subarray(outOffset, outOffset + k);\n    const topKIndices = allTopKIndices.subarray(outOffset, outOffset + k);\n    for (let i = 0; i < k; i++) {\n      topKVals[i] = valAndInd[i].value;\n      topKIndices[i] = valAndInd[i].index;\n    }\n  }\n  // Reshape back to the original input shape, except that the last\n  // dimension is k.\n  const outputShape = xShape.slice();\n  outputShape[outputShape.length - 1] = k;\n\n  return [\n    buffer(outputShape as ShapeMap[R], xDtype, allTopKVals),\n    buffer(outputShape as ShapeMap[R], 'int32', allTopKIndices)\n  ];\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n * =============================================================================\n */\n\nimport {BackendValues, DataType, TensorBuffer, TypedArray, util} from '@tensorflow/tfjs-core';\n\nexport function uniqueImpl(\n    values: BackendValues, axis: number, shape: number[], dtype: DataType): {\n  outputValues: BackendValues,\n  outputShape: number[],\n  indices: BackendValues\n} {\n  // Normalize and validate axis.\n  const $axis = util.parseAxisParam(axis, shape)[0];\n\n  // Calculate the new shape that is suitable for extracting data along the\n  // given axis.\n  //\n  // The rank is 3.\n  // The size of the 1st dimension is the size of all the axes < the given axis.\n  // The size of the 2nd dimension is the same as the size of the given axis.\n  // The size of the 3rd dimension is the size of all the axes > the given axis.\n  //\n  // For example, for a 4D tensor with shape=[2, 3, 5, 4] and axis=2, the\n  // newShape would be: [2*3, 5, 4].\n  //\n  // Note that this is not the final output shape. This will be the shape for an\n  // intermediate TensorBuffer (see inputBuffer below) to allow us to extract\n  // values along the given axis. To demonstrate how it works, consider the\n  // following example:\n  //\n  // Input: a 3D tensor, with shape [1, 2, 3]\n  // [\n  //   [\n  //      [1,2,3],\n  //      [4,5,6]\n  //   ]\n  // ]\n  // Axis: 2 (the last axis).\n  // Along axis 2, we expect to extract 3 tensors: [1,4], [2,5], [3,6].\n  //\n  // For this example, newShape would be: [2, 3, 1], where 2 is calculated from\n  // 1*2. The re-shaped data would look like:\n  //\n  // [\n  //   [\n  //     [1], [2], [3]\n  //   ],\n  //   [\n  //     [4], [5], [6]\n  //   ]\n  // ]\n  //\n  // Then, we can construct a 3-level nested loop by the following dimension\n  // order to extract the values along the axis (dimension1):\n  // i: dimension1       // 0,1,2 (newShape[1])\n  //   m: dimension0     // 0,1   (newShape[0])\n  //     n: dimension2   // 0     (newShape[2])\n  //\n  //                       m, i, n\n  //                      ---------\n  // Iteration 0: data at [0, 0, 0] => \"1\"\n  // Iteration 1: data at [1, 0, 0] => \"4\"\n  // We got [1,4].\n  // Iteration 2: data at [0, 1, 0] => \"2\"\n  // Iteration 3: data at [1, 1, 0] => \"5\"\n  // We got [2,5].\n  // Iteration 4: data at [0, 2, 0] => \"3\"\n  // Iteration 5: data at [1, 2, 0] => \"6\"\n  // We got [3,6].\n  const newShape = [1, shape[0], 1];\n  for (let i = 0; i < $axis; i++) {\n    newShape[0] *= shape[i];\n  }\n  newShape[1] = shape[$axis];\n  for (let i = $axis + 1; i < shape.length; i++) {\n    newShape[2] *= shape[i];\n  }\n\n  // A map from unique elements (their string representations) to their values\n  // in \"indices\" (below).\n  const uniqueElements = new Map<string, number>();\n  // The indices of each unique element in the original tensor along the given\n  // axis. It is 1D and has the same size as the given axis.\n  const indices = new Int32Array(shape[$axis]);\n  // Create a buffer so we can easily extract value at a given location.\n  const inputBuffer = new TensorBuffer(newShape, dtype, values as TypedArray);\n  // The indices along the given axis that have unique elements. This is a\n  // de-duped version of \"indices\" above.\n  const uniqueIndices: number[] = [];\n  const is1DTensor = newShape[0] === 1 && newShape[2] === 1;\n  for (let i = 0; i < shape[$axis]; i++) {\n    // Extract values along the axis.\n    let element: string;\n    if (is1DTensor) {\n      // Fast path for 1D tensor input.\n      element = values[i].toString();\n    } else {\n      const axisValues = [];\n      for (let m = 0; m < newShape[0]; m++) {\n        for (let n = 0; n < newShape[2]; n++) {\n          axisValues.push(inputBuffer.get(m, i, n));\n        }\n      }\n      element = axisValues.join(',');\n    }\n\n    // Dedup and update various indices.\n    const existingIndex = uniqueElements.get(element);\n    if (existingIndex != null) {\n      indices[i] = existingIndex;\n    } else {\n      const uniqueIndex = uniqueElements.size;\n      uniqueElements.set(element, uniqueIndex);\n      indices[i] = uniqueIndex;\n      uniqueIndices.push(i);\n    }\n  }\n\n  // Now we know where each of the unique elements are located along the axis\n  // (uniqueIndices). Extract them from input buffer and store them in the\n  // output buffer.\n  const outputTmpShape = newShape.slice();\n  outputTmpShape[1] = uniqueElements.size;\n  const outputBuffer = new TensorBuffer(outputTmpShape, dtype);\n  uniqueIndices.forEach((uniqueElementIndex, i) => {\n    for (let m = 0; m < newShape[0]; m++) {\n      for (let n = 0; n < newShape[2]; n++) {\n        outputBuffer.set(inputBuffer.get(m, uniqueElementIndex, n), m, i, n);\n      }\n    }\n  });\n\n  // The output shape can be calculated from the input shape with the size of\n  // the given axis replaced by the number of unique elements along that axis.\n  const outputShape = shape.slice();\n  outputShape[$axis] = outputTmpShape[1];\n\n  return {\n    outputValues: outputBuffer.values as BackendValues,\n    outputShape,\n    indices,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBM,SAAU,iBACZ,QAAiC,QAAc;AACjD,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,aAAS,CAAC,MAAM;;AAElB,SAAO,QAAQ,OAAI;AACjB,QAAI,KAAK,MAAM;AACb,mBAAK,OACD,EAAE,UAAU,aACZ,MAAM,GACF,MAAM,yDAAyD;;EAE3E,CAAC;AACH;;;ACVM,SAAU,cAAc,MAAgB;AAC5C,QAAM,eAAe,IAAI,aAAa,KAAK,MAAM;AACjD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,iBAAa,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC;;AAEpC,SAAO;AACT;AAEO,IAAM,MAAM,CAAC,SAAsD;AACxE,QAAM,EAAC,EAAC,IAAI,KAAK;AACjB,QAAM,aAAa,KAAK;AAExB,mBAAiB,GAAG,KAAK;AAEzB,MAAI,eAAe,IAAI,aAAa,aAAK,cAAc,EAAE,KAAK,CAAC;AAC/D,QAAM,SAAS,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAC7C,iBAAe,cAAc,MAAM;AAEnC,SAAO,WAAW,WAAW,cAAc,EAAE,OAAO,EAAE,KAAK;AAC7D;AAEO,IAAM,YAA0B;EACrC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACtBR,SAAU,6BAA6B,IAAyB;AAEpE,SAAO,CAAC,QAAkB,QAAkB,OACpC,OAAmB,UAA2C;AACpE,UAAM,WAAW,qBAAa,2BAA2B,QAAQ,MAAM;AAEvE,UAAM,aAAa,SAAS;AAC5B,UAAM,gBAAgB,aAAK,eAAe,QAAQ;AAClD,UAAM,aAAa,aAAK,cAAc,QAAQ;AAE9C,UAAM,SACF,aAAK,uBAAuB,OAA0B,UAAU;AAEpE,UAAM,QAAQ,OAAO;AACrB,UAAM,QAAQ,OAAO;AAErB,UAAM,WAAW,aAAK,eAAe,MAAM;AAC3C,UAAM,WAAW,aAAK,eAAe,MAAM;AAE3C,UAAM,iBAAiB,qBAAa,iBAAiB,QAAQ,QAAQ;AACrE,UAAM,iBAAiB,qBAAa,iBAAiB,QAAQ,QAAQ;AAErE,QAAI,eAAe,SAAS,eAAe,WAAW,GAAG;AACvD,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,eAAO,CAAC,IAAI,GAAG,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,IAAI,MAAM,MAAM,CAAC;;WAE5D;AACL,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,cAAM,MAAM,aAAK,WAAW,GAAG,YAAY,aAAa;AAExD,cAAM,OAAO,IAAI,MAAM,CAAC,KAAK;AAC7B,uBAAe,QAAQ,OAAK,KAAK,CAAC,IAAI,CAAC;AACvC,cAAM,SAAS,aAAK,WAAW,MAAM,OAAO,QAAQ;AAEpD,cAAM,OAAO,IAAI,MAAM,CAAC,KAAK;AAC7B,uBAAe,QAAQ,OAAK,KAAK,CAAC,IAAI,CAAC;AACvC,cAAM,SAAS,aAAK,WAAW,MAAM,OAAO,QAAQ;AAEpD,eAAO,CAAC,IAAI,GAAG,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC;;;AAI/C,WAAO,CAAC,QAAQ,QAAQ;EAC1B;AACF;;;AC/CM,SAAU,QAAQ,MAAsD;AAE5E,QAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,QAAM,EAAC,MAAAA,OAAM,KAAI,IAAI;AAErB,QAAM,WAAW,QAAQ,KAAK,IAAIA,MAAK,MAAM,EAAE;AAC/C,QAAM,WAAW,QAAQ,KAAK,IAAI,KAAK,MAAM,EAAE;AAE/C,QAAM,cAAc,QAAQ,eAAeA,MAAK,OAAO,WAAW;AAElE,QAAMC,WAAU,QAAQ,KAAK,IAAI,YAAY,MAAM;AAKnD,EAAAA,SAAQ,qBAAqB;IAC3B,MAAM,QAAQ,eAAeD,MAAK,OAAO,WAAW,QAAQ;IAC5D,MAAM,QAAQ,eAAe,KAAK,OAAO,WAAW,QAAQ;;AAG9D,SAAO;AACT;AAEO,IAAM,gBAA8B;EACzC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACpBR,SAAU,MACZ,SAAyB,OACzB,QAAkB,WAAS;AAC7B,MAAI,UAAU,aAAa;AACzB,UAAME,QAAO,MAAM,SAAS,OAAO,SAAS;AAC5C,UAAM,OAAO,MAAM,SAAS,OAAO,SAAS;AAE5C,WAAO,QAAQ,EAAC,QAAQ,EAAC,MAAAA,OAAM,KAAI,GAAG,QAAO,CAAC;;AAGhD,QAAM,SAAS,aAAK,oBAAoB,aAAK,cAAc,KAAK,GAAG,KAAK;AAExE,SAAO,QAAQ,eAAe,OAAO,OAAO,MAAM;AACpD;;;ACnBM,SAAU,SACZ,MAAuD;AACzD,QAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,QAAM,EAAC,EAAC,IAAI;AAEZ,UAAQ,OAAO,EAAE,MAAM;AAEvB,SAAO,EAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,OAAO,EAAE,MAAK;AAC1D;AAEO,IAAM,iBAA+B;EAC1C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACbR,SAAU,KAAK,MAAmD;AAEtE,QAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,QAAM,EAAC,MAAK,IAAI;AAEhB,QAAMC,QAAO,QAAQ,KAAK,IAAI,MAAM,MAAM,EAAE,mBAAmB;AAC/D,QAAM,UAAU,QAAQ,KAAK,IAAIA,MAAK,MAAM,EAAE;AAK9C,SAAO,QAAQ,eAAeA,MAAK,OAAOA,MAAK,OAAO,OAAO;AAC/D;AAEO,IAAM,aAA2B;EACtC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACZR,SAAU,SACZ,QAAoB,OAAiB,WACrC,OAAe;AACjB,MAAI,UAAU,SAAS;AACrB,UAAM,eAAe,WAAW,KAAK,MAAM;AAC3C,WAAO,CAAC,OAAO,SAAS,YAAY;;AAGtC,MAAI,UAAU,QAAQ;AAIpB,UAAM,OAAO,aAAK,aAAa,CAAC,CAAC,GAAG,SAAS;AAE7C,UAAM,CAAC,YAAY,WAAW,IAAI,6BAC9B,CAAC,GAAG,MAAO,MAAM,IAAK,IAAI,CAAC,EAAE,OAAO,CAAA,GAAI,QAAQ,MAAM,MAAM;AAEhE,WAAO,CAAC,aAAa,QAAQ,UAAU;;AAEzC,QAAM,IAAI,MAAM,iCAAiC,SAAS,OAAO,KAAK,EAAE;AAC1E;AAEM,SAAU,KACZ,MAAqE;AAEvE,QAAM,EAAC,QAAQ,SAAS,MAAK,IAAI;AACjC,QAAM,EAAC,EAAC,IAAI;AACZ,QAAM,EAAC,MAAK,IAAI;AAGhB,MAAI,UAAU,aAAa;AACzB,QAAI,EAAE,UAAU,aAAa;AAC3B,aAAO,SAAS,EAAC,QAAQ,EAAC,EAAC,GAAG,QAAO,CAAC;;AAGxC,UAAM,kBAAkB,MAAM,SAAS,EAAE,OAAO,EAAE,KAAK;AACvD,UAAM,SAAS,KAAK,EAAC,QAAQ,EAAC,EAAC,GAAG,SAAS,OAAO,EAAC,OAAO,UAAS,EAAC,CAAC;AAErE,UAAM,SACF,QAAQ,EAAC,QAAQ,EAAC,MAAM,QAAQ,MAAM,gBAAe,GAAG,QAAO,CAAC;AAEpE,YAAQ,8BAA8B,eAAe;AACrD,YAAQ,8BAA8B,MAAM;AAE5C,WAAO;;AAIT,MAAI,EAAE,UAAU,aAAa;AAC3B,UAAM,WAAW,KAAK,EAAC,QAAQ,EAAC,OAAO,EAAC,GAAG,QAAO,CAAC;AACnD,UAAM,SAAS,KAAK,EAAC,QAAQ,EAAC,GAAG,SAAQ,GAAG,SAAS,OAAO,EAAC,MAAK,EAAC,CAAC;AAEpE,YAAQ,8BAA8B,QAAQ;AAE9C,WAAO;;AAGT,MAAI,CAAC,aAAK,gBAAgB,EAAE,OAAO,KAAK,GAAG;AAGzC,UAAM,SAAS,SAAS,EAAC,QAAQ,EAAC,EAAC,GAAG,QAAO,CAAC;AAC9C,WAAO,EAAC,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,MAAK;;AAG3D,QAAM,SAAS,QAAQ,KAAK,IAAI,EAAE,MAAM,EAAE;AAC1C,QAAM,CAAC,aAAa,YAAY,UAAU,IACtC,SAAS,QAAQ,EAAE,OAAO,EAAE,OAAO,KAAK;AAC5C,SAAO,QAAQ,eAAe,aAAa,YAAY,UAAU;AACnE;AAEO,IAAM,aAA2B;EACtC,YAAY;EACZ,aAAa;EACb,YAAY;;;;AC9DR,SAAU,iBACZ,MAAc,YACd,aAAuC,OAAgB;AACzD,MAAI,eAAe,MAAM;AACvB,WAAO,CAAC,EAAC,QAAQ,QAAO,MAAK;AAC3B,YAAM,EAAC,GAAG,EAAC,IAAI;AACf,YAAM,aAAa;AAEnB,uBAAiB,CAAC,GAAG,CAAC,GAAG,IAAI;AAE7B,YAAM,QAAQ,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAC5C,YAAM,QAAQ,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAE5C,YAAM,eAAe,EAAE,UAAU;;QAE7B,qBAAa,uBAAuB,KAA4B;UAChE;AACJ,YAAM,eAAe,EAAE,UAAU;;QAE7B,qBAAa,uBAAuB,KAA4B;UAChE;AACJ,YAAM,SAAS,SAAS,EAAE;AAE1B,YAAM,CAAC,YAAY,WAAW,IAC1B,WAAW,EAAE,OAAO,EAAE,OAAO,cAAc,cAAc,MAAM;AAEnE,aAAO,WAAW,eAAe,aAAa,QAAQ,UAAU;IAClE;;AAGF,SAAO,CAAC,EAAC,QAAQ,QAAO,MAAK;AAC3B,UAAM,EAAC,GAAG,EAAC,IAAI;AACf,UAAM,aAAa;AAEnB,QAAI,EAAE,UAAU,eAAe,EAAE,UAAU,aAAa;AACtD,YAAM,YAAY,KACd,EAAC,QAAQ,EAAC,GAAG,EAAC,GAAG,SAAS,YAAY,OAAO,EAAC,OAAO,YAAW,EAAC,CAAC;AAEtE,YAAM,gBAAgB,WAAW,KAAK,IAAI,UAAU,MAAM;AAE1D,YAAM,QAAQ,cAAc,mBAAmB;AAC/C,YAAM,QAAQ,cAAc,mBAAmB;AAE/C,YAAM,YACF,WAAW,KAAK,IAAI,MAAM,MAAM,EAAE;AACtC,YAAM,YACF,WAAW,KAAK,IAAI,MAAM,MAAM,EAAE;AAEtC,YAAM,YAAY,KACd,EAAC,QAAQ,EAAC,GAAG,EAAC,GAAG,SAAS,YAAY,OAAO,EAAC,OAAO,YAAW,EAAC,CAAC;AAEtE,YAAM,gBAAgB,WAAW,KAAK,IAAI,UAAU,MAAM;AAE1D,YAAM,QAAQ,cAAc,mBAAmB;AAC/C,YAAM,QAAQ,cAAc,mBAAmB;AAE/C,YAAM,YACF,WAAW,KAAK,IAAI,MAAM,MAAM,EAAE;AACtC,YAAM,YACF,WAAW,KAAK,IAAI,MAAM,MAAM,EAAE;AAEtC,YAAM,CAAC,gBAAgB,gBAAgB,WAAW,IAAI,YAClD,EAAE,OAAO,EAAE,OAAO,WAAW,WAAW,WAAW,SAAS;AAEhE,YAAM,aACF,WAAW,eAAe,aAAa,WAAW,cAAc;AAEpE,YAAM,aACF,WAAW,eAAe,aAAa,WAAW,cAAc;AAEpE,YAAM,SAAS,QACX,EAAC,QAAQ,EAAC,MAAM,YAAY,MAAM,WAAU,GAAG,SAAS,WAAU,CAAC;AAEvE,iBAAW,8BAA8B,SAAS;AAClD,iBAAW,8BAA8B,SAAS;AAClD,iBAAW,8BAA8B,UAAU;AACnD,iBAAW,8BAA8B,UAAU;AAEnD,aAAO;WACF;AACL,YAAM,QAAQ,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAC5C,YAAM,QAAQ,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAE5C,YAAM,SAAS,SAAS,EAAE;AAE1B,YAAM,CAAC,YAAY,WAAW,IAC1B,WAAW,EAAE,OAAO,EAAE,OAAO,OAAO,OAAO,MAAM;AAErD,aAAO,WAAW,eAAe,aAAa,QAAQ,UAAU;;EAEpE;AACF;AAMM,SAAU,8BAA8B,IAA0B;AAEtE,SAAO,CAAC,QAAkB,QAAkB,WACpC,WAAyB,WACzB,cAA+D;AACrE,UAAM,cAAc,qBAAa,2BAA2B,QAAQ,MAAM;AAC1E,UAAM,aAAa,aAAK,cAAc,WAAW;AACjD,UAAM,aAAa,YAAY;AAC/B,UAAM,gBAAgB,aAAK,eAAe,WAAW;AAErD,UAAM,iBAAiB,aAAK,uBAAuB,WAAW,UAAU;AACxE,UAAM,iBAAiB,aAAK,uBAAuB,WAAW,UAAU;AAExE,UAAM,iBAAiB,qBAAa,iBAAiB,QAAQ,WAAW;AACxE,UAAM,iBAAiB,qBAAa,iBAAiB,QAAQ,WAAW;AAExE,UAAM,QAAQ,qBAAa,uBAAuB,WAAW,SAAS;AACtE,UAAM,QAAQ,qBAAa,uBAAuB,WAAW,SAAS;AAEtE,UAAM,QAAQ,OAAO;AACrB,UAAM,WAAW,aAAK,eAAe,MAAM;AAE3C,UAAM,QAAQ,OAAO;AACrB,UAAM,WAAW,aAAK,eAAe,MAAM;AAE3C,QAAI,eAAe,SAAS,eAAe,WAAW,GAAG;AACvD,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,OAAO,IAAI,MAAM;AACvB,cAAM,OAAO,IAAI,MAAM;AAEvB,cAAM,SACF,GAAG,MAAM,OAAO,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,GAAG,MAAM,OAAO,CAAC,GACpD,MAAM,OAAO,IAAI,CAAC,CAAC;AAE1B,uBAAe,CAAC,IAAI,OAAO;AAC3B,uBAAe,CAAC,IAAI,OAAO;;WAExB;AACL,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,MAAM,aAAK,WAAW,GAAG,YAAY,aAAa;AAExD,cAAM,OAAO,IAAI,MAAM,CAAC,KAAK;AAC7B,uBAAe,QAAQ,OAAK,KAAK,CAAC,IAAI,CAAC;AACvC,cAAM,SAAS,aAAK,WAAW,MAAM,OAAO,QAAQ;AAEpD,cAAM,OAAO,IAAI,MAAM,CAAC,KAAK;AAC7B,uBAAe,QAAQ,OAAK,KAAK,CAAC,IAAI,CAAC;AACvC,cAAM,SAAS,aAAK,WAAW,MAAM,OAAO,QAAQ;AAEpD,cAAM,WACF,GAAG,MAAM,SAAS,CAAC,GAAG,MAAM,SAAS,IAAI,CAAC,GAAG,MAAM,SAAS,CAAC,GAC1D,MAAM,SAAS,IAAI,CAAC,CAAC;AAE5B,uBAAe,CAAC,IAAI,SAAS;AAC7B,uBAAe,CAAC,IAAI,SAAS;;;AAGjC,WAAO,CAAC,gBAAgB,gBAAgB,WAAW;EACrD;AACF;;;AC3KO,IAAM,UACT,6BAA8B,CAAC,GAAW,MAAc,IAAI,CAAE;AAC3D,IAAM,iBACT,8BAA+B,CAAC,OAAO,OAAO,OAAO,UAAS;AAC5D,SAAO,EAAC,MAAM,QAAQ,OAAO,MAAM,QAAQ,MAAK;AAClD,CAAE;AAEC,IAAM,MAAM,iBAAiB,KAAK,SAAS,cAAc;AAEzD,IAAM,YAA0B;EACrC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACfR,SAAU,aACZ,OAAmB,aAAyB,cAC5C,cAAwB,MAAY;AACtC,QAAM,cAAc,aAAK,cAAc,YAAY;AACnD,QAAM,UAAU,aAAK,oBAAoB,MAAM,YAAY;AAE3D,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,QAAQ,MAAM,CAAC;AACrB,QAAI,QAAQ,GAAG;AACb,YAAM,IAAI,MAAM,+BAA+B;;AAGjD,QAAI,SAAS,MAAM;AACjB;;AAGF,QAAI,cAAc,GAAG;AACnB,cAAQ,KAAK,KAAK,YAAY,CAAC;WAC1B;AACL,cAAQ,KAAK,KAAK;;;AAItB,SAAO;AACT;AAEM,SAAU,mBACZ,MAAuB,YAA6B,MACpD,eAAe,OAAK;AACtB,QAAM,UAAU,KAAK,MAAM,CAAC;AAC5B,QAAM,UAAU,KAAK,MAAM,CAAC;AAE5B,QAAM,SAAS,OAAO,CAAC,SAAS,IAAI,GAAG,WAAW,KAAK;AAEvD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,YAAM,QAAQ,KAAK,IAAI,GAAG,CAAC;AAC3B,UAAI,QAAQ,GAAG;AACb,cAAM,IAAI,MAAM,+BAA+B;;AAGjD,UAAI,SAAS,MAAM;AACjB;;AAGF,UAAI,cAAc;AAChB,eAAO,IAAI,GAAG,GAAG,KAAK;aACjB;AACL,YAAI,WAAW,OAAO,GAAG;AACvB,iBAAO,IAAI,OAAO,IAAI,GAAG,KAAK,IAAI,WAAW,IAAI,GAAG,CAAC,GAAG,GAAG,KAAK;eAC3D;AACL,iBAAO,IAAI,OAAO,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,KAAK;;;;;AAMrD,SAAO;AACT;;;ACvDO,IAAM,iBACT,6BAA8B,CAAC,GAAW,MAAc,IAAI,CAAE;AAE3D,IAAM,aAAa,iBAAiB,YAAY,cAAc;AAE9D,IAAM,mBAAiC;EAC5C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACNR,SAAU,sBACsB,IAA8B;AAElE,SAAO,CAAC,QAAQ,OAAO,UAAS;AAC9B,UAAM,YACF,aAAK,kBAAkB,OAAO,OAAO,MAAM;AAC/C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,gBAAU,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,KAAK;;AAEpC,WAAO;EACT;AACF;;;ACFM,SAAU,gBAEd,MAAc,IACd,OAAsB;AAEtB,QAAM,OAAO,sBAA4B,EAAE;AAE3C,SAAO,wBAA8B,MAAM,MAAM,KAAK;AACxD;AAWM,SAAU,wBAEd,MAAc,WACd,OAAsB;AAEtB,SAAO,CAAC,EAAC,QAAQ,OAAO,QAAO,MAAK;AAClC,UAAM,EAAC,EAAC,IAAI;AACZ,qBAAiB,GAAG,IAAI;AAExB,UAAM,aAAa;AACnB,UAAM,SAAS,WAAW,KAAK,IAAI,EAAE,MAAM,EAAE;AAC7C,QAAI;AACJ,QAAI,EAAE,UAAU,UAAU;AACxB,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,cAAM,IAAI,MAAM,oDAAqD;;AAEvE,gBAAU,qBAAa,uBAAuB,MAAM;WAE/C;AACL,gBAAU;;AAGZ,UAAM,SAAS,SAAS,EAAE;AAC1B,UAAM,YAAY,UAAU,SAAS,QAAQ,KAAK;AAClD,WAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,SAAS;EAC7D;AACF;;;ACxDO,IAAM,WAAW,sBAAsB,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;AAC5D,IAAM,OAAO,wBAAwB,MAAM,QAAQ;AAEnD,IAAM,aAA2B;EACtC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACTR,SAAU,WACZ,QAAuD,UACvD,OAAiB,cAAqB;AACxC,QAAM,UAAU,aAAK,kBAAkB,OAAO,aAAK,cAAc,QAAQ,CAAC;AAE1E,MAAI,gBAAgB,UAAU,UAAU;AAEtC,QAAI,SAAS;AACb,WAAO,QAAQ,WAAQ;AACrB,YAAM,OAAO,aAAK,cAAc,MAAM,KAAK;AAE1C,cAAuB,IAAI,MAAM,MAAoB,MAAM;AAC5D,gBAAU;IACZ,CAAC;SACI;AACL,QAAI,YAAY;AAEhB,WAAO,QAAQ,WAAQ;AACrB,YAAM,cAAc,UAAU,WAC1B,qBAAa,uBAAuB,MAAM,IAAoB,IAC9D,MAAM;AAEV,UAAI,OAAO;AAEX,eAAS,MAAM,GAAG,MAAM,MAAM,MAAM,CAAC,GAAG,EAAE,KAAK;AAC7C,cAAM,SAAS,MAAM,SAAS,CAAC,IAAI;AACnC,iBAAS,MAAM,GAAG,MAAM,MAAM,MAAM,CAAC,GAAG,EAAE,KAAK;AAC7C,kBAAQ,SAAS,GAAG,IAAI,YAAY,MAAM;;;AAI9C,mBAAa,MAAM,MAAM,CAAC;IAC5B,CAAC;;AAGH,SAAO;AACT;;;ACjCO,IAAM,YACT,6BAA6B,CAAC,GAAW,MAAe,MAAM,IAAK,IAAI,CAAC;AACrE,IAAM,QACT,iBAAiB,OAAO,WAAW,MAAwB,MAAM;AAE9D,IAAM,cAA4B;EACvC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACRP,IAAM,UAAU,sBAAsB,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;AAC1D,IAAM,MAAM,wBAAwB,KAAK,SAAS,SAAS;AAE3D,IAAM,YAA0B;EACrC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACNP,IAAM,YAAY,sBAAsB,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;AAC9D,IAAM,QAAQ,wBAAwB,OAAO,SAAS;AAEtD,IAAM,cAA4B;EACvC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACNP,IAAM,YAAY,sBAAsB,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;AAC9D,IAAM,QAAQ,wBAAwB,OAAO,SAAS;AAEtD,IAAM,cAA4B;EACvC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACNP,IAAM,eACT,6BAA6B,CAAC,GAAW,MAAc,KAAK,MAAM,IAAI,CAAC,CAAC;AACrE,IAAM,WACT,iBAAiB,UAAU,cAAc,MAAwB,OAAO;AAErE,IAAM,iBAA+B;EAC1C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACXR,SAAU,aACZ,aAAyB,WAA4B,OACrD,WAAmB,WAAmB,WAAmB,SACzD,aAAuB,YAAkB;AAC3C,QAAM,SAAS,OAAO,CAAC,WAAW,SAAS,GAAG,KAAK;AAEnD,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,QAAQ,CAAA;AACd,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,YAAM,MAAM,YAAY,IAAI,YAAY,CAAC;AACzC,sBAAgB,MAAM,QAAQ,CAAC;AAC/B,YAAM,KAAK,GAAG;;AAEhB,QAAI,eAAe,KAAK,gBAAgB,aAAa,WAAW;AAC9D,YAAM,IAAI,MACN,oBAAoB,KAAK,wBAAwB,WAAW,EAAE;;AAGpE,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,aAAO,OAAO,IAAI,YAAY,CAAC,IAC3B,UAAU,IAAI,GAAG,UAAU,WAAW,eAAe,YAAY,CAAC,CAAC;;;AAI3E,SAAO;AACT;;;AC1BM,SAAU,aACZ,MAA0B,YAC1B,oBAA4B;AAC9B,QAAM,SAAS,OAAO,oBAAoB,KAAK,KAAK;AACpD,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,GAAG;AACpC,UAAM,SAAS,OAAO,WAAW,CAAC;AAElC,UAAM,cAAwB,OAAO,MAAK;AAC1C,UAAM,WAAW,YAAY,CAAC;AAC9B,UAAM,aAAa,YAAY,CAAC;AAChC,UAAM,eAAe,WAAW,WAAW,CAAC,UAAU,UAAU,CAAC;AACjE,gBAAY,CAAC,IAAI,WAAW,OAAO,YAAY;AAE/C,UAAM,gBAAgB,KAAK,WAAW,WAAW;AAEjD,QAAI,KAAK,iBAAiB,gBAAgB,KAAK,OAAO,QAAQ;AAC5D,aAAO,OAAO,CAAC,IAAI,KAAK,OAAO,aAAa;;;AAIhD,SAAO;AACT;;;AClBO,IAAM,cACT,6BAA6B,CAAC,GAAW,MAAe,IAAI,IAAK,IAAI,CAAC;AACnE,IAAM,UACT,iBAAiB,SAAS,aAAa,MAAwB,MAAM;AAElE,IAAM,gBAA8B;EACzC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACRP,IAAM,mBACT,6BAA6B,CAAC,GAAW,MAAe,KAAK,IAAK,IAAI,CAAC;AACpE,IAAM,eAAe,iBACxB,cAAc,kBAAkB,MAAwB,MAAM;AAE3D,IAAM,qBAAmC;EAC9C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACRP,IAAM,WACT,6BAA6B,CAAC,GAAW,MAAe,IAAI,IAAK,IAAI,CAAC;AACnE,IAAM,OACT,iBAAiB,MAAM,UAAU,MAAwB,MAAM;AAE5D,IAAM,aAA2B;EACtC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACRP,IAAM,gBACT,6BAA6B,CAAC,GAAW,MAAe,KAAK,IAAK,IAAI,CAAC;AACpE,IAAM,YACT,iBAAiB,WAAW,eAAe,MAAwB,MAAM;AAEtE,IAAM,kBAAgC;EAC3C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACXR,SAAU,aACZ,OAAe,MAAc,KAAW;AAC1C,QAAM,QAAQ,OAAO,UAAU,MAAM;AAErC,QAAM,SAAS,aAAK,oBAAoB,KAAK,SAAS;AACtD,SAAO,CAAC,IAAI;AACZ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI;;AAG9B,SAAO;AACT;;;ACRO,IAAM,UAAU,sBAAsB,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;AAC1D,IAAM,MAAM,wBAAwB,KAAK,OAAO;AAEhD,IAAM,YAA0B;EACrC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACTR,SAAU,QACZ,OAAmB,YAAoB,UACvC,OAAe;AACjB,QAAM,OAAO,aAAK,uBACd,OAA0B,aAAK,cAAc,QAAQ,CAAC;AAE1D,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,UAAM,SAAS,IAAI;AACnB,QAAI,MAAM,MAAM,MAAM;AACtB,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,YAAM,QAAQ,MAAM,SAAS,CAAC;AAC9B,UAAI,OAAO,MAAM,KAAK,KAClB,QAAQ,KAAK;AACf,cAAM;;;AAGV,SAAK,CAAC,IAAI;;AAEZ,SAAO;AACT;;;AChBO,IAAM,cAAc,6BACtB,CAAC,QAAQ,WAAW,KAAK,IAAI,QAAkB,MAAgB,CAAE;AAC/D,IAAM,UAAU,iBAAiB,SAAS,WAAW;AAErD,IAAM,gBAA8B;EACzC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACPP,IAAM,cAAc,6BACtB,CAAC,QAAQ,WAAW,KAAK,IAAI,QAAkB,MAAgB,CAAE;AAC/D,IAAM,UAAU,iBAAiB,SAAS,WAAW;AAErD,IAAM,gBAA8B;EACzC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACRP,IAAM,eAAe,6BACvB,CAAC,QAAgB,WAAmB,SAAS,MAAO;AAClD,IAAM,sBACT,8BAA+B,CAAC,OAAO,OAAO,OAAO,UAAS;AAC5D,SAAO;IACL,MAAM,QAAQ,QAAQ,QAAQ;IAC9B,MAAM,QAAQ,QAAQ,QAAQ;;AAElC,CAAE;AAEC,IAAM,WACT,iBAAiB,UAAU,cAAc,mBAAmB;AAEzD,IAAM,iBAA+B;EAC1C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACdR,SAAU,QAAQ,OAAmB,QAAkB,QAAgB;AAE3E,QAAM,WACF,aAAK,kBAAkB,IAA4B,MAAM;AAC7D,SAAO,aAAa,CAAA,GAAI,QAAQ,UAAU,OAAO,MAAM;AACzD;AAEM,SAAU,IAAI,MAAoD;AAEtE,QAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,QAAM,EAAC,EAAC,IAAI;AAEZ,mBAAiB,GAAG,KAAK;AAEzB,QAAM,QAAQ,QAAQ,KAAK,IAAI,EAAE,MAAM,EAAE;AACzC,QAAM,CAAC,KAAK,QAAQ,IAAI,QAAQ,OAAO,EAAE,OAAO,EAAE,KAAK;AAEvD,SAAO,QAAQ,eAAe,UAAU,EAAE,OAAO,GAAG;AACtD;AAEO,IAAM,YAA0B;EACrC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACxBP,IAAM,eACT,6BAA8B,CAAC,GAAG,MAAO,MAAM,IAAK,IAAI,CAAE;AACvD,IAAM,WACT,iBAAiB,UAAU,cAAc,MAAsB,MAAM;AAElE,IAAM,iBAA+B;EAC1C,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACVR,SAAU,cACZ,OAAmB,QAAkB,OAAiB,MACtD,UAAkB;AACpB,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,aAAK,cAAc,MAAM;AACvC,QAAM,WAAW,aAAK,eAAe,MAAM;AAC3C,QAAM,aAAa,aAAK,eAAe,QAAQ;AAE/C,QAAM,SAAS,aAAK,uBAChB,OAA0B,aAAK,cAAc,QAAQ,CAAC;AAE1D,WAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,UAAM,MAAM,aAAK,WAAW,GAAG,OAAO,QAAQ;AAG9C,UAAM,SAAmB,IAAI,MAAM,IAAI,MAAM;AAC7C,aAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,aAAOA,EAAC,IAAI,IAAI,KAAKA,EAAC,CAAC;;AAGzB,UAAM,WAAW,aAAK,WAAW,QAAQ,OAAO,UAAU;AAC1D,WAAO,QAAQ,IAAI,MAAM,CAAC;;AAE5B,SAAO;AACT;;;ACpBM,SAAU,UAAU,MAIzB;AACC,QAAM,EAAC,QAAQ,OAAO,QAAO,IAAI;AACjC,QAAM,EAAC,EAAC,IAAI;AACZ,QAAM,EAAC,KAAI,IAAI;AAEf,mBAAiB,GAAG,WAAW;AAE/B,QAAM,QAAQ,EAAE,MAAM;AAEtB,QAAM,WAAqB,IAAI,MAAM,KAAK;AAC1C,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAS,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC,CAAC;;AAG/B,QAAM,SAAS,QAAQ,KAAK,IAAI,EAAE,MAAM,EAAE;AAC1C,QAAM,SAAS,cAAc,QAAQ,EAAE,OAAO,EAAE,OAAO,MAAM,QAAQ;AAErE,QAAM,SAAS,QAAQ,MAAM,QAAQ,UAAU,EAAE,KAAK;AACtD,SAAO,EAAC,QAAQ,OAAO,UAAU,OAAO,EAAE,MAAK;AACjD;AAEO,IAAM,kBAAgC;EAC3C,YAAY;EACZ,aAAa;EACb,YAAY;;;;AC7BR,SAAU,SACZ,QAAkB,QAAkB,OACpC,eAAuB;AAEzB,QAAM,CAAC,UAAU,WAAW,IACxB,qBAAa,0BAA0B,QAAQ,aAAa;AAChE,QAAM,WAAW,WAAW,QAAQ,OAAO;AAC3C,QAAM,UAAU,aAAK,oBACD,aAAK,cAAc,QAAQ,GAAG,QAAQ;AAC1D,QAAM,aAAa,aAAK,cAAc,WAAW;AAEjD,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,UAAM,SAAS,IAAI;AACnB,QAAIC,QAAO;AACX,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,MAAAA,SAAQ,MAAM,SAAS,CAAC;;AAE1B,YAAQ,CAAC,IAAIA;;AAGf,SAAO,EAAC,SAAS,UAAU,SAAQ;AACrC;AAEM,SAAU,KACZ,MAAqE;AAEvE,QAAM,EAAC,QAAQ,SAAS,MAAK,IAAI;AACjC,QAAM,EAAC,EAAC,IAAI;AACZ,QAAM,EAAC,MAAM,SAAQ,IAAI;AAEzB,mBAAiB,GAAG,MAAM;AAE1B,QAAM,QAAQ,EAAE,MAAM;AACtB,QAAM,OAAO,aAAK,eAAe,MAAM,EAAE,KAAK;AAE9C,QAAM,cAAc,qBAAa,mBAAmB,MAAM,KAAK;AAC/D,MAAI,gBAAgB;AACpB,MAAI,YAAY;AAChB,QAAM,0BAA0B,CAAA;AAChC,MAAI,eAAe,MAAM;AACvB,gBAAY,UAAU,EAAC,QAAQ,EAAC,EAAC,GAAG,SAAS,OAAO,EAAC,MAAM,YAAW,EAAC,CAAC;AACxE,4BAAwB,KAAK,SAAS;AACtC,oBAAgB,qBAAa,iBAAiB,cAAc,QAAQ,KAAK;;AAG3E,QAAM,QAAQ,QAAQ,KAAK,IAAI,UAAU,MAAM,EAAE;AACjD,QAAM,EAAC,SAAS,UAAU,SAAQ,IAC9B,SAAS,UAAU,OAAO,UAAU,OAAO,OAAO,aAAa;AAEnE,MAAI,cAAc;AAClB,MAAI,UAAU;AACZ,kBAAc,qBAAa,qBAAqB,UAAU,IAAI;;AAGhE,0BAAwB,QACpB,OAAK,QAAQ,8BAA8B,CAAC,CAAC;AAEjD,SAAO,QAAQ,eAAe,aAAa,UAAU,OAAO;AAC9D;AAEO,IAAM,aAA2B;EACtC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACnEd,SAAS,gBACL,SAAqB,cAAwB,WAAiB;AAChE,UAAQ,QAAQ,CAAC,OAAe,MAAa;AAC3C,QAAI,QAAQ,KAAK,SAAS,WAAW;AACnC,YAAM,YACF,aAAK,WACG,GAAG,aAAa,QAAQ,aAAK,eAAe,YAAY,CAAC,EAC5D,KAAK,GAAG;AACjB,YAAM,IAAI,MACN,WAAW,SAAS,OAAO,KAAK,kBAAkB,SAAS,GAAG;;EAEtE,CAAC;AACH;AAEA,SAAS,eACL,oBAAkC,sBAA4B;AAEhE,WAAS,MAAM,GAAG,MAAM,mBAAmB,QAAQ,EAAE,KAAK;AACxD,UAAM,SAAS,mBAAmB,GAAG;AACrC,UAAM,YAAa,QAAQ,mBAAmB,SAAS,IACnD,uBACA,mBAAmB,MAAM,CAAC,EAAE;AAChC,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,IAAI,MAAM,gCAAgC;;AAElD,QAAI,OAAO,CAAC,IAAI,GAAG;AACjB,YAAM,IAAI,MAAM,oCAAoC;;AAEtD,QAAI,OAAO,OAAO,SAAS,CAAC,IAAI,WAAW;AACzC,YAAM,IAAI,MAAM,0CAA0C;;AAE5D,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,UAAI,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,cAAM,IAAI,MAAM,iDAAiD;;;;AAIzE;AAMA,SAAS,WACL,SAAqB,cACrB,oBAAkC,sBAA4B;AAChE,QAAM,cAAuC,CAAA;AAC7C,MAAI,YAAY;AAEhB,QAAM,YAAY,aAAa,SAAS,IAAI,mBAAmB;AAC/D,QAAM,YAAY,IAAI,MAAM,SAAS,EAAE,KAAK,IAAI,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC;AAE/D,iBAAe,oBAAoB,oBAAoB;AASvD,MAAI,QAAQ;AACZ,WAAS,MAAM,GAAG,MAAM,aAAa,SAAS,GAAG,EAAE,KAAK;AACtD,aAAS,aAAa,GAAG;AACzB,UAAM,YAAY,aAAa,MAAM,CAAC;AACtC,aAAS,IAAI,GAAG,IAAI,QAAQ,GAAG,EAAE,GAAG;AAClC,gBAAU,GAAG,EAAE,KAAK,IAAI,SAAS;;;AAarC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,QAAI,QAAQ,QAAQ,CAAC;AACrB,QAAI,QAAQ,QAAQ,CAAC,IAAI;AAGzB,aAAS,MAAM,GAAG,MAAM,mBAAmB,QAAQ,EAAE,KAAK;AACxD,YAAM,SAAS,mBAAmB,GAAG;AACrC,YAAM,SAAS,MAAM,aAAa,SAAS;AAC3C,UAAI,UAAU,GAAG;AACf,cAAM,kBAAkB,UAAU,MAAM;AACxC,cAAM,QACF,gBAAgB,gBAAgB,SAAS,CAAC,IAAI,OAAO,KAAK;AAC9D,iBAAS,IAAI,OAAO,IAAI,OAAO,EAAE,GAAG;AAClC,oBAAU,MAAM,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,KAAK;;;AAGhD,cAAQ,OAAO,KAAK;AACpB,cAAQ,OAAO,KAAK;;AAEtB,QAAI,UAAU,OAAO;AACnB,kBAAY,KAAK,CAAC,OAAO,KAAK,CAAC;AAC/B,mBAAa,QAAQ;;;AAIzB,SAAO,EAAC,WAAW,aAAa,UAAS;AAC3C;AAEA,SAAS,UAAU,WAAqB;AACtC,QAAM,YAA0B,CAAA;AAChC,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,UAAM,YAAY,UAAU,CAAC,EAAE;AAC/B,UAAM,SAAS,aAAK,kBAAkB,SAAS,SAAS;AACxD,cAAU,KAAK,MAAM;AAErB,cAAU,CAAC,EAAE,QAAQ,CAAC,OAAO,MAAc,OAAO,CAAC,IAAI,KAAK;;AAG9D,SAAO;AACT;AAEA,SAAS,qBAAqB,MAAgB,YAAkB;AAC9D,QAAM,UAAU,KAAK,MAAM,GAAG,UAAU;AACxC,SAAO,QAAQ,SAAS,YAAY;AAClC,YAAQ,KAAK,CAAC;;AAGhB,WAAS,QAAQ,YAAY,QAAQ,KAAK,QAAQ,SAAS;AACzD,YAAQ,aAAa,CAAC,KAAK,KAAK,KAAK;;AAGvC,SAAO;AACT;AAIA,SAAS,iBACL,mBAA+B,wBAC/B,aAAsC,WAAmB,QACzD,aAAqB;AACvB,QAAM,SAAS,qBAAqB,wBAAwB,CAAC,EAAE,CAAC;AAChE,QAAM,UAAU,qBAAqB,aAAa,CAAC,EAAE,CAAC;AAEtD,MAAI,SAAS;AACb,aAAWC,UAAS,aAAa;AAC/B,aAAS,IAAIA,OAAM,CAAC,GAAG,IAAIA,OAAM,CAAC,GAAG,EAAE,GAAG;AACxC,eAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,eAAO,SAAS,UAAU,CAAC,IAAI,kBAAkB,IAAI,SAAS,CAAC;;AAEjE,QAAE;;;AAGR;AAEA,SAAS,UACL,mBAA+B,wBAC/B,wBAAkC,aAClC,WAAiB;AACnB,QAAM,cAAc,uBAAuB,MAAK;AAChD,cAAY,CAAC,IAAI;AAEjB,QAAM,YAAY,aAAK,kBACD,wBACA,aAAK,cAAc,WAAW,CAAC;AAErD,QAAM,cAAc,kBAAkB;AACtC,QAAM,YACF,gBAAgB,IAAI,IAAK,cAAc,uBAAuB,CAAC;AACnE,mBACI,mBAAmB,wBAAwB,aAAa,WACxD,WAAW,WAAW;AAE1B,SAAO,CAAC,WAAW,WAAW;AAChC;AACM,SAAU,iBACZ,oBAAkC,0BAClC,mBAA+B,wBAC/B,wBAAkC,SAClC,cACA,kBAAwB;AAC1B,MAAI,mBAAmB,WAAW,GAAG;AACnC,UAAM,IAAI,MAAM,sCAAsC;;AAGxD,MAAI,yBAAyB,CAAC,EAAE,WAAW,GAAG;AAC5C,UAAM,IAAI,MAAM,mCAAmC;;AAErD,QAAM,YAAY,yBAAyB,CAAC,EAAE,CAAC,IAAI;AACnD,kBAAgB,SAAS,cAAc,SAAS;AAEhD,MAAI,uBAAuB,WAAW,GAAG;AACvC,UAAM,IAAI,MAAM,6BAA6B;;AAE/C,QAAM,uBAAuB,uBAAuB,CAAC;AAIrD,QAAM,EAAC,WAAW,aAAa,UAAS,IAAI,WACxC,SAAS,cAAc,oBAAoB,oBAAoB;AAGnE,QAAM,qBAAqB,UAAU,SAAS;AAC9C,QAAM,oBAAoB,UACtB,mBAAmB,wBAAwB,wBAC3C,aAAa,SAAS;AAE1B,SAAO,CAAC,oBAAoB,kBAAkB,CAAC,GAAG,kBAAkB,CAAC,CAAC;AACxE;;;AC9MA,IAAM,YAAY;AAEZ,SAAU,gBACZ,QAAoB,aAAuB,aAC3C,QAAoB,aAAuB,QAC3C,aAAqB;AAEvB,MAAI,YAAY,SAAS,GAAG;AAC1B,UAAM,IAAI,MAAM,mCAAmC;;AAErD,MAAI,YAAY,SAAS,GAAG;AAC1B,UAAM,IAAI,MAAM,mCAAmC;;AAErD,MAAI,YAAY,SAAS,GAAG;AAC1B,UAAM,IAAI,MAAM,mCAAmC;;AAIrD,QAAM,kBAAkB,YAAY,WAAW;AAC/C,QAAM,kBAAkB,YAAY,WAAW;AAC/C,QAAM,kBAAkB,YAAY,WAAW;AAI/C,QAAM,UAAoB,CAAA;AAC1B,MAAI,CAAC,iBAAiB;AACpB,YAAQ,KAAK,YAAY,CAAC,CAAC;;AAE7B,MAAI,CAAC,iBAAiB;AACpB,YAAQ,KAAK,YAAY,CAAC,CAAC;;AAE7B,MAAI,CAAC,iBAAiB;AACpB,YAAQ,KAAK,YAAY,CAAC,CAAC;;AAG7B,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,QAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG;AACjC,YAAM,IAAI,MAAM,qDAAqD;;;AAGzE,QAAM,QAAQ,QAAQ,WAAW,IAAI,IAAI,QAAQ,CAAC;AAGlD,QAAM,iBACF,aAAK,kBAAkB,SAAS,QAAQ,CAAC;AAC7C,iBAAe,CAAC,IAAI;AACpB,WAAS,MAAM,GAAG,MAAM,OAAO,EAAE,KAAK;AACpC,UAAM,QAAQ,kBAAkB,OAAO,CAAC,IAAI,OAAO,GAAG;AACtD,UAAM,QAAQ,kBAAkB,OAAO,CAAC,IAAI,OAAO,GAAG;AACtD,UAAM,QAAQ,kBAAkB,OAAO,CAAC,IAAI,OAAO,GAAG;AACtD,QAAI,UAAU,GAAG;AACf,YAAM,IAAI,MAAM,qBAAqB;;AAEvC,QAAI;AACJ,QAAM,QAAQ,KAAO,QAAQ,SAAa,QAAQ,KAAO,QAAQ,OAAS;AACxE,aAAO;WACF;AACL,aAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,SAAS,KAAK,CAAC;AAElD,UAAI,OAAO,WAAW;AACpB,cAAM,IAAI,MAAM,yCAAyC,SAAS,EAAE;;;AAGxE,mBAAe,MAAM,CAAC,IAAI,eAAe,GAAG,IAAI;;AAGlD,QAAM,QAAQ,eAAe,KAAK;AAGlC,QAAM,gBACF,aAAK,kBAAkB,aAAa,KAAK;AAE7C,MAAI,aAAa;AACjB,WAAS,MAAM,GAAG,MAAM,OAAO,EAAE,KAAK;AACpC,UAAM,UAAU,eAAe,MAAM,CAAC,IAAI,eAAe,GAAG;AAC5D,QAAI,QAAQ,kBAAkB,OAAO,CAAC,IAAI,OAAO,GAAG;AACpD,UAAM,QAAQ,kBAAkB,OAAO,CAAC,IAAI,OAAO,GAAG;AACtD,aAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,oBAAc,YAAY,IAAI;AAC9B,eAAS;;;AAIb,SAAO,CAAC,gBAAgB,aAAa;AACvC;;;ACpFA,IAAO,mBAAmB,qBAAa;AAGvC,IAAM,yBAAN,MAAM,wBAAsB;EAG1B,YACY,OAA2B,YAC3B,QAA4B,aAC5B,aAA+B,cAC/B,mBACS,oBACA,0BACjB,yBAAiC;AANzB,SAAA,QAAA;AAA2B,SAAA,aAAA;AAC3B,SAAA,SAAA;AAA4B,SAAA,cAAA;AAC5B,SAAA,cAAA;AAA+B,SAAA,eAAA;AAC/B,SAAA,oBAAA;AACS,SAAA,qBAAA;AACA,SAAA,2BAAA;AAEnB,SAAK,oBACD,qBAAa,2BAA2B,uBAAuB;AACnE,SAAK,aAAa,qBAAa,cAAc,KAAK,iBAAiB;EACrE;EAEQ,+BAA+B,WAAiB;AACtD,QAAI,KAAK,kBAAkB,CAAC,MAAM,iBAAiB,gBAAgB;AACjE,aAAO,KAAK,kBAAkB,YAAY,CAAC;WACtC;AACL,aAAO,KAAK,kBAAkB,SAAS;;EAE3C;;EAGQ,sBAAsB,WAAiB;AAC7C,QAAI,KAAK,kBAAkB,CAAC,MAAM,iBAAiB,gBAAgB;AACjE,aAAO,KAAK,mBAAmB,YAAY,CAAC;WACvC;AACL,aAAO,KAAK,mBAAmB,SAAS;;EAE5C;EAEQ,YAAY,WAAiB;AACnC,UAAM,qBAAqB,KAAK,sBAAsB,YAAY,CAAC;AACnE,YAAQ,KAAK,+BAA+B,YAAY,CAAC,GAAG;MAC1D,KAAK,iBAAiB;AACpB,eAAO,wBAAuB,sBAAsB,kBAAkB;MACxE,KAAK,iBAAiB;AACpB,eAAO,wBAAuB,oBAAoB,kBAAkB;MACtE;AACE,cAAM,IAAI,MAAM,gCACZ,iBAAiB,KAAK,+BAClB,YAAY,CAAC,CAAC,CAAC,EAAE;;EAE/B;EAEA,OAAO,oBAAoB,UAAoB;AAC7C,UAAM,eAAe,SAAS;AAC9B,QAAI,iBAAiB,KAAK,iBAAiB,GAAG;AAC5C,aAAO;;AAET,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,eAAe,GAAG,EAAE,GAAG;AACzC,YAAM,eAAe,SAAS,IAAI,CAAC,IAAI,SAAS,CAAC;AACjD,UAAI,eAAe,UAAU;AAC3B,mBAAW;;;AAGf,WAAO;EACT;EAEA,OAAO,sBAAsB,aAAuB;AAClD,UAAM,cAAc,YAAY;AAChC,QAAI,gBAAgB,GAAG;AACrB,aAAO;;AAET,QAAI,kBAAkB;AACtB,QAAI,uBAAuB,YAAY,CAAC;AACxC,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACpC,YAAM,QAAQ,YAAY,CAAC;AAC3B,UAAI,UAAU,sBAAsB;AAClC,+BAAuB;AACvB,mBAAW,KAAK,IAAI,IAAI,iBAAiB,QAAQ;AACjD,0BAAkB;;;AAGtB,WAAO,KAAK,IAAI,cAAc,iBAAiB,QAAQ;EACzD;EAEQ,sBACJ,GAAe,QAAkB,YAAY,MAAI;AACnD,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,EAAE,CAAC,MAAM,IAAI;AACf,eAAO,CAAA;;AAET,YAAM,IAAI,MACN,gFAAgF;;AAGtF,WAAO,UAAU,GAAG,SAAS;EAC/B;EAEQ,oBAAoB,UAAgB;AAC1C,UAAM,aAAa,KAAK;AACxB,UAAM,oBAAoB,KAAK;AAE/B,yBAAa,0BAA0B,mBAAmB,UAAU;AAEpE,UAAM,QAAQ,KAAK,sBAAsB,KAAK,OAAO,KAAK,UAAU;AACpE,UAAM,cAAc,qBAAa,kCAC7B,KAAK,YAAY,OAAO,UAAU;AAEtC,UAAM,SAAS;AAEf,QAAI,OAAO,CAAC,IAAI,GAAG;AACjB,aAAO,CAAC,IAAI;;AAEd,aAAS,IAAI,GAAG,KAAK,KAAK,YAAY,EAAE,GAAG;AACzC,UAAI,OAAO,CAAC,IAAI,GAAG;AACjB,eAAO,CAAC,IAAI,KAAK,YAAY,CAAC;;;AAIlC,WAAO;EACT;;;;;;;;;;;EAYQ,gCACJ,gBAAwB,uBACxB,sBAA4B;AAC9B,UAAM,eAAe,KAAK,IAAI,gBAAgB,oBAAoB;AAClE,UAAM,SAAmB,CAAA;AACzB,QAAI,qBAAqB;AACzB,aAAS,IAAI,GAAG,IAAI,cACf,EAAE,GAAG,sBAAsB,uBAAuB;AACrD,aAAO,KAAK,kBAAkB;;AAEhC,aAAS,IAAI,cAAc,IAAI,gBAAgB,EAAE,GAAG;AAClD,aAAO,KAAK,EAAE;;AAEhB,iBAAK,OACD,OAAO,WAAW,gBAClB,MAAM,yDAAyD;AAEnE,WAAO;EACT;EAEQ,6BACJ,UAAsB,mBACtB,uBAA+B,YAAkB;AACnD,UAAM,eAAe,SAAS;AAC9B,UAAM,SAAmB,CAAA;AACzB,aAAS,IAAI,GAAG,IAAI,eAAe,GAAG,EAAE,GAAG;AACzC,YAAM,YAAY,SAAS,IAAI,CAAC,IAAI,SAAS,CAAC;AAC9C,UAAI,aAAa,KAAK,IAAI,YAAY,SAAS;AAC/C,UAAI,2BAA2B,kBAAkB,CAAC;AAElD,UAAI,6BAA6B,IAAI;AACnC,qBAAa;;AAEf,eAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,eAAO,KAAK,wBAAwB;AACpC,oCAA4B;;AAE9B,eAAS,IAAI,GAAG,IAAI,YAAY,YAAY,EAAE,GAAG;AAC/C,eAAO,KAAK,EAAE;;;AAGlB,QAAI,eAAe,KAAK,OAAO,WAAW,SAAS,eAAe,CAAC,GAAG;AACpE,YAAM,IAAI,MAAM,yBAAyB;;AAG3C,WAAO;EACT;;;;;;;;;;;;;;;;;;;;;;EAuBQ,+BACJ,aAAyB,mBACzB,uBAA+B,YAAkB;AACnD,UAAM,YAAY,YAAY;AAC9B,UAAM,SAAmB,CAAA;AACzB,QAAI,cAAc,GAAG;AACnB,aAAO,CAAA;;AAGT,QAAI,sBAAsB;AAC1B,QAAI,oBAAoB,YAAY,CAAC;AAErC,QAAI,qBAAqB,kBAAkB,QAAQ;AACjD,YAAM,IAAI,MACN,yBAAyB,iBAAiB,4BACtC,kBAAkB,MAAM,EAAE;;AAGpC,QAAI,qBAAqB,kBAAkB,iBAAiB;AAC5D,WAAO,KAAK,kBAAkB;AAC9B,aAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,YAAM,iBAAiB,YAAY,CAAC;AACpC,UAAI,mBAAmB,mBAAmB;AACxC,YAAI,sBAAsB,GAAG;AAC3B,YAAE;AACF,cAAI,sBAAsB,YAAY;AACpC,kCAAsB;iBACjB;AACL,iCAAqB;;;aAGpB;AACL,8BAAsB;AACtB,4BAAoB;AAEpB,YAAI,kBAAkB,kBAAkB,QAAQ;AAC9C,gBAAM,IAAI,MACN,sBAAsB,cAAc,2BAChC,kBAAkB,MAAM,EAAE;;AAGpC,6BAAqB,kBAAkB,cAAc;;AAEvD,aAAO,KAAK,kBAAkB;;AAGhC,QAAI,OAAO,WAAW,YAAY,QAAQ;AACxC,YAAM,IAAI,MAAM,kBAAkB;;AAGpC,WAAO;EACT;EAEQ,qBACJ,WAAmB,mBACnB,uBAA+B,YAAkB;AACnD,UAAM,qBAAqB,KAAK,sBAAsB,SAAS;AAC/D,UAAM,gBAAgB,KAAK,+BAA+B,SAAS;AACnE,YAAQ,eAAe;MACrB,KAAK,iBAAiB;AACpB,eAAO,KAAK,+BACR,oBAAoB,mBAAmB,uBACvC,UAAU;MAChB,KAAK,iBAAiB;AACpB,YAAI,mBAAmB,SAAS,IAAI,kBAAkB,QAAQ;AAC5D,gBAAM,IAAI,MAAM,mDACZ,mBAAmB,SAAS,CAAC,MAAM,kBAAkB,MAAM,EAAE;;AAEnE,eAAO,KAAK,6BACR,oBAAoB,mBAAmB,uBACvC,UAAU;MAChB;AACE,cAAM,IAAI,MACN,+BAA+B,iBAAiB,aAAa,CAAC,EAAE;;EAE1E;EAEQ,wBAAqB;AAC3B,UAAM,uBAAuB,KAAK,mBAAmB,CAAC;AACtD,QAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,YAAM,IAAI,MAAM,+BAA+B;;AAEjD,UAAM,qBAAqB,KAAK,kBAAkB,CAAC;AACnD,YAAQ,oBAAoB;MAC1B,KAAK,iBAAiB;AACpB,eAAO,qBAAqB,CAAC;MAC/B,KAAK,iBAAiB;AACpB,cAAM,IAAI,MAAM,gDAAgD;MAClE,KAAK,iBAAiB;AACpB,eAAO,KAAK,yBAAyB,CAAC,EAAE,CAAC,IAAI;MAC/C;AACE,cAAM,IAAI,MACN,sBAAsB,iBAAiB,kBAAkB,CAAC,EAAE;;EAEtE;EAEA,UAAO;AACL,UAAM,uBAAuB,KAAK,mBAAmB,CAAC;AACtD,QAAI,qBAAqB,UAAU,GAAG;AACpC,YAAM,IAAI,MACN,sEACuC;;AAE7C,UAAM,iBAAiB,KAAK,sBAAqB;AACjD,UAAM,aAAa,KAAK,oBAAoB,cAAc;AAC1D,UAAM,aAAuB,IAAI,MAAM,KAAK,aAAa,CAAC;AAE1D,eAAW,WAAW,SAAS,CAAC,IAAI;AACpC,aAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC/C,iBAAW,CAAC,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC;;AAGtD,UAAM,cAAwB,UAAU,YAAY,KAAK;AACzD,UAAM,eACF,aAAK,kBACD,KAAK,aAAa,aAAK,cAAc,WAAW,CAAC;AAEzD,UAAM,WAAW,WAAW,CAAC,IAAI,WAAW,CAAC;AAC7C,QAAI,WAAW,GAAG;AAChB,UAAI,cAAc,KAAK,gCACnB,gBAAgB,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAChD,eAAS,IAAI,GAAG,KAAK,KAAK,YAAY,EAAE,GAAG;AACzC,cAAM,iBAAiB,KAAK,qBACxB,IAAI,GAAG,aAAa,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AACpD,sBAAc;;AAGhB,WAAK,UAAU,KAAK,YAAY,aAAa,cAAc,WAAW;;AAGxE,WAAO,CAAC,aAAa,YAAY;EACnC;EACA,UACI,YAAoB,aAAuB,cAC3C,aAAqB;AACvB,QAAI,aAAa,WAAW,GAAG;AAC7B;;AAGF,UAAM,aAAa,KAAK;AACxB,UAAM,aAAa;AAEnB,QAAI,eAAe,YAAY,MAAK;AACpC,mBAAe,aAAa,MAAM,aAAa,CAAC;AAChD,UAAM,mBAAmB,aAAK,cAAc,YAAY;AACxD,UAAM,kBAAkB,YAAY;AAIpC,QAAI,eAAe,KAAK;AACxB,QAAI,aAAa,WAAW,oBAAoB,aAAa,WAAW,GAAG;AACzE,YAAM,WAAW,KAAK;AACtB,WAAK,MAAK;AACR,cAAM,qBAAqB,QAAQ,cAAc,QAAQ;AACzD,cAAM,eAAe,YAAY,oBAAoB,YAAY;AACjE,uBAAe,aAAa,SAAQ;MACtC,CAAC;;AAMH,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,SAAS;AACb,aAAS,OAAO,GAAG,QAAQ,iBAAiB,EAAE,MAAM;AAElD,UAAI,OAAO,OAAO,kBAAkB,YAAY,IAAI,IAAI;AAIxD,UAAI,SAAS,QAAQ;AACnB,UAAE;AACF;;AAOF,UAAI,WAAW,QAAQ;AAErB,cAAM,MAAM,WAAW,SAAS,WAAW,gBAAgB;AAC3D,cAAM,MAAM,WAAW,SAAS,WAAW,gBAAgB;AAC3D,cAAM,SAAS,SAAS,YAAY;AACpC,kBAAU,KAAK,KAAK,KAAK;;AAI3B,UAAI,QAAQ,iBAAiB;AAE3B,cAAM,aAAa,aAAa;AAChC,eAAO,KAAK,MAAM,aAAa,gBAAgB;;AAEjD,UAAI,OAAO,QAAQ;AACjB,YAAI,KAAK,aAAa,WAAW,GAAG;AAClC,qBACK,SAAS,SAAS,kBAAkB,OAAO,gBAAgB,EAC3D,KAAK,KAAK,aAAa,CAAC,CAAC;AAC9B,mBAAS;eACJ;AACL,iBAAO,OAAO,QAAQ;AACpB,kBAAM,MAAM,WAAW,MAAM,SAAS,gBAAgB;AACtD,sBAAU,KAAK,cAAc,gBAAgB;AAC7C,cAAE;;;;AAMR,UAAI,OAAO,GAAG;AAEZ,mBAAW,OAAO;AAClB,mBAAW;aACN;AAEL,mBAAW;AACX,mBAAW;AACX,iBAAS,WAAW;;;EAG1B;;AAGF,SAAS,UAAU,KAAiB,KAAiB,MAAY;AAC/D,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAI,CAAC,IAAI,IAAI,CAAC;;AAElB;AAEA,SAAS,UAAU,OAA4B,WAAkB;AAC/D,QAAM,MAAgB,CAAA;AACtB,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,GAAG;AACX,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,aAAa,GAAG,eAAe;;AAEjD,UAAI,MAAM,IAAI;AACZ,cAAM,IAAI,MAAM,aAAa,GAAG,gBAAgB;;AAElD,YAAM;;AAER,QAAI,KAAK,GAAG;;AAGd,SAAO;AACT;AAEM,SAAU,yBACZ,OAAmB,aAAuB,QAC1C,aAAuB,aAAuB,cAC9C,mBAA6B,oBAC7B,0BACA,mBAA2B;AAC7B,SAAO,IAAI,uBACA,OAAO,aAAa,QAAQ,aAAa,aAAa,cACtD,mBAAmB,oBAAoB,0BACvC,iBAAiB,EACvB,QAAO;AACd;;;AC3cM,SAAU,UACZ,OAAe,MAAc,MAC7B,OAAwB;AAC1B,QAAM,gBAAgB,UAAU;AAChC,QAAM,8BAA8B,QAAQ,QAAQ,OAAO;AAC3D,QAAM,8BAA8B,OAAO,SAAS,OAAO;AAE3D,MAAI,iBAAiB,+BACjB,6BAA6B;AAC/B,WAAO,aAAK,oBAAoB,GAAG,KAAK;;AAG1C,QAAM,cAAc,KAAK,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC;AAC7D,QAAM,SAAS,aAAK,oBAAoB,aAAa,KAAK;AAE1D,MAAI,OAAO,SAAS,SAAS,GAAG;AAG9B,WAAO;;AAGT,SAAO,CAAC,IAAI;AACZ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI;;AAE9B,SAAO;AACT;;;ACvBO,IAAM,YAAY,sBAAsB,CAAC,OAAO,IAAI,KAAK,KAAK,EAAE,CAAC;AACjE,IAAM,QAAQ,wBAAwB,OAAO,SAAS;AAEtD,IAAM,cAA4B;EACvC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACHR,SACN,YACI,SAAmC,SACnC,OAAiB,YAAoB,WAAmB,YACxD,WAAmB,SACnB,cACA,gBAAuB;AACzB,QAAM,eAAe,CAAC,aAAa,WAAW,SAAS;AAEvD,QAAM,cAAc,QAAQ;AAC5B,QAAM,cAAc,QAAQ;AAE5B,MAAI,eAAe,GAAG;AACpB,WAAO,OAAO,OAAsB,QAAQ,KAAK;;AAGnD,QAAM,SAAU,wBAAwB,eACpC,eACA,OAAO,cAAc,QAAQ,KAAK;AACtC,MAAI,OAAO,iBAAiB,UAAU;AACnC,WAAO,OAAoB,KAAK,YAAY;aACpC,OAAO,iBAAiB,UAAU;AAC1C,WAAO,OAAsB,KAAK,YAAY;aACtC,OAAO,iBAAiB,WAAW;AAC3C,WAAO,OAAsB,KAAK,CAAC,YAAY;;AAGlD,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAM,QAAQ,CAAA;AACd,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,YAAM,MAAM,YAAY,IAAI,YAAY,CAAC;AACzC,YAAM,KAAK,GAAG;AACd,sBAAgB,MAAM,QAAQ,CAAC;;AAGjC,QAAI,eAAe,KAAK,gBAAgB,aAAa,WAAW;AAC9D,YAAM,IAAI,MAAM,oBAAoB,KAAK,wBAAwB,KAAK,EAAE;;AAG1E,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAI,gBAAgB;AACjB,eAAO,OAAsB,eAAe,YAAY,CAAC,KACrD,YAA2B,IAAI,YAAY,CAAC;aAC5C;AACL,eAAO,OAAO,eAAe,YAAY,CAAC,IAAI,QAAQ,SAAS,IAC3D,YAAY,CAAC,IACb,YAAY,IAAI,YAAY,CAAC;;;;AAKvC,SAAO;AACT;;;ACxDO,IAAM,cACT,sBAAsB,CAAC,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE;AAClD,IAAM,UACT,gBAAgB,SAAS,CAAC,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE;AAErD,IAAM,gBAA8B;EACzC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACRR,SAAU,UACZ,MAAqB,OAAiB,MAAgB,OACtD,OAAe;AACjB,QAAM,cAAc,mBAAW,iBAAiB,OAAO,OAAO,IAAI;AAClE,QAAM,SAAS,aAAK,cAAc,IAAI;AACtC,QAAM,WAAW,aAAK,eAAe,KAAK;AAE1C,MAAI,aAAa;AACf,UAAM,aAAa,mBAAW,kBAAkB,OAAO,QAAQ;AAE/D,QAAI,UAAU,UAAU;AACtB,aAAQ,KAAsB,MAAM,YAAY,aAAa,MAAM;;AAGrE,WAAQ,KAAoB,SAAS,YAAY,aAAa,MAAM;;AAGtE,QAAM,cAAc,UAAU,WAC1B,qBAAa,uBAAuB,IAAoB,IACxD;AAEJ,QAAM,QAAQ,OAAO,OAAO,OAAO,WAAW;AAC9C,QAAM,SAAS,OAAO,MAAM,KAAK;AACjC,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,GAAG;AACpC,UAAM,SAAS,OAAO,WAAW,CAAC;AAClC,UAAM,QAAQ,OAAO,IAAI,CAAC,KAAa,MAAM,MAAM,MAAM,CAAC,CAAC;AAC3D,WAAO,IAAI,MAAM,IAAI,GAAG,KAAK,GAAG,GAAG,MAAM;;AAG3C,MAAI,UAAU,UAAU;AACtB,WAAO,qBAAa,uBAAuB,OAAO,MAAkB;;AAEtE,SAAO,OAAO;AAChB;AAEM,SAAU,MACZ,MAAuE;AAEzE,QAAM,EAAC,QAAQ,SAAS,MAAK,IAAI;AACjC,QAAM,EAAC,EAAC,IAAI;AACZ,QAAM,EAAC,OAAO,KAAI,IAAI;AAEtB,mBAAiB,GAAG,OAAO;AAE3B,QAAM,CAAC,QAAQ,KAAK,IAAI,mBAAW,iBAAiB,GAAG,OAAO,IAAI;AAClE,qBAAW,kBAAkB,GAAG,QAAQ,KAAK;AAE7C,QAAM,OAAO,QAAQ,KAAK,IAAI,EAAE,MAAM,EAAE;AACxC,QAAM,UAAU,UAAU,MAAM,QAAQ,OAAO,EAAE,OAAO,EAAE,KAAK;AAC/D,SAAO,QAAQ,eAAe,OAAO,EAAE,OAAO,OAAO;AACvD;AAEO,IAAM,cAA4B;EACvC,YAAY;EACZ,aAAa;EACb,YAAY;;;;AC1DR,SAAU,wBACZ,SAAqB,cAAwB,cAC7C,QAAoB,aAAuB,YAC3C,cAAoB;AAEtB,QAAM,eAAe,aAAa,CAAC;AACnC,QAAM,YAAY,WAAW,CAAC;AAE9B,QAAM,oBAA+B,IAAI,MAAM,SAAS;AACxD,QAAM,kBAA4B,IAAI,MAAM,YAAY;AAExD,QAAM,OAAO,aAAa,CAAC;AAE3B,MAAI,cAAc,GAAG;AACnB,QAAI,iBAAiB,GAAG;AACtB,YAAM,IAAI,MACN,qBAAa,gDACT,YAAY,CAAC;;AAEvB,UAAM,gBAAgB,aAAK,kBAAkB,cAAc,CAAC;AAC5D,UAAM,eAAe,aAAK,kBAAkB,aAAa,CAAC;AAC1D,WAAO;MACL;MAAe,CAAC,GAAG,IAAI;MAAG;MAAc;MAAmB;;;AAI/D,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,QAAM,YAAsB,IAAI,MAAM,SAAS,EAAE,KAAK,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,cAAc,EAAE,GAAG;AAErC,UAAM,MAAM,QAAQ,IAAI,IAAI;AAC5B,QAAI,MAAM,GAAG;AACX,YAAM,IAAI,MACN,qBAAa,gDAAgD,GAAG,GAAG,CAAC;;AAE1E,QAAI,OAAO,WAAW;AACpB,YAAM,IAAI,MACN,qBAAa,kDACT,GAAG,KAAK,SAAS,CAAC;;AAE5B,MAAE,UAAU,GAAG;AACf,qBAAiB,kBAAmB,OAAO;AAC3C,qBAAiB;;AAGnB,MAAI,cAAc;AAClB,WAAS,MAAM,GAAG,MAAM,WAAW,EAAE,KAAK;AAExC,UAAM,WAAY,UAAU,GAAG,MAAM;AACrC,sBAAkB,GAAG,IAAI;AACzB,kBAAc,eAAe,CAAC;AAE9B,cAAU,GAAG,IAAI,KAAK,IAAI,UAAU,GAAG,GAAG,CAAC;AAO3C,QAAI,MAAM,GAAG;AACX,gBAAU,GAAG,KAAK,UAAU,MAAM,CAAC;;;AAIvC,MAAI,eAAe,gBAAgB;AACjC,UAAM,gBAA4B;AAClC,UAAM,eAA2B;AACjC,aAAS,IAAI,GAAG,IAAI,cAAc,EAAE,GAAG;AACrC,sBAAgB,CAAC,IAAI;;AAEvB,WAAO;MACL;MAAe,CAAC,cAAc,IAAI;MAAG;MAAc;MACnD;;SAEG;AACL,UAAM,mBAAmB,UAAU,YAAY,CAAC;AAChD,UAAM,gBACF,aAAK,kBAAkB,cAAc,mBAAmB,IAAI;AAEhE,UAAM,eACF,aAAK,kBAAkB,aAAa,gBAAgB;AACxD,UAAM,cAAwB,IAAI,MAAM,SAAS,EAAE,KAAK,CAAC;AAGzD,aAAS,IAAI,GAAG,IAAI,cAAc,EAAE,GAAG;AAErC,YAAM,MAAM,QAAQ,IAAI,IAAI;AAC5B,YAAM,SAAS,YAAY,GAAG;AAC9B,YAAM,WAAY,QAAQ,IAAK,IAAI,UAAU,MAAM,CAAC,KAAK;AACzD,kBAAY,GAAG;AACf,eAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAE7B,sBAAc,UAAU,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC;;AAE1D,mBAAa,OAAO,IAAI,OAAO,CAAC;AAEhC,sBAAgB,CAAC,IAAI;;AAIvB,aAAS,MAAM,GAAG,MAAM,WAAW,EAAE,KAAK;AACxC,YAAM,WAAW,YAAY,GAAG;AAChC,UAAI,aAAa,GAAG;AAClB,cAAM,gBAAiB,QAAQ,IAAK,IAAI,UAAU,MAAM,CAAC;AAIzD,sBAAc,gBAAgB,OAAO,CAAC,IAAI;AAC1C,iBAAS,MAAM,GAAG,MAAM,MAAM,EAAE,KAAK;AACnC,wBAAc,gBAAgB,OAAO,GAAG,IAAI;;AAE9C,qBAAa,aAAa,IAAI;;;AAGlC,WAAO;MACL;MAAe,CAAC,kBAAkB,IAAI;MAAG;MAAc;MACvD;;;AAGN;;;ACzHM,SAAU,kBACZ,cAA0B,mBAA6B,YACvD,YACA,aAAqB;AACvB,QAAM,YAAY,aAAK,cAAc,UAAU;AAC/C,QAAM,MAAM,kBAAkB,CAAC;AAC/B,QAAM,aAAa,YAAY;AAI/B,QAAM,cAAwB,CAAA;AAC9B,MAAI,UAAU;AACd,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,UAAM,OAAO,YAAY,CAAC;AAC1B,QAAI,SAAS,IAAI;AACf,UAAI,iBAAiB,IAAI;AACvB,cAAM,IAAI,MACN,qBACK,yDACG,cAAc,CAAC,CAAC;;AAE9B,qBAAe;AACf,kBAAY,KAAK,CAAC;WACb;AACL,UAAI,OAAO,GAAG;AACZ,cAAM,IAAI,MACN,qBAAa,8CACT,GAAG,IAAI,CAAC;;AAElB,iBAAW;AACX,kBAAY,KAAK,IAAI;;;AAGzB,MAAI,iBAAiB,IAAI;AACvB,QAAI,WAAW,GAAG;AAChB,YAAM,IAAI,MACN,qBAAa,qDAAoD,CAAE;;AAEzE,UAAM,UAAU,KAAK,MAAM,YAAY,OAAO;AAC9C,QAAI,UAAU,YAAY,WAAW;AACnC,YAAM,IAAI,MACN,qBAAa,gDACT,YAAY,WAAW,CAAC;;AAGlC,gBAAY,YAAY,IAAI;;AAE9B,QAAM,aAAa,aAAK,cAAc,WAAW;AACjD,MAAI,eAAe,WAAW;AAC5B,UAAM,IAAI,MACN,qBAAa,gDACT,YAAY,WAAW,CAAC;;AAGlC,QAAM,YAAY,WAAW;AAC7B,QAAM,eAAyB,CAAA;AAC/B,MAAI,YAAY,GAAG;AACjB,iBAAa,YAAY,CAAC,IAAI;AAC9B,aAAS,IAAI,YAAY,GAAG,KAAK,GAAG,EAAE,GAAG;AACvC,mBAAa,CAAC,IAAI,aAAa,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC;;;AAI5D,QAAM,gBAA0B,CAAA;AAChC,MAAI,aAAa,GAAG;AAClB,kBAAc,aAAa,CAAC,IAAI;AAChC,aAAS,IAAI,aAAa,GAAG,KAAK,GAAG,EAAE,GAAG;AACxC,oBAAc,CAAC,IAAI,cAAc,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC;;;AAI/D,QAAM,aACF,aAAK,kBAAkB,YAAY,MAAM,UAAU;AACvD,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,QAAI,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAElC,YAAM,aAAa,IAAI,YAAY,CAAC,IAAI,aAAa,CAAC;;AAExD,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAEnC,iBAAW,IAAI,aAAa,CAAC,IAAI,KAAK,MAAM,KAAK,cAAc,CAAC,CAAC;AACjE,YAAM,cAAc,CAAC;;;AAGzB,SAAO,CAAC,YAAY,CAAC,KAAK,UAAU,GAAG,WAAW;AACpD;;;ACvFM,SAAU,2BACZ,OAAmB,YAAsB,YACzC,SAAqB,YAAwB,SAAS,OACtD,eAAe,GAAC;AAClB,QAAM,aAAa,QAAQ;AAG3B,QAAM,YAAsB,CAAC,WAAW,CAAC,GAAG,MAAM,SAAS,WAAW,CAAC,CAAC;AACxE,QAAM,SAAS,UAAU,CAAC;AAG1B,QAAM,uBACF,aAAa,IAAI,WAAW,aAAa,CAAC,IAAI,IAAI;AACtD,QAAM,aAAa;AAEnB,MAAI,aAAa,GAAG;AAClB,UAAM,IAAI,MACN,qBAAa,wDAAuD,CAAE;;AAG5E,QAAM,cAAc,WAAW,MAAK;AACpC,cAAY,CAAC,IAAI;AAEjB,QAAM,eACF,YAAY,OAAO,CAAC,SAAS,UAAU,UAAU,OAAO,CAAC;AAE7D,QAAM,SAAS,aAAK,kBAAkB,YAAY,YAAY;AAI9D,MAAI,eAAe,GAAG;AACpB,QAAI,aAAa,GAAG;AAClB,aAAO,KAAK,YAAY;;AAE1B,WAAO,CAAC,QAAQ,WAAW;;AAG7B,MAAI,cAAc,GAAG;AACnB,UAAM,IAAI,MACN,qBAAa,wDAAuD,CAAE;;AAG5E,MAAI,QAAQ,GAAG,MAAM;AAErB,MAAI,qBAAqB;AACzB,MAAI,WAAW,WAAW,KAAK;AAE/B,SAAO,MAAM;AAEX,QAAI,YAAY;AAChB,QAAI,MAAM,YAAY;AACpB,kBAAY,WAAW,GAAG;AAC1B,UAAI,aAAa,WAAW;AAC1B,UAAE;AACF;;AAGF,UAAI,YAAY,WAAW;AACzB,cAAM,IAAI,MAAM,qBACX,6DAA4D,CAAE;;;AAIvE,QAAI,WAAW,KAAK,YAAY,YAAY;AAC1C,YAAM,IAAI,MACN,qBAAa,yDACT,UAAU,UAAU,CAAC;;AAK/B,QAAI,WAAW,oBAAoB;AACjC,aAAO,KAAK,cAAc,qBAAqB,QAAQ,WAAW,MAAM;;AAG1E,aAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,YAAM,QAAQ,QAAQ,CAAC;AACvB,UAAI,QAAQ,KAAK,SAAS,UAAU,CAAC,GAAG;AACtC,cAAM,IAAI,MACN,qBAAa,uDACT,GAAG,QAAQ,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;;AAEtC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAO,WAAW,SAAS,CAAC,KAAK,MAAM,QAAQ,SAAS,CAAC;;;AAI7D,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAO,WAAW,SAAS,CAAC,KAAK,MAAM;;;AAI3C,YAAQ;AACR,MAAE;AACF,yBAAqB,WAAW;AAChC,eAAW;AACX,QAAI,MAAM,YAAY;AACpB;;;AAKJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,KAAK,cAAc,qBAAqB,QAAQ,aAAa,MAAM;;AAG5E,SAAO,CAAC,QAAQ,WAAW;AAC7B;;;ACzGO,IAAM,WAAW,sBAAsB,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;AAC5D,IAAM,OAAO,gBAAgB,MAAM,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;AAExD,IAAM,aAA2B;EACtC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACNP,IAAM,wBACT,6BAA8B,CAAC,GAAW,MAAa;AACrD,QAAM,OAAO,IAAI;AACjB,SAAO,OAAO;AAChB,CAAE;AACC,IAAM,oBACT,iBAAiB,mBAAmB,qBAAqB;AAEtD,IAAM,0BAAwC;EACnD,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACZP,IAAM,yBAAyB,sBAC5B,CAAC,GAAW,UAAS;AAC3B,QAAM,EAAC,SAAS,eAAe,QAAO,IACpC;AAEF,SAAO,EAAE,QAAQ,IAAI,OAAO,SAAS,gBAAgB,MAAM,EAAE,GAAG,OAAO;AAC3E,CAAC;AAED,IAAM,qBACJ,wBAAwB,oBAAoB,sBAAsB;AAE7D,IAAM,2BAAyC;EACpD,YAAY;EACZ,aAAa;EACb,YAAY;;;;AChBR,SAAU,iBACZ,UAAoB,MAAuB,SAC3C,OAAe;AACjB,QAAM,SAAS,OAAO,UAAU,KAAK,KAAK;AAE1C,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,KAAK;AACpC,UAAM,MAAM,OAAO,WAAW,CAAC;AAE/B,UAAM,SAAmB,IAAI,MAAM,IAAI,MAAM;AAC7C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,aAAO,CAAC,IAAI,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,MAAM,CAAC;;AAE3C,WAAO,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG;;AAGxC,SAAO;AACT;;;ACVA,IAAM,iBAAN,MAAoB;EAQlB,YACI,WAAmB,aAAuB,SAC1C,UAAkB,UAAkB,wBAA+B;AACrE,SAAK,YAAY,aAAK,aAAa,SAAS;AAC5C,SAAK,cAAc;AACnB,SAAK,UAAU,aAAK,aAAa,OAAO;AACxC,SAAK,WAAW,aAAK,aAAa,QAAQ;AAC1C,SAAK,WAAW;AAChB,SAAK,gBAAgB;EACvB;EAEQ,YAAY,YAAkB;AAIpC,WAAO,KAAK,IACR,KAAK,WAAW,IAAI,aAAa,IAAI,KAAK,UAAU,aAAa,CAAC;EACxE;EAEQ,aAAa,QAAgB,YAAkB;AACrD,UAAM,WAAW,KAAK,YAAY,UAAU;AAC5C,WAAO,KAAK,IAAI,GAAK,SAAS,IAAI,WAAY,aAAc,CAAC;EAC/D;EAEQ,aACJ,MAAoB,YAAoB,QACxC,kBAA0B,WAAmB,YAAkB;AACjE,aAAS,aAAa,GAAG,aAAa,WAAW,EAAE,YAAY;AAC7D,YAAM,WAAW,KAAK,YAAY,UAAU;AAC5C,YAAM,cAAc,KAAK,IAAI,GAAG,WAAW,UAAU;AACrD,YAAM,eACF,KAAK,IAAI,GAAG,YAAY,aAAa,aAAa,GAAG;AACzD,YAAM,YAAY,cAAc,cAAc;AAC9C,YAAM,iBACF,cAAc,cAAc,IAAI,IAAI,aAAa;AAIrD,UAAI,YAAY;AAEhB,mBAAa,cAAc,KAAK,QAAQ;AAExC,eAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,qBAAa,KAAK,iBAAiB,CAAC,EAAE;;AAGxC,mBAAa,eAAe,KAAK,SAAS;AAE1C,YAAM,gBAAgB,cAAc,eAAe,YAAY;AAC/D,mBAAa,gBAAgB,KAAK,UAAU;AAG5C,aAAO,mBAAmB,UAAU,IAAI,IAAI,WAAW,SAAS;AAChE,YAAM,QAAQ,OAAO,mBAAmB,UAAU;AAElD,UAAI,iBAAiB;AACrB,YAAM,gBAAgB,CAAC,QACnB,IAAI,QAAQ,CAAC,UAAU,MAAM,gBAAgB,IAAI,KAAK;AAE1D,eAAS,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACpC,sBAAc,KAAK,OAAO;AAC1B,sBAAc,KAAK,SAAS;;AAG9B,eAAS,IAAI,GAAG,IAAI,YAAY,GAAG,EAAE,GAAG;AACtC,sBAAc,KAAK,iBAAiB,CAAC,CAAC;AACtC,sBAAc,KAAK,SAAS;;AAI9B,UAAI,YAAY,GAAG;AAIjB,sBAAc,KAAK,iBAAiB,YAAY,CAAC,CAAC;AAClD,iBAAS,IAAI,GAAG,IAAI,cAAc,EAAE,GAAG;AACrC,wBAAc,KAAK,SAAS;AAC5B,wBAAc,KAAK,QAAQ;;aAExB;AAKL,iBAAS,IAAI,GAAG,IAAI,eAAe,GAAG,EAAE,GAAG;AACzC,wBAAc,KAAK,QAAQ;AAC3B,wBAAc,KAAK,SAAS;;AAE9B,sBAAc,KAAK,QAAQ;;;EAGjC;;;;EAKO,QAAQ,MAAoB,QAAkB;AAInD,UAAM,gBAAgB,KAAK;AAC3B,UAAM,aAAa,OAAO;AAC1B,QAAI,aAAa,GAAG;AAClB,UAAI,YAAY,OAAO,CAAC;AACxB,UAAI,cAAc,GAAG;AACnB,cAAM,IAAI,MAAM,oCAAoC,SAAS,EAAE;;AAEjE,eAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,YAAI,cAAc,OAAO,CAAC,KAAK;AAC/B,sBAAc,eAAgB,OAAO,CAAC,KAAK;AAC3C,YAAI,CAAC,aAAa;AAChB,gBAAM,IAAI,MAAM,uBAAuB,OAAO,CAAC,CAAC,iBAC5C,SAAS,KAAK,aAAa,GAAG;;AAEpC,oBAAY,OAAO,CAAC;;AAEtB,UAAI,cAAc,eAAe;AAC/B,cAAM,IAAI,MAAM,gDACZ,aAAa,SAAS,SAAS,EAAE;;;AAIzC,UAAM,gBAAgB,aAAa;AACnC,UAAM,eAAe,aAAK,kBAAkB,SAAS,UAAU;AAE/D,QAAI,kBAAkB,KAAK,eAAe,GAAG;AAC3C,YAAM,QAAsB,IAAI,MAAM,aAAa;AACnD,eAAS,IAAI,GAAG,KAAK,eAAe,EAAE,GAAG;AACvC,qBAAa,CAAC,IAAI;;AAEpB,aAAO,CAAC,OAAO,YAAY;;AAG7B,iBAAa,CAAC,IAAI;AAClB,aAAS,IAAI,GAAG,KAAK,eAAe,EAAE,GAAG;AACvC,YAAM,SAAS,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC;AACvC,UAAI,YAAY;AAChB,WAAK,YAAY,QAAQ,CAAC,eAAc;AACtC,qBAAa,KAAK,aAAa,QAAQ,UAAU;MACnD,CAAC;AACD,UAAI,KAAK,iBAAiB,SAAS,KAAK,cAAc,GAAG;AACvD,oBAAY;;AAEd,mBAAa,CAAC,IAAI,aAAa,IAAI,CAAC,IAAI;;AAG1C,UAAM,SAAuB,IAAI,MAAM,aAAa,aAAa,CAAC;AAElE,aAAS,IAAI,GAAG,IAAI,eAAe,EAAE,GAAG;AACtC,YAAM,aAAa,OAAO,CAAC;AAC3B,UAAI,iBAAiB,aAAa,CAAC;AACnC,WAAK,YAAY,QAAQ,CAAC,eAAc;AACtC,cAAM,SAAS,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC;AACvC,cAAM,YAAY,KAAK,aAAa,QAAQ,UAAU;AACtD,aAAK,aACD,MAAM,YAAY,QAAQ,gBAAgB,WAAW,UAAU;AACnE,0BAAkB;MACpB,CAAC;AAMD,UAAI,KAAK,iBAAiB,mBAAmB,aAAa,CAAC,GAAG;AAC5D,cAAM,aAAa,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC;AAG3C,YAAI,eAAe,GAAG;AACpB;;AAKF,cAAM,aAAa,aAAa,IAAI,KAAK;AACzC,cAAM,YAAY;AAClB,aAAK,aACD,MAAM,YAAY,QAAQ,gBAAgB,WAAW,UAAU;;;AAGvE,WAAO,CAAC,QAAQ,YAAY;EAC9B;;AAGI,SAAU,iBACZ,MAAoB,YAAwB,WAC5C,aAAuB,SAAiB,UAAkB,UAC1D,wBAA+B;AACjC,SAAO,IAAI,eACA,WAAW,aAAa,SAAS,UAAU,UAC3C,sBAAsB,EAC5B,QAAQ,MAAM,UAAU;AAC/B;;;AC7MA,SAAS,MACL,KAAiB,YAAwB,WACzC,QAAoB;AACtB,MAAI,CAAC,IAAI,QAAQ;AACf;;AAGF,MAAI,WAAW,WAAW,GAAG;AAC3B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,aAAO,KAAK,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC;;AAEpC;;AAGF,MAAI,WAAW,WAAW,GAAG;AAC3B,UAAM,YAAY,WAAW,CAAC;AAC9B,QAAI,IAAI,IAAI,QAAQ,SAAS;AAC7B,WAAO,MAAM,IAAI;AACf,YAAM,QAAQ,IAAI,SAAS,GAAG,CAAC;AAC/B,UAAI,CAAC,aAAa,MAAM,WAAW,GAAG;AACpC,eAAO,KAAK,KAAK;;AAEnB,YAAM,IAAI,SAAS,IAAI,CAAC;AACxB,UAAI,IAAI,QAAQ,SAAS;;AAE3B,QAAI,CAAC,aAAa,IAAI,WAAW,GAAG;AAClC,aAAO,KAAK,GAAG;;AAEjB;;AAIF,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK;AACvC,QAAK,MAAM,IAAI,UAAY,WAAW,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAK;AAC7D,YAAM,QAAQ,IAAI,SAAS,YAAY,CAAC;AACxC,UAAI,CAAC,aAAa,MAAM,WAAW,GAAG;AACpC,eAAO,KAAK,KAAK;;AAEnB,mBAAa,IAAI;;;AAGvB;AAEM,SAAU,gBACZ,OAAqB,WACrB,WAAkB;AACpB,QAAM,YAAY,MAAM;AAGxB,QAAM,SAAuB,CAAA;AAE7B,MAAI,aAAa;AACjB,MAAI,gBAAgB;AACpB,QAAM,aAAuB,IAAI,MAAM,SAAS;AAChD,WAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,UAAM,mBAAmB,OAAO;AAChC,UAAM,MAAM,CAAC,GAAG,WAAW,WAAW,MAAM;AAC5C,UAAM,WAAW,OAAO,SAAS;AACjC,eAAW,CAAC,IAAI;AAChB,kBAAc;AACd,oBAAgB,KAAK,IAAI,eAAe,QAAQ;;AAGlD,QAAM,UAAU,aAAK,kBAAkB,SAAS,aAAa,CAAC;AAC9D,QAAM,SAAuB,IAAI,MAAM,UAAU;AACjD,QAAM,QAA0B,CAAC,WAAW,aAAa;AAEzD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,aAAS,IAAI,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,GAAG;AAEtC,cAAQ,IAAI,CAAC,IAAI;AACjB,cAAQ,IAAI,IAAI,CAAC,IAAI;AACrB,aAAO,CAAC,IAAI,OAAO,CAAC;AACpB,QAAE;;;AAIN,SAAO,CAAC,SAAS,QAAQ,KAAK;AAChC;;;AChFM,SAAU,2BACZ,OAAqB,YAAkB;AACzC,QAAM,SAAS,aAAK,kBAAkB,SAAS,MAAM,MAAM;AAE3D,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,WAAO,CAAC,IACJ,aAAK,cAAc,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,EAAE,mBAAkB;;AAGxE,SAAO;AACT;;;ACPO,IAAM,UAAU,6BAClB,CAAC,QAAgB,WAAmB,SAAS,MAAO;AAClD,IAAM,iBACT,8BAA+B,CAAC,OAAO,OAAO,OAAO,UAAS;AAC5D,SAAO,EAAC,MAAM,QAAQ,OAAO,MAAM,QAAQ,MAAK;AAClD,CAAE;AACC,IAAM,MAAM,iBAAiB,KAAK,SAAS,cAAc;AAEzD,IAAM,YAA0B;EACrC,YAAY;EACZ,aAAa;EACb,YAAY;;;;ACTR,SAAU,SACZ,MACA,MAAc;AAChB,QAAM,WAAqB,IAAI,MAAM,KAAK,IAAI;AAC9C,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAS,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC;;AAEtC,QAAM,SAAS,OAAO,UAAU,KAAK,KAAK;AAC1C,WAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,EAAE,GAAG;AAC7C,UAAM,SAAS,OAAO,WAAW,CAAC;AAElC,UAAM,cAAwB,IAAI,MAAM,KAAK,IAAI;AACjD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,kBAAY,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;;AAG3C,UAAM,gBAAgB,KAAK,WAAW,WAAW;AAEjD,WAAO,OAAO,CAAC,IAAI,KAAK,OAAO,aAAa;;AAE9C,SAAO;AACT;;;ACnBA,IAAM,cAAc,CAAC,GAAS,MAAW;AACvC,QAAM,YAAY,EAAE,QAAQ,EAAE;AAC9B,SAAO,cAAc,IAAI,EAAE,QAAQ,EAAE,QAAQ;AAC/C;AAaA,SAAS,OAAO,OAAe,GAAW,OAAO,GAAG,QAAQ,MAAM,SAAS,GAAC;AAC1E,SAAO,QAAQ,MAAM;AAInB,QAAI,QAAQ,OAAO,KAAK;AACtB,YAAM,IAAI,QAAQ,OAAO;AACzB,YAAMC,KAAI,IAAI,OAAO;AACrB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAClC,YAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,KAAKA,KAAI,IAAI,CAAC;AACrE,YAAM,UAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAIA,KAAI,IAAI,IAAI,EAAE,CAAC;AAC7D,YAAM,WAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAIA,MAAK,IAAI,IAAI,EAAE,CAAC;AACrE,aAAO,OAAO,GAAG,SAAS,QAAQ;;AAGpC,UAAM,IAAI,MAAM,CAAC;AACjB,QAAI,IAAI;AACR,QAAI,IAAI;AAER,iBAAK,KAAK,OAAO,MAAM,CAAC;AAExB,QAAI,YAAY,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AACpC,mBAAK,KAAK,OAAO,MAAM,KAAK;;AAE9B,WAAO,IAAI,GAAG;AACZ,mBAAK,KAAK,OAAO,GAAG,CAAC;AACrB;AACA;AACA,aAAO,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG;AACnC,YAAI,IAAI;;AAEV,aAAO,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG;AACnC,YAAI,IAAI;;;AAGZ,QAAI,YAAY,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG;AACrC,mBAAK,KAAK,OAAO,MAAM,CAAC;WACnB;AACL,UAAI,IAAI;AACR,mBAAK,KAAK,OAAO,GAAG,KAAK;;AAI3B,QAAI,KAAK,GAAG;AACV,aAAO,IAAI;;AAEb,QAAI,KAAK,GAAG;AACV,cAAQ,IAAI;;;AAGlB;AAEM,SAAU,SACZ,GAAe,QAAkB,QAAyB,GAC1D,QAAe;AAGjB,QAAM,UAAU,OAAO,OAAO,SAAS,CAAC;AACxC,QAAM,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,SAAS,SAAS,OAAO;AAClD,QAAM,cAAc,aAAK,uBAAuB,QAAQ,QAAQ,CAAC;AACjE,QAAM,iBAAiB,aAAK,uBAAuB,SAAS,QAAQ,CAAC;AAErE,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,EAAE,SAAS,QAAQ,SAAS,IAAI;AAE7C,QAAI,YAAoB,IAAI,MAAM,KAAK,MAAM;AAC7C,SAAK,QACD,CAAC,OAAe,UAAkB,UAAU,KAAK,IAAI,EAAC,OAAO,MAAK,CAAC;AAEvE,QAAI,IAAI,UAAU,QAAQ;AACxB,aAAO,WAAW,CAAC;AACnB,kBAAY,UAAU,MAAM,GAAG,CAAC;;AAGlC,QAAI,QAAQ;AACV,gBAAU,KAAK,WAAW;;AAG5B,UAAM,YAAY,IAAI;AACtB,UAAM,WAAW,YAAY,SAAS,WAAW,YAAY,CAAC;AAC9D,UAAM,cAAc,eAAe,SAAS,WAAW,YAAY,CAAC;AACpE,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,CAAC,IAAI,UAAU,CAAC,EAAE;AAC3B,kBAAY,CAAC,IAAI,UAAU,CAAC,EAAE;;;AAKlC,QAAM,cAAc,OAAO,MAAK;AAChC,cAAY,YAAY,SAAS,CAAC,IAAI;AAEtC,SAAO;IACL,OAAO,aAA4B,QAAQ,WAAW;IACtD,OAAO,aAA4B,SAAS,cAAc;;AAE9D;;;ACxHM,SAAU,WACZ,QAAuB,MAAc,OAAiB,OAAe;AAMvE,QAAM,QAAQ,aAAK,eAAe,MAAM,KAAK,EAAE,CAAC;AAyDhD,QAAM,WAAW,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;AAChC,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,aAAS,CAAC,KAAK,MAAM,CAAC;;AAExB,WAAS,CAAC,IAAI,MAAM,KAAK;AACzB,WAAS,IAAI,QAAQ,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC7C,aAAS,CAAC,KAAK,MAAM,CAAC;;AAKxB,QAAM,iBAAiB,oBAAI,IAAG;AAG9B,QAAM,UAAU,IAAI,WAAW,MAAM,KAAK,CAAC;AAE3C,QAAM,cAAc,IAAI,aAAa,UAAU,OAAO,MAAoB;AAG1E,QAAM,gBAA0B,CAAA;AAChC,QAAM,aAAa,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;AACxD,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG,KAAK;AAErC,QAAI;AACJ,QAAI,YAAY;AAEd,gBAAU,OAAO,CAAC,EAAE,SAAQ;WACvB;AACL,YAAM,aAAa,CAAA;AACnB,eAAS,IAAI,GAAG,IAAI,SAAS,CAAC,GAAG,KAAK;AACpC,iBAAS,IAAI,GAAG,IAAI,SAAS,CAAC,GAAG,KAAK;AACpC,qBAAW,KAAK,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC;;;AAG5C,gBAAU,WAAW,KAAK,GAAG;;AAI/B,UAAM,gBAAgB,eAAe,IAAI,OAAO;AAChD,QAAI,iBAAiB,MAAM;AACzB,cAAQ,CAAC,IAAI;WACR;AACL,YAAM,cAAc,eAAe;AACnC,qBAAe,IAAI,SAAS,WAAW;AACvC,cAAQ,CAAC,IAAI;AACb,oBAAc,KAAK,CAAC;;;AAOxB,QAAM,iBAAiB,SAAS,MAAK;AACrC,iBAAe,CAAC,IAAI,eAAe;AACnC,QAAM,eAAe,IAAI,aAAa,gBAAgB,KAAK;AAC3D,gBAAc,QAAQ,CAAC,oBAAoB,MAAK;AAC9C,aAAS,IAAI,GAAG,IAAI,SAAS,CAAC,GAAG,KAAK;AACpC,eAAS,IAAI,GAAG,IAAI,SAAS,CAAC,GAAG,KAAK;AACpC,qBAAa,IAAI,YAAY,IAAI,GAAG,oBAAoB,CAAC,GAAG,GAAG,GAAG,CAAC;;;EAGzE,CAAC;AAID,QAAM,cAAc,MAAM,MAAK;AAC/B,cAAY,KAAK,IAAI,eAAe,CAAC;AAErC,SAAO;IACL,cAAc,aAAa;IAC3B;IACA;;AAEJ;", "names": ["real", "complex", "real", "real", "i", "prod", "slice", "i"]}