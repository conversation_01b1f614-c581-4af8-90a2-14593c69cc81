import {
  absConfig,
  add,
  addConfig,
  assertNotComplex,
  binaryKernelFunc,
  bincountImpl,
  bincountReduceImpl,
  bitwiseAndConfig,
  cast,
  castConfig,
  ceilConfig,
  complex,
  complexConfig,
  concatImpl,
  createSimpleBinaryKernelImpl,
  equal,
  equalConfig,
  exp,
  expConfig,
  expm1Config,
  floorConfig,
  floorDivConfig,
  gatherNdImpl,
  gatherV2Impl,
  greaterConfig,
  greaterEqualConfig,
  identity,
  identityConfig,
  lessConfig,
  lessEqualConfig,
  linSpaceImpl,
  logConfig,
  maxImpl,
  maximumConfig,
  minimumConfig,
  multiply,
  multiplyConfig,
  negConfig,
  notEqualConfig,
  prodConfig,
  raggedGatherImpl,
  raggedRangeImpl,
  raggedTensorToTensorImpl,
  rangeImpl,
  real,
  realConfig,
  rsqrtConfig,
  scatterImpl,
  shared_exports,
  sigmoid,
  sigmoidConfig,
  slice,
  sliceConfig,
  sparseFillEmptyRowsImpl,
  sparseReshapeImpl,
  sparseSegmentReductionImpl,
  sqrtConfig,
  squaredDifferenceConfig,
  staticRegexReplaceConfig,
  stridedSliceImpl,
  stringNGramsImpl,
  stringSplitImpl,
  stringToHashBucketFastImpl,
  sub,
  subConfig,
  tileImpl,
  topKImpl,
  transpose,
  transposeConfig,
  transposeImpl,
  unaryKernelFunc,
  uniqueImpl,
  zeros
} from "./chunk-6O5JEXQP.js";
import {
  Acos,
  Acosh,
  AddN,
  All,
  Any,
  ArgMax,
  ArgMin,
  Asin,
  Asinh,
  Atan,
  Atan2,
  Atanh,
  AvgPool,
  AvgPool3D,
  AvgPool3DGrad,
  AvgPoolGrad,
  BatchMatMul,
  BatchToSpaceND,
  Bincount,
  BroadcastArgs,
  ClipByValue,
  ComplexAbs,
  Concat,
  Conv2D,
  Conv2DBackpropFilter,
  Conv2DBackpropInput,
  Conv3D,
  Conv3DBackpropFilterV2,
  Conv3DBackpropInputV2,
  Cos,
  Cosh,
  CropAndResize,
  Cumprod,
  Cumsum,
  DataStorage,
  DenseBincount,
  DepthToSpace,
  DepthwiseConv2dNative,
  DepthwiseConv2dNativeBackpropFilter,
  DepthwiseConv2dNativeBackpropInput,
  Diag,
  Dilation2D,
  Dilation2DBackpropFilter,
  Dilation2DBackpropInput,
  Draw,
  Einsum,
  Elu,
  EluGrad,
  Erf,
  ExpandDims,
  FFT,
  Fill,
  FlipLeftRight,
  FusedBatchNorm,
  FusedConv2D,
  FusedDepthwiseConv2D,
  GatherNd,
  GatherV2,
  IFFT,
  Imag,
  IsFinite,
  IsInf,
  IsNan,
  KernelBackend,
  LRN,
  LRNGrad,
  LeakyRelu,
  LinSpace,
  Log1p,
  LogicalAnd,
  LogicalNot,
  LogicalOr,
  Max,
  MaxPool,
  MaxPool3D,
  MaxPool3DGrad,
  MaxPoolGrad,
  MaxPoolWithArgmax,
  Mean,
  Min,
  MirrorPad,
  Mod,
  Multinomial,
  NonMaxSuppressionV3,
  NonMaxSuppressionV4,
  NonMaxSuppressionV5,
  OneHot,
  OnesLike,
  Pack,
  PadV2,
  Pow,
  Prelu,
  RaggedGather,
  RaggedRange,
  RaggedTensorToTensor,
  Range,
  RealDiv,
  Reciprocal,
  Relu,
  Relu6,
  Reshape,
  ResizeBilinear,
  ResizeBilinearGrad,
  ResizeNearestNeighbor,
  ResizeNearestNeighborGrad,
  Reverse,
  RotateWithOffset,
  Round,
  ScatterNd,
  SearchSorted,
  Select,
  Selu,
  Sign,
  Sin,
  Sinh,
  Softmax,
  Softplus,
  SpaceToBatchND,
  SparseFillEmptyRows,
  SparseReshape,
  SparseSegmentMean,
  SparseSegmentSum,
  SparseToDense,
  SplitV,
  Square,
  Step,
  StridedSlice,
  StringNGrams,
  StringSplit,
  StringToHashBucketFast,
  Sum,
  Tan,
  Tanh,
  TensorBuffer,
  TensorScatterUpdate,
  Tile,
  TopK,
  Transform,
  Unique,
  Unpack,
  UnsortedSegmentSum,
  ZerosLike,
  _FusedMatMul,
  backend_util_exports,
  broadcast_util_exports,
  buffer,
  engine,
  env,
  kernel_impls_exports,
  registerBackend,
  registerKernel,
  require_seedrandom,
  slice_util_exports,
  upcastType,
  util_exports
} from "./chunk-VFSHU3A2.js";
import "./chunk-EXAI6KDO.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@tensorflow/tfjs-backend-cpu/dist/backend_cpu.js
var whereImpl = kernel_impls_exports.whereImpl;
var MathBackendCPU = class _MathBackendCPU extends KernelBackend {
  nextDataId() {
    return _MathBackendCPU.nextDataId++;
  }
  constructor() {
    super();
    this.blockSize = 48;
    this.firstUse = true;
    this.data = new DataStorage(this, engine());
  }
  write(values, shape, dtype) {
    if (this.firstUse) {
      this.firstUse = false;
      if (env().get("IS_NODE")) {
        backend_util_exports.warn("\n============================\nHi, looks like you are running TensorFlow.js in Node.js. To speed things up dramatically, install our node backend, visit https://github.com/tensorflow/tfjs-node for more details. \n============================");
      }
    }
    const dataId = { id: this.nextDataId() };
    this.data.set(dataId, { values, dtype, refCount: 1 });
    return dataId;
  }
  /**
   * Create a data bucket in cpu backend.
   * @param shape Shape of the `TensorInfo`.
   * @param dtype DType of the `TensorInfo`.
   * @param values The value of the `TensorInfo` stored as a flattened array.
   */
  makeTensorInfo(shape, dtype, values) {
    let outId;
    if (dtype === "string" && values != null && values.length > 0 && util_exports.isString(values[0])) {
      const encodedValues = values.map((d) => util_exports.encodeString(d));
      outId = this.write(encodedValues, shape, dtype);
    } else {
      outId = this.write(values, shape, dtype);
    }
    return { dataId: outId, shape, dtype };
  }
  /** Return refCount of a `TensorData`. */
  refCount(dataId) {
    if (this.data.has(dataId)) {
      const tensorData = this.data.get(dataId);
      return tensorData.refCount;
    }
    return 0;
  }
  /** Increase refCount of a `TensorData`. */
  incRef(dataId) {
    const tensorData = this.data.get(dataId);
    tensorData.refCount++;
  }
  /** Decrease refCount of a `TensorData`. */
  decRef(dataId) {
    if (this.data.has(dataId)) {
      const tensorData = this.data.get(dataId);
      tensorData.refCount--;
    }
  }
  move(dataId, values, shape, dtype, refCount) {
    this.data.set(dataId, { values, dtype, refCount });
  }
  numDataIds() {
    return this.data.numDataIds();
  }
  async read(dataId) {
    return this.readSync(dataId);
  }
  readSync(dataId) {
    const { dtype, complexTensorInfos } = this.data.get(dataId);
    if (dtype === "complex64") {
      const realValues = this.readSync(complexTensorInfos.real.dataId);
      const imagValues = this.readSync(complexTensorInfos.imag.dataId);
      return backend_util_exports.mergeRealAndImagArrays(realValues, imagValues);
    }
    return util_exports.convertBackendValuesAndArrayBuffer(this.data.get(dataId).values, dtype);
  }
  bufferSync(t) {
    const data = this.readSync(t.dataId);
    if (t.dtype === "string") {
      try {
        const strings = data.map((d) => util_exports.decodeString(d));
        return buffer(t.shape, t.dtype, strings);
      } catch (_a) {
        throw new Error("Failed to decode encoded string bytes into utf-8");
      }
    }
    return buffer(t.shape, t.dtype, data);
  }
  makeOutput(values, shape, dtype) {
    return engine().makeTensorFromTensorInfo(this.makeTensorInfo(shape, dtype, values), this);
  }
  /**
   * Dispose the memory if the dataId has 0 refCount. Return true if the memory
   * is released or memory is not managed in this backend, false if memory is
   * not cleared.
   * @param dataId
   * @oaram force Optional, remove the data regardless of refCount
   */
  disposeData(dataId, force = false) {
    if (this.data.has(dataId)) {
      this.data.get(dataId).refCount--;
      if (!force && this.data.get(dataId).refCount > 0) {
        return false;
      }
      const { complexTensorInfos } = this.data.get(dataId);
      if (complexTensorInfos != null) {
        this.disposeData(complexTensorInfos.real.dataId, true);
        this.disposeData(complexTensorInfos.imag.dataId, true);
      }
      this.data.delete(dataId);
    }
    return true;
  }
  disposeIntermediateTensorInfo(tensorInfo) {
    this.disposeData(tensorInfo.dataId);
  }
  async time(f) {
    const start = util_exports.now();
    f();
    const kernelMs = util_exports.now() - start;
    return { kernelMs };
  }
  memory() {
    return {
      // Unreliable due to automatic gc. The numbers above are cumulative.
      unreliable: true,
      reasons: ["The reported memory is an upper bound. Due to automatic garbage collection, the true allocated memory may be less."]
    };
  }
  where(condition) {
    assertNotComplex([condition], "where");
    const condVals = this.readSync(condition.dataId);
    return whereImpl(condition.shape, condVals);
  }
  dispose() {
  }
  floatPrecision() {
    return 32;
  }
  /** Returns the smallest representable number.  */
  epsilon() {
    return super.epsilon();
  }
};
MathBackendCPU.nextDataId = 0;

// node_modules/@tensorflow/tfjs-backend-cpu/dist/version.js
var version = "4.22.0";

// node_modules/@tensorflow/tfjs-backend-cpu/dist/base.js
registerBackend(
  "cpu",
  () => new MathBackendCPU(),
  1
  /* priority */
);

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Elu.js
var elu = unaryKernelFunc(Elu, (xi) => xi >= 0 ? xi : Math.exp(xi) - 1);
var eluConfig = {
  kernelName: Elu,
  backendName: "cpu",
  kernelFunc: elu
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LeakyRelu.js
function leakyRelu(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { alpha } = attrs;
  assertNotComplex([x], "leakyRelu");
  const xSize = util_exports.sizeFromShape(x.shape);
  const xVals = backend.data.get(x.dataId).values;
  const outVals = util_exports.getTypedArrayFromDType("float32", xSize);
  for (let i = 0; i < xVals.length; i++) {
    outVals[i] = xVals[i] < 0 ? alpha * xVals[i] : xVals[i];
  }
  return backend.makeTensorInfo(x.shape, "float32", outVals);
}
var leakyReluConfig = {
  kernelName: LeakyRelu,
  backendName: "cpu",
  kernelFunc: leakyRelu
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Prelu.js
var preluImpl = createSimpleBinaryKernelImpl((xValue, aValue) => xValue < 0 ? aValue * xValue : xValue);
function prelu(args) {
  const { inputs, backend } = args;
  const { x, alpha } = inputs;
  assertNotComplex([x, alpha], "prelu");
  const aVals = backend.data.get(x.dataId).values;
  const bVals = backend.data.get(alpha.dataId).values;
  const [resultData, resultShape] = preluImpl(x.shape, alpha.shape, aVals, bVals, "float32");
  return backend.makeTensorInfo(resultShape, "float32", resultData);
}
var preluConfig = {
  kernelName: Prelu,
  backendName: "cpu",
  kernelFunc: prelu
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Relu.js
var relu = unaryKernelFunc(Relu, (xi) => Math.max(0, xi));
var reluConfig = {
  kernelName: Relu,
  backendName: "cpu",
  kernelFunc: relu
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Relu6.js
var relu6 = unaryKernelFunc(Relu6, (xi) => Math.min(Math.max(0, xi), 6));
var relu6Config = {
  kernelName: Relu6,
  backendName: "cpu",
  kernelFunc: relu6
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/utils/fused_utils.js
function applyActivation(backend, x, activation, preluActivationWeights, leakyreluAlpha) {
  if (activation === "linear") {
    return identity({ inputs: { x }, backend });
  } else if (activation === "relu") {
    return relu({ inputs: { x }, backend });
  } else if (activation === "elu") {
    return elu({ inputs: { x }, backend });
  } else if (activation === "relu6") {
    return relu6({ inputs: { x }, backend });
  } else if (activation === "prelu") {
    return prelu({ inputs: { x, alpha: preluActivationWeights }, backend });
  } else if (activation === "leakyrelu") {
    return leakyRelu({ inputs: { x }, backend, attrs: { alpha: leakyreluAlpha } });
  } else if (activation === "sigmoid") {
    return sigmoid({ inputs: { x }, backend });
  }
  throw new Error(`Activation ${activation} has not been implemented for the CPU backend.`);
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Reshape.js
function reshape(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { shape } = attrs;
  const xSize = util_exports.sizeFromShape(x.shape);
  const $shape = util_exports.inferFromImplicitShape(shape, xSize);
  const $xSize = util_exports.sizeFromShape($shape);
  util_exports.assert(xSize === $xSize, () => `The new shape (${$shape}) has ${$xSize} elements and the old shape (${x.shape}) has ${xSize} elements. The new shape and old shape must have the same number of elements.`);
  backend.incRef(x.dataId);
  const xData = backend.data.get(x.dataId);
  if (xData.complexTensorInfos != null) {
    const real2 = xData.complexTensorInfos.real;
    const imag2 = xData.complexTensorInfos.imag;
    real2.shape = $shape;
    imag2.shape = $shape;
  }
  return { dataId: x.dataId, shape: $shape, dtype: x.dtype };
}
var reshapeConfig = {
  kernelName: Reshape,
  backendName: "cpu",
  kernelFunc: reshape
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/BatchMatMul.js
function batchMatMul(args) {
  const { inputs, backend, attrs } = args;
  const { a, b } = inputs;
  const { transposeA, transposeB } = attrs;
  assertNotComplex([a, b], "matMul");
  const aRank = a.shape.length;
  const bRank = b.shape.length;
  const innerShapeA = transposeA ? a.shape[aRank - 2] : a.shape[aRank - 1];
  const innerShapeB = transposeB ? b.shape[bRank - 1] : b.shape[bRank - 2];
  const outerShapeA = transposeA ? a.shape[aRank - 1] : a.shape[aRank - 2];
  const outerShapeB = transposeB ? b.shape[bRank - 2] : b.shape[bRank - 1];
  const outerDimsA = a.shape.slice(0, -2);
  const outerDimsB = b.shape.slice(0, -2);
  const batchDimA = util_exports.sizeFromShape(outerDimsA);
  const batchDimB = util_exports.sizeFromShape(outerDimsB);
  const outShapeOuterDims = broadcast_util_exports.assertAndGetBroadcastShape(a.shape.slice(0, -2), b.shape.slice(0, -2));
  const outShape = outShapeOuterDims.concat([outerShapeA, outerShapeB]);
  util_exports.assert(innerShapeA === innerShapeB, () => `Error in matMul: inner shapes (${innerShapeA}) and (${innerShapeB}) of Tensors with shapes ${a.shape} and ${b.shape} and transposeA=${transposeA} and transposeB=${transposeB} must match.`);
  const a3dShape = transposeA ? [batchDimA, innerShapeA, outerShapeA] : [batchDimA, outerShapeA, innerShapeA];
  const b3dShape = transposeB ? [batchDimB, outerShapeB, innerShapeB] : [batchDimB, innerShapeB, outerShapeB];
  const a3d = reshape({ inputs: { x: a }, backend, attrs: { shape: a3dShape } });
  const b3d = reshape({ inputs: { x: b }, backend, attrs: { shape: b3dShape } });
  const sharedDim = transposeA ? a3d.shape[1] : a3d.shape[2];
  const leftDim = transposeA ? a3d.shape[2] : a3d.shape[1];
  const rightDim = transposeB ? b3d.shape[1] : b3d.shape[2];
  const batchDim = Math.max(batchDimA, batchDimB);
  const a3dValues = backend.data.get(a3d.dataId).values;
  const b3dValues = backend.data.get(b3d.dataId).values;
  const a3dStrides = util_exports.computeStrides(a3d.shape);
  const b3dStrides = util_exports.computeStrides(b3d.shape);
  const [aBatch, aOuterStep, aInnerStep] = transposeA ? [a3dStrides[0], 1, a3dStrides[1]] : [a3dStrides[0], a3dStrides[1], 1];
  const [bInnerStep, bOuterStep, bBatch] = transposeB ? [1, b3dStrides[1], b3dStrides[0]] : [b3dStrides[1], 1, b3dStrides[0]];
  const size = leftDim * rightDim;
  const result = buffer([batchDim, leftDim, rightDim], a3d.dtype);
  const resVals = result.values;
  const blockSize = backend.blockSize;
  for (let bi = 0; bi < batchDim; bi++) {
    const batchIndexA = bi % batchDimA;
    const batchIndexB = bi % batchDimB;
    for (let i0 = 0; i0 < leftDim; i0 += blockSize) {
      const iBlock = Math.min(i0 + blockSize, leftDim);
      for (let j0 = 0; j0 < rightDim; j0 += blockSize) {
        const jBlock = Math.min(j0 + blockSize, rightDim);
        for (let k0 = 0; k0 < sharedDim; k0 += blockSize) {
          const kBlock = Math.min(k0 + blockSize, sharedDim);
          for (let i = i0; i < iBlock; i++) {
            for (let j = j0; j < jBlock; j++) {
              let sum2 = 0;
              for (let k = k0; k < kBlock; k++) {
                const aVal = (
                  // tslint:disable-next-line: max-line-length
                  a3dValues[batchIndexA * aBatch + i * aOuterStep + k * aInnerStep]
                );
                const bVal = (
                  // tslint:disable-next-line: max-line-length
                  b3dValues[k * bInnerStep + j * bOuterStep + batchIndexB * bBatch]
                );
                sum2 += aVal * bVal;
              }
              resVals[bi * size + (i * rightDim + j)] += sum2;
            }
          }
        }
      }
    }
  }
  backend.disposeIntermediateTensorInfo(a3d);
  backend.disposeIntermediateTensorInfo(b3d);
  return backend.makeTensorInfo(outShape, result.dtype, result.values);
}
var batchMatMulConfig = {
  kernelName: BatchMatMul,
  backendName: "cpu",
  kernelFunc: batchMatMul
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/_FusedMatMul.js
function _fusedMatMul(args) {
  const { inputs, backend, attrs } = args;
  const { a, b, bias, preluActivationWeights } = inputs;
  const { transposeA, transposeB, activation, leakyreluAlpha } = attrs;
  let current;
  let addRes;
  let activationRes;
  const intermediates = [];
  const matMulRes = batchMatMul({ inputs: { a, b }, attrs: { transposeA, transposeB }, backend });
  current = matMulRes;
  if (bias) {
    addRes = add({ inputs: { a: current, b: bias }, backend });
    intermediates.push(current);
    current = addRes;
  }
  if (activation) {
    activationRes = applyActivation(backend, current, activation, preluActivationWeights, leakyreluAlpha);
    intermediates.push(current);
    current = activationRes;
  }
  for (const i of intermediates) {
    backend.disposeIntermediateTensorInfo(i);
  }
  return current;
}
var _fusedMatMulConfig = {
  kernelName: _FusedMatMul,
  backendName: "cpu",
  kernelFunc: _fusedMatMul
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Acos.js
var acos = unaryKernelFunc(Acos, (xi) => Math.acos(xi));
var acosConfig = {
  kernelName: Acos,
  backendName: "cpu",
  kernelFunc: acos
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Acosh.js
var acosh = unaryKernelFunc(Acosh, (xi) => Math.acosh(xi));
var acoshConfig = {
  kernelName: Acosh,
  backendName: "cpu",
  kernelFunc: acosh
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/AddN.js
function addN(args) {
  const { inputs, backend } = args;
  const tensors = inputs;
  assertNotComplex(inputs, "addN");
  const vals = tensors.map((t) => backend.data.get(t.dataId).values);
  const outBuf = buffer(tensors[0].shape, tensors[0].dtype);
  const outVals = outBuf.values;
  for (let i = 0; i < tensors.length; i++) {
    const currVals = vals[i];
    for (let j = 0; j < outVals.length; j++) {
      outVals[j] += currVals[j];
    }
  }
  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);
}
var addNConfig = {
  kernelName: AddN,
  backendName: "cpu",
  kernelFunc: addN
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/All.js
function all(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, keepDims } = attrs;
  assertNotComplex(x, "all");
  const origAxes = util_exports.parseAxisParam(axis, x.shape);
  let axes = origAxes;
  const permutedAxes = backend_util_exports.getAxesPermutation(axes, x.shape.length);
  let $x = x;
  if (permutedAxes != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutedAxes } });
    axes = backend_util_exports.getInnerMostAxes(axes.length, x.shape.length);
  }
  backend_util_exports.assertAxesAreInnerMostDims("all", axes, $x.shape.length);
  const [outShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes($x.shape, axes);
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const vals = util_exports.makeZerosTypedArray(util_exports.sizeFromShape(outShape), $x.dtype);
  const aVals = backend.data.get($x.dataId).values;
  for (let i = 0; i < vals.length; ++i) {
    const offset = i * reduceSize;
    let all2 = aVals[offset];
    for (let j = 0; j < reduceSize; ++j) {
      const value = aVals[offset + j];
      all2 = all2 && value;
    }
    vals[i] = all2;
  }
  if (permutedAxes != null) {
    backend.disposeIntermediateTensorInfo($x);
  }
  const result = backend.makeTensorInfo(outShape, $x.dtype, vals);
  if (keepDims) {
    const expandedShape = backend_util_exports.expandShapeToKeepDim(outShape, origAxes);
    const reshapedResult = reshape({ inputs: { x: result }, backend, attrs: { shape: expandedShape } });
    backend.disposeIntermediateTensorInfo(result);
    return reshapedResult;
  }
  return result;
}
var allConfig = {
  kernelName: All,
  backendName: "cpu",
  kernelFunc: all
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Any.js
function any(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, keepDims } = attrs;
  assertNotComplex(x, "any");
  const origAxes = util_exports.parseAxisParam(axis, x.shape);
  let axes = origAxes;
  const permutedAxes = backend_util_exports.getAxesPermutation(axes, x.shape.length);
  let $x = x;
  if (permutedAxes != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutedAxes } });
    axes = backend_util_exports.getInnerMostAxes(axes.length, x.shape.length);
  }
  backend_util_exports.assertAxesAreInnerMostDims("any", axes, $x.shape.length);
  const [outShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes($x.shape, axes);
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const vals = util_exports.makeZerosTypedArray(util_exports.sizeFromShape(outShape), $x.dtype);
  const aVals = backend.data.get($x.dataId).values;
  for (let i = 0; i < vals.length; ++i) {
    const offset = i * reduceSize;
    let anyVal = aVals[offset];
    for (let j = 0; j < reduceSize; ++j) {
      const value = aVals[offset + j];
      anyVal = anyVal || value;
    }
    vals[i] = anyVal;
  }
  if (permutedAxes != null) {
    backend.disposeIntermediateTensorInfo($x);
  }
  const result = backend.makeTensorInfo(outShape, $x.dtype, vals);
  if (keepDims) {
    const expandedShape = backend_util_exports.expandShapeToKeepDim(outShape, origAxes);
    const reshapedResult = reshape({ inputs: { x: result }, backend, attrs: { shape: expandedShape } });
    backend.disposeIntermediateTensorInfo(result);
    return reshapedResult;
  }
  return result;
}
var anyConfig = {
  kernelName: Any,
  backendName: "cpu",
  kernelFunc: any
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ArgMax.js
function argMax(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis } = attrs;
  assertNotComplex(x, "argMax");
  let axes = util_exports.parseAxisParam(axis, x.shape);
  const permutedAxes = backend_util_exports.getAxesPermutation(axes, x.shape.length);
  let $x = x;
  const intermediateTensorInfos = [];
  if (permutedAxes != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutedAxes } });
    intermediateTensorInfos.push($x);
    axes = backend_util_exports.getInnerMostAxes(axes.length, $x.shape.length);
  }
  axes = [axes[0]];
  backend_util_exports.assertAxesAreInnerMostDims("argMax", axes, $x.shape.length);
  const [outShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes($x.shape, axes);
  const outSize = util_exports.sizeFromShape(outShape);
  const vals = util_exports.makeZerosTypedArray(outSize, "int32");
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const aVals = backend.data.get($x.dataId).values;
  for (let i = 0; i < vals.length; ++i) {
    const offset = i * reduceSize;
    let max2 = aVals[offset];
    let maxIndex = 0;
    for (let j = 0; j < reduceSize; ++j) {
      const value = aVals[offset + j];
      if (value > max2) {
        max2 = value;
        maxIndex = j;
      }
    }
    vals[i] = maxIndex;
  }
  intermediateTensorInfos.forEach((t) => backend.disposeIntermediateTensorInfo(t));
  return backend.makeTensorInfo(outShape, "int32", vals);
}
var argMaxConfig = {
  kernelName: ArgMax,
  backendName: "cpu",
  kernelFunc: argMax
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ArgMin.js
function argMin(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis } = attrs;
  assertNotComplex(x, "argMin");
  let axes = util_exports.parseAxisParam(axis, x.shape);
  const permutedAxes = backend_util_exports.getAxesPermutation(axes, x.shape.length);
  let $x = x;
  const intermediateTensorInfos = [];
  if (permutedAxes != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutedAxes } });
    intermediateTensorInfos.push($x);
    axes = backend_util_exports.getInnerMostAxes(axes.length, $x.shape.length);
  }
  axes = [axes[0]];
  backend_util_exports.assertAxesAreInnerMostDims("argMin", axes, $x.shape.length);
  const [outShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes($x.shape, axes);
  const outSize = util_exports.sizeFromShape(outShape);
  const vals = util_exports.makeZerosTypedArray(outSize, "int32");
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const aVals = backend.data.get($x.dataId).values;
  for (let i = 0; i < vals.length; ++i) {
    const offset = i * reduceSize;
    let min2 = aVals[offset];
    let minIndex = 0;
    for (let j = 0; j < reduceSize; ++j) {
      const value = aVals[offset + j];
      if (value < min2) {
        min2 = value;
        minIndex = j;
      }
    }
    vals[i] = minIndex;
  }
  intermediateTensorInfos.forEach((t) => backend.disposeIntermediateTensorInfo(t));
  return backend.makeTensorInfo(outShape, "int32", vals);
}
var argMinConfig = {
  kernelName: ArgMin,
  backendName: "cpu",
  kernelFunc: argMin
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Asin.js
var asin = unaryKernelFunc(Asin, (xi) => Math.asin(xi));
var asinConfig = {
  kernelName: Asin,
  backendName: "cpu",
  kernelFunc: asin
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Asinh.js
var asinh = unaryKernelFunc(Asinh, (xi) => Math.asinh(xi));
var asinhConfig = {
  kernelName: Asinh,
  backendName: "cpu",
  kernelFunc: asinh
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Atan.js
var atan = unaryKernelFunc(Atan, (xi) => Math.atan(xi));
var atanConfig = {
  kernelName: Atan,
  backendName: "cpu",
  kernelFunc: atan
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Atan2.js
var atan2Impl = createSimpleBinaryKernelImpl((aValue, bValue) => Math.atan2(aValue, bValue));
var atan2 = binaryKernelFunc(Atan2, atan2Impl);
var atan2Config = {
  kernelName: Atan2,
  backendName: "cpu",
  kernelFunc: atan2
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Atanh.js
var atanh = unaryKernelFunc(Atanh, (xi) => Math.atanh(xi));
var atanhConfig = {
  kernelName: Atanh,
  backendName: "cpu",
  kernelFunc: atanh
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/utils/pool_utils.js
function pool(xValues, xShape, dtype, strides, convInfo, poolType) {
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padTop = convInfo.padInfo.top;
  const padLeft = convInfo.padInfo.left;
  const initialValue = poolType === "max" ? Number.NEGATIVE_INFINITY : Number.POSITIVE_INFINITY;
  const output = buffer(convInfo.outShape, dtype);
  const outputVals = output.values;
  const outputBatchStrides = convInfo.outShape[1] * convInfo.outShape[2] * convInfo.outShape[3];
  const outputRowStrides = convInfo.outShape[2] * convInfo.outShape[3];
  const outputColStrides = convInfo.outShape[3];
  for (let b = 0; b < convInfo.batchSize; ++b) {
    const outputBatchOffset = b * outputBatchStrides;
    const inputBatchOffset = b * strides[0];
    for (let d = 0; d < convInfo.inChannels; ++d) {
      for (let yR = 0; yR < convInfo.outHeight; ++yR) {
        const xRCorner = yR * strideHeight - padTop;
        const xRMin = Math.max(0, xRCorner);
        const xRMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRCorner);
        const outputRowOffset = outputBatchOffset + yR * outputRowStrides;
        for (let yC = 0; yC < convInfo.outWidth; ++yC) {
          const xCCorner = yC * strideWidth - padLeft;
          const xCMin = Math.max(0, xCCorner);
          const xCMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xCCorner);
          let minMaxValue = initialValue;
          let avgValue = 0;
          let count = 0;
          for (let xR = xRMin; xR < xRMax; xR += dilationHeight) {
            const xROffset = inputBatchOffset + xR * strides[1];
            for (let xC = xCMin; xC < xCMax; xC += dilationWidth) {
              const xCOffset = xROffset + xC * strides[2];
              const pixel = xValues[xCOffset + d];
              if (poolType === "max" && pixel > minMaxValue) {
                minMaxValue = pixel;
              } else if (poolType === "avg") {
                avgValue += pixel;
                count++;
              }
            }
            if (isNaN(minMaxValue)) {
              break;
            }
          }
          const outputOffset = outputRowOffset + yC * outputColStrides + d;
          outputVals[outputOffset] = poolType === "avg" ? avgValue / count : minMaxValue;
        }
      }
    }
  }
  return output;
}
function maxPoolPositions(xValues, xShape, dtype, convInfo, flattenPositions = false, includeBatchInIndex = false) {
  const maxPositions = buffer(convInfo.outShape, "int32");
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padTop = convInfo.padInfo.top;
  const padLeft = convInfo.padInfo.left;
  const xBuf = buffer(xShape, dtype, xValues);
  for (let b = 0; b < convInfo.batchSize; ++b) {
    for (let d = 0; d < convInfo.inChannels; ++d) {
      for (let yR = 0; yR < convInfo.outHeight; ++yR) {
        const xRCorner = yR * strideHeight - padTop;
        let xRMin = xRCorner;
        while (xRMin < 0) {
          xRMin += dilationHeight;
        }
        const xRMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRCorner);
        for (let yC = 0; yC < convInfo.outWidth; ++yC) {
          const xCCorner = yC * strideWidth - padLeft;
          let xCMin = xCCorner;
          while (xCMin < 0) {
            xCMin += dilationWidth;
          }
          const xCMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xCCorner);
          let maxValue = Number.NEGATIVE_INFINITY;
          let maxPosition = -1;
          for (let xR = xRMin; xR < xRMax; xR += dilationHeight) {
            const wR = xR - xRCorner;
            for (let xC = xCMin; xC < xCMax; xC += dilationWidth) {
              const wC = xC - xCCorner;
              const pixel = xBuf.get(b, xR, xC, d);
              if (pixel > maxValue) {
                maxValue = pixel;
                if (flattenPositions) {
                  maxPosition = includeBatchInIndex ? ((b * convInfo.inHeight + xR) * convInfo.inWidth + xC) * convInfo.inChannels + d : (xR * convInfo.inWidth + xC) * convInfo.inChannels + d;
                } else {
                  maxPosition = wR * effectiveFilterWidth + wC;
                }
              }
            }
          }
          maxPositions.set(maxPosition, b, yR, yC, d);
        }
      }
    }
  }
  return maxPositions;
}
function pool3d(xValues, xShape, dtype, strides, convInfo, poolType) {
  const strideDepth = convInfo.strideDepth;
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const dilationDepth = convInfo.dilationDepth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterDepth = convInfo.effectiveFilterDepth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padFront = convInfo.padInfo.front;
  const padTop = convInfo.padInfo.top;
  const padLeft = convInfo.padInfo.left;
  const initialValue = poolType === "max" ? Number.NEGATIVE_INFINITY : Number.POSITIVE_INFINITY;
  const output = buffer(convInfo.outShape, dtype);
  const outputVals = output.values;
  const outputBatchStrides = convInfo.outShape[1] * convInfo.outShape[2] * convInfo.outShape[3] * convInfo.outShape[4];
  const outputDepthStrides = convInfo.outShape[2] * convInfo.outShape[3] * convInfo.outShape[4];
  const outputRowStrides = convInfo.outShape[3] * convInfo.outShape[4];
  const outputColStrides = convInfo.outShape[4];
  for (let batch = 0; batch < convInfo.batchSize; ++batch) {
    const outputBatchOffset = batch * outputBatchStrides;
    const inputBatchOffset = batch * strides[0];
    for (let channel = 0; channel < convInfo.inChannels; ++channel) {
      for (let yDepth = 0; yDepth < convInfo.outDepth; ++yDepth) {
        const xDepthCorner = yDepth * strideDepth - padFront;
        let xDepthMin = xDepthCorner;
        while (xDepthMin < 0) {
          xDepthMin += dilationDepth;
        }
        const xDepthMax = Math.min(convInfo.inDepth, effectiveFilterDepth + xDepthCorner);
        const outputDepthOffset = outputBatchOffset + yDepth * outputDepthStrides;
        for (let yRow = 0; yRow < convInfo.outHeight; ++yRow) {
          const xRowCorner = yRow * strideHeight - padTop;
          let xRowMin = xRowCorner;
          while (xRowMin < 0) {
            xRowMin += dilationHeight;
          }
          const xRowMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRowCorner);
          const outputRowOffset = outputDepthOffset + yRow * outputRowStrides;
          for (let yCol = 0; yCol < convInfo.outWidth; ++yCol) {
            const xColCorner = yCol * strideWidth - padLeft;
            let xColMin = xColCorner;
            while (xColMin < 0) {
              xColMin += dilationWidth;
            }
            const xColMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xColCorner);
            const outputColOffset = outputRowOffset + yCol * outputColStrides;
            let minMaxValue = initialValue;
            let avgValue = 0;
            let count = 0;
            for (let xDepth = xDepthMin; xDepth < xDepthMax; xDepth += dilationDepth) {
              const xDepthOffset = inputBatchOffset + xDepth * strides[1];
              for (let xRow = xRowMin; xRow < xRowMax; xRow += dilationHeight) {
                const xRowOffset = xDepthOffset + xRow * strides[2];
                for (let xCol = xColMin; xCol < xColMax; xCol += dilationWidth) {
                  const xColOffset = xRowOffset + xCol * strides[3];
                  const pixel = xValues[xColOffset + channel];
                  if (poolType === "max" && pixel > minMaxValue) {
                    minMaxValue = pixel;
                  } else if (poolType === "avg") {
                    avgValue += pixel;
                    count++;
                  }
                  if (isNaN(minMaxValue)) {
                    break;
                  }
                }
                if (isNaN(minMaxValue)) {
                  break;
                }
              }
              if (isNaN(minMaxValue)) {
                break;
              }
            }
            const outputOffset = outputColOffset + channel;
            outputVals[outputOffset] = poolType === "avg" ? avgValue / Math.max(count, 1) : minMaxValue;
          }
        }
      }
    }
  }
  return output;
}
function maxPool3dPositions(xBuf, convInfo) {
  const maxPositions = buffer(convInfo.outShape, "int32");
  const strideDepth = convInfo.strideDepth;
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const dilationDepth = convInfo.dilationDepth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterDepth = convInfo.effectiveFilterDepth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padFront = convInfo.padInfo.front;
  const padTop = convInfo.padInfo.top;
  const padLeft = convInfo.padInfo.left;
  for (let batch = 0; batch < convInfo.batchSize; ++batch) {
    for (let channel = 0; channel < convInfo.inChannels; ++channel) {
      for (let yDepth = 0; yDepth < convInfo.outDepth; ++yDepth) {
        const xDepthCorner = yDepth * strideDepth - padFront;
        let xDepthMin = xDepthCorner;
        while (xDepthMin < 0) {
          xDepthMin += dilationDepth;
        }
        const xDepthMax = Math.min(convInfo.inDepth, effectiveFilterDepth + xDepthCorner);
        for (let yRow = 0; yRow < convInfo.outHeight; ++yRow) {
          const xRowCorner = yRow * strideHeight - padTop;
          let xRowMin = xRowCorner;
          while (xRowMin < 0) {
            xRowMin += dilationHeight;
          }
          const xRowMax = Math.min(convInfo.inHeight, effectiveFilterHeight + xRowCorner);
          for (let yCol = 0; yCol < convInfo.outWidth; ++yCol) {
            const xColCorner = yCol * strideWidth - padLeft;
            let xColMin = xColCorner;
            while (xColMin < 0) {
              xColMin += dilationWidth;
            }
            const xColMax = Math.min(convInfo.inWidth, effectiveFilterWidth + xColCorner);
            let maxValue = Number.NEGATIVE_INFINITY;
            let maxPosition = -1;
            for (let xDepth = xDepthMin; xDepth < xDepthMax; xDepth += dilationDepth) {
              const wDepth = xDepth - xDepthCorner;
              for (let xRow = xRowMin; xRow < xRowMax; xRow += dilationHeight) {
                const wRow = xRow - xRowCorner;
                for (let xCol = xColMin; xCol < xColMax; xCol += dilationWidth) {
                  const wCol = xCol - xColCorner;
                  const pixel = xBuf.get(batch, xDepth, xRow, xCol, channel);
                  if (pixel >= maxValue) {
                    maxValue = pixel;
                    maxPosition = wDepth * effectiveFilterHeight * effectiveFilterWidth + wRow * effectiveFilterHeight + wCol;
                  }
                }
              }
            }
            maxPositions.set(maxPosition, batch, yDepth, yRow, yCol, channel);
          }
        }
      }
    }
  }
  return maxPositions;
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPool.js
function avgPool(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  assertNotComplex(x, "avgPool");
  const { filterSize, strides, pad, dimRoundingMode } = attrs;
  const dilations = 1;
  util_exports.assert(backend_util_exports.eitherStridesOrDilationsAreOne(strides, dilations), () => `Error in avgPool: Either strides or dilations must be 1. Got strides ${strides} and dilations '${dilations}'`);
  const convInfo = backend_util_exports.computePool2DInfo(x.shape, filterSize, strides, dilations, pad, dimRoundingMode);
  let res;
  if (convInfo.filterWidth === 1 && convInfo.filterHeight === 1 && util_exports.arraysEqual(convInfo.inShape, convInfo.outShape)) {
    res = identity({ inputs: { x }, backend });
  } else {
    const xValues = backend.data.get(x.dataId).values;
    const strides2 = util_exports.computeStrides(x.shape);
    const buffer2 = pool(xValues, x.shape, x.dtype, strides2, convInfo, "avg");
    res = backend.makeTensorInfo(convInfo.outShape, x.dtype, buffer2.values);
  }
  return res;
}
var avgPoolConfig = {
  kernelName: AvgPool,
  backendName: "cpu",
  kernelFunc: avgPool
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPool3D.js
function avgPool3D(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { filterSize, strides, pad, dimRoundingMode, dataFormat } = attrs;
  assertNotComplex(x, "avgPool3d");
  const convInfo = backend_util_exports.computePool3DInfo(x.shape, filterSize, strides, 1, pad, dimRoundingMode, dataFormat);
  const xValues = backend.data.get(x.dataId).values;
  const outBuf = pool3d(xValues, x.shape, x.dtype, util_exports.computeStrides(x.shape), convInfo, "avg");
  return backend.makeTensorInfo(outBuf.shape, "float32", outBuf.values);
}
var avgPool3DConfig = {
  kernelName: AvgPool3D,
  backendName: "cpu",
  kernelFunc: avgPool3D
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPool3DGrad.js
function avgPool3DGrad(args) {
  const { inputs, backend, attrs } = args;
  const { dy, input } = inputs;
  const { filterSize, strides, pad, dimRoundingMode } = attrs;
  assertNotComplex([dy, input], "avgPool3DGrad");
  const convInfo = backend_util_exports.computePool3DInfo(input.shape, filterSize, strides, 1, pad, dimRoundingMode);
  const strideDepth = convInfo.strideDepth;
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const filterDepth = convInfo.filterDepth;
  const filterHeight = convInfo.filterHeight;
  const filterWidth = convInfo.filterWidth;
  const dilationDepth = convInfo.dilationDepth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterDepth = convInfo.effectiveFilterDepth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padFront = effectiveFilterDepth - 1 - convInfo.padInfo.front;
  const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;
  const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;
  const dx = buffer(input.shape, "float32");
  const avgMultiplier = 1 / (filterDepth * filterHeight * filterWidth);
  const dyBuf = backend.bufferSync(dy);
  for (let batch = 0; batch < convInfo.batchSize; ++batch) {
    for (let channel = 0; channel < convInfo.inChannels; ++channel) {
      for (let dxDepth = 0; dxDepth < convInfo.inDepth; ++dxDepth) {
        for (let dxRow = 0; dxRow < convInfo.inHeight; ++dxRow) {
          for (let dxCol = 0; dxCol < convInfo.inWidth; ++dxCol) {
            const dyDepthCorner = dxDepth - padFront;
            const dyRowCorner = dxRow - padTop;
            const dyColCorner = dxCol - padLeft;
            let dotProd = 0;
            for (let wDepth = 0; wDepth < effectiveFilterDepth; wDepth += dilationDepth) {
              const dyDepth = (dyDepthCorner + wDepth) / strideDepth;
              if (dyDepth < 0 || dyDepth >= convInfo.outDepth || Math.floor(dyDepth) !== dyDepth) {
                continue;
              }
              for (let wRow = 0; wRow < effectiveFilterHeight; wRow += dilationHeight) {
                const dyRow = (dyRowCorner + wRow) / strideHeight;
                if (dyRow < 0 || dyRow >= convInfo.outHeight || Math.floor(dyRow) !== dyRow) {
                  continue;
                }
                for (let wCol = 0; wCol < effectiveFilterWidth; wCol += dilationWidth) {
                  const dyCol = (dyColCorner + wCol) / strideWidth;
                  if (dyCol < 0 || dyCol >= convInfo.outWidth || Math.floor(dyCol) !== dyCol) {
                    continue;
                  }
                  const pixel = dyBuf.get(batch, dyDepth, dyRow, dyCol, channel);
                  dotProd += pixel;
                }
              }
            }
            dx.set(dotProd * avgMultiplier, batch, dxDepth, dxRow, dxCol, channel);
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var avgPool3DGradConfig = {
  kernelName: AvgPool3DGrad,
  backendName: "cpu",
  kernelFunc: avgPool3DGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPoolGrad.js
function avgPoolGrad(args) {
  const { inputs, backend, attrs } = args;
  const { dy, input } = inputs;
  const x = input;
  assertNotComplex([dy, input], "avgPoolGrad");
  const { filterSize, strides, pad } = attrs;
  const convInfo = backend_util_exports.computePool2DInfo(x.shape, filterSize, strides, 1, pad);
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const filterHeight = convInfo.filterHeight;
  const filterWidth = convInfo.filterWidth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;
  const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;
  const dx = buffer(x.shape, "float32");
  const avgMultiplier = 1 / (filterHeight * filterWidth);
  const dyData = backend.data.get(dy.dataId).values;
  const dyBuf = buffer(dy.shape, "float32", dyData);
  for (let b = 0; b < convInfo.batchSize; ++b) {
    for (let d = 0; d < convInfo.inChannels; ++d) {
      for (let dxR = 0; dxR < convInfo.inHeight; ++dxR) {
        for (let dxC = 0; dxC < convInfo.inWidth; ++dxC) {
          const dyRCorner = dxR - padTop;
          const dyCCorner = dxC - padLeft;
          let dotProd = 0;
          for (let wR = 0; wR < effectiveFilterHeight; wR += dilationHeight) {
            const dyR = (dyRCorner + wR) / strideHeight;
            if (dyR < 0 || dyR >= convInfo.outHeight || Math.floor(dyR) !== dyR) {
              continue;
            }
            for (let wC = 0; wC < effectiveFilterWidth; wC += dilationWidth) {
              const dyC = (dyCCorner + wC) / strideWidth;
              if (dyC < 0 || dyC >= convInfo.outWidth || Math.floor(dyC) !== dyC) {
                continue;
              }
              const pixel = dyBuf.get(b, dyR, dyC, d);
              dotProd += pixel;
            }
          }
          dx.set(dotProd * avgMultiplier, b, dxR, dxC, d);
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var avgPoolGradConfig = {
  kernelName: AvgPoolGrad,
  backendName: "cpu",
  kernelFunc: avgPoolGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/BatchNorm.js
function batchNorm(args) {
  const { inputs, backend, attrs } = args;
  const { x, scale: scale2, offset, mean: mean2, variance } = inputs;
  util_exports.assert(mean2.shape.length === variance.shape.length, () => "Batch normalization gradient requires mean and variance to have equal ranks.");
  util_exports.assert(offset == null || mean2.shape.length === offset.shape.length, () => "Batch normalization gradient requires mean and offset to have equal ranks.");
  util_exports.assert(scale2 == null || mean2.shape.length === scale2.shape.length, () => "Batch normalization gradient requires mean and scale to have equal ranks.");
  assertNotComplex([x, mean2, variance, scale2, offset], "batchNorm");
  let { varianceEpsilon } = attrs;
  if (varianceEpsilon == null) {
    varianceEpsilon = 1e-3;
  }
  const xVals = backend.data.get(x.dataId).values;
  const mVals = backend.data.get(mean2.dataId).values;
  const varVals = backend.data.get(variance.dataId).values;
  const sVals = scale2 ? backend.data.get(scale2.dataId).values : new Float32Array([1]);
  const offVals = offset ? backend.data.get(offset.dataId).values : new Float32Array([0]);
  const outVals = new Float32Array(xVals.length);
  const offValsLength = offVals.length;
  const sValsLength = sVals.length;
  const varValsLength = varVals.length;
  const mValsLength = mVals.length;
  let offi = 0;
  let mi = 0;
  let si = 0;
  let vi = 0;
  for (let i = 0; i < xVals.length; ++i) {
    outVals[i] = offVals[offi++] + (xVals[i] - mVals[mi++]) * sVals[si++] / Math.sqrt(varVals[vi++] + varianceEpsilon);
    if (offi >= offValsLength) {
      offi = 0;
    }
    if (mi >= mValsLength) {
      mi = 0;
    }
    if (si >= sValsLength) {
      si = 0;
    }
    if (vi >= varValsLength) {
      vi = 0;
    }
  }
  return backend.makeTensorInfo(x.shape, x.dtype, outVals);
}
var batchNormConfig = {
  kernelName: FusedBatchNorm,
  backendName: "cpu",
  kernelFunc: batchNorm
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/BatchToSpaceND.js
function batchToSpaceND(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { blockShape, crops } = attrs;
  assertNotComplex([x], "batchToSpaceND");
  const prod = blockShape.reduce((a, b) => a * b);
  const reshaped = backend_util_exports.getReshaped(x.shape, blockShape, prod);
  const permuted = backend_util_exports.getPermuted(reshaped.length, blockShape.length);
  const reshapedPermuted = backend_util_exports.getReshapedPermuted(x.shape, blockShape, prod);
  const sliceBeginCoords = backend_util_exports.getSliceBeginCoords(crops, blockShape.length);
  const sliceSize = backend_util_exports.getSliceSize(reshapedPermuted, crops, blockShape.length);
  const xReshaped = reshape({ inputs: { x }, backend, attrs: { shape: reshaped } });
  const xTransposed = transpose({ inputs: { x: xReshaped }, backend, attrs: { perm: permuted } });
  const xTransposedReshaped = reshape({ inputs: { x: xTransposed }, backend, attrs: { shape: reshapedPermuted } });
  const result = slice({
    inputs: { x: xTransposedReshaped },
    backend,
    attrs: { begin: sliceBeginCoords, size: sliceSize }
  });
  backend.disposeIntermediateTensorInfo(xReshaped);
  backend.disposeIntermediateTensorInfo(xTransposed);
  backend.disposeIntermediateTensorInfo(xTransposedReshaped);
  return result;
}
var batchToSpaceNDConfig = {
  kernelName: BatchToSpaceND,
  backendName: "cpu",
  kernelFunc: batchToSpaceND
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Bincount.js
function bincount(args) {
  const { inputs, backend, attrs } = args;
  const { x, weights } = inputs;
  const { size } = attrs;
  const xVals = backend.data.get(x.dataId).values;
  const weightsVals = backend.data.get(weights.dataId).values;
  const outVals = bincountImpl(xVals, weightsVals, weights.dtype, weights.shape, size);
  return backend.makeTensorInfo([size], weights.dtype, outVals);
}
var bincountConfig = {
  kernelName: Bincount,
  backendName: "cpu",
  kernelFunc: bincount
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/BroadcastArgs.js
function broadcastArgs(args) {
  const { inputs, backend } = args;
  const { s0, s1 } = inputs;
  const s0Vals = backend.data.get(s0.dataId).values;
  const s1Vals = backend.data.get(s1.dataId).values;
  const broadcastShape = backend_util_exports.assertAndGetBroadcastShape(Array.from(s0Vals), Array.from(s1Vals));
  return backend.makeTensorInfo([broadcastShape.length], "int32", Int32Array.from(broadcastShape));
}
var broadcastArgsConfig = {
  kernelName: BroadcastArgs,
  backendName: "cpu",
  kernelFunc: broadcastArgs
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ClipByValue.js
var clipByValue = unaryKernelFunc(ClipByValue, (xi, attrs) => {
  const clipAttrs = attrs;
  if (xi > clipAttrs.clipValueMax) {
    return clipAttrs.clipValueMax;
  }
  return xi < clipAttrs.clipValueMin ? clipAttrs.clipValueMin : xi;
});
var clipByValueConfig = {
  kernelName: ClipByValue,
  backendName: "cpu",
  kernelFunc: clipByValue
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ComplexAbs.js
var complexAbs = (args) => {
  const { x } = args.inputs;
  const cpuBackend = args.backend;
  const resultValues = new Float32Array(util_exports.sizeFromShape(x.shape));
  const complexVals = cpuBackend.data.get(x.dataId);
  const real2 = complexVals.complexTensorInfos.real;
  const imag2 = complexVals.complexTensorInfos.imag;
  const realVals = cpuBackend.data.get(real2.dataId).values;
  const imagVals = cpuBackend.data.get(imag2.dataId).values;
  for (let i = 0; i < realVals.length; i++) {
    const real3 = realVals[i];
    const imag3 = imagVals[i];
    resultValues[i] = Math.hypot(real3, imag3);
  }
  return cpuBackend.makeOutput(resultValues, x.shape, "float32");
};
var complexAbsConfig = {
  kernelName: ComplexAbs,
  backendName: "cpu",
  kernelFunc: complexAbs
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Imag.js
function imag(args) {
  const { inputs, backend } = args;
  const { input } = inputs;
  const imag2 = backend.data.get(input.dataId).complexTensorInfos.imag;
  const imagVal = backend.data.get(imag2.dataId).values;
  return backend.makeTensorInfo(imag2.shape, imag2.dtype, imagVal);
}
var imagConfig = {
  kernelName: Imag,
  backendName: "cpu",
  kernelFunc: imag
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Concat.js
function concat(args) {
  const { inputs, backend, attrs } = args;
  const { axis } = attrs;
  const $axis = util_exports.parseAxisParam(axis, inputs[0].shape)[0];
  const shapes = inputs.map((t) => t.shape);
  backend_util_exports.assertParamsConsistent(shapes, $axis);
  let outShape = backend_util_exports.computeOutShape(inputs.map((t) => t.shape), $axis);
  if (util_exports.sizeFromShape(outShape) === 0) {
    return backend.makeTensorInfo(outShape, inputs[0].dtype, []);
  }
  const $inputs = inputs.filter((t) => util_exports.sizeFromShape(t.shape) > 0);
  if ($inputs.length === 1) {
    return identity({ inputs: { x: $inputs[0] }, backend });
  }
  if ($inputs[0].dtype === "complex64") {
    const reals = $inputs.map((t) => real({ inputs: { input: t }, backend }));
    const imags = $inputs.map((t) => imag({ inputs: { input: t }, backend }));
    const realConcated = concat({ inputs: reals, backend, attrs: { axis: $axis } });
    const imagConcated = concat({ inputs: imags, backend, attrs: { axis: $axis } });
    const result = complex({ inputs: { real: realConcated, imag: imagConcated }, backend });
    reals.forEach((r) => backend.disposeIntermediateTensorInfo(r));
    imags.forEach((i) => backend.disposeIntermediateTensorInfo(i));
    backend.disposeIntermediateTensorInfo(realConcated);
    backend.disposeIntermediateTensorInfo(imagConcated);
    return result;
  }
  const inputs2D = $inputs.map((t) => {
    const innerSize = util_exports.sizeFromShape(t.shape.slice($axis));
    const shape = [-1, innerSize];
    return reshape({ inputs: { x: t }, backend, attrs: { shape } });
  });
  const inputsValShapes = inputs2D.map((t) => {
    return { vals: backend.data.get(t.dataId).values, shape: t.shape };
  });
  outShape = backend_util_exports.computeOutShape(
    inputs2D.map((t) => t.shape),
    1
    /* axis */
  );
  const simplyConcat = inputs2D[0].shape[0] === 1;
  const outVals = concatImpl(inputsValShapes, outShape, inputs[0].dtype, simplyConcat);
  const finalOutShape = backend_util_exports.computeOutShape($inputs.map((t) => t.shape), $axis);
  const outInfo = backend.makeTensorInfo(finalOutShape, inputs[0].dtype, outVals);
  inputs2D.forEach((t) => backend.disposeIntermediateTensorInfo(t));
  return outInfo;
}
var concatConfig = {
  kernelName: Concat,
  backendName: "cpu",
  kernelFunc: concat
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Conv2D.js
function conv2D(args) {
  const { inputs, backend, attrs } = args;
  const { x, filter } = inputs;
  const { strides, pad, dataFormat, dilations, dimRoundingMode } = attrs;
  assertNotComplex([x, filter], "conv2d");
  const $dataFormat = backend_util_exports.convertConv2DDataFormat(dataFormat);
  const convInfo = backend_util_exports.computeConv2DInfo(x.shape, filter.shape, strides, dilations, pad, dimRoundingMode, false, $dataFormat);
  const filterHeight = convInfo.filterHeight;
  const filterWidth = convInfo.filterWidth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const padLeft = convInfo.padInfo.left;
  const padTop = convInfo.padInfo.top;
  const isChannelsLast = convInfo.dataFormat === "channelsLast";
  const y = new TensorBuffer(convInfo.outShape, x.dtype);
  const xStrides = util_exports.computeStrides(x.shape);
  const filterStrides = util_exports.computeStrides(filter.shape);
  const xBatchStride = xStrides[0];
  const xRowStride = isChannelsLast ? xStrides[1] : xStrides[2];
  const xColStride = isChannelsLast ? xStrides[2] : 1;
  const xChannelStride = isChannelsLast ? 1 : xStrides[1];
  const yBatchStride = y.strides[0];
  const yRowStride = isChannelsLast ? y.strides[1] : y.strides[2];
  const yColStride = isChannelsLast ? y.strides[2] : 1;
  const yChannelStride = isChannelsLast ? 1 : y.strides[1];
  const xVals = backend.data.get(x.dataId).values;
  const wVals = backend.data.get(filter.dataId).values;
  const yVals = y.values;
  for (let b = 0; b < convInfo.batchSize; ++b) {
    const xOffset1 = b * xBatchStride;
    const yOffset1 = b * yBatchStride;
    for (let yR = 0; yR < convInfo.outHeight; ++yR) {
      const yOffset2 = yOffset1 + yR * yRowStride;
      const xRCorner = yR * convInfo.strideHeight - padTop;
      for (let wR = 0; wR < filterHeight; ++wR) {
        const xR = xRCorner + wR * dilationHeight;
        if (xR < 0 || xR >= convInfo.inHeight) {
          continue;
        }
        const wOffset1 = wR * filterStrides[0];
        const xOffset2 = xOffset1 + xR * xRowStride;
        for (let yC = 0; yC < convInfo.outWidth; ++yC) {
          const yOffset3 = yOffset2 + yC * yColStride;
          const xCCorner = yC * convInfo.strideWidth - padLeft;
          for (let wC = 0; wC < filterWidth; ++wC) {
            const xC = xCCorner + wC * dilationWidth;
            if (xC < 0 || xC >= convInfo.inWidth) {
              continue;
            }
            const wOffset2 = wOffset1 + wC * filterStrides[1];
            const xOffset3 = xOffset2 + xC * xColStride;
            let wOffset3 = wOffset2;
            for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
              const xVal = xVals[xOffset3 + d1 * xChannelStride];
              for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
                yVals[yOffset3 + d2 * yChannelStride] += xVal * wVals[wOffset3 + d2];
              }
              wOffset3 += convInfo.outChannels;
            }
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(y.shape, y.dtype, yVals);
}
var conv2DConfig = {
  kernelName: Conv2D,
  backendName: "cpu",
  kernelFunc: conv2D
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Conv2DBackpropFilter.js
function conv2DBackpropFilter(args) {
  const { inputs, backend, attrs } = args;
  const { x, dy } = inputs;
  const { strides, pad, dataFormat, dimRoundingMode, filterShape } = attrs;
  assertNotComplex([x, dy], "conv2dBackpropFilter");
  const $dataFormat = backend_util_exports.convertConv2DDataFormat(dataFormat);
  const convInfo = backend_util_exports.computeConv2DInfo(x.shape, filterShape, strides, 1, pad, dimRoundingMode, false, $dataFormat);
  const { strideHeight, strideWidth, filterHeight, filterWidth } = convInfo;
  const isChannelsLast = convInfo.dataFormat === "channelsLast";
  const dW = new TensorBuffer(convInfo.filterShape, "float32");
  const leftPad = convInfo.padInfo.left;
  const topPad = convInfo.padInfo.top;
  const xVals = backend.data.get(x.dataId).values;
  const dyVals = backend.data.get(dy.dataId).values;
  const xBuf = new TensorBuffer(x.shape, x.dtype, xVals);
  const dyBuf = new TensorBuffer(dy.shape, dy.dtype, dyVals);
  for (let wR = 0; wR < filterHeight; ++wR) {
    const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));
    const yRMax = Math.min(convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);
    for (let wC = 0; wC < filterWidth; ++wC) {
      const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));
      const yCMax = Math.min(convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);
      for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
        for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
          let dotProd = 0;
          for (let b = 0; b < convInfo.batchSize; ++b) {
            for (let yR = yRMin; yR < yRMax; ++yR) {
              const xR = wR + yR * strideHeight - topPad;
              for (let yC = yCMin; yC < yCMax; ++yC) {
                const xC = wC + yC * strideWidth - leftPad;
                if (isChannelsLast) {
                  dotProd += xBuf.get(b, xR, xC, d1) * dyBuf.get(b, yR, yC, d2);
                } else {
                  dotProd += xBuf.get(b, d1, xR, xC) * dyBuf.get(b, d2, yR, yC);
                }
              }
            }
          }
          dW.set(dotProd, wR, wC, d1, d2);
        }
      }
    }
  }
  return backend.makeTensorInfo(dW.shape, dW.dtype, dW.values);
}
var conv2DBackpropFilterConfig = {
  kernelName: Conv2DBackpropFilter,
  backendName: "cpu",
  kernelFunc: conv2DBackpropFilter
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Conv2DBackpropInput.js
function conv2DBackpropInput(args) {
  const { inputs, backend, attrs } = args;
  const { dy, filter } = inputs;
  const { inputShape, strides, pad, dataFormat, dimRoundingMode } = attrs;
  assertNotComplex([dy, filter], "conv2dBackpropInput");
  const filterStrides = util_exports.computeStrides(filter.shape);
  const dyStrides = util_exports.computeStrides(dy.shape);
  let $dataFormat = backend_util_exports.convertConv2DDataFormat(dataFormat);
  const convInfo = backend_util_exports.computeConv2DInfo(inputShape, filter.shape, strides, 1, pad, dimRoundingMode, false, $dataFormat);
  const dx = new TensorBuffer(convInfo.inShape, "float32");
  const dxValues = dx.values;
  const dyValues = backend.data.get(dy.dataId).values;
  const fltValues = backend.data.get(filter.dataId).values;
  const [fltS0, fltS1, fltS2] = filterStrides;
  const { batchSize, filterHeight, filterWidth, inChannels, inHeight, inWidth, outChannels, outHeight, outWidth, strideHeight, strideWidth } = convInfo;
  $dataFormat = convInfo.dataFormat;
  const topPad = filterHeight - 1 - convInfo.padInfo.top;
  const leftPad = filterWidth - 1 - convInfo.padInfo.left;
  const isChannelsLast = $dataFormat === "channelsLast";
  const xBatchStride = dx.strides[0];
  const xRowStride = isChannelsLast ? dx.strides[1] : dx.strides[2];
  const xColStride = isChannelsLast ? dx.strides[2] : 1;
  const xChannelStride = isChannelsLast ? 1 : dx.strides[1];
  const yBatchStride = dyStrides[0];
  const yRowStride = isChannelsLast ? dyStrides[1] : dyStrides[2];
  const yColStride = isChannelsLast ? dyStrides[2] : 1;
  const yChannelStride = isChannelsLast ? 1 : dyStrides[1];
  for (let b = 0; b < batchSize; ++b) {
    for (let d1 = 0; d1 < inChannels; ++d1) {
      for (let xR = 0; xR < inHeight; ++xR) {
        const xRCorner = xR - topPad;
        const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));
        const yRMax = Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);
        for (let xC = 0; xC < inWidth; ++xC) {
          const xCCorner = xC - leftPad;
          const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));
          const yCMax = Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);
          let dotProd = 0;
          for (let yR = xRMin; yR < yRMax; ++yR) {
            const wR = yR * strideHeight - xRCorner;
            for (let yC = xCMin; yC < yCMax; ++yC) {
              const wC = yC * strideWidth - xCCorner;
              const dyOffset = yBatchStride * b + yRowStride * yR + yColStride * yC;
              const fltOffset = fltS0 * (filterHeight - 1 - wR) + fltS1 * (filterWidth - 1 - wC) + fltS2 * d1;
              for (let d2 = 0; d2 < outChannels; ++d2) {
                const pixel = dyValues[dyOffset + yChannelStride * d2];
                const weight = fltValues[fltOffset + d2];
                dotProd += pixel * weight;
              }
            }
          }
          const dxOffset = xBatchStride * b + xRowStride * xR + xColStride * xC + xChannelStride * d1;
          dxValues[dxOffset] = dotProd;
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var conv2DBackpropInputConfig = {
  kernelName: Conv2DBackpropInput,
  backendName: "cpu",
  kernelFunc: conv2DBackpropInput
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Conv3D.js
function conv3D(args) {
  const { inputs, backend, attrs } = args;
  const { x, filter } = inputs;
  const { strides, pad, dilations } = attrs;
  assertNotComplex([x, filter], "conv3d");
  const convInfo = backend_util_exports.computeConv3DInfo(x.shape, filter.shape, strides, dilations, pad);
  const { filterDepth, filterHeight, filterWidth, dilationDepth, dilationHeight, dilationWidth, padInfo } = convInfo;
  const padFront = padInfo.front;
  const padLeft = padInfo.left;
  const padTop = padInfo.top;
  const y = new TensorBuffer(convInfo.outShape, x.dtype);
  const xVals = backend.data.get(x.dataId).values;
  const wVals = backend.data.get(filter.dataId).values;
  const yVals = y.values;
  const xStrides = util_exports.computeStrides(x.shape);
  const filterStrides = util_exports.computeStrides(filter.shape);
  for (let b = 0; b < convInfo.batchSize; ++b) {
    const xOffset1 = b * xStrides[0];
    const yOffset1 = b * y.strides[0];
    for (let yF = 0; yF < convInfo.outDepth; ++yF) {
      const yOffset2 = yOffset1 + yF * y.strides[1];
      const xFCorner = yF * convInfo.strideDepth - padFront;
      for (let wF = 0; wF < filterDepth; ++wF) {
        const xF = xFCorner + wF * dilationDepth;
        if (xF < 0 || xF >= convInfo.inDepth) {
          continue;
        }
        const wOffset1 = wF * filterStrides[0];
        const xOffset2 = xOffset1 + xF * xStrides[1];
        for (let yR = 0; yR < convInfo.outHeight; ++yR) {
          const yOffset3 = yOffset2 + yR * y.strides[2];
          const xRCorner = yR * convInfo.strideHeight - padTop;
          for (let wR = 0; wR < filterHeight; ++wR) {
            const xR = xRCorner + wR * dilationHeight;
            if (xR < 0 || xR >= convInfo.inHeight) {
              continue;
            }
            const wOffset2 = wOffset1 + wR * filterStrides[1];
            const xOffset3 = xOffset2 + xR * xStrides[2];
            for (let yC = 0; yC < convInfo.outWidth; ++yC) {
              const yOffset4 = yOffset3 + yC * convInfo.outChannels;
              const xCCorner = yC * convInfo.strideWidth - padLeft;
              for (let wC = 0; wC < filterWidth; ++wC) {
                const xC = xCCorner + wC * dilationWidth;
                if (xC < 0 || xC >= convInfo.inWidth) {
                  continue;
                }
                const wOffset3 = wOffset2 + wC * filterStrides[2];
                const xOffset4 = xOffset3 + xC * convInfo.inChannels;
                let wOffset4 = wOffset3;
                for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
                  const xVal = xVals[xOffset4 + d1];
                  for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
                    yVals[yOffset4 + d2] += xVal * wVals[wOffset4 + d2];
                  }
                  wOffset4 += convInfo.outChannels;
                }
              }
            }
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(y.shape, y.dtype, y.values);
}
var conv3DConfig = {
  kernelName: Conv3D,
  backendName: "cpu",
  kernelFunc: conv3D
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Conv3DBackpropFilterV2.js
function conv3DBackpropFilterV2(args) {
  const { inputs, backend, attrs } = args;
  const { x, dy } = inputs;
  const { strides, pad, filterShape } = attrs;
  assertNotComplex([x, dy], "conv3dBackpropFilterV2");
  const xStrides = util_exports.computeStrides(x.shape);
  const dyStrides = util_exports.computeStrides(dy.shape);
  const convInfo = backend_util_exports.computeConv3DInfo(x.shape, filterShape, strides, 1, pad);
  const strideDepth = convInfo.strideDepth;
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const filterDepth = convInfo.filterDepth;
  const filterHeight = convInfo.filterHeight;
  const filterWidth = convInfo.filterWidth;
  const dw = new TensorBuffer(convInfo.filterShape, "float32");
  const dwValues = dw.values;
  const [dwS0, dwS1, dwS2, dwS3] = dw.strides;
  const dyValues = backend.data.get(dy.dataId).values;
  const [dyS0, dyS1, dyS2, dyS3] = dyStrides;
  const xValues = backend.data.get(x.dataId).values;
  const [xS0, xS1, xS2, xS3] = xStrides;
  const frontPad = convInfo.padInfo.front;
  const leftPad = convInfo.padInfo.left;
  const topPad = convInfo.padInfo.top;
  for (let wF = 0; wF < filterDepth; ++wF) {
    const yFMin = Math.max(0, Math.ceil((frontPad - wF) / strideDepth));
    const yFMax = Math.min(convInfo.outDepth, (convInfo.inDepth + frontPad - wF) / strideDepth);
    const wOffset1 = wF * dwS0;
    for (let wR = 0; wR < filterHeight; ++wR) {
      const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));
      const yRMax = Math.min(convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);
      const wOffset2 = wR * dwS1 + wOffset1;
      for (let wC = 0; wC < filterWidth; ++wC) {
        const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));
        const yCMax = Math.min(convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);
        const wOffset3 = wC * dwS2 + wOffset2;
        for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
          const wOffset4 = d1 * dwS3 + wOffset3;
          for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
            let dotProd = 0;
            for (let b = 0; b < convInfo.batchSize; ++b) {
              const xOffset1 = b * xS0;
              const yOffset1 = b * dyS0;
              for (let yF = yFMin; yF < yFMax; ++yF) {
                const xF = wF + yF * strideDepth - frontPad;
                const xOffset2 = xF * xS1 + xOffset1;
                const yOffset2 = yF * dyS1 + yOffset1;
                for (let yR = yRMin; yR < yRMax; ++yR) {
                  const xR = wR + yR * strideHeight - topPad;
                  const xOffset3 = xR * xS2 + xOffset2;
                  const yOffset3 = yR * dyS2 + yOffset2;
                  for (let yC = yCMin; yC < yCMax; ++yC) {
                    const xC = wC + yC * strideWidth - leftPad;
                    const xOffset4 = xC * xS3 + xOffset3;
                    const yOffset4 = yC * dyS3 + yOffset3;
                    dotProd += xValues[xOffset4 + d1] * dyValues[yOffset4 + d2];
                  }
                }
              }
            }
            dwValues[wOffset4 + d2] = dotProd;
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(dw.shape, dw.dtype, dw.values);
}
var conv3DBackpropFilterV2Config = {
  kernelName: Conv3DBackpropFilterV2,
  backendName: "cpu",
  kernelFunc: conv3DBackpropFilterV2
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Conv3DBackpropInputV2.js
function conv3DBackpropInputV2(args) {
  const { inputs, backend, attrs } = args;
  const { dy, filter } = inputs;
  const { pad, strides, inputShape } = attrs;
  assertNotComplex([dy], "conv3dBackpropInputV2");
  const dyStrides = util_exports.computeStrides(dy.shape);
  const filterStrides = util_exports.computeStrides(filter.shape);
  const convInfo = backend_util_exports.computeConv3DInfo(inputShape, filter.shape, strides, 1, pad);
  const dx = new TensorBuffer(convInfo.inShape, "float32");
  const dxValues = dx.values;
  const [dxS0, dxS1, dxS2, dxS3] = dx.strides;
  const dyValues = backend.data.get(dy.dataId).values;
  const [dyS0, dyS1, dyS2, dyS3] = dyStrides;
  const fltValues = backend.data.get(filter.dataId).values;
  const [fltS0, fltS1, fltS2, fltS3] = filterStrides;
  const { batchSize, filterDepth, filterHeight, filterWidth, inChannels, inDepth, inHeight, inWidth, outChannels, outDepth, outHeight, outWidth, strideDepth, strideHeight, strideWidth } = convInfo;
  const frontPad = filterDepth - 1 - convInfo.padInfo.front;
  const topPad = filterHeight - 1 - convInfo.padInfo.top;
  const leftPad = filterWidth - 1 - convInfo.padInfo.left;
  for (let b = 0; b < batchSize; ++b) {
    for (let d1 = 0; d1 < inChannels; ++d1) {
      for (let xF = 0; xF < inDepth; ++xF) {
        const xFCorner = xF - frontPad;
        const xFMin = Math.max(0, Math.ceil(xFCorner / strideDepth));
        const yFMax = Math.min(outDepth, (filterDepth + xFCorner) / strideDepth);
        for (let xR = 0; xR < inHeight; ++xR) {
          const xRCorner = xR - topPad;
          const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));
          const yRMax = Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);
          for (let xC = 0; xC < inWidth; ++xC) {
            const xCCorner = xC - leftPad;
            const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));
            const yCMax = Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);
            let dotProd = 0;
            for (let yF = xFMin; yF < yFMax; ++yF) {
              const wF = yF * strideDepth - xFCorner;
              for (let yR = xRMin; yR < yRMax; ++yR) {
                const wR = yR * strideHeight - xRCorner;
                for (let yC = xCMin; yC < yCMax; ++yC) {
                  const wC = yC * strideWidth - xCCorner;
                  const dyOffset = dyS0 * b + dyS1 * yF + dyS2 * yR + dyS3 * yC;
                  const fltOffset = fltS0 * (filterDepth - 1 - wF) + fltS1 * (filterHeight - 1 - wR) + fltS2 * (filterWidth - 1 - wC) + fltS3 * d1;
                  for (let d2 = 0; d2 < outChannels; ++d2) {
                    const pixel = dyValues[dyOffset + d2];
                    const weight = fltValues[fltOffset + d2];
                    dotProd += pixel * weight;
                  }
                }
              }
            }
            dxValues[dxS0 * b + dxS1 * xF + dxS2 * xR + dxS3 * xC + d1] = dotProd;
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var conv3DBackpropInputV2Config = {
  kernelName: Conv3DBackpropInputV2,
  backendName: "cpu",
  kernelFunc: conv3DBackpropInputV2
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Cos.js
var cos = unaryKernelFunc(Cos, (xi) => Math.cos(xi));
var cosConfig = {
  kernelName: Cos,
  backendName: "cpu",
  kernelFunc: cos
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Cosh.js
var cosh = unaryKernelFunc(Cosh, (xi) => Math.cosh(xi));
var coshConfig = {
  kernelName: Cosh,
  backendName: "cpu",
  kernelFunc: cosh
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/CropAndResize.js
function cropAndResize(args) {
  const { inputs, backend, attrs } = args;
  const { image, boxes, boxInd } = inputs;
  const { cropSize, method, extrapolationValue } = attrs;
  const [batch, imageHeight, imageWidth, numChannels] = image.shape;
  const numBoxes = boxes.shape[0];
  const [cropHeight, cropWidth] = cropSize;
  const output = buffer([numBoxes, cropHeight, cropWidth, numChannels], "float32");
  const boxVals = backend.data.get(boxes.dataId).values;
  const boxIndVals = backend.data.get(boxInd.dataId).values;
  const imageVals = backend.data.get(image.dataId).values;
  const inStride = util_exports.computeStrides(image.shape);
  const outStride = util_exports.computeStrides(output.shape);
  for (let b = 0; b < numBoxes; b++) {
    const startInd = b * 4;
    const y1 = boxVals[startInd];
    const x1 = boxVals[startInd + 1];
    const y2 = boxVals[startInd + 2];
    const x2 = boxVals[startInd + 3];
    const bInd = boxIndVals[b];
    if (bInd >= batch) {
      continue;
    }
    const heightScale = cropHeight > 1 ? (y2 - y1) * (imageHeight - 1) / (cropHeight - 1) : 0;
    const widthScale = cropWidth > 1 ? (x2 - x1) * (imageWidth - 1) / (cropWidth - 1) : 0;
    for (let y = 0; y < cropHeight; y++) {
      const yInd = cropHeight > 1 ? y1 * (imageHeight - 1) + y * heightScale : 0.5 * (y1 + y2) * (imageHeight - 1);
      if (yInd < 0 || yInd > imageHeight - 1) {
        for (let x = 0; x < cropWidth; x++) {
          for (let c = 0; c < numChannels; c++) {
            const ind = c + x * outStride[2] + y * outStride[1] + b * outStride[0];
            output.values[ind] = extrapolationValue;
          }
        }
        continue;
      }
      if (method === "bilinear") {
        const topInd = Math.floor(yInd);
        const bottomInd = Math.ceil(yInd);
        const yLerp = yInd - topInd;
        for (let x = 0; x < cropWidth; x++) {
          const xInd = cropWidth > 1 ? x1 * (imageWidth - 1) + x * widthScale : 0.5 * (x1 + x2) * (imageWidth - 1);
          if (xInd < 0 || xInd > imageWidth - 1) {
            for (let c = 0; c < numChannels; c++) {
              const ind = c + x * outStride[2] + y * outStride[1] + b * outStride[0];
              output.values[ind] = extrapolationValue;
            }
            continue;
          }
          const leftInd = Math.floor(xInd);
          const rightInd = Math.ceil(xInd);
          const xLerp = xInd - leftInd;
          for (let c = 0; c < numChannels; c++) {
            let ind = c + leftInd * inStride[2] + topInd * inStride[1] + bInd * inStride[0];
            const topLeft = imageVals[ind];
            ind = c + rightInd * inStride[2] + topInd * inStride[1] + bInd * inStride[0];
            const topRight = imageVals[ind];
            ind = c + leftInd * inStride[2] + bottomInd * inStride[1] + bInd * inStride[0];
            const bottomLeft = imageVals[ind];
            ind = c + rightInd * inStride[2] + bottomInd * inStride[1] + bInd * inStride[0];
            const bottomRight = imageVals[ind];
            const top = topLeft + (topRight - topLeft) * xLerp;
            const bottom = bottomLeft + (bottomRight - bottomLeft) * xLerp;
            ind = c + x * outStride[2] + y * outStride[1] + b * outStride[0];
            output.values[ind] = top + (bottom - top) * yLerp;
          }
        }
      } else {
        for (let x = 0; x < cropWidth; ++x) {
          const xInd = cropWidth > 1 ? x1 * (imageWidth - 1) + x * widthScale : 0.5 * (x1 + x2) * (imageWidth - 1);
          if (xInd < 0 || xInd > imageWidth - 1) {
            for (let c = 0; c < numChannels; c++) {
              const ind = c + x * outStride[2] + y * outStride[1] + b * outStride[0];
              output.values[ind] = extrapolationValue;
            }
            continue;
          }
          const closestX = Math.round(xInd);
          const closestY = Math.round(yInd);
          for (let c = 0; c < numChannels; c++) {
            const inInd = c + closestX * inStride[2] + closestY * inStride[1] + bInd * inStride[0];
            const outInd = c + x * outStride[2] + y * outStride[1] + b * outStride[0];
            output.values[outInd] = imageVals[inInd];
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(output.shape, output.dtype, output.values);
}
var cropAndResizeConfig = {
  kernelName: CropAndResize,
  backendName: "cpu",
  kernelFunc: cropAndResize
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Cumprod.js
function cumprod(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, exclusive, reverse: reverse2 } = attrs;
  assertNotComplex(x, "cumprod");
  const permutation = backend_util_exports.getAxesPermutation([axis], x.shape.length);
  let $x = x;
  if (permutation != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutation } });
  }
  const permutedAxis = backend_util_exports.getInnerMostAxes(1, x.shape.length)[0];
  if (permutedAxis !== $x.shape.length - 1) {
    throw new Error(`backend.cumprod in CPU expects an inner-most axis=${$x.shape.length - 1} but got axis=${permutedAxis}`);
  }
  const resultDtype = upcastType($x.dtype, "int32");
  const vals = util_exports.makeOnesTypedArray(util_exports.sizeFromShape($x.shape), resultDtype);
  const aVals = backend.data.get($x.dataId).values;
  const finalDim = $x.shape[$x.shape.length - 1];
  const indexAdjuster = reverse2 ? (i, j) => i + finalDim - j - 1 : (i, j) => i + j;
  for (let i = 0; i < aVals.length; i += finalDim) {
    for (let j = 0; j < finalDim; j++) {
      const idx = indexAdjuster(i, j);
      if (j === 0) {
        vals[idx] = exclusive ? 1 : aVals[idx];
      } else {
        const prevIdx = indexAdjuster(i, j - 1);
        vals[idx] = exclusive ? aVals[prevIdx] * vals[prevIdx] : aVals[idx] * vals[prevIdx];
      }
    }
  }
  const result = backend.makeTensorInfo($x.shape, resultDtype, vals);
  if (permutation != null) {
    const reversePermutation = backend_util_exports.getUndoAxesPermutation(permutation);
    const reverseTransposedResult = transpose({ inputs: { x: result }, backend, attrs: { perm: reversePermutation } });
    backend.disposeIntermediateTensorInfo(result);
    backend.disposeIntermediateTensorInfo($x);
    return reverseTransposedResult;
  }
  return result;
}
var cumprodConfig = {
  kernelName: Cumprod,
  backendName: "cpu",
  kernelFunc: cumprod
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Cumsum.js
function cumsum(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, exclusive, reverse: reverse2 } = attrs;
  assertNotComplex(x, "cumsum");
  const permutation = backend_util_exports.getAxesPermutation([axis], x.shape.length);
  let $x = x;
  if (permutation != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutation } });
  }
  const permutedAxis = backend_util_exports.getInnerMostAxes(1, x.shape.length)[0];
  if (permutedAxis !== $x.shape.length - 1) {
    throw new Error(`backend.cumsum in CPU expects an inner-most axis=${$x.shape.length - 1} but got axis=${permutedAxis}`);
  }
  const resultDtype = upcastType($x.dtype, "int32");
  const vals = util_exports.makeZerosTypedArray(util_exports.sizeFromShape($x.shape), resultDtype);
  const aVals = backend.data.get($x.dataId).values;
  const finalDim = $x.shape[$x.shape.length - 1];
  const indexAdjuster = reverse2 ? (i, j) => i + finalDim - j - 1 : (i, j) => i + j;
  for (let i = 0; i < aVals.length; i += finalDim) {
    for (let j = 0; j < finalDim; j++) {
      const idx = indexAdjuster(i, j);
      if (j === 0) {
        vals[idx] = exclusive ? 0 : aVals[idx];
      } else {
        const prevIdx = indexAdjuster(i, j - 1);
        vals[idx] = exclusive ? aVals[prevIdx] + vals[prevIdx] : aVals[idx] + vals[prevIdx];
      }
    }
  }
  const result = backend.makeTensorInfo($x.shape, resultDtype, vals);
  if (permutation != null) {
    const reversePermutation = backend_util_exports.getUndoAxesPermutation(permutation);
    const reverseTransposedResult = transpose({ inputs: { x: result }, backend, attrs: { perm: reversePermutation } });
    backend.disposeIntermediateTensorInfo(result);
    backend.disposeIntermediateTensorInfo($x);
    return reverseTransposedResult;
  }
  return result;
}
var cumsumConfig = {
  kernelName: Cumsum,
  backendName: "cpu",
  kernelFunc: cumsum
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/DenseBincount.js
function denseBincount(args) {
  const { inputs, backend, attrs } = args;
  const { x, weights } = inputs;
  const { size, binaryOutput } = attrs;
  if (x.shape.length === 1) {
    const xVals = backend.data.get(x.dataId).values;
    const weightsVals = backend.data.get(weights.dataId).values;
    const outVals = bincountImpl(xVals, weightsVals, weights.dtype, weights.shape, size);
    return backend.makeTensorInfo([size], weights.dtype, outVals);
  } else if (x.shape.length === 2) {
    const xBuf = backend.bufferSync(x);
    const weightsBuf = backend.bufferSync(weights);
    const outBuf = bincountReduceImpl(xBuf, weightsBuf, size, binaryOutput);
    return backend.makeTensorInfo(outBuf.shape, weights.dtype, outBuf.values);
  }
  throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank${x.shape.length}.`);
}
var denseBincountConfig = {
  kernelName: DenseBincount,
  backendName: "cpu",
  kernelFunc: denseBincount
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/DepthToSpace.js
function depthToSpace(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { blockSize, dataFormat } = attrs;
  util_exports.assert(dataFormat === "NHWC", () => `Only NHWC dataFormat supported on CPU for depthToSpace. Got ${dataFormat}`);
  const batchSize = x.shape[0];
  const inputHeight = x.shape[1];
  const inputWidth = x.shape[2];
  const inputDepth = x.shape[3];
  const outputHeight = inputHeight * blockSize;
  const outputWidth = inputWidth * blockSize;
  const outputDepth = inputDepth / (blockSize * blockSize);
  const xValues = backend.data.get(x.dataId).values;
  const result = new Float32Array(batchSize * outputHeight * outputWidth * outputDepth);
  let outputIdx = 0;
  for (let b = 0; b < batchSize; ++b) {
    for (let h = 0; h < outputHeight; ++h) {
      const inH = Math.floor(h / blockSize);
      const offsetH = h % blockSize;
      for (let w = 0; w < outputWidth; ++w) {
        const inW = Math.floor(w / blockSize);
        const offsetW = w % blockSize;
        const offsetD = (offsetH * blockSize + offsetW) * outputDepth;
        for (let d = 0; d < outputDepth; ++d) {
          const inD = d + offsetD;
          const inputIdx = inD + inputDepth * (inW + inputWidth * (inH + inputHeight * b));
          result[outputIdx++] = xValues[inputIdx];
        }
      }
    }
  }
  return backend.makeTensorInfo([batchSize, outputHeight, outputWidth, outputDepth], x.dtype, result);
}
var depthToSpaceConfig = {
  kernelName: DepthToSpace,
  backendName: "cpu",
  kernelFunc: depthToSpace
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/DepthwiseConv2dNative.js
function depthwiseConv2dNative(args) {
  const { inputs, backend, attrs } = args;
  const { x, filter } = inputs;
  const { strides, pad, dilations, dimRoundingMode } = attrs;
  assertNotComplex([x, filter], "depthwiseConv2DNative");
  const xStrides = util_exports.computeStrides(x.shape);
  const filterStrides = util_exports.computeStrides(filter.shape);
  let $dilations = dilations;
  if ($dilations == null) {
    $dilations = [1, 1];
  }
  util_exports.assert(backend_util_exports.eitherStridesOrDilationsAreOne(strides, $dilations), () => `Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides ${strides} and dilations '${$dilations}'`);
  const convInfo = backend_util_exports.computeConv2DInfo(
    x.shape,
    filter.shape,
    strides,
    $dilations,
    pad,
    dimRoundingMode,
    true
    /* depthwise */
  );
  const { filterHeight, filterWidth, dilationHeight, dilationWidth, padInfo } = convInfo;
  const padLeft = padInfo.left;
  const padTop = padInfo.top;
  const chMul = convInfo.outChannels / convInfo.inChannels;
  const y = new TensorBuffer(convInfo.outShape, x.dtype);
  const xVals = backend.data.get(x.dataId).values;
  const wVals = backend.data.get(filter.dataId).values;
  const yVals = y.values;
  for (let b = 0; b < convInfo.batchSize; ++b) {
    const xOffset1 = b * xStrides[0];
    const yOffset1 = b * y.strides[0];
    for (let yR = 0; yR < convInfo.outHeight; ++yR) {
      const yOffset2 = yOffset1 + yR * y.strides[1];
      const xRCorner = yR * convInfo.strideHeight - padTop;
      for (let wR = 0; wR < filterHeight; ++wR) {
        const xR = xRCorner + wR * dilationHeight;
        if (xR < 0 || xR >= convInfo.inHeight) {
          continue;
        }
        const wOffset1 = wR * filterStrides[0];
        const xOffset2 = xOffset1 + xR * xStrides[1];
        for (let yC = 0; yC < convInfo.outWidth; ++yC) {
          const yOffset3 = yOffset2 + yC * y.strides[2];
          const xCCorner = yC * convInfo.strideWidth - padLeft;
          for (let wC = 0; wC < filterWidth; ++wC) {
            const xC = xCCorner + wC * dilationWidth;
            if (xC < 0 || xC >= convInfo.inWidth) {
              continue;
            }
            const wOffset2 = wOffset1 + wC * filterStrides[1];
            const xOffset3 = xOffset2 + xC * convInfo.inChannels;
            let yOffset4 = yOffset3;
            let wOffset3 = wOffset2;
            for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
              const xVal = xVals[xOffset3 + d1];
              for (let q = 0; q < chMul; ++q) {
                yVals[yOffset4 + q] += xVal * wVals[wOffset3 + q];
              }
              yOffset4 += chMul;
              wOffset3 += chMul;
            }
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(y.shape, y.dtype, y.values);
}
var depthwiseConv2dNativeConfig = {
  kernelName: DepthwiseConv2dNative,
  backendName: "cpu",
  kernelFunc: depthwiseConv2dNative
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/DepthwiseConv2dNativeBackpropFilter.js
function depthwiseConv2dNativeBackpropFilter(args) {
  const { inputs, backend, attrs } = args;
  const { x, dy } = inputs;
  const { strides, dilations, pad, dimRoundingMode, filterShape } = attrs;
  assertNotComplex([x, dy], "depthwiseConv2dNativeBackpropFilter");
  const convInfo = backend_util_exports.computeConv2DInfo(
    x.shape,
    filterShape,
    strides,
    dilations,
    pad,
    dimRoundingMode,
    true
    /* depthwise */
  );
  const { strideHeight, strideWidth, filterHeight, filterWidth } = convInfo;
  const dW = new TensorBuffer(convInfo.filterShape, "float32");
  const leftPad = convInfo.padInfo.left;
  const topPad = convInfo.padInfo.top;
  const chMul = convInfo.outChannels / convInfo.inChannels;
  const xVals = backend.data.get(x.dataId).values;
  const xBuf = new TensorBuffer(x.shape, x.dtype, xVals);
  const dyVals = backend.data.get(dy.dataId).values;
  const dyBuf = new TensorBuffer(dy.shape, dy.dtype, dyVals);
  for (let wR = 0; wR < filterHeight; ++wR) {
    const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));
    const yRMax = Math.min(convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);
    for (let wC = 0; wC < filterWidth; ++wC) {
      const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));
      const yCMax = Math.min(convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);
      for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
        const d1 = Math.trunc(d2 / chMul);
        const dm = d2 % chMul;
        let dotProd = 0;
        for (let b = 0; b < convInfo.batchSize; ++b) {
          for (let yR = yRMin; yR < yRMax; ++yR) {
            const xR = wR + yR * strideHeight - topPad;
            for (let yC = yCMin; yC < yCMax; ++yC) {
              const xC = wC + yC * strideWidth - leftPad;
              dotProd += xBuf.get(b, xR, xC, d1) * dyBuf.get(b, yR, yC, d2);
            }
          }
        }
        dW.set(dotProd, wR, wC, d1, dm);
      }
    }
  }
  return backend.makeTensorInfo(dW.shape, dW.dtype, dW.values);
}
var depthwiseConv2dNativeBackpropFilterConfig = {
  kernelName: DepthwiseConv2dNativeBackpropFilter,
  backendName: "cpu",
  kernelFunc: depthwiseConv2dNativeBackpropFilter
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/DepthwiseConv2dNativeBackpropInput.js
function depthwiseConv2dNativeBackpropInput(args) {
  const { inputs, backend, attrs } = args;
  const { dy, filter } = inputs;
  const { strides, dilations, pad, dimRoundingMode, inputShape } = attrs;
  assertNotComplex([dy, filter], "depthwiseConv2DNativeBackpropInput");
  const dyStrides = util_exports.computeStrides(dy.shape);
  const filterStrides = util_exports.computeStrides(filter.shape);
  const convInfo = backend_util_exports.computeConv2DInfo(
    inputShape,
    filter.shape,
    strides,
    dilations,
    pad,
    dimRoundingMode,
    true
    /* depthwise */
  );
  const dx = new TensorBuffer(convInfo.inShape, "float32");
  const dxValues = dx.values;
  const [dxS0, dxS1, dxS2] = dx.strides;
  const dyValues = backend.data.get(dy.dataId).values;
  const [dyS0, dyS1, dyS2] = dyStrides;
  const fltValues = backend.data.get(filter.dataId).values;
  const [fltS0, fltS1, fltS2] = filterStrides;
  const { batchSize, filterHeight, filterWidth, inChannels, inHeight, inWidth, outChannels, outHeight, outWidth, strideHeight, strideWidth } = convInfo;
  const topPad = filterHeight - 1 - convInfo.padInfo.top;
  const leftPad = filterWidth - 1 - convInfo.padInfo.left;
  const chMul = outChannels / inChannels;
  for (let b = 0; b < batchSize; ++b) {
    for (let d1 = 0; d1 < inChannels; ++d1) {
      for (let xR = 0; xR < inHeight; ++xR) {
        const xRCorner = xR - topPad;
        const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));
        const yRMax = Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);
        for (let xC = 0; xC < inWidth; ++xC) {
          const xCCorner = xC - leftPad;
          const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));
          const yCMax = Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);
          let dotProd = 0;
          for (let yR = xRMin; yR < yRMax; ++yR) {
            const wR = yR * strideHeight - xRCorner;
            for (let yC = xCMin; yC < yCMax; ++yC) {
              const wC = yC * strideWidth - xCCorner;
              const dyOffset = dyS0 * b + dyS1 * yR + dyS2 * yC;
              const fltOffset = fltS0 * (filterHeight - 1 - wR) + fltS1 * (filterWidth - 1 - wC) + fltS2 * d1;
              for (let dm = 0; dm < chMul; ++dm) {
                const d2 = d1 * chMul + dm;
                const pixel = dyValues[dyOffset + d2];
                const weight = fltValues[fltOffset + dm];
                dotProd += pixel * weight;
              }
            }
          }
          dxValues[dxS0 * b + dxS1 * xR + dxS2 * xC + d1] = dotProd;
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var depthwiseConv2dNativeBackpropInputConfig = {
  kernelName: DepthwiseConv2dNativeBackpropInput,
  backendName: "cpu",
  kernelFunc: depthwiseConv2dNativeBackpropInput
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Diag.js
function diag(args) {
  const { inputs, backend } = args;
  const { x } = inputs;
  const xSize = util_exports.sizeFromShape(x.shape);
  const xVals = backend.data.get(x.dataId).values;
  const outBuf = buffer([xSize, xSize], x.dtype);
  const vals = outBuf.values;
  for (let i = 0; i < xVals.length; i++) {
    vals[i * xSize + i] = xVals[i];
  }
  const outShape = [...x.shape, ...x.shape];
  return backend.makeTensorInfo(outShape, outBuf.dtype, outBuf.values);
}
var diagConfig = {
  kernelName: Diag,
  backendName: "cpu",
  kernelFunc: diag
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Dilation2D.js
var dilation2DConfig = {
  kernelName: Dilation2D,
  backendName: "cpu",
  kernelFunc: ({ inputs, backend, attrs }) => {
    const { x, filter } = inputs;
    const { strides, pad, dilations } = attrs;
    const cpuBackend = backend;
    const xVals = cpuBackend.data.get(x.dataId).values;
    const xRank = x.shape.length;
    const filterVals = cpuBackend.data.get(filter.dataId).values;
    const filterRank = filter.shape.length;
    const { batchSize, inHeight, inWidth, inChannels, outHeight, outWidth, padInfo, strideHeight, strideWidth, filterHeight, filterWidth, dilationHeight, dilationWidth, outShape } = backend_util_exports.computeDilation2DInfo(x.shape, filter.shape, strides, pad, "NHWC", dilations);
    const outSize = util_exports.sizeFromShape(outShape);
    const outRank = outShape.length;
    const outputVals = util_exports.getArrayFromDType(x.dtype, outSize);
    for (let b = 0; b < batchSize; ++b) {
      for (let hOut = 0; hOut < outHeight; ++hOut) {
        const hBeg = hOut * strideHeight - padInfo.top;
        for (let wOut = 0; wOut < outWidth; ++wOut) {
          const wBeg = wOut * strideWidth - padInfo.left;
          for (let d = 0; d < inChannels; ++d) {
            let curVal = Number.MIN_SAFE_INTEGER;
            for (let h = 0; h < filterHeight; ++h) {
              const hIn = hBeg + h * dilationHeight;
              if (hIn >= 0 && hIn < inHeight) {
                for (let w = 0; w < filterWidth; ++w) {
                  const wIn = wBeg + w * dilationWidth;
                  if (wIn >= 0 && wIn < inWidth) {
                    const xIndex = util_exports.locToIndex([b, hIn, wIn, d], xRank, util_exports.computeStrides(x.shape));
                    const filterIndex = util_exports.locToIndex([h, w, d], filterRank, util_exports.computeStrides(filter.shape));
                    const val = xVals[xIndex] + filterVals[filterIndex];
                    if (val > curVal) {
                      curVal = val;
                    }
                  }
                }
              }
            }
            const outputIndex = util_exports.locToIndex([b, hOut, wOut, d], outRank, util_exports.computeStrides(outShape));
            outputVals[outputIndex] = curVal;
          }
        }
      }
    }
    const dataId = cpuBackend.write(util_exports.toTypedArray(outputVals, x.dtype), outShape, x.dtype);
    return { dataId, shape: outShape, dtype: x.dtype };
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Dilation2DBackpropFilter.js
var dilation2DBackpropFilterConfig = {
  kernelName: Dilation2DBackpropFilter,
  backendName: "cpu",
  kernelFunc: ({ inputs, backend, attrs }) => {
    const { x, filter, dy } = inputs;
    const { strides, pad, dilations } = attrs;
    const cpuBackend = backend;
    const $x = util_exports.toNestedArray(x.shape, cpuBackend.data.get(x.dataId).values);
    const $filter = util_exports.toNestedArray(filter.shape, cpuBackend.data.get(filter.dataId).values);
    const { batchSize, inHeight, inWidth, inChannels, outHeight, outWidth, padInfo, strideHeight, strideWidth, filterHeight, filterWidth, dilationHeight, dilationWidth, outShape } = backend_util_exports.computeDilation2DInfo(x.shape, filter.shape, strides, pad, "NHWC", dilations);
    util_exports.assert(dy.rank === outShape.length, () => `Error in ${Dilation2DBackpropFilter}, dy must have the same rank as output ${outShape.length}, but got ${dy.rank}`);
    const $dy = util_exports.toNestedArray(outShape, cpuBackend.data.get(dy.dataId).values);
    const gradients = util_exports.makeZerosNestedTypedArray(filter.shape, filter.dtype);
    for (let b = 0; b < batchSize; ++b) {
      for (let hOut = 0; hOut < outHeight; ++hOut) {
        const hBeg = hOut * strideHeight - padInfo.top;
        for (let wOut = 0; wOut < outWidth; ++wOut) {
          const wBeg = wOut * strideWidth - padInfo.left;
          for (let d = 0; d < inChannels; ++d) {
            let curVal = Number.MIN_SAFE_INTEGER;
            let hMax = 0;
            let wMax = 0;
            for (let h = 0; h < filterHeight; ++h) {
              const hIn = hBeg + h * dilationHeight;
              if (hIn >= 0 && hIn < inHeight) {
                for (let w = 0; w < filterWidth; ++w) {
                  const wIn = wBeg + w * dilationWidth;
                  if (wIn >= 0 && wIn < inWidth) {
                    const val = $x[b][hIn][wIn][d] + $filter[h][w][d];
                    if (val > curVal) {
                      curVal = val;
                      hMax = h;
                      wMax = w;
                    }
                  }
                }
              }
            }
            gradients[hMax][wMax][d] += $dy[b][hOut][wOut][d];
          }
        }
      }
    }
    const dataId = cpuBackend.write(util_exports.toTypedArray(gradients, x.dtype), filter.shape, filter.dtype);
    return { dataId, shape: filter.shape, dtype: filter.dtype };
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Dilation2DBackpropInput.js
var dilation2DBackpropInputConfig = {
  kernelName: Dilation2DBackpropInput,
  backendName: "cpu",
  kernelFunc: ({ inputs, backend, attrs }) => {
    const { x, filter, dy } = inputs;
    const { strides, pad, dilations } = attrs;
    const cpuBackend = backend;
    const $x = util_exports.toNestedArray(x.shape, cpuBackend.data.get(x.dataId).values);
    const $filter = util_exports.toNestedArray(filter.shape, cpuBackend.data.get(filter.dataId).values);
    const { batchSize, inHeight, inWidth, inChannels, outHeight, outWidth, padInfo, strideHeight, strideWidth, filterHeight, filterWidth, dilationHeight, dilationWidth, outShape } = backend_util_exports.computeDilation2DInfo(x.shape, filter.shape, strides, pad, "NHWC", dilations);
    util_exports.assert(dy.rank === outShape.length, () => `Error in ${Dilation2DBackpropInput}, dy must have the same rank as output ${outShape.length}, but got ${dy.rank}`);
    const $dy = util_exports.toNestedArray(outShape, cpuBackend.data.get(dy.dataId).values);
    const gradients = util_exports.makeZerosNestedTypedArray(x.shape, x.dtype);
    for (let b = 0; b < batchSize; ++b) {
      for (let hOut = 0; hOut < outHeight; ++hOut) {
        const hBeg = hOut * strideHeight - padInfo.top;
        for (let wOut = 0; wOut < outWidth; ++wOut) {
          const wBeg = wOut * strideWidth - padInfo.left;
          for (let d = 0; d < inChannels; ++d) {
            let curVal = Number.MIN_SAFE_INTEGER;
            let hInMax = hBeg < 0 ? 0 : hBeg;
            let wInMax = wBeg < 0 ? 0 : wBeg;
            for (let h = 0; h < filterHeight; ++h) {
              const hIn = hBeg + h * dilationHeight;
              if (hIn >= 0 && hIn < inHeight) {
                for (let w = 0; w < filterWidth; ++w) {
                  const wIn = wBeg + w * dilationWidth;
                  if (wIn >= 0 && wIn < inWidth) {
                    const val = $x[b][hIn][wIn][d] + $filter[h][w][d];
                    if (val > curVal) {
                      curVal = val;
                      hInMax = hIn;
                      wInMax = wIn;
                    }
                  }
                }
              }
            }
            gradients[b][hInMax][wInMax][d] += $dy[b][hOut][wOut][d];
          }
        }
      }
    }
    const dataId = cpuBackend.write(util_exports.toTypedArray(gradients, x.dtype), x.shape, x.dtype);
    return { dataId, shape: x.shape, dtype: x.dtype };
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Draw.js
function draw(args) {
  const { inputs, backend, attrs } = args;
  const { image } = inputs;
  const { canvas, options } = attrs;
  const { contextOptions, imageOptions } = options || {};
  const alpha = (imageOptions === null || imageOptions === void 0 ? void 0 : imageOptions.alpha) || 1;
  const contextType = (contextOptions === null || contextOptions === void 0 ? void 0 : contextOptions.contextType) || "2d";
  if (contextType !== "2d") {
    throw new Error(`Context type ${contextOptions.contextType} is not supported by the CPU backend.`);
  }
  const ctx = canvas.getContext(contextType, (contextOptions === null || contextOptions === void 0 ? void 0 : contextOptions.contextAttributes) || {});
  if (ctx == null) {
    throw new Error(`Could not get the context with ${contextType} type.`);
  }
  const [height, width] = image.shape.slice(0, 2);
  const depth = image.shape.length === 2 ? 1 : image.shape[2];
  const data = backend.data.get(image.dataId).values;
  const multiplier = image.dtype === "float32" ? 255 : 1;
  const bytes = new Uint8ClampedArray(width * height * 4);
  for (let i = 0; i < height * width; ++i) {
    const rgba = [0, 0, 0, 255 * alpha];
    for (let d = 0; d < depth; d++) {
      const value = data[i * depth + d];
      if (image.dtype === "float32") {
        if (value < 0 || value > 1) {
          throw new Error(`Tensor values for a float32 Tensor must be in the range [0 - 1] but encountered ${value}.`);
        }
      } else if (image.dtype === "int32") {
        if (value < 0 || value > 255) {
          throw new Error(`Tensor values for a int32 Tensor must be in the range [0 - 255] but encountered ${value}.`);
        }
      }
      if (depth === 1) {
        rgba[0] = value * multiplier;
        rgba[1] = value * multiplier;
        rgba[2] = value * multiplier;
      } else {
        rgba[d] = value * multiplier;
      }
    }
    const j = i * 4;
    bytes[j + 0] = Math.round(rgba[0]);
    bytes[j + 1] = Math.round(rgba[1]);
    bytes[j + 2] = Math.round(rgba[2]);
    bytes[j + 3] = Math.round(rgba[3]);
  }
  canvas.width = width;
  canvas.height = height;
  const imageData = new ImageData(bytes, width, height);
  ctx.putImageData(imageData, 0, 0);
  return image;
}
var drawConfig = {
  kernelName: Draw,
  backendName: "cpu",
  kernelFunc: draw
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Sum.js
function sum(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, keepDims } = attrs;
  assertNotComplex(x, "sum");
  let $x;
  if (x.dtype === "bool") {
    $x = cast({ inputs: { x }, backend, attrs: { dtype: "int32" } });
  } else {
    $x = identity({ inputs: { x }, backend });
  }
  const xRank = $x.shape.length;
  const axes = util_exports.parseAxisParam(axis, $x.shape);
  const permutation = backend_util_exports.getAxesPermutation(axes, xRank);
  let reductionAxes = axes;
  let permutedX = $x;
  if (permutation != null) {
    permutedX = transpose({ inputs: { x: $x }, backend, attrs: { perm: permutation } });
    reductionAxes = backend_util_exports.getInnerMostAxes(reductionAxes.length, xRank);
  }
  backend_util_exports.assertAxesAreInnerMostDims("sum", reductionAxes, permutedX.shape.length);
  const [outShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes(permutedX.shape, reductionAxes);
  const resultDtype = backend_util_exports.upcastType(permutedX.dtype, "int32");
  let result = zeros(backend, outShape, resultDtype);
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const vals = backend.data.get(result.dataId).values;
  const aVals = backend.data.get(permutedX.dataId).values;
  for (let i = 0; i < vals.length; ++i) {
    const offset = i * reduceSize;
    let sum2 = 0;
    for (let j = 0; j < reduceSize; ++j) {
      sum2 += aVals[offset + j];
    }
    vals[i] = sum2;
  }
  if (keepDims) {
    const newShape = backend_util_exports.expandShapeToKeepDim(result.shape, axes);
    const oldResult = result;
    result = reshape({ inputs: { x: result }, backend, attrs: { shape: newShape } });
    backend.disposeIntermediateTensorInfo(oldResult);
  }
  backend.disposeIntermediateTensorInfo($x);
  if (permutation != null) {
    backend.disposeIntermediateTensorInfo(permutedX);
  }
  return result;
}
var sumConfig = {
  kernelName: Sum,
  backendName: "cpu",
  kernelFunc: sum
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Einsum.js
function einsum(args) {
  const { inputs, backend, attrs } = args;
  const { equation } = attrs;
  const tensors = inputs;
  const { allDims, summedDims, idDims } = backend_util_exports.decodeEinsumEquation(equation, tensors.length);
  backend_util_exports.checkEinsumDimSizes(allDims.length, idDims, tensors);
  const { path, steps } = backend_util_exports.getEinsumComputePath(summedDims, idDims);
  const nSteps = steps.length;
  let out = null;
  let numDimsRemaining = allDims.length;
  const tensorsToDispose = [];
  for (let i = 0; i < nSteps; ++i) {
    for (const idTerm of steps[i]) {
      const { permutationIndices: perm, expandDims: dimsToExpand } = backend_util_exports.getEinsumPermutation(numDimsRemaining, idDims[idTerm]);
      let x;
      if (backend_util_exports.isIdentityPermutation(perm)) {
        x = tensors[idTerm];
      } else {
        x = transpose({ inputs: { x: tensors[idTerm] }, backend, attrs: { perm } });
        tensorsToDispose.push(x);
      }
      const targetShape = x.shape.slice();
      for (let k = 0; k < dimsToExpand.length; ++k) {
        targetShape.splice(dimsToExpand[k], 0, 1);
      }
      if (!util_exports.arraysEqual(x.shape, targetShape)) {
        x = reshape({ inputs: { x }, backend, attrs: { shape: targetShape } });
        tensorsToDispose.push(x);
      }
      if (out === null) {
        out = x;
      } else {
        out = multiply({ inputs: { a: x, b: out }, backend });
        tensorsToDispose.push(out);
      }
    }
    if (i < nSteps - 1) {
      if (path[i] >= 0) {
        out = sum({
          inputs: { x: out },
          backend,
          attrs: {
            axis: path[i] - (allDims.length - numDimsRemaining),
            keepDims: false
          }
        });
        tensorsToDispose.push(out);
      }
      numDimsRemaining--;
    }
  }
  for (const tensorInfo of tensorsToDispose) {
    if (tensorInfo === out) {
      continue;
    }
    backend.disposeIntermediateTensorInfo(tensorInfo);
  }
  return out;
}
var einsumConfig = {
  kernelName: Einsum,
  backendName: "cpu",
  kernelFunc: einsum
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/EluGrad.js
function eluGrad(args) {
  const { inputs, backend } = args;
  const { dy, y } = inputs;
  assertNotComplex([dy, y], "eluGrad");
  const resultValues = new Float32Array(util_exports.sizeFromShape(y.shape));
  const values = backend.data.get(y.dataId).values;
  const dyValues = backend.data.get(dy.dataId).values;
  for (let i = 0; i < values.length; ++i) {
    const v = values[i];
    if (v >= 0) {
      resultValues[i] = dyValues[i];
    } else {
      resultValues[i] = dyValues[i] * (v + 1);
    }
  }
  return backend.makeTensorInfo(y.shape, "float32", resultValues);
}
var eluGradConfig = {
  kernelName: EluGrad,
  backendName: "cpu",
  kernelFunc: eluGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Erf.js
var p = backend_util_exports.ERF_P;
var a1 = backend_util_exports.ERF_A1;
var a2 = backend_util_exports.ERF_A2;
var a3 = backend_util_exports.ERF_A3;
var a4 = backend_util_exports.ERF_A4;
var a5 = backend_util_exports.ERF_A5;
var erf = unaryKernelFunc(Erf, (xi) => {
  const sign2 = Math.sign(xi);
  const v = Math.abs(xi);
  const t = 1 / (1 + p * v);
  return sign2 * (1 - ((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-v * v));
});
var erfConfig = {
  kernelName: Erf,
  backendName: "cpu",
  kernelFunc: erf
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ExpandDims.js
function expandDims(args) {
  const { inputs, backend, attrs } = args;
  const { input } = inputs;
  const { dim } = attrs;
  const inputRank = input.shape.length;
  const newShape = input.shape.slice();
  let $dim = dim;
  if (dim < 0) {
    util_exports.assert(-(inputRank + 1) <= dim, () => `Axis must be in the interval [${-(inputRank + 1)}, ${inputRank}]`);
    $dim = inputRank + dim + 1;
  }
  newShape.splice($dim, 0, 1);
  return reshape({ inputs: { x: input }, backend, attrs: { shape: newShape } });
}
var expandDimsConfig = {
  kernelName: ExpandDims,
  backendName: "cpu",
  kernelFunc: expandDims
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/RealDiv.js
var realDivImpl = createSimpleBinaryKernelImpl((a, b) => a / b);
var div = binaryKernelFunc(RealDiv, realDivImpl);
var realDivConfig = {
  kernelName: RealDiv,
  backendName: "cpu",
  kernelFunc: div
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/utils/fft_utils.js
function fftBatch(input, inverse, cpuBackend) {
  const inputShape = input.shape;
  const batch = inputShape[0];
  const innerDim = inputShape[1];
  const inputVals = cpuBackend.data.get(input.dataId);
  const real2D = inputVals.complexTensorInfos.real;
  const imag2D = inputVals.complexTensorInfos.imag;
  const resultShape = [batch, innerDim];
  const resultSize = util_exports.sizeFromShape(resultShape);
  const resultReal = util_exports.getTypedArrayFromDType("float32", resultSize);
  const resultImag = util_exports.getTypedArrayFromDType("float32", resultSize);
  for (let b = 0; b < batch; b++) {
    const r = slice({
      inputs: { x: real2D },
      backend: cpuBackend,
      attrs: { begin: [b, 0], size: [1, innerDim] }
    });
    const i = slice({
      inputs: { x: imag2D },
      backend: cpuBackend,
      attrs: { begin: [b, 0], size: [1, innerDim] }
    });
    const input2 = complex({ inputs: { real: r, imag: i }, backend: cpuBackend });
    const { real: real2, imag: imag2 } = fftImpl(input2, inverse, cpuBackend);
    const res = backend_util_exports.mergeRealAndImagArrays(real2, imag2);
    for (let d = 0; d < innerDim; d++) {
      const c = backend_util_exports.getComplexWithIndex(res, d);
      resultReal[b * innerDim + d] = c.real;
      resultImag[b * innerDim + d] = c.imag;
    }
    cpuBackend.disposeIntermediateTensorInfo(r);
    cpuBackend.disposeIntermediateTensorInfo(i);
    cpuBackend.disposeIntermediateTensorInfo(input2);
  }
  const $realInfo = cpuBackend.makeTensorInfo(resultShape, "float32", resultReal);
  const $imagInfo = cpuBackend.makeTensorInfo(resultShape, "float32", resultImag);
  const result = complex({ inputs: { real: $realInfo, imag: $imagInfo }, backend: cpuBackend });
  cpuBackend.disposeIntermediateTensorInfo($realInfo);
  cpuBackend.disposeIntermediateTensorInfo($imagInfo);
  return result;
}
function fftImpl(input, inverse, cpuBackend) {
  const inputSize = util_exports.sizeFromShape(input.shape);
  const inputVals = cpuBackend.data.get(input.dataId);
  const realVals = cpuBackend.data.get(inputVals.complexTensorInfos.real.dataId).values;
  const imagVals = cpuBackend.data.get(inputVals.complexTensorInfos.imag.dataId).values;
  if (isExponentOf2(inputSize)) {
    const result = fftRadix2(realVals, imagVals, inputSize, inverse, cpuBackend);
    const resultShape = [input.shape[0], input.shape[1]];
    if (inverse) {
      const realInfo = cpuBackend.makeTensorInfo(resultShape, "float32", result.real);
      const imagInfo = cpuBackend.makeTensorInfo(resultShape, "float32", result.imag);
      const sizeInfo = cpuBackend.makeTensorInfo([], "float32", util_exports.createScalarValue(inputSize, "float32"));
      const sizeInfoCopy = identity({ inputs: { x: sizeInfo }, backend: cpuBackend });
      const divRealInfo = realDivConfig.kernelFunc({ inputs: { a: realInfo, b: sizeInfo }, backend: cpuBackend });
      const divImagInfo = realDivConfig.kernelFunc({ inputs: { a: imagInfo, b: sizeInfoCopy }, backend: cpuBackend });
      const divRealVals = cpuBackend.data.get(divRealInfo.dataId).values;
      const divImagVals = cpuBackend.data.get(divImagInfo.dataId).values;
      cpuBackend.disposeIntermediateTensorInfo(realInfo);
      cpuBackend.disposeIntermediateTensorInfo(imagInfo);
      cpuBackend.disposeIntermediateTensorInfo(sizeInfo);
      cpuBackend.disposeIntermediateTensorInfo(sizeInfoCopy);
      cpuBackend.disposeIntermediateTensorInfo(divRealInfo);
      cpuBackend.disposeIntermediateTensorInfo(divImagInfo);
      return { real: divRealVals, imag: divImagVals };
    }
    return result;
  } else {
    const data = backend_util_exports.mergeRealAndImagArrays(realVals, imagVals);
    const rawOutput = fourierTransformByMatmul(data, inputSize, inverse);
    return backend_util_exports.splitRealAndImagArrays(rawOutput);
  }
}
function isExponentOf2(size) {
  return (size & size - 1) === 0;
}
function fftRadix2(realVals, imagVals, size, inverse, cpuBackend) {
  if (size === 1) {
    return { real: realVals, imag: imagVals };
  }
  const data = backend_util_exports.mergeRealAndImagArrays(realVals, imagVals);
  const half = size / 2;
  const evenComplex = backend_util_exports.complexWithEvenIndex(data);
  const evenRealVals = evenComplex.real;
  const evenImagVals = evenComplex.imag;
  const evenShape = [evenRealVals.length];
  const evenRealInfo = cpuBackend.makeTensorInfo(evenShape, "float32", evenRealVals);
  const evenImagInfo = cpuBackend.makeTensorInfo(evenShape, "float32", evenImagVals);
  const evenTensorInfo = complex({ inputs: { real: evenRealInfo, imag: evenImagInfo }, backend: cpuBackend });
  const oddComplex = backend_util_exports.complexWithOddIndex(data);
  const oddRealVals = oddComplex.real;
  const oddImagVals = oddComplex.imag;
  const oddShape = [oddRealVals.length];
  const oddRealInfo = cpuBackend.makeTensorInfo(oddShape, "float32", oddRealVals);
  const oddImagInfo = cpuBackend.makeTensorInfo(oddShape, "float32", oddImagVals);
  const oddTensorInfo = complex({ inputs: { real: oddRealInfo, imag: oddImagInfo }, backend: cpuBackend });
  const $evenComplex = fftRadix2(evenRealVals, evenImagVals, half, inverse, cpuBackend);
  const $evenRealVals = $evenComplex.real;
  const $evenImagVals = $evenComplex.imag;
  const $evenShape = [$evenRealVals.length];
  const $evenRealInfo = cpuBackend.makeTensorInfo($evenShape, "float32", $evenRealVals);
  const $evenImagInfo = cpuBackend.makeTensorInfo($evenShape, "float32", $evenImagVals);
  const $evenTensorInfo = complex({
    inputs: { real: $evenRealInfo, imag: $evenImagInfo },
    backend: cpuBackend
  });
  const $oddComplex = fftRadix2(oddRealVals, oddImagVals, half, inverse, cpuBackend);
  const $oddRealVals = $oddComplex.real;
  const $oddImagVals = $oddComplex.imag;
  const $oddShape = [$oddRealVals.length];
  const $oddRealInfo = cpuBackend.makeTensorInfo($oddShape, "float32", $oddRealVals);
  const $oddImagInfo = cpuBackend.makeTensorInfo($oddShape, "float32", $oddImagVals);
  const $oddTensorInfo = complex({ inputs: { real: $oddRealInfo, imag: $oddImagInfo }, backend: cpuBackend });
  const e = backend_util_exports.exponents(size, inverse);
  const eShape = [e.real.length];
  const eRealInfo = cpuBackend.makeTensorInfo(eShape, "float32", e.real);
  const eImagInfo = cpuBackend.makeTensorInfo(eShape, "float32", e.imag);
  const complexInfo = complex({ inputs: { real: eRealInfo, imag: eImagInfo }, backend: cpuBackend });
  const exponentInfo = multiply({ inputs: { a: complexInfo, b: $oddTensorInfo }, backend: cpuBackend });
  const addPart = add({
    inputs: { a: $evenTensorInfo, b: exponentInfo },
    backend: cpuBackend
  });
  const subPart = sub({
    inputs: { a: $evenTensorInfo, b: exponentInfo },
    backend: cpuBackend
  });
  const addPartReal = real({ inputs: { input: addPart }, backend: cpuBackend });
  const subPartReal = real({ inputs: { input: subPart }, backend: cpuBackend });
  const addPartImag = imag({ inputs: { input: addPart }, backend: cpuBackend });
  const subPartImag = imag({ inputs: { input: subPart }, backend: cpuBackend });
  const $real = concat({
    inputs: [addPartReal, subPartReal],
    backend: cpuBackend,
    attrs: { axis: 0 }
  });
  const $imag = concat({
    inputs: [addPartImag, subPartImag],
    backend: cpuBackend,
    attrs: { axis: 0 }
  });
  const $realVals = cpuBackend.data.get($real.dataId).values;
  const $imagVals = cpuBackend.data.get($imag.dataId).values;
  cpuBackend.disposeIntermediateTensorInfo(evenRealInfo);
  cpuBackend.disposeIntermediateTensorInfo(evenImagInfo);
  cpuBackend.disposeIntermediateTensorInfo(evenTensorInfo);
  cpuBackend.disposeIntermediateTensorInfo(oddRealInfo);
  cpuBackend.disposeIntermediateTensorInfo(oddImagInfo);
  cpuBackend.disposeIntermediateTensorInfo(oddTensorInfo);
  cpuBackend.disposeIntermediateTensorInfo($evenRealInfo);
  cpuBackend.disposeIntermediateTensorInfo($evenImagInfo);
  cpuBackend.disposeIntermediateTensorInfo($evenTensorInfo);
  cpuBackend.disposeIntermediateTensorInfo($oddRealInfo);
  cpuBackend.disposeIntermediateTensorInfo($oddImagInfo);
  cpuBackend.disposeIntermediateTensorInfo($oddTensorInfo);
  cpuBackend.disposeIntermediateTensorInfo(eRealInfo);
  cpuBackend.disposeIntermediateTensorInfo(eImagInfo);
  cpuBackend.disposeIntermediateTensorInfo(complexInfo);
  cpuBackend.disposeIntermediateTensorInfo(exponentInfo);
  cpuBackend.disposeIntermediateTensorInfo(addPart);
  cpuBackend.disposeIntermediateTensorInfo(subPart);
  cpuBackend.disposeIntermediateTensorInfo(addPartReal);
  cpuBackend.disposeIntermediateTensorInfo(addPartImag);
  cpuBackend.disposeIntermediateTensorInfo(subPartReal);
  cpuBackend.disposeIntermediateTensorInfo(subPartImag);
  cpuBackend.disposeIntermediateTensorInfo($real);
  cpuBackend.disposeIntermediateTensorInfo($imag);
  return { real: $realVals, imag: $imagVals };
}
function fourierTransformByMatmul(data, size, inverse) {
  const ret = new Float32Array(size * 2);
  for (let r = 0; r < size; r++) {
    let real2 = 0;
    let imag2 = 0;
    for (let c = 0; c < size; c++) {
      const e = backend_util_exports.exponent(r * c, size, inverse);
      const term = backend_util_exports.getComplexWithIndex(data, c);
      real2 += term.real * e.real - term.imag * e.imag;
      imag2 += term.real * e.imag + term.imag * e.real;
    }
    if (inverse) {
      real2 /= size;
      imag2 /= size;
    }
    backend_util_exports.assignToTypedArray(ret, real2, imag2, r);
  }
  return ret;
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/FFT.js
function fft(args) {
  const { inputs, backend } = args;
  const { input } = inputs;
  const inputSize = util_exports.sizeFromShape(input.shape);
  const innerDimensionSize = input.shape[input.shape.length - 1];
  const batch = inputSize / innerDimensionSize;
  const input2D = reshape({
    inputs: { x: input },
    backend,
    attrs: { shape: [batch, innerDimensionSize] }
  });
  const result = fftBatch(input2D, false, backend);
  const resultReshaped = reshape({ inputs: { x: result }, backend, attrs: { shape: input.shape } });
  backend.disposeIntermediateTensorInfo(input2D);
  backend.disposeIntermediateTensorInfo(result);
  return resultReshaped;
}
var fftConfig = {
  kernelName: FFT,
  backendName: "cpu",
  kernelFunc: fft
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Fill.js
function fill(args) {
  const { backend, attrs } = args;
  const { shape, value, dtype } = attrs;
  const $dtype = dtype || util_exports.inferDtype(value);
  const values = util_exports.getArrayFromDType($dtype, util_exports.sizeFromShape(shape));
  fillValues(values, value, $dtype);
  return backend.makeTensorInfo(shape, $dtype, values);
}
var fillConfig = {
  kernelName: Fill,
  backendName: "cpu",
  kernelFunc: fill
};
function fillValues(values, value, dtype) {
  if (dtype === "string") {
    values.fill(value);
  } else {
    values.fill(value);
  }
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/FlipLeftRight.js
var flipLeftRightConfig = {
  kernelName: FlipLeftRight,
  backendName: "cpu",
  kernelFunc: ({ inputs, attrs, backend }) => {
    const { image } = inputs;
    const cpuBackend = backend;
    const output = util_exports.getTypedArrayFromDType(image.dtype, util_exports.sizeFromShape(image.shape));
    const [batch, imageHeight, imageWidth, numChannels] = image.shape;
    const imageVals = cpuBackend.data.get(image.dataId).values;
    for (let batchIdx = 0; batchIdx < batch; batchIdx++) {
      const batchOffset = batchIdx * imageWidth * imageHeight * numChannels;
      for (let row = 0; row < imageHeight; row++) {
        const rowOffset = row * (imageWidth * numChannels);
        for (let col = 0; col < imageWidth; col++) {
          const colOffset = col * numChannels;
          for (let channel = 0; channel < numChannels; channel++) {
            const coordX = Math.round(imageWidth - col - 1);
            const outIdx = batchOffset + rowOffset + colOffset + channel;
            let outputValue = imageVals[outIdx];
            if (coordX >= 0 && coordX < imageWidth) {
              const rotatedColOffset = coordX * numChannels;
              const imageIdx = batchOffset + rowOffset + rotatedColOffset + channel;
              outputValue = imageVals[imageIdx];
            }
            output[outIdx] = outputValue;
          }
        }
      }
    }
    const dataId = cpuBackend.write(output, image.shape, image.dtype);
    return { dataId, shape: image.shape, dtype: image.dtype };
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/FusedConv2D.js
function fusedConv2D(args) {
  const { inputs, backend, attrs } = args;
  const { x, filter, bias, preluActivationWeights } = inputs;
  const { strides, pad, dataFormat, dilations, dimRoundingMode, activation, leakyreluAlpha } = attrs;
  let result = conv2D({
    inputs: { x, filter },
    backend,
    attrs: { strides, pad, dataFormat, dilations, dimRoundingMode }
  });
  if (bias) {
    const resultOld = result;
    if (dataFormat === "NCHW" && bias.shape.length === 1 && bias.shape[0] !== 1) {
      const reshapedBias = reshape({ inputs: { x: bias }, backend, attrs: { shape: [bias.shape[0], 1, 1] } });
      result = add({ inputs: { a: result, b: reshapedBias }, backend });
      backend.disposeIntermediateTensorInfo(reshapedBias);
    } else {
      result = add({ inputs: { a: result, b: bias }, backend });
    }
    backend.disposeIntermediateTensorInfo(resultOld);
  }
  if (activation) {
    const resultOld = result;
    if (dataFormat === "NCHW" && activation === "prelu" && preluActivationWeights.shape.length === 1 && preluActivationWeights.shape[0] !== 1) {
      const reshapedAlpha = reshape({
        inputs: { x: preluActivationWeights },
        backend,
        attrs: { shape: [preluActivationWeights.shape[0], 1, 1] }
      });
      result = applyActivation(backend, result, activation, reshapedAlpha, leakyreluAlpha);
      backend.disposeIntermediateTensorInfo(reshapedAlpha);
    } else {
      result = applyActivation(backend, result, activation, preluActivationWeights, leakyreluAlpha);
    }
    backend.disposeIntermediateTensorInfo(resultOld);
  }
  return result;
}
var fusedConv2DConfig = {
  kernelName: FusedConv2D,
  backendName: "cpu",
  kernelFunc: fusedConv2D
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/FusedDepthwiseConv2D.js
function fusedDepthwiseConv2D(args) {
  const { inputs, backend, attrs } = args;
  const { x, filter, bias, preluActivationWeights } = inputs;
  const { strides, pad, dataFormat, dilations, dimRoundingMode, activation, leakyreluAlpha } = attrs;
  let result = depthwiseConv2dNative({
    inputs: { x, filter },
    backend,
    attrs: { strides, pad, dataFormat, dilations, dimRoundingMode }
  });
  if (bias) {
    const oldResult = result;
    result = add({ inputs: { a: result, b: bias }, backend });
    backend.disposeIntermediateTensorInfo(oldResult);
  }
  if (activation) {
    const oldResult = result;
    result = applyActivation(backend, result, activation, preluActivationWeights, leakyreluAlpha);
    backend.disposeIntermediateTensorInfo(oldResult);
  }
  return result;
}
var fusedDepthwiseConv2DConfig = {
  kernelName: FusedDepthwiseConv2D,
  backendName: "cpu",
  kernelFunc: fusedDepthwiseConv2D
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/GatherNd.js
function gatherNd(args) {
  const { inputs, backend } = args;
  const { params, indices } = inputs;
  const paramsSize = util_exports.sizeFromShape(params.shape);
  const indicesShape = indices.shape;
  const sliceRank = indicesShape[indicesShape.length - 1];
  const [resultShape, numSlices, sliceSize, strides] = backend_util_exports.prepareAndValidate(params, indices);
  if (numSlices === 0) {
    return backend.makeTensorInfo(resultShape, params.dtype, []);
  }
  const indicesData = backend.data.get(indices.dataId).values;
  const paramsBuf = backend.bufferSync(params);
  const outBuf = gatherNdImpl(indicesData, paramsBuf, params.dtype, numSlices, sliceRank, sliceSize, strides, params.shape, paramsSize);
  return backend.makeTensorInfo(resultShape, params.dtype, outBuf.values);
}
var gatherNdConfig = {
  kernelName: GatherNd,
  backendName: "cpu",
  kernelFunc: gatherNd
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/GatherV2.js
function gatherV2(args) {
  const { inputs, backend, attrs } = args;
  const { x, indices } = inputs;
  const { axis, batchDims } = attrs;
  assertNotComplex([x, indices], "gatherV2");
  const parsedAxis = util_exports.parseAxisParam(axis, x.shape)[0];
  const indicesVals = backend.data.get(indices.dataId).values;
  const axisDim = x.shape[parsedAxis];
  for (let i = 0; i < indicesVals.length; ++i) {
    const index = indicesVals[i];
    util_exports.assert(index <= axisDim - 1 && index >= 0, () => `GatherV2: the index value ${index} is not in [0, ${axisDim - 1}]`);
  }
  let $batchDims = batchDims;
  if (batchDims == null) {
    $batchDims = 0;
  }
  const indicesSize = util_exports.sizeFromShape(indices.shape);
  const shapeInfo = backend_util_exports.segment_util.collectGatherOpShapeInfo(x, indices, parsedAxis, $batchDims);
  const flattenX = reshape({
    inputs: { x },
    backend,
    attrs: {
      shape: [
        shapeInfo.batchSize,
        shapeInfo.outerSize,
        shapeInfo.dimSize,
        shapeInfo.sliceSize
      ]
    }
  });
  const flattenIndex = reshape({
    inputs: { x: indices },
    backend,
    attrs: { shape: [shapeInfo.batchSize, indicesSize / shapeInfo.batchSize] }
  });
  const flattenOutputShape = [
    shapeInfo.batchSize,
    shapeInfo.outerSize,
    indicesSize / shapeInfo.batchSize,
    shapeInfo.sliceSize
  ];
  const indicesBuf = backend.bufferSync(flattenIndex);
  const xBuf = backend.bufferSync(flattenX);
  const outBuf = gatherV2Impl(xBuf, indicesBuf, flattenOutputShape);
  backend.disposeIntermediateTensorInfo(flattenX);
  backend.disposeIntermediateTensorInfo(flattenIndex);
  return backend.makeTensorInfo(shapeInfo.outputShape, outBuf.dtype, outBuf.values);
}
var gatherV2Config = {
  kernelName: GatherV2,
  backendName: "cpu",
  kernelFunc: gatherV2
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/IFFT.js
function ifft(args) {
  const { inputs, backend } = args;
  const { input } = inputs;
  const inputSize = util_exports.sizeFromShape(input.shape);
  const innerDimensionSize = input.shape[input.shape.length - 1];
  const batch = inputSize / innerDimensionSize;
  const input2D = reshape({
    inputs: { x: input },
    backend,
    attrs: { shape: [batch, innerDimensionSize] }
  });
  const result = fftBatch(input2D, true, backend);
  const resultReshaped = reshape({ inputs: { x: result }, backend, attrs: { shape: input.shape } });
  backend.disposeIntermediateTensorInfo(input2D);
  backend.disposeIntermediateTensorInfo(result);
  return resultReshaped;
}
var ifftConfig = {
  kernelName: IFFT,
  backendName: "cpu",
  kernelFunc: ifft
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/IsFinite.js
var isFinite = unaryKernelFunc(IsFinite, (xi) => Number.isFinite(xi) ? 1 : 0, "bool");
var isFiniteConfig = {
  kernelName: IsFinite,
  backendName: "cpu",
  kernelFunc: isFinite
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/IsInf.js
var isInf = unaryKernelFunc(IsInf, (xi) => Math.abs(xi) === Infinity ? 1 : 0, "bool");
var isInfConfig = {
  kernelName: IsInf,
  backendName: "cpu",
  kernelFunc: isInf
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/IsNaN.js
var isNaN2 = unaryKernelFunc(IsNan, (xi) => Number.isNaN(xi) ? 1 : 0, "bool");
var isNaNConfig = {
  kernelName: IsNan,
  backendName: "cpu",
  kernelFunc: isNaN2
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LinSpace.js
function linSpace(args) {
  const { backend, attrs } = args;
  const { start, stop, num } = attrs;
  const outVals = linSpaceImpl(start, stop, num);
  return backend.makeTensorInfo([outVals.length], "float32", outVals);
}
var linSpaceConfig = {
  kernelName: LinSpace,
  backendName: "cpu",
  kernelFunc: linSpace
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Log1p.js
var log1p = unaryKernelFunc(Log1p, (xi) => Math.log1p(xi));
var log1pConfig = {
  kernelName: Log1p,
  backendName: "cpu",
  kernelFunc: log1p
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LogicalAnd.js
var logicalAndImpl = createSimpleBinaryKernelImpl((a, b) => a && b);
var logicalAnd = binaryKernelFunc(LogicalAnd, logicalAndImpl, null, "bool");
var logicalAndConfig = {
  kernelName: LogicalAnd,
  backendName: "cpu",
  kernelFunc: logicalAnd
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LogicalNot.js
var logicalNot = unaryKernelFunc(LogicalNot, (xi) => xi ? 0 : 1, "bool");
var logicalNotConfig = {
  kernelName: LogicalNot,
  backendName: "cpu",
  kernelFunc: logicalNot
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LogicalOr.js
var logicalOrImpl = createSimpleBinaryKernelImpl((a, b) => a || b);
var logicalOr = binaryKernelFunc(LogicalOr, logicalOrImpl, null, "bool");
var logicalOrConfig = {
  kernelName: LogicalOr,
  backendName: "cpu",
  kernelFunc: logicalOr
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LRN.js
function lRN(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { depthRadius, bias, alpha, beta } = attrs;
  assertNotComplex(x, "LRN");
  const channels = x.shape[3];
  const maxD = channels - 1;
  const xValues = backend.data.get(x.dataId).values;
  const size = util_exports.sizeFromShape(x.shape);
  const result = new Float32Array(size);
  function sumAcrossChannels(offset) {
    const currentChannel = offset % channels;
    let beginSumOffset = offset - currentChannel + Math.max(0, currentChannel - depthRadius);
    const endSumOffset = offset - currentChannel + Math.min(currentChannel + depthRadius, maxD);
    let sum2 = 0;
    for (; beginSumOffset <= endSumOffset; beginSumOffset++) {
      const z = xValues[beginSumOffset];
      sum2 += z * z;
    }
    return sum2;
  }
  for (let offset = 0; offset < size; offset++) {
    const sum2 = sumAcrossChannels(offset);
    const val = xValues[offset] * Math.pow(bias + alpha * sum2, -beta);
    result[offset] = val;
  }
  return backend.makeTensorInfo(x.shape, x.dtype, result);
}
var LRNConfig = {
  kernelName: LRN,
  backendName: "cpu",
  kernelFunc: lRN
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/LRNGrad.js
function lRNGrad(args) {
  const { inputs, backend, attrs } = args;
  const { x, y, dy } = inputs;
  const { depthRadius, bias, alpha, beta } = attrs;
  assertNotComplex(dy, "LRNGrad");
  const dySize = util_exports.sizeFromShape(dy.shape);
  const channels = dy.shape[3];
  const dyValues = backend.data.get(dy.dataId).values;
  const xValues = backend.data.get(x.dataId).values;
  const yValues = backend.data.get(y.dataId).values;
  const result = new Float32Array(dySize);
  const size = dySize;
  for (let offset = 0; offset < size; offset++) {
    const currentChannel = offset % channels;
    const depthBegin = offset - currentChannel + Math.max(0, currentChannel - depthRadius);
    const depthEnd = offset - currentChannel + Math.min(channels, currentChannel + depthRadius + 1);
    let norm = 0;
    for (let k = depthBegin; k < depthEnd; k++) {
      norm += Math.pow(xValues[k], 2);
    }
    norm = alpha * norm + bias;
    for (let k = depthBegin; k < depthEnd; k++) {
      let dyi = -2 * alpha * beta * xValues[k] * yValues[offset] / norm;
      if (offset === k) {
        dyi += Math.pow(norm, -beta);
      }
      dyi *= dyValues[offset];
      result[k] += dyi;
    }
  }
  return backend.makeTensorInfo(dy.shape, x.dtype, result);
}
var LRNGradConfig = {
  kernelName: LRNGrad,
  backendName: "cpu",
  kernelFunc: lRNGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Max.js
function max(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { reductionIndices, keepDims } = attrs;
  const cpuBackend = backend;
  let xShape = x.shape;
  const xRank = xShape.length;
  const origAxes = util_exports.parseAxisParam(reductionIndices, xShape);
  let axes = origAxes;
  const permutedAxes = backend_util_exports.getAxesPermutation(axes, xRank);
  let xVals = cpuBackend.data.get(x.dataId).values;
  if (permutedAxes != null) {
    const newShape = new Array(xRank);
    for (let i = 0; i < newShape.length; i++) {
      newShape[i] = xShape[permutedAxes[i]];
    }
    xVals = transposeImpl(xVals, xShape, x.dtype, permutedAxes, newShape);
    axes = backend_util_exports.getInnerMostAxes(axes.length, xRank);
    xShape = newShape;
  }
  assertNotComplex(x, "max");
  backend_util_exports.assertAxesAreInnerMostDims("max", axes, xRank);
  const [maxOutShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes(xShape, axes);
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const result = maxImpl(xVals, reduceSize, maxOutShape, x.dtype);
  const dataId = cpuBackend.write(result, maxOutShape, x.dtype);
  let outShape = maxOutShape;
  if (keepDims) {
    const newShape = backend_util_exports.expandShapeToKeepDim(maxOutShape, origAxes);
    outShape = newShape;
  }
  return { dataId, shape: outShape, dtype: x.dtype };
}
var maxConfig = {
  kernelName: Max,
  backendName: "cpu",
  kernelFunc: max
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPool.js
function maxPool(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  assertNotComplex(x, "maxPool");
  const { filterSize, strides, pad, dimRoundingMode } = attrs;
  const dilations = 1;
  util_exports.assert(backend_util_exports.eitherStridesOrDilationsAreOne(strides, dilations), () => `Error in maxPool: Either strides or dilations must be 1. Got strides ${strides} and dilations '${dilations}'`);
  const convInfo = backend_util_exports.computePool2DInfo(x.shape, filterSize, strides, dilations, pad, dimRoundingMode);
  let res;
  if (convInfo.filterWidth === 1 && convInfo.filterHeight === 1 && util_exports.arraysEqual(convInfo.inShape, convInfo.outShape)) {
    res = identity({ inputs: { x }, backend });
  } else {
    const xValues = backend.data.get(x.dataId).values;
    const strides2 = util_exports.computeStrides(x.shape);
    const buffer2 = pool(xValues, x.shape, x.dtype, strides2, convInfo, "max");
    res = backend.makeTensorInfo(convInfo.outShape, x.dtype, buffer2.values);
  }
  return res;
}
var maxPoolConfig = {
  kernelName: MaxPool,
  backendName: "cpu",
  kernelFunc: maxPool
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPool3D.js
function maxPool3D(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { filterSize, strides, pad, dimRoundingMode, dataFormat } = attrs;
  assertNotComplex(x, "maxPool3d");
  const convInfo = backend_util_exports.computePool3DInfo(x.shape, filterSize, strides, 1, pad, dimRoundingMode, dataFormat);
  const xValues = backend.data.get(x.dataId).values;
  const outBuf = pool3d(xValues, x.shape, x.dtype, util_exports.computeStrides(x.shape), convInfo, "max");
  return backend.makeTensorInfo(outBuf.shape, "float32", outBuf.values);
}
var maxPool3DConfig = {
  kernelName: MaxPool3D,
  backendName: "cpu",
  kernelFunc: maxPool3D
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPool3DGrad.js
function maxPool3DGrad(args) {
  const { inputs, backend, attrs } = args;
  const { dy, input } = inputs;
  const { filterSize, strides, pad, dimRoundingMode } = attrs;
  assertNotComplex([dy, input], "maxPool3DGrad");
  const convInfo = backend_util_exports.computePool3DInfo(input.shape, filterSize, strides, 1, pad, dimRoundingMode);
  const inputBuf = backend.bufferSync(input);
  const maxPosBuf = maxPool3dPositions(inputBuf, convInfo);
  const strideDepth = convInfo.strideDepth;
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const dilationDepth = convInfo.dilationDepth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterDepth = convInfo.effectiveFilterDepth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padFront = effectiveFilterDepth - 1 - convInfo.padInfo.front;
  const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;
  const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;
  const dx = buffer(input.shape, "float32");
  const dyBuf = backend.bufferSync(dy);
  for (let batch = 0; batch < convInfo.batchSize; ++batch) {
    for (let channel = 0; channel < convInfo.inChannels; ++channel) {
      for (let dxDepth = 0; dxDepth < convInfo.inDepth; ++dxDepth) {
        for (let dxRow = 0; dxRow < convInfo.inHeight; ++dxRow) {
          for (let dxCol = 0; dxCol < convInfo.inWidth; ++dxCol) {
            const dyDepthCorner = dxDepth - padFront;
            const dyRowCorner = dxRow - padTop;
            const dyColCorner = dxCol - padLeft;
            let dotProd = 0;
            for (let wDepth = 0; wDepth < effectiveFilterDepth; wDepth += dilationDepth) {
              const dyDepth = (dyDepthCorner + wDepth) / strideDepth;
              if (dyDepth < 0 || dyDepth >= convInfo.outDepth || Math.floor(dyDepth) !== dyDepth) {
                continue;
              }
              for (let wRow = 0; wRow < effectiveFilterHeight; wRow += dilationHeight) {
                const dyRow = (dyRowCorner + wRow) / strideHeight;
                if (dyRow < 0 || dyRow >= convInfo.outHeight || Math.floor(dyRow) !== dyRow) {
                  continue;
                }
                for (let wCol = 0; wCol < effectiveFilterWidth; wCol += dilationWidth) {
                  const dyCol = (dyColCorner + wCol) / strideWidth;
                  if (dyCol < 0 || dyCol >= convInfo.outWidth || Math.floor(dyCol) !== dyCol) {
                    continue;
                  }
                  const maxPos = effectiveFilterDepth * effectiveFilterHeight * effectiveFilterWidth - 1 - maxPosBuf.get(batch, dyDepth, dyRow, dyCol, channel);
                  const curPos = wDepth * effectiveFilterHeight * effectiveFilterWidth + wRow * effectiveFilterWidth + wCol;
                  const mask = maxPos === curPos ? 1 : 0;
                  if (mask === 0) {
                    continue;
                  }
                  const pixel = dyBuf.get(batch, dyDepth, dyRow, dyCol, channel);
                  dotProd += pixel * mask;
                }
              }
            }
            dx.set(dotProd, batch, dxDepth, dxRow, dxCol, channel);
          }
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var maxPool3DGradConfig = {
  kernelName: MaxPool3DGrad,
  backendName: "cpu",
  kernelFunc: maxPool3DGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPoolGrad.js
function maxPoolGrad(args) {
  const { inputs, backend, attrs } = args;
  const { dy, input, output } = inputs;
  const x = input;
  assertNotComplex([input, output], "maxPoolGrad");
  const { filterSize, strides, pad, dimRoundingMode } = attrs;
  const convInfo = backend_util_exports.computePool2DInfo(x.shape, filterSize, strides, 1, pad, dimRoundingMode);
  const xValues = backend.data.get(x.dataId).values;
  const maxPosBuf = buffer(convInfo.outShape, x.dtype, maxPoolPositions(xValues, x.shape, x.dtype, convInfo).values);
  const strideHeight = convInfo.strideHeight;
  const strideWidth = convInfo.strideWidth;
  const dilationHeight = convInfo.dilationHeight;
  const dilationWidth = convInfo.dilationWidth;
  const effectiveFilterHeight = convInfo.effectiveFilterHeight;
  const effectiveFilterWidth = convInfo.effectiveFilterWidth;
  const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;
  const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;
  const dx = buffer(x.shape, "float32");
  const dyData = backend.data.get(dy.dataId).values;
  const dyBuf = buffer(dy.shape, "float32", dyData);
  for (let b = 0; b < convInfo.batchSize; ++b) {
    for (let d = 0; d < convInfo.inChannels; ++d) {
      for (let dxR = 0; dxR < convInfo.inHeight; ++dxR) {
        for (let dxC = 0; dxC < convInfo.inWidth; ++dxC) {
          const dyRCorner = dxR - padTop;
          const dyCCorner = dxC - padLeft;
          let dotProd = 0;
          for (let wR = 0; wR < effectiveFilterHeight; wR += dilationHeight) {
            const dyR = (dyRCorner + wR) / strideHeight;
            if (dyR < 0 || dyR >= convInfo.outHeight || Math.floor(dyR) !== dyR) {
              continue;
            }
            for (let wC = 0; wC < effectiveFilterWidth; wC += dilationWidth) {
              const dyC = (dyCCorner + wC) / strideWidth;
              if (dyC < 0 || dyC >= convInfo.outWidth || Math.floor(dyC) !== dyC) {
                continue;
              }
              const maxPos = effectiveFilterHeight * effectiveFilterWidth - 1 - maxPosBuf.get(b, dyR, dyC, d);
              const curPos = wR * effectiveFilterWidth + wC;
              const mask = maxPos === curPos ? 1 : 0;
              if (mask === 0) {
                continue;
              }
              const pixel = dyBuf.get(b, dyR, dyC, d);
              dotProd += pixel * mask;
            }
          }
          dx.set(dotProd, b, dxR, dxC, d);
        }
      }
    }
  }
  return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
var maxPoolGradConfig = {
  kernelName: MaxPoolGrad,
  backendName: "cpu",
  kernelFunc: maxPoolGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPoolWithArgmax_impl.js
function maxPoolWithArgmaxImpl(xValues, xShape, dtype, includeBatchInIndex, convInfo) {
  const strides = util_exports.computeStrides(xShape);
  const maxPools = pool(xValues, xShape, dtype, strides, convInfo, "max");
  const maxPositions = maxPoolPositions(xValues, xShape, dtype, convInfo, true, includeBatchInIndex);
  return [maxPools.values, maxPositions.values];
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPoolWithArgmax.js
var maxPoolWithArgmaxConfig = {
  kernelName: MaxPoolWithArgmax,
  backendName: "cpu",
  kernelFunc: ({ inputs, attrs, backend }) => {
    const { x } = inputs;
    const { filterSize, strides, pad, includeBatchInIndex } = attrs;
    const cpuBackend = backend;
    assertNotComplex(x, "MaxPoolWithArgmax");
    const values = cpuBackend.data.get(x.dataId).values;
    const convInfo = backend_util_exports.computePool2DInfo(x.shape, filterSize, strides, [1, 1], pad);
    const [pooled, indexes] = maxPoolWithArgmaxImpl(values, x.shape, x.dtype, includeBatchInIndex, convInfo);
    const pooledDataId = cpuBackend.write(pooled, convInfo.outShape, x.dtype);
    const indexesDataId = cpuBackend.write(indexes, convInfo.outShape, x.dtype);
    return [
      { dataId: pooledDataId, shape: convInfo.outShape, dtype: x.dtype },
      { dataId: indexesDataId, shape: convInfo.outShape, dtype: "int32" }
    ];
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Mean.js
function mean(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, keepDims } = attrs;
  const axes = util_exports.parseAxisParam(axis, x.shape);
  const shapes = backend_util_exports.computeOutAndReduceShapes(x.shape, axes);
  const reduceShape = shapes[1];
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const toDispose = [];
  const reduceSizeScalar = backend.makeTensorInfo([], "float32", new Float32Array([reduceSize]));
  toDispose.push(reduceSizeScalar);
  const $x = cast({ inputs: { x }, backend, attrs: { dtype: "float32" } });
  toDispose.push($x);
  const res = div({ inputs: { a: $x, b: reduceSizeScalar }, backend });
  toDispose.push(res);
  const result = sum({ inputs: { x: res }, backend, attrs: { axis, keepDims } });
  toDispose.forEach((t) => backend.disposeIntermediateTensorInfo(t));
  return result;
}
var meanConfig = {
  kernelName: Mean,
  backendName: "cpu",
  kernelFunc: mean
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Min.js
function min(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { axis, keepDims } = attrs;
  assertNotComplex(x, "min");
  const origAxes = util_exports.parseAxisParam(axis, x.shape);
  let axes = origAxes;
  const permutedAxes = backend_util_exports.getAxesPermutation(axes, x.shape.length);
  let $x = x;
  if (permutedAxes != null) {
    $x = transpose({ inputs: { x }, backend, attrs: { perm: permutedAxes } });
    axes = backend_util_exports.getInnerMostAxes(axes.length, x.shape.length);
  }
  backend_util_exports.assertAxesAreInnerMostDims("min", axes, $x.shape.length);
  const [outShape, reduceShape] = backend_util_exports.computeOutAndReduceShapes($x.shape, axes);
  const reduceSize = util_exports.sizeFromShape(reduceShape);
  const vals = util_exports.makeZerosTypedArray(util_exports.sizeFromShape(outShape), $x.dtype);
  const aVals = backend.data.get($x.dataId).values;
  for (let i = 0; i < vals.length; ++i) {
    const offset = i * reduceSize;
    let min2 = aVals[offset];
    for (let j = 0; j < reduceSize; ++j) {
      const value = aVals[offset + j];
      if (Number.isNaN(value) || value < min2) {
        min2 = value;
      }
    }
    vals[i] = min2;
  }
  if (permutedAxes != null) {
    backend.disposeIntermediateTensorInfo($x);
  }
  const result = backend.makeTensorInfo(outShape, $x.dtype, vals);
  if (keepDims) {
    const expandedShape = backend_util_exports.expandShapeToKeepDim(outShape, origAxes);
    const reshapedResult = reshape({ inputs: { x: result }, backend, attrs: { shape: expandedShape } });
    backend.disposeIntermediateTensorInfo(result);
    return reshapedResult;
  }
  return result;
}
var minConfig = {
  kernelName: Min,
  backendName: "cpu",
  kernelFunc: min
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/MirrorPad.js
function mirrorPad(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { paddings, mode } = attrs;
  assertNotComplex(x, "mirrorPad");
  const outShape = paddings.map(
    (p2, i) => p2[0] + x.shape[i] + p2[1]
    /* afterPad */
  );
  const start = paddings.map((p2) => p2[0]);
  const end = paddings.map((p2, i) => p2[0] + x.shape[i]);
  const offset = mode === "reflect" ? 0 : 1;
  const xVals = backend.data.get(x.dataId).values;
  const xRank = x.shape.length;
  const xStrides = util_exports.computeStrides(x.shape);
  const resultSize = util_exports.sizeFromShape(outShape);
  const resultRank = outShape.length;
  const resultStrides = util_exports.computeStrides(outShape);
  const resVals = util_exports.getTypedArrayFromDType(x.dtype, resultSize);
  for (let i = 0; i < resultSize; i++) {
    let coords = util_exports.indexToLoc(i, resultRank, resultStrides);
    for (let i2 = 0; i2 < resultRank; i2++) {
      if (coords[i2] < start[i2]) {
        coords[i2] = start[i2] * 2 - coords[i2] - offset;
      } else if (coords[i2] >= end[i2]) {
        coords[i2] = (end[i2] - 1) * 2 - coords[i2] + offset;
      }
    }
    coords = coords.map((c, i2) => c - start[i2]);
    const inIndex = util_exports.locToIndex(coords, xRank, xStrides);
    resVals[i] = xVals[inIndex];
  }
  const outId = backend.write(resVals, outShape, x.dtype);
  return { dataId: outId, shape: outShape, dtype: x.dtype };
}
var mirrorPadConfig = {
  kernelName: MirrorPad,
  backendName: "cpu",
  kernelFunc: mirrorPad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Mod.js
var modImpl = createSimpleBinaryKernelImpl((aValue, bValue) => {
  const rem = aValue % bValue;
  if (aValue < 0 && bValue < 0 || aValue >= 0 && bValue >= 0) {
    return rem;
  } else {
    return (rem + bValue) % bValue;
  }
});
var mod = binaryKernelFunc(Mod, modImpl);
var modConfig = {
  kernelName: Mod,
  backendName: "cpu",
  kernelFunc: mod
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Multinomial.js
var seedrandom = __toESM(require_seedrandom());

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Softmax.js
function softmax(args) {
  const { inputs, backend, attrs } = args;
  const { logits } = inputs;
  const { dim } = attrs;
  const logitsRank = logits.shape.length;
  let $dim = dim;
  if ($dim === -1) {
    $dim = logitsRank - 1;
  }
  if ($dim !== logitsRank - 1) {
    throw Error(`Softmax along a non-last dimension is not yet supported. Logits was rank ${logitsRank} and dim was ${$dim}`);
  }
  const axes = util_exports.parseAxisParam([$dim], logits.shape);
  const maxLogit = max({
    inputs: { x: logits },
    backend,
    attrs: { reductionIndices: axes, keepDims: false }
  });
  const expandedShape = backend_util_exports.expandShapeToKeepDim(maxLogit.shape, axes);
  const maxLogitReshaped = reshape({ inputs: { x: maxLogit }, backend, attrs: { shape: expandedShape } });
  const a = sub({ inputs: { a: logits, b: maxLogitReshaped }, backend });
  const b = exp({ inputs: { x: a }, backend });
  const sumExp = sum({ inputs: { x: b }, backend, attrs: { axis: axes, keepDims: false } });
  const sumReshaped = reshape({ inputs: { x: sumExp }, backend, attrs: { shape: expandedShape } });
  const result = div({ inputs: { a: b, b: sumReshaped }, backend });
  backend.disposeIntermediateTensorInfo(maxLogit);
  backend.disposeIntermediateTensorInfo(maxLogitReshaped);
  backend.disposeIntermediateTensorInfo(a);
  backend.disposeIntermediateTensorInfo(b);
  backend.disposeIntermediateTensorInfo(sumExp);
  backend.disposeIntermediateTensorInfo(sumReshaped);
  return result;
}
var softmaxConfig = {
  kernelName: Softmax,
  backendName: "cpu",
  kernelFunc: softmax
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Multinomial.js
function multinomial(args) {
  const { inputs, backend, attrs } = args;
  const { logits } = inputs;
  const { numSamples, seed, normalized } = attrs;
  assertNotComplex(logits, "multinomial");
  const probabilities = normalized ? logits : softmax({ inputs: { logits }, backend, attrs: { dim: -1 } });
  const batchSize = probabilities.shape[0];
  const numEvents = probabilities.shape[1];
  const probVals = backend.data.get(probabilities.dataId).values;
  const resShape = [batchSize, numSamples];
  const resVals = util_exports.makeZerosTypedArray(util_exports.sizeFromShape(resShape), "int32");
  for (let b = 0; b < batchSize; ++b) {
    const offset = b * numEvents;
    const cdf = new Float32Array(numEvents - 1);
    cdf[0] = probVals[offset];
    for (let event = 1; event < cdf.length; ++event) {
      cdf[event] = cdf[event - 1] + probVals[offset + event];
    }
    const random = seedrandom.alea(seed.toString());
    const outOffset = b * numSamples;
    for (let sampleId = 0; sampleId < numSamples; ++sampleId) {
      const r = random();
      resVals[outOffset + sampleId] = cdf.length;
      for (let event = 0; event < cdf.length; event++) {
        if (r < cdf[event]) {
          resVals[outOffset + sampleId] = event;
          break;
        }
      }
    }
  }
  if (!normalized) {
    backend.disposeIntermediateTensorInfo(probabilities);
  }
  return backend.makeTensorInfo(resShape, "int32", resVals);
}
var multinomialConfig = {
  kernelName: Multinomial,
  backendName: "cpu",
  kernelFunc: multinomial
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/NonMaxSuppressionV3.js
var nonMaxSuppressionV3Impl = kernel_impls_exports.nonMaxSuppressionV3Impl;
function nonMaxSuppressionV3(args) {
  const { inputs, backend, attrs } = args;
  const { boxes, scores } = inputs;
  const { maxOutputSize, iouThreshold, scoreThreshold } = attrs;
  assertNotComplex(boxes, "NonMaxSuppression");
  const boxesVals = backend.data.get(boxes.dataId).values;
  const scoresVals = backend.data.get(scores.dataId).values;
  const { selectedIndices } = nonMaxSuppressionV3Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold);
  return backend.makeTensorInfo([selectedIndices.length], "int32", new Int32Array(selectedIndices));
}
var nonMaxSuppressionV3Config = {
  kernelName: NonMaxSuppressionV3,
  backendName: "cpu",
  kernelFunc: nonMaxSuppressionV3
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/NonMaxSuppressionV4.js
var nonMaxSuppressionV4Impl = kernel_impls_exports.nonMaxSuppressionV4Impl;
function nonMaxSuppressionV4(args) {
  const { inputs, backend, attrs } = args;
  const { boxes, scores } = inputs;
  const { maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize } = attrs;
  assertNotComplex(boxes, "NonMaxSuppressionPadded");
  const boxesVals = backend.data.get(boxes.dataId).values;
  const scoresVals = backend.data.get(scores.dataId).values;
  const { selectedIndices, validOutputs } = nonMaxSuppressionV4Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize);
  return [
    backend.makeTensorInfo([selectedIndices.length], "int32", new Int32Array(selectedIndices)),
    backend.makeTensorInfo([], "int32", new Int32Array([validOutputs]))
  ];
}
var nonMaxSuppressionV4Config = {
  kernelName: NonMaxSuppressionV4,
  backendName: "cpu",
  kernelFunc: nonMaxSuppressionV4
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/NonMaxSuppressionV5.js
var nonMaxSuppressionV5Impl = kernel_impls_exports.nonMaxSuppressionV5Impl;
function nonMaxSuppressionV5(args) {
  const { inputs, backend, attrs } = args;
  const { boxes, scores } = inputs;
  const { maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma } = attrs;
  assertNotComplex(boxes, "NonMaxSuppressionWithScore");
  const boxesVals = backend.data.get(boxes.dataId).values;
  const scoresVals = backend.data.get(scores.dataId).values;
  const maxOutputSizeVal = maxOutputSize;
  const iouThresholdVal = iouThreshold;
  const scoreThresholdVal = scoreThreshold;
  const softNmsSigmaVal = softNmsSigma;
  const { selectedIndices, selectedScores } = nonMaxSuppressionV5Impl(boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal, scoreThresholdVal, softNmsSigmaVal);
  return [
    backend.makeTensorInfo([selectedIndices.length], "int32", new Int32Array(selectedIndices)),
    backend.makeTensorInfo([selectedScores.length], "float32", new Float32Array(selectedScores))
  ];
}
var nonMaxSuppressionV5Config = {
  kernelName: NonMaxSuppressionV5,
  backendName: "cpu",
  kernelFunc: nonMaxSuppressionV5
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/OneHot.js
function oneHot(args) {
  const { inputs, backend, attrs } = args;
  const { indices } = inputs;
  const { dtype, depth, onValue, offValue } = attrs;
  assertNotComplex(indices, "oneHot");
  const indicesSize = util_exports.sizeFromShape(indices.shape);
  const res = new Float32Array(indicesSize * depth);
  res.fill(offValue);
  const indicesVal = backend.data.get(indices.dataId).values;
  for (let event = 0; event < indicesSize; ++event) {
    if (indicesVal[event] >= 0 && indicesVal[event] < depth) {
      res[event * depth + indicesVal[event]] = onValue;
    }
  }
  return backend.makeTensorInfo([...indices.shape, depth], dtype, res);
}
var oneHotConfig = {
  kernelName: OneHot,
  backendName: "cpu",
  kernelFunc: oneHot
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ZerosLike.js
function zerosLike(args) {
  const { inputs, backend } = args;
  const { x } = inputs;
  if (x.dtype === "string") {
    throw new Error("zerosLike is not supported for string tensors");
  } else if (x.dtype === "complex64") {
    const realPart = real({ inputs: { input: x }, backend });
    const r = zerosLike({ inputs: { x: realPart }, backend });
    const imagPart = imag({ inputs: { input: x }, backend });
    const i = zerosLike({ inputs: { x: imagPart }, backend });
    const result = complex({ inputs: { real: r, imag: i }, backend });
    backend.disposeIntermediateTensorInfo(realPart);
    backend.disposeIntermediateTensorInfo(r);
    backend.disposeIntermediateTensorInfo(imagPart);
    backend.disposeIntermediateTensorInfo(i);
    return result;
  } else {
    return fill({ backend, attrs: { shape: x.shape, value: 0, dtype: x.dtype } });
  }
}
var zerosLikeConfig = {
  kernelName: ZerosLike,
  backendName: "cpu",
  kernelFunc: zerosLike
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/OnesLike.js
function onesLike(args) {
  const { inputs, backend } = args;
  const { x } = inputs;
  if (x.dtype === "string") {
    throw new Error("onesLike is not supported for string tensors");
  } else if (x.dtype === "complex64") {
    const realPart = real({ inputs: { input: x }, backend });
    const r = onesLike({ inputs: { x: realPart }, backend });
    const imagPart = imag({ inputs: { input: x }, backend });
    const i = zerosLike({ inputs: { x: imagPart }, backend });
    const result = complex({ inputs: { real: r, imag: i }, backend });
    backend.disposeIntermediateTensorInfo(realPart);
    backend.disposeIntermediateTensorInfo(r);
    backend.disposeIntermediateTensorInfo(imagPart);
    backend.disposeIntermediateTensorInfo(i);
    return result;
  } else {
    return fill({ backend, attrs: { shape: x.shape, value: 1, dtype: x.dtype } });
  }
}
var onesLikeConfig = {
  kernelName: OnesLike,
  backendName: "cpu",
  kernelFunc: onesLike
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Pack.js
function pack(args) {
  const { inputs, backend, attrs } = args;
  const { axis } = attrs;
  if (inputs.length === 1) {
    return expandDims({ inputs: { input: inputs[0] }, backend, attrs: { dim: axis } });
  }
  const shape = inputs[0].shape;
  const dtype = inputs[0].dtype;
  inputs.forEach((t) => {
    util_exports.assertShapesMatch(shape, t.shape, "All tensors passed to stack must have matching shapes");
    util_exports.assert(dtype === t.dtype, () => "All tensors passed to stack must have matching dtypes");
  });
  const intermediateTensorInfos = [];
  const expandedTensors = inputs.map((t) => {
    const expandedT = expandDims({ inputs: { input: t }, backend, attrs: { dim: axis } });
    intermediateTensorInfos.push(expandedT);
    return expandedT;
  });
  const result = concat({ inputs: expandedTensors, backend, attrs: { axis } });
  intermediateTensorInfos.forEach((t) => backend.disposeIntermediateTensorInfo(t));
  return result;
}
var packConfig = {
  kernelName: Pack,
  backendName: "cpu",
  kernelFunc: pack
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/PadV2.js
function padV2(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { paddings, constantValue } = attrs;
  assertNotComplex(x, "pad");
  const outShape = paddings.map(
    (p2, i) => p2[0] + x.shape[i] + p2[1]
    /* afterPad */
  );
  const start = paddings.map((p2) => p2[0]);
  const xVals = backend.data.get(x.dataId).values;
  const xSize = util_exports.sizeFromShape(x.shape);
  const xRank = x.shape.length;
  const xStrides = util_exports.computeStrides(x.shape);
  const resultSize = util_exports.sizeFromShape(outShape);
  const resultRank = outShape.length;
  const resultStrides = util_exports.computeStrides(outShape);
  const resVals = util_exports.getTypedArrayFromDType(x.dtype, resultSize);
  if (constantValue !== 0) {
    resVals.fill(constantValue);
  }
  for (let i = 0; i < xSize; i++) {
    const coords = util_exports.indexToLoc(i, xRank, xStrides);
    const outCoords = coords.map((c, i2) => c + start[i2]);
    const outIndex = util_exports.locToIndex(outCoords, resultRank, resultStrides);
    resVals[outIndex] = xVals[i];
  }
  const outId = backend.write(resVals, outShape, x.dtype);
  return { dataId: outId, shape: outShape, dtype: x.dtype };
}
var padV2Config = {
  kernelName: PadV2,
  backendName: "cpu",
  kernelFunc: padV2
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Pow.js
var powImpl = createSimpleBinaryKernelImpl((a, b) => Math.pow(a, b));
var pow = binaryKernelFunc(Pow, powImpl);
var powConfig = {
  kernelName: Pow,
  backendName: "cpu",
  kernelFunc: pow
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/RaggedGather.js
function raggedGather(args) {
  const { inputs, backend, attrs } = args;
  const { paramsNestedSplits, paramsDenseValues, indices } = inputs;
  const { outputRaggedRank } = attrs;
  const $paramsNestedSplits = paramsNestedSplits.map((t) => backend.data.get(t.dataId).values);
  const $paramsNestedSplitsShapes = paramsNestedSplits.map((t) => t.shape);
  const $paramsDenseValues = backend.data.get(paramsDenseValues.dataId).values;
  const $indices = backend.data.get(indices.dataId).values;
  const [outputNestedSplits, outputDenseValues, outputDenseValuesShape] = raggedGatherImpl($paramsNestedSplits, $paramsNestedSplitsShapes, $paramsDenseValues, paramsDenseValues.shape, paramsDenseValues.dtype, $indices, indices.shape, outputRaggedRank);
  const outputNestedSplitsTensors = outputNestedSplits.map((splits) => backend.makeTensorInfo([splits.length], "int32", splits));
  const outputDenseValuesTensor = backend.makeTensorInfo(outputDenseValuesShape, paramsDenseValues.dtype, outputDenseValues);
  return outputNestedSplitsTensors.concat([outputDenseValuesTensor]);
}
var raggedGatherConfig = {
  kernelName: RaggedGather,
  backendName: "cpu",
  kernelFunc: raggedGather
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/RaggedRange.js
function raggedRange(args) {
  const { inputs, backend } = args;
  const { starts, limits, deltas } = inputs;
  const $starts = backend.data.get(starts.dataId).values;
  const $limits = backend.data.get(limits.dataId).values;
  const $deltas = backend.data.get(deltas.dataId).values;
  const [rtNestedSplitsData, rtDenseValuesData] = raggedRangeImpl($starts, starts.shape, starts.dtype, $limits, limits.shape, $deltas, deltas.shape);
  const rtNestedSplits = backend.makeTensorInfo([rtNestedSplitsData.length], "int32", rtNestedSplitsData);
  const rtDenseValues = backend.makeTensorInfo([rtDenseValuesData.length], starts.dtype, rtDenseValuesData);
  return [rtNestedSplits, rtDenseValues];
}
var raggedRangeConfig = {
  kernelName: RaggedRange,
  backendName: "cpu",
  kernelFunc: raggedRange
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/RaggedTensorToTensor.js
function raggedTensorToTensor(args) {
  const { inputs, backend, attrs } = args;
  const { shape, values, defaultValue, rowPartitionTensors } = inputs;
  const { rowPartitionTypes } = attrs;
  const $shape = backend.data.get(shape.dataId).values;
  const $values = backend.data.get(values.dataId).values;
  const $defaultValue = backend.data.get(defaultValue.dataId).values;
  const $rowPartitionValues = rowPartitionTensors.map((t) => backend.data.get(t.dataId).values);
  const rowPartitionValuesShapes = rowPartitionTensors.map((t) => t.shape);
  const [outputShape, output] = raggedTensorToTensorImpl($shape, shape.shape, $values, values.shape, values.dtype, $defaultValue, defaultValue.shape, $rowPartitionValues, rowPartitionValuesShapes, rowPartitionTypes);
  return backend.makeTensorInfo(outputShape, values.dtype, output);
}
var raggedTensorToTensorConfig = {
  kernelName: RaggedTensorToTensor,
  backendName: "cpu",
  kernelFunc: raggedTensorToTensor
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Range.js
function range(args) {
  const { backend, attrs } = args;
  const { start, stop, dtype, step: step2 } = attrs;
  const values = rangeImpl(start, stop, step2, dtype);
  return backend.makeTensorInfo([values.length], dtype, values);
}
var rangeConfig = {
  kernelName: Range,
  backendName: "cpu",
  kernelFunc: range
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Reciprocal.js
var reciprocal = unaryKernelFunc(Reciprocal, (xi) => 1 / xi);
var reciprocalConfig = {
  kernelName: Reciprocal,
  backendName: "cpu",
  kernelFunc: reciprocal
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeBilinear.js
function resizeBilinear(args) {
  const { inputs, backend, attrs } = args;
  const { images } = inputs;
  const { alignCorners, halfPixelCenters, size } = attrs;
  assertNotComplex(images, "resizeBilinear");
  const imagesStrides = util_exports.computeStrides(images.shape);
  const [newHeight, newWidth] = size;
  const [batch, oldHeight, oldWidth, numChannels] = images.shape;
  const xValues = backend.data.get(images.dataId).values;
  const result = new Float32Array(util_exports.sizeFromShape([batch, newHeight, newWidth, numChannels]));
  const effectiveInputSize = [
    alignCorners && newHeight > 1 ? oldHeight - 1 : oldHeight,
    alignCorners && newWidth > 1 ? oldWidth - 1 : oldWidth
  ];
  const effectiveOutputSize = [
    alignCorners && newHeight > 1 ? newHeight - 1 : newHeight,
    alignCorners && newWidth > 1 ? newWidth - 1 : newWidth
  ];
  let outputIdx = 0;
  const effectiveRowSizeRatio = effectiveInputSize[0] / effectiveOutputSize[0];
  const effectiveColSizeRatio = effectiveInputSize[1] / effectiveOutputSize[1];
  for (let b = 0; b < batch; b++) {
    for (let r = 0; r < newHeight; r++) {
      let sourceFracRow;
      if (halfPixelCenters) {
        sourceFracRow = effectiveRowSizeRatio * (r + 0.5) - 0.5;
      } else {
        sourceFracRow = effectiveRowSizeRatio * r;
      }
      const sourceRowFloor = Math.max(0, Math.floor(sourceFracRow));
      const rowFrac = sourceFracRow - sourceRowFloor;
      const sourceRowCeil = Math.min(oldHeight - 1, Math.ceil(sourceFracRow));
      const topRowOffset = b * imagesStrides[0] + sourceRowFloor * imagesStrides[1];
      const botRowOffset = b * imagesStrides[0] + sourceRowCeil * imagesStrides[1];
      for (let c = 0; c < newWidth; c++) {
        let sourceFracCol;
        if (halfPixelCenters) {
          sourceFracCol = effectiveColSizeRatio * (c + 0.5) - 0.5;
        } else {
          sourceFracCol = effectiveColSizeRatio * c;
        }
        const sourceColFloor = Math.max(0, Math.floor(sourceFracCol));
        const colFrac = sourceFracCol - sourceColFloor;
        const sourceColCeil = Math.min(oldWidth - 1, Math.ceil(sourceFracCol));
        const topLeftOffest = topRowOffset + sourceColFloor * imagesStrides[2];
        const botLeftOffset = botRowOffset + sourceColFloor * imagesStrides[2];
        const topRightOffset = topRowOffset + sourceColCeil * imagesStrides[2];
        const botRightOffest = botRowOffset + sourceColCeil * imagesStrides[2];
        for (let d = 0; d < numChannels; d++) {
          const topLeft = xValues[topLeftOffest + d];
          const bottomLeft = xValues[botLeftOffset + d];
          const topRight = xValues[topRightOffset + d];
          const bottomRight = xValues[botRightOffest + d];
          const top = topLeft + (topRight - topLeft) * colFrac;
          const bottom = bottomLeft + (bottomRight - bottomLeft) * colFrac;
          const newValue = top + (bottom - top) * rowFrac;
          result[outputIdx++] = newValue;
        }
      }
    }
  }
  return backend.makeTensorInfo([batch, newHeight, newWidth, numChannels], "float32", result);
}
var resizeBilinearConfig = {
  kernelName: ResizeBilinear,
  backendName: "cpu",
  kernelFunc: resizeBilinear
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeBilinearGrad.js
function resizeBilinearGrad(args) {
  const { inputs, backend, attrs } = args;
  const { images, dy } = inputs;
  const { alignCorners } = attrs;
  assertNotComplex([dy, images], "resizeBilinearGrad");
  const imagesStrides = util_exports.computeStrides(images.shape);
  const [batch, xHeight, xWidth, depth] = images.shape;
  const [, yHeight, yWidth] = dy.shape;
  const output = new Float32Array(batch * xHeight * xWidth * depth);
  const effectiveXSize = [
    alignCorners && yHeight > 1 ? xHeight - 1 : xHeight,
    alignCorners && yWidth > 1 ? xWidth - 1 : xWidth
  ];
  const effectiveYSize = [
    alignCorners && yHeight > 1 ? yHeight - 1 : yHeight,
    alignCorners && yWidth > 1 ? yWidth - 1 : yWidth
  ];
  const heightScale = effectiveXSize[0] / effectiveYSize[0];
  const widthScale = effectiveXSize[1] / effectiveYSize[1];
  const dyValues = backend.data.get(dy.dataId).values;
  let offset = 0;
  for (let b = 0; b < batch; b++) {
    const bOffset = b * imagesStrides[0];
    for (let r = 0; r < yHeight; r++) {
      const dxR = r * heightScale;
      const topDxRIndex = Math.floor(dxR);
      const bottomDxRIndex = Math.min(Math.ceil(dxR), xHeight - 1);
      const topDxROffset = bOffset + topDxRIndex * imagesStrides[1];
      const bottomDxROffset = bOffset + bottomDxRIndex * imagesStrides[1];
      const dxRLerp = dxR - topDxRIndex;
      const inverseDxRLerp = 1 - dxRLerp;
      for (let c = 0; c < yWidth; c++) {
        const dxC = c * widthScale;
        const leftDxCIndex = Math.floor(dxC);
        const rightDxCIndex = Math.min(Math.ceil(dxC), xWidth - 1);
        const dxCLerp = dxC - leftDxCIndex;
        const inverseDxCLerp = 1 - dxCLerp;
        const topLeftRCOffset = topDxROffset + leftDxCIndex * imagesStrides[2];
        const topRightRCOffset = topDxROffset + rightDxCIndex * imagesStrides[2];
        const bottomLeftRCOffset = bottomDxROffset + leftDxCIndex * imagesStrides[2];
        const bottomRightRCOffset = bottomDxROffset + rightDxCIndex * imagesStrides[2];
        const inverseDxRLerpTimesInverseDxCLerp = inverseDxRLerp * inverseDxCLerp;
        const inverseDxRLerpTimesDxCLerp = inverseDxRLerp * dxCLerp;
        const dxRLerpTimesInverseDxCLerp = dxRLerp * inverseDxCLerp;
        const dxRLerpTimesDxCLerp = dxRLerp * dxCLerp;
        for (let d = 0; d < depth; d++) {
          const dyVal = dyValues[offset++];
          output[topLeftRCOffset + d] += dyVal * inverseDxRLerpTimesInverseDxCLerp;
          output[topRightRCOffset + d] += dyVal * inverseDxRLerpTimesDxCLerp;
          output[bottomLeftRCOffset + d] += dyVal * dxRLerpTimesInverseDxCLerp;
          output[bottomRightRCOffset + d] += dyVal * dxRLerpTimesDxCLerp;
        }
      }
    }
  }
  return backend.makeTensorInfo([batch, xWidth, xHeight, depth], "float32", output);
}
var resizeBilinearGradConfig = {
  kernelName: ResizeBilinearGrad,
  backendName: "cpu",
  kernelFunc: resizeBilinearGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeNearestNeighbor.js
function resizeNearestNeighbor(args) {
  const { inputs, backend, attrs } = args;
  const { images } = inputs;
  const { alignCorners, halfPixelCenters, size } = attrs;
  assertNotComplex(images, "resizeNearestNeighbor");
  const imagesStrides = util_exports.computeStrides(images.shape);
  const [newHeight, newWidth] = size;
  const [batch, oldHeight, oldWidth, numChannels] = images.shape;
  const xValues = backend.data.get(images.dataId).values;
  const output = new Float32Array(batch * newHeight * newWidth * numChannels);
  const effectiveInputSize = [
    alignCorners && newHeight > 1 ? oldHeight - 1 : oldHeight,
    alignCorners && newWidth > 1 ? oldWidth - 1 : oldWidth
  ];
  const effectiveOutputSize = [
    alignCorners && newHeight > 1 ? newHeight - 1 : newHeight,
    alignCorners && newWidth > 1 ? newWidth - 1 : newWidth
  ];
  const effectiveRowSizeRatio = effectiveInputSize[0] / effectiveOutputSize[0];
  const effectiveColSizeRatio = effectiveInputSize[1] / effectiveOutputSize[1];
  let outputOffset = 0;
  for (let b = 0; b < batch; b++) {
    const batchOffset = b * imagesStrides[0];
    for (let r = 0; r < newHeight; r++) {
      const sourceFracRow = halfPixelCenters ? effectiveRowSizeRatio * (r + 0.5) : effectiveRowSizeRatio * r;
      let sourceNearestRow = Math.min(oldHeight - 1, alignCorners ? Math.round(sourceFracRow) : Math.floor(sourceFracRow));
      if (halfPixelCenters) {
        sourceNearestRow = Math.max(0, sourceNearestRow);
      }
      const rowOffset = batchOffset + sourceNearestRow * imagesStrides[1];
      for (let c = 0; c < newWidth; c++) {
        const sourceFracCol = halfPixelCenters ? effectiveColSizeRatio * (c + 0.5) : effectiveColSizeRatio * c;
        let sourceNearestCol = Math.min(oldWidth - 1, alignCorners ? Math.round(sourceFracCol) : Math.floor(sourceFracCol));
        if (halfPixelCenters) {
          sourceNearestCol = Math.max(0, sourceNearestCol);
        }
        const colOffset = rowOffset + sourceNearestCol * imagesStrides[2];
        for (let d = 0; d < numChannels; d++) {
          const newVal = xValues[colOffset + d];
          output[outputOffset++] = newVal;
        }
      }
    }
  }
  return backend.makeTensorInfo([batch, newHeight, newWidth, numChannels], images.dtype, output);
}
var resizeNearestNeighborConfig = {
  kernelName: ResizeNearestNeighbor,
  backendName: "cpu",
  kernelFunc: resizeNearestNeighbor
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeNearestNeighborGrad.js
function resizeNearestNeighborGrad(args) {
  const { inputs, backend, attrs } = args;
  const { images, dy } = inputs;
  const { alignCorners } = attrs;
  assertNotComplex([dy, images], "resizeNearestNeighborGrad");
  const imagesStrides = util_exports.computeStrides(images.shape);
  const dyStrides = util_exports.computeStrides(dy.shape);
  const [batch, xHeight, xWidth, depth] = images.shape;
  const [, yHeight, yWidth] = dy.shape;
  const output = new Float32Array(batch * xHeight * xWidth * depth);
  const dyValues = backend.data.get(dy.dataId).values;
  const effectiveXSize = [
    alignCorners && yHeight > 1 ? xHeight - 1 : xHeight,
    alignCorners && yWidth > 1 ? xWidth - 1 : xWidth
  ];
  const effectiveYSize = [
    alignCorners && yHeight > 1 ? yHeight - 1 : yHeight,
    alignCorners && yWidth > 1 ? yWidth - 1 : yWidth
  ];
  const heightScale = effectiveXSize[0] / effectiveYSize[0];
  const widthScale = effectiveXSize[1] / effectiveYSize[1];
  const invHeightScale = 1 / heightScale;
  const invWidthScale = 1 / widthScale;
  const winHeight = Math.ceil(invHeightScale) * 2 + 2;
  const winWidth = Math.ceil(invWidthScale) * 2 + 2;
  for (let b = 0; b < batch; b++) {
    const batchOffset = b * imagesStrides[0];
    for (let r = 0; r < xHeight; r++) {
      const rowOffset = batchOffset + r * imagesStrides[1];
      const startRLerp = Math.floor(r * invHeightScale);
      const startDyR = Math.floor(startRLerp - winHeight / 2);
      for (let c = 0; c < xWidth; c++) {
        const colOffset = rowOffset + c * imagesStrides[2];
        const startCLerp = Math.floor(c * invWidthScale);
        const startDyC = Math.floor(startCLerp - winWidth / 2);
        for (let d = 0; d < depth; d++) {
          let accum = 0;
          for (let dyRIndex = 0; dyRIndex < winHeight; dyRIndex++) {
            const dyR = dyRIndex + startDyR;
            if (dyR < 0 || dyR >= yHeight) {
              continue;
            }
            const dyROffset = batchOffset + dyR * dyStrides[1];
            const sourceFracRow = dyR * heightScale;
            const sourceNearestRow = Math.min(xHeight - 1, alignCorners ? Math.round(sourceFracRow) : Math.floor(sourceFracRow));
            if (r !== sourceNearestRow) {
              continue;
            }
            for (let dyCIndex = 0; dyCIndex < winWidth; dyCIndex++) {
              const dyC = dyCIndex + startDyC;
              if (dyC < 0 || dyC >= yWidth) {
                continue;
              }
              const dyCOffset = dyROffset + dyC * dyStrides[2];
              const sourceFracCol = dyC * widthScale;
              const sourceNearestCol = Math.min(xWidth - 1, alignCorners ? Math.round(sourceFracCol) : Math.floor(sourceFracCol));
              if (c === sourceNearestCol) {
                accum += dyValues[dyCOffset + d];
              }
            }
          }
          output[colOffset + d] = accum;
        }
      }
    }
  }
  return backend.makeTensorInfo(images.shape, images.dtype, output);
}
var resizeNearestNeighborGradConfig = {
  kernelName: ResizeNearestNeighborGrad,
  backendName: "cpu",
  kernelFunc: resizeNearestNeighborGrad
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Reverse.js
function reverse(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { dims } = attrs;
  assertNotComplex(x, "reverse");
  const xRank = x.shape.length;
  const $dims = util_exports.parseAxisParam(dims, x.shape);
  if (xRank === 0) {
    return identity({ inputs: { x }, backend });
  }
  const outBuf = new TensorBuffer(x.shape, x.dtype);
  const xBuf = backend.bufferSync(x);
  for (let i = 0; i < outBuf.size; i++) {
    const outLoc = outBuf.indexToLoc(i);
    const inLoc = outLoc.slice();
    $dims.forEach((d) => inLoc[d] = x.shape[d] - 1 - inLoc[d]);
    outBuf.set(xBuf.get(...inLoc), ...outLoc);
  }
  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);
}
var reverseConfig = {
  kernelName: Reverse,
  backendName: "cpu",
  kernelFunc: reverse
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/RotateWithOffset.js
var rotateWithOffsetConfig = {
  kernelName: RotateWithOffset,
  backendName: "cpu",
  kernelFunc: ({ inputs, attrs, backend }) => {
    const { image } = inputs;
    const { radians, fillValue, center } = attrs;
    const cpuBackend = backend;
    const output = util_exports.getTypedArrayFromDType(image.dtype, util_exports.sizeFromShape(image.shape));
    const [batch, imageHeight, imageWidth, numChannels] = image.shape;
    const [centerX, centerY] = backend_util_exports.getImageCenter(center, imageHeight, imageWidth);
    const fullOpacityValue = 255;
    const sinFactor = Math.sin(radians);
    const cosFactor = Math.cos(radians);
    const imageVals = cpuBackend.data.get(image.dataId).values;
    for (let batchIdx = 0; batchIdx < batch; batchIdx++) {
      const batchOffset = batchIdx * imageWidth * imageHeight * numChannels;
      for (let row = 0; row < imageHeight; row++) {
        const rowOffset = row * (imageWidth * numChannels);
        for (let col = 0; col < imageWidth; col++) {
          const colOffset = col * numChannels;
          for (let channel = 0; channel < numChannels; channel++) {
            const coords = [batch, row, col, channel];
            const x = coords[2];
            const y = coords[1];
            let coordX = (x - centerX) * cosFactor - (y - centerY) * sinFactor;
            let coordY = (x - centerX) * sinFactor + (y - centerY) * cosFactor;
            coordX = Math.round(coordX + centerX);
            coordY = Math.round(coordY + centerY);
            let outputValue = fillValue;
            if (typeof fillValue !== "number") {
              if (channel === 3) {
                outputValue = fullOpacityValue;
              } else {
                outputValue = fillValue[channel];
              }
            }
            if (coordX >= 0 && coordX < imageWidth && coordY >= 0 && coordY < imageHeight) {
              const rotatedRowOffset = coordY * (imageWidth * numChannels);
              const rotatedColOffset = coordX * numChannels;
              const imageIdx = batchOffset + rotatedRowOffset + rotatedColOffset + channel;
              outputValue = imageVals[imageIdx];
            }
            const outIdx = batchOffset + rowOffset + colOffset + channel;
            output[outIdx] = outputValue;
          }
        }
      }
    }
    const dataId = cpuBackend.write(output, image.shape, image.dtype);
    return { dataId, shape: image.shape, dtype: image.dtype };
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Round.js
var round = unaryKernelFunc(Round, (xi) => {
  const base = Math.floor(xi);
  if (xi - base < 0.5) {
    return Math.floor(xi);
  } else if (xi - base > 0.5) {
    return Math.ceil(xi);
  } else {
    if (base % 2 === 0) {
      return base;
    } else {
      return base + 1;
    }
  }
});
var roundConfig = {
  kernelName: Round,
  backendName: "cpu",
  kernelFunc: round
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/ScatterNd.js
function scatterNd(args) {
  const { inputs, backend, attrs } = args;
  const { indices, updates } = inputs;
  const { shape } = attrs;
  const { sliceRank, numUpdates, sliceSize, strides, outputSize } = backend_util_exports.calculateShapes(updates, indices, shape);
  const sumDupeIndices = true;
  const indicesBuf = backend.bufferSync(indices);
  const updatesBuf = backend.bufferSync(updates);
  const outBuf = scatterImpl(indicesBuf, updatesBuf, shape, outputSize, sliceSize, numUpdates, sliceRank, strides, 0, sumDupeIndices);
  return backend.makeTensorInfo(shape, outBuf.dtype, outBuf.values);
}
var scatterNdConfig = {
  kernelName: ScatterNd,
  backendName: "cpu",
  kernelFunc: scatterNd
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SearchSorted_impl.js
function lowerBound(array, value) {
  let left = 0;
  let right = array.length;
  let mid = 0;
  while (left < right) {
    mid = Math.floor((left + right) / 2);
    if (array[mid] < value) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  return right;
}
function upperBound(array, value) {
  let left = 0;
  let right = array.length;
  let mid = 0;
  while (left < right) {
    mid = Math.floor((left + right) / 2);
    if (array[mid] <= value) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  return right;
}
function searchSortedImpl(sortedInputs, values, batchSize, numInputs, numValues, side) {
  const output = util_exports.getArrayFromDType("int32", batchSize * numValues);
  for (let b = 0; b < batchSize; ++b) {
    const sortedInputsSlice = sortedInputs.slice(b * numInputs, (b + 1) * numInputs);
    const outputOffset = b * numValues;
    for (let i = 0; i < numValues; ++i) {
      output[outputOffset + i] = side === "left" ? lowerBound(sortedInputsSlice, values[i + outputOffset]) : upperBound(sortedInputsSlice, values[i + outputOffset]);
    }
  }
  return output;
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SearchSorted.js
function searchSorted(args) {
  const { inputs, backend, attrs } = args;
  const { sortedSequence, values } = inputs;
  const { side } = attrs;
  const $sortedSequence = backend.data.get(sortedSequence.dataId).values;
  const $values = backend.data.get(values.dataId).values;
  const output = searchSortedImpl($sortedSequence, $values, sortedSequence.shape[0], sortedSequence.shape[1], values.shape[1], side);
  return backend.makeTensorInfo(values.shape, "int32", output);
}
var searchSortedConfig = {
  kernelName: SearchSorted,
  backendName: "cpu",
  kernelFunc: searchSorted
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Select.js
function select(args) {
  const { inputs, backend } = args;
  const { condition, t, e } = inputs;
  assertNotComplex([condition, t, e], "select");
  const conditionRank = condition.shape.length;
  const values = backend.data.get(condition.dataId).values;
  const tValues = backend.data.get(t.dataId).values;
  const eValues = backend.data.get(e.dataId).values;
  const resultDtype = upcastType(t.dtype, e.dtype);
  const newValues = util_exports.makeZerosTypedArray(util_exports.sizeFromShape(t.shape), resultDtype);
  let index = 0;
  const offset = conditionRank === 0 || conditionRank > 1 || t.shape.length === 1 ? 1 : util_exports.sizeFromShape(t.shape.slice(1));
  for (let i = 0; i < values.length; i++) {
    for (let j = 0; j < offset; j++) {
      if (values[i] === 1) {
        newValues[index++] = tValues[i];
      } else {
        newValues[index++] = eValues[i];
      }
    }
  }
  return backend.makeTensorInfo(t.shape, resultDtype, newValues);
}
var selectConfig = {
  kernelName: Select,
  backendName: "cpu",
  kernelFunc: select
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Selu.js
var scaleAlpha = backend_util_exports.SELU_SCALEALPHA;
var scale = backend_util_exports.SELU_SCALE;
var selu = unaryKernelFunc(Selu, (xi) => {
  if (xi >= 0) {
    return scale * xi;
  } else {
    return scaleAlpha * (Math.exp(xi) - 1);
  }
});
var seluConfig = {
  kernelName: Selu,
  backendName: "cpu",
  kernelFunc: selu
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Sign.js
var sign = unaryKernelFunc(Sign, (xi) => {
  if (xi < 0) {
    return -1;
  } else if (xi > 0) {
    return 1;
  } else {
    return 0;
  }
});
var signConfig = {
  kernelName: Sign,
  backendName: "cpu",
  kernelFunc: sign
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Sin.js
var sin = unaryKernelFunc(Sin, (xi) => Math.sin(xi));
var sinConfig = {
  kernelName: Sin,
  backendName: "cpu",
  kernelFunc: sin
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Sinh.js
var sinh = unaryKernelFunc(Sinh, (xi) => Math.sinh(xi));
var sinhConfig = {
  kernelName: Sinh,
  backendName: "cpu",
  kernelFunc: sinh
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Softplus.js
var epsilon = 11920928955078125e-23;
var threshold = Math.log(epsilon) + 2;
var softplus = unaryKernelFunc(Softplus, (xi) => {
  const tooLarge = xi > -threshold;
  const tooSmall = xi < threshold;
  const expX = Math.exp(xi);
  let result;
  if (tooSmall) {
    result = expX;
  } else if (tooLarge) {
    result = xi;
  } else {
    result = Math.log(1 + expX);
  }
  return result;
});
var softplusConfig = {
  kernelName: Softplus,
  backendName: "cpu",
  kernelFunc: softplus
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SpaceToBatchND.js
function spaceToBatchND(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { blockShape, paddings } = attrs;
  assertNotComplex([x], "spaceToBatchND");
  const prod = util_exports.sizeFromShape(blockShape);
  const completePaddings = [[0, 0]];
  completePaddings.push(...paddings);
  for (let i = 1 + blockShape.length; i < x.shape.length; ++i) {
    completePaddings.push([0, 0]);
  }
  const paddedX = padV2Config.kernelFunc({
    inputs: { x },
    backend,
    attrs: { paddings: completePaddings, constantValue: 0 }
  });
  const reshapedPaddedShape = backend_util_exports.getReshaped(paddedX.shape, blockShape, prod, false);
  const permutedReshapedPaddedPermutation = backend_util_exports.getPermuted(reshapedPaddedShape.length, blockShape.length, false);
  const flattenShape = backend_util_exports.getReshapedPermuted(paddedX.shape, blockShape, prod, false);
  const reshapeInputs = { x: paddedX };
  const reshapeAttrs = { shape: reshapedPaddedShape };
  const paddedXReshaped = reshape({ inputs: reshapeInputs, backend, attrs: reshapeAttrs });
  const transposeInputs = { x: paddedXReshaped };
  const transposeAttrs = { perm: permutedReshapedPaddedPermutation };
  const paddedXT = transpose({ inputs: transposeInputs, backend, attrs: transposeAttrs });
  const resultReshapeInputs = { x: paddedXT };
  const resultReshapeAttrs = { shape: flattenShape };
  const result = reshape({ inputs: resultReshapeInputs, backend, attrs: resultReshapeAttrs });
  backend.disposeIntermediateTensorInfo(paddedX);
  backend.disposeIntermediateTensorInfo(paddedXReshaped);
  backend.disposeIntermediateTensorInfo(paddedXT);
  return result;
}
var spaceToBatchNDConfig = {
  kernelName: SpaceToBatchND,
  backendName: "cpu",
  kernelFunc: spaceToBatchND
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SparseFillEmptyRows.js
function sparseFillEmptyRows(args) {
  const { inputs, backend } = args;
  const { indices, values, denseShape, defaultValue } = inputs;
  if (denseShape.shape.length !== 1) {
    throw new Error(`Dense shape must be a vector, saw:
        ${denseShape.shape}`);
  }
  if (indices.shape.length !== 2) {
    throw new Error(`Indices must be a matrix, saw:
        ${indices.shape}`);
  }
  if (values.shape.length !== 1) {
    throw new Error(`Values must be a vector, saw:
        ${values.shape}`);
  }
  if (defaultValue.shape.length !== 0) {
    throw new Error(`Default value must be a scalar, saw:
        ${defaultValue.shape}`);
  }
  const $indices = backend.data.get(indices.dataId).values;
  const $values = backend.data.get(values.dataId).values;
  const $denseShape = backend.data.get(denseShape.dataId).values;
  const $defaultValue = backend.data.get(defaultValue.dataId).values[0];
  const [outputIndices, outputIndicesShape, outputValues, emptyRowIndicator, reverseIndexMap] = sparseFillEmptyRowsImpl($indices, indices.shape, indices.dtype, $values, values.dtype, $denseShape, $defaultValue);
  return [
    backend.makeTensorInfo(outputIndicesShape, indices.dtype, outputIndices),
    backend.makeTensorInfo([outputIndicesShape[0]], values.dtype, outputValues),
    backend.makeTensorInfo([emptyRowIndicator.length], "bool", new Uint8Array(emptyRowIndicator.map((value) => Number(value)))),
    backend.makeTensorInfo([reverseIndexMap.length], indices.dtype, new Int32Array(reverseIndexMap))
  ];
}
var sparseFillEmptyRowsConfig = {
  kernelName: SparseFillEmptyRows,
  backendName: "cpu",
  kernelFunc: sparseFillEmptyRows
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SparseReshape.js
function sparseReshape(args) {
  const { inputs, backend } = args;
  const { inputIndices, inputShape, newShape } = inputs;
  if (inputIndices.shape.length !== 2) {
    throw new Error(`Input indices should be a matrix but received shape
        ${inputIndices.shape}`);
  }
  if (inputShape.shape.length !== 1) {
    throw new Error(`Input shape should be a vector but received shape
        ${inputShape.shape}`);
  }
  if (newShape.shape.length !== 1) {
    throw new Error(`Target shape should be a vector but received shape ${newShape.shape}`);
  }
  const $inputShape = Array.from(backend.data.get(inputShape.dataId).values);
  const $inputIndices = backend.data.get(inputIndices.dataId).values;
  const targetShape = Array.from(backend.data.get(newShape.dataId).values);
  const [newIndices, indicesShape, outputShape] = sparseReshapeImpl($inputIndices, inputIndices.shape, inputIndices.dtype, $inputShape, targetShape);
  return [
    backend.makeTensorInfo(indicesShape, inputIndices.dtype, newIndices),
    backend.makeTensorInfo([outputShape.length], newShape.dtype, new Int32Array(outputShape))
  ];
}
var sparseReshapeConfig = {
  kernelName: SparseReshape,
  backendName: "cpu",
  kernelFunc: sparseReshape
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SparseSegmentMean.js
function sparseSegmentMean(args) {
  const { inputs, backend } = args;
  const { data, indices, segmentIds } = inputs;
  if (data.shape.length < 1) {
    throw new Error(`Data should be at least 1 dimensional but received scalar`);
  }
  if (indices.shape.length !== 1) {
    throw new Error(`Indices should be a vector but received shape
          ${indices.shape}`);
  }
  if (segmentIds.shape.length !== 1) {
    throw new Error(`Segment ids should be a vector but received shape
          ${segmentIds.shape}`);
  }
  if (indices.shape[0] !== segmentIds.shape[0]) {
    throw new Error(`segmentIds and indices should have same size.`);
  }
  const $data = backend.data.get(data.dataId).values;
  const $indices = backend.data.get(indices.dataId).values;
  const $segmentIds = backend.data.get(segmentIds.dataId).values;
  const [outputData, outputDataShape] = sparseSegmentReductionImpl($data, data.shape, data.dtype, $indices, $segmentIds, true);
  return backend.makeTensorInfo(outputDataShape, data.dtype, outputData);
}
var sparseSegmentMeanConfig = {
  kernelName: SparseSegmentMean,
  backendName: "cpu",
  kernelFunc: sparseSegmentMean
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SparseSegmentSum.js
function sparseSegmentSum(args) {
  const { inputs, backend } = args;
  const { data, indices, segmentIds } = inputs;
  if (data.shape.length < 1) {
    throw new Error(`Data should be at least 1 dimensional but received scalar`);
  }
  if (indices.shape.length !== 1) {
    throw new Error(`Indices should be a vector but received shape
         ${indices.shape}`);
  }
  if (segmentIds.shape.length !== 1) {
    throw new Error(`Segment ids should be a vector but received shape
         ${segmentIds.shape}`);
  }
  if (indices.shape[0] !== segmentIds.shape[0]) {
    throw new Error(`segmentIds and indices should have same size.`);
  }
  const $data = backend.data.get(data.dataId).values;
  const $indices = backend.data.get(indices.dataId).values;
  const $segmentIds = backend.data.get(segmentIds.dataId).values;
  const [outputData, outputDataShape] = sparseSegmentReductionImpl($data, data.shape, data.dtype, $indices, $segmentIds);
  return backend.makeTensorInfo(outputDataShape, data.dtype, outputData);
}
var sparseSegmentSumConfig = {
  kernelName: SparseSegmentSum,
  backendName: "cpu",
  kernelFunc: sparseSegmentSum
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SparseToDense.js
function sparseToDense(args) {
  const { inputs, backend, attrs } = args;
  const { sparseIndices, sparseValues, defaultValue } = inputs;
  const { outputShape } = attrs;
  const { sliceRank, numUpdates, sliceSize, strides, outputSize } = backend_util_exports.calculateShapes(sparseValues, sparseIndices, outputShape);
  const sumDupeIndices = false;
  const indicesBuf = backend.bufferSync(sparseIndices);
  let outBuf;
  switch (sparseValues.dtype) {
    case "bool": {
      const updatesBuf = backend.bufferSync(sparseValues);
      const $defaultValue = Boolean(backend.data.get(defaultValue.dataId).values[0]);
      outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);
      break;
    }
    case "float32": {
      const updatesBuf = backend.bufferSync(sparseValues);
      const $defaultValue = backend.data.get(defaultValue.dataId).values[0];
      outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);
      break;
    }
    case "int32": {
      const updatesBuf = backend.bufferSync(sparseValues);
      const $defaultValue = backend.data.get(defaultValue.dataId).values[0];
      outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);
      break;
    }
    case "string": {
      const updatesBuf = backend.bufferSync(sparseValues);
      const $defaultValue = util_exports.decodeString(backend.data.get(defaultValue.dataId).values[0]);
      outBuf = scatterImpl(indicesBuf, updatesBuf, outputShape, outputSize, sliceSize, numUpdates, sliceRank, strides, $defaultValue, sumDupeIndices);
      break;
    }
    default:
      throw new Error(`Unsupported type ${sparseValues.dtype}`);
  }
  return backend.makeTensorInfo(outputShape, outBuf.dtype, outBuf.values);
}
var sparseToDenseConfig = {
  kernelName: SparseToDense,
  backendName: "cpu",
  kernelFunc: sparseToDense
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/SplitV.js
function splitV(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { numOrSizeSplits, axis } = attrs;
  const $axis = util_exports.parseAxisParam(axis, x.shape)[0];
  const splitSizes = backend_util_exports.prepareSplitSize(x, numOrSizeSplits, $axis);
  const begin = new Array(x.shape.length).fill(0);
  const size = x.shape.slice();
  return splitSizes.map((s) => {
    const sliceSize = [...size];
    sliceSize[$axis] = s;
    const sliceT = slice({ inputs: { x }, backend, attrs: { begin, size: sliceSize } });
    begin[$axis] += s;
    return sliceT;
  });
}
var splitVConfig = {
  kernelName: SplitV,
  backendName: "cpu",
  kernelFunc: splitV
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Square.js
var squareConfig = {
  kernelName: Square,
  backendName: "cpu",
  kernelFunc: ({ inputs, backend }) => {
    const { x } = inputs;
    const cpuBackend = backend;
    assertNotComplex(x, "square");
    const values = cpuBackend.data.get(x.dataId).values;
    const newValues = new Float32Array(values.length);
    for (let i = 0; i < values.length; ++i) {
      const value = values[i];
      newValues[i] = value * value;
    }
    const dataId = cpuBackend.write(newValues, x.shape, x.dtype);
    return { dataId, shape: x.shape, dtype: x.dtype };
  }
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Step.js
var step = unaryKernelFunc(Step, (xi, attrs) => {
  const stepAttrs = attrs;
  if (isNaN(xi)) {
    return NaN;
  } else {
    return xi > 0 ? 1 : stepAttrs.alpha;
  }
});
var stepConfig = {
  kernelName: Step,
  backendName: "cpu",
  kernelFunc: step
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/StridedSlice.js
function stridedSlice(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { begin, end, strides, beginMask, endMask, ellipsisMask, newAxisMask, shrinkAxisMask } = attrs;
  assertNotComplex(x, "stridedSlice");
  const { finalShapeSparse, finalShape, isIdentity, sliceDim0, isSimpleSlice, begin: $begin, end: $end, strides: $strides } = slice_util_exports.sliceInfo(x.shape, begin, end, strides, beginMask, endMask, ellipsisMask, newAxisMask, shrinkAxisMask);
  let result;
  if (isIdentity) {
    result = reshape({ inputs: { x }, backend, attrs: { shape: finalShape } });
  } else if (sliceDim0 || isSimpleSlice) {
    util_exports.assert(x.shape.length >= 1, () => `Input must have rank at least 1, got: ${x.shape.length}`);
    const size = slice_util_exports.computeOutShape($begin, $end, $strides);
    const sliced = slice({ inputs: { x }, backend, attrs: { begin: $begin, size } });
    result = reshape({ inputs: { x: sliced }, backend, attrs: { shape: finalShape } });
    backend.disposeIntermediateTensorInfo(sliced);
  } else {
    const xBuf = backend.bufferSync(x);
    const outBuf = stridedSliceImpl(finalShapeSparse, xBuf, $strides, $begin);
    result = backend.makeTensorInfo(finalShape, outBuf.dtype, outBuf.values);
  }
  return result;
}
var stridedSliceConfig = {
  kernelName: StridedSlice,
  backendName: "cpu",
  kernelFunc: stridedSlice
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/StringNGrams.js
function stringNGrams(args) {
  const { inputs, backend, attrs } = args;
  const { separator, nGramWidths, leftPad, rightPad, padWidth, preserveShortSequences } = attrs;
  const { data, dataSplits } = inputs;
  const $data = backend.data.get(data.dataId).values;
  const $dataSplits = backend.data.get(dataSplits.dataId).values;
  const [nGrams, nGramsSplits] = stringNGramsImpl($data, $dataSplits, separator, nGramWidths, leftPad, rightPad, padWidth, preserveShortSequences);
  return [
    backend.makeTensorInfo([nGrams.length], "string", nGrams),
    backend.makeTensorInfo(dataSplits.shape, "int32", nGramsSplits)
  ];
}
var stringNGramsConfig = {
  kernelName: StringNGrams,
  backendName: "cpu",
  kernelFunc: stringNGrams
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/StringSplit.js
function stringSplit(args) {
  const { inputs, backend, attrs } = args;
  const { skipEmpty } = attrs;
  const { input, delimiter } = inputs;
  if (input.dtype !== "string") {
    throw new Error("Input must be of datatype string");
  }
  if (input.shape.length !== 1) {
    throw new Error(`Input must be a vector, got shape: ${input.shape}`);
  }
  if (delimiter.shape.length !== 0) {
    throw new Error(`Delimiter must be a scalar, got shape: ${delimiter.shape}`);
  }
  const $input = backend.data.get(input.dataId).values;
  const $delimiter = backend.data.get(delimiter.dataId).values[0];
  const [indices, values, shape] = stringSplitImpl($input, $delimiter, skipEmpty);
  const outputSize = values.length;
  return [
    backend.makeTensorInfo([outputSize, 2], "int32", indices),
    backend.makeTensorInfo([outputSize], "string", values),
    backend.makeTensorInfo([2], "int32", new Int32Array(shape))
  ];
}
var stringSplitConfig = {
  kernelName: StringSplit,
  backendName: "cpu",
  kernelFunc: stringSplit
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/StringToHashBucketFast.js
function stringToHashBucketFast(args) {
  const { inputs, backend, attrs } = args;
  const { numBuckets } = attrs;
  const { input } = inputs;
  if (input.dtype !== "string") {
    throw new Error("Input must be of datatype string");
  }
  if (numBuckets <= 0) {
    throw new Error(`Number of buckets must be at least 1`);
  }
  const $input = backend.data.get(input.dataId).values;
  const output = stringToHashBucketFastImpl($input, numBuckets);
  return backend.makeTensorInfo(input.shape, "int32", output);
}
var stringToHashBucketFastConfig = {
  kernelName: StringToHashBucketFast,
  backendName: "cpu",
  kernelFunc: stringToHashBucketFast
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Tan.js
var tan = unaryKernelFunc(Tan, (xi) => Math.tan(xi));
var tanConfig = {
  kernelName: Tan,
  backendName: "cpu",
  kernelFunc: tan
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Tanh.js
var tanh = unaryKernelFunc(Tanh, (xi) => Math.tanh(xi));
var tanhConfig = {
  kernelName: Tanh,
  backendName: "cpu",
  kernelFunc: tanh
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/TensorScatterUpdate.js
function tensorScatterUpdate(args) {
  const { inputs, backend } = args;
  const { tensor, indices, updates } = inputs;
  const { sliceRank, numUpdates, sliceSize, strides, outputSize } = backend_util_exports.calculateShapes(updates, indices, tensor.shape);
  const sumDupeIndices = false;
  const indicesBuf = backend.bufferSync(indices);
  const updatesBuf = backend.bufferSync(updates);
  const tensorBuf = backend.bufferSync(tensor);
  const outBuf = scatterImpl(indicesBuf, updatesBuf, tensor.shape, outputSize, sliceSize, numUpdates, sliceRank, strides, tensorBuf, sumDupeIndices);
  return backend.makeTensorInfo(tensor.shape, outBuf.dtype, outBuf.values);
}
var tensorScatterUpdateConfig = {
  kernelName: TensorScatterUpdate,
  backendName: "cpu",
  kernelFunc: tensorScatterUpdate
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Tile.js
function tile(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { reps } = attrs;
  assertNotComplex(x, "tile");
  const outBuf = tileImpl(backend.bufferSync(x), reps);
  return backend.makeTensorInfo(outBuf.shape, outBuf.dtype, outBuf.values);
}
var tileConfig = {
  kernelName: Tile,
  backendName: "cpu",
  kernelFunc: tile
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/TopK.js
function topK(args) {
  const { inputs, backend, attrs } = args;
  const { x } = inputs;
  const { k, sorted } = attrs;
  assertNotComplex(x, "topk");
  const xVals = backend.data.get(x.dataId).values;
  const [allTopKVals, allTopKIndices] = topKImpl(xVals, x.shape, x.dtype, k, sorted);
  return [
    backend.makeTensorInfo(allTopKVals.shape, allTopKVals.dtype, allTopKVals.values),
    backend.makeTensorInfo(allTopKIndices.shape, allTopKIndices.dtype, allTopKIndices.values)
  ];
}
var topKConfig = {
  kernelName: TopK,
  backendName: "cpu",
  kernelFunc: topK
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Transform.js
function transform(args) {
  const { inputs, attrs, backend } = args;
  const { image, transforms } = inputs;
  const { interpolation, fillMode, fillValue, outputShape } = attrs;
  const [batch, imageHeight, imageWidth, numChannels] = image.shape;
  const [outHeight, outWidth] = outputShape != null ? outputShape : [imageHeight, imageWidth];
  const outShape = [batch, outHeight, outWidth, numChannels];
  const inStrides = util_exports.computeStrides(image.shape);
  const batchInStride = inStrides[0];
  const rowInStride = inStrides[1];
  const colInStride = inStrides[2];
  const outStrides = util_exports.computeStrides(outShape);
  const batchOutStride = outStrides[0];
  const rowOutStride = outStrides[1];
  const colOutStride = outStrides[2];
  const outVals = util_exports.getTypedArrayFromDType(image.dtype, util_exports.sizeFromShape(outShape));
  outVals.fill(fillValue);
  const imageVals = backend.data.get(image.dataId).values;
  const transformVals = backend.data.get(transforms.dataId).values;
  for (let b = 0; b < batch; ++b) {
    const transform2 = transforms.shape[0] === 1 ? transformVals : transformVals.subarray(b * 8, b * 8 + 8);
    for (let outY = 0; outY < outHeight; ++outY) {
      for (let outX = 0; outX < outWidth; ++outX) {
        for (let channel = 0; channel < numChannels; ++channel) {
          let val;
          const projection = transform2[6] * outX + transform2[7] * outY + 1;
          if (projection === 0) {
            continue;
          }
          const inX = (transform2[0] * outX + transform2[1] * outY + transform2[2]) / projection;
          const inY = (transform2[3] * outX + transform2[4] * outY + transform2[5]) / projection;
          const x = mapCoord(inX, imageWidth, fillMode);
          const y = mapCoord(inY, imageHeight, fillMode);
          switch (interpolation) {
            case "nearest":
              val = nearestInterpolation(imageVals, imageHeight, imageWidth, batchInStride, rowInStride, colInStride, b, y, x, channel, fillValue);
              break;
            case "bilinear":
              val = bilinearInterpolation(imageVals, imageHeight, imageWidth, batchInStride, rowInStride, colInStride, b, y, x, channel, fillValue);
              break;
            default:
              throw new Error(`Error in Transform: Expect 'nearest' or 'bilinear', but got ${interpolation}`);
          }
          const ind = b * batchOutStride + outY * rowOutStride + outX * colOutStride + channel;
          outVals[ind] = val;
        }
      }
    }
    return backend.makeTensorInfo(outShape, image.dtype, outVals);
  }
  const dataId = backend.write(outVals, outShape, image.dtype);
  return { dataId, shape: image.shape, dtype: image.dtype };
}
var transformConfig = {
  kernelName: Transform,
  backendName: "cpu",
  kernelFunc: transform
};
function mapCoord(outCoord, len, mode) {
  switch (mode) {
    case "reflect":
      return mapCoordReflect(outCoord, len);
    case "wrap":
      return mapCoordWrap(outCoord, len);
    case "nearest":
      return mapCoordNearest(outCoord, len);
    case "constant":
    default:
      return mapCoordConstant(outCoord, len);
  }
}
function mapCoordReflect(outCoord, len) {
  let inCoord = outCoord;
  if (inCoord < 0) {
    if (len <= 1) {
      inCoord = 0;
    } else {
      const sz2 = 2 * len;
      if (inCoord < sz2) {
        inCoord = sz2 * Math.trunc(-inCoord / sz2) + inCoord;
      }
      inCoord = inCoord < -len ? inCoord + sz2 : -inCoord - 1;
    }
  } else if (inCoord > len - 1) {
    if (len <= 1) {
      inCoord = 0;
    } else {
      const sz2 = 2 * len;
      inCoord -= sz2 * Math.trunc(inCoord / sz2);
      if (inCoord >= len) {
        inCoord = sz2 - inCoord - 1;
      }
    }
  }
  return util_exports.clamp(0, inCoord, len - 1);
}
function mapCoordWrap(outCoord, len) {
  let inCoord = outCoord;
  if (inCoord < 0) {
    if (len <= 1) {
      inCoord = 0;
    } else {
      const sz = len - 1;
      inCoord += len * (Math.trunc(-inCoord / sz) + 1);
    }
  } else if (inCoord > len - 1) {
    if (len <= 1) {
      inCoord = 0;
    } else {
      const sz = len - 1;
      inCoord -= len * Math.trunc(inCoord / sz);
    }
  }
  return util_exports.clamp(0, inCoord, len - 1);
}
function mapCoordConstant(outCoord, len) {
  return outCoord;
}
function mapCoordNearest(outCoord, len) {
  return util_exports.clamp(0, outCoord, len - 1);
}
function readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, y, x, channel, fillValue) {
  const ind = batch * batchStride + y * rowStride + x * colStride + channel;
  if (0 <= y && y < imageHeight && 0 <= x && x < imageWidth) {
    return imageVals[ind];
  } else {
    return fillValue;
  }
}
function nearestInterpolation(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, y, x, channel, fillValue) {
  const $y = Math.round(y);
  const $x = Math.round(x);
  return readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, $y, $x, channel, fillValue);
}
function bilinearInterpolation(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, y, x, channel, fillValue) {
  const yFloor = Math.floor(y);
  const xFloor = Math.floor(x);
  const yCeil = yFloor + 1;
  const xCeil = xFloor + 1;
  const valueYFloor = (xCeil - x) * readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yFloor, xFloor, channel, fillValue) + (x - xFloor) * readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yFloor, xCeil, channel, fillValue);
  const valueYCeil = (xCeil - x) * readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yCeil, xFloor, channel, fillValue) + (x - xFloor) * readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yCeil, xCeil, channel, fillValue);
  return (yCeil - y) * valueYFloor + (y - yFloor) * valueYCeil;
}

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Unique.js
function unique(args) {
  const { inputs, attrs, backend } = args;
  const { axis } = attrs;
  const { x } = inputs;
  assertNotComplex(x, "unique");
  const values = backend.data.get(x.dataId).values;
  const { outputValues, outputShape, indices } = uniqueImpl(values, axis, x.shape, x.dtype);
  return [
    backend.makeTensorInfo(outputShape, x.dtype, outputValues),
    backend.makeTensorInfo([indices.length], "int32", indices)
  ];
}
var uniqueConfig = {
  kernelName: Unique,
  backendName: "cpu",
  kernelFunc: unique
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/Unpack.js
function unpack(args) {
  const { inputs, backend, attrs } = args;
  const { value } = inputs;
  let { axis } = attrs;
  if (axis < 0) {
    axis += value.shape.length;
  }
  const valueRank = value.shape.length;
  const num = value.shape[axis];
  const outShape = new Array(valueRank - 1);
  let outIndex = 0;
  for (let i = 0; i < valueRank; i++) {
    if (i !== axis) {
      outShape[outIndex++] = value.shape[i];
    }
  }
  const begin = new Array(valueRank).fill(0);
  const size = value.shape.slice();
  size[axis] = 1;
  const res = new Array(num);
  for (let i = 0; i < res.length; i++) {
    begin[axis] = i;
    const tempRes = slice({ inputs: { x: value }, backend, attrs: { begin, size } });
    res[i] = reshape({ inputs: { x: tempRes }, backend, attrs: { shape: outShape } });
    backend.disposeIntermediateTensorInfo(tempRes);
  }
  return res;
}
var unpackConfig = {
  kernelName: Unpack,
  backendName: "cpu",
  kernelFunc: unpack
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/kernels/UnsortedSegmentSum.js
function unsortedSegmentSum(args) {
  const { inputs, backend, attrs } = args;
  const { x, segmentIds } = inputs;
  const { numSegments } = attrs;
  assertNotComplex(x, "unsortedSegmentSum");
  const xRank = x.shape.length;
  const segmentIdsRank = segmentIds.shape.length;
  const res = [];
  const intermediates = [];
  const numIters = xRank - segmentIdsRank;
  let $segmentIds = segmentIds;
  for (let i = 0; i < numIters; ++i) {
    const expanded = expandDims({ inputs: { input: $segmentIds }, backend, attrs: { dim: i + 1 } });
    $segmentIds = expanded;
    intermediates.push(expanded);
  }
  for (let i = 0; i < numSegments; ++i) {
    const scalarValue = util_exports.createScalarValue(i, "int32");
    const segmentId = backend.makeTensorInfo([], "int32", scalarValue);
    const mask = equal({ inputs: { a: segmentId, b: $segmentIds }, backend });
    const maskCasted = cast({ inputs: { x: mask }, backend, attrs: { dtype: "float32" } });
    const mul = multiply({ inputs: { a: maskCasted, b: x }, backend });
    const sumTensorInfo = sum({ inputs: { x: mul }, backend, attrs: { axis: 0, keepDims: false } });
    res.push(sumTensorInfo);
    intermediates.push(segmentId);
    intermediates.push(mask);
    intermediates.push(maskCasted);
    intermediates.push(mul);
    intermediates.push(sumTensorInfo);
  }
  const result = pack({ inputs: res, backend, attrs: { axis: 0 } });
  intermediates.forEach((t) => backend.disposeIntermediateTensorInfo(t));
  return result;
}
var unsortedSegmentSumConfig = {
  kernelName: UnsortedSegmentSum,
  backendName: "cpu",
  kernelFunc: unsortedSegmentSum
};

// node_modules/@tensorflow/tfjs-backend-cpu/dist/register_all_kernels.js
var kernelConfigs = [
  _fusedMatMulConfig,
  absConfig,
  acosConfig,
  acoshConfig,
  addConfig,
  addNConfig,
  allConfig,
  anyConfig,
  argMaxConfig,
  argMinConfig,
  asinConfig,
  asinhConfig,
  atanConfig,
  atan2Config,
  atanhConfig,
  avgPoolConfig,
  avgPool3DConfig,
  avgPool3DGradConfig,
  avgPoolGradConfig,
  batchMatMulConfig,
  batchNormConfig,
  batchToSpaceNDConfig,
  bincountConfig,
  bitwiseAndConfig,
  broadcastArgsConfig,
  castConfig,
  ceilConfig,
  clipByValueConfig,
  complexConfig,
  complexAbsConfig,
  concatConfig,
  conv2DConfig,
  conv2DBackpropFilterConfig,
  conv2DBackpropInputConfig,
  conv3DConfig,
  conv3DBackpropFilterV2Config,
  conv3DBackpropInputV2Config,
  cosConfig,
  coshConfig,
  cropAndResizeConfig,
  cumprodConfig,
  cumsumConfig,
  denseBincountConfig,
  depthToSpaceConfig,
  depthwiseConv2dNativeConfig,
  depthwiseConv2dNativeBackpropFilterConfig,
  depthwiseConv2dNativeBackpropInputConfig,
  diagConfig,
  dilation2DConfig,
  dilation2DBackpropFilterConfig,
  dilation2DBackpropInputConfig,
  drawConfig,
  einsumConfig,
  eluConfig,
  eluGradConfig,
  equalConfig,
  erfConfig,
  expConfig,
  expandDimsConfig,
  expm1Config,
  fftConfig,
  fillConfig,
  flipLeftRightConfig,
  floorConfig,
  floorDivConfig,
  fusedConv2DConfig,
  fusedDepthwiseConv2DConfig,
  gatherNdConfig,
  gatherV2Config,
  greaterConfig,
  greaterEqualConfig,
  identityConfig,
  ifftConfig,
  imagConfig,
  isFiniteConfig,
  isInfConfig,
  isNaNConfig,
  leakyReluConfig,
  lessConfig,
  lessEqualConfig,
  linSpaceConfig,
  logConfig,
  log1pConfig,
  logicalAndConfig,
  logicalNotConfig,
  logicalOrConfig,
  LRNConfig,
  LRNGradConfig,
  maxConfig,
  maximumConfig,
  maxPoolConfig,
  maxPool3DConfig,
  maxPool3DGradConfig,
  maxPoolGradConfig,
  maxPoolWithArgmaxConfig,
  meanConfig,
  minConfig,
  minimumConfig,
  mirrorPadConfig,
  modConfig,
  multinomialConfig,
  multiplyConfig,
  negConfig,
  nonMaxSuppressionV3Config,
  nonMaxSuppressionV4Config,
  nonMaxSuppressionV5Config,
  notEqualConfig,
  oneHotConfig,
  onesLikeConfig,
  packConfig,
  padV2Config,
  powConfig,
  preluConfig,
  prodConfig,
  raggedGatherConfig,
  raggedRangeConfig,
  raggedTensorToTensorConfig,
  rangeConfig,
  realConfig,
  realDivConfig,
  reciprocalConfig,
  reluConfig,
  relu6Config,
  reshapeConfig,
  resizeBilinearConfig,
  resizeBilinearGradConfig,
  resizeNearestNeighborConfig,
  resizeNearestNeighborGradConfig,
  reverseConfig,
  rotateWithOffsetConfig,
  roundConfig,
  rsqrtConfig,
  scatterNdConfig,
  searchSortedConfig,
  selectConfig,
  seluConfig,
  sigmoidConfig,
  signConfig,
  sinConfig,
  sinhConfig,
  sliceConfig,
  softmaxConfig,
  softplusConfig,
  spaceToBatchNDConfig,
  sparseFillEmptyRowsConfig,
  sparseReshapeConfig,
  sparseSegmentMeanConfig,
  sparseSegmentSumConfig,
  sparseToDenseConfig,
  splitVConfig,
  sqrtConfig,
  squareConfig,
  squaredDifferenceConfig,
  staticRegexReplaceConfig,
  stepConfig,
  stridedSliceConfig,
  stringNGramsConfig,
  stringSplitConfig,
  stringToHashBucketFastConfig,
  subConfig,
  sumConfig,
  tanConfig,
  tanhConfig,
  tensorScatterUpdateConfig,
  tileConfig,
  topKConfig,
  transformConfig,
  transposeConfig,
  uniqueConfig,
  unpackConfig,
  unsortedSegmentSumConfig,
  zerosLikeConfig
];
for (const kernelConfig of kernelConfigs) {
  registerKernel(kernelConfig);
}
export {
  MathBackendCPU,
  shared_exports as shared,
  version as version_cpu
};
/*! Bundled license information:

@tensorflow/tfjs-backend-cpu/dist/backend_cpu.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/BroadcastArgs.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Einsum.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SparseFillEmptyRows.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SparseReshape.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SparseSegmentMean.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SparseSegmentSum.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/StringNGrams.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/StringSplit.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/StringToHashBucketFast.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Transform.js:
  (**
   * @license
   * Copyright 2021 Google LLC. All Rights Reserved.
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)

@tensorflow/tfjs-backend-cpu/dist/version.js:
  (** @license See the LICENSE file. *)

@tensorflow/tfjs-backend-cpu/dist/base.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LeakyRelu.js:
@tensorflow/tfjs-backend-cpu/dist/utils/fused_utils.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Reshape.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/AddN.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/All.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Any.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ArgMax.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ArgMin.js:
@tensorflow/tfjs-backend-cpu/dist/utils/pool_utils.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPool.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPool3D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPool3DGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/AvgPoolGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/BatchNorm.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/BatchToSpaceND.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Bincount.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Imag.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Concat.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Conv2D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Conv2DBackpropFilter.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Conv2DBackpropInput.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Conv3D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Conv3DBackpropFilterV2.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Conv3DBackpropInputV2.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Cos.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/CropAndResize.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Cumsum.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/DenseBincount.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/DepthToSpace.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/DepthwiseConv2dNative.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/DepthwiseConv2dNativeBackpropFilter.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/DepthwiseConv2dNativeBackpropInput.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Diag.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Dilation2D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Dilation2DBackpropFilter.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Dilation2DBackpropInput.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Sum.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/EluGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ExpandDims.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/RealDiv.js:
@tensorflow/tfjs-backend-cpu/dist/utils/fft_utils.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/FFT.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Fill.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/FlipLeftRight.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/FusedConv2D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/FusedDepthwiseConv2D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/GatherNd.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/GatherV2.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/IFFT.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LinSpace.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LogicalAnd.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LogicalOr.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LRN.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LRNGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Max.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPool.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPool3D.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPool3DGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPoolGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPoolWithArgmax_impl.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MaxPoolWithArgmax.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Mean.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Min.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/MirrorPad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Mod.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Softmax.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Multinomial.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/NonMaxSuppressionV3.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/NonMaxSuppressionV4.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/OneHot.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ZerosLike.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/OnesLike.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Pack.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/PadV2.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Pow.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Range.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeBilinear.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeBilinearGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeNearestNeighbor.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ResizeNearestNeighborGrad.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Reverse.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/RotateWithOffset.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ScatterNd.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Select.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SpaceToBatchND.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SparseToDense.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SplitV.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/StridedSlice.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Tile.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/TopK.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Unpack.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/UnsortedSegmentSum.js:
@tensorflow/tfjs-backend-cpu/dist/register_all_kernels.js:
@tensorflow/tfjs-backend-cpu/dist/index.js:
  (**
   * @license
   * Copyright 2020 Google LLC. All Rights Reserved.
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)

@tensorflow/tfjs-backend-cpu/dist/kernels/Elu.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Prelu.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Relu.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Relu6.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/BatchMatMul.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/_FusedMatMul.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Acos.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Acosh.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Asin.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Asinh.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Atan.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Atan2.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Atanh.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ClipByValue.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/ComplexAbs.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Cosh.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Erf.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/IsFinite.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/IsInf.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/IsNaN.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Log1p.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/LogicalNot.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Reciprocal.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Round.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Selu.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Sign.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Sin.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Sinh.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Softplus.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Step.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Tan.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Tanh.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Unique.js:
  (**
   * @license
   * Copyright 2020 Google LLC. All Rights Reserved.
   * Licensed under the Apache License, Version 2.0 (the License);
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an AS IS BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)

@tensorflow/tfjs-backend-cpu/dist/kernels/Cumprod.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/RaggedGather.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/RaggedTensorToTensor.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SearchSorted_impl.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/SearchSorted.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/TensorScatterUpdate.js:
  (**
   * @license
   * Copyright 2022 Google LLC. All Rights Reserved.
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)

@tensorflow/tfjs-backend-cpu/dist/kernels/Draw.js:
  (**
   * @license
   * Copyright 2023 Google LLC.
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)

@tensorflow/tfjs-backend-cpu/dist/kernels/NonMaxSuppressionV5.js:
@tensorflow/tfjs-backend-cpu/dist/kernels/Square.js:
  (**
   * @license
   * Copyright 2019 Google LLC. All Rights Reserved.
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)

@tensorflow/tfjs-backend-cpu/dist/kernels/RaggedRange.js:
  (**
   * @license
   * Copyright 2022 Google LLC.
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   * =============================================================================
   *)
*/
//# sourceMappingURL=@tensorflow_tfjs-backend-cpu.js.map
