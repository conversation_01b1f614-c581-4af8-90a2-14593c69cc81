import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';

export function validateConditionObject(
  condition: Types.MinimalCondition,
  data: Record<string, any> = {},
) {
  const resolveData = (obj: Types.MinimalConditionDataObject) =>
    obj?.category === 'answer' ? get(data, `${obj?.value}`) : obj?.value;
  const a = resolveData(condition.initial_data);
  const b = resolveData(condition.expected_data);

  const validateHas = (x, y) => {
    if (Array.isArray(x)) {
      return x.includes(y);
    }
    if (typeof x === 'object') {
      return y in x;
    }
    return `${x}`.includes(`${y}`);
  };

  const validateFilled = x => {
    if (typeof x === 'object') {
      return !isEmpty(x);
    }
    if (x === false) {
      return true;
    }
    if (x === 0) {
      return true;
    }
    return !!x;
  };

  const OP_MAP: Record<Types.DEGuardOp, () => boolean> = {
    '==': () => a === b,
    '!=': () => a !== b,
    '<': () => a < b,
    '>': () => a > b,
    '<=': () => a <= b,
    '>=': () => a >= b,
    filled: () => validateFilled(a),
    empty: () => !validateFilled(a),
    starts_with: () => `${a}`.startsWith(`${b}`),
    ends_with: () => `${a}`.endsWith(`${b}`),
    has: () => validateHas(a, b),
    in: () => validateHas(b, a),
    not_has: () => !validateHas(a, b),
    not_in: () => !validateHas(b, a),
    is: () => a === b,
    is_not: () => a !== b,
  };

  try {
    const func = OP_MAP[condition.op];
    if (!func) {
      throw new Error(`OP '${condition.op}' Not implemented`);
    }
    if (condition.not) {
      return !func();
    }
    return func();
  } catch (err) {
    console.error(err);
    return false;
  }
}

export function validateConditionGroups(
  groups: Types.DEGuardGroup[],
  data: Record<string, any> = {},
) {
  const groupsPassList = groups.map(g => {
    const childrenPassList = g.children.map(c => validateConditionObject(c, data));
    if (g.link_operation === 'or') {
      return childrenPassList.some(x => x);
    }
    return childrenPassList.every(x => x);
  });

  return groupsPassList.every(x => x);
}
