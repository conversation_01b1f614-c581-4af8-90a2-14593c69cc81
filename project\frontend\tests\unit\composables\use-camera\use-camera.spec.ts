/// <reference types="vite/client" />
import cloneDeep from 'lodash/cloneDeep';
import snakeCase from 'lodash/snakeCase';
import { afterAll, describe, expect, test, vi } from 'vitest';

import { type MediaDeviceInfoDetailed, useCamera } from '@ekyc/composables/use-camera';

type CameraLog = {
  user_agent: string;
  slug: string;
  created_at: string;
  log_camera: {
    devices: MediaDeviceInfoDetailed[];
  };
};

function setMockGetDetailedVideoDevices(
  detailedVideoDevices: MediaDeviceInfoDetailed[],
  facingMode = 'environment',
) {
  const devices = cloneDeep(detailedVideoDevices);
  window.navigator = {
    ...navigator,
    mediaDevices: {
      ...navigator.mediaDevices,
      getSupportedConstraints: vi.fn(),
      enumerateDevices: () => devices.map(d => d.device) as any,
      getUserMedia: (constraints: any) => {
        // Find device by deviceId or by facingMode
        const device =
          devices.find(dev => dev.device.deviceId === constraints.video?.deviceId?.exact) ||
          devices.find(dev => dev.tracks[0]?.capabilities.facingMode?.includes(facingMode));

        return {
          getTracks: () =>
            device.tracks.map(track => ({
              ...track,
              getSettings: () => track.settings,
              getCapabilities: () => track.capabilities,
              getConstraints: () => track.constraints,
              applyConstraints: vi.fn(),
              stop: vi.fn(),
            })),
        } as any;
      },
    },
  };
}

function getMockVideo(): HTMLVideoElement {
  return {
    src: null,
    srcObject: null,
    readyState: 4,
    play: vi.fn(),
    videoWidth: null,
    videoHeight: null,
    width: null,
    height: null,
    clientWidth: null,
    clientHeight: null,
    offsetWidth: null,
    offsetHeight: null,
    autoplay: null,
    getVideoPlaybackQuality: vi.fn(),
    playsInline: null,
    poster: null,
    buffered: null,
    controls: null,
    crossOrigin: null,
    currentSrc: null,
    currentTime: null,
    defaultMuted: null,
    defaultPlaybackRate: null,
    disableRemotePlayback: null,
    duration: null,
    ended: null,
    error: null,
    loop: null,
    mediaKeys: null,
    muted: null,
    paused: null,
    playbackRate: null,
    played: null,
    preload: null,
    preservesPitch: null,
    networkState: null,
    remote: null,
    seekable: null,
    seeking: null,
    volume: null,
    style: null,
  } as unknown as HTMLVideoElement;
}

function normalizeDeviceLog(
  log: CameraLog['log_camera'],
  facingMode: string,
): CameraLog['log_camera'] {
  return {
    ...log,
    devices: log.devices.map((device, i) => ({
      ...device,
      device: { ...device.device, deviceId: `$D${i}`, groupId: `$G${i}` },
      tracks: (device.tracks || [device.device]).map((track, j) => ({
        ...track,
        id: `$T${j}`,
        settings: { ...track.settings, deviceId: `$D${i}` },
        constraints: { ...track.constraints, deviceId: { exact: `$D${i}` } },
        capabilities: {
          ...track.capabilities,
          deviceId: `$D${i}`,
          facingMode: track.capabilities?.facingMode || [facingMode],
        },
      })),
    })),
  };
}

type Entries = [string, () => Promise<{ default: CameraLog[] }>][];

describe('use-camera', () => {
  const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => undefined);
  const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => undefined);

  afterAll(() => {
    mockConsoleLog.mockRestore();
    mockConsoleError.mockRestore();
  });

  const generateTestsForDir = (entries: Entries, facingMode: 'environment' | 'user') => {
    const cameraType = facingMode === 'environment' ? 'Back' : 'Front';

    for (const [filePath, importFn] of entries) {
      describe(`${cameraType} Camera - ${filePath}`, async () => {
        const rows = (await importFn()).default;

        const limitedRows = rows.slice(0, 1);

        if (limitedRows.length === 0) {
          test.skip(`No valid test data found in ${filePath}`, () => {});
          return;
        }

        test.each(limitedRows)(
          `slug: $slug | UA: $user_agent`,
          async row => {
            const logData =
              typeof row.log_camera === 'string' ? JSON.parse(row.log_camera) : row.log_camera;

            const normalizedLog = normalizeDeviceLog(logData, facingMode);
            setMockGetDetailedVideoDevices(normalizedLog.devices, facingMode);

            const cameraRef = ref(getMockVideo());
            const workingConstraints = ref({});
            const { loadCamera } = useCamera({
              videoRef: cameraRef,
              highQuality: false,
              startFacingMode: facingMode,
              overrideConstraints: {},
              workingConstraints: workingConstraints,
            });

            const result = await loadCamera();

            const snapshot = result.logs.attempts.map(att => ({
              deviceMethod: att.deviceMethod,
              resolutionMethod: att.resolutionMethod,
              constraints: att.constraints,
              streamTracks: att.streamTracks,
              reConstraints: att.video?.reConstraints,
              reFocused: att.video?.reFocused,
              reIso: att.video?.reIso,
            }));

            const ua = snakeCase(row.user_agent).split('_like')[0].slice(0, 100);
            const subfolder = facingMode === 'user' ? 'front' : 'back';
            const snapshotFilename = `./__snapshots__/use-camera/${subfolder}/${ua}_${row.slug}.snap.json`;

            await expect(JSON.stringify(snapshot, null, 2)).toMatchFileSnapshot(snapshotFilename);
          },
          30000,
        );
      });
    }
  };

  generateTestsForDir(Object.entries(import.meta.glob('./mock_back/*')) as Entries, 'environment');
  generateTestsForDir(Object.entries(import.meta.glob('./mock_front/*')) as Entries, 'user');
});
