{"name": "dynamic-form", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "watch": "vite", "lint": "eslint --fix", "test": "yarn test:unit --dom", "test:unit": "vitest", "test:unit:ui": "vitest --ui --config=vitest.config.ui.ts", "test:storybook": "test-storybook --url http://localhost:9009/", "storybook": "storybook dev -p 9009", "visualize": "npx vite-bundle-visualizer", "generate:json-schema:form": "npx ts-json-schema-generator --path src/types/index.d.ts  -o ./src/helpers/json-schemas/form.schema.json --type Types.ISchema --no-type-check", "generate:json-schema:page": "npx ts-json-schema-generator --path src/types/index.d.ts  -o ./src/helpers/json-schemas/page.schema.json --type Types.ISchemaPage --no-type-check", "postinstall-now-disabled": "yarn workspaces run setup-local"}, "workspaces": {"packages": ["packages/*"], "nohoist": ["@creditok/dynamic-form-flow/**"]}, "dependencies": {"@creditok/dynamic-form-core": "^1.0.0", "@creditok/dynamic-form-helpers": "^1.0.8", "@creditok/ekyc": "^0.3.24", "@gtm-support/vue2-gtm": "^1.3.0", "@guolao/vue-monaco-editor": "^1.5.4", "@mdi/font": "^7.0.96", "@popperjs/core": "^2.11.8", "@sentry/vite-plugin": "^4.0.2", "@sentry/vue": "^9.43.0", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-font-family": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/vue-2": "2.11.5", "@vueuse/core": "~11.3.0", "@vueuse/integrations": "~11.3.0", "apexcharts": "^3.41.0", "axios": "^1.11.0", "buefy": "^0.9.29", "bulma-o-steps": "^1.1.0", "core-js": "^3.22.4", "crypto-js": "^4.2.0", "date-fns": "^2.29.1", "date-fns-tz": "^1.3.6", "dompurify": "^3.2.3", "fuse.js": "^6.6.2", "jsondiffpatch": "^0.6.0", "libphonenumber-js": "^1.10.37", "lodash": "^4.17.21", "masonry-layout": "^4.2.2", "normalize-url": "^8.0.0", "pdfjs-dist": "2.12.313", "pinia": "^2.3.1", "register-service-worker": "^1.7.2", "signature_pad": "^4.1.7", "universal-cookie": "^8.0.1", "url-pattern": "^1.0.3", "v-calendar": "^2.4.2", "vue": "^2.7.16", "vue-audio-recorder": "^3.0.1", "vue-color": "^2.8.1", "vue-easy-lightbox": "^0.20.0", "vue-i18n": "^8.28.2", "vue-imask": "^6.6.1", "vue-recaptcha": "^1.3.0", "vue-router": "^3.6.5", "vuedraggable": "^2.24.3", "zod": "^3.24.1"}, "devDependencies": {"@rollup/plugin-dsv": "^3.0.5", "@storybook/addon-designs": "^7.0.9", "@storybook/addon-essentials": "^7.6.20", "@storybook/addon-interactions": "^7.6.20", "@storybook/addon-links": "^7.6.20", "@storybook/addon-storysource": "^7.6.20", "@storybook/addon-themes": "^7.6.20", "@storybook/test": "^7.6.20", "@storybook/test-runner": "~0.19.1", "@storybook/vue": "^7.6.17", "@storybook/vue-vite": "^7.6.17", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.2.0", "@types/masonry-layout": "^4.2.8", "@types/node": "^24.3.0", "@types/pendo-io-browser": "^2.19.1", "@types/webpack-chain": "^5.2.0", "@vitejs/plugin-legacy": "^7.2.1", "@vitejs/plugin-vue2": "^2.3.3", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue-macros/volar": "^0.30.15", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-airbnb-with-typescript": "^8.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "~1.3.6", "autoprefixer": "~9.8.8", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^10.0.0", "cssnano": "~4.1.11", "eslint": "^9.32.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-vue": "^10.3.0", "happy-dom": "^18.0.1", "monaco-editor": "^0.52.2", "msw": "^2.10.4", "msw-storybook-addon": "^2.0.5", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.2.4", "postcss-scss": "^4.0.9", "postcss-utilities": "^0.8.4", "prettier": "^3.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "sass": "~1.78.0", "sass-loader": "~14.2.1", "storybook": "^7.6.20", "svgo-loader": "^4.0.0", "tailwindcss": "^3.4.17", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.9.2", "typescript-strict-plugin": "^2.4.4", "unplugin-auto-import": "^20.1.0", "unplugin-vue-define-options": "^1.5.5", "vite": "^7.1.3", "vite-plugin-checker": "^0.10.3", "vite-plugin-circular-dependency": "^0.5.0", "vitest": "^3.2.4", "vitest-canvas-mock": "^0.3.3", "vue-eslint-parser": "^10.2.0", "vue-template-compiler": "^2.7.16"}, "resolutions": {"**/vue-demi": "*", "jackspeak": "2.1.1"}, "msw": {"workerDirectory": ["public"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}