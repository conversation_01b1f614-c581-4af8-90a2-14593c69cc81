{"version": 3, "sources": ["../../lodash/_baseExtremum.js"], "sourcesContent": ["var isSymbol = require('./isSymbol');\n\n/**\n * The base implementation of methods like `_.max` and `_.min` which accepts a\n * `comparator` to determine the extremum value.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The iteratee invoked per iteration.\n * @param {Function} comparator The comparator used to compare values.\n * @returns {*} Returns the extremum value.\n */\nfunction baseExtremum(array, iteratee, comparator) {\n  var index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var value = array[index],\n        current = iteratee(value);\n\n    if (current != null && (computed === undefined\n          ? (current === current && !isSymbol(current))\n          : comparator(current, computed)\n        )) {\n      var computed = current,\n          result = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseExtremum;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAYf,aAAS,aAAa,OAAO,UAAU,YAAY;AACjD,UAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,MAAM,KAAK,GACnB,UAAU,SAAS,KAAK;AAE5B,YAAI,WAAW,SAAS,aAAa,SAC5B,YAAY,WAAW,CAAC,SAAS,OAAO,IACzC,WAAW,SAAS,QAAQ,IAC7B;AACL,cAAI,WAAW,SACX,SAAS;AAAA,QACf;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}