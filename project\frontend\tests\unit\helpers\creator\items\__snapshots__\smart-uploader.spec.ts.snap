// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Smart Uploader (builder) > should build schema to match the previous snapshot, locale=en 1`] = `
{
  "builder": {
    "type": "smart_uploader",
  },
  "configs": {
    "file_type": [
      "jpeg",
      "jpg",
      "png",
      "pdf",
    ],
    "max_file_count": 100,
    "max_file_size": 12000000,
    "max_page_count": 20,
  },
  "display": {
    "hide_label": true,
    "label": "Upload your document here",
    "show_file_type": true,
    "show_max_file_count": true,
    "show_max_file_size": true,
    "uploader_style": "dropzone",
  },
  "extract_verify_fields": [],
  "extract_verify_tables": [],
  "layout": "UploaderWrapper",
  "match_keywords": [],
  "match_level": 80,
  "match_type": "all",
  "name": undefined,
  "override_images": {
    "add_file": "https://cdn.uppass.io/images/verifio/element-preview/uploader-sample-file.jpg",
  },
  "override_messages": {
    "dropzone": "Upload your document here",
    "file_type": "Only JPG, PNG, PDF file type is supported",
    "instruction": "<p>Please upload documents that meet your requirements.<br><br>1. Only JPG, PNG, PDF file type is support<br>2. Can only upload 1 document</p>",
  },
  "type": "SmartUploader",
  "validator_messages": {
    "required": "Please upload your file",
  },
  "validator_rule": undefined,
  "visible": undefined,
  "visible_flag_invert": undefined,
}
`;

exports[`Smart Uploader (builder) > should build schema to match the previous snapshot, locale=th 1`] = `
{
  "builder": {
    "type": "smart_uploader",
  },
  "configs": {
    "file_type": [
      "jpeg",
      "jpg",
      "png",
      "pdf",
    ],
    "max_file_count": 100,
    "max_file_size": 12000000,
    "max_page_count": 20,
  },
  "display": {
    "hide_label": true,
    "label": "อัปโหลดเอกสารของคุณที่นี่",
    "show_file_type": true,
    "show_max_file_count": true,
    "show_max_file_size": true,
    "uploader_style": "dropzone",
  },
  "extract_verify_fields": [],
  "extract_verify_tables": [],
  "layout": "UploaderWrapper",
  "match_keywords": [],
  "match_level": 80,
  "match_type": "all",
  "name": undefined,
  "override_images": {
    "add_file": "https://cdn.uppass.io/images/verifio/element-preview/uploader-sample-file.jpg",
  },
  "override_messages": {
    "dropzone": "อัปโหลดเอกสารของคุณที่นี่",
    "file_type": "รองรับเฉพาะไฟล์ประเภท JPG, PNG, PDF เท่านั้น",
    "instruction": "<p>โปรดอัปโหลดเอกสารที่ตรงตามข้อกำหนดของคุณ<br><br>1. รองรับเฉพาะไฟล์ประเภท JPG, PNG, PDF เท่านั้น<br>2. สามารถอัปโหลดได้เพียง 1 ไฟล์</p>",
  },
  "type": "SmartUploader",
  "validator_messages": {
    "required": "โปรดอัปโหลดไฟล์ของคุณ",
  },
  "validator_rule": undefined,
  "visible": undefined,
  "visible_flag_invert": undefined,
}
`;
