{"version": 3, "sources": ["../../lodash/_copyObject.js", "../../lodash/_cloneBuffer.js", "../../lodash/_copyArray.js", "../../lodash/_initCloneObject.js", "../../lodash/_cloneArrayBuffer.js", "../../lodash/_cloneTypedArray.js"], "sourcesContent": ["var assignValue = require('./_assignValue'),\n    baseAssignValue = require('./_baseAssignValue');\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\nmodule.exports = copyObject;\n", "var root = require('./_root');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\nmodule.exports = cloneBuffer;\n", "/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\nmodule.exports = copyArray;\n", "var baseCreate = require('./_baseCreate'),\n    getPrototype = require('./_getPrototype'),\n    isPrototype = require('./_isPrototype');\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\nmodule.exports = initCloneObject;\n", "var Uint8Array = require('./_Uint8Array');\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\nmodule.exports = cloneArrayBuffer;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\nmodule.exports = cloneTypedArray;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,kBAAkB;AAYtB,aAAS,WAAW,QAAQ,OAAO,QAAQ,YAAY;AACrD,UAAI,QAAQ,CAAC;AACb,iBAAW,SAAS,CAAC;AAErB,UAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,MAAM,MAAM,KAAK;AAErB,YAAI,WAAW,aACX,WAAW,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,KAAK,QAAQ,MAAM,IACxD;AAEJ,YAAI,aAAa,QAAW;AAC1B,qBAAW,OAAO,GAAG;AAAA,QACvB;AACA,YAAI,OAAO;AACT,0BAAgB,QAAQ,KAAK,QAAQ;AAAA,QACvC,OAAO;AACL,sBAAY,QAAQ,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvCjB;AAAA;AAAA,QAAI,OAAO;AAGX,QAAI,cAAc,OAAO,WAAW,YAAY,WAAW,CAAC,QAAQ,YAAY;AAGhF,QAAI,aAAa,eAAe,OAAO,UAAU,YAAY,UAAU,CAAC,OAAO,YAAY;AAG3F,QAAI,gBAAgB,cAAc,WAAW,YAAY;AAGzD,QAAI,SAAS,gBAAgB,KAAK,SAAS;AAA3C,QACI,cAAc,SAAS,OAAO,cAAc;AAUhD,aAAS,YAAY,QAAQ,QAAQ;AACnC,UAAI,QAAQ;AACV,eAAO,OAAO,MAAM;AAAA,MACtB;AACA,UAAI,SAAS,OAAO,QAChB,SAAS,cAAc,YAAY,MAAM,IAAI,IAAI,OAAO,YAAY,MAAM;AAE9E,aAAO,KAAK,MAAM;AAClB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAQA,aAAS,UAAU,QAAQ,OAAO;AAChC,UAAI,QAAQ,IACR,SAAS,OAAO;AAEpB,gBAAU,QAAQ,MAAM,MAAM;AAC9B,aAAO,EAAE,QAAQ,QAAQ;AACvB,cAAM,KAAK,IAAI,OAAO,KAAK;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AADnB,QAEI,cAAc;AASlB,aAAS,gBAAgB,QAAQ;AAC/B,aAAQ,OAAO,OAAO,eAAe,cAAc,CAAC,YAAY,MAAM,IAClE,WAAW,aAAa,MAAM,CAAC,IAC/B,CAAC;AAAA,IACP;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,aAAa;AASjB,aAAS,iBAAiB,aAAa;AACrC,UAAI,SAAS,IAAI,YAAY,YAAY,YAAY,UAAU;AAC/D,UAAI,WAAW,MAAM,EAAE,IAAI,IAAI,WAAW,WAAW,CAAC;AACtD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,mBAAmB;AAUvB,aAAS,gBAAgB,YAAY,QAAQ;AAC3C,UAAI,SAAS,SAAS,iBAAiB,WAAW,MAAM,IAAI,WAAW;AACvE,aAAO,IAAI,WAAW,YAAY,QAAQ,WAAW,YAAY,WAAW,MAAM;AAAA,IACpF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}