{"version": 3, "sources": ["../../@tensorflow-models/face-landmarks-detection/dist/face-landmarks-detection.esm.js"], "sourcesContent": ["/**\n    * @license\n    * Copyright 2024 Google LLC. All Rights Reserved.\n    * Licensed under the Apache License, Version 2.0 (the \"License\");\n    * you may not use this file except in compliance with the License.\n    * You may obtain a copy of the License at\n    *\n    * http://www.apache.org/licenses/LICENSE-2.0\n    *\n    * Unless required by applicable law or agreed to in writing, software\n    * distributed under the License is distributed on an \"AS IS\" BASIS,\n    * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n    * See the License for the specific language governing permissions and\n    * limitations under the License.\n    * =============================================================================\n    */\nimport*as t from\"@mediapipe/face_mesh\";import*as e from\"@tensorflow/tfjs-core\";import{Tensor as n,browser as r,tensor1d as i,dispose as o,tensor2d as a,image as u,tidy as s,cast as c,squeeze as h,expandDims as l,slice as f,add as d,mul as p,div as g,exp as v,sub as m,concat as y,reshape as w,clipByValue as b,sigmoid as x,util as M}from\"@tensorflow/tfjs-core\";import*as A from\"@tensorflow/tfjs-converter\";import{loadGraphModel as T}from\"@tensorflow/tfjs-converter\";var E=function(){return E=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},E.apply(this,arguments)};function S(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{s(r.next(t))}catch(t){o(t)}}function u(t){try{s(r.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,u)}s((r=r.apply(t,e||[])).next())}))}function F(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},\"function\"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(n)throw new TypeError(\"Generator is already executing.\");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}function C(t){var e=t.map((function(t){return t[0]}));return e.push(t[t.length-1][1]),e}var O={lips:C([[61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]]),leftEye:C([[263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]]),leftEyebrow:C([[276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]]),leftIris:C([[474,475],[475,476],[476,477],[477,474]]),rightEye:C([[33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]]),rightEyebrow:C([[46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]]),rightIris:C([[469,470],[470,471],[471,472],[472,469]]),faceOval:C([[10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]])},_=[[127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]],j=Object.entries(O).map((function(t){var e=t[0];return t[1].map((function(t){return[t,e]}))})).flat(),k=new Map(j);function R(t){for(var e={locationData:{relativeKeypoints:[]}},n=Number.MAX_SAFE_INTEGER,r=Number.MIN_SAFE_INTEGER,i=Number.MAX_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,a=0;a<t.length;++a){var u=t[a];n=Math.min(n,u.x),r=Math.max(r,u.x),i=Math.min(i,u.y),o=Math.max(o,u.y),e.locationData.relativeKeypoints.push({x:u.x,y:u.y})}return e.locationData.relativeBoundingBox={xMin:n,yMin:i,xMax:r,yMax:o,width:r-n,height:o-i},e}var I={runtime:\"mediapipe\",maxFaces:1,refineLandmarks:!1};var L=function(){function n(e){var n=this;this.width=0,this.height=0,this.selfieMode=!1,this.faceMeshSolution=new t.FaceMesh({locateFile:function(t,n){return e.solutionPath?e.solutionPath.replace(/\\/+$/,\"\")+\"/\"+t:n+\"/\"+t}}),this.faceMeshSolution.setOptions({refineLandmarks:e.refineLandmarks,selfieMode:this.selfieMode,maxNumFaces:e.maxFaces}),this.faceMeshSolution.onResults((function(t){if(n.height=t.image.height,n.width=t.image.width,n.faces=[],null!==t.multiFaceLandmarks)for(var e=t.multiFaceLandmarks,r=0;r<e.length;r++){var i=n.translateOutput(e[r]);n.faces.push({keypoints:i,box:R(i).locationData.relativeBoundingBox})}}))}return n.prototype.translateOutput=function(t){var e=this;return t.map((function(t,n){var r={x:t.x*e.width,y:t.y*e.height,z:t.z*e.width},i=k.get(n);return null!=i&&(r.name=i),r}))},n.prototype.estimateFaces=function(t,n){return S(this,void 0,void 0,(function(){var r,i;return F(this,(function(o){switch(o.label){case 0:return n&&n.flipHorizontal&&n.flipHorizontal!==this.selfieMode&&(this.selfieMode=n.flipHorizontal,this.faceMeshSolution.setOptions({selfieMode:this.selfieMode})),t instanceof e.Tensor?(i=ImageData.bind,[4,e.browser.toPixels(t)]):[3,2];case 1:return r=new(i.apply(ImageData,[void 0,o.sent(),t.shape[1],t.shape[0]])),[3,3];case 2:r=t,o.label=3;case 3:return t=r,[4,this.faceMeshSolution.send({image:t})];case 4:return o.sent(),[2,this.faces]}}))}))},n.prototype.dispose=function(){this.faceMeshSolution.close()},n.prototype.reset=function(){this.faceMeshSolution.reset(),this.width=0,this.height=0,this.faces=null,this.selfieMode=!1},n.prototype.initialize=function(){return this.faceMeshSolution.initialize()},n}();function B(t){return S(this,void 0,void 0,(function(){var e,n;return F(this,(function(r){switch(r.label){case 0:return e=function(t){if(null==t)return E({},I);var e=E({},t);return e.runtime=\"mediapipe\",null==e.maxFaces&&(e.maxFaces=I.maxFaces),null==e.refineLandmarks&&(e.refineLandmarks=I.refineLandmarks),e}(t),[4,(n=new L(e)).initialize()];case 1:return r.sent(),[2,n]}}))}))}var D=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:{},P={};(function(){var t;function e(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var n=\"function\"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t};var r=function(t){t=[\"object\"==typeof globalThis&&globalThis,t,\"object\"==typeof window&&window,\"object\"==typeof self&&self,\"object\"==typeof D&&D];for(var e=0;e<t.length;++e){var n=t[e];if(n&&n.Math==Math)return n}throw Error(\"Cannot find global object\")}(this);function i(t,e){if(e)t:{var i=r;t=t.split(\".\");for(var o=0;o<t.length-1;o++){var a=t[o];if(!(a in i))break t;i=i[a]}(e=e(o=i[t=t[t.length-1]]))!=o&&null!=e&&n(i,t,{configurable:!0,writable:!0,value:e})}}function o(t){return(t={next:t})[Symbol.iterator]=function(){return this},t}function a(t){var n=\"undefined\"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];return n?n.call(t):{next:e(t)}}function u(t){if(!(t instanceof Array)){t=a(t);for(var e,n=[];!(e=t.next()).done;)n.push(e.value);t=n}return t}i(\"Symbol\",(function(t){function e(t,e){this.g=t,n(this,\"description\",{configurable:!0,writable:!0,value:e})}if(t)return t;e.prototype.toString=function(){return this.g};var r=\"jscomp_symbol_\"+(1e9*Math.random()>>>0)+\"_\",i=0;return function t(n){if(this instanceof t)throw new TypeError(\"Symbol is not a constructor\");return new e(r+(n||\"\")+\"_\"+i++,n)}})),i(\"Symbol.iterator\",(function(t){if(t)return t;t=Symbol(\"Symbol.iterator\");for(var i=\"Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array\".split(\" \"),a=0;a<i.length;a++){var u=r[i[a]];\"function\"==typeof u&&\"function\"!=typeof u.prototype[t]&&n(u.prototype,t,{configurable:!0,writable:!0,value:function(){return o(e(this))}})}return t}));var s,c=\"function\"==typeof Object.create?Object.create:function(t){function e(){}return e.prototype=t,new e};if(\"function\"==typeof Object.setPrototypeOf)s=Object.setPrototypeOf;else{var h;t:{var l={};try{l.__proto__={a:!0},h=l.a;break t}catch(t){}h=!1}s=h?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw new TypeError(t+\" is not extensible\");return t}:null}var f=s;function d(t,e){if(t.prototype=c(e.prototype),t.prototype.constructor=t,f)f(t,e);else for(var n in e)if(\"prototype\"!=n)if(Object.defineProperties){var r=Object.getOwnPropertyDescriptor(e,n);r&&Object.defineProperty(t,n,r)}else t[n]=e[n];t.na=e.prototype}function p(){this.l=!1,this.i=null,this.h=void 0,this.g=1,this.u=this.o=0,this.j=null}function g(t){if(t.l)throw new TypeError(\"Generator is already running\");t.l=!0}function v(t,e){t.j={da:e,ea:!0},t.g=t.o||t.u}function m(t,e,n){return t.g=n,{value:e}}function y(t){this.g=new p,this.h=t}function w(t,e,n,r){try{var i=e.call(t.g.i,n);if(!(i instanceof Object))throw new TypeError(\"Iterator result \"+i+\" is not an object\");if(!i.done)return t.g.l=!1,i;var o=i.value}catch(e){return t.g.i=null,v(t.g,e),b(t)}return t.g.i=null,r.call(t.g,o),b(t)}function b(t){for(;t.g.g;)try{var e=t.h(t.g);if(e)return t.g.l=!1,{value:e.value,done:!1}}catch(e){t.g.h=void 0,v(t.g,e)}if(t.g.l=!1,t.g.j){if(e=t.g.j,t.g.j=null,e.ea)throw e.da;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function x(t){this.next=function(e){return g(t.g),t.g.i?e=w(t,t.g.i.next,e,t.g.s):(t.g.s(e),e=b(t)),e},this.throw=function(e){return g(t.g),t.g.i?e=w(t,t.g.i.throw,e,t.g.s):(v(t.g,e),e=b(t)),e},this.return=function(e){return function(t,e){g(t.g);var n=t.g.i;return n?w(t,\"return\"in n?n.return:function(t){return{value:t,done:!0}},e,t.g.return):(t.g.return(e),b(t))}(t,e)},this[Symbol.iterator]=function(){return this}}function M(t){return function(t){function e(e){return t.next(e)}function n(e){return t.throw(e)}return new Promise((function(r,i){!function t(o){o.done?r(o.value):Promise.resolve(o.value).then(e,n).then(t,i)}(t.next())}))}(new x(new y(t)))}p.prototype.s=function(t){this.h=t},p.prototype.return=function(t){this.j={return:t},this.g=this.u},i(\"Promise\",(function(t){function e(t){this.h=0,this.i=void 0,this.g=[],this.s=!1;var e=this.j();try{t(e.resolve,e.reject)}catch(t){e.reject(t)}}function n(){this.g=null}function i(t){return t instanceof e?t:new e((function(e){e(t)}))}if(t)return t;n.prototype.h=function(t){if(null==this.g){this.g=[];var e=this;this.i((function(){e.l()}))}this.g.push(t)};var o=r.setTimeout;n.prototype.i=function(t){o(t,0)},n.prototype.l=function(){for(;this.g&&this.g.length;){var t=this.g;this.g=[];for(var e=0;e<t.length;++e){var n=t[e];t[e]=null;try{n()}catch(t){this.j(t)}}}this.g=null},n.prototype.j=function(t){this.i((function(){throw t}))},e.prototype.j=function(){function t(t){return function(r){n||(n=!0,t.call(e,r))}}var e=this,n=!1;return{resolve:t(this.D),reject:t(this.l)}},e.prototype.D=function(t){if(t===this)this.l(new TypeError(\"A Promise cannot resolve to itself\"));else if(t instanceof e)this.H(t);else{t:switch(typeof t){case\"object\":var n=null!=t;break t;case\"function\":n=!0;break t;default:n=!1}n?this.A(t):this.o(t)}},e.prototype.A=function(t){var e=void 0;try{e=t.then}catch(t){return void this.l(t)}\"function\"==typeof e?this.I(e,t):this.o(t)},e.prototype.l=function(t){this.u(2,t)},e.prototype.o=function(t){this.u(1,t)},e.prototype.u=function(t,e){if(0!=this.h)throw Error(\"Cannot settle(\"+t+\", \"+e+\"): Promise already settled in state\"+this.h);this.h=t,this.i=e,2===this.h&&this.G(),this.B()},e.prototype.G=function(){var t=this;o((function(){if(t.C()){var e=r.console;void 0!==e&&e.error(t.i)}}),1)},e.prototype.C=function(){if(this.s)return!1;var t=r.CustomEvent,e=r.Event,n=r.dispatchEvent;return void 0===n||(\"function\"==typeof t?t=new t(\"unhandledrejection\",{cancelable:!0}):\"function\"==typeof e?t=new e(\"unhandledrejection\",{cancelable:!0}):(t=r.document.createEvent(\"CustomEvent\")).initCustomEvent(\"unhandledrejection\",!1,!0,t),t.promise=this,t.reason=this.i,n(t))},e.prototype.B=function(){if(null!=this.g){for(var t=0;t<this.g.length;++t)u.h(this.g[t]);this.g=null}};var u=new n;return e.prototype.H=function(t){var e=this.j();t.M(e.resolve,e.reject)},e.prototype.I=function(t,e){var n=this.j();try{t.call(e,n.resolve,n.reject)}catch(t){n.reject(t)}},e.prototype.then=function(t,n){function r(t,e){return\"function\"==typeof t?function(e){try{i(t(e))}catch(t){o(t)}}:e}var i,o,a=new e((function(t,e){i=t,o=e}));return this.M(r(t,i),r(n,o)),a},e.prototype.catch=function(t){return this.then(void 0,t)},e.prototype.M=function(t,e){function n(){switch(r.h){case 1:t(r.i);break;case 2:e(r.i);break;default:throw Error(\"Unexpected state: \"+r.h)}}var r=this;null==this.g?u.h(n):this.g.push(n),this.s=!0},e.resolve=i,e.reject=function(t){return new e((function(e,n){n(t)}))},e.race=function(t){return new e((function(e,n){for(var r=a(t),o=r.next();!o.done;o=r.next())i(o.value).M(e,n)}))},e.all=function(t){var n=a(t),r=n.next();return r.done?i([]):new e((function(t,e){function o(e){return function(n){a[e]=n,0==--u&&t(a)}}var a=[],u=0;do{a.push(void 0),u++,i(r.value).M(o(a.length-1),e),r=n.next()}while(!r.done)}))},e}));var A=\"function\"==typeof Object.assign?Object.assign:function(t,e){for(var n=1;n<arguments.length;n++){var r=arguments[n];if(r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t};i(\"Object.assign\",(function(t){return t||A})),i(\"Object.is\",(function(t){return t||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}})),i(\"Array.prototype.includes\",(function(t){return t||function(t,e){var n=this;n instanceof String&&(n=String(n));var r=n.length;for(0>(e=e||0)&&(e=Math.max(e+r,0));e<r;e++){var i=n[e];if(i===t||Object.is(i,t))return!0}return!1}})),i(\"String.prototype.includes\",(function(t){return t||function(t,e){if(null==this)throw new TypeError(\"The 'this' value for String.prototype.includes must not be null or undefined\");if(t instanceof RegExp)throw new TypeError(\"First argument to String.prototype.includes must not be a regular expression\");return-1!==this.indexOf(t,e||0)}})),i(\"Array.prototype.keys\",(function(t){return t||function(){return function(t,e){t instanceof String&&(t+=\"\");var n=0,r=!1,i={next:function(){if(!r&&n<t.length){var i=n++;return{value:e(i,t[i]),done:!1}}return r=!0,{done:!0,value:void 0}}};return i[Symbol.iterator]=function(){return i},i}(this,(function(t){return t}))}}));var T=this||self;function E(t,e){t=t.split(\".\");var n,r=T;t[0]in r||void 0===r.execScript||r.execScript(\"var \"+t[0]);for(;t.length&&(n=t.shift());)t.length||void 0===e?r=r[n]&&r[n]!==Object.prototype[n]?r[n]:r[n]={}:r[n]=e}function S(){throw Error(\"Invalid UTF8\")}function F(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}var C,O,_=\"undefined\"!=typeof TextDecoder,j=\"undefined\"!=typeof TextEncoder,k={},R=null;function I(t){var e;void 0===e&&(e=0),B(),e=k[e];for(var n=Array(Math.floor(t.length/3)),r=e[64]||\"\",i=0,o=0;i<t.length-2;i+=3){var a=t[i],u=t[i+1],s=t[i+2],c=e[a>>2];a=e[(3&a)<<4|u>>4],u=e[(15&u)<<2|s>>6],s=e[63&s],n[o++]=c+a+u+s}switch(c=0,s=r,t.length-i){case 2:s=e[(15&(c=t[i+1]))<<2]||r;case 1:t=t[i],n[o]=e[t>>2]+e[(3&t)<<4|c>>4]+s+r}return n.join(\"\")}function L(t){var e=t.length,n=3*e/4;n%3?n=Math.floor(n):-1!=\"=.\".indexOf(t[e-1])&&(n=-1!=\"=.\".indexOf(t[e-2])?n-2:n-1);var r=new Uint8Array(n),i=0;return function(t,e){function n(e){for(;r<t.length;){var n=t.charAt(r++),i=R[n];if(null!=i)return i;if(!/^[\\s\\xa0]*$/.test(n))throw Error(\"Unknown base64 encoding at char: \"+n)}return e}B();for(var r=0;;){var i=n(-1),o=n(0),a=n(64),u=n(64);if(64===u&&-1===i)break;e(i<<2|o>>4),64!=a&&(e(o<<4&240|a>>2),64!=u&&e(a<<6&192|u))}}(t,(function(t){r[i++]=t})),i!==n?r.subarray(0,i):r}function B(){if(!R){R={};for(var t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\"),e=[\"+/=\",\"+/\",\"-_=\",\"-_.\",\"-_\"],n=0;5>n;n++){var r=t.concat(e[n].split(\"\"));k[n]=r;for(var i=0;i<r.length;i++){var o=r[i];void 0===R[o]&&(R[o]=i)}}}}var P,z=\"function\"==typeof Uint8Array;function U(t){return z&&null!=t&&t instanceof Uint8Array}function N(t){if(this.L=t,null!==t&&0===t.length)throw Error(\"ByteString should be constructed with non-empty values\")}var V=\"function\"==typeof Uint8Array.prototype.slice,H=0;function K(t,e){return Error(\"Invalid wire type: \"+t+\" (at position \"+e+\")\")}function W(){return Error(\"Failed to read varint, encoding is invalid.\")}function G(t,e){e=void 0!==(e=void 0===e?{}:e).v&&e.v,this.h=null,this.g=this.i=this.j=0,this.v=e,t&&X(this,t)}function X(t,e){t.h=function(t,e){if(t.constructor===Uint8Array)return t;if(t.constructor===ArrayBuffer)return new Uint8Array(t);if(t.constructor===Array)return new Uint8Array(t);if(t.constructor===String)return L(t);if(t.constructor===N)return!e&&(e=t.L)&&e.constructor===Uint8Array?e:(e=null==(e=t.L)||U(e)?e:\"string\"==typeof e?L(e):null,(t=t.L=e)?new Uint8Array(t):P||(P=new Uint8Array(0)));if(t instanceof Uint8Array)return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw Error(\"Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, or Array of numbers\")}(e,t.v),t.j=0,t.i=t.h.length,t.g=t.j}function Y(t){if(t.g>t.i)throw Error(\"Tried to read past the end of the data \"+t.g+\" > \"+t.i)}function J(t){var e=t.h,n=e[t.g],r=127&n;if(128>n)return t.g+=1,Y(t),r;if(r|=(127&(n=e[t.g+1]))<<7,128>n)return t.g+=2,Y(t),r;if(r|=(127&(n=e[t.g+2]))<<14,128>n)return t.g+=3,Y(t),r;if(r|=(127&(n=e[t.g+3]))<<21,128>n)return t.g+=4,Y(t),r;if(n=e[t.g+4],t.g+=5,r|=(15&n)<<28,128>n)return Y(t),r;if(128<=e[t.g++]&&128<=e[t.g++]&&128<=e[t.g++]&&128<=e[t.g++]&&128<=e[t.g++])throw W();return Y(t),r}G.prototype.reset=function(){this.g=this.j};var q=[];function $(){this.g=[]}function Z(t,e){for(;127<e;)t.g.push(127&e|128),e>>>=7;t.g.push(e)}function Q(t){var e={},n=void 0!==e.W&&e.W;this.l={v:void 0!==e.v&&e.v},this.W=n,e=this.l,q.length?(n=q.pop(),e&&(n.v=e.v),t&&X(n,t),t=n):t=new G(t,e),this.g=t,this.j=this.g.g,this.h=this.i=-1}function tt(t){var e=t.g;if(e.g==e.i)return!1;t.j=t.g.g;var n=J(t.g)>>>0;if(e=n>>>3,!(0<=(n&=7)&&5>=n))throw K(n,t.j);if(1>e)throw Error(\"Invalid field number: \"+e+\" (at position \"+t.j+\")\");return t.i=e,t.h=n,!0}function et(t){switch(t.h){case 0:if(0!=t.h)et(t);else t:{for(var e=(t=t.g).g,n=e+10;e<n;)if(0==(128&t.h[e++])){t.g=e,Y(t);break t}throw W()}break;case 1:(t=t.g).g+=8,Y(t);break;case 2:2!=t.h?et(t):(e=J(t.g)>>>0,(t=t.g).g+=e,Y(t));break;case 5:(t=t.g).g+=4,Y(t);break;case 3:for(e=t.i;;){if(!tt(t))throw Error(\"Unmatched start-group tag: stream EOF\");if(4==t.h){if(t.i!=e)throw Error(\"Unmatched end-group tag\");break}et(t)}break;default:throw K(t.h,t.j)}}$.prototype.length=function(){return this.g.length},$.prototype.end=function(){var t=this.g;return this.g=[],t},Q.prototype.reset=function(){this.g.reset(),this.j=this.g.g,this.h=this.i=-1};var nt=[];function rt(){this.i=[],this.h=0,this.g=new $}function it(t,e){0!==e.length&&(t.i.push(e),t.h+=e.length)}var ot=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol()?Symbol(void 0):void 0;function at(t,e){Object.isFrozen(t)||(ot?t[ot]|=e:void 0!==t.N?t.N|=e:Object.defineProperties(t,{N:{value:e,configurable:!0,writable:!0,enumerable:!1}}))}function ut(t){var e;return null==(e=ot?t[ot]:t.N)?0:e}function st(t){return at(t,1),t}function ct(t){return!!Array.isArray(t)&&!!(2&ut(t))}function ht(t){if(!Array.isArray(t))throw Error(\"cannot mark non-array as immutable\");at(t,2)}function lt(t){return null!==t&&\"object\"==typeof t&&!Array.isArray(t)&&t.constructor===Object}var ft=Object.freeze(st([]));function dt(t){if(ct(t.m))throw Error(\"Cannot mutate an immutable Message\")}var pt,gt=\"undefined\"!=typeof Symbol&&void 0!==Symbol.hasInstance;function vt(t){return{value:t,configurable:!1,writable:!1,enumerable:!1}}function mt(t,e,n){return-1===e?null:e>=t.i?t.g?t.g[e]:void 0:void 0!==n&&n&&t.g&&null!=(n=t.g[e])?n:t.m[e+t.h]}function yt(t,e,n,r){r=void 0!==r&&r,dt(t),e<t.i&&!r?t.m[e+t.h]=n:(t.g||(t.g=t.m[t.i+t.h]={}))[e]=n}function wt(t,e,n,r){n=void 0===n||n;var i=mt(t,e,r=void 0!==r&&r);return null==i&&(i=ft),ct(t.m)?n&&(ht(i),Object.freeze(i)):(i===ft||ct(i))&&yt(t,e,i=st(i.slice()),r),i}function bt(t,e,n){return null==(t=null==(t=mt(t,e))?t:+t)?void 0===n?0:n:t}function xt(t,e,n,r){t.j||(t.j={});var i=ct(t.m),o=t.j[n];if(!o){r=wt(t,n,!0,void 0!==r&&r),o=[],i=i||ct(r);for(var a=0;a<r.length;a++)o[a]=new e(r[a]),i&&ht(o[a].m);i&&(ht(o),Object.freeze(o)),t.j[n]=o}return o}function Mt(t,e,n,r,i){var o=void 0!==o&&o;return dt(t),o=xt(t,n,e,o),n=r||new n,t=wt(t,e),null!=i?(o.splice(i,0,n),t.splice(i,0,n.m)):(o.push(n),t.push(n.m)),n}function At(t,e){return null==(t=mt(t,e))?0:t}function Tt(t,e){return null==(t=mt(t,e))?\"\":t}function Et(t){var e=Ct;return Ft(t,e=void 0===e?Ot:e)}function St(t,e){if(null!=t){if(Array.isArray(t))t=Ft(t,e);else if(lt(t)){var n,r={};for(n in t)r[n]=St(t[n],e);t=r}else t=e(t);return t}}function Ft(t,e){for(var n=t.slice(),r=0;r<n.length;r++)n[r]=St(n[r],e);return Array.isArray(t)&&1&ut(t)&&st(n),n}function Ct(t){return t&&\"object\"==typeof t&&t.toJSON?t.toJSON():(t=function(t){switch(typeof t){case\"number\":return isFinite(t)?t:String(t);case\"object\":if(t&&!Array.isArray(t)){if(U(t))return I(t);if(t instanceof N){var e=t.L;return e=null==e||\"string\"==typeof e?e:z&&e instanceof Uint8Array?I(e):null,(t.L=e)||\"\"}}}return t}(t),Array.isArray(t)?Et(t):t)}function Ot(t){return U(t)?new Uint8Array(t):t}function _t(t,e,n){t||(t=pt),pt=null;var r=this.constructor.h;if(t||(t=r?[r]:[]),this.h=(r?0:-1)-(this.constructor.g||0),this.j=void 0,this.m=t,t=(r=this.m.length)-1,r&&lt(r=this.m[t])?(this.i=t-this.h,this.g=r):void 0!==e&&-1<e?(this.i=Math.max(e,t+1-this.h),this.g=void 0):this.i=Number.MAX_VALUE,n)for(e=0;e<n.length;e++)if((t=n[e])<this.i)t+=this.h,(r=this.m[t])?Array.isArray(r)&&st(r):this.m[t]=ft;else{var i=(r=this.g||(this.g=this.m[this.i+this.h]={}))[t];i?Array.isArray(i)&&st(i):r[t]=ft}}function jt(){_t.apply(this,arguments)}if(_t.prototype.toJSON=function(){return Et(this.m)},_t.prototype.toString=function(){return this.m.toString()},d(jt,_t),gt){var kt={};Object.defineProperties(jt,(kt[Symbol.hasInstance]=vt((function(){throw Error(\"Cannot perform instanceof checks for MutableMessage\")})),kt))}function Rt(t,e,n){if(n){var r,i={};for(r in n){var o=n[r],a=o.ha;a||(i.F=o.la||o.fa.P,o.aa?(i.U=Ut(o.aa),a=function(t){return function(e,n,r){return t.F(e,n,r,t.U)}}(i)):o.ca?(i.T=Nt(o.X.g,o.ca),a=function(t){return function(e,n,r){return t.F(e,n,r,t.T)}}(i)):a=i.F,o.ha=a),a(e,t,o.X),i={F:i.F,U:i.U,T:i.T}}}!function(t,e){if(e=e.ba){it(t,t.g.end());for(var n=0;n<e.length;n++)it(t,e[n])}}(e,t)}var It=Symbol();function Lt(t,e,n){return t[It]||(t[It]=function(t,r){return e(t,r,n)})}function Bt(t){var e=t[It];if(!e){var n=Qt(t);e=function(t,e){return te(t,e,n)},t[It]=e}return e}function Dt(t){var e=function(t){var e=t.aa;return e?Bt(e):(e=t.ka)?Lt(t.X.g,e,t.ca):void 0}(t),n=t.X,r=t.fa.O;return e?function(t,i){return r(t,i,n,e)}:function(t,e){return r(t,e,n)}}function Pt(t,e,n,r,i,o){var a=0;for((t=t()).length&&\"number\"!=typeof t[0]&&(n(e,t[0]),a++);a<t.length;){n=t[a++];for(var u=a+1;u<t.length&&\"number\"!=typeof t[u];)u++;var s=t[a++];switch(u-=a){case 0:r(e,n,s);break;case 1:r(e,n,s,t[a++]);break;case 2:i(e,n,s,t[a++],t[a++]);break;case 3:u=t[a++];var c=t[a++],h=t[a++];Array.isArray(h)?i(e,n,s,u,c,h):o(e,n,s,u,c,h);break;case 4:o(e,n,s,t[a++],t[a++],t[a++],t[a++]);break;default:throw Error(\"unexpected number of binary field arguments: \"+u)}}return e}var zt=Symbol();function Ut(t){var e=t[zt];if(!e){var n=Xt(t);e=function(t,e){return ne(t,e,n)},t[zt]=e}return e}function Nt(t,e){var n=t[zt];return n||(n=function(t,n){return Rt(t,n,e)},t[zt]=n),n}var Vt=Symbol();function Ht(t,e){t.push(e)}function Kt(t,e,n){t.push(e,n.P)}function Wt(t,e,n,r,i){var o=Ut(i),a=n.P;t.push(e,(function(t,e,n){return a(t,e,n,r,o)}))}function Gt(t,e,n,r,i,o){var a=Nt(r,o),u=n.P;t.push(e,(function(t,e,n){return u(t,e,n,r,a)}))}function Xt(t){var e=t[Vt];return e||Pt(t,t[Vt]=[],Ht,Kt,Wt,Gt)}var Yt=Symbol();function Jt(t,e){t[0]=e}function qt(t,e,n,r){var i=n.O;t[e]=r?function(t,e,n){return i(t,e,n,r)}:i}function $t(t,e,n,r,i,o){var a=n.O,u=Bt(i);t[e]=function(t,e,n){return a(t,e,n,r,u,o)}}function Zt(t,e,n,r,i,o,a){var u=n.O,s=Lt(r,i,o);t[e]=function(t,e,n){return u(t,e,n,r,s,a)}}function Qt(t){var e=t[Yt];return e||Pt(t,t[Yt]={},Jt,qt,$t,Zt)}function te(t,e,n){for(;tt(e)&&4!=e.h;){var r=e.i,i=n[r];if(!i){var o=n[0];o&&(o=o[r])&&(i=n[r]=Dt(o))}if(!(i&&i(e,t,r)||(i=e,r=t,o=i.j,et(i),i.W))){var a=i.g.h;i=o===(i=i.g.g)?P||(P=new Uint8Array(0)):V?a.slice(o,i):new Uint8Array(a.subarray(o,i)),(o=r.ba)?o.push(i):r.ba=[i]}}return t}function ee(t,e,n){if(nt.length){var r=nt.pop();t&&(X(r.g,t),r.i=-1,r.h=-1),t=r}else t=new Q(t);try{return te(new e,t,Qt(n))}finally{(e=t.g).h=null,e.j=0,e.i=0,e.g=0,e.v=!1,t.i=-1,t.h=-1,100>nt.length&&nt.push(t)}}function ne(t,e,n){for(var r=n.length,i=1==r%2,o=i?1:0;o<r;o+=2)(0,n[o+1])(e,t,n[o]);Rt(t,e,i?n[0]:void 0)}function re(t,e){var n=new rt;ne(t,n,Xt(e)),it(n,n.g.end()),t=new Uint8Array(n.h);for(var r=(e=n.i).length,i=0,o=0;o<r;o++){var a=e[o];t.set(a,i),i+=a.length}return n.i=[t],t}function ie(t,e){return{O:t,P:e}}var oe=ie((function(t,e,n){if(5!==t.h)return!1;var r=(t=t.g).h[t.g],i=t.h[t.g+1],o=t.h[t.g+2],a=t.h[t.g+3];return t.g+=4,Y(t),t=2*((i=(r<<0|i<<8|o<<16|a<<24)>>>0)>>31)+1,r=i>>>23&255,i&=8388607,yt(e,n,255==r?i?NaN:1/0*t:0==r?t*Math.pow(2,-149)*i:t*Math.pow(2,r-150)*(i+Math.pow(2,23))),!0}),(function(t,e,n){if(null!=(e=mt(e,n))){Z(t.g,8*n+5),t=t.g;var r=e;0===(r=(n=0>r?1:0)?-r:r)?0<1/r?H=0:(0,H=2147483648):isNaN(r)?(0,H=2147483647):34028234663852886e22<r?(0,H=(n<<31|2139095040)>>>0):11754943508222875e-54>r?(r=Math.round(r/Math.pow(2,-149)),0,H=(n<<31|r)>>>0):(e=Math.floor(Math.log(r)/Math.LN2),r*=Math.pow(2,-e),16777216<=(r=Math.round(8388608*r))&&++e,0,H=(n<<31|e+127<<23|8388607&r)>>>0),n=H,t.g.push(n>>>0&255),t.g.push(n>>>8&255),t.g.push(n>>>16&255),t.g.push(n>>>24&255)}})),ae=ie((function(t,e,n){if(0!==t.h)return!1;for(var r=t.g,i=128,o=0,a=t=0;4>a&&128<=i;a++)i=r.h[r.g++],Y(r),o|=(127&i)<<7*a;if(128<=i&&(i=r.h[r.g++],Y(r),o|=(127&i)<<28,t|=(127&i)>>4),128<=i)for(a=0;5>a&&128<=i;a++)i=r.h[r.g++],Y(r),t|=(127&i)<<7*a+3;if(!(128>i))throw W();return r=o>>>0,(t=2147483648&(i=t>>>0))&&(i=~i>>>0,0==(r=1+~r>>>0)&&(i=i+1>>>0)),r=4294967296*i+(r>>>0),yt(e,n,t?-r:r),!0}),(function(t,e,n){if(null!=(e=mt(e,n))&&null!=e){Z(t.g,8*n),t=t.g;var r=e;for(n=0>r,e=(r=Math.abs(r))>>>0,r=Math.floor((r-e)/4294967296),r>>>=0,n&&(r=~r>>>0,4294967295<(e=1+(~e>>>0))&&(e=0,4294967295<++r&&(r=0))),n=H=e,e=r;0<e||127<n;)t.g.push(127&n|128),n=(n>>>7|e<<25)>>>0,e>>>=7;t.g.push(n)}})),ue=ie((function(t,e,n){return 0===t.h&&(yt(e,n,J(t.g)),!0)}),(function(t,e,n){if(null!=(e=mt(e,n))&&null!=e)if(Z(t.g,8*n),t=t.g,0<=(n=e))Z(t,n);else{for(e=0;9>e;e++)t.g.push(127&n|128),n>>=7;t.g.push(1)}})),se=ie((function(t,e,n){if(2!==t.h)return!1;var r,i=J(t.g)>>>0,o=(t=t.g).g;if(t.g+=i,Y(t),t=t.h,_)(r=C)||(r=C=new TextDecoder(\"utf-8\",{fatal:!0})),r=r.decode(t.subarray(o,o+i));else{i=o+i;for(var a,u,s,c=[],h=null;o<i;)128>(a=t[o++])?c.push(a):224>a?o>=i?S():(u=t[o++],194>a||128!=(192&u)?(o--,S()):c.push((31&a)<<6|63&u)):240>a?o>=i-1?S():128!=(192&(u=t[o++]))||224===a&&160>u||237===a&&160<=u||128!=(192&(r=t[o++]))?(o--,S()):c.push((15&a)<<12|(63&u)<<6|63&r):244>=a?o>=i-2?S():128!=(192&(u=t[o++]))||0!=u-144+(a<<28)>>30||128!=(192&(r=t[o++]))||128!=(192&(s=t[o++]))?(o--,S()):(a=(7&a)<<18|(63&u)<<12|(63&r)<<6|63&s,a-=65536,c.push(55296+(a>>10&1023),56320+(1023&a))):S(),8192<=c.length&&(h=F(h,c),c.length=0);r=F(h,c)}return yt(e,n,r),!0}),(function(t,e,n){if(null!=(e=mt(e,n))){var r=!1;if(r=void 0!==r&&r,j){if(r&&/(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])/.test(e))throw Error(\"Found an unpaired surrogate\");e=(O||(O=new TextEncoder)).encode(e)}else{for(var i=0,o=new Uint8Array(3*e.length),a=0;a<e.length;a++){var u=e.charCodeAt(a);if(128>u)o[i++]=u;else{if(2048>u)o[i++]=u>>6|192;else{if(55296<=u&&57343>=u){if(56319>=u&&a<e.length){var s=e.charCodeAt(++a);if(56320<=s&&57343>=s){u=1024*(u-55296)+s-56320+65536,o[i++]=u>>18|240,o[i++]=u>>12&63|128,o[i++]=u>>6&63|128,o[i++]=63&u|128;continue}a--}if(r)throw Error(\"Found an unpaired surrogate\");u=65533}o[i++]=u>>12|224,o[i++]=u>>6&63|128}o[i++]=63&u|128}}e=o.subarray(0,i)}Z(t.g,8*n+2),Z(t.g,e.length),it(t,t.g.end()),it(t,e)}})),ce=ie((function(t,e,n,r,i){if(2!==t.h)return!1;e=Mt(e,n,r),n=t.g.i,r=J(t.g)>>>0;var o=t.g.g+r,a=o-n;if(0>=a&&(t.g.i=o,i(e,t),a=o-t.g.g),a)throw Error(\"Message parsing ended unexpectedly. Expected to read \"+r+\" bytes, instead read \"+(r-a)+\" bytes, either the data ended unexpectedly or the message misreported its own length\");return t.g.g=o,t.g.i=n,!0}),(function(t,e,n,r,i){if(null!=(e=xt(e,r,n)))for(r=0;r<e.length;r++){var o=t;Z(o.g,8*n+2);var a=o.g.end();it(o,a),a.push(o.h),o=a,i(e[r],t),a=t;var u=o.pop();for(u=a.h+a.g.length()-u;127<u;)o.push(127&u|128),u>>>=7,a.h++;o.push(u),a.h++}}));function he(){jt.apply(this,arguments)}if(d(he,jt),gt){var le={};Object.defineProperties(he,(le[Symbol.hasInstance]=vt(Object[Symbol.hasInstance]),le))}function fe(t){he.call(this,t)}function de(){return[1,ue,2,oe,3,se,4,se]}function pe(t){he.call(this,t,-1,ve)}function ge(){return[1,ce,fe,de]}d(fe,he),d(pe,he),pe.prototype.addClassification=function(t,e){return Mt(this,1,fe,t,e),this};var ve=[1];function me(t){he.call(this,t)}function ye(){return[1,oe,2,oe,3,oe,4,oe,5,oe]}function we(t){he.call(this,t,-1,xe)}function be(){return[1,ce,me,ye]}d(me,he),d(we,he);var xe=[1];function Me(t){he.call(this,t)}function Ae(){return[1,oe,2,oe,3,oe,4,oe,5,oe,6,ae]}d(Me,he);var Te=[[61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]],Ee=[[263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]],Se=[[276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]],Fe=[[33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]],Ce=[[46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]],Oe=[[10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]],_e=[].concat(u(Te),u(Ee),u(Se),u(Fe),u(Ce),u(Oe));function je(t,e,n){if(n=t.createShader(0===n?t.VERTEX_SHADER:t.FRAGMENT_SHADER),t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw Error(\"Could not compile WebGL shader.\\n\\n\"+t.getShaderInfoLog(n));return n}function ke(t){return xt(t,fe,1).map((function(t){return{index:At(t,1),ga:bt(t,2),label:null!=mt(t,3)?Tt(t,3):void 0,displayName:null!=mt(t,4)?Tt(t,4):void 0}}))}function Re(t){return{x:bt(t,1),y:bt(t,2),z:bt(t,3),visibility:null!=mt(t,4)?bt(t,4):void 0}}function Ie(t,e){this.h=t,this.g=e,this.l=0}function Le(t,e,n){return function(t,e){var n=t.g;if(void 0===t.o){var r=je(n,\"\\n  attribute vec2 aVertex;\\n  attribute vec2 aTex;\\n  varying vec2 vTex;\\n  void main(void) {\\n    gl_Position = vec4(aVertex, 0.0, 1.0);\\n    vTex = aTex;\\n  }\",0),i=je(n,\"\\n  precision mediump float;\\n  varying vec2 vTex;\\n  uniform sampler2D sampler0;\\n  void main(){\\n    gl_FragColor = texture2D(sampler0, vTex);\\n  }\",1),o=n.createProgram();if(n.attachShader(o,r),n.attachShader(o,i),n.linkProgram(o),!n.getProgramParameter(o,n.LINK_STATUS))throw Error(\"Could not compile WebGL program.\\n\\n\"+n.getProgramInfoLog(o));r=t.o=o,n.useProgram(r),i=n.getUniformLocation(r,\"sampler0\"),t.j={K:n.getAttribLocation(r,\"aVertex\"),J:n.getAttribLocation(r,\"aTex\"),ma:i},t.u=n.createBuffer(),n.bindBuffer(n.ARRAY_BUFFER,t.u),n.enableVertexAttribArray(t.j.K),n.vertexAttribPointer(t.j.K,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),t.s=n.createBuffer(),n.bindBuffer(n.ARRAY_BUFFER,t.s),n.enableVertexAttribArray(t.j.J),n.vertexAttribPointer(t.j.J,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.uniform1i(i,0)}r=t.j,n.useProgram(t.o),n.canvas.width=e.width,n.canvas.height=e.height,n.viewport(0,0,e.width,e.height),n.activeTexture(n.TEXTURE0),t.h.bindTexture2d(e.glName),n.enableVertexAttribArray(r.K),n.bindBuffer(n.ARRAY_BUFFER,t.u),n.vertexAttribPointer(r.K,2,n.FLOAT,!1,0,0),n.enableVertexAttribArray(r.J),n.bindBuffer(n.ARRAY_BUFFER,t.s),n.vertexAttribPointer(r.J,2,n.FLOAT,!1,0,0),n.bindFramebuffer(n.DRAW_FRAMEBUFFER?n.DRAW_FRAMEBUFFER:n.FRAMEBUFFER,null),n.clearColor(0,0,0,0),n.clear(n.COLOR_BUFFER_BIT),n.colorMask(!0,!0,!0,!0),n.drawArrays(n.TRIANGLE_FAN,0,4),n.disableVertexAttribArray(r.K),n.disableVertexAttribArray(r.J),n.bindBuffer(n.ARRAY_BUFFER,null),t.h.bindTexture2d(0)}(t,e),\"function\"==typeof t.g.canvas.transferToImageBitmap?Promise.resolve(t.g.canvas.transferToImageBitmap()):n?Promise.resolve(t.g.canvas):\"function\"==typeof createImageBitmap?createImageBitmap(t.g.canvas):(void 0===t.i&&(t.i=document.createElement(\"canvas\")),new Promise((function(e){t.i.height=t.g.canvas.height,t.i.width=t.g.canvas.width,t.i.getContext(\"2d\",{}).drawImage(t.g.canvas,0,0,t.g.canvas.width,t.g.canvas.height),e(t.i)})))}function Be(t){this.g=t}var De=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function Pe(t,e){return e+t}function ze(t,e){window[t]=e}function Ue(t){if(this.g=t,this.listeners={},this.j={},this.H={},this.o={},this.u={},this.I=this.s=this.Z=!0,this.D=Promise.resolve(),this.Y=\"\",this.C={},this.locateFile=t&&t.locateFile||Pe,\"object\"==typeof window)var e=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf(\"/\"))+\"/\";else{if(\"undefined\"==typeof location)throw Error(\"solutions can only be loaded on a web page or in a web worker\");e=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf(\"/\"))+\"/\"}if(this.$=e,t.options)for(var n=(e=a(Object.keys(t.options))).next();!n.done;n=e.next()){n=n.value;var r=t.options[n].default;void 0!==r&&(this.j[n]=\"function\"==typeof r?r():r)}}function Ne(t){var e,n,r,i,o,a,s,c,h,l,f;return M((function(d){switch(d.g){case 1:return t.Z?(e=void 0===t.g.files?[]:\"function\"==typeof t.g.files?t.g.files(t.j):t.g.files,m(d,M((function(t){switch(t.g){case 1:return t.o=2,m(t,WebAssembly.instantiate(De),4);case 4:t.g=3,t.o=0;break;case 2:return t.o=0,t.j=null,t.return(!1);case 3:return t.return(!0)}})),2)):d.return();case 2:if(n=d.h,\"object\"==typeof window)return ze(\"createMediapipeSolutionsWasm\",{locateFile:t.locateFile}),ze(\"createMediapipeSolutionsPackedAssets\",{locateFile:t.locateFile}),a=e.filter((function(t){return void 0!==t.data})),s=e.filter((function(t){return void 0===t.data})),c=Promise.all(a.map((function(e){var n=Ve(t,e.url);if(void 0!==e.path){var r=e.path;n=n.then((function(e){return t.overrideFile(r,e),Promise.resolve(e)}))}return n}))),h=Promise.all(s.map((function(e){return void 0===e.simd||e.simd&&n||!e.simd&&!n?function(t){var e=document.createElement(\"script\");return e.setAttribute(\"src\",t),e.setAttribute(\"crossorigin\",\"anonymous\"),new Promise((function(t){e.addEventListener(\"load\",(function(){t()}),!1),e.addEventListener(\"error\",(function(){t()}),!1),document.body.appendChild(e)}))}(t.locateFile(e.url,t.$)):Promise.resolve()}))).then((function(){var e,n,r;return M((function(i){if(1==i.g)return e=window.createMediapipeSolutionsWasm,n=window.createMediapipeSolutionsPackedAssets,r=t,m(i,e(n),2);r.h=i.h,i.g=0}))})),l=M((function(e){return t.g.graph&&t.g.graph.url?e=m(e,Ve(t,t.g.graph.url),0):(e.g=0,e=void 0),e})),m(d,Promise.all([h,c,l]),7);if(\"function\"!=typeof importScripts)throw Error(\"solutions can only be loaded on a web page or in a web worker\");return r=e.filter((function(t){return void 0===t.simd||t.simd&&n||!t.simd&&!n})).map((function(e){return t.locateFile(e.url,t.$)})),importScripts.apply(null,u(r)),i=t,m(d,createMediapipeSolutionsWasm(Module),6);case 6:i.h=d.h,t.l=new OffscreenCanvas(1,1),t.h.canvas=t.l,o=t.h.GL.createContext(t.l,{antialias:!1,alpha:!1,ja:\"undefined\"!=typeof WebGL2RenderingContext?2:1}),t.h.GL.makeContextCurrent(o),d.g=4;break;case 7:if(t.l=document.createElement(\"canvas\"),!(f=t.l.getContext(\"webgl2\",{}))&&!(f=t.l.getContext(\"webgl\",{})))return alert(\"Failed to create WebGL canvas context when passing video frame.\"),d.return();t.G=f,t.h.canvas=t.l,t.h.createContext(t.l,!0,!0,{});case 4:t.i=new t.h.SolutionWasm,t.Z=!1,d.g=0}}))}function Ve(t,e){var n,r;return M((function(i){return e in t.H?i.return(t.H[e]):(n=t.locateFile(e,\"\"),r=fetch(n).then((function(t){return t.arrayBuffer()})),t.H[e]=r,i.return(r))}))}function He(t,e,n){var r,i,o,u,s,c,h,l,f,d,p,g,v,y;return M((function(w){switch(w.g){case 1:if(!n)return w.return(e);for(r={},i=0,o=a(Object.keys(n)),u=o.next();!u.done;u=o.next())s=u.value,\"string\"!=typeof(c=n[s])&&\"texture\"===c.type&&void 0!==e[c.stream]&&++i;1<i&&(t.I=!1),h=a(Object.keys(n)),u=h.next();case 2:if(u.done){w.g=4;break}if(l=u.value,\"string\"==typeof(f=n[l]))return v=r,y=l,m(w,function(t,e,n){var r;return M((function(i){return\"number\"==typeof n||n instanceof Uint8Array||n instanceof t.h.Uint8BlobList?i.return(n):n instanceof t.h.Texture2dDataOut?((r=t.u[e])||(r=new Ie(t.h,t.G),t.u[e]=r),i.return(Le(r,n,t.I))):i.return(void 0)}))}(t,l,e[f]),14);if(d=e[f.stream],\"detection_list\"===f.type){if(d){for(var b=d.getRectList(),x=d.getLandmarksList(),A=d.getClassificationsList(),T=[],E=0;E<b.size();++E){var S=ee(b.get(E),Me,Ae);S={boundingBox:{xCenter:bt(S,1),yCenter:bt(S,2),height:bt(S,3),width:bt(S,4),rotation:bt(S,5,0),rectId:At(S,6)},landmarks:xt(ee(x.get(E),we,be),me,1).map(Re),V:ke(ee(A.get(E),pe,ge))},T.push(S)}b=T}else b=[];r[l]=b,w.g=7;break}if(\"proto_list\"===f.type){if(d){for(b=Array(d.size()),x=0;x<d.size();x++)b[x]=d.get(x);d.delete()}else b=[];r[l]=b,w.g=7;break}if(void 0===d){w.g=3;break}if(\"float_list\"===f.type){r[l]=d,w.g=7;break}if(\"proto\"===f.type){r[l]=d,w.g=7;break}if(\"texture\"!==f.type)throw Error(\"Unknown output config type: '\"+f.type+\"'\");return(p=t.u[l])||(p=new Ie(t.h,t.G),t.u[l]=p),m(w,Le(p,d,t.I),13);case 13:g=w.h,r[l]=g;case 7:f.transform&&r[l]&&(r[l]=f.transform(r[l])),w.g=3;break;case 14:v[y]=w.h;case 3:u=h.next(),w.g=2;break;case 4:return w.return(r)}}))}function Ke(t,e){for(var n=e.name||\"$\",r=[].concat(u(e.wants)),i=new t.h.StringList,o=a(e.wants),s=o.next();!s.done;s=o.next())i.push_back(s.value);o=t.h.PacketListener.implement({onResults:function(i){for(var o={},a=0;a<e.wants.length;++a)o[r[a]]=i.get(a);var u=t.listeners[n];u&&(t.D=He(t,o,e.outs).then((function(n){n=u(n);for(var i=0;i<e.wants.length;++i){var a=o[r[i]];\"object\"==typeof a&&a.hasOwnProperty&&a.hasOwnProperty(\"delete\")&&a.delete()}n&&(t.D=n)})))}}),t.i.attachMultiListener(i,o),i.delete()}function We(t){var e=this;t=t||{};var n={url:\"face_detection_short.binarypb\"},r={type:1,graphOptionXref:{calculatorType:\"TensorsToDetectionsCalculator\",calculatorName:\"facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator\",fieldName:\"min_score_thresh\"}};this.g=new Ue({locateFile:t.locateFile,files:[{data:!0,url:\"face_detection_short.binarypb\"},{data:!0,url:\"face_detection_short_range.tflite\"},{simd:!0,url:\"face_detection_solution_simd_wasm_bin.js\"},{simd:!1,url:\"face_detection_solution_wasm_bin.js\"}],graph:n,listeners:[{wants:[\"detections\",\"image_transformed\"],outs:{image:\"image_transformed\",detections:{type:\"detection_list\",stream:\"detections\"}}}],inputs:{image:{type:\"video\",stream:\"input_frames_gpu\"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:\"InferenceCalculator\",fieldName:\"use_cpu_inference\"},default:\"object\"==typeof window&&void 0!==window.navigator&&(\"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod\".split(\";\").includes(navigator.platform)||navigator.userAgent.includes(\"Mac\")&&\"ontouchend\"in document)},selfieMode:{type:0,graphOptionXref:{calculatorType:\"GlScalerCalculator\",calculatorIndex:1,fieldName:\"flip_horizontal\"}},model:{type:0,onChange:function(t){var i,o,u,s,c;return M((function(h){switch(h.g){case 1:i=a(\"short\"===t?[\"face_detection_short_range.tflite\"]:[\"face_detection_full_range_sparse.tflite\"]),o=i.next();case 2:if(o.done){h.g=4;break}return u=o.value,s=\"third_party/mediapipe/modules/face_detection/\"+u,m(h,Ve(e.g,u),5);case 5:c=h.h,e.g.overrideFile(s,c),o=i.next(),h.g=2;break;case 4:return n.url=\"short\"===t?\"face_detection_short.binarypb\":\"face_detection_full.binarypb\",r.graphOptionXref.calculatorName=\"short\"===t?\"facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator\":\"facedetectionfullrangegpu__facedetectionfullrangecommon__TensorsToDetectionsCalculator\",h.return(!0)}}))}},minDetectionConfidence:r}})}(t=Ue.prototype).close=function(){return this.i&&this.i.delete(),Promise.resolve()},t.reset=function(){var t=this;return M((function(e){t.i&&(t.i.reset(),t.o={},t.u={}),e.g=0}))},t.setOptions=function(t,e){var n=this;if(e=e||this.g.options){for(var r=[],i=[],o={},u=a(Object.keys(t)),s=u.next();!s.done;o={R:o.R,S:o.S},s=u.next()){var c=s.value;c in this.j&&this.j[c]===t[c]||(this.j[c]=t[c],void 0!==(s=e[c])&&(s.onChange&&(o.R=s.onChange,o.S=t[c],r.push(function(t){return function(){return M((function(e){if(1==e.g)return m(e,t.R(t.S),2);!0===e.h&&(n.s=!0),e.g=0}))}}(o))),s.graphOptionXref&&(c={valueNumber:1===s.type?t[c]:0,valueBoolean:0===s.type&&t[c],valueString:2===s.type?t[c]:\"\"},s=Object.assign(Object.assign(Object.assign({},{calculatorName:\"\",calculatorIndex:0}),s.graphOptionXref),c),i.push(s))))}0===r.length&&0===i.length||(this.s=!0,this.B=(void 0===this.B?[]:this.B).concat(i),this.A=(void 0===this.A?[]:this.A).concat(r))}},t.initialize=function(){var t=this;return M((function(e){return 1==e.g?m(e,Ne(t),2):3!=e.g?m(e,function(t){var e,n,r,i,o,u,s,c;return M((function(h){if(1==h.g)return t.g.graph&&t.g.graph.url&&t.Y===t.g.graph.url?h.return():(t.s=!0,t.g.graph&&t.g.graph.url?(t.Y=t.g.graph.url,m(h,Ve(t,t.g.graph.url),3)):void(h.g=2));for(2!=h.g&&(e=h.h,t.i.loadGraph(e)),n=a(Object.keys(t.C)),r=n.next();!r.done;r=n.next())i=r.value,t.i.overrideFile(i,t.C[i]);if(t.C={},t.g.listeners)for(o=a(t.g.listeners),u=o.next();!u.done;u=o.next())s=u.value,Ke(t,s);c=t.j,t.j={},t.setOptions(c),h.g=0}))}(t),3):m(e,function(t){var e,n,r,i,o,u;return M((function(s){switch(s.g){case 1:if(!t.s)return s.return();if(!t.A){s.g=2;break}e=a(t.A),n=e.next();case 3:if(n.done){s.g=5;break}return m(s,(0,n.value)(),4);case 4:n=e.next(),s.g=3;break;case 5:t.A=void 0;case 2:if(t.B){for(r=new t.h.GraphOptionChangeRequestList,i=a(t.B),o=i.next();!o.done;o=i.next())u=o.value,r.push_back(u);t.i.changeOptions(r),r.delete(),t.B=void 0}t.s=!1,s.g=0}}))}(t),0)}))},t.overrideFile=function(t,e){this.i?this.i.overrideFile(t,e):this.C[t]=e},t.clearOverriddenFiles=function(){this.C={},this.i&&this.i.clearOverriddenFiles()},t.send=function(t,e){var n,r,i,o,u,s,c,h,l,f=this;return M((function(d){switch(d.g){case 1:return f.g.inputs?(n=1e3*(null==e?performance.now():e),m(d,f.D,2)):d.return();case 2:return m(d,f.initialize(),3);case 3:for(r=new f.h.PacketDataList,i=a(Object.keys(t)),o=i.next();!o.done;o=i.next())if(u=o.value,s=f.g.inputs[u]){t:{var p=t[u];switch(s.type){case\"video\":var g=f.o[s.stream];if(g||(g=new Ie(f.h,f.G),f.o[s.stream]=g),0===g.l&&(g.l=g.h.createTexture()),\"undefined\"!=typeof HTMLVideoElement&&p instanceof HTMLVideoElement)var v=p.videoWidth,y=p.videoHeight;else\"undefined\"!=typeof HTMLImageElement&&p instanceof HTMLImageElement?(v=p.naturalWidth,y=p.naturalHeight):(v=p.width,y=p.height);y={glName:g.l,width:v,height:y},(v=g.g).canvas.width=y.width,v.canvas.height=y.height,v.activeTexture(v.TEXTURE0),g.h.bindTexture2d(g.l),v.texImage2D(v.TEXTURE_2D,0,v.RGBA,v.RGBA,v.UNSIGNED_BYTE,p),g.h.bindTexture2d(0),g=y;break t;case\"detections\":for((g=f.o[s.stream])||(g=new Be(f.h),f.o[s.stream]=g),g.data||(g.data=new g.g.DetectionListData),g.data.reset(p.length),y=0;y<p.length;++y){v=p[y];var w=g.data,b=w.setBoundingBox,x=y,M=v.boundingBox,A=new Me;if(yt(A,1,M.xCenter),yt(A,2,M.yCenter),yt(A,3,M.height),yt(A,4,M.width),yt(A,5,M.rotation),yt(A,6,M.rectId),M=re(A,Ae),b.call(w,x,M),v.landmarks)for(w=0;w<v.landmarks.length;++w){var T=!!(A=v.landmarks[w]).visibility;x=(b=g.data).addNormalizedLandmark,M=y,A=Object.assign(Object.assign({},A),{visibility:T?A.visibility:0}),yt(T=new me,1,A.x),yt(T,2,A.y),yt(T,3,A.z),A.visibility&&yt(T,4,A.visibility),A=re(T,ye),x.call(b,M,A)}if(v.V)for(w=0;w<v.V.length;++w)x=(b=g.data).addClassification,M=y,A=v.V[w],yt(T=new fe,2,A.ga),A.index&&yt(T,1,A.index),A.label&&yt(T,3,A.label),A.displayName&&yt(T,4,A.displayName),A=re(T,de),x.call(b,M,A)}g=g.data;break t;default:g={}}}switch(c=g,h=s.stream,s.type){case\"video\":r.pushTexture2d(Object.assign(Object.assign({},c),{stream:h,timestamp:n}));break;case\"detections\":(l=c).stream=h,l.timestamp=n,r.pushDetectionList(l);break;default:throw Error(\"Unknown input config type: '\"+s.type+\"'\")}}return f.i.send(r),m(d,f.D,4);case 4:r.delete(),d.g=0}}))},t.onResults=function(t,e){this.listeners[e||\"$\"]=t},E(\"Solution\",Ue),E(\"OptionType\",{BOOL:0,NUMBER:1,ia:2,0:\"BOOL\",1:\"NUMBER\",2:\"STRING\"}),(t=We.prototype).close=function(){return this.g.close(),Promise.resolve()},t.onResults=function(t){this.g.onResults(t)},t.initialize=function(){var t=this;return M((function(e){return m(e,t.g.initialize(),0)}))},t.reset=function(){this.g.reset()},t.send=function(t){var e=this;return M((function(n){return m(n,e.g.send(t),0)}))},t.setOptions=function(t){this.g.setOptions(t)},E(\"FaceDetection\",We),E(\"FACEDETECTION_LIPS\",Te),E(\"FACEDETECTION_LEFT_EYE\",Ee),E(\"FACEDETECTION_LEFT_EYEBROW\",Se),E(\"FACEDETECTION_RIGHT_EYE\",Fe),E(\"FACEDETECTION_RIGHT_EYEBROW\",Ce),E(\"FACEDETECTION_FACE_OVAL\",Oe),E(\"FACEDETECTION_CONTOURS\",_e),E(\"FACEDETECTION_TESSELATION\",[[127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]]),E(\"VERSION\",\"0.4.1646425229\")}).call(D);var z=function(){return z=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},z.apply(this,arguments)};function U(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{s(r.next(t))}catch(t){o(t)}}function u(t){try{s(r.throw(t))}catch(t){o(t)}}function s(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,u)}s((r=r.apply(t,e||[])).next())}))}function N(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},\"function\"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(n)throw new TypeError(\"Generator is already executing.\");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!((i=(i=a.trys).length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}var V=[\"rightEye\",\"leftEye\",\"noseTip\",\"mouthCenter\",\"rightEarTragion\",\"leftEarTragion\"],H={modelType:\"short\",runtime:\"mediapipe\",maxFaces:1},K=function(){function t(t){var e=this;this.width=0,this.height=0,this.selfieMode=!1,this.faceDetectorSolution=new P.FaceDetection({locateFile:function(e,n){if(t.solutionPath){var r=t.solutionPath.replace(/\\/+$/,\"\");return\"\".concat(r,\"/\").concat(e)}return\"\".concat(n,\"/\").concat(e)}}),this.faceDetectorSolution.setOptions({selfieMode:this.selfieMode,model:t.modelType}),this.faceDetectorSolution.onResults((function(t){if(e.height=t.image.height,e.width=t.image.width,e.faces=[],null!==t.detections)for(var n=0,r=t.detections;n<r.length;n++){var i=r[n];e.faces.push(e.normalizedToAbsolute(i.landmarks,(void 0,void 0,void 0,{xMin:a=(o=i.boundingBox).xCenter-o.width/2,xMax:a+o.width,yMin:u=o.yCenter-o.height/2,yMax:u+o.height,width:o.width,height:o.height})))}var o,a,u}))}return t.prototype.normalizedToAbsolute=function(t,e){var n=this;return{keypoints:t.map((function(t,e){return{x:t.x*n.width,y:t.y*n.height,name:V[e]}})),box:{xMin:e.xMin*this.width,yMin:e.yMin*this.height,xMax:e.xMax*this.width,yMax:e.yMax*this.height,width:e.width*this.width,height:e.height*this.height}}},t.prototype.estimateFaces=function(t,e){return U(this,void 0,void 0,(function(){var i,o;return N(this,(function(a){switch(a.label){case 0:return e&&e.flipHorizontal&&e.flipHorizontal!==this.selfieMode&&(this.selfieMode=e.flipHorizontal,this.faceDetectorSolution.setOptions({selfieMode:this.selfieMode})),t instanceof n?(o=ImageData.bind,[4,r.toPixels(t)]):[3,2];case 1:return i=new(o.apply(ImageData,[void 0,a.sent(),t.shape[1],t.shape[0]])),[3,3];case 2:i=t,a.label=3;case 3:return t=i,[4,this.faceDetectorSolution.send({image:t})];case 4:return a.sent(),[2,this.faces]}}))}))},t.prototype.dispose=function(){this.faceDetectorSolution.close()},t.prototype.reset=function(){this.faceDetectorSolution.reset(),this.width=0,this.height=0,this.faces=null,this.selfieMode=!1},t.prototype.initialize=function(){return this.faceDetectorSolution.initialize()},t}();function W(t){return U(this,void 0,void 0,(function(){var e,n;return N(this,(function(r){switch(r.label){case 0:return e=function(t){if(null==t)return z({},H);var e=z({},t);return e.runtime=\"mediapipe\",null==e.modelType&&(e.modelType=H.modelType),null==e.maxFaces&&(e.maxFaces=H.maxFaces),e}(t),[4,(n=new K(e)).initialize()];case 1:return r.sent(),[2,n]}}))}))}function G(t){return t instanceof n?{height:t.shape[0],width:t.shape[1]}:{height:t.height,width:t.width}}function X(t){return t instanceof n?t:r.fromPixels(t)}function Y(t,e){M.assert(0!==t.width,(function(){return\"\".concat(e,\" width cannot be 0.\")})),M.assert(0!==t.height,(function(){return\"\".concat(e,\" height cannot be 0.\")}))}function J(t,e,n){var r=e.outputTensorSize,i=e.keepAspectRatio,o=e.borderMode,h=e.outputTensorFloatRange,f=G(t),g=function(t,e){return e?{xCenter:e.xCenter*t.width,yCenter:e.yCenter*t.height,width:e.width*t.width,height:e.height*t.height,rotation:e.rotation}:{xCenter:.5*t.width,yCenter:.5*t.height,width:t.width,height:t.height,rotation:0}}(f,n),v=function(t,e,n){if(void 0===n&&(n=!1),!n)return{top:0,left:0,right:0,bottom:0};var r=e.height,i=e.width;Y(e,\"targetSize\"),Y(t,\"roi\");var o,a,u=r/i,s=t.height/t.width,c=0,h=0;return u>s?(o=t.width,a=t.width*u,h=(1-s/u)/2):(o=t.height/u,a=t.height,c=(1-u/s)/2),t.width=o,t.height=a,{top:h,left:c,right:c,bottom:h}}(g,r,i),m=function(t,e,n,r){var i=t.width,o=t.height,a=r?-1:1,u=Math.cos(t.rotation),s=Math.sin(t.rotation),c=t.xCenter,h=t.yCenter,l=1/e,f=1/n,d=new Array(16);return d[0]=i*u*a*l,d[1]=-o*s*l,d[2]=0,d[3]=(-.5*i*u*a+.5*o*s+c)*l,d[4]=i*s*a*f,d[5]=o*u*f,d[6]=0,d[7]=(-.5*o*u-.5*i*s*a+h)*f,d[8]=0,d[9]=0,d[10]=i*l,d[11]=0,d[12]=0,d[13]=0,d[14]=0,d[15]=1,function(t){if(16!==t.length)throw new Error(\"Array length must be 16 but got \".concat(t.length));return[[t[0],t[1],t[2],t[3]],[t[4],t[5],t[6],t[7]],[t[8],t[9],t[10],t[11]],[t[12],t[13],t[14],t[15]]]}(d)}(g,f.width,f.height,!1),y=s((function(){var e=X(t),n=a(function(t,e,n){return Y(n,\"inputResolution\"),[1/n.width*t[0][0]*e.width,1/n.height*t[0][1]*e.width,t[0][3]*e.width,1/n.width*t[1][0]*e.height,1/n.height*t[1][1]*e.height,t[1][3]*e.height,0,0]}(m,f,r),[1,8]),i=\"zero\"===o?\"constant\":\"nearest\",g=u.transform(l(c(e,\"float32\")),n,\"bilinear\",i,0,[r.height,r.width]);return null!=h?function(t,e){var n=function(t,e,n,r){var i=(r-n)/255;return{scale:i,offset:n-0*i}}(0,0,e[0],e[1]);return s((function(){return d(p(t,n.scale),n.offset)}))}(g,h):g}));return{imageTensor:y,padding:v,transformationMatrix:m}}function q(t){null==t.reduceBoxesInLowestLayer&&(t.reduceBoxesInLowestLayer=!1),null==t.interpolatedScaleAspectRatio&&(t.interpolatedScaleAspectRatio=1),null==t.fixedAnchorSize&&(t.fixedAnchorSize=!1);for(var e=[],n=0;n<t.numLayers;){for(var r=[],i=[],o=[],a=[],u=n;u<t.strides.length&&t.strides[u]===t.strides[n];){var s=$(t.minScale,t.maxScale,u,t.strides.length);if(0===u&&t.reduceBoxesInLowestLayer)o.push(1),o.push(2),o.push(.5),a.push(.1),a.push(s),a.push(s);else{for(var c=0;c<t.aspectRatios.length;++c)o.push(t.aspectRatios[c]),a.push(s);if(t.interpolatedScaleAspectRatio>0){var h=u===t.strides.length-1?1:$(t.minScale,t.maxScale,u+1,t.strides.length);a.push(Math.sqrt(s*h)),o.push(t.interpolatedScaleAspectRatio)}}u++}for(var l=0;l<o.length;++l){var f=Math.sqrt(o[l]);r.push(a[l]/f),i.push(a[l]*f)}var d=0,p=0;if(t.featureMapHeight.length>0)d=t.featureMapHeight[n],p=t.featureMapWidth[n];else{var g=t.strides[n];d=Math.ceil(t.inputSizeHeight/g),p=Math.ceil(t.inputSizeWidth/g)}for(var v=0;v<d;++v)for(var m=0;m<p;++m)for(var y=0;y<r.length;++y){var w={xCenter:(m+t.anchorOffsetX)/p,yCenter:(v+t.anchorOffsetY)/d,width:0,height:0};t.fixedAnchorSize?(w.width=1,w.height=1):(w.width=i[y],w.height=r[y]),e.push(w)}n=u}return e}function $(t,e,n,r){return 1===r?.5*(t+e):t+(e-t)*n/(r-1)}function Z(t,e){var n=e[0],r=e[1];return[n*t[0]+r*t[1]+t[3],n*t[4]+r*t[5]+t[7]]}function Q(t,e,n,r){return U(this,void 0,void 0,(function(){var r,s,c,h,l;return N(this,(function(f){switch(f.label){case 0:return t.sort((function(t,e){return Math.max.apply(Math,e.score)-Math.max.apply(Math,t.score)})),r=a(t.map((function(t){return[t.locationData.relativeBoundingBox.yMin,t.locationData.relativeBoundingBox.xMin,t.locationData.relativeBoundingBox.yMax,t.locationData.relativeBoundingBox.xMax]}))),s=i(t.map((function(t){return t.score[0]}))),[4,u.nonMaxSuppressionAsync(r,s,e,n)];case 1:return[4,(c=f.sent()).array()];case 2:return h=f.sent(),l=t.filter((function(t,e){return h.indexOf(e)>-1})),o([r,s,c]),[2,l]}}))}))}function tt(t,e,n){return U(this,void 0,void 0,(function(){var r,i,a,u,c;return N(this,(function(l){switch(l.label){case 0:return r=t[0],i=t[1],a=function(t,e,n){return s((function(){var r,i,o,a;n.reverseOutputOrder?(i=h(f(t,[0,n.boxCoordOffset+0],[-1,1])),r=h(f(t,[0,n.boxCoordOffset+1],[-1,1])),a=h(f(t,[0,n.boxCoordOffset+2],[-1,1])),o=h(f(t,[0,n.boxCoordOffset+3],[-1,1]))):(r=h(f(t,[0,n.boxCoordOffset+0],[-1,1])),i=h(f(t,[0,n.boxCoordOffset+1],[-1,1])),o=h(f(t,[0,n.boxCoordOffset+2],[-1,1])),a=h(f(t,[0,n.boxCoordOffset+3],[-1,1]))),i=d(p(g(i,n.xScale),e.w),e.x),r=d(p(g(r,n.yScale),e.h),e.y),n.applyExponentialOnBoxSize?(o=p(v(g(o,n.hScale)),e.h),a=p(v(g(a,n.wScale)),e.w)):(o=p(g(o,n.hScale),e.h),a=p(g(a,n.wScale),e.h));var u=m(r,g(o,2)),s=m(i,g(a,2)),c=d(r,g(o,2)),l=d(i,g(a,2)),b=y([w(u,[n.numBoxes,1]),w(s,[n.numBoxes,1]),w(c,[n.numBoxes,1]),w(l,[n.numBoxes,1])],1);if(n.numKeypoints)for(var x=0;x<n.numKeypoints;++x){var M=n.keypointCoordOffset+x*n.numValuesPerKeypoint,A=void 0,T=void 0;n.reverseOutputOrder?(A=h(f(t,[0,M],[-1,1])),T=h(f(t,[0,M+1],[-1,1]))):(T=h(f(t,[0,M],[-1,1])),A=h(f(t,[0,M+1],[-1,1])));var E=d(p(g(A,n.xScale),e.w),e.x),S=d(p(g(T,n.yScale),e.h),e.y);b=y([b,w(E,[n.numBoxes,1]),w(S,[n.numBoxes,1])],1)}return b}))}(i,e,n),u=s((function(){var t=r;return n.sigmoidScore?(null!=n.scoreClippingThresh&&(t=b(r,-n.scoreClippingThresh,n.scoreClippingThresh)),t=x(t)):t})),[4,et(a,u,n)];case 1:return c=l.sent(),o([a,u]),[2,c]}}))}))}function et(t,e,n){return U(this,void 0,void 0,(function(){var r,i,o,a,u,s,c,h,l,f,d,p;return N(this,(function(g){switch(g.label){case 0:return r=[],[4,t.data()];case 1:return i=g.sent(),[4,e.data()];case 2:for(o=g.sent(),a=0;a<n.numBoxes;++a)if(!(null!=n.minScoreThresh&&o[a]<n.minScoreThresh||(u=a*n.numCoords,s=nt(i[u+0],i[u+1],i[u+2],i[u+3],o[a],n.flipVertically,a),(c=s.locationData.relativeBoundingBox).width<0||c.height<0))){if(n.numKeypoints>0)for((h=s.locationData).relativeKeypoints=[],l=n.numKeypoints*n.numValuesPerKeypoint,f=0;f<l;f+=n.numValuesPerKeypoint)d=u+n.keypointCoordOffset+f,p={x:i[d+0],y:n.flipVertically?1-i[d+1]:i[d+1]},h.relativeKeypoints.push(p);r.push(s)}return[2,r]}}))}))}function nt(t,e,n,r,i,o,a){return{score:[i],ind:a,locationData:{relativeBoundingBox:{xMin:e,yMin:o?1-n:t,xMax:r,yMax:o?1-t:n,width:r-e,height:n-t}}}}var rt,it={reduceBoxesInLowestLayer:!1,interpolatedScaleAspectRatio:1,featureMapHeight:[],featureMapWidth:[],numLayers:4,minScale:.1484375,maxScale:.75,inputSizeHeight:128,inputSizeWidth:128,anchorOffsetX:.5,anchorOffsetY:.5,strides:[8,16,16,16],aspectRatios:[1],fixedAnchorSize:!0},ot={reduceBoxesInLowestLayer:!1,interpolatedScaleAspectRatio:0,featureMapHeight:[],featureMapWidth:[],numLayers:1,minScale:.1484375,maxScale:.75,inputSizeHeight:192,inputSizeWidth:192,anchorOffsetX:.5,anchorOffsetY:.5,strides:[4],aspectRatios:[1],fixedAnchorSize:!0},at={runtime:\"tfjs\",modelType:\"short\",maxFaces:1,detectorModelUrl:\"https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1\"},ut={applyExponentialOnBoxSize:!1,flipVertically:!1,ignoreClasses:[],numClasses:1,numBoxes:896,numCoords:16,boxCoordOffset:0,keypointCoordOffset:4,numKeypoints:6,numValuesPerKeypoint:2,sigmoidScore:!0,scoreClippingThresh:100,reverseOutputOrder:!0,xScale:128,yScale:128,hScale:128,wScale:128,minScoreThresh:.5},st={applyExponentialOnBoxSize:!1,flipVertically:!1,ignoreClasses:[],numClasses:1,numBoxes:2304,numCoords:16,boxCoordOffset:0,keypointCoordOffset:4,numKeypoints:6,numValuesPerKeypoint:2,sigmoidScore:!0,scoreClippingThresh:100,reverseOutputOrder:!0,xScale:192,yScale:192,hScale:192,wScale:192,minScoreThresh:.6},ct={outputTensorSize:{width:128,height:128},keepAspectRatio:!0,outputTensorFloatRange:[-1,1],borderMode:\"zero\"},ht={outputTensorSize:{width:192,height:192},keepAspectRatio:!0,outputTensorFloatRange:[-1,1],borderMode:\"zero\"},lt=function(){function t(t,e,n){this.detectorModel=e,this.maxFaces=n,\"full\"===t?(this.imageToTensorConfig=ht,this.tensorsToDetectionConfig=st,this.anchors=q(ot)):(this.imageToTensorConfig=ct,this.tensorsToDetectionConfig=ut,this.anchors=q(it));var r=i(this.anchors.map((function(t){return t.width}))),o=i(this.anchors.map((function(t){return t.height}))),a=i(this.anchors.map((function(t){return t.xCenter}))),u=i(this.anchors.map((function(t){return t.yCenter})));this.anchorTensor={x:a,y:u,w:r,h:o}}return t.prototype.dispose=function(){this.detectorModel.dispose(),o([this.anchorTensor.x,this.anchorTensor.y,this.anchorTensor.w,this.anchorTensor.h])},t.prototype.reset=function(){},t.prototype.detectFaces=function(t,e){return void 0===e&&(e=!1),U(this,void 0,void 0,(function(){var n,r,i,a,d,p,g,v,m,y,w;return N(this,(function(b){switch(b.label){case 0:return null==t?(this.reset(),[2,[]]):(n=s((function(){var n=c(X(t),\"float32\");return e&&(n=h(u.flipLeftRight(l(n,0)),[0])),n})),r=J(n,this.imageToTensorConfig),i=r.imageTensor,a=r.transformationMatrix,d=this.detectorModel.execute(i,\"Identity:0\"),p=function(t){return s((function(){var e=function(t){return s((function(){return[f(t,[0,0,0],[1,-1,1]),f(t,[0,0,1],[1,-1,-1])]}))}(t),n=e[0],r=e[1];return{boxes:h(r),logits:h(n)}}))}(d),g=p.boxes,[4,tt([v=p.logits,g],this.anchorTensor,this.tensorsToDetectionConfig)]);case 1:return 0===(m=b.sent()).length?(o([n,i,d,v,g]),[2,m]):[4,Q(m,this.maxFaces,.3)];case 2:return y=b.sent(),w=function(t,e){void 0===t&&(t=[]);var n,r=(n=e,[].concat.apply([],n));return t.forEach((function(t){var e=t.locationData;e.relativeKeypoints.forEach((function(t){var e=Z(r,[t.x,t.y]),n=e[0],i=e[1];t.x=n,t.y=i}));var n=e.relativeBoundingBox,i=Number.MAX_VALUE,o=Number.MAX_VALUE,a=Number.MIN_VALUE,u=Number.MIN_VALUE;[[n.xMin,n.yMin],[n.xMin+n.width,n.yMin],[n.xMin+n.width,n.yMin+n.height],[n.xMin,n.yMin+n.height]].forEach((function(t){var e=Z(r,t),n=e[0],s=e[1];i=Math.min(i,n),a=Math.max(a,n),o=Math.min(o,s),u=Math.max(u,s)})),e.relativeBoundingBox={xMin:i,xMax:a,yMin:o,yMax:u,width:a-i,height:u-o}})),t}(y,a),o([n,i,d,v,g]),[2,w]}}))}))},t.prototype.estimateFaces=function(t,e){return U(this,void 0,void 0,(function(){var n,r;return N(this,(function(i){return n=G(t),r=!!e&&e.flipHorizontal,[2,this.detectFaces(t,r).then((function(t){return t.map((function(t){for(var e=t.locationData.relativeKeypoints.map((function(t,e){return z(z({},t),{x:t.x*n.width,y:t.y*n.height,name:V[e]})})),r=t.locationData.relativeBoundingBox,i=0,o=[\"width\",\"xMax\",\"xMin\"];i<o.length;i++)r[o[i]]*=n.width;for(var a=0,u=[\"height\",\"yMax\",\"yMin\"];a<u.length;a++)r[u[a]]*=n.height;return{keypoints:e,box:r}}))}))]}))}))},t}();function ft(t){return U(this,void 0,void 0,(function(){var e,n,r;return N(this,(function(i){switch(i.label){case 0:return e=function(t){if(null==t)return z({},at);var e=z({},t);return null==e.modelType&&(e.modelType=at.modelType),null==e.maxFaces&&(e.maxFaces=at.maxFaces),null==e.detectorModelUrl&&(\"full\"===e.modelType?e.detectorModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/face_detection/full/1\":e.detectorModelUrl=\"https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1\"),e}(t),n=\"string\"==typeof e.detectorModelUrl&&e.detectorModelUrl.indexOf(\"https://tfhub.dev\")>-1,[4,T(e.detectorModelUrl,{fromTFHub:n})];case 1:return r=i.sent(),[2,new lt(e.modelType,r,e.maxFaces)]}}))}))}function dt(t,e){return U(this,void 0,void 0,(function(){var n,r;return N(this,(function(i){if(t===rt.MediaPipeFaceDetector){if(r=void 0,null!=(n=e)){if(\"tfjs\"===n.runtime)return[2,ft(n)];if(\"mediapipe\"===n.runtime)return[2,W(n)];r=n.runtime}throw new Error(\"Expect modelConfig.runtime to be either 'tfjs' \"+\"or 'mediapipe', but got \".concat(r))}throw new Error(\"\".concat(t,\" is not a supported model name.\"))}))}))}function pt(t){return t.width*t.height}function gt(t){var e=t.xCenter-t.width/2,n=e+t.width,r=t.yCenter-t.height/2;return{xMin:e,xMax:n,yMin:r,yMax:r+t.height,width:t.width,height:t.height}}function vt(t,e){var n=gt(t),r=gt(e);if(!function(t,e){return!(t.xMax<e.xMin||e.xMax<t.xMin||t.yMax<e.yMin||e.yMax<t.yMin)}(n,r))return 0;var i=pt(function(t,e){var n=Math.max(t.xMin,e.xMin),r=Math.min(t.xMax,e.xMax),i=Math.max(t.yMin,e.yMin),o=Math.min(t.yMax,e.yMax);return{xMin:n,xMax:r,yMin:i,yMax:o,width:Math.max(r-n,0),height:Math.max(o-i,0)}}(n,r)),o=pt(n)+pt(r)-i;return o>0?i/o:0}function mt(t,e,n,r){var i=t.width,o=t.height,a=r?-1:1,u=Math.cos(t.rotation),s=Math.sin(t.rotation),c=t.xCenter,h=t.yCenter,l=1/e,f=1/n,d=new Array(16);return d[0]=i*u*a*l,d[1]=-o*s*l,d[2]=0,d[3]=(-.5*i*u*a+.5*o*s+c)*l,d[4]=i*s*a*f,d[5]=o*u*f,d[6]=0,d[7]=(-.5*o*u-.5*i*s*a+h)*f,d[8]=0,d[9]=0,d[10]=i*l,d[11]=0,d[12]=0,d[13]=0,d[14]=0,d[15]=1,function(t){if(16!==t.length)throw new Error(\"Array length must be 16 but got \"+t.length);return[[t[0],t[1],t[2],t[3]],[t[4],t[5],t[6],t[7]],[t[8],t[9],t[10],t[11]],[t[12],t[13],t[14],t[15]]]}(d)}function yt(t){return t instanceof e.Tensor?{height:t.shape[0],width:t.shape[1]}:{height:t.height,width:t.width}}function wt(t){return t-2*Math.PI*Math.floor((t+Math.PI)/(2*Math.PI))}function bt(t){return t instanceof e.Tensor?t:e.browser.fromPixels(t)}function xt(t,n){e.util.assert(0!==t.width,(function(){return n+\" width cannot be 0.\"})),e.util.assert(0!==t.height,(function(){return n+\" height cannot be 0.\"}))}function Mt(t,n){var r=function(t,e,n,r){var i=e-t,o=r-n;if(0===i)throw new Error(\"Original min and max are both \"+t+\", range cannot be 0.\");var a=o/i;return{scale:a,offset:n-t*a}}(0,255,n[0],n[1]);return e.tidy((function(){return e.add(e.mul(t,r.scale),r.offset)}))}function At(t,n,r){var i=n.outputTensorSize,o=n.keepAspectRatio,a=n.borderMode,u=n.outputTensorFloatRange,s=yt(t),c=function(t,e){return e?{xCenter:e.xCenter*t.width,yCenter:e.yCenter*t.height,width:e.width*t.width,height:e.height*t.height,rotation:e.rotation}:{xCenter:.5*t.width,yCenter:.5*t.height,width:t.width,height:t.height,rotation:0}}(s,r),h=function(t,e,n){if(void 0===n&&(n=!1),!n)return{top:0,left:0,right:0,bottom:0};var r=e.height,i=e.width;xt(e,\"targetSize\"),xt(t,\"roi\");var o,a,u=r/i,s=t.height/t.width,c=0,h=0;return u>s?(o=t.width,a=t.width*u,h=(1-s/u)/2):(o=t.height/u,a=t.height,c=(1-u/s)/2),t.width=o,t.height=a,{top:h,left:c,right:c,bottom:h}}(c,i,o),l=mt(c,s.width,s.height,!1),f=e.tidy((function(){var n=bt(t),r=e.tensor2d(function(t,e,n){return xt(n,\"inputResolution\"),[1/n.width*t[0][0]*e.width,1/n.height*t[0][1]*e.width,t[0][3]*e.width,1/n.width*t[1][0]*e.height,1/n.height*t[1][1]*e.height,t[1][3]*e.height,0,0]}(l,s,i),[1,8]),o=\"zero\"===a?\"constant\":\"nearest\",c=e.image.transform(e.expandDims(e.cast(n,\"float32\")),r,\"bilinear\",o,0,[i.height,i.width]);return null!=u?Mt(c,u):c}));return{imageTensor:f,padding:h,transformationMatrix:l}}function Tt(t){return{xCenter:t.xMin+t.width/2,yCenter:t.yMin+t.height/2,width:t.width,height:t.height}}function Et(t){var e=t.relativeKeypoints;if(e.length<=1)throw new Error(\"2 or more keypoints required to calculate a rect.\");var n=Number.MAX_VALUE,r=Number.MAX_VALUE,i=Number.MIN_VALUE,o=Number.MIN_VALUE;return e.forEach((function(t){n=Math.min(n,t.x),i=Math.max(i,t.x),r=Math.min(r,t.y),o=Math.max(o,t.y)})),{xCenter:(n+i)/2,yCenter:(r+o)/2,width:i-n,height:o-r}}function St(t,e,n,r,i){var o=\"rect\"===n?function(t,e,n){var r,i=t.locationData;if(\"boundingbox\"===e)r=Tt(i.boundingBox);else{r=Et(i);var o=n.width,a=n.height;r.xCenter=Math.round(r.xCenter*o),r.yCenter=Math.round(r.yCenter*a),r.width=Math.round(r.width*o),r.height=Math.round(r.height*a)}return r}(t,e,r):function(t,e){var n=t.locationData;return\"boundingbox\"===e?Tt(n.relativeBoundingBox):Et(n)}(t,e);return i&&(o.rotation=function(t,e,n){var r,i=t.locationData,o=n.rotationVectorStartKeypointIndex,a=n.rotationVectorEndKeypointIndex;r=n.rotationVectorTargetAngle?n.rotationVectorTargetAngle:Math.PI*n.rotationVectorTargetAngleDegree/180;var u=i.relativeKeypoints[o].x*e.width,s=i.relativeKeypoints[o].y*e.height,c=i.relativeKeypoints[a].x*e.width,h=i.relativeKeypoints[a].y*e.height;return wt(r-Math.atan2(-(h-s),c-u))}(t,r,i)),o}function Ft(t,e,n){for(var r=0;r<e.length;++r){var i=e[r],o=n[t[r]];o.x=i.x,o.y=i.y}}function Ct(t,e,n,r){if(\"string\"==typeof e){if(\"copy\"===e)for(var i=0;i<n.length;++i)r[t[i]].z=n[i].z}else{var o=function(t,e){for(var n=0,r=0;r<e.length;++r)n+=t[e[r]].z;return n/e.length}(r,e);for(i=0;i<t.length;++i)r[t[i]].z=o}}function Ot(t,e){for(var n=function(t){var e=[].concat.apply([],t.map((function(t){return t.indexesMapping})));if(0===e.length)throw new Error(\"There should be at least one landmark in indexes mapping\");var n=e[0],r=e[0],i=new Set(e);i.forEach((function(t){n=Math.min(n,t),r=Math.max(r,t)}));var o=i.size;if(0!==n)throw new Error(\"Indexes are expected to start with 0 instead of \"+n);if(r+1!==o)throw new Error(\"Indexes should have no gaps but \"+(r-o+1)+\" indexes are missing\");return o}(e),r=new Array(n).fill(null).map(Object),i=0;i<t.length;++i){var o=t[i],a=e[i];if(o.length!==a.indexesMapping.length)throw new Error(\"There are \"+o.length+\" refinement landmarks while mapping has \"+a.indexesMapping.length);Ft(a.indexesMapping,o,r),Ct(a.indexesMapping,a.zRefinement,o,r)}return r}function _t(t,e){return t.map((function(t){var n=E(E({},t),{x:t.x*e.width,y:t.y*e.height});return null!=t.z&&(n.z=t.z*e.width),n}))}function jt(t,e){return\"none\"===t?e:function(t){return 1/(1+Math.exp(-t))}(e)}function kt(t,e,n,r){return S(this,void 0,void 0,(function(){var i,o,a,u,s,c,h,l;return F(this,(function(f){switch(f.label){case 0:return n=n||e.flipHorizontally||!1,r=r||e.flipVertically||!1,i=t.size,o=i/e.numLandmarks,[4,t.data()];case 1:for(a=f.sent(),u=[],s=0;s<e.numLandmarks;++s)c=s*o,(l={x:0,y:0}).x=n?e.inputImageWidth-a[c]:a[c],o>1&&(l.y=r?e.inputImageHeight-a[c+1]:a[c+1]),o>2&&(l.z=a[c+2]),o>3&&(l.score=jt(e.visibilityActivation,a[c+3])),u.push(l);for(h=0;h<u.length;++h)(l=u[h]).x=l.x/e.inputImageWidth,l.y=l.y/e.inputImageHeight,l.z=l.z/e.inputImageWidth/(e.normalizeZ||1);return[2,u]}}))}))}function Rt(t,e,n){var r=t.width,i=t.height,o=t.rotation;if(null==n.rotation&&null==n.rotationDegree||(o=function(t,e){null!=e.rotation?t+=e.rotation:null!=e.rotationDegree&&(t+=Math.PI*e.rotationDegree/180);return wt(t)}(o,n)),0===o)t.xCenter=t.xCenter+r*n.shiftX,t.yCenter=t.yCenter+i*n.shiftY;else{var a=(e.width*r*n.shiftX*Math.cos(o)-e.height*i*n.shiftY*Math.sin(o))/e.width,u=(e.width*r*n.shiftX*Math.sin(o)+e.height*i*n.shiftY*Math.cos(o))/e.height;t.xCenter=t.xCenter+a,t.yCenter=t.yCenter+u}if(n.squareLong){var s=Math.max(r*e.width,i*e.height);r=s/e.width,i=s/e.height}else if(n.squareShort){var c=Math.min(r*e.width,i*e.height);r=c/e.width,i=c/e.height}return t.width=r*n.scaleX,t.height=i*n.scaleY,t}(rt||(rt={})).MediaPipeFaceDetector=\"MediaPipeFaceDetector\";var It={runtime:\"tfjs\",maxFaces:1,refineLandmarks:!1,landmarkModelUrl:\"https://tfhub.dev/mediapipe/tfjs-model/face_landmarks_detection/face_mesh/1\"},Lt={flipHorizontal:!1,staticImageMode:!1},Bt={shiftX:0,shiftY:0,scaleX:1.5,scaleY:1.5,squareLong:!0},Dt={outputTensorSize:{width:192,height:192},outputTensorFloatRange:[0,1],borderMode:\"replicate\"},Pt={numLandmarks:468,inputImageWidth:192,inputImageHeight:192,visibilityActivation:\"none\",flipHorizontally:!1,flipVertically:!1},zt={numLandmarks:80,inputImageWidth:192,inputImageHeight:192,visibilityActivation:\"none\",flipHorizontally:!1,flipVertically:!1},Ut={numLandmarks:71,inputImageWidth:192,inputImageHeight:192,visibilityActivation:\"none\",flipHorizontally:!1,flipVertically:!1},Nt={numLandmarks:5,inputImageWidth:192,inputImageHeight:192,visibilityActivation:\"none\",flipHorizontally:!1,flipVertically:!1},Vt={indexesMapping:Array.from(Array(468).keys()),zRefinement:\"copy\"},Ht={indexesMapping:[61,146,91,181,84,17,314,405,321,375,291,185,40,39,37,0,267,269,270,409,78,95,88,178,87,14,317,402,318,324,308,191,80,81,82,13,312,311,310,415,76,77,90,180,85,16,315,404,320,307,306,184,74,73,72,11,302,303,304,408,62,96,89,179,86,15,316,403,319,325,292,183,42,41,38,12,268,271,272,407],zRefinement:\"none\"},Kt={indexesMapping:[33,7,163,144,145,153,154,155,133,246,161,160,159,158,157,173,130,25,110,24,23,22,26,112,243,247,30,29,27,28,56,190,226,31,228,229,230,231,232,233,244,113,225,224,223,222,221,189,35,124,46,53,52,65,143,111,117,118,119,120,121,128,245,156,70,63,105,66,107,55,193],zRefinement:\"none\"},Wt={indexesMapping:[263,249,390,373,374,380,381,382,362,466,388,387,386,385,384,398,359,255,339,254,253,252,256,341,463,467,260,259,257,258,286,414,446,261,448,449,450,451,452,453,464,342,445,444,443,442,441,413,265,353,276,283,282,295,372,340,346,347,348,349,350,357,465,383,300,293,334,296,336,285,417],zRefinement:\"none\"},Gt={indexesMapping:[468,469,470,471,472],zRefinement:[33,7,163,144,145,153,154,155,133,246,161,160,159,158,157,173]},Xt={indexesMapping:[473,474,475,476,477],zRefinement:[263,249,390,373,374,380,381,382,362,466,388,387,386,385,384,398]};var Yt,Jt=function(){function t(t,e,n,r){this.detector=t,this.landmarkModel=e,this.maxFaces=n,this.withAttention=r,this.prevFaceRectsFromLandmarks=null}return t.prototype.estimateFaces=function(t,n){return S(this,void 0,void 0,(function(){var r,i,o,a,u,s,c,h,l,f,d,p,g,v=this;return F(this,(function(m){switch(m.label){case 0:return r=function(t){if(null==t)return E({},Lt);var e=E({},t);return null==e.flipHorizontal&&(e.flipHorizontal=Lt.flipHorizontal),null==e.staticImageMode&&(e.staticImageMode=Lt.staticImageMode),e}(n),null==t?(this.reset(),[2,[]]):(i=yt(t),o=e.tidy((function(){var n=e.cast(bt(t),\"float32\");if(r.flipHorizontal){n=e.squeeze(e.image.flipLeftRight(e.expandDims(n,0)),[0])}return n})),a=this.prevFaceRectsFromLandmarks,r.staticImageMode||null==a||a.length<this.maxFaces?[4,this.detector.detectFaces(o,!1)]:[3,2]);case 1:return 0===(s=m.sent()).length?(this.reset(),o.dispose(),[2,[]]):(u=s.map((function(t){return v.faceDetectionFrontDetectionToRoi(t,i)})),[3,3]);case 2:u=[],m.label=3;case 3:return y=.5,w=[],[u,a||[]].forEach((function(t){return t.forEach((function(t){(w=w.filter((function(e){return vt(t,e)<=y}))).push(t)}))})),c=w,[4,Promise.all(c.map((function(t){return v.faceLandmark(t,o)})))];case 4:for(h=m.sent(),l=[],this.prevFaceRectsFromLandmarks=[],f=0;f<h.length;++f)null!=(d=h[f])&&(this.prevFaceRectsFromLandmarks.push(this.faceLandmarksToRoi(d,i)),null!=(p=_t(d,i))&&p.forEach((function(t,e){var n=k.get(e);null!=n&&(t.name=n)})),g=R(p),l.push({keypoints:p,box:g.locationData.relativeBoundingBox}));return o.dispose(),[2,l]}var y,w}))}))},t.prototype.dispose=function(){this.detector.dispose(),this.landmarkModel.dispose()},t.prototype.reset=function(){this.detector.reset(),this.prevFaceRectsFromLandmarks=null},t.prototype.faceDetectionFrontDetectionToRoi=function(t,e){return Rt(St(t,\"boundingbox\",\"normRect\",e,{rotationVectorStartKeypointIndex:0,rotationVectorEndKeypointIndex:1,rotationVectorTargetAngleDegree:0}),e,Bt)},t.prototype.faceLandmark=function(t,n){return S(this,void 0,void 0,(function(){var r,i,o,a,u,s,c;return F(this,(function(h){switch(h.label){case 0:return r=At(n,Dt,t).imageTensor,i=[\"output_faceflag\"].concat(this.withAttention?[\"output_mesh_identity\",\"output_lips\",\"Identity_6:0\",\"Identity_1:0\",\"Identity_2:0\",\"Identity_5:0\"]:[\"output_mesh\"]),o=this.landmarkModel.execute(r,i),a=o[0],u=o.slice(1),[4,a.data()];case 1:return h.sent()[0]<.5?(e.dispose(o),e.dispose(r),[2,null]):this.withAttention?[4,this.tensorsToFaceLandmarksWithAttention(u)]:[3,3];case 2:return s=h.sent(),[3,5];case 3:return[4,this.tensorsToFaceLandmarks(u)];case 4:s=h.sent(),h.label=5;case 5:return c=function(t,e,n){void 0===n&&(n={ignoreRotation:!1});for(var r=[],i=0,o=t;i<o.length;i++){var a=o[i],u=a.x-.5,s=a.y-.5,c=n.ignoreRotation?0:e.rotation,h=Math.cos(c)*u-Math.sin(c)*s,l=Math.sin(c)*u+Math.cos(c)*s;h=h*e.width+e.xCenter,l=l*e.height+e.yCenter;var f=a.z*e.width,d=E({},a);d.x=h,d.y=l,d.z=f,r.push(d)}return r}(s,t),e.dispose(o),e.dispose(r),[2,c]}}))}))},t.prototype.tensorsToFaceLandmarks=function(t){return S(this,void 0,void 0,(function(){return F(this,(function(e){return[2,kt(t[0],Pt)]}))}))},t.prototype.tensorsToFaceLandmarksWithAttention=function(t){return S(this,void 0,void 0,(function(){var e,n,r,i,o,a;return F(this,(function(u){switch(u.label){case 0:return[4,kt(t[0],Pt)];case 1:return e=u.sent(),[4,kt(t[1],zt)];case 2:return n=u.sent(),[4,kt(t[3],Ut)];case 3:return r=u.sent(),[4,kt(t[5],Ut)];case 4:return i=u.sent(),[4,kt(t[4],Nt)];case 5:return o=u.sent(),[4,kt(t[2],Nt)];case 6:return a=u.sent(),[2,Ot([e,n,r,i,o,a],[Vt,Ht,Kt,Wt,Gt,Xt])]}}))}))},t.prototype.faceLandmarksToRoi=function(t,e){return Rt(St(R(t),\"boundingbox\",\"normRect\",e,{rotationVectorStartKeypointIndex:33,rotationVectorEndKeypointIndex:263,rotationVectorTargetAngleDegree:0}),e,Bt)},t}();function qt(t){return S(this,void 0,void 0,(function(){var e,n,r,i;return F(this,(function(o){switch(o.label){case 0:return e=function(t){if(null==t)return E({},It);var e=E({},t);return e.runtime=\"tfjs\",null==e.maxFaces&&(e.maxFaces=It.maxFaces),null==e.refineLandmarks&&(e.refineLandmarks=It.refineLandmarks),null==e.landmarkModelUrl&&(e.landmarkModelUrl=e.refineLandmarks?\"https://tfhub.dev/mediapipe/tfjs-model/face_landmarks_detection/attention_mesh/1\":\"https://tfhub.dev/mediapipe/tfjs-model/face_landmarks_detection/face_mesh/1\"),e}(t),n=\"string\"==typeof e.landmarkModelUrl&&e.landmarkModelUrl.indexOf(\"https://tfhub.dev\")>-1,[4,A.loadGraphModel(e.landmarkModelUrl,{fromTFHub:n})];case 1:return r=o.sent(),[4,dt(rt.MediaPipeFaceDetector,{modelType:\"short\",maxFaces:e.maxFaces,detectorModelUrl:e.detectorModelUrl,runtime:e.runtime})];case 2:return i=o.sent(),[2,new Jt(i,r,e.maxFaces,e.refineLandmarks)]}}))}))}function $t(t,e){return S(this,void 0,void 0,(function(){var n,r;return F(this,(function(i){if(t===Yt.MediaPipeFaceMesh){if(r=void 0,null!=(n=e)){if(\"tfjs\"===n.runtime)return[2,qt(n)];if(\"mediapipe\"===n.runtime)return[2,B(n)];r=n.runtime}throw new Error(\"Expect modelConfig.runtime to be either 'tfjs' or 'mediapipe', but got \"+r)}throw new Error(t+\" is not a supported model name.\")}))}))}!function(t){t.MediaPipeFaceMesh=\"MediaPipeFaceMesh\"}(Yt||(Yt={}));var Zt=Object.freeze({__proto__:null,getKeypointIndexByContour:function(t){if(t===Yt.MediaPipeFaceMesh)return O;throw new Error(\"Model \"+t+\" is not supported.\")},getAdjacentPairs:function(t){if(t===Yt.MediaPipeFaceMesh)return _;throw new Error(\"Model \"+t+\" is not supported.\")}});export{Yt as SupportedModels,$t as createDetector,Zt as util};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,QAAgB;AAAkc,IAAI,IAAE,WAAU;AAAC,SAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,aAAQ,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,UAAQ,KAAK,IAAE,UAAU,CAAC,EAAE,QAAO,UAAU,eAAe,KAAK,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,EAAE,CAAC;AAAG,WAAOA;AAAA,EAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAC;AAAE,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,KAAI,MAAI,IAAE,UAAW,SAAS,GAAE,GAAE;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,KAAKA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,MAAMA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAIC;AAAE,MAAAD,GAAE,OAAK,EAAEA,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAa,IAAEA,KAAE,IAAI,EAAG,SAASD,IAAE;AAAC,QAAAA,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAG,KAAK,GAAE,CAAC;AAAA,IAAC;AAAC,OAAG,IAAE,EAAE,MAAMD,IAAE,KAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,IAAE,EAAC,OAAM,GAAE,MAAK,WAAU;AAAC,QAAG,IAAE,EAAE,CAAC,EAAE,OAAM,EAAE,CAAC;AAAE,WAAO,EAAE,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC;AAAE,SAAO,IAAE,EAAC,MAAK,EAAE,CAAC,GAAE,OAAM,EAAE,CAAC,GAAE,QAAO,EAAE,CAAC,EAAC,GAAE,cAAY,OAAO,WAAS,EAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,WAAO;AAAA,EAAI,IAAG;AAAE,WAAS,EAAEE,IAAE;AAAC,WAAO,SAASC,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,YAAG,EAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,eAAK,IAAG,KAAG;AAAC,cAAG,IAAE,GAAE,MAAI,IAAE,IAAEA,GAAE,CAAC,IAAE,EAAE,SAAOA,GAAE,CAAC,IAAE,EAAE,WAAS,IAAE,EAAE,WAAS,EAAE,KAAK,CAAC,GAAE,KAAG,EAAE,SAAO,EAAE,IAAE,EAAE,KAAK,GAAEA,GAAE,CAAC,CAAC,GAAG,KAAK,QAAO;AAAE,kBAAO,IAAE,GAAE,MAAIA,KAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,KAAK,IAAGA,GAAE,CAAC,GAAE;AAAA,YAAC,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAEA;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAO,EAAE,SAAQ,EAAC,OAAMA,GAAE,CAAC,GAAE,MAAK,MAAE;AAAA,YAAE,KAAK;AAAE,gBAAE,SAAQ,IAAEA,GAAE,CAAC,GAAEA,KAAE,CAAC,CAAC;AAAE;AAAA,YAAS,KAAK;AAAE,cAAAA,KAAE,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,YAAS;AAAQ,kBAAG,EAAE,IAAE,EAAE,OAAM,IAAE,EAAE,SAAO,KAAG,EAAE,EAAE,SAAO,CAAC,MAAI,MAAIA,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,IAAG;AAAC,oBAAE;AAAE;AAAA,cAAQ;AAAC,kBAAG,MAAIA,GAAE,CAAC,MAAI,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,IAAG;AAAC,kBAAE,QAAMA,GAAE,CAAC;AAAE;AAAA,cAAK;AAAC,kBAAG,MAAIA,GAAE,CAAC,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,IAAEA;AAAE;AAAA,cAAK;AAAC,kBAAG,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,EAAE,IAAI,KAAKA,EAAC;AAAE;AAAA,cAAK;AAAC,gBAAE,CAAC,KAAG,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,UAAQ;AAAC,UAAAA,KAAE,EAAE,KAAKF,IAAE,CAAC;AAAA,QAAC,SAAOA,IAAE;AAAC,UAAAE,KAAE,CAAC,GAAEF,EAAC,GAAE,IAAE;AAAA,QAAC,UAAC;AAAQ,cAAE,IAAE;AAAA,QAAC;AAAC,YAAG,IAAEE,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,eAAM,EAAC,OAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,QAAO,MAAK,KAAE;AAAA,MAAC,EAAE,CAACA,IAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,MAAI,IAAEA,GAAE,IAAK,SAASA,IAAE;AAAC,WAAOA,GAAE,CAAC;AAAA,EAAC,CAAE;AAAE,SAAO,EAAE,KAAKA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,CAAC,GAAE;AAAC;AAAC,IAAI,IAAE,EAAC,MAAK,EAAE,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,SAAQ,EAAE,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,aAAY,EAAE,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,UAAS,EAAE,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,UAAS,EAAE,CAAC,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,cAAa,EAAE,CAAC,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,CAAC,CAAC,GAAE,WAAU,EAAE,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,UAAS,EAAE,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,CAAC,CAAC,EAAC;AAA91C,IAAg2C,IAAE,CAAC,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAxnyB,IAA0nyB,IAAE,OAAO,QAAQ,CAAC,EAAE,IAAK,SAASA,IAAE;AAAC,MAAI,IAAEA,GAAE,CAAC;AAAE,SAAOA,GAAE,CAAC,EAAE,IAAK,SAASA,IAAE;AAAC,WAAM,CAACA,IAAE,CAAC;AAAA,EAAC,CAAE;AAAC,CAAE,EAAE,KAAK;AAA9tyB,IAAguyB,IAAE,IAAI,IAAI,CAAC;AAAE,SAAS,EAAEA,IAAE;AAAC,WAAQ,IAAE,EAAC,cAAa,EAAC,mBAAkB,CAAC,EAAC,EAAC,GAAE,IAAE,OAAO,kBAAiB,IAAE,OAAO,kBAAiB,IAAE,OAAO,kBAAiB,IAAE,OAAO,kBAAiB,IAAE,GAAE,IAAEA,GAAE,QAAO,EAAE,GAAE;AAAC,QAAI,IAAEA,GAAE,CAAC;AAAE,QAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,EAAE,CAAC,GAAE,EAAE,aAAa,kBAAkB,KAAK,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,aAAa,sBAAoB,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,IAAE,GAAE,QAAO,IAAE,EAAC,GAAE;AAAC;AAAC,IAAI,IAAE,EAAC,SAAQ,aAAY,UAAS,GAAE,iBAAgB,MAAE;AAAE,IAAI,IAAE,WAAU;AAAC,WAAS,EAAE,GAAE;AAAC,QAAII,KAAE;AAAK,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,aAAW,OAAG,KAAK,mBAAiB,IAAM,WAAS,EAAC,YAAW,SAASJ,IAAEI,IAAE;AAAC,aAAO,EAAE,eAAa,EAAE,aAAa,QAAQ,QAAO,EAAE,IAAE,MAAIJ,KAAEI,KAAE,MAAIJ;AAAA,IAAC,EAAC,CAAC,GAAE,KAAK,iBAAiB,WAAW,EAAC,iBAAgB,EAAE,iBAAgB,YAAW,KAAK,YAAW,aAAY,EAAE,SAAQ,CAAC,GAAE,KAAK,iBAAiB,UAAW,SAASA,IAAE;AAAC,UAAGI,GAAE,SAAOJ,GAAE,MAAM,QAAOI,GAAE,QAAMJ,GAAE,MAAM,OAAMI,GAAE,QAAM,CAAC,GAAE,SAAOJ,GAAE,mBAAmB,UAAQC,KAAED,GAAE,oBAAmB,IAAE,GAAE,IAAEC,GAAE,QAAO,KAAI;AAAC,YAAI,IAAEG,GAAE,gBAAgBH,GAAE,CAAC,CAAC;AAAE,QAAAG,GAAE,MAAM,KAAK,EAAC,WAAU,GAAE,KAAI,EAAE,CAAC,EAAE,aAAa,oBAAmB,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO,EAAE,UAAU,kBAAgB,SAASJ,IAAE;AAAC,QAAI,IAAE;AAAK,WAAOA,GAAE,IAAK,SAASA,IAAEI,IAAE;AAAC,UAAI,IAAE,EAAC,GAAEJ,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,QAAO,GAAEA,GAAE,IAAE,EAAE,MAAK,GAAE,IAAE,EAAE,IAAII,EAAC;AAAE,aAAO,QAAM,MAAI,EAAE,OAAK,IAAG;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,gBAAc,SAASJ,IAAEI,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAOA,MAAGA,GAAE,kBAAgBA,GAAE,mBAAiB,KAAK,eAAa,KAAK,aAAWA,GAAE,gBAAe,KAAK,iBAAiB,WAAW,EAAC,YAAW,KAAK,WAAU,CAAC,IAAGJ,cAAe,UAAQ,IAAE,UAAU,MAAK,CAAC,GAAI,gBAAQ,SAASA,EAAC,CAAC,KAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,KAAI,EAAE,MAAM,WAAU,CAAC,QAAO,EAAE,KAAK,GAAEA,GAAE,MAAM,CAAC,GAAEA,GAAE,MAAM,CAAC,CAAC,CAAC,MAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAEA,IAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAOA,KAAE,GAAE,CAAC,GAAE,KAAK,iBAAiB,KAAK,EAAC,OAAMA,GAAC,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,KAAK,KAAK;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,iBAAiB,MAAM;AAAA,EAAC,GAAE,EAAE,UAAU,QAAM,WAAU;AAAC,SAAK,iBAAiB,MAAM,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,MAAK,KAAK,aAAW;AAAA,EAAE,GAAE,EAAE,UAAU,aAAW,WAAU;AAAC,WAAO,KAAK,iBAAiB,WAAW;AAAA,EAAC,GAAE;AAAC,EAAE;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,CAAC;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,mBAAOC,GAAE,UAAQ,aAAY,QAAMA,GAAE,aAAWA,GAAE,WAAS,EAAE,WAAU,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB,EAAE,kBAAiBA;AAAA,UAAC,EAAED,EAAC,GAAE,CAAC,IAAG,IAAE,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,IAAI,IAAE,eAAa,OAAO,aAAW,aAAW,eAAa,OAAO,SAAO,SAAO,eAAa,OAAO,SAAO,SAAO,eAAa,OAAO,OAAK,OAAK,CAAC;AAAnJ,IAAqJ,IAAE,CAAC;AAAA,CAAG,WAAU;AAAC,MAAIA;AAAE,WAAS,EAAEA,IAAE;AAAC,QAAIC,KAAE;AAAE,WAAO,WAAU;AAAC,aAAOA,KAAED,GAAE,SAAO,EAAC,MAAK,OAAG,OAAMA,GAAEC,IAAG,EAAC,IAAE,EAAC,MAAK,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC,MAAI,IAAE,cAAY,OAAO,OAAO,mBAAiB,OAAO,iBAAe,SAASD,IAAEC,IAAEG,IAAE;AAAC,WAAOJ,MAAG,MAAM,aAAWA,MAAG,OAAO,cAAYA,GAAEC,EAAC,IAAEG,GAAE,QAAOJ;AAAA,EAAC;AAAE,MAAI,IAAE,SAASA,IAAE;AAAC,IAAAA,KAAE,CAAC,YAAU,OAAO,cAAY,YAAWA,IAAE,YAAU,OAAO,UAAQ,QAAO,YAAU,OAAO,QAAM,MAAK,YAAU,OAAO,KAAG,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAO,EAAEC,IAAE;AAAC,UAAIG,KAAEJ,GAAEC,EAAC;AAAE,UAAGG,MAAGA,GAAE,QAAM,KAAK,QAAOA;AAAA,IAAC;AAAC,UAAM,MAAM,2BAA2B;AAAA,EAAC,EAAE,IAAI;AAAE,WAAS,EAAEJ,IAAEC,IAAE;AAAC,QAAGA,GAAE,IAAE;AAAC,UAAII,KAAE;AAAE,MAAAL,KAAEA,GAAE,MAAM,GAAG;AAAE,eAAQE,KAAE,GAAEA,KAAEF,GAAE,SAAO,GAAEE,MAAI;AAAC,YAAII,KAAEN,GAAEE,EAAC;AAAE,YAAG,EAAEI,MAAKD,IAAG,OAAM;AAAE,QAAAA,KAAEA,GAAEC,EAAC;AAAA,MAAC;AAAC,OAACL,KAAEA,GAAEC,KAAEG,GAAEL,KAAEA,GAAEA,GAAE,SAAO,CAAC,CAAC,CAAC,MAAIE,MAAG,QAAMD,MAAG,EAAEI,IAAEL,IAAE,EAAC,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,YAAOA,KAAE,EAAC,MAAKA,GAAC,GAAG,OAAO,QAAQ,IAAE,WAAU;AAAC,aAAO;AAAA,IAAI,GAAEA;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,QAAII,KAAE,eAAa,OAAO,UAAQ,OAAO,YAAUJ,GAAE,OAAO,QAAQ;AAAE,WAAOI,KAAEA,GAAE,KAAKJ,EAAC,IAAE,EAAC,MAAK,EAAEA,EAAC,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,QAAG,EAAEA,cAAa,QAAO;AAAC,MAAAA,KAAE,EAAEA,EAAC;AAAE,eAAQC,IAAEG,KAAE,CAAC,GAAE,EAAEH,KAAED,GAAE,KAAK,GAAG,OAAM,CAAAI,GAAE,KAAKH,GAAE,KAAK;AAAE,MAAAD,KAAEI;AAAA,IAAC;AAAC,WAAOJ;AAAA,EAAC;AAAC,IAAE,UAAU,SAASA,IAAE;AAAC,aAASC,GAAED,IAAEC,IAAE;AAAC,WAAK,IAAED,IAAE,EAAE,MAAK,eAAc,EAAC,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC;AAAA,IAAC;AAAC,QAAGD,GAAE,QAAOA;AAAE,IAAAC,GAAE,UAAU,WAAS,WAAU;AAAC,aAAO,KAAK;AAAA,IAAC;AAAE,QAAIM,KAAE,oBAAkB,MAAI,KAAK,OAAO,MAAI,KAAG,KAAIF,KAAE;AAAE,WAAO,SAASL,GAAEI,IAAE;AAAC,UAAG,gBAAgBJ,GAAE,OAAM,IAAI,UAAU,6BAA6B;AAAE,aAAO,IAAIC,GAAEM,MAAGH,MAAG,MAAI,MAAIC,MAAID,EAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,EAAE,mBAAmB,SAASJ,IAAE;AAAC,QAAGA,GAAE,QAAOA;AAAE,IAAAA,KAAE,OAAO,iBAAiB;AAAE,aAAQK,KAAE,uHAAuH,MAAM,GAAG,GAAEC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAIH,KAAE,EAAEE,GAAEC,EAAC,CAAC;AAAE,oBAAY,OAAOH,MAAG,cAAY,OAAOA,GAAE,UAAUH,EAAC,KAAG,EAAEG,GAAE,WAAUH,IAAE,EAAC,cAAa,MAAG,UAAS,MAAG,OAAM,WAAU;AAAC,eAAO,EAAE,EAAE,IAAI,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC,CAAE;AAAE,MAAI,GAAE,IAAE,cAAY,OAAO,OAAO,SAAO,OAAO,SAAO,SAASA,IAAE;AAAC,aAASC,KAAG;AAAA,IAAC;AAAC,WAAOA,GAAE,YAAUD,IAAE,IAAIC;AAAA,EAAC;AAAE,MAAG,cAAY,OAAO,OAAO,eAAe,KAAE,OAAO;AAAA,OAAmB;AAAC,QAAI;AAAE,OAAE;AAAC,UAAI,IAAE,CAAC;AAAE,UAAG;AAAC,UAAE,YAAU,EAAC,GAAE,KAAE,GAAE,IAAE,EAAE;AAAE,cAAM;AAAA,MAAC,SAAOD,IAAE;AAAA,MAAC;AAAC,UAAE;AAAA,IAAE;AAAC,QAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,UAAGD,GAAE,YAAUC,IAAED,GAAE,cAAYC,GAAE,OAAM,IAAI,UAAUD,KAAE,oBAAoB;AAAE,aAAOA;AAAA,IAAC,IAAE;AAAA,EAAI;AAAC,MAAI,IAAE;AAAE,WAAS,EAAEA,IAAEC,IAAE;AAAC,QAAGD,GAAE,YAAU,EAAEC,GAAE,SAAS,GAAED,GAAE,UAAU,cAAYA,IAAE,EAAE,GAAEA,IAAEC,EAAC;AAAA,QAAO,UAAQG,MAAKH,GAAE,KAAG,eAAaG,GAAE,KAAG,OAAO,kBAAiB;AAAC,UAAIG,KAAE,OAAO,yBAAyBN,IAAEG,EAAC;AAAE,MAAAG,MAAG,OAAO,eAAeP,IAAEI,IAAEG,EAAC;AAAA,IAAC,MAAM,CAAAP,GAAEI,EAAC,IAAEH,GAAEG,EAAC;AAAE,IAAAJ,GAAE,KAAGC,GAAE;AAAA,EAAS;AAAC,WAAS,IAAG;AAAC,SAAK,IAAE,OAAG,KAAK,IAAE,MAAK,KAAK,IAAE,QAAO,KAAK,IAAE,GAAE,KAAK,IAAE,KAAK,IAAE,GAAE,KAAK,IAAE;AAAA,EAAI;AAAC,WAAS,EAAED,IAAE;AAAC,QAAGA,GAAE,EAAE,OAAM,IAAI,UAAU,8BAA8B;AAAE,IAAAA,GAAE,IAAE;AAAA,EAAE;AAAC,WAAS,EAAEA,IAAEC,IAAE;AAAC,IAAAD,GAAE,IAAE,EAAC,IAAGC,IAAE,IAAG,KAAE,GAAED,GAAE,IAAEA,GAAE,KAAGA,GAAE;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAEC,IAAEG,IAAE;AAAC,WAAOJ,GAAE,IAAEI,IAAE,EAAC,OAAMH,GAAC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,SAAK,IAAE,IAAI,KAAE,KAAK,IAAEA;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAEC,IAAEG,IAAEG,IAAE;AAAC,QAAG;AAAC,UAAIF,KAAEJ,GAAE,KAAKD,GAAE,EAAE,GAAEI,EAAC;AAAE,UAAG,EAAEC,cAAa,QAAQ,OAAM,IAAI,UAAU,qBAAmBA,KAAE,mBAAmB;AAAE,UAAG,CAACA,GAAE,KAAK,QAAOL,GAAE,EAAE,IAAE,OAAGK;AAAE,UAAIH,KAAEG,GAAE;AAAA,IAAK,SAAOJ,IAAE;AAAC,aAAOD,GAAE,EAAE,IAAE,MAAK,EAAEA,GAAE,GAAEC,EAAC,GAAE,EAAED,EAAC;AAAA,IAAC;AAAC,WAAOA,GAAE,EAAE,IAAE,MAAKO,GAAE,KAAKP,GAAE,GAAEE,EAAC,GAAE,EAAEF,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,WAAKA,GAAE,EAAE,IAAG,KAAG;AAAC,UAAIC,KAAED,GAAE,EAAEA,GAAE,CAAC;AAAE,UAAGC,GAAE,QAAOD,GAAE,EAAE,IAAE,OAAG,EAAC,OAAMC,GAAE,OAAM,MAAK,MAAE;AAAA,IAAC,SAAOA,IAAE;AAAC,MAAAD,GAAE,EAAE,IAAE,QAAO,EAAEA,GAAE,GAAEC,EAAC;AAAA,IAAC;AAAC,QAAGD,GAAE,EAAE,IAAE,OAAGA,GAAE,EAAE,GAAE;AAAC,UAAGC,KAAED,GAAE,EAAE,GAAEA,GAAE,EAAE,IAAE,MAAKC,GAAE,GAAG,OAAMA,GAAE;AAAG,aAAM,EAAC,OAAMA,GAAE,QAAO,MAAK,KAAE;AAAA,IAAC;AAAC,WAAM,EAAC,OAAM,QAAO,MAAK,KAAE;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,SAAK,OAAK,SAASC,IAAE;AAAC,aAAO,EAAED,GAAE,CAAC,GAAEA,GAAE,EAAE,IAAEC,KAAE,EAAED,IAAEA,GAAE,EAAE,EAAE,MAAKC,IAAED,GAAE,EAAE,CAAC,KAAGA,GAAE,EAAE,EAAEC,EAAC,GAAEA,KAAE,EAAED,EAAC,IAAGC;AAAA,IAAC,GAAE,KAAK,QAAM,SAASA,IAAE;AAAC,aAAO,EAAED,GAAE,CAAC,GAAEA,GAAE,EAAE,IAAEC,KAAE,EAAED,IAAEA,GAAE,EAAE,EAAE,OAAMC,IAAED,GAAE,EAAE,CAAC,KAAG,EAAEA,GAAE,GAAEC,EAAC,GAAEA,KAAE,EAAED,EAAC,IAAGC;AAAA,IAAC,GAAE,KAAK,SAAO,SAASA,IAAE;AAAC,aAAO,SAASD,IAAEC,IAAE;AAAC,UAAED,GAAE,CAAC;AAAE,YAAII,KAAEJ,GAAE,EAAE;AAAE,eAAOI,KAAE,EAAEJ,IAAE,YAAWI,KAAEA,GAAE,SAAO,SAASJ,IAAE;AAAC,iBAAM,EAAC,OAAMA,IAAE,MAAK,KAAE;AAAA,QAAC,GAAEC,IAAED,GAAE,EAAE,MAAM,KAAGA,GAAE,EAAE,OAAOC,EAAC,GAAE,EAAED,EAAC;AAAA,MAAE,EAAEA,IAAEC,EAAC;AAAA,IAAC,GAAE,KAAK,OAAO,QAAQ,IAAE,WAAU;AAAC,aAAO;AAAA,IAAI;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,WAAO,SAASA,IAAE;AAAC,eAASC,GAAEA,IAAE;AAAC,eAAOD,GAAE,KAAKC,EAAC;AAAA,MAAC;AAAC,eAASG,GAAEH,IAAE;AAAC,eAAOD,GAAE,MAAMC,EAAC;AAAA,MAAC;AAAC,aAAO,IAAI,QAAS,SAASM,IAAEF,IAAE;AAAC,SAAC,SAASL,GAAEE,IAAE;AAAC,UAAAA,GAAE,OAAKK,GAAEL,GAAE,KAAK,IAAE,QAAQ,QAAQA,GAAE,KAAK,EAAE,KAAKD,IAAEG,EAAC,EAAE,KAAKJ,IAAEK,EAAC;AAAA,QAAC,EAAEL,GAAE,KAAK,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE,IAAI,EAAE,IAAI,EAAEA,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,IAAE,UAAU,IAAE,SAASA,IAAE;AAAC,SAAK,IAAEA;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASA,IAAE;AAAC,SAAK,IAAE,EAAC,QAAOA,GAAC,GAAE,KAAK,IAAE,KAAK;AAAA,EAAC,GAAE,EAAE,WAAW,SAASA,IAAE;AAAC,aAASC,GAAED,IAAE;AAAC,WAAK,IAAE,GAAE,KAAK,IAAE,QAAO,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE;AAAG,UAAIC,KAAE,KAAK,EAAE;AAAE,UAAG;AAAC,QAAAD,GAAEC,GAAE,SAAQA,GAAE,MAAM;AAAA,MAAC,SAAOD,IAAE;AAAC,QAAAC,GAAE,OAAOD,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASI,KAAG;AAAC,WAAK,IAAE;AAAA,IAAI;AAAC,aAASC,GAAEL,IAAE;AAAC,aAAOA,cAAaC,KAAED,KAAE,IAAIC,GAAG,SAASA,IAAE;AAAC,QAAAA,GAAED,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,QAAGA,GAAE,QAAOA;AAAE,IAAAI,GAAE,UAAU,IAAE,SAASJ,IAAE;AAAC,UAAG,QAAM,KAAK,GAAE;AAAC,aAAK,IAAE,CAAC;AAAE,YAAIC,KAAE;AAAK,aAAK,EAAG,WAAU;AAAC,UAAAA,GAAE,EAAE;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,WAAK,EAAE,KAAKD,EAAC;AAAA,IAAC;AAAE,QAAIE,KAAE,EAAE;AAAW,IAAAE,GAAE,UAAU,IAAE,SAASJ,IAAE;AAAC,MAAAE,GAAEF,IAAE,CAAC;AAAA,IAAC,GAAEI,GAAE,UAAU,IAAE,WAAU;AAAC,aAAK,KAAK,KAAG,KAAK,EAAE,UAAQ;AAAC,YAAIJ,KAAE,KAAK;AAAE,aAAK,IAAE,CAAC;AAAE,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAO,EAAEC,IAAE;AAAC,cAAIG,KAAEJ,GAAEC,EAAC;AAAE,UAAAD,GAAEC,EAAC,IAAE;AAAK,cAAG;AAAC,YAAAG,GAAE;AAAA,UAAC,SAAOJ,IAAE;AAAC,iBAAK,EAAEA,EAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,WAAK,IAAE;AAAA,IAAI,GAAEI,GAAE,UAAU,IAAE,SAASJ,IAAE;AAAC,WAAK,EAAG,WAAU;AAAC,cAAMA;AAAA,MAAC,CAAE;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,WAAU;AAAC,eAASD,GAAEA,IAAE;AAAC,eAAO,SAASO,IAAE;AAAC,UAAAH,OAAIA,KAAE,MAAGJ,GAAE,KAAKC,IAAEM,EAAC;AAAA,QAAE;AAAA,MAAC;AAAC,UAAIN,KAAE,MAAKG,KAAE;AAAG,aAAM,EAAC,SAAQJ,GAAE,KAAK,CAAC,GAAE,QAAOA,GAAE,KAAK,CAAC,EAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,SAASD,IAAE;AAAC,UAAGA,OAAI,KAAK,MAAK,EAAE,IAAI,UAAU,oCAAoC,CAAC;AAAA,eAAUA,cAAaC,GAAE,MAAK,EAAED,EAAC;AAAA,WAAM;AAAC,UAAE,SAAO,OAAOA,IAAE;AAAA,UAAC,KAAI;AAAS,gBAAII,KAAE,QAAMJ;AAAE,kBAAM;AAAA,UAAE,KAAI;AAAW,YAAAI,KAAE;AAAG,kBAAM;AAAA,UAAE;AAAQ,YAAAA,KAAE;AAAA,QAAE;AAAC,QAAAA,KAAE,KAAK,EAAEJ,EAAC,IAAE,KAAK,EAAEA,EAAC;AAAA,MAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,SAASD,IAAE;AAAC,UAAIC,KAAE;AAAO,UAAG;AAAC,QAAAA,KAAED,GAAE;AAAA,MAAI,SAAOA,IAAE;AAAC,eAAO,KAAK,KAAK,EAAEA,EAAC;AAAA,MAAC;AAAC,oBAAY,OAAOC,KAAE,KAAK,EAAEA,IAAED,EAAC,IAAE,KAAK,EAAEA,EAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,SAASD,IAAE;AAAC,WAAK,EAAE,GAAEA,EAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,SAASD,IAAE;AAAC,WAAK,EAAE,GAAEA,EAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,SAASD,IAAEC,IAAE;AAAC,UAAG,KAAG,KAAK,EAAE,OAAM,MAAM,mBAAiBD,KAAE,OAAKC,KAAE,wCAAsC,KAAK,CAAC;AAAE,WAAK,IAAED,IAAE,KAAK,IAAEC,IAAE,MAAI,KAAK,KAAG,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,IAAC,GAAEA,GAAE,UAAU,IAAE,WAAU;AAAC,UAAID,KAAE;AAAK,MAAAE,GAAG,WAAU;AAAC,YAAGF,GAAE,EAAE,GAAE;AAAC,cAAIC,KAAE,EAAE;AAAQ,qBAASA,MAAGA,GAAE,MAAMD,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,GAAG,CAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,WAAU;AAAC,UAAG,KAAK,EAAE,QAAM;AAAG,UAAID,KAAE,EAAE,aAAYC,KAAE,EAAE,OAAMG,KAAE,EAAE;AAAc,aAAO,WAASA,OAAI,cAAY,OAAOJ,KAAEA,KAAE,IAAIA,GAAE,sBAAqB,EAAC,YAAW,KAAE,CAAC,IAAE,cAAY,OAAOC,KAAED,KAAE,IAAIC,GAAE,sBAAqB,EAAC,YAAW,KAAE,CAAC,KAAGD,KAAE,EAAE,SAAS,YAAY,aAAa,GAAG,gBAAgB,sBAAqB,OAAG,MAAGA,EAAC,GAAEA,GAAE,UAAQ,MAAKA,GAAE,SAAO,KAAK,GAAEI,GAAEJ,EAAC;AAAA,IAAE,GAAEC,GAAE,UAAU,IAAE,WAAU;AAAC,UAAG,QAAM,KAAK,GAAE;AAAC,iBAAQD,KAAE,GAAEA,KAAE,KAAK,EAAE,QAAO,EAAEA,GAAE,CAAAG,GAAE,EAAE,KAAK,EAAEH,EAAC,CAAC;AAAE,aAAK,IAAE;AAAA,MAAI;AAAA,IAAC;AAAE,QAAIG,KAAE,IAAIC;AAAE,WAAOH,GAAE,UAAU,IAAE,SAASD,IAAE;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAE,MAAAD,GAAE,EAAEC,GAAE,SAAQA,GAAE,MAAM;AAAA,IAAC,GAAEA,GAAE,UAAU,IAAE,SAASD,IAAEC,IAAE;AAAC,UAAIG,KAAE,KAAK,EAAE;AAAE,UAAG;AAAC,QAAAJ,GAAE,KAAKC,IAAEG,GAAE,SAAQA,GAAE,MAAM;AAAA,MAAC,SAAOJ,IAAE;AAAC,QAAAI,GAAE,OAAOJ,EAAC;AAAA,MAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,OAAK,SAASD,IAAEI,IAAE;AAAC,eAASG,GAAEP,IAAEC,IAAE;AAAC,eAAM,cAAY,OAAOD,KAAE,SAASC,IAAE;AAAC,cAAG;AAAC,YAAAI,GAAEL,GAAEC,EAAC,CAAC;AAAA,UAAC,SAAOD,IAAE;AAAC,YAAAE,GAAEF,EAAC;AAAA,UAAC;AAAA,QAAC,IAAEC;AAAA,MAAC;AAAC,UAAII,IAAEH,IAAEI,KAAE,IAAIL,GAAG,SAASD,IAAEC,IAAE;AAAC,QAAAI,KAAEL,IAAEE,KAAED;AAAA,MAAC,CAAE;AAAE,aAAO,KAAK,EAAEM,GAAEP,IAAEK,EAAC,GAAEE,GAAEH,IAAEF,EAAC,CAAC,GAAEI;AAAA,IAAC,GAAEL,GAAE,UAAU,QAAM,SAASD,IAAE;AAAC,aAAO,KAAK,KAAK,QAAOA,EAAC;AAAA,IAAC,GAAEC,GAAE,UAAU,IAAE,SAASD,IAAEC,IAAE;AAAC,eAASG,KAAG;AAAC,gBAAOG,GAAE,GAAE;AAAA,UAAC,KAAK;AAAE,YAAAP,GAAEO,GAAE,CAAC;AAAE;AAAA,UAAM,KAAK;AAAE,YAAAN,GAAEM,GAAE,CAAC;AAAE;AAAA,UAAM;AAAQ,kBAAM,MAAM,uBAAqBA,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAIA,KAAE;AAAK,cAAM,KAAK,IAAEJ,GAAE,EAAEC,EAAC,IAAE,KAAK,EAAE,KAAKA,EAAC,GAAE,KAAK,IAAE;AAAA,IAAE,GAAEH,GAAE,UAAQI,IAAEJ,GAAE,SAAO,SAASD,IAAE;AAAC,aAAO,IAAIC,GAAG,SAASA,IAAEG,IAAE;AAAC,QAAAA,GAAEJ,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,GAAEC,GAAE,OAAK,SAASD,IAAE;AAAC,aAAO,IAAIC,GAAG,SAASA,IAAEG,IAAE;AAAC,iBAAQG,KAAE,EAAEP,EAAC,GAAEE,KAAEK,GAAE,KAAK,GAAE,CAACL,GAAE,MAAKA,KAAEK,GAAE,KAAK,EAAE,CAAAF,GAAEH,GAAE,KAAK,EAAE,EAAED,IAAEG,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC,GAAEH,GAAE,MAAI,SAASD,IAAE;AAAC,UAAII,KAAE,EAAEJ,EAAC,GAAEO,KAAEH,GAAE,KAAK;AAAE,aAAOG,GAAE,OAAKF,GAAE,CAAC,CAAC,IAAE,IAAIJ,GAAG,SAASD,IAAEC,IAAE;AAAC,iBAASC,GAAED,IAAE;AAAC,iBAAO,SAASG,IAAE;AAAC,YAAAE,GAAEL,EAAC,IAAEG,IAAE,KAAG,EAAED,MAAGH,GAAEM,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,YAAIA,KAAE,CAAC,GAAEH,KAAE;AAAE,WAAE;AAAC,UAAAG,GAAE,KAAK,MAAM,GAAEH,MAAIE,GAAEE,GAAE,KAAK,EAAE,EAAEL,GAAEI,GAAE,SAAO,CAAC,GAAEL,EAAC,GAAEM,KAAEH,GAAE,KAAK;AAAA,QAAC,SAAO,CAACG,GAAE;AAAA,MAAK,CAAE;AAAA,IAAC,GAAEN;AAAA,EAAC,CAAE;AAAE,MAAI,IAAE,cAAY,OAAO,OAAO,SAAO,OAAO,SAAO,SAASD,IAAEC,IAAE;AAAC,aAAQG,KAAE,GAAEA,KAAE,UAAU,QAAOA,MAAI;AAAC,UAAIG,KAAE,UAAUH,EAAC;AAAE,UAAGG,GAAE,UAAQF,MAAKE,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEF,EAAC,MAAIL,GAAEK,EAAC,IAAEE,GAAEF,EAAC;AAAA,IAAE;AAAC,WAAOL;AAAA,EAAC;AAAE,IAAE,iBAAiB,SAASA,IAAE;AAAC,WAAOA,MAAG;AAAA,EAAC,CAAE,GAAE,EAAE,aAAa,SAASA,IAAE;AAAC,WAAOA,MAAG,SAASA,IAAEC,IAAE;AAAC,aAAOD,OAAIC,KAAE,MAAID,MAAG,IAAEA,MAAG,IAAEC,KAAED,MAAGA,MAAGC,MAAGA;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,EAAE,4BAA4B,SAASD,IAAE;AAAC,WAAOA,MAAG,SAASA,IAAEC,IAAE;AAAC,UAAIG,KAAE;AAAK,MAAAA,cAAa,WAASA,KAAE,OAAOA,EAAC;AAAG,UAAIG,KAAEH,GAAE;AAAO,WAAI,KAAGH,KAAEA,MAAG,OAAKA,KAAE,KAAK,IAAIA,KAAEM,IAAE,CAAC,IAAGN,KAAEM,IAAEN,MAAI;AAAC,YAAII,KAAED,GAAEH,EAAC;AAAE,YAAGI,OAAIL,MAAG,OAAO,GAAGK,IAAEL,EAAC,EAAE,QAAM;AAAA,MAAE;AAAC,aAAM;AAAA,IAAE;AAAA,EAAC,CAAE,GAAE,EAAE,6BAA6B,SAASA,IAAE;AAAC,WAAOA,MAAG,SAASA,IAAEC,IAAE;AAAC,UAAG,QAAM,KAAK,OAAM,IAAI,UAAU,8EAA8E;AAAE,UAAGD,cAAa,OAAO,OAAM,IAAI,UAAU,8EAA8E;AAAE,aAAM,OAAK,KAAK,QAAQA,IAAEC,MAAG,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,EAAE,wBAAwB,SAASD,IAAE;AAAC,WAAOA,MAAG,WAAU;AAAC,aAAO,SAASA,IAAEC,IAAE;AAAC,QAAAD,cAAa,WAASA,MAAG;AAAI,YAAII,KAAE,GAAEG,KAAE,OAAGF,KAAE,EAAC,MAAK,WAAU;AAAC,cAAG,CAACE,MAAGH,KAAEJ,GAAE,QAAO;AAAC,gBAAIK,KAAED;AAAI,mBAAM,EAAC,OAAMH,GAAEI,IAAEL,GAAEK,EAAC,CAAC,GAAE,MAAK,MAAE;AAAA,UAAC;AAAC,iBAAOE,KAAE,MAAG,EAAC,MAAK,MAAG,OAAM,OAAM;AAAA,QAAC,EAAC;AAAE,eAAOF,GAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,iBAAOA;AAAA,QAAC,GAAEA;AAAA,MAAC,EAAE,MAAM,SAASL,IAAE;AAAC,eAAOA;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC,CAAE;AAAE,MAAI,IAAE,QAAM;AAAK,WAASQ,GAAER,IAAEC,IAAE;AAAC,IAAAD,KAAEA,GAAE,MAAM,GAAG;AAAE,QAAII,IAAEG,KAAE;AAAE,IAAAP,GAAE,CAAC,KAAIO,MAAG,WAASA,GAAE,cAAYA,GAAE,WAAW,SAAOP,GAAE,CAAC,CAAC;AAAE,WAAKA,GAAE,WAASI,KAAEJ,GAAE,MAAM,KAAI,CAAAA,GAAE,UAAQ,WAASC,KAAEM,KAAEA,GAAEH,EAAC,KAAGG,GAAEH,EAAC,MAAI,OAAO,UAAUA,EAAC,IAAEG,GAAEH,EAAC,IAAEG,GAAEH,EAAC,IAAE,CAAC,IAAEG,GAAEH,EAAC,IAAEH;AAAA,EAAC;AAAC,WAASQ,KAAG;AAAC,UAAM,MAAM,cAAc;AAAA,EAAC;AAAC,WAASC,GAAEV,IAAEC,IAAE;AAAC,WAAOA,KAAE,OAAO,aAAa,MAAM,MAAKA,EAAC,GAAE,QAAMD,KAAEC,KAAED,KAAEC;AAAA,EAAC;AAAC,MAAIU,IAAEC,IAAEC,KAAE,eAAa,OAAO,aAAYC,KAAE,eAAa,OAAO,aAAYC,KAAE,CAAC,GAAEC,KAAE;AAAK,WAASC,GAAEjB,IAAE;AAAC,QAAIC;AAAE,eAASA,OAAIA,KAAE,IAAGiB,GAAE,GAAEjB,KAAEc,GAAEd,EAAC;AAAE,aAAQG,KAAE,MAAM,KAAK,MAAMJ,GAAE,SAAO,CAAC,CAAC,GAAEO,KAAEN,GAAE,EAAE,KAAG,IAAGI,KAAE,GAAEH,KAAE,GAAEG,KAAEL,GAAE,SAAO,GAAEK,MAAG,GAAE;AAAC,UAAIC,KAAEN,GAAEK,EAAC,GAAEF,KAAEH,GAAEK,KAAE,CAAC,GAAEc,KAAEnB,GAAEK,KAAE,CAAC,GAAEe,KAAEnB,GAAEK,MAAG,CAAC;AAAE,MAAAA,KAAEL,IAAG,IAAEK,OAAI,IAAEH,MAAG,CAAC,GAAEA,KAAEF,IAAG,KAAGE,OAAI,IAAEgB,MAAG,CAAC,GAAEA,KAAElB,GAAE,KAAGkB,EAAC,GAAEf,GAAEF,IAAG,IAAEkB,KAAEd,KAAEH,KAAEgB;AAAA,IAAC;AAAC,YAAOC,KAAE,GAAED,KAAEZ,IAAEP,GAAE,SAAOK,IAAE;AAAA,MAAC,KAAK;AAAE,QAAAc,KAAElB,IAAG,MAAImB,KAAEpB,GAAEK,KAAE,CAAC,OAAK,CAAC,KAAGE;AAAA,MAAE,KAAK;AAAE,QAAAP,KAAEA,GAAEK,EAAC,GAAED,GAAEF,EAAC,IAAED,GAAED,MAAG,CAAC,IAAEC,IAAG,IAAED,OAAI,IAAEoB,MAAG,CAAC,IAAED,KAAEZ;AAAA,IAAC;AAAC,WAAOH,GAAE,KAAK,EAAE;AAAA,EAAC;AAAC,WAASiB,GAAErB,IAAE;AAAC,QAAIC,KAAED,GAAE,QAAOI,KAAE,IAAEH,KAAE;AAAE,IAAAG,KAAE,IAAEA,KAAE,KAAK,MAAMA,EAAC,IAAE,MAAI,KAAK,QAAQJ,GAAEC,KAAE,CAAC,CAAC,MAAIG,KAAE,MAAI,KAAK,QAAQJ,GAAEC,KAAE,CAAC,CAAC,IAAEG,KAAE,IAAEA,KAAE;AAAG,QAAIG,KAAE,IAAI,WAAWH,EAAC,GAAEC,KAAE;AAAE,WAAO,SAASL,IAAEC,IAAE;AAAC,eAASG,GAAEH,IAAE;AAAC,eAAKM,KAAEP,GAAE,UAAQ;AAAC,cAAII,KAAEJ,GAAE,OAAOO,IAAG,GAAEF,KAAEW,GAAEZ,EAAC;AAAE,cAAG,QAAMC,GAAE,QAAOA;AAAE,cAAG,CAAC,cAAc,KAAKD,EAAC,EAAE,OAAM,MAAM,sCAAoCA,EAAC;AAAA,QAAC;AAAC,eAAOH;AAAA,MAAC;AAAC,MAAAiB,GAAE;AAAE,eAAQX,KAAE,OAAI;AAAC,YAAIF,KAAED,GAAE,EAAE,GAAEF,KAAEE,GAAE,CAAC,GAAEE,KAAEF,GAAE,EAAE,GAAED,KAAEC,GAAE,EAAE;AAAE,YAAG,OAAKD,MAAG,OAAKE,GAAE;AAAM,QAAAJ,GAAEI,MAAG,IAAEH,MAAG,CAAC,GAAE,MAAII,OAAIL,GAAEC,MAAG,IAAE,MAAII,MAAG,CAAC,GAAE,MAAIH,MAAGF,GAAEK,MAAG,IAAE,MAAIH,EAAC;AAAA,MAAE;AAAA,IAAC,EAAEH,IAAG,SAASA,IAAE;AAAC,MAAAO,GAAEF,IAAG,IAAEL;AAAA,IAAC,CAAE,GAAEK,OAAID,KAAEG,GAAE,SAAS,GAAEF,EAAC,IAAEE;AAAA,EAAC;AAAC,WAASW,KAAG;AAAC,QAAG,CAACF,IAAE;AAAC,MAAAA,KAAE,CAAC;AAAE,eAAQhB,KAAE,iEAAiE,MAAM,EAAE,GAAEC,KAAE,CAAC,OAAM,MAAK,OAAM,OAAM,IAAI,GAAEG,KAAE,GAAE,IAAEA,IAAEA,MAAI;AAAC,YAAIG,KAAEP,GAAE,OAAOC,GAAEG,EAAC,EAAE,MAAM,EAAE,CAAC;AAAE,QAAAW,GAAEX,EAAC,IAAEG;AAAE,iBAAQF,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAI;AAAC,cAAIH,KAAEK,GAAEF,EAAC;AAAE,qBAASW,GAAEd,EAAC,MAAIc,GAAEd,EAAC,IAAEG;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAIiB,IAAEC,KAAE,cAAY,OAAO;AAAW,WAASC,GAAExB,IAAE;AAAC,WAAOuB,MAAG,QAAMvB,MAAGA,cAAa;AAAA,EAAU;AAAC,WAASyB,GAAEzB,IAAE;AAAC,QAAG,KAAK,IAAEA,IAAE,SAAOA,MAAG,MAAIA,GAAE,OAAO,OAAM,MAAM,wDAAwD;AAAA,EAAC;AAAC,MAAI0B,KAAE,cAAY,OAAO,WAAW,UAAU,OAAMC,KAAE;AAAE,WAASC,GAAE5B,IAAEC,IAAE;AAAC,WAAO,MAAM,wBAAsBD,KAAE,mBAAiBC,KAAE,GAAG;AAAA,EAAC;AAAC,WAAS4B,KAAG;AAAC,WAAO,MAAM,6CAA6C;AAAA,EAAC;AAAC,WAASC,GAAE9B,IAAEC,IAAE;AAAC,IAAAA,KAAE,YAAUA,KAAE,WAASA,KAAE,CAAC,IAAEA,IAAG,KAAGA,GAAE,GAAE,KAAK,IAAE,MAAK,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,KAAK,IAAEA,IAAED,MAAG+B,GAAE,MAAK/B,EAAC;AAAA,EAAC;AAAC,WAAS+B,GAAE/B,IAAEC,IAAE;AAAC,IAAAD,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,UAAGD,GAAE,gBAAc,WAAW,QAAOA;AAAE,UAAGA,GAAE,gBAAc,YAAY,QAAO,IAAI,WAAWA,EAAC;AAAE,UAAGA,GAAE,gBAAc,MAAM,QAAO,IAAI,WAAWA,EAAC;AAAE,UAAGA,GAAE,gBAAc,OAAO,QAAOqB,GAAErB,EAAC;AAAE,UAAGA,GAAE,gBAAcyB,GAAE,QAAM,CAACxB,OAAIA,KAAED,GAAE,MAAIC,GAAE,gBAAc,aAAWA,MAAGA,KAAE,SAAOA,KAAED,GAAE,MAAIwB,GAAEvB,EAAC,IAAEA,KAAE,YAAU,OAAOA,KAAEoB,GAAEpB,EAAC,IAAE,OAAMD,KAAEA,GAAE,IAAEC,MAAG,IAAI,WAAWD,EAAC,IAAEsB,OAAIA,KAAE,IAAI,WAAW,CAAC;AAAI,UAAGtB,cAAa,WAAW,QAAO,IAAI,WAAWA,GAAE,QAAOA,GAAE,YAAWA,GAAE,UAAU;AAAE,YAAM,MAAM,2HAA2H;AAAA,IAAC,EAAEC,IAAED,GAAE,CAAC,GAAEA,GAAE,IAAE,GAAEA,GAAE,IAAEA,GAAE,EAAE,QAAOA,GAAE,IAAEA,GAAE;AAAA,EAAC;AAAC,WAASgC,GAAEhC,IAAE;AAAC,QAAGA,GAAE,IAAEA,GAAE,EAAE,OAAM,MAAM,4CAA0CA,GAAE,IAAE,QAAMA,GAAE,CAAC;AAAA,EAAC;AAAC,WAASiC,GAAEjC,IAAE;AAAC,QAAIC,KAAED,GAAE,GAAEI,KAAEH,GAAED,GAAE,CAAC,GAAEO,KAAE,MAAIH;AAAE,QAAG,MAAIA,GAAE,QAAOJ,GAAE,KAAG,GAAEgC,GAAEhC,EAAC,GAAEO;AAAE,QAAGA,OAAI,OAAKH,KAAEH,GAAED,GAAE,IAAE,CAAC,OAAK,GAAE,MAAII,GAAE,QAAOJ,GAAE,KAAG,GAAEgC,GAAEhC,EAAC,GAAEO;AAAE,QAAGA,OAAI,OAAKH,KAAEH,GAAED,GAAE,IAAE,CAAC,OAAK,IAAG,MAAII,GAAE,QAAOJ,GAAE,KAAG,GAAEgC,GAAEhC,EAAC,GAAEO;AAAE,QAAGA,OAAI,OAAKH,KAAEH,GAAED,GAAE,IAAE,CAAC,OAAK,IAAG,MAAII,GAAE,QAAOJ,GAAE,KAAG,GAAEgC,GAAEhC,EAAC,GAAEO;AAAE,QAAGH,KAAEH,GAAED,GAAE,IAAE,CAAC,GAAEA,GAAE,KAAG,GAAEO,OAAI,KAAGH,OAAI,IAAG,MAAIA,GAAE,QAAO4B,GAAEhC,EAAC,GAAEO;AAAE,QAAG,OAAKN,GAAED,GAAE,GAAG,KAAG,OAAKC,GAAED,GAAE,GAAG,KAAG,OAAKC,GAAED,GAAE,GAAG,KAAG,OAAKC,GAAED,GAAE,GAAG,KAAG,OAAKC,GAAED,GAAE,GAAG,EAAE,OAAM6B,GAAE;AAAE,WAAOG,GAAEhC,EAAC,GAAEO;AAAA,EAAC;AAAC,EAAAuB,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,IAAE,KAAK;AAAA,EAAC;AAAE,MAAII,KAAE,CAAC;AAAE,WAASC,KAAG;AAAC,SAAK,IAAE,CAAC;AAAA,EAAC;AAAC,WAASC,GAAEpC,IAAEC,IAAE;AAAC,WAAK,MAAIA,KAAG,CAAAD,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK;AAAE,IAAAD,GAAE,EAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,WAASoC,GAAErC,IAAE;AAAC,QAAIC,KAAE,CAAC,GAAEG,KAAE,WAASH,GAAE,KAAGA,GAAE;AAAE,SAAK,IAAE,EAAC,GAAE,WAASA,GAAE,KAAGA,GAAE,EAAC,GAAE,KAAK,IAAEG,IAAEH,KAAE,KAAK,GAAEiC,GAAE,UAAQ9B,KAAE8B,GAAE,IAAI,GAAEjC,OAAIG,GAAE,IAAEH,GAAE,IAAGD,MAAG+B,GAAE3B,IAAEJ,EAAC,GAAEA,KAAEI,MAAGJ,KAAE,IAAI8B,GAAE9B,IAAEC,EAAC,GAAE,KAAK,IAAED,IAAE,KAAK,IAAE,KAAK,EAAE,GAAE,KAAK,IAAE,KAAK,IAAE;AAAA,EAAE;AAAC,WAASsC,IAAGtC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAE,QAAGC,GAAE,KAAGA,GAAE,EAAE,QAAM;AAAG,IAAAD,GAAE,IAAEA,GAAE,EAAE;AAAE,QAAII,KAAE6B,GAAEjC,GAAE,CAAC,MAAI;AAAE,QAAGC,KAAEG,OAAI,GAAE,EAAE,MAAIA,MAAG,MAAI,KAAGA,IAAG,OAAMwB,GAAExB,IAAEJ,GAAE,CAAC;AAAE,QAAG,IAAEC,GAAE,OAAM,MAAM,2BAAyBA,KAAE,mBAAiBD,GAAE,IAAE,GAAG;AAAE,WAAOA,GAAE,IAAEC,IAAED,GAAE,IAAEI,IAAE;AAAA,EAAE;AAAC,WAASmC,IAAGvC,IAAE;AAAC,YAAOA,GAAE,GAAE;AAAA,MAAC,KAAK;AAAE,YAAG,KAAGA,GAAE,EAAE,CAAAuC,IAAGvC,EAAC;AAAA,YAAO,IAAE;AAAC,mBAAQC,MAAGD,KAAEA,GAAE,GAAG,GAAEI,KAAEH,KAAE,IAAGA,KAAEG,KAAG,KAAG,MAAI,MAAIJ,GAAE,EAAEC,IAAG,IAAG;AAAC,YAAAD,GAAE,IAAEC,IAAE+B,GAAEhC,EAAC;AAAE,kBAAM;AAAA,UAAC;AAAC,gBAAM6B,GAAE;AAAA,QAAC;AAAC;AAAA,MAAM,KAAK;AAAE,SAAC7B,KAAEA,GAAE,GAAG,KAAG,GAAEgC,GAAEhC,EAAC;AAAE;AAAA,MAAM,KAAK;AAAE,aAAGA,GAAE,IAAEuC,IAAGvC,EAAC,KAAGC,KAAEgC,GAAEjC,GAAE,CAAC,MAAI,IAAGA,KAAEA,GAAE,GAAG,KAAGC,IAAE+B,GAAEhC,EAAC;AAAG;AAAA,MAAM,KAAK;AAAE,SAACA,KAAEA,GAAE,GAAG,KAAG,GAAEgC,GAAEhC,EAAC;AAAE;AAAA,MAAM,KAAK;AAAE,aAAIC,KAAED,GAAE,OAAI;AAAC,cAAG,CAACsC,IAAGtC,EAAC,EAAE,OAAM,MAAM,uCAAuC;AAAE,cAAG,KAAGA,GAAE,GAAE;AAAC,gBAAGA,GAAE,KAAGC,GAAE,OAAM,MAAM,yBAAyB;AAAE;AAAA,UAAK;AAAC,UAAAsC,IAAGvC,EAAC;AAAA,QAAC;AAAC;AAAA,MAAM;AAAQ,cAAM4B,GAAE5B,GAAE,GAAEA,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,EAAAmC,GAAE,UAAU,SAAO,WAAU;AAAC,WAAO,KAAK,EAAE;AAAA,EAAM,GAAEA,GAAE,UAAU,MAAI,WAAU;AAAC,QAAInC,KAAE,KAAK;AAAE,WAAO,KAAK,IAAE,CAAC,GAAEA;AAAA,EAAC,GAAEqC,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,EAAE,MAAM,GAAE,KAAK,IAAE,KAAK,EAAE,GAAE,KAAK,IAAE,KAAK,IAAE;AAAA,EAAE;AAAE,MAAIG,MAAG,CAAC;AAAE,WAASC,MAAI;AAAC,SAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,IAAE,IAAIN;AAAA,EAAC;AAAC,WAASO,IAAG1C,IAAEC,IAAE;AAAC,UAAIA,GAAE,WAASD,GAAE,EAAE,KAAKC,EAAC,GAAED,GAAE,KAAGC,GAAE;AAAA,EAAO;AAAC,MAAI0C,MAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,IAAE,OAAO,MAAM,IAAE;AAAO,WAASC,IAAG5C,IAAEC,IAAE;AAAC,WAAO,SAASD,EAAC,MAAI2C,MAAG3C,GAAE2C,GAAE,KAAG1C,KAAE,WAASD,GAAE,IAAEA,GAAE,KAAGC,KAAE,OAAO,iBAAiBD,IAAE,EAAC,GAAE,EAAC,OAAMC,IAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC,CAAC;AAAA,EAAE;AAAC,WAAS4C,IAAG7C,IAAE;AAAC,QAAIC;AAAE,WAAO,SAAOA,KAAE0C,MAAG3C,GAAE2C,GAAE,IAAE3C,GAAE,KAAG,IAAEC;AAAA,EAAC;AAAC,WAAS6C,IAAG9C,IAAE;AAAC,WAAO4C,IAAG5C,IAAE,CAAC,GAAEA;AAAA,EAAC;AAAC,WAAS+C,IAAG/C,IAAE;AAAC,WAAM,CAAC,CAAC,MAAM,QAAQA,EAAC,KAAG,CAAC,EAAE,IAAE6C,IAAG7C,EAAC;AAAA,EAAE;AAAC,WAASgD,IAAGhD,IAAE;AAAC,QAAG,CAAC,MAAM,QAAQA,EAAC,EAAE,OAAM,MAAM,oCAAoC;AAAE,IAAA4C,IAAG5C,IAAE,CAAC;AAAA,EAAC;AAAC,WAASiD,IAAGjD,IAAE;AAAC,WAAO,SAAOA,MAAG,YAAU,OAAOA,MAAG,CAAC,MAAM,QAAQA,EAAC,KAAGA,GAAE,gBAAc;AAAA,EAAM;AAAC,MAAIkD,MAAG,OAAO,OAAOJ,IAAG,CAAC,CAAC,CAAC;AAAE,WAASK,IAAGnD,IAAE;AAAC,QAAG+C,IAAG/C,GAAE,CAAC,EAAE,OAAM,MAAM,oCAAoC;AAAA,EAAC;AAAC,MAAIoD,KAAGC,MAAG,eAAa,OAAO,UAAQ,WAAS,OAAO;AAAY,WAASC,IAAGtD,IAAE;AAAC,WAAM,EAAC,OAAMA,IAAE,cAAa,OAAG,UAAS,OAAG,YAAW,MAAE;AAAA,EAAC;AAAC,WAASuD,IAAGvD,IAAEC,IAAEG,IAAE;AAAC,WAAM,OAAKH,KAAE,OAAKA,MAAGD,GAAE,IAAEA,GAAE,IAAEA,GAAE,EAAEC,EAAC,IAAE,SAAO,WAASG,MAAGA,MAAGJ,GAAE,KAAG,SAAOI,KAAEJ,GAAE,EAAEC,EAAC,KAAGG,KAAEJ,GAAE,EAAEC,KAAED,GAAE,CAAC;AAAA,EAAC;AAAC,WAASwD,IAAGxD,IAAEC,IAAEG,IAAEG,IAAE;AAAC,IAAAA,KAAE,WAASA,MAAGA,IAAE4C,IAAGnD,EAAC,GAAEC,KAAED,GAAE,KAAG,CAACO,KAAEP,GAAE,EAAEC,KAAED,GAAE,CAAC,IAAEI,MAAGJ,GAAE,MAAIA,GAAE,IAAEA,GAAE,EAAEA,GAAE,IAAEA,GAAE,CAAC,IAAE,CAAC,IAAIC,EAAC,IAAEG;AAAA,EAAC;AAAC,WAASqD,IAAGzD,IAAEC,IAAEG,IAAEG,IAAE;AAAC,IAAAH,KAAE,WAASA,MAAGA;AAAE,QAAIC,KAAEkD,IAAGvD,IAAEC,IAAEM,KAAE,WAASA,MAAGA,EAAC;AAAE,WAAO,QAAMF,OAAIA,KAAE6C,MAAIH,IAAG/C,GAAE,CAAC,IAAEI,OAAI4C,IAAG3C,EAAC,GAAE,OAAO,OAAOA,EAAC,MAAIA,OAAI6C,OAAIH,IAAG1C,EAAC,MAAImD,IAAGxD,IAAEC,IAAEI,KAAEyC,IAAGzC,GAAE,MAAM,CAAC,GAAEE,EAAC,GAAEF;AAAA,EAAC;AAAC,WAASqD,IAAG1D,IAAEC,IAAEG,IAAE;AAAC,WAAO,SAAOJ,KAAE,SAAOA,KAAEuD,IAAGvD,IAAEC,EAAC,KAAGD,KAAE,CAACA,MAAG,WAASI,KAAE,IAAEA,KAAEJ;AAAA,EAAC;AAAC,WAAS2D,IAAG3D,IAAEC,IAAEG,IAAEG,IAAE;AAAC,IAAAP,GAAE,MAAIA,GAAE,IAAE,CAAC;AAAG,QAAIK,KAAE0C,IAAG/C,GAAE,CAAC,GAAEE,KAAEF,GAAE,EAAEI,EAAC;AAAE,QAAG,CAACF,IAAE;AAAC,MAAAK,KAAEkD,IAAGzD,IAAEI,IAAE,MAAG,WAASG,MAAGA,EAAC,GAAEL,KAAE,CAAC,GAAEG,KAAEA,MAAG0C,IAAGxC,EAAC;AAAE,eAAQD,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAJ,GAAEI,EAAC,IAAE,IAAIL,GAAEM,GAAED,EAAC,CAAC,GAAED,MAAG2C,IAAG9C,GAAEI,EAAC,EAAE,CAAC;AAAE,MAAAD,OAAI2C,IAAG9C,EAAC,GAAE,OAAO,OAAOA,EAAC,IAAGF,GAAE,EAAEI,EAAC,IAAEF;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAC,WAAS0D,IAAG5D,IAAEC,IAAEG,IAAEG,IAAEF,IAAE;AAAC,QAAIH,KAAE,WAASA,MAAGA;AAAE,WAAOiD,IAAGnD,EAAC,GAAEE,KAAEyD,IAAG3D,IAAEI,IAAEH,IAAEC,EAAC,GAAEE,KAAEG,MAAG,IAAIH,MAAEJ,KAAEyD,IAAGzD,IAAEC,EAAC,GAAE,QAAMI,MAAGH,GAAE,OAAOG,IAAE,GAAED,EAAC,GAAEJ,GAAE,OAAOK,IAAE,GAAED,GAAE,CAAC,MAAIF,GAAE,KAAKE,EAAC,GAAEJ,GAAE,KAAKI,GAAE,CAAC,IAAGA;AAAA,EAAC;AAAC,WAASyD,IAAG7D,IAAEC,IAAE;AAAC,WAAO,SAAOD,KAAEuD,IAAGvD,IAAEC,EAAC,KAAG,IAAED;AAAA,EAAC;AAAC,WAAS8D,IAAG9D,IAAEC,IAAE;AAAC,WAAO,SAAOD,KAAEuD,IAAGvD,IAAEC,EAAC,KAAG,KAAGD;AAAA,EAAC;AAAC,WAAS+D,IAAG/D,IAAE;AAAC,QAAIC,KAAE+D;AAAG,WAAOC,IAAGjE,IAAEC,KAAE,WAASA,KAAEiE,MAAGjE,EAAC;AAAA,EAAC;AAAC,WAASkE,IAAGnE,IAAEC,IAAE;AAAC,QAAG,QAAMD,IAAE;AAAC,UAAG,MAAM,QAAQA,EAAC,EAAE,CAAAA,KAAEiE,IAAGjE,IAAEC,EAAC;AAAA,eAAUgD,IAAGjD,EAAC,GAAE;AAAC,YAAII,IAAEG,KAAE,CAAC;AAAE,aAAIH,MAAKJ,GAAE,CAAAO,GAAEH,EAAC,IAAE+D,IAAGnE,GAAEI,EAAC,GAAEH,EAAC;AAAE,QAAAD,KAAEO;AAAA,MAAC,MAAM,CAAAP,KAAEC,GAAED,EAAC;AAAE,aAAOA;AAAA,IAAC;AAAA,EAAC;AAAC,WAASiE,IAAGjE,IAAEC,IAAE;AAAC,aAAQG,KAAEJ,GAAE,MAAM,GAAEO,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,CAAAH,GAAEG,EAAC,IAAE4D,IAAG/D,GAAEG,EAAC,GAAEN,EAAC;AAAE,WAAO,MAAM,QAAQD,EAAC,KAAG,IAAE6C,IAAG7C,EAAC,KAAG8C,IAAG1C,EAAC,GAAEA;AAAA,EAAC;AAAC,WAAS4D,IAAGhE,IAAE;AAAC,WAAOA,MAAG,YAAU,OAAOA,MAAGA,GAAE,SAAOA,GAAE,OAAO,KAAGA,KAAE,SAASA,IAAE;AAAC,cAAO,OAAOA,IAAE;AAAA,QAAC,KAAI;AAAS,iBAAO,SAASA,EAAC,IAAEA,KAAE,OAAOA,EAAC;AAAA,QAAE,KAAI;AAAS,cAAGA,MAAG,CAAC,MAAM,QAAQA,EAAC,GAAE;AAAC,gBAAGwB,GAAExB,EAAC,EAAE,QAAOiB,GAAEjB,EAAC;AAAE,gBAAGA,cAAayB,IAAE;AAAC,kBAAIxB,KAAED,GAAE;AAAE,qBAAOC,KAAE,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAEsB,MAAGtB,cAAa,aAAWgB,GAAEhB,EAAC,IAAE,OAAMD,GAAE,IAAEC,OAAI;AAAA,YAAE;AAAA,UAAC;AAAA,MAAC;AAAC,aAAOD;AAAA,IAAC,EAAEA,EAAC,GAAE,MAAM,QAAQA,EAAC,IAAE+D,IAAG/D,EAAC,IAAEA;AAAA,EAAE;AAAC,WAASkE,IAAGlE,IAAE;AAAC,WAAOwB,GAAExB,EAAC,IAAE,IAAI,WAAWA,EAAC,IAAEA;AAAA,EAAC;AAAC,WAASoE,IAAGpE,IAAEC,IAAEG,IAAE;AAAC,IAAAJ,OAAIA,KAAEoD,MAAIA,MAAG;AAAK,QAAI7C,KAAE,KAAK,YAAY;AAAE,QAAGP,OAAIA,KAAEO,KAAE,CAACA,EAAC,IAAE,CAAC,IAAG,KAAK,KAAGA,KAAE,IAAE,OAAK,KAAK,YAAY,KAAG,IAAG,KAAK,IAAE,QAAO,KAAK,IAAEP,IAAEA,MAAGO,KAAE,KAAK,EAAE,UAAQ,GAAEA,MAAG0C,IAAG1C,KAAE,KAAK,EAAEP,EAAC,CAAC,KAAG,KAAK,IAAEA,KAAE,KAAK,GAAE,KAAK,IAAEO,MAAG,WAASN,MAAG,KAAGA,MAAG,KAAK,IAAE,KAAK,IAAIA,IAAED,KAAE,IAAE,KAAK,CAAC,GAAE,KAAK,IAAE,UAAQ,KAAK,IAAE,OAAO,WAAUI,GAAE,MAAIH,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,MAAID,KAAEI,GAAEH,EAAC,KAAG,KAAK,EAAE,CAAAD,MAAG,KAAK,IAAGO,KAAE,KAAK,EAAEP,EAAC,KAAG,MAAM,QAAQO,EAAC,KAAGuC,IAAGvC,EAAC,IAAE,KAAK,EAAEP,EAAC,IAAEkD;AAAA,SAAO;AAAC,UAAI7C,MAAGE,KAAE,KAAK,MAAI,KAAK,IAAE,KAAK,EAAE,KAAK,IAAE,KAAK,CAAC,IAAE,CAAC,IAAIP,EAAC;AAAE,MAAAK,KAAE,MAAM,QAAQA,EAAC,KAAGyC,IAAGzC,EAAC,IAAEE,GAAEP,EAAC,IAAEkD;AAAA,IAAE;AAAA,EAAC;AAAC,WAASmB,MAAI;AAAC,IAAAD,IAAG,MAAM,MAAK,SAAS;AAAA,EAAC;AAAC,MAAGA,IAAG,UAAU,SAAO,WAAU;AAAC,WAAOL,IAAG,KAAK,CAAC;AAAA,EAAC,GAAEK,IAAG,UAAU,WAAS,WAAU;AAAC,WAAO,KAAK,EAAE,SAAS;AAAA,EAAC,GAAE,EAAEC,KAAGD,GAAE,GAAEf,KAAG;AAAC,QAAIiB,MAAG,CAAC;AAAE,WAAO,iBAAiBD,MAAIC,IAAG,OAAO,WAAW,IAAEhB,IAAI,WAAU;AAAC,YAAM,MAAM,qDAAqD;AAAA,IAAC,CAAE,GAAEgB,IAAG;AAAA,EAAC;AAAC,WAASC,IAAGvE,IAAEC,IAAEG,IAAE;AAAC,QAAGA,IAAE;AAAC,UAAIG,IAAEF,KAAE,CAAC;AAAE,WAAIE,MAAKH,IAAE;AAAC,YAAIF,KAAEE,GAAEG,EAAC,GAAED,KAAEJ,GAAE;AAAG,QAAAI,OAAID,GAAE,IAAEH,GAAE,MAAIA,GAAE,GAAG,GAAEA,GAAE,MAAIG,GAAE,IAAEmE,IAAGtE,GAAE,EAAE,GAAEI,KAAE,yBAASN,IAAE;AAAC,iBAAO,SAASC,IAAEG,IAAEG,IAAE;AAAC,mBAAOP,GAAE,EAAEC,IAAEG,IAAEG,IAAEP,GAAE,CAAC;AAAA,UAAC;AAAA,QAAC,EAAEK,EAAC,KAAGH,GAAE,MAAIG,GAAE,IAAEoE,IAAGvE,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEI,KAAE,yBAASN,IAAE;AAAC,iBAAO,SAASC,IAAEG,IAAEG,IAAE;AAAC,mBAAOP,GAAE,EAAEC,IAAEG,IAAEG,IAAEP,GAAE,CAAC;AAAA,UAAC;AAAA,QAAC,EAAEK,EAAC,KAAGC,KAAED,GAAE,GAAEH,GAAE,KAAGI,KAAGA,GAAEL,IAAED,IAAEE,GAAE,CAAC,GAAEG,KAAE,EAAC,GAAEA,GAAE,GAAE,GAAEA,GAAE,GAAE,GAAEA,GAAE,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,KAAC,SAASL,IAAEC,IAAE;AAAC,UAAGA,KAAEA,GAAE,IAAG;AAAC,QAAAyC,IAAG1C,IAAEA,GAAE,EAAE,IAAI,CAAC;AAAE,iBAAQI,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,CAAAsC,IAAG1C,IAAEC,GAAEG,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,EAAEH,IAAED,EAAC;AAAA,EAAC;AAAC,MAAI0E,MAAG,OAAO;AAAE,WAASC,IAAG3E,IAAEC,IAAEG,IAAE;AAAC,WAAOJ,GAAE0E,GAAE,MAAI1E,GAAE0E,GAAE,IAAE,SAAS1E,IAAEO,IAAE;AAAC,aAAON,GAAED,IAAEO,IAAEH,EAAC;AAAA,IAAC;AAAA,EAAE;AAAC,WAASwE,IAAG5E,IAAE;AAAC,QAAIC,KAAED,GAAE0E,GAAE;AAAE,QAAG,CAACzE,IAAE;AAAC,UAAIG,KAAE,GAAGJ,EAAC;AAAE,MAAAC,KAAE,SAASD,IAAEC,IAAE;AAAC,eAAO,GAAGD,IAAEC,IAAEG,EAAC;AAAA,MAAC,GAAEJ,GAAE0E,GAAE,IAAEzE;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAC,WAAS4E,IAAG7E,IAAE;AAAC,QAAIC,KAAE,SAASD,IAAE;AAAC,UAAIC,KAAED,GAAE;AAAG,aAAOC,KAAE2E,IAAG3E,EAAC,KAAGA,KAAED,GAAE,MAAI2E,IAAG3E,GAAE,EAAE,GAAEC,IAAED,GAAE,EAAE,IAAE;AAAA,IAAM,EAAEA,EAAC,GAAEI,KAAEJ,GAAE,GAAEO,KAAEP,GAAE,GAAG;AAAE,WAAOC,KAAE,SAASD,IAAEK,IAAE;AAAC,aAAOE,GAAEP,IAAEK,IAAED,IAAEH,EAAC;AAAA,IAAC,IAAE,SAASD,IAAEC,IAAE;AAAC,aAAOM,GAAEP,IAAEC,IAAEG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS0E,IAAG9E,IAAEC,IAAEG,IAAEG,IAAEF,IAAEH,IAAE;AAAC,QAAII,KAAE;AAAE,UAAKN,KAAEA,GAAE,GAAG,UAAQ,YAAU,OAAOA,GAAE,CAAC,MAAII,GAAEH,IAAED,GAAE,CAAC,CAAC,GAAEM,OAAKA,KAAEN,GAAE,UAAQ;AAAC,MAAAI,KAAEJ,GAAEM,IAAG;AAAE,eAAQH,KAAEG,KAAE,GAAEH,KAAEH,GAAE,UAAQ,YAAU,OAAOA,GAAEG,EAAC,IAAG,CAAAA;AAAI,UAAIgB,KAAEnB,GAAEM,IAAG;AAAE,cAAOH,MAAGG,IAAE;AAAA,QAAC,KAAK;AAAE,UAAAC,GAAEN,IAAEG,IAAEe,EAAC;AAAE;AAAA,QAAM,KAAK;AAAE,UAAAZ,GAAEN,IAAEG,IAAEe,IAAEnB,GAAEM,IAAG,CAAC;AAAE;AAAA,QAAM,KAAK;AAAE,UAAAD,GAAEJ,IAAEG,IAAEe,IAAEnB,GAAEM,IAAG,GAAEN,GAAEM,IAAG,CAAC;AAAE;AAAA,QAAM,KAAK;AAAE,UAAAH,KAAEH,GAAEM,IAAG;AAAE,cAAIc,KAAEpB,GAAEM,IAAG,GAAEyE,KAAE/E,GAAEM,IAAG;AAAE,gBAAM,QAAQyE,EAAC,IAAE1E,GAAEJ,IAAEG,IAAEe,IAAEhB,IAAEiB,IAAE2D,EAAC,IAAE7E,GAAED,IAAEG,IAAEe,IAAEhB,IAAEiB,IAAE2D,EAAC;AAAE;AAAA,QAAM,KAAK;AAAE,UAAA7E,GAAED,IAAEG,IAAEe,IAAEnB,GAAEM,IAAG,GAAEN,GAAEM,IAAG,GAAEN,GAAEM,IAAG,GAAEN,GAAEM,IAAG,CAAC;AAAE;AAAA,QAAM;AAAQ,gBAAM,MAAM,kDAAgDH,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAC,MAAI+E,MAAG,OAAO;AAAE,WAASR,IAAGxE,IAAE;AAAC,QAAIC,KAAED,GAAEgF,GAAE;AAAE,QAAG,CAAC/E,IAAE;AAAC,UAAIG,KAAE6E,IAAGjF,EAAC;AAAE,MAAAC,KAAE,SAASD,IAAEC,IAAE;AAAC,eAAO,GAAGD,IAAEC,IAAEG,EAAC;AAAA,MAAC,GAAEJ,GAAEgF,GAAE,IAAE/E;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAC,WAASwE,IAAGzE,IAAEC,IAAE;AAAC,QAAIG,KAAEJ,GAAEgF,GAAE;AAAE,WAAO5E,OAAIA,KAAE,SAASJ,IAAEI,IAAE;AAAC,aAAOmE,IAAGvE,IAAEI,IAAEH,EAAC;AAAA,IAAC,GAAED,GAAEgF,GAAE,IAAE5E,KAAGA;AAAA,EAAC;AAAC,MAAI8E,MAAG,OAAO;AAAE,WAASC,IAAGnF,IAAEC,IAAE;AAAC,IAAAD,GAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,WAASmF,IAAGpF,IAAEC,IAAEG,IAAE;AAAC,IAAAJ,GAAE,KAAKC,IAAEG,GAAE,CAAC;AAAA,EAAC;AAAC,WAASiF,IAAGrF,IAAEC,IAAEG,IAAEG,IAAEF,IAAE;AAAC,QAAIH,KAAEsE,IAAGnE,EAAC,GAAEC,KAAEF,GAAE;AAAE,IAAAJ,GAAE,KAAKC,IAAG,SAASD,IAAEC,IAAEG,IAAE;AAAC,aAAOE,GAAEN,IAAEC,IAAEG,IAAEG,IAAEL,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAASoF,IAAGtF,IAAEC,IAAEG,IAAEG,IAAEF,IAAEH,IAAE;AAAC,QAAII,KAAEmE,IAAGlE,IAAEL,EAAC,GAAEC,KAAEC,GAAE;AAAE,IAAAJ,GAAE,KAAKC,IAAG,SAASD,IAAEC,IAAEG,IAAE;AAAC,aAAOD,GAAEH,IAAEC,IAAEG,IAAEG,IAAED,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS2E,IAAGjF,IAAE;AAAC,QAAIC,KAAED,GAAEkF,GAAE;AAAE,WAAOjF,MAAG6E,IAAG9E,IAAEA,GAAEkF,GAAE,IAAE,CAAC,GAAEC,KAAGC,KAAGC,KAAGC,GAAE;AAAA,EAAC;AAAC,MAAIC,MAAG,OAAO;AAAE,WAASC,IAAGxF,IAAEC,IAAE;AAAC,IAAAD,GAAE,CAAC,IAAEC;AAAA,EAAC;AAAC,WAASwF,IAAGzF,IAAEC,IAAEG,IAAEG,IAAE;AAAC,QAAIF,KAAED,GAAE;AAAE,IAAAJ,GAAEC,EAAC,IAAEM,KAAE,SAASP,IAAEC,IAAEG,IAAE;AAAC,aAAOC,GAAEL,IAAEC,IAAEG,IAAEG,EAAC;AAAA,IAAC,IAAEF;AAAA,EAAC;AAAC,WAASqF,IAAG1F,IAAEC,IAAEG,IAAEG,IAAEF,IAAEH,IAAE;AAAC,QAAII,KAAEF,GAAE,GAAED,KAAEyE,IAAGvE,EAAC;AAAE,IAAAL,GAAEC,EAAC,IAAE,SAASD,IAAEC,IAAEG,IAAE;AAAC,aAAOE,GAAEN,IAAEC,IAAEG,IAAEG,IAAEJ,IAAED,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAASyF,IAAG3F,IAAEC,IAAEG,IAAEG,IAAEF,IAAEH,IAAEI,IAAE;AAAC,QAAIH,KAAEC,GAAE,GAAEe,KAAEwD,IAAGpE,IAAEF,IAAEH,EAAC;AAAE,IAAAF,GAAEC,EAAC,IAAE,SAASD,IAAEC,IAAEG,IAAE;AAAC,aAAOD,GAAEH,IAAEC,IAAEG,IAAEG,IAAEY,IAAEb,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,GAAGN,IAAE;AAAC,QAAIC,KAAED,GAAEuF,GAAE;AAAE,WAAOtF,MAAG6E,IAAG9E,IAAEA,GAAEuF,GAAE,IAAE,CAAC,GAAEC,KAAGC,KAAGC,KAAGC,GAAE;AAAA,EAAC;AAAC,WAAS,GAAG3F,IAAEC,IAAEG,IAAE;AAAC,WAAKkC,IAAGrC,EAAC,KAAG,KAAGA,GAAE,KAAG;AAAC,UAAIM,KAAEN,GAAE,GAAEI,KAAED,GAAEG,EAAC;AAAE,UAAG,CAACF,IAAE;AAAC,YAAIH,KAAEE,GAAE,CAAC;AAAE,QAAAF,OAAIA,KAAEA,GAAEK,EAAC,OAAKF,KAAED,GAAEG,EAAC,IAAEsE,IAAG3E,EAAC;AAAA,MAAE;AAAC,UAAG,EAAEG,MAAGA,GAAEJ,IAAED,IAAEO,EAAC,MAAIF,KAAEJ,IAAEM,KAAEP,IAAEE,KAAEG,GAAE,GAAEkC,IAAGlC,EAAC,GAAEA,GAAE,KAAI;AAAC,YAAIC,KAAED,GAAE,EAAE;AAAE,QAAAA,KAAEH,QAAKG,KAAEA,GAAE,EAAE,KAAGiB,OAAIA,KAAE,IAAI,WAAW,CAAC,KAAGI,KAAEpB,GAAE,MAAMJ,IAAEG,EAAC,IAAE,IAAI,WAAWC,GAAE,SAASJ,IAAEG,EAAC,CAAC,IAAGH,KAAEK,GAAE,MAAIL,GAAE,KAAKG,EAAC,IAAEE,GAAE,KAAG,CAACF,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOL;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAEG,IAAE;AAAC,QAAGoC,IAAG,QAAO;AAAC,UAAIjC,KAAEiC,IAAG,IAAI;AAAE,MAAAxC,OAAI+B,GAAExB,GAAE,GAAEP,EAAC,GAAEO,GAAE,IAAE,IAAGA,GAAE,IAAE,KAAIP,KAAEO;AAAA,IAAC,MAAM,CAAAP,KAAE,IAAIqC,GAAErC,EAAC;AAAE,QAAG;AAAC,aAAO,GAAG,IAAIC,MAAED,IAAE,GAAGI,EAAC,CAAC;AAAA,IAAC,UAAC;AAAQ,OAACH,KAAED,GAAE,GAAG,IAAE,MAAKC,GAAE,IAAE,GAAEA,GAAE,IAAE,GAAEA,GAAE,IAAE,GAAEA,GAAE,IAAE,OAAGD,GAAE,IAAE,IAAGA,GAAE,IAAE,IAAG,MAAIwC,IAAG,UAAQA,IAAG,KAAKxC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAEG,IAAE;AAAC,aAAQG,KAAEH,GAAE,QAAOC,KAAE,KAAGE,KAAE,GAAEL,KAAEG,KAAE,IAAE,GAAEH,KAAEK,IAAEL,MAAG,EAAE,EAAC,GAAEE,GAAEF,KAAE,CAAC,GAAGD,IAAED,IAAEI,GAAEF,EAAC,CAAC;AAAE,IAAAqE,IAAGvE,IAAEC,IAAEI,KAAED,GAAE,CAAC,IAAE,MAAM;AAAA,EAAC;AAAC,WAAS,GAAGJ,IAAEC,IAAE;AAAC,QAAIG,KAAE,IAAIqC;AAAG,OAAGzC,IAAEI,IAAE6E,IAAGhF,EAAC,CAAC,GAAEyC,IAAGtC,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAEJ,KAAE,IAAI,WAAWI,GAAE,CAAC;AAAE,aAAQG,MAAGN,KAAEG,GAAE,GAAG,QAAOC,KAAE,GAAEH,KAAE,GAAEA,KAAEK,IAAEL,MAAI;AAAC,UAAII,KAAEL,GAAEC,EAAC;AAAE,MAAAF,GAAE,IAAIM,IAAED,EAAC,GAAEA,MAAGC,GAAE;AAAA,IAAM;AAAC,WAAOF,GAAE,IAAE,CAACJ,EAAC,GAAEA;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAE;AAAC,WAAM,EAAC,GAAED,IAAE,GAAEC,GAAC;AAAA,EAAC;AAAC,MAAI,KAAG,GAAI,SAASD,IAAEC,IAAEG,IAAE;AAAC,QAAG,MAAIJ,GAAE,EAAE,QAAM;AAAG,QAAIO,MAAGP,KAAEA,GAAE,GAAG,EAAEA,GAAE,CAAC,GAAEK,KAAEL,GAAE,EAAEA,GAAE,IAAE,CAAC,GAAEE,KAAEF,GAAE,EAAEA,GAAE,IAAE,CAAC,GAAEM,KAAEN,GAAE,EAAEA,GAAE,IAAE,CAAC;AAAE,WAAOA,GAAE,KAAG,GAAEgC,GAAEhC,EAAC,GAAEA,KAAE,MAAIK,MAAGE,MAAG,IAAEF,MAAG,IAAEH,MAAG,KAAGI,MAAG,QAAM,MAAI,MAAI,GAAEC,KAAEF,OAAI,KAAG,KAAIA,MAAG,SAAQmD,IAAGvD,IAAEG,IAAE,OAAKG,KAAEF,KAAE,MAAI,IAAE,IAAEL,KAAE,KAAGO,KAAEP,KAAE,KAAK,IAAI,GAAE,IAAI,IAAEK,KAAEL,KAAE,KAAK,IAAI,GAAEO,KAAE,GAAG,KAAGF,KAAE,KAAK,IAAI,GAAE,EAAE,EAAE,GAAE;AAAA,EAAE,GAAI,SAASL,IAAEC,IAAEG,IAAE;AAAC,QAAG,SAAOH,KAAEsD,IAAGtD,IAAEG,EAAC,IAAG;AAAC,MAAAgC,GAAEpC,GAAE,GAAE,IAAEI,KAAE,CAAC,GAAEJ,KAAEA,GAAE;AAAE,UAAIO,KAAEN;AAAE,aAAKM,MAAGH,KAAE,IAAEG,KAAE,IAAE,KAAG,CAACA,KAAEA,MAAG,IAAE,IAAEA,KAAEoB,KAAE,KAAG,GAAEA,KAAE,cAAY,MAAMpB,EAAC,KAAG,GAAEoB,KAAE,cAAY,uBAAqBpB,MAAG,GAAEoB,MAAGvB,MAAG,KAAG,gBAAc,KAAG,wBAAsBG,MAAGA,KAAE,KAAK,MAAMA,KAAE,KAAK,IAAI,GAAE,IAAI,CAAC,GAAE,GAAEoB,MAAGvB,MAAG,KAAGG,QAAK,MAAIN,KAAE,KAAK,MAAM,KAAK,IAAIM,EAAC,IAAE,KAAK,GAAG,GAAEA,MAAG,KAAK,IAAI,GAAE,CAACN,EAAC,GAAE,aAAWM,KAAE,KAAK,MAAM,UAAQA,EAAC,MAAI,EAAEN,IAAE,GAAE0B,MAAGvB,MAAG,KAAGH,KAAE,OAAK,KAAG,UAAQM,QAAK,IAAGH,KAAEuB,IAAE3B,GAAE,EAAE,KAAKI,OAAI,IAAE,GAAG,GAAEJ,GAAE,EAAE,KAAKI,OAAI,IAAE,GAAG,GAAEJ,GAAE,EAAE,KAAKI,OAAI,KAAG,GAAG,GAAEJ,GAAE,EAAE,KAAKI,OAAI,KAAG,GAAG;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,KAAG,GAAI,SAASJ,IAAEC,IAAEG,IAAE;AAAC,QAAG,MAAIJ,GAAE,EAAE,QAAM;AAAG,aAAQO,KAAEP,GAAE,GAAEK,KAAE,KAAIH,KAAE,GAAEI,KAAEN,KAAE,GAAE,IAAEM,MAAG,OAAKD,IAAEC,KAAI,CAAAD,KAAEE,GAAE,EAAEA,GAAE,GAAG,GAAEyB,GAAEzB,EAAC,GAAEL,OAAI,MAAIG,OAAI,IAAEC;AAAE,QAAG,OAAKD,OAAIA,KAAEE,GAAE,EAAEA,GAAE,GAAG,GAAEyB,GAAEzB,EAAC,GAAEL,OAAI,MAAIG,OAAI,IAAGL,OAAI,MAAIK,OAAI,IAAG,OAAKA,GAAE,MAAIC,KAAE,GAAE,IAAEA,MAAG,OAAKD,IAAEC,KAAI,CAAAD,KAAEE,GAAE,EAAEA,GAAE,GAAG,GAAEyB,GAAEzB,EAAC,GAAEP,OAAI,MAAIK,OAAI,IAAEC,KAAE;AAAE,QAAG,EAAE,MAAID,IAAG,OAAMwB,GAAE;AAAE,WAAOtB,KAAEL,OAAI,IAAGF,KAAE,cAAYK,KAAEL,OAAI,QAAMK,KAAE,CAACA,OAAI,GAAE,MAAIE,KAAE,IAAE,CAACA,OAAI,OAAKF,KAAEA,KAAE,MAAI,KAAIE,KAAE,aAAWF,MAAGE,OAAI,IAAGiD,IAAGvD,IAAEG,IAAEJ,KAAE,CAACO,KAAEA,EAAC,GAAE;AAAA,EAAE,GAAI,SAASP,IAAEC,IAAEG,IAAE;AAAC,QAAG,SAAOH,KAAEsD,IAAGtD,IAAEG,EAAC,MAAI,QAAMH,IAAE;AAAC,MAAAmC,GAAEpC,GAAE,GAAE,IAAEI,EAAC,GAAEJ,KAAEA,GAAE;AAAE,UAAIO,KAAEN;AAAE,WAAIG,KAAE,IAAEG,IAAEN,MAAGM,KAAE,KAAK,IAAIA,EAAC,OAAK,GAAEA,KAAE,KAAK,OAAOA,KAAEN,MAAG,UAAU,GAAEM,QAAK,GAAEH,OAAIG,KAAE,CAACA,OAAI,GAAE,cAAYN,KAAE,KAAG,CAACA,OAAI,QAAMA,KAAE,GAAE,aAAW,EAAEM,OAAIA,KAAE,MAAKH,KAAEuB,KAAE1B,IAAEA,KAAEM,IAAE,IAAEN,MAAG,MAAIG,KAAG,CAAAJ,GAAE,EAAE,KAAK,MAAII,KAAE,GAAG,GAAEA,MAAGA,OAAI,IAAEH,MAAG,QAAM,GAAEA,QAAK;AAAE,MAAAD,GAAE,EAAE,KAAKI,EAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,KAAG,GAAI,SAASJ,IAAEC,IAAEG,IAAE;AAAC,WAAO,MAAIJ,GAAE,MAAIwD,IAAGvD,IAAEG,IAAE6B,GAAEjC,GAAE,CAAC,CAAC,GAAE;AAAA,EAAG,GAAI,SAASA,IAAEC,IAAEG,IAAE;AAAC,QAAG,SAAOH,KAAEsD,IAAGtD,IAAEG,EAAC,MAAI,QAAMH,GAAE,KAAGmC,GAAEpC,GAAE,GAAE,IAAEI,EAAC,GAAEJ,KAAEA,GAAE,GAAE,MAAII,KAAEH,IAAG,CAAAmC,GAAEpC,IAAEI,EAAC;AAAA,SAAM;AAAC,WAAIH,KAAE,GAAE,IAAEA,IAAEA,KAAI,CAAAD,GAAE,EAAE,KAAK,MAAII,KAAE,GAAG,GAAEA,OAAI;AAAE,MAAAJ,GAAE,EAAE,KAAK,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,KAAG,GAAI,SAASA,IAAEC,IAAEG,IAAE;AAAC,QAAG,MAAIJ,GAAE,EAAE,QAAM;AAAG,QAAIO,IAAEF,KAAE4B,GAAEjC,GAAE,CAAC,MAAI,GAAEE,MAAGF,KAAEA,GAAE,GAAG;AAAE,QAAGA,GAAE,KAAGK,IAAE2B,GAAEhC,EAAC,GAAEA,KAAEA,GAAE,GAAEa,GAAE,EAACN,KAAEI,QAAKJ,KAAEI,KAAE,IAAI,YAAY,SAAQ,EAAC,OAAM,KAAE,CAAC,IAAGJ,KAAEA,GAAE,OAAOP,GAAE,SAASE,IAAEA,KAAEG,EAAC,CAAC;AAAA,SAAM;AAAC,MAAAA,KAAEH,KAAEG;AAAE,eAAQC,IAAEH,IAAEgB,IAAEC,KAAE,CAAC,GAAE2D,KAAE,MAAK7E,KAAEG,KAAG,QAAKC,KAAEN,GAAEE,IAAG,KAAGkB,GAAE,KAAKd,EAAC,IAAE,MAAIA,KAAEJ,MAAGG,KAAEI,GAAE,KAAGN,KAAEH,GAAEE,IAAG,GAAE,MAAII,MAAG,QAAM,MAAIH,OAAID,MAAIO,GAAE,KAAGW,GAAE,MAAM,KAAGd,OAAI,IAAE,KAAGH,EAAC,KAAG,MAAIG,KAAEJ,MAAGG,KAAE,IAAEI,GAAE,IAAE,QAAM,OAAKN,KAAEH,GAAEE,IAAG,OAAK,QAAMI,MAAG,MAAIH,MAAG,QAAMG,MAAG,OAAKH,MAAG,QAAM,OAAKI,KAAEP,GAAEE,IAAG,OAAKA,MAAIO,GAAE,KAAGW,GAAE,MAAM,KAAGd,OAAI,MAAI,KAAGH,OAAI,IAAE,KAAGI,EAAC,IAAE,OAAKD,KAAEJ,MAAGG,KAAE,IAAEI,GAAE,IAAE,QAAM,OAAKN,KAAEH,GAAEE,IAAG,OAAK,KAAGC,KAAE,OAAKG,MAAG,OAAK,MAAI,QAAM,OAAKC,KAAEP,GAAEE,IAAG,OAAK,QAAM,OAAKiB,KAAEnB,GAAEE,IAAG,OAAKA,MAAIO,GAAE,MAAIH,MAAG,IAAEA,OAAI,MAAI,KAAGH,OAAI,MAAI,KAAGI,OAAI,IAAE,KAAGY,IAAEb,MAAG,OAAMc,GAAE,KAAK,SAAOd,MAAG,KAAG,OAAM,SAAO,OAAKA,GAAE,KAAGG,GAAE,GAAE,QAAMW,GAAE,WAAS2D,KAAErE,GAAEqE,IAAE3D,EAAC,GAAEA,GAAE,SAAO;AAAG,MAAAb,KAAEG,GAAEqE,IAAE3D,EAAC;AAAA,IAAC;AAAC,WAAOoC,IAAGvD,IAAEG,IAAEG,EAAC,GAAE;AAAA,EAAE,GAAI,SAASP,IAAEC,IAAEG,IAAE;AAAC,QAAG,SAAOH,KAAEsD,IAAGtD,IAAEG,EAAC,IAAG;AAAC,UAAIG,KAAE;AAAG,UAAGA,KAAE,WAASA,MAAGA,IAAEO,IAAE;AAAC,YAAGP,MAAG,2EAA2E,KAAKN,EAAC,EAAE,OAAM,MAAM,6BAA6B;AAAE,QAAAA,MAAGW,OAAIA,KAAE,IAAI,gBAAc,OAAOX,EAAC;AAAA,MAAC,OAAK;AAAC,iBAAQI,KAAE,GAAEH,KAAE,IAAI,WAAW,IAAED,GAAE,MAAM,GAAEK,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAI;AAAC,cAAIH,KAAEF,GAAE,WAAWK,EAAC;AAAE,cAAG,MAAIH,GAAE,CAAAD,GAAEG,IAAG,IAAEF;AAAA,eAAM;AAAC,gBAAG,OAAKA,GAAE,CAAAD,GAAEG,IAAG,IAAEF,MAAG,IAAE;AAAA,iBAAQ;AAAC,kBAAG,SAAOA,MAAG,SAAOA,IAAE;AAAC,oBAAG,SAAOA,MAAGG,KAAEL,GAAE,QAAO;AAAC,sBAAIkB,KAAElB,GAAE,WAAW,EAAEK,EAAC;AAAE,sBAAG,SAAOa,MAAG,SAAOA,IAAE;AAAC,oBAAAhB,KAAE,QAAMA,KAAE,SAAOgB,KAAE,QAAM,OAAMjB,GAAEG,IAAG,IAAEF,MAAG,KAAG,KAAID,GAAEG,IAAG,IAAEF,MAAG,KAAG,KAAG,KAAID,GAAEG,IAAG,IAAEF,MAAG,IAAE,KAAG,KAAID,GAAEG,IAAG,IAAE,KAAGF,KAAE;AAAI;AAAA,kBAAQ;AAAC,kBAAAG;AAAA,gBAAG;AAAC,oBAAGC,GAAE,OAAM,MAAM,6BAA6B;AAAE,gBAAAJ,KAAE;AAAA,cAAK;AAAC,cAAAD,GAAEG,IAAG,IAAEF,MAAG,KAAG,KAAID,GAAEG,IAAG,IAAEF,MAAG,IAAE,KAAG;AAAA,YAAG;AAAC,YAAAD,GAAEG,IAAG,IAAE,KAAGF,KAAE;AAAA,UAAG;AAAA,QAAC;AAAC,QAAAF,KAAEC,GAAE,SAAS,GAAEG,EAAC;AAAA,MAAC;AAAC,MAAA+B,GAAEpC,GAAE,GAAE,IAAEI,KAAE,CAAC,GAAEgC,GAAEpC,GAAE,GAAEC,GAAE,MAAM,GAAEyC,IAAG1C,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAE0C,IAAG1C,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,KAAG,GAAI,SAASD,IAAEC,IAAEG,IAAEG,IAAEF,IAAE;AAAC,QAAG,MAAIL,GAAE,EAAE,QAAM;AAAG,IAAAC,KAAE2D,IAAG3D,IAAEG,IAAEG,EAAC,GAAEH,KAAEJ,GAAE,EAAE,GAAEO,KAAE0B,GAAEjC,GAAE,CAAC,MAAI;AAAE,QAAIE,KAAEF,GAAE,EAAE,IAAEO,IAAED,KAAEJ,KAAEE;AAAE,QAAG,KAAGE,OAAIN,GAAE,EAAE,IAAEE,IAAEG,GAAEJ,IAAED,EAAC,GAAEM,KAAEJ,KAAEF,GAAE,EAAE,IAAGM,GAAE,OAAM,MAAM,0DAAwDC,KAAE,2BAAyBA,KAAED,MAAG,sFAAsF;AAAE,WAAON,GAAE,EAAE,IAAEE,IAAEF,GAAE,EAAE,IAAEI,IAAE;AAAA,EAAE,GAAI,SAASJ,IAAEC,IAAEG,IAAEG,IAAEF,IAAE;AAAC,QAAG,SAAOJ,KAAE0D,IAAG1D,IAAEM,IAAEH,EAAC,GAAG,MAAIG,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,UAAIL,KAAEF;AAAE,MAAAoC,GAAElC,GAAE,GAAE,IAAEE,KAAE,CAAC;AAAE,UAAIE,KAAEJ,GAAE,EAAE,IAAI;AAAE,MAAAwC,IAAGxC,IAAEI,EAAC,GAAEA,GAAE,KAAKJ,GAAE,CAAC,GAAEA,KAAEI,IAAED,GAAEJ,GAAEM,EAAC,GAAEP,EAAC,GAAEM,KAAEN;AAAE,UAAIG,KAAED,GAAE,IAAI;AAAE,WAAIC,KAAEG,GAAE,IAAEA,GAAE,EAAE,OAAO,IAAEH,IAAE,MAAIA,KAAG,CAAAD,GAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK,GAAEG,GAAE;AAAI,MAAAJ,GAAE,KAAKC,EAAC,GAAEG,GAAE;AAAA,IAAG;AAAA,EAAC,CAAE;AAAE,WAAS,KAAI;AAAC,IAAA+D,IAAG,MAAM,MAAK,SAAS;AAAA,EAAC;AAAC,MAAG,EAAE,IAAGA,GAAE,GAAEhB,KAAG;AAAC,QAAI,KAAG,CAAC;AAAE,WAAO,iBAAiB,KAAI,GAAG,OAAO,WAAW,IAAEC,IAAG,OAAO,OAAO,WAAW,CAAC,GAAE,GAAG;AAAA,EAAC;AAAC,WAAS,GAAGtD,IAAE;AAAC,OAAG,KAAK,MAAKA,EAAC;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,WAAM,CAAC,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,EAAE;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,OAAG,KAAK,MAAKA,IAAE,IAAG,EAAE;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,WAAM,CAAC,GAAE,IAAG,IAAG,EAAE;AAAA,EAAC;AAAC,IAAE,IAAG,EAAE,GAAE,EAAE,IAAG,EAAE,GAAE,GAAG,UAAU,oBAAkB,SAASA,IAAEC,IAAE;AAAC,WAAO2D,IAAG,MAAK,GAAE,IAAG5D,IAAEC,EAAC,GAAE;AAAA,EAAI;AAAE,MAAI,KAAG,CAAC,CAAC;AAAE,WAAS,GAAGD,IAAE;AAAC,OAAG,KAAK,MAAKA,EAAC;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,WAAM,CAAC,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,EAAE;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,OAAG,KAAK,MAAKA,IAAE,IAAG,EAAE;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,WAAM,CAAC,GAAE,IAAG,IAAG,EAAE;AAAA,EAAC;AAAC,IAAE,IAAG,EAAE,GAAE,EAAE,IAAG,EAAE;AAAE,MAAI,KAAG,CAAC,CAAC;AAAE,WAAS,GAAGA,IAAE;AAAC,OAAG,KAAK,MAAKA,EAAC;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,WAAM,CAAC,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,EAAE;AAAA,EAAC;AAAC,IAAE,IAAG,EAAE;AAAE,MAAI,KAAG,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,GAAE,KAAG,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,GAAE,KAAG,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,GAAE,KAAG,CAAC,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,GAAE,KAAG,CAAC,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,CAAC,GAAE,KAAG,CAAC,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,CAAC,GAAE,KAAG,CAAC,EAAE,OAAO,EAAE,EAAE,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE,CAAC;AAAE,WAAS,GAAGA,IAAEC,IAAEG,IAAE;AAAC,QAAGA,KAAEJ,GAAE,aAAa,MAAII,KAAEJ,GAAE,gBAAcA,GAAE,eAAe,GAAEA,GAAE,aAAaI,IAAEH,EAAC,GAAED,GAAE,cAAcI,EAAC,GAAE,CAACJ,GAAE,mBAAmBI,IAAEJ,GAAE,cAAc,EAAE,OAAM,MAAM,wCAAsCA,GAAE,iBAAiBI,EAAC,CAAC;AAAE,WAAOA;AAAA,EAAC;AAAC,WAAS,GAAGJ,IAAE;AAAC,WAAO2D,IAAG3D,IAAE,IAAG,CAAC,EAAE,IAAK,SAASA,IAAE;AAAC,aAAM,EAAC,OAAM6D,IAAG7D,IAAE,CAAC,GAAE,IAAG0D,IAAG1D,IAAE,CAAC,GAAE,OAAM,QAAMuD,IAAGvD,IAAE,CAAC,IAAE8D,IAAG9D,IAAE,CAAC,IAAE,QAAO,aAAY,QAAMuD,IAAGvD,IAAE,CAAC,IAAE8D,IAAG9D,IAAE,CAAC,IAAE,OAAM;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAM,EAAC,GAAE0D,IAAG1D,IAAE,CAAC,GAAE,GAAE0D,IAAG1D,IAAE,CAAC,GAAE,GAAE0D,IAAG1D,IAAE,CAAC,GAAE,YAAW,QAAMuD,IAAGvD,IAAE,CAAC,IAAE0D,IAAG1D,IAAE,CAAC,IAAE,OAAM;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAE;AAAC,SAAK,IAAED,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAE;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAEC,IAAEG,IAAE;AAAC,WAAO,SAASJ,IAAEC,IAAE;AAAC,UAAIG,KAAEJ,GAAE;AAAE,UAAG,WAASA,GAAE,GAAE;AAAC,YAAIO,KAAE,GAAGH,IAAE,qKAAoK,CAAC,GAAEC,KAAE,GAAGD,IAAE,yJAAwJ,CAAC,GAAEF,KAAEE,GAAE,cAAc;AAAE,YAAGA,GAAE,aAAaF,IAAEK,EAAC,GAAEH,GAAE,aAAaF,IAAEG,EAAC,GAAED,GAAE,YAAYF,EAAC,GAAE,CAACE,GAAE,oBAAoBF,IAAEE,GAAE,WAAW,EAAE,OAAM,MAAM,yCAAuCA,GAAE,kBAAkBF,EAAC,CAAC;AAAE,QAAAK,KAAEP,GAAE,IAAEE,IAAEE,GAAE,WAAWG,EAAC,GAAEF,KAAED,GAAE,mBAAmBG,IAAE,UAAU,GAAEP,GAAE,IAAE,EAAC,GAAEI,GAAE,kBAAkBG,IAAE,SAAS,GAAE,GAAEH,GAAE,kBAAkBG,IAAE,MAAM,GAAE,IAAGF,GAAC,GAAEL,GAAE,IAAEI,GAAE,aAAa,GAAEA,GAAE,WAAWA,GAAE,cAAaJ,GAAE,CAAC,GAAEI,GAAE,wBAAwBJ,GAAE,EAAE,CAAC,GAAEI,GAAE,oBAAoBJ,GAAE,EAAE,GAAE,GAAEI,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAa,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,EAAE,CAAC,GAAEA,GAAE,WAAW,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,GAAEJ,GAAE,IAAEI,GAAE,aAAa,GAAEA,GAAE,WAAWA,GAAE,cAAaJ,GAAE,CAAC,GAAEI,GAAE,wBAAwBJ,GAAE,EAAE,CAAC,GAAEI,GAAE,oBAAoBJ,GAAE,EAAE,GAAE,GAAEI,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAa,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,WAAW,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,GAAEA,GAAE,UAAUC,IAAE,CAAC;AAAA,MAAC;AAAC,MAAAE,KAAEP,GAAE,GAAEI,GAAE,WAAWJ,GAAE,CAAC,GAAEI,GAAE,OAAO,QAAMH,GAAE,OAAMG,GAAE,OAAO,SAAOH,GAAE,QAAOG,GAAE,SAAS,GAAE,GAAEH,GAAE,OAAMA,GAAE,MAAM,GAAEG,GAAE,cAAcA,GAAE,QAAQ,GAAEJ,GAAE,EAAE,cAAcC,GAAE,MAAM,GAAEG,GAAE,wBAAwBG,GAAE,CAAC,GAAEH,GAAE,WAAWA,GAAE,cAAaJ,GAAE,CAAC,GAAEI,GAAE,oBAAoBG,GAAE,GAAE,GAAEH,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,wBAAwBG,GAAE,CAAC,GAAEH,GAAE,WAAWA,GAAE,cAAaJ,GAAE,CAAC,GAAEI,GAAE,oBAAoBG,GAAE,GAAE,GAAEH,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,gBAAgBA,GAAE,mBAAiBA,GAAE,mBAAiBA,GAAE,aAAY,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,UAAU,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAEA,GAAE,yBAAyBG,GAAE,CAAC,GAAEH,GAAE,yBAAyBG,GAAE,CAAC,GAAEH,GAAE,WAAWA,GAAE,cAAa,IAAI,GAAEJ,GAAE,EAAE,cAAc,CAAC;AAAA,IAAC,EAAEA,IAAEC,EAAC,GAAE,cAAY,OAAOD,GAAE,EAAE,OAAO,wBAAsB,QAAQ,QAAQA,GAAE,EAAE,OAAO,sBAAsB,CAAC,IAAEI,KAAE,QAAQ,QAAQJ,GAAE,EAAE,MAAM,IAAE,cAAY,OAAO,oBAAkB,kBAAkBA,GAAE,EAAE,MAAM,KAAG,WAASA,GAAE,MAAIA,GAAE,IAAE,SAAS,cAAc,QAAQ,IAAG,IAAI,QAAS,SAASC,IAAE;AAAC,MAAAD,GAAE,EAAE,SAAOA,GAAE,EAAE,OAAO,QAAOA,GAAE,EAAE,QAAMA,GAAE,EAAE,OAAO,OAAMA,GAAE,EAAE,WAAW,MAAK,CAAC,CAAC,EAAE,UAAUA,GAAE,EAAE,QAAO,GAAE,GAAEA,GAAE,EAAE,OAAO,OAAMA,GAAE,EAAE,OAAO,MAAM,GAAEC,GAAED,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAE;AAAC,WAAS,GAAGA,IAAE;AAAC,SAAK,IAAEA;AAAA,EAAC;AAAC,MAAI,KAAG,IAAI,WAAW,CAAC,GAAE,IAAG,KAAI,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,IAAG,IAAG,EAAE,CAAC;AAAE,WAAS,GAAGA,IAAEC,IAAE;AAAC,WAAOA,KAAED;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAE;AAAC,WAAOD,EAAC,IAAEC;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAE;AAAC,QAAG,KAAK,IAAEA,IAAE,KAAK,YAAU,CAAC,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,MAAG,KAAK,IAAE,QAAQ,QAAQ,GAAE,KAAK,IAAE,IAAG,KAAK,IAAE,CAAC,GAAE,KAAK,aAAWA,MAAGA,GAAE,cAAY,IAAG,YAAU,OAAO,OAAO,KAAIC,KAAE,OAAO,SAAS,SAAS,SAAS,EAAE,UAAU,GAAE,OAAO,SAAS,SAAS,SAAS,EAAE,YAAY,GAAG,CAAC,IAAE;AAAA,SAAQ;AAAC,UAAG,eAAa,OAAO,SAAS,OAAM,MAAM,+DAA+D;AAAE,MAAAA,KAAE,SAAS,SAAS,SAAS,EAAE,UAAU,GAAE,SAAS,SAAS,SAAS,EAAE,YAAY,GAAG,CAAC,IAAE;AAAA,IAAG;AAAC,QAAG,KAAK,IAAEA,IAAED,GAAE,QAAQ,UAAQI,MAAGH,KAAE,EAAE,OAAO,KAAKD,GAAE,OAAO,CAAC,GAAG,KAAK,GAAE,CAACI,GAAE,MAAKA,KAAEH,GAAE,KAAK,GAAE;AAAC,MAAAG,KAAEA,GAAE;AAAM,UAAIG,KAAEP,GAAE,QAAQI,EAAC,EAAE;AAAQ,iBAASG,OAAI,KAAK,EAAEH,EAAC,IAAE,cAAY,OAAOG,KAAEA,GAAE,IAAEA;AAAA,IAAE;AAAA,EAAC;AAAC,WAAS,GAAGP,IAAE;AAAC,QAAIC,IAAEG,IAAEG,IAAEF,IAAEH,IAAEI,IAAEa,IAAEC,IAAE2D,IAAEa,IAAEC;AAAE,WAAO,EAAG,SAASC,IAAE;AAAC,cAAOA,GAAE,GAAE;AAAA,QAAC,KAAK;AAAE,iBAAO9F,GAAE,KAAGC,KAAE,WAASD,GAAE,EAAE,QAAM,CAAC,IAAE,cAAY,OAAOA,GAAE,EAAE,QAAMA,GAAE,EAAE,MAAMA,GAAE,CAAC,IAAEA,GAAE,EAAE,OAAM,EAAE8F,IAAE,EAAG,SAAS9F,IAAE;AAAC,oBAAOA,GAAE,GAAE;AAAA,cAAC,KAAK;AAAE,uBAAOA,GAAE,IAAE,GAAE,EAAEA,IAAE,YAAY,YAAY,EAAE,GAAE,CAAC;AAAA,cAAE,KAAK;AAAE,gBAAAA,GAAE,IAAE,GAAEA,GAAE,IAAE;AAAE;AAAA,cAAM,KAAK;AAAE,uBAAOA,GAAE,IAAE,GAAEA,GAAE,IAAE,MAAKA,GAAE,OAAO,KAAE;AAAA,cAAE,KAAK;AAAE,uBAAOA,GAAE,OAAO,IAAE;AAAA,YAAC;AAAA,UAAC,CAAE,GAAE,CAAC,KAAG8F,GAAE,OAAO;AAAA,QAAE,KAAK;AAAE,cAAG1F,KAAE0F,GAAE,GAAE,YAAU,OAAO,OAAO,QAAO,GAAG,gCAA+B,EAAC,YAAW9F,GAAE,WAAU,CAAC,GAAE,GAAG,wCAAuC,EAAC,YAAWA,GAAE,WAAU,CAAC,GAAEM,KAAEL,GAAE,OAAQ,SAASD,IAAE;AAAC,mBAAO,WAASA,GAAE;AAAA,UAAI,CAAE,GAAEmB,KAAElB,GAAE,OAAQ,SAASD,IAAE;AAAC,mBAAO,WAASA,GAAE;AAAA,UAAI,CAAE,GAAEoB,KAAE,QAAQ,IAAId,GAAE,IAAK,SAASL,IAAE;AAAC,gBAAIG,KAAE,GAAGJ,IAAEC,GAAE,GAAG;AAAE,gBAAG,WAASA,GAAE,MAAK;AAAC,kBAAIM,KAAEN,GAAE;AAAK,cAAAG,KAAEA,GAAE,KAAM,SAASH,IAAE;AAAC,uBAAOD,GAAE,aAAaO,IAAEN,EAAC,GAAE,QAAQ,QAAQA,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAC,mBAAOG;AAAA,UAAC,CAAE,CAAC,GAAE2E,KAAE,QAAQ,IAAI5D,GAAE,IAAK,SAASlB,IAAE;AAAC,mBAAO,WAASA,GAAE,QAAMA,GAAE,QAAMG,MAAG,CAACH,GAAE,QAAM,CAACG,KAAE,SAASJ,IAAE;AAAC,kBAAIC,KAAE,SAAS,cAAc,QAAQ;AAAE,qBAAOA,GAAE,aAAa,OAAMD,EAAC,GAAEC,GAAE,aAAa,eAAc,WAAW,GAAE,IAAI,QAAS,SAASD,IAAE;AAAC,gBAAAC,GAAE,iBAAiB,QAAQ,WAAU;AAAC,kBAAAD,GAAE;AAAA,gBAAC,GAAG,KAAE,GAAEC,GAAE,iBAAiB,SAAS,WAAU;AAAC,kBAAAD,GAAE;AAAA,gBAAC,GAAG,KAAE,GAAE,SAAS,KAAK,YAAYC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,EAAED,GAAE,WAAWC,GAAE,KAAID,GAAE,CAAC,CAAC,IAAE,QAAQ,QAAQ;AAAA,UAAC,CAAE,CAAC,EAAE,KAAM,WAAU;AAAC,gBAAIC,IAAEG,IAAEG;AAAE,mBAAO,EAAG,SAASF,IAAE;AAAC,kBAAG,KAAGA,GAAE,EAAE,QAAOJ,KAAE,OAAO,8BAA6BG,KAAE,OAAO,sCAAqCG,KAAEP,IAAE,EAAEK,IAAEJ,GAAEG,EAAC,GAAE,CAAC;AAAE,cAAAG,GAAE,IAAEF,GAAE,GAAEA,GAAE,IAAE;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE,GAAEuF,KAAE,EAAG,SAAS3F,IAAE;AAAC,mBAAOD,GAAE,EAAE,SAAOA,GAAE,EAAE,MAAM,MAAIC,KAAE,EAAEA,IAAE,GAAGD,IAAEA,GAAE,EAAE,MAAM,GAAG,GAAE,CAAC,KAAGC,GAAE,IAAE,GAAEA,KAAE,SAAQA;AAAA,UAAC,CAAE,GAAE,EAAE6F,IAAE,QAAQ,IAAI,CAACf,IAAE3D,IAAEwE,EAAC,CAAC,GAAE,CAAC;AAAE,cAAG,cAAY,OAAO,cAAc,OAAM,MAAM,+DAA+D;AAAE,iBAAOrF,KAAEN,GAAE,OAAQ,SAASD,IAAE;AAAC,mBAAO,WAASA,GAAE,QAAMA,GAAE,QAAMI,MAAG,CAACJ,GAAE,QAAM,CAACI;AAAA,UAAC,CAAE,EAAE,IAAK,SAASH,IAAE;AAAC,mBAAOD,GAAE,WAAWC,GAAE,KAAID,GAAE,CAAC;AAAA,UAAC,CAAE,GAAE,cAAc,MAAM,MAAK,EAAEO,EAAC,CAAC,GAAEF,KAAEL,IAAE,EAAE8F,IAAE,6BAA6B,MAAM,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,UAAAzF,GAAE,IAAEyF,GAAE,GAAE9F,GAAE,IAAE,IAAI,gBAAgB,GAAE,CAAC,GAAEA,GAAE,EAAE,SAAOA,GAAE,GAAEE,KAAEF,GAAE,EAAE,GAAG,cAAcA,GAAE,GAAE,EAAC,WAAU,OAAG,OAAM,OAAG,IAAG,eAAa,OAAO,yBAAuB,IAAE,EAAC,CAAC,GAAEA,GAAE,EAAE,GAAG,mBAAmBE,EAAC,GAAE4F,GAAE,IAAE;AAAE;AAAA,QAAM,KAAK;AAAE,cAAG9F,GAAE,IAAE,SAAS,cAAc,QAAQ,GAAE,EAAE6F,KAAE7F,GAAE,EAAE,WAAW,UAAS,CAAC,CAAC,MAAI,EAAE6F,KAAE7F,GAAE,EAAE,WAAW,SAAQ,CAAC,CAAC,GAAG,QAAO,MAAM,iEAAiE,GAAE8F,GAAE,OAAO;AAAE,UAAA9F,GAAE,IAAE6F,IAAE7F,GAAE,EAAE,SAAOA,GAAE,GAAEA,GAAE,EAAE,cAAcA,GAAE,GAAE,MAAG,MAAG,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,UAAAA,GAAE,IAAE,IAAIA,GAAE,EAAE,gBAAaA,GAAE,IAAE,OAAG8F,GAAE,IAAE;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,GAAG9F,IAAEC,IAAE;AAAC,QAAIG,IAAEG;AAAE,WAAO,EAAG,SAASF,IAAE;AAAC,aAAOJ,MAAKD,GAAE,IAAEK,GAAE,OAAOL,GAAE,EAAEC,EAAC,CAAC,KAAGG,KAAEJ,GAAE,WAAWC,IAAE,EAAE,GAAEM,KAAE,MAAMH,EAAC,EAAE,KAAM,SAASJ,IAAE;AAAC,eAAOA,GAAE,YAAY;AAAA,MAAC,CAAE,GAAEA,GAAE,EAAEC,EAAC,IAAEM,IAAEF,GAAE,OAAOE,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAC,WAAS,GAAGP,IAAEC,IAAEG,IAAE;AAAC,QAAIG,IAAEF,IAAEH,IAAEC,IAAEgB,IAAEC,IAAE2D,IAAEa,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,WAAO,EAAG,SAASC,IAAE;AAAC,cAAOA,GAAE,GAAE;AAAA,QAAC,KAAK;AAAE,cAAG,CAAC/F,GAAE,QAAO+F,GAAE,OAAOlG,EAAC;AAAE,eAAIM,KAAE,CAAC,GAAEF,KAAE,GAAEH,KAAE,EAAE,OAAO,KAAKE,EAAC,CAAC,GAAED,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,EAAE,CAAAiB,KAAEhB,GAAE,OAAM,YAAU,QAAOiB,KAAEhB,GAAEe,EAAC,MAAI,cAAYC,GAAE,QAAM,WAASnB,GAAEmB,GAAE,MAAM,KAAG,EAAEf;AAAE,cAAEA,OAAIL,GAAE,IAAE,QAAI+E,KAAE,EAAE,OAAO,KAAK3E,EAAC,CAAC,GAAED,KAAE4E,GAAE,KAAK;AAAA,QAAE,KAAK;AAAE,cAAG5E,GAAE,MAAK;AAAC,YAAAgG,GAAE,IAAE;AAAE;AAAA,UAAK;AAAC,cAAGP,KAAEzF,GAAE,OAAM,YAAU,QAAO0F,KAAEzF,GAAEwF,EAAC,GAAG,QAAOK,KAAE1F,IAAE2F,KAAEN,IAAE,EAAEO,IAAE,SAASnG,IAAEC,IAAEG,IAAE;AAAC,gBAAIG;AAAE,mBAAO,EAAG,SAASF,IAAE;AAAC,qBAAM,YAAU,OAAOD,MAAGA,cAAa,cAAYA,cAAaJ,GAAE,EAAE,gBAAcK,GAAE,OAAOD,EAAC,IAAEA,cAAaJ,GAAE,EAAE,qBAAmBO,KAAEP,GAAE,EAAEC,EAAC,OAAKM,KAAE,IAAI,GAAGP,GAAE,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAEC,EAAC,IAAEM,KAAGF,GAAE,OAAO,GAAGE,IAAEH,IAAEJ,GAAE,CAAC,CAAC,KAAGK,GAAE,OAAO,MAAM;AAAA,YAAC,CAAE;AAAA,UAAC,EAAEL,IAAE4F,IAAE3F,GAAE4F,EAAC,CAAC,GAAE,EAAE;AAAE,cAAGC,KAAE7F,GAAE4F,GAAE,MAAM,GAAE,qBAAmBA,GAAE,MAAK;AAAC,gBAAGC,IAAE;AAAC,uBAAQM,KAAEN,GAAE,YAAY,GAAEO,KAAEP,GAAE,iBAAiB,GAAEQ,KAAER,GAAE,uBAAuB,GAAES,KAAE,CAAC,GAAE/F,KAAE,GAAEA,KAAE4F,GAAE,KAAK,GAAE,EAAE5F,IAAE;AAAC,oBAAIC,KAAE,GAAG2F,GAAE,IAAI5F,EAAC,GAAE,IAAG,EAAE;AAAE,gBAAAC,KAAE,EAAC,aAAY,EAAC,SAAQiD,IAAGjD,IAAE,CAAC,GAAE,SAAQiD,IAAGjD,IAAE,CAAC,GAAE,QAAOiD,IAAGjD,IAAE,CAAC,GAAE,OAAMiD,IAAGjD,IAAE,CAAC,GAAE,UAASiD,IAAGjD,IAAE,GAAE,CAAC,GAAE,QAAOoD,IAAGpD,IAAE,CAAC,EAAC,GAAE,WAAUkD,IAAG,GAAG0C,GAAE,IAAI7F,EAAC,GAAE,IAAG,EAAE,GAAE,IAAG,CAAC,EAAE,IAAI,EAAE,GAAE,GAAE,GAAG,GAAG8F,GAAE,IAAI9F,EAAC,GAAE,IAAG,EAAE,CAAC,EAAC,GAAE+F,GAAE,KAAK9F,EAAC;AAAA,cAAC;AAAC,cAAA2F,KAAEG;AAAA,YAAC,MAAM,CAAAH,KAAE,CAAC;AAAE,YAAA7F,GAAEqF,EAAC,IAAEQ,IAAED,GAAE,IAAE;AAAE;AAAA,UAAK;AAAC,cAAG,iBAAeN,GAAE,MAAK;AAAC,gBAAGC,IAAE;AAAC,mBAAIM,KAAE,MAAMN,GAAE,KAAK,CAAC,GAAEO,KAAE,GAAEA,KAAEP,GAAE,KAAK,GAAEO,KAAI,CAAAD,GAAEC,EAAC,IAAEP,GAAE,IAAIO,EAAC;AAAE,cAAAP,GAAE,OAAO;AAAA,YAAC,MAAM,CAAAM,KAAE,CAAC;AAAE,YAAA7F,GAAEqF,EAAC,IAAEQ,IAAED,GAAE,IAAE;AAAE;AAAA,UAAK;AAAC,cAAG,WAASL,IAAE;AAAC,YAAAK,GAAE,IAAE;AAAE;AAAA,UAAK;AAAC,cAAG,iBAAeN,GAAE,MAAK;AAAC,YAAAtF,GAAEqF,EAAC,IAAEE,IAAEK,GAAE,IAAE;AAAE;AAAA,UAAK;AAAC,cAAG,YAAUN,GAAE,MAAK;AAAC,YAAAtF,GAAEqF,EAAC,IAAEE,IAAEK,GAAE,IAAE;AAAE;AAAA,UAAK;AAAC,cAAG,cAAYN,GAAE,KAAK,OAAM,MAAM,kCAAgCA,GAAE,OAAK,GAAG;AAAE,kBAAOE,KAAE/F,GAAE,EAAE4F,EAAC,OAAKG,KAAE,IAAI,GAAG/F,GAAE,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE4F,EAAC,IAAEG,KAAG,EAAEI,IAAE,GAAGJ,IAAED,IAAE9F,GAAE,CAAC,GAAE,EAAE;AAAA,QAAE,KAAK;AAAG,UAAAgG,KAAEG,GAAE,GAAE5F,GAAEqF,EAAC,IAAEI;AAAA,QAAE,KAAK;AAAE,UAAAH,GAAE,aAAWtF,GAAEqF,EAAC,MAAIrF,GAAEqF,EAAC,IAAEC,GAAE,UAAUtF,GAAEqF,EAAC,CAAC,IAAGO,GAAE,IAAE;AAAE;AAAA,QAAM,KAAK;AAAG,UAAAF,GAAEC,EAAC,IAAEC,GAAE;AAAA,QAAE,KAAK;AAAE,UAAAhG,KAAE4E,GAAE,KAAK,GAAEoB,GAAE,IAAE;AAAE;AAAA,QAAM,KAAK;AAAE,iBAAOA,GAAE,OAAO5F,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,GAAGP,IAAEC,IAAE;AAAC,aAAQG,KAAEH,GAAE,QAAM,KAAIM,KAAE,CAAC,EAAE,OAAO,EAAEN,GAAE,KAAK,CAAC,GAAEI,KAAE,IAAIL,GAAE,EAAE,cAAWE,KAAE,EAAED,GAAE,KAAK,GAAEkB,KAAEjB,GAAE,KAAK,GAAE,CAACiB,GAAE,MAAKA,KAAEjB,GAAE,KAAK,EAAE,CAAAG,GAAE,UAAUc,GAAE,KAAK;AAAE,IAAAjB,KAAEF,GAAE,EAAE,eAAe,UAAU,EAAC,WAAU,SAASK,IAAE;AAAC,eAAQH,KAAE,CAAC,GAAEI,KAAE,GAAEA,KAAEL,GAAE,MAAM,QAAO,EAAEK,GAAE,CAAAJ,GAAEK,GAAED,EAAC,CAAC,IAAED,GAAE,IAAIC,EAAC;AAAE,UAAIH,KAAEH,GAAE,UAAUI,EAAC;AAAE,MAAAD,OAAIH,GAAE,IAAE,GAAGA,IAAEE,IAAED,GAAE,IAAI,EAAE,KAAM,SAASG,IAAE;AAAC,QAAAA,KAAED,GAAEC,EAAC;AAAE,iBAAQC,KAAE,GAAEA,KAAEJ,GAAE,MAAM,QAAO,EAAEI,IAAE;AAAC,cAAIC,KAAEJ,GAAEK,GAAEF,EAAC,CAAC;AAAE,sBAAU,OAAOC,MAAGA,GAAE,kBAAgBA,GAAE,eAAe,QAAQ,KAAGA,GAAE,OAAO;AAAA,QAAC;AAAC,QAAAF,OAAIJ,GAAE,IAAEI;AAAA,MAAE,CAAE;AAAA,IAAE,EAAC,CAAC,GAAEJ,GAAE,EAAE,oBAAoBK,IAAEH,EAAC,GAAEG,GAAE,OAAO;AAAA,EAAC;AAAC,WAAS,GAAGL,IAAE;AAAC,QAAIC,KAAE;AAAK,IAAAD,KAAEA,MAAG,CAAC;AAAE,QAAII,KAAE,EAAC,KAAI,gCAA+B,GAAEG,KAAE,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,iCAAgC,gBAAe,4FAA2F,WAAU,mBAAkB,EAAC;AAAE,SAAK,IAAE,IAAI,GAAG,EAAC,YAAWP,GAAE,YAAW,OAAM,CAAC,EAAC,MAAK,MAAG,KAAI,gCAA+B,GAAE,EAAC,MAAK,MAAG,KAAI,oCAAmC,GAAE,EAAC,MAAK,MAAG,KAAI,2CAA0C,GAAE,EAAC,MAAK,OAAG,KAAI,sCAAqC,CAAC,GAAE,OAAMI,IAAE,WAAU,CAAC,EAAC,OAAM,CAAC,cAAa,mBAAmB,GAAE,MAAK,EAAC,OAAM,qBAAoB,YAAW,EAAC,MAAK,kBAAiB,QAAO,aAAY,EAAC,EAAC,CAAC,GAAE,QAAO,EAAC,OAAM,EAAC,MAAK,SAAQ,QAAO,mBAAkB,EAAC,GAAE,SAAQ,EAAC,iBAAgB,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,uBAAsB,WAAU,oBAAmB,GAAE,SAAQ,YAAU,OAAO,UAAQ,WAAS,OAAO,cAAY,kEAAkE,MAAM,GAAG,EAAE,SAAS,UAAU,QAAQ,KAAG,UAAU,UAAU,SAAS,KAAK,KAAG,gBAAe,UAAS,GAAE,YAAW,EAAC,MAAK,GAAE,iBAAgB,EAAC,gBAAe,sBAAqB,iBAAgB,GAAE,WAAU,kBAAiB,EAAC,GAAE,OAAM,EAAC,MAAK,GAAE,UAAS,SAASJ,IAAE;AAAC,UAAIK,IAAEH,IAAEC,IAAEgB,IAAEC;AAAE,aAAO,EAAG,SAAS2D,IAAE;AAAC,gBAAOA,GAAE,GAAE;AAAA,UAAC,KAAK;AAAE,YAAA1E,KAAE,EAAE,YAAUL,KAAE,CAAC,mCAAmC,IAAE,CAAC,yCAAyC,CAAC,GAAEE,KAAEG,GAAE,KAAK;AAAA,UAAE,KAAK;AAAE,gBAAGH,GAAE,MAAK;AAAC,cAAA6E,GAAE,IAAE;AAAE;AAAA,YAAK;AAAC,mBAAO5E,KAAED,GAAE,OAAMiB,KAAE,kDAAgDhB,IAAE,EAAE4E,IAAE,GAAG9E,GAAE,GAAEE,EAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,YAAAiB,KAAE2D,GAAE,GAAE9E,GAAE,EAAE,aAAakB,IAAEC,EAAC,GAAElB,KAAEG,GAAE,KAAK,GAAE0E,GAAE,IAAE;AAAE;AAAA,UAAM,KAAK;AAAE,mBAAO3E,GAAE,MAAI,YAAUJ,KAAE,kCAAgC,gCAA+BO,GAAE,gBAAgB,iBAAe,YAAUP,KAAE,6FAA2F,0FAAyF+E,GAAE,OAAO,IAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,EAAC,GAAE,wBAAuBxE,GAAC,EAAC,CAAC;AAAA,EAAC;AAAC,GAACP,KAAE,GAAG,WAAW,QAAM,WAAU;AAAC,WAAO,KAAK,KAAG,KAAK,EAAE,OAAO,GAAE,QAAQ,QAAQ;AAAA,EAAC,GAAEA,GAAE,QAAM,WAAU;AAAC,QAAIA,KAAE;AAAK,WAAO,EAAG,SAASC,IAAE;AAAC,MAAAD,GAAE,MAAIA,GAAE,EAAE,MAAM,GAAEA,GAAE,IAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,IAAGC,GAAE,IAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAED,GAAE,aAAW,SAASA,IAAEC,IAAE;AAAC,QAAIG,KAAE;AAAK,QAAGH,KAAEA,MAAG,KAAK,EAAE,SAAQ;AAAC,eAAQM,KAAE,CAAC,GAAEF,KAAE,CAAC,GAAEH,KAAE,CAAC,GAAEC,KAAE,EAAE,OAAO,KAAKH,EAAC,CAAC,GAAEmB,KAAEhB,GAAE,KAAK,GAAE,CAACgB,GAAE,MAAKjB,KAAE,EAAC,GAAEA,GAAE,GAAE,GAAEA,GAAE,EAAC,GAAEiB,KAAEhB,GAAE,KAAK,GAAE;AAAC,YAAIiB,KAAED,GAAE;AAAM,QAAAC,MAAK,KAAK,KAAG,KAAK,EAAEA,EAAC,MAAIpB,GAAEoB,EAAC,MAAI,KAAK,EAAEA,EAAC,IAAEpB,GAAEoB,EAAC,GAAE,YAAUD,KAAElB,GAAEmB,EAAC,OAAKD,GAAE,aAAWjB,GAAE,IAAEiB,GAAE,UAASjB,GAAE,IAAEF,GAAEoB,EAAC,GAAEb,GAAE,KAAK,yBAASP,IAAE;AAAC,iBAAO,WAAU;AAAC,mBAAO,EAAG,SAASC,IAAE;AAAC,kBAAG,KAAGA,GAAE,EAAE,QAAO,EAAEA,IAAED,GAAE,EAAEA,GAAE,CAAC,GAAE,CAAC;AAAE,uBAAKC,GAAE,MAAIG,GAAE,IAAE,OAAIH,GAAE,IAAE;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,QAAC,EAAEC,EAAC,CAAC,IAAGiB,GAAE,oBAAkBC,KAAE,EAAC,aAAY,MAAID,GAAE,OAAKnB,GAAEoB,EAAC,IAAE,GAAE,cAAa,MAAID,GAAE,QAAMnB,GAAEoB,EAAC,GAAE,aAAY,MAAID,GAAE,OAAKnB,GAAEoB,EAAC,IAAE,GAAE,GAAED,KAAE,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAE,EAAC,gBAAe,IAAG,iBAAgB,EAAC,CAAC,GAAEA,GAAE,eAAe,GAAEC,EAAC,GAAEf,GAAE,KAAKc,EAAC;AAAA,MAAI;AAAC,YAAIZ,GAAE,UAAQ,MAAIF,GAAE,WAAS,KAAK,IAAE,MAAG,KAAK,KAAG,WAAS,KAAK,IAAE,CAAC,IAAE,KAAK,GAAG,OAAOA,EAAC,GAAE,KAAK,KAAG,WAAS,KAAK,IAAE,CAAC,IAAE,KAAK,GAAG,OAAOE,EAAC;AAAA,IAAE;AAAA,EAAC,GAAEP,GAAE,aAAW,WAAU;AAAC,QAAIA,KAAE;AAAK,WAAO,EAAG,SAASC,IAAE;AAAC,aAAO,KAAGA,GAAE,IAAE,EAAEA,IAAE,GAAGD,EAAC,GAAE,CAAC,IAAE,KAAGC,GAAE,IAAE,EAAEA,IAAE,SAASD,IAAE;AAAC,YAAIC,IAAEG,IAAEG,IAAEF,IAAEH,IAAEC,IAAEgB,IAAEC;AAAE,eAAO,EAAG,SAAS2D,IAAE;AAAC,cAAG,KAAGA,GAAE,EAAE,QAAO/E,GAAE,EAAE,SAAOA,GAAE,EAAE,MAAM,OAAKA,GAAE,MAAIA,GAAE,EAAE,MAAM,MAAI+E,GAAE,OAAO,KAAG/E,GAAE,IAAE,MAAGA,GAAE,EAAE,SAAOA,GAAE,EAAE,MAAM,OAAKA,GAAE,IAAEA,GAAE,EAAE,MAAM,KAAI,EAAE+E,IAAE,GAAG/E,IAAEA,GAAE,EAAE,MAAM,GAAG,GAAE,CAAC,KAAG,MAAK+E,GAAE,IAAE;AAAI,eAAI,KAAGA,GAAE,MAAI9E,KAAE8E,GAAE,GAAE/E,GAAE,EAAE,UAAUC,EAAC,IAAGG,KAAE,EAAE,OAAO,KAAKJ,GAAE,CAAC,CAAC,GAAEO,KAAEH,GAAE,KAAK,GAAE,CAACG,GAAE,MAAKA,KAAEH,GAAE,KAAK,EAAE,CAAAC,KAAEE,GAAE,OAAMP,GAAE,EAAE,aAAaK,IAAEL,GAAE,EAAEK,EAAC,CAAC;AAAE,cAAGL,GAAE,IAAE,CAAC,GAAEA,GAAE,EAAE,UAAU,MAAIE,KAAE,EAAEF,GAAE,EAAE,SAAS,GAAEG,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,EAAE,CAAAiB,KAAEhB,GAAE,OAAM,GAAGH,IAAEmB,EAAC;AAAE,UAAAC,KAAEpB,GAAE,GAAEA,GAAE,IAAE,CAAC,GAAEA,GAAE,WAAWoB,EAAC,GAAE2D,GAAE,IAAE;AAAA,QAAC,CAAE;AAAA,MAAC,EAAE/E,EAAC,GAAE,CAAC,IAAE,EAAEC,IAAE,SAASD,IAAE;AAAC,YAAIC,IAAEG,IAAEG,IAAEF,IAAEH,IAAEC;AAAE,eAAO,EAAG,SAASgB,IAAE;AAAC,kBAAOA,GAAE,GAAE;AAAA,YAAC,KAAK;AAAE,kBAAG,CAACnB,GAAE,EAAE,QAAOmB,GAAE,OAAO;AAAE,kBAAG,CAACnB,GAAE,GAAE;AAAC,gBAAAmB,GAAE,IAAE;AAAE;AAAA,cAAK;AAAC,cAAAlB,KAAE,EAAED,GAAE,CAAC,GAAEI,KAAEH,GAAE,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAGG,GAAE,MAAK;AAAC,gBAAAe,GAAE,IAAE;AAAE;AAAA,cAAK;AAAC,qBAAO,EAAEA,KAAG,GAAEf,GAAE,OAAO,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,cAAAA,KAAEH,GAAE,KAAK,GAAEkB,GAAE,IAAE;AAAE;AAAA,YAAM,KAAK;AAAE,cAAAnB,GAAE,IAAE;AAAA,YAAO,KAAK;AAAE,kBAAGA,GAAE,GAAE;AAAC,qBAAIO,KAAE,IAAIP,GAAE,EAAE,gCAA6BK,KAAE,EAAEL,GAAE,CAAC,GAAEE,KAAEG,GAAE,KAAK,GAAE,CAACH,GAAE,MAAKA,KAAEG,GAAE,KAAK,EAAE,CAAAF,KAAED,GAAE,OAAMK,GAAE,UAAUJ,EAAC;AAAE,gBAAAH,GAAE,EAAE,cAAcO,EAAC,GAAEA,GAAE,OAAO,GAAEP,GAAE,IAAE;AAAA,cAAM;AAAC,cAAAA,GAAE,IAAE,OAAGmB,GAAE,IAAE;AAAA,UAAC;AAAA,QAAC,CAAE;AAAA,MAAC,EAAEnB,EAAC,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,eAAa,SAASA,IAAEC,IAAE;AAAC,SAAK,IAAE,KAAK,EAAE,aAAaD,IAAEC,EAAC,IAAE,KAAK,EAAED,EAAC,IAAEC;AAAA,EAAC,GAAED,GAAE,uBAAqB,WAAU;AAAC,SAAK,IAAE,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,OAAK,SAASA,IAAEC,IAAE;AAAC,QAAIG,IAAEG,IAAEF,IAAEH,IAAEC,IAAEgB,IAAEC,IAAE2D,IAAEa,IAAEC,KAAE;AAAK,WAAO,EAAG,SAASC,IAAE;AAAC,cAAOA,GAAE,GAAE;AAAA,QAAC,KAAK;AAAE,iBAAOD,GAAE,EAAE,UAAQzF,KAAE,OAAK,QAAMH,KAAE,YAAY,IAAI,IAAEA,KAAG,EAAE6F,IAAED,GAAE,GAAE,CAAC,KAAGC,GAAE,OAAO;AAAA,QAAE,KAAK;AAAE,iBAAO,EAAEA,IAAED,GAAE,WAAW,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,eAAItF,KAAE,IAAIsF,GAAE,EAAE,kBAAexF,KAAE,EAAE,OAAO,KAAKL,EAAC,CAAC,GAAEE,KAAEG,GAAE,KAAK,GAAE,CAACH,GAAE,MAAKA,KAAEG,GAAE,KAAK,EAAE,KAAGF,KAAED,GAAE,OAAMiB,KAAE0E,GAAE,EAAE,OAAO1F,EAAC,GAAE;AAAC,eAAE;AAAC,kBAAI4F,KAAE/F,GAAEG,EAAC;AAAE,sBAAOgB,GAAE,MAAK;AAAA,gBAAC,KAAI;AAAQ,sBAAI6E,KAAEH,GAAE,EAAE1E,GAAE,MAAM;AAAE,sBAAG6E,OAAIA,KAAE,IAAI,GAAGH,GAAE,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE1E,GAAE,MAAM,IAAE6E,KAAG,MAAIA,GAAE,MAAIA,GAAE,IAAEA,GAAE,EAAE,cAAc,IAAG,eAAa,OAAO,oBAAkBD,cAAa,iBAAiB,KAAIE,KAAEF,GAAE,YAAWG,KAAEH,GAAE;AAAA,sBAAgB,gBAAa,OAAO,oBAAkBA,cAAa,oBAAkBE,KAAEF,GAAE,cAAaG,KAAEH,GAAE,kBAAgBE,KAAEF,GAAE,OAAMG,KAAEH,GAAE;AAAQ,kBAAAG,KAAE,EAAC,QAAOF,GAAE,GAAE,OAAMC,IAAE,QAAOC,GAAC,IAAGD,KAAED,GAAE,GAAG,OAAO,QAAME,GAAE,OAAMD,GAAE,OAAO,SAAOC,GAAE,QAAOD,GAAE,cAAcA,GAAE,QAAQ,GAAED,GAAE,EAAE,cAAcA,GAAE,CAAC,GAAEC,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcF,EAAC,GAAEC,GAAE,EAAE,cAAc,CAAC,GAAEA,KAAEE;AAAE,wBAAM;AAAA,gBAAE,KAAI;AAAa,wBAAKF,KAAEH,GAAE,EAAE1E,GAAE,MAAM,OAAK6E,KAAE,IAAI,GAAGH,GAAE,CAAC,GAAEA,GAAE,EAAE1E,GAAE,MAAM,IAAE6E,KAAGA,GAAE,SAAOA,GAAE,OAAK,IAAIA,GAAE,EAAE,sBAAmBA,GAAE,KAAK,MAAMD,GAAE,MAAM,GAAEG,KAAE,GAAEA,KAAEH,GAAE,QAAO,EAAEG,IAAE;AAAC,oBAAAD,KAAEF,GAAEG,EAAC;AAAE,wBAAIC,KAAEH,GAAE,MAAKI,KAAED,GAAE,gBAAeE,KAAEH,IAAEM,KAAEP,GAAE,aAAYK,KAAE,IAAI;AAAG,wBAAG9C,IAAG8C,IAAE,GAAEE,GAAE,OAAO,GAAEhD,IAAG8C,IAAE,GAAEE,GAAE,OAAO,GAAEhD,IAAG8C,IAAE,GAAEE,GAAE,MAAM,GAAEhD,IAAG8C,IAAE,GAAEE,GAAE,KAAK,GAAEhD,IAAG8C,IAAE,GAAEE,GAAE,QAAQ,GAAEhD,IAAG8C,IAAE,GAAEE,GAAE,MAAM,GAAEA,KAAE,GAAGF,IAAE,EAAE,GAAEF,GAAE,KAAKD,IAAEE,IAAEG,EAAC,GAAEP,GAAE,UAAU,MAAIE,KAAE,GAAEA,KAAEF,GAAE,UAAU,QAAO,EAAEE,IAAE;AAAC,0BAAII,KAAE,CAAC,EAAED,KAAEL,GAAE,UAAUE,EAAC,GAAG;AAAW,sBAAAE,MAAGD,KAAEJ,GAAE,MAAM,uBAAsBQ,KAAEN,IAAEI,KAAE,OAAO,OAAO,OAAO,OAAO,CAAC,GAAEA,EAAC,GAAE,EAAC,YAAWC,KAAED,GAAE,aAAW,EAAC,CAAC,GAAE9C,IAAG+C,KAAE,IAAI,MAAG,GAAED,GAAE,CAAC,GAAE9C,IAAG+C,IAAE,GAAED,GAAE,CAAC,GAAE9C,IAAG+C,IAAE,GAAED,GAAE,CAAC,GAAEA,GAAE,cAAY9C,IAAG+C,IAAE,GAAED,GAAE,UAAU,GAAEA,KAAE,GAAGC,IAAE,EAAE,GAAEF,GAAE,KAAKD,IAAEI,IAAEF,EAAC;AAAA,oBAAC;AAAC,wBAAGL,GAAE,EAAE,MAAIE,KAAE,GAAEA,KAAEF,GAAE,EAAE,QAAO,EAAEE,GAAE,CAAAE,MAAGD,KAAEJ,GAAE,MAAM,mBAAkBQ,KAAEN,IAAEI,KAAEL,GAAE,EAAEE,EAAC,GAAE3C,IAAG+C,KAAE,IAAI,MAAG,GAAED,GAAE,EAAE,GAAEA,GAAE,SAAO9C,IAAG+C,IAAE,GAAED,GAAE,KAAK,GAAEA,GAAE,SAAO9C,IAAG+C,IAAE,GAAED,GAAE,KAAK,GAAEA,GAAE,eAAa9C,IAAG+C,IAAE,GAAED,GAAE,WAAW,GAAEA,KAAE,GAAGC,IAAE,EAAE,GAAEF,GAAE,KAAKD,IAAEI,IAAEF,EAAC;AAAA,kBAAC;AAAC,kBAAAN,KAAEA,GAAE;AAAK,wBAAM;AAAA,gBAAE;AAAQ,kBAAAA,KAAE,CAAC;AAAA,cAAC;AAAA,YAAC;AAAC,oBAAO5E,KAAE4E,IAAEjB,KAAE5D,GAAE,QAAOA,GAAE,MAAK;AAAA,cAAC,KAAI;AAAQ,gBAAAZ,GAAE,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAEa,EAAC,GAAE,EAAC,QAAO2D,IAAE,WAAU3E,GAAC,CAAC,CAAC;AAAE;AAAA,cAAM,KAAI;AAAa,iBAACwF,KAAExE,IAAG,SAAO2D,IAAEa,GAAE,YAAUxF,IAAEG,GAAE,kBAAkBqF,EAAC;AAAE;AAAA,cAAM;AAAQ,sBAAM,MAAM,iCAA+BzE,GAAE,OAAK,GAAG;AAAA,YAAC;AAAA,UAAC;AAAC,iBAAO0E,GAAE,EAAE,KAAKtF,EAAC,GAAE,EAAEuF,IAAED,GAAE,GAAE,CAAC;AAAA,QAAE,KAAK;AAAE,UAAAtF,GAAE,OAAO,GAAEuF,GAAE,IAAE;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE9F,GAAE,YAAU,SAASA,IAAEC,IAAE;AAAC,SAAK,UAAUA,MAAG,GAAG,IAAED;AAAA,EAAC,GAAEQ,GAAE,YAAW,EAAE,GAAEA,GAAE,cAAa,EAAC,MAAK,GAAE,QAAO,GAAE,IAAG,GAAE,GAAE,QAAO,GAAE,UAAS,GAAE,SAAQ,CAAC,IAAGR,KAAE,GAAG,WAAW,QAAM,WAAU;AAAC,WAAO,KAAK,EAAE,MAAM,GAAE,QAAQ,QAAQ;AAAA,EAAC,GAAEA,GAAE,YAAU,SAASA,IAAE;AAAC,SAAK,EAAE,UAAUA,EAAC;AAAA,EAAC,GAAEA,GAAE,aAAW,WAAU;AAAC,QAAIA,KAAE;AAAK,WAAO,EAAG,SAASC,IAAE;AAAC,aAAO,EAAEA,IAAED,GAAE,EAAE,WAAW,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,QAAM,WAAU;AAAC,SAAK,EAAE,MAAM;AAAA,EAAC,GAAEA,GAAE,OAAK,SAASA,IAAE;AAAC,QAAIC,KAAE;AAAK,WAAO,EAAG,SAASG,IAAE;AAAC,aAAO,EAAEA,IAAEH,GAAE,EAAE,KAAKD,EAAC,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,aAAW,SAASA,IAAE;AAAC,SAAK,EAAE,WAAWA,EAAC;AAAA,EAAC,GAAEQ,GAAE,iBAAgB,EAAE,GAAEA,GAAE,sBAAqB,EAAE,GAAEA,GAAE,0BAAyB,EAAE,GAAEA,GAAE,8BAA6B,EAAE,GAAEA,GAAE,2BAA0B,EAAE,GAAEA,GAAE,+BAA8B,EAAE,GAAEA,GAAE,2BAA0B,EAAE,GAAEA,GAAE,0BAAyB,EAAE,GAAEA,GAAE,6BAA4B,CAAC,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAgB;AAAC,GAAG,KAAK,CAAC;AAAE,IAAI,IAAE,WAAU;AAAC,SAAO,IAAE,OAAO,UAAQ,SAASR,IAAE;AAAC,aAAQ,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,UAAQ,KAAK,IAAE,UAAU,CAAC,EAAE,QAAO,UAAU,eAAe,KAAK,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,EAAE,CAAC;AAAG,WAAOA;AAAA,EAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAC;AAAE,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,KAAI,MAAI,IAAE,UAAW,SAAS,GAAE,GAAE;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,KAAKA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,UAAE,EAAE,MAAMA,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAIC;AAAE,MAAAD,GAAE,OAAK,EAAEA,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAa,IAAEA,KAAE,IAAI,EAAG,SAASD,IAAE;AAAC,QAAAA,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAG,KAAK,GAAE,CAAC;AAAA,IAAC;AAAC,OAAG,IAAE,EAAE,MAAMD,IAAE,KAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,IAAE,EAAC,OAAM,GAAE,MAAK,WAAU;AAAC,QAAG,IAAE,EAAE,CAAC,EAAE,OAAM,EAAE,CAAC;AAAE,WAAO,EAAE,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC;AAAE,SAAO,IAAE,EAAC,MAAK,EAAE,CAAC,GAAE,OAAM,EAAE,CAAC,GAAE,QAAO,EAAE,CAAC,EAAC,GAAE,cAAY,OAAO,WAAS,EAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,WAAO;AAAA,EAAI,IAAG;AAAE,WAAS,EAAEE,IAAE;AAAC,WAAO,SAASC,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,YAAG,EAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,eAAK,IAAG,KAAG;AAAC,cAAG,IAAE,GAAE,MAAI,IAAE,IAAEA,GAAE,CAAC,IAAE,EAAE,SAAOA,GAAE,CAAC,IAAE,EAAE,WAAS,IAAE,EAAE,WAAS,EAAE,KAAK,CAAC,GAAE,KAAG,EAAE,SAAO,EAAE,IAAE,EAAE,KAAK,GAAEA,GAAE,CAAC,CAAC,GAAG,KAAK,QAAO;AAAE,kBAAO,IAAE,GAAE,MAAIA,KAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,KAAK,IAAGA,GAAE,CAAC,GAAE;AAAA,YAAC,KAAK;AAAA,YAAE,KAAK;AAAE,kBAAEA;AAAE;AAAA,YAAM,KAAK;AAAE,qBAAO,EAAE,SAAQ,EAAC,OAAMA,GAAE,CAAC,GAAE,MAAK,MAAE;AAAA,YAAE,KAAK;AAAE,gBAAE,SAAQ,IAAEA,GAAE,CAAC,GAAEA,KAAE,CAAC,CAAC;AAAE;AAAA,YAAS,KAAK;AAAE,cAAAA,KAAE,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,YAAS;AAAQ,kBAAG,GAAG,KAAG,IAAE,EAAE,MAAM,SAAO,KAAG,EAAE,EAAE,SAAO,CAAC,MAAI,MAAIA,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,IAAG;AAAC,oBAAE;AAAE;AAAA,cAAQ;AAAC,kBAAG,MAAIA,GAAE,CAAC,MAAI,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,IAAG;AAAC,kBAAE,QAAMA,GAAE,CAAC;AAAE;AAAA,cAAK;AAAC,kBAAG,MAAIA,GAAE,CAAC,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,IAAEA;AAAE;AAAA,cAAK;AAAC,kBAAG,KAAG,EAAE,QAAM,EAAE,CAAC,GAAE;AAAC,kBAAE,QAAM,EAAE,CAAC,GAAE,EAAE,IAAI,KAAKA,EAAC;AAAE;AAAA,cAAK;AAAC,gBAAE,CAAC,KAAG,EAAE,IAAI,IAAI,GAAE,EAAE,KAAK,IAAI;AAAE;AAAA,UAAQ;AAAC,UAAAA,KAAE,EAAE,KAAKF,IAAE,CAAC;AAAA,QAAC,SAAOA,IAAE;AAAC,UAAAE,KAAE,CAAC,GAAEF,EAAC,GAAE,IAAE;AAAA,QAAC,UAAC;AAAQ,cAAE,IAAE;AAAA,QAAC;AAAC,YAAG,IAAEE,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,eAAM,EAAC,OAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,QAAO,MAAK,KAAE;AAAA,MAAC,EAAE,CAACA,IAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,CAAC,YAAW,WAAU,WAAU,eAAc,mBAAkB,gBAAgB;AAAtF,IAAwF,IAAE,EAAC,WAAU,SAAQ,SAAQ,aAAY,UAAS,EAAC;AAA3I,IAA6I,IAAE,WAAU;AAAC,WAASH,GAAEA,IAAE;AAAC,QAAI,IAAE;AAAK,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,aAAW,OAAG,KAAK,uBAAqB,IAAI,EAAE,cAAc,EAAC,YAAW,SAASC,IAAE,GAAE;AAAC,UAAGD,GAAE,cAAa;AAAC,YAAI,IAAEA,GAAE,aAAa,QAAQ,QAAO,EAAE;AAAE,eAAM,GAAG,OAAO,GAAE,GAAG,EAAE,OAAOC,EAAC;AAAA,MAAC;AAAC,aAAM,GAAG,OAAO,GAAE,GAAG,EAAE,OAAOA,EAAC;AAAA,IAAC,EAAC,CAAC,GAAE,KAAK,qBAAqB,WAAW,EAAC,YAAW,KAAK,YAAW,OAAMD,GAAE,UAAS,CAAC,GAAE,KAAK,qBAAqB,UAAW,SAASA,IAAE;AAAC,UAAG,EAAE,SAAOA,GAAE,MAAM,QAAO,EAAE,QAAMA,GAAE,MAAM,OAAM,EAAE,QAAM,CAAC,GAAE,SAAOA,GAAE,WAAW,UAAQ,IAAE,GAAE,IAAEA,GAAE,YAAW,IAAE,EAAE,QAAO,KAAI;AAAC,YAAI,IAAE,EAAE,CAAC;AAAE,UAAE,MAAM,KAAK,EAAE,qBAAqB,EAAE,YAAW,QAAO,QAAO,QAAO,EAAC,MAAK,KAAG,IAAE,EAAE,aAAa,UAAQ,EAAE,QAAM,GAAE,MAAK,IAAE,EAAE,OAAM,MAAK,IAAE,EAAE,UAAQ,EAAE,SAAO,GAAE,MAAK,IAAE,EAAE,QAAO,OAAM,EAAE,OAAM,QAAO,EAAE,OAAM,EAAE,CAAC;AAAA,MAAC;AAAC,UAAI,GAAE,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,uBAAqB,SAASA,IAAE,GAAE;AAAC,QAAI,IAAE;AAAK,WAAM,EAAC,WAAUA,GAAE,IAAK,SAASA,IAAEC,IAAE;AAAC,aAAM,EAAC,GAAED,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,QAAO,MAAK,EAAEC,EAAC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAI,EAAC,MAAK,EAAE,OAAK,KAAK,OAAM,MAAK,EAAE,OAAK,KAAK,QAAO,MAAK,EAAE,OAAK,KAAK,OAAM,MAAK,EAAE,OAAK,KAAK,QAAO,OAAM,EAAE,QAAM,KAAK,OAAM,QAAO,EAAE,SAAO,KAAK,OAAM,EAAC;AAAA,EAAC,GAAED,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,KAAG,EAAE,kBAAgB,EAAE,mBAAiB,KAAK,eAAa,KAAK,aAAW,EAAE,gBAAe,KAAK,qBAAqB,WAAW,EAAC,YAAW,KAAK,WAAU,CAAC,IAAGA,cAAa,UAAG,IAAE,UAAU,MAAK,CAAC,GAAE,gBAAE,SAASA,EAAC,CAAC,KAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,KAAI,EAAE,MAAM,WAAU,CAAC,QAAO,EAAE,KAAK,GAAEA,GAAE,MAAM,CAAC,GAAEA,GAAE,MAAM,CAAC,CAAC,CAAC,MAAG,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAEA,IAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAOA,KAAE,GAAE,CAAC,GAAE,KAAK,qBAAqB,KAAK,EAAC,OAAMA,GAAC,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,KAAK,KAAK;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,qBAAqB,MAAM;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,qBAAqB,MAAM,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,MAAK,KAAK,aAAW;AAAA,EAAE,GAAEA,GAAE,UAAU,aAAW,WAAU;AAAC,WAAO,KAAK,qBAAqB,WAAW;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,CAAC;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,mBAAOC,GAAE,UAAQ,aAAY,QAAMA,GAAE,cAAYA,GAAE,YAAU,EAAE,YAAW,QAAMA,GAAE,aAAWA,GAAE,WAAS,EAAE,WAAUA;AAAA,UAAC,EAAED,EAAC,GAAE,CAAC,IAAG,IAAE,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa,SAAE,EAAC,QAAOA,GAAE,MAAM,CAAC,GAAE,OAAMA,GAAE,MAAM,CAAC,EAAC,IAAE,EAAC,QAAOA,GAAE,QAAO,OAAMA,GAAE,MAAK;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa,SAAEA,KAAE,gBAAE,WAAWA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,eAAE,OAAO,MAAIA,GAAE,OAAO,WAAU;AAAC,WAAM,GAAG,OAAO,GAAE,qBAAqB;AAAA,EAAC,CAAE,GAAE,aAAE,OAAO,MAAIA,GAAE,QAAQ,WAAU;AAAC,WAAM,GAAG,OAAO,GAAE,sBAAsB;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,kBAAiB,IAAE,EAAE,iBAAgB,IAAE,EAAE,YAAW,IAAE,EAAE,wBAAuB,IAAE,EAAEA,EAAC,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,WAAOA,KAAE,EAAC,SAAQA,GAAE,UAAQD,GAAE,OAAM,SAAQC,GAAE,UAAQD,GAAE,QAAO,OAAMC,GAAE,QAAMD,GAAE,OAAM,QAAOC,GAAE,SAAOD,GAAE,QAAO,UAASC,GAAE,SAAQ,IAAE,EAAC,SAAQ,MAAGD,GAAE,OAAM,SAAQ,MAAGA,GAAE,QAAO,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,UAAS,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEG,IAAE;AAAC,QAAG,WAASA,OAAIA,KAAE,QAAI,CAACA,GAAE,QAAM,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,QAAIG,KAAEN,GAAE,QAAOI,KAAEJ,GAAE;AAAM,MAAEA,IAAE,YAAY,GAAE,EAAED,IAAE,KAAK;AAAE,QAAIE,IAAE,GAAE,IAAEK,KAAEF,IAAE,IAAEL,GAAE,SAAOA,GAAE,OAAM,IAAE,GAAE+E,KAAE;AAAE,WAAO,IAAE,KAAG7E,KAAEF,GAAE,OAAM,IAAEA,GAAE,QAAM,GAAE+E,MAAG,IAAE,IAAE,KAAG,MAAI7E,KAAEF,GAAE,SAAO,GAAE,IAAEA,GAAE,QAAO,KAAG,IAAE,IAAE,KAAG,IAAGA,GAAE,QAAME,IAAEF,GAAE,SAAO,GAAE,EAAC,KAAI+E,IAAE,MAAK,GAAE,OAAM,GAAE,QAAOA,GAAC;AAAA,EAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,SAAS/E,IAAEC,IAAEG,IAAEG,IAAE;AAAC,QAAIF,KAAEL,GAAE,OAAME,KAAEF,GAAE,QAAO,IAAEO,KAAE,KAAG,GAAE,IAAE,KAAK,IAAIP,GAAE,QAAQ,GAAE,IAAE,KAAK,IAAIA,GAAE,QAAQ,GAAE,IAAEA,GAAE,SAAQ+E,KAAE/E,GAAE,SAAQ,IAAE,IAAEC,IAAE4F,KAAE,IAAEzF,IAAE,IAAE,IAAI,MAAM,EAAE;AAAE,WAAO,EAAE,CAAC,IAAEC,KAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,CAACH,KAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAIG,KAAE,IAAE,IAAE,MAAGH,KAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAEG,KAAE,IAAE,IAAEwF,IAAE,EAAE,CAAC,IAAE3F,KAAE,IAAE2F,IAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI3F,KAAE,IAAE,MAAGG,KAAE,IAAE,IAAE0E,MAAGc,IAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,EAAE,IAAExF,KAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,SAASL,IAAE;AAAC,UAAG,OAAKA,GAAE,OAAO,OAAM,IAAI,MAAM,mCAAmC,OAAOA,GAAE,MAAM,CAAC;AAAE,aAAM,CAAC,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,GAAE,CAACA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,EAAE,CAAC;AAAA,EAAC,EAAE,GAAE,EAAE,OAAM,EAAE,QAAO,KAAE,GAAE,IAAE,KAAG,WAAU;AAAC,QAAIC,KAAE,EAAED,EAAC,GAAEI,KAAE,SAAE,SAASJ,IAAEC,IAAEG,IAAE;AAAC,aAAO,EAAEA,IAAE,iBAAiB,GAAE,CAAC,IAAEA,GAAE,QAAMJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,OAAM,IAAEG,GAAE,SAAOJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,OAAMD,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,OAAM,IAAEG,GAAE,QAAMJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,QAAO,IAAEG,GAAE,SAAOJ,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,QAAOD,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,QAAO,GAAE,CAAC;AAAA,IAAC,EAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAEI,KAAE,WAAS,IAAE,aAAW,WAAU2F,KAAE,MAAE,UAAU,WAAE,KAAE/F,IAAE,SAAS,CAAC,GAAEG,IAAE,YAAWC,IAAE,GAAE,CAAC,EAAE,QAAO,EAAE,KAAK,CAAC;AAAE,WAAO,QAAM,IAAE,SAASL,IAAEC,IAAE;AAAC,UAAIG,KAAE,SAASJ,IAAEC,IAAEG,IAAEG,IAAE;AAAC,YAAIF,MAAGE,KAAEH,MAAG;AAAI,eAAM,EAAC,OAAMC,IAAE,QAAOD,KAAE,IAAEC,GAAC;AAAA,MAAC,EAAE,GAAE,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,aAAO,KAAG,WAAU;AAAC,eAAO,IAAE,IAAED,IAAEI,GAAE,KAAK,GAAEA,GAAE,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,EAAE4F,IAAE,CAAC,IAAEA;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,aAAY,GAAE,SAAQ,GAAE,sBAAqB,EAAC;AAAC;AAAC,SAAS,EAAEhG,IAAE;AAAC,UAAMA,GAAE,6BAA2BA,GAAE,2BAAyB,QAAI,QAAMA,GAAE,iCAA+BA,GAAE,+BAA6B,IAAG,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB;AAAI,WAAQ,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEA,GAAE,aAAW;AAAC,aAAQ,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAEA,GAAE,QAAQ,UAAQA,GAAE,QAAQ,CAAC,MAAIA,GAAE,QAAQ,CAAC,KAAG;AAAC,UAAI,IAAE,EAAEA,GAAE,UAASA,GAAE,UAAS,GAAEA,GAAE,QAAQ,MAAM;AAAE,UAAG,MAAI,KAAGA,GAAE,yBAAyB,GAAE,KAAK,CAAC,GAAE,EAAE,KAAK,CAAC,GAAE,EAAE,KAAK,GAAE,GAAE,EAAE,KAAK,GAAE,GAAE,EAAE,KAAK,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,WAAM;AAAC,iBAAQ,IAAE,GAAE,IAAEA,GAAE,aAAa,QAAO,EAAE,EAAE,GAAE,KAAKA,GAAE,aAAa,CAAC,CAAC,GAAE,EAAE,KAAK,CAAC;AAAE,YAAGA,GAAE,+BAA6B,GAAE;AAAC,cAAI,IAAE,MAAIA,GAAE,QAAQ,SAAO,IAAE,IAAE,EAAEA,GAAE,UAASA,GAAE,UAAS,IAAE,GAAEA,GAAE,QAAQ,MAAM;AAAE,YAAE,KAAK,KAAK,KAAK,IAAE,CAAC,CAAC,GAAE,EAAE,KAAKA,GAAE,4BAA4B;AAAA,QAAC;AAAA,MAAC;AAAC;AAAA,IAAG;AAAC,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,UAAI,IAAE,KAAK,KAAK,EAAE,CAAC,CAAC;AAAE,QAAE,KAAK,EAAE,CAAC,IAAE,CAAC,GAAE,EAAE,KAAK,EAAE,CAAC,IAAE,CAAC;AAAA,IAAC;AAAC,QAAI,IAAE,GAAE,IAAE;AAAE,QAAGA,GAAE,iBAAiB,SAAO,EAAE,KAAEA,GAAE,iBAAiB,CAAC,GAAE,IAAEA,GAAE,gBAAgB,CAAC;AAAA,SAAM;AAAC,UAAI,IAAEA,GAAE,QAAQ,CAAC;AAAE,UAAE,KAAK,KAAKA,GAAE,kBAAgB,CAAC,GAAE,IAAE,KAAK,KAAKA,GAAE,iBAAe,CAAC;AAAA,IAAC;AAAC,aAAQ,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,UAAQ,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,UAAI,IAAE,EAAC,UAAS,IAAEA,GAAE,iBAAe,GAAE,UAAS,IAAEA,GAAE,iBAAe,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,MAAAA,GAAE,mBAAiB,EAAE,QAAM,GAAE,EAAE,SAAO,MAAI,EAAE,QAAM,EAAE,CAAC,GAAE,EAAE,SAAO,EAAE,CAAC,IAAG,EAAE,KAAK,CAAC;AAAA,IAAC;AAAC,QAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,MAAI,IAAE,OAAIA,KAAE,KAAGA,MAAG,IAAEA,MAAG,KAAG,IAAE;AAAE;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,SAAM,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,IAAE,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAIO,IAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAOP,GAAE,KAAM,SAASA,IAAEC,IAAE;AAAC,mBAAO,KAAK,IAAI,MAAM,MAAKA,GAAE,KAAK,IAAE,KAAK,IAAI,MAAM,MAAKD,GAAE,KAAK;AAAA,UAAC,CAAE,GAAEO,KAAE,SAAEP,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAM,CAACA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,MAAKA,GAAE,aAAa,oBAAoB,IAAI;AAAA,UAAC,CAAE,CAAC,GAAE,IAAE,SAAEA,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAOA,GAAE,MAAM,CAAC;AAAA,UAAC,CAAE,CAAC,GAAE,CAAC,GAAE,MAAE,uBAAuBO,IAAE,GAAE,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAM,CAAC,IAAG,IAAE,EAAE,KAAK,GAAG,MAAM,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,IAAEP,GAAE,OAAQ,SAASA,IAAEC,IAAE;AAAC,mBAAO,EAAE,QAAQA,EAAC,IAAE;AAAA,UAAE,CAAE,GAAE,QAAE,CAACM,IAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGP,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAEA,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAE,SAASA,IAAEC,IAAEG,IAAE;AAAC,mBAAO,KAAG,WAAU;AAAC,kBAAIG,IAAEF,IAAE,GAAEC;AAAE,cAAAF,GAAE,sBAAoBC,KAAE,QAAE,MAAEL,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEG,KAAE,QAAE,MAAEP,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEE,KAAE,QAAE,MAAEN,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEJ,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,MAAIG,KAAE,QAAE,MAAEP,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEC,KAAE,QAAE,MAAEL,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEJ,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAEE,KAAE,QAAE,MAAEN,IAAE,CAAC,GAAEI,GAAE,iBAAe,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,IAAGC,KAAE,IAAE,IAAE,IAAEA,IAAED,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEM,KAAE,IAAE,IAAE,IAAEA,IAAEH,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,GAAE,6BAA2B,IAAE,IAAE,IAAE,IAAE,GAAEA,GAAE,MAAM,CAAC,GAAEH,GAAE,CAAC,GAAEK,KAAE,IAAE,IAAE,IAAEA,IAAEF,GAAE,MAAM,CAAC,GAAEH,GAAE,CAAC,MAAI,IAAE,IAAE,IAAE,GAAEG,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEK,KAAE,IAAE,IAAEA,IAAEF,GAAE,MAAM,GAAEH,GAAE,CAAC;AAAG,kBAAIE,KAAE,IAAEI,IAAE,IAAE,GAAE,CAAC,CAAC,GAAE,IAAE,IAAEF,IAAE,IAAEC,IAAE,CAAC,CAAC,GAAEc,KAAE,IAAEb,IAAE,IAAE,GAAE,CAAC,CAAC,GAAEqF,KAAE,IAAEvF,IAAE,IAAEC,IAAE,CAAC,CAAC,GAAE,IAAE,OAAE,CAAC,QAAEH,IAAE,CAACC,GAAE,UAAS,CAAC,CAAC,GAAE,QAAE,GAAE,CAACA,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEgB,IAAE,CAAChB,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEwF,IAAE,CAACxF,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,CAAC;AAAE,kBAAGA,GAAE,aAAa,UAAQ,IAAE,GAAE,IAAEA,GAAE,cAAa,EAAE,GAAE;AAAC,oBAAI,IAAEA,GAAE,sBAAoB,IAAEA,GAAE,sBAAqB,IAAE,QAAO,IAAE;AAAO,gBAAAA,GAAE,sBAAoB,IAAE,QAAE,MAAEJ,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEA,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,MAAI,IAAE,QAAE,MAAEA,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,GAAE,IAAE,QAAE,MAAEA,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC;AAAG,oBAAIQ,KAAE,IAAE,IAAE,IAAE,GAAEJ,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEQ,KAAE,IAAE,IAAE,IAAE,GAAEL,GAAE,MAAM,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC;AAAE,oBAAE,OAAE,CAAC,GAAE,QAAEO,IAAE,CAACJ,GAAE,UAAS,CAAC,CAAC,GAAE,QAAEK,IAAE,CAACL,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,CAAC;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAC,CAAE;AAAA,UAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,KAAG,WAAU;AAAC,gBAAIJ,KAAE;AAAE,mBAAO,EAAE,gBAAc,QAAM,EAAE,wBAAsBA,KAAE,YAAE,GAAE,CAAC,EAAE,qBAAoB,EAAE,mBAAmB,IAAGA,KAAE,QAAEA,EAAC,KAAGA;AAAA,UAAC,CAAE,GAAE,CAAC,GAAE,GAAG,GAAE,GAAE,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,QAAE,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,CAAC,GAAE,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,GAAE,IAAE,EAAE,UAAS,EAAE,EAAE,KAAG,EAAE,QAAM,EAAE,kBAAgB,EAAE,CAAC,IAAE,EAAE,mBAAiB,IAAE,IAAE,EAAE,WAAU,IAAE,GAAG,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,gBAAe,CAAC,IAAG,IAAE,EAAE,aAAa,qBAAqB,QAAM,KAAG,EAAE,SAAO,KAAI;AAAC,gBAAG,EAAE,eAAa,EAAE,OAAK,IAAE,EAAE,cAAc,oBAAkB,CAAC,GAAE,IAAE,EAAE,eAAa,EAAE,sBAAqB,IAAE,GAAE,IAAE,GAAE,KAAG,EAAE,qBAAqB,KAAE,IAAE,EAAE,sBAAoB,GAAE,IAAE,EAAC,GAAE,EAAE,IAAE,CAAC,GAAE,GAAE,EAAE,iBAAe,IAAE,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,EAAC,GAAE,EAAE,kBAAkB,KAAK,CAAC;AAAE,cAAE,KAAK,CAAC;AAAA,UAAC;AAAC,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAM,EAAC,OAAM,CAAC,CAAC,GAAE,KAAI,GAAE,cAAa,EAAC,qBAAoB,EAAC,MAAK,GAAE,MAAK,IAAE,IAAE,IAAEA,IAAE,MAAK,GAAE,MAAK,IAAE,IAAEA,KAAE,GAAE,OAAM,IAAE,GAAE,QAAO,IAAEA,GAAC,EAAC,EAAC;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,EAAC,0BAAyB,OAAG,8BAA6B,GAAE,kBAAiB,CAAC,GAAE,iBAAgB,CAAC,GAAE,WAAU,GAAE,UAAS,WAAS,UAAS,MAAI,iBAAgB,KAAI,gBAAe,KAAI,eAAc,KAAG,eAAc,KAAG,SAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,cAAa,CAAC,CAAC,GAAE,iBAAgB,KAAE;AAAzR,IAA2R,KAAG,EAAC,0BAAyB,OAAG,8BAA6B,GAAE,kBAAiB,CAAC,GAAE,iBAAgB,CAAC,GAAE,WAAU,GAAE,UAAS,WAAS,UAAS,MAAI,iBAAgB,KAAI,gBAAe,KAAI,eAAc,KAAG,eAAc,KAAG,SAAQ,CAAC,CAAC,GAAE,cAAa,CAAC,CAAC,GAAE,iBAAgB,KAAE;AAApiB,IAAsiB,KAAG,EAAC,SAAQ,QAAO,WAAU,SAAQ,UAAS,GAAE,kBAAiB,gEAA+D;AAAtqB,IAAwqB,KAAG,EAAC,2BAA0B,OAAG,gBAAe,OAAG,eAAc,CAAC,GAAE,YAAW,GAAE,UAAS,KAAI,WAAU,IAAG,gBAAe,GAAE,qBAAoB,GAAE,cAAa,GAAE,sBAAqB,GAAE,cAAa,MAAG,qBAAoB,KAAI,oBAAmB,MAAG,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,gBAAe,IAAE;AAA39B,IAA69B,KAAG,EAAC,2BAA0B,OAAG,gBAAe,OAAG,eAAc,CAAC,GAAE,YAAW,GAAE,UAAS,MAAK,WAAU,IAAG,gBAAe,GAAE,qBAAoB,GAAE,cAAa,GAAE,sBAAqB,GAAE,cAAa,MAAG,qBAAoB,KAAI,oBAAmB,MAAG,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,gBAAe,IAAE;AAAjxC,IAAmxC,KAAG,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,iBAAgB,MAAG,wBAAuB,CAAC,IAAG,CAAC,GAAE,YAAW,OAAM;AAAj4C,IAAm4C,KAAG,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,iBAAgB,MAAG,wBAAuB,CAAC,IAAG,CAAC,GAAE,YAAW,OAAM;AAAj/C,IAAm/C,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE,GAAE;AAAC,SAAK,gBAAc,GAAE,KAAK,WAAS,GAAE,WAASA,MAAG,KAAK,sBAAoB,IAAG,KAAK,2BAAyB,IAAG,KAAK,UAAQ,EAAE,EAAE,MAAI,KAAK,sBAAoB,IAAG,KAAK,2BAAyB,IAAG,KAAK,UAAQ,EAAE,EAAE;AAAG,QAAI,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAK,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAM,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAO,CAAE,CAAC,GAAE,IAAE,SAAE,KAAK,QAAQ,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAO,CAAE,CAAC;AAAE,SAAK,eAAa,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,cAAc,QAAQ,GAAE,QAAE,CAAC,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa,CAAC,CAAC;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAA,EAAC,GAAEA,GAAE,UAAU,cAAY,SAASA,IAAE,GAAE;AAAC,WAAO,WAAS,MAAI,IAAE,QAAI,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,QAAMA,MAAG,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,IAAE,KAAG,WAAU;AAAC,kBAAII,KAAE,KAAE,EAAEJ,EAAC,GAAE,SAAS;AAAE,qBAAO,MAAII,KAAE,QAAE,MAAE,cAAc,WAAEA,IAAE,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC,IAAGA;AAAA,YAAC,CAAE,GAAE,IAAE,EAAE,GAAE,KAAK,mBAAmB,GAAE,IAAE,EAAE,aAAY,IAAE,EAAE,sBAAqB,IAAE,KAAK,cAAc,QAAQ,GAAE,YAAY,GAAE,IAAE,SAASJ,IAAE;AAAC,qBAAO,KAAG,WAAU;AAAC,oBAAIC,KAAE,SAASD,IAAE;AAAC,yBAAO,KAAG,WAAU;AAAC,2BAAM,CAAC,MAAEA,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,CAAC,GAAE,MAAEA,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAAA,kBAAC,CAAE;AAAA,gBAAC,EAAEA,EAAC,GAAEI,KAAEH,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC;AAAE,uBAAM,EAAC,OAAM,QAAEM,EAAC,GAAE,QAAO,QAAEH,EAAC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,EAAE,CAAC,GAAE,IAAE,EAAE,OAAM,CAAC,GAAE,GAAG,CAAC,IAAE,EAAE,QAAO,CAAC,GAAE,KAAK,cAAa,KAAK,wBAAwB,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAO,OAAK,IAAE,EAAE,KAAK,GAAG,UAAQ,QAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,KAAG,CAAC,GAAE,EAAE,GAAE,KAAK,UAAS,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,IAAE,SAASJ,IAAEC,IAAE;AAAC,yBAASD,OAAIA,KAAE,CAAC;AAAG,kBAAII,IAAEG,MAAGH,KAAEH,IAAE,CAAC,EAAE,OAAO,MAAM,CAAC,GAAEG,EAAC;AAAG,qBAAOJ,GAAE,QAAS,SAASA,IAAE;AAAC,oBAAIC,KAAED,GAAE;AAAa,gBAAAC,GAAE,kBAAkB,QAAS,SAASD,IAAE;AAAC,sBAAIC,KAAE,EAAEM,IAAE,CAACP,GAAE,GAAEA,GAAE,CAAC,CAAC,GAAEI,KAAEH,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC;AAAE,kBAAAD,GAAE,IAAEI,IAAEJ,GAAE,IAAEK;AAAA,gBAAC,CAAE;AAAE,oBAAID,KAAEH,GAAE,qBAAoBI,KAAE,OAAO,WAAU,IAAE,OAAO,WAAUC,KAAE,OAAO,WAAU,IAAE,OAAO;AAAU,iBAAC,CAACF,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,OAAKA,GAAE,OAAMA,GAAE,IAAI,GAAE,CAACA,GAAE,OAAKA,GAAE,OAAMA,GAAE,OAAKA,GAAE,MAAM,GAAE,CAACA,GAAE,MAAKA,GAAE,OAAKA,GAAE,MAAM,CAAC,EAAE,QAAS,SAASJ,IAAE;AAAC,sBAAIC,KAAE,EAAEM,IAAEP,EAAC,GAAEI,KAAEH,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC;AAAE,kBAAAI,KAAE,KAAK,IAAIA,IAAED,EAAC,GAAEE,KAAE,KAAK,IAAIA,IAAEF,EAAC,GAAE,IAAE,KAAK,IAAI,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,CAAC;AAAA,gBAAC,CAAE,GAAEH,GAAE,sBAAoB,EAAC,MAAKI,IAAE,MAAKC,IAAE,MAAK,GAAE,MAAK,GAAE,OAAMA,KAAED,IAAE,QAAO,IAAE,EAAC;AAAA,cAAC,CAAE,GAAEL;AAAA,YAAC,EAAE,GAAE,CAAC,GAAE,QAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,eAAO,IAAE,EAAEA,EAAC,GAAE,IAAE,CAAC,CAAC,KAAG,EAAE,gBAAe,CAAC,GAAE,KAAK,YAAYA,IAAE,CAAC,EAAE,KAAM,SAASA,IAAE;AAAC,iBAAOA,GAAE,IAAK,SAASA,IAAE;AAAC,qBAAQC,KAAED,GAAE,aAAa,kBAAkB,IAAK,SAASA,IAAEC,IAAE;AAAC,qBAAO,EAAE,EAAE,CAAC,GAAED,EAAC,GAAE,EAAC,GAAEA,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,QAAO,MAAK,EAAEC,EAAC,EAAC,CAAC;AAAA,YAAC,CAAE,GAAEM,KAAEP,GAAE,aAAa,qBAAoBK,KAAE,GAAE,IAAE,CAAC,SAAQ,QAAO,MAAM,GAAEA,KAAE,EAAE,QAAOA,KAAI,CAAAE,GAAE,EAAEF,EAAC,CAAC,KAAG,EAAE;AAAM,qBAAQ,IAAE,GAAE,IAAE,CAAC,UAAS,QAAO,MAAM,GAAE,IAAE,EAAE,QAAO,IAAI,CAAAE,GAAE,EAAE,CAAC,CAAC,KAAG,EAAE;AAAO,mBAAM,EAAC,WAAUN,IAAE,KAAIM,GAAC;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEP;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,EAAE;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,mBAAO,QAAMC,GAAE,cAAYA,GAAE,YAAU,GAAG,YAAW,QAAMA,GAAE,aAAWA,GAAE,WAAS,GAAG,WAAU,QAAMA,GAAE,qBAAmB,WAASA,GAAE,YAAUA,GAAE,mBAAiB,iEAA+DA,GAAE,mBAAiB,kEAAiEA;AAAA,UAAC,EAAED,EAAC,GAAE,IAAE,YAAU,OAAO,EAAE,oBAAkB,EAAE,iBAAiB,QAAQ,mBAAmB,IAAE,IAAG,CAAC,GAAE,eAAE,EAAE,kBAAiB,EAAC,WAAU,EAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,IAAI,GAAG,EAAE,WAAU,GAAE,EAAE,QAAQ,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,UAAGA,OAAI,GAAG,uBAAsB;AAAC,YAAG,IAAE,QAAO,SAAO,IAAE,IAAG;AAAC,cAAG,WAAS,EAAE,QAAQ,QAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAE,cAAG,gBAAc,EAAE,QAAQ,QAAM,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,cAAE,EAAE;AAAA,QAAO;AAAC,cAAM,IAAI,MAAM,oDAAkD,2BAA2B,OAAO,CAAC,CAAC;AAAA,MAAC;AAAC,YAAM,IAAI,MAAM,GAAG,OAAOA,IAAE,iCAAiC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,QAAMA,GAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAI,IAAEA,GAAE,UAAQA,GAAE,QAAM,GAAE,IAAE,IAAEA,GAAE,OAAM,IAAEA,GAAE,UAAQA,GAAE,SAAO;AAAE,SAAM,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,IAAEA,GAAE,QAAO,OAAMA,GAAE,OAAM,QAAOA,GAAE,OAAM;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAI,IAAE,GAAGA,EAAC,GAAE,IAAE,GAAG,CAAC;AAAE,MAAG,CAAC,SAASA,IAAEC,IAAE;AAAC,WAAM,EAAED,GAAE,OAAKC,GAAE,QAAMA,GAAE,OAAKD,GAAE,QAAMA,GAAE,OAAKC,GAAE,QAAMA,GAAE,OAAKD,GAAE;AAAA,EAAK,EAAE,GAAE,CAAC,EAAE,QAAO;AAAE,MAAI,IAAE,GAAG,SAASA,IAAEC,IAAE;AAAC,QAAIG,KAAE,KAAK,IAAIJ,GAAE,MAAKC,GAAE,IAAI,GAAEM,KAAE,KAAK,IAAIP,GAAE,MAAKC,GAAE,IAAI,GAAEI,KAAE,KAAK,IAAIL,GAAE,MAAKC,GAAE,IAAI,GAAEC,KAAE,KAAK,IAAIF,GAAE,MAAKC,GAAE,IAAI;AAAE,WAAM,EAAC,MAAKG,IAAE,MAAKG,IAAE,MAAKF,IAAE,MAAKH,IAAE,OAAM,KAAK,IAAIK,KAAEH,IAAE,CAAC,GAAE,QAAO,KAAK,IAAIF,KAAEG,IAAE,CAAC,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,CAAC,GAAE,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE;AAAE,SAAO,IAAE,IAAE,IAAE,IAAE;AAAC;AAAC,SAAS,GAAGL,IAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAEA,GAAE,OAAM,IAAEA,GAAE,QAAO,IAAE,IAAE,KAAG,GAAE,IAAE,KAAK,IAAIA,GAAE,QAAQ,GAAE,IAAE,KAAK,IAAIA,GAAE,QAAQ,GAAE,IAAEA,GAAE,SAAQ,IAAEA,GAAE,SAAQ,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,IAAI,MAAM,EAAE;AAAE,SAAO,EAAE,CAAC,IAAE,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,CAAC,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI,IAAE,IAAE,IAAE,MAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,KAAG,OAAI,IAAE,IAAE,MAAG,IAAE,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,EAAE,IAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,EAAE,EAAE,IAAE,GAAE,SAASA,IAAE;AAAC,QAAG,OAAKA,GAAE,OAAO,OAAM,IAAI,MAAM,qCAAmCA,GAAE,MAAM;AAAE,WAAM,CAAC,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,GAAE,CAACA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC,CAAC;AAAA,EAAC,EAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,cAAe,SAAO,EAAC,QAAOA,GAAE,MAAM,CAAC,GAAE,OAAMA,GAAE,MAAM,CAAC,EAAC,IAAE,EAAC,QAAOA,GAAE,QAAO,OAAMA,GAAE,MAAK;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,IAAE,KAAK,KAAG,KAAK,OAAOA,KAAE,KAAK,OAAK,IAAE,KAAK,GAAG;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,cAAe,SAAOA,KAAI,gBAAQ,WAAWA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,EAAE,aAAK,OAAO,MAAIA,GAAE,OAAO,WAAU;AAAC,WAAO,IAAE;AAAA,EAAqB,CAAE,GAAI,aAAK,OAAO,MAAIA,GAAE,QAAQ,WAAU;AAAC,WAAO,IAAE;AAAA,EAAsB,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,MAAI,IAAE,SAASA,IAAE,GAAEI,IAAEG,IAAE;AAAC,QAAI,IAAE,IAAEP,IAAE,IAAEO,KAAEH;AAAE,QAAG,MAAI,EAAE,OAAM,IAAI,MAAM,mCAAiCJ,KAAE,sBAAsB;AAAE,QAAI,IAAE,IAAE;AAAE,WAAM,EAAC,OAAM,GAAE,QAAOI,KAAEJ,KAAE,EAAC;AAAA,EAAC,EAAE,GAAE,KAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,SAAS,KAAM,WAAU;AAAC,WAAS,IAAM,IAAIA,IAAE,EAAE,KAAK,GAAE,EAAE,MAAM;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,kBAAiB,IAAE,EAAE,iBAAgB,IAAE,EAAE,YAAW,IAAE,EAAE,wBAAuB,IAAE,GAAGA,EAAC,GAAE,IAAE,SAASA,IAAE,GAAE;AAAC,WAAO,IAAE,EAAC,SAAQ,EAAE,UAAQA,GAAE,OAAM,SAAQ,EAAE,UAAQA,GAAE,QAAO,OAAM,EAAE,QAAMA,GAAE,OAAM,QAAO,EAAE,SAAOA,GAAE,QAAO,UAAS,EAAE,SAAQ,IAAE,EAAC,SAAQ,MAAGA,GAAE,OAAM,SAAQ,MAAGA,GAAE,QAAO,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,UAAS,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,GAAE,IAAE,SAASA,IAAE,GAAEI,IAAE;AAAC,QAAG,WAASA,OAAIA,KAAE,QAAI,CAACA,GAAE,QAAM,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,QAAIG,KAAE,EAAE,QAAOF,KAAE,EAAE;AAAM,OAAG,GAAE,YAAY,GAAE,GAAGL,IAAE,KAAK;AAAE,QAAIE,IAAEI,IAAEH,KAAEI,KAAEF,IAAEc,KAAEnB,GAAE,SAAOA,GAAE,OAAMoB,KAAE,GAAE2D,KAAE;AAAE,WAAO5E,KAAEgB,MAAGjB,KAAEF,GAAE,OAAMM,KAAEN,GAAE,QAAMG,IAAE4E,MAAG,IAAE5D,KAAEhB,MAAG,MAAID,KAAEF,GAAE,SAAOG,IAAEG,KAAEN,GAAE,QAAOoB,MAAG,IAAEjB,KAAEgB,MAAG,IAAGnB,GAAE,QAAME,IAAEF,GAAE,SAAOM,IAAE,EAAC,KAAIyE,IAAE,MAAK3D,IAAE,OAAMA,IAAE,QAAO2D,GAAC;AAAA,EAAC,EAAE,GAAE,GAAE,CAAC,GAAE,IAAE,GAAG,GAAE,EAAE,OAAM,EAAE,QAAO,KAAE,GAAE,IAAI,KAAM,WAAU;AAAC,QAAI3E,KAAE,GAAGJ,EAAC,GAAEO,KAAI,SAAS,SAASP,IAAE,GAAEI,IAAE;AAAC,aAAO,GAAGA,IAAE,iBAAiB,GAAE,CAAC,IAAEA,GAAE,QAAMJ,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,OAAM,IAAEI,GAAE,SAAOJ,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,OAAMA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,OAAM,IAAEI,GAAE,QAAMJ,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,QAAO,IAAEI,GAAE,SAAOJ,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,QAAOA,GAAE,CAAC,EAAE,CAAC,IAAE,EAAE,QAAO,GAAE,CAAC;AAAA,IAAC,EAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE,WAAS,IAAE,aAAW,WAAUkB,KAAI,MAAM,UAAY,WAAa,KAAKhB,IAAE,SAAS,CAAC,GAAEG,IAAE,YAAWL,IAAE,GAAE,CAAC,EAAE,QAAO,EAAE,KAAK,CAAC;AAAE,WAAO,QAAM,IAAE,GAAGkB,IAAE,CAAC,IAAEA;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,aAAY,GAAE,SAAQ,GAAE,sBAAqB,EAAC;AAAC;AAAC,SAAS,GAAGpB,IAAE;AAAC,SAAM,EAAC,SAAQA,GAAE,OAAKA,GAAE,QAAM,GAAE,SAAQA,GAAE,OAAKA,GAAE,SAAO,GAAE,OAAMA,GAAE,OAAM,QAAOA,GAAE,OAAM;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAI,IAAEA,GAAE;AAAkB,MAAG,EAAE,UAAQ,EAAE,OAAM,IAAI,MAAM,mDAAmD;AAAE,MAAI,IAAE,OAAO,WAAU,IAAE,OAAO,WAAU,IAAE,OAAO,WAAU,IAAE,OAAO;AAAU,SAAO,EAAE,QAAS,SAASA,IAAE;AAAC,QAAE,KAAK,IAAI,GAAEA,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAEA,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAEA,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAEA,GAAE,CAAC;AAAA,EAAC,CAAE,GAAE,EAAC,UAAS,IAAE,KAAG,GAAE,UAAS,IAAE,KAAG,GAAE,OAAM,IAAE,GAAE,QAAO,IAAE,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,WAAS,IAAE,SAASA,IAAEC,IAAEG,IAAE;AAAC,QAAIG,IAAEF,KAAEL,GAAE;AAAa,QAAG,kBAAgBC,GAAE,CAAAM,KAAE,GAAGF,GAAE,WAAW;AAAA,SAAM;AAAC,MAAAE,KAAE,GAAGF,EAAC;AAAE,UAAIH,KAAEE,GAAE,OAAM,IAAEA,GAAE;AAAO,MAAAG,GAAE,UAAQ,KAAK,MAAMA,GAAE,UAAQL,EAAC,GAAEK,GAAE,UAAQ,KAAK,MAAMA,GAAE,UAAQ,CAAC,GAAEA,GAAE,QAAM,KAAK,MAAMA,GAAE,QAAML,EAAC,GAAEK,GAAE,SAAO,KAAK,MAAMA,GAAE,SAAO,CAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC,EAAEP,IAAE,GAAE,CAAC,IAAE,SAASA,IAAEC,IAAE;AAAC,QAAIG,KAAEJ,GAAE;AAAa,WAAM,kBAAgBC,KAAE,GAAGG,GAAE,mBAAmB,IAAE,GAAGA,EAAC;AAAA,EAAC,EAAEJ,IAAE,CAAC;AAAE,SAAO,MAAI,EAAE,WAAS,SAASA,IAAEC,IAAEG,IAAE;AAAC,QAAIG,IAAEF,KAAEL,GAAE,cAAaE,KAAEE,GAAE,kCAAiC,IAAEA,GAAE;AAA+B,IAAAG,KAAEH,GAAE,4BAA0BA,GAAE,4BAA0B,KAAK,KAAGA,GAAE,kCAAgC;AAAI,QAAI,IAAEC,GAAE,kBAAkBH,EAAC,EAAE,IAAED,GAAE,OAAM,IAAEI,GAAE,kBAAkBH,EAAC,EAAE,IAAED,GAAE,QAAO,IAAEI,GAAE,kBAAkB,CAAC,EAAE,IAAEJ,GAAE,OAAM,IAAEI,GAAE,kBAAkB,CAAC,EAAE,IAAEJ,GAAE;AAAO,WAAO,GAAGM,KAAE,KAAK,MAAM,EAAE,IAAE,IAAG,IAAE,CAAC,CAAC;AAAA,EAAC,EAAEP,IAAE,GAAE,CAAC,IAAG;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,GAAE;AAAC,QAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAEA,GAAE,CAAC,CAAC;AAAE,MAAE,IAAE,EAAE,GAAE,EAAE,IAAE,EAAE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,MAAG,YAAU,OAAO,GAAE;AAAC,QAAG,WAAS,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,GAAEA,GAAE,CAAC,CAAC,EAAE,IAAE,EAAE,CAAC,EAAE;AAAA,EAAC,OAAK;AAAC,QAAI,IAAE,SAASA,IAAEC,IAAE;AAAC,eAAQG,KAAE,GAAEG,KAAE,GAAEA,KAAEN,GAAE,QAAO,EAAEM,GAAE,CAAAH,MAAGJ,GAAEC,GAAEM,EAAC,CAAC,EAAE;AAAE,aAAOH,KAAEH,GAAE;AAAA,IAAM,EAAE,GAAE,CAAC;AAAE,SAAI,IAAE,GAAE,IAAED,GAAE,QAAO,EAAE,EAAE,GAAEA,GAAE,CAAC,CAAC,EAAE,IAAE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,WAAQ,IAAE,SAASA,IAAE;AAAC,QAAIC,KAAE,CAAC,EAAE,OAAO,MAAM,CAAC,GAAED,GAAE,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAc,CAAE,CAAC;AAAE,QAAG,MAAIC,GAAE,OAAO,OAAM,IAAI,MAAM,0DAA0D;AAAE,QAAIG,KAAEH,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,GAAEI,KAAE,IAAI,IAAIJ,EAAC;AAAE,IAAAI,GAAE,QAAS,SAASL,IAAE;AAAC,MAAAI,KAAE,KAAK,IAAIA,IAAEJ,EAAC,GAAEO,KAAE,KAAK,IAAIA,IAAEP,EAAC;AAAA,IAAC,CAAE;AAAE,QAAIE,KAAEG,GAAE;AAAK,QAAG,MAAID,GAAE,OAAM,IAAI,MAAM,qDAAmDA,EAAC;AAAE,QAAGG,KAAE,MAAIL,GAAE,OAAM,IAAI,MAAM,sCAAoCK,KAAEL,KAAE,KAAG,sBAAsB;AAAE,WAAOA;AAAA,EAAC,EAAE,CAAC,GAAE,IAAE,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,EAAE,IAAI,MAAM,GAAE,IAAE,GAAE,IAAEF,GAAE,QAAO,EAAE,GAAE;AAAC,QAAI,IAAEA,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,QAAG,EAAE,WAAS,EAAE,eAAe,OAAO,OAAM,IAAI,MAAM,eAAa,EAAE,SAAO,6CAA2C,EAAE,eAAe,MAAM;AAAE,OAAG,EAAE,gBAAe,GAAE,CAAC,GAAE,GAAG,EAAE,gBAAe,EAAE,aAAY,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAOA,GAAE,IAAK,SAASA,IAAE;AAAC,QAAI,IAAE,EAAE,EAAE,CAAC,GAAEA,EAAC,GAAE,EAAC,GAAEA,GAAE,IAAE,EAAE,OAAM,GAAEA,GAAE,IAAE,EAAE,OAAM,CAAC;AAAE,WAAO,QAAMA,GAAE,MAAI,EAAE,IAAEA,GAAE,IAAE,EAAE,QAAO;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAM,WAASA,KAAE,IAAE,SAASA,IAAE;AAAC,WAAO,KAAG,IAAE,KAAK,IAAI,CAACA,EAAC;AAAA,EAAE,EAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,KAAG,EAAE,oBAAkB,OAAG,IAAE,KAAG,EAAE,kBAAgB,OAAG,IAAEA,GAAE,MAAK,IAAE,IAAE,EAAE,cAAa,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,QAAE,KAAK;AAAE,eAAI,IAAE,EAAE,KAAK,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,cAAa,EAAE,EAAE,KAAE,IAAE,IAAG,IAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAG,IAAE,IAAE,EAAE,kBAAgB,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,IAAE,MAAI,EAAE,IAAE,IAAE,EAAE,mBAAiB,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,IAAG,IAAE,MAAI,EAAE,IAAE,EAAE,IAAE,CAAC,IAAG,IAAE,MAAI,EAAE,QAAM,GAAG,EAAE,sBAAqB,EAAE,IAAE,CAAC,CAAC,IAAG,EAAE,KAAK,CAAC;AAAE,eAAI,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,EAAC,IAAE,EAAE,CAAC,GAAG,IAAE,EAAE,IAAE,EAAE,iBAAgB,EAAE,IAAE,EAAE,IAAE,EAAE,kBAAiB,EAAE,IAAE,EAAE,IAAE,EAAE,mBAAiB,EAAE,cAAY;AAAG,iBAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE,GAAE;AAAC,MAAI,IAAEA,GAAE,OAAM,IAAEA,GAAE,QAAO,IAAEA,GAAE;AAAS,MAAG,QAAM,EAAE,YAAU,QAAM,EAAE,mBAAiB,IAAE,SAASA,IAAEC,IAAE;AAAC,YAAMA,GAAE,WAASD,MAAGC,GAAE,WAAS,QAAMA,GAAE,mBAAiBD,MAAG,KAAK,KAAGC,GAAE,iBAAe;AAAK,WAAO,GAAGD,EAAC;AAAA,EAAC,EAAE,GAAE,CAAC,IAAG,MAAI,EAAE,CAAAA,GAAE,UAAQA,GAAE,UAAQ,IAAE,EAAE,QAAOA,GAAE,UAAQA,GAAE,UAAQ,IAAE,EAAE;AAAA,OAAW;AAAC,QAAI,KAAG,EAAE,QAAM,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,IAAE,EAAE,SAAO,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,KAAG,EAAE,OAAM,KAAG,EAAE,QAAM,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,IAAE,EAAE,SAAO,IAAE,EAAE,SAAO,KAAK,IAAI,CAAC,KAAG,EAAE;AAAO,IAAAA,GAAE,UAAQA,GAAE,UAAQ,GAAEA,GAAE,UAAQA,GAAE,UAAQ;AAAA,EAAC;AAAC,MAAG,EAAE,YAAW;AAAC,QAAI,IAAE,KAAK,IAAI,IAAE,EAAE,OAAM,IAAE,EAAE,MAAM;AAAE,QAAE,IAAE,EAAE,OAAM,IAAE,IAAE,EAAE;AAAA,EAAM,WAAS,EAAE,aAAY;AAAC,QAAI,IAAE,KAAK,IAAI,IAAE,EAAE,OAAM,IAAE,EAAE,MAAM;AAAE,QAAE,IAAE,EAAE,OAAM,IAAE,IAAE,EAAE;AAAA,EAAM;AAAC,SAAOA,GAAE,QAAM,IAAE,EAAE,QAAOA,GAAE,SAAO,IAAE,EAAE,QAAOA;AAAC;AAAA,CAAE,OAAK,KAAG,CAAC,IAAI,wBAAsB;AAAwB,IAAI,KAAG,EAAC,SAAQ,QAAO,UAAS,GAAE,iBAAgB,OAAG,kBAAiB,8EAA6E;AAAnJ,IAAqJ,KAAG,EAAC,gBAAe,OAAG,iBAAgB,MAAE;AAA7L,IAA+L,KAAG,EAAC,QAAO,GAAE,QAAO,GAAE,QAAO,KAAI,QAAO,KAAI,YAAW,KAAE;AAAxP,IAA0P,KAAG,EAAC,kBAAiB,EAAC,OAAM,KAAI,QAAO,IAAG,GAAE,wBAAuB,CAAC,GAAE,CAAC,GAAE,YAAW,YAAW;AAAzV,IAA2V,KAAG,EAAC,cAAa,KAAI,iBAAgB,KAAI,kBAAiB,KAAI,sBAAqB,QAAO,kBAAiB,OAAG,gBAAe,MAAE;AAA1d,IAA4d,KAAG,EAAC,cAAa,IAAG,iBAAgB,KAAI,kBAAiB,KAAI,sBAAqB,QAAO,kBAAiB,OAAG,gBAAe,MAAE;AAA1lB,IAA4lB,KAAG,EAAC,cAAa,IAAG,iBAAgB,KAAI,kBAAiB,KAAI,sBAAqB,QAAO,kBAAiB,OAAG,gBAAe,MAAE;AAA1tB,IAA4tB,KAAG,EAAC,cAAa,GAAE,iBAAgB,KAAI,kBAAiB,KAAI,sBAAqB,QAAO,kBAAiB,OAAG,gBAAe,MAAE;AAAz1B,IAA21B,KAAG,EAAC,gBAAe,MAAM,KAAK,MAAM,GAAG,EAAE,KAAK,CAAC,GAAE,aAAY,OAAM;AAA95B,IAAg6B,KAAG,EAAC,gBAAe,CAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,GAAE,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,GAAG,GAAE,aAAY,OAAM;AAAnuC,IAAquC,KAAG,EAAC,gBAAe,CAAC,IAAG,GAAE,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG,GAAE,aAAY,OAAM;AAAjhD,IAAmhD,KAAG,EAAC,gBAAe,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,GAAE,aAAY,OAAM;AAAt1D,IAAw1D,KAAG,EAAC,gBAAe,CAAC,KAAI,KAAI,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,IAAG,GAAE,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,EAAC;AAA38D,IAA68D,KAAG,EAAC,gBAAe,CAAC,KAAI,KAAI,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,EAAC;AAAE,IAAI;AAAJ,IAAO,KAAG,WAAU;AAAC,WAASA,GAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,SAAK,WAASA,IAAE,KAAK,gBAAc,GAAE,KAAK,WAAS,GAAE,KAAK,gBAAc,GAAE,KAAK,6BAA2B;AAAA,EAAI;AAAC,SAAOA,GAAE,UAAU,gBAAc,SAASA,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE;AAAK,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAE,SAASA,IAAE;AAAC,kBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,EAAE;AAAE,kBAAI,IAAE,EAAE,CAAC,GAAEA,EAAC;AAAE,qBAAO,QAAM,EAAE,mBAAiB,EAAE,iBAAe,GAAG,iBAAgB,QAAM,EAAE,oBAAkB,EAAE,kBAAgB,GAAG,kBAAiB;AAAA,YAAC,EAAE,CAAC,GAAE,QAAMA,MAAG,KAAK,MAAM,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,IAAE,GAAGA,EAAC,GAAE,IAAI,KAAM,WAAU;AAAC,kBAAII,KAAI,KAAK,GAAGJ,EAAC,GAAE,SAAS;AAAE,kBAAG,EAAE,gBAAe;AAAC,gBAAAI,KAAI,QAAU,MAAM,cAAgB,WAAWA,IAAE,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC;AAAA,cAAC;AAAC,qBAAOA;AAAA,YAAC,CAAE,GAAE,IAAE,KAAK,4BAA2B,EAAE,mBAAiB,QAAM,KAAG,EAAE,SAAO,KAAK,WAAS,CAAC,GAAE,KAAK,SAAS,YAAY,GAAE,KAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,UAAG,KAAK;AAAE,mBAAO,OAAK,IAAE,EAAE,KAAK,GAAG,UAAQ,KAAK,MAAM,GAAE,EAAE,QAAQ,GAAE,CAAC,GAAE,CAAC,CAAC,MAAI,IAAE,EAAE,IAAK,SAASJ,IAAE;AAAC,qBAAO,EAAE,iCAAiCA,IAAE,CAAC;AAAA,YAAC,CAAE,GAAE,CAAC,GAAE,CAAC;AAAA,UAAG,KAAK;AAAE,gBAAE,CAAC,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,KAAG,IAAE,CAAC,GAAE,CAAC,GAAE,KAAG,CAAC,CAAC,EAAE,QAAS,SAASA,IAAE;AAAC,qBAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,iBAAC,IAAE,EAAE,OAAQ,SAAS,GAAE;AAAC,yBAAO,GAAGA,IAAE,CAAC,KAAG;AAAA,gBAAC,CAAE,GAAG,KAAKA,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE,GAAE,IAAE,GAAE,CAAC,GAAE,QAAQ,IAAI,EAAE,IAAK,SAASA,IAAE;AAAC,qBAAO,EAAE,aAAaA,IAAE,CAAC;AAAA,YAAC,CAAE,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,iBAAI,IAAE,EAAE,KAAK,GAAE,IAAE,CAAC,GAAE,KAAK,6BAA2B,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,EAAE,EAAE,UAAO,IAAE,EAAE,CAAC,OAAK,KAAK,2BAA2B,KAAK,KAAK,mBAAmB,GAAE,CAAC,CAAC,GAAE,SAAO,IAAE,GAAG,GAAE,CAAC,MAAI,EAAE,QAAS,SAASA,IAAE,GAAE;AAAC,kBAAII,KAAE,EAAE,IAAI,CAAC;AAAE,sBAAMA,OAAIJ,GAAE,OAAKI;AAAA,YAAE,CAAE,GAAE,IAAE,EAAE,CAAC,GAAE,EAAE,KAAK,EAAC,WAAU,GAAE,KAAI,EAAE,aAAa,oBAAmB,CAAC;AAAG,mBAAO,EAAE,QAAQ,GAAE,CAAC,GAAE,CAAC;AAAA,QAAC;AAAC,YAAI,GAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEJ,GAAE,UAAU,UAAQ,WAAU;AAAC,SAAK,SAAS,QAAQ,GAAE,KAAK,cAAc,QAAQ;AAAA,EAAC,GAAEA,GAAE,UAAU,QAAM,WAAU;AAAC,SAAK,SAAS,MAAM,GAAE,KAAK,6BAA2B;AAAA,EAAI,GAAEA,GAAE,UAAU,mCAAiC,SAASA,IAAE,GAAE;AAAC,WAAO,GAAG,GAAGA,IAAE,eAAc,YAAW,GAAE,EAAC,kCAAiC,GAAE,gCAA+B,GAAE,iCAAgC,EAAC,CAAC,GAAE,GAAE,EAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,eAAa,SAASA,IAAE,GAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAO,IAAE,GAAG,GAAE,IAAGA,EAAC,EAAE,aAAY,IAAE,CAAC,iBAAiB,EAAE,OAAO,KAAK,gBAAc,CAAC,wBAAuB,eAAc,gBAAe,gBAAe,gBAAe,cAAc,IAAE,CAAC,aAAa,CAAC,GAAE,IAAE,KAAK,cAAc,QAAQ,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,CAAC,GAAE,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,EAAE,KAAK,EAAE,CAAC,IAAE,OAAM,QAAQ,CAAC,GAAI,QAAQ,CAAC,GAAE,CAAC,GAAE,IAAI,KAAG,KAAK,gBAAc,CAAC,GAAE,KAAK,oCAAoC,CAAC,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAM,CAAC,GAAE,KAAK,uBAAuB,CAAC,CAAC;AAAA,UAAE,KAAK;AAAE,gBAAE,EAAE,KAAK,GAAE,EAAE,QAAM;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,SAASA,IAAE,GAAEI,IAAE;AAAC,yBAASA,OAAIA,KAAE,EAAC,gBAAe,MAAE;AAAG,uBAAQG,KAAE,CAAC,GAAEF,KAAE,GAAEH,KAAEF,IAAEK,KAAEH,GAAE,QAAOG,MAAI;AAAC,oBAAIC,KAAEJ,GAAEG,EAAC,GAAEF,KAAEG,GAAE,IAAE,KAAGa,KAAEb,GAAE,IAAE,KAAGc,KAAEhB,GAAE,iBAAe,IAAE,EAAE,UAAS2E,KAAE,KAAK,IAAI3D,EAAC,IAAEjB,KAAE,KAAK,IAAIiB,EAAC,IAAED,IAAE,IAAE,KAAK,IAAIC,EAAC,IAAEjB,KAAE,KAAK,IAAIiB,EAAC,IAAED;AAAE,gBAAA4D,KAAEA,KAAE,EAAE,QAAM,EAAE,SAAQ,IAAE,IAAE,EAAE,SAAO,EAAE;AAAQ,oBAAI,IAAEzE,GAAE,IAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAEA,EAAC;AAAE,kBAAE,IAAEyE,IAAE,EAAE,IAAE,GAAE,EAAE,IAAE,GAAExE,GAAE,KAAK,CAAC;AAAA,cAAC;AAAC,qBAAOA;AAAA,YAAC,EAAE,GAAEP,EAAC,GAAI,QAAQ,CAAC,GAAI,QAAQ,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,yBAAuB,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,eAAM,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,sCAAoC,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,aAAO,EAAE,MAAM,SAAS,GAAE;AAAC,gBAAO,EAAE,OAAM;AAAA,UAAC,KAAK;AAAE,mBAAM,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAGA,GAAE,CAAC,GAAE,EAAE,CAAC;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAEA,GAAE,UAAU,qBAAmB,SAASA,IAAE,GAAE;AAAC,WAAO,GAAG,GAAG,EAAEA,EAAC,GAAE,eAAc,YAAW,GAAE,EAAC,kCAAiC,IAAG,gCAA+B,KAAI,iCAAgC,EAAC,CAAC,GAAE,GAAE,EAAE;AAAA,EAAC,GAAEA;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE,GAAE,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,cAAO,EAAE,OAAM;AAAA,QAAC,KAAK;AAAE,iBAAO,IAAE,SAASA,IAAE;AAAC,gBAAG,QAAMA,GAAE,QAAO,EAAE,CAAC,GAAE,EAAE;AAAE,gBAAIC,KAAE,EAAE,CAAC,GAAED,EAAC;AAAE,mBAAOC,GAAE,UAAQ,QAAO,QAAMA,GAAE,aAAWA,GAAE,WAAS,GAAG,WAAU,QAAMA,GAAE,oBAAkBA,GAAE,kBAAgB,GAAG,kBAAiB,QAAMA,GAAE,qBAAmBA,GAAE,mBAAiBA,GAAE,kBAAgB,qFAAmF,gFAA+EA;AAAA,UAAC,EAAED,EAAC,GAAE,IAAE,YAAU,OAAO,EAAE,oBAAkB,EAAE,iBAAiB,QAAQ,mBAAmB,IAAE,IAAG,CAAC,GAAI,eAAe,EAAE,kBAAiB,EAAC,WAAU,EAAC,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,GAAG,GAAG,uBAAsB,EAAC,WAAU,SAAQ,UAAS,EAAE,UAAS,kBAAiB,EAAE,kBAAiB,SAAQ,EAAE,QAAO,CAAC,CAAC;AAAA,QAAE,KAAK;AAAE,iBAAO,IAAE,EAAE,KAAK,GAAE,CAAC,GAAE,IAAI,GAAG,GAAE,GAAE,EAAE,UAAS,EAAE,eAAe,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,GAAGA,IAAE,GAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,WAAU;AAAC,QAAI,GAAE;AAAE,WAAO,EAAE,MAAM,SAAS,GAAE;AAAC,UAAGA,OAAI,GAAG,mBAAkB;AAAC,YAAG,IAAE,QAAO,SAAO,IAAE,IAAG;AAAC,cAAG,WAAS,EAAE,QAAQ,QAAM,CAAC,GAAE,GAAG,CAAC,CAAC;AAAE,cAAG,gBAAc,EAAE,QAAQ,QAAM,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,cAAE,EAAE;AAAA,QAAO;AAAC,cAAM,IAAI,MAAM,4EAA0E,CAAC;AAAA,MAAC;AAAC,YAAM,IAAI,MAAMA,KAAE,iCAAiC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAE,oBAAkB;AAAmB,EAAE,OAAK,KAAG,CAAC,EAAE;AAAE,IAAI,KAAG,OAAO,OAAO,EAAC,WAAU,MAAK,2BAA0B,SAASA,IAAE;AAAC,MAAGA,OAAI,GAAG,kBAAkB,QAAO;AAAE,QAAM,IAAI,MAAM,WAASA,KAAE,oBAAoB;AAAC,GAAE,kBAAiB,SAASA,IAAE;AAAC,MAAGA,OAAI,GAAG,kBAAkB,QAAO;AAAE,QAAM,IAAI,MAAM,WAASA,KAAE,oBAAoB;AAAC,EAAC,CAAC;", "names": ["t", "e", "o", "u", "n", "i", "a", "r", "E", "S", "F", "C", "O", "_", "j", "k", "R", "I", "B", "s", "c", "L", "P", "z", "U", "N", "V", "H", "K", "W", "G", "X", "Y", "J", "q", "$", "Z", "Q", "tt", "et", "nt", "rt", "it", "ot", "at", "ut", "st", "ct", "ht", "lt", "ft", "dt", "pt", "gt", "vt", "mt", "yt", "wt", "bt", "xt", "Mt", "At", "Tt", "Et", "Ct", "Ft", "<PERSON>t", "St", "_t", "jt", "kt", "Rt", "Ut", "Nt", "It", "Lt", "Bt", "Dt", "Pt", "h", "zt", "Xt", "Vt", "Ht", "Kt", "Wt", "Gt", "Yt", "Jt", "qt", "$t", "Zt", "l", "f", "d", "p", "g", "v", "y", "w", "b", "x", "A", "T", "M"]}