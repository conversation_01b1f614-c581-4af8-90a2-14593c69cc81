import {
  require_isArrayLike
} from "./chunk-EI3ATIN2.js";
import {
  require_isObjectLike
} from "./chunk-MRQ46THD.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/isArrayLikeObject.js
var require_isArrayLikeObject = __commonJS({
  "node_modules/lodash/isArrayLikeObject.js"(exports, module) {
    var isArrayLike = require_isArrayLike();
    var isObjectLike = require_isObjectLike();
    function isArrayLikeObject(value) {
      return isObjectLike(value) && isArrayLike(value);
    }
    module.exports = isArrayLikeObject;
  }
});

export {
  require_isArrayLikeObject
};
//# sourceMappingURL=chunk-DOSSVMDF.js.map
