import { afterEach } from 'node:test';
import { beforeEach, describe, expect, test, vi } from 'vitest';

import useAdminFormManager from '@/composables/use-admin-form-manager';
import { getDefaultCreatorSchema } from '@/helpers/creator/schema-default';
import CreatorStep from '@/helpers/creator/steps/step';

const mocks = vi.hoisted(() => {
  return {
    useFormStore: vi.fn(),
    mockAbortController: {
      abort: vi.fn(),
      signal: { aborted: false },
    },
  };
});

vi.mock('@/composables/use-admin-report-manager', () => ({
  useFormStore: mocks.useFormStore.mockReturnValue({ save: vi.fn().mockResolvedValue(true) }),
}));

describe('use-admin-form-manager', () => {
  const adminFormManager = useAdminFormManager();
  const { saveSchema, formStore } = adminFormManager;
  vi.spyOn(adminFormManager, 'listForm').mockResolvedValue(true);

  let frontendSchema;

  beforeEach(() => {
    const schema = getDefaultCreatorSchema('test');
    schema.setSchema({} as any);
    const creatorStep = new CreatorStep();
    creatorStep.name = 'step_1';
    schema.addChild(creatorStep);
    const item1 = schema.itemFactory.createFlowItemFromSchema(
      {
        builder: { type: 'short_long_answer' },
        name: 'item_1',
      },
      { parent: schema.steps.at(-1) },
    );
    const item2 = schema.itemFactory.createFlowItemFromSchema(
      {
        builder: { type: 'short_long_answer' },
        name: 'item_2',
        visible: false,
      },
      { parent: schema.steps.at(-1) },
    );
    const item3 = schema.itemFactory.createFlowItemFromSchema(
      {
        builder: { type: 'short_long_answer' },
        name: 'item_3',
        visible: { _show: 'required' },
      },
      { parent: schema.steps.at(-1) },
    );
    schema.steps.at(-1).addChild(item1);
    schema.steps.at(-1).addChild(item2);
    schema.steps.at(-1).addChild(item3);
    frontendSchema = schema.schema;
  });

  describe('saveSchema', () => {
    const saveFormStoreSpy = vi.spyOn(formStore, 'save').mockResolvedValue({});
    const mockform = {
      frontend_schema: frontendSchema as Types.ISchema,
      report_schema: {} as Types.ISchemaReport,
      changed_key: { test_1: 'test_1_modified' },
    };

    describe('Should save success', () => {
      afterEach(() => {
        saveFormStoreSpy.mockReset();
      });

      test('Full payload', async () => {
        const slug = 'test-slug';
        const form = mockform;
        const expectedPayload = {
          ...form,
          formSlug: slug,
          frontend_schema: JSON.stringify(form.frontend_schema),
          report_schema: JSON.stringify(form.report_schema),
        };

        const res = await saveSchema(slug, form);
        expect(saveFormStoreSpy).toHaveBeenCalledWith(expectedPayload);
        expect(res).toBeTruthy();
      });

      test('No frontend schema', async () => {
        const slug = 'test-slug';
        const form = {
          ...mockform,
          frontend_schema: undefined,
        };
        const expectedPayload = {
          ...form,
          formSlug: slug,
          report_schema: JSON.stringify(form.report_schema),
        };
        const res = await saveSchema(slug, form);
        expect(saveFormStoreSpy).toHaveBeenCalledWith(expectedPayload);
        expect(res).toBeTruthy();
      });

      test('No report schema', async () => {
        const slug = 'test-slug';
        const form = {
          ...mockform,
          report_schema: undefined,
        };
        const expectedPayload = {
          ...form,
          formSlug: slug,
          frontend_schema: JSON.stringify(form.frontend_schema),
        };
        const res = await saveSchema(slug, form);
        expect(saveFormStoreSpy).toHaveBeenCalledWith(expectedPayload);
        expect(res).toBeTruthy();
      });

      test('No changed key (undefined)', async () => {
        const slug = 'test-slug';
        const form = {
          ...mockform,
          changed_key: undefined,
        };
        const expectedPayload = {
          ...form,
          formSlug: slug,
          frontend_schema: JSON.stringify(form.frontend_schema),
          report_schema: JSON.stringify(form.report_schema),
          changed_key: undefined,
        };

        const res = await saveSchema(slug, form);
        expect(saveFormStoreSpy).toHaveBeenCalledWith(expectedPayload);
        expect(res).toBeTruthy();
      });

      test('No changed key (empty object)', async () => {
        const slug = 'test-slug';
        const form = {
          ...mockform,
          changed_key: {},
        };
        const expectedPayload = {
          ...form,
          formSlug: slug,
          frontend_schema: JSON.stringify(form.frontend_schema),
          report_schema: JSON.stringify(form.report_schema),
          changed_key: undefined,
        };
        const res = await saveSchema(slug, form);
        expect(saveFormStoreSpy).toHaveBeenCalledWith(expectedPayload);
        expect(res).toBeTruthy();
      });
    });

    test('Should save failed', async () => {
      saveFormStoreSpy.mockRejectedValueOnce(new Error());
      const slug = 'test-slug';
      const form = mockform;
      const expectedPayload = {
        ...form,
        formSlug: slug,
        frontend_schema: JSON.stringify(form.frontend_schema),
        report_schema: JSON.stringify(form.report_schema),
      };

      const res = await saveSchema(slug, form);
      expect(saveFormStoreSpy).toHaveBeenCalledWith(expectedPayload);
      expect(res).toBeFalsy();
    });
  });

  describe('listForm cancellation', () => {
    let mockFormStore;
    let mockListRequest;
    let adminFormManager;

    beforeEach(() => {
      // Reset mocks
      vi.clearAllMocks();

      // Create a fresh mock for each test
      mockListRequest = vi.fn();
      mockFormStore = {
        list: mockListRequest,
        listRequestController: { value: mocks.mockAbortController },
      };

      // Mock the form store
      vi.doMock('@/store/form', () => ({
        useFormStore: () => mockFormStore,
      }));

      // Get a fresh instance of the composable
      adminFormManager = useAdminFormManager();
    });

    test('should cancel previous request when new listForm is called', async () => {
      // Setup: First request that will be cancelled
      let firstRequestResolve;
      const firstRequestPromise = new Promise(resolve => {
        firstRequestResolve = resolve;
      });
      mockListRequest.mockReturnValueOnce(firstRequestPromise);

      // Setup: Second request that will complete
      mockListRequest.mockResolvedValueOnce({ data: { data: [], total: 0 } });

      // Start first request
      const firstCall = adminFormManager.listForm({ page: 1 });

      // Start second request (should cancel first)
      const secondCall = adminFormManager.listForm({ page: 2 });

      // Verify abort was called on the controller
      expect(mocks.mockAbortController.abort).toHaveBeenCalled();

      // Complete the first request (should be ignored)
      firstRequestResolve({ data: { data: [], total: 0 } });

      // Both calls should resolve to the same result (from second request)
      const [firstResult, secondResult] = await Promise.all([firstCall, secondCall]);
      expect(firstResult).toBe(secondResult);
      expect(firstResult).toBe(true);
    });

    test('should handle AbortError gracefully', async () => {
      const abortError = new Error('Request aborted');
      abortError.name = 'AbortError';
      mockListRequest.mockRejectedValueOnce(abortError);

      const result = await adminFormManager.listForm();

      expect(result).toBe(false);
      // Should not throw the error
    });

    test('should handle CanceledError gracefully', async () => {
      const canceledError = new Error('Request canceled');
      canceledError.name = 'CanceledError';
      mockListRequest.mockRejectedValueOnce(canceledError);

      const result = await adminFormManager.listForm();

      expect(result).toBe(false);
      // Should not throw the error
    });

    test('should share promise between multiple concurrent calls', async () => {
      let requestResolve;
      const requestPromise = new Promise(resolve => {
        requestResolve = resolve;
      });
      mockListRequest.mockReturnValueOnce(requestPromise);

      // Start multiple concurrent requests
      const call1 = adminFormManager.listForm({ page: 1 });
      const call2 = adminFormManager.listForm({ page: 2 });
      const call3 = adminFormManager.listForm({ page: 3 });

      // Only the last call should trigger abort (for the previous ones)
      expect(mocks.mockAbortController.abort).toHaveBeenCalledTimes(2);

      // Complete the request
      requestResolve({ data: { data: [], total: 0 } });

      // All calls should resolve to the same result
      const [result1, result2, result3] = await Promise.all([call1, call2, call3]);
      expect(result1).toBe(result2);
      expect(result2).toBe(result3);
      expect(result1).toBe(true);
    });
  });
});
