import {
  loadGraphModel
} from "./chunk-HFYGB3K7.js";
import {
  Tensor,
  add,
  browser_exports,
  cast,
  clipByValue,
  concat,
  dispose,
  div,
  exp,
  expandDims,
  image,
  mul,
  reshape,
  sigmoid,
  slice,
  squeeze,
  sub,
  tensor1d,
  tensor2d,
  tidy,
  util_exports
} from "./chunk-VFSHU3A2.js";
import {
  require_face_detection
} from "./chunk-XPRVYLME.js";
import "./chunk-EXAI6KDO.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@tensorflow-models/face-detection/dist/face-detection.esm.js
var import_face_detection = __toESM(require_face_detection());
var b = function() {
  return b = Object.assign || function(e2) {
    for (var t, n = 1, i = arguments.length; n < i; n++) for (var o in t = arguments[n]) Object.prototype.hasOwnProperty.call(t, o) && (e2[o] = t[o]);
    return e2;
  }, b.apply(this, arguments);
};
function T(e2, t, n, i) {
  return new (n || (n = Promise))(function(o, r) {
    function a(e3) {
      try {
        h(i.next(e3));
      } catch (e4) {
        r(e4);
      }
    }
    function s(e3) {
      try {
        h(i.throw(e3));
      } catch (e4) {
        r(e4);
      }
    }
    function h(e3) {
      var t2;
      e3.done ? o(e3.value) : (t2 = e3.value, t2 instanceof n ? t2 : new n(function(e4) {
        e4(t2);
      })).then(a, s);
    }
    h((i = i.apply(e2, t || [])).next());
  });
}
function C(e2, t) {
  var n, i, o, r, a = { label: 0, sent: function() {
    if (1 & o[0]) throw o[1];
    return o[1];
  }, trys: [], ops: [] };
  return r = { next: s(0), throw: s(1), return: s(2) }, "function" == typeof Symbol && (r[Symbol.iterator] = function() {
    return this;
  }), r;
  function s(r2) {
    return function(s2) {
      return function(r3) {
        if (n) throw new TypeError("Generator is already executing.");
        for (; a; ) try {
          if (n = 1, i && (o = 2 & r3[0] ? i.return : r3[0] ? i.throw || ((o = i.return) && o.call(i), 0) : i.next) && !(o = o.call(i, r3[1])).done) return o;
          switch (i = 0, o && (r3 = [2 & r3[0], o.value]), r3[0]) {
            case 0:
            case 1:
              o = r3;
              break;
            case 4:
              return a.label++, { value: r3[1], done: false };
            case 5:
              a.label++, i = r3[1], r3 = [0];
              continue;
            case 7:
              r3 = a.ops.pop(), a.trys.pop();
              continue;
            default:
              if (!(o = a.trys, (o = o.length > 0 && o[o.length - 1]) || 6 !== r3[0] && 2 !== r3[0])) {
                a = 0;
                continue;
              }
              if (3 === r3[0] && (!o || r3[1] > o[0] && r3[1] < o[3])) {
                a.label = r3[1];
                break;
              }
              if (6 === r3[0] && a.label < o[1]) {
                a.label = o[1], o = r3;
                break;
              }
              if (o && a.label < o[2]) {
                a.label = o[2], a.ops.push(r3);
                break;
              }
              o[2] && a.ops.pop(), a.trys.pop();
              continue;
          }
          r3 = t.call(e2, a);
        } catch (e3) {
          r3 = [6, e3], i = 0;
        } finally {
          n = o = 0;
        }
        if (5 & r3[0]) throw r3[1];
        return { value: r3[0] ? r3[1] : void 0, done: true };
      }([r2, s2]);
    };
  }
}
var O = ["rightEye", "leftEye", "noseTip", "mouthCenter", "rightEarTragion", "leftEarTragion"];
var B = { modelType: "short", runtime: "mediapipe", maxFaces: 1 };
var z = function() {
  function i(t) {
    var n = this;
    this.width = 0, this.height = 0, this.selfieMode = false, this.faceDetectorSolution = new import_face_detection.FaceDetection({ locateFile: function(e2, n2) {
      if (t.solutionPath) {
        var i2 = t.solutionPath.replace(/\/+$/, "");
        return "".concat(i2, "/").concat(e2);
      }
      return "".concat(n2, "/").concat(e2);
    } }), this.faceDetectorSolution.setOptions({ selfieMode: this.selfieMode, model: t.modelType }), this.faceDetectorSolution.onResults(function(e2) {
      if (n.height = e2.image.height, n.width = e2.image.width, n.faces = [], null !== e2.detections) for (var t2 = 0, i2 = e2.detections; t2 < i2.length; t2++) {
        var o = i2[t2];
        n.faces.push(n.normalizedToAbsolute(o.landmarks, (r = o.boundingBox, a = void 0, s = void 0, h = void 0, a = r.xCenter - r.width / 2, s = a + r.width, h = r.yCenter - r.height / 2, { xMin: a, xMax: s, yMin: h, yMax: h + r.height, width: r.width, height: r.height })));
      }
      var r, a, s, h;
    });
  }
  return i.prototype.normalizedToAbsolute = function(e2, t) {
    var n = this;
    return { keypoints: e2.map(function(e3, t2) {
      return { x: e3.x * n.width, y: e3.y * n.height, name: O[t2] };
    }), box: { xMin: t.xMin * this.width, yMin: t.yMin * this.height, xMax: t.xMax * this.width, yMax: t.yMax * this.height, width: t.width * this.width, height: t.height * this.height } };
  }, i.prototype.estimateFaces = function(e2, i2) {
    return T(this, void 0, void 0, function() {
      var o, r;
      return C(this, function(a) {
        switch (a.label) {
          case 0:
            return i2 && i2.flipHorizontal && i2.flipHorizontal !== this.selfieMode && (this.selfieMode = i2.flipHorizontal, this.faceDetectorSolution.setOptions({ selfieMode: this.selfieMode })), e2 instanceof Tensor ? (r = ImageData.bind, [4, browser_exports.toPixels(e2)]) : [3, 2];
          case 1:
            return o = new (r.apply(ImageData, [void 0, a.sent(), e2.shape[1], e2.shape[0]]))(), [3, 3];
          case 2:
            o = e2, a.label = 3;
          case 3:
            return e2 = o, [4, this.faceDetectorSolution.send({ image: e2 })];
          case 4:
            return a.sent(), [2, this.faces];
        }
      });
    });
  }, i.prototype.dispose = function() {
    this.faceDetectorSolution.close();
  }, i.prototype.reset = function() {
    this.faceDetectorSolution.reset(), this.width = 0, this.height = 0, this.faces = null, this.selfieMode = false;
  }, i.prototype.initialize = function() {
    return this.faceDetectorSolution.initialize();
  }, i;
}();
function D(e2) {
  return T(this, void 0, void 0, function() {
    var t, n;
    return C(this, function(i) {
      switch (i.label) {
        case 0:
          return t = function(e3) {
            if (null == e3) return b({}, B);
            var t2 = b({}, e3);
            return t2.runtime = "mediapipe", null == t2.modelType && (t2.modelType = B.modelType), null == t2.maxFaces && (t2.maxFaces = B.maxFaces), t2;
          }(e2), [4, (n = new z(t)).initialize()];
        case 1:
          return i.sent(), [2, n];
      }
    });
  });
}
function A(e2, t, n, i) {
  var o = e2.width, r = e2.height, a = i ? -1 : 1, s = Math.cos(e2.rotation), h = Math.sin(e2.rotation), u = e2.xCenter, c = e2.yCenter, l = 1 / t, f = 1 / n, d = new Array(16);
  return d[0] = o * s * a * l, d[1] = -r * h * l, d[2] = 0, d[3] = (-0.5 * o * s * a + 0.5 * r * h + u) * l, d[4] = o * h * a * f, d[5] = r * s * f, d[6] = 0, d[7] = (-0.5 * r * s - 0.5 * o * h * a + c) * f, d[8] = 0, d[9] = 0, d[10] = o * l, d[11] = 0, d[12] = 0, d[13] = 0, d[14] = 0, d[15] = 1, function(e3) {
    if (16 !== e3.length) throw new Error("Array length must be 16 but got ".concat(e3.length));
    return [[e3[0], e3[1], e3[2], e3[3]], [e3[4], e3[5], e3[6], e3[7]], [e3[8], e3[9], e3[10], e3[11]], [e3[12], e3[13], e3[14], e3[15]]];
  }(d);
}
function F(e2) {
  return e2 instanceof Tensor ? { height: e2.shape[0], width: e2.shape[1] } : { height: e2.height, width: e2.width };
}
function E(e2) {
  return e2 instanceof Tensor ? e2 : browser_exports.fromPixels(e2);
}
function R(e2, t) {
  util_exports.assert(0 !== e2.width, function() {
    return "".concat(t, " width cannot be 0.");
  }), util_exports.assert(0 !== e2.height, function() {
    return "".concat(t, " height cannot be 0.");
  });
}
function L(e2, t) {
  var n = function(e3, t2, n2, i) {
    var o = t2 - e3, r = i - n2;
    if (0 === o) throw new Error("Original min and max are both ".concat(e3, ", range cannot be 0."));
    var a = r / o;
    return { scale: a, offset: n2 - e3 * a };
  }(0, 255, t[0], t[1]);
  return tidy(function() {
    return add(mul(e2, n.scale), n.offset);
  });
}
function K(e2, t, n) {
  var i = t.outputTensorSize, r = t.keepAspectRatio, a = t.borderMode, l = t.outputTensorFloatRange, f = F(e2), d = function(e3, t2) {
    return t2 ? { xCenter: t2.xCenter * e3.width, yCenter: t2.yCenter * e3.height, width: t2.width * e3.width, height: t2.height * e3.height, rotation: t2.rotation } : { xCenter: 0.5 * e3.width, yCenter: 0.5 * e3.height, width: e3.width, height: e3.height, rotation: 0 };
  }(f, n), p = function(e3, t2, n2) {
    if (void 0 === n2 && (n2 = false), !n2) return { top: 0, left: 0, right: 0, bottom: 0 };
    var i2 = t2.height, o = t2.width;
    R(t2, "targetSize"), R(e3, "roi");
    var r2, a2, s = i2 / o, h = e3.height / e3.width, u = 0, c = 0;
    return s > h ? (r2 = e3.width, a2 = e3.width * s, c = (1 - h / s) / 2) : (r2 = e3.height / s, a2 = e3.height, u = (1 - s / h) / 2), e3.width = r2, e3.height = a2, { top: c, left: u, right: u, bottom: c };
  }(d, i, r), m = A(d, f.width, f.height, false), x = tidy(function() {
    var t2 = E(e2), n2 = tensor2d(function(e3, t3, n3) {
      return R(n3, "inputResolution"), [1 / n3.width * e3[0][0] * t3.width, 1 / n3.height * e3[0][1] * t3.width, e3[0][3] * t3.width, 1 / n3.width * e3[1][0] * t3.height, 1 / n3.height * e3[1][1] * t3.height, e3[1][3] * t3.height, 0, 0];
    }(m, f, i), [1, 8]), o = "zero" === a ? "constant" : "nearest", r2 = image.transform(expandDims(cast(t2, "float32")), n2, "bilinear", o, 0, [i.height, i.width]);
    return null != l ? L(r2, l) : r2;
  });
  return { imageTensor: x, padding: p, transformationMatrix: m };
}
function k(e2) {
  null == e2.reduceBoxesInLowestLayer && (e2.reduceBoxesInLowestLayer = false), null == e2.interpolatedScaleAspectRatio && (e2.interpolatedScaleAspectRatio = 1), null == e2.fixedAnchorSize && (e2.fixedAnchorSize = false);
  for (var t = [], n = 0; n < e2.numLayers; ) {
    for (var i = [], o = [], r = [], a = [], s = n; s < e2.strides.length && e2.strides[s] === e2.strides[n]; ) {
      var h = P(e2.minScale, e2.maxScale, s, e2.strides.length);
      if (0 === s && e2.reduceBoxesInLowestLayer) r.push(1), r.push(2), r.push(0.5), a.push(0.1), a.push(h), a.push(h);
      else {
        for (var u = 0; u < e2.aspectRatios.length; ++u) r.push(e2.aspectRatios[u]), a.push(h);
        if (e2.interpolatedScaleAspectRatio > 0) {
          var c = s === e2.strides.length - 1 ? 1 : P(e2.minScale, e2.maxScale, s + 1, e2.strides.length);
          a.push(Math.sqrt(h * c)), r.push(e2.interpolatedScaleAspectRatio);
        }
      }
      s++;
    }
    for (var l = 0; l < r.length; ++l) {
      var f = Math.sqrt(r[l]);
      i.push(a[l] / f), o.push(a[l] * f);
    }
    var d = 0, p = 0;
    if (e2.featureMapHeight.length > 0) d = e2.featureMapHeight[n], p = e2.featureMapWidth[n];
    else {
      var m = e2.strides[n];
      d = Math.ceil(e2.inputSizeHeight / m), p = Math.ceil(e2.inputSizeWidth / m);
    }
    for (var x = 0; x < d; ++x) for (var g = 0; g < p; ++g) for (var y = 0; y < i.length; ++y) {
      var v = { xCenter: (g + e2.anchorOffsetX) / p, yCenter: (x + e2.anchorOffsetY) / d, width: 0, height: 0 };
      e2.fixedAnchorSize ? (v.width = 1, v.height = 1) : (v.width = o[y], v.height = i[y]), t.push(v);
    }
    n = s;
  }
  return t;
}
function P(e2, t, n, i) {
  return 1 === i ? 0.5 * (e2 + t) : e2 + (t - e2) * n / (i - 1);
}
function V(e2, t) {
  var n = t[0], i = t[1];
  return [n * e2[0] + i * e2[1] + e2[3], n * e2[4] + i * e2[5] + e2[7]];
}
function H(e2) {
  return tidy(function() {
    var t = function(e3) {
      return tidy(function() {
        return [slice(e3, [0, 0, 0], [1, -1, 1]), slice(e3, [0, 0, 1], [1, -1, -1])];
      });
    }(e2), n = t[0], i = t[1];
    return { boxes: squeeze(i), logits: squeeze(n) };
  });
}
function U(e2, t, n, i) {
  return T(this, void 0, void 0, function() {
    var i2, o, r, a, u;
    return C(this, function(c) {
      switch (c.label) {
        case 0:
          return e2.sort(function(e3, t2) {
            return Math.max.apply(Math, t2.score) - Math.max.apply(Math, e3.score);
          }), i2 = tensor2d(e2.map(function(e3) {
            return [e3.locationData.relativeBoundingBox.yMin, e3.locationData.relativeBoundingBox.xMin, e3.locationData.relativeBoundingBox.yMax, e3.locationData.relativeBoundingBox.xMax];
          })), o = tensor1d(e2.map(function(e3) {
            return e3.score[0];
          })), [4, image.nonMaxSuppressionAsync(i2, o, t, n)];
        case 1:
          return [4, (r = c.sent()).array()];
        case 2:
          return a = c.sent(), u = e2.filter(function(e3, t2) {
            return a.indexOf(t2) > -1;
          }), dispose([i2, o, r]), [2, u];
      }
    });
  });
}
function j(e2, t, n) {
  return T(this, void 0, void 0, function() {
    var i, s, h, u, c;
    return C(this, function(p) {
      switch (p.label) {
        case 0:
          return i = e2[0], s = e2[1], h = function(e3, t2, n2) {
            return tidy(function() {
              var i2, o, s2, h2;
              n2.reverseOutputOrder ? (o = squeeze(slice(e3, [0, n2.boxCoordOffset + 0], [-1, 1])), i2 = squeeze(slice(e3, [0, n2.boxCoordOffset + 1], [-1, 1])), h2 = squeeze(slice(e3, [0, n2.boxCoordOffset + 2], [-1, 1])), s2 = squeeze(slice(e3, [0, n2.boxCoordOffset + 3], [-1, 1]))) : (i2 = squeeze(slice(e3, [0, n2.boxCoordOffset + 0], [-1, 1])), o = squeeze(slice(e3, [0, n2.boxCoordOffset + 1], [-1, 1])), s2 = squeeze(slice(e3, [0, n2.boxCoordOffset + 2], [-1, 1])), h2 = squeeze(slice(e3, [0, n2.boxCoordOffset + 3], [-1, 1]))), o = add(mul(div(o, n2.xScale), t2.w), t2.x), i2 = add(mul(div(i2, n2.yScale), t2.h), t2.y), n2.applyExponentialOnBoxSize ? (s2 = mul(exp(div(s2, n2.hScale)), t2.h), h2 = mul(exp(div(h2, n2.wScale)), t2.w)) : (s2 = mul(div(s2, n2.hScale), t2.h), h2 = mul(div(h2, n2.wScale), t2.h));
              var u2 = sub(i2, div(s2, 2)), c2 = sub(o, div(h2, 2)), d = add(i2, div(s2, 2)), p2 = add(o, div(h2, 2)), w = concat([reshape(u2, [n2.numBoxes, 1]), reshape(c2, [n2.numBoxes, 1]), reshape(d, [n2.numBoxes, 1]), reshape(p2, [n2.numBoxes, 1])], 1);
              if (n2.numKeypoints) for (var M = 0; M < n2.numKeypoints; ++M) {
                var S = n2.keypointCoordOffset + M * n2.numValuesPerKeypoint, b2 = void 0, T2 = void 0;
                n2.reverseOutputOrder ? (b2 = squeeze(slice(e3, [0, S], [-1, 1])), T2 = squeeze(slice(e3, [0, S + 1], [-1, 1]))) : (T2 = squeeze(slice(e3, [0, S], [-1, 1])), b2 = squeeze(slice(e3, [0, S + 1], [-1, 1])));
                var C2 = add(mul(div(b2, n2.xScale), t2.w), t2.x), O2 = add(mul(div(T2, n2.yScale), t2.h), t2.y);
                w = concat([w, reshape(C2, [n2.numBoxes, 1]), reshape(O2, [n2.numBoxes, 1])], 1);
              }
              return w;
            });
          }(s, t, n), u = tidy(function() {
            var e3 = i;
            return n.sigmoidScore ? (null != n.scoreClippingThresh && (e3 = clipByValue(i, -n.scoreClippingThresh, n.scoreClippingThresh)), e3 = sigmoid(e3)) : e3;
          }), [4, I(h, u, n)];
        case 1:
          return c = p.sent(), dispose([h, u]), [2, c];
      }
    });
  });
}
function I(e2, t, n) {
  return T(this, void 0, void 0, function() {
    var i, o, r, a, s, h, u, c, l, f, d, p;
    return C(this, function(m) {
      switch (m.label) {
        case 0:
          return i = [], [4, e2.data()];
        case 1:
          return o = m.sent(), [4, t.data()];
        case 2:
          for (r = m.sent(), a = 0; a < n.numBoxes; ++a) if (!(null != n.minScoreThresh && r[a] < n.minScoreThresh || (s = a * n.numCoords, h = _(o[s + 0], o[s + 1], o[s + 2], o[s + 3], r[a], n.flipVertically, a), (u = h.locationData.relativeBoundingBox).width < 0 || u.height < 0))) {
            if (n.numKeypoints > 0) for ((c = h.locationData).relativeKeypoints = [], l = n.numKeypoints * n.numValuesPerKeypoint, f = 0; f < l; f += n.numValuesPerKeypoint) d = s + n.keypointCoordOffset + f, p = { x: o[d + 0], y: n.flipVertically ? 1 - o[d + 1] : o[d + 1] }, c.relativeKeypoints.push(p);
            i.push(h);
          }
          return [2, i];
      }
    });
  });
}
function _(e2, t, n, i, o, r, a) {
  return { score: [o], ind: a, locationData: { relativeBoundingBox: { xMin: t, yMin: r ? 1 - n : e2, xMax: i, yMax: r ? 1 - e2 : n, width: i - t, height: n - e2 } } };
}
var N = { reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: [], featureMapWidth: [], numLayers: 4, minScale: 0.1484375, maxScale: 0.75, inputSizeHeight: 128, inputSizeWidth: 128, anchorOffsetX: 0.5, anchorOffsetY: 0.5, strides: [8, 16, 16, 16], aspectRatios: [1], fixedAnchorSize: true };
var W = { reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 0, featureMapHeight: [], featureMapWidth: [], numLayers: 1, minScale: 0.1484375, maxScale: 0.75, inputSizeHeight: 192, inputSizeWidth: 192, anchorOffsetX: 0.5, anchorOffsetY: 0.5, strides: [4], aspectRatios: [1], fixedAnchorSize: true };
var X = { runtime: "tfjs", modelType: "short", maxFaces: 1, detectorModelUrl: "https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1" };
var Y = { applyExponentialOnBoxSize: false, flipVertically: false, ignoreClasses: [], numClasses: 1, numBoxes: 896, numCoords: 16, boxCoordOffset: 0, keypointCoordOffset: 4, numKeypoints: 6, numValuesPerKeypoint: 2, sigmoidScore: true, scoreClippingThresh: 100, reverseOutputOrder: true, xScale: 128, yScale: 128, hScale: 128, wScale: 128, minScoreThresh: 0.5 };
var q = { applyExponentialOnBoxSize: false, flipVertically: false, ignoreClasses: [], numClasses: 1, numBoxes: 2304, numCoords: 16, boxCoordOffset: 0, keypointCoordOffset: 4, numKeypoints: 6, numValuesPerKeypoint: 2, sigmoidScore: true, scoreClippingThresh: 100, reverseOutputOrder: true, xScale: 192, yScale: 192, hScale: 192, wScale: 192, minScoreThresh: 0.6 };
var G = 0.3;
var $ = { outputTensorSize: { width: 128, height: 128 }, keepAspectRatio: true, outputTensorFloatRange: [-1, 1], borderMode: "zero" };
var J = { outputTensorSize: { width: 192, height: 192 }, keepAspectRatio: true, outputTensorFloatRange: [-1, 1], borderMode: "zero" };
var Q;
var Z = function() {
  function e2(e3, t, n) {
    this.detectorModel = t, this.maxFaces = n, "full" === e3 ? (this.imageToTensorConfig = J, this.tensorsToDetectionConfig = q, this.anchors = k(W)) : (this.imageToTensorConfig = $, this.tensorsToDetectionConfig = Y, this.anchors = k(N));
    var i = tensor1d(this.anchors.map(function(e4) {
      return e4.width;
    })), o = tensor1d(this.anchors.map(function(e4) {
      return e4.height;
    })), r = tensor1d(this.anchors.map(function(e4) {
      return e4.xCenter;
    })), a = tensor1d(this.anchors.map(function(e4) {
      return e4.yCenter;
    }));
    this.anchorTensor = { x: r, y: a, w: i, h: o };
  }
  return e2.prototype.dispose = function() {
    this.detectorModel.dispose(), dispose([this.anchorTensor.x, this.anchorTensor.y, this.anchorTensor.w, this.anchorTensor.h]);
  }, e2.prototype.reset = function() {
  }, e2.prototype.detectFaces = function(e3, t) {
    return void 0 === t && (t = false), T(this, void 0, void 0, function() {
      var n, i, r, a, s, l, p, m, x, g, y;
      return C(this, function(v) {
        switch (v.label) {
          case 0:
            return null == e3 ? (this.reset(), [2, []]) : (n = tidy(function() {
              var n2 = cast(E(e3), "float32");
              if (t) {
                n2 = squeeze(image.flipLeftRight(expandDims(n2, 0)), [0]);
              }
              return n2;
            }), i = K(n, this.imageToTensorConfig), r = i.imageTensor, a = i.transformationMatrix, s = this.detectorModel.execute(r, "Identity:0"), l = H(s), p = l.boxes, [4, j([m = l.logits, p], this.anchorTensor, this.tensorsToDetectionConfig)]);
          case 1:
            return 0 === (x = v.sent()).length ? (dispose([n, r, s, m, p]), [2, x]) : [4, U(x, this.maxFaces, G)];
          case 2:
            return g = v.sent(), y = function(e4, t2) {
              void 0 === e4 && (e4 = []);
              var n2, i2 = (n2 = t2, [].concat.apply([], n2));
              return e4.forEach(function(e5) {
                var t3 = e5.locationData;
                t3.relativeKeypoints.forEach(function(e6) {
                  var t4 = V(i2, [e6.x, e6.y]), n4 = t4[0], o2 = t4[1];
                  e6.x = n4, e6.y = o2;
                });
                var n3 = t3.relativeBoundingBox, o = Number.MAX_VALUE, r2 = Number.MAX_VALUE, a2 = Number.MIN_VALUE, s2 = Number.MIN_VALUE;
                [[n3.xMin, n3.yMin], [n3.xMin + n3.width, n3.yMin], [n3.xMin + n3.width, n3.yMin + n3.height], [n3.xMin, n3.yMin + n3.height]].forEach(function(e6) {
                  var t4 = V(i2, e6), n4 = t4[0], h = t4[1];
                  o = Math.min(o, n4), a2 = Math.max(a2, n4), r2 = Math.min(r2, h), s2 = Math.max(s2, h);
                }), t3.relativeBoundingBox = { xMin: o, xMax: a2, yMin: r2, yMax: s2, width: a2 - o, height: s2 - r2 };
              }), e4;
            }(g, a), dispose([n, r, s, m, p]), [2, y];
        }
      });
    });
  }, e2.prototype.estimateFaces = function(e3, t) {
    return T(this, void 0, void 0, function() {
      var n, i;
      return C(this, function(o) {
        return n = F(e3), i = !!t && t.flipHorizontal, [2, this.detectFaces(e3, i).then(function(e4) {
          return e4.map(function(e5) {
            for (var t2 = e5.locationData.relativeKeypoints.map(function(e6, t3) {
              return b(b({}, e6), { x: e6.x * n.width, y: e6.y * n.height, name: O[t3] });
            }), i2 = e5.locationData.relativeBoundingBox, o2 = 0, r = ["width", "xMax", "xMin"]; o2 < r.length; o2++) {
              i2[r[o2]] *= n.width;
            }
            for (var a = 0, s = ["height", "yMax", "yMin"]; a < s.length; a++) {
              i2[s[a]] *= n.height;
            }
            return { keypoints: t2, box: i2 };
          });
        })];
      });
    });
  }, e2;
}();
function ee(e2) {
  return T(this, void 0, void 0, function() {
    var t, n, i;
    return C(this, function(o) {
      switch (o.label) {
        case 0:
          return t = function(e3) {
            if (null == e3) return b({}, X);
            var t2 = b({}, e3);
            null == t2.modelType && (t2.modelType = X.modelType), null == t2.maxFaces && (t2.maxFaces = X.maxFaces), null == t2.detectorModelUrl && ("full" === t2.modelType ? t2.detectorModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/face_detection/full/1" : t2.detectorModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1");
            return t2;
          }(e2), n = "string" == typeof t.detectorModelUrl && t.detectorModelUrl.indexOf("https://tfhub.dev") > -1, [4, loadGraphModel(t.detectorModelUrl, { fromTFHub: n })];
        case 1:
          return i = o.sent(), [2, new Z(t.modelType, i, t.maxFaces)];
      }
    });
  });
}
function te(e2, t) {
  return T(this, void 0, void 0, function() {
    var n, i;
    return C(this, function(o) {
      if (e2 === Q.MediaPipeFaceDetector) {
        if (i = void 0, null != (n = t)) {
          if ("tfjs" === n.runtime) return [2, ee(n)];
          if ("mediapipe" === n.runtime) return [2, D(n)];
          i = n.runtime;
        }
        throw new Error("Expect modelConfig.runtime to be either 'tfjs' " + "or 'mediapipe', but got ".concat(i));
      }
      throw new Error("".concat(e2, " is not a supported model name."));
    });
  });
}
!function(e2) {
  e2.MediaPipeFaceDetector = "MediaPipeFaceDetector";
}(Q || (Q = {}));
export {
  z as MediaPipeFaceDetectorMediaPipe,
  Z as MediaPipeFaceDetectorTfjs,
  Q as SupportedModels,
  te as createDetector
};
/*! Bundled license information:

@tensorflow-models/face-detection/dist/face-detection.esm.js:
  (**
      * @license
      * Copyright 2024 Google LLC. All Rights Reserved.
      * Licensed under the Apache License, Version 2.0 (the "License");
      * you may not use this file except in compliance with the License.
      * You may obtain a copy of the License at
      *
      * http://www.apache.org/licenses/LICENSE-2.0
      *
      * Unless required by applicable law or agreed to in writing, software
      * distributed under the License is distributed on an "AS IS" BASIS,
      * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      * See the License for the specific language governing permissions and
      * limitations under the License.
      * =============================================================================
      *)
*/
//# sourceMappingURL=@tensorflow-models_face-detection.js.map
