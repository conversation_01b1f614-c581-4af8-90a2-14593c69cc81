import { isPocketDevice } from '@helpers/helpers/user-agent';

export const useSwitchAppWatcher = () => {
  const isWatcherActive = ref(false);
  let callbackFn = () => {};

  function initSwitchAppWatcher(callback: () => void) {
    if (isWatcherActive.value) {
      return;
    }

    const isMobile =
      isPocketDevice() &&
      !navigator.webdriver &&
      (navigator as any).userAgentData?.mobile !== false;

    callbackFn = () => {
      if (!document.hidden && isMobile) {
        callback();
      }
    };

    document.addEventListener('visibilitychange', callbackFn);
    isWatcherActive.value = true;
  }

  function deinitSwitchAppWatcher() {
    if (!isWatcherActive.value) {
      return;
    }

    document.removeEventListener('visibilitychange', callbackFn);
    isWatcherActive.value = false;
  }

  return {
    isWatcherActive,
    initSwitchAppWatcher,
    deinitSwitchAppWatcher,
  };
};
