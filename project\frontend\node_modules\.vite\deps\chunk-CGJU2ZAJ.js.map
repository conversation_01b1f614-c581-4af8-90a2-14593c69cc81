{"version": 3, "sources": ["../../vue-demi/lib/index.mjs"], "sourcesContent": ["import Vue from 'vue'\nimport { getCurrentInstance } from 'vue'\n\nvar isVue2 = true\nvar isVue3 = false\nvar Vue2 = Vue\nvar warn = Vue.util.warn\n\nfunction install() {}\n\n// createApp polyfill\nexport function createApp(rootComponent, rootProps) {\n  var vm\n  var provide = {}\n  var app = {\n    config: Vue.config,\n    use: Vue.use.bind(Vue),\n    mixin: Vue.mixin.bind(Vue),\n    component: Vue.component.bind(Vue),\n    provide: function (key, value) {\n      provide[key] = value\n      return this\n    },\n    directive: function (name, dir) {\n      if (dir) {\n        Vue.directive(name, dir)\n        return app\n      } else {\n        return Vue.directive(name)\n      }\n    },\n    mount: function (el, hydrating) {\n      if (!vm) {\n        vm = new Vue(Object.assign({ propsData: rootProps }, rootComponent, { provide: Object.assign(provide, rootComponent.provide) }))\n        vm.$mount(el, hydrating)\n        return vm\n      } else {\n        return vm\n      }\n    },\n    unmount: function () {\n      if (vm) {\n        vm.$destroy()\n        vm = undefined\n      }\n    },\n  }\n  return app\n}\n\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n  warn\n}\n\n// Vue 3 components mock\nfunction createMockComponent(name) {\n  return {\n    setup() {\n      throw new Error('[vue-demi] ' + name + ' is not supported in Vue 2. It\\'s provided to avoid compiler errors.')\n    }\n  }\n}\nexport var Fragment = /*#__PURE__*/ createMockComponent('Fragment')\nexport var Transition = /*#__PURE__*/ createMockComponent('Transition')\nexport var TransitionGroup = /*#__PURE__*/ createMockComponent('TransitionGroup')\nexport var Teleport = /*#__PURE__*/ createMockComponent('Teleport')\nexport var Suspense = /*#__PURE__*/ createMockComponent('Suspense')\nexport var KeepAlive = /*#__PURE__*/ createMockComponent('KeepAlive')\n\nexport * from 'vue'\n\n// Not implemented https://github.com/vuejs/core/pull/8111, falls back to getCurrentInstance()\nexport function hasInjectionContext() {\n  return !!getCurrentInstance()\n}\n"], "mappings": ";;;;;;;AAAA;AACA;AAyEA;AAvEA,IAAI,SAAS;AACb,IAAI,SAAS;AAEb,IAAI,OAAO,IAAI,KAAK;AAsDpB,SAAS,oBAAoB,MAAM;AACjC,SAAO;AAAA,IACL,QAAQ;AACN,YAAM,IAAI,MAAM,gBAAgB,OAAO,qEAAsE;AAAA,IAC/G;AAAA,EACF;AACF;AACO,IAAI,WAAyB,oBAAoB,UAAU;AAC3D,IAAI,aAA2B,oBAAoB,YAAY;AAC/D,IAAI,kBAAgC,oBAAoB,iBAAiB;AACzE,IAAI,WAAyB,oBAAoB,UAAU;AAC3D,IAAI,WAAyB,oBAAoB,UAAU;AAC3D,IAAI,YAA0B,oBAAoB,WAAW;AAK7D,SAAS,sBAAsB;AACpC,SAAO,CAAC,CAAC,mBAAmB;AAC9B;", "names": []}