{"version": "3.2.4", "results": [["dynamic-form:tests/unit/components/form-input-items/smart-uploader/SmartUploader.spec.ts", {"duration": 1846.7098000000005, "failed": false}], ["dynamic-form:tests/unit/composables/use-admin-dashboard-table-common.spec.ts", {"duration": 1032.7196999999996, "failed": false}], ["dynamic-form:tests/unit/composables/use-camera/use-camera.spec.ts", {"duration": 5308.5463, "failed": false}], ["dynamic-form:tests/unit/helpers/creator/items/consent.spec.ts", {"duration": 237.1143000000011, "failed": false}], [":tests/unit/helpers/form-control/index.spec.ts", {"duration": 1042.2842, "failed": false}], [":tests/unit/helpers/form-data/index.spec.ts", {"duration": 449.52269999999953, "failed": false}], [":tests/unit/helpers/models/device-special-flags.spec.ts", {"duration": 1083.7624000000005, "failed": false}], [":tests/unit/items/documents/EkycDocPassport/EkycDocPassport.spec.ts", {"duration": 6545.833900000001, "failed": false}], [":tests/unit/store/application.spec.ts", {"duration": 372.4418999999998, "failed": false}], [":tests/unit/composables/use-authentication-manager.spec.ts", {"duration": 624.5495000000001, "failed": false}], [":tests/unit/composables/use-flow-publisher.spec.ts", {"duration": 1648.0403000000006, "failed": false}], [":tests/unit/composables/decision-flow/use-decision-flow-manager.spec.ts", {"duration": 328.5190999999995, "failed": false}], [":tests/unit/composables/use-admin-dashboard-table-common.spec.ts", {"duration": 244.0587999999998, "failed": false}], [":tests/unit/helpers/creator/items/consent.spec.ts", {"duration": 355.53269999999975, "failed": false}], [":tests/unit/composables/use-admin-api-token.spec.ts", {"duration": 746.8054999999999, "failed": false}], [":tests/unit/components/form-input-items/smart-uploader/SmartUploader.spec.ts", {"duration": 2141.6859000000004, "failed": false}], [":tests/unit/composables/use-popup-unsupported-browser.spec.ts", {"duration": 248.3492000000001, "failed": false}], [":tests/unit/components/form-report-items/aml/use-aml-report.spec.ts", {"duration": 200.98490000000038, "failed": false}], [":tests/unit/components/form-report-items/integration/use-integration-report.spec.ts", {"duration": 212.39360000000033, "failed": false}], [":tests/unit/composables/use-common-address-item.spec.ts", {"duration": 940.8351000000002, "failed": false}], [":tests/unit/helpers/creator/items/smart-uploader.spec.ts", {"duration": 204.93089999999984, "failed": false}], [":tests/unit/store/dynamic-form-instance.spec.ts", {"duration": 215.95310000000018, "failed": false}], [":tests/unit/helpers/creator/dashboard-settings.spec.ts", {"duration": 1318.4730999999992, "failed": false}], [":tests/unit/helpers/creator/items/full-name.spec.ts", {"duration": 361.3519000000001, "failed": false}], [":tests/unit/composables/use-camera/use-camera.spec.ts", {"duration": 5306.307000000001, "failed": false}], [":tests/unit/composables/use-dynamic-event.spec.ts", {"duration": 540.7761, "failed": false}], [":tests/unit/helpers/creator/items/items.spec.ts", {"duration": 270.1713999999997, "failed": false}], [":tests/unit/views/admin/decision-flow/use-flow-decision-result-view.spec.ts", {"duration": 192.7444000000005, "failed": false}], [":tests/unit/composables/use-admin-form-manager.spec.ts", {"duration": 1218.5419000000002, "failed": false}], [":tests/unit/helpers/creator/steps/validator/business_id.spec.ts", {"duration": 419.43380000000025, "failed": false}], [":tests/unit/router/router-workspace-checker.spec.ts", {"duration": 447.5743, "failed": false}], [":tests/unit/helpers/creator/schema.spec.ts", {"duration": 266.6773000000003, "failed": false}], [":tests/unit/helpers/creator/report-schema.spec.ts", {"duration": 2058.9035999999996, "failed": false}], [":tests/unit/components/form-input-items/offline-uploader/OfflineUploader.spec.ts", {"duration": 1406.8545999999997, "failed": false}], [":tests/unit/components/form-report-items/business/use-business-report.spec.ts", {"duration": 166.50480000000016, "failed": false}], [":tests/unit/helpers/locale-utils.spec.ts", {"duration": 180.7641000000001, "failed": false}], [":tests/unit/store/permission.spec.ts", {"duration": 173.59119999999984, "failed": false}], [":tests/unit/helpers/creator/steps/ekyc/items/document.spec.ts", {"duration": 877.2330000000002, "failed": false}], [":tests/unit/helpers/creator/items/business-information.spec.ts", {"duration": 272.77300000000014, "failed": false}], [":tests/unit/views/admin/outflows/FlowOutflowTasksDetailView/FlowOutflowTasksDetailView.spec.ts", {"duration": 290.5554000000002, "failed": false}], [":tests/unit/composables/use-common-form-layout.spec.ts", {"duration": 189.56929999999966, "failed": false}], [":tests/unit/helpers/address/thai-address-database.spec.ts", {"duration": 1397.4046, "failed": false}], [":tests/unit/helpers/creator/steps/document-next.spec.ts", {"duration": 1752.5870999999988, "failed": false}], [":tests/unit/helpers/creator/item-collector.spec.ts", {"duration": 158.41630000000032, "failed": false}], [":tests/unit/helpers/address/use-address-translation.spec.ts", {"duration": 218.71759999999995, "failed": false}], [":tests/unit/composables/dynamic-form-settings/use-dynamic-form-settings-base.spec.ts", {"duration": 225.95430000000033, "failed": false}], [":tests/unit/helpers/creator/items/address.spec.ts", {"duration": 309.2404999999999, "failed": false}], [":tests/unit/helpers/creator/steps/ekyc/document-next.spec.ts", {"duration": 228.22830000000022, "failed": false}], [":tests/unit/helpers/schema-processor.spec.ts", {"duration": 202.95800000000008, "failed": false}], [":tests/unit/helpers/creator/steps/document.spec.ts", {"duration": 1863.6090000000004, "failed": false}], [":tests/unit/helpers/user-agent.spec.ts", {"duration": 216.43190000000027, "failed": false}], [":tests/unit/components/InputText/InputText.spec.ts", {"duration": 204.83839999999964, "failed": false}], [":tests/unit/helpers/creator/items/choice.spec.ts", {"duration": 227.2132999999999, "failed": false}], [":tests/unit/components/admin/dashboard/ReportItemCollection.spec.ts", {"duration": 223.59519999999975, "failed": false}], [":tests/unit/composables/use-popup-cross-device.spec.ts", {"duration": 229.94840000000022, "failed": false}], [":tests/unit/helpers/creator/steps/validator/different.spec.ts", {"duration": 178.6320999999998, "failed": false}], [":tests/unit/helpers/creator/steps/validator/different_but_x.spec.ts", {"duration": 215.9924000000001, "failed": false}], [":tests/unit/helpers/creator/steps/validator/same_but_x.spec.ts", {"duration": 214.10030000000006, "failed": false}], [":tests/unit/helpers/creator/steps/liveness-next.spec.ts", {"duration": 269.55459999999994, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required.spec.ts", {"duration": 209.8353000000002, "failed": false}], [":tests/unit/helpers/creator/items/name-prefix.spec.ts", {"duration": 131.5899999999997, "failed": false}], [":tests/unit/helpers/creator/steps/validator/size.spec.ts", {"duration": 188.35080000000016, "failed": false}], [":tests/unit/helpers/creator/steps/validator/integer.spec.ts", {"duration": 192.61220000000003, "failed": false}], [":tests/unit/helpers/creator/steps/validator/boolean.spec.ts", {"duration": 213.63910000000033, "failed": false}], [":tests/unit/store/dynamic-form.spec.ts", {"duration": 191.32150000000001, "failed": false}], [":tests/unit/store/step-visibility.spec.ts", {"duration": 195.70759999999996, "failed": false}], [":tests/unit/helpers/creator/steps/validator/max.spec.ts", {"duration": 222.4079999999999, "failed": false}], [":tests/unit/helpers/creator/steps/validator/min.spec.ts", {"duration": 222.11979999999994, "failed": false}], [":tests/unit/helpers/creator/steps/validator/is.spec.ts", {"duration": 229.05499999999984, "failed": false}], [":tests/unit/helpers/creator/steps/liveness.spec.ts", {"duration": 228.3847999999998, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_without.spec.ts", {"duration": 192.92679999999973, "failed": false}], [":tests/unit/helpers/creator/steps/validator/numeric.spec.ts", {"duration": 194.17819999999983, "failed": false}], [":tests/unit/helpers/creator/steps/validator/url.spec.ts", {"duration": 196.41840000000002, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_with_all.spec.ts", {"duration": 204.9991, "failed": false}], [":tests/unit/helpers/creator/steps/validator/datetime_after.spec.ts", {"duration": 189.88160000000016, "failed": false}], [":tests/unit/helpers/creator/steps/validator/after_or_equal.spec.ts", {"duration": 199.05409999999983, "failed": false}], [":tests/unit/helpers/creator/steps/validator/regex.spec.ts", {"duration": 191.86380000000008, "failed": false}], [":tests/unit/helpers/creator/steps/validator/hex.spec.ts", {"duration": 215.4303, "failed": false}], [":tests/unit/helpers/creator/steps/validator/alpha_dash.spec.ts", {"duration": 208.05880000000025, "failed": false}], [":tests/unit/helpers/router-utils.spec.ts", {"duration": 190.81050000000005, "failed": false}], [":tests/unit/helpers/creator/steps/validator/accepted.spec.ts", {"duration": 194.1650999999997, "failed": false}], [":tests/unit/helpers/creator/steps/validator/in.spec.ts", {"duration": 209.4139, "failed": false}], [":tests/unit/helpers/creator/steps/validator/pass_checksum.spec.ts", {"duration": 193.86270000000013, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_with.spec.ts", {"duration": 178.5695999999998, "failed": false}], [":tests/unit/helpers/creator/steps/validator/before_or_equal.spec.ts", {"duration": 208.7125000000001, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_if.spec.ts", {"duration": 213.3062, "failed": false}], [":tests/unit/helpers/creator/steps/validator/email.spec.ts", {"duration": 188.86929999999984, "failed": false}], [":tests/unit/helpers/creator/steps/validator/alpha_num.spec.ts", {"duration": 211.49590000000035, "failed": false}], [":tests/unit/helpers/creator/items/dynamic-result.spec.ts", {"duration": 243.5972999999999, "failed": false}], [":tests/unit/helpers/creator/steps/validator/confirmed.spec.ts", {"duration": 180.86569999999983, "failed": false}], [":tests/unit/helpers/creator/steps/validator/array.spec.ts", {"duration": 179.74329999999964, "failed": false}], [":tests/unit/helpers/creator/steps/validator/alpha.spec.ts", {"duration": 203.54410000000007, "failed": false}], [":tests/unit/helpers/creator/steps/validator/not_in.spec.ts", {"duration": 212.80330000000004, "failed": false}], [":tests/unit/helpers/creator/steps/validator/between_number.spec.ts", {"duration": 218.40110000000004, "failed": false}], [":tests/unit/composables/use-popup.spec.ts", {"duration": 163.9802000000002, "failed": false}], [":tests/unit/helpers/creator/steps/validator/same.spec.ts", {"duration": 200.53769999999986, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_unless.spec.ts", {"duration": 194.57330000000002, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_without_all.spec.ts", {"duration": 186.48669999999993, "failed": false}], [":tests/unit/helpers/creator/steps/validator/string.spec.ts", {"duration": 215.73950000000013, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_if_condition.spec.ts", {"duration": 195.3056999999999, "failed": false}], [":tests/unit/helpers/creator/steps/validator/required_if_between.spec.ts", {"duration": 230.33009999999967, "failed": false}], [":tests/unit/helpers/creator/schema-default.spec.ts", {"duration": 1492.0883999999987, "failed": false}], [":tests/unit/helpers/creator/steps/validator/before_now.spec.ts", {"duration": 206.69889999999987, "failed": false}], [":tests/unit/helpers/creator/steps/validator/after_now.spec.ts", {"duration": 194.79759999999987, "failed": false}], [":tests/unit/helpers/creator/steps/ekyc/document.spec.ts", {"duration": 365.70759999999973, "failed": false}], [":tests/unit/helpers/validator.spec.ts", {"duration": 207.27430000000004, "failed": false}], [":tests/unit/helpers/creator/steps/validator/datetime_before.spec.ts", {"duration": 144.30069999999978, "failed": false}], [":tests/unit/helpers/image-utils.spec.ts", {"duration": 192.4749999999999, "failed": false}], [":tests/unit/helpers/creator/steps/validator/date_time.spec.ts", {"duration": 127.16789999999992, "failed": false}], [":tests/unit/helpers/creator/steps/pages/landing.spec.ts", {"duration": 241.60699999999997, "failed": false}], [":tests/unit/helpers/creator/steps/pages/embed.spec.ts", {"duration": 230.36459999999988, "failed": false}], [":tests/unit/helpers/creator/steps/pages/thanks.spec.ts", {"duration": 232.0987, "failed": false}], ["dynamic-form:tests/unit/helpers/models/device-special-flags.spec.ts", {"duration": 308.49420000007376, "failed": false}], ["dynamic-form:tests/unit/helpers/user-agent.spec.ts", {"duration": 0, "failed": false}], ["dynamic-form:tests/unit/composables/use-admin-form-manager.spec.ts", {"duration": 269.80830000014976, "failed": true}]]}