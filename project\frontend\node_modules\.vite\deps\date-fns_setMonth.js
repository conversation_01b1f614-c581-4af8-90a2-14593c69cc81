import {
  getDaysInMonth
} from "./chunk-SPZ2S4WF.js";
import {
  toInteger
} from "./chunk-SQMTRHET.js";
import {
  toDate
} from "./chunk-HUOGXHLH.js";
import {
  requiredArgs
} from "./chunk-Z43A42SM.js";
import "./chunk-PLDDJCW6.js";

// node_modules/date-fns/esm/setMonth/index.js
function setMonth(dirtyDate, dirtyMonth) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var month = toInteger(dirtyMonth);
  var year = date.getFullYear();
  var day = date.getDate();
  var dateWithDesiredMonth = /* @__PURE__ */ new Date(0);
  dateWithDesiredMonth.setFullYear(year, month, 15);
  dateWithDesiredMonth.setHours(0, 0, 0, 0);
  var daysInMonth = getDaysInMonth(dateWithDesiredMonth);
  date.setMonth(month, Math.min(day, daysInMonth));
  return date;
}
export {
  setMonth as default
};
//# sourceMappingURL=date-fns_setMonth.js.map
