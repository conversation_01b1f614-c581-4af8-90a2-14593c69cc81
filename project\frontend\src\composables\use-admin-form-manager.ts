import axios from 'axios';
import isEmpty from 'lodash/isEmpty';
import { storeToRefs } from 'pinia';

import { useSchemaConfigFormSettings } from '@core/composables/dynamic-form-settings/use-schema-config-form-settings';
import { useAlert } from '@core/composables/use-alert';
import i18n from '@core/plugins/i18n';

import { generateReportSchema } from '@/helpers/creator/report-schema';
import { getDefaultSchemaConfig } from '@/helpers/creator/schema-config-default';
import { getDefaultCreatorSchema } from '@/helpers/creator/schema-default';
import pinia from '@/plugins/pinia';
import { useFormStore } from '@/store/form';

import { useAdminApplicationManager } from './use-admin-application-manager';

const formLoadingState = reactive({
  list: false,
  create: false,
  rename: false,
  delete: false,
  apply: false,
  save_schema: false,
});
const isAnyLoading = computed(() => Object.values(formLoadingState).some(loading => loading));

const ordering = ref('-updated_at');
const searchText = ref('');

const filterType = ref<'all' | 'highlight' | 'active' | 'inactive' | string>('all');
const filters = reactive({
  highlight: 0,
  is_active: 0,
});

// Shared promise for listForm to ensure all callers wait for the same request
let currentListFormPromise: Promise<boolean> | null = null;
function syncFilterTypeToFilters() {
  filters.highlight = undefined;
  filters.is_active = undefined;
  switch (filterType.value) {
    case 'active':
      filters.is_active = 1;
      break;
    case 'inactive':
      filters.is_active = 0;
      break;
    case 'highlight':
      filters.highlight = 1;
      break;
    default:
      break;
  }
}
export const useAdminFormManager = () => {
  const alert = useAlert();
  const formStore = useFormStore(pinia);
  const { forms, infos } = storeToRefs(formStore);
  const { filteredStatus, filteredDateField, timeSpan, customTimeStart, customTimeEnd } =
    useAdminApplicationManager();

  function handleFormApiError(err: Error, defaultAlertMsg: string = 'Error') {
    console.error(err);
    if (axios.isAxiosError(err)) {
      if (err.response) {
        console.warn('Response error', err.response);
        const status = err?.response?.status;
        const data = err?.response?.data as Record<string, string>;

        // Flow Limit
        if (status === 426) {
          console.warn('Flow Limit Error', status, data);
          const alertMsg = data.detail ?? defaultAlertMsg;
          return alert.addAlert(alertMsg);
        }
      }
    }
    return alert.addAlert(defaultAlertMsg);
  }

  async function listForm(payload = {}) {
    let isSuccess = false;
    try {
      formLoadingState.list = true;
      syncFilterTypeToFilters();
      await formStore.list({
        ordering: ordering.value,
        search: searchText.value,
        ...filters,
        ...payload,
      });

      isSuccess = true;
    } catch (err) {
      console.error(err);
    }
    formLoadingState.list = false;
    return isSuccess;
  }

  async function createForm(name: string, slug?: string, customItems: string[] | false = false) {
    try {
      formLoadingState.create = true;

      // Generate
      const schema = getDefaultCreatorSchema(slug, customItems).schema;
      const schemaConfig = getDefaultSchemaConfig();
      const reportSchema = generateReportSchema(schema, { schemaConfig });

      // Create
      const newForm = await formStore.create({
        name,
        slug,
        frontend_schema: schema,
        report_schema: reportSchema,
      });

      // Save settings
      const { schemaConfigSetting, saveSchemaConfig } = useSchemaConfigFormSettings(newForm.slug);
      schemaConfigSetting.value = schemaConfig;
      await saveSchemaConfig();

      // Refresh
      await listForm();
      return newForm;
    } catch (err) {
      handleFormApiError(err, 'Create Error');
      return false;
    } finally {
      formLoadingState.create = false;
    }
  }

  async function renameForm(formSlug: string, name: string) {
    try {
      formLoadingState.rename = true;
      await formStore.save({ name, formSlug });
      await listForm();
      return true;
    } catch (err) {
      handleFormApiError(err, 'Rename Error');
      return false;
    } finally {
      formLoadingState.rename = false;
    }
  }

  async function duplicateForm(name: string, slug: string, sourceSlug: string) {
    try {
      formLoadingState.create = true;
      const result = await formStore.clone({ name, slug, source_slug: sourceSlug });
      return result;
    } catch (err) {
      handleFormApiError(err, 'Duplicate Error');
      return false;
    } finally {
      formLoadingState.create = false;
    }
  }

  async function deleteForm(slug: string) {
    try {
      formLoadingState.delete = true;
      await formStore.remove(slug);
      await listForm();
    } catch (err) {
      handleFormApiError(err, 'Delete Error');
    }
    formLoadingState.delete = false;
  }

  async function applyForm(slug: string) {
    try {
      formLoadingState.apply = true;
      const data = await formStore.apply(slug);
      const { form_url: formUrl } = data;
      window.open(formUrl, '_blank');
    } catch (err) {
      handleFormApiError(err, 'Apply Error');
    }
    formLoadingState.apply = false;
  }

  async function setHighlight(formSlug: string, highlight: boolean) {
    try {
      await formStore.setHighlight({ formSlug, highlight });
      return true;
    } catch (err) {
      handleFormApiError(err, 'Set highlight Error');
      return false;
    }
  }

  async function setActive(formSlug: string, active: boolean, update = true) {
    try {
      const data = await formStore.setActive({ formSlug, active, update });
      return data;
    } catch (err) {
      handleFormApiError(err, 'Set active Error');
      return false;
    }
  }

  async function getInfo(slug: string, lang = i18n.locale) {
    try {
      formLoadingState.list = true;
      return await formStore.getInfo({ formSlug: slug, locale: lang });
    } catch (err) {
      handleFormApiError(err, 'Error getting info');
      return null;
    } finally {
      formLoadingState.list = false;
    }
  }

  function getSchema(slug: string, lang = i18n.locale): Types.ISchema {
    return formStore.info(slug, lang).frontend_schema;
  }

  function getReportSchema(slug: string, lang = i18n.locale): Types.ISchema {
    return formStore.info(slug, lang).report_schema;
  }

  async function saveSchema(
    formSlug: string,
    // eslint-disable-next-line camelcase
    form: {
      frontend_schema?: Types.ISchema;
      report_schema?: Types.ISchemaReport;
      changed_key?: Record<string, string>;
    },
  ) {
    try {
      formLoadingState.save_schema = true;
      const payload = { formSlug } as any;
      if (form.frontend_schema) {
        payload.frontend_schema = JSON.stringify(form.frontend_schema);
      }
      if (form.report_schema) {
        payload.report_schema = JSON.stringify(form.report_schema);
      }
      if (form?.changed_key && !isEmpty(form.changed_key)) {
        payload.changed_key = form?.changed_key;
      }

      await formStore.save(payload);
      await listForm();
      return true;
    } catch (err) {
      console.error(err);
      const errorMessage = err?.response?.data?.frontend_schema;
      if (typeof errorMessage === 'string') {
        alert.addAlert(errorMessage);
      } else if (typeof errorMessage === 'object') {
        const errorMessageArray = Object.values(errorMessage);
        alert.addAlert(errorMessageArray[0]);
      } else {
        alert.addAlert('Save schema Error');
      }
      return false;
    } finally {
      formLoadingState.save_schema = false;
    }
  }

  async function listCustomStatus(formSlug: string): Promise<Types.CustomStatusOption[]> {
    try {
      const statusList = await formStore.listCustomStatus({
        formSlug,
      });
      const choices = statusList.map(status => ({
        label: status.value,
        value: status.value,
        id: status.id,
        choices: status.options.map(opt => ({
          label: opt.value,
          value: opt.value,
        })),
      }));

      return choices;
    } catch (err) {
      console.error(err);
    }
    return [];
  }

  return {
    forms,
    infos,
    formLoadingState,
    isAnyLoading,

    searchText,
    filteredStatus,
    filteredDateField,
    timeSpan,
    customTimeStart,
    customTimeEnd,
    ordering,
    filterType,
    filters,

    listForm,
    createForm,
    renameForm,
    duplicateForm,
    deleteForm,
    applyForm,
    setHighlight,
    setActive,

    getInfo,
    getSchema,
    getReportSchema,
    saveSchema,
    listCustomStatus,

    // For testing
    formStore,
  };
};

export default useAdminFormManager;
