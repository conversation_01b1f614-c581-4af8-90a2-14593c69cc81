{"version": 3, "sources": ["../../vue-recaptcha/dist/vue-recaptcha.es.js"], "sourcesContent": ["function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nvar defer = function defer() {\n  var state = false; // Resolved or not\n\n  var callbacks = [];\n\n  var resolve = function resolve(val) {\n    if (state) {\n      return;\n    }\n\n    state = true;\n\n    for (var i = 0, len = callbacks.length; i < len; i++) {\n      callbacks[i](val);\n    }\n  };\n\n  var then = function then(cb) {\n    if (!state) {\n      callbacks.push(cb);\n      return;\n    }\n\n    cb();\n  };\n\n  var deferred = {\n    resolved: function resolved() {\n      return state;\n    },\n    resolve: resolve,\n    promise: {\n      then: then\n    }\n  };\n  return deferred;\n};\n\nvar ownProp = Object.prototype.hasOwnProperty;\nfunction createRecaptcha() {\n  var deferred = defer();\n  return {\n    notify: function notify() {\n      deferred.resolve();\n    },\n    wait: function wait() {\n      return deferred.promise;\n    },\n    render: function render(ele, options, cb) {\n      this.wait().then(function () {\n        cb(window.grecaptcha.render(ele, options));\n      });\n    },\n    reset: function reset(widgetId) {\n      if (typeof widgetId === 'undefined') {\n        return;\n      }\n\n      this.assertLoaded();\n      this.wait().then(function () {\n        return window.grecaptcha.reset(widgetId);\n      });\n    },\n    execute: function execute(widgetId) {\n      if (typeof widgetId === 'undefined') {\n        return;\n      }\n\n      this.assertLoaded();\n      this.wait().then(function () {\n        return window.grecaptcha.execute(widgetId);\n      });\n    },\n    checkRecaptchaLoad: function checkRecaptchaLoad() {\n      if (ownProp.call(window, 'grecaptcha') && ownProp.call(window.grecaptcha, 'render')) {\n        this.notify();\n      }\n    },\n    assertLoaded: function assertLoaded() {\n      if (!deferred.resolved()) {\n        throw new Error('ReCAPTCHA has not been loaded');\n      }\n    }\n  };\n}\nvar recaptcha = createRecaptcha();\n\nif (typeof window !== 'undefined') {\n  window.vueRecaptchaApiLoaded = recaptcha.notify;\n}\n\nvar VueRecaptcha = {\n  name: 'VueRecaptcha',\n  props: {\n    sitekey: {\n      type: String,\n      required: true\n    },\n    theme: {\n      type: String\n    },\n    badge: {\n      type: String\n    },\n    type: {\n      type: String\n    },\n    size: {\n      type: String\n    },\n    tabindex: {\n      type: String\n    },\n    loadRecaptchaScript: {\n      type: Boolean,\n      \"default\": false\n    },\n    recaptchaScriptId: {\n      type: String,\n      \"default\": '__RECAPTCHA_SCRIPT'\n    },\n    recaptchaHost: {\n      type: String,\n      \"default\": 'www.google.com'\n    },\n    language: {\n      type: String,\n      \"default\": ''\n    }\n  },\n  beforeMount: function beforeMount() {\n    if (this.loadRecaptchaScript) {\n      if (!document.getElementById(this.recaptchaScriptId)) {\n        // Note: vueRecaptchaApiLoaded load callback name is per the latest documentation\n        var script = document.createElement('script');\n        script.id = this.recaptchaScriptId;\n        script.src = \"https://\" + this.recaptchaHost + \"/recaptcha/api.js?onload=vueRecaptchaApiLoaded&render=explicit&hl=\" + this.language;\n        script.async = true;\n        script.defer = true;\n        document.head.appendChild(script);\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    recaptcha.checkRecaptchaLoad();\n\n    var opts = _extends({}, this.$props, {\n      callback: this.emitVerify,\n      'expired-callback': this.emitExpired,\n      'error-callback': this.emitError\n    });\n\n    var container = this.$slots[\"default\"] ? this.$el.children[0] : this.$el;\n    recaptcha.render(container, opts, function (id) {\n      _this.$widgetId = id;\n\n      _this.$emit('render', id);\n    });\n  },\n  methods: {\n    reset: function reset() {\n      recaptcha.reset(this.$widgetId);\n    },\n    execute: function execute() {\n      recaptcha.execute(this.$widgetId);\n    },\n    emitVerify: function emitVerify(response) {\n      this.$emit('verify', response);\n    },\n    emitExpired: function emitExpired() {\n      this.$emit('expired');\n    },\n    emitError: function emitError() {\n      this.$emit('error');\n    }\n  },\n  render: function render(h) {\n    return h('div', {}, this.$slots[\"default\"]);\n  }\n};\n\nexport default VueRecaptcha;\n"], "mappings": ";;;AAAA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,IAAI,QAAQ,SAASA,SAAQ;AAC3B,MAAI,QAAQ;AAEZ,MAAI,YAAY,CAAC;AAEjB,MAAI,UAAU,SAASC,SAAQ,KAAK;AAClC,QAAI,OAAO;AACT;AAAA,IACF;AAEA,YAAQ;AAER,aAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK;AACpD,gBAAU,CAAC,EAAE,GAAG;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,OAAO,SAASC,MAAK,IAAI;AAC3B,QAAI,CAAC,OAAO;AACV,gBAAU,KAAK,EAAE;AACjB;AAAA,IACF;AAEA,OAAG;AAAA,EACL;AAEA,MAAI,WAAW;AAAA,IACb,UAAU,SAAS,WAAW;AAC5B,aAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA,SAAS;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,UAAU,OAAO,UAAU;AAC/B,SAAS,kBAAkB;AACzB,MAAI,WAAW,MAAM;AACrB,SAAO;AAAA,IACL,QAAQ,SAAS,SAAS;AACxB,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA,MAAM,SAAS,OAAO;AACpB,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,QAAQ,SAASC,QAAO,KAAK,SAAS,IAAI;AACxC,WAAK,KAAK,EAAE,KAAK,WAAY;AAC3B,WAAG,OAAO,WAAW,OAAO,KAAK,OAAO,CAAC;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,IACA,OAAO,SAASC,OAAM,UAAU;AAC9B,UAAI,OAAO,aAAa,aAAa;AACnC;AAAA,MACF;AAEA,WAAK,aAAa;AAClB,WAAK,KAAK,EAAE,KAAK,WAAY;AAC3B,eAAO,OAAO,WAAW,MAAM,QAAQ;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,IACA,SAAS,SAASC,SAAQ,UAAU;AAClC,UAAI,OAAO,aAAa,aAAa;AACnC;AAAA,MACF;AAEA,WAAK,aAAa;AAClB,WAAK,KAAK,EAAE,KAAK,WAAY;AAC3B,eAAO,OAAO,WAAW,QAAQ,QAAQ;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,IACA,oBAAoB,SAAS,qBAAqB;AAChD,UAAI,QAAQ,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK,OAAO,YAAY,QAAQ,GAAG;AACnF,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,IACA,cAAc,SAAS,eAAe;AACpC,UAAI,CAAC,SAAS,SAAS,GAAG;AACxB,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,YAAY,gBAAgB;AAEhC,IAAI,OAAO,WAAW,aAAa;AACjC,SAAO,wBAAwB,UAAU;AAC3C;AAEA,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,IACR;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,IACR;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,qBAAqB;AAAA,MACnB,MAAM;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,mBAAmB;AAAA,MACjB,MAAM;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,KAAK,qBAAqB;AAC5B,UAAI,CAAC,SAAS,eAAe,KAAK,iBAAiB,GAAG;AAEpD,YAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,eAAO,KAAK,KAAK;AACjB,eAAO,MAAM,aAAa,KAAK,gBAAgB,uEAAuE,KAAK;AAC3H,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,iBAAS,KAAK,YAAY,MAAM;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,QAAI,QAAQ;AAEZ,cAAU,mBAAmB;AAE7B,QAAI,OAAO,SAAS,CAAC,GAAG,KAAK,QAAQ;AAAA,MACnC,UAAU,KAAK;AAAA,MACf,oBAAoB,KAAK;AAAA,MACzB,kBAAkB,KAAK;AAAA,IACzB,CAAC;AAED,QAAI,YAAY,KAAK,OAAO,SAAS,IAAI,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK;AACrE,cAAU,OAAO,WAAW,MAAM,SAAU,IAAI;AAC9C,YAAM,YAAY;AAElB,YAAM,MAAM,UAAU,EAAE;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA,IACP,OAAO,SAAS,QAAQ;AACtB,gBAAU,MAAM,KAAK,SAAS;AAAA,IAChC;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,gBAAU,QAAQ,KAAK,SAAS;AAAA,IAClC;AAAA,IACA,YAAY,SAAS,WAAW,UAAU;AACxC,WAAK,MAAM,UAAU,QAAQ;AAAA,IAC/B;AAAA,IACA,aAAa,SAAS,cAAc;AAClC,WAAK,MAAM,SAAS;AAAA,IACtB;AAAA,IACA,WAAW,SAAS,YAAY;AAC9B,WAAK,MAAM,OAAO;AAAA,IACpB;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,OAAO,GAAG;AACzB,WAAO,EAAE,OAAO,CAAC,GAAG,KAAK,OAAO,SAAS,CAAC;AAAA,EAC5C;AACF;AAEA,IAAO,2BAAQ;", "names": ["defer", "resolve", "then", "render", "reset", "execute"]}