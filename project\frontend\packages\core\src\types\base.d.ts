/// <reference path="../../../helpers/src/types/dynamic-event.d.ts" />

declare module Types {
  interface ConfigPartForSchema {
    styling?: Record<string, string | number>;
    configs?: Record<string, any>;
  }

  interface ISchemaMixin {
    path: Record<string, string>;
    pool: Record<
      string,
      {
        type: 'constant' | 'formula' | 'portal';
        value: any;
      }
    >;
  }

  interface CommonPartForSchema extends ConfigPartForSchema {
    name?: string;
    type?: string;
    save_path?: (string | number)[];
    hide_report?: boolean;
    hide_print?: boolean;
    visible?: Record<string, any> | boolean;
    visible_flag_invert?: boolean; // invert the visible rules
    visible_target?: 'all' | 'only_asterisk'; // point the target that "visible" will be apply.
    mixins?: ISchemaMixin;
    builder?: {
      type?: string;
      backups?: Record<string, any>;
      report_generator?: {
        name?: string;
      };
    };
    __disable_generator?: boolean;
  }

  type ElementProps = {
    disabled?: boolean;
    autocomplete?: 'on' | 'off' | string;
    notrack?: boolean;
  };

  interface ISchemaItemEvent {
    name?: string;
    event_type: string;
    guards?: Types.DEGuard[];
    actions: Types.DEAction[];
  }

  type ShowTimeConfig = {
    from?: string;
    to?: string;
    timezone?: string;
  };

  type ValueTypeHtml = string;
}
