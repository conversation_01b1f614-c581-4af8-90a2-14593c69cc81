{"version": 3, "sources": ["../../lodash/_baseMap.js"], "sourcesContent": ["var baseEach = require('./_baseEach'),\n    isArrayLike = require('./isArrayLike');\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n      result = isArrayLike(collection) ? Array(collection.length) : [];\n\n  baseEach(collection, function(value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\n\nmodule.exports = baseMap;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,cAAc;AAUlB,aAAS,QAAQ,YAAY,UAAU;AACrC,UAAI,QAAQ,IACR,SAAS,YAAY,UAAU,IAAI,MAAM,WAAW,MAAM,IAAI,CAAC;AAEnE,eAAS,YAAY,SAAS,OAAO,KAAKA,aAAY;AACpD,eAAO,EAAE,KAAK,IAAI,SAAS,OAAO,KAAKA,WAAU;AAAA,MACnD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["collection"]}