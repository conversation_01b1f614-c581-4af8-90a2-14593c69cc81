{"version": 3, "sources": ["../../prosemirror-view/dist/index.js"], "sourcesContent": ["import { TextSelection, NodeSelection, AllSelection, Selection } from 'prosemirror-state';\nimport { DOMSerializer, Fragment, Mark, Slice, DOMParser } from 'prosemirror-model';\nimport { dropPoint } from 'prosemirror-transform';\n\nconst domIndex = function (node) {\n    for (var index = 0;; index++) {\n        node = node.previousSibling;\n        if (!node)\n            return index;\n    }\n};\nconst parentNode = function (node) {\n    let parent = node.assignedSlot || node.parentNode;\n    return parent && parent.nodeType == 11 ? parent.host : parent;\n};\nlet reusedRange = null;\n// Note that this will always return the same range, because DOM range\n// objects are every expensive, and keep slowing down subsequent DOM\n// updates, for some reason.\nconst textRange = function (node, from, to) {\n    let range = reusedRange || (reusedRange = document.createRange());\n    range.setEnd(node, to == null ? node.nodeValue.length : to);\n    range.setStart(node, from || 0);\n    return range;\n};\nconst clearReusedRange = function () {\n    reusedRange = null;\n};\n// Scans forward and backward through DOM positions equivalent to the\n// given one to see if the two are in the same place (i.e. after a\n// text node vs at the end of that text node)\nconst isEquivalentPosition = function (node, off, targetNode, targetOff) {\n    return targetNode && (scanFor(node, off, targetNode, targetOff, -1) ||\n        scanFor(node, off, targetNode, targetOff, 1));\n};\nconst atomElements = /^(img|br|input|textarea|hr)$/i;\nfunction scanFor(node, off, targetNode, targetOff, dir) {\n    var _a;\n    for (;;) {\n        if (node == targetNode && off == targetOff)\n            return true;\n        if (off == (dir < 0 ? 0 : nodeSize(node))) {\n            let parent = node.parentNode;\n            if (!parent || parent.nodeType != 1 || hasBlockDesc(node) || atomElements.test(node.nodeName) ||\n                node.contentEditable == \"false\")\n                return false;\n            off = domIndex(node) + (dir < 0 ? 0 : 1);\n            node = parent;\n        }\n        else if (node.nodeType == 1) {\n            let child = node.childNodes[off + (dir < 0 ? -1 : 0)];\n            if (child.nodeType == 1 && child.contentEditable == \"false\") {\n                if ((_a = child.pmViewDesc) === null || _a === void 0 ? void 0 : _a.ignoreForSelection)\n                    off += dir;\n                else\n                    return false;\n            }\n            else {\n                node = child;\n                off = dir < 0 ? nodeSize(node) : 0;\n            }\n        }\n        else {\n            return false;\n        }\n    }\n}\nfunction nodeSize(node) {\n    return node.nodeType == 3 ? node.nodeValue.length : node.childNodes.length;\n}\nfunction textNodeBefore$1(node, offset) {\n    for (;;) {\n        if (node.nodeType == 3 && offset)\n            return node;\n        if (node.nodeType == 1 && offset > 0) {\n            if (node.contentEditable == \"false\")\n                return null;\n            node = node.childNodes[offset - 1];\n            offset = nodeSize(node);\n        }\n        else if (node.parentNode && !hasBlockDesc(node)) {\n            offset = domIndex(node);\n            node = node.parentNode;\n        }\n        else {\n            return null;\n        }\n    }\n}\nfunction textNodeAfter$1(node, offset) {\n    for (;;) {\n        if (node.nodeType == 3 && offset < node.nodeValue.length)\n            return node;\n        if (node.nodeType == 1 && offset < node.childNodes.length) {\n            if (node.contentEditable == \"false\")\n                return null;\n            node = node.childNodes[offset];\n            offset = 0;\n        }\n        else if (node.parentNode && !hasBlockDesc(node)) {\n            offset = domIndex(node) + 1;\n            node = node.parentNode;\n        }\n        else {\n            return null;\n        }\n    }\n}\nfunction isOnEdge(node, offset, parent) {\n    for (let atStart = offset == 0, atEnd = offset == nodeSize(node); atStart || atEnd;) {\n        if (node == parent)\n            return true;\n        let index = domIndex(node);\n        node = node.parentNode;\n        if (!node)\n            return false;\n        atStart = atStart && index == 0;\n        atEnd = atEnd && index == nodeSize(node);\n    }\n}\nfunction hasBlockDesc(dom) {\n    let desc;\n    for (let cur = dom; cur; cur = cur.parentNode)\n        if (desc = cur.pmViewDesc)\n            break;\n    return desc && desc.node && desc.node.isBlock && (desc.dom == dom || desc.contentDOM == dom);\n}\n// Work around Chrome issue https://bugs.chromium.org/p/chromium/issues/detail?id=447523\n// (isCollapsed inappropriately returns true in shadow dom)\nconst selectionCollapsed = function (domSel) {\n    return domSel.focusNode && isEquivalentPosition(domSel.focusNode, domSel.focusOffset, domSel.anchorNode, domSel.anchorOffset);\n};\nfunction keyEvent(keyCode, key) {\n    let event = document.createEvent(\"Event\");\n    event.initEvent(\"keydown\", true, true);\n    event.keyCode = keyCode;\n    event.key = event.code = key;\n    return event;\n}\nfunction deepActiveElement(doc) {\n    let elt = doc.activeElement;\n    while (elt && elt.shadowRoot)\n        elt = elt.shadowRoot.activeElement;\n    return elt;\n}\nfunction caretFromPoint(doc, x, y) {\n    if (doc.caretPositionFromPoint) {\n        try { // Firefox throws for this call in hard-to-predict circumstances (#994)\n            let pos = doc.caretPositionFromPoint(x, y);\n            // Clip the offset, because Chrome will return a text offset\n            // into <input> nodes, which can't be treated as a regular DOM\n            // offset\n            if (pos)\n                return { node: pos.offsetNode, offset: Math.min(nodeSize(pos.offsetNode), pos.offset) };\n        }\n        catch (_) { }\n    }\n    if (doc.caretRangeFromPoint) {\n        let range = doc.caretRangeFromPoint(x, y);\n        if (range)\n            return { node: range.startContainer, offset: Math.min(nodeSize(range.startContainer), range.startOffset) };\n    }\n}\n\nconst nav = typeof navigator != \"undefined\" ? navigator : null;\nconst doc = typeof document != \"undefined\" ? document : null;\nconst agent = (nav && nav.userAgent) || \"\";\nconst ie_edge = /Edge\\/(\\d+)/.exec(agent);\nconst ie_upto10 = /MSIE \\d/.exec(agent);\nconst ie_11up = /Trident\\/(?:[7-9]|\\d{2,})\\..*rv:(\\d+)/.exec(agent);\nconst ie = !!(ie_upto10 || ie_11up || ie_edge);\nconst ie_version = ie_upto10 ? document.documentMode : ie_11up ? +ie_11up[1] : ie_edge ? +ie_edge[1] : 0;\nconst gecko = !ie && /gecko\\/(\\d+)/i.test(agent);\ngecko && +(/Firefox\\/(\\d+)/.exec(agent) || [0, 0])[1];\nconst _chrome = !ie && /Chrome\\/(\\d+)/.exec(agent);\nconst chrome = !!_chrome;\nconst chrome_version = _chrome ? +_chrome[1] : 0;\nconst safari = !ie && !!nav && /Apple Computer/.test(nav.vendor);\n// Is true for both iOS and iPadOS for convenience\nconst ios = safari && (/Mobile\\/\\w+/.test(agent) || !!nav && nav.maxTouchPoints > 2);\nconst mac = ios || (nav ? /Mac/.test(nav.platform) : false);\nconst windows = nav ? /Win/.test(nav.platform) : false;\nconst android = /Android \\d/.test(agent);\nconst webkit = !!doc && \"webkitFontSmoothing\" in doc.documentElement.style;\nconst webkit_version = webkit ? +(/\\bAppleWebKit\\/(\\d+)/.exec(navigator.userAgent) || [0, 0])[1] : 0;\n\nfunction windowRect(doc) {\n    let vp = doc.defaultView && doc.defaultView.visualViewport;\n    if (vp)\n        return {\n            left: 0, right: vp.width,\n            top: 0, bottom: vp.height\n        };\n    return { left: 0, right: doc.documentElement.clientWidth,\n        top: 0, bottom: doc.documentElement.clientHeight };\n}\nfunction getSide(value, side) {\n    return typeof value == \"number\" ? value : value[side];\n}\nfunction clientRect(node) {\n    let rect = node.getBoundingClientRect();\n    // Adjust for elements with style \"transform: scale()\"\n    let scaleX = (rect.width / node.offsetWidth) || 1;\n    let scaleY = (rect.height / node.offsetHeight) || 1;\n    // Make sure scrollbar width isn't included in the rectangle\n    return { left: rect.left, right: rect.left + node.clientWidth * scaleX,\n        top: rect.top, bottom: rect.top + node.clientHeight * scaleY };\n}\nfunction scrollRectIntoView(view, rect, startDOM) {\n    let scrollThreshold = view.someProp(\"scrollThreshold\") || 0, scrollMargin = view.someProp(\"scrollMargin\") || 5;\n    let doc = view.dom.ownerDocument;\n    for (let parent = startDOM || view.dom;;) {\n        if (!parent)\n            break;\n        if (parent.nodeType != 1) {\n            parent = parentNode(parent);\n            continue;\n        }\n        let elt = parent;\n        let atTop = elt == doc.body;\n        let bounding = atTop ? windowRect(doc) : clientRect(elt);\n        let moveX = 0, moveY = 0;\n        if (rect.top < bounding.top + getSide(scrollThreshold, \"top\"))\n            moveY = -(bounding.top - rect.top + getSide(scrollMargin, \"top\"));\n        else if (rect.bottom > bounding.bottom - getSide(scrollThreshold, \"bottom\"))\n            moveY = rect.bottom - rect.top > bounding.bottom - bounding.top\n                ? rect.top + getSide(scrollMargin, \"top\") - bounding.top\n                : rect.bottom - bounding.bottom + getSide(scrollMargin, \"bottom\");\n        if (rect.left < bounding.left + getSide(scrollThreshold, \"left\"))\n            moveX = -(bounding.left - rect.left + getSide(scrollMargin, \"left\"));\n        else if (rect.right > bounding.right - getSide(scrollThreshold, \"right\"))\n            moveX = rect.right - bounding.right + getSide(scrollMargin, \"right\");\n        if (moveX || moveY) {\n            if (atTop) {\n                doc.defaultView.scrollBy(moveX, moveY);\n            }\n            else {\n                let startX = elt.scrollLeft, startY = elt.scrollTop;\n                if (moveY)\n                    elt.scrollTop += moveY;\n                if (moveX)\n                    elt.scrollLeft += moveX;\n                let dX = elt.scrollLeft - startX, dY = elt.scrollTop - startY;\n                rect = { left: rect.left - dX, top: rect.top - dY, right: rect.right - dX, bottom: rect.bottom - dY };\n            }\n        }\n        let pos = atTop ? \"fixed\" : getComputedStyle(parent).position;\n        if (/^(fixed|sticky)$/.test(pos))\n            break;\n        parent = pos == \"absolute\" ? parent.offsetParent : parentNode(parent);\n    }\n}\n// Store the scroll position of the editor's parent nodes, along with\n// the top position of an element near the top of the editor, which\n// will be used to make sure the visible viewport remains stable even\n// when the size of the content above changes.\nfunction storeScrollPos(view) {\n    let rect = view.dom.getBoundingClientRect(), startY = Math.max(0, rect.top);\n    let refDOM, refTop;\n    for (let x = (rect.left + rect.right) / 2, y = startY + 1; y < Math.min(innerHeight, rect.bottom); y += 5) {\n        let dom = view.root.elementFromPoint(x, y);\n        if (!dom || dom == view.dom || !view.dom.contains(dom))\n            continue;\n        let localRect = dom.getBoundingClientRect();\n        if (localRect.top >= startY - 20) {\n            refDOM = dom;\n            refTop = localRect.top;\n            break;\n        }\n    }\n    return { refDOM: refDOM, refTop: refTop, stack: scrollStack(view.dom) };\n}\nfunction scrollStack(dom) {\n    let stack = [], doc = dom.ownerDocument;\n    for (let cur = dom; cur; cur = parentNode(cur)) {\n        stack.push({ dom: cur, top: cur.scrollTop, left: cur.scrollLeft });\n        if (dom == doc)\n            break;\n    }\n    return stack;\n}\n// Reset the scroll position of the editor's parent nodes to that what\n// it was before, when storeScrollPos was called.\nfunction resetScrollPos({ refDOM, refTop, stack }) {\n    let newRefTop = refDOM ? refDOM.getBoundingClientRect().top : 0;\n    restoreScrollStack(stack, newRefTop == 0 ? 0 : newRefTop - refTop);\n}\nfunction restoreScrollStack(stack, dTop) {\n    for (let i = 0; i < stack.length; i++) {\n        let { dom, top, left } = stack[i];\n        if (dom.scrollTop != top + dTop)\n            dom.scrollTop = top + dTop;\n        if (dom.scrollLeft != left)\n            dom.scrollLeft = left;\n    }\n}\nlet preventScrollSupported = null;\n// Feature-detects support for .focus({preventScroll: true}), and uses\n// a fallback kludge when not supported.\nfunction focusPreventScroll(dom) {\n    if (dom.setActive)\n        return dom.setActive(); // in IE\n    if (preventScrollSupported)\n        return dom.focus(preventScrollSupported);\n    let stored = scrollStack(dom);\n    dom.focus(preventScrollSupported == null ? {\n        get preventScroll() {\n            preventScrollSupported = { preventScroll: true };\n            return true;\n        }\n    } : undefined);\n    if (!preventScrollSupported) {\n        preventScrollSupported = false;\n        restoreScrollStack(stored, 0);\n    }\n}\nfunction findOffsetInNode(node, coords) {\n    let closest, dxClosest = 2e8, coordsClosest, offset = 0;\n    let rowBot = coords.top, rowTop = coords.top;\n    let firstBelow, coordsBelow;\n    for (let child = node.firstChild, childIndex = 0; child; child = child.nextSibling, childIndex++) {\n        let rects;\n        if (child.nodeType == 1)\n            rects = child.getClientRects();\n        else if (child.nodeType == 3)\n            rects = textRange(child).getClientRects();\n        else\n            continue;\n        for (let i = 0; i < rects.length; i++) {\n            let rect = rects[i];\n            if (rect.top <= rowBot && rect.bottom >= rowTop) {\n                rowBot = Math.max(rect.bottom, rowBot);\n                rowTop = Math.min(rect.top, rowTop);\n                let dx = rect.left > coords.left ? rect.left - coords.left\n                    : rect.right < coords.left ? coords.left - rect.right : 0;\n                if (dx < dxClosest) {\n                    closest = child;\n                    dxClosest = dx;\n                    coordsClosest = dx && closest.nodeType == 3 ? {\n                        left: rect.right < coords.left ? rect.right : rect.left,\n                        top: coords.top\n                    } : coords;\n                    if (child.nodeType == 1 && dx)\n                        offset = childIndex + (coords.left >= (rect.left + rect.right) / 2 ? 1 : 0);\n                    continue;\n                }\n            }\n            else if (rect.top > coords.top && !firstBelow && rect.left <= coords.left && rect.right >= coords.left) {\n                firstBelow = child;\n                coordsBelow = { left: Math.max(rect.left, Math.min(rect.right, coords.left)), top: rect.top };\n            }\n            if (!closest && (coords.left >= rect.right && coords.top >= rect.top ||\n                coords.left >= rect.left && coords.top >= rect.bottom))\n                offset = childIndex + 1;\n        }\n    }\n    if (!closest && firstBelow) {\n        closest = firstBelow;\n        coordsClosest = coordsBelow;\n        dxClosest = 0;\n    }\n    if (closest && closest.nodeType == 3)\n        return findOffsetInText(closest, coordsClosest);\n    if (!closest || (dxClosest && closest.nodeType == 1))\n        return { node, offset };\n    return findOffsetInNode(closest, coordsClosest);\n}\nfunction findOffsetInText(node, coords) {\n    let len = node.nodeValue.length;\n    let range = document.createRange();\n    for (let i = 0; i < len; i++) {\n        range.setEnd(node, i + 1);\n        range.setStart(node, i);\n        let rect = singleRect(range, 1);\n        if (rect.top == rect.bottom)\n            continue;\n        if (inRect(coords, rect))\n            return { node, offset: i + (coords.left >= (rect.left + rect.right) / 2 ? 1 : 0) };\n    }\n    return { node, offset: 0 };\n}\nfunction inRect(coords, rect) {\n    return coords.left >= rect.left - 1 && coords.left <= rect.right + 1 &&\n        coords.top >= rect.top - 1 && coords.top <= rect.bottom + 1;\n}\nfunction targetKludge(dom, coords) {\n    let parent = dom.parentNode;\n    if (parent && /^li$/i.test(parent.nodeName) && coords.left < dom.getBoundingClientRect().left)\n        return parent;\n    return dom;\n}\nfunction posFromElement(view, elt, coords) {\n    let { node, offset } = findOffsetInNode(elt, coords), bias = -1;\n    if (node.nodeType == 1 && !node.firstChild) {\n        let rect = node.getBoundingClientRect();\n        bias = rect.left != rect.right && coords.left > (rect.left + rect.right) / 2 ? 1 : -1;\n    }\n    return view.docView.posFromDOM(node, offset, bias);\n}\nfunction posFromCaret(view, node, offset, coords) {\n    // Browser (in caretPosition/RangeFromPoint) will agressively\n    // normalize towards nearby inline nodes. Since we are interested in\n    // positions between block nodes too, we first walk up the hierarchy\n    // of nodes to see if there are block nodes that the coordinates\n    // fall outside of. If so, we take the position before/after that\n    // block. If not, we call `posFromDOM` on the raw node/offset.\n    let outsideBlock = -1;\n    for (let cur = node, sawBlock = false;;) {\n        if (cur == view.dom)\n            break;\n        let desc = view.docView.nearestDesc(cur, true), rect;\n        if (!desc)\n            return null;\n        if (desc.dom.nodeType == 1 && (desc.node.isBlock && desc.parent || !desc.contentDOM) &&\n            // Ignore elements with zero-size bounding rectangles\n            ((rect = desc.dom.getBoundingClientRect()).width || rect.height)) {\n            if (desc.node.isBlock && desc.parent && !/^T(R|BODY|HEAD|FOOT)$/.test(desc.dom.nodeName)) {\n                // Only apply the horizontal test to the innermost block. Vertical for any parent.\n                if (!sawBlock && rect.left > coords.left || rect.top > coords.top)\n                    outsideBlock = desc.posBefore;\n                else if (!sawBlock && rect.right < coords.left || rect.bottom < coords.top)\n                    outsideBlock = desc.posAfter;\n                sawBlock = true;\n            }\n            if (!desc.contentDOM && outsideBlock < 0 && !desc.node.isText) {\n                // If we are inside a leaf, return the side of the leaf closer to the coords\n                let before = desc.node.isBlock ? coords.top < (rect.top + rect.bottom) / 2\n                    : coords.left < (rect.left + rect.right) / 2;\n                return before ? desc.posBefore : desc.posAfter;\n            }\n        }\n        cur = desc.dom.parentNode;\n    }\n    return outsideBlock > -1 ? outsideBlock : view.docView.posFromDOM(node, offset, -1);\n}\nfunction elementFromPoint(element, coords, box) {\n    let len = element.childNodes.length;\n    if (len && box.top < box.bottom) {\n        for (let startI = Math.max(0, Math.min(len - 1, Math.floor(len * (coords.top - box.top) / (box.bottom - box.top)) - 2)), i = startI;;) {\n            let child = element.childNodes[i];\n            if (child.nodeType == 1) {\n                let rects = child.getClientRects();\n                for (let j = 0; j < rects.length; j++) {\n                    let rect = rects[j];\n                    if (inRect(coords, rect))\n                        return elementFromPoint(child, coords, rect);\n                }\n            }\n            if ((i = (i + 1) % len) == startI)\n                break;\n        }\n    }\n    return element;\n}\n// Given an x,y position on the editor, get the position in the document.\nfunction posAtCoords(view, coords) {\n    let doc = view.dom.ownerDocument, node, offset = 0;\n    let caret = caretFromPoint(doc, coords.left, coords.top);\n    if (caret)\n        ({ node, offset } = caret);\n    let elt = (view.root.elementFromPoint ? view.root : doc)\n        .elementFromPoint(coords.left, coords.top);\n    let pos;\n    if (!elt || !view.dom.contains(elt.nodeType != 1 ? elt.parentNode : elt)) {\n        let box = view.dom.getBoundingClientRect();\n        if (!inRect(coords, box))\n            return null;\n        elt = elementFromPoint(view.dom, coords, box);\n        if (!elt)\n            return null;\n    }\n    // Safari's caretRangeFromPoint returns nonsense when on a draggable element\n    if (safari) {\n        for (let p = elt; node && p; p = parentNode(p))\n            if (p.draggable)\n                node = undefined;\n    }\n    elt = targetKludge(elt, coords);\n    if (node) {\n        if (gecko && node.nodeType == 1) {\n            // Firefox will sometimes return offsets into <input> nodes, which\n            // have no actual children, from caretPositionFromPoint (#953)\n            offset = Math.min(offset, node.childNodes.length);\n            // It'll also move the returned position before image nodes,\n            // even if those are behind it.\n            if (offset < node.childNodes.length) {\n                let next = node.childNodes[offset], box;\n                if (next.nodeName == \"IMG\" && (box = next.getBoundingClientRect()).right <= coords.left &&\n                    box.bottom > coords.top)\n                    offset++;\n            }\n        }\n        let prev;\n        // When clicking above the right side of an uneditable node, Chrome will report a cursor position after that node.\n        if (webkit && offset && node.nodeType == 1 && (prev = node.childNodes[offset - 1]).nodeType == 1 &&\n            prev.contentEditable == \"false\" && prev.getBoundingClientRect().top >= coords.top)\n            offset--;\n        // Suspiciously specific kludge to work around caret*FromPoint\n        // never returning a position at the end of the document\n        if (node == view.dom && offset == node.childNodes.length - 1 && node.lastChild.nodeType == 1 &&\n            coords.top > node.lastChild.getBoundingClientRect().bottom)\n            pos = view.state.doc.content.size;\n        // Ignore positions directly after a BR, since caret*FromPoint\n        // 'round up' positions that would be more accurately placed\n        // before the BR node.\n        else if (offset == 0 || node.nodeType != 1 || node.childNodes[offset - 1].nodeName != \"BR\")\n            pos = posFromCaret(view, node, offset, coords);\n    }\n    if (pos == null)\n        pos = posFromElement(view, elt, coords);\n    let desc = view.docView.nearestDesc(elt, true);\n    return { pos, inside: desc ? desc.posAtStart - desc.border : -1 };\n}\nfunction nonZero(rect) {\n    return rect.top < rect.bottom || rect.left < rect.right;\n}\nfunction singleRect(target, bias) {\n    let rects = target.getClientRects();\n    if (rects.length) {\n        let first = rects[bias < 0 ? 0 : rects.length - 1];\n        if (nonZero(first))\n            return first;\n    }\n    return Array.prototype.find.call(rects, nonZero) || target.getBoundingClientRect();\n}\nconst BIDI = /[\\u0590-\\u05f4\\u0600-\\u06ff\\u0700-\\u08ac]/;\n// Given a position in the document model, get a bounding box of the\n// character at that position, relative to the window.\nfunction coordsAtPos(view, pos, side) {\n    let { node, offset, atom } = view.docView.domFromPos(pos, side < 0 ? -1 : 1);\n    let supportEmptyRange = webkit || gecko;\n    if (node.nodeType == 3) {\n        // These browsers support querying empty text ranges. Prefer that in\n        // bidi context or when at the end of a node.\n        if (supportEmptyRange && (BIDI.test(node.nodeValue) || (side < 0 ? !offset : offset == node.nodeValue.length))) {\n            let rect = singleRect(textRange(node, offset, offset), side);\n            // Firefox returns bad results (the position before the space)\n            // when querying a position directly after line-broken\n            // whitespace. Detect this situation and and kludge around it\n            if (gecko && offset && /\\s/.test(node.nodeValue[offset - 1]) && offset < node.nodeValue.length) {\n                let rectBefore = singleRect(textRange(node, offset - 1, offset - 1), -1);\n                if (rectBefore.top == rect.top) {\n                    let rectAfter = singleRect(textRange(node, offset, offset + 1), -1);\n                    if (rectAfter.top != rect.top)\n                        return flattenV(rectAfter, rectAfter.left < rectBefore.left);\n                }\n            }\n            return rect;\n        }\n        else {\n            let from = offset, to = offset, takeSide = side < 0 ? 1 : -1;\n            if (side < 0 && !offset) {\n                to++;\n                takeSide = -1;\n            }\n            else if (side >= 0 && offset == node.nodeValue.length) {\n                from--;\n                takeSide = 1;\n            }\n            else if (side < 0) {\n                from--;\n            }\n            else {\n                to++;\n            }\n            return flattenV(singleRect(textRange(node, from, to), takeSide), takeSide < 0);\n        }\n    }\n    let $dom = view.state.doc.resolve(pos - (atom || 0));\n    // Return a horizontal line in block context\n    if (!$dom.parent.inlineContent) {\n        if (atom == null && offset && (side < 0 || offset == nodeSize(node))) {\n            let before = node.childNodes[offset - 1];\n            if (before.nodeType == 1)\n                return flattenH(before.getBoundingClientRect(), false);\n        }\n        if (atom == null && offset < nodeSize(node)) {\n            let after = node.childNodes[offset];\n            if (after.nodeType == 1)\n                return flattenH(after.getBoundingClientRect(), true);\n        }\n        return flattenH(node.getBoundingClientRect(), side >= 0);\n    }\n    // Inline, not in text node (this is not Bidi-safe)\n    if (atom == null && offset && (side < 0 || offset == nodeSize(node))) {\n        let before = node.childNodes[offset - 1];\n        let target = before.nodeType == 3 ? textRange(before, nodeSize(before) - (supportEmptyRange ? 0 : 1))\n            // BR nodes tend to only return the rectangle before them.\n            // Only use them if they are the last element in their parent\n            : before.nodeType == 1 && (before.nodeName != \"BR\" || !before.nextSibling) ? before : null;\n        if (target)\n            return flattenV(singleRect(target, 1), false);\n    }\n    if (atom == null && offset < nodeSize(node)) {\n        let after = node.childNodes[offset];\n        while (after.pmViewDesc && after.pmViewDesc.ignoreForCoords)\n            after = after.nextSibling;\n        let target = !after ? null : after.nodeType == 3 ? textRange(after, 0, (supportEmptyRange ? 0 : 1))\n            : after.nodeType == 1 ? after : null;\n        if (target)\n            return flattenV(singleRect(target, -1), true);\n    }\n    // All else failed, just try to get a rectangle for the target node\n    return flattenV(singleRect(node.nodeType == 3 ? textRange(node) : node, -side), side >= 0);\n}\nfunction flattenV(rect, left) {\n    if (rect.width == 0)\n        return rect;\n    let x = left ? rect.left : rect.right;\n    return { top: rect.top, bottom: rect.bottom, left: x, right: x };\n}\nfunction flattenH(rect, top) {\n    if (rect.height == 0)\n        return rect;\n    let y = top ? rect.top : rect.bottom;\n    return { top: y, bottom: y, left: rect.left, right: rect.right };\n}\nfunction withFlushedState(view, state, f) {\n    let viewState = view.state, active = view.root.activeElement;\n    if (viewState != state)\n        view.updateState(state);\n    if (active != view.dom)\n        view.focus();\n    try {\n        return f();\n    }\n    finally {\n        if (viewState != state)\n            view.updateState(viewState);\n        if (active != view.dom && active)\n            active.focus();\n    }\n}\n// Whether vertical position motion in a given direction\n// from a position would leave a text block.\nfunction endOfTextblockVertical(view, state, dir) {\n    let sel = state.selection;\n    let $pos = dir == \"up\" ? sel.$from : sel.$to;\n    return withFlushedState(view, state, () => {\n        let { node: dom } = view.docView.domFromPos($pos.pos, dir == \"up\" ? -1 : 1);\n        for (;;) {\n            let nearest = view.docView.nearestDesc(dom, true);\n            if (!nearest)\n                break;\n            if (nearest.node.isBlock) {\n                dom = nearest.contentDOM || nearest.dom;\n                break;\n            }\n            dom = nearest.dom.parentNode;\n        }\n        let coords = coordsAtPos(view, $pos.pos, 1);\n        for (let child = dom.firstChild; child; child = child.nextSibling) {\n            let boxes;\n            if (child.nodeType == 1)\n                boxes = child.getClientRects();\n            else if (child.nodeType == 3)\n                boxes = textRange(child, 0, child.nodeValue.length).getClientRects();\n            else\n                continue;\n            for (let i = 0; i < boxes.length; i++) {\n                let box = boxes[i];\n                if (box.bottom > box.top + 1 &&\n                    (dir == \"up\" ? coords.top - box.top > (box.bottom - coords.top) * 2\n                        : box.bottom - coords.bottom > (coords.bottom - box.top) * 2))\n                    return false;\n            }\n        }\n        return true;\n    });\n}\nconst maybeRTL = /[\\u0590-\\u08ac]/;\nfunction endOfTextblockHorizontal(view, state, dir) {\n    let { $head } = state.selection;\n    if (!$head.parent.isTextblock)\n        return false;\n    let offset = $head.parentOffset, atStart = !offset, atEnd = offset == $head.parent.content.size;\n    let sel = view.domSelection();\n    if (!sel)\n        return $head.pos == $head.start() || $head.pos == $head.end();\n    // If the textblock is all LTR, or the browser doesn't support\n    // Selection.modify (Edge), fall back to a primitive approach\n    if (!maybeRTL.test($head.parent.textContent) || !sel.modify)\n        return dir == \"left\" || dir == \"backward\" ? atStart : atEnd;\n    return withFlushedState(view, state, () => {\n        // This is a huge hack, but appears to be the best we can\n        // currently do: use `Selection.modify` to move the selection by\n        // one character, and see if that moves the cursor out of the\n        // textblock (or doesn't move it at all, when at the start/end of\n        // the document).\n        let { focusNode: oldNode, focusOffset: oldOff, anchorNode, anchorOffset } = view.domSelectionRange();\n        let oldBidiLevel = sel.caretBidiLevel // Only for Firefox\n        ;\n        sel.modify(\"move\", dir, \"character\");\n        let parentDOM = $head.depth ? view.docView.domAfterPos($head.before()) : view.dom;\n        let { focusNode: newNode, focusOffset: newOff } = view.domSelectionRange();\n        let result = newNode && !parentDOM.contains(newNode.nodeType == 1 ? newNode : newNode.parentNode) ||\n            (oldNode == newNode && oldOff == newOff);\n        // Restore the previous selection\n        try {\n            sel.collapse(anchorNode, anchorOffset);\n            if (oldNode && (oldNode != anchorNode || oldOff != anchorOffset) && sel.extend)\n                sel.extend(oldNode, oldOff);\n        }\n        catch (_) { }\n        if (oldBidiLevel != null)\n            sel.caretBidiLevel = oldBidiLevel;\n        return result;\n    });\n}\nlet cachedState = null;\nlet cachedDir = null;\nlet cachedResult = false;\nfunction endOfTextblock(view, state, dir) {\n    if (cachedState == state && cachedDir == dir)\n        return cachedResult;\n    cachedState = state;\n    cachedDir = dir;\n    return cachedResult = dir == \"up\" || dir == \"down\"\n        ? endOfTextblockVertical(view, state, dir)\n        : endOfTextblockHorizontal(view, state, dir);\n}\n\n// View descriptions are data structures that describe the DOM that is\n// used to represent the editor's content. They are used for:\n//\n// - Incremental redrawing when the document changes\n//\n// - Figuring out what part of the document a given DOM position\n//   corresponds to\n//\n// - Wiring in custom implementations of the editing interface for a\n//   given node\n//\n// They form a doubly-linked mutable tree, starting at `view.docView`.\nconst NOT_DIRTY = 0, CHILD_DIRTY = 1, CONTENT_DIRTY = 2, NODE_DIRTY = 3;\n// Superclass for the various kinds of descriptions. Defines their\n// basic structure and shared methods.\nclass ViewDesc {\n    constructor(parent, children, dom, \n    // This is the node that holds the child views. It may be null for\n    // descs that don't have children.\n    contentDOM) {\n        this.parent = parent;\n        this.children = children;\n        this.dom = dom;\n        this.contentDOM = contentDOM;\n        this.dirty = NOT_DIRTY;\n        // An expando property on the DOM node provides a link back to its\n        // description.\n        dom.pmViewDesc = this;\n    }\n    // Used to check whether a given description corresponds to a\n    // widget/mark/node.\n    matchesWidget(widget) { return false; }\n    matchesMark(mark) { return false; }\n    matchesNode(node, outerDeco, innerDeco) { return false; }\n    matchesHack(nodeName) { return false; }\n    // When parsing in-editor content (in domchange.js), we allow\n    // descriptions to determine the parse rules that should be used to\n    // parse them.\n    parseRule() { return null; }\n    // Used by the editor's event handler to ignore events that come\n    // from certain descs.\n    stopEvent(event) { return false; }\n    // The size of the content represented by this desc.\n    get size() {\n        let size = 0;\n        for (let i = 0; i < this.children.length; i++)\n            size += this.children[i].size;\n        return size;\n    }\n    // For block nodes, this represents the space taken up by their\n    // start/end tokens.\n    get border() { return 0; }\n    destroy() {\n        this.parent = undefined;\n        if (this.dom.pmViewDesc == this)\n            this.dom.pmViewDesc = undefined;\n        for (let i = 0; i < this.children.length; i++)\n            this.children[i].destroy();\n    }\n    posBeforeChild(child) {\n        for (let i = 0, pos = this.posAtStart;; i++) {\n            let cur = this.children[i];\n            if (cur == child)\n                return pos;\n            pos += cur.size;\n        }\n    }\n    get posBefore() {\n        return this.parent.posBeforeChild(this);\n    }\n    get posAtStart() {\n        return this.parent ? this.parent.posBeforeChild(this) + this.border : 0;\n    }\n    get posAfter() {\n        return this.posBefore + this.size;\n    }\n    get posAtEnd() {\n        return this.posAtStart + this.size - 2 * this.border;\n    }\n    localPosFromDOM(dom, offset, bias) {\n        // If the DOM position is in the content, use the child desc after\n        // it to figure out a position.\n        if (this.contentDOM && this.contentDOM.contains(dom.nodeType == 1 ? dom : dom.parentNode)) {\n            if (bias < 0) {\n                let domBefore, desc;\n                if (dom == this.contentDOM) {\n                    domBefore = dom.childNodes[offset - 1];\n                }\n                else {\n                    while (dom.parentNode != this.contentDOM)\n                        dom = dom.parentNode;\n                    domBefore = dom.previousSibling;\n                }\n                while (domBefore && !((desc = domBefore.pmViewDesc) && desc.parent == this))\n                    domBefore = domBefore.previousSibling;\n                return domBefore ? this.posBeforeChild(desc) + desc.size : this.posAtStart;\n            }\n            else {\n                let domAfter, desc;\n                if (dom == this.contentDOM) {\n                    domAfter = dom.childNodes[offset];\n                }\n                else {\n                    while (dom.parentNode != this.contentDOM)\n                        dom = dom.parentNode;\n                    domAfter = dom.nextSibling;\n                }\n                while (domAfter && !((desc = domAfter.pmViewDesc) && desc.parent == this))\n                    domAfter = domAfter.nextSibling;\n                return domAfter ? this.posBeforeChild(desc) : this.posAtEnd;\n            }\n        }\n        // Otherwise, use various heuristics, falling back on the bias\n        // parameter, to determine whether to return the position at the\n        // start or at the end of this view desc.\n        let atEnd;\n        if (dom == this.dom && this.contentDOM) {\n            atEnd = offset > domIndex(this.contentDOM);\n        }\n        else if (this.contentDOM && this.contentDOM != this.dom && this.dom.contains(this.contentDOM)) {\n            atEnd = dom.compareDocumentPosition(this.contentDOM) & 2;\n        }\n        else if (this.dom.firstChild) {\n            if (offset == 0)\n                for (let search = dom;; search = search.parentNode) {\n                    if (search == this.dom) {\n                        atEnd = false;\n                        break;\n                    }\n                    if (search.previousSibling)\n                        break;\n                }\n            if (atEnd == null && offset == dom.childNodes.length)\n                for (let search = dom;; search = search.parentNode) {\n                    if (search == this.dom) {\n                        atEnd = true;\n                        break;\n                    }\n                    if (search.nextSibling)\n                        break;\n                }\n        }\n        return (atEnd == null ? bias > 0 : atEnd) ? this.posAtEnd : this.posAtStart;\n    }\n    nearestDesc(dom, onlyNodes = false) {\n        for (let first = true, cur = dom; cur; cur = cur.parentNode) {\n            let desc = this.getDesc(cur), nodeDOM;\n            if (desc && (!onlyNodes || desc.node)) {\n                // If dom is outside of this desc's nodeDOM, don't count it.\n                if (first && (nodeDOM = desc.nodeDOM) &&\n                    !(nodeDOM.nodeType == 1 ? nodeDOM.contains(dom.nodeType == 1 ? dom : dom.parentNode) : nodeDOM == dom))\n                    first = false;\n                else\n                    return desc;\n            }\n        }\n    }\n    getDesc(dom) {\n        let desc = dom.pmViewDesc;\n        for (let cur = desc; cur; cur = cur.parent)\n            if (cur == this)\n                return desc;\n    }\n    posFromDOM(dom, offset, bias) {\n        for (let scan = dom; scan; scan = scan.parentNode) {\n            let desc = this.getDesc(scan);\n            if (desc)\n                return desc.localPosFromDOM(dom, offset, bias);\n        }\n        return -1;\n    }\n    // Find the desc for the node after the given pos, if any. (When a\n    // parent node overrode rendering, there might not be one.)\n    descAt(pos) {\n        for (let i = 0, offset = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (offset == pos && end != offset) {\n                while (!child.border && child.children.length) {\n                    for (let i = 0; i < child.children.length; i++) {\n                        let inner = child.children[i];\n                        if (inner.size) {\n                            child = inner;\n                            break;\n                        }\n                    }\n                }\n                return child;\n            }\n            if (pos < end)\n                return child.descAt(pos - offset - child.border);\n            offset = end;\n        }\n    }\n    domFromPos(pos, side) {\n        if (!this.contentDOM)\n            return { node: this.dom, offset: 0, atom: pos + 1 };\n        // First find the position in the child array\n        let i = 0, offset = 0;\n        for (let curPos = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = curPos + child.size;\n            if (end > pos || child instanceof TrailingHackViewDesc) {\n                offset = pos - curPos;\n                break;\n            }\n            curPos = end;\n        }\n        // If this points into the middle of a child, call through\n        if (offset)\n            return this.children[i].domFromPos(offset - this.children[i].border, side);\n        // Go back if there were any zero-length widgets with side >= 0 before this point\n        for (let prev; i && !(prev = this.children[i - 1]).size && prev instanceof WidgetViewDesc && prev.side >= 0; i--) { }\n        // Scan towards the first useable node\n        if (side <= 0) {\n            let prev, enter = true;\n            for (;; i--, enter = false) {\n                prev = i ? this.children[i - 1] : null;\n                if (!prev || prev.dom.parentNode == this.contentDOM)\n                    break;\n            }\n            if (prev && side && enter && !prev.border && !prev.domAtom)\n                return prev.domFromPos(prev.size, side);\n            return { node: this.contentDOM, offset: prev ? domIndex(prev.dom) + 1 : 0 };\n        }\n        else {\n            let next, enter = true;\n            for (;; i++, enter = false) {\n                next = i < this.children.length ? this.children[i] : null;\n                if (!next || next.dom.parentNode == this.contentDOM)\n                    break;\n            }\n            if (next && enter && !next.border && !next.domAtom)\n                return next.domFromPos(0, side);\n            return { node: this.contentDOM, offset: next ? domIndex(next.dom) : this.contentDOM.childNodes.length };\n        }\n    }\n    // Used to find a DOM range in a single parent for a given changed\n    // range.\n    parseRange(from, to, base = 0) {\n        if (this.children.length == 0)\n            return { node: this.contentDOM, from, to, fromOffset: 0, toOffset: this.contentDOM.childNodes.length };\n        let fromOffset = -1, toOffset = -1;\n        for (let offset = base, i = 0;; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (fromOffset == -1 && from <= end) {\n                let childBase = offset + child.border;\n                // FIXME maybe descend mark views to parse a narrower range?\n                if (from >= childBase && to <= end - child.border && child.node &&\n                    child.contentDOM && this.contentDOM.contains(child.contentDOM))\n                    return child.parseRange(from, to, childBase);\n                from = offset;\n                for (let j = i; j > 0; j--) {\n                    let prev = this.children[j - 1];\n                    if (prev.size && prev.dom.parentNode == this.contentDOM && !prev.emptyChildAt(1)) {\n                        fromOffset = domIndex(prev.dom) + 1;\n                        break;\n                    }\n                    from -= prev.size;\n                }\n                if (fromOffset == -1)\n                    fromOffset = 0;\n            }\n            if (fromOffset > -1 && (end > to || i == this.children.length - 1)) {\n                to = end;\n                for (let j = i + 1; j < this.children.length; j++) {\n                    let next = this.children[j];\n                    if (next.size && next.dom.parentNode == this.contentDOM && !next.emptyChildAt(-1)) {\n                        toOffset = domIndex(next.dom);\n                        break;\n                    }\n                    to += next.size;\n                }\n                if (toOffset == -1)\n                    toOffset = this.contentDOM.childNodes.length;\n                break;\n            }\n            offset = end;\n        }\n        return { node: this.contentDOM, from, to, fromOffset, toOffset };\n    }\n    emptyChildAt(side) {\n        if (this.border || !this.contentDOM || !this.children.length)\n            return false;\n        let child = this.children[side < 0 ? 0 : this.children.length - 1];\n        return child.size == 0 || child.emptyChildAt(side);\n    }\n    domAfterPos(pos) {\n        let { node, offset } = this.domFromPos(pos, 0);\n        if (node.nodeType != 1 || offset == node.childNodes.length)\n            throw new RangeError(\"No node after pos \" + pos);\n        return node.childNodes[offset];\n    }\n    // View descs are responsible for setting any selection that falls\n    // entirely inside of them, so that custom implementations can do\n    // custom things with the selection. Note that this falls apart when\n    // a selection starts in such a node and ends in another, in which\n    // case we just use whatever domFromPos produces as a best effort.\n    setSelection(anchor, head, view, force = false) {\n        // If the selection falls entirely in a child, give it to that child\n        let from = Math.min(anchor, head), to = Math.max(anchor, head);\n        for (let i = 0, offset = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (from > offset && to < end)\n                return child.setSelection(anchor - offset - child.border, head - offset - child.border, view, force);\n            offset = end;\n        }\n        let anchorDOM = this.domFromPos(anchor, anchor ? -1 : 1);\n        let headDOM = head == anchor ? anchorDOM : this.domFromPos(head, head ? -1 : 1);\n        let domSel = view.root.getSelection();\n        let selRange = view.domSelectionRange();\n        let brKludge = false;\n        // On Firefox, using Selection.collapse to put the cursor after a\n        // BR node for some reason doesn't always work (#1073). On Safari,\n        // the cursor sometimes inexplicable visually lags behind its\n        // reported position in such situations (#1092).\n        if ((gecko || safari) && anchor == head) {\n            let { node, offset } = anchorDOM;\n            if (node.nodeType == 3) {\n                brKludge = !!(offset && node.nodeValue[offset - 1] == \"\\n\");\n                // Issue #1128\n                if (brKludge && offset == node.nodeValue.length) {\n                    for (let scan = node, after; scan; scan = scan.parentNode) {\n                        if (after = scan.nextSibling) {\n                            if (after.nodeName == \"BR\")\n                                anchorDOM = headDOM = { node: after.parentNode, offset: domIndex(after) + 1 };\n                            break;\n                        }\n                        let desc = scan.pmViewDesc;\n                        if (desc && desc.node && desc.node.isBlock)\n                            break;\n                    }\n                }\n            }\n            else {\n                let prev = node.childNodes[offset - 1];\n                brKludge = prev && (prev.nodeName == \"BR\" || prev.contentEditable == \"false\");\n            }\n        }\n        // Firefox can act strangely when the selection is in front of an\n        // uneditable node. See #1163 and https://bugzilla.mozilla.org/show_bug.cgi?id=1709536\n        if (gecko && selRange.focusNode && selRange.focusNode != headDOM.node && selRange.focusNode.nodeType == 1) {\n            let after = selRange.focusNode.childNodes[selRange.focusOffset];\n            if (after && after.contentEditable == \"false\")\n                force = true;\n        }\n        if (!(force || brKludge && safari) &&\n            isEquivalentPosition(anchorDOM.node, anchorDOM.offset, selRange.anchorNode, selRange.anchorOffset) &&\n            isEquivalentPosition(headDOM.node, headDOM.offset, selRange.focusNode, selRange.focusOffset))\n            return;\n        // Selection.extend can be used to create an 'inverted' selection\n        // (one where the focus is before the anchor), but not all\n        // browsers support it yet.\n        let domSelExtended = false;\n        if ((domSel.extend || anchor == head) && !brKludge) {\n            domSel.collapse(anchorDOM.node, anchorDOM.offset);\n            try {\n                if (anchor != head)\n                    domSel.extend(headDOM.node, headDOM.offset);\n                domSelExtended = true;\n            }\n            catch (_) {\n                // In some cases with Chrome the selection is empty after calling\n                // collapse, even when it should be valid. This appears to be a bug, but\n                // it is difficult to isolate. If this happens fallback to the old path\n                // without using extend.\n                // Similarly, this could crash on Safari if the editor is hidden, and\n                // there was no selection.\n            }\n        }\n        if (!domSelExtended) {\n            if (anchor > head) {\n                let tmp = anchorDOM;\n                anchorDOM = headDOM;\n                headDOM = tmp;\n            }\n            let range = document.createRange();\n            range.setEnd(headDOM.node, headDOM.offset);\n            range.setStart(anchorDOM.node, anchorDOM.offset);\n            domSel.removeAllRanges();\n            domSel.addRange(range);\n        }\n    }\n    ignoreMutation(mutation) {\n        return !this.contentDOM && mutation.type != \"selection\";\n    }\n    get contentLost() {\n        return this.contentDOM && this.contentDOM != this.dom && !this.dom.contains(this.contentDOM);\n    }\n    // Remove a subtree of the element tree that has been touched\n    // by a DOM change, so that the next update will redraw it.\n    markDirty(from, to) {\n        for (let offset = 0, i = 0; i < this.children.length; i++) {\n            let child = this.children[i], end = offset + child.size;\n            if (offset == end ? from <= end && to >= offset : from < end && to > offset) {\n                let startInside = offset + child.border, endInside = end - child.border;\n                if (from >= startInside && to <= endInside) {\n                    this.dirty = from == offset || to == end ? CONTENT_DIRTY : CHILD_DIRTY;\n                    if (from == startInside && to == endInside &&\n                        (child.contentLost || child.dom.parentNode != this.contentDOM))\n                        child.dirty = NODE_DIRTY;\n                    else\n                        child.markDirty(from - startInside, to - startInside);\n                    return;\n                }\n                else {\n                    child.dirty = child.dom == child.contentDOM && child.dom.parentNode == this.contentDOM && !child.children.length\n                        ? CONTENT_DIRTY : NODE_DIRTY;\n                }\n            }\n            offset = end;\n        }\n        this.dirty = CONTENT_DIRTY;\n    }\n    markParentsDirty() {\n        let level = 1;\n        for (let node = this.parent; node; node = node.parent, level++) {\n            let dirty = level == 1 ? CONTENT_DIRTY : CHILD_DIRTY;\n            if (node.dirty < dirty)\n                node.dirty = dirty;\n        }\n    }\n    get domAtom() { return false; }\n    get ignoreForCoords() { return false; }\n    get ignoreForSelection() { return false; }\n    isText(text) { return false; }\n}\n// A widget desc represents a widget decoration, which is a DOM node\n// drawn between the document nodes.\nclass WidgetViewDesc extends ViewDesc {\n    constructor(parent, widget, view, pos) {\n        let self, dom = widget.type.toDOM;\n        if (typeof dom == \"function\")\n            dom = dom(view, () => {\n                if (!self)\n                    return pos;\n                if (self.parent)\n                    return self.parent.posBeforeChild(self);\n            });\n        if (!widget.type.spec.raw) {\n            if (dom.nodeType != 1) {\n                let wrap = document.createElement(\"span\");\n                wrap.appendChild(dom);\n                dom = wrap;\n            }\n            dom.contentEditable = \"false\";\n            dom.classList.add(\"ProseMirror-widget\");\n        }\n        super(parent, [], dom, null);\n        this.widget = widget;\n        this.widget = widget;\n        self = this;\n    }\n    matchesWidget(widget) {\n        return this.dirty == NOT_DIRTY && widget.type.eq(this.widget.type);\n    }\n    parseRule() { return { ignore: true }; }\n    stopEvent(event) {\n        let stop = this.widget.spec.stopEvent;\n        return stop ? stop(event) : false;\n    }\n    ignoreMutation(mutation) {\n        return mutation.type != \"selection\" || this.widget.spec.ignoreSelection;\n    }\n    destroy() {\n        this.widget.type.destroy(this.dom);\n        super.destroy();\n    }\n    get domAtom() { return true; }\n    get ignoreForSelection() { return !!this.widget.type.spec.relaxedSide; }\n    get side() { return this.widget.type.side; }\n}\nclass CompositionViewDesc extends ViewDesc {\n    constructor(parent, dom, textDOM, text) {\n        super(parent, [], dom, null);\n        this.textDOM = textDOM;\n        this.text = text;\n    }\n    get size() { return this.text.length; }\n    localPosFromDOM(dom, offset) {\n        if (dom != this.textDOM)\n            return this.posAtStart + (offset ? this.size : 0);\n        return this.posAtStart + offset;\n    }\n    domFromPos(pos) {\n        return { node: this.textDOM, offset: pos };\n    }\n    ignoreMutation(mut) {\n        return mut.type === 'characterData' && mut.target.nodeValue == mut.oldValue;\n    }\n}\n// A mark desc represents a mark. May have multiple children,\n// depending on how the mark is split. Note that marks are drawn using\n// a fixed nesting order, for simplicity and predictability, so in\n// some cases they will be split more often than would appear\n// necessary.\nclass MarkViewDesc extends ViewDesc {\n    constructor(parent, mark, dom, contentDOM, spec) {\n        super(parent, [], dom, contentDOM);\n        this.mark = mark;\n        this.spec = spec;\n    }\n    static create(parent, mark, inline, view) {\n        let custom = view.nodeViews[mark.type.name];\n        let spec = custom && custom(mark, view, inline);\n        if (!spec || !spec.dom)\n            spec = DOMSerializer.renderSpec(document, mark.type.spec.toDOM(mark, inline), null, mark.attrs);\n        return new MarkViewDesc(parent, mark, spec.dom, spec.contentDOM || spec.dom, spec);\n    }\n    parseRule() {\n        if ((this.dirty & NODE_DIRTY) || this.mark.type.spec.reparseInView)\n            return null;\n        return { mark: this.mark.type.name, attrs: this.mark.attrs, contentElement: this.contentDOM };\n    }\n    matchesMark(mark) { return this.dirty != NODE_DIRTY && this.mark.eq(mark); }\n    markDirty(from, to) {\n        super.markDirty(from, to);\n        // Move dirty info to nearest node view\n        if (this.dirty != NOT_DIRTY) {\n            let parent = this.parent;\n            while (!parent.node)\n                parent = parent.parent;\n            if (parent.dirty < this.dirty)\n                parent.dirty = this.dirty;\n            this.dirty = NOT_DIRTY;\n        }\n    }\n    slice(from, to, view) {\n        let copy = MarkViewDesc.create(this.parent, this.mark, true, view);\n        let nodes = this.children, size = this.size;\n        if (to < size)\n            nodes = replaceNodes(nodes, to, size, view);\n        if (from > 0)\n            nodes = replaceNodes(nodes, 0, from, view);\n        for (let i = 0; i < nodes.length; i++)\n            nodes[i].parent = copy;\n        copy.children = nodes;\n        return copy;\n    }\n    ignoreMutation(mutation) {\n        return this.spec.ignoreMutation ? this.spec.ignoreMutation(mutation) : super.ignoreMutation(mutation);\n    }\n    destroy() {\n        if (this.spec.destroy)\n            this.spec.destroy();\n        super.destroy();\n    }\n}\n// Node view descs are the main, most common type of view desc, and\n// correspond to an actual node in the document. Unlike mark descs,\n// they populate their child array themselves.\nclass NodeViewDesc extends ViewDesc {\n    constructor(parent, node, outerDeco, innerDeco, dom, contentDOM, nodeDOM, view, pos) {\n        super(parent, [], dom, contentDOM);\n        this.node = node;\n        this.outerDeco = outerDeco;\n        this.innerDeco = innerDeco;\n        this.nodeDOM = nodeDOM;\n    }\n    // By default, a node is rendered using the `toDOM` method from the\n    // node type spec. But client code can use the `nodeViews` spec to\n    // supply a custom node view, which can influence various aspects of\n    // the way the node works.\n    //\n    // (Using subclassing for this was intentionally decided against,\n    // since it'd require exposing a whole slew of finicky\n    // implementation details to the user code that they probably will\n    // never need.)\n    static create(parent, node, outerDeco, innerDeco, view, pos) {\n        let custom = view.nodeViews[node.type.name], descObj;\n        let spec = custom && custom(node, view, () => {\n            // (This is a function that allows the custom view to find its\n            // own position)\n            if (!descObj)\n                return pos;\n            if (descObj.parent)\n                return descObj.parent.posBeforeChild(descObj);\n        }, outerDeco, innerDeco);\n        let dom = spec && spec.dom, contentDOM = spec && spec.contentDOM;\n        if (node.isText) {\n            if (!dom)\n                dom = document.createTextNode(node.text);\n            else if (dom.nodeType != 3)\n                throw new RangeError(\"Text must be rendered as a DOM text node\");\n        }\n        else if (!dom) {\n            let spec = DOMSerializer.renderSpec(document, node.type.spec.toDOM(node), null, node.attrs);\n            ({ dom, contentDOM } = spec);\n        }\n        if (!contentDOM && !node.isText && dom.nodeName != \"BR\") { // Chrome gets confused by <br contenteditable=false>\n            if (!dom.hasAttribute(\"contenteditable\"))\n                dom.contentEditable = \"false\";\n            if (node.type.spec.draggable)\n                dom.draggable = true;\n        }\n        let nodeDOM = dom;\n        dom = applyOuterDeco(dom, outerDeco, node);\n        if (spec)\n            return descObj = new CustomNodeViewDesc(parent, node, outerDeco, innerDeco, dom, contentDOM || null, nodeDOM, spec, view, pos + 1);\n        else if (node.isText)\n            return new TextViewDesc(parent, node, outerDeco, innerDeco, dom, nodeDOM, view);\n        else\n            return new NodeViewDesc(parent, node, outerDeco, innerDeco, dom, contentDOM || null, nodeDOM, view, pos + 1);\n    }\n    parseRule() {\n        // Experimental kludge to allow opt-in re-parsing of nodes\n        if (this.node.type.spec.reparseInView)\n            return null;\n        // FIXME the assumption that this can always return the current\n        // attrs means that if the user somehow manages to change the\n        // attrs in the dom, that won't be picked up. Not entirely sure\n        // whether this is a problem\n        let rule = { node: this.node.type.name, attrs: this.node.attrs };\n        if (this.node.type.whitespace == \"pre\")\n            rule.preserveWhitespace = \"full\";\n        if (!this.contentDOM) {\n            rule.getContent = () => this.node.content;\n        }\n        else if (!this.contentLost) {\n            rule.contentElement = this.contentDOM;\n        }\n        else {\n            // Chrome likes to randomly recreate parent nodes when\n            // backspacing things. When that happens, this tries to find the\n            // new parent.\n            for (let i = this.children.length - 1; i >= 0; i--) {\n                let child = this.children[i];\n                if (this.dom.contains(child.dom.parentNode)) {\n                    rule.contentElement = child.dom.parentNode;\n                    break;\n                }\n            }\n            if (!rule.contentElement)\n                rule.getContent = () => Fragment.empty;\n        }\n        return rule;\n    }\n    matchesNode(node, outerDeco, innerDeco) {\n        return this.dirty == NOT_DIRTY && node.eq(this.node) &&\n            sameOuterDeco(outerDeco, this.outerDeco) && innerDeco.eq(this.innerDeco);\n    }\n    get size() { return this.node.nodeSize; }\n    get border() { return this.node.isLeaf ? 0 : 1; }\n    // Syncs `this.children` to match `this.node.content` and the local\n    // decorations, possibly introducing nesting for marks. Then, in a\n    // separate step, syncs the DOM inside `this.contentDOM` to\n    // `this.children`.\n    updateChildren(view, pos) {\n        let inline = this.node.inlineContent, off = pos;\n        let composition = view.composing ? this.localCompositionInfo(view, pos) : null;\n        let localComposition = composition && composition.pos > -1 ? composition : null;\n        let compositionInChild = composition && composition.pos < 0;\n        let updater = new ViewTreeUpdater(this, localComposition && localComposition.node, view);\n        iterDeco(this.node, this.innerDeco, (widget, i, insideNode) => {\n            if (widget.spec.marks)\n                updater.syncToMarks(widget.spec.marks, inline, view);\n            else if (widget.type.side >= 0 && !insideNode)\n                updater.syncToMarks(i == this.node.childCount ? Mark.none : this.node.child(i).marks, inline, view);\n            // If the next node is a desc matching this widget, reuse it,\n            // otherwise insert the widget as a new view desc.\n            updater.placeWidget(widget, view, off);\n        }, (child, outerDeco, innerDeco, i) => {\n            // Make sure the wrapping mark descs match the node's marks.\n            updater.syncToMarks(child.marks, inline, view);\n            // Try several strategies for drawing this node\n            let compIndex;\n            if (updater.findNodeMatch(child, outerDeco, innerDeco, i)) ;\n            else if (compositionInChild && view.state.selection.from > off &&\n                view.state.selection.to < off + child.nodeSize &&\n                (compIndex = updater.findIndexWithChild(composition.node)) > -1 &&\n                updater.updateNodeAt(child, outerDeco, innerDeco, compIndex, view)) ;\n            else if (updater.updateNextNode(child, outerDeco, innerDeco, view, i, off)) ;\n            else {\n                // Add it as a new view\n                updater.addNode(child, outerDeco, innerDeco, view, off);\n            }\n            off += child.nodeSize;\n        });\n        // Drop all remaining descs after the current position.\n        updater.syncToMarks([], inline, view);\n        if (this.node.isTextblock)\n            updater.addTextblockHacks();\n        updater.destroyRest();\n        // Sync the DOM if anything changed\n        if (updater.changed || this.dirty == CONTENT_DIRTY) {\n            // May have to protect focused DOM from being changed if a composition is active\n            if (localComposition)\n                this.protectLocalComposition(view, localComposition);\n            renderDescs(this.contentDOM, this.children, view);\n            if (ios)\n                iosHacks(this.dom);\n        }\n    }\n    localCompositionInfo(view, pos) {\n        // Only do something if both the selection and a focused text node\n        // are inside of this node\n        let { from, to } = view.state.selection;\n        if (!(view.state.selection instanceof TextSelection) || from < pos || to > pos + this.node.content.size)\n            return null;\n        let textNode = view.input.compositionNode;\n        if (!textNode || !this.dom.contains(textNode.parentNode))\n            return null;\n        if (this.node.inlineContent) {\n            // Find the text in the focused node in the node, stop if it's not\n            // there (may have been modified through other means, in which\n            // case it should overwritten)\n            let text = textNode.nodeValue;\n            let textPos = findTextInFragment(this.node.content, text, from - pos, to - pos);\n            return textPos < 0 ? null : { node: textNode, pos: textPos, text };\n        }\n        else {\n            return { node: textNode, pos: -1, text: \"\" };\n        }\n    }\n    protectLocalComposition(view, { node, pos, text }) {\n        // The node is already part of a local view desc, leave it there\n        if (this.getDesc(node))\n            return;\n        // Create a composition view for the orphaned nodes\n        let topNode = node;\n        for (;; topNode = topNode.parentNode) {\n            if (topNode.parentNode == this.contentDOM)\n                break;\n            while (topNode.previousSibling)\n                topNode.parentNode.removeChild(topNode.previousSibling);\n            while (topNode.nextSibling)\n                topNode.parentNode.removeChild(topNode.nextSibling);\n            if (topNode.pmViewDesc)\n                topNode.pmViewDesc = undefined;\n        }\n        let desc = new CompositionViewDesc(this, topNode, node, text);\n        view.input.compositionNodes.push(desc);\n        // Patch up this.children to contain the composition view\n        this.children = replaceNodes(this.children, pos, pos + text.length, view, desc);\n    }\n    // If this desc must be updated to match the given node decoration,\n    // do so and return true.\n    update(node, outerDeco, innerDeco, view) {\n        if (this.dirty == NODE_DIRTY ||\n            !node.sameMarkup(this.node))\n            return false;\n        this.updateInner(node, outerDeco, innerDeco, view);\n        return true;\n    }\n    updateInner(node, outerDeco, innerDeco, view) {\n        this.updateOuterDeco(outerDeco);\n        this.node = node;\n        this.innerDeco = innerDeco;\n        if (this.contentDOM)\n            this.updateChildren(view, this.posAtStart);\n        this.dirty = NOT_DIRTY;\n    }\n    updateOuterDeco(outerDeco) {\n        if (sameOuterDeco(outerDeco, this.outerDeco))\n            return;\n        let needsWrap = this.nodeDOM.nodeType != 1;\n        let oldDOM = this.dom;\n        this.dom = patchOuterDeco(this.dom, this.nodeDOM, computeOuterDeco(this.outerDeco, this.node, needsWrap), computeOuterDeco(outerDeco, this.node, needsWrap));\n        if (this.dom != oldDOM) {\n            oldDOM.pmViewDesc = undefined;\n            this.dom.pmViewDesc = this;\n        }\n        this.outerDeco = outerDeco;\n    }\n    // Mark this node as being the selected node.\n    selectNode() {\n        if (this.nodeDOM.nodeType == 1)\n            this.nodeDOM.classList.add(\"ProseMirror-selectednode\");\n        if (this.contentDOM || !this.node.type.spec.draggable)\n            this.dom.draggable = true;\n    }\n    // Remove selected node marking from this node.\n    deselectNode() {\n        if (this.nodeDOM.nodeType == 1) {\n            this.nodeDOM.classList.remove(\"ProseMirror-selectednode\");\n            if (this.contentDOM || !this.node.type.spec.draggable)\n                this.dom.removeAttribute(\"draggable\");\n        }\n    }\n    get domAtom() { return this.node.isAtom; }\n}\n// Create a view desc for the top-level document node, to be exported\n// and used by the view class.\nfunction docViewDesc(doc, outerDeco, innerDeco, dom, view) {\n    applyOuterDeco(dom, outerDeco, doc);\n    let docView = new NodeViewDesc(undefined, doc, outerDeco, innerDeco, dom, dom, dom, view, 0);\n    if (docView.contentDOM)\n        docView.updateChildren(view, 0);\n    return docView;\n}\nclass TextViewDesc extends NodeViewDesc {\n    constructor(parent, node, outerDeco, innerDeco, dom, nodeDOM, view) {\n        super(parent, node, outerDeco, innerDeco, dom, null, nodeDOM, view, 0);\n    }\n    parseRule() {\n        let skip = this.nodeDOM.parentNode;\n        while (skip && skip != this.dom && !skip.pmIsDeco)\n            skip = skip.parentNode;\n        return { skip: (skip || true) };\n    }\n    update(node, outerDeco, innerDeco, view) {\n        if (this.dirty == NODE_DIRTY || (this.dirty != NOT_DIRTY && !this.inParent()) ||\n            !node.sameMarkup(this.node))\n            return false;\n        this.updateOuterDeco(outerDeco);\n        if ((this.dirty != NOT_DIRTY || node.text != this.node.text) && node.text != this.nodeDOM.nodeValue) {\n            this.nodeDOM.nodeValue = node.text;\n            if (view.trackWrites == this.nodeDOM)\n                view.trackWrites = null;\n        }\n        this.node = node;\n        this.dirty = NOT_DIRTY;\n        return true;\n    }\n    inParent() {\n        let parentDOM = this.parent.contentDOM;\n        for (let n = this.nodeDOM; n; n = n.parentNode)\n            if (n == parentDOM)\n                return true;\n        return false;\n    }\n    domFromPos(pos) {\n        return { node: this.nodeDOM, offset: pos };\n    }\n    localPosFromDOM(dom, offset, bias) {\n        if (dom == this.nodeDOM)\n            return this.posAtStart + Math.min(offset, this.node.text.length);\n        return super.localPosFromDOM(dom, offset, bias);\n    }\n    ignoreMutation(mutation) {\n        return mutation.type != \"characterData\" && mutation.type != \"selection\";\n    }\n    slice(from, to, view) {\n        let node = this.node.cut(from, to), dom = document.createTextNode(node.text);\n        return new TextViewDesc(this.parent, node, this.outerDeco, this.innerDeco, dom, dom, view);\n    }\n    markDirty(from, to) {\n        super.markDirty(from, to);\n        if (this.dom != this.nodeDOM && (from == 0 || to == this.nodeDOM.nodeValue.length))\n            this.dirty = NODE_DIRTY;\n    }\n    get domAtom() { return false; }\n    isText(text) { return this.node.text == text; }\n}\n// A dummy desc used to tag trailing BR or IMG nodes created to work\n// around contentEditable terribleness.\nclass TrailingHackViewDesc extends ViewDesc {\n    parseRule() { return { ignore: true }; }\n    matchesHack(nodeName) { return this.dirty == NOT_DIRTY && this.dom.nodeName == nodeName; }\n    get domAtom() { return true; }\n    get ignoreForCoords() { return this.dom.nodeName == \"IMG\"; }\n}\n// A separate subclass is used for customized node views, so that the\n// extra checks only have to be made for nodes that are actually\n// customized.\nclass CustomNodeViewDesc extends NodeViewDesc {\n    constructor(parent, node, outerDeco, innerDeco, dom, contentDOM, nodeDOM, spec, view, pos) {\n        super(parent, node, outerDeco, innerDeco, dom, contentDOM, nodeDOM, view, pos);\n        this.spec = spec;\n    }\n    // A custom `update` method gets to decide whether the update goes\n    // through. If it does, and there's a `contentDOM` node, our logic\n    // updates the children.\n    update(node, outerDeco, innerDeco, view) {\n        if (this.dirty == NODE_DIRTY)\n            return false;\n        if (this.spec.update && (this.node.type == node.type || this.spec.multiType)) {\n            let result = this.spec.update(node, outerDeco, innerDeco);\n            if (result)\n                this.updateInner(node, outerDeco, innerDeco, view);\n            return result;\n        }\n        else if (!this.contentDOM && !node.isLeaf) {\n            return false;\n        }\n        else {\n            return super.update(node, outerDeco, innerDeco, view);\n        }\n    }\n    selectNode() {\n        this.spec.selectNode ? this.spec.selectNode() : super.selectNode();\n    }\n    deselectNode() {\n        this.spec.deselectNode ? this.spec.deselectNode() : super.deselectNode();\n    }\n    setSelection(anchor, head, view, force) {\n        this.spec.setSelection ? this.spec.setSelection(anchor, head, view.root)\n            : super.setSelection(anchor, head, view, force);\n    }\n    destroy() {\n        if (this.spec.destroy)\n            this.spec.destroy();\n        super.destroy();\n    }\n    stopEvent(event) {\n        return this.spec.stopEvent ? this.spec.stopEvent(event) : false;\n    }\n    ignoreMutation(mutation) {\n        return this.spec.ignoreMutation ? this.spec.ignoreMutation(mutation) : super.ignoreMutation(mutation);\n    }\n}\n// Sync the content of the given DOM node with the nodes associated\n// with the given array of view descs, recursing into mark descs\n// because this should sync the subtree for a whole node at a time.\nfunction renderDescs(parentDOM, descs, view) {\n    let dom = parentDOM.firstChild, written = false;\n    for (let i = 0; i < descs.length; i++) {\n        let desc = descs[i], childDOM = desc.dom;\n        if (childDOM.parentNode == parentDOM) {\n            while (childDOM != dom) {\n                dom = rm(dom);\n                written = true;\n            }\n            dom = dom.nextSibling;\n        }\n        else {\n            written = true;\n            parentDOM.insertBefore(childDOM, dom);\n        }\n        if (desc instanceof MarkViewDesc) {\n            let pos = dom ? dom.previousSibling : parentDOM.lastChild;\n            renderDescs(desc.contentDOM, desc.children, view);\n            dom = pos ? pos.nextSibling : parentDOM.firstChild;\n        }\n    }\n    while (dom) {\n        dom = rm(dom);\n        written = true;\n    }\n    if (written && view.trackWrites == parentDOM)\n        view.trackWrites = null;\n}\nconst OuterDecoLevel = function (nodeName) {\n    if (nodeName)\n        this.nodeName = nodeName;\n};\nOuterDecoLevel.prototype = Object.create(null);\nconst noDeco = [new OuterDecoLevel];\nfunction computeOuterDeco(outerDeco, node, needsWrap) {\n    if (outerDeco.length == 0)\n        return noDeco;\n    let top = needsWrap ? noDeco[0] : new OuterDecoLevel, result = [top];\n    for (let i = 0; i < outerDeco.length; i++) {\n        let attrs = outerDeco[i].type.attrs;\n        if (!attrs)\n            continue;\n        if (attrs.nodeName)\n            result.push(top = new OuterDecoLevel(attrs.nodeName));\n        for (let name in attrs) {\n            let val = attrs[name];\n            if (val == null)\n                continue;\n            if (needsWrap && result.length == 1)\n                result.push(top = new OuterDecoLevel(node.isInline ? \"span\" : \"div\"));\n            if (name == \"class\")\n                top.class = (top.class ? top.class + \" \" : \"\") + val;\n            else if (name == \"style\")\n                top.style = (top.style ? top.style + \";\" : \"\") + val;\n            else if (name != \"nodeName\")\n                top[name] = val;\n        }\n    }\n    return result;\n}\nfunction patchOuterDeco(outerDOM, nodeDOM, prevComputed, curComputed) {\n    // Shortcut for trivial case\n    if (prevComputed == noDeco && curComputed == noDeco)\n        return nodeDOM;\n    let curDOM = nodeDOM;\n    for (let i = 0; i < curComputed.length; i++) {\n        let deco = curComputed[i], prev = prevComputed[i];\n        if (i) {\n            let parent;\n            if (prev && prev.nodeName == deco.nodeName && curDOM != outerDOM &&\n                (parent = curDOM.parentNode) && parent.nodeName.toLowerCase() == deco.nodeName) {\n                curDOM = parent;\n            }\n            else {\n                parent = document.createElement(deco.nodeName);\n                parent.pmIsDeco = true;\n                parent.appendChild(curDOM);\n                prev = noDeco[0];\n                curDOM = parent;\n            }\n        }\n        patchAttributes(curDOM, prev || noDeco[0], deco);\n    }\n    return curDOM;\n}\nfunction patchAttributes(dom, prev, cur) {\n    for (let name in prev)\n        if (name != \"class\" && name != \"style\" && name != \"nodeName\" && !(name in cur))\n            dom.removeAttribute(name);\n    for (let name in cur)\n        if (name != \"class\" && name != \"style\" && name != \"nodeName\" && cur[name] != prev[name])\n            dom.setAttribute(name, cur[name]);\n    if (prev.class != cur.class) {\n        let prevList = prev.class ? prev.class.split(\" \").filter(Boolean) : [];\n        let curList = cur.class ? cur.class.split(\" \").filter(Boolean) : [];\n        for (let i = 0; i < prevList.length; i++)\n            if (curList.indexOf(prevList[i]) == -1)\n                dom.classList.remove(prevList[i]);\n        for (let i = 0; i < curList.length; i++)\n            if (prevList.indexOf(curList[i]) == -1)\n                dom.classList.add(curList[i]);\n        if (dom.classList.length == 0)\n            dom.removeAttribute(\"class\");\n    }\n    if (prev.style != cur.style) {\n        if (prev.style) {\n            let prop = /\\s*([\\w\\-\\xa1-\\uffff]+)\\s*:(?:\"(?:\\\\.|[^\"])*\"|'(?:\\\\.|[^'])*'|\\(.*?\\)|[^;])*/g, m;\n            while (m = prop.exec(prev.style))\n                dom.style.removeProperty(m[1]);\n        }\n        if (cur.style)\n            dom.style.cssText += cur.style;\n    }\n}\nfunction applyOuterDeco(dom, deco, node) {\n    return patchOuterDeco(dom, dom, noDeco, computeOuterDeco(deco, node, dom.nodeType != 1));\n}\nfunction sameOuterDeco(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!a[i].type.eq(b[i].type))\n            return false;\n    return true;\n}\n// Remove a DOM node and return its next sibling.\nfunction rm(dom) {\n    let next = dom.nextSibling;\n    dom.parentNode.removeChild(dom);\n    return next;\n}\n// Helper class for incrementally updating a tree of mark descs and\n// the widget and node descs inside of them.\nclass ViewTreeUpdater {\n    constructor(top, lock, view) {\n        this.lock = lock;\n        this.view = view;\n        // Index into `this.top`'s child array, represents the current\n        // update position.\n        this.index = 0;\n        // When entering a mark, the current top and index are pushed\n        // onto this.\n        this.stack = [];\n        // Tracks whether anything was changed\n        this.changed = false;\n        this.top = top;\n        this.preMatch = preMatch(top.node.content, top);\n    }\n    // Destroy and remove the children between the given indices in\n    // `this.top`.\n    destroyBetween(start, end) {\n        if (start == end)\n            return;\n        for (let i = start; i < end; i++)\n            this.top.children[i].destroy();\n        this.top.children.splice(start, end - start);\n        this.changed = true;\n    }\n    // Destroy all remaining children in `this.top`.\n    destroyRest() {\n        this.destroyBetween(this.index, this.top.children.length);\n    }\n    // Sync the current stack of mark descs with the given array of\n    // marks, reusing existing mark descs when possible.\n    syncToMarks(marks, inline, view) {\n        let keep = 0, depth = this.stack.length >> 1;\n        let maxKeep = Math.min(depth, marks.length);\n        while (keep < maxKeep &&\n            (keep == depth - 1 ? this.top : this.stack[(keep + 1) << 1])\n                .matchesMark(marks[keep]) && marks[keep].type.spec.spanning !== false)\n            keep++;\n        while (keep < depth) {\n            this.destroyRest();\n            this.top.dirty = NOT_DIRTY;\n            this.index = this.stack.pop();\n            this.top = this.stack.pop();\n            depth--;\n        }\n        while (depth < marks.length) {\n            this.stack.push(this.top, this.index + 1);\n            let found = -1;\n            for (let i = this.index; i < Math.min(this.index + 3, this.top.children.length); i++) {\n                let next = this.top.children[i];\n                if (next.matchesMark(marks[depth]) && !this.isLocked(next.dom)) {\n                    found = i;\n                    break;\n                }\n            }\n            if (found > -1) {\n                if (found > this.index) {\n                    this.changed = true;\n                    this.destroyBetween(this.index, found);\n                }\n                this.top = this.top.children[this.index];\n            }\n            else {\n                let markDesc = MarkViewDesc.create(this.top, marks[depth], inline, view);\n                this.top.children.splice(this.index, 0, markDesc);\n                this.top = markDesc;\n                this.changed = true;\n            }\n            this.index = 0;\n            depth++;\n        }\n    }\n    // Try to find a node desc matching the given data. Skip over it and\n    // return true when successful.\n    findNodeMatch(node, outerDeco, innerDeco, index) {\n        let found = -1, targetDesc;\n        if (index >= this.preMatch.index &&\n            (targetDesc = this.preMatch.matches[index - this.preMatch.index]).parent == this.top &&\n            targetDesc.matchesNode(node, outerDeco, innerDeco)) {\n            found = this.top.children.indexOf(targetDesc, this.index);\n        }\n        else {\n            for (let i = this.index, e = Math.min(this.top.children.length, i + 5); i < e; i++) {\n                let child = this.top.children[i];\n                if (child.matchesNode(node, outerDeco, innerDeco) && !this.preMatch.matched.has(child)) {\n                    found = i;\n                    break;\n                }\n            }\n        }\n        if (found < 0)\n            return false;\n        this.destroyBetween(this.index, found);\n        this.index++;\n        return true;\n    }\n    updateNodeAt(node, outerDeco, innerDeco, index, view) {\n        let child = this.top.children[index];\n        if (child.dirty == NODE_DIRTY && child.dom == child.contentDOM)\n            child.dirty = CONTENT_DIRTY;\n        if (!child.update(node, outerDeco, innerDeco, view))\n            return false;\n        this.destroyBetween(this.index, index);\n        this.index++;\n        return true;\n    }\n    findIndexWithChild(domNode) {\n        for (;;) {\n            let parent = domNode.parentNode;\n            if (!parent)\n                return -1;\n            if (parent == this.top.contentDOM) {\n                let desc = domNode.pmViewDesc;\n                if (desc)\n                    for (let i = this.index; i < this.top.children.length; i++) {\n                        if (this.top.children[i] == desc)\n                            return i;\n                    }\n                return -1;\n            }\n            domNode = parent;\n        }\n    }\n    // Try to update the next node, if any, to the given data. Checks\n    // pre-matches to avoid overwriting nodes that could still be used.\n    updateNextNode(node, outerDeco, innerDeco, view, index, pos) {\n        for (let i = this.index; i < this.top.children.length; i++) {\n            let next = this.top.children[i];\n            if (next instanceof NodeViewDesc) {\n                let preMatch = this.preMatch.matched.get(next);\n                if (preMatch != null && preMatch != index)\n                    return false;\n                let nextDOM = next.dom, updated;\n                // Can't update if nextDOM is or contains this.lock, except if\n                // it's a text node whose content already matches the new text\n                // and whose decorations match the new ones.\n                let locked = this.isLocked(nextDOM) &&\n                    !(node.isText && next.node && next.node.isText && next.nodeDOM.nodeValue == node.text &&\n                        next.dirty != NODE_DIRTY && sameOuterDeco(outerDeco, next.outerDeco));\n                if (!locked && next.update(node, outerDeco, innerDeco, view)) {\n                    this.destroyBetween(this.index, i);\n                    if (next.dom != nextDOM)\n                        this.changed = true;\n                    this.index++;\n                    return true;\n                }\n                else if (!locked && (updated = this.recreateWrapper(next, node, outerDeco, innerDeco, view, pos))) {\n                    this.destroyBetween(this.index, i);\n                    this.top.children[this.index] = updated;\n                    if (updated.contentDOM) {\n                        updated.dirty = CONTENT_DIRTY;\n                        updated.updateChildren(view, pos + 1);\n                        updated.dirty = NOT_DIRTY;\n                    }\n                    this.changed = true;\n                    this.index++;\n                    return true;\n                }\n                break;\n            }\n        }\n        return false;\n    }\n    // When a node with content is replaced by a different node with\n    // identical content, move over its children.\n    recreateWrapper(next, node, outerDeco, innerDeco, view, pos) {\n        if (next.dirty || node.isAtom || !next.children.length ||\n            !next.node.content.eq(node.content) ||\n            !sameOuterDeco(outerDeco, next.outerDeco) || !innerDeco.eq(next.innerDeco))\n            return null;\n        let wrapper = NodeViewDesc.create(this.top, node, outerDeco, innerDeco, view, pos);\n        if (wrapper.contentDOM) {\n            wrapper.children = next.children;\n            next.children = [];\n            for (let ch of wrapper.children)\n                ch.parent = wrapper;\n        }\n        next.destroy();\n        return wrapper;\n    }\n    // Insert the node as a newly created node desc.\n    addNode(node, outerDeco, innerDeco, view, pos) {\n        let desc = NodeViewDesc.create(this.top, node, outerDeco, innerDeco, view, pos);\n        if (desc.contentDOM)\n            desc.updateChildren(view, pos + 1);\n        this.top.children.splice(this.index++, 0, desc);\n        this.changed = true;\n    }\n    placeWidget(widget, view, pos) {\n        let next = this.index < this.top.children.length ? this.top.children[this.index] : null;\n        if (next && next.matchesWidget(widget) &&\n            (widget == next.widget || !next.widget.type.toDOM.parentNode)) {\n            this.index++;\n        }\n        else {\n            let desc = new WidgetViewDesc(this.top, widget, view, pos);\n            this.top.children.splice(this.index++, 0, desc);\n            this.changed = true;\n        }\n    }\n    // Make sure a textblock looks and behaves correctly in\n    // contentEditable.\n    addTextblockHacks() {\n        let lastChild = this.top.children[this.index - 1], parent = this.top;\n        while (lastChild instanceof MarkViewDesc) {\n            parent = lastChild;\n            lastChild = parent.children[parent.children.length - 1];\n        }\n        if (!lastChild || // Empty textblock\n            !(lastChild instanceof TextViewDesc) ||\n            /\\n$/.test(lastChild.node.text) ||\n            (this.view.requiresGeckoHackNode && /\\s$/.test(lastChild.node.text))) {\n            // Avoid bugs in Safari's cursor drawing (#1165) and Chrome's mouse selection (#1152)\n            if ((safari || chrome) && lastChild && lastChild.dom.contentEditable == \"false\")\n                this.addHackNode(\"IMG\", parent);\n            this.addHackNode(\"BR\", this.top);\n        }\n    }\n    addHackNode(nodeName, parent) {\n        if (parent == this.top && this.index < parent.children.length && parent.children[this.index].matchesHack(nodeName)) {\n            this.index++;\n        }\n        else {\n            let dom = document.createElement(nodeName);\n            if (nodeName == \"IMG\") {\n                dom.className = \"ProseMirror-separator\";\n                dom.alt = \"\";\n            }\n            if (nodeName == \"BR\")\n                dom.className = \"ProseMirror-trailingBreak\";\n            let hack = new TrailingHackViewDesc(this.top, [], dom, null);\n            if (parent != this.top)\n                parent.children.push(hack);\n            else\n                parent.children.splice(this.index++, 0, hack);\n            this.changed = true;\n        }\n    }\n    isLocked(node) {\n        return this.lock && (node == this.lock || node.nodeType == 1 && node.contains(this.lock.parentNode));\n    }\n}\n// Iterate from the end of the fragment and array of descs to find\n// directly matching ones, in order to avoid overeagerly reusing those\n// for other nodes. Returns the fragment index of the first node that\n// is part of the sequence of matched nodes at the end of the\n// fragment.\nfunction preMatch(frag, parentDesc) {\n    let curDesc = parentDesc, descI = curDesc.children.length;\n    let fI = frag.childCount, matched = new Map, matches = [];\n    outer: while (fI > 0) {\n        let desc;\n        for (;;) {\n            if (descI) {\n                let next = curDesc.children[descI - 1];\n                if (next instanceof MarkViewDesc) {\n                    curDesc = next;\n                    descI = next.children.length;\n                }\n                else {\n                    desc = next;\n                    descI--;\n                    break;\n                }\n            }\n            else if (curDesc == parentDesc) {\n                break outer;\n            }\n            else {\n                // FIXME\n                descI = curDesc.parent.children.indexOf(curDesc);\n                curDesc = curDesc.parent;\n            }\n        }\n        let node = desc.node;\n        if (!node)\n            continue;\n        if (node != frag.child(fI - 1))\n            break;\n        --fI;\n        matched.set(desc, fI);\n        matches.push(desc);\n    }\n    return { index: fI, matched, matches: matches.reverse() };\n}\nfunction compareSide(a, b) {\n    return a.type.side - b.type.side;\n}\n// This function abstracts iterating over the nodes and decorations in\n// a fragment. Calls `onNode` for each node, with its local and child\n// decorations. Splits text nodes when there is a decoration starting\n// or ending inside of them. Calls `onWidget` for each widget.\nfunction iterDeco(parent, deco, onWidget, onNode) {\n    let locals = deco.locals(parent), offset = 0;\n    // Simple, cheap variant for when there are no local decorations\n    if (locals.length == 0) {\n        for (let i = 0; i < parent.childCount; i++) {\n            let child = parent.child(i);\n            onNode(child, locals, deco.forChild(offset, child), i);\n            offset += child.nodeSize;\n        }\n        return;\n    }\n    let decoIndex = 0, active = [], restNode = null;\n    for (let parentIndex = 0;;) {\n        let widget, widgets;\n        while (decoIndex < locals.length && locals[decoIndex].to == offset) {\n            let next = locals[decoIndex++];\n            if (next.widget) {\n                if (!widget)\n                    widget = next;\n                else\n                    (widgets || (widgets = [widget])).push(next);\n            }\n        }\n        if (widget) {\n            if (widgets) {\n                widgets.sort(compareSide);\n                for (let i = 0; i < widgets.length; i++)\n                    onWidget(widgets[i], parentIndex, !!restNode);\n            }\n            else {\n                onWidget(widget, parentIndex, !!restNode);\n            }\n        }\n        let child, index;\n        if (restNode) {\n            index = -1;\n            child = restNode;\n            restNode = null;\n        }\n        else if (parentIndex < parent.childCount) {\n            index = parentIndex;\n            child = parent.child(parentIndex++);\n        }\n        else {\n            break;\n        }\n        for (let i = 0; i < active.length; i++)\n            if (active[i].to <= offset)\n                active.splice(i--, 1);\n        while (decoIndex < locals.length && locals[decoIndex].from <= offset && locals[decoIndex].to > offset)\n            active.push(locals[decoIndex++]);\n        let end = offset + child.nodeSize;\n        if (child.isText) {\n            let cutAt = end;\n            if (decoIndex < locals.length && locals[decoIndex].from < cutAt)\n                cutAt = locals[decoIndex].from;\n            for (let i = 0; i < active.length; i++)\n                if (active[i].to < cutAt)\n                    cutAt = active[i].to;\n            if (cutAt < end) {\n                restNode = child.cut(cutAt - offset);\n                child = child.cut(0, cutAt - offset);\n                end = cutAt;\n                index = -1;\n            }\n        }\n        else {\n            while (decoIndex < locals.length && locals[decoIndex].to < end)\n                decoIndex++;\n        }\n        let outerDeco = child.isInline && !child.isLeaf ? active.filter(d => !d.inline) : active.slice();\n        onNode(child, outerDeco, deco.forChild(offset, child), index);\n        offset = end;\n    }\n}\n// List markers in Mobile Safari will mysteriously disappear\n// sometimes. This works around that.\nfunction iosHacks(dom) {\n    if (dom.nodeName == \"UL\" || dom.nodeName == \"OL\") {\n        let oldCSS = dom.style.cssText;\n        dom.style.cssText = oldCSS + \"; list-style: square !important\";\n        window.getComputedStyle(dom).listStyle;\n        dom.style.cssText = oldCSS;\n    }\n}\n// Find a piece of text in an inline fragment, overlapping from-to\nfunction findTextInFragment(frag, text, from, to) {\n    for (let i = 0, pos = 0; i < frag.childCount && pos <= to;) {\n        let child = frag.child(i++), childStart = pos;\n        pos += child.nodeSize;\n        if (!child.isText)\n            continue;\n        let str = child.text;\n        while (i < frag.childCount) {\n            let next = frag.child(i++);\n            pos += next.nodeSize;\n            if (!next.isText)\n                break;\n            str += next.text;\n        }\n        if (pos >= from) {\n            if (pos >= to && str.slice(to - text.length - childStart, to - childStart) == text)\n                return to - text.length;\n            let found = childStart < to ? str.lastIndexOf(text, to - childStart - 1) : -1;\n            if (found >= 0 && found + text.length + childStart >= from)\n                return childStart + found;\n            if (from == to && str.length >= (to + text.length) - childStart &&\n                str.slice(to - childStart, to - childStart + text.length) == text)\n                return to;\n        }\n    }\n    return -1;\n}\n// Replace range from-to in an array of view descs with replacement\n// (may be null to just delete). This goes very much against the grain\n// of the rest of this code, which tends to create nodes with the\n// right shape in one go, rather than messing with them after\n// creation, but is necessary in the composition hack.\nfunction replaceNodes(nodes, from, to, view, replacement) {\n    let result = [];\n    for (let i = 0, off = 0; i < nodes.length; i++) {\n        let child = nodes[i], start = off, end = off += child.size;\n        if (start >= to || end <= from) {\n            result.push(child);\n        }\n        else {\n            if (start < from)\n                result.push(child.slice(0, from - start, view));\n            if (replacement) {\n                result.push(replacement);\n                replacement = undefined;\n            }\n            if (end > to)\n                result.push(child.slice(to - start, child.size, view));\n        }\n    }\n    return result;\n}\n\nfunction selectionFromDOM(view, origin = null) {\n    let domSel = view.domSelectionRange(), doc = view.state.doc;\n    if (!domSel.focusNode)\n        return null;\n    let nearestDesc = view.docView.nearestDesc(domSel.focusNode), inWidget = nearestDesc && nearestDesc.size == 0;\n    let head = view.docView.posFromDOM(domSel.focusNode, domSel.focusOffset, 1);\n    if (head < 0)\n        return null;\n    let $head = doc.resolve(head), anchor, selection;\n    if (selectionCollapsed(domSel)) {\n        anchor = head;\n        while (nearestDesc && !nearestDesc.node)\n            nearestDesc = nearestDesc.parent;\n        let nearestDescNode = nearestDesc.node;\n        if (nearestDesc && nearestDescNode.isAtom && NodeSelection.isSelectable(nearestDescNode) && nearestDesc.parent\n            && !(nearestDescNode.isInline && isOnEdge(domSel.focusNode, domSel.focusOffset, nearestDesc.dom))) {\n            let pos = nearestDesc.posBefore;\n            selection = new NodeSelection(head == pos ? $head : doc.resolve(pos));\n        }\n    }\n    else {\n        if (domSel instanceof view.dom.ownerDocument.defaultView.Selection && domSel.rangeCount > 1) {\n            let min = head, max = head;\n            for (let i = 0; i < domSel.rangeCount; i++) {\n                let range = domSel.getRangeAt(i);\n                min = Math.min(min, view.docView.posFromDOM(range.startContainer, range.startOffset, 1));\n                max = Math.max(max, view.docView.posFromDOM(range.endContainer, range.endOffset, -1));\n            }\n            if (min < 0)\n                return null;\n            [anchor, head] = max == view.state.selection.anchor ? [max, min] : [min, max];\n            $head = doc.resolve(head);\n        }\n        else {\n            anchor = view.docView.posFromDOM(domSel.anchorNode, domSel.anchorOffset, 1);\n        }\n        if (anchor < 0)\n            return null;\n    }\n    let $anchor = doc.resolve(anchor);\n    if (!selection) {\n        let bias = origin == \"pointer\" || (view.state.selection.head < $head.pos && !inWidget) ? 1 : -1;\n        selection = selectionBetween(view, $anchor, $head, bias);\n    }\n    return selection;\n}\nfunction editorOwnsSelection(view) {\n    return view.editable ? view.hasFocus() :\n        hasSelection(view) && document.activeElement && document.activeElement.contains(view.dom);\n}\nfunction selectionToDOM(view, force = false) {\n    let sel = view.state.selection;\n    syncNodeSelection(view, sel);\n    if (!editorOwnsSelection(view))\n        return;\n    // The delayed drag selection causes issues with Cell Selections\n    // in Safari. And the drag selection delay is to workarond issues\n    // which only present in Chrome.\n    if (!force && view.input.mouseDown && view.input.mouseDown.allowDefault && chrome) {\n        let domSel = view.domSelectionRange(), curSel = view.domObserver.currentSelection;\n        if (domSel.anchorNode && curSel.anchorNode &&\n            isEquivalentPosition(domSel.anchorNode, domSel.anchorOffset, curSel.anchorNode, curSel.anchorOffset)) {\n            view.input.mouseDown.delayedSelectionSync = true;\n            view.domObserver.setCurSelection();\n            return;\n        }\n    }\n    view.domObserver.disconnectSelection();\n    if (view.cursorWrapper) {\n        selectCursorWrapper(view);\n    }\n    else {\n        let { anchor, head } = sel, resetEditableFrom, resetEditableTo;\n        if (brokenSelectBetweenUneditable && !(sel instanceof TextSelection)) {\n            if (!sel.$from.parent.inlineContent)\n                resetEditableFrom = temporarilyEditableNear(view, sel.from);\n            if (!sel.empty && !sel.$from.parent.inlineContent)\n                resetEditableTo = temporarilyEditableNear(view, sel.to);\n        }\n        view.docView.setSelection(anchor, head, view, force);\n        if (brokenSelectBetweenUneditable) {\n            if (resetEditableFrom)\n                resetEditable(resetEditableFrom);\n            if (resetEditableTo)\n                resetEditable(resetEditableTo);\n        }\n        if (sel.visible) {\n            view.dom.classList.remove(\"ProseMirror-hideselection\");\n        }\n        else {\n            view.dom.classList.add(\"ProseMirror-hideselection\");\n            if (\"onselectionchange\" in document)\n                removeClassOnSelectionChange(view);\n        }\n    }\n    view.domObserver.setCurSelection();\n    view.domObserver.connectSelection();\n}\n// Kludge to work around Webkit not allowing a selection to start/end\n// between non-editable block nodes. We briefly make something\n// editable, set the selection, then set it uneditable again.\nconst brokenSelectBetweenUneditable = safari || chrome && chrome_version < 63;\nfunction temporarilyEditableNear(view, pos) {\n    let { node, offset } = view.docView.domFromPos(pos, 0);\n    let after = offset < node.childNodes.length ? node.childNodes[offset] : null;\n    let before = offset ? node.childNodes[offset - 1] : null;\n    if (safari && after && after.contentEditable == \"false\")\n        return setEditable(after);\n    if ((!after || after.contentEditable == \"false\") &&\n        (!before || before.contentEditable == \"false\")) {\n        if (after)\n            return setEditable(after);\n        else if (before)\n            return setEditable(before);\n    }\n}\nfunction setEditable(element) {\n    element.contentEditable = \"true\";\n    if (safari && element.draggable) {\n        element.draggable = false;\n        element.wasDraggable = true;\n    }\n    return element;\n}\nfunction resetEditable(element) {\n    element.contentEditable = \"false\";\n    if (element.wasDraggable) {\n        element.draggable = true;\n        element.wasDraggable = null;\n    }\n}\nfunction removeClassOnSelectionChange(view) {\n    let doc = view.dom.ownerDocument;\n    doc.removeEventListener(\"selectionchange\", view.input.hideSelectionGuard);\n    let domSel = view.domSelectionRange();\n    let node = domSel.anchorNode, offset = domSel.anchorOffset;\n    doc.addEventListener(\"selectionchange\", view.input.hideSelectionGuard = () => {\n        if (domSel.anchorNode != node || domSel.anchorOffset != offset) {\n            doc.removeEventListener(\"selectionchange\", view.input.hideSelectionGuard);\n            setTimeout(() => {\n                if (!editorOwnsSelection(view) || view.state.selection.visible)\n                    view.dom.classList.remove(\"ProseMirror-hideselection\");\n            }, 20);\n        }\n    });\n}\nfunction selectCursorWrapper(view) {\n    let domSel = view.domSelection(), range = document.createRange();\n    if (!domSel)\n        return;\n    let node = view.cursorWrapper.dom, img = node.nodeName == \"IMG\";\n    if (img)\n        range.setStart(node.parentNode, domIndex(node) + 1);\n    else\n        range.setStart(node, 0);\n    range.collapse(true);\n    domSel.removeAllRanges();\n    domSel.addRange(range);\n    // Kludge to kill 'control selection' in IE11 when selecting an\n    // invisible cursor wrapper, since that would result in those weird\n    // resize handles and a selection that considers the absolutely\n    // positioned wrapper, rather than the root editable node, the\n    // focused element.\n    if (!img && !view.state.selection.visible && ie && ie_version <= 11) {\n        node.disabled = true;\n        node.disabled = false;\n    }\n}\nfunction syncNodeSelection(view, sel) {\n    if (sel instanceof NodeSelection) {\n        let desc = view.docView.descAt(sel.from);\n        if (desc != view.lastSelectedViewDesc) {\n            clearNodeSelection(view);\n            if (desc)\n                desc.selectNode();\n            view.lastSelectedViewDesc = desc;\n        }\n    }\n    else {\n        clearNodeSelection(view);\n    }\n}\n// Clear all DOM statefulness of the last node selection.\nfunction clearNodeSelection(view) {\n    if (view.lastSelectedViewDesc) {\n        if (view.lastSelectedViewDesc.parent)\n            view.lastSelectedViewDesc.deselectNode();\n        view.lastSelectedViewDesc = undefined;\n    }\n}\nfunction selectionBetween(view, $anchor, $head, bias) {\n    return view.someProp(\"createSelectionBetween\", f => f(view, $anchor, $head))\n        || TextSelection.between($anchor, $head, bias);\n}\nfunction hasFocusAndSelection(view) {\n    if (view.editable && !view.hasFocus())\n        return false;\n    return hasSelection(view);\n}\nfunction hasSelection(view) {\n    let sel = view.domSelectionRange();\n    if (!sel.anchorNode)\n        return false;\n    try {\n        // Firefox will raise 'permission denied' errors when accessing\n        // properties of `sel.anchorNode` when it's in a generated CSS\n        // element.\n        return view.dom.contains(sel.anchorNode.nodeType == 3 ? sel.anchorNode.parentNode : sel.anchorNode) &&\n            (view.editable || view.dom.contains(sel.focusNode.nodeType == 3 ? sel.focusNode.parentNode : sel.focusNode));\n    }\n    catch (_) {\n        return false;\n    }\n}\nfunction anchorInRightPlace(view) {\n    let anchorDOM = view.docView.domFromPos(view.state.selection.anchor, 0);\n    let domSel = view.domSelectionRange();\n    return isEquivalentPosition(anchorDOM.node, anchorDOM.offset, domSel.anchorNode, domSel.anchorOffset);\n}\n\nfunction moveSelectionBlock(state, dir) {\n    let { $anchor, $head } = state.selection;\n    let $side = dir > 0 ? $anchor.max($head) : $anchor.min($head);\n    let $start = !$side.parent.inlineContent ? $side : $side.depth ? state.doc.resolve(dir > 0 ? $side.after() : $side.before()) : null;\n    return $start && Selection.findFrom($start, dir);\n}\nfunction apply(view, sel) {\n    view.dispatch(view.state.tr.setSelection(sel).scrollIntoView());\n    return true;\n}\nfunction selectHorizontally(view, dir, mods) {\n    let sel = view.state.selection;\n    if (sel instanceof TextSelection) {\n        if (mods.indexOf(\"s\") > -1) {\n            let { $head } = sel, node = $head.textOffset ? null : dir < 0 ? $head.nodeBefore : $head.nodeAfter;\n            if (!node || node.isText || !node.isLeaf)\n                return false;\n            let $newHead = view.state.doc.resolve($head.pos + node.nodeSize * (dir < 0 ? -1 : 1));\n            return apply(view, new TextSelection(sel.$anchor, $newHead));\n        }\n        else if (!sel.empty) {\n            return false;\n        }\n        else if (view.endOfTextblock(dir > 0 ? \"forward\" : \"backward\")) {\n            let next = moveSelectionBlock(view.state, dir);\n            if (next && (next instanceof NodeSelection))\n                return apply(view, next);\n            return false;\n        }\n        else if (!(mac && mods.indexOf(\"m\") > -1)) {\n            let $head = sel.$head, node = $head.textOffset ? null : dir < 0 ? $head.nodeBefore : $head.nodeAfter, desc;\n            if (!node || node.isText)\n                return false;\n            let nodePos = dir < 0 ? $head.pos - node.nodeSize : $head.pos;\n            if (!(node.isAtom || (desc = view.docView.descAt(nodePos)) && !desc.contentDOM))\n                return false;\n            if (NodeSelection.isSelectable(node)) {\n                return apply(view, new NodeSelection(dir < 0 ? view.state.doc.resolve($head.pos - node.nodeSize) : $head));\n            }\n            else if (webkit) {\n                // Chrome and Safari will introduce extra pointless cursor\n                // positions around inline uneditable nodes, so we have to\n                // take over and move the cursor past them (#937)\n                return apply(view, new TextSelection(view.state.doc.resolve(dir < 0 ? nodePos : nodePos + node.nodeSize)));\n            }\n            else {\n                return false;\n            }\n        }\n    }\n    else if (sel instanceof NodeSelection && sel.node.isInline) {\n        return apply(view, new TextSelection(dir > 0 ? sel.$to : sel.$from));\n    }\n    else {\n        let next = moveSelectionBlock(view.state, dir);\n        if (next)\n            return apply(view, next);\n        return false;\n    }\n}\nfunction nodeLen(node) {\n    return node.nodeType == 3 ? node.nodeValue.length : node.childNodes.length;\n}\nfunction isIgnorable(dom, dir) {\n    let desc = dom.pmViewDesc;\n    return desc && desc.size == 0 && (dir < 0 || dom.nextSibling || dom.nodeName != \"BR\");\n}\nfunction skipIgnoredNodes(view, dir) {\n    return dir < 0 ? skipIgnoredNodesBefore(view) : skipIgnoredNodesAfter(view);\n}\n// Make sure the cursor isn't directly after one or more ignored\n// nodes, which will confuse the browser's cursor motion logic.\nfunction skipIgnoredNodesBefore(view) {\n    let sel = view.domSelectionRange();\n    let node = sel.focusNode, offset = sel.focusOffset;\n    if (!node)\n        return;\n    let moveNode, moveOffset, force = false;\n    // Gecko will do odd things when the selection is directly in front\n    // of a non-editable node, so in that case, move it into the next\n    // node if possible. Issue prosemirror/prosemirror#832.\n    if (gecko && node.nodeType == 1 && offset < nodeLen(node) && isIgnorable(node.childNodes[offset], -1))\n        force = true;\n    for (;;) {\n        if (offset > 0) {\n            if (node.nodeType != 1) {\n                break;\n            }\n            else {\n                let before = node.childNodes[offset - 1];\n                if (isIgnorable(before, -1)) {\n                    moveNode = node;\n                    moveOffset = --offset;\n                }\n                else if (before.nodeType == 3) {\n                    node = before;\n                    offset = node.nodeValue.length;\n                }\n                else\n                    break;\n            }\n        }\n        else if (isBlockNode(node)) {\n            break;\n        }\n        else {\n            let prev = node.previousSibling;\n            while (prev && isIgnorable(prev, -1)) {\n                moveNode = node.parentNode;\n                moveOffset = domIndex(prev);\n                prev = prev.previousSibling;\n            }\n            if (!prev) {\n                node = node.parentNode;\n                if (node == view.dom)\n                    break;\n                offset = 0;\n            }\n            else {\n                node = prev;\n                offset = nodeLen(node);\n            }\n        }\n    }\n    if (force)\n        setSelFocus(view, node, offset);\n    else if (moveNode)\n        setSelFocus(view, moveNode, moveOffset);\n}\n// Make sure the cursor isn't directly before one or more ignored\n// nodes.\nfunction skipIgnoredNodesAfter(view) {\n    let sel = view.domSelectionRange();\n    let node = sel.focusNode, offset = sel.focusOffset;\n    if (!node)\n        return;\n    let len = nodeLen(node);\n    let moveNode, moveOffset;\n    for (;;) {\n        if (offset < len) {\n            if (node.nodeType != 1)\n                break;\n            let after = node.childNodes[offset];\n            if (isIgnorable(after, 1)) {\n                moveNode = node;\n                moveOffset = ++offset;\n            }\n            else\n                break;\n        }\n        else if (isBlockNode(node)) {\n            break;\n        }\n        else {\n            let next = node.nextSibling;\n            while (next && isIgnorable(next, 1)) {\n                moveNode = next.parentNode;\n                moveOffset = domIndex(next) + 1;\n                next = next.nextSibling;\n            }\n            if (!next) {\n                node = node.parentNode;\n                if (node == view.dom)\n                    break;\n                offset = len = 0;\n            }\n            else {\n                node = next;\n                offset = 0;\n                len = nodeLen(node);\n            }\n        }\n    }\n    if (moveNode)\n        setSelFocus(view, moveNode, moveOffset);\n}\nfunction isBlockNode(dom) {\n    let desc = dom.pmViewDesc;\n    return desc && desc.node && desc.node.isBlock;\n}\nfunction textNodeAfter(node, offset) {\n    while (node && offset == node.childNodes.length && !hasBlockDesc(node)) {\n        offset = domIndex(node) + 1;\n        node = node.parentNode;\n    }\n    while (node && offset < node.childNodes.length) {\n        let next = node.childNodes[offset];\n        if (next.nodeType == 3)\n            return next;\n        if (next.nodeType == 1 && next.contentEditable == \"false\")\n            break;\n        node = next;\n        offset = 0;\n    }\n}\nfunction textNodeBefore(node, offset) {\n    while (node && !offset && !hasBlockDesc(node)) {\n        offset = domIndex(node);\n        node = node.parentNode;\n    }\n    while (node && offset) {\n        let next = node.childNodes[offset - 1];\n        if (next.nodeType == 3)\n            return next;\n        if (next.nodeType == 1 && next.contentEditable == \"false\")\n            break;\n        node = next;\n        offset = node.childNodes.length;\n    }\n}\nfunction setSelFocus(view, node, offset) {\n    if (node.nodeType != 3) {\n        let before, after;\n        if (after = textNodeAfter(node, offset)) {\n            node = after;\n            offset = 0;\n        }\n        else if (before = textNodeBefore(node, offset)) {\n            node = before;\n            offset = before.nodeValue.length;\n        }\n    }\n    let sel = view.domSelection();\n    if (!sel)\n        return;\n    if (selectionCollapsed(sel)) {\n        let range = document.createRange();\n        range.setEnd(node, offset);\n        range.setStart(node, offset);\n        sel.removeAllRanges();\n        sel.addRange(range);\n    }\n    else if (sel.extend) {\n        sel.extend(node, offset);\n    }\n    view.domObserver.setCurSelection();\n    let { state } = view;\n    // If no state update ends up happening, reset the selection.\n    setTimeout(() => {\n        if (view.state == state)\n            selectionToDOM(view);\n    }, 50);\n}\nfunction findDirection(view, pos) {\n    let $pos = view.state.doc.resolve(pos);\n    if (!(chrome || windows) && $pos.parent.inlineContent) {\n        let coords = view.coordsAtPos(pos);\n        if (pos > $pos.start()) {\n            let before = view.coordsAtPos(pos - 1);\n            let mid = (before.top + before.bottom) / 2;\n            if (mid > coords.top && mid < coords.bottom && Math.abs(before.left - coords.left) > 1)\n                return before.left < coords.left ? \"ltr\" : \"rtl\";\n        }\n        if (pos < $pos.end()) {\n            let after = view.coordsAtPos(pos + 1);\n            let mid = (after.top + after.bottom) / 2;\n            if (mid > coords.top && mid < coords.bottom && Math.abs(after.left - coords.left) > 1)\n                return after.left > coords.left ? \"ltr\" : \"rtl\";\n        }\n    }\n    let computed = getComputedStyle(view.dom).direction;\n    return computed == \"rtl\" ? \"rtl\" : \"ltr\";\n}\n// Check whether vertical selection motion would involve node\n// selections. If so, apply it (if not, the result is left to the\n// browser)\nfunction selectVertically(view, dir, mods) {\n    let sel = view.state.selection;\n    if (sel instanceof TextSelection && !sel.empty || mods.indexOf(\"s\") > -1)\n        return false;\n    if (mac && mods.indexOf(\"m\") > -1)\n        return false;\n    let { $from, $to } = sel;\n    if (!$from.parent.inlineContent || view.endOfTextblock(dir < 0 ? \"up\" : \"down\")) {\n        let next = moveSelectionBlock(view.state, dir);\n        if (next && (next instanceof NodeSelection))\n            return apply(view, next);\n    }\n    if (!$from.parent.inlineContent) {\n        let side = dir < 0 ? $from : $to;\n        let beyond = sel instanceof AllSelection ? Selection.near(side, dir) : Selection.findFrom(side, dir);\n        return beyond ? apply(view, beyond) : false;\n    }\n    return false;\n}\nfunction stopNativeHorizontalDelete(view, dir) {\n    if (!(view.state.selection instanceof TextSelection))\n        return true;\n    let { $head, $anchor, empty } = view.state.selection;\n    if (!$head.sameParent($anchor))\n        return true;\n    if (!empty)\n        return false;\n    if (view.endOfTextblock(dir > 0 ? \"forward\" : \"backward\"))\n        return true;\n    let nextNode = !$head.textOffset && (dir < 0 ? $head.nodeBefore : $head.nodeAfter);\n    if (nextNode && !nextNode.isText) {\n        let tr = view.state.tr;\n        if (dir < 0)\n            tr.delete($head.pos - nextNode.nodeSize, $head.pos);\n        else\n            tr.delete($head.pos, $head.pos + nextNode.nodeSize);\n        view.dispatch(tr);\n        return true;\n    }\n    return false;\n}\nfunction switchEditable(view, node, state) {\n    view.domObserver.stop();\n    node.contentEditable = state;\n    view.domObserver.start();\n}\n// Issue #867 / #1090 / https://bugs.chromium.org/p/chromium/issues/detail?id=903821\n// In which Safari (and at some point in the past, Chrome) does really\n// wrong things when the down arrow is pressed when the cursor is\n// directly at the start of a textblock and has an uneditable node\n// after it\nfunction safariDownArrowBug(view) {\n    if (!safari || view.state.selection.$head.parentOffset > 0)\n        return false;\n    let { focusNode, focusOffset } = view.domSelectionRange();\n    if (focusNode && focusNode.nodeType == 1 && focusOffset == 0 &&\n        focusNode.firstChild && focusNode.firstChild.contentEditable == \"false\") {\n        let child = focusNode.firstChild;\n        switchEditable(view, child, \"true\");\n        setTimeout(() => switchEditable(view, child, \"false\"), 20);\n    }\n    return false;\n}\n// A backdrop key mapping used to make sure we always suppress keys\n// that have a dangerous default effect, even if the commands they are\n// bound to return false, and to make sure that cursor-motion keys\n// find a cursor (as opposed to a node selection) when pressed. For\n// cursor-motion keys, the code in the handlers also takes care of\n// block selections.\nfunction getMods(event) {\n    let result = \"\";\n    if (event.ctrlKey)\n        result += \"c\";\n    if (event.metaKey)\n        result += \"m\";\n    if (event.altKey)\n        result += \"a\";\n    if (event.shiftKey)\n        result += \"s\";\n    return result;\n}\nfunction captureKeyDown(view, event) {\n    let code = event.keyCode, mods = getMods(event);\n    if (code == 8 || (mac && code == 72 && mods == \"c\")) { // Backspace, Ctrl-h on Mac\n        return stopNativeHorizontalDelete(view, -1) || skipIgnoredNodes(view, -1);\n    }\n    else if ((code == 46 && !event.shiftKey) || (mac && code == 68 && mods == \"c\")) { // Delete, Ctrl-d on Mac\n        return stopNativeHorizontalDelete(view, 1) || skipIgnoredNodes(view, 1);\n    }\n    else if (code == 13 || code == 27) { // Enter, Esc\n        return true;\n    }\n    else if (code == 37 || (mac && code == 66 && mods == \"c\")) { // Left arrow, Ctrl-b on Mac\n        let dir = code == 37 ? (findDirection(view, view.state.selection.from) == \"ltr\" ? -1 : 1) : -1;\n        return selectHorizontally(view, dir, mods) || skipIgnoredNodes(view, dir);\n    }\n    else if (code == 39 || (mac && code == 70 && mods == \"c\")) { // Right arrow, Ctrl-f on Mac\n        let dir = code == 39 ? (findDirection(view, view.state.selection.from) == \"ltr\" ? 1 : -1) : 1;\n        return selectHorizontally(view, dir, mods) || skipIgnoredNodes(view, dir);\n    }\n    else if (code == 38 || (mac && code == 80 && mods == \"c\")) { // Up arrow, Ctrl-p on Mac\n        return selectVertically(view, -1, mods) || skipIgnoredNodes(view, -1);\n    }\n    else if (code == 40 || (mac && code == 78 && mods == \"c\")) { // Down arrow, Ctrl-n on Mac\n        return safariDownArrowBug(view) || selectVertically(view, 1, mods) || skipIgnoredNodes(view, 1);\n    }\n    else if (mods == (mac ? \"m\" : \"c\") &&\n        (code == 66 || code == 73 || code == 89 || code == 90)) { // Mod-[biyz]\n        return true;\n    }\n    return false;\n}\n\nfunction serializeForClipboard(view, slice) {\n    view.someProp(\"transformCopied\", f => { slice = f(slice, view); });\n    let context = [], { content, openStart, openEnd } = slice;\n    while (openStart > 1 && openEnd > 1 && content.childCount == 1 && content.firstChild.childCount == 1) {\n        openStart--;\n        openEnd--;\n        let node = content.firstChild;\n        context.push(node.type.name, node.attrs != node.type.defaultAttrs ? node.attrs : null);\n        content = node.content;\n    }\n    let serializer = view.someProp(\"clipboardSerializer\") || DOMSerializer.fromSchema(view.state.schema);\n    let doc = detachedDoc(), wrap = doc.createElement(\"div\");\n    wrap.appendChild(serializer.serializeFragment(content, { document: doc }));\n    let firstChild = wrap.firstChild, needsWrap, wrappers = 0;\n    while (firstChild && firstChild.nodeType == 1 && (needsWrap = wrapMap[firstChild.nodeName.toLowerCase()])) {\n        for (let i = needsWrap.length - 1; i >= 0; i--) {\n            let wrapper = doc.createElement(needsWrap[i]);\n            while (wrap.firstChild)\n                wrapper.appendChild(wrap.firstChild);\n            wrap.appendChild(wrapper);\n            wrappers++;\n        }\n        firstChild = wrap.firstChild;\n    }\n    if (firstChild && firstChild.nodeType == 1)\n        firstChild.setAttribute(\"data-pm-slice\", `${openStart} ${openEnd}${wrappers ? ` -${wrappers}` : \"\"} ${JSON.stringify(context)}`);\n    let text = view.someProp(\"clipboardTextSerializer\", f => f(slice, view)) ||\n        slice.content.textBetween(0, slice.content.size, \"\\n\\n\");\n    return { dom: wrap, text, slice };\n}\n// Read a slice of content from the clipboard (or drop data).\nfunction parseFromClipboard(view, text, html, plainText, $context) {\n    let inCode = $context.parent.type.spec.code;\n    let dom, slice;\n    if (!html && !text)\n        return null;\n    let asText = text && (plainText || inCode || !html);\n    if (asText) {\n        view.someProp(\"transformPastedText\", f => { text = f(text, inCode || plainText, view); });\n        if (inCode)\n            return text ? new Slice(Fragment.from(view.state.schema.text(text.replace(/\\r\\n?/g, \"\\n\"))), 0, 0) : Slice.empty;\n        let parsed = view.someProp(\"clipboardTextParser\", f => f(text, $context, plainText, view));\n        if (parsed) {\n            slice = parsed;\n        }\n        else {\n            let marks = $context.marks();\n            let { schema } = view.state, serializer = DOMSerializer.fromSchema(schema);\n            dom = document.createElement(\"div\");\n            text.split(/(?:\\r\\n?|\\n)+/).forEach(block => {\n                let p = dom.appendChild(document.createElement(\"p\"));\n                if (block)\n                    p.appendChild(serializer.serializeNode(schema.text(block, marks)));\n            });\n        }\n    }\n    else {\n        view.someProp(\"transformPastedHTML\", f => { html = f(html, view); });\n        dom = readHTML(html);\n        if (webkit)\n            restoreReplacedSpaces(dom);\n    }\n    let contextNode = dom && dom.querySelector(\"[data-pm-slice]\");\n    let sliceData = contextNode && /^(\\d+) (\\d+)(?: -(\\d+))? (.*)/.exec(contextNode.getAttribute(\"data-pm-slice\") || \"\");\n    if (sliceData && sliceData[3])\n        for (let i = +sliceData[3]; i > 0; i--) {\n            let child = dom.firstChild;\n            while (child && child.nodeType != 1)\n                child = child.nextSibling;\n            if (!child)\n                break;\n            dom = child;\n        }\n    if (!slice) {\n        let parser = view.someProp(\"clipboardParser\") || view.someProp(\"domParser\") || DOMParser.fromSchema(view.state.schema);\n        slice = parser.parseSlice(dom, {\n            preserveWhitespace: !!(asText || sliceData),\n            context: $context,\n            ruleFromNode(dom) {\n                if (dom.nodeName == \"BR\" && !dom.nextSibling &&\n                    dom.parentNode && !inlineParents.test(dom.parentNode.nodeName))\n                    return { ignore: true };\n                return null;\n            }\n        });\n    }\n    if (sliceData) {\n        slice = addContext(closeSlice(slice, +sliceData[1], +sliceData[2]), sliceData[4]);\n    }\n    else { // HTML wasn't created by ProseMirror. Make sure top-level siblings are coherent\n        slice = Slice.maxOpen(normalizeSiblings(slice.content, $context), true);\n        if (slice.openStart || slice.openEnd) {\n            let openStart = 0, openEnd = 0;\n            for (let node = slice.content.firstChild; openStart < slice.openStart && !node.type.spec.isolating; openStart++, node = node.firstChild) { }\n            for (let node = slice.content.lastChild; openEnd < slice.openEnd && !node.type.spec.isolating; openEnd++, node = node.lastChild) { }\n            slice = closeSlice(slice, openStart, openEnd);\n        }\n    }\n    view.someProp(\"transformPasted\", f => { slice = f(slice, view); });\n    return slice;\n}\nconst inlineParents = /^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;\n// Takes a slice parsed with parseSlice, which means there hasn't been\n// any content-expression checking done on the top nodes, tries to\n// find a parent node in the current context that might fit the nodes,\n// and if successful, rebuilds the slice so that it fits into that parent.\n//\n// This addresses the problem that Transform.replace expects a\n// coherent slice, and will fail to place a set of siblings that don't\n// fit anywhere in the schema.\nfunction normalizeSiblings(fragment, $context) {\n    if (fragment.childCount < 2)\n        return fragment;\n    for (let d = $context.depth; d >= 0; d--) {\n        let parent = $context.node(d);\n        let match = parent.contentMatchAt($context.index(d));\n        let lastWrap, result = [];\n        fragment.forEach(node => {\n            if (!result)\n                return;\n            let wrap = match.findWrapping(node.type), inLast;\n            if (!wrap)\n                return result = null;\n            if (inLast = result.length && lastWrap.length && addToSibling(wrap, lastWrap, node, result[result.length - 1], 0)) {\n                result[result.length - 1] = inLast;\n            }\n            else {\n                if (result.length)\n                    result[result.length - 1] = closeRight(result[result.length - 1], lastWrap.length);\n                let wrapped = withWrappers(node, wrap);\n                result.push(wrapped);\n                match = match.matchType(wrapped.type);\n                lastWrap = wrap;\n            }\n        });\n        if (result)\n            return Fragment.from(result);\n    }\n    return fragment;\n}\nfunction withWrappers(node, wrap, from = 0) {\n    for (let i = wrap.length - 1; i >= from; i--)\n        node = wrap[i].create(null, Fragment.from(node));\n    return node;\n}\n// Used to group adjacent nodes wrapped in similar parents by\n// normalizeSiblings into the same parent node\nfunction addToSibling(wrap, lastWrap, node, sibling, depth) {\n    if (depth < wrap.length && depth < lastWrap.length && wrap[depth] == lastWrap[depth]) {\n        let inner = addToSibling(wrap, lastWrap, node, sibling.lastChild, depth + 1);\n        if (inner)\n            return sibling.copy(sibling.content.replaceChild(sibling.childCount - 1, inner));\n        let match = sibling.contentMatchAt(sibling.childCount);\n        if (match.matchType(depth == wrap.length - 1 ? node.type : wrap[depth + 1]))\n            return sibling.copy(sibling.content.append(Fragment.from(withWrappers(node, wrap, depth + 1))));\n    }\n}\nfunction closeRight(node, depth) {\n    if (depth == 0)\n        return node;\n    let fragment = node.content.replaceChild(node.childCount - 1, closeRight(node.lastChild, depth - 1));\n    let fill = node.contentMatchAt(node.childCount).fillBefore(Fragment.empty, true);\n    return node.copy(fragment.append(fill));\n}\nfunction closeRange(fragment, side, from, to, depth, openEnd) {\n    let node = side < 0 ? fragment.firstChild : fragment.lastChild, inner = node.content;\n    if (fragment.childCount > 1)\n        openEnd = 0;\n    if (depth < to - 1)\n        inner = closeRange(inner, side, from, to, depth + 1, openEnd);\n    if (depth >= from)\n        inner = side < 0 ? node.contentMatchAt(0).fillBefore(inner, openEnd <= depth).append(inner)\n            : inner.append(node.contentMatchAt(node.childCount).fillBefore(Fragment.empty, true));\n    return fragment.replaceChild(side < 0 ? 0 : fragment.childCount - 1, node.copy(inner));\n}\nfunction closeSlice(slice, openStart, openEnd) {\n    if (openStart < slice.openStart)\n        slice = new Slice(closeRange(slice.content, -1, openStart, slice.openStart, 0, slice.openEnd), openStart, slice.openEnd);\n    if (openEnd < slice.openEnd)\n        slice = new Slice(closeRange(slice.content, 1, openEnd, slice.openEnd, 0, 0), slice.openStart, openEnd);\n    return slice;\n}\n// Trick from jQuery -- some elements must be wrapped in other\n// elements for innerHTML to work. I.e. if you do `div.innerHTML =\n// \"<td>..</td>\"` the table cells are ignored.\nconst wrapMap = {\n    thead: [\"table\"],\n    tbody: [\"table\"],\n    tfoot: [\"table\"],\n    caption: [\"table\"],\n    colgroup: [\"table\"],\n    col: [\"table\", \"colgroup\"],\n    tr: [\"table\", \"tbody\"],\n    td: [\"table\", \"tbody\", \"tr\"],\n    th: [\"table\", \"tbody\", \"tr\"]\n};\nlet _detachedDoc = null;\nfunction detachedDoc() {\n    return _detachedDoc || (_detachedDoc = document.implementation.createHTMLDocument(\"title\"));\n}\nlet _policy = null;\nfunction maybeWrapTrusted(html) {\n    let trustedTypes = window.trustedTypes;\n    if (!trustedTypes)\n        return html;\n    // With the require-trusted-types-for CSP, Chrome will block\n    // innerHTML, even on a detached document. This wraps the string in\n    // a way that makes the browser allow us to use its parser again.\n    if (!_policy)\n        _policy = trustedTypes.defaultPolicy || trustedTypes.createPolicy(\"ProseMirrorClipboard\", { createHTML: (s) => s });\n    return _policy.createHTML(html);\n}\nfunction readHTML(html) {\n    let metas = /^(\\s*<meta [^>]*>)*/.exec(html);\n    if (metas)\n        html = html.slice(metas[0].length);\n    let elt = detachedDoc().createElement(\"div\");\n    let firstTag = /<([a-z][^>\\s]+)/i.exec(html), wrap;\n    if (wrap = firstTag && wrapMap[firstTag[1].toLowerCase()])\n        html = wrap.map(n => \"<\" + n + \">\").join(\"\") + html + wrap.map(n => \"</\" + n + \">\").reverse().join(\"\");\n    elt.innerHTML = maybeWrapTrusted(html);\n    if (wrap)\n        for (let i = 0; i < wrap.length; i++)\n            elt = elt.querySelector(wrap[i]) || elt;\n    return elt;\n}\n// Webkit browsers do some hard-to-predict replacement of regular\n// spaces with non-breaking spaces when putting content on the\n// clipboard. This tries to convert such non-breaking spaces (which\n// will be wrapped in a plain span on Chrome, a span with class\n// Apple-converted-space on Safari) back to regular spaces.\nfunction restoreReplacedSpaces(dom) {\n    let nodes = dom.querySelectorAll(chrome ? \"span:not([class]):not([style])\" : \"span.Apple-converted-space\");\n    for (let i = 0; i < nodes.length; i++) {\n        let node = nodes[i];\n        if (node.childNodes.length == 1 && node.textContent == \"\\u00a0\" && node.parentNode)\n            node.parentNode.replaceChild(dom.ownerDocument.createTextNode(\" \"), node);\n    }\n}\nfunction addContext(slice, context) {\n    if (!slice.size)\n        return slice;\n    let schema = slice.content.firstChild.type.schema, array;\n    try {\n        array = JSON.parse(context);\n    }\n    catch (e) {\n        return slice;\n    }\n    let { content, openStart, openEnd } = slice;\n    for (let i = array.length - 2; i >= 0; i -= 2) {\n        let type = schema.nodes[array[i]];\n        if (!type || type.hasRequiredAttrs())\n            break;\n        content = Fragment.from(type.create(array[i + 1], content));\n        openStart++;\n        openEnd++;\n    }\n    return new Slice(content, openStart, openEnd);\n}\n\n// A collection of DOM events that occur within the editor, and callback functions\n// to invoke when the event fires.\nconst handlers = {};\nconst editHandlers = {};\nconst passiveHandlers = { touchstart: true, touchmove: true };\nclass InputState {\n    constructor() {\n        this.shiftKey = false;\n        this.mouseDown = null;\n        this.lastKeyCode = null;\n        this.lastKeyCodeTime = 0;\n        this.lastClick = { time: 0, x: 0, y: 0, type: \"\", button: 0 };\n        this.lastSelectionOrigin = null;\n        this.lastSelectionTime = 0;\n        this.lastIOSEnter = 0;\n        this.lastIOSEnterFallbackTimeout = -1;\n        this.lastFocus = 0;\n        this.lastTouch = 0;\n        this.lastChromeDelete = 0;\n        this.composing = false;\n        this.compositionNode = null;\n        this.composingTimeout = -1;\n        this.compositionNodes = [];\n        this.compositionEndedAt = -2e8;\n        this.compositionID = 1;\n        // Set to a composition ID when there are pending changes at compositionend\n        this.compositionPendingChanges = 0;\n        this.domChangeCount = 0;\n        this.eventHandlers = Object.create(null);\n        this.hideSelectionGuard = null;\n    }\n}\nfunction initInput(view) {\n    for (let event in handlers) {\n        let handler = handlers[event];\n        view.dom.addEventListener(event, view.input.eventHandlers[event] = (event) => {\n            if (eventBelongsToView(view, event) && !runCustomHandler(view, event) &&\n                (view.editable || !(event.type in editHandlers)))\n                handler(view, event);\n        }, passiveHandlers[event] ? { passive: true } : undefined);\n    }\n    // On Safari, for reasons beyond my understanding, adding an input\n    // event handler makes an issue where the composition vanishes when\n    // you press enter go away.\n    if (safari)\n        view.dom.addEventListener(\"input\", () => null);\n    ensureListeners(view);\n}\nfunction setSelectionOrigin(view, origin) {\n    view.input.lastSelectionOrigin = origin;\n    view.input.lastSelectionTime = Date.now();\n}\nfunction destroyInput(view) {\n    view.domObserver.stop();\n    for (let type in view.input.eventHandlers)\n        view.dom.removeEventListener(type, view.input.eventHandlers[type]);\n    clearTimeout(view.input.composingTimeout);\n    clearTimeout(view.input.lastIOSEnterFallbackTimeout);\n}\nfunction ensureListeners(view) {\n    view.someProp(\"handleDOMEvents\", currentHandlers => {\n        for (let type in currentHandlers)\n            if (!view.input.eventHandlers[type])\n                view.dom.addEventListener(type, view.input.eventHandlers[type] = event => runCustomHandler(view, event));\n    });\n}\nfunction runCustomHandler(view, event) {\n    return view.someProp(\"handleDOMEvents\", handlers => {\n        let handler = handlers[event.type];\n        return handler ? handler(view, event) || event.defaultPrevented : false;\n    });\n}\nfunction eventBelongsToView(view, event) {\n    if (!event.bubbles)\n        return true;\n    if (event.defaultPrevented)\n        return false;\n    for (let node = event.target; node != view.dom; node = node.parentNode)\n        if (!node || node.nodeType == 11 ||\n            (node.pmViewDesc && node.pmViewDesc.stopEvent(event)))\n            return false;\n    return true;\n}\nfunction dispatchEvent(view, event) {\n    if (!runCustomHandler(view, event) && handlers[event.type] &&\n        (view.editable || !(event.type in editHandlers)))\n        handlers[event.type](view, event);\n}\neditHandlers.keydown = (view, _event) => {\n    let event = _event;\n    view.input.shiftKey = event.keyCode == 16 || event.shiftKey;\n    if (inOrNearComposition(view, event))\n        return;\n    view.input.lastKeyCode = event.keyCode;\n    view.input.lastKeyCodeTime = Date.now();\n    // Suppress enter key events on Chrome Android, because those tend\n    // to be part of a confused sequence of composition events fired,\n    // and handling them eagerly tends to corrupt the input.\n    if (android && chrome && event.keyCode == 13)\n        return;\n    if (event.keyCode != 229)\n        view.domObserver.forceFlush();\n    // On iOS, if we preventDefault enter key presses, the virtual\n    // keyboard gets confused. So the hack here is to set a flag that\n    // makes the DOM change code recognize that what just happens should\n    // be replaced by whatever the Enter key handlers do.\n    if (ios && event.keyCode == 13 && !event.ctrlKey && !event.altKey && !event.metaKey) {\n        let now = Date.now();\n        view.input.lastIOSEnter = now;\n        view.input.lastIOSEnterFallbackTimeout = setTimeout(() => {\n            if (view.input.lastIOSEnter == now) {\n                view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\")));\n                view.input.lastIOSEnter = 0;\n            }\n        }, 200);\n    }\n    else if (view.someProp(\"handleKeyDown\", f => f(view, event)) || captureKeyDown(view, event)) {\n        event.preventDefault();\n    }\n    else {\n        setSelectionOrigin(view, \"key\");\n    }\n};\neditHandlers.keyup = (view, event) => {\n    if (event.keyCode == 16)\n        view.input.shiftKey = false;\n};\neditHandlers.keypress = (view, _event) => {\n    let event = _event;\n    if (inOrNearComposition(view, event) || !event.charCode ||\n        event.ctrlKey && !event.altKey || mac && event.metaKey)\n        return;\n    if (view.someProp(\"handleKeyPress\", f => f(view, event))) {\n        event.preventDefault();\n        return;\n    }\n    let sel = view.state.selection;\n    if (!(sel instanceof TextSelection) || !sel.$from.sameParent(sel.$to)) {\n        let text = String.fromCharCode(event.charCode);\n        let deflt = () => view.state.tr.insertText(text).scrollIntoView();\n        if (!/[\\r\\n]/.test(text) && !view.someProp(\"handleTextInput\", f => f(view, sel.$from.pos, sel.$to.pos, text, deflt)))\n            view.dispatch(deflt());\n        event.preventDefault();\n    }\n};\nfunction eventCoords(event) { return { left: event.clientX, top: event.clientY }; }\nfunction isNear(event, click) {\n    let dx = click.x - event.clientX, dy = click.y - event.clientY;\n    return dx * dx + dy * dy < 100;\n}\nfunction runHandlerOnContext(view, propName, pos, inside, event) {\n    if (inside == -1)\n        return false;\n    let $pos = view.state.doc.resolve(inside);\n    for (let i = $pos.depth + 1; i > 0; i--) {\n        if (view.someProp(propName, f => i > $pos.depth ? f(view, pos, $pos.nodeAfter, $pos.before(i), event, true)\n            : f(view, pos, $pos.node(i), $pos.before(i), event, false)))\n            return true;\n    }\n    return false;\n}\nfunction updateSelection(view, selection, origin) {\n    if (!view.focused)\n        view.focus();\n    if (view.state.selection.eq(selection))\n        return;\n    let tr = view.state.tr.setSelection(selection);\n    if (origin == \"pointer\")\n        tr.setMeta(\"pointer\", true);\n    view.dispatch(tr);\n}\nfunction selectClickedLeaf(view, inside) {\n    if (inside == -1)\n        return false;\n    let $pos = view.state.doc.resolve(inside), node = $pos.nodeAfter;\n    if (node && node.isAtom && NodeSelection.isSelectable(node)) {\n        updateSelection(view, new NodeSelection($pos), \"pointer\");\n        return true;\n    }\n    return false;\n}\nfunction selectClickedNode(view, inside) {\n    if (inside == -1)\n        return false;\n    let sel = view.state.selection, selectedNode, selectAt;\n    if (sel instanceof NodeSelection)\n        selectedNode = sel.node;\n    let $pos = view.state.doc.resolve(inside);\n    for (let i = $pos.depth + 1; i > 0; i--) {\n        let node = i > $pos.depth ? $pos.nodeAfter : $pos.node(i);\n        if (NodeSelection.isSelectable(node)) {\n            if (selectedNode && sel.$from.depth > 0 &&\n                i >= sel.$from.depth && $pos.before(sel.$from.depth + 1) == sel.$from.pos)\n                selectAt = $pos.before(sel.$from.depth);\n            else\n                selectAt = $pos.before(i);\n            break;\n        }\n    }\n    if (selectAt != null) {\n        updateSelection(view, NodeSelection.create(view.state.doc, selectAt), \"pointer\");\n        return true;\n    }\n    else {\n        return false;\n    }\n}\nfunction handleSingleClick(view, pos, inside, event, selectNode) {\n    return runHandlerOnContext(view, \"handleClickOn\", pos, inside, event) ||\n        view.someProp(\"handleClick\", f => f(view, pos, event)) ||\n        (selectNode ? selectClickedNode(view, inside) : selectClickedLeaf(view, inside));\n}\nfunction handleDoubleClick(view, pos, inside, event) {\n    return runHandlerOnContext(view, \"handleDoubleClickOn\", pos, inside, event) ||\n        view.someProp(\"handleDoubleClick\", f => f(view, pos, event));\n}\nfunction handleTripleClick(view, pos, inside, event) {\n    return runHandlerOnContext(view, \"handleTripleClickOn\", pos, inside, event) ||\n        view.someProp(\"handleTripleClick\", f => f(view, pos, event)) ||\n        defaultTripleClick(view, inside, event);\n}\nfunction defaultTripleClick(view, inside, event) {\n    if (event.button != 0)\n        return false;\n    let doc = view.state.doc;\n    if (inside == -1) {\n        if (doc.inlineContent) {\n            updateSelection(view, TextSelection.create(doc, 0, doc.content.size), \"pointer\");\n            return true;\n        }\n        return false;\n    }\n    let $pos = doc.resolve(inside);\n    for (let i = $pos.depth + 1; i > 0; i--) {\n        let node = i > $pos.depth ? $pos.nodeAfter : $pos.node(i);\n        let nodePos = $pos.before(i);\n        if (node.inlineContent)\n            updateSelection(view, TextSelection.create(doc, nodePos + 1, nodePos + 1 + node.content.size), \"pointer\");\n        else if (NodeSelection.isSelectable(node))\n            updateSelection(view, NodeSelection.create(doc, nodePos), \"pointer\");\n        else\n            continue;\n        return true;\n    }\n}\nfunction forceDOMFlush(view) {\n    return endComposition(view);\n}\nconst selectNodeModifier = mac ? \"metaKey\" : \"ctrlKey\";\nhandlers.mousedown = (view, _event) => {\n    let event = _event;\n    view.input.shiftKey = event.shiftKey;\n    let flushed = forceDOMFlush(view);\n    let now = Date.now(), type = \"singleClick\";\n    if (now - view.input.lastClick.time < 500 && isNear(event, view.input.lastClick) && !event[selectNodeModifier] &&\n        view.input.lastClick.button == event.button) {\n        if (view.input.lastClick.type == \"singleClick\")\n            type = \"doubleClick\";\n        else if (view.input.lastClick.type == \"doubleClick\")\n            type = \"tripleClick\";\n    }\n    view.input.lastClick = { time: now, x: event.clientX, y: event.clientY, type, button: event.button };\n    let pos = view.posAtCoords(eventCoords(event));\n    if (!pos)\n        return;\n    if (type == \"singleClick\") {\n        if (view.input.mouseDown)\n            view.input.mouseDown.done();\n        view.input.mouseDown = new MouseDown(view, pos, event, !!flushed);\n    }\n    else if ((type == \"doubleClick\" ? handleDoubleClick : handleTripleClick)(view, pos.pos, pos.inside, event)) {\n        event.preventDefault();\n    }\n    else {\n        setSelectionOrigin(view, \"pointer\");\n    }\n};\nclass MouseDown {\n    constructor(view, pos, event, flushed) {\n        this.view = view;\n        this.pos = pos;\n        this.event = event;\n        this.flushed = flushed;\n        this.delayedSelectionSync = false;\n        this.mightDrag = null;\n        this.startDoc = view.state.doc;\n        this.selectNode = !!event[selectNodeModifier];\n        this.allowDefault = event.shiftKey;\n        let targetNode, targetPos;\n        if (pos.inside > -1) {\n            targetNode = view.state.doc.nodeAt(pos.inside);\n            targetPos = pos.inside;\n        }\n        else {\n            let $pos = view.state.doc.resolve(pos.pos);\n            targetNode = $pos.parent;\n            targetPos = $pos.depth ? $pos.before() : 0;\n        }\n        const target = flushed ? null : event.target;\n        const targetDesc = target ? view.docView.nearestDesc(target, true) : null;\n        this.target = targetDesc && targetDesc.dom.nodeType == 1 ? targetDesc.dom : null;\n        let { selection } = view.state;\n        if (event.button == 0 &&\n            targetNode.type.spec.draggable && targetNode.type.spec.selectable !== false ||\n            selection instanceof NodeSelection && selection.from <= targetPos && selection.to > targetPos)\n            this.mightDrag = {\n                node: targetNode,\n                pos: targetPos,\n                addAttr: !!(this.target && !this.target.draggable),\n                setUneditable: !!(this.target && gecko && !this.target.hasAttribute(\"contentEditable\"))\n            };\n        if (this.target && this.mightDrag && (this.mightDrag.addAttr || this.mightDrag.setUneditable)) {\n            this.view.domObserver.stop();\n            if (this.mightDrag.addAttr)\n                this.target.draggable = true;\n            if (this.mightDrag.setUneditable)\n                setTimeout(() => {\n                    if (this.view.input.mouseDown == this)\n                        this.target.setAttribute(\"contentEditable\", \"false\");\n                }, 20);\n            this.view.domObserver.start();\n        }\n        view.root.addEventListener(\"mouseup\", this.up = this.up.bind(this));\n        view.root.addEventListener(\"mousemove\", this.move = this.move.bind(this));\n        setSelectionOrigin(view, \"pointer\");\n    }\n    done() {\n        this.view.root.removeEventListener(\"mouseup\", this.up);\n        this.view.root.removeEventListener(\"mousemove\", this.move);\n        if (this.mightDrag && this.target) {\n            this.view.domObserver.stop();\n            if (this.mightDrag.addAttr)\n                this.target.removeAttribute(\"draggable\");\n            if (this.mightDrag.setUneditable)\n                this.target.removeAttribute(\"contentEditable\");\n            this.view.domObserver.start();\n        }\n        if (this.delayedSelectionSync)\n            setTimeout(() => selectionToDOM(this.view));\n        this.view.input.mouseDown = null;\n    }\n    up(event) {\n        this.done();\n        if (!this.view.dom.contains(event.target))\n            return;\n        let pos = this.pos;\n        if (this.view.state.doc != this.startDoc)\n            pos = this.view.posAtCoords(eventCoords(event));\n        this.updateAllowDefault(event);\n        if (this.allowDefault || !pos) {\n            setSelectionOrigin(this.view, \"pointer\");\n        }\n        else if (handleSingleClick(this.view, pos.pos, pos.inside, event, this.selectNode)) {\n            event.preventDefault();\n        }\n        else if (event.button == 0 &&\n            (this.flushed ||\n                // Safari ignores clicks on draggable elements\n                (safari && this.mightDrag && !this.mightDrag.node.isAtom) ||\n                // Chrome will sometimes treat a node selection as a\n                // cursor, but still report that the node is selected\n                // when asked through getSelection. You'll then get a\n                // situation where clicking at the point where that\n                // (hidden) cursor is doesn't change the selection, and\n                // thus doesn't get a reaction from ProseMirror. This\n                // works around that.\n                (chrome && !this.view.state.selection.visible &&\n                    Math.min(Math.abs(pos.pos - this.view.state.selection.from), Math.abs(pos.pos - this.view.state.selection.to)) <= 2))) {\n            updateSelection(this.view, Selection.near(this.view.state.doc.resolve(pos.pos)), \"pointer\");\n            event.preventDefault();\n        }\n        else {\n            setSelectionOrigin(this.view, \"pointer\");\n        }\n    }\n    move(event) {\n        this.updateAllowDefault(event);\n        setSelectionOrigin(this.view, \"pointer\");\n        if (event.buttons == 0)\n            this.done();\n    }\n    updateAllowDefault(event) {\n        if (!this.allowDefault && (Math.abs(this.event.x - event.clientX) > 4 ||\n            Math.abs(this.event.y - event.clientY) > 4))\n            this.allowDefault = true;\n    }\n}\nhandlers.touchstart = view => {\n    view.input.lastTouch = Date.now();\n    forceDOMFlush(view);\n    setSelectionOrigin(view, \"pointer\");\n};\nhandlers.touchmove = view => {\n    view.input.lastTouch = Date.now();\n    setSelectionOrigin(view, \"pointer\");\n};\nhandlers.contextmenu = view => forceDOMFlush(view);\nfunction inOrNearComposition(view, event) {\n    if (view.composing)\n        return true;\n    // See https://www.stum.de/2016/06/24/handling-ime-events-in-javascript/.\n    // On Japanese input method editors (IMEs), the Enter key is used to confirm character\n    // selection. On Safari, when Enter is pressed, compositionend and keydown events are\n    // emitted. The keydown event triggers newline insertion, which we don't want.\n    // This method returns true if the keydown event should be ignored.\n    // We only ignore it once, as pressing Enter a second time *should* insert a newline.\n    // Furthermore, the keydown event timestamp must be close to the compositionEndedAt timestamp.\n    // This guards against the case where compositionend is triggered without the keyboard\n    // (e.g. character confirmation may be done with the mouse), and keydown is triggered\n    // afterwards- we wouldn't want to ignore the keydown event in this case.\n    if (safari && Math.abs(event.timeStamp - view.input.compositionEndedAt) < 500) {\n        view.input.compositionEndedAt = -2e8;\n        return true;\n    }\n    return false;\n}\n// Drop active composition after 5 seconds of inactivity on Android\nconst timeoutComposition = android ? 5000 : -1;\neditHandlers.compositionstart = editHandlers.compositionupdate = view => {\n    if (!view.composing) {\n        view.domObserver.flush();\n        let { state } = view, $pos = state.selection.$to;\n        if (state.selection instanceof TextSelection &&\n            (state.storedMarks ||\n                (!$pos.textOffset && $pos.parentOffset && $pos.nodeBefore.marks.some(m => m.type.spec.inclusive === false)))) {\n            // Need to wrap the cursor in mark nodes different from the ones in the DOM context\n            view.markCursor = view.state.storedMarks || $pos.marks();\n            endComposition(view, true);\n            view.markCursor = null;\n        }\n        else {\n            endComposition(view, !state.selection.empty);\n            // In firefox, if the cursor is after but outside a marked node,\n            // the inserted text won't inherit the marks. So this moves it\n            // inside if necessary.\n            if (gecko && state.selection.empty && $pos.parentOffset && !$pos.textOffset && $pos.nodeBefore.marks.length) {\n                let sel = view.domSelectionRange();\n                for (let node = sel.focusNode, offset = sel.focusOffset; node && node.nodeType == 1 && offset != 0;) {\n                    let before = offset < 0 ? node.lastChild : node.childNodes[offset - 1];\n                    if (!before)\n                        break;\n                    if (before.nodeType == 3) {\n                        let sel = view.domSelection();\n                        if (sel)\n                            sel.collapse(before, before.nodeValue.length);\n                        break;\n                    }\n                    else {\n                        node = before;\n                        offset = -1;\n                    }\n                }\n            }\n        }\n        view.input.composing = true;\n    }\n    scheduleComposeEnd(view, timeoutComposition);\n};\neditHandlers.compositionend = (view, event) => {\n    if (view.composing) {\n        view.input.composing = false;\n        view.input.compositionEndedAt = event.timeStamp;\n        view.input.compositionPendingChanges = view.domObserver.pendingRecords().length ? view.input.compositionID : 0;\n        view.input.compositionNode = null;\n        if (view.input.compositionPendingChanges)\n            Promise.resolve().then(() => view.domObserver.flush());\n        view.input.compositionID++;\n        scheduleComposeEnd(view, 20);\n    }\n};\nfunction scheduleComposeEnd(view, delay) {\n    clearTimeout(view.input.composingTimeout);\n    if (delay > -1)\n        view.input.composingTimeout = setTimeout(() => endComposition(view), delay);\n}\nfunction clearComposition(view) {\n    if (view.composing) {\n        view.input.composing = false;\n        view.input.compositionEndedAt = timestampFromCustomEvent();\n    }\n    while (view.input.compositionNodes.length > 0)\n        view.input.compositionNodes.pop().markParentsDirty();\n}\nfunction findCompositionNode(view) {\n    let sel = view.domSelectionRange();\n    if (!sel.focusNode)\n        return null;\n    let textBefore = textNodeBefore$1(sel.focusNode, sel.focusOffset);\n    let textAfter = textNodeAfter$1(sel.focusNode, sel.focusOffset);\n    if (textBefore && textAfter && textBefore != textAfter) {\n        let descAfter = textAfter.pmViewDesc, lastChanged = view.domObserver.lastChangedTextNode;\n        if (textBefore == lastChanged || textAfter == lastChanged)\n            return lastChanged;\n        if (!descAfter || !descAfter.isText(textAfter.nodeValue)) {\n            return textAfter;\n        }\n        else if (view.input.compositionNode == textAfter) {\n            let descBefore = textBefore.pmViewDesc;\n            if (!(!descBefore || !descBefore.isText(textBefore.nodeValue)))\n                return textAfter;\n        }\n    }\n    return textBefore || textAfter;\n}\nfunction timestampFromCustomEvent() {\n    let event = document.createEvent(\"Event\");\n    event.initEvent(\"event\", true, true);\n    return event.timeStamp;\n}\n/**\n@internal\n*/\nfunction endComposition(view, restarting = false) {\n    if (android && view.domObserver.flushingSoon >= 0)\n        return;\n    view.domObserver.forceFlush();\n    clearComposition(view);\n    if (restarting || view.docView && view.docView.dirty) {\n        let sel = selectionFromDOM(view), cur = view.state.selection;\n        if (sel && !sel.eq(cur))\n            view.dispatch(view.state.tr.setSelection(sel));\n        else if ((view.markCursor || restarting) && !cur.$from.node(cur.$from.sharedDepth(cur.to)).inlineContent)\n            view.dispatch(view.state.tr.deleteSelection());\n        else\n            view.updateState(view.state);\n        return true;\n    }\n    return false;\n}\nfunction captureCopy(view, dom) {\n    // The extra wrapper is somehow necessary on IE/Edge to prevent the\n    // content from being mangled when it is put onto the clipboard\n    if (!view.dom.parentNode)\n        return;\n    let wrap = view.dom.parentNode.appendChild(document.createElement(\"div\"));\n    wrap.appendChild(dom);\n    wrap.style.cssText = \"position: fixed; left: -10000px; top: 10px\";\n    let sel = getSelection(), range = document.createRange();\n    range.selectNodeContents(dom);\n    // Done because IE will fire a selectionchange moving the selection\n    // to its start when removeAllRanges is called and the editor still\n    // has focus (which will mess up the editor's selection state).\n    view.dom.blur();\n    sel.removeAllRanges();\n    sel.addRange(range);\n    setTimeout(() => {\n        if (wrap.parentNode)\n            wrap.parentNode.removeChild(wrap);\n        view.focus();\n    }, 50);\n}\n// This is very crude, but unfortunately both these browsers _pretend_\n// that they have a clipboard API—all the objects and methods are\n// there, they just don't work, and they are hard to test.\nconst brokenClipboardAPI = (ie && ie_version < 15) ||\n    (ios && webkit_version < 604);\nhandlers.copy = editHandlers.cut = (view, _event) => {\n    let event = _event;\n    let sel = view.state.selection, cut = event.type == \"cut\";\n    if (sel.empty)\n        return;\n    // IE and Edge's clipboard interface is completely broken\n    let data = brokenClipboardAPI ? null : event.clipboardData;\n    let slice = sel.content(), { dom, text } = serializeForClipboard(view, slice);\n    if (data) {\n        event.preventDefault();\n        data.clearData();\n        data.setData(\"text/html\", dom.innerHTML);\n        data.setData(\"text/plain\", text);\n    }\n    else {\n        captureCopy(view, dom);\n    }\n    if (cut)\n        view.dispatch(view.state.tr.deleteSelection().scrollIntoView().setMeta(\"uiEvent\", \"cut\"));\n};\nfunction sliceSingleNode(slice) {\n    return slice.openStart == 0 && slice.openEnd == 0 && slice.content.childCount == 1 ? slice.content.firstChild : null;\n}\nfunction capturePaste(view, event) {\n    if (!view.dom.parentNode)\n        return;\n    let plainText = view.input.shiftKey || view.state.selection.$from.parent.type.spec.code;\n    let target = view.dom.parentNode.appendChild(document.createElement(plainText ? \"textarea\" : \"div\"));\n    if (!plainText)\n        target.contentEditable = \"true\";\n    target.style.cssText = \"position: fixed; left: -10000px; top: 10px\";\n    target.focus();\n    let plain = view.input.shiftKey && view.input.lastKeyCode != 45;\n    setTimeout(() => {\n        view.focus();\n        if (target.parentNode)\n            target.parentNode.removeChild(target);\n        if (plainText)\n            doPaste(view, target.value, null, plain, event);\n        else\n            doPaste(view, target.textContent, target.innerHTML, plain, event);\n    }, 50);\n}\nfunction doPaste(view, text, html, preferPlain, event) {\n    let slice = parseFromClipboard(view, text, html, preferPlain, view.state.selection.$from);\n    if (view.someProp(\"handlePaste\", f => f(view, event, slice || Slice.empty)))\n        return true;\n    if (!slice)\n        return false;\n    let singleNode = sliceSingleNode(slice);\n    let tr = singleNode\n        ? view.state.tr.replaceSelectionWith(singleNode, preferPlain)\n        : view.state.tr.replaceSelection(slice);\n    view.dispatch(tr.scrollIntoView().setMeta(\"paste\", true).setMeta(\"uiEvent\", \"paste\"));\n    return true;\n}\nfunction getText(clipboardData) {\n    let text = clipboardData.getData(\"text/plain\") || clipboardData.getData(\"Text\");\n    if (text)\n        return text;\n    let uris = clipboardData.getData(\"text/uri-list\");\n    return uris ? uris.replace(/\\r?\\n/g, \" \") : \"\";\n}\neditHandlers.paste = (view, _event) => {\n    let event = _event;\n    // Handling paste from JavaScript during composition is very poorly\n    // handled by browsers, so as a dodgy but preferable kludge, we just\n    // let the browser do its native thing there, except on Android,\n    // where the editor is almost always composing.\n    if (view.composing && !android)\n        return;\n    let data = brokenClipboardAPI ? null : event.clipboardData;\n    let plain = view.input.shiftKey && view.input.lastKeyCode != 45;\n    if (data && doPaste(view, getText(data), data.getData(\"text/html\"), plain, event))\n        event.preventDefault();\n    else\n        capturePaste(view, event);\n};\nclass Dragging {\n    constructor(slice, move, node) {\n        this.slice = slice;\n        this.move = move;\n        this.node = node;\n    }\n}\nconst dragCopyModifier = mac ? \"altKey\" : \"ctrlKey\";\nfunction dragMoves(view, event) {\n    let moves = view.someProp(\"dragCopies\", test => !test(event));\n    return moves != null ? moves : !event[dragCopyModifier];\n}\nhandlers.dragstart = (view, _event) => {\n    let event = _event;\n    let mouseDown = view.input.mouseDown;\n    if (mouseDown)\n        mouseDown.done();\n    if (!event.dataTransfer)\n        return;\n    let sel = view.state.selection;\n    let pos = sel.empty ? null : view.posAtCoords(eventCoords(event));\n    let node;\n    if (pos && pos.pos >= sel.from && pos.pos <= (sel instanceof NodeSelection ? sel.to - 1 : sel.to)) ;\n    else if (mouseDown && mouseDown.mightDrag) {\n        node = NodeSelection.create(view.state.doc, mouseDown.mightDrag.pos);\n    }\n    else if (event.target && event.target.nodeType == 1) {\n        let desc = view.docView.nearestDesc(event.target, true);\n        if (desc && desc.node.type.spec.draggable && desc != view.docView)\n            node = NodeSelection.create(view.state.doc, desc.posBefore);\n    }\n    let draggedSlice = (node || view.state.selection).content();\n    let { dom, text, slice } = serializeForClipboard(view, draggedSlice);\n    // Pre-120 Chrome versions clear files when calling `clearData` (#1472)\n    if (!event.dataTransfer.files.length || !chrome || chrome_version > 120)\n        event.dataTransfer.clearData();\n    event.dataTransfer.setData(brokenClipboardAPI ? \"Text\" : \"text/html\", dom.innerHTML);\n    // See https://github.com/ProseMirror/prosemirror/issues/1156\n    event.dataTransfer.effectAllowed = \"copyMove\";\n    if (!brokenClipboardAPI)\n        event.dataTransfer.setData(\"text/plain\", text);\n    view.dragging = new Dragging(slice, dragMoves(view, event), node);\n};\nhandlers.dragend = view => {\n    let dragging = view.dragging;\n    window.setTimeout(() => {\n        if (view.dragging == dragging)\n            view.dragging = null;\n    }, 50);\n};\neditHandlers.dragover = editHandlers.dragenter = (_, e) => e.preventDefault();\neditHandlers.drop = (view, _event) => {\n    let event = _event;\n    let dragging = view.dragging;\n    view.dragging = null;\n    if (!event.dataTransfer)\n        return;\n    let eventPos = view.posAtCoords(eventCoords(event));\n    if (!eventPos)\n        return;\n    let $mouse = view.state.doc.resolve(eventPos.pos);\n    let slice = dragging && dragging.slice;\n    if (slice) {\n        view.someProp(\"transformPasted\", f => { slice = f(slice, view); });\n    }\n    else {\n        slice = parseFromClipboard(view, getText(event.dataTransfer), brokenClipboardAPI ? null : event.dataTransfer.getData(\"text/html\"), false, $mouse);\n    }\n    let move = !!(dragging && dragMoves(view, event));\n    if (view.someProp(\"handleDrop\", f => f(view, event, slice || Slice.empty, move))) {\n        event.preventDefault();\n        return;\n    }\n    if (!slice)\n        return;\n    event.preventDefault();\n    let insertPos = slice ? dropPoint(view.state.doc, $mouse.pos, slice) : $mouse.pos;\n    if (insertPos == null)\n        insertPos = $mouse.pos;\n    let tr = view.state.tr;\n    if (move) {\n        let { node } = dragging;\n        if (node)\n            node.replace(tr);\n        else\n            tr.deleteSelection();\n    }\n    let pos = tr.mapping.map(insertPos);\n    let isNode = slice.openStart == 0 && slice.openEnd == 0 && slice.content.childCount == 1;\n    let beforeInsert = tr.doc;\n    if (isNode)\n        tr.replaceRangeWith(pos, pos, slice.content.firstChild);\n    else\n        tr.replaceRange(pos, pos, slice);\n    if (tr.doc.eq(beforeInsert))\n        return;\n    let $pos = tr.doc.resolve(pos);\n    if (isNode && NodeSelection.isSelectable(slice.content.firstChild) &&\n        $pos.nodeAfter && $pos.nodeAfter.sameMarkup(slice.content.firstChild)) {\n        tr.setSelection(new NodeSelection($pos));\n    }\n    else {\n        let end = tr.mapping.map(insertPos);\n        tr.mapping.maps[tr.mapping.maps.length - 1].forEach((_from, _to, _newFrom, newTo) => end = newTo);\n        tr.setSelection(selectionBetween(view, $pos, tr.doc.resolve(end)));\n    }\n    view.focus();\n    view.dispatch(tr.setMeta(\"uiEvent\", \"drop\"));\n};\nhandlers.focus = view => {\n    view.input.lastFocus = Date.now();\n    if (!view.focused) {\n        view.domObserver.stop();\n        view.dom.classList.add(\"ProseMirror-focused\");\n        view.domObserver.start();\n        view.focused = true;\n        setTimeout(() => {\n            if (view.docView && view.hasFocus() && !view.domObserver.currentSelection.eq(view.domSelectionRange()))\n                selectionToDOM(view);\n        }, 20);\n    }\n};\nhandlers.blur = (view, _event) => {\n    let event = _event;\n    if (view.focused) {\n        view.domObserver.stop();\n        view.dom.classList.remove(\"ProseMirror-focused\");\n        view.domObserver.start();\n        if (event.relatedTarget && view.dom.contains(event.relatedTarget))\n            view.domObserver.currentSelection.clear();\n        view.focused = false;\n    }\n};\nhandlers.beforeinput = (view, _event) => {\n    let event = _event;\n    // We should probably do more with beforeinput events, but support\n    // is so spotty that I'm still waiting to see where they are going.\n    // Very specific hack to deal with backspace sometimes failing on\n    // Chrome Android when after an uneditable node.\n    if (chrome && android && event.inputType == \"deleteContentBackward\") {\n        view.domObserver.flushSoon();\n        let { domChangeCount } = view.input;\n        setTimeout(() => {\n            if (view.input.domChangeCount != domChangeCount)\n                return; // Event already had some effect\n            // This bug tends to close the virtual keyboard, so we refocus\n            view.dom.blur();\n            view.focus();\n            if (view.someProp(\"handleKeyDown\", f => f(view, keyEvent(8, \"Backspace\"))))\n                return;\n            let { $cursor } = view.state.selection;\n            // Crude approximation of backspace behavior when no command handled it\n            if ($cursor && $cursor.pos > 0)\n                view.dispatch(view.state.tr.delete($cursor.pos - 1, $cursor.pos).scrollIntoView());\n        }, 50);\n    }\n};\n// Make sure all handlers get registered\nfor (let prop in editHandlers)\n    handlers[prop] = editHandlers[prop];\n\nfunction compareObjs(a, b) {\n    if (a == b)\n        return true;\n    for (let p in a)\n        if (a[p] !== b[p])\n            return false;\n    for (let p in b)\n        if (!(p in a))\n            return false;\n    return true;\n}\nclass WidgetType {\n    constructor(toDOM, spec) {\n        this.toDOM = toDOM;\n        this.spec = spec || noSpec;\n        this.side = this.spec.side || 0;\n    }\n    map(mapping, span, offset, oldOffset) {\n        let { pos, deleted } = mapping.mapResult(span.from + oldOffset, this.side < 0 ? -1 : 1);\n        return deleted ? null : new Decoration(pos - offset, pos - offset, this);\n    }\n    valid() { return true; }\n    eq(other) {\n        return this == other ||\n            (other instanceof WidgetType &&\n                (this.spec.key && this.spec.key == other.spec.key ||\n                    this.toDOM == other.toDOM && compareObjs(this.spec, other.spec)));\n    }\n    destroy(node) {\n        if (this.spec.destroy)\n            this.spec.destroy(node);\n    }\n}\nclass InlineType {\n    constructor(attrs, spec) {\n        this.attrs = attrs;\n        this.spec = spec || noSpec;\n    }\n    map(mapping, span, offset, oldOffset) {\n        let from = mapping.map(span.from + oldOffset, this.spec.inclusiveStart ? -1 : 1) - offset;\n        let to = mapping.map(span.to + oldOffset, this.spec.inclusiveEnd ? 1 : -1) - offset;\n        return from >= to ? null : new Decoration(from, to, this);\n    }\n    valid(_, span) { return span.from < span.to; }\n    eq(other) {\n        return this == other ||\n            (other instanceof InlineType && compareObjs(this.attrs, other.attrs) &&\n                compareObjs(this.spec, other.spec));\n    }\n    static is(span) { return span.type instanceof InlineType; }\n    destroy() { }\n}\nclass NodeType {\n    constructor(attrs, spec) {\n        this.attrs = attrs;\n        this.spec = spec || noSpec;\n    }\n    map(mapping, span, offset, oldOffset) {\n        let from = mapping.mapResult(span.from + oldOffset, 1);\n        if (from.deleted)\n            return null;\n        let to = mapping.mapResult(span.to + oldOffset, -1);\n        if (to.deleted || to.pos <= from.pos)\n            return null;\n        return new Decoration(from.pos - offset, to.pos - offset, this);\n    }\n    valid(node, span) {\n        let { index, offset } = node.content.findIndex(span.from), child;\n        return offset == span.from && !(child = node.child(index)).isText && offset + child.nodeSize == span.to;\n    }\n    eq(other) {\n        return this == other ||\n            (other instanceof NodeType && compareObjs(this.attrs, other.attrs) &&\n                compareObjs(this.spec, other.spec));\n    }\n    destroy() { }\n}\n/**\nDecoration objects can be provided to the view through the\n[`decorations` prop](https://prosemirror.net/docs/ref/#view.EditorProps.decorations). They come in\nseveral variants—see the static members of this class for details.\n*/\nclass Decoration {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The start position of the decoration.\n    */\n    from, \n    /**\n    The end position. Will be the same as `from` for [widget\n    decorations](https://prosemirror.net/docs/ref/#view.Decoration^widget).\n    */\n    to, \n    /**\n    @internal\n    */\n    type) {\n        this.from = from;\n        this.to = to;\n        this.type = type;\n    }\n    /**\n    @internal\n    */\n    copy(from, to) {\n        return new Decoration(from, to, this.type);\n    }\n    /**\n    @internal\n    */\n    eq(other, offset = 0) {\n        return this.type.eq(other.type) && this.from + offset == other.from && this.to + offset == other.to;\n    }\n    /**\n    @internal\n    */\n    map(mapping, offset, oldOffset) {\n        return this.type.map(mapping, this, offset, oldOffset);\n    }\n    /**\n    Creates a widget decoration, which is a DOM node that's shown in\n    the document at the given position. It is recommended that you\n    delay rendering the widget by passing a function that will be\n    called when the widget is actually drawn in a view, but you can\n    also directly pass a DOM node. `getPos` can be used to find the\n    widget's current document position.\n    */\n    static widget(pos, toDOM, spec) {\n        return new Decoration(pos, pos, new WidgetType(toDOM, spec));\n    }\n    /**\n    Creates an inline decoration, which adds the given attributes to\n    each inline node between `from` and `to`.\n    */\n    static inline(from, to, attrs, spec) {\n        return new Decoration(from, to, new InlineType(attrs, spec));\n    }\n    /**\n    Creates a node decoration. `from` and `to` should point precisely\n    before and after a node in the document. That node, and only that\n    node, will receive the given attributes.\n    */\n    static node(from, to, attrs, spec) {\n        return new Decoration(from, to, new NodeType(attrs, spec));\n    }\n    /**\n    The spec provided when creating this decoration. Can be useful\n    if you've stored extra information in that object.\n    */\n    get spec() { return this.type.spec; }\n    /**\n    @internal\n    */\n    get inline() { return this.type instanceof InlineType; }\n    /**\n    @internal\n    */\n    get widget() { return this.type instanceof WidgetType; }\n}\nconst none = [], noSpec = {};\n/**\nA collection of [decorations](https://prosemirror.net/docs/ref/#view.Decoration), organized in such\na way that the drawing algorithm can efficiently use and compare\nthem. This is a persistent data structure—it is not modified,\nupdates create a new value.\n*/\nclass DecorationSet {\n    /**\n    @internal\n    */\n    constructor(local, children) {\n        this.local = local.length ? local : none;\n        this.children = children.length ? children : none;\n    }\n    /**\n    Create a set of decorations, using the structure of the given\n    document. This will consume (modify) the `decorations` array, so\n    you must make a copy if you want need to preserve that.\n    */\n    static create(doc, decorations) {\n        return decorations.length ? buildTree(decorations, doc, 0, noSpec) : empty;\n    }\n    /**\n    Find all decorations in this set which touch the given range\n    (including decorations that start or end directly at the\n    boundaries) and match the given predicate on their spec. When\n    `start` and `end` are omitted, all decorations in the set are\n    considered. When `predicate` isn't given, all decorations are\n    assumed to match.\n    */\n    find(start, end, predicate) {\n        let result = [];\n        this.findInner(start == null ? 0 : start, end == null ? 1e9 : end, result, 0, predicate);\n        return result;\n    }\n    findInner(start, end, result, offset, predicate) {\n        for (let i = 0; i < this.local.length; i++) {\n            let span = this.local[i];\n            if (span.from <= end && span.to >= start && (!predicate || predicate(span.spec)))\n                result.push(span.copy(span.from + offset, span.to + offset));\n        }\n        for (let i = 0; i < this.children.length; i += 3) {\n            if (this.children[i] < end && this.children[i + 1] > start) {\n                let childOff = this.children[i] + 1;\n                this.children[i + 2].findInner(start - childOff, end - childOff, result, offset + childOff, predicate);\n            }\n        }\n    }\n    /**\n    Map the set of decorations in response to a change in the\n    document.\n    */\n    map(mapping, doc, options) {\n        if (this == empty || mapping.maps.length == 0)\n            return this;\n        return this.mapInner(mapping, doc, 0, 0, options || noSpec);\n    }\n    /**\n    @internal\n    */\n    mapInner(mapping, node, offset, oldOffset, options) {\n        let newLocal;\n        for (let i = 0; i < this.local.length; i++) {\n            let mapped = this.local[i].map(mapping, offset, oldOffset);\n            if (mapped && mapped.type.valid(node, mapped))\n                (newLocal || (newLocal = [])).push(mapped);\n            else if (options.onRemove)\n                options.onRemove(this.local[i].spec);\n        }\n        if (this.children.length)\n            return mapChildren(this.children, newLocal || [], mapping, node, offset, oldOffset, options);\n        else\n            return newLocal ? new DecorationSet(newLocal.sort(byPos), none) : empty;\n    }\n    /**\n    Add the given array of decorations to the ones in the set,\n    producing a new set. Consumes the `decorations` array. Needs\n    access to the current document to create the appropriate tree\n    structure.\n    */\n    add(doc, decorations) {\n        if (!decorations.length)\n            return this;\n        if (this == empty)\n            return DecorationSet.create(doc, decorations);\n        return this.addInner(doc, decorations, 0);\n    }\n    addInner(doc, decorations, offset) {\n        let children, childIndex = 0;\n        doc.forEach((childNode, childOffset) => {\n            let baseOffset = childOffset + offset, found;\n            if (!(found = takeSpansForNode(decorations, childNode, baseOffset)))\n                return;\n            if (!children)\n                children = this.children.slice();\n            while (childIndex < children.length && children[childIndex] < childOffset)\n                childIndex += 3;\n            if (children[childIndex] == childOffset)\n                children[childIndex + 2] = children[childIndex + 2].addInner(childNode, found, baseOffset + 1);\n            else\n                children.splice(childIndex, 0, childOffset, childOffset + childNode.nodeSize, buildTree(found, childNode, baseOffset + 1, noSpec));\n            childIndex += 3;\n        });\n        let local = moveSpans(childIndex ? withoutNulls(decorations) : decorations, -offset);\n        for (let i = 0; i < local.length; i++)\n            if (!local[i].type.valid(doc, local[i]))\n                local.splice(i--, 1);\n        return new DecorationSet(local.length ? this.local.concat(local).sort(byPos) : this.local, children || this.children);\n    }\n    /**\n    Create a new set that contains the decorations in this set, minus\n    the ones in the given array.\n    */\n    remove(decorations) {\n        if (decorations.length == 0 || this == empty)\n            return this;\n        return this.removeInner(decorations, 0);\n    }\n    removeInner(decorations, offset) {\n        let children = this.children, local = this.local;\n        for (let i = 0; i < children.length; i += 3) {\n            let found;\n            let from = children[i] + offset, to = children[i + 1] + offset;\n            for (let j = 0, span; j < decorations.length; j++)\n                if (span = decorations[j]) {\n                    if (span.from > from && span.to < to) {\n                        decorations[j] = null;\n                        (found || (found = [])).push(span);\n                    }\n                }\n            if (!found)\n                continue;\n            if (children == this.children)\n                children = this.children.slice();\n            let removed = children[i + 2].removeInner(found, from + 1);\n            if (removed != empty) {\n                children[i + 2] = removed;\n            }\n            else {\n                children.splice(i, 3);\n                i -= 3;\n            }\n        }\n        if (local.length)\n            for (let i = 0, span; i < decorations.length; i++)\n                if (span = decorations[i]) {\n                    for (let j = 0; j < local.length; j++)\n                        if (local[j].eq(span, offset)) {\n                            if (local == this.local)\n                                local = this.local.slice();\n                            local.splice(j--, 1);\n                        }\n                }\n        if (children == this.children && local == this.local)\n            return this;\n        return local.length || children.length ? new DecorationSet(local, children) : empty;\n    }\n    forChild(offset, node) {\n        if (this == empty)\n            return this;\n        if (node.isLeaf)\n            return DecorationSet.empty;\n        let child, local;\n        for (let i = 0; i < this.children.length; i += 3)\n            if (this.children[i] >= offset) {\n                if (this.children[i] == offset)\n                    child = this.children[i + 2];\n                break;\n            }\n        let start = offset + 1, end = start + node.content.size;\n        for (let i = 0; i < this.local.length; i++) {\n            let dec = this.local[i];\n            if (dec.from < end && dec.to > start && (dec.type instanceof InlineType)) {\n                let from = Math.max(start, dec.from) - start, to = Math.min(end, dec.to) - start;\n                if (from < to)\n                    (local || (local = [])).push(dec.copy(from, to));\n            }\n        }\n        if (local) {\n            let localSet = new DecorationSet(local.sort(byPos), none);\n            return child ? new DecorationGroup([localSet, child]) : localSet;\n        }\n        return child || empty;\n    }\n    /**\n    @internal\n    */\n    eq(other) {\n        if (this == other)\n            return true;\n        if (!(other instanceof DecorationSet) ||\n            this.local.length != other.local.length ||\n            this.children.length != other.children.length)\n            return false;\n        for (let i = 0; i < this.local.length; i++)\n            if (!this.local[i].eq(other.local[i]))\n                return false;\n        for (let i = 0; i < this.children.length; i += 3)\n            if (this.children[i] != other.children[i] ||\n                this.children[i + 1] != other.children[i + 1] ||\n                !this.children[i + 2].eq(other.children[i + 2]))\n                return false;\n        return true;\n    }\n    /**\n    @internal\n    */\n    locals(node) {\n        return removeOverlap(this.localsInner(node));\n    }\n    /**\n    @internal\n    */\n    localsInner(node) {\n        if (this == empty)\n            return none;\n        if (node.inlineContent || !this.local.some(InlineType.is))\n            return this.local;\n        let result = [];\n        for (let i = 0; i < this.local.length; i++) {\n            if (!(this.local[i].type instanceof InlineType))\n                result.push(this.local[i]);\n        }\n        return result;\n    }\n    forEachSet(f) { f(this); }\n}\n/**\nThe empty set of decorations.\n*/\nDecorationSet.empty = new DecorationSet([], []);\n/**\n@internal\n*/\nDecorationSet.removeOverlap = removeOverlap;\nconst empty = DecorationSet.empty;\n// An abstraction that allows the code dealing with decorations to\n// treat multiple DecorationSet objects as if it were a single object\n// with (a subset of) the same interface.\nclass DecorationGroup {\n    constructor(members) {\n        this.members = members;\n    }\n    map(mapping, doc) {\n        const mappedDecos = this.members.map(member => member.map(mapping, doc, noSpec));\n        return DecorationGroup.from(mappedDecos);\n    }\n    forChild(offset, child) {\n        if (child.isLeaf)\n            return DecorationSet.empty;\n        let found = [];\n        for (let i = 0; i < this.members.length; i++) {\n            let result = this.members[i].forChild(offset, child);\n            if (result == empty)\n                continue;\n            if (result instanceof DecorationGroup)\n                found = found.concat(result.members);\n            else\n                found.push(result);\n        }\n        return DecorationGroup.from(found);\n    }\n    eq(other) {\n        if (!(other instanceof DecorationGroup) ||\n            other.members.length != this.members.length)\n            return false;\n        for (let i = 0; i < this.members.length; i++)\n            if (!this.members[i].eq(other.members[i]))\n                return false;\n        return true;\n    }\n    locals(node) {\n        let result, sorted = true;\n        for (let i = 0; i < this.members.length; i++) {\n            let locals = this.members[i].localsInner(node);\n            if (!locals.length)\n                continue;\n            if (!result) {\n                result = locals;\n            }\n            else {\n                if (sorted) {\n                    result = result.slice();\n                    sorted = false;\n                }\n                for (let j = 0; j < locals.length; j++)\n                    result.push(locals[j]);\n            }\n        }\n        return result ? removeOverlap(sorted ? result : result.sort(byPos)) : none;\n    }\n    // Create a group for the given array of decoration sets, or return\n    // a single set when possible.\n    static from(members) {\n        switch (members.length) {\n            case 0: return empty;\n            case 1: return members[0];\n            default: return new DecorationGroup(members.every(m => m instanceof DecorationSet) ? members :\n                members.reduce((r, m) => r.concat(m instanceof DecorationSet ? m : m.members), []));\n        }\n    }\n    forEachSet(f) {\n        for (let i = 0; i < this.members.length; i++)\n            this.members[i].forEachSet(f);\n    }\n}\nfunction mapChildren(oldChildren, newLocal, mapping, node, offset, oldOffset, options) {\n    let children = oldChildren.slice();\n    // Mark the children that are directly touched by changes, and\n    // move those that are after the changes.\n    for (let i = 0, baseOffset = oldOffset; i < mapping.maps.length; i++) {\n        let moved = 0;\n        mapping.maps[i].forEach((oldStart, oldEnd, newStart, newEnd) => {\n            let dSize = (newEnd - newStart) - (oldEnd - oldStart);\n            for (let i = 0; i < children.length; i += 3) {\n                let end = children[i + 1];\n                if (end < 0 || oldStart > end + baseOffset - moved)\n                    continue;\n                let start = children[i] + baseOffset - moved;\n                if (oldEnd >= start) {\n                    children[i + 1] = oldStart <= start ? -2 : -1;\n                }\n                else if (oldStart >= baseOffset && dSize) {\n                    children[i] += dSize;\n                    children[i + 1] += dSize;\n                }\n            }\n            moved += dSize;\n        });\n        baseOffset = mapping.maps[i].map(baseOffset, -1);\n    }\n    // Find the child nodes that still correspond to a single node,\n    // recursively call mapInner on them and update their positions.\n    let mustRebuild = false;\n    for (let i = 0; i < children.length; i += 3)\n        if (children[i + 1] < 0) { // Touched nodes\n            if (children[i + 1] == -2) {\n                mustRebuild = true;\n                children[i + 1] = -1;\n                continue;\n            }\n            let from = mapping.map(oldChildren[i] + oldOffset), fromLocal = from - offset;\n            if (fromLocal < 0 || fromLocal >= node.content.size) {\n                mustRebuild = true;\n                continue;\n            }\n            // Must read oldChildren because children was tagged with -1\n            let to = mapping.map(oldChildren[i + 1] + oldOffset, -1), toLocal = to - offset;\n            let { index, offset: childOffset } = node.content.findIndex(fromLocal);\n            let childNode = node.maybeChild(index);\n            if (childNode && childOffset == fromLocal && childOffset + childNode.nodeSize == toLocal) {\n                let mapped = children[i + 2]\n                    .mapInner(mapping, childNode, from + 1, oldChildren[i] + oldOffset + 1, options);\n                if (mapped != empty) {\n                    children[i] = fromLocal;\n                    children[i + 1] = toLocal;\n                    children[i + 2] = mapped;\n                }\n                else {\n                    children[i + 1] = -2;\n                    mustRebuild = true;\n                }\n            }\n            else {\n                mustRebuild = true;\n            }\n        }\n    // Remaining children must be collected and rebuilt into the appropriate structure\n    if (mustRebuild) {\n        let decorations = mapAndGatherRemainingDecorations(children, oldChildren, newLocal, mapping, offset, oldOffset, options);\n        let built = buildTree(decorations, node, 0, options);\n        newLocal = built.local;\n        for (let i = 0; i < children.length; i += 3)\n            if (children[i + 1] < 0) {\n                children.splice(i, 3);\n                i -= 3;\n            }\n        for (let i = 0, j = 0; i < built.children.length; i += 3) {\n            let from = built.children[i];\n            while (j < children.length && children[j] < from)\n                j += 3;\n            children.splice(j, 0, built.children[i], built.children[i + 1], built.children[i + 2]);\n        }\n    }\n    return new DecorationSet(newLocal.sort(byPos), children);\n}\nfunction moveSpans(spans, offset) {\n    if (!offset || !spans.length)\n        return spans;\n    let result = [];\n    for (let i = 0; i < spans.length; i++) {\n        let span = spans[i];\n        result.push(new Decoration(span.from + offset, span.to + offset, span.type));\n    }\n    return result;\n}\nfunction mapAndGatherRemainingDecorations(children, oldChildren, decorations, mapping, offset, oldOffset, options) {\n    // Gather all decorations from the remaining marked children\n    function gather(set, oldOffset) {\n        for (let i = 0; i < set.local.length; i++) {\n            let mapped = set.local[i].map(mapping, offset, oldOffset);\n            if (mapped)\n                decorations.push(mapped);\n            else if (options.onRemove)\n                options.onRemove(set.local[i].spec);\n        }\n        for (let i = 0; i < set.children.length; i += 3)\n            gather(set.children[i + 2], set.children[i] + oldOffset + 1);\n    }\n    for (let i = 0; i < children.length; i += 3)\n        if (children[i + 1] == -1)\n            gather(children[i + 2], oldChildren[i] + oldOffset + 1);\n    return decorations;\n}\nfunction takeSpansForNode(spans, node, offset) {\n    if (node.isLeaf)\n        return null;\n    let end = offset + node.nodeSize, found = null;\n    for (let i = 0, span; i < spans.length; i++) {\n        if ((span = spans[i]) && span.from > offset && span.to < end) {\n            (found || (found = [])).push(span);\n            spans[i] = null;\n        }\n    }\n    return found;\n}\nfunction withoutNulls(array) {\n    let result = [];\n    for (let i = 0; i < array.length; i++)\n        if (array[i] != null)\n            result.push(array[i]);\n    return result;\n}\n// Build up a tree that corresponds to a set of decorations. `offset`\n// is a base offset that should be subtracted from the `from` and `to`\n// positions in the spans (so that we don't have to allocate new spans\n// for recursive calls).\nfunction buildTree(spans, node, offset, options) {\n    let children = [], hasNulls = false;\n    node.forEach((childNode, localStart) => {\n        let found = takeSpansForNode(spans, childNode, localStart + offset);\n        if (found) {\n            hasNulls = true;\n            let subtree = buildTree(found, childNode, offset + localStart + 1, options);\n            if (subtree != empty)\n                children.push(localStart, localStart + childNode.nodeSize, subtree);\n        }\n    });\n    let locals = moveSpans(hasNulls ? withoutNulls(spans) : spans, -offset).sort(byPos);\n    for (let i = 0; i < locals.length; i++)\n        if (!locals[i].type.valid(node, locals[i])) {\n            if (options.onRemove)\n                options.onRemove(locals[i].spec);\n            locals.splice(i--, 1);\n        }\n    return locals.length || children.length ? new DecorationSet(locals, children) : empty;\n}\n// Used to sort decorations so that ones with a low start position\n// come first, and within a set with the same start position, those\n// with an smaller end position come first.\nfunction byPos(a, b) {\n    return a.from - b.from || a.to - b.to;\n}\n// Scan a sorted array of decorations for partially overlapping spans,\n// and split those so that only fully overlapping spans are left (to\n// make subsequent rendering easier). Will return the input array if\n// no partially overlapping spans are found (the common case).\nfunction removeOverlap(spans) {\n    let working = spans;\n    for (let i = 0; i < working.length - 1; i++) {\n        let span = working[i];\n        if (span.from != span.to)\n            for (let j = i + 1; j < working.length; j++) {\n                let next = working[j];\n                if (next.from == span.from) {\n                    if (next.to != span.to) {\n                        if (working == spans)\n                            working = spans.slice();\n                        // Followed by a partially overlapping larger span. Split that\n                        // span.\n                        working[j] = next.copy(next.from, span.to);\n                        insertAhead(working, j + 1, next.copy(span.to, next.to));\n                    }\n                    continue;\n                }\n                else {\n                    if (next.from < span.to) {\n                        if (working == spans)\n                            working = spans.slice();\n                        // The end of this one overlaps with a subsequent span. Split\n                        // this one.\n                        working[i] = span.copy(span.from, next.from);\n                        insertAhead(working, j, span.copy(next.from, span.to));\n                    }\n                    break;\n                }\n            }\n    }\n    return working;\n}\nfunction insertAhead(array, i, deco) {\n    while (i < array.length && byPos(deco, array[i]) > 0)\n        i++;\n    array.splice(i, 0, deco);\n}\n// Get the decorations associated with the current props of a view.\nfunction viewDecorations(view) {\n    let found = [];\n    view.someProp(\"decorations\", f => {\n        let result = f(view.state);\n        if (result && result != empty)\n            found.push(result);\n    });\n    if (view.cursorWrapper)\n        found.push(DecorationSet.create(view.state.doc, [view.cursorWrapper.deco]));\n    return DecorationGroup.from(found);\n}\n\nconst observeOptions = {\n    childList: true,\n    characterData: true,\n    characterDataOldValue: true,\n    attributes: true,\n    attributeOldValue: true,\n    subtree: true\n};\n// IE11 has very broken mutation observers, so we also listen to DOMCharacterDataModified\nconst useCharData = ie && ie_version <= 11;\nclass SelectionState {\n    constructor() {\n        this.anchorNode = null;\n        this.anchorOffset = 0;\n        this.focusNode = null;\n        this.focusOffset = 0;\n    }\n    set(sel) {\n        this.anchorNode = sel.anchorNode;\n        this.anchorOffset = sel.anchorOffset;\n        this.focusNode = sel.focusNode;\n        this.focusOffset = sel.focusOffset;\n    }\n    clear() {\n        this.anchorNode = this.focusNode = null;\n    }\n    eq(sel) {\n        return sel.anchorNode == this.anchorNode && sel.anchorOffset == this.anchorOffset &&\n            sel.focusNode == this.focusNode && sel.focusOffset == this.focusOffset;\n    }\n}\nclass DOMObserver {\n    constructor(view, handleDOMChange) {\n        this.view = view;\n        this.handleDOMChange = handleDOMChange;\n        this.queue = [];\n        this.flushingSoon = -1;\n        this.observer = null;\n        this.currentSelection = new SelectionState;\n        this.onCharData = null;\n        this.suppressingSelectionUpdates = false;\n        this.lastChangedTextNode = null;\n        this.observer = window.MutationObserver &&\n            new window.MutationObserver(mutations => {\n                for (let i = 0; i < mutations.length; i++)\n                    this.queue.push(mutations[i]);\n                // IE11 will sometimes (on backspacing out a single character\n                // text node after a BR node) call the observer callback\n                // before actually updating the DOM, which will cause\n                // ProseMirror to miss the change (see #930)\n                if (ie && ie_version <= 11 && mutations.some(m => m.type == \"childList\" && m.removedNodes.length ||\n                    m.type == \"characterData\" && m.oldValue.length > m.target.nodeValue.length))\n                    this.flushSoon();\n                else\n                    this.flush();\n            });\n        if (useCharData) {\n            this.onCharData = e => {\n                this.queue.push({ target: e.target, type: \"characterData\", oldValue: e.prevValue });\n                this.flushSoon();\n            };\n        }\n        this.onSelectionChange = this.onSelectionChange.bind(this);\n    }\n    flushSoon() {\n        if (this.flushingSoon < 0)\n            this.flushingSoon = window.setTimeout(() => { this.flushingSoon = -1; this.flush(); }, 20);\n    }\n    forceFlush() {\n        if (this.flushingSoon > -1) {\n            window.clearTimeout(this.flushingSoon);\n            this.flushingSoon = -1;\n            this.flush();\n        }\n    }\n    start() {\n        if (this.observer) {\n            this.observer.takeRecords();\n            this.observer.observe(this.view.dom, observeOptions);\n        }\n        if (this.onCharData)\n            this.view.dom.addEventListener(\"DOMCharacterDataModified\", this.onCharData);\n        this.connectSelection();\n    }\n    stop() {\n        if (this.observer) {\n            let take = this.observer.takeRecords();\n            if (take.length) {\n                for (let i = 0; i < take.length; i++)\n                    this.queue.push(take[i]);\n                window.setTimeout(() => this.flush(), 20);\n            }\n            this.observer.disconnect();\n        }\n        if (this.onCharData)\n            this.view.dom.removeEventListener(\"DOMCharacterDataModified\", this.onCharData);\n        this.disconnectSelection();\n    }\n    connectSelection() {\n        this.view.dom.ownerDocument.addEventListener(\"selectionchange\", this.onSelectionChange);\n    }\n    disconnectSelection() {\n        this.view.dom.ownerDocument.removeEventListener(\"selectionchange\", this.onSelectionChange);\n    }\n    suppressSelectionUpdates() {\n        this.suppressingSelectionUpdates = true;\n        setTimeout(() => this.suppressingSelectionUpdates = false, 50);\n    }\n    onSelectionChange() {\n        if (!hasFocusAndSelection(this.view))\n            return;\n        if (this.suppressingSelectionUpdates)\n            return selectionToDOM(this.view);\n        // Deletions on IE11 fire their events in the wrong order, giving\n        // us a selection change event before the DOM changes are\n        // reported.\n        if (ie && ie_version <= 11 && !this.view.state.selection.empty) {\n            let sel = this.view.domSelectionRange();\n            // Selection.isCollapsed isn't reliable on IE\n            if (sel.focusNode && isEquivalentPosition(sel.focusNode, sel.focusOffset, sel.anchorNode, sel.anchorOffset))\n                return this.flushSoon();\n        }\n        this.flush();\n    }\n    setCurSelection() {\n        this.currentSelection.set(this.view.domSelectionRange());\n    }\n    ignoreSelectionChange(sel) {\n        if (!sel.focusNode)\n            return true;\n        let ancestors = new Set, container;\n        for (let scan = sel.focusNode; scan; scan = parentNode(scan))\n            ancestors.add(scan);\n        for (let scan = sel.anchorNode; scan; scan = parentNode(scan))\n            if (ancestors.has(scan)) {\n                container = scan;\n                break;\n            }\n        let desc = container && this.view.docView.nearestDesc(container);\n        if (desc && desc.ignoreMutation({\n            type: \"selection\",\n            target: container.nodeType == 3 ? container.parentNode : container\n        })) {\n            this.setCurSelection();\n            return true;\n        }\n    }\n    pendingRecords() {\n        if (this.observer)\n            for (let mut of this.observer.takeRecords())\n                this.queue.push(mut);\n        return this.queue;\n    }\n    flush() {\n        let { view } = this;\n        if (!view.docView || this.flushingSoon > -1)\n            return;\n        let mutations = this.pendingRecords();\n        if (mutations.length)\n            this.queue = [];\n        let sel = view.domSelectionRange();\n        let newSel = !this.suppressingSelectionUpdates && !this.currentSelection.eq(sel) && hasFocusAndSelection(view) && !this.ignoreSelectionChange(sel);\n        let from = -1, to = -1, typeOver = false, added = [];\n        if (view.editable) {\n            for (let i = 0; i < mutations.length; i++) {\n                let result = this.registerMutation(mutations[i], added);\n                if (result) {\n                    from = from < 0 ? result.from : Math.min(result.from, from);\n                    to = to < 0 ? result.to : Math.max(result.to, to);\n                    if (result.typeOver)\n                        typeOver = true;\n                }\n            }\n        }\n        if (gecko && added.length) {\n            let brs = added.filter(n => n.nodeName == \"BR\");\n            if (brs.length == 2) {\n                let [a, b] = brs;\n                if (a.parentNode && a.parentNode.parentNode == b.parentNode)\n                    b.remove();\n                else\n                    a.remove();\n            }\n            else {\n                let { focusNode } = this.currentSelection;\n                for (let br of brs) {\n                    let parent = br.parentNode;\n                    if (parent && parent.nodeName == \"LI\" && (!focusNode || blockParent(view, focusNode) != parent))\n                        br.remove();\n                }\n            }\n        }\n        let readSel = null;\n        // If it looks like the browser has reset the selection to the\n        // start of the document after focus, restore the selection from\n        // the state\n        if (from < 0 && newSel && view.input.lastFocus > Date.now() - 200 &&\n            Math.max(view.input.lastTouch, view.input.lastClick.time) < Date.now() - 300 &&\n            selectionCollapsed(sel) && (readSel = selectionFromDOM(view)) &&\n            readSel.eq(Selection.near(view.state.doc.resolve(0), 1))) {\n            view.input.lastFocus = 0;\n            selectionToDOM(view);\n            this.currentSelection.set(sel);\n            view.scrollToSelection();\n        }\n        else if (from > -1 || newSel) {\n            if (from > -1) {\n                view.docView.markDirty(from, to);\n                checkCSS(view);\n            }\n            this.handleDOMChange(from, to, typeOver, added);\n            if (view.docView && view.docView.dirty)\n                view.updateState(view.state);\n            else if (!this.currentSelection.eq(sel))\n                selectionToDOM(view);\n            this.currentSelection.set(sel);\n        }\n    }\n    registerMutation(mut, added) {\n        // Ignore mutations inside nodes that were already noted as inserted\n        if (added.indexOf(mut.target) > -1)\n            return null;\n        let desc = this.view.docView.nearestDesc(mut.target);\n        if (mut.type == \"attributes\" &&\n            (desc == this.view.docView || mut.attributeName == \"contenteditable\" ||\n                // Firefox sometimes fires spurious events for null/empty styles\n                (mut.attributeName == \"style\" && !mut.oldValue && !mut.target.getAttribute(\"style\"))))\n            return null;\n        if (!desc || desc.ignoreMutation(mut))\n            return null;\n        if (mut.type == \"childList\") {\n            for (let i = 0; i < mut.addedNodes.length; i++) {\n                let node = mut.addedNodes[i];\n                added.push(node);\n                if (node.nodeType == 3)\n                    this.lastChangedTextNode = node;\n            }\n            if (desc.contentDOM && desc.contentDOM != desc.dom && !desc.contentDOM.contains(mut.target))\n                return { from: desc.posBefore, to: desc.posAfter };\n            let prev = mut.previousSibling, next = mut.nextSibling;\n            if (ie && ie_version <= 11 && mut.addedNodes.length) {\n                // IE11 gives us incorrect next/prev siblings for some\n                // insertions, so if there are added nodes, recompute those\n                for (let i = 0; i < mut.addedNodes.length; i++) {\n                    let { previousSibling, nextSibling } = mut.addedNodes[i];\n                    if (!previousSibling || Array.prototype.indexOf.call(mut.addedNodes, previousSibling) < 0)\n                        prev = previousSibling;\n                    if (!nextSibling || Array.prototype.indexOf.call(mut.addedNodes, nextSibling) < 0)\n                        next = nextSibling;\n                }\n            }\n            let fromOffset = prev && prev.parentNode == mut.target\n                ? domIndex(prev) + 1 : 0;\n            let from = desc.localPosFromDOM(mut.target, fromOffset, -1);\n            let toOffset = next && next.parentNode == mut.target\n                ? domIndex(next) : mut.target.childNodes.length;\n            let to = desc.localPosFromDOM(mut.target, toOffset, 1);\n            return { from, to };\n        }\n        else if (mut.type == \"attributes\") {\n            return { from: desc.posAtStart - desc.border, to: desc.posAtEnd + desc.border };\n        }\n        else { // \"characterData\"\n            this.lastChangedTextNode = mut.target;\n            return {\n                from: desc.posAtStart,\n                to: desc.posAtEnd,\n                // An event was generated for a text change that didn't change\n                // any text. Mark the dom change to fall back to assuming the\n                // selection was typed over with an identical value if it can't\n                // find another change.\n                typeOver: mut.target.nodeValue == mut.oldValue\n            };\n        }\n    }\n}\nlet cssChecked = new WeakMap();\nlet cssCheckWarned = false;\nfunction checkCSS(view) {\n    if (cssChecked.has(view))\n        return;\n    cssChecked.set(view, null);\n    if (['normal', 'nowrap', 'pre-line'].indexOf(getComputedStyle(view.dom).whiteSpace) !== -1) {\n        view.requiresGeckoHackNode = gecko;\n        if (cssCheckWarned)\n            return;\n        console[\"warn\"](\"ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package.\");\n        cssCheckWarned = true;\n    }\n}\nfunction rangeToSelectionRange(view, range) {\n    let anchorNode = range.startContainer, anchorOffset = range.startOffset;\n    let focusNode = range.endContainer, focusOffset = range.endOffset;\n    let currentAnchor = view.domAtPos(view.state.selection.anchor);\n    // Since such a range doesn't distinguish between anchor and head,\n    // use a heuristic that flips it around if its end matches the\n    // current anchor.\n    if (isEquivalentPosition(currentAnchor.node, currentAnchor.offset, focusNode, focusOffset))\n        [anchorNode, anchorOffset, focusNode, focusOffset] = [focusNode, focusOffset, anchorNode, anchorOffset];\n    return { anchorNode, anchorOffset, focusNode, focusOffset };\n}\n// Used to work around a Safari Selection/shadow DOM bug\n// Based on https://github.com/codemirror/dev/issues/414 fix\nfunction safariShadowSelectionRange(view, selection) {\n    if (selection.getComposedRanges) {\n        let range = selection.getComposedRanges(view.root)[0];\n        if (range)\n            return rangeToSelectionRange(view, range);\n    }\n    let found;\n    function read(event) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n        found = event.getTargetRanges()[0];\n    }\n    // Because Safari (at least in 2018-2022) doesn't provide regular\n    // access to the selection inside a shadowRoot, we have to perform a\n    // ridiculous hack to get at it—using `execCommand` to trigger a\n    // `beforeInput` event so that we can read the target range from the\n    // event.\n    view.dom.addEventListener(\"beforeinput\", read, true);\n    document.execCommand(\"indent\");\n    view.dom.removeEventListener(\"beforeinput\", read, true);\n    return found ? rangeToSelectionRange(view, found) : null;\n}\nfunction blockParent(view, node) {\n    for (let p = node.parentNode; p && p != view.dom; p = p.parentNode) {\n        let desc = view.docView.nearestDesc(p, true);\n        if (desc && desc.node.isBlock)\n            return p;\n    }\n    return null;\n}\n\n// Note that all referencing and parsing is done with the\n// start-of-operation selection and document, since that's the one\n// that the DOM represents. If any changes came in in the meantime,\n// the modification is mapped over those before it is applied, in\n// readDOMChange.\nfunction parseBetween(view, from_, to_) {\n    let { node: parent, fromOffset, toOffset, from, to } = view.docView.parseRange(from_, to_);\n    let domSel = view.domSelectionRange();\n    let find;\n    let anchor = domSel.anchorNode;\n    if (anchor && view.dom.contains(anchor.nodeType == 1 ? anchor : anchor.parentNode)) {\n        find = [{ node: anchor, offset: domSel.anchorOffset }];\n        if (!selectionCollapsed(domSel))\n            find.push({ node: domSel.focusNode, offset: domSel.focusOffset });\n    }\n    // Work around issue in Chrome where backspacing sometimes replaces\n    // the deleted content with a random BR node (issues #799, #831)\n    if (chrome && view.input.lastKeyCode === 8) {\n        for (let off = toOffset; off > fromOffset; off--) {\n            let node = parent.childNodes[off - 1], desc = node.pmViewDesc;\n            if (node.nodeName == \"BR\" && !desc) {\n                toOffset = off;\n                break;\n            }\n            if (!desc || desc.size)\n                break;\n        }\n    }\n    let startDoc = view.state.doc;\n    let parser = view.someProp(\"domParser\") || DOMParser.fromSchema(view.state.schema);\n    let $from = startDoc.resolve(from);\n    let sel = null, doc = parser.parse(parent, {\n        topNode: $from.parent,\n        topMatch: $from.parent.contentMatchAt($from.index()),\n        topOpen: true,\n        from: fromOffset,\n        to: toOffset,\n        preserveWhitespace: $from.parent.type.whitespace == \"pre\" ? \"full\" : true,\n        findPositions: find,\n        ruleFromNode,\n        context: $from\n    });\n    if (find && find[0].pos != null) {\n        let anchor = find[0].pos, head = find[1] && find[1].pos;\n        if (head == null)\n            head = anchor;\n        sel = { anchor: anchor + from, head: head + from };\n    }\n    return { doc, sel, from, to };\n}\nfunction ruleFromNode(dom) {\n    let desc = dom.pmViewDesc;\n    if (desc) {\n        return desc.parseRule();\n    }\n    else if (dom.nodeName == \"BR\" && dom.parentNode) {\n        // Safari replaces the list item or table cell with a BR\n        // directly in the list node (?!) if you delete the last\n        // character in a list item or table cell (#708, #862)\n        if (safari && /^(ul|ol)$/i.test(dom.parentNode.nodeName)) {\n            let skip = document.createElement(\"div\");\n            skip.appendChild(document.createElement(\"li\"));\n            return { skip };\n        }\n        else if (dom.parentNode.lastChild == dom || safari && /^(tr|table)$/i.test(dom.parentNode.nodeName)) {\n            return { ignore: true };\n        }\n    }\n    else if (dom.nodeName == \"IMG\" && dom.getAttribute(\"mark-placeholder\")) {\n        return { ignore: true };\n    }\n    return null;\n}\nconst isInline = /^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|img|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;\nfunction readDOMChange(view, from, to, typeOver, addedNodes) {\n    let compositionID = view.input.compositionPendingChanges || (view.composing ? view.input.compositionID : 0);\n    view.input.compositionPendingChanges = 0;\n    if (from < 0) {\n        let origin = view.input.lastSelectionTime > Date.now() - 50 ? view.input.lastSelectionOrigin : null;\n        let newSel = selectionFromDOM(view, origin);\n        if (newSel && !view.state.selection.eq(newSel)) {\n            if (chrome && android &&\n                view.input.lastKeyCode === 13 && Date.now() - 100 < view.input.lastKeyCodeTime &&\n                view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\"))))\n                return;\n            let tr = view.state.tr.setSelection(newSel);\n            if (origin == \"pointer\")\n                tr.setMeta(\"pointer\", true);\n            else if (origin == \"key\")\n                tr.scrollIntoView();\n            if (compositionID)\n                tr.setMeta(\"composition\", compositionID);\n            view.dispatch(tr);\n        }\n        return;\n    }\n    let $before = view.state.doc.resolve(from);\n    let shared = $before.sharedDepth(to);\n    from = $before.before(shared + 1);\n    to = view.state.doc.resolve(to).after(shared + 1);\n    let sel = view.state.selection;\n    let parse = parseBetween(view, from, to);\n    let doc = view.state.doc, compare = doc.slice(parse.from, parse.to);\n    let preferredPos, preferredSide;\n    // Prefer anchoring to end when Backspace is pressed\n    if (view.input.lastKeyCode === 8 && Date.now() - 100 < view.input.lastKeyCodeTime) {\n        preferredPos = view.state.selection.to;\n        preferredSide = \"end\";\n    }\n    else {\n        preferredPos = view.state.selection.from;\n        preferredSide = \"start\";\n    }\n    view.input.lastKeyCode = null;\n    let change = findDiff(compare.content, parse.doc.content, parse.from, preferredPos, preferredSide);\n    if (change)\n        view.input.domChangeCount++;\n    if ((ios && view.input.lastIOSEnter > Date.now() - 225 || android) &&\n        addedNodes.some(n => n.nodeType == 1 && !isInline.test(n.nodeName)) &&\n        (!change || change.endA >= change.endB) &&\n        view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\")))) {\n        view.input.lastIOSEnter = 0;\n        return;\n    }\n    if (!change) {\n        if (typeOver && sel instanceof TextSelection && !sel.empty && sel.$head.sameParent(sel.$anchor) &&\n            !view.composing && !(parse.sel && parse.sel.anchor != parse.sel.head)) {\n            change = { start: sel.from, endA: sel.to, endB: sel.to };\n        }\n        else {\n            if (parse.sel) {\n                let sel = resolveSelection(view, view.state.doc, parse.sel);\n                if (sel && !sel.eq(view.state.selection)) {\n                    let tr = view.state.tr.setSelection(sel);\n                    if (compositionID)\n                        tr.setMeta(\"composition\", compositionID);\n                    view.dispatch(tr);\n                }\n            }\n            return;\n        }\n    }\n    // Handle the case where overwriting a selection by typing matches\n    // the start or end of the selected content, creating a change\n    // that's smaller than what was actually overwritten.\n    if (view.state.selection.from < view.state.selection.to &&\n        change.start == change.endB &&\n        view.state.selection instanceof TextSelection) {\n        if (change.start > view.state.selection.from && change.start <= view.state.selection.from + 2 &&\n            view.state.selection.from >= parse.from) {\n            change.start = view.state.selection.from;\n        }\n        else if (change.endA < view.state.selection.to && change.endA >= view.state.selection.to - 2 &&\n            view.state.selection.to <= parse.to) {\n            change.endB += (view.state.selection.to - change.endA);\n            change.endA = view.state.selection.to;\n        }\n    }\n    // IE11 will insert a non-breaking space _ahead_ of the space after\n    // the cursor space when adding a space before another space. When\n    // that happened, adjust the change to cover the space instead.\n    if (ie && ie_version <= 11 && change.endB == change.start + 1 &&\n        change.endA == change.start && change.start > parse.from &&\n        parse.doc.textBetween(change.start - parse.from - 1, change.start - parse.from + 1) == \" \\u00a0\") {\n        change.start--;\n        change.endA--;\n        change.endB--;\n    }\n    let $from = parse.doc.resolveNoCache(change.start - parse.from);\n    let $to = parse.doc.resolveNoCache(change.endB - parse.from);\n    let $fromA = doc.resolve(change.start);\n    let inlineChange = $from.sameParent($to) && $from.parent.inlineContent && $fromA.end() >= change.endA;\n    let nextSel;\n    // If this looks like the effect of pressing Enter (or was recorded\n    // as being an iOS enter press), just dispatch an Enter key instead.\n    if (((ios && view.input.lastIOSEnter > Date.now() - 225 &&\n        (!inlineChange || addedNodes.some(n => n.nodeName == \"DIV\" || n.nodeName == \"P\"))) ||\n        (!inlineChange && $from.pos < parse.doc.content.size &&\n            (!$from.sameParent($to) || !$from.parent.inlineContent) &&\n            !/\\S/.test(parse.doc.textBetween($from.pos, $to.pos, \"\", \"\")) &&\n            (nextSel = Selection.findFrom(parse.doc.resolve($from.pos + 1), 1, true)) &&\n            nextSel.head > $from.pos)) &&\n        view.someProp(\"handleKeyDown\", f => f(view, keyEvent(13, \"Enter\")))) {\n        view.input.lastIOSEnter = 0;\n        return;\n    }\n    // Same for backspace\n    if (view.state.selection.anchor > change.start &&\n        looksLikeBackspace(doc, change.start, change.endA, $from, $to) &&\n        view.someProp(\"handleKeyDown\", f => f(view, keyEvent(8, \"Backspace\")))) {\n        if (android && chrome)\n            view.domObserver.suppressSelectionUpdates(); // #820\n        return;\n    }\n    // Chrome will occasionally, during composition, delete the\n    // entire composition and then immediately insert it again. This is\n    // used to detect that situation.\n    if (chrome && change.endB == change.start)\n        view.input.lastChromeDelete = Date.now();\n    // This tries to detect Android virtual keyboard\n    // enter-and-pick-suggestion action. That sometimes (see issue\n    // #1059) first fires a DOM mutation, before moving the selection to\n    // the newly created block. And then, because ProseMirror cleans up\n    // the DOM selection, it gives up moving the selection entirely,\n    // leaving the cursor in the wrong place. When that happens, we drop\n    // the new paragraph from the initial change, and fire a simulated\n    // enter key afterwards.\n    if (android && !inlineChange && $from.start() != $to.start() && $to.parentOffset == 0 && $from.depth == $to.depth &&\n        parse.sel && parse.sel.anchor == parse.sel.head && parse.sel.head == change.endA) {\n        change.endB -= 2;\n        $to = parse.doc.resolveNoCache(change.endB - parse.from);\n        setTimeout(() => {\n            view.someProp(\"handleKeyDown\", function (f) { return f(view, keyEvent(13, \"Enter\")); });\n        }, 20);\n    }\n    let chFrom = change.start, chTo = change.endA;\n    let mkTr = (base) => {\n        let tr = base || view.state.tr.replace(chFrom, chTo, parse.doc.slice(change.start - parse.from, change.endB - parse.from));\n        if (parse.sel) {\n            let sel = resolveSelection(view, tr.doc, parse.sel);\n            // Chrome will sometimes, during composition, report the\n            // selection in the wrong place. If it looks like that is\n            // happening, don't update the selection.\n            // Edge just doesn't move the cursor forward when you start typing\n            // in an empty block or between br nodes.\n            if (sel && !(chrome && view.composing && sel.empty &&\n                (change.start != change.endB || view.input.lastChromeDelete < Date.now() - 100) &&\n                (sel.head == chFrom || sel.head == tr.mapping.map(chTo) - 1) ||\n                ie && sel.empty && sel.head == chFrom))\n                tr.setSelection(sel);\n        }\n        if (compositionID)\n            tr.setMeta(\"composition\", compositionID);\n        return tr.scrollIntoView();\n    };\n    let markChange;\n    if (inlineChange) {\n        if ($from.pos == $to.pos) { // Deletion\n            // IE11 sometimes weirdly moves the DOM selection around after\n            // backspacing out the first element in a textblock\n            if (ie && ie_version <= 11 && $from.parentOffset == 0) {\n                view.domObserver.suppressSelectionUpdates();\n                setTimeout(() => selectionToDOM(view), 20);\n            }\n            let tr = mkTr(view.state.tr.delete(chFrom, chTo));\n            let marks = doc.resolve(change.start).marksAcross(doc.resolve(change.endA));\n            if (marks)\n                tr.ensureMarks(marks);\n            view.dispatch(tr);\n        }\n        else if ( // Adding or removing a mark\n        change.endA == change.endB &&\n            (markChange = isMarkChange($from.parent.content.cut($from.parentOffset, $to.parentOffset), $fromA.parent.content.cut($fromA.parentOffset, change.endA - $fromA.start())))) {\n            let tr = mkTr(view.state.tr);\n            if (markChange.type == \"add\")\n                tr.addMark(chFrom, chTo, markChange.mark);\n            else\n                tr.removeMark(chFrom, chTo, markChange.mark);\n            view.dispatch(tr);\n        }\n        else if ($from.parent.child($from.index()).isText && $from.index() == $to.index() - ($to.textOffset ? 0 : 1)) {\n            // Both positions in the same text node -- simply insert text\n            let text = $from.parent.textBetween($from.parentOffset, $to.parentOffset);\n            let deflt = () => mkTr(view.state.tr.insertText(text, chFrom, chTo));\n            if (!view.someProp(\"handleTextInput\", f => f(view, chFrom, chTo, text, deflt)))\n                view.dispatch(deflt());\n        }\n    }\n    else {\n        view.dispatch(mkTr());\n    }\n}\nfunction resolveSelection(view, doc, parsedSel) {\n    if (Math.max(parsedSel.anchor, parsedSel.head) > doc.content.size)\n        return null;\n    return selectionBetween(view, doc.resolve(parsedSel.anchor), doc.resolve(parsedSel.head));\n}\n// Given two same-length, non-empty fragments of inline content,\n// determine whether the first could be created from the second by\n// removing or adding a single mark type.\nfunction isMarkChange(cur, prev) {\n    let curMarks = cur.firstChild.marks, prevMarks = prev.firstChild.marks;\n    let added = curMarks, removed = prevMarks, type, mark, update;\n    for (let i = 0; i < prevMarks.length; i++)\n        added = prevMarks[i].removeFromSet(added);\n    for (let i = 0; i < curMarks.length; i++)\n        removed = curMarks[i].removeFromSet(removed);\n    if (added.length == 1 && removed.length == 0) {\n        mark = added[0];\n        type = \"add\";\n        update = (node) => node.mark(mark.addToSet(node.marks));\n    }\n    else if (added.length == 0 && removed.length == 1) {\n        mark = removed[0];\n        type = \"remove\";\n        update = (node) => node.mark(mark.removeFromSet(node.marks));\n    }\n    else {\n        return null;\n    }\n    let updated = [];\n    for (let i = 0; i < prev.childCount; i++)\n        updated.push(update(prev.child(i)));\n    if (Fragment.from(updated).eq(cur))\n        return { mark, type };\n}\nfunction looksLikeBackspace(old, start, end, $newStart, $newEnd) {\n    if ( // The content must have shrunk\n    end - start <= $newEnd.pos - $newStart.pos ||\n        // newEnd must point directly at or after the end of the block that newStart points into\n        skipClosingAndOpening($newStart, true, false) < $newEnd.pos)\n        return false;\n    let $start = old.resolve(start);\n    // Handle the case where, rather than joining blocks, the change just removed an entire block\n    if (!$newStart.parent.isTextblock) {\n        let after = $start.nodeAfter;\n        return after != null && end == start + after.nodeSize;\n    }\n    // Start must be at the end of a block\n    if ($start.parentOffset < $start.parent.content.size || !$start.parent.isTextblock)\n        return false;\n    let $next = old.resolve(skipClosingAndOpening($start, true, true));\n    // The next textblock must start before end and end near it\n    if (!$next.parent.isTextblock || $next.pos > end ||\n        skipClosingAndOpening($next, true, false) < end)\n        return false;\n    // The fragments after the join point must match\n    return $newStart.parent.content.cut($newStart.parentOffset).eq($next.parent.content);\n}\nfunction skipClosingAndOpening($pos, fromEnd, mayOpen) {\n    let depth = $pos.depth, end = fromEnd ? $pos.end() : $pos.pos;\n    while (depth > 0 && (fromEnd || $pos.indexAfter(depth) == $pos.node(depth).childCount)) {\n        depth--;\n        end++;\n        fromEnd = false;\n    }\n    if (mayOpen) {\n        let next = $pos.node(depth).maybeChild($pos.indexAfter(depth));\n        while (next && !next.isLeaf) {\n            next = next.firstChild;\n            end++;\n        }\n    }\n    return end;\n}\nfunction findDiff(a, b, pos, preferredPos, preferredSide) {\n    let start = a.findDiffStart(b, pos);\n    if (start == null)\n        return null;\n    let { a: endA, b: endB } = a.findDiffEnd(b, pos + a.size, pos + b.size);\n    if (preferredSide == \"end\") {\n        let adjust = Math.max(0, start - Math.min(endA, endB));\n        preferredPos -= endA + adjust - start;\n    }\n    if (endA < start && a.size < b.size) {\n        let move = preferredPos <= start && preferredPos >= endA ? start - preferredPos : 0;\n        start -= move;\n        if (start && start < b.size && isSurrogatePair(b.textBetween(start - 1, start + 1)))\n            start += move ? 1 : -1;\n        endB = start + (endB - endA);\n        endA = start;\n    }\n    else if (endB < start) {\n        let move = preferredPos <= start && preferredPos >= endB ? start - preferredPos : 0;\n        start -= move;\n        if (start && start < a.size && isSurrogatePair(a.textBetween(start - 1, start + 1)))\n            start += move ? 1 : -1;\n        endA = start + (endA - endB);\n        endB = start;\n    }\n    return { start, endA, endB };\n}\nfunction isSurrogatePair(str) {\n    if (str.length != 2)\n        return false;\n    let a = str.charCodeAt(0), b = str.charCodeAt(1);\n    return a >= 0xDC00 && a <= 0xDFFF && b >= 0xD800 && b <= 0xDBFF;\n}\n\n/**\n@internal\n*/\nconst __parseFromClipboard = parseFromClipboard;\n/**\n@internal\n*/\nconst __endComposition = endComposition;\n/**\nAn editor view manages the DOM structure that represents an\neditable document. Its state and behavior are determined by its\n[props](https://prosemirror.net/docs/ref/#view.DirectEditorProps).\n*/\nclass EditorView {\n    /**\n    Create a view. `place` may be a DOM node that the editor should\n    be appended to, a function that will place it into the document,\n    or an object whose `mount` property holds the node to use as the\n    document container. If it is `null`, the editor will not be\n    added to the document.\n    */\n    constructor(place, props) {\n        this._root = null;\n        /**\n        @internal\n        */\n        this.focused = false;\n        /**\n        Kludge used to work around a Chrome bug @internal\n        */\n        this.trackWrites = null;\n        this.mounted = false;\n        /**\n        @internal\n        */\n        this.markCursor = null;\n        /**\n        @internal\n        */\n        this.cursorWrapper = null;\n        /**\n        @internal\n        */\n        this.lastSelectedViewDesc = undefined;\n        /**\n        @internal\n        */\n        this.input = new InputState;\n        this.prevDirectPlugins = [];\n        this.pluginViews = [];\n        /**\n        Holds `true` when a hack node is needed in Firefox to prevent the\n        [space is eaten issue](https://github.com/ProseMirror/prosemirror/issues/651)\n        @internal\n        */\n        this.requiresGeckoHackNode = false;\n        /**\n        When editor content is being dragged, this object contains\n        information about the dragged slice and whether it is being\n        copied or moved. At any other time, it is null.\n        */\n        this.dragging = null;\n        this._props = props;\n        this.state = props.state;\n        this.directPlugins = props.plugins || [];\n        this.directPlugins.forEach(checkStateComponent);\n        this.dispatch = this.dispatch.bind(this);\n        this.dom = (place && place.mount) || document.createElement(\"div\");\n        if (place) {\n            if (place.appendChild)\n                place.appendChild(this.dom);\n            else if (typeof place == \"function\")\n                place(this.dom);\n            else if (place.mount)\n                this.mounted = true;\n        }\n        this.editable = getEditable(this);\n        updateCursorWrapper(this);\n        this.nodeViews = buildNodeViews(this);\n        this.docView = docViewDesc(this.state.doc, computeDocDeco(this), viewDecorations(this), this.dom, this);\n        this.domObserver = new DOMObserver(this, (from, to, typeOver, added) => readDOMChange(this, from, to, typeOver, added));\n        this.domObserver.start();\n        initInput(this);\n        this.updatePluginViews();\n    }\n    /**\n    Holds `true` when a\n    [composition](https://w3c.github.io/uievents/#events-compositionevents)\n    is active.\n    */\n    get composing() { return this.input.composing; }\n    /**\n    The view's current [props](https://prosemirror.net/docs/ref/#view.EditorProps).\n    */\n    get props() {\n        if (this._props.state != this.state) {\n            let prev = this._props;\n            this._props = {};\n            for (let name in prev)\n                this._props[name] = prev[name];\n            this._props.state = this.state;\n        }\n        return this._props;\n    }\n    /**\n    Update the view's props. Will immediately cause an update to\n    the DOM.\n    */\n    update(props) {\n        if (props.handleDOMEvents != this._props.handleDOMEvents)\n            ensureListeners(this);\n        let prevProps = this._props;\n        this._props = props;\n        if (props.plugins) {\n            props.plugins.forEach(checkStateComponent);\n            this.directPlugins = props.plugins;\n        }\n        this.updateStateInner(props.state, prevProps);\n    }\n    /**\n    Update the view by updating existing props object with the object\n    given as argument. Equivalent to `view.update(Object.assign({},\n    view.props, props))`.\n    */\n    setProps(props) {\n        let updated = {};\n        for (let name in this._props)\n            updated[name] = this._props[name];\n        updated.state = this.state;\n        for (let name in props)\n            updated[name] = props[name];\n        this.update(updated);\n    }\n    /**\n    Update the editor's `state` prop, without touching any of the\n    other props.\n    */\n    updateState(state) {\n        this.updateStateInner(state, this._props);\n    }\n    updateStateInner(state, prevProps) {\n        var _a;\n        let prev = this.state, redraw = false, updateSel = false;\n        // When stored marks are added, stop composition, so that they can\n        // be displayed.\n        if (state.storedMarks && this.composing) {\n            clearComposition(this);\n            updateSel = true;\n        }\n        this.state = state;\n        let pluginsChanged = prev.plugins != state.plugins || this._props.plugins != prevProps.plugins;\n        if (pluginsChanged || this._props.plugins != prevProps.plugins || this._props.nodeViews != prevProps.nodeViews) {\n            let nodeViews = buildNodeViews(this);\n            if (changedNodeViews(nodeViews, this.nodeViews)) {\n                this.nodeViews = nodeViews;\n                redraw = true;\n            }\n        }\n        if (pluginsChanged || prevProps.handleDOMEvents != this._props.handleDOMEvents) {\n            ensureListeners(this);\n        }\n        this.editable = getEditable(this);\n        updateCursorWrapper(this);\n        let innerDeco = viewDecorations(this), outerDeco = computeDocDeco(this);\n        let scroll = prev.plugins != state.plugins && !prev.doc.eq(state.doc) ? \"reset\"\n            : state.scrollToSelection > prev.scrollToSelection ? \"to selection\" : \"preserve\";\n        let updateDoc = redraw || !this.docView.matchesNode(state.doc, outerDeco, innerDeco);\n        if (updateDoc || !state.selection.eq(prev.selection))\n            updateSel = true;\n        let oldScrollPos = scroll == \"preserve\" && updateSel && this.dom.style.overflowAnchor == null && storeScrollPos(this);\n        if (updateSel) {\n            this.domObserver.stop();\n            // Work around an issue in Chrome, IE, and Edge where changing\n            // the DOM around an active selection puts it into a broken\n            // state where the thing the user sees differs from the\n            // selection reported by the Selection object (#710, #973,\n            // #1011, #1013, #1035).\n            let forceSelUpdate = updateDoc && (ie || chrome) && !this.composing &&\n                !prev.selection.empty && !state.selection.empty && selectionContextChanged(prev.selection, state.selection);\n            if (updateDoc) {\n                // If the node that the selection points into is written to,\n                // Chrome sometimes starts misreporting the selection, so this\n                // tracks that and forces a selection reset when our update\n                // did write to the node.\n                let chromeKludge = chrome ? (this.trackWrites = this.domSelectionRange().focusNode) : null;\n                if (this.composing)\n                    this.input.compositionNode = findCompositionNode(this);\n                if (redraw || !this.docView.update(state.doc, outerDeco, innerDeco, this)) {\n                    this.docView.updateOuterDeco(outerDeco);\n                    this.docView.destroy();\n                    this.docView = docViewDesc(state.doc, outerDeco, innerDeco, this.dom, this);\n                }\n                if (chromeKludge && !this.trackWrites)\n                    forceSelUpdate = true;\n            }\n            // Work around for an issue where an update arriving right between\n            // a DOM selection change and the \"selectionchange\" event for it\n            // can cause a spurious DOM selection update, disrupting mouse\n            // drag selection.\n            if (forceSelUpdate ||\n                !(this.input.mouseDown && this.domObserver.currentSelection.eq(this.domSelectionRange()) &&\n                    anchorInRightPlace(this))) {\n                selectionToDOM(this, forceSelUpdate);\n            }\n            else {\n                syncNodeSelection(this, state.selection);\n                this.domObserver.setCurSelection();\n            }\n            this.domObserver.start();\n        }\n        this.updatePluginViews(prev);\n        if (((_a = this.dragging) === null || _a === void 0 ? void 0 : _a.node) && !prev.doc.eq(state.doc))\n            this.updateDraggedNode(this.dragging, prev);\n        if (scroll == \"reset\") {\n            this.dom.scrollTop = 0;\n        }\n        else if (scroll == \"to selection\") {\n            this.scrollToSelection();\n        }\n        else if (oldScrollPos) {\n            resetScrollPos(oldScrollPos);\n        }\n    }\n    /**\n    @internal\n    */\n    scrollToSelection() {\n        let startDOM = this.domSelectionRange().focusNode;\n        if (!startDOM || !this.dom.contains(startDOM.nodeType == 1 ? startDOM : startDOM.parentNode)) ;\n        else if (this.someProp(\"handleScrollToSelection\", f => f(this))) ;\n        else if (this.state.selection instanceof NodeSelection) {\n            let target = this.docView.domAfterPos(this.state.selection.from);\n            if (target.nodeType == 1)\n                scrollRectIntoView(this, target.getBoundingClientRect(), startDOM);\n        }\n        else {\n            scrollRectIntoView(this, this.coordsAtPos(this.state.selection.head, 1), startDOM);\n        }\n    }\n    destroyPluginViews() {\n        let view;\n        while (view = this.pluginViews.pop())\n            if (view.destroy)\n                view.destroy();\n    }\n    updatePluginViews(prevState) {\n        if (!prevState || prevState.plugins != this.state.plugins || this.directPlugins != this.prevDirectPlugins) {\n            this.prevDirectPlugins = this.directPlugins;\n            this.destroyPluginViews();\n            for (let i = 0; i < this.directPlugins.length; i++) {\n                let plugin = this.directPlugins[i];\n                if (plugin.spec.view)\n                    this.pluginViews.push(plugin.spec.view(this));\n            }\n            for (let i = 0; i < this.state.plugins.length; i++) {\n                let plugin = this.state.plugins[i];\n                if (plugin.spec.view)\n                    this.pluginViews.push(plugin.spec.view(this));\n            }\n        }\n        else {\n            for (let i = 0; i < this.pluginViews.length; i++) {\n                let pluginView = this.pluginViews[i];\n                if (pluginView.update)\n                    pluginView.update(this, prevState);\n            }\n        }\n    }\n    updateDraggedNode(dragging, prev) {\n        let sel = dragging.node, found = -1;\n        if (this.state.doc.nodeAt(sel.from) == sel.node) {\n            found = sel.from;\n        }\n        else {\n            let movedPos = sel.from + (this.state.doc.content.size - prev.doc.content.size);\n            let moved = movedPos > 0 && this.state.doc.nodeAt(movedPos);\n            if (moved == sel.node)\n                found = movedPos;\n        }\n        this.dragging = new Dragging(dragging.slice, dragging.move, found < 0 ? undefined : NodeSelection.create(this.state.doc, found));\n    }\n    someProp(propName, f) {\n        let prop = this._props && this._props[propName], value;\n        if (prop != null && (value = f ? f(prop) : prop))\n            return value;\n        for (let i = 0; i < this.directPlugins.length; i++) {\n            let prop = this.directPlugins[i].props[propName];\n            if (prop != null && (value = f ? f(prop) : prop))\n                return value;\n        }\n        let plugins = this.state.plugins;\n        if (plugins)\n            for (let i = 0; i < plugins.length; i++) {\n                let prop = plugins[i].props[propName];\n                if (prop != null && (value = f ? f(prop) : prop))\n                    return value;\n            }\n    }\n    /**\n    Query whether the view has focus.\n    */\n    hasFocus() {\n        // Work around IE not handling focus correctly if resize handles are shown.\n        // If the cursor is inside an element with resize handles, activeElement\n        // will be that element instead of this.dom.\n        if (ie) {\n            // If activeElement is within this.dom, and there are no other elements\n            // setting `contenteditable` to false in between, treat it as focused.\n            let node = this.root.activeElement;\n            if (node == this.dom)\n                return true;\n            if (!node || !this.dom.contains(node))\n                return false;\n            while (node && this.dom != node && this.dom.contains(node)) {\n                if (node.contentEditable == 'false')\n                    return false;\n                node = node.parentElement;\n            }\n            return true;\n        }\n        return this.root.activeElement == this.dom;\n    }\n    /**\n    Focus the editor.\n    */\n    focus() {\n        this.domObserver.stop();\n        if (this.editable)\n            focusPreventScroll(this.dom);\n        selectionToDOM(this);\n        this.domObserver.start();\n    }\n    /**\n    Get the document root in which the editor exists. This will\n    usually be the top-level `document`, but might be a [shadow\n    DOM](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Shadow_DOM)\n    root if the editor is inside one.\n    */\n    get root() {\n        let cached = this._root;\n        if (cached == null)\n            for (let search = this.dom.parentNode; search; search = search.parentNode) {\n                if (search.nodeType == 9 || (search.nodeType == 11 && search.host)) {\n                    if (!search.getSelection)\n                        Object.getPrototypeOf(search).getSelection = () => search.ownerDocument.getSelection();\n                    return this._root = search;\n                }\n            }\n        return cached || document;\n    }\n    /**\n    When an existing editor view is moved to a new document or\n    shadow tree, call this to make it recompute its root.\n    */\n    updateRoot() {\n        this._root = null;\n    }\n    /**\n    Given a pair of viewport coordinates, return the document\n    position that corresponds to them. May return null if the given\n    coordinates aren't inside of the editor. When an object is\n    returned, its `pos` property is the position nearest to the\n    coordinates, and its `inside` property holds the position of the\n    inner node that the position falls inside of, or -1 if it is at\n    the top level, not in any node.\n    */\n    posAtCoords(coords) {\n        return posAtCoords(this, coords);\n    }\n    /**\n    Returns the viewport rectangle at a given document position.\n    `left` and `right` will be the same number, as this returns a\n    flat cursor-ish rectangle. If the position is between two things\n    that aren't directly adjacent, `side` determines which element\n    is used. When < 0, the element before the position is used,\n    otherwise the element after.\n    */\n    coordsAtPos(pos, side = 1) {\n        return coordsAtPos(this, pos, side);\n    }\n    /**\n    Find the DOM position that corresponds to the given document\n    position. When `side` is negative, find the position as close as\n    possible to the content before the position. When positive,\n    prefer positions close to the content after the position. When\n    zero, prefer as shallow a position as possible.\n    \n    Note that you should **not** mutate the editor's internal DOM,\n    only inspect it (and even that is usually not necessary).\n    */\n    domAtPos(pos, side = 0) {\n        return this.docView.domFromPos(pos, side);\n    }\n    /**\n    Find the DOM node that represents the document node after the\n    given position. May return `null` when the position doesn't point\n    in front of a node or if the node is inside an opaque node view.\n    \n    This is intended to be able to call things like\n    `getBoundingClientRect` on that DOM node. Do **not** mutate the\n    editor DOM directly, or add styling this way, since that will be\n    immediately overriden by the editor as it redraws the node.\n    */\n    nodeDOM(pos) {\n        let desc = this.docView.descAt(pos);\n        return desc ? desc.nodeDOM : null;\n    }\n    /**\n    Find the document position that corresponds to a given DOM\n    position. (Whenever possible, it is preferable to inspect the\n    document structure directly, rather than poking around in the\n    DOM, but sometimes—for example when interpreting an event\n    target—you don't have a choice.)\n    \n    The `bias` parameter can be used to influence which side of a DOM\n    node to use when the position is inside a leaf node.\n    */\n    posAtDOM(node, offset, bias = -1) {\n        let pos = this.docView.posFromDOM(node, offset, bias);\n        if (pos == null)\n            throw new RangeError(\"DOM position not inside the editor\");\n        return pos;\n    }\n    /**\n    Find out whether the selection is at the end of a textblock when\n    moving in a given direction. When, for example, given `\"left\"`,\n    it will return true if moving left from the current cursor\n    position would leave that position's parent textblock. Will apply\n    to the view's current state by default, but it is possible to\n    pass a different state.\n    */\n    endOfTextblock(dir, state) {\n        return endOfTextblock(this, state || this.state, dir);\n    }\n    /**\n    Run the editor's paste logic with the given HTML string. The\n    `event`, if given, will be passed to the\n    [`handlePaste`](https://prosemirror.net/docs/ref/#view.EditorProps.handlePaste) hook.\n    */\n    pasteHTML(html, event) {\n        return doPaste(this, \"\", html, false, event || new ClipboardEvent(\"paste\"));\n    }\n    /**\n    Run the editor's paste logic with the given plain-text input.\n    */\n    pasteText(text, event) {\n        return doPaste(this, text, null, true, event || new ClipboardEvent(\"paste\"));\n    }\n    /**\n    Serialize the given slice as it would be if it was copied from\n    this editor. Returns a DOM element that contains a\n    representation of the slice as its children, a textual\n    representation, and the transformed slice (which can be\n    different from the given input due to hooks like\n    [`transformCopied`](https://prosemirror.net/docs/ref/#view.EditorProps.transformCopied)).\n    */\n    serializeForClipboard(slice) {\n        return serializeForClipboard(this, slice);\n    }\n    /**\n    Removes the editor from the DOM and destroys all [node\n    views](https://prosemirror.net/docs/ref/#view.NodeView).\n    */\n    destroy() {\n        if (!this.docView)\n            return;\n        destroyInput(this);\n        this.destroyPluginViews();\n        if (this.mounted) {\n            this.docView.update(this.state.doc, [], viewDecorations(this), this);\n            this.dom.textContent = \"\";\n        }\n        else if (this.dom.parentNode) {\n            this.dom.parentNode.removeChild(this.dom);\n        }\n        this.docView.destroy();\n        this.docView = null;\n        clearReusedRange();\n    }\n    /**\n    This is true when the view has been\n    [destroyed](https://prosemirror.net/docs/ref/#view.EditorView.destroy) (and thus should not be\n    used anymore).\n    */\n    get isDestroyed() {\n        return this.docView == null;\n    }\n    /**\n    Used for testing.\n    */\n    dispatchEvent(event) {\n        return dispatchEvent(this, event);\n    }\n    /**\n    @internal\n    */\n    domSelectionRange() {\n        let sel = this.domSelection();\n        if (!sel)\n            return { focusNode: null, focusOffset: 0, anchorNode: null, anchorOffset: 0 };\n        return safari && this.root.nodeType === 11 &&\n            deepActiveElement(this.dom.ownerDocument) == this.dom && safariShadowSelectionRange(this, sel) || sel;\n    }\n    /**\n    @internal\n    */\n    domSelection() {\n        return this.root.getSelection();\n    }\n}\nEditorView.prototype.dispatch = function (tr) {\n    let dispatchTransaction = this._props.dispatchTransaction;\n    if (dispatchTransaction)\n        dispatchTransaction.call(this, tr);\n    else\n        this.updateState(this.state.apply(tr));\n};\nfunction computeDocDeco(view) {\n    let attrs = Object.create(null);\n    attrs.class = \"ProseMirror\";\n    attrs.contenteditable = String(view.editable);\n    view.someProp(\"attributes\", value => {\n        if (typeof value == \"function\")\n            value = value(view.state);\n        if (value)\n            for (let attr in value) {\n                if (attr == \"class\")\n                    attrs.class += \" \" + value[attr];\n                else if (attr == \"style\")\n                    attrs.style = (attrs.style ? attrs.style + \";\" : \"\") + value[attr];\n                else if (!attrs[attr] && attr != \"contenteditable\" && attr != \"nodeName\")\n                    attrs[attr] = String(value[attr]);\n            }\n    });\n    if (!attrs.translate)\n        attrs.translate = \"no\";\n    return [Decoration.node(0, view.state.doc.content.size, attrs)];\n}\nfunction updateCursorWrapper(view) {\n    if (view.markCursor) {\n        let dom = document.createElement(\"img\");\n        dom.className = \"ProseMirror-separator\";\n        dom.setAttribute(\"mark-placeholder\", \"true\");\n        dom.setAttribute(\"alt\", \"\");\n        view.cursorWrapper = { dom, deco: Decoration.widget(view.state.selection.from, dom, { raw: true, marks: view.markCursor }) };\n    }\n    else {\n        view.cursorWrapper = null;\n    }\n}\nfunction getEditable(view) {\n    return !view.someProp(\"editable\", value => value(view.state) === false);\n}\nfunction selectionContextChanged(sel1, sel2) {\n    let depth = Math.min(sel1.$anchor.sharedDepth(sel1.head), sel2.$anchor.sharedDepth(sel2.head));\n    return sel1.$anchor.start(depth) != sel2.$anchor.start(depth);\n}\nfunction buildNodeViews(view) {\n    let result = Object.create(null);\n    function add(obj) {\n        for (let prop in obj)\n            if (!Object.prototype.hasOwnProperty.call(result, prop))\n                result[prop] = obj[prop];\n    }\n    view.someProp(\"nodeViews\", add);\n    view.someProp(\"markViews\", add);\n    return result;\n}\nfunction changedNodeViews(a, b) {\n    let nA = 0, nB = 0;\n    for (let prop in a) {\n        if (a[prop] != b[prop])\n            return true;\n        nA++;\n    }\n    for (let _ in b)\n        nB++;\n    return nA != nB;\n}\nfunction checkStateComponent(plugin) {\n    if (plugin.spec.state || plugin.spec.filterTransaction || plugin.spec.appendTransaction)\n        throw new RangeError(\"Plugins passed directly to the view must not have a state component\");\n}\n\nexport { Decoration, DecorationSet, EditorView, __endComposition, __parseFromClipboard };\n"], "mappings": ";;;;;;;;;;;;;;AAIA,IAAM,WAAW,SAAU,MAAM;AAC7B,WAAS,QAAQ,KAAI,SAAS;AAC1B,WAAO,KAAK;AACZ,QAAI,CAAC;AACD,aAAO;AAAA,EACf;AACJ;AACA,IAAM,aAAa,SAAU,MAAM;AAC/B,MAAI,SAAS,KAAK,gBAAgB,KAAK;AACvC,SAAO,UAAU,OAAO,YAAY,KAAK,OAAO,OAAO;AAC3D;AACA,IAAI,cAAc;AAIlB,IAAM,YAAY,SAAU,MAAM,MAAM,IAAI;AACxC,MAAI,QAAQ,gBAAgB,cAAc,SAAS,YAAY;AAC/D,QAAM,OAAO,MAAM,MAAM,OAAO,KAAK,UAAU,SAAS,EAAE;AAC1D,QAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,SAAO;AACX;AACA,IAAM,mBAAmB,WAAY;AACjC,gBAAc;AAClB;AAIA,IAAM,uBAAuB,SAAU,MAAM,KAAK,YAAY,WAAW;AACrE,SAAO,eAAe,QAAQ,MAAM,KAAK,YAAY,WAAW,EAAE,KAC9D,QAAQ,MAAM,KAAK,YAAY,WAAW,CAAC;AACnD;AACA,IAAM,eAAe;AACrB,SAAS,QAAQ,MAAM,KAAK,YAAY,WAAW,KAAK;AACpD,MAAI;AACJ,aAAS;AACL,QAAI,QAAQ,cAAc,OAAO;AAC7B,aAAO;AACX,QAAI,QAAQ,MAAM,IAAI,IAAI,SAAS,IAAI,IAAI;AACvC,UAAI,SAAS,KAAK;AAClB,UAAI,CAAC,UAAU,OAAO,YAAY,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,KAAK,QAAQ,KACxF,KAAK,mBAAmB;AACxB,eAAO;AACX,YAAM,SAAS,IAAI,KAAK,MAAM,IAAI,IAAI;AACtC,aAAO;AAAA,IACX,WACS,KAAK,YAAY,GAAG;AACzB,UAAI,QAAQ,KAAK,WAAW,OAAO,MAAM,IAAI,KAAK,EAAE;AACpD,UAAI,MAAM,YAAY,KAAK,MAAM,mBAAmB,SAAS;AACzD,aAAK,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAChE,iBAAO;AAAA;AAEP,iBAAO;AAAA,MACf,OACK;AACD,eAAO;AACP,cAAM,MAAM,IAAI,SAAS,IAAI,IAAI;AAAA,MACrC;AAAA,IACJ,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,SAAS,MAAM;AACpB,SAAO,KAAK,YAAY,IAAI,KAAK,UAAU,SAAS,KAAK,WAAW;AACxE;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACpC,aAAS;AACL,QAAI,KAAK,YAAY,KAAK;AACtB,aAAO;AACX,QAAI,KAAK,YAAY,KAAK,SAAS,GAAG;AAClC,UAAI,KAAK,mBAAmB;AACxB,eAAO;AACX,aAAO,KAAK,WAAW,SAAS,CAAC;AACjC,eAAS,SAAS,IAAI;AAAA,IAC1B,WACS,KAAK,cAAc,CAAC,aAAa,IAAI,GAAG;AAC7C,eAAS,SAAS,IAAI;AACtB,aAAO,KAAK;AAAA,IAChB,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,gBAAgB,MAAM,QAAQ;AACnC,aAAS;AACL,QAAI,KAAK,YAAY,KAAK,SAAS,KAAK,UAAU;AAC9C,aAAO;AACX,QAAI,KAAK,YAAY,KAAK,SAAS,KAAK,WAAW,QAAQ;AACvD,UAAI,KAAK,mBAAmB;AACxB,eAAO;AACX,aAAO,KAAK,WAAW,MAAM;AAC7B,eAAS;AAAA,IACb,WACS,KAAK,cAAc,CAAC,aAAa,IAAI,GAAG;AAC7C,eAAS,SAAS,IAAI,IAAI;AAC1B,aAAO,KAAK;AAAA,IAChB,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,SAAS,MAAM,QAAQ,QAAQ;AACpC,WAAS,UAAU,UAAU,GAAG,QAAQ,UAAU,SAAS,IAAI,GAAG,WAAW,SAAQ;AACjF,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,QAAQ,SAAS,IAAI;AACzB,WAAO,KAAK;AACZ,QAAI,CAAC;AACD,aAAO;AACX,cAAU,WAAW,SAAS;AAC9B,YAAQ,SAAS,SAAS,SAAS,IAAI;AAAA,EAC3C;AACJ;AACA,SAAS,aAAa,KAAK;AACvB,MAAI;AACJ,WAAS,MAAM,KAAK,KAAK,MAAM,IAAI;AAC/B,QAAI,OAAO,IAAI;AACX;AACR,SAAO,QAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY,KAAK,OAAO,OAAO,KAAK,cAAc;AAC5F;AAGA,IAAM,qBAAqB,SAAU,QAAQ;AACzC,SAAO,OAAO,aAAa,qBAAqB,OAAO,WAAW,OAAO,aAAa,OAAO,YAAY,OAAO,YAAY;AAChI;AACA,SAAS,SAAS,SAAS,KAAK;AAC5B,MAAI,QAAQ,SAAS,YAAY,OAAO;AACxC,QAAM,UAAU,WAAW,MAAM,IAAI;AACrC,QAAM,UAAU;AAChB,QAAM,MAAM,MAAM,OAAO;AACzB,SAAO;AACX;AACA,SAAS,kBAAkBA,MAAK;AAC5B,MAAI,MAAMA,KAAI;AACd,SAAO,OAAO,IAAI;AACd,UAAM,IAAI,WAAW;AACzB,SAAO;AACX;AACA,SAAS,eAAeA,MAAK,GAAG,GAAG;AAC/B,MAAIA,KAAI,wBAAwB;AAC5B,QAAI;AACA,UAAI,MAAMA,KAAI,uBAAuB,GAAG,CAAC;AAIzC,UAAI;AACA,eAAO,EAAE,MAAM,IAAI,YAAY,QAAQ,KAAK,IAAI,SAAS,IAAI,UAAU,GAAG,IAAI,MAAM,EAAE;AAAA,IAC9F,SACO,GAAG;AAAA,IAAE;AAAA,EAChB;AACA,MAAIA,KAAI,qBAAqB;AACzB,QAAI,QAAQA,KAAI,oBAAoB,GAAG,CAAC;AACxC,QAAI;AACA,aAAO,EAAE,MAAM,MAAM,gBAAgB,QAAQ,KAAK,IAAI,SAAS,MAAM,cAAc,GAAG,MAAM,WAAW,EAAE;AAAA,EACjH;AACJ;AAEA,IAAM,MAAM,OAAO,aAAa,cAAc,YAAY;AAC1D,IAAM,MAAM,OAAO,YAAY,cAAc,WAAW;AACxD,IAAM,QAAS,OAAO,IAAI,aAAc;AACxC,IAAM,UAAU,cAAc,KAAK,KAAK;AACxC,IAAM,YAAY,UAAU,KAAK,KAAK;AACtC,IAAM,UAAU,wCAAwC,KAAK,KAAK;AAClE,IAAM,KAAK,CAAC,EAAE,aAAa,WAAW;AACtC,IAAM,aAAa,YAAY,SAAS,eAAe,UAAU,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI;AACvG,IAAM,QAAQ,CAAC,MAAM,gBAAgB,KAAK,KAAK;AAC/C,SAAS,EAAE,iBAAiB,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;AACpD,IAAM,UAAU,CAAC,MAAM,gBAAgB,KAAK,KAAK;AACjD,IAAM,SAAS,CAAC,CAAC;AACjB,IAAM,iBAAiB,UAAU,CAAC,QAAQ,CAAC,IAAI;AAC/C,IAAM,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,iBAAiB,KAAK,IAAI,MAAM;AAE/D,IAAM,MAAM,WAAW,cAAc,KAAK,KAAK,KAAK,CAAC,CAAC,OAAO,IAAI,iBAAiB;AAClF,IAAM,MAAM,QAAQ,MAAM,MAAM,KAAK,IAAI,QAAQ,IAAI;AACrD,IAAM,UAAU,MAAM,MAAM,KAAK,IAAI,QAAQ,IAAI;AACjD,IAAM,UAAU,aAAa,KAAK,KAAK;AACvC,IAAM,SAAS,CAAC,CAAC,OAAO,yBAAyB,IAAI,gBAAgB;AACrE,IAAM,iBAAiB,SAAS,EAAE,uBAAuB,KAAK,UAAU,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;AAEnG,SAAS,WAAWA,MAAK;AACrB,MAAI,KAAKA,KAAI,eAAeA,KAAI,YAAY;AAC5C,MAAI;AACA,WAAO;AAAA,MACH,MAAM;AAAA,MAAG,OAAO,GAAG;AAAA,MACnB,KAAK;AAAA,MAAG,QAAQ,GAAG;AAAA,IACvB;AACJ,SAAO;AAAA,IAAE,MAAM;AAAA,IAAG,OAAOA,KAAI,gBAAgB;AAAA,IACzC,KAAK;AAAA,IAAG,QAAQA,KAAI,gBAAgB;AAAA,EAAa;AACzD;AACA,SAAS,QAAQ,OAAO,MAAM;AAC1B,SAAO,OAAO,SAAS,WAAW,QAAQ,MAAM,IAAI;AACxD;AACA,SAAS,WAAW,MAAM;AACtB,MAAI,OAAO,KAAK,sBAAsB;AAEtC,MAAI,SAAU,KAAK,QAAQ,KAAK,eAAgB;AAChD,MAAI,SAAU,KAAK,SAAS,KAAK,gBAAiB;AAElD,SAAO;AAAA,IAAE,MAAM,KAAK;AAAA,IAAM,OAAO,KAAK,OAAO,KAAK,cAAc;AAAA,IAC5D,KAAK,KAAK;AAAA,IAAK,QAAQ,KAAK,MAAM,KAAK,eAAe;AAAA,EAAO;AACrE;AACA,SAAS,mBAAmB,MAAM,MAAM,UAAU;AAC9C,MAAI,kBAAkB,KAAK,SAAS,iBAAiB,KAAK,GAAG,eAAe,KAAK,SAAS,cAAc,KAAK;AAC7G,MAAIA,OAAM,KAAK,IAAI;AACnB,WAAS,SAAS,YAAY,KAAK,SAAO;AACtC,QAAI,CAAC;AACD;AACJ,QAAI,OAAO,YAAY,GAAG;AACtB,eAAS,WAAW,MAAM;AAC1B;AAAA,IACJ;AACA,QAAI,MAAM;AACV,QAAI,QAAQ,OAAOA,KAAI;AACvB,QAAI,WAAW,QAAQ,WAAWA,IAAG,IAAI,WAAW,GAAG;AACvD,QAAI,QAAQ,GAAG,QAAQ;AACvB,QAAI,KAAK,MAAM,SAAS,MAAM,QAAQ,iBAAiB,KAAK;AACxD,cAAQ,EAAE,SAAS,MAAM,KAAK,MAAM,QAAQ,cAAc,KAAK;AAAA,aAC1D,KAAK,SAAS,SAAS,SAAS,QAAQ,iBAAiB,QAAQ;AACtE,cAAQ,KAAK,SAAS,KAAK,MAAM,SAAS,SAAS,SAAS,MACtD,KAAK,MAAM,QAAQ,cAAc,KAAK,IAAI,SAAS,MACnD,KAAK,SAAS,SAAS,SAAS,QAAQ,cAAc,QAAQ;AACxE,QAAI,KAAK,OAAO,SAAS,OAAO,QAAQ,iBAAiB,MAAM;AAC3D,cAAQ,EAAE,SAAS,OAAO,KAAK,OAAO,QAAQ,cAAc,MAAM;AAAA,aAC7D,KAAK,QAAQ,SAAS,QAAQ,QAAQ,iBAAiB,OAAO;AACnE,cAAQ,KAAK,QAAQ,SAAS,QAAQ,QAAQ,cAAc,OAAO;AACvE,QAAI,SAAS,OAAO;AAChB,UAAI,OAAO;AACP,QAAAA,KAAI,YAAY,SAAS,OAAO,KAAK;AAAA,MACzC,OACK;AACD,YAAI,SAAS,IAAI,YAAY,SAAS,IAAI;AAC1C,YAAI;AACA,cAAI,aAAa;AACrB,YAAI;AACA,cAAI,cAAc;AACtB,YAAI,KAAK,IAAI,aAAa,QAAQ,KAAK,IAAI,YAAY;AACvD,eAAO,EAAE,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,QAAQ,IAAI,QAAQ,KAAK,SAAS,GAAG;AAAA,MACxG;AAAA,IACJ;AACA,QAAI,MAAM,QAAQ,UAAU,iBAAiB,MAAM,EAAE;AACrD,QAAI,mBAAmB,KAAK,GAAG;AAC3B;AACJ,aAAS,OAAO,aAAa,OAAO,eAAe,WAAW,MAAM;AAAA,EACxE;AACJ;AAKA,SAAS,eAAe,MAAM;AAC1B,MAAI,OAAO,KAAK,IAAI,sBAAsB,GAAG,SAAS,KAAK,IAAI,GAAG,KAAK,GAAG;AAC1E,MAAI,QAAQ;AACZ,WAAS,KAAK,KAAK,OAAO,KAAK,SAAS,GAAG,IAAI,SAAS,GAAG,IAAI,KAAK,IAAI,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG;AACvG,QAAI,MAAM,KAAK,KAAK,iBAAiB,GAAG,CAAC;AACzC,QAAI,CAAC,OAAO,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI,SAAS,GAAG;AACjD;AACJ,QAAI,YAAY,IAAI,sBAAsB;AAC1C,QAAI,UAAU,OAAO,SAAS,IAAI;AAC9B,eAAS;AACT,eAAS,UAAU;AACnB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,QAAgB,QAAgB,OAAO,YAAY,KAAK,GAAG,EAAE;AAC1E;AACA,SAAS,YAAY,KAAK;AACtB,MAAI,QAAQ,CAAC,GAAGA,OAAM,IAAI;AAC1B,WAAS,MAAM,KAAK,KAAK,MAAM,WAAW,GAAG,GAAG;AAC5C,UAAM,KAAK,EAAE,KAAK,KAAK,KAAK,IAAI,WAAW,MAAM,IAAI,WAAW,CAAC;AACjE,QAAI,OAAOA;AACP;AAAA,EACR;AACA,SAAO;AACX;AAGA,SAAS,eAAe,EAAE,QAAQ,QAAQ,MAAM,GAAG;AAC/C,MAAI,YAAY,SAAS,OAAO,sBAAsB,EAAE,MAAM;AAC9D,qBAAmB,OAAO,aAAa,IAAI,IAAI,YAAY,MAAM;AACrE;AACA,SAAS,mBAAmB,OAAO,MAAM;AACrC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,EAAE,KAAK,KAAK,KAAK,IAAI,MAAM,CAAC;AAChC,QAAI,IAAI,aAAa,MAAM;AACvB,UAAI,YAAY,MAAM;AAC1B,QAAI,IAAI,cAAc;AAClB,UAAI,aAAa;AAAA,EACzB;AACJ;AACA,IAAI,yBAAyB;AAG7B,SAAS,mBAAmB,KAAK;AAC7B,MAAI,IAAI;AACJ,WAAO,IAAI,UAAU;AACzB,MAAI;AACA,WAAO,IAAI,MAAM,sBAAsB;AAC3C,MAAI,SAAS,YAAY,GAAG;AAC5B,MAAI,MAAM,0BAA0B,OAAO;AAAA,IACvC,IAAI,gBAAgB;AAChB,+BAAyB,EAAE,eAAe,KAAK;AAC/C,aAAO;AAAA,IACX;AAAA,EACJ,IAAI,MAAS;AACb,MAAI,CAAC,wBAAwB;AACzB,6BAAyB;AACzB,uBAAmB,QAAQ,CAAC;AAAA,EAChC;AACJ;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACpC,MAAI,SAAS,YAAY,KAAK,eAAe,SAAS;AACtD,MAAI,SAAS,OAAO,KAAK,SAAS,OAAO;AACzC,MAAI,YAAY;AAChB,WAAS,QAAQ,KAAK,YAAY,aAAa,GAAG,OAAO,QAAQ,MAAM,aAAa,cAAc;AAC9F,QAAI;AACJ,QAAI,MAAM,YAAY;AAClB,cAAQ,MAAM,eAAe;AAAA,aACxB,MAAM,YAAY;AACvB,cAAQ,UAAU,KAAK,EAAE,eAAe;AAAA;AAExC;AACJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI,KAAK,OAAO,UAAU,KAAK,UAAU,QAAQ;AAC7C,iBAAS,KAAK,IAAI,KAAK,QAAQ,MAAM;AACrC,iBAAS,KAAK,IAAI,KAAK,KAAK,MAAM;AAClC,YAAI,KAAK,KAAK,OAAO,OAAO,OAAO,KAAK,OAAO,OAAO,OAChD,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,KAAK,QAAQ;AAC5D,YAAI,KAAK,WAAW;AAChB,oBAAU;AACV,sBAAY;AACZ,0BAAgB,MAAM,QAAQ,YAAY,IAAI;AAAA,YAC1C,MAAM,KAAK,QAAQ,OAAO,OAAO,KAAK,QAAQ,KAAK;AAAA,YACnD,KAAK,OAAO;AAAA,UAChB,IAAI;AACJ,cAAI,MAAM,YAAY,KAAK;AACvB,qBAAS,cAAc,OAAO,SAAS,KAAK,OAAO,KAAK,SAAS,IAAI,IAAI;AAC7E;AAAA,QACJ;AAAA,MACJ,WACS,KAAK,MAAM,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,OAAO,QAAQ,KAAK,SAAS,OAAO,MAAM;AACpG,qBAAa;AACb,sBAAc,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,OAAO,OAAO,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI;AAAA,MAChG;AACA,UAAI,CAAC,YAAY,OAAO,QAAQ,KAAK,SAAS,OAAO,OAAO,KAAK,OAC7D,OAAO,QAAQ,KAAK,QAAQ,OAAO,OAAO,KAAK;AAC/C,iBAAS,aAAa;AAAA,IAC9B;AAAA,EACJ;AACA,MAAI,CAAC,WAAW,YAAY;AACxB,cAAU;AACV,oBAAgB;AAChB,gBAAY;AAAA,EAChB;AACA,MAAI,WAAW,QAAQ,YAAY;AAC/B,WAAO,iBAAiB,SAAS,aAAa;AAClD,MAAI,CAAC,WAAY,aAAa,QAAQ,YAAY;AAC9C,WAAO,EAAE,MAAM,OAAO;AAC1B,SAAO,iBAAiB,SAAS,aAAa;AAClD;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACpC,MAAI,MAAM,KAAK,UAAU;AACzB,MAAI,QAAQ,SAAS,YAAY;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,OAAO,MAAM,IAAI,CAAC;AACxB,UAAM,SAAS,MAAM,CAAC;AACtB,QAAI,OAAO,WAAW,OAAO,CAAC;AAC9B,QAAI,KAAK,OAAO,KAAK;AACjB;AACJ,QAAI,OAAO,QAAQ,IAAI;AACnB,aAAO,EAAE,MAAM,QAAQ,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK,SAAS,IAAI,IAAI,GAAG;AAAA,EACzF;AACA,SAAO,EAAE,MAAM,QAAQ,EAAE;AAC7B;AACA,SAAS,OAAO,QAAQ,MAAM;AAC1B,SAAO,OAAO,QAAQ,KAAK,OAAO,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAC/D,OAAO,OAAO,KAAK,MAAM,KAAK,OAAO,OAAO,KAAK,SAAS;AAClE;AACA,SAAS,aAAa,KAAK,QAAQ;AAC/B,MAAI,SAAS,IAAI;AACjB,MAAI,UAAU,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO,OAAO,IAAI,sBAAsB,EAAE;AACrF,WAAO;AACX,SAAO;AACX;AACA,SAAS,eAAe,MAAM,KAAK,QAAQ;AACvC,MAAI,EAAE,MAAM,OAAO,IAAI,iBAAiB,KAAK,MAAM,GAAG,OAAO;AAC7D,MAAI,KAAK,YAAY,KAAK,CAAC,KAAK,YAAY;AACxC,QAAI,OAAO,KAAK,sBAAsB;AACtC,WAAO,KAAK,QAAQ,KAAK,SAAS,OAAO,QAAQ,KAAK,OAAO,KAAK,SAAS,IAAI,IAAI;AAAA,EACvF;AACA,SAAO,KAAK,QAAQ,WAAW,MAAM,QAAQ,IAAI;AACrD;AACA,SAAS,aAAa,MAAM,MAAM,QAAQ,QAAQ;AAO9C,MAAI,eAAe;AACnB,WAAS,MAAM,MAAM,WAAW,WAAS;AACrC,QAAI,OAAO,KAAK;AACZ;AACJ,QAAI,OAAO,KAAK,QAAQ,YAAY,KAAK,IAAI,GAAG;AAChD,QAAI,CAAC;AACD,aAAO;AACX,QAAI,KAAK,IAAI,YAAY,MAAM,KAAK,KAAK,WAAW,KAAK,UAAU,CAAC,KAAK;AAAA,MAEnE,OAAO,KAAK,IAAI,sBAAsB,GAAG,SAAS,KAAK,SAAS;AAClE,UAAI,KAAK,KAAK,WAAW,KAAK,UAAU,CAAC,wBAAwB,KAAK,KAAK,IAAI,QAAQ,GAAG;AAEtF,YAAI,CAAC,YAAY,KAAK,OAAO,OAAO,QAAQ,KAAK,MAAM,OAAO;AAC1D,yBAAe,KAAK;AAAA,iBACf,CAAC,YAAY,KAAK,QAAQ,OAAO,QAAQ,KAAK,SAAS,OAAO;AACnE,yBAAe,KAAK;AACxB,mBAAW;AAAA,MACf;AACA,UAAI,CAAC,KAAK,cAAc,eAAe,KAAK,CAAC,KAAK,KAAK,QAAQ;AAE3D,YAAI,SAAS,KAAK,KAAK,UAAU,OAAO,OAAO,KAAK,MAAM,KAAK,UAAU,IACnE,OAAO,QAAQ,KAAK,OAAO,KAAK,SAAS;AAC/C,eAAO,SAAS,KAAK,YAAY,KAAK;AAAA,MAC1C;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AAAA,EACnB;AACA,SAAO,eAAe,KAAK,eAAe,KAAK,QAAQ,WAAW,MAAM,QAAQ,EAAE;AACtF;AACA,SAAS,iBAAiB,SAAS,QAAQ,KAAK;AAC5C,MAAI,MAAM,QAAQ,WAAW;AAC7B,MAAI,OAAO,IAAI,MAAM,IAAI,QAAQ;AAC7B,aAAS,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,QAAQ,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,YAAU;AACnI,UAAI,QAAQ,QAAQ,WAAW,CAAC;AAChC,UAAI,MAAM,YAAY,GAAG;AACrB,YAAI,QAAQ,MAAM,eAAe;AACjC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAI,OAAO,MAAM,CAAC;AAClB,cAAI,OAAO,QAAQ,IAAI;AACnB,mBAAO,iBAAiB,OAAO,QAAQ,IAAI;AAAA,QACnD;AAAA,MACJ;AACA,WAAK,KAAK,IAAI,KAAK,QAAQ;AACvB;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,YAAY,MAAM,QAAQ;AAC/B,MAAIA,OAAM,KAAK,IAAI,eAAe,MAAM,SAAS;AACjD,MAAI,QAAQ,eAAeA,MAAK,OAAO,MAAM,OAAO,GAAG;AACvD,MAAI;AACA,KAAC,EAAE,MAAM,OAAO,IAAI;AACxB,MAAI,OAAO,KAAK,KAAK,mBAAmB,KAAK,OAAOA,MAC/C,iBAAiB,OAAO,MAAM,OAAO,GAAG;AAC7C,MAAI;AACJ,MAAI,CAAC,OAAO,CAAC,KAAK,IAAI,SAAS,IAAI,YAAY,IAAI,IAAI,aAAa,GAAG,GAAG;AACtE,QAAI,MAAM,KAAK,IAAI,sBAAsB;AACzC,QAAI,CAAC,OAAO,QAAQ,GAAG;AACnB,aAAO;AACX,UAAM,iBAAiB,KAAK,KAAK,QAAQ,GAAG;AAC5C,QAAI,CAAC;AACD,aAAO;AAAA,EACf;AAEA,MAAI,QAAQ;AACR,aAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC;AACzC,UAAI,EAAE;AACF,eAAO;AAAA,EACnB;AACA,QAAM,aAAa,KAAK,MAAM;AAC9B,MAAI,MAAM;AACN,QAAI,SAAS,KAAK,YAAY,GAAG;AAG7B,eAAS,KAAK,IAAI,QAAQ,KAAK,WAAW,MAAM;AAGhD,UAAI,SAAS,KAAK,WAAW,QAAQ;AACjC,YAAI,OAAO,KAAK,WAAW,MAAM,GAAG;AACpC,YAAI,KAAK,YAAY,UAAU,MAAM,KAAK,sBAAsB,GAAG,SAAS,OAAO,QAC/E,IAAI,SAAS,OAAO;AACpB;AAAA,MACR;AAAA,IACJ;AACA,QAAI;AAEJ,QAAI,UAAU,UAAU,KAAK,YAAY,MAAM,OAAO,KAAK,WAAW,SAAS,CAAC,GAAG,YAAY,KAC3F,KAAK,mBAAmB,WAAW,KAAK,sBAAsB,EAAE,OAAO,OAAO;AAC9E;AAGJ,QAAI,QAAQ,KAAK,OAAO,UAAU,KAAK,WAAW,SAAS,KAAK,KAAK,UAAU,YAAY,KACvF,OAAO,MAAM,KAAK,UAAU,sBAAsB,EAAE;AACpD,YAAM,KAAK,MAAM,IAAI,QAAQ;AAAA,aAIxB,UAAU,KAAK,KAAK,YAAY,KAAK,KAAK,WAAW,SAAS,CAAC,EAAE,YAAY;AAClF,YAAM,aAAa,MAAM,MAAM,QAAQ,MAAM;AAAA,EACrD;AACA,MAAI,OAAO;AACP,UAAM,eAAe,MAAM,KAAK,MAAM;AAC1C,MAAI,OAAO,KAAK,QAAQ,YAAY,KAAK,IAAI;AAC7C,SAAO,EAAE,KAAK,QAAQ,OAAO,KAAK,aAAa,KAAK,SAAS,GAAG;AACpE;AACA,SAAS,QAAQ,MAAM;AACnB,SAAO,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK;AACtD;AACA,SAAS,WAAW,QAAQ,MAAM;AAC9B,MAAI,QAAQ,OAAO,eAAe;AAClC,MAAI,MAAM,QAAQ;AACd,QAAI,QAAQ,MAAM,OAAO,IAAI,IAAI,MAAM,SAAS,CAAC;AACjD,QAAI,QAAQ,KAAK;AACb,aAAO;AAAA,EACf;AACA,SAAO,MAAM,UAAU,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO,sBAAsB;AACrF;AACA,IAAM,OAAO;AAGb,SAAS,YAAY,MAAM,KAAK,MAAM;AAClC,MAAI,EAAE,MAAM,QAAQ,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC;AAC3E,MAAI,oBAAoB,UAAU;AAClC,MAAI,KAAK,YAAY,GAAG;AAGpB,QAAI,sBAAsB,KAAK,KAAK,KAAK,SAAS,MAAM,OAAO,IAAI,CAAC,SAAS,UAAU,KAAK,UAAU,UAAU;AAC5G,UAAI,OAAO,WAAW,UAAU,MAAM,QAAQ,MAAM,GAAG,IAAI;AAI3D,UAAI,SAAS,UAAU,KAAK,KAAK,KAAK,UAAU,SAAS,CAAC,CAAC,KAAK,SAAS,KAAK,UAAU,QAAQ;AAC5F,YAAI,aAAa,WAAW,UAAU,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,EAAE;AACvE,YAAI,WAAW,OAAO,KAAK,KAAK;AAC5B,cAAI,YAAY,WAAW,UAAU,MAAM,QAAQ,SAAS,CAAC,GAAG,EAAE;AAClE,cAAI,UAAU,OAAO,KAAK;AACtB,mBAAO,SAAS,WAAW,UAAU,OAAO,WAAW,IAAI;AAAA,QACnE;AAAA,MACJ;AACA,aAAO;AAAA,IACX,OACK;AACD,UAAI,OAAO,QAAQ,KAAK,QAAQ,WAAW,OAAO,IAAI,IAAI;AAC1D,UAAI,OAAO,KAAK,CAAC,QAAQ;AACrB;AACA,mBAAW;AAAA,MACf,WACS,QAAQ,KAAK,UAAU,KAAK,UAAU,QAAQ;AACnD;AACA,mBAAW;AAAA,MACf,WACS,OAAO,GAAG;AACf;AAAA,MACJ,OACK;AACD;AAAA,MACJ;AACA,aAAO,SAAS,WAAW,UAAU,MAAM,MAAM,EAAE,GAAG,QAAQ,GAAG,WAAW,CAAC;AAAA,IACjF;AAAA,EACJ;AACA,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,OAAO,QAAQ,EAAE;AAEnD,MAAI,CAAC,KAAK,OAAO,eAAe;AAC5B,QAAI,QAAQ,QAAQ,WAAW,OAAO,KAAK,UAAU,SAAS,IAAI,IAAI;AAClE,UAAI,SAAS,KAAK,WAAW,SAAS,CAAC;AACvC,UAAI,OAAO,YAAY;AACnB,eAAO,SAAS,OAAO,sBAAsB,GAAG,KAAK;AAAA,IAC7D;AACA,QAAI,QAAQ,QAAQ,SAAS,SAAS,IAAI,GAAG;AACzC,UAAI,QAAQ,KAAK,WAAW,MAAM;AAClC,UAAI,MAAM,YAAY;AAClB,eAAO,SAAS,MAAM,sBAAsB,GAAG,IAAI;AAAA,IAC3D;AACA,WAAO,SAAS,KAAK,sBAAsB,GAAG,QAAQ,CAAC;AAAA,EAC3D;AAEA,MAAI,QAAQ,QAAQ,WAAW,OAAO,KAAK,UAAU,SAAS,IAAI,IAAI;AAClE,QAAI,SAAS,KAAK,WAAW,SAAS,CAAC;AACvC,QAAI,SAAS,OAAO,YAAY,IAAI,UAAU,QAAQ,SAAS,MAAM,KAAK,oBAAoB,IAAI,EAAE,IAG9F,OAAO,YAAY,MAAM,OAAO,YAAY,QAAQ,CAAC,OAAO,eAAe,SAAS;AAC1F,QAAI;AACA,aAAO,SAAS,WAAW,QAAQ,CAAC,GAAG,KAAK;AAAA,EACpD;AACA,MAAI,QAAQ,QAAQ,SAAS,SAAS,IAAI,GAAG;AACzC,QAAI,QAAQ,KAAK,WAAW,MAAM;AAClC,WAAO,MAAM,cAAc,MAAM,WAAW;AACxC,cAAQ,MAAM;AAClB,QAAI,SAAS,CAAC,QAAQ,OAAO,MAAM,YAAY,IAAI,UAAU,OAAO,GAAI,oBAAoB,IAAI,CAAE,IAC5F,MAAM,YAAY,IAAI,QAAQ;AACpC,QAAI;AACA,aAAO,SAAS,WAAW,QAAQ,EAAE,GAAG,IAAI;AAAA,EACpD;AAEA,SAAO,SAAS,WAAW,KAAK,YAAY,IAAI,UAAU,IAAI,IAAI,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC7F;AACA,SAAS,SAAS,MAAM,MAAM;AAC1B,MAAI,KAAK,SAAS;AACd,WAAO;AACX,MAAI,IAAI,OAAO,KAAK,OAAO,KAAK;AAChC,SAAO,EAAE,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,MAAM,GAAG,OAAO,EAAE;AACnE;AACA,SAAS,SAAS,MAAM,KAAK;AACzB,MAAI,KAAK,UAAU;AACf,WAAO;AACX,MAAI,IAAI,MAAM,KAAK,MAAM,KAAK;AAC9B,SAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM;AACnE;AACA,SAAS,iBAAiB,MAAM,OAAO,GAAG;AACtC,MAAI,YAAY,KAAK,OAAO,SAAS,KAAK,KAAK;AAC/C,MAAI,aAAa;AACb,SAAK,YAAY,KAAK;AAC1B,MAAI,UAAU,KAAK;AACf,SAAK,MAAM;AACf,MAAI;AACA,WAAO,EAAE;AAAA,EACb,UACA;AACI,QAAI,aAAa;AACb,WAAK,YAAY,SAAS;AAC9B,QAAI,UAAU,KAAK,OAAO;AACtB,aAAO,MAAM;AAAA,EACrB;AACJ;AAGA,SAAS,uBAAuB,MAAM,OAAO,KAAK;AAC9C,MAAI,MAAM,MAAM;AAChB,MAAI,OAAO,OAAO,OAAO,IAAI,QAAQ,IAAI;AACzC,SAAO,iBAAiB,MAAM,OAAO,MAAM;AACvC,QAAI,EAAE,MAAM,IAAI,IAAI,KAAK,QAAQ,WAAW,KAAK,KAAK,OAAO,OAAO,KAAK,CAAC;AAC1E,eAAS;AACL,UAAI,UAAU,KAAK,QAAQ,YAAY,KAAK,IAAI;AAChD,UAAI,CAAC;AACD;AACJ,UAAI,QAAQ,KAAK,SAAS;AACtB,cAAM,QAAQ,cAAc,QAAQ;AACpC;AAAA,MACJ;AACA,YAAM,QAAQ,IAAI;AAAA,IACtB;AACA,QAAI,SAAS,YAAY,MAAM,KAAK,KAAK,CAAC;AAC1C,aAAS,QAAQ,IAAI,YAAY,OAAO,QAAQ,MAAM,aAAa;AAC/D,UAAI;AACJ,UAAI,MAAM,YAAY;AAClB,gBAAQ,MAAM,eAAe;AAAA,eACxB,MAAM,YAAY;AACvB,gBAAQ,UAAU,OAAO,GAAG,MAAM,UAAU,MAAM,EAAE,eAAe;AAAA;AAEnE;AACJ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAI,MAAM,MAAM,CAAC;AACjB,YAAI,IAAI,SAAS,IAAI,MAAM,MACtB,OAAO,OAAO,OAAO,MAAM,IAAI,OAAO,IAAI,SAAS,OAAO,OAAO,IAC5D,IAAI,SAAS,OAAO,UAAU,OAAO,SAAS,IAAI,OAAO;AAC/D,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,IAAM,WAAW;AACjB,SAAS,yBAAyB,MAAM,OAAO,KAAK;AAChD,MAAI,EAAE,MAAM,IAAI,MAAM;AACtB,MAAI,CAAC,MAAM,OAAO;AACd,WAAO;AACX,MAAI,SAAS,MAAM,cAAc,UAAU,CAAC,QAAQ,QAAQ,UAAU,MAAM,OAAO,QAAQ;AAC3F,MAAI,MAAM,KAAK,aAAa;AAC5B,MAAI,CAAC;AACD,WAAO,MAAM,OAAO,MAAM,MAAM,KAAK,MAAM,OAAO,MAAM,IAAI;AAGhE,MAAI,CAAC,SAAS,KAAK,MAAM,OAAO,WAAW,KAAK,CAAC,IAAI;AACjD,WAAO,OAAO,UAAU,OAAO,aAAa,UAAU;AAC1D,SAAO,iBAAiB,MAAM,OAAO,MAAM;AAMvC,QAAI,EAAE,WAAW,SAAS,aAAa,QAAQ,YAAY,aAAa,IAAI,KAAK,kBAAkB;AACnG,QAAI,eAAe,IAAI;AAEvB,QAAI,OAAO,QAAQ,KAAK,WAAW;AACnC,QAAI,YAAY,MAAM,QAAQ,KAAK,QAAQ,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK;AAC9E,QAAI,EAAE,WAAW,SAAS,aAAa,OAAO,IAAI,KAAK,kBAAkB;AACzE,QAAI,SAAS,WAAW,CAAC,UAAU,SAAS,QAAQ,YAAY,IAAI,UAAU,QAAQ,UAAU,KAC3F,WAAW,WAAW,UAAU;AAErC,QAAI;AACA,UAAI,SAAS,YAAY,YAAY;AACrC,UAAI,YAAY,WAAW,cAAc,UAAU,iBAAiB,IAAI;AACpE,YAAI,OAAO,SAAS,MAAM;AAAA,IAClC,SACO,GAAG;AAAA,IAAE;AACZ,QAAI,gBAAgB;AAChB,UAAI,iBAAiB;AACzB,WAAO;AAAA,EACX,CAAC;AACL;AACA,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,SAAS,eAAe,MAAM,OAAO,KAAK;AACtC,MAAI,eAAe,SAAS,aAAa;AACrC,WAAO;AACX,gBAAc;AACd,cAAY;AACZ,SAAO,eAAe,OAAO,QAAQ,OAAO,SACtC,uBAAuB,MAAM,OAAO,GAAG,IACvC,yBAAyB,MAAM,OAAO,GAAG;AACnD;AAcA,IAAM,YAAY;AAAlB,IAAqB,cAAc;AAAnC,IAAsC,gBAAgB;AAAtD,IAAyD,aAAa;AAGtE,IAAM,WAAN,MAAe;AAAA,EACX,YAAY,QAAQ,UAAU,KAG9B,YAAY;AACR,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,QAAQ;AAGb,QAAI,aAAa;AAAA,EACrB;AAAA;AAAA;AAAA,EAGA,cAAc,QAAQ;AAAE,WAAO;AAAA,EAAO;AAAA,EACtC,YAAY,MAAM;AAAE,WAAO;AAAA,EAAO;AAAA,EAClC,YAAY,MAAM,WAAW,WAAW;AAAE,WAAO;AAAA,EAAO;AAAA,EACxD,YAAY,UAAU;AAAE,WAAO;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA,EAItC,YAAY;AAAE,WAAO;AAAA,EAAM;AAAA;AAAA;AAAA,EAG3B,UAAU,OAAO;AAAE,WAAO;AAAA,EAAO;AAAA;AAAA,EAEjC,IAAI,OAAO;AACP,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ;AACtC,cAAQ,KAAK,SAAS,CAAC,EAAE;AAC7B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,IAAI,SAAS;AAAE,WAAO;AAAA,EAAG;AAAA,EACzB,UAAU;AACN,SAAK,SAAS;AACd,QAAI,KAAK,IAAI,cAAc;AACvB,WAAK,IAAI,aAAa;AAC1B,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ;AACtC,WAAK,SAAS,CAAC,EAAE,QAAQ;AAAA,EACjC;AAAA,EACA,eAAe,OAAO;AAClB,aAAS,IAAI,GAAG,MAAM,KAAK,cAAa,KAAK;AACzC,UAAI,MAAM,KAAK,SAAS,CAAC;AACzB,UAAI,OAAO;AACP,eAAO;AACX,aAAO,IAAI;AAAA,IACf;AAAA,EACJ;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,OAAO,eAAe,IAAI;AAAA,EAC1C;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK,SAAS,KAAK,OAAO,eAAe,IAAI,IAAI,KAAK,SAAS;AAAA,EAC1E;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,YAAY,KAAK;AAAA,EACjC;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,aAAa,KAAK,OAAO,IAAI,KAAK;AAAA,EAClD;AAAA,EACA,gBAAgB,KAAK,QAAQ,MAAM;AAG/B,QAAI,KAAK,cAAc,KAAK,WAAW,SAAS,IAAI,YAAY,IAAI,MAAM,IAAI,UAAU,GAAG;AACvF,UAAI,OAAO,GAAG;AACV,YAAI,WAAW;AACf,YAAI,OAAO,KAAK,YAAY;AACxB,sBAAY,IAAI,WAAW,SAAS,CAAC;AAAA,QACzC,OACK;AACD,iBAAO,IAAI,cAAc,KAAK;AAC1B,kBAAM,IAAI;AACd,sBAAY,IAAI;AAAA,QACpB;AACA,eAAO,aAAa,GAAG,OAAO,UAAU,eAAe,KAAK,UAAU;AAClE,sBAAY,UAAU;AAC1B,eAAO,YAAY,KAAK,eAAe,IAAI,IAAI,KAAK,OAAO,KAAK;AAAA,MACpE,OACK;AACD,YAAI,UAAU;AACd,YAAI,OAAO,KAAK,YAAY;AACxB,qBAAW,IAAI,WAAW,MAAM;AAAA,QACpC,OACK;AACD,iBAAO,IAAI,cAAc,KAAK;AAC1B,kBAAM,IAAI;AACd,qBAAW,IAAI;AAAA,QACnB;AACA,eAAO,YAAY,GAAG,OAAO,SAAS,eAAe,KAAK,UAAU;AAChE,qBAAW,SAAS;AACxB,eAAO,WAAW,KAAK,eAAe,IAAI,IAAI,KAAK;AAAA,MACvD;AAAA,IACJ;AAIA,QAAI;AACJ,QAAI,OAAO,KAAK,OAAO,KAAK,YAAY;AACpC,cAAQ,SAAS,SAAS,KAAK,UAAU;AAAA,IAC7C,WACS,KAAK,cAAc,KAAK,cAAc,KAAK,OAAO,KAAK,IAAI,SAAS,KAAK,UAAU,GAAG;AAC3F,cAAQ,IAAI,wBAAwB,KAAK,UAAU,IAAI;AAAA,IAC3D,WACS,KAAK,IAAI,YAAY;AAC1B,UAAI,UAAU;AACV,iBAAS,SAAS,OAAM,SAAS,OAAO,YAAY;AAChD,cAAI,UAAU,KAAK,KAAK;AACpB,oBAAQ;AACR;AAAA,UACJ;AACA,cAAI,OAAO;AACP;AAAA,QACR;AACJ,UAAI,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC1C,iBAAS,SAAS,OAAM,SAAS,OAAO,YAAY;AAChD,cAAI,UAAU,KAAK,KAAK;AACpB,oBAAQ;AACR;AAAA,UACJ;AACA,cAAI,OAAO;AACP;AAAA,QACR;AAAA,IACR;AACA,YAAQ,SAAS,OAAO,OAAO,IAAI,SAAS,KAAK,WAAW,KAAK;AAAA,EACrE;AAAA,EACA,YAAY,KAAK,YAAY,OAAO;AAChC,aAAS,QAAQ,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI,YAAY;AACzD,UAAI,OAAO,KAAK,QAAQ,GAAG,GAAG;AAC9B,UAAI,SAAS,CAAC,aAAa,KAAK,OAAO;AAEnC,YAAI,UAAU,UAAU,KAAK,YACzB,EAAE,QAAQ,YAAY,IAAI,QAAQ,SAAS,IAAI,YAAY,IAAI,MAAM,IAAI,UAAU,IAAI,WAAW;AAClG,kBAAQ;AAAA;AAER,iBAAO;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,IAAI;AACf,aAAS,MAAM,MAAM,KAAK,MAAM,IAAI;AAChC,UAAI,OAAO;AACP,eAAO;AAAA,EACnB;AAAA,EACA,WAAW,KAAK,QAAQ,MAAM;AAC1B,aAAS,OAAO,KAAK,MAAM,OAAO,KAAK,YAAY;AAC/C,UAAI,OAAO,KAAK,QAAQ,IAAI;AAC5B,UAAI;AACA,eAAO,KAAK,gBAAgB,KAAK,QAAQ,IAAI;AAAA,IACrD;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,OAAO,KAAK;AACR,aAAS,IAAI,GAAG,SAAS,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AACvD,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,SAAS,MAAM;AACnD,UAAI,UAAU,OAAO,OAAO,QAAQ;AAChC,eAAO,CAAC,MAAM,UAAU,MAAM,SAAS,QAAQ;AAC3C,mBAASC,KAAI,GAAGA,KAAI,MAAM,SAAS,QAAQA,MAAK;AAC5C,gBAAI,QAAQ,MAAM,SAASA,EAAC;AAC5B,gBAAI,MAAM,MAAM;AACZ,sBAAQ;AACR;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AACA,UAAI,MAAM;AACN,eAAO,MAAM,OAAO,MAAM,SAAS,MAAM,MAAM;AACnD,eAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,WAAW,KAAK,MAAM;AAClB,QAAI,CAAC,KAAK;AACN,aAAO,EAAE,MAAM,KAAK,KAAK,QAAQ,GAAG,MAAM,MAAM,EAAE;AAEtD,QAAI,IAAI,GAAG,SAAS;AACpB,aAAS,SAAS,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAChD,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,SAAS,MAAM;AACnD,UAAI,MAAM,OAAO,iBAAiB,sBAAsB;AACpD,iBAAS,MAAM;AACf;AAAA,MACJ;AACA,eAAS;AAAA,IACb;AAEA,QAAI;AACA,aAAO,KAAK,SAAS,CAAC,EAAE,WAAW,SAAS,KAAK,SAAS,CAAC,EAAE,QAAQ,IAAI;AAE7E,aAAS,MAAM,KAAK,EAAE,OAAO,KAAK,SAAS,IAAI,CAAC,GAAG,QAAQ,gBAAgB,kBAAkB,KAAK,QAAQ,GAAG,KAAK;AAAA,IAAE;AAEpH,QAAI,QAAQ,GAAG;AACX,UAAI,MAAM,QAAQ;AAClB,eAAQ,KAAK,QAAQ,OAAO;AACxB,eAAO,IAAI,KAAK,SAAS,IAAI,CAAC,IAAI;AAClC,YAAI,CAAC,QAAQ,KAAK,IAAI,cAAc,KAAK;AACrC;AAAA,MACR;AACA,UAAI,QAAQ,QAAQ,SAAS,CAAC,KAAK,UAAU,CAAC,KAAK;AAC/C,eAAO,KAAK,WAAW,KAAK,MAAM,IAAI;AAC1C,aAAO,EAAE,MAAM,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,GAAG,IAAI,IAAI,EAAE;AAAA,IAC9E,OACK;AACD,UAAI,MAAM,QAAQ;AAClB,eAAQ,KAAK,QAAQ,OAAO;AACxB,eAAO,IAAI,KAAK,SAAS,SAAS,KAAK,SAAS,CAAC,IAAI;AACrD,YAAI,CAAC,QAAQ,KAAK,IAAI,cAAc,KAAK;AACrC;AAAA,MACR;AACA,UAAI,QAAQ,SAAS,CAAC,KAAK,UAAU,CAAC,KAAK;AACvC,eAAO,KAAK,WAAW,GAAG,IAAI;AAClC,aAAO,EAAE,MAAM,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,GAAG,IAAI,KAAK,WAAW,WAAW,OAAO;AAAA,IAC1G;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,WAAW,MAAM,IAAI,OAAO,GAAG;AAC3B,QAAI,KAAK,SAAS,UAAU;AACxB,aAAO,EAAE,MAAM,KAAK,YAAY,MAAM,IAAI,YAAY,GAAG,UAAU,KAAK,WAAW,WAAW,OAAO;AACzG,QAAI,aAAa,IAAI,WAAW;AAChC,aAAS,SAAS,MAAM,IAAI,KAAI,KAAK;AACjC,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,SAAS,MAAM;AACnD,UAAI,cAAc,MAAM,QAAQ,KAAK;AACjC,YAAI,YAAY,SAAS,MAAM;AAE/B,YAAI,QAAQ,aAAa,MAAM,MAAM,MAAM,UAAU,MAAM,QACvD,MAAM,cAAc,KAAK,WAAW,SAAS,MAAM,UAAU;AAC7D,iBAAO,MAAM,WAAW,MAAM,IAAI,SAAS;AAC/C,eAAO;AACP,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,cAAI,OAAO,KAAK,SAAS,IAAI,CAAC;AAC9B,cAAI,KAAK,QAAQ,KAAK,IAAI,cAAc,KAAK,cAAc,CAAC,KAAK,aAAa,CAAC,GAAG;AAC9E,yBAAa,SAAS,KAAK,GAAG,IAAI;AAClC;AAAA,UACJ;AACA,kBAAQ,KAAK;AAAA,QACjB;AACA,YAAI,cAAc;AACd,uBAAa;AAAA,MACrB;AACA,UAAI,aAAa,OAAO,MAAM,MAAM,KAAK,KAAK,SAAS,SAAS,IAAI;AAChE,aAAK;AACL,iBAAS,IAAI,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC/C,cAAI,OAAO,KAAK,SAAS,CAAC;AAC1B,cAAI,KAAK,QAAQ,KAAK,IAAI,cAAc,KAAK,cAAc,CAAC,KAAK,aAAa,EAAE,GAAG;AAC/E,uBAAW,SAAS,KAAK,GAAG;AAC5B;AAAA,UACJ;AACA,gBAAM,KAAK;AAAA,QACf;AACA,YAAI,YAAY;AACZ,qBAAW,KAAK,WAAW,WAAW;AAC1C;AAAA,MACJ;AACA,eAAS;AAAA,IACb;AACA,WAAO,EAAE,MAAM,KAAK,YAAY,MAAM,IAAI,YAAY,SAAS;AAAA,EACnE;AAAA,EACA,aAAa,MAAM;AACf,QAAI,KAAK,UAAU,CAAC,KAAK,cAAc,CAAC,KAAK,SAAS;AAClD,aAAO;AACX,QAAI,QAAQ,KAAK,SAAS,OAAO,IAAI,IAAI,KAAK,SAAS,SAAS,CAAC;AACjE,WAAO,MAAM,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,EACrD;AAAA,EACA,YAAY,KAAK;AACb,QAAI,EAAE,MAAM,OAAO,IAAI,KAAK,WAAW,KAAK,CAAC;AAC7C,QAAI,KAAK,YAAY,KAAK,UAAU,KAAK,WAAW;AAChD,YAAM,IAAI,WAAW,uBAAuB,GAAG;AACnD,WAAO,KAAK,WAAW,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,QAAQ,MAAM,MAAM,QAAQ,OAAO;AAE5C,QAAI,OAAO,KAAK,IAAI,QAAQ,IAAI,GAAG,KAAK,KAAK,IAAI,QAAQ,IAAI;AAC7D,aAAS,IAAI,GAAG,SAAS,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AACvD,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,SAAS,MAAM;AACnD,UAAI,OAAO,UAAU,KAAK;AACtB,eAAO,MAAM,aAAa,SAAS,SAAS,MAAM,QAAQ,OAAO,SAAS,MAAM,QAAQ,MAAM,KAAK;AACvG,eAAS;AAAA,IACb;AACA,QAAI,YAAY,KAAK,WAAW,QAAQ,SAAS,KAAK,CAAC;AACvD,QAAI,UAAU,QAAQ,SAAS,YAAY,KAAK,WAAW,MAAM,OAAO,KAAK,CAAC;AAC9E,QAAI,SAAS,KAAK,KAAK,aAAa;AACpC,QAAI,WAAW,KAAK,kBAAkB;AACtC,QAAI,WAAW;AAKf,SAAK,SAAS,WAAW,UAAU,MAAM;AACrC,UAAI,EAAE,MAAM,OAAO,IAAI;AACvB,UAAI,KAAK,YAAY,GAAG;AACpB,mBAAW,CAAC,EAAE,UAAU,KAAK,UAAU,SAAS,CAAC,KAAK;AAEtD,YAAI,YAAY,UAAU,KAAK,UAAU,QAAQ;AAC7C,mBAAS,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,YAAY;AACvD,gBAAI,QAAQ,KAAK,aAAa;AAC1B,kBAAI,MAAM,YAAY;AAClB,4BAAY,UAAU,EAAE,MAAM,MAAM,YAAY,QAAQ,SAAS,KAAK,IAAI,EAAE;AAChF;AAAA,YACJ;AACA,gBAAI,OAAO,KAAK;AAChB,gBAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK;AAC/B;AAAA,UACR;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,OAAO,KAAK,WAAW,SAAS,CAAC;AACrC,mBAAW,SAAS,KAAK,YAAY,QAAQ,KAAK,mBAAmB;AAAA,MACzE;AAAA,IACJ;AAGA,QAAI,SAAS,SAAS,aAAa,SAAS,aAAa,QAAQ,QAAQ,SAAS,UAAU,YAAY,GAAG;AACvG,UAAI,QAAQ,SAAS,UAAU,WAAW,SAAS,WAAW;AAC9D,UAAI,SAAS,MAAM,mBAAmB;AAClC,gBAAQ;AAAA,IAChB;AACA,QAAI,EAAE,SAAS,YAAY,WACvB,qBAAqB,UAAU,MAAM,UAAU,QAAQ,SAAS,YAAY,SAAS,YAAY,KACjG,qBAAqB,QAAQ,MAAM,QAAQ,QAAQ,SAAS,WAAW,SAAS,WAAW;AAC3F;AAIJ,QAAI,iBAAiB;AACrB,SAAK,OAAO,UAAU,UAAU,SAAS,CAAC,UAAU;AAChD,aAAO,SAAS,UAAU,MAAM,UAAU,MAAM;AAChD,UAAI;AACA,YAAI,UAAU;AACV,iBAAO,OAAO,QAAQ,MAAM,QAAQ,MAAM;AAC9C,yBAAiB;AAAA,MACrB,SACO,GAAG;AAAA,MAOV;AAAA,IACJ;AACA,QAAI,CAAC,gBAAgB;AACjB,UAAI,SAAS,MAAM;AACf,YAAI,MAAM;AACV,oBAAY;AACZ,kBAAU;AAAA,MACd;AACA,UAAI,QAAQ,SAAS,YAAY;AACjC,YAAM,OAAO,QAAQ,MAAM,QAAQ,MAAM;AACzC,YAAM,SAAS,UAAU,MAAM,UAAU,MAAM;AAC/C,aAAO,gBAAgB;AACvB,aAAO,SAAS,KAAK;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,eAAe,UAAU;AACrB,WAAO,CAAC,KAAK,cAAc,SAAS,QAAQ;AAAA,EAChD;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK,cAAc,KAAK,cAAc,KAAK,OAAO,CAAC,KAAK,IAAI,SAAS,KAAK,UAAU;AAAA,EAC/F;AAAA;AAAA;AAAA,EAGA,UAAU,MAAM,IAAI;AAChB,aAAS,SAAS,GAAG,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AACvD,UAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,SAAS,MAAM;AACnD,UAAI,UAAU,MAAM,QAAQ,OAAO,MAAM,SAAS,OAAO,OAAO,KAAK,QAAQ;AACzE,YAAI,cAAc,SAAS,MAAM,QAAQ,YAAY,MAAM,MAAM;AACjE,YAAI,QAAQ,eAAe,MAAM,WAAW;AACxC,eAAK,QAAQ,QAAQ,UAAU,MAAM,MAAM,gBAAgB;AAC3D,cAAI,QAAQ,eAAe,MAAM,cAC5B,MAAM,eAAe,MAAM,IAAI,cAAc,KAAK;AACnD,kBAAM,QAAQ;AAAA;AAEd,kBAAM,UAAU,OAAO,aAAa,KAAK,WAAW;AACxD;AAAA,QACJ,OACK;AACD,gBAAM,QAAQ,MAAM,OAAO,MAAM,cAAc,MAAM,IAAI,cAAc,KAAK,cAAc,CAAC,MAAM,SAAS,SACpG,gBAAgB;AAAA,QAC1B;AAAA,MACJ;AACA,eAAS;AAAA,IACb;AACA,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,mBAAmB;AACf,QAAI,QAAQ;AACZ,aAAS,OAAO,KAAK,QAAQ,MAAM,OAAO,KAAK,QAAQ,SAAS;AAC5D,UAAI,QAAQ,SAAS,IAAI,gBAAgB;AACzC,UAAI,KAAK,QAAQ;AACb,aAAK,QAAQ;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,IAAI,UAAU;AAAE,WAAO;AAAA,EAAO;AAAA,EAC9B,IAAI,kBAAkB;AAAE,WAAO;AAAA,EAAO;AAAA,EACtC,IAAI,qBAAqB;AAAE,WAAO;AAAA,EAAO;AAAA,EACzC,OAAO,MAAM;AAAE,WAAO;AAAA,EAAO;AACjC;AAGA,IAAM,iBAAN,cAA6B,SAAS;AAAA,EAClC,YAAY,QAAQ,QAAQ,MAAM,KAAK;AACnC,QAAI,MAAM,MAAM,OAAO,KAAK;AAC5B,QAAI,OAAO,OAAO;AACd,YAAM,IAAI,MAAM,MAAM;AAClB,YAAI,CAAC;AACD,iBAAO;AACX,YAAI,KAAK;AACL,iBAAO,KAAK,OAAO,eAAe,IAAI;AAAA,MAC9C,CAAC;AACL,QAAI,CAAC,OAAO,KAAK,KAAK,KAAK;AACvB,UAAI,IAAI,YAAY,GAAG;AACnB,YAAI,OAAO,SAAS,cAAc,MAAM;AACxC,aAAK,YAAY,GAAG;AACpB,cAAM;AAAA,MACV;AACA,UAAI,kBAAkB;AACtB,UAAI,UAAU,IAAI,oBAAoB;AAAA,IAC1C;AACA,UAAM,QAAQ,CAAC,GAAG,KAAK,IAAI;AAC3B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,WAAO;AAAA,EACX;AAAA,EACA,cAAc,QAAQ;AAClB,WAAO,KAAK,SAAS,aAAa,OAAO,KAAK,GAAG,KAAK,OAAO,IAAI;AAAA,EACrE;AAAA,EACA,YAAY;AAAE,WAAO,EAAE,QAAQ,KAAK;AAAA,EAAG;AAAA,EACvC,UAAU,OAAO;AACb,QAAI,OAAO,KAAK,OAAO,KAAK;AAC5B,WAAO,OAAO,KAAK,KAAK,IAAI;AAAA,EAChC;AAAA,EACA,eAAe,UAAU;AACrB,WAAO,SAAS,QAAQ,eAAe,KAAK,OAAO,KAAK;AAAA,EAC5D;AAAA,EACA,UAAU;AACN,SAAK,OAAO,KAAK,QAAQ,KAAK,GAAG;AACjC,UAAM,QAAQ;AAAA,EAClB;AAAA,EACA,IAAI,UAAU;AAAE,WAAO;AAAA,EAAM;AAAA,EAC7B,IAAI,qBAAqB;AAAE,WAAO,CAAC,CAAC,KAAK,OAAO,KAAK,KAAK;AAAA,EAAa;AAAA,EACvE,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO,KAAK;AAAA,EAAM;AAC/C;AACA,IAAM,sBAAN,cAAkC,SAAS;AAAA,EACvC,YAAY,QAAQ,KAAK,SAAS,MAAM;AACpC,UAAM,QAAQ,CAAC,GAAG,KAAK,IAAI;AAC3B,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EACtC,gBAAgB,KAAK,QAAQ;AACzB,QAAI,OAAO,KAAK;AACZ,aAAO,KAAK,cAAc,SAAS,KAAK,OAAO;AACnD,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA,EACA,WAAW,KAAK;AACZ,WAAO,EAAE,MAAM,KAAK,SAAS,QAAQ,IAAI;AAAA,EAC7C;AAAA,EACA,eAAe,KAAK;AAChB,WAAO,IAAI,SAAS,mBAAmB,IAAI,OAAO,aAAa,IAAI;AAAA,EACvE;AACJ;AAMA,IAAM,eAAN,MAAM,sBAAqB,SAAS;AAAA,EAChC,YAAY,QAAQ,MAAM,KAAK,YAAY,MAAM;AAC7C,UAAM,QAAQ,CAAC,GAAG,KAAK,UAAU;AACjC,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,OAAO,QAAQ,MAAM,QAAQ,MAAM;AACtC,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,IAAI;AAC1C,QAAI,OAAO,UAAU,OAAO,MAAM,MAAM,MAAM;AAC9C,QAAI,CAAC,QAAQ,CAAC,KAAK;AACf,aAAO,cAAc,WAAW,UAAU,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG,MAAM,KAAK,KAAK;AAClG,WAAO,IAAI,cAAa,QAAQ,MAAM,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK,IAAI;AAAA,EACrF;AAAA,EACA,YAAY;AACR,QAAK,KAAK,QAAQ,cAAe,KAAK,KAAK,KAAK,KAAK;AACjD,aAAO;AACX,WAAO,EAAE,MAAM,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,OAAO,gBAAgB,KAAK,WAAW;AAAA,EAChG;AAAA,EACA,YAAY,MAAM;AAAE,WAAO,KAAK,SAAS,cAAc,KAAK,KAAK,GAAG,IAAI;AAAA,EAAG;AAAA,EAC3E,UAAU,MAAM,IAAI;AAChB,UAAM,UAAU,MAAM,EAAE;AAExB,QAAI,KAAK,SAAS,WAAW;AACzB,UAAI,SAAS,KAAK;AAClB,aAAO,CAAC,OAAO;AACX,iBAAS,OAAO;AACpB,UAAI,OAAO,QAAQ,KAAK;AACpB,eAAO,QAAQ,KAAK;AACxB,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,IAAI,MAAM;AAClB,QAAI,OAAO,cAAa,OAAO,KAAK,QAAQ,KAAK,MAAM,MAAM,IAAI;AACjE,QAAI,QAAQ,KAAK,UAAU,OAAO,KAAK;AACvC,QAAI,KAAK;AACL,cAAQ,aAAa,OAAO,IAAI,MAAM,IAAI;AAC9C,QAAI,OAAO;AACP,cAAQ,aAAa,OAAO,GAAG,MAAM,IAAI;AAC7C,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,YAAM,CAAC,EAAE,SAAS;AACtB,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA,EACA,eAAe,UAAU;AACrB,WAAO,KAAK,KAAK,iBAAiB,KAAK,KAAK,eAAe,QAAQ,IAAI,MAAM,eAAe,QAAQ;AAAA,EACxG;AAAA,EACA,UAAU;AACN,QAAI,KAAK,KAAK;AACV,WAAK,KAAK,QAAQ;AACtB,UAAM,QAAQ;AAAA,EAClB;AACJ;AAIA,IAAM,eAAN,MAAM,sBAAqB,SAAS;AAAA,EAChC,YAAY,QAAQ,MAAM,WAAW,WAAW,KAAK,YAAY,SAAS,MAAM,KAAK;AACjF,UAAM,QAAQ,CAAC,GAAG,KAAK,UAAU;AACjC,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,QAAQ,MAAM,WAAW,WAAW,MAAM,KAAK;AACzD,QAAI,SAAS,KAAK,UAAU,KAAK,KAAK,IAAI,GAAG;AAC7C,QAAI,OAAO,UAAU,OAAO,MAAM,MAAM,MAAM;AAG1C,UAAI,CAAC;AACD,eAAO;AACX,UAAI,QAAQ;AACR,eAAO,QAAQ,OAAO,eAAe,OAAO;AAAA,IACpD,GAAG,WAAW,SAAS;AACvB,QAAI,MAAM,QAAQ,KAAK,KAAK,aAAa,QAAQ,KAAK;AACtD,QAAI,KAAK,QAAQ;AACb,UAAI,CAAC;AACD,cAAM,SAAS,eAAe,KAAK,IAAI;AAAA,eAClC,IAAI,YAAY;AACrB,cAAM,IAAI,WAAW,0CAA0C;AAAA,IACvE,WACS,CAAC,KAAK;AACX,UAAIC,QAAO,cAAc,WAAW,UAAU,KAAK,KAAK,KAAK,MAAM,IAAI,GAAG,MAAM,KAAK,KAAK;AAC1F,OAAC,EAAE,KAAK,WAAW,IAAIA;AAAA,IAC3B;AACA,QAAI,CAAC,cAAc,CAAC,KAAK,UAAU,IAAI,YAAY,MAAM;AACrD,UAAI,CAAC,IAAI,aAAa,iBAAiB;AACnC,YAAI,kBAAkB;AAC1B,UAAI,KAAK,KAAK,KAAK;AACf,YAAI,YAAY;AAAA,IACxB;AACA,QAAI,UAAU;AACd,UAAM,eAAe,KAAK,WAAW,IAAI;AACzC,QAAI;AACA,aAAO,UAAU,IAAI,mBAAmB,QAAQ,MAAM,WAAW,WAAW,KAAK,cAAc,MAAM,SAAS,MAAM,MAAM,MAAM,CAAC;AAAA,aAC5H,KAAK;AACV,aAAO,IAAI,aAAa,QAAQ,MAAM,WAAW,WAAW,KAAK,SAAS,IAAI;AAAA;AAE9E,aAAO,IAAI,cAAa,QAAQ,MAAM,WAAW,WAAW,KAAK,cAAc,MAAM,SAAS,MAAM,MAAM,CAAC;AAAA,EACnH;AAAA,EACA,YAAY;AAER,QAAI,KAAK,KAAK,KAAK,KAAK;AACpB,aAAO;AAKX,QAAI,OAAO,EAAE,MAAM,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM;AAC/D,QAAI,KAAK,KAAK,KAAK,cAAc;AAC7B,WAAK,qBAAqB;AAC9B,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa,MAAM,KAAK,KAAK;AAAA,IACtC,WACS,CAAC,KAAK,aAAa;AACxB,WAAK,iBAAiB,KAAK;AAAA,IAC/B,OACK;AAID,eAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,YAAI,QAAQ,KAAK,SAAS,CAAC;AAC3B,YAAI,KAAK,IAAI,SAAS,MAAM,IAAI,UAAU,GAAG;AACzC,eAAK,iBAAiB,MAAM,IAAI;AAChC;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC,KAAK;AACN,aAAK,aAAa,MAAM,SAAS;AAAA,IACzC;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,MAAM,WAAW,WAAW;AACpC,WAAO,KAAK,SAAS,aAAa,KAAK,GAAG,KAAK,IAAI,KAC/C,cAAc,WAAW,KAAK,SAAS,KAAK,UAAU,GAAG,KAAK,SAAS;AAAA,EAC/E;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK,KAAK;AAAA,EAAU;AAAA,EACxC,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK,SAAS,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhD,eAAe,MAAM,KAAK;AACtB,QAAI,SAAS,KAAK,KAAK,eAAe,MAAM;AAC5C,QAAI,cAAc,KAAK,YAAY,KAAK,qBAAqB,MAAM,GAAG,IAAI;AAC1E,QAAI,mBAAmB,eAAe,YAAY,MAAM,KAAK,cAAc;AAC3E,QAAI,qBAAqB,eAAe,YAAY,MAAM;AAC1D,QAAI,UAAU,IAAI,gBAAgB,MAAM,oBAAoB,iBAAiB,MAAM,IAAI;AACvF,aAAS,KAAK,MAAM,KAAK,WAAW,CAAC,QAAQ,GAAG,eAAe;AAC3D,UAAI,OAAO,KAAK;AACZ,gBAAQ,YAAY,OAAO,KAAK,OAAO,QAAQ,IAAI;AAAA,eAC9C,OAAO,KAAK,QAAQ,KAAK,CAAC;AAC/B,gBAAQ,YAAY,KAAK,KAAK,KAAK,aAAa,KAAK,OAAO,KAAK,KAAK,MAAM,CAAC,EAAE,OAAO,QAAQ,IAAI;AAGtG,cAAQ,YAAY,QAAQ,MAAM,GAAG;AAAA,IACzC,GAAG,CAAC,OAAO,WAAW,WAAW,MAAM;AAEnC,cAAQ,YAAY,MAAM,OAAO,QAAQ,IAAI;AAE7C,UAAI;AACJ,UAAI,QAAQ,cAAc,OAAO,WAAW,WAAW,CAAC,EAAG;AAAA,eAClD,sBAAsB,KAAK,MAAM,UAAU,OAAO,OACvD,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM,aACrC,YAAY,QAAQ,mBAAmB,YAAY,IAAI,KAAK,MAC7D,QAAQ,aAAa,OAAO,WAAW,WAAW,WAAW,IAAI,EAAG;AAAA,eAC/D,QAAQ,eAAe,OAAO,WAAW,WAAW,MAAM,GAAG,GAAG,EAAG;AAAA,WACvE;AAED,gBAAQ,QAAQ,OAAO,WAAW,WAAW,MAAM,GAAG;AAAA,MAC1D;AACA,aAAO,MAAM;AAAA,IACjB,CAAC;AAED,YAAQ,YAAY,CAAC,GAAG,QAAQ,IAAI;AACpC,QAAI,KAAK,KAAK;AACV,cAAQ,kBAAkB;AAC9B,YAAQ,YAAY;AAEpB,QAAI,QAAQ,WAAW,KAAK,SAAS,eAAe;AAEhD,UAAI;AACA,aAAK,wBAAwB,MAAM,gBAAgB;AACvD,kBAAY,KAAK,YAAY,KAAK,UAAU,IAAI;AAChD,UAAI;AACA,iBAAS,KAAK,GAAG;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,qBAAqB,MAAM,KAAK;AAG5B,QAAI,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM;AAC9B,QAAI,EAAE,KAAK,MAAM,qBAAqB,kBAAkB,OAAO,OAAO,KAAK,MAAM,KAAK,KAAK,QAAQ;AAC/F,aAAO;AACX,QAAI,WAAW,KAAK,MAAM;AAC1B,QAAI,CAAC,YAAY,CAAC,KAAK,IAAI,SAAS,SAAS,UAAU;AACnD,aAAO;AACX,QAAI,KAAK,KAAK,eAAe;AAIzB,UAAI,OAAO,SAAS;AACpB,UAAI,UAAU,mBAAmB,KAAK,KAAK,SAAS,MAAM,OAAO,KAAK,KAAK,GAAG;AAC9E,aAAO,UAAU,IAAI,OAAO,EAAE,MAAM,UAAU,KAAK,SAAS,KAAK;AAAA,IACrE,OACK;AACD,aAAO,EAAE,MAAM,UAAU,KAAK,IAAI,MAAM,GAAG;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,wBAAwB,MAAM,EAAE,MAAM,KAAK,KAAK,GAAG;AAE/C,QAAI,KAAK,QAAQ,IAAI;AACjB;AAEJ,QAAI,UAAU;AACd,aAAQ,UAAU,QAAQ,YAAY;AAClC,UAAI,QAAQ,cAAc,KAAK;AAC3B;AACJ,aAAO,QAAQ;AACX,gBAAQ,WAAW,YAAY,QAAQ,eAAe;AAC1D,aAAO,QAAQ;AACX,gBAAQ,WAAW,YAAY,QAAQ,WAAW;AACtD,UAAI,QAAQ;AACR,gBAAQ,aAAa;AAAA,IAC7B;AACA,QAAI,OAAO,IAAI,oBAAoB,MAAM,SAAS,MAAM,IAAI;AAC5D,SAAK,MAAM,iBAAiB,KAAK,IAAI;AAErC,SAAK,WAAW,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,QAAQ,MAAM,IAAI;AAAA,EAClF;AAAA;AAAA;AAAA,EAGA,OAAO,MAAM,WAAW,WAAW,MAAM;AACrC,QAAI,KAAK,SAAS,cACd,CAAC,KAAK,WAAW,KAAK,IAAI;AAC1B,aAAO;AACX,SAAK,YAAY,MAAM,WAAW,WAAW,IAAI;AACjD,WAAO;AAAA,EACX;AAAA,EACA,YAAY,MAAM,WAAW,WAAW,MAAM;AAC1C,SAAK,gBAAgB,SAAS;AAC9B,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,QAAI,KAAK;AACL,WAAK,eAAe,MAAM,KAAK,UAAU;AAC7C,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,gBAAgB,WAAW;AACvB,QAAI,cAAc,WAAW,KAAK,SAAS;AACvC;AACJ,QAAI,YAAY,KAAK,QAAQ,YAAY;AACzC,QAAI,SAAS,KAAK;AAClB,SAAK,MAAM,eAAe,KAAK,KAAK,KAAK,SAAS,iBAAiB,KAAK,WAAW,KAAK,MAAM,SAAS,GAAG,iBAAiB,WAAW,KAAK,MAAM,SAAS,CAAC;AAC3J,QAAI,KAAK,OAAO,QAAQ;AACpB,aAAO,aAAa;AACpB,WAAK,IAAI,aAAa;AAAA,IAC1B;AACA,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA,EAEA,aAAa;AACT,QAAI,KAAK,QAAQ,YAAY;AACzB,WAAK,QAAQ,UAAU,IAAI,0BAA0B;AACzD,QAAI,KAAK,cAAc,CAAC,KAAK,KAAK,KAAK,KAAK;AACxC,WAAK,IAAI,YAAY;AAAA,EAC7B;AAAA;AAAA,EAEA,eAAe;AACX,QAAI,KAAK,QAAQ,YAAY,GAAG;AAC5B,WAAK,QAAQ,UAAU,OAAO,0BAA0B;AACxD,UAAI,KAAK,cAAc,CAAC,KAAK,KAAK,KAAK,KAAK;AACxC,aAAK,IAAI,gBAAgB,WAAW;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA,IAAI,UAAU;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAC7C;AAGA,SAAS,YAAYF,MAAK,WAAW,WAAW,KAAK,MAAM;AACvD,iBAAe,KAAK,WAAWA,IAAG;AAClC,MAAI,UAAU,IAAI,aAAa,QAAWA,MAAK,WAAW,WAAW,KAAK,KAAK,KAAK,MAAM,CAAC;AAC3F,MAAI,QAAQ;AACR,YAAQ,eAAe,MAAM,CAAC;AAClC,SAAO;AACX;AACA,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACpC,YAAY,QAAQ,MAAM,WAAW,WAAW,KAAK,SAAS,MAAM;AAChE,UAAM,QAAQ,MAAM,WAAW,WAAW,KAAK,MAAM,SAAS,MAAM,CAAC;AAAA,EACzE;AAAA,EACA,YAAY;AACR,QAAI,OAAO,KAAK,QAAQ;AACxB,WAAO,QAAQ,QAAQ,KAAK,OAAO,CAAC,KAAK;AACrC,aAAO,KAAK;AAChB,WAAO,EAAE,MAAO,QAAQ,KAAM;AAAA,EAClC;AAAA,EACA,OAAO,MAAM,WAAW,WAAW,MAAM;AACrC,QAAI,KAAK,SAAS,cAAe,KAAK,SAAS,aAAa,CAAC,KAAK,SAAS,KACvE,CAAC,KAAK,WAAW,KAAK,IAAI;AAC1B,aAAO;AACX,SAAK,gBAAgB,SAAS;AAC9B,SAAK,KAAK,SAAS,aAAa,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,WAAW;AACjG,WAAK,QAAQ,YAAY,KAAK;AAC9B,UAAI,KAAK,eAAe,KAAK;AACzB,aAAK,cAAc;AAAA,IAC3B;AACA,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACP,QAAI,YAAY,KAAK,OAAO;AAC5B,aAAS,IAAI,KAAK,SAAS,GAAG,IAAI,EAAE;AAChC,UAAI,KAAK;AACL,eAAO;AACf,WAAO;AAAA,EACX;AAAA,EACA,WAAW,KAAK;AACZ,WAAO,EAAE,MAAM,KAAK,SAAS,QAAQ,IAAI;AAAA,EAC7C;AAAA,EACA,gBAAgB,KAAK,QAAQ,MAAM;AAC/B,QAAI,OAAO,KAAK;AACZ,aAAO,KAAK,aAAa,KAAK,IAAI,QAAQ,KAAK,KAAK,KAAK,MAAM;AACnE,WAAO,MAAM,gBAAgB,KAAK,QAAQ,IAAI;AAAA,EAClD;AAAA,EACA,eAAe,UAAU;AACrB,WAAO,SAAS,QAAQ,mBAAmB,SAAS,QAAQ;AAAA,EAChE;AAAA,EACA,MAAM,MAAM,IAAI,MAAM;AAClB,QAAI,OAAO,KAAK,KAAK,IAAI,MAAM,EAAE,GAAG,MAAM,SAAS,eAAe,KAAK,IAAI;AAC3E,WAAO,IAAI,cAAa,KAAK,QAAQ,MAAM,KAAK,WAAW,KAAK,WAAW,KAAK,KAAK,IAAI;AAAA,EAC7F;AAAA,EACA,UAAU,MAAM,IAAI;AAChB,UAAM,UAAU,MAAM,EAAE;AACxB,QAAI,KAAK,OAAO,KAAK,YAAY,QAAQ,KAAK,MAAM,KAAK,QAAQ,UAAU;AACvE,WAAK,QAAQ;AAAA,EACrB;AAAA,EACA,IAAI,UAAU;AAAE,WAAO;AAAA,EAAO;AAAA,EAC9B,OAAO,MAAM;AAAE,WAAO,KAAK,KAAK,QAAQ;AAAA,EAAM;AAClD;AAGA,IAAM,uBAAN,cAAmC,SAAS;AAAA,EACxC,YAAY;AAAE,WAAO,EAAE,QAAQ,KAAK;AAAA,EAAG;AAAA,EACvC,YAAY,UAAU;AAAE,WAAO,KAAK,SAAS,aAAa,KAAK,IAAI,YAAY;AAAA,EAAU;AAAA,EACzF,IAAI,UAAU;AAAE,WAAO;AAAA,EAAM;AAAA,EAC7B,IAAI,kBAAkB;AAAE,WAAO,KAAK,IAAI,YAAY;AAAA,EAAO;AAC/D;AAIA,IAAM,qBAAN,cAAiC,aAAa;AAAA,EAC1C,YAAY,QAAQ,MAAM,WAAW,WAAW,KAAK,YAAY,SAAS,MAAM,MAAM,KAAK;AACvF,UAAM,QAAQ,MAAM,WAAW,WAAW,KAAK,YAAY,SAAS,MAAM,GAAG;AAC7E,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,WAAW,WAAW,MAAM;AACrC,QAAI,KAAK,SAAS;AACd,aAAO;AACX,QAAI,KAAK,KAAK,WAAW,KAAK,KAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY;AAC1E,UAAI,SAAS,KAAK,KAAK,OAAO,MAAM,WAAW,SAAS;AACxD,UAAI;AACA,aAAK,YAAY,MAAM,WAAW,WAAW,IAAI;AACrD,aAAO;AAAA,IACX,WACS,CAAC,KAAK,cAAc,CAAC,KAAK,QAAQ;AACvC,aAAO;AAAA,IACX,OACK;AACD,aAAO,MAAM,OAAO,MAAM,WAAW,WAAW,IAAI;AAAA,IACxD;AAAA,EACJ;AAAA,EACA,aAAa;AACT,SAAK,KAAK,aAAa,KAAK,KAAK,WAAW,IAAI,MAAM,WAAW;AAAA,EACrE;AAAA,EACA,eAAe;AACX,SAAK,KAAK,eAAe,KAAK,KAAK,aAAa,IAAI,MAAM,aAAa;AAAA,EAC3E;AAAA,EACA,aAAa,QAAQ,MAAM,MAAM,OAAO;AACpC,SAAK,KAAK,eAAe,KAAK,KAAK,aAAa,QAAQ,MAAM,KAAK,IAAI,IACjE,MAAM,aAAa,QAAQ,MAAM,MAAM,KAAK;AAAA,EACtD;AAAA,EACA,UAAU;AACN,QAAI,KAAK,KAAK;AACV,WAAK,KAAK,QAAQ;AACtB,UAAM,QAAQ;AAAA,EAClB;AAAA,EACA,UAAU,OAAO;AACb,WAAO,KAAK,KAAK,YAAY,KAAK,KAAK,UAAU,KAAK,IAAI;AAAA,EAC9D;AAAA,EACA,eAAe,UAAU;AACrB,WAAO,KAAK,KAAK,iBAAiB,KAAK,KAAK,eAAe,QAAQ,IAAI,MAAM,eAAe,QAAQ;AAAA,EACxG;AACJ;AAIA,SAAS,YAAY,WAAW,OAAO,MAAM;AACzC,MAAI,MAAM,UAAU,YAAY,UAAU;AAC1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK;AACrC,QAAI,SAAS,cAAc,WAAW;AAClC,aAAO,YAAY,KAAK;AACpB,cAAM,GAAG,GAAG;AACZ,kBAAU;AAAA,MACd;AACA,YAAM,IAAI;AAAA,IACd,OACK;AACD,gBAAU;AACV,gBAAU,aAAa,UAAU,GAAG;AAAA,IACxC;AACA,QAAI,gBAAgB,cAAc;AAC9B,UAAI,MAAM,MAAM,IAAI,kBAAkB,UAAU;AAChD,kBAAY,KAAK,YAAY,KAAK,UAAU,IAAI;AAChD,YAAM,MAAM,IAAI,cAAc,UAAU;AAAA,IAC5C;AAAA,EACJ;AACA,SAAO,KAAK;AACR,UAAM,GAAG,GAAG;AACZ,cAAU;AAAA,EACd;AACA,MAAI,WAAW,KAAK,eAAe;AAC/B,SAAK,cAAc;AAC3B;AACA,IAAM,iBAAiB,SAAU,UAAU;AACvC,MAAI;AACA,SAAK,WAAW;AACxB;AACA,eAAe,YAAY,uBAAO,OAAO,IAAI;AAC7C,IAAM,SAAS,CAAC,IAAI,gBAAc;AAClC,SAAS,iBAAiB,WAAW,MAAM,WAAW;AAClD,MAAI,UAAU,UAAU;AACpB,WAAO;AACX,MAAI,MAAM,YAAY,OAAO,CAAC,IAAI,IAAI,kBAAgB,SAAS,CAAC,GAAG;AACnE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,QAAQ,UAAU,CAAC,EAAE,KAAK;AAC9B,QAAI,CAAC;AACD;AACJ,QAAI,MAAM;AACN,aAAO,KAAK,MAAM,IAAI,eAAe,MAAM,QAAQ,CAAC;AACxD,aAAS,QAAQ,OAAO;AACpB,UAAI,MAAM,MAAM,IAAI;AACpB,UAAI,OAAO;AACP;AACJ,UAAI,aAAa,OAAO,UAAU;AAC9B,eAAO,KAAK,MAAM,IAAI,eAAe,KAAK,WAAW,SAAS,KAAK,CAAC;AACxE,UAAI,QAAQ;AACR,YAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,MAAM;AAAA,eAC5C,QAAQ;AACb,YAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,MAAM;AAAA,eAC5C,QAAQ;AACb,YAAI,IAAI,IAAI;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,UAAU,SAAS,cAAc,aAAa;AAElE,MAAI,gBAAgB,UAAU,eAAe;AACzC,WAAO;AACX,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,QAAI,OAAO,YAAY,CAAC,GAAG,OAAO,aAAa,CAAC;AAChD,QAAI,GAAG;AACH,UAAI;AACJ,UAAI,QAAQ,KAAK,YAAY,KAAK,YAAY,UAAU,aACnD,SAAS,OAAO,eAAe,OAAO,SAAS,YAAY,KAAK,KAAK,UAAU;AAChF,iBAAS;AAAA,MACb,OACK;AACD,iBAAS,SAAS,cAAc,KAAK,QAAQ;AAC7C,eAAO,WAAW;AAClB,eAAO,YAAY,MAAM;AACzB,eAAO,OAAO,CAAC;AACf,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,oBAAgB,QAAQ,QAAQ,OAAO,CAAC,GAAG,IAAI;AAAA,EACnD;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,KAAK,MAAM,KAAK;AACrC,WAAS,QAAQ;AACb,QAAI,QAAQ,WAAW,QAAQ,WAAW,QAAQ,cAAc,EAAE,QAAQ;AACtE,UAAI,gBAAgB,IAAI;AAChC,WAAS,QAAQ;AACb,QAAI,QAAQ,WAAW,QAAQ,WAAW,QAAQ,cAAc,IAAI,IAAI,KAAK,KAAK,IAAI;AAClF,UAAI,aAAa,MAAM,IAAI,IAAI,CAAC;AACxC,MAAI,KAAK,SAAS,IAAI,OAAO;AACzB,QAAI,WAAW,KAAK,QAAQ,KAAK,MAAM,MAAM,GAAG,EAAE,OAAO,OAAO,IAAI,CAAC;AACrE,QAAI,UAAU,IAAI,QAAQ,IAAI,MAAM,MAAM,GAAG,EAAE,OAAO,OAAO,IAAI,CAAC;AAClE,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACjC,UAAI,QAAQ,QAAQ,SAAS,CAAC,CAAC,KAAK;AAChC,YAAI,UAAU,OAAO,SAAS,CAAC,CAAC;AACxC,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,UAAI,SAAS,QAAQ,QAAQ,CAAC,CAAC,KAAK;AAChC,YAAI,UAAU,IAAI,QAAQ,CAAC,CAAC;AACpC,QAAI,IAAI,UAAU,UAAU;AACxB,UAAI,gBAAgB,OAAO;AAAA,EACnC;AACA,MAAI,KAAK,SAAS,IAAI,OAAO;AACzB,QAAI,KAAK,OAAO;AACZ,UAAI,OAAO,iFAAiF;AAC5F,aAAO,IAAI,KAAK,KAAK,KAAK,KAAK;AAC3B,YAAI,MAAM,eAAe,EAAE,CAAC,CAAC;AAAA,IACrC;AACA,QAAI,IAAI;AACJ,UAAI,MAAM,WAAW,IAAI;AAAA,EACjC;AACJ;AACA,SAAS,eAAe,KAAK,MAAM,MAAM;AACrC,SAAO,eAAe,KAAK,KAAK,QAAQ,iBAAiB,MAAM,MAAM,IAAI,YAAY,CAAC,CAAC;AAC3F;AACA,SAAS,cAAc,GAAG,GAAG;AACzB,MAAI,EAAE,UAAU,EAAE;AACd,WAAO;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,QAAI,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE,IAAI;AACvB,aAAO;AACf,SAAO;AACX;AAEA,SAAS,GAAG,KAAK;AACb,MAAI,OAAO,IAAI;AACf,MAAI,WAAW,YAAY,GAAG;AAC9B,SAAO;AACX;AAGA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,KAAK,MAAM,MAAM;AACzB,SAAK,OAAO;AACZ,SAAK,OAAO;AAGZ,SAAK,QAAQ;AAGb,SAAK,QAAQ,CAAC;AAEd,SAAK,UAAU;AACf,SAAK,MAAM;AACX,SAAK,WAAW,SAAS,IAAI,KAAK,SAAS,GAAG;AAAA,EAClD;AAAA;AAAA;AAAA,EAGA,eAAe,OAAO,KAAK;AACvB,QAAI,SAAS;AACT;AACJ,aAAS,IAAI,OAAO,IAAI,KAAK;AACzB,WAAK,IAAI,SAAS,CAAC,EAAE,QAAQ;AACjC,SAAK,IAAI,SAAS,OAAO,OAAO,MAAM,KAAK;AAC3C,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA,EAEA,cAAc;AACV,SAAK,eAAe,KAAK,OAAO,KAAK,IAAI,SAAS,MAAM;AAAA,EAC5D;AAAA;AAAA;AAAA,EAGA,YAAY,OAAO,QAAQ,MAAM;AAC7B,QAAI,OAAO,GAAG,QAAQ,KAAK,MAAM,UAAU;AAC3C,QAAI,UAAU,KAAK,IAAI,OAAO,MAAM,MAAM;AAC1C,WAAO,OAAO,YACT,QAAQ,QAAQ,IAAI,KAAK,MAAM,KAAK,MAAO,OAAO,KAAM,CAAC,GACrD,YAAY,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,EAAE,KAAK,KAAK,aAAa;AACpE;AACJ,WAAO,OAAO,OAAO;AACjB,WAAK,YAAY;AACjB,WAAK,IAAI,QAAQ;AACjB,WAAK,QAAQ,KAAK,MAAM,IAAI;AAC5B,WAAK,MAAM,KAAK,MAAM,IAAI;AAC1B;AAAA,IACJ;AACA,WAAO,QAAQ,MAAM,QAAQ;AACzB,WAAK,MAAM,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAC;AACxC,UAAI,QAAQ;AACZ,eAAS,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,SAAS,MAAM,GAAG,KAAK;AAClF,YAAI,OAAO,KAAK,IAAI,SAAS,CAAC;AAC9B,YAAI,KAAK,YAAY,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,SAAS,KAAK,GAAG,GAAG;AAC5D,kBAAQ;AACR;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,QAAQ,IAAI;AACZ,YAAI,QAAQ,KAAK,OAAO;AACpB,eAAK,UAAU;AACf,eAAK,eAAe,KAAK,OAAO,KAAK;AAAA,QACzC;AACA,aAAK,MAAM,KAAK,IAAI,SAAS,KAAK,KAAK;AAAA,MAC3C,OACK;AACD,YAAI,WAAW,aAAa,OAAO,KAAK,KAAK,MAAM,KAAK,GAAG,QAAQ,IAAI;AACvE,aAAK,IAAI,SAAS,OAAO,KAAK,OAAO,GAAG,QAAQ;AAChD,aAAK,MAAM;AACX,aAAK,UAAU;AAAA,MACnB;AACA,WAAK,QAAQ;AACb;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,cAAc,MAAM,WAAW,WAAW,OAAO;AAC7C,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,KAAK,SAAS,UACtB,aAAa,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,KAAK,GAAG,UAAU,KAAK,OACjF,WAAW,YAAY,MAAM,WAAW,SAAS,GAAG;AACpD,cAAQ,KAAK,IAAI,SAAS,QAAQ,YAAY,KAAK,KAAK;AAAA,IAC5D,OACK;AACD,eAAS,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,QAAQ,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK;AAChF,YAAI,QAAQ,KAAK,IAAI,SAAS,CAAC;AAC/B,YAAI,MAAM,YAAY,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,SAAS,QAAQ,IAAI,KAAK,GAAG;AACpF,kBAAQ;AACR;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ;AACR,aAAO;AACX,SAAK,eAAe,KAAK,OAAO,KAAK;AACrC,SAAK;AACL,WAAO;AAAA,EACX;AAAA,EACA,aAAa,MAAM,WAAW,WAAW,OAAO,MAAM;AAClD,QAAI,QAAQ,KAAK,IAAI,SAAS,KAAK;AACnC,QAAI,MAAM,SAAS,cAAc,MAAM,OAAO,MAAM;AAChD,YAAM,QAAQ;AAClB,QAAI,CAAC,MAAM,OAAO,MAAM,WAAW,WAAW,IAAI;AAC9C,aAAO;AACX,SAAK,eAAe,KAAK,OAAO,KAAK;AACrC,SAAK;AACL,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,SAAS;AACxB,eAAS;AACL,UAAI,SAAS,QAAQ;AACrB,UAAI,CAAC;AACD,eAAO;AACX,UAAI,UAAU,KAAK,IAAI,YAAY;AAC/B,YAAI,OAAO,QAAQ;AACnB,YAAI;AACA,mBAAS,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,SAAS,QAAQ,KAAK;AACxD,gBAAI,KAAK,IAAI,SAAS,CAAC,KAAK;AACxB,qBAAO;AAAA,UACf;AACJ,eAAO;AAAA,MACX;AACA,gBAAU;AAAA,IACd;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,eAAe,MAAM,WAAW,WAAW,MAAM,OAAO,KAAK;AACzD,aAAS,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,SAAS,QAAQ,KAAK;AACxD,UAAI,OAAO,KAAK,IAAI,SAAS,CAAC;AAC9B,UAAI,gBAAgB,cAAc;AAC9B,YAAIG,YAAW,KAAK,SAAS,QAAQ,IAAI,IAAI;AAC7C,YAAIA,aAAY,QAAQA,aAAY;AAChC,iBAAO;AACX,YAAI,UAAU,KAAK,KAAK;AAIxB,YAAI,SAAS,KAAK,SAAS,OAAO,KAC9B,EAAE,KAAK,UAAU,KAAK,QAAQ,KAAK,KAAK,UAAU,KAAK,QAAQ,aAAa,KAAK,QAC7E,KAAK,SAAS,cAAc,cAAc,WAAW,KAAK,SAAS;AAC3E,YAAI,CAAC,UAAU,KAAK,OAAO,MAAM,WAAW,WAAW,IAAI,GAAG;AAC1D,eAAK,eAAe,KAAK,OAAO,CAAC;AACjC,cAAI,KAAK,OAAO;AACZ,iBAAK,UAAU;AACnB,eAAK;AACL,iBAAO;AAAA,QACX,WACS,CAAC,WAAW,UAAU,KAAK,gBAAgB,MAAM,MAAM,WAAW,WAAW,MAAM,GAAG,IAAI;AAC/F,eAAK,eAAe,KAAK,OAAO,CAAC;AACjC,eAAK,IAAI,SAAS,KAAK,KAAK,IAAI;AAChC,cAAI,QAAQ,YAAY;AACpB,oBAAQ,QAAQ;AAChB,oBAAQ,eAAe,MAAM,MAAM,CAAC;AACpC,oBAAQ,QAAQ;AAAA,UACpB;AACA,eAAK,UAAU;AACf,eAAK;AACL,iBAAO;AAAA,QACX;AACA;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,gBAAgB,MAAM,MAAM,WAAW,WAAW,MAAM,KAAK;AACzD,QAAI,KAAK,SAAS,KAAK,UAAU,CAAC,KAAK,SAAS,UAC5C,CAAC,KAAK,KAAK,QAAQ,GAAG,KAAK,OAAO,KAClC,CAAC,cAAc,WAAW,KAAK,SAAS,KAAK,CAAC,UAAU,GAAG,KAAK,SAAS;AACzE,aAAO;AACX,QAAI,UAAU,aAAa,OAAO,KAAK,KAAK,MAAM,WAAW,WAAW,MAAM,GAAG;AACjF,QAAI,QAAQ,YAAY;AACpB,cAAQ,WAAW,KAAK;AACxB,WAAK,WAAW,CAAC;AACjB,eAAS,MAAM,QAAQ;AACnB,WAAG,SAAS;AAAA,IACpB;AACA,SAAK,QAAQ;AACb,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,QAAQ,MAAM,WAAW,WAAW,MAAM,KAAK;AAC3C,QAAI,OAAO,aAAa,OAAO,KAAK,KAAK,MAAM,WAAW,WAAW,MAAM,GAAG;AAC9E,QAAI,KAAK;AACL,WAAK,eAAe,MAAM,MAAM,CAAC;AACrC,SAAK,IAAI,SAAS,OAAO,KAAK,SAAS,GAAG,IAAI;AAC9C,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,YAAY,QAAQ,MAAM,KAAK;AAC3B,QAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,SAAS,SAAS,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI;AACnF,QAAI,QAAQ,KAAK,cAAc,MAAM,MAChC,UAAU,KAAK,UAAU,CAAC,KAAK,OAAO,KAAK,MAAM,aAAa;AAC/D,WAAK;AAAA,IACT,OACK;AACD,UAAI,OAAO,IAAI,eAAe,KAAK,KAAK,QAAQ,MAAM,GAAG;AACzD,WAAK,IAAI,SAAS,OAAO,KAAK,SAAS,GAAG,IAAI;AAC9C,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,oBAAoB;AAChB,QAAI,YAAY,KAAK,IAAI,SAAS,KAAK,QAAQ,CAAC,GAAG,SAAS,KAAK;AACjE,WAAO,qBAAqB,cAAc;AACtC,eAAS;AACT,kBAAY,OAAO,SAAS,OAAO,SAAS,SAAS,CAAC;AAAA,IAC1D;AACA,QAAI,CAAC;AAAA,IACD,EAAE,qBAAqB,iBACvB,MAAM,KAAK,UAAU,KAAK,IAAI,KAC7B,KAAK,KAAK,yBAAyB,MAAM,KAAK,UAAU,KAAK,IAAI,GAAI;AAEtE,WAAK,UAAU,WAAW,aAAa,UAAU,IAAI,mBAAmB;AACpE,aAAK,YAAY,OAAO,MAAM;AAClC,WAAK,YAAY,MAAM,KAAK,GAAG;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,YAAY,UAAU,QAAQ;AAC1B,QAAI,UAAU,KAAK,OAAO,KAAK,QAAQ,OAAO,SAAS,UAAU,OAAO,SAAS,KAAK,KAAK,EAAE,YAAY,QAAQ,GAAG;AAChH,WAAK;AAAA,IACT,OACK;AACD,UAAI,MAAM,SAAS,cAAc,QAAQ;AACzC,UAAI,YAAY,OAAO;AACnB,YAAI,YAAY;AAChB,YAAI,MAAM;AAAA,MACd;AACA,UAAI,YAAY;AACZ,YAAI,YAAY;AACpB,UAAI,OAAO,IAAI,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI;AAC3D,UAAI,UAAU,KAAK;AACf,eAAO,SAAS,KAAK,IAAI;AAAA;AAEzB,eAAO,SAAS,OAAO,KAAK,SAAS,GAAG,IAAI;AAChD,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,SAAS,MAAM;AACX,WAAO,KAAK,SAAS,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAK,SAAS,KAAK,KAAK,UAAU;AAAA,EACtG;AACJ;AAMA,SAAS,SAAS,MAAM,YAAY;AAChC,MAAI,UAAU,YAAY,QAAQ,QAAQ,SAAS;AACnD,MAAI,KAAK,KAAK,YAAY,UAAU,oBAAI,OAAK,UAAU,CAAC;AACxD,QAAO,QAAO,KAAK,GAAG;AAClB,QAAI;AACJ,eAAS;AACL,UAAI,OAAO;AACP,YAAI,OAAO,QAAQ,SAAS,QAAQ,CAAC;AACrC,YAAI,gBAAgB,cAAc;AAC9B,oBAAU;AACV,kBAAQ,KAAK,SAAS;AAAA,QAC1B,OACK;AACD,iBAAO;AACP;AACA;AAAA,QACJ;AAAA,MACJ,WACS,WAAW,YAAY;AAC5B,cAAM;AAAA,MACV,OACK;AAED,gBAAQ,QAAQ,OAAO,SAAS,QAAQ,OAAO;AAC/C,kBAAU,QAAQ;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,OAAO,KAAK;AAChB,QAAI,CAAC;AACD;AACJ,QAAI,QAAQ,KAAK,MAAM,KAAK,CAAC;AACzB;AACJ,MAAE;AACF,YAAQ,IAAI,MAAM,EAAE;AACpB,YAAQ,KAAK,IAAI;AAAA,EACrB;AACA,SAAO,EAAE,OAAO,IAAI,SAAS,SAAS,QAAQ,QAAQ,EAAE;AAC5D;AACA,SAAS,YAAY,GAAG,GAAG;AACvB,SAAO,EAAE,KAAK,OAAO,EAAE,KAAK;AAChC;AAKA,SAAS,SAAS,QAAQ,MAAM,UAAU,QAAQ;AAC9C,MAAI,SAAS,KAAK,OAAO,MAAM,GAAG,SAAS;AAE3C,MAAI,OAAO,UAAU,GAAG;AACpB,aAAS,IAAI,GAAG,IAAI,OAAO,YAAY,KAAK;AACxC,UAAI,QAAQ,OAAO,MAAM,CAAC;AAC1B,aAAO,OAAO,QAAQ,KAAK,SAAS,QAAQ,KAAK,GAAG,CAAC;AACrD,gBAAU,MAAM;AAAA,IACpB;AACA;AAAA,EACJ;AACA,MAAI,YAAY,GAAG,SAAS,CAAC,GAAG,WAAW;AAC3C,WAAS,cAAc,OAAK;AACxB,QAAI,QAAQ;AACZ,WAAO,YAAY,OAAO,UAAU,OAAO,SAAS,EAAE,MAAM,QAAQ;AAChE,UAAI,OAAO,OAAO,WAAW;AAC7B,UAAI,KAAK,QAAQ;AACb,YAAI,CAAC;AACD,mBAAS;AAAA;AAET,WAAC,YAAY,UAAU,CAAC,MAAM,IAAI,KAAK,IAAI;AAAA,MACnD;AAAA,IACJ;AACA,QAAI,QAAQ;AACR,UAAI,SAAS;AACT,gBAAQ,KAAK,WAAW;AACxB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,mBAAS,QAAQ,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ;AAAA,MACpD,OACK;AACD,iBAAS,QAAQ,aAAa,CAAC,CAAC,QAAQ;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI,OAAO;AACX,QAAI,UAAU;AACV,cAAQ;AACR,cAAQ;AACR,iBAAW;AAAA,IACf,WACS,cAAc,OAAO,YAAY;AACtC,cAAQ;AACR,cAAQ,OAAO,MAAM,aAAa;AAAA,IACtC,OACK;AACD;AAAA,IACJ;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,UAAI,OAAO,CAAC,EAAE,MAAM;AAChB,eAAO,OAAO,KAAK,CAAC;AAC5B,WAAO,YAAY,OAAO,UAAU,OAAO,SAAS,EAAE,QAAQ,UAAU,OAAO,SAAS,EAAE,KAAK;AAC3F,aAAO,KAAK,OAAO,WAAW,CAAC;AACnC,QAAI,MAAM,SAAS,MAAM;AACzB,QAAI,MAAM,QAAQ;AACd,UAAI,QAAQ;AACZ,UAAI,YAAY,OAAO,UAAU,OAAO,SAAS,EAAE,OAAO;AACtD,gBAAQ,OAAO,SAAS,EAAE;AAC9B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,YAAI,OAAO,CAAC,EAAE,KAAK;AACf,kBAAQ,OAAO,CAAC,EAAE;AAC1B,UAAI,QAAQ,KAAK;AACb,mBAAW,MAAM,IAAI,QAAQ,MAAM;AACnC,gBAAQ,MAAM,IAAI,GAAG,QAAQ,MAAM;AACnC,cAAM;AACN,gBAAQ;AAAA,MACZ;AAAA,IACJ,OACK;AACD,aAAO,YAAY,OAAO,UAAU,OAAO,SAAS,EAAE,KAAK;AACvD;AAAA,IACR;AACA,QAAI,YAAY,MAAM,YAAY,CAAC,MAAM,SAAS,OAAO,OAAO,OAAK,CAAC,EAAE,MAAM,IAAI,OAAO,MAAM;AAC/F,WAAO,OAAO,WAAW,KAAK,SAAS,QAAQ,KAAK,GAAG,KAAK;AAC5D,aAAS;AAAA,EACb;AACJ;AAGA,SAAS,SAAS,KAAK;AACnB,MAAI,IAAI,YAAY,QAAQ,IAAI,YAAY,MAAM;AAC9C,QAAI,SAAS,IAAI,MAAM;AACvB,QAAI,MAAM,UAAU,SAAS;AAC7B,WAAO,iBAAiB,GAAG,EAAE;AAC7B,QAAI,MAAM,UAAU;AAAA,EACxB;AACJ;AAEA,SAAS,mBAAmB,MAAM,MAAM,MAAM,IAAI;AAC9C,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,cAAc,OAAO,MAAK;AACxD,QAAI,QAAQ,KAAK,MAAM,GAAG,GAAG,aAAa;AAC1C,WAAO,MAAM;AACb,QAAI,CAAC,MAAM;AACP;AACJ,QAAI,MAAM,MAAM;AAChB,WAAO,IAAI,KAAK,YAAY;AACxB,UAAI,OAAO,KAAK,MAAM,GAAG;AACzB,aAAO,KAAK;AACZ,UAAI,CAAC,KAAK;AACN;AACJ,aAAO,KAAK;AAAA,IAChB;AACA,QAAI,OAAO,MAAM;AACb,UAAI,OAAO,MAAM,IAAI,MAAM,KAAK,KAAK,SAAS,YAAY,KAAK,UAAU,KAAK;AAC1E,eAAO,KAAK,KAAK;AACrB,UAAI,QAAQ,aAAa,KAAK,IAAI,YAAY,MAAM,KAAK,aAAa,CAAC,IAAI;AAC3E,UAAI,SAAS,KAAK,QAAQ,KAAK,SAAS,cAAc;AAClD,eAAO,aAAa;AACxB,UAAI,QAAQ,MAAM,IAAI,UAAW,KAAK,KAAK,SAAU,cACjD,IAAI,MAAM,KAAK,YAAY,KAAK,aAAa,KAAK,MAAM,KAAK;AAC7D,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAMA,SAAS,aAAa,OAAO,MAAM,IAAI,MAAM,aAAa;AACtD,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC5C,QAAI,QAAQ,MAAM,CAAC,GAAG,QAAQ,KAAK,MAAM,OAAO,MAAM;AACtD,QAAI,SAAS,MAAM,OAAO,MAAM;AAC5B,aAAO,KAAK,KAAK;AAAA,IACrB,OACK;AACD,UAAI,QAAQ;AACR,eAAO,KAAK,MAAM,MAAM,GAAG,OAAO,OAAO,IAAI,CAAC;AAClD,UAAI,aAAa;AACb,eAAO,KAAK,WAAW;AACvB,sBAAc;AAAA,MAClB;AACA,UAAI,MAAM;AACN,eAAO,KAAK,MAAM,MAAM,KAAK,OAAO,MAAM,MAAM,IAAI,CAAC;AAAA,IAC7D;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB,MAAM,SAAS,MAAM;AAC3C,MAAI,SAAS,KAAK,kBAAkB,GAAGH,OAAM,KAAK,MAAM;AACxD,MAAI,CAAC,OAAO;AACR,WAAO;AACX,MAAI,cAAc,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,WAAW,eAAe,YAAY,QAAQ;AAC5G,MAAI,OAAO,KAAK,QAAQ,WAAW,OAAO,WAAW,OAAO,aAAa,CAAC;AAC1E,MAAI,OAAO;AACP,WAAO;AACX,MAAI,QAAQA,KAAI,QAAQ,IAAI,GAAG,QAAQ;AACvC,MAAI,mBAAmB,MAAM,GAAG;AAC5B,aAAS;AACT,WAAO,eAAe,CAAC,YAAY;AAC/B,oBAAc,YAAY;AAC9B,QAAI,kBAAkB,YAAY;AAClC,QAAI,eAAe,gBAAgB,UAAU,cAAc,aAAa,eAAe,KAAK,YAAY,UACjG,EAAE,gBAAgB,YAAY,SAAS,OAAO,WAAW,OAAO,aAAa,YAAY,GAAG,IAAI;AACnG,UAAI,MAAM,YAAY;AACtB,kBAAY,IAAI,cAAc,QAAQ,MAAM,QAAQA,KAAI,QAAQ,GAAG,CAAC;AAAA,IACxE;AAAA,EACJ,OACK;AACD,QAAI,kBAAkB,KAAK,IAAI,cAAc,YAAY,aAAa,OAAO,aAAa,GAAG;AACzF,UAAI,MAAM,MAAM,MAAM;AACtB,eAAS,IAAI,GAAG,IAAI,OAAO,YAAY,KAAK;AACxC,YAAI,QAAQ,OAAO,WAAW,CAAC;AAC/B,cAAM,KAAK,IAAI,KAAK,KAAK,QAAQ,WAAW,MAAM,gBAAgB,MAAM,aAAa,CAAC,CAAC;AACvF,cAAM,KAAK,IAAI,KAAK,KAAK,QAAQ,WAAW,MAAM,cAAc,MAAM,WAAW,EAAE,CAAC;AAAA,MACxF;AACA,UAAI,MAAM;AACN,eAAO;AACX,OAAC,QAAQ,IAAI,IAAI,OAAO,KAAK,MAAM,UAAU,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;AAC5E,cAAQA,KAAI,QAAQ,IAAI;AAAA,IAC5B,OACK;AACD,eAAS,KAAK,QAAQ,WAAW,OAAO,YAAY,OAAO,cAAc,CAAC;AAAA,IAC9E;AACA,QAAI,SAAS;AACT,aAAO;AAAA,EACf;AACA,MAAI,UAAUA,KAAI,QAAQ,MAAM;AAChC,MAAI,CAAC,WAAW;AACZ,QAAI,OAAO,UAAU,aAAc,KAAK,MAAM,UAAU,OAAO,MAAM,OAAO,CAAC,WAAY,IAAI;AAC7F,gBAAY,iBAAiB,MAAM,SAAS,OAAO,IAAI;AAAA,EAC3D;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,MAAM;AAC/B,SAAO,KAAK,WAAW,KAAK,SAAS,IACjC,aAAa,IAAI,KAAK,SAAS,iBAAiB,SAAS,cAAc,SAAS,KAAK,GAAG;AAChG;AACA,SAAS,eAAe,MAAM,QAAQ,OAAO;AACzC,MAAI,MAAM,KAAK,MAAM;AACrB,oBAAkB,MAAM,GAAG;AAC3B,MAAI,CAAC,oBAAoB,IAAI;AACzB;AAIJ,MAAI,CAAC,SAAS,KAAK,MAAM,aAAa,KAAK,MAAM,UAAU,gBAAgB,QAAQ;AAC/E,QAAI,SAAS,KAAK,kBAAkB,GAAG,SAAS,KAAK,YAAY;AACjE,QAAI,OAAO,cAAc,OAAO,cAC5B,qBAAqB,OAAO,YAAY,OAAO,cAAc,OAAO,YAAY,OAAO,YAAY,GAAG;AACtG,WAAK,MAAM,UAAU,uBAAuB;AAC5C,WAAK,YAAY,gBAAgB;AACjC;AAAA,IACJ;AAAA,EACJ;AACA,OAAK,YAAY,oBAAoB;AACrC,MAAI,KAAK,eAAe;AACpB,wBAAoB,IAAI;AAAA,EAC5B,OACK;AACD,QAAI,EAAE,QAAQ,KAAK,IAAI,KAAK,mBAAmB;AAC/C,QAAI,iCAAiC,EAAE,eAAe,gBAAgB;AAClE,UAAI,CAAC,IAAI,MAAM,OAAO;AAClB,4BAAoB,wBAAwB,MAAM,IAAI,IAAI;AAC9D,UAAI,CAAC,IAAI,SAAS,CAAC,IAAI,MAAM,OAAO;AAChC,0BAAkB,wBAAwB,MAAM,IAAI,EAAE;AAAA,IAC9D;AACA,SAAK,QAAQ,aAAa,QAAQ,MAAM,MAAM,KAAK;AACnD,QAAI,+BAA+B;AAC/B,UAAI;AACA,sBAAc,iBAAiB;AACnC,UAAI;AACA,sBAAc,eAAe;AAAA,IACrC;AACA,QAAI,IAAI,SAAS;AACb,WAAK,IAAI,UAAU,OAAO,2BAA2B;AAAA,IACzD,OACK;AACD,WAAK,IAAI,UAAU,IAAI,2BAA2B;AAClD,UAAI,uBAAuB;AACvB,qCAA6B,IAAI;AAAA,IACzC;AAAA,EACJ;AACA,OAAK,YAAY,gBAAgB;AACjC,OAAK,YAAY,iBAAiB;AACtC;AAIA,IAAM,gCAAgC,UAAU,UAAU,iBAAiB;AAC3E,SAAS,wBAAwB,MAAM,KAAK;AACxC,MAAI,EAAE,MAAM,OAAO,IAAI,KAAK,QAAQ,WAAW,KAAK,CAAC;AACrD,MAAI,QAAQ,SAAS,KAAK,WAAW,SAAS,KAAK,WAAW,MAAM,IAAI;AACxE,MAAI,SAAS,SAAS,KAAK,WAAW,SAAS,CAAC,IAAI;AACpD,MAAI,UAAU,SAAS,MAAM,mBAAmB;AAC5C,WAAO,YAAY,KAAK;AAC5B,OAAK,CAAC,SAAS,MAAM,mBAAmB,aACnC,CAAC,UAAU,OAAO,mBAAmB,UAAU;AAChD,QAAI;AACA,aAAO,YAAY,KAAK;AAAA,aACnB;AACL,aAAO,YAAY,MAAM;AAAA,EACjC;AACJ;AACA,SAAS,YAAY,SAAS;AAC1B,UAAQ,kBAAkB;AAC1B,MAAI,UAAU,QAAQ,WAAW;AAC7B,YAAQ,YAAY;AACpB,YAAQ,eAAe;AAAA,EAC3B;AACA,SAAO;AACX;AACA,SAAS,cAAc,SAAS;AAC5B,UAAQ,kBAAkB;AAC1B,MAAI,QAAQ,cAAc;AACtB,YAAQ,YAAY;AACpB,YAAQ,eAAe;AAAA,EAC3B;AACJ;AACA,SAAS,6BAA6B,MAAM;AACxC,MAAIA,OAAM,KAAK,IAAI;AACnB,EAAAA,KAAI,oBAAoB,mBAAmB,KAAK,MAAM,kBAAkB;AACxE,MAAI,SAAS,KAAK,kBAAkB;AACpC,MAAI,OAAO,OAAO,YAAY,SAAS,OAAO;AAC9C,EAAAA,KAAI,iBAAiB,mBAAmB,KAAK,MAAM,qBAAqB,MAAM;AAC1E,QAAI,OAAO,cAAc,QAAQ,OAAO,gBAAgB,QAAQ;AAC5D,MAAAA,KAAI,oBAAoB,mBAAmB,KAAK,MAAM,kBAAkB;AACxE,iBAAW,MAAM;AACb,YAAI,CAAC,oBAAoB,IAAI,KAAK,KAAK,MAAM,UAAU;AACnD,eAAK,IAAI,UAAU,OAAO,2BAA2B;AAAA,MAC7D,GAAG,EAAE;AAAA,IACT;AAAA,EACJ,CAAC;AACL;AACA,SAAS,oBAAoB,MAAM;AAC/B,MAAI,SAAS,KAAK,aAAa,GAAG,QAAQ,SAAS,YAAY;AAC/D,MAAI,CAAC;AACD;AACJ,MAAI,OAAO,KAAK,cAAc,KAAK,MAAM,KAAK,YAAY;AAC1D,MAAI;AACA,UAAM,SAAS,KAAK,YAAY,SAAS,IAAI,IAAI,CAAC;AAAA;AAElD,UAAM,SAAS,MAAM,CAAC;AAC1B,QAAM,SAAS,IAAI;AACnB,SAAO,gBAAgB;AACvB,SAAO,SAAS,KAAK;AAMrB,MAAI,CAAC,OAAO,CAAC,KAAK,MAAM,UAAU,WAAW,MAAM,cAAc,IAAI;AACjE,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACpB;AACJ;AACA,SAAS,kBAAkB,MAAM,KAAK;AAClC,MAAI,eAAe,eAAe;AAC9B,QAAI,OAAO,KAAK,QAAQ,OAAO,IAAI,IAAI;AACvC,QAAI,QAAQ,KAAK,sBAAsB;AACnC,yBAAmB,IAAI;AACvB,UAAI;AACA,aAAK,WAAW;AACpB,WAAK,uBAAuB;AAAA,IAChC;AAAA,EACJ,OACK;AACD,uBAAmB,IAAI;AAAA,EAC3B;AACJ;AAEA,SAAS,mBAAmB,MAAM;AAC9B,MAAI,KAAK,sBAAsB;AAC3B,QAAI,KAAK,qBAAqB;AAC1B,WAAK,qBAAqB,aAAa;AAC3C,SAAK,uBAAuB;AAAA,EAChC;AACJ;AACA,SAAS,iBAAiB,MAAM,SAAS,OAAO,MAAM;AAClD,SAAO,KAAK,SAAS,0BAA0B,OAAK,EAAE,MAAM,SAAS,KAAK,CAAC,KACpE,cAAc,QAAQ,SAAS,OAAO,IAAI;AACrD;AACA,SAAS,qBAAqB,MAAM;AAChC,MAAI,KAAK,YAAY,CAAC,KAAK,SAAS;AAChC,WAAO;AACX,SAAO,aAAa,IAAI;AAC5B;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,MAAM,KAAK,kBAAkB;AACjC,MAAI,CAAC,IAAI;AACL,WAAO;AACX,MAAI;AAIA,WAAO,KAAK,IAAI,SAAS,IAAI,WAAW,YAAY,IAAI,IAAI,WAAW,aAAa,IAAI,UAAU,MAC7F,KAAK,YAAY,KAAK,IAAI,SAAS,IAAI,UAAU,YAAY,IAAI,IAAI,UAAU,aAAa,IAAI,SAAS;AAAA,EAClH,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACJ;AACA,SAAS,mBAAmB,MAAM;AAC9B,MAAI,YAAY,KAAK,QAAQ,WAAW,KAAK,MAAM,UAAU,QAAQ,CAAC;AACtE,MAAI,SAAS,KAAK,kBAAkB;AACpC,SAAO,qBAAqB,UAAU,MAAM,UAAU,QAAQ,OAAO,YAAY,OAAO,YAAY;AACxG;AAEA,SAAS,mBAAmB,OAAO,KAAK;AACpC,MAAI,EAAE,SAAS,MAAM,IAAI,MAAM;AAC/B,MAAI,QAAQ,MAAM,IAAI,QAAQ,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK;AAC5D,MAAI,SAAS,CAAC,MAAM,OAAO,gBAAgB,QAAQ,MAAM,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,IAAI;AAC/H,SAAO,UAAU,UAAU,SAAS,QAAQ,GAAG;AACnD;AACA,SAAS,MAAM,MAAM,KAAK;AACtB,OAAK,SAAS,KAAK,MAAM,GAAG,aAAa,GAAG,EAAE,eAAe,CAAC;AAC9D,SAAO;AACX;AACA,SAAS,mBAAmB,MAAM,KAAK,MAAM;AACzC,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,eAAe,eAAe;AAC9B,QAAI,KAAK,QAAQ,GAAG,IAAI,IAAI;AACxB,UAAI,EAAE,MAAM,IAAI,KAAK,OAAO,MAAM,aAAa,OAAO,MAAM,IAAI,MAAM,aAAa,MAAM;AACzF,UAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,KAAK;AAC9B,eAAO;AACX,UAAI,WAAW,KAAK,MAAM,IAAI,QAAQ,MAAM,MAAM,KAAK,YAAY,MAAM,IAAI,KAAK,EAAE;AACpF,aAAO,MAAM,MAAM,IAAI,cAAc,IAAI,SAAS,QAAQ,CAAC;AAAA,IAC/D,WACS,CAAC,IAAI,OAAO;AACjB,aAAO;AAAA,IACX,WACS,KAAK,eAAe,MAAM,IAAI,YAAY,UAAU,GAAG;AAC5D,UAAI,OAAO,mBAAmB,KAAK,OAAO,GAAG;AAC7C,UAAI,QAAS,gBAAgB;AACzB,eAAO,MAAM,MAAM,IAAI;AAC3B,aAAO;AAAA,IACX,WACS,EAAE,OAAO,KAAK,QAAQ,GAAG,IAAI,KAAK;AACvC,UAAI,QAAQ,IAAI,OAAO,OAAO,MAAM,aAAa,OAAO,MAAM,IAAI,MAAM,aAAa,MAAM,WAAW;AACtG,UAAI,CAAC,QAAQ,KAAK;AACd,eAAO;AACX,UAAI,UAAU,MAAM,IAAI,MAAM,MAAM,KAAK,WAAW,MAAM;AAC1D,UAAI,EAAE,KAAK,WAAW,OAAO,KAAK,QAAQ,OAAO,OAAO,MAAM,CAAC,KAAK;AAChE,eAAO;AACX,UAAI,cAAc,aAAa,IAAI,GAAG;AAClC,eAAO,MAAM,MAAM,IAAI,cAAc,MAAM,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC;AAAA,MAC7G,WACS,QAAQ;AAIb,eAAO,MAAM,MAAM,IAAI,cAAc,KAAK,MAAM,IAAI,QAAQ,MAAM,IAAI,UAAU,UAAU,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC7G,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ,WACS,eAAe,iBAAiB,IAAI,KAAK,UAAU;AACxD,WAAO,MAAM,MAAM,IAAI,cAAc,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,EACvE,OACK;AACD,QAAI,OAAO,mBAAmB,KAAK,OAAO,GAAG;AAC7C,QAAI;AACA,aAAO,MAAM,MAAM,IAAI;AAC3B,WAAO;AAAA,EACX;AACJ;AACA,SAAS,QAAQ,MAAM;AACnB,SAAO,KAAK,YAAY,IAAI,KAAK,UAAU,SAAS,KAAK,WAAW;AACxE;AACA,SAAS,YAAY,KAAK,KAAK;AAC3B,MAAI,OAAO,IAAI;AACf,SAAO,QAAQ,KAAK,QAAQ,MAAM,MAAM,KAAK,IAAI,eAAe,IAAI,YAAY;AACpF;AACA,SAAS,iBAAiB,MAAM,KAAK;AACjC,SAAO,MAAM,IAAI,uBAAuB,IAAI,IAAI,sBAAsB,IAAI;AAC9E;AAGA,SAAS,uBAAuB,MAAM;AAClC,MAAI,MAAM,KAAK,kBAAkB;AACjC,MAAI,OAAO,IAAI,WAAW,SAAS,IAAI;AACvC,MAAI,CAAC;AACD;AACJ,MAAI,UAAU,YAAY,QAAQ;AAIlC,MAAI,SAAS,KAAK,YAAY,KAAK,SAAS,QAAQ,IAAI,KAAK,YAAY,KAAK,WAAW,MAAM,GAAG,EAAE;AAChG,YAAQ;AACZ,aAAS;AACL,QAAI,SAAS,GAAG;AACZ,UAAI,KAAK,YAAY,GAAG;AACpB;AAAA,MACJ,OACK;AACD,YAAI,SAAS,KAAK,WAAW,SAAS,CAAC;AACvC,YAAI,YAAY,QAAQ,EAAE,GAAG;AACzB,qBAAW;AACX,uBAAa,EAAE;AAAA,QACnB,WACS,OAAO,YAAY,GAAG;AAC3B,iBAAO;AACP,mBAAS,KAAK,UAAU;AAAA,QAC5B;AAEI;AAAA,MACR;AAAA,IACJ,WACS,YAAY,IAAI,GAAG;AACxB;AAAA,IACJ,OACK;AACD,UAAI,OAAO,KAAK;AAChB,aAAO,QAAQ,YAAY,MAAM,EAAE,GAAG;AAClC,mBAAW,KAAK;AAChB,qBAAa,SAAS,IAAI;AAC1B,eAAO,KAAK;AAAA,MAChB;AACA,UAAI,CAAC,MAAM;AACP,eAAO,KAAK;AACZ,YAAI,QAAQ,KAAK;AACb;AACJ,iBAAS;AAAA,MACb,OACK;AACD,eAAO;AACP,iBAAS,QAAQ,IAAI;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI;AACA,gBAAY,MAAM,MAAM,MAAM;AAAA,WACzB;AACL,gBAAY,MAAM,UAAU,UAAU;AAC9C;AAGA,SAAS,sBAAsB,MAAM;AACjC,MAAI,MAAM,KAAK,kBAAkB;AACjC,MAAI,OAAO,IAAI,WAAW,SAAS,IAAI;AACvC,MAAI,CAAC;AACD;AACJ,MAAI,MAAM,QAAQ,IAAI;AACtB,MAAI,UAAU;AACd,aAAS;AACL,QAAI,SAAS,KAAK;AACd,UAAI,KAAK,YAAY;AACjB;AACJ,UAAI,QAAQ,KAAK,WAAW,MAAM;AAClC,UAAI,YAAY,OAAO,CAAC,GAAG;AACvB,mBAAW;AACX,qBAAa,EAAE;AAAA,MACnB;AAEI;AAAA,IACR,WACS,YAAY,IAAI,GAAG;AACxB;AAAA,IACJ,OACK;AACD,UAAI,OAAO,KAAK;AAChB,aAAO,QAAQ,YAAY,MAAM,CAAC,GAAG;AACjC,mBAAW,KAAK;AAChB,qBAAa,SAAS,IAAI,IAAI;AAC9B,eAAO,KAAK;AAAA,MAChB;AACA,UAAI,CAAC,MAAM;AACP,eAAO,KAAK;AACZ,YAAI,QAAQ,KAAK;AACb;AACJ,iBAAS,MAAM;AAAA,MACnB,OACK;AACD,eAAO;AACP,iBAAS;AACT,cAAM,QAAQ,IAAI;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI;AACA,gBAAY,MAAM,UAAU,UAAU;AAC9C;AACA,SAAS,YAAY,KAAK;AACtB,MAAI,OAAO,IAAI;AACf,SAAO,QAAQ,KAAK,QAAQ,KAAK,KAAK;AAC1C;AACA,SAAS,cAAc,MAAM,QAAQ;AACjC,SAAO,QAAQ,UAAU,KAAK,WAAW,UAAU,CAAC,aAAa,IAAI,GAAG;AACpE,aAAS,SAAS,IAAI,IAAI;AAC1B,WAAO,KAAK;AAAA,EAChB;AACA,SAAO,QAAQ,SAAS,KAAK,WAAW,QAAQ;AAC5C,QAAI,OAAO,KAAK,WAAW,MAAM;AACjC,QAAI,KAAK,YAAY;AACjB,aAAO;AACX,QAAI,KAAK,YAAY,KAAK,KAAK,mBAAmB;AAC9C;AACJ,WAAO;AACP,aAAS;AAAA,EACb;AACJ;AACA,SAAS,eAAe,MAAM,QAAQ;AAClC,SAAO,QAAQ,CAAC,UAAU,CAAC,aAAa,IAAI,GAAG;AAC3C,aAAS,SAAS,IAAI;AACtB,WAAO,KAAK;AAAA,EAChB;AACA,SAAO,QAAQ,QAAQ;AACnB,QAAI,OAAO,KAAK,WAAW,SAAS,CAAC;AACrC,QAAI,KAAK,YAAY;AACjB,aAAO;AACX,QAAI,KAAK,YAAY,KAAK,KAAK,mBAAmB;AAC9C;AACJ,WAAO;AACP,aAAS,KAAK,WAAW;AAAA,EAC7B;AACJ;AACA,SAAS,YAAY,MAAM,MAAM,QAAQ;AACrC,MAAI,KAAK,YAAY,GAAG;AACpB,QAAI,QAAQ;AACZ,QAAI,QAAQ,cAAc,MAAM,MAAM,GAAG;AACrC,aAAO;AACP,eAAS;AAAA,IACb,WACS,SAAS,eAAe,MAAM,MAAM,GAAG;AAC5C,aAAO;AACP,eAAS,OAAO,UAAU;AAAA,IAC9B;AAAA,EACJ;AACA,MAAI,MAAM,KAAK,aAAa;AAC5B,MAAI,CAAC;AACD;AACJ,MAAI,mBAAmB,GAAG,GAAG;AACzB,QAAI,QAAQ,SAAS,YAAY;AACjC,UAAM,OAAO,MAAM,MAAM;AACzB,UAAM,SAAS,MAAM,MAAM;AAC3B,QAAI,gBAAgB;AACpB,QAAI,SAAS,KAAK;AAAA,EACtB,WACS,IAAI,QAAQ;AACjB,QAAI,OAAO,MAAM,MAAM;AAAA,EAC3B;AACA,OAAK,YAAY,gBAAgB;AACjC,MAAI,EAAE,MAAM,IAAI;AAEhB,aAAW,MAAM;AACb,QAAI,KAAK,SAAS;AACd,qBAAe,IAAI;AAAA,EAC3B,GAAG,EAAE;AACT;AACA,SAAS,cAAc,MAAM,KAAK;AAC9B,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,GAAG;AACrC,MAAI,EAAE,UAAU,YAAY,KAAK,OAAO,eAAe;AACnD,QAAI,SAAS,KAAK,YAAY,GAAG;AACjC,QAAI,MAAM,KAAK,MAAM,GAAG;AACpB,UAAI,SAAS,KAAK,YAAY,MAAM,CAAC;AACrC,UAAI,OAAO,OAAO,MAAM,OAAO,UAAU;AACzC,UAAI,MAAM,OAAO,OAAO,MAAM,OAAO,UAAU,KAAK,IAAI,OAAO,OAAO,OAAO,IAAI,IAAI;AACjF,eAAO,OAAO,OAAO,OAAO,OAAO,QAAQ;AAAA,IACnD;AACA,QAAI,MAAM,KAAK,IAAI,GAAG;AAClB,UAAI,QAAQ,KAAK,YAAY,MAAM,CAAC;AACpC,UAAI,OAAO,MAAM,MAAM,MAAM,UAAU;AACvC,UAAI,MAAM,OAAO,OAAO,MAAM,OAAO,UAAU,KAAK,IAAI,MAAM,OAAO,OAAO,IAAI,IAAI;AAChF,eAAO,MAAM,OAAO,OAAO,OAAO,QAAQ;AAAA,IAClD;AAAA,EACJ;AACA,MAAI,WAAW,iBAAiB,KAAK,GAAG,EAAE;AAC1C,SAAO,YAAY,QAAQ,QAAQ;AACvC;AAIA,SAAS,iBAAiB,MAAM,KAAK,MAAM;AACvC,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,eAAe,iBAAiB,CAAC,IAAI,SAAS,KAAK,QAAQ,GAAG,IAAI;AAClE,WAAO;AACX,MAAI,OAAO,KAAK,QAAQ,GAAG,IAAI;AAC3B,WAAO;AACX,MAAI,EAAE,OAAO,IAAI,IAAI;AACrB,MAAI,CAAC,MAAM,OAAO,iBAAiB,KAAK,eAAe,MAAM,IAAI,OAAO,MAAM,GAAG;AAC7E,QAAI,OAAO,mBAAmB,KAAK,OAAO,GAAG;AAC7C,QAAI,QAAS,gBAAgB;AACzB,aAAO,MAAM,MAAM,IAAI;AAAA,EAC/B;AACA,MAAI,CAAC,MAAM,OAAO,eAAe;AAC7B,QAAI,OAAO,MAAM,IAAI,QAAQ;AAC7B,QAAI,SAAS,eAAe,eAAe,UAAU,KAAK,MAAM,GAAG,IAAI,UAAU,SAAS,MAAM,GAAG;AACnG,WAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAAA,EAC1C;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,MAAM,KAAK;AAC3C,MAAI,EAAE,KAAK,MAAM,qBAAqB;AAClC,WAAO;AACX,MAAI,EAAE,OAAO,SAAS,OAAAI,OAAM,IAAI,KAAK,MAAM;AAC3C,MAAI,CAAC,MAAM,WAAW,OAAO;AACzB,WAAO;AACX,MAAI,CAACA;AACD,WAAO;AACX,MAAI,KAAK,eAAe,MAAM,IAAI,YAAY,UAAU;AACpD,WAAO;AACX,MAAI,WAAW,CAAC,MAAM,eAAe,MAAM,IAAI,MAAM,aAAa,MAAM;AACxE,MAAI,YAAY,CAAC,SAAS,QAAQ;AAC9B,QAAI,KAAK,KAAK,MAAM;AACpB,QAAI,MAAM;AACN,SAAG,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,GAAG;AAAA;AAElD,SAAG,OAAO,MAAM,KAAK,MAAM,MAAM,SAAS,QAAQ;AACtD,SAAK,SAAS,EAAE;AAChB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM,MAAM,OAAO;AACvC,OAAK,YAAY,KAAK;AACtB,OAAK,kBAAkB;AACvB,OAAK,YAAY,MAAM;AAC3B;AAMA,SAAS,mBAAmB,MAAM;AAC9B,MAAI,CAAC,UAAU,KAAK,MAAM,UAAU,MAAM,eAAe;AACrD,WAAO;AACX,MAAI,EAAE,WAAW,YAAY,IAAI,KAAK,kBAAkB;AACxD,MAAI,aAAa,UAAU,YAAY,KAAK,eAAe,KACvD,UAAU,cAAc,UAAU,WAAW,mBAAmB,SAAS;AACzE,QAAI,QAAQ,UAAU;AACtB,mBAAe,MAAM,OAAO,MAAM;AAClC,eAAW,MAAM,eAAe,MAAM,OAAO,OAAO,GAAG,EAAE;AAAA,EAC7D;AACA,SAAO;AACX;AAOA,SAAS,QAAQ,OAAO;AACpB,MAAI,SAAS;AACb,MAAI,MAAM;AACN,cAAU;AACd,MAAI,MAAM;AACN,cAAU;AACd,MAAI,MAAM;AACN,cAAU;AACd,MAAI,MAAM;AACN,cAAU;AACd,SAAO;AACX;AACA,SAAS,eAAe,MAAM,OAAO;AACjC,MAAI,OAAO,MAAM,SAAS,OAAO,QAAQ,KAAK;AAC9C,MAAI,QAAQ,KAAM,OAAO,QAAQ,MAAM,QAAQ,KAAM;AACjD,WAAO,2BAA2B,MAAM,EAAE,KAAK,iBAAiB,MAAM,EAAE;AAAA,EAC5E,WACU,QAAQ,MAAM,CAAC,MAAM,YAAc,OAAO,QAAQ,MAAM,QAAQ,KAAM;AAC5E,WAAO,2BAA2B,MAAM,CAAC,KAAK,iBAAiB,MAAM,CAAC;AAAA,EAC1E,WACS,QAAQ,MAAM,QAAQ,IAAI;AAC/B,WAAO;AAAA,EACX,WACS,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;AACvD,QAAI,MAAM,QAAQ,KAAM,cAAc,MAAM,KAAK,MAAM,UAAU,IAAI,KAAK,QAAQ,KAAK,IAAK;AAC5F,WAAO,mBAAmB,MAAM,KAAK,IAAI,KAAK,iBAAiB,MAAM,GAAG;AAAA,EAC5E,WACS,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;AACvD,QAAI,MAAM,QAAQ,KAAM,cAAc,MAAM,KAAK,MAAM,UAAU,IAAI,KAAK,QAAQ,IAAI,KAAM;AAC5F,WAAO,mBAAmB,MAAM,KAAK,IAAI,KAAK,iBAAiB,MAAM,GAAG;AAAA,EAC5E,WACS,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;AACvD,WAAO,iBAAiB,MAAM,IAAI,IAAI,KAAK,iBAAiB,MAAM,EAAE;AAAA,EACxE,WACS,QAAQ,MAAO,OAAO,QAAQ,MAAM,QAAQ,KAAM;AACvD,WAAO,mBAAmB,IAAI,KAAK,iBAAiB,MAAM,GAAG,IAAI,KAAK,iBAAiB,MAAM,CAAC;AAAA,EAClG,WACS,SAAS,MAAM,MAAM,SACzB,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,KAAK;AACxD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,sBAAsB,MAAM,OAAO;AACxC,OAAK,SAAS,mBAAmB,OAAK;AAAE,YAAQ,EAAE,OAAO,IAAI;AAAA,EAAG,CAAC;AACjE,MAAI,UAAU,CAAC,GAAG,EAAE,SAAS,WAAW,QAAQ,IAAI;AACpD,SAAO,YAAY,KAAK,UAAU,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,cAAc,GAAG;AAClG;AACA;AACA,QAAI,OAAO,QAAQ;AACnB,YAAQ,KAAK,KAAK,KAAK,MAAM,KAAK,SAAS,KAAK,KAAK,eAAe,KAAK,QAAQ,IAAI;AACrF,cAAU,KAAK;AAAA,EACnB;AACA,MAAI,aAAa,KAAK,SAAS,qBAAqB,KAAK,cAAc,WAAW,KAAK,MAAM,MAAM;AACnG,MAAIJ,OAAM,YAAY,GAAG,OAAOA,KAAI,cAAc,KAAK;AACvD,OAAK,YAAY,WAAW,kBAAkB,SAAS,EAAE,UAAUA,KAAI,CAAC,CAAC;AACzE,MAAI,aAAa,KAAK,YAAY,WAAW,WAAW;AACxD,SAAO,cAAc,WAAW,YAAY,MAAM,YAAY,QAAQ,WAAW,SAAS,YAAY,CAAC,IAAI;AACvG,aAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,UAAI,UAAUA,KAAI,cAAc,UAAU,CAAC,CAAC;AAC5C,aAAO,KAAK;AACR,gBAAQ,YAAY,KAAK,UAAU;AACvC,WAAK,YAAY,OAAO;AACxB;AAAA,IACJ;AACA,iBAAa,KAAK;AAAA,EACtB;AACA,MAAI,cAAc,WAAW,YAAY;AACrC,eAAW,aAAa,iBAAiB,GAAG,SAAS,IAAI,OAAO,GAAG,WAAW,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK,UAAU,OAAO,CAAC,EAAE;AACnI,MAAI,OAAO,KAAK,SAAS,2BAA2B,OAAK,EAAE,OAAO,IAAI,CAAC,KACnE,MAAM,QAAQ,YAAY,GAAG,MAAM,QAAQ,MAAM,MAAM;AAC3D,SAAO,EAAE,KAAK,MAAM,MAAM,MAAM;AACpC;AAEA,SAAS,mBAAmB,MAAM,MAAM,MAAM,WAAW,UAAU;AAC/D,MAAI,SAAS,SAAS,OAAO,KAAK,KAAK;AACvC,MAAI,KAAK;AACT,MAAI,CAAC,QAAQ,CAAC;AACV,WAAO;AACX,MAAI,SAAS,SAAS,aAAa,UAAU,CAAC;AAC9C,MAAI,QAAQ;AACR,SAAK,SAAS,uBAAuB,OAAK;AAAE,aAAO,EAAE,MAAM,UAAU,WAAW,IAAI;AAAA,IAAG,CAAC;AACxF,QAAI;AACA,aAAO,OAAO,IAAI,MAAM,SAAS,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,QAAQ,UAAU,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,MAAM;AAC/G,QAAI,SAAS,KAAK,SAAS,uBAAuB,OAAK,EAAE,MAAM,UAAU,WAAW,IAAI,CAAC;AACzF,QAAI,QAAQ;AACR,cAAQ;AAAA,IACZ,OACK;AACD,UAAI,QAAQ,SAAS,MAAM;AAC3B,UAAI,EAAE,OAAO,IAAI,KAAK,OAAO,aAAa,cAAc,WAAW,MAAM;AACzE,YAAM,SAAS,cAAc,KAAK;AAClC,WAAK,MAAM,eAAe,EAAE,QAAQ,WAAS;AACzC,YAAI,IAAI,IAAI,YAAY,SAAS,cAAc,GAAG,CAAC;AACnD,YAAI;AACA,YAAE,YAAY,WAAW,cAAc,OAAO,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,MACzE,CAAC;AAAA,IACL;AAAA,EACJ,OACK;AACD,SAAK,SAAS,uBAAuB,OAAK;AAAE,aAAO,EAAE,MAAM,IAAI;AAAA,IAAG,CAAC;AACnE,UAAM,SAAS,IAAI;AACnB,QAAI;AACA,4BAAsB,GAAG;AAAA,EACjC;AACA,MAAI,cAAc,OAAO,IAAI,cAAc,iBAAiB;AAC5D,MAAI,YAAY,eAAe,gCAAgC,KAAK,YAAY,aAAa,eAAe,KAAK,EAAE;AACnH,MAAI,aAAa,UAAU,CAAC;AACxB,aAAS,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK;AACpC,UAAI,QAAQ,IAAI;AAChB,aAAO,SAAS,MAAM,YAAY;AAC9B,gBAAQ,MAAM;AAClB,UAAI,CAAC;AACD;AACJ,YAAM;AAAA,IACV;AACJ,MAAI,CAAC,OAAO;AACR,QAAI,SAAS,KAAK,SAAS,iBAAiB,KAAK,KAAK,SAAS,WAAW,KAAK,UAAU,WAAW,KAAK,MAAM,MAAM;AACrH,YAAQ,OAAO,WAAW,KAAK;AAAA,MAC3B,oBAAoB,CAAC,EAAE,UAAU;AAAA,MACjC,SAAS;AAAA,MACT,aAAaK,MAAK;AACd,YAAIA,KAAI,YAAY,QAAQ,CAACA,KAAI,eAC7BA,KAAI,cAAc,CAAC,cAAc,KAAKA,KAAI,WAAW,QAAQ;AAC7D,iBAAO,EAAE,QAAQ,KAAK;AAC1B,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,WAAW;AACX,YAAQ,WAAW,WAAW,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,EACpF,OACK;AACD,YAAQ,MAAM,QAAQ,kBAAkB,MAAM,SAAS,QAAQ,GAAG,IAAI;AACtE,QAAI,MAAM,aAAa,MAAM,SAAS;AAClC,UAAI,YAAY,GAAG,UAAU;AAC7B,eAAS,OAAO,MAAM,QAAQ,YAAY,YAAY,MAAM,aAAa,CAAC,KAAK,KAAK,KAAK,WAAW,aAAa,OAAO,KAAK,YAAY;AAAA,MAAE;AAC3I,eAAS,OAAO,MAAM,QAAQ,WAAW,UAAU,MAAM,WAAW,CAAC,KAAK,KAAK,KAAK,WAAW,WAAW,OAAO,KAAK,WAAW;AAAA,MAAE;AACnI,cAAQ,WAAW,OAAO,WAAW,OAAO;AAAA,IAChD;AAAA,EACJ;AACA,OAAK,SAAS,mBAAmB,OAAK;AAAE,YAAQ,EAAE,OAAO,IAAI;AAAA,EAAG,CAAC;AACjE,SAAO;AACX;AACA,IAAM,gBAAgB;AAStB,SAAS,kBAAkB,UAAU,UAAU;AAC3C,MAAI,SAAS,aAAa;AACtB,WAAO;AACX,WAAS,IAAI,SAAS,OAAO,KAAK,GAAG,KAAK;AACtC,QAAI,SAAS,SAAS,KAAK,CAAC;AAC5B,QAAI,QAAQ,OAAO,eAAe,SAAS,MAAM,CAAC,CAAC;AACnD,QAAI,UAAU,SAAS,CAAC;AACxB,aAAS,QAAQ,UAAQ;AACrB,UAAI,CAAC;AACD;AACJ,UAAI,OAAO,MAAM,aAAa,KAAK,IAAI,GAAG;AAC1C,UAAI,CAAC;AACD,eAAO,SAAS;AACpB,UAAI,SAAS,OAAO,UAAU,SAAS,UAAU,aAAa,MAAM,UAAU,MAAM,OAAO,OAAO,SAAS,CAAC,GAAG,CAAC,GAAG;AAC/G,eAAO,OAAO,SAAS,CAAC,IAAI;AAAA,MAChC,OACK;AACD,YAAI,OAAO;AACP,iBAAO,OAAO,SAAS,CAAC,IAAI,WAAW,OAAO,OAAO,SAAS,CAAC,GAAG,SAAS,MAAM;AACrF,YAAI,UAAU,aAAa,MAAM,IAAI;AACrC,eAAO,KAAK,OAAO;AACnB,gBAAQ,MAAM,UAAU,QAAQ,IAAI;AACpC,mBAAW;AAAA,MACf;AAAA,IACJ,CAAC;AACD,QAAI;AACA,aAAO,SAAS,KAAK,MAAM;AAAA,EACnC;AACA,SAAO;AACX;AACA,SAAS,aAAa,MAAM,MAAM,OAAO,GAAG;AACxC,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,MAAM;AACrC,WAAO,KAAK,CAAC,EAAE,OAAO,MAAM,SAAS,KAAK,IAAI,CAAC;AACnD,SAAO;AACX;AAGA,SAAS,aAAa,MAAM,UAAU,MAAM,SAAS,OAAO;AACxD,MAAI,QAAQ,KAAK,UAAU,QAAQ,SAAS,UAAU,KAAK,KAAK,KAAK,SAAS,KAAK,GAAG;AAClF,QAAI,QAAQ,aAAa,MAAM,UAAU,MAAM,QAAQ,WAAW,QAAQ,CAAC;AAC3E,QAAI;AACA,aAAO,QAAQ,KAAK,QAAQ,QAAQ,aAAa,QAAQ,aAAa,GAAG,KAAK,CAAC;AACnF,QAAI,QAAQ,QAAQ,eAAe,QAAQ,UAAU;AACrD,QAAI,MAAM,UAAU,SAAS,KAAK,SAAS,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,CAAC;AACtE,aAAO,QAAQ,KAAK,QAAQ,QAAQ,OAAO,SAAS,KAAK,aAAa,MAAM,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EACtG;AACJ;AACA,SAAS,WAAW,MAAM,OAAO;AAC7B,MAAI,SAAS;AACT,WAAO;AACX,MAAI,WAAW,KAAK,QAAQ,aAAa,KAAK,aAAa,GAAG,WAAW,KAAK,WAAW,QAAQ,CAAC,CAAC;AACnG,MAAI,OAAO,KAAK,eAAe,KAAK,UAAU,EAAE,WAAW,SAAS,OAAO,IAAI;AAC/E,SAAO,KAAK,KAAK,SAAS,OAAO,IAAI,CAAC;AAC1C;AACA,SAAS,WAAW,UAAU,MAAM,MAAM,IAAI,OAAO,SAAS;AAC1D,MAAI,OAAO,OAAO,IAAI,SAAS,aAAa,SAAS,WAAW,QAAQ,KAAK;AAC7E,MAAI,SAAS,aAAa;AACtB,cAAU;AACd,MAAI,QAAQ,KAAK;AACb,YAAQ,WAAW,OAAO,MAAM,MAAM,IAAI,QAAQ,GAAG,OAAO;AAChE,MAAI,SAAS;AACT,YAAQ,OAAO,IAAI,KAAK,eAAe,CAAC,EAAE,WAAW,OAAO,WAAW,KAAK,EAAE,OAAO,KAAK,IACpF,MAAM,OAAO,KAAK,eAAe,KAAK,UAAU,EAAE,WAAW,SAAS,OAAO,IAAI,CAAC;AAC5F,SAAO,SAAS,aAAa,OAAO,IAAI,IAAI,SAAS,aAAa,GAAG,KAAK,KAAK,KAAK,CAAC;AACzF;AACA,SAAS,WAAW,OAAO,WAAW,SAAS;AAC3C,MAAI,YAAY,MAAM;AAClB,YAAQ,IAAI,MAAM,WAAW,MAAM,SAAS,IAAI,WAAW,MAAM,WAAW,GAAG,MAAM,OAAO,GAAG,WAAW,MAAM,OAAO;AAC3H,MAAI,UAAU,MAAM;AAChB,YAAQ,IAAI,MAAM,WAAW,MAAM,SAAS,GAAG,SAAS,MAAM,SAAS,GAAG,CAAC,GAAG,MAAM,WAAW,OAAO;AAC1G,SAAO;AACX;AAIA,IAAM,UAAU;AAAA,EACZ,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,OAAO;AAAA,EACf,SAAS,CAAC,OAAO;AAAA,EACjB,UAAU,CAAC,OAAO;AAAA,EAClB,KAAK,CAAC,SAAS,UAAU;AAAA,EACzB,IAAI,CAAC,SAAS,OAAO;AAAA,EACrB,IAAI,CAAC,SAAS,SAAS,IAAI;AAAA,EAC3B,IAAI,CAAC,SAAS,SAAS,IAAI;AAC/B;AACA,IAAI,eAAe;AACnB,SAAS,cAAc;AACnB,SAAO,iBAAiB,eAAe,SAAS,eAAe,mBAAmB,OAAO;AAC7F;AACA,IAAI,UAAU;AACd,SAAS,iBAAiB,MAAM;AAC5B,MAAI,eAAe,OAAO;AAC1B,MAAI,CAAC;AACD,WAAO;AAIX,MAAI,CAAC;AACD,cAAU,aAAa,iBAAiB,aAAa,aAAa,wBAAwB,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;AACtH,SAAO,QAAQ,WAAW,IAAI;AAClC;AACA,SAAS,SAAS,MAAM;AACpB,MAAI,QAAQ,sBAAsB,KAAK,IAAI;AAC3C,MAAI;AACA,WAAO,KAAK,MAAM,MAAM,CAAC,EAAE,MAAM;AACrC,MAAI,MAAM,YAAY,EAAE,cAAc,KAAK;AAC3C,MAAI,WAAW,mBAAmB,KAAK,IAAI,GAAG;AAC9C,MAAI,OAAO,YAAY,QAAQ,SAAS,CAAC,EAAE,YAAY,CAAC;AACpD,WAAO,KAAK,IAAI,OAAK,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,OAAO,KAAK,IAAI,OAAK,OAAO,IAAI,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE;AACzG,MAAI,YAAY,iBAAiB,IAAI;AACrC,MAAI;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAC7B,YAAM,IAAI,cAAc,KAAK,CAAC,CAAC,KAAK;AAC5C,SAAO;AACX;AAMA,SAAS,sBAAsB,KAAK;AAChC,MAAI,QAAQ,IAAI,iBAAiB,SAAS,mCAAmC,4BAA4B;AACzG,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,OAAO,MAAM,CAAC;AAClB,QAAI,KAAK,WAAW,UAAU,KAAK,KAAK,eAAe,OAAY,KAAK;AACpE,WAAK,WAAW,aAAa,IAAI,cAAc,eAAe,GAAG,GAAG,IAAI;AAAA,EAChF;AACJ;AACA,SAAS,WAAW,OAAO,SAAS;AAChC,MAAI,CAAC,MAAM;AACP,WAAO;AACX,MAAI,SAAS,MAAM,QAAQ,WAAW,KAAK,QAAQ;AACnD,MAAI;AACA,YAAQ,KAAK,MAAM,OAAO;AAAA,EAC9B,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,WAAW,QAAQ,IAAI;AACtC,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC3C,QAAI,OAAO,OAAO,MAAM,MAAM,CAAC,CAAC;AAChC,QAAI,CAAC,QAAQ,KAAK,iBAAiB;AAC/B;AACJ,cAAU,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,CAAC,GAAG,OAAO,CAAC;AAC1D;AACA;AAAA,EACJ;AACA,SAAO,IAAI,MAAM,SAAS,WAAW,OAAO;AAChD;AAIA,IAAM,WAAW,CAAC;AAClB,IAAM,eAAe,CAAC;AACtB,IAAM,kBAAkB,EAAE,YAAY,MAAM,WAAW,KAAK;AAC5D,IAAM,aAAN,MAAiB;AAAA,EACb,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,YAAY,EAAE,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE;AAC5D,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,8BAA8B;AACnC,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB,CAAC;AACzB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AAErB,SAAK,4BAA4B;AACjC,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,uBAAO,OAAO,IAAI;AACvC,SAAK,qBAAqB;AAAA,EAC9B;AACJ;AACA,SAAS,UAAU,MAAM;AACrB,WAAS,SAAS,UAAU;AACxB,QAAI,UAAU,SAAS,KAAK;AAC5B,SAAK,IAAI,iBAAiB,OAAO,KAAK,MAAM,cAAc,KAAK,IAAI,CAACC,WAAU;AAC1E,UAAI,mBAAmB,MAAMA,MAAK,KAAK,CAAC,iBAAiB,MAAMA,MAAK,MAC/D,KAAK,YAAY,EAAEA,OAAM,QAAQ;AAClC,gBAAQ,MAAMA,MAAK;AAAA,IAC3B,GAAG,gBAAgB,KAAK,IAAI,EAAE,SAAS,KAAK,IAAI,MAAS;AAAA,EAC7D;AAIA,MAAI;AACA,SAAK,IAAI,iBAAiB,SAAS,MAAM,IAAI;AACjD,kBAAgB,IAAI;AACxB;AACA,SAAS,mBAAmB,MAAM,QAAQ;AACtC,OAAK,MAAM,sBAAsB;AACjC,OAAK,MAAM,oBAAoB,KAAK,IAAI;AAC5C;AACA,SAAS,aAAa,MAAM;AACxB,OAAK,YAAY,KAAK;AACtB,WAAS,QAAQ,KAAK,MAAM;AACxB,SAAK,IAAI,oBAAoB,MAAM,KAAK,MAAM,cAAc,IAAI,CAAC;AACrE,eAAa,KAAK,MAAM,gBAAgB;AACxC,eAAa,KAAK,MAAM,2BAA2B;AACvD;AACA,SAAS,gBAAgB,MAAM;AAC3B,OAAK,SAAS,mBAAmB,qBAAmB;AAChD,aAAS,QAAQ;AACb,UAAI,CAAC,KAAK,MAAM,cAAc,IAAI;AAC9B,aAAK,IAAI,iBAAiB,MAAM,KAAK,MAAM,cAAc,IAAI,IAAI,WAAS,iBAAiB,MAAM,KAAK,CAAC;AAAA,EACnH,CAAC;AACL;AACA,SAAS,iBAAiB,MAAM,OAAO;AACnC,SAAO,KAAK,SAAS,mBAAmB,CAAAC,cAAY;AAChD,QAAI,UAAUA,UAAS,MAAM,IAAI;AACjC,WAAO,UAAU,QAAQ,MAAM,KAAK,KAAK,MAAM,mBAAmB;AAAA,EACtE,CAAC;AACL;AACA,SAAS,mBAAmB,MAAM,OAAO;AACrC,MAAI,CAAC,MAAM;AACP,WAAO;AACX,MAAI,MAAM;AACN,WAAO;AACX,WAAS,OAAO,MAAM,QAAQ,QAAQ,KAAK,KAAK,OAAO,KAAK;AACxD,QAAI,CAAC,QAAQ,KAAK,YAAY,MACzB,KAAK,cAAc,KAAK,WAAW,UAAU,KAAK;AACnD,aAAO;AACf,SAAO;AACX;AACA,SAAS,cAAc,MAAM,OAAO;AAChC,MAAI,CAAC,iBAAiB,MAAM,KAAK,KAAK,SAAS,MAAM,IAAI,MACpD,KAAK,YAAY,EAAE,MAAM,QAAQ;AAClC,aAAS,MAAM,IAAI,EAAE,MAAM,KAAK;AACxC;AACA,aAAa,UAAU,CAAC,MAAM,WAAW;AACrC,MAAI,QAAQ;AACZ,OAAK,MAAM,WAAW,MAAM,WAAW,MAAM,MAAM;AACnD,MAAI,oBAAoB,MAAM,KAAK;AAC/B;AACJ,OAAK,MAAM,cAAc,MAAM;AAC/B,OAAK,MAAM,kBAAkB,KAAK,IAAI;AAItC,MAAI,WAAW,UAAU,MAAM,WAAW;AACtC;AACJ,MAAI,MAAM,WAAW;AACjB,SAAK,YAAY,WAAW;AAKhC,MAAI,OAAO,MAAM,WAAW,MAAM,CAAC,MAAM,WAAW,CAAC,MAAM,UAAU,CAAC,MAAM,SAAS;AACjF,QAAI,MAAM,KAAK,IAAI;AACnB,SAAK,MAAM,eAAe;AAC1B,SAAK,MAAM,8BAA8B,WAAW,MAAM;AACtD,UAAI,KAAK,MAAM,gBAAgB,KAAK;AAChC,aAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,SAAS,IAAI,OAAO,CAAC,CAAC;AAClE,aAAK,MAAM,eAAe;AAAA,MAC9B;AAAA,IACJ,GAAG,GAAG;AAAA,EACV,WACS,KAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,KAAK,CAAC,KAAK,eAAe,MAAM,KAAK,GAAG;AACzF,UAAM,eAAe;AAAA,EACzB,OACK;AACD,uBAAmB,MAAM,KAAK;AAAA,EAClC;AACJ;AACA,aAAa,QAAQ,CAAC,MAAM,UAAU;AAClC,MAAI,MAAM,WAAW;AACjB,SAAK,MAAM,WAAW;AAC9B;AACA,aAAa,WAAW,CAAC,MAAM,WAAW;AACtC,MAAI,QAAQ;AACZ,MAAI,oBAAoB,MAAM,KAAK,KAAK,CAAC,MAAM,YAC3C,MAAM,WAAW,CAAC,MAAM,UAAU,OAAO,MAAM;AAC/C;AACJ,MAAI,KAAK,SAAS,kBAAkB,OAAK,EAAE,MAAM,KAAK,CAAC,GAAG;AACtD,UAAM,eAAe;AACrB;AAAA,EACJ;AACA,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,EAAE,eAAe,kBAAkB,CAAC,IAAI,MAAM,WAAW,IAAI,GAAG,GAAG;AACnE,QAAI,OAAO,OAAO,aAAa,MAAM,QAAQ;AAC7C,QAAI,QAAQ,MAAM,KAAK,MAAM,GAAG,WAAW,IAAI,EAAE,eAAe;AAChE,QAAI,CAAC,SAAS,KAAK,IAAI,KAAK,CAAC,KAAK,SAAS,mBAAmB,OAAK,EAAE,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AAC/G,WAAK,SAAS,MAAM,CAAC;AACzB,UAAM,eAAe;AAAA,EACzB;AACJ;AACA,SAAS,YAAY,OAAO;AAAE,SAAO,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ;AAAG;AAClF,SAAS,OAAO,OAAO,OAAO;AAC1B,MAAI,KAAK,MAAM,IAAI,MAAM,SAAS,KAAK,MAAM,IAAI,MAAM;AACvD,SAAO,KAAK,KAAK,KAAK,KAAK;AAC/B;AACA,SAAS,oBAAoB,MAAM,UAAU,KAAK,QAAQ,OAAO;AAC7D,MAAI,UAAU;AACV,WAAO;AACX,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,MAAM;AACxC,WAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK;AACrC,QAAI,KAAK,SAAS,UAAU,OAAK,IAAI,KAAK,QAAQ,EAAE,MAAM,KAAK,KAAK,WAAW,KAAK,OAAO,CAAC,GAAG,OAAO,IAAI,IACpG,EAAE,MAAM,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,OAAO,KAAK,CAAC;AAC1D,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,MAAM,WAAW,QAAQ;AAC9C,MAAI,CAAC,KAAK;AACN,SAAK,MAAM;AACf,MAAI,KAAK,MAAM,UAAU,GAAG,SAAS;AACjC;AACJ,MAAI,KAAK,KAAK,MAAM,GAAG,aAAa,SAAS;AAC7C,MAAI,UAAU;AACV,OAAG,QAAQ,WAAW,IAAI;AAC9B,OAAK,SAAS,EAAE;AACpB;AACA,SAAS,kBAAkB,MAAM,QAAQ;AACrC,MAAI,UAAU;AACV,WAAO;AACX,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,MAAM,GAAG,OAAO,KAAK;AACvD,MAAI,QAAQ,KAAK,UAAU,cAAc,aAAa,IAAI,GAAG;AACzD,oBAAgB,MAAM,IAAI,cAAc,IAAI,GAAG,SAAS;AACxD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM,QAAQ;AACrC,MAAI,UAAU;AACV,WAAO;AACX,MAAI,MAAM,KAAK,MAAM,WAAW,cAAc;AAC9C,MAAI,eAAe;AACf,mBAAe,IAAI;AACvB,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,MAAM;AACxC,WAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK;AACrC,QAAI,OAAO,IAAI,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAK,CAAC;AACxD,QAAI,cAAc,aAAa,IAAI,GAAG;AAClC,UAAI,gBAAgB,IAAI,MAAM,QAAQ,KAClC,KAAK,IAAI,MAAM,SAAS,KAAK,OAAO,IAAI,MAAM,QAAQ,CAAC,KAAK,IAAI,MAAM;AACtE,mBAAW,KAAK,OAAO,IAAI,MAAM,KAAK;AAAA;AAEtC,mBAAW,KAAK,OAAO,CAAC;AAC5B;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,YAAY,MAAM;AAClB,oBAAgB,MAAM,cAAc,OAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,SAAS;AAC/E,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,kBAAkB,MAAM,KAAK,QAAQ,OAAO,YAAY;AAC7D,SAAO,oBAAoB,MAAM,iBAAiB,KAAK,QAAQ,KAAK,KAChE,KAAK,SAAS,eAAe,OAAK,EAAE,MAAM,KAAK,KAAK,CAAC,MACpD,aAAa,kBAAkB,MAAM,MAAM,IAAI,kBAAkB,MAAM,MAAM;AACtF;AACA,SAAS,kBAAkB,MAAM,KAAK,QAAQ,OAAO;AACjD,SAAO,oBAAoB,MAAM,uBAAuB,KAAK,QAAQ,KAAK,KACtE,KAAK,SAAS,qBAAqB,OAAK,EAAE,MAAM,KAAK,KAAK,CAAC;AACnE;AACA,SAAS,kBAAkB,MAAM,KAAK,QAAQ,OAAO;AACjD,SAAO,oBAAoB,MAAM,uBAAuB,KAAK,QAAQ,KAAK,KACtE,KAAK,SAAS,qBAAqB,OAAK,EAAE,MAAM,KAAK,KAAK,CAAC,KAC3D,mBAAmB,MAAM,QAAQ,KAAK;AAC9C;AACA,SAAS,mBAAmB,MAAM,QAAQ,OAAO;AAC7C,MAAI,MAAM,UAAU;AAChB,WAAO;AACX,MAAIP,OAAM,KAAK,MAAM;AACrB,MAAI,UAAU,IAAI;AACd,QAAIA,KAAI,eAAe;AACnB,sBAAgB,MAAM,cAAc,OAAOA,MAAK,GAAGA,KAAI,QAAQ,IAAI,GAAG,SAAS;AAC/E,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,MAAI,OAAOA,KAAI,QAAQ,MAAM;AAC7B,WAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK;AACrC,QAAI,OAAO,IAAI,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAK,CAAC;AACxD,QAAI,UAAU,KAAK,OAAO,CAAC;AAC3B,QAAI,KAAK;AACL,sBAAgB,MAAM,cAAc,OAAOA,MAAK,UAAU,GAAG,UAAU,IAAI,KAAK,QAAQ,IAAI,GAAG,SAAS;AAAA,aACnG,cAAc,aAAa,IAAI;AACpC,sBAAgB,MAAM,cAAc,OAAOA,MAAK,OAAO,GAAG,SAAS;AAAA;AAEnE;AACJ,WAAO;AAAA,EACX;AACJ;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,eAAe,IAAI;AAC9B;AACA,IAAM,qBAAqB,MAAM,YAAY;AAC7C,SAAS,YAAY,CAAC,MAAM,WAAW;AACnC,MAAI,QAAQ;AACZ,OAAK,MAAM,WAAW,MAAM;AAC5B,MAAI,UAAU,cAAc,IAAI;AAChC,MAAI,MAAM,KAAK,IAAI,GAAG,OAAO;AAC7B,MAAI,MAAM,KAAK,MAAM,UAAU,OAAO,OAAO,OAAO,OAAO,KAAK,MAAM,SAAS,KAAK,CAAC,MAAM,kBAAkB,KACzG,KAAK,MAAM,UAAU,UAAU,MAAM,QAAQ;AAC7C,QAAI,KAAK,MAAM,UAAU,QAAQ;AAC7B,aAAO;AAAA,aACF,KAAK,MAAM,UAAU,QAAQ;AAClC,aAAO;AAAA,EACf;AACA,OAAK,MAAM,YAAY,EAAE,MAAM,KAAK,GAAG,MAAM,SAAS,GAAG,MAAM,SAAS,MAAM,QAAQ,MAAM,OAAO;AACnG,MAAI,MAAM,KAAK,YAAY,YAAY,KAAK,CAAC;AAC7C,MAAI,CAAC;AACD;AACJ,MAAI,QAAQ,eAAe;AACvB,QAAI,KAAK,MAAM;AACX,WAAK,MAAM,UAAU,KAAK;AAC9B,SAAK,MAAM,YAAY,IAAI,UAAU,MAAM,KAAK,OAAO,CAAC,CAAC,OAAO;AAAA,EACpE,YACU,QAAQ,gBAAgB,oBAAoB,mBAAmB,MAAM,IAAI,KAAK,IAAI,QAAQ,KAAK,GAAG;AACxG,UAAM,eAAe;AAAA,EACzB,OACK;AACD,uBAAmB,MAAM,SAAS;AAAA,EACtC;AACJ;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM,KAAK,OAAO,SAAS;AACnC,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,uBAAuB;AAC5B,SAAK,YAAY;AACjB,SAAK,WAAW,KAAK,MAAM;AAC3B,SAAK,aAAa,CAAC,CAAC,MAAM,kBAAkB;AAC5C,SAAK,eAAe,MAAM;AAC1B,QAAI,YAAY;AAChB,QAAI,IAAI,SAAS,IAAI;AACjB,mBAAa,KAAK,MAAM,IAAI,OAAO,IAAI,MAAM;AAC7C,kBAAY,IAAI;AAAA,IACpB,OACK;AACD,UAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,IAAI,GAAG;AACzC,mBAAa,KAAK;AAClB,kBAAY,KAAK,QAAQ,KAAK,OAAO,IAAI;AAAA,IAC7C;AACA,UAAM,SAAS,UAAU,OAAO,MAAM;AACtC,UAAM,aAAa,SAAS,KAAK,QAAQ,YAAY,QAAQ,IAAI,IAAI;AACrE,SAAK,SAAS,cAAc,WAAW,IAAI,YAAY,IAAI,WAAW,MAAM;AAC5E,QAAI,EAAE,UAAU,IAAI,KAAK;AACzB,QAAI,MAAM,UAAU,KAChB,WAAW,KAAK,KAAK,aAAa,WAAW,KAAK,KAAK,eAAe,SACtE,qBAAqB,iBAAiB,UAAU,QAAQ,aAAa,UAAU,KAAK;AACpF,WAAK,YAAY;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS,CAAC,EAAE,KAAK,UAAU,CAAC,KAAK,OAAO;AAAA,QACxC,eAAe,CAAC,EAAE,KAAK,UAAU,SAAS,CAAC,KAAK,OAAO,aAAa,iBAAiB;AAAA,MACzF;AACJ,QAAI,KAAK,UAAU,KAAK,cAAc,KAAK,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAC3F,WAAK,KAAK,YAAY,KAAK;AAC3B,UAAI,KAAK,UAAU;AACf,aAAK,OAAO,YAAY;AAC5B,UAAI,KAAK,UAAU;AACf,mBAAW,MAAM;AACb,cAAI,KAAK,KAAK,MAAM,aAAa;AAC7B,iBAAK,OAAO,aAAa,mBAAmB,OAAO;AAAA,QAC3D,GAAG,EAAE;AACT,WAAK,KAAK,YAAY,MAAM;AAAA,IAChC;AACA,SAAK,KAAK,iBAAiB,WAAW,KAAK,KAAK,KAAK,GAAG,KAAK,IAAI,CAAC;AAClE,SAAK,KAAK,iBAAiB,aAAa,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,CAAC;AACxE,uBAAmB,MAAM,SAAS;AAAA,EACtC;AAAA,EACA,OAAO;AACH,SAAK,KAAK,KAAK,oBAAoB,WAAW,KAAK,EAAE;AACrD,SAAK,KAAK,KAAK,oBAAoB,aAAa,KAAK,IAAI;AACzD,QAAI,KAAK,aAAa,KAAK,QAAQ;AAC/B,WAAK,KAAK,YAAY,KAAK;AAC3B,UAAI,KAAK,UAAU;AACf,aAAK,OAAO,gBAAgB,WAAW;AAC3C,UAAI,KAAK,UAAU;AACf,aAAK,OAAO,gBAAgB,iBAAiB;AACjD,WAAK,KAAK,YAAY,MAAM;AAAA,IAChC;AACA,QAAI,KAAK;AACL,iBAAW,MAAM,eAAe,KAAK,IAAI,CAAC;AAC9C,SAAK,KAAK,MAAM,YAAY;AAAA,EAChC;AAAA,EACA,GAAG,OAAO;AACN,SAAK,KAAK;AACV,QAAI,CAAC,KAAK,KAAK,IAAI,SAAS,MAAM,MAAM;AACpC;AACJ,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,KAAK,MAAM,OAAO,KAAK;AAC5B,YAAM,KAAK,KAAK,YAAY,YAAY,KAAK,CAAC;AAClD,SAAK,mBAAmB,KAAK;AAC7B,QAAI,KAAK,gBAAgB,CAAC,KAAK;AAC3B,yBAAmB,KAAK,MAAM,SAAS;AAAA,IAC3C,WACS,kBAAkB,KAAK,MAAM,IAAI,KAAK,IAAI,QAAQ,OAAO,KAAK,UAAU,GAAG;AAChF,YAAM,eAAe;AAAA,IACzB,WACS,MAAM,UAAU,MACpB,KAAK;AAAA,IAED,UAAU,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQjD,UAAU,CAAC,KAAK,KAAK,MAAM,UAAU,WAClC,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,MAAM,UAAU,IAAI,GAAG,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,MAAM,UAAU,EAAE,CAAC,KAAK,IAAK;AAC/H,sBAAgB,KAAK,MAAM,UAAU,KAAK,KAAK,KAAK,MAAM,IAAI,QAAQ,IAAI,GAAG,CAAC,GAAG,SAAS;AAC1F,YAAM,eAAe;AAAA,IACzB,OACK;AACD,yBAAmB,KAAK,MAAM,SAAS;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,KAAK,OAAO;AACR,SAAK,mBAAmB,KAAK;AAC7B,uBAAmB,KAAK,MAAM,SAAS;AACvC,QAAI,MAAM,WAAW;AACjB,WAAK,KAAK;AAAA,EAClB;AAAA,EACA,mBAAmB,OAAO;AACtB,QAAI,CAAC,KAAK,iBAAiB,KAAK,IAAI,KAAK,MAAM,IAAI,MAAM,OAAO,IAAI,KAChE,KAAK,IAAI,KAAK,MAAM,IAAI,MAAM,OAAO,IAAI;AACzC,WAAK,eAAe;AAAA,EAC5B;AACJ;AACA,SAAS,aAAa,UAAQ;AAC1B,OAAK,MAAM,YAAY,KAAK,IAAI;AAChC,gBAAc,IAAI;AAClB,qBAAmB,MAAM,SAAS;AACtC;AACA,SAAS,YAAY,UAAQ;AACzB,OAAK,MAAM,YAAY,KAAK,IAAI;AAChC,qBAAmB,MAAM,SAAS;AACtC;AACA,SAAS,cAAc,UAAQ,cAAc,IAAI;AACjD,SAAS,oBAAoB,MAAM,OAAO;AACtC,MAAI,KAAK;AACL,WAAO;AAWX,MAAI,UAAU,KAAK,IAAI,MAAM,YAAY,KAAK,MAAM,kBAAkB,IAAI,KAAK;AAC3E,SAAK,MAAM,qBAAqB;AAChC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAM,qBAAqB,UAAU,MAAO;AAC5C,aAAa,mBAAmB,aAAa,oBAAoB,UAAQ;AACrE,MAAI,CAAC,KAAK,WAAW;AACjB,SAAK,YAAY,MAAM;AACvB,QAAI,EAAE,MAAM,IAAI,MAAM,OAAO,MAAM,UAAU;AAC7C,QAAI,MAAM,qBAAqB,kBAC1B,MAAM,eACF,CAAC,KAAK,cAAc,KAAK,gBAAgB,KAAK,WAAW,MAAM,KAAK,OAAK,EAAE,KAAK,KAAK,cAAc,KAAK,IAAK;AAElH,WAAK,aAAa,KAAK,MAAM,eAAe,KAAK,MAAM;AACvD,qBAAe,MAAM,IAAI;AACzB,WAAK,aAAa;AAAA,IACtB,OACK;AACD,qBAAe,MAAM,CAAC,MAAM,UAAU,KAAK;AAI3C,UAAI,SAAS,MAAM,UAAU,SAAS,KAAK,gBAAgB,CAAC,KAAK,cAAc,KAAK,WAAW,MAAM,QAAQ;AACzG,YAAI,MAAM,KAAK,kBAAkB;AACjC,iBAAS,OAAO,IAAI,WAAW,SAAS,IAAI,aAAa,QAAQ,KAAK,YAAY,KAAK,UAAU,KAAI;AACjG,cAAI,SAAS,SAAS,IAAI,KAAK,YAAY,KAAK,WAAW,SAAS,CAAC;AACrE,cAAI,CAAC;AACD;AACJ,cAAI,OAAO,YAAY,GAAG;AACtB,gBAAIQ,OAAM,KAAK,aAAa;AAC5B,gBAAIA;AACA,cAAAA,KAAI,SAAS,QAAQ,OAAO,UAAU,MAAM;AAChD;AAAA,UACJ,OACK;AACD,mBAAO;AACP,qBAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,MAAM,YAAY;AAAA,EAC3B;AACA,qBAAmB,MAAM,kBAAkB;AAC/C;AACA,aAAa,iBAAiB,CAAC,MAAM,UAAU;AAC3C,MAAI,KAAK,WAAW;AAChB,SAAK,MAAM,YAAY;AACvB,SAAK,MAAM,qBAAqB,MAAM;AACtC,SAAK,MAAM,4BAA4B,KAAK,YAAY,eAAe,EAAE,SAAS,KAAK,MAAM,gBAAgB;AAC7G,SAAK,MAAM,kBAAkB;AAC7B,QAAI,KAAK,MAAM;AACX,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,YAAY,MAAM,CAAC;AACzD,SAAK,MAAM;AACX,uBAAmB,MAAM,EAAE;AAAA,EAC/B;AACJ;AACA,SAAS,mBAAmB,MAAM,OAAO;AACrC,eAAa,KAAK,MAAM,gBAAgB;AACxC,MAAI,QAAQ;AACR,SAAK,MAAM,mBAAmB,WAAW,MAAM,eAAe,IAAI,GAAG,KAAK;AAClF;AACA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,KAAK,WAAW;AAChB,SAAK,MAAM,YAAY;AACvB,SAAK,MAAM,qBAAqB,yBAAyB;AAAA,EAC7D;AACA,SAAO,KAAK,MAAM,iBAAiB,SAAS;AACxC,SAAK,MAAM,iBAAiB,IAAI,EAAE,iBAAiB;AAC3D;AACA,SAAS,oBAAoB,MAAM;AAC/B,MAAI,MAAM,KAAK,kBAAkB;AACjC,MAAI,CAAC,IAAI;AACL,WAAO;AACX,MAAI,aAAa,iBAAiB,IAAI,WAAW,IAAI,WAAW;AAChE,MAAI,YAAY,gBAAgB,IAAI,WAAW,IAAI,WAAW;AAC9D,MAAI,cAAc,aAAa,cAAc,WAAW;AACpD,QAAI,YAAY,UAAU,YAAY,cAAc,KAAK,YAAY;AACrE,QAAI,cAAc,eAAe,aAAa;AAC1C,aAAO;AACX,QAAI,CAAC,aAAa,CAAC,UAAU,OAAO,UAAU,SAAS,GAAG;AACtD,aAAO;AAAA,IACX,WACS,KAAK,MAAM,mBAAmB,WAAW;AAC9C,UAAI,aAAa,WAAW;AAC5B,UAAI,EAAE,CAAC,cAAc,CAAC,WAAW,OAAO,WAAW,SAAS;AACxD,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO,cAAc;AACzB;AACA,SAAS,2BAA2B;AAChC,MAAI,QAAQ,SAAS,YAAY,OAAO;AACxC,QAAM,UAAU,SAAS,MAAM,IAAI;AACnC,SAAO,MAAM;AACjB;AAIA,SAAS,eAAe,MAAM,aAAa,OAAO;AAC9C,MAAI,WAAW,KAAK,YAAY,gBAAgB;AAC5C;AACJ,OAAK,YAAY,WAAW;AAC5B,mBAAiB,IAAI;AACrB,MAAI,cAAc,KAAK,WAAW,KAAK,QAAQ,OAAO;AAClD,QAAI,MAAM,iBAAiB,IAAI,GAAG,MAAM,KAAK,MAAM;AACnD,QAAI,OAAO,CAAC,IAAI,GAAG,GAAG;AAClB,WAAK,SAAS,KAAK,MAAM,GAAG,aAAa,GAAG,CAAC;AAAA,cACvC,KAAK,cAAc,eAAe,CAAC,IAAI,MAAM,KAAK,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC,EAAE;AACvF,WAAK,SAAS,KAAK,MAAM,GAAG,gBAAgB,CAAC;AAAA;AAE7C,WAAK,YAAY,KAAK,KAAK;AAC/B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM,KAAK;AAG5B,MAAI,CAAC,KAAK,IAAI;AACV;AACJ,MAAI,OAAO,KAAK,IAAI,WAAW,YAAY,SAAS,cAAc,KAAK,CAAC;AACxE,OAAK,YAAY,GAAG;AACpB,OAAK,MAAM,UAAU;AACrB,MAAI,MAAM,aAAa,GAAG,QAAQ,SAAS,YAAY;AACvD,QAAM,mBAAmB,GAAG;AAI5B,OAAK,IAAI,KAAK;AACd,MAAI,gBAAgB;AACpB,MAAI,SAAS,KAAK;AAClB,aAAW,MAAM;AACb,QAAI,KAAK;AACL,WAAK,WAAW,YAAY,IAAI;AACpC,SAAK,MAAM;AAAA,EACf,GAAG,EAAE;AACT;AAIA,IAAM,qBAAsB,MAAM,aAAa,MAC1C,OAAO,iBAAiB;AAC7B,SAAS,OAAO,aAAa,MAAM,CAAC,MAAM,WAAW;AACjD,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,MAAM,WAAW,MAAM,MAAM,QAAQ;AACpD,MAAI,IAAI;AACJ;AAEJ,MAAI,OAAO,qBAAqB,OAAO,MAAM;AAC7C,MAAI,QAAQ,IAAI,QAAQ,GAAG,EAAE,KAAK,KAAK,IAAI,sBAAsB,MAAM,KAAK;AAC5E,MAAI,MAAM;AACN,UAAM,eAAe;AACrB,SAAK,UAAU;AACf,SAAK,QAAQ,aAAa,IAAI,SAAS;AACvC,SAAK,QAAQ,cAAc,IAAI;AAAA,EACnC,OACK;AACD,gBAAY,MAAM,GAAG;AAAA,EACzB;AACA,MAAI;AACA,SAAK,SAAS,KAAK,MAAM,GAAG,gBAAgB,EAAE,eAAe,EAAE,QAAQ,WAAW,KAAK,CAAC;AAChG;AACA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,MAAM,aAAa,KAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,cAAc,IAAI,MAAM,QAAQ,aAAa;AACpH;AACA,SAAS,aAAa,MAAM,OAAO;AAC/B,MAAI,CAAC,KAAK,IAAI;AACV;AACJ,MAAI,YAAY,KAAK,MAAM,YAAY,KAAK,MAAM,UAAU,MAAM,OAAO,KAAK,KAAK;AACnF,MAAI,SAAS,KAAK,IAAI,WAAW,YAAY,SAAS,cAAc,YAAY,aAAa,KAAK,CAAC;AACnG,MAAI,CAAC;AACD,WAAO,kBAAkB;AAC7B,SAAO,MAAM,UAAU;AACvB,SAAO,MAAM;AACb,MAAI,QAAQ,KAAK,MAAM,YAAY,KAAK,MAAM,eAAe;AAC7D,aAAW,MAAM;AACb,SAAK,MAAM;AACX,QAAI,OAAO;AACP,aAAO,WAAW,YAAY,MAAM;AACxC,QAAI;AACA,cAAQ,MAAM,OAAO,OAAO,MAAM,OAAO,KAAK;AAAA;AAE9C,cAAQ,MAAM,OAAO,aAAa,OAAO,WAAW,OAAO,KAAK;AAAA,EACxE,GAAG,EAAE;AACT;AACA,SAAS,QAAQ,MAAM,MAAM,MAAM,aAAa,OAAO;AACnD,MAAI,QAAQ,mBAAmB,MAAM,MAAM,MAAM,aAAa,KAAK,MAAM,UAAU,KAAK;AACxF,MAAI,KAAK,SAAS,eAAe,OAAK,EAAE,MAAM,OAAO,SAAS,MAAM,KAAK,CAAC;AACtE,WAAO;AACX,MAAI,CAAC;AACD,WAAO;AACX,MAAI,aAAa,gBAAgB,KAAK;AACtC,MAAI,KAAK,aACH,KAAK,MAAM,GAAG,qBAAqB,YAAY,WAAW,IAC1D,KAAK,MAAM,GAAG,iBAAiB,KAAK;AAC1C,OAAK,SAAS,GAAG,eAAe,EAAE,QAAQ,SAAS,IAAI,EAAE,QAAQ,WAAW,OAAO,CAAC;AACpF,SAAO;AACX;AACA,SAAS,QAAQ,eAAe;AAC5B,MAAI,OAAO,cAAc,QAAQ,YAAY,KAAK,cAAc,QAAQ,MAAM;AAC9E,MAAI;AACA,WAAO;AACX,MAAI,OAAO,cAAc,QAAQ,eAAe;AAChD,SAAO,OAAO,KAAK,QAAQ,UAAU,GAAG,IAAI;AAChD;AACA,aAAa,QAAQ,CAAC,MAAM,WAAW;AACnC,MAAI,QAAQ;AAKZ,MAAI,KAAK,aAAa,CAAC;AACnB;AACJ,MAAI,OAAO,qBAAqB,OAAO,MAAM;AAC7C,MAAI,QAAQ,KAAK,MAAM,YAAY,KAAK,MAAM,eAAe;AAC7D,MAAI,QAAQ,QAAQ,MAAM,QAAQ,IAAI,GAAG,KAAK,QAAQ,WAAW,GAAG,OAAO,KAAK;AAC5E,UAAM,eAAe;AAAA;AAErB,iBAAa,MAAM,KAAK;AAChC;AACA,IAAM,WAAN,MAAe;AAAA,EACX,YAAY,OAAO,MAAM,MAAM;AAC3B,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,mBAAmB,MAAM,WAAW;AAC1C,SAAS,UAAU,MAAM,OAAO;AAC5B,MAAI,QAAQ,KAAK,SAAS,cAAc,UAAQ,CAAC,KAAK,KAAK,CAAC;AAC5D,SAAO,SAAS,OAAO,QAAQ,CAAC,MAAM,gBAAgB;AAC1D;AACA,SAAS,YAAY,CAAC,MAAM,WAAW;AACnC,MAAI,QAAQ;AACZ,MAAI,YAAY,KAAK,MAAM;AAC3B,MAAI;AACA,cAAU,KAAK;AACnB,MAAI,CAAC,MAAM;AACP;AACJ,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,MAAM,IAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,KAAK,CAAC;AAChE,MAAI;AACJ,MAAI,OAAO,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,eAAe,gBAAgB,IAAI,KAAK,IAAI,IAAI,IAAK;AAAA,WAC1F,aAAa,UAAU,WAAW;AACvC,WAAO,cAAc,OAAO,KAAK,MAAM,KAAK,UAAU,UAAU,GAAG;AAAA,EACvE,WACS,MAAM,UAAU,MAAM,OAAO,YAAY,GAAG;AACjD,QAAI,OAAO,KAAK,QAAQ,YAAY,MAAM,QAAQ,IAAI;AACtD,QAAI,QAAQ,KAAK,KAAK,KAAK,KAAK,aAAa,QAAQ,KAAK;AACtD,aAAO,cAAc,OAAO,KAAK,MAAM,KAAK,KAAK,SAAS;AAAA,EAClE;AACA,MAAI,gBAAgB,QAAQ,KAAK,MAAM,WAAW,QAAQ;AAC1D,MAAI,EAAE,KAAK,MAAM,MAAM,IAAI,sBAAsB,MAAM,YAAY;AAEnE,MAAI,CAAC,MAAM,aAAa,MAAM,UAAU,CAAC,UAAU,iBAAiB;AAChE,UAAM,aAAa,UAAU;AACjC,QAAM,aAAa,QAAQ,qBAAqB,SAAS,aAAa,IAAI,SAAS;AAEnF,QAAM,aAAa,gBAAgB;AACnC,MAAI,CAAC;AACD,UAAM,aAAa,QAAQ,cAAc,IAAI;AACjD,OAAK,WAAW,IAAI,SAAS,OAAO,UAAU,MAAM,KAAK,GAAG,IAAI;AACpE;AACA,SAAS,UAAU,UAAQ;AACvB,MAAI,WAAW,KAAK;AACpB,SAAO,WAAW,MAAM;AACpB,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW;AAAA,EACxB,GAAG,EAAE;AACT;AACA,aAAa,WAAW,aAAa,YAAY,CAAC,GAAG,MAAM,EAAE,eAAe;AAC5E,aAAa,OAAO,CAAC,MAAM,WAAW;AAClC,MAAI,QAAQ;AACZ,MAAI,WAAW,KAAK;AACpB,OAAK,WAAW;AAChB,MAAI,CAAC,MAAM;AACP;AACJ,MAAI,WAAW,KAAK,YAAY,YAAY,KAAK,CAAC;AAClD,MAAI,CAAC;AACD;AACJ,MAAI,SAAS,KAAK,MAAM,IAAI,QAAQ,SAAS,GAAG;AAChD,MAAI,QAAQ,YAAY,SAAS;AACjC,MAAI,OAAO;AACP,SAAK,SAAS,mBAAmB,OAAK;AAAE,cAAQ,EAAE,OAAO,IAAI;AAAA,IAAG,CAAC;AAAA,EACrE,OACK;AACD,YAAQ,mBAAmB,MAAM,QAAQ,MAAM,YAAY,GAAG,qBAAqB,OAAO,MAAM,aAAa,QAAQ,WAAW,GAAG,OAAO,MAAM;AAAA,EACpJ;AACA,MAAI,OAAO,CAAC,EAAE,YAAY,UAAU,MAAM,KAAK;AAC/C,MAAI,KAAK,SAAS,cAAc,OAAK,EAAE,MAAM,OAAO,SAAS,MAAM,OAAO,IAAI,CAAC,GAAG;AAC9E,UAAM,eAAe;AACrB;AAAA,EACJ;AACA,MAAI,CAAC;AACD;AACJ,QAAM,eAAe;AACrB,MAAI,YAAY,QAAQ,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,IAAI,OAAO;AAC9E,MAAI,aAAa;AACb,gBAAY,OAAO;AACvB,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,MAAM;AACN,QAAI,EAAE,KAAK,IAAI;AACf,QAAI;AACA,WAAK,QAAQ,EAAE;AAAA;AAEf,SAAG,gBAAgB;AAAA,EAC3B;AACA,MAAI,MAAM,GAAG,QAAQ,IAAI,SAAS;AAClC,MAAI,SAAS,MAAM,aAAa,KAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,cAAc;AACvF,MAAI,eAAe,GAAG;AACtB,MAAI;AACA,OAAG,iBAAiB,KAAK,KAAK,MAAM,QAAQ,UAAU;AAAA;AAEtD,OAAG,aAAa,KAAK,KAAK,KAAK;AACnC,MAAI,GAAG,IAAI,GAAG,YAAY;AACtB;AACJ,MAAI,OAAO,GAAG,IAAI,QAAQ,GAAG;AAC7B,MAAI,UAAU,cAAc,aAAa,MAAM,QAAQ,UAAU,KAC7D,KAAK,aAAa,KAAK,UAAU,WAAW,MAAM,QAAQ,UAAU,GAAG;AACvE,OAAG,aAAa,IAAI,cAAc,IAAI,CAAC;AAAA,EAC3C,OACK;AACD,QAAI,MAAM,GAAG,QAAQ,IAAI,SAAS;AAClC,OAAG,QAAQ,KAAK,GAAG,QAAQ,KAAK,SAAS,CAAC,EAAE,QAAQ,CAAC,OAAO,KAAK,UAAU,UAAU,MAAM,KAAK;AAChG,OAAG,aAAa,iBAAiB,MAAM,MAAM,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC;AAAA,EACrE;AACA,OAAK,MAAM;AACX,OAAK,SAAS,GAAG,QAAQ,WAAW,MAAM,CAAC;AAC/C;AACA,SAAS,QAAQ,UAAQ;AACrB,OAAK,MAAM,YAAY,KAAK,IAAI;AAChC,MAAI,CAAC,KAAK,SAAS;AACf,SAAK,YAAY,KAAK;AACtB,SAAK,IAAI,UAAU,IAAI,qBAAqB;AAC5C,SAAK,YAAY,MAAM;AACvB,SAAK,UAAU;AACf,eAAW,MAAM;AACb,UAAI,KAAK,WAAW,KAAK,SAAS,KAAK,CAAC,KAAK,YAAY,iBAAiB,GAAG,KAAK,kBAAkB,CAAC;AACjG,uBAAe,IAAI;AAAA,IAC3B,GAAG,EAAE;AAAA,EACT;AACJ;AACA,SAAS,OAAO,CAAC,MAAM,WAAW;AAC9B,MAAI,QAAQ;AACZ,MAAI,KAAK,SAAS;AACd,SAAK,YAAY,KAAK;AACtB,SAAK,IAAI,UAAU,OAAO,qBAAqB;AAC/C,SAAK,YAAY,MAAM;AACvB,QAAI,MAAM,iBAAiB,KAAK,IAAI,SAAS,MAAM,aAAa;AAC5D,WAAK,YAAY,iBAAiB,MAAM;AAC5C,SAAK,UAAU;AAAA,EACnB;AACJ;AACA,SAAS,cAAc,CAAC,MAAM,WAAW;AACrC,MAAI,QAAQ;AAKZ,MAAI,UAAU,WAAW,MAAM,aAAa,yBAAyB;AACjE,SAAK,YAAY,UAAU;AAC3B,QAAI,EAAE,eAAe,IAAI,KAAK;AAC9B,eAAW,MAAM;AACb,UAAI,KAAK,MAAM,kBAAkB;AAC7B;AAEJ,WAAK,IAAI,KAAK;AACd,WAAK,MAAM;AACX,UAAI,KAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC;AACrE;AACJ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM;AAE7B,UAAI,WAAW,QAAQ,MAAM;AACzB,aAAK,SAAS,KAAK,MAAM,GAAG,OAAO,QAAQ,MAAM,GAAG,QAAQ,GAAG,EAAE,eAAe,CAAC;AAAA,IACzF,GAAG,EAAE;AAAA,EACT;AACJ;AAEA,SAAS,QAAQ;AACb,WAAS,IAAI,IAAI,aAAa,IAAI;AAEtC,SAAS,YAAY,GAAG,GAAG;AACvB,MAAI,KAAK;AACL,WAAO;AACX,WAAS,KAAK;AACV,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AACZ,aAAO;AACf,WAAS,KAAK;AACV,QAAI,EAAE,KAAK;AACP,aAAO;AACf,SAAO;AACX;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAAY,OAAO,MAAM;AACrB,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ;AACpB,SAAK,OAAO,KAAK,KAAK,QAAQ;AAAA,EAClC;AAAA,EACA,IAAI,SAAS,MAAM,QAAQ,WAAW;AAClC,QAAI,EAAE,KAAK,QAAQ,IAAI,QAAQ,UAAU,KAAK,OAAO,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC;AACtF,WAAO,UAAU,OAAO,IAAI,WAAW,MAAM,QAAQ,MAAM,QAAQ,IAAI;AAAA,EAC3E;AAAA,EACA,QAAQ;AAAE,WAAO;AAAA,EAAM;AAAA,EACvB,GAAG,OAAO;AACN,WAAO,QAAQ,SACV,iBAAiB,gBACb,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,OAC1C,KAAK,SAAS,MAAM,SAAS,YAAY,KAAK,MAAM,MAAM,IAAI;AAAA,EAC9E;AAAA,EACA,QAAQ,MAAM;AACV,QAAI,KAAK,KAAK;AACV,WAAK,KAAK,QAAQ,IAAI;AAAA,EAC9B;AACJ;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAAY,OAAO,MAAM;AACrB,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,MAAM,QAAQ,WAAW;AAClC,QAAI,OAAO,QAAQ,IAAI,KAAK,OAAO,WAAW,KAAK,KAAK,iBAAiB,KAAK,CAAC,IAAI;AACnF,QAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,WAAW,KAAK,KAAK,eAAe,IAAI,EAAE,IAAI;AAC7E,WAAO,QAAQ,KAAK,OAAO,IAAI,WAAW,MAAM,IAAI,IAAI;AAAA,EAC5D;AAAA,EACA,MAAM,GAAG,MAAM;AAAE,WAAO,KAAK,OAAO,KAAK;AAAA,EAAI;AAAA,EAC7C,GAAG,OAAO;AACN,WAAO,QAAQ,SACV,iBAAiB,eAAc,YAAY,KAAK,OAAO,MAAM,KAAK,KAC/D,YAAY,KAAK,MAAM,MAAM,IAAI;AAAA,EAC7C;AAAA,EACA,OAAO,GAAG,MAAM;AAAE,WAAO,KAAK,gBAAgB;AAAA,EAAY;AAAA,EAC1D,UAAU;AAAA,EAAE;AAChB;AACA,IAAM,WAAN,MAAM,UAAS;AAAA,EACX,YAAY,OAAO,MAAM;AACrB,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,MAAM,QAAQ,WAAW;AAClC,QAAI,OAAO,QAAQ,UAAU,KAAK,OAAO,WAAW,CAAC;AACrD,QAAI,KAAK;AACL,aAAO;AACX,QAAI,KAAK,QAAQ,UAAU,KAAK,KAAK,WAAW,EAAE;AAClD,QAAI,GAAG,WAAW,GAAG,OAAO,KAAK;AAC7B,aAAO;AACX,WAAO,IAAI,WAAW,KAAK,MAAM,QAAQ,GAAG,MAAM,QAAQ,IAAI;AAAA,EAClE;AAAA,EACA,MAAM,MAAM,MAAM;AACd,QAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,KAAK,IAAI,GAAG;AAC3D,WAAO,UAAU,KAAK,QAAQ,EAAE,QAAQ,KAAK,MAAM,KAAK,GAAG,UAAU,SAAS,MAAM,YAAY,KAAK;AAAA,EACzG;AAAA,EACA,GAAG,OAAO;AACN,WAAO,QAAQ,SACV,iBAAiB,aAAY,YAAY,KAAK,OAAO,MAAM,KAAK,KAC7D,YAAY,KAAK,MAAM,MAAM,IAAI;AAAA,EAC7C;AAAA,EACA,UAAU;AAAA,EAAE;AAChB;AAMA,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA,EAIb,YAIA,MAKA,IAIA,MAAM;AACF,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,MAAM,IAAI;AACX,WAAO,IAAI,YAAW,MAAM,IAAI,KAAK,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO,SAAS,GAAG;AAClB,WAAO,KAAK,KAAK,GAAG,MAAM,IAAI,KAAK,KAAK,OAAO,UAAU,MAAM,QAAQ,KAAK,KAAK,UAAU,MAAM;AAAA,EACrG;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS,QAAQ,WAAW;AAC5B,WAAO,KAAK,KAAK,IAAI,SAAS,MAAM,QAAQ,SAAS;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,OAAO,KAAK,OAAO,MAAM;AAC5B,WAAO,IAAI,YAAW,KAAK,KAAK,IAAI,WAAW,OAAO,IAAI,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,MAAM,IAAI,OAAO,MAAM;AACjC,WAAO,IAAI,YAAW,MAAM,IAAI,IAAI,WAAW,OAAO,IAAI,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK,MAAM,IAAI,OAAO,MAAM;AAC/B,WAAO,IAAI,YAAW,MAAM,IAAI,IAAI,SAAS,OAAO,IAAI,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AAAE,WAAO,KAAK,KAAK;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,SAAS;AAAE,WAAO,KAAK,gBAAgB;AAAA,EAAY;AAAA;AAAA;AAAA;AAAA,EAIvD,IAAI,SAAS;AAAE,WAAO,KAAK,gBAAgB;AAAA,EAAY;AAC3D;AACA,IAAM,OAAO,CAAC;AAAd,IAAiB,SAAS,CAAC;AAO3B,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA,EAIhB,YAAY,OAAO,UAAU;AACzB,SAAK,QAAQ,MAAM,SAAS,QAAQ;AACpC,SAAK,WAAW,SAAS,SAAS,WAAW;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAOR,MAAK,aAAa;AAC5B,WAAO,YAAY,SAAS,UAAU,aAAaA,MAAK,GAAG,MAAM,IAAI;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,KAAK,OAAO,KAAK,WAAW;AACxB,QAAI,SAAS,CAAC;AACd,SAAK,UAAU,SAAS,OAAO,IAAI,OAAO,OAAO,OAAO,MAAM,KAAK,QAAQ,GAAG,SAAS;AACvF,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO,KAAK,QAAQ,QAAQ,WAAW;AAC7C,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,UAAI,KAAK,QAAQ,OAAO,KAAK,MAAM,UAAU,CAAC,aAAa,UAAU,KAAK,IAAI;AAC1E,eAAO,KAAK,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,KAAK,MAAM,CAAC;AAAA,IACnE;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAC9C,UAAI,KAAK,SAAS,CAAC,IAAI,OAAO,KAAK,SAAS,IAAI,CAAC,IAAI,OAAO;AACxD,YAAI,WAAW,KAAK,SAAS,CAAC,IAAI;AAClC,aAAK,SAAS,IAAI,CAAC,EAAE,UAAU,QAAQ,UAAU,MAAM,UAAU,QAAQ,SAAS,UAAU,SAAS;AAAA,MACzG;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAASA,MAAK,SAAS;AACvB,QAAI,QAAQ,SAAS,QAAQ,KAAK,UAAU;AACxC,aAAO;AACX,WAAO,KAAK,SAAS,SAASA,MAAK,GAAG,GAAG,WAAW,MAAM;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,SAAS,MAAM,QAAQ,WAAW,SAAS;AAChD,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,SAAS,KAAK,MAAM,CAAC,EAAE,IAAI,SAAS,QAAQ,SAAS;AACzD,UAAI,UAAU,OAAO,KAAK,MAAM,MAAM,MAAM;AACxC,SAAC,aAAa,WAAW,CAAC,IAAI,KAAK,MAAM;AAAA,eACpC,QAAQ;AACb,gBAAQ,SAAS,KAAK,MAAM,CAAC,EAAE,IAAI;AAAA,IAC3C;AACA,QAAI,KAAK,SAAS;AACd,aAAO,YAAY,KAAK,UAAU,YAAY,CAAC,GAAG,SAAS,MAAM,QAAQ,WAAW,OAAO;AAAA;AAE3F,aAAO,WAAW,IAAI,eAAc,SAAS,KAAK,KAAK,GAAG,IAAI,IAAI;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAIA,MAAK,aAAa;AAClB,QAAI,CAAC,YAAY;AACb,aAAO;AACX,QAAI,QAAQ;AACR,aAAO,eAAc,OAAOA,MAAK,WAAW;AAChD,WAAO,KAAK,SAASA,MAAK,aAAa,CAAC;AAAA,EAC5C;AAAA,EACA,SAASA,MAAK,aAAa,QAAQ;AAC/B,QAAI,UAAU,aAAa;AAC3B,IAAAA,KAAI,QAAQ,CAAC,WAAW,gBAAgB;AACpC,UAAI,aAAa,cAAc,QAAQ;AACvC,UAAI,EAAE,QAAQ,iBAAiB,aAAa,WAAW,UAAU;AAC7D;AACJ,UAAI,CAAC;AACD,mBAAW,KAAK,SAAS,MAAM;AACnC,aAAO,aAAa,SAAS,UAAU,SAAS,UAAU,IAAI;AAC1D,sBAAc;AAClB,UAAI,SAAS,UAAU,KAAK;AACxB,iBAAS,aAAa,CAAC,IAAI,SAAS,aAAa,CAAC,EAAE,SAAS,WAAW,OAAO,aAAa,CAAC;AAAA;AAE7F,iBAAS,OAAO,YAAY,GAAG,aAAa,cAAc,UAAU,UAAU,UAAU,OAAO,WAAW,aAAa,GAAG,MAAM,CAAC;AACrI,oBAAc;AAAA,IAClB,CAAC;AACD,QAAI,QAAQ,UAAU,aAAa,aAAa,WAAW,IAAI,aAAa,CAAC,MAAM;AACnF,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,CAAC,MAAM,CAAC,EAAE,KAAK,MAAMA,MAAK,MAAM,CAAC,CAAC;AAClC,cAAM,OAAO,KAAK,CAAC;AAC3B,WAAO,IAAI,eAAc,MAAM,SAAS,KAAK,MAAM,OAAO,KAAK,EAAE,KAAK,KAAK,IAAI,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,EACxH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,aAAa;AAChB,QAAI,YAAY,UAAU,KAAK,QAAQ;AACnC,aAAO;AACX,WAAO,KAAK,YAAY,aAAa,CAAC;AAAA,EAC1C;AAAA,EACA,YAAY,aAAa,QAAQ;AAC7B,QAAI,WAAW,KAAK,UAAU,QAAQ,KAAK;AAC3C,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AACzC,UAAI;AACJ,UAAI,OAAO,SAAS,CAAC,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,IAAI;AACxD,eAAS,IAAI,GAAG,MAAM,IAAI,YAAY,QAAQ;AAC1C,YAAI,OAAO,YAAY,CAAC,GAAG;AACvB,cAAI,KAAK,OAAO,QAAQ,KAAK,KAAK,IAAI;AAClC,wBAAY,CAAC,IAAI;AACjB,aAAC,UAAU,QAAQ,CAAC,IAAI,KAAK,IAAI;AAAA,UACrC;AAAA,QACJ;AACJ,UAAI,CAAC;AACD;AACJ,UAAI,YAAY,KAAK;AACjB,mBAAW,KAAK,SAAS,MAAM;AACnC,UAAI,UAAU,SAAS,IAAI,CAAC,EAAE,YAAY,OAAO,OAAO,CAAC;AACzD,UAAI,WAAW,OAAO;AAClB,iBAAS,IAAI,CAAC,IAAI;AAAA,MACtB,OACK;AACD,iBAAS,OAAO,GAAG,CAAC;AACpB,aAAK;AAAA,MACT;AAAA,IACJ;AACA,QAAI,MAAM;AACN,eAAS,IAAI,GAAG,MAAM,IAAI,YAAY,QAAQ;AAC1C,YAAI,OAAO,YAAY,CAAC,GAAG;AACvB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,gBAAI,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM,GAAG;AAC3B,kBAAI,SAAS,KAAK;AACd,wBAAQ,KAAK,MAAM,MAAM;AAC7B,oBAAM,OAAO,KAAK,CAAC;AAAA,YACvB;AAAA,QACR;AAAA;AACR,QAAI,YAAY,KAAK,YAAY,SAAS,KAAK;AAC3C,aAAO;AACX,WAAO,MAAM,UAAU,SAAS,SAAS,IAAI,eAAc,OAAO,QAAQ,IAAI;AAAA,EAClF;AAAA,EACA,SAAS,QAAQ,MAAM;AACnB,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,KAAK;AACL,aAAO,eAAc;AACzB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,UAAI,KAAK,SAAS,CAAC,KAAK,QAAQ;AAC5B,YAAI,KAAK,SAAS,CAAC,KAAK;AACpB,kBAAQ,KAAK,SAAS,IAAI,CAAC;AAC/B;AAAA,MACJ;AACJ,QAAI,QAAQ,SAAS,GAAG,MAAM,QAAQ,KAAK,QAAQ;AACnD,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,MAAM,KAAK,MAAM,CAAC;AACtB,UAAI,IAAI,OAAO,OAAO,IAAI,KAAK,SAAU,IAAI,gBAAgB,YAAa;AACtE,YAAI,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI;AAC3E,YAAI,OAAO;AACP,WAAC,UAAU,QAAQ,CAAC,IAAI,KAAK,IAAI,KAAK,MAAM,EAAE,CAAC;AAAA,MACvD;AAAA,IACJ;AACA,QAAI,OAAO;AACP,UAAI,WAAW,IAAI,eAAc,MAAM,KAAK,KAAK,GAAG,IAAI;AACxD,aAAO,QAAQ,IAAI,gBAAgB,CAAC,UAAU,KAAK,CAAC,IAAI;AAAA,IAC5D;AACA,WAAO,SAAS;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,EAAE,iBAAiB,mBACnB,KAAK,MAAM,UAAU,MAAM,MAAM,UACjC,KAAK,SAAS,UAAU,MAAM,SAAS;AACvC,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AACnC,UAAI,CAAC,KAAK,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM,CAAC,CAAC;AAChC,eAAO;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,UAAI,KAAK,SAAS,CAAC,KAAK,MAAM,SAAS,CAAC,KACpC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,SAAS,IAAI,CAAC,KAC5C,CAAC,KAAK,SAAS,IAAI,CAAC,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,CAAC;AAC9C,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM;AACT,WAAO,cAAc,KAAK,YAAY,IAAI,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,MAAM;AACd,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,KAAK,iBAAiB,CAAC,KAAK,MAAM,KAAK,WAAW,EAAE;AACpD,aAAO,KAAK;AAChB,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,EAAE,KAAK,MAAM,CAAC,EAAE,gBAAgB;AAChC,eAAO,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AAAA,EACA,WAAW,GAAG;AAAE,MAAE,IAAI;AAAA,EAAG;AAC7B;AAIA,cAAc,QAAQ,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;AAI9C,cAAc,gBAAgB;AAC9B,IAAM,QAAQ,cAAc;AAI5B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAClB,YAAY,SAAS;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,SAASA,MAAK;AACd,UAAM,cAAc,KAAK,QAAQ,IAAI,YAAU,OAAO,IAAI,SAASA,MAAK,MAAM,CAAC;AAC/E,WAAO,iBAAgB,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,SAAS,QAAQ,OAAO;AACpB,QAAI,MAAM;AACN,aAAO,cAAc;AACzB,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,UAAI,SAAS,KAAK,QAAQ,CAAC,EAAE,SAAS,QAAQ,KAAK;AACnD,UAAI,UAAU;AACV;AACJ,UAAI,kBAAkB;AAClB,gBAAQ,MAAM,OAAO,OAAO,OAAO;AAAA;AAEnC,cAAM,KAAK,MAAM;AAAA,IACzB;AACA,WAAO,iBAAgB,KAAK,KAAK;AAAA,EACrC;AAAA,EACA,GAAG,OAAO;AACN,QAAI,EAAE,iBAAiB,qBACnB,MAAM,QAAQ,UAAU,KAAK,QAAQ;AACrC,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ;AACrC,UAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,CAAC;AACpC,eAAO;AACf,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM;AACT,QAAI,QAAQ,SAAS;AACrB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,UAAI,SAAS,KAAK,QAAQ,CAAC,EAAE,YAAY,IAAI;AAC7C,UAAI,CAAC,OAAO;AACR;AACJ,UAAI,CAAC,QAAQ;AACT,iBAAS;AAAA,MACb,OACK;AACD,YAAI,QAAQ;AACR,mBAAS,OAAO,MAAM;AACtB,mBAAS;AAAA,QACb;AACA,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,iBAAO,KAAK,OAAO,CAAC,CAAC;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO,SAAS,cAAc,SAAS,SAAS,OAAO,KAAK,KAAK,CAAC,IAAI;AAAA,EAC1E;AAAA;AAAA;AAAA,EAGA,OAAO,KAAK,SAAS;AACjB,YAAQ,QAAQ,QAAQ;AAAA,MACpB,KAAK;AAAG,eAAO;AAAA,MACf,KAAK;AAAG,eAAO,QAAQ,CAAC;AAAA,MACxB;AAAS,eAAO,IAAI,iBAAgB,QAAQ,MAAM,OAAK,aAAa,aAAa,IAAI,UACjF,QAAQ,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,aAAa,gBAAgB,IAAI,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;AAAA,IAC1F;AAAA,EACJ;AAAA,EACA,WAAW,GAAG;AACV,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ;AACrC,WAAK,QAAQ,CAAC,EAAE,WAAW,CAAC;AAAA,EACpC;AACJ;AACA,SAAS,YAAY,aAAa,UAAU,SAAS,MAAM,QAAQ,WAAW,SAAS;AACnF,MAAI,WAAW,YAAY,MAAM;AAGjC,WAAS,IAAI,GAAG,aAAa,WAAW,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAClE,QAAI,QAAQ;AACZ,YAAQ,KAAK,CAAC,EAAE,QAAQ,CAAC,UAAU,QAAQ,UAAU,WAAW;AAC5D,UAAI,QAAS,SAAS,YAAa,SAAS;AAC5C,eAASC,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK,GAAG;AACzC,YAAI,MAAM,SAASA,KAAI,CAAC;AACxB,YAAI,MAAM,KAAK,WAAW,MAAM,aAAa;AACzC;AACJ,YAAI,QAAQ,SAASA,EAAC,IAAI,aAAa;AACvC,YAAI,UAAU,OAAO;AACjB,mBAASA,KAAI,CAAC,IAAI,YAAY,QAAQ,KAAK;AAAA,QAC/C,WACS,YAAY,cAAc,OAAO;AACtC,mBAASA,EAAC,KAAK;AACf,mBAASA,KAAI,CAAC,KAAK;AAAA,QACvB;AAAA,MACJ;AACA,eAAS;AAAA,IACb,CAAC;AACD,iBAAa,QAAQ,KAAK,CAAC,EAAE,IAAI,YAAY,EAAE;AAAA,EACnD;AAGA,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,QAAI,SAAS,IAAI,CAAC,IAAI,GAAG;AACrB,UAAI,SAAS,IAAI,CAAC,KAAK,IAAI;AACvB,sBAAc;AACd,iBAAS,IAAI,CAAC,IAAI;AAClB;AAAA,MACJ;AACA,UAAI,OAAO,QAAQ,IAAI,YAAY,CAAC,IAAI,SAAS,GAAG,YAAY,OAAO;AACvE,UAAI,YAAY,KAAK,aAAa,KAAK,QAAQ,MAAM;AACjD,sBAAc;AACd;AAAA,MACJ;AAEA,UAAI,KAAK,QAAQ,IAAI,YAAY,IAAI,CAAC,IAAI,WAAW,EAAE,GAAG,UAAU,KAAK;AACzE,UAAI,EAAE,OAAO,QAAQ,YAAY,IAAI,KAAK,QAAQ,UAAU,SAAS;AACrE,UAAI,YAAY,KAAK,WAAW,KAAK;AACrC,UAAI,aAAa,eAAe,aAAa,cAAc,UAAU,YAAY,SAAS;AACtF,YAAI,SAAS,SAAS,IAAI,CAAC,EACtB,SAAS,SAAS,WAAW,OAAO,GAAG,YAAY,CAAC,IAAI,YAAY,GAAG,OAAO;AACnF,YAAI,UAAU,OAAO;AACjB,mBAAS,CAAC,IAAI;AACd,mBAAS,IAAI,CAAC,IAAI;AAClB,mBAAS,IAAI,CAAC,IAAI;AAAA,QACtB,OACK;AACD,mBAAS,IAAI,CAAC,IAAI;AAClB,wBAAc;AAAA,QAClB;AAAA,MACJ,OACK;AACD,sBAAc;AAAA,MAClB;AAAA,IACJ;AAEJ,MAAI,aAAa;AACb,QAAI,cAAc,iCAAiC,UAAU,aAAa,UAAU,SAAS,QAAQ,WAAW,OAAO;AACvH,QAAI,QAAQ,UAAU,aAAa,MAAM,GAAG,OAAO;AACnD,eAAW,MAAM;AACjB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAI,SAAS,IAAI,CAAC,IAAI,GAAG;AACrB,iBAAS,OAAO,GAAG,CAAC;AACpB,aAAK;AAAA,MACT;AACJ,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,SAAS,QAAQ,KAAK,GAAG;AACtD,UAAI,OAAO,MAAM,SAAS,CAAC;AAC3B,aAAO,IAAI,SAAS,UAAU,SAAS,CAAC,IAAI;AACxC,aAAK;AACT,eAAS,OAAO,GAAG,GAAG,MAAM,SAAS,CAAC,GAAG,MAAM,SAAS,IAAI,CAAC,GAAG,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,IACzF;AAAA,EACJ;AACA,SAAO,IAAI,cAAc,SAAS,KAAK,KAAK,GAAG,QAAQ;AAC3D;AACA,SAAS,UAAU,OAAO,QAAQ;AAC9B,MAAI,CAAC,UAAU,CAAC,MAAM;AAClB,WAAO;AACX,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,OAAO,MAAM,CAAC;AAClB,WAAO,KAAK,IAAI,WAAW,KAAK,OAAO,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC/E;AACA,SAAO;AACX;AACA,SAAS,iCAAiC,UAAU,aAAa,aAAa,SAAS,QAAQ,WAAW,SAAS;AAE/G,WAAS,OAAO,KAAKQ,YAAW;AAC5B,aAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AACvC,UAAI,SAAS,IAAI,MAAM,CAAC,EAAE,IAAI,SAAS,QAAQA,UAAS;AACxD,UAAI;AACA,oBAAY,KAAK,MAAM;AAAA,eAClB,QAAQ;AACb,gBAAQ,SAAS,IAAI,MAAM,CAAC,EAAE,IAAI;AAAA,IAC1C;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC1C,aAAO,IAAI,SAAS,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC,IAAIA,aAAY,CAAC;AAAA,EACnE;AACA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,QAAI,SAAS,IAAI,CAAC,KAAK;AACnB,aAAO,SAAS,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,YAAY,CAAC;AAC9D,SAAO;AACX;AACA,SAAS,iBAAiB,OAAO,MAAM,QAAQ;AAC3C,MAAI,KAAK;AACL,WAAO;AACX,MAAI,MAAM,SAAS,KAAK,UAAU,QAAQ;AAC1C,WAAS,IAAI,GAAG,MAAM,IAAI,MAAM,QAAQ,KAAK;AACzC,SAAK,OAAO,MAAM,CAAC,MAAM,KAAK,OAAO,UAAU,KAAK,KAAK,KAAK;AAC1D,OAAC,UAAU,QAAQ,CAAC,IAAI,KAAK,IAAI;AACjC,YAAM,CAAC,IAAI;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,QAAI,MAAM,CAAC,KAAK;AACZ,aAAO,KAAK,MAAM,CAAC,CAAC;AAC5B,SAAO;AACX;AAKA,SAAS,UAAU,OAAO,MAAM,QAAQ,SAAS;AAC7C,MAAI,WAAW,CAAC,GAAG,WAAW;AAC9B,OAAK,QAAQ,CAAC,WAAW,eAAe;AACpC,QAAI,QAAQ,iBAAiB,OAAO,WAAW,aAAa,MAAM;AAClE,QAAI,OAAO;AACP,iBAAW;AACX,UAAI,UAAU,UAAU,OAAO,WAAW,SAAS,aAAa,GAAG,OAAO;AAC1E,UAAI,WAAW;AACX,iBAAS,KAAK,YAAY,aAAa,UAAU,UAAU,OAAO;AAAA,IAC1E;AAAA,EACJ,CAAC;AACD,MAAI,SAAS,UAAU,WAAW,aAAa,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,KAAK,KAAK;AAClF,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,QAAI,CAAC,OAAO,CAAC,EAAE,KAAK,MAAM,MAAM,OAAO,CAAC,CAAC,GAAG;AACxC,UAAI,QAAQ;AACR,gBAAQ,SAAS,OAAO,CAAC,EAAE,IAAI;AACnC,aAAO,OAAO,KAAK,CAAC;AAAA,IACxB;AACJ,SAAO,OAAO,UAAU,SAAS,SAAS,IAAI,cAAc,QAAQ,QAAQ,IAAI;AACpF;AAIA,SAAS,MAAM,GAAG,GAAG;AACjB,SAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AACvC;AAKA,SAAS,cAAc,OAAO;AAC1B,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK;AACzC,QAAI,OAAO,QAAQ,CAAC;AACpB,QAAI,KAAK,QAAQ,KAAK;AAClB,eAAS,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACzC,YAAI,OAAO,QAAQ,CAAC;AACpB,YAAI,KAAK,QAAQ,KAAK,MAAM;AACxB,cAAI,KAAK,MAAM,KAAK,IAAI;AACpB,gBAAI,WAAW;AACX,wBAAU,MAAM,MAAM;AAG1B,oBAAQ,CAAC,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AACzC,wBAAY,SAAS,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE,CAAC;AAAA,UAC3D;AACA;AAAA,QACJ,OACK;AACD,cAAI,KAAK,OAAO,KAAK,IAAI;AACrB,gBAAI,WAAW;AACX,wBAAU,MAAM,MAAM;AAG1B,oBAAQ,CAAC,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAC3C,wBAAY,SAAS,GAAG,KAAK,KAAK,KAAK,MAAM,KAAK,EAAE,CAAC;AAAA,UACzD;AACA;AAAA,QACJ;AAAA,MACJ;AAAA,EACR;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO,GAAG,MAAM;AACjC,SAAO,IAAI,MAAM,UAAU,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI;AAC/C;AACJ,QAAM,OAAO,GAAG,GAAG,IAAI;AAC3B;AAEA,SAAS,gBAAgB,MAAM;AAC3B,MAAI,QAAQ,CAAC;AACb,OAAK,SAAS,eAAe,OAAK;AAC9B,QAAI,SAAS,EAAE,KAAK,KAAK;AACzB,QAAI,UAAU,UAAU;AACpB,YAAM,KAAK,MAAM;AAAA,EACzB,CAAC;AACD,MAAI,KAAK;AACL,UAAM,KAAK,cAAc,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,cAAc,IAAI,CAAC,CAAC;AAC9E,SAAO,gBAAgB,KAAK,KAAK;AACrC;AAEA,IAAM,iBAAiB;AAAA,EACnB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,SAAS;AACb;AAEA,IAAM,cAAc,MAAM,cAAc;AACxC,IAAM,iBAAN,MAAqB;AAAA,EACjB,cAAc;AACV,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,IAAI,KAAK;AACL,SAAK,aAAa,IAAI;AACtB,SAAK,eAAe,IAAI;AACxB,SAAK,YAAY,IAAI;AACrB,SAAK,cAAc,IAAI;AAAA,EAC3B;AAAA,EACA,QAAQ;AACJ,SAAK,aAAa,KAAK,YAAY;AAAA,EACvC;AAAA,EACA,GAAG,KAAK;AACJ,WAAO,IAAI,cAAc,KAAK,cAAc,IAAI,gBAAgB,KAAK,gBACjE,IAAI,aAAa,KAAK,aAAa,IAAI,eAAe,KAAK;AAAA,EACnE;AACJ;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,MAAM,iBAAiB;AAC/B,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,SAAK,QAAQ,CAAC;AACd,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,mBAAmB,IAAI;AAC5B,SAAK,aAAa;AAClB,SAAK,8BAA8B;AACnC,SAAK,sBAAsB;AAC3B,SAAK,WAAW,OAAO,oBACnB,IAAI,OAAO,iBAAiB,eAAa;AACrC,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAClC,aAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAKhC,UAAI,MAAM,cAAc,MAAM,UAAU,KAAK,OAAK,EAAE,QAAQ,eAAe,EAAE,aAAa,UACtF,EAAE,QAAQ,mBAAmB,EAAE,SAAS,SAAS,EAAE,OAAO,UAAU,MAAM;AAC1E,aAAK,UAAU;AAAA;AAEf,aAAK,MAAM;AAAA,IACnB,CAAC;AACL,QAAI,aAAa;AACb,WAAK,aAAa,OAAK;AACnB,aAAK,MAAM,KAAK,EAAE,QAAQ,EAAE,QAAQ,MAAM,iBAAiB,UAAU,EAAE,UAAU,CAAC;AAClF,aAAK,UAAU;AAAA,MACnB;AAAA,IACJ;AACA,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAAA,EAC7D;AAAA,EACA,YAAY;AACR,QAAI,KAAK,eAAe;AACpB,WAAK,eAAe,OAAO,WAAW,MAAM;AAAE,aAAK,eAAe;AAAI,aAAK,MAAM;AAAA,MAAG,GAAG,EAAE;AAAA,EACjG;AAAA,EACA,aAAa;AACT,QAAI,KAAK,eAAe,IAAI;AACxB,aAAO,aAAa,KAAK,YAAY;AACrC,WAAK,eAAe;AACpB,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,UAAU;AACf,WAAK,SAAS,YAAY;AAC1B,WAAK,SAAS,QAAQ,KAAK,KAAK,KAAK,cAAc;AAAA,IACvD;AACA,QAAI,KAAK;AACL,WAAK,KAAK,IAAI,iBAAiB,4BAA4B,KAAK,UAAU;AAC9E,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,OAAO;AACH,QAAI,KAAK,UAAU;AACf,UAAI,OAAO,KAAK,SAAS,YAAY;AACrC,UAAI,KAAK,QAAQ;AACb,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAC7B,eAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC3B,eAAO,WAAW,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA,MAC5C;AACA,WAAK,SAAS,WAAW;AAAA,IAC7B;AACA,QAAI,KAAK;AACL,WAAK,KAAK,IAAI,oBAAoB,4BAA4B,KAAK,UAAU;AACjF,SAAK,oBAAoB;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACf,SAAK,KAAK,IAAI,cAAc,iBAAiB,mBAAmB,KAAK,iBAAiB;AAAA,EAC1F;AAAA,EACA,sBAAsB;AAClB,SAAK,KAAK,IAAI,cAAc,oBAAoB,mBAAmB,KAAK,iBAAiB;AAAA,EAC7F;AAAA,EACA,2BAA2B;AACvB,SAAK,8BAA8B;AACnC,eAAW,MAAM,KAAK,8BAA8B,OAAO,EAAE;AAAA,EACjE;AAAA,EACA,oBAAoB;AAChB,QAAI,CAAC,qBAAqB,KAAK,IAAI;AAC/B;AACJ,QAAI,KAAK;AACL,aAAO,eAAe,KAAK,IAAI;AAInC,QAAI,MAAM,cAAc,MAAM,CAAC,KAAK,KAAK,MAAM,UAAU,OAAO;AAC5D,UAAI,MAAM,KAAK,KAAK,kBAAkB;AAEtC,UAAI,IAAI,aAAa,qBAAqB,IAAI,WAAW,IAAI,aAAa,IAAI,YAAY,IAAI,YAAY;AACtG,eAAO,KAAK,UAAU;AAAA,IAC9B;AACA,SAAK,MAAM;AAAA,EACf;AAAA,EACA,kBAAkB;AACd,SAAK,iBAAiB,IAAI,KAAK,KAAK,kBAAkB,CAAC;AAAA,EAC3D;AAAA,EACA,sBAAsB,KAAK;AACvB,QAAI,CAAC,IAAI;AACL,aAAO;AACX,QAAI,YAAY,oBAAI,OAAK;AACzB,aAAS,OAAO,IAAI,WAAW,MAAM,OAAO,WAAW,IAAI;AACvD,gBAAU,IAAI,IAAI;AACtB,aAAS,OAAO,IAAI,YAAY,MAAM,OAAO,WAAW,IAAI;AACxD,UAAI,UAAU,IAAI,IAAI,GAAG;AACrB,oBAAY;AACZ;AAAA,MACJ;AACJ,QAAI,OAAO,aAAa,KAAK,KAAK,QAAQ,YAAY,SAAS;AAC/D,QAAI,QAAQ,KAAK,eAAe;AAAA,MAC5B,MAAM;AAAA,MACN,QAAQ,UAAU,YAAY,IAAI,UAAU,aAAa;AAAA,IAC7D,CAAC,GAAG;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK;AACL,eAAS,OAAO,KAAK,SAAS,YAAY;AACtC,aAAK,MAAM,KAAK,GAAG;AAC3B,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,CAAC,KAAK,WAAW,KAAK,eAAe;AACrC;AACJ,QAAI,YAAY,KAAK,eAAe;AACpC,QAAI,UAAU;AACV,WAAK,QAAQ,CAAC;AAClB,QAAI,MAAM,KAAK,kBAAkB;AACjC,QAAI,SAAS,CAAC,KAAK,+BAA+B,CAAC,KAAK,iBAAiB,GAAG,GAAG,KAAK,qBAAqB,IAAI,KAAK,CAAC,KAAK,sBAAsB,GAAG;AACjJ,QAAI,OAAO,IAAI,KAAK,IAAI,WAAW,OAAO,QAAQ,CAAC;AACnD,QAAI,KAAK,UAAU;AACf,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,YAAI,SAAS,KAAK,iBAAiB,UAAU,CAAC,GAAG,KAAK;AACtD,YAAI,QAAQ;AACR,iBAAO,OAAO,IAAI,OAAO,OAAO,KAAK,IAAI,OAAO,MAAM,IAAI;AAC1D,eAAK,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,IAAI,EAAE;AAChD,cAAI,OAAO;AACP,uBAAW;AAAA,QACnB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,SAAS,MAAM,QAAQ;AACvB,UAAI,MAAM,MAAM,OAAO,OAAK,EAAE,YAAY,IAAI;AAC9C,UAAI,IAAI,UAAU,GAAG;AACjB,YAAI,CAAC,GAAG,CAAC,IAAI;AACb,YAAI,EAAE,cAAc,EAAE,WAAW,cAAc,EAAE;AAC7C,YAAE,OAAO;AAAA;AAET,YAAE,OAAO;AAAA,MACjB,OACK;AACD,YAAI,EAAE,UAAU,IAAI,KAAK;AACzB,iBAAS,MAAM,KAAK;AAChB,cAAI,SAAS,GAAG;AAChB,cAAI,UAAU,OAAO,YAAY,SAAS,CAAC,aAAa,YAAY,MAAM,SAAS,KAAK;AACpF,eAAG,OAAO;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,UAAU;AAId,QAAI,OAAO,KAAK,UAAU,KAAK,MAAM,YAAY,KAAK,IAAI,IAAI,OAC1D,KAAK,IAAI,KAAK,MAAM,WAAW,KAAK,MAAM,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,OACzE,mBAAmB,GAAG,MAAM,UAAU,iBAAiB,IAAI,MAC3D,QAAQ,GAAG,UAAU,KAAK,KAAK,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG;AAC1D,WAAK,MAAM,YAAY;AACvB,qBAAe,IAAI;AACnB,WAAK,iBAAiB,IAAI,GAAG;AAC7B,WAAK,kBAAkB;AAAA,IAC3B,WACS,OAAO,MAAM,QAAQ;AAC1B,UAAI,OAAO,IAAI;AACX,aAAK,QAAQ,UAAU,MAAM,EAAE;AAC/B,iBAAS,IAAI;AAAA,MACjB;AACA,WAAK,gBAAgB,MAAM,IAAI,UAAU,KAAK;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC7B,aAAK,YAAY,KAAK,KAAK;AAAA,eACtB,CAAC,KAAK,iBAAiB,GAAG,GAAG;AAClC,uBAAe,IAAI;AACvB,WAAK,iBAAiB,IAAI,GAAG;AAAA,IACjC;AAAA,EACJ;AAAA,EACA,iBAAiB,KAAK,OAAO;AAEzB,QAAI,MAAM,QAAQ,IAAI,MAAM,IAAI;AAC5B,aAAO;AACX,QAAI,OAAO,KAAK,KAAK,QAAQ,YAAY,IAAI,MAAM;AACnD,QAAI,IAAI,QAAQ,iBACX,QAAQ,KAAK,KAAK,WAAW,IAAI,iBAAiB;AAAA,IAE9C,IAAI,iBAAiB,WAAW,CAAC,IAAI,YAAY,CAAC,IAAI,OAAO,aAAa,OAAO;AACtF,aAAO;AACX,QAAI,CAAC,QAAQ,KAAK,eAAe,GAAG;AAChC,aAAO;AACX,QAAI,IAAI,QAAQ,aAAa;AACzB,eAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC5C,YAAI,OAAO,IAAI,WAAW,CAAC;AAC3B,cAAM,KAAK,IAAI;AACf,YAAI,KAAK,YAAY;AACjB,eAAK,sBAAsB;AAAA,MACnC;AACA,UAAI,KAAK,cAAc,KAAK,cAAc,KAAK,OAAO,CAAC,KAAK,WAAW,SAAS,IAAI,MAAM;AACtF,eAAO,EAAE,MAAM,KAAK,WAAW,IAAI,KAAK,SAAS;AACrD,UAAI,OAAO,IAAI,iBAAiB,OAAO,IAAI;AAC3C,UAAI,MAAM,cAAc,MAAM,IAAI,WAAW,QAAQ;AAGjD,iBAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC5C,cAAI,EAAE,iBAAiB,YAAY,IAAI,IAAI,WAAW,CAAC;AACvD,cAAI,CAAC,mBAAmB,MAAM,UAAU,QAAQ,KAAK,IAAI,YAAY,eAAe,IAAI;AACpF,mBAAO;AACX,cAAI,CAAC,eAAe,MAAM,UAAU,QAAQ,KAAK,IAAI,YAAY,WAAW,IAAI;AAC5E,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,UAAI,aAAa,QAAQ,KAAK,cAAc,IAAI,SAC1C,SAAS,IAAI,IAAI,IAAI;AAC3B,UAAI,OAAO,KAAK,gBAAgB,IAAI,QAAQ,YAAY,EAAE;AAC1D,UAAI,WAAW,QAAQ,KAAK,cAAc,IAAI,SACxC,SAAS,IAAI,IAAI,IAAI,OAAO,WAAW;AAC7C,UAAI,KAAK,KAAK,gBAAgB,IAAI,QAAQ,UAAU,CAAC;AACrD,aAAO,EAAE,MAAM,GAAG;AAAA,IACtB,WACS,IAAI,QAAQ,cAAc;AAC/B,aAAO,EAAE,MAAM,KAAK,aAAa,KAAK,QAAQ,IAAI,KAAK,WAAW,KAAK,OAAO;AAAA,IAClF,OACK;AACD,WAAK,sBAAsB,IAAI;AAC/B,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX,IAAI,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,QAKT,UAAU,IAAI,OAAO,aAAa,IAAI;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAI,aAAa,oBAAI,QAAQ;AAC7B,IAAI,iBAAiB;AACrB,SAAS,SAAS,MAAM;AACpB,MAAI,WAAW,IAAI,IAAI;AACnB;AACJ,aAAW,IAAI,MAAM,IAAI;AACzB,MAAI,CAAC,UAAU,UAAU,UAAU,EAAE,QAAQ,iBAAiB,KAAK,GAAG,EAAE,UAAU,MAAM,IAAI;AACxF,SAAK,wBAAwB;AAC7B,QAAI;AACA;AACJ,YAAQ,MAAM,EAAE,0KAA0K;AAC1L,qBAAiB;AAAA,EACrB;AACJ;AACA,SAAS,sBAAsB,MAAM,OAAO;AACxC,MAAI,aAAa,MAAM,gBAAgB,eAAe,MAAM;AAC5D,MAAI,YAAY,MAAM,cAAc,cAAc,MAAM;AACxD,MAAI,gBAAgB,KAAK,SAAS,KAAK,MAAM,UAAU,MAAM;AAI7D,MAAI,qBAAqB,cAAc,MAAM,cAAc,QAAQ,WAAW,WAAW;AACrF,KAAC,YAAY,cAAc,WAAW,WAAW,IAAI,CAAC,WAAW,aAAa,YAAY,YAAY;AAC1G,SAAO,EAAE,YAAY,cAAc,WAAW,YAAY;AAC9D;AAGA,SAAS,2BAA2B,MAAM,WAAW;AACjD,MAAI,UAAU,mBAAmB;AAC7B,QAAI,QAAQ,UAAU,kBAAkB,KAAK,IAAI,EAAE,CAAC;AACpD,QAAI;AACA,aAAO,sBAAsB,MAAM,KAAK;AAAA,EAChD;AACA,MAAI;AACJ,WAAS,KAAK,OAAO;AACjB,UAAM,eAAe;AACrB,UAAM,yBAAyB;AAC/B,YAAQ,MAAM,gBAAgB,EAAE,CAAC;AAAA,EACrC;AAMA,OAAK,IAAI,iBAAiB,eAAe,MAAM,IAAI;AACnD,WAAS,YAAY,QAAQ;AAC7B,OAAK,IAAI,oBAAoB,eAAe,MAAM,IAAI;AACtD,SAAO,QAAQ,sBAAsB,MAAM,KAAK,IAAI;AACxD;AACA,SAAS,YAAY,MAAM,MAAM;AAC7B,WAAS,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,YAAY;AAChE,QAAI,OAAO,KAAK,QAAQ,YAAY,GAAG,IAAI;AAC3C,QAAI,QAAQ,KAAK,KAAK;AAClB,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAOA,SAAS,aAAa,MAAM,OAAO,KAAK;AACpC,MAAI,EAAE,MAAM,QAAQ,YAAY,UAAU,MAAM,GAAG,IAAI,KAAK,QAAQ,WAAW,OAAO,GAAG;AACzF,MAAI,SAAS,KAAK,kBAAkB;AACpC,MAAI;AACJ,MAAI,SAAS,OAAO;AACpB,MAAI,UAAU,KAAK,IAAI,SAAS,OAAO,YAAY,IAAI,SAAS,OAAO,UAAU,GAAG;AAChF,WAAO,CAAC,EAAE,MAAM,QAAQ,QAAQ,OAAO,aAAa,CAAC;AACrD,QAAI,CAAC,mBAAmB,MAAM;AAC1B,WAAK,KAAK,EAAE,MAAM,OAAO,WAAW,QAAQ,OAAO,YAAY,CAAC;AAAA,EACxE;AAGA,MAAI,UAAU,KAAK,MAAM,gBAAgB,GAAG;AACxC,aAAS,MAAM,UAAU,MAAM,YAAY,OAAO;AAC9C,UAAI,OAAO,OAAO,WAAW,MAAM,CAAC,GAAG,OAAO,KAAK;AACnD,UAAI,KAAK,YAAY,QAAQ,CAAC,MAAM;AAChC,mBAAW;AACX;AAAA,MACJ;AACA,UAAI,CAAC,QAAQ,KAAK;AACd;AAAA,IACR;AAAA,EACJ;AACA,MAAI,WAAW,KAAK,MAAM;AAC1B,MAAI,SAAS,KAAK,SAAS,WAAW,KAAK,UAAU,WAAW,KAAK,MAAM,MAAM;AACjF,MAAI,QAAQ,SAAS,QAAQ,IAAI;AACjC,MAAI,MAAM,MAAMT,OAAM,OAAO,MAAM,QAAQ;AAAA,IACvC,SAAS,MAAM;AAAA,IACf,UAAU,MAAM,OAAO,eAAe,MAAM,MAAM,CAAC;AAAA,IACnD,SAAS;AAAA,IACT,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,oBAAoB,MAAM,OAAO,KAAK,cAAc,QAAQ,SAAS;AAAA,IACrE,eAAe;AAAA,IACf;AAAA,IACA,SAAS;AAAA,EACb,CAAC;AACD,MAAI,QAAQ,KAAK,CAAC,EAAE,OAAO,MAAM;AAC7B,QAAIU,UAAS,KAAK,CAAC,EAAE,KAAK,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;AACpD,QAAI,QAAQ;AACR,aAAOA;AACX,UAAM,EAAE,QAAQA,UAAS,MAAM,MAAM,OAAO,KAAK;AAAA,EACrD;AACA,SAAO,EAAE,KAAAV,MAAK,KAAK,MAAM,GAAG;AAChC;AACA,SAAS,aAAa,KAAK;AACvB,MAAI,OAAO,IAAI;AACf,MAAI,MAAM;AACN,WAAO,KAAK,UAAU;AAAA,EAC1B,WACS,IAAI,YAAY,QAAQ,IAAI,YAAY;AAI7C,QAAI,UAAU,aAAa,KAAK,IAAI,WAAW,QAAQ,GAAG;AACtD,UAAI,OAAO,SAAS,cAAc,KAAK;AACvC,WAAK,YAAY,SAAS,cAAc,IAAI,CAAC;AAC7C,aAAO,EAAE,KAAK;AAAA,IAClB,WACS,IAAI,WAAW,aAAa,OAAO,UAAU,gBAAgB,KAAK,IAAI,WAAW,QAAQ,GAAG;AACjG,aAAO,EAAE,QAAQ,KAAK;AAAA,IAC1B;AAAA,EACJ,WACS,IAAI,YAAY,SAAS,IAAI,aAAa,kBAAkB,GAAG;AACpE,WAAO,EAAE,QAAQ,KAAK;AAAA,EAC1B;AACA,SAAO;AACX;AACA,IAAM,WAAW;AACjB,SAAS,cAAc,MAAM,MAAM,IAAI,UAAU,YAAY;AACzD,MAAI,gBAAgB,KAAK,MAAM,8BAA8B,KAAK,YAAY,KAAK,MAAM,gBAAgB;AACzG,OAAK,MAAM,4BAA4B;AACvC,MAAI,OAAO,GAAG;AACV,QAAI,SAAS,KAAK,MAAM,oBAAoB,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,sBAAsB;AAC/F,QAAI,SAAS,iBAAiB,MAAM,MAAM;AAC1C,QAAI,UAAU,CAAC,KAAK,MAAM,UAAU,GAAG,MAAM,GAAG;AAC5C,UAAI,UAAU,WACV,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,MAAM,mBAC/D,KAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,SAAS,IAAI,OAAO,CAAC,CAAC;AAClE;AACJ,UAAI,KAAK,KAAK,MAAM,GAAG,aAAa,MAAM;AAC1C,UAAI,UAAU;AACV,WAAG,QAAQ,WAAW,IAAI;AAAA,eACrB,UAAU;AACf,WAAG,eAAe;AACtB,UAAI;AACA,WAAG,QAAQ,eAAe,aAAa;AAC3C,WAAK,SAAS,EAAE;AAAA,IACpB;AACA;AAAA,EACJ;AACA,MAAI,UAAU,KAAK,MAAM,IAAI,QAAQ,IAAI;AACzC,MAAI,SAAS,QAAQ,YAAY,EAAE;AACnC,SAAO,QAAQ,OAAO,SAAS,CAAC;AAChC,OAAK,KAAK,MAAM,IAAI,QAAQ,EAAE,EAAE,MAAM,SAAS,CAAC;AAChD,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,QAAQ,aAAa,MAAM,MAAM,EAAE;AACvC,MAAIA,OAAM,KAAK,MAAM,KAAK,UAAUA,KAAI,MAAM,MAAM,MAAM,MAAM,EAAE;AAClE,MAAI,cAAc;AAElB,MAAI,KAAK,MAAM,gBAAgB,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,MAAM,iBAAiB;AAC/E,mBAAe,KAAK,MAAM,UAAU;AACpC,oBAAgB;AAAA,EACpB,OACK;AACD,mBAAe,KAAK,MAAM,UAAU;AACpC,oBAAgB;AAAA,EACpB;AACA,OAAK,MAAM,cAAc;AACzB,MAAI,SAAS,SAAS,QAAQ,SAAS,MAAM,IAAI,SAAS,MAAM,MAAM,cAAc,aAAa;AACjG,MAAI;AACA,SAAK,MAAM;AACf,OAAK,OAAO,KAAK,MAAM,eAAe,KAAK,IAAI,IAAI,OAAO,YACtD,WAAW,KAAK,OAAK,EAAE,YAAY,KAAK,CAAC,SAAS,KAAK,EAAE,QAAQ,CAAC,MACjE,CAAC,UAAU,OAAO,QAAQ,OAAO,SAClC,KAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,SAAS,IAAI,OAAO,CAAC,CAAC,GAAG;AACrE,SAAK,MAAM,eAAe;AAC1B;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ;AACT,QAAI,YAAY,eAAe,iBAAiB,CAAC,IAAI,SAAS,IAAI,MAAM,WAAW,IAAI,OAAO,KAC1F,CAAC,KAAK,aAAa,EAAE,MAAM,OAAO,MAAM,IAAI,UAAU,MAAM,IAAI,OAAO;AACvE,eAAS,EAAE,OAAO,IAAI,MAAM,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG;AAAA,IAC3D,OACK;AACD,UAAI,MAAM,KAAK;AACX,YAAIQ,OAAM,iBAAiB,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AAC1D,YAAIA,QAAO,CAACA,KAAI,GAAG,KAAK,MAAM,SAAS,GAAG;AACtC,cAAI,KAAK,KAAK,MAAM,GAAG,aAAaA,IAAG;AACvC,cAAI;AACA,eAAG,QAAQ,eAAe,aAAa;AAC3C,eAAK,SAAS,EAAE;AAAA,QACpB;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,EACJ;AAIA,MAAI,KAAK,MAAM,UAAU,OAAO,KAAK,MAAM,UAAU,MACjD,OAAO,SAAS,OAAO,QACvB,KAAK,MAAM,qBAAqB,eAAe;AAC/C,QAAI,OAAO,QAAQ,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,KAAK,MAAM,UAAU,OAAO,KACxF,KAAK,MAAM,UAAU,QAAQ,MAAM,MAAM;AACzC,aAAO,QAAQ,KAAK,MAAM,UAAU;AAAA,IACxC,WACS,OAAO,OAAO,KAAK,MAAM,UAAU,MAAM,OAAO,QAAQ,KAAK,MAAM,UAAU,KAAK,KACvF,KAAK,MAAM,UAAU,MAAM,MAAM,IAAI;AACrC,aAAO,QAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AACjD,aAAO,OAAO,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA,EACJ;AAIA,MAAI,MAAM,cAAc,MAAM,OAAO,QAAQ,OAAO,QAAQ,KACxD,OAAO,QAAQ,OAAO,SAAS,OAAO,QAAQ,MAAM,QACpD,MAAM,IAAI,YAAY,OAAO,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ,MAAM,OAAO,CAAC,KAAK,MAAW;AAClG,WAAO;AACP,WAAO;AACP,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,MAAM,IAAI,eAAe,OAAO,QAAQ,MAAM,IAAI;AAC9D,MAAI,MAAM,MAAM,IAAI,eAAe,OAAO,OAAO,MAAM,IAAI;AAC3D,MAAI,SAASR,KAAI,QAAQ,OAAO,KAAK;AACrC,MAAI,eAAe,MAAM,WAAW,GAAG,KAAK,MAAM,OAAO,iBAAiB,OAAO,IAAI,KAAK,OAAO;AACjG,MAAI;AAGJ,OAAM,OAAO,KAAK,MAAM,eAAe,KAAK,IAAI,IAAI,QAC/C,CAAC,gBAAgB,WAAW,KAAK,OAAK,EAAE,YAAY,SAAS,EAAE,YAAY,GAAG,MAC9E,CAAC,gBAAgB,MAAM,MAAM,MAAM,IAAI,QAAQ,SAC3C,CAAC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,OAAO,kBACzC,CAAC,KAAK,KAAK,MAAM,IAAI,YAAY,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,MAC3D,UAAU,UAAU,SAAS,MAAM,IAAI,QAAQ,MAAM,MAAM,CAAC,GAAG,GAAG,IAAI,MACvE,QAAQ,OAAO,MAAM,QACzB,KAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,SAAS,IAAI,OAAO,CAAC,CAAC,GAAG;AACrE,SAAK,MAAM,eAAe;AAC1B;AAAA,EACJ;AAEA,MAAI,KAAK,MAAM,UAAU,SAAS,OAAO,SACrC,mBAAmBA,MAAK,OAAO,OAAO,OAAO,MAAM,OAAO,GAAG,KAC7D,KAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,GAAG;AACxE,QAAI,WAAW;AACX,WAAK,YAAY,yBAAyB;AAC9C;AAAA,EACJ;AAIA,MAAI,UAAU,OAAO,QAAQ,OAAO;AAChC,SAAK,MAAM,mBAAmB,KAAK,IAAI;AAS3C,MAAI,WAAW,CAAC,gBAAgB,MAAM,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,gBAAgB,KAAK,MAAM,SAAS,IAAI,SACxG,MAAM,OAAO,MAAM,IAAI,UAAU,MAAM,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO,MAAM;AAClF,WAAO,QAAQ;AACf,UAAM,MAAM,IAAI,eAAe,OAAO,OAAO,MAAM,IAAI;AACvD,eAAW,MAAM;AACb,WAAK,SAAS,iBAAiB,SAAU,GAAG;AAAE,eAAO,EAAE,MAAM,SAAS,IAAI,OAAO,CAAC;AAAA,MAAG,CAAC;AAAA,IAC1F,GAAG,EAAE;AAAA,EACT;AACA,MAAI,SAAS,OAAO,OAAO,OAAO,OAAO;AACzC,MAAI,OAAO,CAAC,SAAS;AACjB,QAAI,KAAK,QAAQ,KAAK,MAAM,GAAG,QAAQ,QAAQ,MAAM,MAAM,IAAI,MAAM,OAAO,QAAQ,MAAM,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;AACzH,QAAI,MAAM,KAAK;AACX,UAAIQ,OAAM,iBAAiB,MAAM,GAAG,KAAK,MAAM,GAAG;AAMlD,UAAIA,QAAO,EAAE,UAAU,KAAK,aAAaA,KAAI,UACxC,OAAO,SAAS,OAAO,QAAQ,KAAK,MAAM,mBAAmB,KAAK,IAAI,IAAI,SAC1EA,KAAI,QAAQ,UAAUA,KAAI,QAAQ,GAAG,QAAQ,IAAI,IAAI,IAAI,MAC1D,MAAMA,KAAI,SAASA,KAAI,QAAQ;AAC/B,WAAG,aAAaA,IAAG;AAAA,IAC3B;AACA,QAAI;AACA,SAAG,QAAQ,eAAe,aAAa;AAC3C,WAAO,GAAG,eAAe;AAAA,EAC7B;AACA,MAAI;AACJ,MAAI,cAAc;AACd,QAAI,MAAM,OAAO,IAAI,KAAK;AAGtB,UAAI,MAAM,cAAc,MAAM,MAAM,gBAAgB,GAAG;AACnD,aAAK,YAAY,yBAAyB;AAC1C,mBAAW,MAAM,eAAe,IAAI,GAAG,EAAE;AAAA,MAC7C;AACA,UAAI,KAAK,KAAK,KAAK,MAAM,GAAG,OAAO,QAAQ,IAAI,CAAC;AAChD,UAAI,QAAQR,KAAI,QAAQ,OAAO,KAAK,EAAE,YAAYA,KAAI,QAAQ,OAAO,IAAI,CAAC;AAC1E,UAAI;AACA,WAAG,YAAY,KAAK;AACxB,WAAK,SAAS,EAAE;AAAA,IACpB;AAAA;AAAA,MAEA,OAAO,QAAQ,OAAO,SACjB,aAAa,aAAa,MAAM,OAAO,QAAQ,IAAI,MAAM,cAAc,IAAI,YAAY,GAAG,OAAO,OAAO,QAAQ,IAAI,OAAO,cAAc,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA,MAAI;AAC3K,UAAI,KAAK,KAAK,KAAK,MAAM,EAAE;AAC3B,UAAI,WAAW,QAAQ;AACnB,WAAG,QAAQ,QAAQ,MAAM,WAAW,IAAI;AAAA;AAExC,WAAG,WAAW,QAAQ,MAAM,WAAW,IAAI;AAC/C,WAAK,SAAS,EAAE;AAAA,IACpB,WACS,MAAM,OAAO,MAAM,MAAM,MAAM,CAAC,EAAE,UAAU,MAAM,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,aAAa,IAAI,IAAI;AAE1G,UAAI,OAAO,MAAM,OAAO,YAAY,MAAM,cAAc,IAAI,YAAY;AACxE,UAAI,QAAQ,MAAM,KAAK,KAAK,MAAM,GAAG,WAAW,MAAM,QAAQ,IAAI,CAAC;AACnE,UAAI,CAAC,KAAK,SAAS,mBAAmB,OAAK,EAAE,MAAM,QAAQ,MAAM,MAAM,KAAK,CAAC;AACzE,aAAK,SAAS,MAAM,CAAC;AAAA,IAC7B;AAAA,EACJ,OACK;AACD,SAAK,SAAS,KAAK,CAAC;AAAA,EACxB;AACJ;AACA,SAAS,iBAAiB,MAAMA,MAAK,WAAW;AAC5C,MAAI,KAAK,IAAI,UAAU,QAAQ,UAAU,IAAI,IAAIA,KAAI,QAAQ;AACzD,WAAO;AACX,SAAO,iBAAiB,MAAMA,KAAI,QAAQ,UAAU,MAAM,GAAGA,KAAI,QAAQ,UAAU,IAAI,CAAC;AAC5F;AAIA,SAAS,aAAa,KAAK,MAAM;AAC7B,MAAI,WAAW,IAAI,WAAW,OAAO,YAAY,KAAK,WAAW;AACjE,MAAI,QAAQ,UAAU,UAAU,WAAW,MAAM,MAAM;AACvD,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAClC,YAAQ,UAAU,CAAC,EAAE,cAAc,KAAK;AAC5C,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACjC,cAAU,SAAS,CAAC,EAAE,cAAc,OAAO;AAC/C,MAAI,MAAM,UAAU,KAAK,QAAQ,UAAU,GAAG;AAC1C,WAAO,MAAM,CAAC;AACd,WAAO;AACP,aAAS,CAAC,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC;AAAA,EAC1D,WACS,MAAM,UAAU,KAAK,QAAQ,UAAU,GAAG;AAC/C,WAAO,QAAQ,CAAC;AAChB,WAAO;AACP,aAAS,CAAC,SAAS,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK,CAAC;AAAA,EAC/D,OACK;AACD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,YAAY;AACjC,YAAQ,KAAK,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;AACtC,MAAI,SAAS,KAAK,OAAO,EAAE,GAAG,GAAG;AAC7B,WAAO,EAAE,MAAM,KAAK;AAC5B;AACA,SAAS,mBAAmB,KAAK,OAAO,KAAK,WAAW,SAAS;AAC7D;AAAA;AAAA,IACA,MAAM,SAAS,QAAQ,MAAM,UAAU;AAAA,IAEnC,sBAAsB,WAAW,MAAM,KAAK,IAAI,QAAQ;AAAA;AACxD,WAAO;AACX,MAAI,SAAS,IAAI,QAAQ,KAAK;AAE9B,MAAI,CAAC,UAAU,OAAO,aAAa;AAC/B,QAAI,QAAQ,OAAO;AACnB,WAAO,SAAS,QAAQ,OAAO,QAAQ,MAAM;AAAA,EACjD;AAEA,MAAI,OAAO,eAAe,OAAO,OAAO,QAAQ,QAAQ,CAAC,OAAO,OAAO;AACnE,WAAO;AACX,MAAI,QAAQ,IAAI,QAAQ,sBAAsB,QAAQ,MAAM,IAAI,CAAC;AAEjE,MAAI,CAAC,MAAM,OAAO,eAAe,MAAM,MAAM,OACzC,sBAAsB,OAAO,MAAM,KAAK,IAAI;AAC5C,WAAO;AAEX,SAAO,UAAU,OAAO,QAAQ,IAAI,UAAU,YAAY,EAAE,GAAG,MAAM,OAAO,OAAO;AACvF;AACA,SAAS,sBAAsB,MAAM,SAAS,SAAS;AACnD,MAAI,QAAQ,KAAK,OAAO,MAAM,UAAU,KAAK,IAAI,IAAI,KAAK;AAC1D,SAAO,QAAQ,MAAM,WAAW,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,aAAa;AACpF;AACA;AACA,cAAU;AAAA,EACd;AACA,MAAI,SAAS;AACT,QAAI,OAAO,KAAK,KAAK,KAAK,EAAE,WAAW,KAAK,WAAW,KAAK,CAAC;AAC7D,WAAO,QAAQ,CAAC,KAAK,QAAQ;AACzB,aAAO,KAAK;AACZ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,SAAS,GAAG,GAAG,KAAK,cAAc,eAAe;AACtD,MAAI,QAAQ,EAAE,cAAc,GAAG,GAAG;AAClC,MAAI,SAAS;AACT,WAAO;AACX,MAAI,EAAE,GAAG,MAAM,GAAG,KAAK,IAAI,EAAE,YAAY,GAAG,MAAM,EAAE,MAAM,MAAM,EAAE,IAAI;AACtE,MAAI,iBAAiB,OAAO;AACxB,QAAI,SAAS,KAAK,IAAI,GAAG,QAAQ,KAAK,IAAI,MAAM,IAAI,CAAC;AACrD,oBAAgB,OAAO,SAAS;AAAA,EACpC;AACA,MAAI,OAAO,SAAS,EAAE,OAAO,EAAE,MAAM;AACjC,QAAI,OAAO,gBAAgB,SAAS,gBAAgB,OAAO,QAAQ,eAAe;AAClF,aAAS;AACT,QAAI,SAAS,QAAQ,EAAE,QAAQ,gBAAgB,EAAE,YAAY,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC9E,eAAS,OAAO,IAAI;AACxB,WAAO,SAAS,OAAO;AACvB,WAAO;AAAA,EACX,WACS,OAAO,OAAO;AACnB,QAAI,OAAO,gBAAgB,SAAS,gBAAgB,OAAO,QAAQ,eAAe;AAClF,aAAS;AACT,QAAI,SAAS,QAAQ,EAAE,QAAQ,gBAAgB,EAAE,YAAY,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC9E,eAAS,OAAO,IAAI;AACxB,WAAO,SAAS,OAAO;AACvB,WAAO;AAAA,EACX;AACA,SAAO,EAAE,OAAO,MAAM,KAAK;AAC/B;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,IAAI,UAAU;AACd,WAAO;AACX,MAAI,IAAI,IAAI,WAAW,CAAC,GAAG,IAAI,IAAI,WAAW,CAAC;AAC/C,SAAO,KAAK,SAAU,KAAK,SAAU,KAAK,SAAU,KAAK;AAC7D;AAKA,IAAM,uBAAuB;AAI7B,IAAM,mBAAmB;AAMzB,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQb,YAAY,OAAO,OAAO;AACtB,SAAK,QAAQ;AAIb,SAAK,UAAU;AAIf,SAAK,cAAc;AACnB,SAAK,UAAU;AAIf,SAAK,aAAa;AAIlB,SAAK,gBAAgB;AAIrB,SAAK,uBAAuB;AAI5B,SAAK,QAAQ,IAAI;AACjB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,cAAc,CAAC;AAMpB,SAAK,wBAAwB;AAM7B,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ,MAAM;AACnB,SAAK,gBAAgB,MAAM,WAAW,CAAC;AACvC,SAAK,cAAc,QAAQ,mBAAmB;AAC9C,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,MAAO,SAAS,MAAM,SAAU,SAAS,cAAc,KAAK;AACjE,QAAI,OAAO;AACP,UAAI,MAAM;AACN,cAAM,YAAY,KAAK,GAAG;AAAA,eACrB,OAAO,SAAS;AACrB,cAAM,KAAK,GAAG;AAAA,eACT,MAAM;AACX,aAAK,UAAU;AAAA,IACvB;AACA,SAAK,WAAW,YAAY,IAAI;AAChC,wBAAoB,IAAI;AACxB,SAAK,YAAY,eAAe,IAAI;AACpC,SAAK,UAAU,YAAY,KAAK,MAAM,KAAK,eAAe,IAAI,GAAG,gBAAgB,IAAI,GAAG,KAAK,KAAK,IAAI;AACtG,SAAK,cAAc,IAAI,YAAY,MAAM,CAAC,MAAM,IAAI,UAAU,UAAU,cAAc,MAAM,MAAM,IAAI,UAAU,KAAK,CAAC;AACtH,SAAK,YAAY,MAAM;AACvB,cAAU,IAAI;AACd,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AAAE,WAAO,KAAK,MAAM;AAAA,EAAW;AAAA;AAAA;AAAA;AAAA,EAI/C,IAAI,QAAQ;AACR,QAAI,KAAK,OAAO,SAAS,KAAK,OAAO;AACjC,UAAI,OAAO,KAAK;AAChB,WAAK,SAAS,CAAC;AACf,eAAS,QAAQ;AACb,aAAK,OAAO,IAAI,IAAI,KAAK,IAAI;AACjC,WAAK,OAAO,QAAQ,KAAK;AAAA,IAC7B;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACV,QAAI,MAAM,mBAAmB,KAAK,OAAO;AACrC,sBAAgB,IAAI;AACxB,QAAI,YAAY,KAAK;AACrB,SAAK,SAAS;AACd,QAAI,MAAM,SAAS;AACf,YAAM,QAAQ,QAAQ,mBAAmB;AACzC,WAAK,gBAAgB,MAAM;AAAA,IAC/B;AACA,SAAK,iBAAiB,MAAM,OAAO,SAAS;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACZ,QAAI,UAAU,CAAC;AACf,aAAS,QAAQ,KAAK;AAClB,cAAQ,IAAI,IAAI,KAAK,OAAO,IAAI;AACpC,YAAQ,QAAQ,KAAK;AACrB,aAAS,QAAQ;AACb,cAAQ,IAAI,IAAI,MAAM,IAAI;AAC9B,SAAK,OAAO,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACf,SAAK,iBAAiB,OAAO,KAAK,MAAM;AAAA,EAC5C;AAAA,EACA,iBAAiB,OAAO,WAAW;AAC/B,QAAI;AACJ,QAAI,OAAO,KAAK,OAAO,SAAS,OAAO,YAAY;AAGnD,QAAI,MAAM,eAAe,KAAK,WAAW;AACrC,uBAAiB,IAAI;AACrB,kBAAY;AAAA,IAChB;AACA,SAAK,QAAQ;AACb,QAAI,iBAAiB,KAAK,WAAW,MAAM,WAAW,KAAK,OAAO,WAAW,UAAU;AACvF,QAAI,kBAAkB,KAAK,OAAO,WAAW,UAAU,WAAW,KAAK,OAAO,aAAa,UAAU,WAAW;AAC5G,UAAI,YAAY,eAAe,IAAI;AACnC,UAAI,iBAAiB,WAAW,KAAK,SAAS,GAAG;AAC7C,aAAK,YAAY;AACjB,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,QAAI,kBAAkB,UAAU,mBAAmB,KAAK,OAAO,iBAAiB;AAC5E,sBAAgB,IAAI;AAAA,IACxB;AACA,SAAK,WAAW,YAAY,IAAI;AAChC,wBAAoB,IAAI;AACxB,QAAI,YAAY,gBAAgB,IAAI,GAAG,YAAY,eAAe,IAAI;AACtE,QAAI,SAAS,KAAK,WAAW,MAAM,WAAW,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,UAClE,MAAM,oBAAoB,KAAK,oBAAoB,iBAAiB;AAC1E,QAAI,YAAY,UAAU,CAAC,KAAK,QAAQ,YAAY,MAAM,KAAK,WAAW,SAAS;AACnF,QAAI,aAAa,CAAC,MAAM,UAAU,GAAG,KAAK,SAAS;AAC/C,kBAAY;AAChB,QAAI,eAAe,UAAU,cAAc,aAAa,KAAK,IAAI,MAAM,kBAAkB,QAAQ,eAAe,IAAI;AACpH,QAAI,WAAW;AACX,WAAK,YAAY,KAAK;AAMtB,UAAI,iBAAiB,cAAc,MAAM,WAAW,CAAC,KAAK,aACtD,CAAC,KAAK,UAAU,SAAS,CAAC,MAAM,UAAU,SAAS,wBAAwB,KAAK,WAAW,MAAM,SAAS;AAC9G,UAAI,WAAW;AAKX,YAAI,eAAe,SAAU,KAAK,cAAc,KAAK,kBAAkB,EAAE,YAAa;AACtF,YAAI,KAAK;AACL,eAAK,MAAM,kBAAkB,oBAAoB,IAAI;AACzD,YAAI,UAAU,CAAC,KAAK,QAAQ,OAAO,MAAM,KAAK,WAAW,WAAW,IAAI,GAAG;AACvE,eAAK,QAAQ,gBAAgB,SAAS;AACtC,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU,YAAY,MAAM,KAAK,WAAW,WAAW,KAAK,KAAK,IAAI;AAAA,QAC9E;AACA,YAAI,gBAAgB,CAAC,KAAK;AACtB,2BAAiB;AAAA,MACzB;AAKA,UAAI,kBACA,EAAE,KAAK,MAAM,aAAa,KAAK,YAAY,iBAAiB,GAAG,KAAK,kBAAkB,CAAC,KACnF,mBAAmB,IAAI,IAAI;AAC/B,uBAAe,MAAM,cAAc;AAAA,MACvC,OACK;AACD,0BAAkB,MAAM,MAAM,SAAS;AACvC,aAAK,YAAY,gBAAgB;AAAA,MACrC;AACA,WAAK,YAAY,MAAM;AAAA,IAC3B;AACA,SAAK,kBAAkB,IAAI;AAC3B,UAAM,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG;AAC7F,WAAK,kBAAkB,KAAK,UAAU,IAAI;AAC9C,QAAI,UAAU,SAAS;AACnB,WAAK,IAAI,YAAY;AAAA,IACzB,WACS,UAAU,gBAAgB;AAC/B,WAAK,kBAAkB;AAAA,IAC3B,WACS,cAAc;AACnB,qBAAe,YAAY;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAChB,QAAI,WAAW,KAAK,kBAAkB,EAAE;AACxC,QAAI,CAAC,YAAY,CAAC,KAAK,IAAI,SAAS,SAAS,YAAY,IAAI,WAAW,SAAS,UAAU,EAAG;AAAA,aACrF,KAAK,SAAS,2BAA2B,OAAK,EAAE,IAAI,CAAC,EAAG;AAAA,aACxD,KAAK,MAAM,qBAAqB,eAAe;AACpD,UAAI,SAAS,KAAK,QAAQ,YAAY,KAAK,MAAM,UAAU,IAAI;AAC/D,UAAI,OAAO,YAAY;AACnB,2BAAmB,MAAM,OAAO,sBAAsB,GAAG,QAAQ;AAAA,IACzE,OACK;AACD,yBAAmB,MAAM,KAAK,YAAY,KAAK,MAAM,UAAU,MAAM,CAAC,GAAG,QAAQ;AAAA,IACrF;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,QAAI;AACJ,WAAO,OAAO,KAAK,YAAY,IAAI;AAC/B,UAAI,KAAK;AACL,aAAK,QAAQ;AAAA,EACzB;AAAA,EACA,kBAAkB,WAAW;AACzB,QAAI,CAAC,aAAa,UAAU,WAAW,KAAK,MAAM,WAAW,KAAK,iBAAiB,KAAK,mBAAmB;AACvG,WAAK,oBAAoB,KAAK;AAC9B,WAAK,mBAAmB;AACxB,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,QAAQ,KAAK;AAChD,YAAI,SAAS,KAAK,cAAc,CAAC;AACjC,YAAI,OAAO,KAAK;AACZ,eAAK,YAAY,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC;AAAA,MACpD;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK;AAChD,YAAI,SAAS,KAAK,MAAM,QAAQ,CAAC;AACjC,YAAI,OAAO,KAAK;AACZ,eAAK,YAAY,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC;AAAA,MACpD;AAAA,IACJ,OACK;AACD,eAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC9C,YAAI,aAAa,KAAK,YAAY,CAAC;AACnC,YAAI,WAAW;AACX,qBAAW,OAAO,MAAM,SAAS;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB,UAAU,MAAM;AAC9B,QAAI,MAAM,SAAS,MAAM,QAAQ;AACjC,QAAI,KAAK,MAAM,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI,MAAM;AAC7C,cAAQ,IAAI;AAAA,IAChB,OACK;AACD,UAAI,WAAW,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,OAAO,KAAK,IAAI,QAAQ;AAC1E,UAAI,QAAQ,WAAW,KAAK,KAAK,MAAM,IAAI,OAAO,QAAQ;AAC1D,UAAI,SAAS,IAAI;AACb,gBAAQ;AAAA,IAChB;AACA,SAAK,WAAW,IAAI,SAAS,SAAS,OAAO,SAAS,MAAM,QAAQ,IAAI,SAAY,cAAc,OAAO,KAAK,MAAM,KAAK,KAAK,CAAC;AAAA,EACnI;AAAA,EACA,SAAS,UAAU,GAAG;AAClB,QAAI,OAAO,KAAK,UAAU,KAAK,OAAO,QAAQ,GAAG;AACjD,QAAI,QAAQ,SAAS,QAAQ,IAAI,EAAE,IAAI,IAAI;AACvC,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,QAAQ,KAAK;AAChD,UAAIW,QAAO,KAAK,cAAc,CAAC,EAAE,MAAM,QAAQ;AAC/C,UAAIA,SAAQ,SAAS,QAAQ,IAAI,EAAEA,KAAI,IAAIA;AACvC,eAAO;AAAA,IACf;AACA,QAAI,UAAU,KAAK,MAAM;AACzB,QAAI;AACA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAIA,QAAO,QAAQ,CAAC,EAAE,MAAM,QAAQ;AACpC,YAAIA,SAAQ,SAAS,QAAQ,IAAI,EAAEA,KAAI,IAAIA;AACvC,iBAAO;AAAA,MACf;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAIP,QAAI,IAAI;AAGJ,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,QAAQ,KAAK;AACb,eAAO;AACX,UAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,SAAS,IAAI;AAChC,eAAO;AACX,aAAO,QAAQ,KAAK,OAAO,QAAQ,KAAK,IAAI,SAAS,IAAI,GAAG;AACxD,YAAI,KAAK,mBAAmB;AACxB,iBAAO;AACX,eAAO,KAAK;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AACA,WAAO,KAAK,KAAK,iBAAiB,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,SAAK,YAAY,KAAK;AACtB,QAAI,KAAK;AACL,yBAAmB,KAAK,GAAG;AAC/B,mBAAe,IAAI;AACnB,SAAK,YAAY,MAAM;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACP,QAAI,SAAS,KAAK;AAClB,QAAI,UAAU;AACV,eAAS,SAAS,KAAK,IAAI,YAAY,QAAQ,SAAS,OAAO,YAAY;AACvE,YAAI,OAAO,YAAY,KAAM,OAAO,YAAY,MAAM,OAAO,MAAO;AAChE,cAAI,CAAC,OAAO;AACR,mBAAO,eAAe,MAAM,EAAE,eAAe,MAAM,OAAO,cAAc,aAAa;AACzF,iBAAO,KAAK,QAAQ;AAAA,QACxB;AAAA,MACJ;AACJ,WAAO,UAAU;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACT,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,QAAQ;AAChB,WAAO,YAAY,MAAM,MAAM;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,KAAK,OAAO,GAAG;AACvB,WAAO,YAAY,MAAM,KAAK,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,KAAK,OAAO,GAAG;AACpB,WAAO,KAAK,QAAQ,WAAW,KAAK,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,KAAK;AACT,QAAI,OAAO,KAAK,QAAQ,OAAO,GAAG;AAClC,WAAO,OAAO,KAAK,UAAU;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,MAAM,QAAQ,OAAO,IAAI;AAC9B,QAAI,MAAM,KAAK,QAAQ,WAAW,MAAM,QAAQ,IAAI;AACpD,QAAI,OAAO;AACP,YAAM,IAAI,WAAW,oCAAoC;AAC7D,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,KAAK,OAAO;AACvB,WAAO,eAAe,MAAM,SAAS,KAAK,OAAO,GAAG;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,MAAM,OAAO;AACnB,WAAO,QAAQ,MAAM,IAAI,MAAM,OAAO,SAAS,IAAI,eAAe,OAAO,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM,OAAO;AACnB,WAAO,QAAQ,MAAM,MAAM,MAAM,MAAM,SAAS,IAAI,eAAe,OAAO,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,OAAO;AACzB,WAAO,sBAAsB,MAAM,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACN,QAAI,CAAC,KAAK;AACN;AACJ,iBAAa,IAAI;AACjB,SAAK,mBAAmB;AACxB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,OAAO,KAAK,MAAM,KAAK,CAAC,GAAG,gBAAgB,IAAI,GAAG,IAAI;AACnE,WAAK,IAAI,cAAc;AAAA,IAC3B,WACS,KAAK,IAAI,YAAY;AAC1B,WAAK,IAAI,WAAW,YAAY,KAAK,GAAG;AAAA,IAC5C;AACA,SAAK,QAAQ,QAAQ;AACrB,SAAK,UAAU;AACf,qBAAiB;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,cAAc;AACd,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO;AACjB,WAAO,cAAc,MAAM,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAChB,QAAI,MAAM,KAAK,aAAa;AAC5B,QAAI,CAAC;AACD,aAAO,EAAE,WAAW,MAAM,aAAa,GAAG,YAAY,MAAM,cAAc,EAAE;AAChF,WAAO,UAAU,KAAK,KAAK,aAAa,MACpC,kBAAkB,KAAK,IAAI,aAAa,KAAK,KAAK,OAAO,2BAA2B,MAAM,GAAG,KAAK;AAAA,EAC1G;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,WAAO,KAAK,KAAK,aAAa;AAAA,EAClC;AACJ;AACA,WAAW,UAAU,WAAW,SAAU,IAAI;AAC1C,MAAI,sBAAsB,KAAK,OAAO;AACtC,MAAI;AACA,wBAAoB,KAAK,MAAM,EAAE;AAAA;AAEjC,SAAK,YAAY,KAAK,MAAM,MAAM,EAAE,CAAC;AAC7C;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,QAAM,QAAQ;AACd,QAAM,kBAAkB,OAAO,KAAK,QAAQ;AAC5C,OAAK,SAAS,cAAc,WAAS;AACjC,QAAI,OAAO,SAAS;AAChB,cAAQ,MAAM,KAAK,KAAK;AAC5B,QAAI;AACA,eAAS,QAAQ,OAAO;AACpB,YAAI,QAAQ;AACR,gBAAM,SAAS,MAAM,MAAM,IAAI;AAAA,iBAC1B,QAAQ;AACb,gBAAM,SAAS,MAAM,QAAQ,MAAM,QAAQ,MAAM,MAAM,MAAM,IAAI;AAAA,iBAC5D,CAAC,MAAM,IAAI,KAAK,QAAQ,qBAAqB,QAAQ;AAC1D,gBAAM,IAAI,IAAI,OAAO,MAAM,IAAI,CAAC;AAAA,MACxC;AAAA,EACR,CAAC;AACD,MAAI,CAAC,MAAM;AACP,UAAM,YAAY;AACtB,SAAO,CAAC,WAAW,KAAK,GAAG,KAAK,MAAM,IAAI,QAAQ,MAAM,KAAK,CAAC;AAClE;AACA,SAAS,oBAAoB,MAAM;AAC/B,MAAI,KAAK,YAAY;AACjB,QAAI,MAAM,SAAS,cAAc,KAAK;AACtC,QAAI,YAAY;AAChB,QAAI,aAAa,oBAAoB,MAAM;AAC3C,QAAI,aAAa,OAAO,EAAE;AAC1B,SAAK,gBAAgB,EAAE,KAAK,MAAM,WAAW,OAAO,KAAK,MAAM,UAAU,MAAM,KAAK,EAAE,KAAK,MAAM,OAAO,KAAK,WAAW,CAAC,EAAE;AAAA,EAC/H,OACK;AACD,SAAK,gBAAgB;AAAA,EACzB;AACJ;AACA,SAAS,YAAY,MAAM;AACvB,SAAO,CAAC,KAAK,SAAS,YAAY,WAAS,MAAM,KAAK,KAAK,MAAM,KAAK;AAC1E;AACA,SAAS,wBAAwB,MAAM,MAAM;AACzC,MAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ,YAAY,KAAK,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK,IAAI,CAAC;AAC7F,SAAO,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,MAAM,KAAK;AAChE;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,WAAS,IAAI,KAAK;AACd,aAAS,QAAQ;AACb,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,IAAI;AAClD,eAAO,IAAI,IAAI,IAAI,IAAI;AAAA,EACnC;AACA,OAAK,SAAS,aAAa,GAAG;AAC9B,OAAK,SAAS,aAAa,GAAG;AAC9B,SAAO;AACX;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,MAAI,KAAK,GAAG,KAAK;AACjB,WAAS,QAAQ,GAAG;AAChB,QAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AACjB,aAAO;AACX;AAAA,EACJ;AACA,WAAS,KAAK;AACV;AACJ,SAAO,MAAM;AACjB;AACA,SAAS,oBAAoB,QAAQ;AACjC,MAAI,OAAO,KAAK,SAAS,OAAO,KAAK,qBAAqB,OAAO,KAAK;AAClE,UAAM,IAAI,WAAW,qEAAqE;AAClG;", "names": ["doc", "i", "spec", "preMatch", "empty", "dom", "event", "handlers", "sel", "oldOffset", "anchor", "prop"]}