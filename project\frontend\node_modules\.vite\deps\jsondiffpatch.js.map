{"version": 3, "sources": ["../../jsondiffpatch/lib/processor.js", "../../jsondiffpatch/lib/pipe.js", "../../jsondiffpatch/lib/contexts/context.js", "../../jsondiffpatch/lib/clone.js", "../../jsondiffpatch/lib/contexts/diff.js", "../../jsondiffpatch/lib/contexts/patch.js", "../../jsondiffpatch/lib/contexts/reverse.js", "../../jsondiffpatch/lib/filters/trivial.js", "../../jsondiffpatch/lib/filters/nested.js", "../../jsondiffpatch/lib/filters/lcs.js", "../../jsondiffpatch/lib/filters/arrays.js", "../../jsondiffpatch/lib/filters/dates.js", "../../jsondiffpatch/lib/filters/texts.js", "../../jsondiffpatch/lib/diffpatcher.js", "../../jsondiffpatch/lib/date-reviver.js", "../../jsondiffpatch/lib/index.js"], "sourcesContent": ["class Processor {\n    constructor(options) {\n        this.selfOptions = options || {};\n        this.pipes = {};\n    }\n    options(options) {\n        if (options) {\n            this.selfOptions = options;\n        }\n        return this.selfOptions;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    pipe(name, pipeArg) {\n        let pipe = pipeArg;\n        if (typeof name === 'string') {\n            if (typeof pipe === 'undefined') {\n                return this.pipes[name];\n            }\n            else {\n                this.pipes[name] = pipe;\n            }\n        }\n        if (name && name.name) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            pipe = name;\n            if (pipe.processor === this) {\n                return pipe;\n            }\n            this.pipes[pipe.name] = pipe;\n        }\n        pipe.processor = this;\n        return pipe;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    process(input, pipe) {\n        let context = input;\n        context.options = this.options();\n        let nextPipe = pipe || input.pipe || 'default';\n        let lastPipe;\n        while (nextPipe) {\n            if (typeof context.nextAfterChildren !== 'undefined') {\n                // children processed and coming back to parent\n                context.next = context.nextAfterChildren;\n                context.nextAfterChildren = null;\n            }\n            if (typeof nextPipe === 'string') {\n                nextPipe = this.pipe(nextPipe);\n            }\n            nextPipe.process(context);\n            lastPipe = nextPipe;\n            nextPipe = null;\n            if (context) {\n                if (context.next) {\n                    context = context.next;\n                    nextPipe = context.pipe || lastPipe;\n                }\n            }\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return context.hasResult ? context.result : undefined;\n    }\n}\nexport default Processor;\n", "// eslint-disable-next-line @typescript-eslint/no-explicit-any\nclass Pipe {\n    constructor(name) {\n        this.name = name;\n        this.filters = [];\n    }\n    process(input) {\n        if (!this.processor) {\n            throw new Error('add this pipe to a processor before using it');\n        }\n        const debug = this.debug;\n        const length = this.filters.length;\n        const context = input;\n        for (let index = 0; index < length; index++) {\n            const filter = this.filters[index];\n            if (debug) {\n                this.log(`filter: ${filter.filterName}`);\n            }\n            filter(context);\n            if (typeof context === 'object' && context.exiting) {\n                context.exiting = false;\n                break;\n            }\n        }\n        if (!context.next && this.resultCheck) {\n            this.resultCheck(context);\n        }\n    }\n    log(msg) {\n        console.log(`[jsondiffpatch] ${this.name} pipe, ${msg}`);\n    }\n    append(...args) {\n        this.filters.push(...args);\n        return this;\n    }\n    prepend(...args) {\n        this.filters.unshift(...args);\n        return this;\n    }\n    indexOf(filterName) {\n        if (!filterName) {\n            throw new Error('a filter name is required');\n        }\n        for (let index = 0; index < this.filters.length; index++) {\n            const filter = this.filters[index];\n            if (filter.filterName === filterName) {\n                return index;\n            }\n        }\n        throw new Error(`filter not found: ${filterName}`);\n    }\n    list() {\n        return this.filters.map((f) => f.filterName);\n    }\n    after(filterName, ...params) {\n        const index = this.indexOf(filterName);\n        this.filters.splice(index + 1, 0, ...params);\n        return this;\n    }\n    before(filterName, ...params) {\n        const index = this.indexOf(filterName);\n        this.filters.splice(index, 0, ...params);\n        return this;\n    }\n    replace(filterName, ...params) {\n        const index = this.indexOf(filterName);\n        this.filters.splice(index, 1, ...params);\n        return this;\n    }\n    remove(filterName) {\n        const index = this.indexOf(filterName);\n        this.filters.splice(index, 1);\n        return this;\n    }\n    clear() {\n        this.filters.length = 0;\n        return this;\n    }\n    shouldHaveResult(should) {\n        if (should === false) {\n            this.resultCheck = null;\n            return;\n        }\n        if (this.resultCheck) {\n            return;\n        }\n        this.resultCheck = (context) => {\n            if (!context.hasResult) {\n                console.log(context);\n                const error = new Error(`${this.name} failed`);\n                error.noResult = true;\n                throw error;\n            }\n        };\n        return this;\n    }\n}\nexport default Pipe;\n", "export default class Context {\n    setResult(result) {\n        this.result = result;\n        this.hasResult = true;\n        return this;\n    }\n    exit() {\n        this.exiting = true;\n        return this;\n    }\n    push(child, name) {\n        child.parent = this;\n        if (typeof name !== 'undefined') {\n            child.childName = name;\n        }\n        child.root = this.root || this;\n        child.options = child.options || this.options;\n        if (!this.children) {\n            this.children = [child];\n            this.nextAfterChildren = this.next || null;\n            this.next = child;\n        }\n        else {\n            this.children[this.children.length - 1].next = child;\n            this.children.push(child);\n        }\n        child.next = this;\n        return this;\n    }\n}\n", "function cloneRegExp(re) {\n    const regexMatch = /^\\/(.*)\\/([gimyu]*)$/.exec(re.toString());\n    return new RegExp(regexMatch[1], regexMatch[2]);\n}\nexport default function clone(arg) {\n    if (typeof arg !== 'object') {\n        return arg;\n    }\n    if (arg === null) {\n        return null;\n    }\n    if (Array.isArray(arg)) {\n        return arg.map(clone);\n    }\n    if (arg instanceof Date) {\n        return new Date(arg.getTime());\n    }\n    if (arg instanceof RegExp) {\n        return cloneRegExp(arg);\n    }\n    const cloned = {};\n    for (const name in arg) {\n        if (Object.prototype.hasOwnProperty.call(arg, name)) {\n            cloned[name] = clone(arg[name]);\n        }\n    }\n    return cloned;\n}\n", "import Context from './context.js';\nimport defaultClone from '../clone.js';\nclass DiffContext extends Context {\n    constructor(left, right) {\n        super();\n        this.left = left;\n        this.right = right;\n        this.pipe = 'diff';\n    }\n    setResult(result) {\n        if (this.options.cloneDiffValues && typeof result === 'object') {\n            const clone = typeof this.options.cloneDiffValues === 'function'\n                ? this.options.cloneDiffValues\n                : defaultClone;\n            if (typeof result[0] === 'object') {\n                result[0] = clone(result[0]);\n            }\n            if (typeof result[1] === 'object') {\n                result[1] = clone(result[1]);\n            }\n        }\n        return super.setResult(result);\n    }\n}\nexport default DiffContext;\n", "import Context from './context.js';\nclass PatchContext extends Context {\n    constructor(left, delta) {\n        super();\n        this.left = left;\n        this.delta = delta;\n        this.pipe = 'patch';\n    }\n}\nexport default PatchContext;\n", "import Context from './context.js';\nclass ReverseContext extends Context {\n    constructor(delta) {\n        super();\n        this.delta = delta;\n        this.pipe = 'reverse';\n    }\n}\nexport default ReverseContext;\n", "export const diffFilter = function trivialMatchesDiffFilter(context) {\n    if (context.left === context.right) {\n        context.setResult(undefined).exit();\n        return;\n    }\n    if (typeof context.left === 'undefined') {\n        if (typeof context.right === 'function') {\n            throw new Error('functions are not supported');\n        }\n        context.setResult([context.right]).exit();\n        return;\n    }\n    if (typeof context.right === 'undefined') {\n        context.setResult([context.left, 0, 0]).exit();\n        return;\n    }\n    if (typeof context.left === 'function' ||\n        typeof context.right === 'function') {\n        throw new Error('functions are not supported');\n    }\n    context.leftType = context.left === null ? 'null' : typeof context.left;\n    context.rightType = context.right === null ? 'null' : typeof context.right;\n    if (context.leftType !== context.rightType) {\n        context.setResult([context.left, context.right]).exit();\n        return;\n    }\n    if (context.leftType === 'boolean' || context.leftType === 'number') {\n        context.setResult([context.left, context.right]).exit();\n        return;\n    }\n    if (context.leftType === 'object') {\n        context.leftIsArray = Array.isArray(context.left);\n    }\n    if (context.rightType === 'object') {\n        context.rightIsArray = Array.isArray(context.right);\n    }\n    if (context.leftIsArray !== context.rightIsArray) {\n        context.setResult([context.left, context.right]).exit();\n        return;\n    }\n    if (context.left instanceof RegExp) {\n        if (context.right instanceof RegExp) {\n            context\n                .setResult([context.left.toString(), context.right.toString()])\n                .exit();\n        }\n        else {\n            context.setResult([context.left, context.right]).exit();\n        }\n    }\n};\ndiffFilter.filterName = 'trivial';\nexport const patchFilter = function trivialMatchesPatchFilter(context) {\n    if (typeof context.delta === 'undefined') {\n        context.setResult(context.left).exit();\n        return;\n    }\n    context.nested = !Array.isArray(context.delta);\n    if (context.nested) {\n        return;\n    }\n    const nonNestedDelta = context.delta;\n    if (nonNestedDelta.length === 1) {\n        context.setResult(nonNestedDelta[0]).exit();\n        return;\n    }\n    if (nonNestedDelta.length === 2) {\n        if (context.left instanceof RegExp) {\n            const regexArgs = /^\\/(.*)\\/([gimyu]+)$/.exec(nonNestedDelta[1]);\n            if (regexArgs) {\n                context.setResult(new RegExp(regexArgs[1], regexArgs[2])).exit();\n                return;\n            }\n        }\n        context.setResult(nonNestedDelta[1]).exit();\n        return;\n    }\n    if (nonNestedDelta.length === 3 && nonNestedDelta[2] === 0) {\n        context.setResult(undefined).exit();\n    }\n};\npatchFilter.filterName = 'trivial';\nexport const reverseFilter = function trivialReferseFilter(context) {\n    if (typeof context.delta === 'undefined') {\n        context.setResult(context.delta).exit();\n        return;\n    }\n    context.nested = !Array.isArray(context.delta);\n    if (context.nested) {\n        return;\n    }\n    const nonNestedDelta = context.delta;\n    if (nonNestedDelta.length === 1) {\n        context.setResult([nonNestedDelta[0], 0, 0]).exit();\n        return;\n    }\n    if (nonNestedDelta.length === 2) {\n        context.setResult([nonNestedDelta[1], nonNestedDelta[0]]).exit();\n        return;\n    }\n    if (nonNestedDelta.length === 3 && nonNestedDelta[2] === 0) {\n        context.setResult([nonNestedDelta[0]]).exit();\n    }\n};\nreverseFilter.filterName = 'trivial';\n", "import DiffContext from '../contexts/diff.js';\nimport PatchContext from '../contexts/patch.js';\nimport ReverseContext from '../contexts/reverse.js';\nexport const collectChildrenDiffFilter = (context) => {\n    if (!context || !context.children) {\n        return;\n    }\n    const length = context.children.length;\n    let child;\n    let result = context.result;\n    for (let index = 0; index < length; index++) {\n        child = context.children[index];\n        if (typeof child.result === 'undefined') {\n            continue;\n        }\n        result = result || {};\n        result[child.childName] = child.result;\n    }\n    if (result && context.leftIsArray) {\n        result._t = 'a';\n    }\n    context.setResult(result).exit();\n};\ncollectChildrenDiffFilter.filterName = 'collectChildren';\nexport const objectsDiffFilter = (context) => {\n    if (context.leftIsArray || context.leftType !== 'object') {\n        return;\n    }\n    const left = context.left;\n    const right = context.right;\n    let name;\n    let child;\n    const propertyFilter = context.options.propertyFilter;\n    for (name in left) {\n        if (!Object.prototype.hasOwnProperty.call(left, name)) {\n            continue;\n        }\n        if (propertyFilter && !propertyFilter(name, context)) {\n            continue;\n        }\n        child = new DiffContext(left[name], right[name]);\n        context.push(child, name);\n    }\n    for (name in right) {\n        if (!Object.prototype.hasOwnProperty.call(right, name)) {\n            continue;\n        }\n        if (propertyFilter && !propertyFilter(name, context)) {\n            continue;\n        }\n        if (typeof left[name] === 'undefined') {\n            child = new DiffContext(undefined, right[name]);\n            context.push(child, name);\n        }\n    }\n    if (!context.children || context.children.length === 0) {\n        context.setResult(undefined).exit();\n        return;\n    }\n    context.exit();\n};\nobjectsDiffFilter.filterName = 'objects';\nexport const patchFilter = function nestedPatchFilter(context) {\n    if (!context.nested) {\n        return;\n    }\n    const nestedDelta = context.delta;\n    if (nestedDelta._t) {\n        return;\n    }\n    const objectDelta = nestedDelta;\n    let name;\n    let child;\n    for (name in objectDelta) {\n        child = new PatchContext(context.left[name], objectDelta[name]);\n        context.push(child, name);\n    }\n    context.exit();\n};\npatchFilter.filterName = 'objects';\nexport const collectChildrenPatchFilter = function collectChildrenPatchFilter(context) {\n    if (!context || !context.children) {\n        return;\n    }\n    const deltaWithChildren = context.delta;\n    if (deltaWithChildren._t) {\n        return;\n    }\n    const object = context.left;\n    const length = context.children.length;\n    let child;\n    for (let index = 0; index < length; index++) {\n        child = context.children[index];\n        const property = child.childName;\n        if (Object.prototype.hasOwnProperty.call(context.left, property) &&\n            child.result === undefined) {\n            delete object[property];\n        }\n        else if (object[property] !== child.result) {\n            object[property] = child.result;\n        }\n    }\n    context.setResult(object).exit();\n};\ncollectChildrenPatchFilter.filterName = 'collectChildren';\nexport const reverseFilter = function nestedReverseFilter(context) {\n    if (!context.nested) {\n        return;\n    }\n    const nestedDelta = context.delta;\n    if (nestedDelta._t) {\n        return;\n    }\n    const objectDelta = context.delta;\n    let name;\n    let child;\n    for (name in objectDelta) {\n        child = new ReverseContext(objectDelta[name]);\n        context.push(child, name);\n    }\n    context.exit();\n};\nreverseFilter.filterName = 'objects';\nexport const collectChildrenReverseFilter = (context) => {\n    if (!context || !context.children) {\n        return;\n    }\n    const deltaWithChildren = context.delta;\n    if (deltaWithChildren._t) {\n        return;\n    }\n    const length = context.children.length;\n    let child;\n    const delta = {};\n    for (let index = 0; index < length; index++) {\n        child = context.children[index];\n        const property = child.childName;\n        if (delta[property] !== child.result) {\n            delta[property] = child.result;\n        }\n    }\n    context.setResult(delta).exit();\n};\ncollectChildrenReverseFilter.filterName = 'collectChildren';\n", "/*\n\nLCS implementation that supports arrays or strings\n\nreference: http://en.wikipedia.org/wiki/Longest_common_subsequence_problem\n\n*/\nconst defaultMatch = function (array1, array2, index1, index2) {\n    return array1[index1] === array2[index2];\n};\nconst lengthMatrix = function (array1, array2, match, context) {\n    const len1 = array1.length;\n    const len2 = array2.length;\n    let x, y;\n    // initialize empty matrix of len1+1 x len2+1\n    const matrix = new Array(len1 + 1);\n    for (x = 0; x < len1 + 1; x++) {\n        matrix[x] = new Array(len2 + 1);\n        for (y = 0; y < len2 + 1; y++) {\n            matrix[x][y] = 0;\n        }\n    }\n    matrix.match = match;\n    // save sequence lengths for each coordinate\n    for (x = 1; x < len1 + 1; x++) {\n        for (y = 1; y < len2 + 1; y++) {\n            if (match(array1, array2, x - 1, y - 1, context)) {\n                matrix[x][y] = matrix[x - 1][y - 1] + 1;\n            }\n            else {\n                matrix[x][y] = Math.max(matrix[x - 1][y], matrix[x][y - 1]);\n            }\n        }\n    }\n    return matrix;\n};\nconst backtrack = function (matrix, array1, array2, context) {\n    let index1 = array1.length;\n    let index2 = array2.length;\n    const subsequence = {\n        sequence: [],\n        indices1: [],\n        indices2: [],\n    };\n    while (index1 !== 0 && index2 !== 0) {\n        const sameLetter = matrix.match(array1, array2, index1 - 1, index2 - 1, context);\n        if (sameLetter) {\n            subsequence.sequence.unshift(array1[index1 - 1]);\n            subsequence.indices1.unshift(index1 - 1);\n            subsequence.indices2.unshift(index2 - 1);\n            --index1;\n            --index2;\n        }\n        else {\n            const valueAtMatrixAbove = matrix[index1][index2 - 1];\n            const valueAtMatrixLeft = matrix[index1 - 1][index2];\n            if (valueAtMatrixAbove > valueAtMatrixLeft) {\n                --index2;\n            }\n            else {\n                --index1;\n            }\n        }\n    }\n    return subsequence;\n};\nconst get = function (array1, array2, match, context) {\n    const innerContext = context || {};\n    const matrix = lengthMatrix(array1, array2, match || defaultMatch, innerContext);\n    return backtrack(matrix, array1, array2, innerContext);\n};\nexport default {\n    get,\n};\n", "import DiffContext from '../contexts/diff.js';\nimport Patch<PERSON>ontext from '../contexts/patch.js';\nimport ReverseContext from '../contexts/reverse.js';\nimport lcs from './lcs.js';\nconst ARRAY_MOVE = 3;\nfunction arraysHaveMatchByRef(array1, array2, len1, len2) {\n    for (let index1 = 0; index1 < len1; index1++) {\n        const val1 = array1[index1];\n        for (let index2 = 0; index2 < len2; index2++) {\n            const val2 = array2[index2];\n            if (index1 !== index2 && val1 === val2) {\n                return true;\n            }\n        }\n    }\n}\nfunction matchItems(array1, array2, index1, index2, context) {\n    const value1 = array1[index1];\n    const value2 = array2[index2];\n    if (value1 === value2) {\n        return true;\n    }\n    if (typeof value1 !== 'object' || typeof value2 !== 'object') {\n        return false;\n    }\n    const objectHash = context.objectHash;\n    if (!objectHash) {\n        // no way to match objects was provided, try match by position\n        return context.matchByPosition && index1 === index2;\n    }\n    context.hashCache1 = context.hashCache1 || [];\n    let hash1 = context.hashCache1[index1];\n    if (typeof hash1 === 'undefined') {\n        context.hashCache1[index1] = hash1 = objectHash(value1, index1);\n    }\n    if (typeof hash1 === 'undefined') {\n        return false;\n    }\n    context.hashCache2 = context.hashCache2 || [];\n    let hash2 = context.hashCache2[index2];\n    if (typeof hash2 === 'undefined') {\n        context.hashCache2[index2] = hash2 = objectHash(value2, index2);\n    }\n    if (typeof hash2 === 'undefined') {\n        return false;\n    }\n    return hash1 === hash2;\n}\nexport const diffFilter = function arraysDiffFilter(context) {\n    if (!context.leftIsArray) {\n        return;\n    }\n    const matchContext = {\n        objectHash: context.options && context.options.objectHash,\n        matchByPosition: context.options && context.options.matchByPosition,\n    };\n    let commonHead = 0;\n    let commonTail = 0;\n    let index;\n    let index1;\n    let index2;\n    const array1 = context.left;\n    const array2 = context.right;\n    const len1 = array1.length;\n    const len2 = array2.length;\n    let child;\n    if (len1 > 0 &&\n        len2 > 0 &&\n        !matchContext.objectHash &&\n        typeof matchContext.matchByPosition !== 'boolean') {\n        matchContext.matchByPosition = !arraysHaveMatchByRef(array1, array2, len1, len2);\n    }\n    // separate common head\n    while (commonHead < len1 &&\n        commonHead < len2 &&\n        matchItems(array1, array2, commonHead, commonHead, matchContext)) {\n        index = commonHead;\n        child = new DiffContext(array1[index], array2[index]);\n        context.push(child, index);\n        commonHead++;\n    }\n    // separate common tail\n    while (commonTail + commonHead < len1 &&\n        commonTail + commonHead < len2 &&\n        matchItems(array1, array2, len1 - 1 - commonTail, len2 - 1 - commonTail, matchContext)) {\n        index1 = len1 - 1 - commonTail;\n        index2 = len2 - 1 - commonTail;\n        child = new DiffContext(array1[index1], array2[index2]);\n        context.push(child, index2);\n        commonTail++;\n    }\n    let result;\n    if (commonHead + commonTail === len1) {\n        if (len1 === len2) {\n            // arrays are identical\n            context.setResult(undefined).exit();\n            return;\n        }\n        // trivial case, a block (1 or more consecutive items) was added\n        result = result || {\n            _t: 'a',\n        };\n        for (index = commonHead; index < len2 - commonTail; index++) {\n            result[index] = [array2[index]];\n        }\n        context.setResult(result).exit();\n        return;\n    }\n    if (commonHead + commonTail === len2) {\n        // trivial case, a block (1 or more consecutive items) was removed\n        result = result || {\n            _t: 'a',\n        };\n        for (index = commonHead; index < len1 - commonTail; index++) {\n            result[`_${index}`] = [array1[index], 0, 0];\n        }\n        context.setResult(result).exit();\n        return;\n    }\n    // reset hash cache\n    delete matchContext.hashCache1;\n    delete matchContext.hashCache2;\n    // diff is not trivial, find the LCS (Longest Common Subsequence)\n    const trimmed1 = array1.slice(commonHead, len1 - commonTail);\n    const trimmed2 = array2.slice(commonHead, len2 - commonTail);\n    const seq = lcs.get(trimmed1, trimmed2, matchItems, matchContext);\n    const removedItems = [];\n    result = result || {\n        _t: 'a',\n    };\n    for (index = commonHead; index < len1 - commonTail; index++) {\n        if (seq.indices1.indexOf(index - commonHead) < 0) {\n            // removed\n            result[`_${index}`] = [array1[index], 0, 0];\n            removedItems.push(index);\n        }\n    }\n    let detectMove = true;\n    if (context.options &&\n        context.options.arrays &&\n        context.options.arrays.detectMove === false) {\n        detectMove = false;\n    }\n    let includeValueOnMove = false;\n    if (context.options &&\n        context.options.arrays &&\n        context.options.arrays.includeValueOnMove) {\n        includeValueOnMove = true;\n    }\n    const removedItemsLength = removedItems.length;\n    for (index = commonHead; index < len2 - commonTail; index++) {\n        const indexOnArray2 = seq.indices2.indexOf(index - commonHead);\n        if (indexOnArray2 < 0) {\n            // added, try to match with a removed item and register as position move\n            let isMove = false;\n            if (detectMove && removedItemsLength > 0) {\n                for (let removeItemIndex1 = 0; removeItemIndex1 < removedItemsLength; removeItemIndex1++) {\n                    index1 = removedItems[removeItemIndex1];\n                    if (matchItems(trimmed1, trimmed2, index1 - commonHead, index - commonHead, matchContext)) {\n                        // store position move as: [originalValue, newPosition, ARRAY_MOVE]\n                        result[`_${index1}`].splice(1, 2, index, ARRAY_MOVE);\n                        if (!includeValueOnMove) {\n                            // don't include moved value on diff, to save bytes\n                            result[`_${index1}`][0] = '';\n                        }\n                        index2 = index;\n                        child = new DiffContext(array1[index1], array2[index2]);\n                        context.push(child, index2);\n                        removedItems.splice(removeItemIndex1, 1);\n                        isMove = true;\n                        break;\n                    }\n                }\n            }\n            if (!isMove) {\n                // added\n                result[index] = [array2[index]];\n            }\n        }\n        else {\n            // match, do inner diff\n            index1 = seq.indices1[indexOnArray2] + commonHead;\n            index2 = seq.indices2[indexOnArray2] + commonHead;\n            child = new DiffContext(array1[index1], array2[index2]);\n            context.push(child, index2);\n        }\n    }\n    context.setResult(result).exit();\n};\ndiffFilter.filterName = 'arrays';\nconst compare = {\n    numerically(a, b) {\n        return a - b;\n    },\n    numericallyBy(name) {\n        return (a, b) => a[name] - b[name];\n    },\n};\nexport const patchFilter = function nestedPatchFilter(context) {\n    if (!context.nested) {\n        return;\n    }\n    const nestedDelta = context.delta;\n    if (nestedDelta._t !== 'a') {\n        return;\n    }\n    let index;\n    let index1;\n    const delta = nestedDelta;\n    const array = context.left;\n    // first, separate removals, insertions and modifications\n    let toRemove = [];\n    let toInsert = [];\n    const toModify = [];\n    for (index in delta) {\n        if (index !== '_t') {\n            if (index[0] === '_') {\n                const removedOrMovedIndex = index;\n                // removed item from original array\n                if (delta[removedOrMovedIndex][2] === 0 ||\n                    delta[removedOrMovedIndex][2] === ARRAY_MOVE) {\n                    toRemove.push(parseInt(index.slice(1), 10));\n                }\n                else {\n                    throw new Error('only removal or move can be applied at original array indices,' +\n                        ` invalid diff type: ${delta[removedOrMovedIndex][2]}`);\n                }\n            }\n            else {\n                const numberIndex = index;\n                if (delta[numberIndex].length === 1) {\n                    // added item at new array\n                    toInsert.push({\n                        index: parseInt(numberIndex, 10),\n                        value: delta[numberIndex][0],\n                    });\n                }\n                else {\n                    // modified item at new array\n                    toModify.push({\n                        index: parseInt(numberIndex, 10),\n                        delta: delta[numberIndex],\n                    });\n                }\n            }\n        }\n    }\n    // remove items, in reverse order to avoid sawing our own floor\n    toRemove = toRemove.sort(compare.numerically);\n    for (index = toRemove.length - 1; index >= 0; index--) {\n        index1 = toRemove[index];\n        const indexDiff = delta[`_${index1}`];\n        const removedValue = array.splice(index1, 1)[0];\n        if (indexDiff[2] === ARRAY_MOVE) {\n            // reinsert later\n            toInsert.push({\n                index: indexDiff[1],\n                value: removedValue,\n            });\n        }\n    }\n    // insert items, in reverse order to avoid moving our own floor\n    toInsert = toInsert.sort(compare.numericallyBy('index'));\n    const toInsertLength = toInsert.length;\n    for (index = 0; index < toInsertLength; index++) {\n        const insertion = toInsert[index];\n        array.splice(insertion.index, 0, insertion.value);\n    }\n    // apply modifications\n    const toModifyLength = toModify.length;\n    let child;\n    if (toModifyLength > 0) {\n        for (index = 0; index < toModifyLength; index++) {\n            const modification = toModify[index];\n            child = new PatchContext(array[modification.index], modification.delta);\n            context.push(child, modification.index);\n        }\n    }\n    if (!context.children) {\n        context.setResult(array).exit();\n        return;\n    }\n    context.exit();\n};\npatchFilter.filterName = 'arrays';\nexport const collectChildrenPatchFilter = function collectChildrenPatchFilter(context) {\n    if (!context || !context.children) {\n        return;\n    }\n    const deltaWithChildren = context.delta;\n    if (deltaWithChildren._t !== 'a') {\n        return;\n    }\n    const array = context.left;\n    const length = context.children.length;\n    let child;\n    for (let index = 0; index < length; index++) {\n        child = context.children[index];\n        const arrayIndex = child.childName;\n        array[arrayIndex] = child.result;\n    }\n    context.setResult(array).exit();\n};\ncollectChildrenPatchFilter.filterName = 'arraysCollectChildren';\nexport const reverseFilter = function arraysReverseFilter(context) {\n    if (!context.nested) {\n        const nonNestedDelta = context.delta;\n        if (nonNestedDelta[2] === ARRAY_MOVE) {\n            const arrayMoveDelta = nonNestedDelta;\n            context.newName = `_${arrayMoveDelta[1]}`;\n            context\n                .setResult([\n                arrayMoveDelta[0],\n                parseInt(context.childName.substring(1), 10),\n                ARRAY_MOVE,\n            ])\n                .exit();\n        }\n        return;\n    }\n    const nestedDelta = context.delta;\n    if (nestedDelta._t !== 'a') {\n        return;\n    }\n    const arrayDelta = nestedDelta;\n    let name;\n    let child;\n    for (name in arrayDelta) {\n        if (name === '_t') {\n            continue;\n        }\n        child = new ReverseContext(arrayDelta[name]);\n        context.push(child, name);\n    }\n    context.exit();\n};\nreverseFilter.filterName = 'arrays';\nconst reverseArrayDeltaIndex = (delta, index, itemDelta) => {\n    if (typeof index === 'string' && index[0] === '_') {\n        return parseInt(index.substring(1), 10);\n    }\n    else if (Array.isArray(itemDelta) && itemDelta[2] === 0) {\n        return `_${index}`;\n    }\n    let reverseIndex = +index;\n    for (const deltaIndex in delta) {\n        const deltaItem = delta[deltaIndex];\n        if (Array.isArray(deltaItem)) {\n            if (deltaItem[2] === ARRAY_MOVE) {\n                const moveFromIndex = parseInt(deltaIndex.substring(1), 10);\n                const moveToIndex = deltaItem[1];\n                if (moveToIndex === +index) {\n                    return moveFromIndex;\n                }\n                if (moveFromIndex <= reverseIndex && moveToIndex > reverseIndex) {\n                    reverseIndex++;\n                }\n                else if (moveFromIndex >= reverseIndex &&\n                    moveToIndex < reverseIndex) {\n                    reverseIndex--;\n                }\n            }\n            else if (deltaItem[2] === 0) {\n                const deleteIndex = parseInt(deltaIndex.substring(1), 10);\n                if (deleteIndex <= reverseIndex) {\n                    reverseIndex++;\n                }\n            }\n            else if (deltaItem.length === 1 &&\n                parseInt(deltaIndex, 10) <= reverseIndex) {\n                reverseIndex--;\n            }\n        }\n    }\n    return reverseIndex;\n};\nexport const collectChildrenReverseFilter = (context) => {\n    if (!context || !context.children) {\n        return;\n    }\n    const deltaWithChildren = context.delta;\n    if (deltaWithChildren._t !== 'a') {\n        return;\n    }\n    const arrayDelta = deltaWithChildren;\n    const length = context.children.length;\n    let child;\n    const delta = {\n        _t: 'a',\n    };\n    for (let index = 0; index < length; index++) {\n        child = context.children[index];\n        let name = child.newName;\n        if (typeof name === 'undefined') {\n            name = reverseArrayDeltaIndex(arrayDelta, child.childName, child.result);\n        }\n        if (delta[name] !== child.result) {\n            // There's no way to type this well.\n            delta[name] = child.result;\n        }\n    }\n    context.setResult(delta).exit();\n};\ncollectChildrenReverseFilter.filterName = 'arraysCollectChildren';\n", "export const diffFilter = function datesDiffFilter(context) {\n    if (context.left instanceof Date) {\n        if (context.right instanceof Date) {\n            if (context.left.getTime() !== context.right.getTime()) {\n                context.setResult([context.left, context.right]);\n            }\n            else {\n                context.setResult(undefined);\n            }\n        }\n        else {\n            context.setResult([context.left, context.right]);\n        }\n        context.exit();\n    }\n    else if (context.right instanceof Date) {\n        context.setResult([context.left, context.right]).exit();\n    }\n};\ndiffFilter.filterName = 'dates';\n", "const TEXT_DIFF = 2;\nconst DEFAULT_MIN_LENGTH = 60;\nlet cachedDiffPatch = null;\nfunction getDiffMatchPatch(options, required) {\n    var _a;\n    if (!cachedDiffPatch) {\n        let instance;\n        if ((_a = options === null || options === void 0 ? void 0 : options.textDiff) === null || _a === void 0 ? void 0 : _a.diffMatchPatch) {\n            instance = new options.textDiff.diffMatchPatch();\n        }\n        else {\n            if (!required) {\n                return null;\n            }\n            const error = new Error('The diff-match-patch library was not provided. Pass the library in through the options or use the `jsondiffpatch/with-text-diffs` entry-point.');\n            // eslint-disable-next-line camelcase\n            error.diff_match_patch_not_found = true;\n            throw error;\n        }\n        cachedDiffPatch = {\n            diff: function (txt1, txt2) {\n                return instance.patch_toText(instance.patch_make(txt1, txt2));\n            },\n            patch: function (txt1, patch) {\n                const results = instance.patch_apply(instance.patch_fromText(patch), txt1);\n                for (let i = 0; i < results[1].length; i++) {\n                    if (!results[1][i]) {\n                        const error = new Error('text patch failed');\n                        error.textPatchFailed = true;\n                    }\n                }\n                return results[0];\n            },\n        };\n    }\n    return cachedDiffPatch;\n}\nexport const diffFilter = function textsDiffFilter(context) {\n    if (context.leftType !== 'string') {\n        return;\n    }\n    const left = context.left;\n    const right = context.right;\n    const minLength = (context.options &&\n        context.options.textDiff &&\n        context.options.textDiff.minLength) ||\n        DEFAULT_MIN_LENGTH;\n    if (left.length < minLength || right.length < minLength) {\n        context.setResult([left, right]).exit();\n        return;\n    }\n    // large text, try to use a text-diff algorithm\n    const diffMatchPatch = getDiffMatchPatch(context.options);\n    if (!diffMatchPatch) {\n        // diff-match-patch library not available,\n        // fallback to regular string replace\n        context.setResult([left, right]).exit();\n        return;\n    }\n    const diff = diffMatchPatch.diff;\n    context.setResult([diff(left, right), 0, TEXT_DIFF]).exit();\n};\ndiffFilter.filterName = 'texts';\nexport const patchFilter = function textsPatchFilter(context) {\n    if (context.nested) {\n        return;\n    }\n    const nonNestedDelta = context.delta;\n    if (nonNestedDelta[2] !== TEXT_DIFF) {\n        return;\n    }\n    const textDiffDelta = nonNestedDelta;\n    // text-diff, use a text-patch algorithm\n    const patch = getDiffMatchPatch(context.options, true).patch;\n    context.setResult(patch(context.left, textDiffDelta[0])).exit();\n};\npatchFilter.filterName = 'texts';\nconst textDeltaReverse = function (delta) {\n    let i;\n    let l;\n    let line;\n    let lineTmp;\n    let header = null;\n    const headerRegex = /^@@ +-(\\d+),(\\d+) +\\+(\\d+),(\\d+) +@@$/;\n    let lineHeader;\n    const lines = delta.split('\\n');\n    for (i = 0, l = lines.length; i < l; i++) {\n        line = lines[i];\n        const lineStart = line.slice(0, 1);\n        if (lineStart === '@') {\n            header = headerRegex.exec(line);\n            lineHeader = i;\n            // fix header\n            lines[lineHeader] =\n                '@@ -' +\n                    header[3] +\n                    ',' +\n                    header[4] +\n                    ' +' +\n                    header[1] +\n                    ',' +\n                    header[2] +\n                    ' @@';\n        }\n        else if (lineStart === '+') {\n            lines[i] = '-' + lines[i].slice(1);\n            if (lines[i - 1].slice(0, 1) === '+') {\n                // swap lines to keep default order (-+)\n                lineTmp = lines[i];\n                lines[i] = lines[i - 1];\n                lines[i - 1] = lineTmp;\n            }\n        }\n        else if (lineStart === '-') {\n            lines[i] = '+' + lines[i].slice(1);\n        }\n    }\n    return lines.join('\\n');\n};\nexport const reverseFilter = function textsReverseFilter(context) {\n    if (context.nested) {\n        return;\n    }\n    const nonNestedDelta = context.delta;\n    if (nonNestedDelta[2] !== TEXT_DIFF) {\n        return;\n    }\n    const textDiffDelta = nonNestedDelta;\n    // text-diff, use a text-diff algorithm\n    context\n        .setResult([textDeltaReverse(textDiffDelta[0]), 0, TEXT_DIFF])\n        .exit();\n};\nreverseFilter.filterName = 'texts';\n", "import Processor from './processor.js';\nimport Pipe from './pipe.js';\nimport DiffContext from './contexts/diff.js';\nimport PatchContext from './contexts/patch.js';\nimport ReverseContext from './contexts/reverse.js';\nimport clone from './clone.js';\nimport * as trivial from './filters/trivial.js';\nimport * as nested from './filters/nested.js';\nimport * as arrays from './filters/arrays.js';\nimport * as dates from './filters/dates.js';\nimport * as texts from './filters/texts.js';\nclass DiffPatcher {\n    constructor(options) {\n        this.processor = new Processor(options);\n        this.processor.pipe(new Pipe('diff')\n            .append(nested.collectChildrenDiffFilter, trivial.diffFilter, dates.diffFilter, texts.diffFilter, nested.objectsDiffFilter, arrays.diffFilter)\n            .shouldHaveResult());\n        this.processor.pipe(new Pipe('patch')\n            .append(nested.collectChildrenPatchFilter, arrays.collectChildrenPatchFilter, trivial.patchFilter, texts.patchFilter, nested.patchFilter, arrays.patchFilter)\n            .shouldHaveResult());\n        this.processor.pipe(new Pipe('reverse')\n            .append(nested.collectChildrenReverseFilter, arrays.collectChildrenReverseFilter, trivial.reverseFilter, texts.reverseFilter, nested.reverseFilter, arrays.reverseFilter)\n            .shouldHaveResult());\n    }\n    options(options) {\n        return this.processor.options(options);\n    }\n    diff(left, right) {\n        return this.processor.process(new DiffContext(left, right));\n    }\n    patch(left, delta) {\n        return this.processor.process(new PatchContext(left, delta));\n    }\n    reverse(delta) {\n        return this.processor.process(new ReverseContext(delta));\n    }\n    unpatch(right, delta) {\n        return this.patch(right, this.reverse(delta));\n    }\n    clone(value) {\n        return clone(value);\n    }\n}\nexport default DiffPatcher;\n", "// use as 2nd parameter for JSON.parse to revive Date instances\nexport default function dateReviver(key, value) {\n    let parts;\n    if (typeof value === 'string') {\n        parts =\n            /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d*))?(Z|([+-])(\\d{2}):(\\d{2}))$/.exec(value);\n        if (parts) {\n            return new Date(Date.UTC(+parts[1], +parts[2] - 1, +parts[3], +parts[4], +parts[5], +parts[6], +(parts[7] || 0)));\n        }\n    }\n    return value;\n}\n", "import DiffPatcher from './diffpatcher.js';\nimport dateReviver from './date-reviver.js';\nexport { DiffPatcher, dateReviver };\nexport function create(options) {\n    return new DiffPatcher(options);\n}\nlet defaultInstance;\nexport function diff(left, right) {\n    if (!defaultInstance) {\n        defaultInstance = new DiffPatcher();\n    }\n    return defaultInstance.diff(left, right);\n}\nexport function patch(left, delta) {\n    if (!defaultInstance) {\n        defaultInstance = new DiffPatcher();\n    }\n    return defaultInstance.patch(left, delta);\n}\nexport function unpatch(right, delta) {\n    if (!defaultInstance) {\n        defaultInstance = new DiffPatcher();\n    }\n    return defaultInstance.unpatch(right, delta);\n}\nexport function reverse(delta) {\n    if (!defaultInstance) {\n        defaultInstance = new DiffPatcher();\n    }\n    return defaultInstance.reverse(delta);\n}\nexport function clone(value) {\n    if (!defaultInstance) {\n        defaultInstance = new DiffPatcher();\n    }\n    return defaultInstance.clone(value);\n}\n"], "mappings": ";;;AAAA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,SAAS;AACjB,SAAK,cAAc,WAAW,CAAC;AAC/B,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA,EACA,QAAQ,SAAS;AACb,QAAI,SAAS;AACT,WAAK,cAAc;AAAA,IACvB;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,KAAK,MAAM,SAAS;AAChB,QAAI,OAAO;AACX,QAAI,OAAO,SAAS,UAAU;AAC1B,UAAI,OAAO,SAAS,aAAa;AAC7B,eAAO,KAAK,MAAM,IAAI;AAAA,MAC1B,OACK;AACD,aAAK,MAAM,IAAI,IAAI;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,QAAQ,KAAK,MAAM;AAEnB,aAAO;AACP,UAAI,KAAK,cAAc,MAAM;AACzB,eAAO;AAAA,MACX;AACA,WAAK,MAAM,KAAK,IAAI,IAAI;AAAA,IAC5B;AACA,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,QAAQ,OAAO,MAAM;AACjB,QAAI,UAAU;AACd,YAAQ,UAAU,KAAK,QAAQ;AAC/B,QAAI,WAAW,QAAQ,MAAM,QAAQ;AACrC,QAAI;AACJ,WAAO,UAAU;AACb,UAAI,OAAO,QAAQ,sBAAsB,aAAa;AAElD,gBAAQ,OAAO,QAAQ;AACvB,gBAAQ,oBAAoB;AAAA,MAChC;AACA,UAAI,OAAO,aAAa,UAAU;AAC9B,mBAAW,KAAK,KAAK,QAAQ;AAAA,MACjC;AACA,eAAS,QAAQ,OAAO;AACxB,iBAAW;AACX,iBAAW;AACX,UAAI,SAAS;AACT,YAAI,QAAQ,MAAM;AACd,oBAAU,QAAQ;AAClB,qBAAW,QAAQ,QAAQ;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,QAAQ,YAAY,QAAQ,SAAS;AAAA,EAChD;AACJ;AACA,IAAO,oBAAQ;;;AC7Df,IAAM,OAAN,MAAW;AAAA,EACP,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,CAAC,KAAK,WAAW;AACjB,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAClE;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,KAAK,QAAQ;AAC5B,UAAM,UAAU;AAChB,aAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AACzC,YAAM,SAAS,KAAK,QAAQ,KAAK;AACjC,UAAI,OAAO;AACP,aAAK,IAAI,WAAW,OAAO,UAAU,EAAE;AAAA,MAC3C;AACA,aAAO,OAAO;AACd,UAAI,OAAO,YAAY,YAAY,QAAQ,SAAS;AAChD,gBAAQ,UAAU;AAClB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,QAAQ,QAAQ,KAAK,aAAa;AACnC,WAAK,YAAY,OAAO;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACL,YAAQ,IAAI,mBAAmB,KAAK,IAAI,UAAU,GAAG,EAAE;AAAA,EAC3D;AAAA,EACA,UAAU,MAAM;AACZ,SAAK,QAAQ,KAAK,GAAG,IAAI;AACzB,WAAO;AAAA,EACX;AAAA,EACA,WAAW,MAAM;AACb,SAAK,QAAQ,QAAQ,GAAG,IAAI;AAC5B,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,YAAY;AAChB,QAAI,CAAC,YAAY;AACb,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC/C;AACA,aAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,QAAQ,SAAS;AACtD,YAAM,SAAS,KAAK,QAAQ,KAAK;AACjC,UAAI,OAAO,eAAe,YAAY;AAClC,eAAO;AAAA,MACX;AAAA,IACJ;AACA,UAAM,IAAI,MAAM,qBAAqB,UAAU,EAAE;AAAA,EACrD;AAAA,EACA,OAAO;AACH,WAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAE,UAAU;AAAA,EAC/C;AAAA,EACA,MAAM,eAAe,QAAQ;AACzB,UAAM,QAAQ,KAAK,QAAQ,UAAU;AACrC,SAAK,QAAQ,OAAO,QAAQ,GAAG,GAAG,GAAG,MAAM;AAC3C,WAAO;AAAA,EACX;AAAA,EACA,OAAO,eAAe,QAAQ;AAC1B,UAAM,QAAQ,KAAK,QAAQ,UAAU;AACrC,SAAK,QAAQ,OAAO,OAAO,GAAG,GAAG,MAAM;AACvC,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,eAAe,QAAQ;AAC3B,UAAM,QAAQ,KAAK,QAAQ,UAAU;AACrC,SAAK,QAAQ,OAAO,OAAO,GAAG,GAAG,MAAM;AACvC,WAAO;AAAA,EACX;AAAA,EACA,OAAO,YAAY;AACf,UAAM,QAAQ,KAAK,QAAQ,UAAU;AACrC,SAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,SAAK,QAAQ,SAAS;AACtB,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,QAAQ;AACrB,QAAI,WAAW,OAAO;AAClB,WAAK,cAAc;AACnB;AAAA,IACJ;AACA,QAAI,KAAK,aAAa;AAClB;AAAA,IACJ;AACA,SAAK,cAAc,CAAC,YAAY;AAC5B,UAAI,CAAC,QAAQ,WAAW;AACpB,gBAAQ,IAAI,OAAO;AACnB,cAAM,QAAQ,IAAI,MAAM,GAAG,KAAK,IAAI,SAAS;AAC7C,cAAM,WAAW;AACjB,cAAM;AAAA,MACV;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAO,eAAQ;;;ACjGf,IAAqB,UAArB,MAA6B;AAAA,EACzB,UAAU,QAAQ;AACd,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA,EACA,OAAO;AACH,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AAAA,EACA,KAAK,OAAO,MAAM;AACd,UAAM,SAAS;AACf,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,YAAY;AAAA,IACtB;AACA,UAAM,OAAO,KAAK,QAAQ;AAC1B,UAAM,UAAU,MAAM,WAAW,KAAK;AACtC,QAAI,CAAC,KAAK,UAAU;AAChB,WAAK,WAAW,CAAC,KAAK;AACtB,WAAK,oBAAoB,KAAK,QAAQ;AACtC,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,SAAS,KAAK,SAAS,SAAS,CAAC,EAAE,OAAO;AAC/C,WAAK,SAAS,KAAK,KAAK;AAAA,IAC5B;AACA,UAAM,OAAO;AACb,WAAO;AAAA,EACX;AACJ;;;AC7BA,SAAS,YAAY,IAAI;AACrB,QAAM,aAAa,uBAAuB,KAAK,GAAG,SAAS,CAAC;AAC5D,SAAO,IAAI,OAAO,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAClD;AACe,SAAR,MAAuB,KAAK;AAC/B,MAAI,OAAO,QAAQ,UAAU;AACzB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,WAAO,IAAI,IAAI,KAAK;AAAA,EACxB;AACA,MAAI,eAAe,MAAM;AACrB,WAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,EACjC;AACA,MAAI,eAAe,QAAQ;AACvB,WAAO,YAAY,GAAG;AAAA,EAC1B;AACA,QAAM,SAAS,CAAC;AAChB,aAAW,QAAQ,KAAK;AACpB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,GAAG;AACjD,aAAO,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,IAClC;AAAA,EACJ;AACA,SAAO;AACX;;;ACzBA,IAAM,cAAN,cAA0B,QAAQ;AAAA,EAC9B,YAAY,MAAM,OAAO;AACrB,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,UAAU,QAAQ;AACd,QAAI,KAAK,QAAQ,mBAAmB,OAAO,WAAW,UAAU;AAC5D,YAAMA,SAAQ,OAAO,KAAK,QAAQ,oBAAoB,aAChD,KAAK,QAAQ,kBACb;AACN,UAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAC/B,eAAO,CAAC,IAAIA,OAAM,OAAO,CAAC,CAAC;AAAA,MAC/B;AACA,UAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAC/B,eAAO,CAAC,IAAIA,OAAM,OAAO,CAAC,CAAC;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO,MAAM,UAAU,MAAM;AAAA,EACjC;AACJ;AACA,IAAO,eAAQ;;;ACvBf,IAAM,eAAN,cAA2B,QAAQ;AAAA,EAC/B,YAAY,MAAM,OAAO;AACrB,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAO,gBAAQ;;;ACRf,IAAM,iBAAN,cAA6B,QAAQ;AAAA,EACjC,YAAY,OAAO;AACf,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAO,kBAAQ;;;ACRR,IAAM,aAAa,SAAS,yBAAyB,SAAS;AACjE,MAAI,QAAQ,SAAS,QAAQ,OAAO;AAChC,YAAQ,UAAU,MAAS,EAAE,KAAK;AAClC;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,SAAS,aAAa;AACrC,QAAI,OAAO,QAAQ,UAAU,YAAY;AACrC,YAAM,IAAI,MAAM,6BAA6B;AAAA,IACjD;AACA,YAAQ,UAAU,CAAC,QAAQ,KAAK,CAAC,EAAE,KAAK;AACxC;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,UAAU,aAAa;AACtC,YAAQ,UAAU,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK;AAC7C;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,SAAS,cACxB,OAAO,QAAQ,UAAU,YAAY;AACrC,UAAM,IAAI,MAAM,6BAA6B;AAAA,EACjD;AACA,UAAQ,WAAW,QAAQ,SAAS,OAAO,SAAS,OAAO,QAAQ;AACnE,UAAQ,YAAY,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ;AACrE,MAAI,QAAQ,aAAa,QAAQ,WAAW;AACxC,YAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK;AACtD;AAAA,EACJ;AACA,MAAI,QAAQ,aAAa,aAAa,QAAQ,aAAa,UAAU;AACjE,YAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK;AACtD;AAAA,EACJ;AACA,MAAI,QAAQ,aAAa,UAAU;AAC/B,YAAQ,cAAc,MAAM,QAAQ,QAAQ,IAAI;AAAA,EACpD;AACA,MAAI,QAAQ,cAAc,UAAU;AAChC,YAAQ,eAAe,MAAM,QAAQ,QAAQ,KAAK;AAAA,EACtD;AACA,MAAI,QAAQ,gBAAgB,QAAQ,cAAc;AAC9C,YAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK;AACtD;AAAA,EACJ;AACA,MAAI,QAAQ,gBAAgB,QAAQ;AAChC,QAAI,QAAQ,iBAAiB,QAAQ;AACjC,cACK,UAAU,CAAC,QAAQ,KAAK,SAAS,GAAG,QAAQ,MAAM,SAAS,CAAC,CAAC,EAC7D,KAAK;AAAA,IACd,OACK;AACD,cAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK;AAAA,IAC1D;AAAA,EACJ;AACJ;AACA,WAAW,aAAa;AACjB,IAAM,cAAc,SAAS,0BAA0B,SAAS;AACnE,MAAI,OAAO,QAAQ,UAAU,aAAa;AACtC,YAAQ,UAAU,QAAQ,IAAI,EAAE,KAAK;AACrC;AAAA,EACJ;AACA,UAAQ,SAAS,CAAC,MAAM,QAAQ,QAAQ,KAAK;AAC7C,MAAI,QAAQ,QAAQ;AAChB;AAAA,EACJ;AACA,QAAM,iBAAiB,QAAQ;AAC/B,MAAI,eAAe,WAAW,GAAG;AAC7B,YAAQ,UAAU,eAAe,CAAC,CAAC,EAAE,KAAK;AAC1C;AAAA,EACJ;AACA,MAAI,eAAe,WAAW,GAAG;AAC7B,QAAI,QAAQ,gBAAgB,QAAQ;AAChC,YAAM,YAAY,uBAAuB,KAAK,eAAe,CAAC,CAAC;AAC/D,UAAI,WAAW;AACX,gBAAQ,UAAU,IAAI,OAAO,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK;AAC/D;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,UAAU,eAAe,CAAC,CAAC,EAAE,KAAK;AAC1C;AAAA,EACJ;AACA,MAAI,eAAe,WAAW,KAAK,eAAe,CAAC,MAAM,GAAG;AACxD,YAAQ,UAAU,MAAS,EAAE,KAAK;AAAA,EACtC;AACJ;AACA,YAAY,aAAa;AAClB,IAAM,gBAAgB,SAAS,qBAAqB,SAAS;AAChE,MAAI,OAAO,QAAQ,UAAU,aAAa;AACtC,YAAQ,UAAU,QAAQ,KAAK,EAAE,KAAK;AACtC;AAAA,EACJ;AACA,UAAQ,SAAS,CAAC,MAAM,QAAQ,QAAQ,KAAK;AAC7C,MAAI,QAAQ,QAAQ;AAChB;AAAA,EACJ;AACA,QAAM,iBAAiB,QAAQ;AAC/B,MAAI,eAAe,WAAW,GAAG;AAC7B,YAAQ,UAAU,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK;AAClD;AAAA,EACJ;AACA,MAAI,eAAe,WAAW,GAAG;AAC7B,YAAQ,UAAU,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK;AAC/D;AAAA,EACJ;AACA,MAAI,eAAe,WAAW,KAAK,eAAe,CAAC,MAAM,GAAG;AACxD,YAAQ,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK;AAAA,EAChD;AACJ;AACA,cAAc,aAAa;;;ACrGpB,IAAM,4BAA4B,CAAC,YAAY;AAClD,MAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;AAC/B;AAAA,EACJ;AACA,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI;AACJ,MAAI,SAAS,QAAQ;AACrB,WAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AACzC,YAAQ,QAAQ,SAAS,KAAK;AAC9B,QAAI,OAAO,MAAM,WAAW,aAAa;AACrC;AAAA,IACJ;AACA,aAAS,UAAU,CAAC;AACpB,WAAO,MAAM,SAAS,IAAI,MAAM;AAAA,EACpC;AACA,MAAI,UAAU,QAAQ,aAAa;AAC/B,WAAO,KAAK;AAAA,EAChB;AACA,UAAQ,UAAU,MAAM,EAAE,KAAK;AACnC;AACA,0BAA0B,aAAa;AAChC,IAAM,oBAAoB,CAAC,YAAY;AAC1C,MAAI,QAAQ,eAAe,QAAQ,aAAa,UAAU;AACtD;AAAA,EACJ;AACA,QAAM,OAAO,QAAQ;AACrB,QAAM,QAAQ,QAAQ;AACtB,MAAI;AACJ,MAAI;AACJ,QAAM,iBAAiB,QAAQ,QAAQ;AACvC,OAAK,QAAQ,MAAM;AACf,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACnD;AAAA,IACJ;AACA,QAAI,kBAAkB,CAAC,eAAe,MAAM,OAAO,GAAG;AAClD;AAAA,IACJ;AACA,YAAQ,IAAI,aAAY,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC;AAC/C,YAAQ,KAAK,OAAO,IAAI;AAAA,EAC5B;AACA,OAAK,QAAQ,OAAO;AAChB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,IAAI,GAAG;AACpD;AAAA,IACJ;AACA,QAAI,kBAAkB,CAAC,eAAe,MAAM,OAAO,GAAG;AAClD;AAAA,IACJ;AACA,QAAI,OAAO,KAAK,IAAI,MAAM,aAAa;AACnC,cAAQ,IAAI,aAAY,QAAW,MAAM,IAAI,CAAC;AAC9C,cAAQ,KAAK,OAAO,IAAI;AAAA,IAC5B;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ,YAAY,QAAQ,SAAS,WAAW,GAAG;AACpD,YAAQ,UAAU,MAAS,EAAE,KAAK;AAClC;AAAA,EACJ;AACA,UAAQ,KAAK;AACjB;AACA,kBAAkB,aAAa;AACxB,IAAMC,eAAc,SAAS,kBAAkB,SAAS;AAC3D,MAAI,CAAC,QAAQ,QAAQ;AACjB;AAAA,EACJ;AACA,QAAM,cAAc,QAAQ;AAC5B,MAAI,YAAY,IAAI;AAChB;AAAA,EACJ;AACA,QAAM,cAAc;AACpB,MAAI;AACJ,MAAI;AACJ,OAAK,QAAQ,aAAa;AACtB,YAAQ,IAAI,cAAa,QAAQ,KAAK,IAAI,GAAG,YAAY,IAAI,CAAC;AAC9D,YAAQ,KAAK,OAAO,IAAI;AAAA,EAC5B;AACA,UAAQ,KAAK;AACjB;AACAA,aAAY,aAAa;AAClB,IAAM,6BAA6B,SAASC,4BAA2B,SAAS;AACnF,MAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;AAC/B;AAAA,EACJ;AACA,QAAM,oBAAoB,QAAQ;AAClC,MAAI,kBAAkB,IAAI;AACtB;AAAA,EACJ;AACA,QAAM,SAAS,QAAQ;AACvB,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI;AACJ,WAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AACzC,YAAQ,QAAQ,SAAS,KAAK;AAC9B,UAAM,WAAW,MAAM;AACvB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,MAAM,QAAQ,KAC3D,MAAM,WAAW,QAAW;AAC5B,aAAO,OAAO,QAAQ;AAAA,IAC1B,WACS,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACxC,aAAO,QAAQ,IAAI,MAAM;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,UAAU,MAAM,EAAE,KAAK;AACnC;AACA,2BAA2B,aAAa;AACjC,IAAMC,iBAAgB,SAAS,oBAAoB,SAAS;AAC/D,MAAI,CAAC,QAAQ,QAAQ;AACjB;AAAA,EACJ;AACA,QAAM,cAAc,QAAQ;AAC5B,MAAI,YAAY,IAAI;AAChB;AAAA,EACJ;AACA,QAAM,cAAc,QAAQ;AAC5B,MAAI;AACJ,MAAI;AACJ,OAAK,QAAQ,aAAa;AACtB,YAAQ,IAAI,gBAAe,YAAY,IAAI,CAAC;AAC5C,YAAQ,KAAK,OAAO,IAAI;AAAA,EAC5B;AACA,UAAQ,KAAK;AACjB;AACAA,eAAc,aAAa;AACpB,IAAM,+BAA+B,CAAC,YAAY;AACrD,MAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;AAC/B;AAAA,EACJ;AACA,QAAM,oBAAoB,QAAQ;AAClC,MAAI,kBAAkB,IAAI;AACtB;AAAA,EACJ;AACA,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI;AACJ,QAAM,QAAQ,CAAC;AACf,WAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AACzC,YAAQ,QAAQ,SAAS,KAAK;AAC9B,UAAM,WAAW,MAAM;AACvB,QAAI,MAAM,QAAQ,MAAM,MAAM,QAAQ;AAClC,YAAM,QAAQ,IAAI,MAAM;AAAA,IAC5B;AAAA,EACJ;AACA,UAAQ,UAAU,KAAK,EAAE,KAAK;AAClC;AACA,6BAA6B,aAAa;;;ACxI1C,IAAM,eAAe,SAAU,QAAQ,QAAQ,QAAQ,QAAQ;AAC3D,SAAO,OAAO,MAAM,MAAM,OAAO,MAAM;AAC3C;AACA,IAAM,eAAe,SAAU,QAAQ,QAAQ,OAAO,SAAS;AAC3D,QAAM,OAAO,OAAO;AACpB,QAAM,OAAO,OAAO;AACpB,MAAI,GAAG;AAEP,QAAM,SAAS,IAAI,MAAM,OAAO,CAAC;AACjC,OAAK,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AAC3B,WAAO,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC;AAC9B,SAAK,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AAC3B,aAAO,CAAC,EAAE,CAAC,IAAI;AAAA,IACnB;AAAA,EACJ;AACA,SAAO,QAAQ;AAEf,OAAK,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AAC3B,SAAK,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK;AAC3B,UAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG;AAC9C,eAAO,CAAC,EAAE,CAAC,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI;AAAA,MAC1C,OACK;AACD,eAAO,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,MAC9D;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,YAAY,SAAU,QAAQ,QAAQ,QAAQ,SAAS;AACzD,MAAI,SAAS,OAAO;AACpB,MAAI,SAAS,OAAO;AACpB,QAAM,cAAc;AAAA,IAChB,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,EACf;AACA,SAAO,WAAW,KAAK,WAAW,GAAG;AACjC,UAAM,aAAa,OAAO,MAAM,QAAQ,QAAQ,SAAS,GAAG,SAAS,GAAG,OAAO;AAC/E,QAAI,YAAY;AACZ,kBAAY,SAAS,QAAQ,OAAO,SAAS,CAAC,CAAC;AAC/C,kBAAY,SAAS,QAAQ,SAAS,CAAC;AACvC,kBAAY,SAAS,QAAQ,SAAS,CAAC;AACvC,QAAE;AACF,QAAE;AAAA,IACN,OACK;AACD,YAAM,qBAAqB,OAAO,MAAM,EAAE,SAAS,CAAC;AACpD,YAAM,oBAAoB,OAAO,SAAS,CAAC,EAAE,MAAM;AACnD,UAAI,qBAAqB,mBAAmB;AACxC,UAAE;AAAA,MACN,OACK;AACD,UAAE;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,MAAM,SAAU,QAAQ,QAAQ,OAAO,SAAS;AAClD,QAAM,eAAe,WAAW,CAAC;AACjC,QAAM,SAAS,aAAa,QAAQ,QAAQ,SAAS,cAAc,YAAY;AAC/E,SAAO,UAAU,QAAQ,QAAQ,QAAQ,YAAY;AACzD;AACA,IAAO,cAAQ;AAAA,EACX;AACJ;;;ACrEA,IAAM,aAAa;AACnB,SAAS,qBAAqB,QAAQ,QAAQ,MAAM,MAAM;AACtD,WAAS,SAAS,GAAG,SAAS,MAAM,UAAU;AAC1C,UAAM,OAAO,OAAO,MAAM;AAC1B,aAAS,SAAS,GAAG,SAAS,MAAM,UAAU;AAC1C,YAAM,OAAO,OAAO,MAAM;AAC1B,UAAI,WAAW,UAAU,SAAS,MAAM;AACpC,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,SAAS;AACzD,QAAM,SAAS,OAAO,MAAM;AAC5B,QAAM,SAAS,OAAO,MAAM;AAC5B,MAAI,WAAW,QAAQ;AACnB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AAC1D,WAAO;AAAA,EACX;AACA,QAAM,aAAa,QAAQ;AAC3B,MAAI,CAAC,YAAY;AAEb,WAAO,QAAQ,mBAAmB,WAAW;AAAA,EACjD;AACA,UAAQ,aAAa,QAAQ,cAAc,CAAC;AAC5C,MAAI,QAAQ,QAAQ,WAAW,MAAM;AACrC,MAAI,OAAO,UAAU,aAAa;AAC9B,YAAQ,WAAW,MAAM,IAAI,QAAQ,WAAW,QAAQ,MAAM;AAAA,EAClE;AACA,MAAI,OAAO,UAAU,aAAa;AAC9B,WAAO;AAAA,EACX;AACA,UAAQ,aAAa,QAAQ,cAAc,CAAC;AAC5C,MAAI,QAAQ,QAAQ,WAAW,MAAM;AACrC,MAAI,OAAO,UAAU,aAAa;AAC9B,YAAQ,WAAW,MAAM,IAAI,QAAQ,WAAW,QAAQ,MAAM;AAAA,EAClE;AACA,MAAI,OAAO,UAAU,aAAa;AAC9B,WAAO;AAAA,EACX;AACA,SAAO,UAAU;AACrB;AACO,IAAMC,cAAa,SAAS,iBAAiB,SAAS;AACzD,MAAI,CAAC,QAAQ,aAAa;AACtB;AAAA,EACJ;AACA,QAAM,eAAe;AAAA,IACjB,YAAY,QAAQ,WAAW,QAAQ,QAAQ;AAAA,IAC/C,iBAAiB,QAAQ,WAAW,QAAQ,QAAQ;AAAA,EACxD;AACA,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,SAAS,QAAQ;AACvB,QAAM,SAAS,QAAQ;AACvB,QAAM,OAAO,OAAO;AACpB,QAAM,OAAO,OAAO;AACpB,MAAI;AACJ,MAAI,OAAO,KACP,OAAO,KACP,CAAC,aAAa,cACd,OAAO,aAAa,oBAAoB,WAAW;AACnD,iBAAa,kBAAkB,CAAC,qBAAqB,QAAQ,QAAQ,MAAM,IAAI;AAAA,EACnF;AAEA,SAAO,aAAa,QAChB,aAAa,QACb,WAAW,QAAQ,QAAQ,YAAY,YAAY,YAAY,GAAG;AAClE,YAAQ;AACR,YAAQ,IAAI,aAAY,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC;AACpD,YAAQ,KAAK,OAAO,KAAK;AACzB;AAAA,EACJ;AAEA,SAAO,aAAa,aAAa,QAC7B,aAAa,aAAa,QAC1B,WAAW,QAAQ,QAAQ,OAAO,IAAI,YAAY,OAAO,IAAI,YAAY,YAAY,GAAG;AACxF,aAAS,OAAO,IAAI;AACpB,aAAS,OAAO,IAAI;AACpB,YAAQ,IAAI,aAAY,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC;AACtD,YAAQ,KAAK,OAAO,MAAM;AAC1B;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,aAAa,eAAe,MAAM;AAClC,QAAI,SAAS,MAAM;AAEf,cAAQ,UAAU,MAAS,EAAE,KAAK;AAClC;AAAA,IACJ;AAEA,aAAS,UAAU;AAAA,MACf,IAAI;AAAA,IACR;AACA,SAAK,QAAQ,YAAY,QAAQ,OAAO,YAAY,SAAS;AACzD,aAAO,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC;AAAA,IAClC;AACA,YAAQ,UAAU,MAAM,EAAE,KAAK;AAC/B;AAAA,EACJ;AACA,MAAI,aAAa,eAAe,MAAM;AAElC,aAAS,UAAU;AAAA,MACf,IAAI;AAAA,IACR;AACA,SAAK,QAAQ,YAAY,QAAQ,OAAO,YAAY,SAAS;AACzD,aAAO,IAAI,KAAK,EAAE,IAAI,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC;AAAA,IAC9C;AACA,YAAQ,UAAU,MAAM,EAAE,KAAK;AAC/B;AAAA,EACJ;AAEA,SAAO,aAAa;AACpB,SAAO,aAAa;AAEpB,QAAM,WAAW,OAAO,MAAM,YAAY,OAAO,UAAU;AAC3D,QAAM,WAAW,OAAO,MAAM,YAAY,OAAO,UAAU;AAC3D,QAAM,MAAM,YAAI,IAAI,UAAU,UAAU,YAAY,YAAY;AAChE,QAAM,eAAe,CAAC;AACtB,WAAS,UAAU;AAAA,IACf,IAAI;AAAA,EACR;AACA,OAAK,QAAQ,YAAY,QAAQ,OAAO,YAAY,SAAS;AACzD,QAAI,IAAI,SAAS,QAAQ,QAAQ,UAAU,IAAI,GAAG;AAE9C,aAAO,IAAI,KAAK,EAAE,IAAI,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC;AAC1C,mBAAa,KAAK,KAAK;AAAA,IAC3B;AAAA,EACJ;AACA,MAAI,aAAa;AACjB,MAAI,QAAQ,WACR,QAAQ,QAAQ,UAChB,QAAQ,QAAQ,OAAO,eAAe,OAAO;AAC7C,iBAAa;AAAA,EACjB;AACA,MAAI,qBAAqB;AACzB,MAAI,QAAQ,WACR,QAAQ,QAAQ,UAChB,QAAQ,QAAQ,OAAO,oBAAoB;AAC3C,yBAAqB;AAAA,EACzB;AACA,QAAM,qBAAqB,aAAa;AACxC,OAAK,QAAQ,YAAY,QAAQ,OAAO,YAAY,SAAS;AACzD,UAAM,gBAAgB,IAAI,SAAS,QAAQ,QAAQ,UAAU;AAC7D,QAAI,gBAAgB,GAAG;AAEnB,UAAI,SAAS;AACb,UAAI,cAAc,qBAAqB,GAAG;AACtC,iBAAS,mBAAmB,GAAG,mBAAmB,oBAAoB,oBAAoB;AACtF,mBAAS,aAAa,gBAAgB;AACtC,cAAI,WAAW,UAAU,UAAU,SAAS,YAAY,QAAQ,YAAY,YAAY,GAAG;AAEvF,mBAAO,IAAI,MAAM,EAAE,EAAE,OAAO,GAAG,GAAG,OAAO,UAAU;AACnD,gBAAI,CAAC,oBAAoB;AAErB,qBAAO,IAAI,MAAM,EAAE,EAAE,CAAC,IAAI;AAAA,YAC9B;AACA,qBAAS;AACT,oBAAQ,IAAI,aAAY,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC;AACtD,oBAAQ,KAAK,OAAO,MAAM;AAC1B,yBAAa,OAAO,kBAAkB,CAAC;AACvC,qBAAS;AACT;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC,QAAQ;AAET,eAAO,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC;AAAA,MAClC;AAAA,IACJ,OACK;AAED,eAAS,IAAI,SAAS,aAAa,IAAI;AACvC,eAAS,IAAI,SAAS,aAAa,IAAI;AACvC,cAAQ,IAAI,aAAY,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC;AACtD,cAAQ,KAAK,OAAO,MAAM;AAAA,IAC9B;AAAA,EACJ;AACA,UAAQ,UAAU,MAAM,EAAE,KAAK;AACnC;AACAA,YAAW,aAAa;AACxB,IAAM,UAAU;AAAA,EACZ,YAAY,GAAG,GAAG;AACd,WAAO,IAAI;AAAA,EACf;AAAA,EACA,cAAc,MAAM;AAChB,WAAO,CAAC,GAAG,MAAM,EAAE,IAAI,IAAI,EAAE,IAAI;AAAA,EACrC;AACJ;AACO,IAAMC,eAAc,SAASC,mBAAkB,SAAS;AAC3D,MAAI,CAAC,QAAQ,QAAQ;AACjB;AAAA,EACJ;AACA,QAAM,cAAc,QAAQ;AAC5B,MAAI,YAAY,OAAO,KAAK;AACxB;AAAA,EACJ;AACA,MAAI;AACJ,MAAI;AACJ,QAAM,QAAQ;AACd,QAAM,QAAQ,QAAQ;AAEtB,MAAI,WAAW,CAAC;AAChB,MAAI,WAAW,CAAC;AAChB,QAAM,WAAW,CAAC;AAClB,OAAK,SAAS,OAAO;AACjB,QAAI,UAAU,MAAM;AAChB,UAAI,MAAM,CAAC,MAAM,KAAK;AAClB,cAAM,sBAAsB;AAE5B,YAAI,MAAM,mBAAmB,EAAE,CAAC,MAAM,KAClC,MAAM,mBAAmB,EAAE,CAAC,MAAM,YAAY;AAC9C,mBAAS,KAAK,SAAS,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;AAAA,QAC9C,OACK;AACD,gBAAM,IAAI,MAAM,qFACW,MAAM,mBAAmB,EAAE,CAAC,CAAC,EAAE;AAAA,QAC9D;AAAA,MACJ,OACK;AACD,cAAM,cAAc;AACpB,YAAI,MAAM,WAAW,EAAE,WAAW,GAAG;AAEjC,mBAAS,KAAK;AAAA,YACV,OAAO,SAAS,aAAa,EAAE;AAAA,YAC/B,OAAO,MAAM,WAAW,EAAE,CAAC;AAAA,UAC/B,CAAC;AAAA,QACL,OACK;AAED,mBAAS,KAAK;AAAA,YACV,OAAO,SAAS,aAAa,EAAE;AAAA,YAC/B,OAAO,MAAM,WAAW;AAAA,UAC5B,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,aAAW,SAAS,KAAK,QAAQ,WAAW;AAC5C,OAAK,QAAQ,SAAS,SAAS,GAAG,SAAS,GAAG,SAAS;AACnD,aAAS,SAAS,KAAK;AACvB,UAAM,YAAY,MAAM,IAAI,MAAM,EAAE;AACpC,UAAM,eAAe,MAAM,OAAO,QAAQ,CAAC,EAAE,CAAC;AAC9C,QAAI,UAAU,CAAC,MAAM,YAAY;AAE7B,eAAS,KAAK;AAAA,QACV,OAAO,UAAU,CAAC;AAAA,QAClB,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,aAAW,SAAS,KAAK,QAAQ,cAAc,OAAO,CAAC;AACvD,QAAM,iBAAiB,SAAS;AAChC,OAAK,QAAQ,GAAG,QAAQ,gBAAgB,SAAS;AAC7C,UAAM,YAAY,SAAS,KAAK;AAChC,UAAM,OAAO,UAAU,OAAO,GAAG,UAAU,KAAK;AAAA,EACpD;AAEA,QAAM,iBAAiB,SAAS;AAChC,MAAI;AACJ,MAAI,iBAAiB,GAAG;AACpB,SAAK,QAAQ,GAAG,QAAQ,gBAAgB,SAAS;AAC7C,YAAM,eAAe,SAAS,KAAK;AACnC,cAAQ,IAAI,cAAa,MAAM,aAAa,KAAK,GAAG,aAAa,KAAK;AACtE,cAAQ,KAAK,OAAO,aAAa,KAAK;AAAA,IAC1C;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ,UAAU;AACnB,YAAQ,UAAU,KAAK,EAAE,KAAK;AAC9B;AAAA,EACJ;AACA,UAAQ,KAAK;AACjB;AACAD,aAAY,aAAa;AAClB,IAAME,8BAA6B,SAASA,4BAA2B,SAAS;AACnF,MAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;AAC/B;AAAA,EACJ;AACA,QAAM,oBAAoB,QAAQ;AAClC,MAAI,kBAAkB,OAAO,KAAK;AAC9B;AAAA,EACJ;AACA,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI;AACJ,WAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AACzC,YAAQ,QAAQ,SAAS,KAAK;AAC9B,UAAM,aAAa,MAAM;AACzB,UAAM,UAAU,IAAI,MAAM;AAAA,EAC9B;AACA,UAAQ,UAAU,KAAK,EAAE,KAAK;AAClC;AACAA,4BAA2B,aAAa;AACjC,IAAMC,iBAAgB,SAAS,oBAAoB,SAAS;AAC/D,MAAI,CAAC,QAAQ,QAAQ;AACjB,UAAM,iBAAiB,QAAQ;AAC/B,QAAI,eAAe,CAAC,MAAM,YAAY;AAClC,YAAM,iBAAiB;AACvB,cAAQ,UAAU,IAAI,eAAe,CAAC,CAAC;AACvC,cACK,UAAU;AAAA,QACX,eAAe,CAAC;AAAA,QAChB,SAAS,QAAQ,UAAU,UAAU,CAAC,GAAG,EAAE;AAAA,QAC3C;AAAA,MACJ,CAAC,EACI,KAAK;AAAA,IACd;AACA;AAAA,EACJ;AACA,QAAM,cAAc,QAAQ;AAC5B,MAAI,YAAY,OAAO,KAAK;AACxB;AAAA,EACJ;AACA,QAAM,aAAa;AACnB,MAAI;AACJ,MAAI;AACJ,OAAK,QAAQ,YAAY;AACrB,QAAI,SAAS,MAAM;AACf;AAAA,IACJ;AACA,YAAQ,IAAI,gBAAe,WAAW,IAAI,CAAC;AAC3C,YAAQ,KAAK,OAAO,IAAI;AAAA,EAC5B;AACA,UAAQ,KAAK;AACjB;AACAA,eAAc,aAAa;AAC3B,IAAM,yBAAyB,CAAC,OAAO,OAAO,cAAc;AACxD,MAAI,OAAO,UAAU,YAAY,MAAM,CAAC,MAAM,KAAK;AAC/C,WAAO,SAAS,MAAM,UAAU,CAAC,GAAG,EAAE;AAAA,EAC1C,WACS,MAAM,QAAQ,SAAS,KAAK,UAAU,CAAC,MAAM,GAAG;AACrD,WAAO,IAAI,KAAK;AAAA,EACpB;AACA,MAAI,eAAe,CAAC;AACpB,aAAW,cAAc,OAAO;AAC5B,UAAM,YAAY,MAAM,UAAU;AAClC,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,UAAI,UAAU,CAAC,MAAM,YAAY;AAC7B,cAAM,gBAAgB,SAAS,WAAW,UAAU,CAAC,GAAG,EAAE;AAC1D,cAAM,cAAc,UAAU,CAAC;AAC/B,YAAI,gBAAgB,CAAC,OAAO;AACxB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,gBAAgB,cAAc,cAAc;AAC7D;AAAA,QACJ,WACS,iBAAiB,gBACtB,cAAc,cAAc;AAC5B;AAAA,QACJ;AAAA,MACJ,WACS,UAAU,CAAC,MAAM,GAAG;AACzB,cAAM,cAAc,SAAS,WAAW,UAAU,CAAC,GAAG,EAAE;AACxD,YAAI,eAAe,cAAc;AAC7B;AAAA,QACJ;AAAA,MACJ,WACS,UAAU,WAAW,KAC1B,SAAS,YAAY,EAAE,KAAK,cAAc;AAC1C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAMC,gCAA+B,CAAC,YAAY;AACrD,MAAI,CAAC,WAAW,CAAC,QAAQ,UAAU;AAC/B;AAAA,EACJ;AACA,QAAM,oBAAoB,QAAQ;AAClC,MAAI,kBAAkB,OAAO,KAAK;AAC9B;AAAA,EACJ;AACA,QAAM,aAAa;AACnB,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI;AACJ,QAAM,QAAQ;AAAA,IACV,IAAI;AAAA,EACR;AACA,WAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AACzC,YAAQ,QAAQ,SAAS,KAAK;AAC9B,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,SAAS,aAAa;AAC7B,aAAO,uBAAuB,YAAY,MAAM,WAAW,MAAM,MAAM;AAAA,IAC3E;AACA,QAAI,MAAM,IAAI,MAAM,MAAM,QAAQ;AAE9B,YAAM,IAAI,IAAI,MAAM;AAAA,IACxB;AAAA,EACJ;AACA,UAAQ,UAAU,KAAK,EAAE,KAAK;AAClC;AACAA,8BAA6B,aAAa;;;ACnZnC,IAAMC,cAAa,SAAS,gBAAgB,SAAS;AACxD,MAAI,QAAQ,gBAAgB,MAAM;AAC9B,QAAI,QAAQ,iBAAiB,MAAM;AAC/B,UAAI,QAAQ,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,GAAG;AACpD,gBAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC;AAAA,MACnD,OACK;AACD,gBAAQ,UAAU,MAAS;AAAA,MAC/B;AAAA,IACJ,OACK;AACD,cAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC;AAAA,IACnD;AACA,YAAQ,KAAK;AAAA,EACjB,WACS,QAAQ,iBAAiB,MAAM;AACpC,YAAQ,UAAU,CAAC,QAAQ,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK;AAAA,EAC1D;AACJ;AACAA,YAAW,aAAa;;;ACnBxB,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAC3B,IAAI,kBAAkB;AACtB,SAAS,kBAAkB,SAAS,UAAU;AAC1C,MAAI;AACJ,MAAI,CAAC,iBAAiB;AAClB,QAAI;AACJ,SAAK,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAClI,iBAAW,IAAI,QAAQ,SAAS,eAAe;AAAA,IACnD,OACK;AACD,UAAI,CAAC,UAAU;AACX,eAAO;AAAA,MACX;AACA,YAAM,QAAQ,IAAI,MAAM,gJAAgJ;AAExK,YAAM,6BAA6B;AACnC,YAAM;AAAA,IACV;AACA,sBAAkB;AAAA,MACd,MAAM,SAAU,MAAM,MAAM;AACxB,eAAO,SAAS,aAAa,SAAS,WAAW,MAAM,IAAI,CAAC;AAAA,MAChE;AAAA,MACA,OAAO,SAAU,MAAMC,QAAO;AAC1B,cAAM,UAAU,SAAS,YAAY,SAAS,eAAeA,MAAK,GAAG,IAAI;AACzE,iBAAS,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,QAAQ,KAAK;AACxC,cAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG;AAChB,kBAAM,QAAQ,IAAI,MAAM,mBAAmB;AAC3C,kBAAM,kBAAkB;AAAA,UAC5B;AAAA,QACJ;AACA,eAAO,QAAQ,CAAC;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAMC,cAAa,SAAS,gBAAgB,SAAS;AACxD,MAAI,QAAQ,aAAa,UAAU;AAC/B;AAAA,EACJ;AACA,QAAM,OAAO,QAAQ;AACrB,QAAM,QAAQ,QAAQ;AACtB,QAAM,YAAa,QAAQ,WACvB,QAAQ,QAAQ,YAChB,QAAQ,QAAQ,SAAS,aACzB;AACJ,MAAI,KAAK,SAAS,aAAa,MAAM,SAAS,WAAW;AACrD,YAAQ,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK;AACtC;AAAA,EACJ;AAEA,QAAM,iBAAiB,kBAAkB,QAAQ,OAAO;AACxD,MAAI,CAAC,gBAAgB;AAGjB,YAAQ,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK;AACtC;AAAA,EACJ;AACA,QAAMC,QAAO,eAAe;AAC5B,UAAQ,UAAU,CAACA,MAAK,MAAM,KAAK,GAAG,GAAG,SAAS,CAAC,EAAE,KAAK;AAC9D;AACAD,YAAW,aAAa;AACjB,IAAME,eAAc,SAAS,iBAAiB,SAAS;AAC1D,MAAI,QAAQ,QAAQ;AAChB;AAAA,EACJ;AACA,QAAM,iBAAiB,QAAQ;AAC/B,MAAI,eAAe,CAAC,MAAM,WAAW;AACjC;AAAA,EACJ;AACA,QAAM,gBAAgB;AAEtB,QAAMH,SAAQ,kBAAkB,QAAQ,SAAS,IAAI,EAAE;AACvD,UAAQ,UAAUA,OAAM,QAAQ,MAAM,cAAc,CAAC,CAAC,CAAC,EAAE,KAAK;AAClE;AACAG,aAAY,aAAa;AACzB,IAAM,mBAAmB,SAAU,OAAO;AACtC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS;AACb,QAAM,cAAc;AACpB,MAAI;AACJ,QAAM,QAAQ,MAAM,MAAM,IAAI;AAC9B,OAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AACtC,WAAO,MAAM,CAAC;AACd,UAAM,YAAY,KAAK,MAAM,GAAG,CAAC;AACjC,QAAI,cAAc,KAAK;AACnB,eAAS,YAAY,KAAK,IAAI;AAC9B,mBAAa;AAEb,YAAM,UAAU,IACZ,SACI,OAAO,CAAC,IACR,MACA,OAAO,CAAC,IACR,OACA,OAAO,CAAC,IACR,MACA,OAAO,CAAC,IACR;AAAA,IACZ,WACS,cAAc,KAAK;AACxB,YAAM,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,MAAM,CAAC;AACjC,UAAI,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,KAAK;AAElC,kBAAU,MAAM,CAAC;AACjB,cAAM,CAAC,IAAI,MAAM,IAAI,CAAC;AACtB,cAAM,IAAI,CAAC,IAAI;AAAA,MACnB;AAAA,IACJ,WACS,cAAc,KAAK;AACxB,YAAM,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,MAAM,CAAC;AAAA,IACrC;AAAA,EACJ;AACA,SAAO,MAAM,KAAK,IAAI;AAC1B;AACO,IAAMC,iBAAgB,SAAS,mBAAmB,SAAS;AAC9D,MAAI,QAAQ,QAAQ;AAChB;AAAA,EACJ;AACA,QAAM,iBAAiB,QAAQ;AAC/B,MAAI,eAAe,CAAC,MAAM,WAAW;AACjC;AAAA,EACJ;AACA,QAAM,gBAAgB;AAEtB,UACK,UAAU,CAAC,iBAAiB,cAAc,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,EAC5D,KAAK;AACd;AACAA,eAAc,aAAa;;;AC1H3B,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,SAAS;AACjB,SAAK,YAAY,IAAI,kBAAU,OAAO;AACtC,SAAK,UAAU,KAAK,IAAI,aAAK,MAAM,EAC9B,OAAc,2BAAmC,YAAkBC,aAAkBA,aAAmB,mBAA0BA,WAAU,EAC5I,iBAAiB,CAAC;AACvB,SAAK,UAAU,KAAK,IAAI,aAAK,OAAO,EAC/B,OAAc,4BAAmCC,6BAAoC,aAAmBC,cAAoBA,cAAoBA,YAAW,EAC3J,iBAAiB,CAAC;AACvB,SAAK,UAAU,KAAK,IAAI,aAAK,SAAS,EACjC,OAAc,8BAAqCC,+BAAsC,eAAqBC,gBAAsBA,gBAAsBA,cAAa,EACvK,iBAAiB,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS;AACb,WAAO,KAAK,UAAU,QAAQ,OAAO;AAAA,EACzC;AAAA,EACA,KAAK,MAAM,OAAO;AACd,WAAO,KAAK,UAAU,QAAQ,IAAI,aAAY,MAAM,KAAK,CAAC;AAAA,EAC9D;AAAA,EACA,MAAM,MAAM,OAAO;AACf,WAAO,KAAK,UAAU,QAAQ,IAAI,cAAa,MAAM,KAAK,CAAC;AAAA,EAC/D;AAAA,EACA,QAAQ,OAAO;AACX,WAAO,KAAK,UAAU,QAAQ,IAAI,gBAAe,KAAK,CAAC;AAAA,EAC3D;AAAA,EACA,QAAQ,OAAO,OAAO;AAClB,WAAO,KAAK,MAAM,OAAO,KAAK,QAAQ,KAAK,CAAC;AAAA,EAChD;AAAA,EACA,MAAM,OAAO;AACT,WAAO,MAAM,KAAK;AAAA,EACtB;AACJ;AACA,IAAO,sBAAQ;;;AC1CA,SAAR,YAA6B,KAAK,OAAO;AAC5C,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC3B,YACI,yFAAyF,KAAK,KAAK;AACvG,QAAI,OAAO;AACP,aAAO,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;AAAA,IACpH;AAAA,EACJ;AACA,SAAO;AACX;;;ACRO,SAAS,OAAO,SAAS;AAC5B,SAAO,IAAI,oBAAY,OAAO;AAClC;AACA,IAAI;AACG,SAAS,KAAK,MAAM,OAAO;AAC9B,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,IAAI,oBAAY;AAAA,EACtC;AACA,SAAO,gBAAgB,KAAK,MAAM,KAAK;AAC3C;AACO,SAAS,MAAM,MAAM,OAAO;AAC/B,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,IAAI,oBAAY;AAAA,EACtC;AACA,SAAO,gBAAgB,MAAM,MAAM,KAAK;AAC5C;AACO,SAAS,QAAQ,OAAO,OAAO;AAClC,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,IAAI,oBAAY;AAAA,EACtC;AACA,SAAO,gBAAgB,QAAQ,OAAO,KAAK;AAC/C;AACO,SAAS,QAAQ,OAAO;AAC3B,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,IAAI,oBAAY;AAAA,EACtC;AACA,SAAO,gBAAgB,QAAQ,KAAK;AACxC;AACO,SAASC,OAAM,OAAO;AACzB,MAAI,CAAC,iBAAiB;AAClB,sBAAkB,IAAI,oBAAY;AAAA,EACtC;AACA,SAAO,gBAAgB,MAAM,KAAK;AACtC;", "names": ["clone", "patchFilter", "collectChildrenP<PERSON><PERSON><PERSON>er", "reverseFilter", "diffFilter", "patchFilter", "nested<PERSON><PERSON><PERSON><PERSON>er", "collectChildrenP<PERSON><PERSON><PERSON>er", "reverseFilter", "collectChildrenReverseFilter", "diffFilter", "patch", "diffFilter", "diff", "patchFilter", "reverseFilter", "diffFilter", "collectChildrenP<PERSON><PERSON><PERSON>er", "patchFilter", "collectChildrenReverseFilter", "reverseFilter", "clone"]}