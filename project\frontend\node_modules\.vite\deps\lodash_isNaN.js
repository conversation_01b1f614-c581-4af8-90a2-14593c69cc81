import {
  require_isObjectLike
} from "./chunk-MRQ46THD.js";
import {
  require_baseGetTag
} from "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/isNumber.js
var require_isNumber = __commonJS({
  "node_modules/lodash/isNumber.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var numberTag = "[object Number]";
    function isNumber(value) {
      return typeof value == "number" || isObjectLike(value) && baseGetTag(value) == numberTag;
    }
    module.exports = isNumber;
  }
});

// node_modules/lodash/isNaN.js
var require_isNaN = __commonJS({
  "node_modules/lodash/isNaN.js"(exports, module) {
    var isNumber = require_isNumber();
    function isNaN(value) {
      return isNumber(value) && value != +value;
    }
    module.exports = isNaN;
  }
});
export default require_isNaN();
//# sourceMappingURL=lodash_isNaN.js.map
