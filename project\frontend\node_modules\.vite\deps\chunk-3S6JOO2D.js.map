{"version": 3, "sources": ["../../lodash/_baseSum.js"], "sourcesContent": ["/**\n * The base implementation of `_.sum` and `_.sumBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the sum.\n */\nfunction baseSum(array, iteratee) {\n  var result,\n      index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var current = iteratee(array[index]);\n    if (current !== undefined) {\n      result = result === undefined ? current : (result + current);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseSum;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,QAAQ,OAAO,UAAU;AAChC,UAAI,QACA,QAAQ,IACR,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,UAAU,SAAS,MAAM,KAAK,CAAC;AACnC,YAAI,YAAY,QAAW;AACzB,mBAAS,WAAW,SAAY,UAAW,SAAS;AAAA,QACtD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}