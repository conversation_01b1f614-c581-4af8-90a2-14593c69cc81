import axios from 'axios';
import get from 'lodash/get';
import set from 'lodash/set';
import { defineStore } from 'pinia';

import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';
import { getEndpoint } from '@core/helpers/get-endpoint';

import { useWorkspaceStore } from './workspace';

const { dynamicFormApp } = useDynamicFormApp();

export const useFormStore = defineStore('form', () => {
  // Ref (states)
  const forms = ref<Types.Form[]>([]);
  const infos = ref<{ [slug: string]: { [locale: string]: any } }>({});
  const total = ref<number>(0);
  const currentPage = ref<number>(1);
  const pages = ref<number>(1);
  const pageSizes = ref<number[]>([50, 100, 200]);
  const previousUrl = ref<string | null>(null);
  const nextUrl = ref<string | null>(null);
  const perPage = ref(50);

  // Request controllers for cancellation
  const listRequestController = ref<AbortController>();

  // Function (actions)
  const getFormBySlug = (slug: string) => forms.value.find(form => form.slug === slug);

  const info = (slug, locale = 'th') => get(infos.value, [slug, locale]);

  const setForms = payload => {
    forms.value = payload;
  };

  const appendForms = payload => {
    forms.value.push(...payload);
  };

  const setInfos = payload => {
    infos.value = payload;
  };

  const setTotal = payload => {
    total.value = payload;
  };

  const setCurrentPage = payload => {
    currentPage.value = payload;
  };

  const setPages = payload => {
    pages.value = payload;
  };

  const setPreviousUrl = payload => {
    previousUrl.value = payload;
  };

  const setNextUrl = payload => {
    nextUrl.value = payload;
  };

  const deleteForm = formSlug => {
    const i = forms.value.findIndex(f => f.slug === formSlug);
    forms.value.splice(i, 1);
  };

  const setInfo = ({ formSlug, locale = 'th', formInfo }) => {
    infos.value = set(infos.value, [formSlug, locale], formInfo);
  };

  const setForm = ({ formSlug, form }) => {
    const i = forms.value.findIndex(f => f.slug === formSlug);
    if (i > -1) {
      Object.assign(forms.value[i], form);
    } else {
      forms.value.push(...form);
    }
  };

  // Helper function to create list request with cancellation support
  const createListRequest = ({ workspaceSlug, params }) => {
    let url = getEndpoint('WORKSPACE_FORM_LIST');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    const controller = new AbortController();
    const promise = axios.get(url, {
      params,
      signal: controller.signal,
    });
    return {
      promise,
      controller,
    };
  };

  const list = async (payload = {}, { cancelPrevious = true } = {}) => {
    if (cancelPrevious) {
      listRequestController.value?.abort();
    }

    const params = {
      page: currentPage.value || 1,
      page_size: perPage.value,
      ...payload,
    };
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;

    const { promise, controller } = createListRequest({ workspaceSlug, params });
    listRequestController.value = controller;

    const res = await promise;
    setForms(res.data.data);
    setTotal(res.data.total);
    setPages(res.data.last_page);
    setPreviousUrl(res.data.prev_page_url);
    setNextUrl(res.data.next_page_url);
    setCurrentPage(params.page);
    return res;
  };

  const listMore = async (payload = {}) => {
    const params = {
      page: 1,
      page_size: perPage.value,
      ...payload,
    };
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_FORM_LIST');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    const res = await axios.get(url, { params });
    appendForms(res.data.data);
    setTotal(res.data.total);
    setPages(res.data.last_page);
    setPreviousUrl(res.data.prev_page_url);
    setNextUrl(res.data.next_page_url);
    setCurrentPage(params.page);
    return res;
  };

  const create = async form => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_FORM_LIST');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':lang', dynamicFormApp.locale);
    const { data: newForm } = await axios.post(url, form);
    appendForms([newForm]);
    return newForm;
  };

  const getInfo = async ({ formSlug, locale = dynamicFormApp.locale }) => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_FORM_DETAIL');
    url = url.replace(':lang', locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.get(url);
    setInfo({ formSlug, formInfo: res.data, locale });
    return res.data;
  };

  const save = async ({ formSlug, ...form }) => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_FORM_DETAIL');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const { data: newForm } = await axios.patch(url, form);
    return newForm;
  };

  const clone = async form => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = `${getEndpoint('WORKSPACE_FORM_DETAIL')}clone/`;
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', form.source_slug);
    const { data: newForm } = await axios.post(url, form);
    return newForm;
  };

  const remove = async formSlug => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_FORM_DETAIL');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const res = await axios.delete(url);
    deleteForm(formSlug);
    return res.data;
  };

  const setActive = async ({ formSlug, active, update = true }) => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_FORM_DETAIL');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const { data: form } = await axios.patch(url, { is_active: active });
    if (update) {
      setForm({ formSlug, form });
    }

    return form;
  };

  const setHighlight = async ({ formSlug, highlight }) => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = `${getEndpoint('WORKSPACE_FORM_DETAIL')}mysettings/`;
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const { data: form } = await axios.post(url, { highlight });
    setForm({ formSlug, form });
    return form;
  };

  const saveStyling = async ({ formSlug, styling }) => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = `${getEndpoint('WORKSPACE_FORM_DETAIL')}styling/`;
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    const { data: form } = await axios.post(url, styling);
    return form;
  };

  const listCustomStatus = async ({ formSlug = '', params = {} }) => {
    const workspaceStore = useWorkspaceStore();
    const workspaceSlug = workspaceStore.currentWorkspace?.slug;
    let url = getEndpoint('WORKSPACE_CUSTOM_STATUS_LIST');
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':workspaceSlug', workspaceSlug);
    url = url.replace(':formSlug', formSlug);
    type APIResult = { id: number; value: string; options: { id: number; value: string }[] }[];
    const res = await axios.get<APIResult>(url, {
      params,
    });
    return res.data;
  };

  const apply = async slug => {
    let url = `${getEndpoint('NEW_APPLICATION')}`;
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':formSlug', slug);
    const res = await axios.post(url, {});
    return res.data;
  };

  const uploadLogoByFile = async ({ formSlug, file }: { formSlug: string; file: File }) => {
    let url = `${getEndpoint('FORM_PUBLIC_UPLOAD')}by_file/`;
    url = url.replace(':lang', dynamicFormApp.locale);
    url = url.replace(':formSlug', formSlug);
    const formData = new FormData();
    formData.append('data', file);
    const res = await axios.post<string>(url, formData);

    return res.data;
  };
  const uploadLogoByUrl = async ({ formSlug, url }: { formSlug: string; url: string }) => {
    let apiUrl = `${getEndpoint('FORM_PUBLIC_UPLOAD')}by_url/`;
    apiUrl = apiUrl.replace(':lang', dynamicFormApp.locale);
    apiUrl = apiUrl.replace(':formSlug', formSlug);
    const res = await axios.post<string>(apiUrl, { url });

    return res.data;
  };

  return {
    forms,
    infos,
    total,
    currentPage,
    pages,
    perPage,
    pageSizes,
    previousUrl,
    nextUrl,

    getFormBySlug,
    info,
    setForms,
    appendForms,
    setInfos,
    setTotal,
    setCurrentPage,
    setPages,
    setPreviousUrl,
    setNextUrl,
    deleteForm,
    setInfo,
    setForm,
    list,
    listMore,
    create,
    getInfo,
    save,
    clone,
    remove,
    setActive,
    setHighlight,
    saveStyling,
    listCustomStatus,
    apply,
    uploadLogoByFile,
    uploadLogoByUrl,
    listRequestController,
  };
});

export default { useFormStore };
