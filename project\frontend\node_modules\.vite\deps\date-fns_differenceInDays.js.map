{"version": 3, "sources": ["../../date-fns/esm/differenceInCalendarDays/index.js", "../../date-fns/esm/differenceInDays/index.js"], "sourcesContent": ["import getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport startOfDay from \"../startOfDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000;\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport default function differenceInCalendarDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfDayLeft = startOfDay(dirtyDateLeft);\n  var startOfDayRight = startOfDay(dirtyDateRight);\n  var timestampLeft = startOfDayLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfDayLeft);\n  var timestampRight = startOfDayRight.getTime() - getTimezoneOffsetInMilliseconds(startOfDayRight);\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a day is not constant\n  // (e.g. it's different in the day of the daylight saving time clock shift)\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_DAY);\n}", "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(dateLeft, dateRight) {\n  var diff = dateLeft.getFullYear() - dateRight.getFullYear() || dateLeft.getMonth() - dateRight.getMonth() || dateLeft.getDate() - dateRight.getDate() || dateLeft.getHours() - dateRight.getHours() || dateLeft.getMinutes() - dateRight.getMinutes() || dateLeft.getSeconds() - dateRight.getSeconds() || dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full days according to the local timezone\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n//=> 92\n */\nexport default function differenceInDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareLocalAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarDays(dateLeft, dateRight));\n  dateLeft.setDate(dateLeft.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastDayNotFull = Number(compareLocalAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}"], "mappings": ";;;;;;;;;;;;;;;AAGA,IAAI,sBAAsB;AAgCX,SAAR,yBAA0C,eAAe,gBAAgB;AAC9E,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,WAAW,aAAa;AAC7C,MAAI,kBAAkB,WAAW,cAAc;AAC/C,MAAI,gBAAgB,eAAe,QAAQ,IAAI,gCAAgC,cAAc;AAC7F,MAAI,iBAAiB,gBAAgB,QAAQ,IAAI,gCAAgC,eAAe;AAKhG,SAAO,KAAK,OAAO,gBAAgB,kBAAkB,mBAAmB;AAC1E;;;ACxCA,SAAS,gBAAgB,UAAU,WAAW;AAC5C,MAAI,OAAO,SAAS,YAAY,IAAI,UAAU,YAAY,KAAK,SAAS,SAAS,IAAI,UAAU,SAAS,KAAK,SAAS,QAAQ,IAAI,UAAU,QAAQ,KAAK,SAAS,SAAS,IAAI,UAAU,SAAS,KAAK,SAAS,WAAW,IAAI,UAAU,WAAW,KAAK,SAAS,WAAW,IAAI,UAAU,WAAW,KAAK,SAAS,gBAAgB,IAAI,UAAU,gBAAgB;AAClW,MAAI,OAAO,GAAG;AACZ,WAAO;AAAA,EACT,WAAW,OAAO,GAAG;AACnB,WAAO;AAAA,EAET,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAmDe,SAAR,iBAAkC,eAAe,gBAAgB;AACtE,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,OAAO,gBAAgB,UAAU,SAAS;AAC9C,MAAI,aAAa,KAAK,IAAI,yBAAyB,UAAU,SAAS,CAAC;AACvE,WAAS,QAAQ,SAAS,QAAQ,IAAI,OAAO,UAAU;AAIvD,MAAI,mBAAmB,OAAO,gBAAgB,UAAU,SAAS,MAAM,CAAC,IAAI;AAC5E,MAAI,SAAS,QAAQ,aAAa;AAElC,SAAO,WAAW,IAAI,IAAI;AAC5B;", "names": []}