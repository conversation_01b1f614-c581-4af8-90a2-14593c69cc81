import {
  require_arrayLikeKeys
} from "./chunk-EHIGHKKH.js";
import {
  require_baseKeys
} from "./chunk-D4QEHMHD.js";
import {
  require_isArrayLike
} from "./chunk-EI3ATIN2.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/keys.js
var require_keys = __commonJS({
  "node_modules/lodash/keys.js"(exports, module) {
    var arrayLikeKeys = require_arrayLikeKeys();
    var baseKeys = require_baseKeys();
    var isArrayLike = require_isArrayLike();
    function keys(object) {
      return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);
    }
    module.exports = keys;
  }
});

export {
  require_keys
};
//# sourceMappingURL=chunk-WIEA6MZB.js.map
