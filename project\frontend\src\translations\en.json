{"common": {"or": "or", "please_select": "Please select", "field": "field", "not_found": "not found", "name": "name", "label": "label", "done": "Done", "add": "Add", "remove": "Remove", "hide": "<PERSON>de", "show": "Show", "step": "Step", "section": "Section", "item": "<PERSON><PERSON>", "page": "Page", "male": "Male", "female": "Female", "description": "Description", "selected": "Selected", "more": "More", "submitted": "Submitted", "loading": "Loading . . .", "yes": "Yes", "no": "No"}, "name_prefix": {"mr": "Mr.", "mrs": "Mrs.", "miss": "Miss"}, "navigation": {"retry": "Retry", "back_to_home": "Go to Home Page", "page_not_found": "Page not found", "previous": "Previous", "next": "Next", "submit": "Submit", "save": "Save", "leave_warning": "Are you sure you want to leave this page ? Your inputted data will not be saved"}, "Select": {"search": "Search"}, "OfflineUploader": {"upload": "Upload your file here", "drag": "Drag file here, or", "choose_file": "Browse", "more_drag": "Drag file here, or", "more_choose_file": "Browse", "show_example": "Show example", "hide_example": "Hide example", "processing": "Processing..", "capture": "Capture", "file": "file | files", "supported_type": "File format : {accept}", "max_file_count": "File limit : {count}", "max_file_size": "Max size : {size} MB", "deleting": "Deleting", "please_enter_password": "Please enter password for the file '{name}' you uploaded", "invalid_password": "Password incorrect. Please re-enter correct password for the file '{name}' you uploaded", "picture_modal": {"capture_note": "Please capture the image without glare", "capture": "Capture", "cancel_question": "Are you sure you want to leave taking the photo?", "error_open_camera": "There is a problem while requesting your camera"}}, "InputDate": {"day": "Day", "month": "Month", "year": "Year", "year_old": "y.o.", "ce": "A.C.", "be": "B.E.", "unknown": "Unknown"}, "WidgetAddress": {"zipcode": "Zipcode", "province": "Province", "district": "District", "subdistrict": "Sub-district", "detail": "House no./Soi/Road"}, "AddressAutofill": {"search": {"label": "Search for Sub-district, District, Postal Code", "placeholder": "Search for Sub-district, District, Postal Code"}}, "OfflineAudioRecorder": {"transcribe": "Transcribe", "delete": "Delete", "placeholder": "Transcribe audio or fill the box"}, "SignaturePad": {"clear": "Clear", "resign": "Re-sign"}, "PopupWebview": {"please_open_browser": "Please continue in {browser}", "subtitle": "Your current browser does not support some features.", "action_button": "Open {browser}", "close_button": "Close this warning", "install": "Install {browser}", "ios": {"instructions": ["1. <PERSON><PERSON> the URL below.", "2. Open this URL in the Safari."], "copy_button": "Copy", "copied_button": "Copied!", "header": "Please continue in Safari"}, "android": {"header": ["{browser} not installed?", "Once installed, tab 'Open {browser}' button to continue."]}, "other_device": {"other_app": ["1. Download 'Google Chrome' at https://www.google.com/chrome/", "2. Open this website in Google Chrome"]}, "app": {"line": "Line", "facebook": "Facebook", "other_app": "unsupported app/browser"}, "browser": {"chrome": "Google Chrome", "firefox": "Firefox", "safari": "Safari", "huawei": "<PERSON><PERSON><PERSON>"}}, "PopupUnsupportedBrowser": {"continue": "Continue in this browser", "chrome_change_title": "Please continue in the latest version of Google Chrome", "chrome_change_desc": "Your current browser may not support some features. ", "chrome_change_action_btn": "Open in Google Chrome", "chrome_update_title": "Please update Google Chrome", "chrome_update_desc": "Your current browser may not support some features. ", "safari_change_title": "Please continue in the latest version of Safari", "safari_change_desc": "Your current browser may not support some features. ", "safari_change_action_1": "Copy the URL below", "safari_change_action_2": "Open this URL in Safari or Google Chrome", "safari_update_title": "Please update iOS version", "safari_update_desc": "Safari is outdated and may not support some features.\nYou can also use Google Chrome.", "safari_update_action_1": "Copy the URL below", "safari_update_action_2": "Open this URL in Google Chrome"}, "Report": {"response": "Response", "led": {"general_detail": {"full_name": "Defendant Full Name", "id_card": "ID card Number", "court_name": "Court Name", "court_type": "Court Type", "black_case": "Black Case Number", "black_case_year": "Black Case Year", "red_case": "Red Case Number", "red_case_year": "Red Case Year", "plaintiff": "Plaintiff", "defentdant": "Defentdant", "case_capital": "Case Capital", "baht_unit": "Baht"}, "judgement_info": {"bkr_prot_date": "Date of Judgement for Bankruptcy", "bkr_gaz_date": "Date of Announcement of Bankruptcy Judgment", "rbrk_date": "Date of Discharge from Bankruptcy", "rgaz_date": "Date of Discharge Announcement", "df_expire_date": "Debtor's Discharge Date"}}}, "List": {"add": "Add entry", "maxed_out": "Cannot add more entry", "fill_empty_value": "Please fill empty field(s)"}, "errors": {"error": "Can't {action} ({error})", "server_error": "Service Unavailable", "save": "Saving or submitting is unavailable", "upload_file": "Can't upload file: {name}", "delete_file": "Can't delete file", "unsupported_file": "File '{name}' is not supported", "incorrect_document_types": "Incorrect document types", "too_big_file": "File size too big", "too_many_pages": "The total number of pages exceeds {max_pages}. Excess pages will be automatically removed.", "unprocessable_file": "Unprocessable File", "unauthorized": "Unauthorized", "page_not_found": "Page not found", "locked": "This application is already submitted", "inactive": "We're sorry, <PERSON> is unavailable now.", "schema": "Cannot retrieve form information", "field_answer_not_found": "No answer for this field", "forbidden": "You do not have permission to access this page"}, "home": {"title": "Online Application", "subtitle": "", "start_btn": "Apply Now", "admin_btn": "for admin", "cant_create": "Cannot create new application, Please try again later"}, "login": {"title": "Applications administration system", "email": "E-mail", "password": "Password", "error_invalid_email": "Please enter a valid email", "error_invalid_password": "Please enter a valid password", "login_btn": "Log in", "error_login_credential": "Incorrect email or password.", "error_login_attempts": "Too many failed attempts, try again in a few hours", "error_invalid_account_social_auth": "Invalid credential.", "error_invalid_account_domain_social_auth": "Please sign up with your corporate email address."}, "report": {"error_cant_load_report": "Can't load report", "error_cant_load_event_tracking": "Can't load additional device information", "show_hidden": "Show hidden fields", "show_empty": "Show empty fields", "one_page": "Show in one page"}, "not_found": {"back_to_home": "Go to Home Page", "skipped": "The destination page is saved and can't be revisited"}, "thankyou": {"back_to_home": "Go to Home Page", "sent": "The application is submitted", "will_contact": "We will contact you back within 4 working days for additional information", "thanks": "Thank you for your interest"}, "application_not_found": {"error_header": "Application Not Found", "error_message": "The application you are looking for does not exist. Please check the URL and try again.", "code": "404"}, "FooterBar": {"cant_save": "Save failed"}, "NavBar": {"schema": "<PERSON><PERSON><PERSON>", "builder": "Edit Form", "builder-for-page": "Dynamic Pages", "style": "<PERSON><PERSON>", "dashboard": "Dashboard", "logout": "Log out", "applist": "Application List", "application": "Application List", "setting": "App Setting", "user": "Users", "role": "Team", "main_menu": "Main menu", "edit": "Edit", "manage": "Manage", "form": "Form", "flows": "Flows", "submissions": "Submissions", "billing": "Billing", "manage_users": "Manage Users", "api_token": "API Token", "help_center": "Help Center", "tools": "Tools"}, "dashboard": {"application_status": {"ekyc:need_review": "Need Review", "ekyc:pass": "Pass", "ekyc:fail": "Fail", "incomplete": "Incomplete", "created": "Created", "started": "Started", "all": "All", "submitted": "Submitted", "complete": "Complete"}, "header": "Application list", "application": "Application", "form": "Form", "view_report": "View report", "view_form": "View form", "previous": "Previous", "next": "Next", "page": "Page", "user": "User", "create": "Create", "edit": "Edit", "delete": "Delete", "info": "Info", "submitted_filter": "Submitted form only", "all_steps": "All steps", "filter_by": "Filter by", "exclude": "Exclude", "filter_unset": "Remove this filter"}, "EkycPage": {"already_finished": "This step has already been completed on another device, You may proceed to the next step", "status": "Status", "score": "Score", "download": "Download", "video": "Video", "image": "Image", "refresh_browser": "<PERSON><PERSON><PERSON>er"}, "InputTelephone": {"invalid_country": "Invalid phone number", "invalid_length": "Invalid phone number", "not_a_number": "Invalid phone number", "too_long": "Invalid phone number", "too_short": "Invalid phone number", "required": "Please enter a valid mobile phone number"}, "OTP": {"send_btn": "Send OTP", "resend_btn": "Request a new code", "heading": "Enter the {digits} digit OTP code", "subheading": "we sent to <b>{mobile}</b>", "check_btn": "Check", "sent_message": "Please enter the verification code we sent to", "reference": "Reference", "already_sent_message": "We already sent an SMS with OTP code to the phone number you requested since last time yout visited this page) ", "not_yet_received": "Did not receive a code?", "remaining_time": "Remaining Time: ", "resend_cooldown": "Please wait for {cooldownLeft} second(s) to request a new code", "change_number": "Change mobile number", "change_number_cooldown": "Please wait for {cooldownLeft} second(s) to change mobile number", "recaptcha_checkbox_instruction": "Please check the box below and follow the instruction", "source_invalid": "Please enter a valid mobile phone number", "passed": "Correct OTP code", "not_passed": "Please enter a valid code", "error_message": "Please enter a valid code", "error_code_invalid": "Incorrect OTP code", "error_ref_invalid": "Reference Code is invalid", "error_ref_expired": "Code is expired", "error_ref_max_attempt": "Maximum number of verification is exceeded", "error_send": "Cannot request OTP", "error_check": "Network error, Please try again"}, "OtpEmail": {"send_btn": "Send OTP", "resend_btn": "Resend code", "heading": "Verify your Email", "subheading": "Enter the Validation Code we sent to <b>{email}</b>", "check_btn": "Check", "sent_message": "Please enter the verification code we sent to", "reference": "Reference", "already_sent_message": "We already sent an E-mail with {digits} digits code to the phone number you requested since last time yout visited this page) ", "not_yet_received": "Did not receive a code?", "remaining_time": "Remaining Time: ", "resend_cooldown": "Please wait for {cooldownLeft} second(s) to request a new code", "change_number": "Change E-mail", "change_number_cooldown": "Please wait for {cooldownLeft} second(s) to change E-mail", "recaptcha_checkbox_instruction": "Please check the box below and follow the instruction", "source_invalid": "Please enter a valid email address", "passed": "Correct OTP code", "not_passed": "Please enter a valid code", "error_message": "Please enter a valid code", "error_code_invalid": "Code is invalid", "error_ref_invalid": "Reference Code is invalid", "error_ref_expired": "Code is expired", "error_ref_max_attempt": "Maximum number of verification is exceeded", "error_send": "Cannot request OTP", "error_check": "Network error, Please try again"}, "BankStatementUploader": {"validation_transaction_oldest_before": "Your oldest transaction must be greater than {number} months.", "validation_transaction_latest_after": "Your most recent transaction must be within this month or last month.", "validation_transaction_min_month": "You must have a combined statement of at least {number} months.", "validation_valid_structure": "Statement format doesn't match. Please upload file again.", "validation_name_match": "The account name doesn't match. Please upload file again.", "validation_has_transaction": "There are no statements in this document. Please upload a new file.", "validation_same_creation_mod_date": "The information in the document may have been modified. Please upload a new file.", "validation_valid_fonts": "The information in the document may have been modified. Please upload a new file.", "validation_trustable_pdf_producer": "The information in the document may have been modified. Please upload a new file.", "validation_valid_account_number": "Unable to read the statement. Please upload your bank statement from mobile banking only.", "validation_producer_check": "The information in the document may have been modified. Please upload a new file.", "validation_default": "Error uploading. Please upload the file again.", "instruction": "Upload Bank Statement (PDF only)", "recent_upload": "Recent uploaded", "recent_upload_items": "{number} items", "select_files_again": "Select files again", "select_bank_again": "Select a new bank", "upload": "Select", "check": "Check", "modal_bank_title": "Select your bank", "provider_insctuction": {"th-bay": ["Open the KMA application", "Select the \"Personal Accounts\" menu", "Go to the account for which you want to request a statement", "Press the \"Request Statement\" button", "Select the desired time range or date you want", "Select the language you prefer", "Enter the email details for receiving the statement", "Press the \"Submit Request\" button", "On the \"Request Statement Successful\" page, click the \"Finish\" button", "Go to the email used to request the statement and download the file"], "th-kbank": ["Open the K Plus application", "Select the \"Transactions\" menu", "Enter the password to log in", "Select the \"Statement\" menu", "Choose the desired language, select the document delivery method, and select the month for which you want the statement", "Press the \"Submit Request\" button", "Go to the email used to request the statement and download the file"], "th-ktb": ["Open the Krungthai Next application", "Go to \"Account\"", "Press the \"Other Menu\" button", "Select \"Statement\"", "Specify the desired time period for requesting the statement and select the preferred language", "Click on the \"Send Statement\" button", "The information will be sent to the specified email address", "Go to the email used to request the statement and download the file"], "th-scb": ["Open the SCB Easy application", "Select the \"My Transactions\" menu.", "Select the \"Consolidation page\" menu.", "Select the account you want to request a statement for", "Select \"Other Services\"", "Select \"Request Account Statement\"", "Select the desired time period", "Click \"Request\"", "Go to the email used to request the statement and download the file"], "th-ttb": ["Launch the TTB touch application", "Enter the password to log in", "Select the account for which you want to request information", "Press the \"Other\" button", "Select the \"Request statement\" menu", "Select \"Language\"", "Choose \"Detailed statement\"", "Select the period of time that you would like to request a statement", "Press the \"Issue document\" button", "Press the \"Save file\" button"], "th-bbl": ["Go to BualuangM Banking app", "Go to \"Account\"", "Enter the password to login", "Select the account you wish", "Select the 3 dots in the upper right corner", "Select Request statement", "Choose the desired time frame and language, then submit the request", "Go to the email used to request the statement and download the file"], "th-gsb": ["Open the MyMo application by GSB", "Select the menu \"Account\"", "Select the account for which you want to request a transaction statement", "Select \"Statement\"", "Select the desired time frame and language", "Press the \"Submit Request\" button", "On the success page, click the \"Finish\" button", "Go to the email used to request the statement and download the file"]}, "provider_name": {"th-bay": "Bank of Ayudhya", "th-kbank": "Kasikornbank", "th-ktb": "Krung Thai Bank Public Co. Ltd.", "th-scb": "Siam Commercial Bank", "th-ttb": "TMB Thanachart Bank Public Company Limited", "th-bbl": "Bangkok Bank", "th-gsb": "Government Savings Bank", "th-lh": "LH Bank", "th-uob": "UOB Bank", "th-baac": "Bank for Agriculture and Agricultural Cooperatives"}, "provider_sub_title": {"th-ttb": "(Condensed statement are not supported)", "th-bay": "", "th-kbank": "", "th-ktb": "", "th-scb": "", "th-bbl": "", "th-gsb": ""}, "modal_password_title": "Enter password", "modal_password_text": "Your Bank Statement is encrypted", "modal_password_desc": "Please enter the password to unlock your bank statement for text extraction.", "modal_password_field_label": "Bank statement password", "modal_password_placeholder": "Password", "modal_validating_text": "Extracting text...", "modal_validating_desc": "Hang tight. We are reading and validating your bank statement.", "modal_error_common_text": "Cannot connect to the server", "modal_error_common_desc": "Please upload your file again or select a new bank.", "modal_error_file_text": "Invalid file format", "modal_error_file_desc": "Please upload your PDF bank statement, exported from your mobile banking application.", "modal_error_max_file_size_text": "The file size is larger than specified.", "modal_error_max_file_size_desc": "Please upload your statement in PDF format no larger than {size} MB.", "modal_error_max_file_count_text": "The maximum number of files has been reached.", "modal_error_max_file_count_desc": "Please upload your statement of no more than {count} files.", "modal_error_password_title": "Please re-enter the password", "modal_error_password_text": "Incorrect password", "modal_error_password_desc": "Please re-enter the valid password to unlock your bank statement for text extraction.", "modal_error_duplicate_file_text": "This file was already uploaded", "modal_error_duplicate_file_desc": "You have previously uploaded this file. Please upload another bank statement file.", "processing": "Processing...", "please_enter_name": "Please enter your name before uploading bank statement", "delete_title": "Delete File", "delete_question": "Do you want to delete this bank statement?", "delete_confirm": "Delete", "delete_cancel": "Cancel"}, "SmartUploader": {"close": "Close", "check": "Check", "modal_password_title": "Enter password", "modal_password_text": "Your files are encrypted", "modal_password_desc": "Enter the password to unlock <b>‘{fileName}’</b> and all other encrypted files. All files must use the same password.", "modal_password_field_label": "Password", "modal_password_placeholder": "Password", "modal_validating_title": "Validating", "modal_validating_text": "Checking your files", "modal_validating_desc": "Please wait while we verify and extract your documents.", "modal_error_password_title": "Please re-enter the password", "modal_error_password_text": "Incorrect password", "modal_error_password_desc": "Please re-enter the correct password to unlock <b>‘{fileName}’</b>. Make sure all files use the same password.", "modal_error_password_conflict_title": "Encrypted File Conflict", "modal_error_password_conflict_text": "Password didn’t match", "modal_error_password_conflict_desc_1": "The file <b> ‘{fileName}’ </b> requires a password that doesn’t match the one used for the previous files", "modal_error_password_conflict_desc_2": "Please remove all previously uploaded encrypted files and try again."}, "UtilityBill": {"validation_name_match": "Username and invoice name do not match.", "validation_address": "Address in the the bill does not match the applicant's address. Please try again", "validation_same_creation_mod_date": "Please use unmodified files.", "validation_producer_check": "The information in the document may have been modified. Please upload a new file.", "validation_creator_check": "The information in the document may have been modified. Please upload a new file.", "validation_author_check": "The information in the document may have been modified. Please upload a new file.", "validation_valid_structure": "The information in the document may have been modified. Please upload a new file.", "validation_valid_utilitybill_type": "Please use a valid provider.", "validation_default": "There was an error uploading. Please try again", "instruction": {"electric_bill": "Upload a electricity bill (PDF only)", "water_bill": "Upload a water bill (PDF only)", "telco_bill": "Upload a telco bill (PDF only)"}, "recent_upload": "Recent uploaded", "recent_upload_items": "{number} items", "select_files_again": "Select files again", "select_provider_again": "Select a new provider", "upload": "Upload", "check": "Check", "modal_provider_title": "Please select a provider", "modal_password_text": "Password", "modal_password_desc": "Please enter the password to unlock your utility bill for text extraction.", "modal_password_field_label": "Utility bill password", "modal_validating_text": "Extracting text...", "modal_validating_desc": "Hang tight. We are reading and validating your utility bill.", "modal_error_common_text": "Cannot connect to the server", "modal_error_common_desc": "Please upload your file again or select new provider.", "modal_error_file_text": "Invalid file format", "modal_error_file_desc": "Please upload your PDF utility bill.", "modal_error_max_file_size_text": "The file size is larger than specified.", "modal_error_max_file_size_desc": "Please upload your utility bill in PDF format no larger than {size} MB.", "modal_error_max_file_count_text": "The maximum number of files has been reached.", "modal_error_max_file_count_desc": "Please upload your utility bill of no more than {count} files.", "modal_error_password_text": "Incorrect password", "modal_error_password_desc": "Please re-enter the valid password to unlock your utility bill for text extraction.", "modal_error_duplicate_file_text": "This file was already uploaded", "modal_error_duplicate_file_desc": "You have previously uploaded this file. Please upload another utility bill file.", "processing": "Extracting text...", "please_enter_name": "Please enter your name before uploading the bill", "please_enter_address": "Please enter your address before uploading the bill", "delete_title": "Delete File", "delete_question": "Do you want to delete this file?", "delete_confirm": "Delete", "delete_cancel": "Cancel", "picture_modal": {"capture_note": "Please capture the image without glare", "capture": "Capture", "cancel_question": "Are you sure you want to leave taking the photo?", "error_open_camera": "There is a problem while requesting your camera"}, "provider_insctuction": {"mea": ["Open the MEA SmartLife application", "Log in by filling in the account showing the contract and the electricity meter code for at least 1 month.", "Press the \"3-tick\" icon and select the menu. \"Electronic documents\".", "Press to select the card of the electric meter code whose name matches the user.", "Select the latest billing cycle.", "Select \"Electricity Bill\" and enter your email address.", "Press send email and download the file from email."], "pea": ["Open the PEA Smart Plus application.", "Click to select the electricity account you want to request an invoice for.", "Click to select the billing cycle you want to request an invoice for.", "Press the \"Download electricity bill\" button.", "Press the download button in the bottom right corner of the screen.", "On the download page, press SAVE."], "mwa": ["Open the MWA onMobile application.", "Select menu \"Water User Information\".", "Add water user registration.", "Enter the water user registration and press \"Add water user registration\" or \"Add via QR Code\".", "Choose water usage statistics and water bill.", "Click on the PDF icon under the last month's water bill."], "pwa": [], "dtac": ["Open Dtac application.", "Login with user number.", "Select the \"Other\" menu.", "Select \"Receipt & Invoice\".", "Select \"Invoice\"", "Press the download icon to download the file."], "true": ["Open the True iService application.", "Login with your number.", "Select menu \"Bill & Use\".", "Select \"Previous Billing Cycle\".", "Click to see details.", "Press the \"Share\" icon on the top right, then press \"Save\".", "Check the name must be the same as the user."], "ais": ["Go to the my AIS application and log in > Select the \"More\" menu from the three lines below.", "Select the \"AIS\" tab.", "Select the menu \"Backward Bill\" (The screen will show the history of service payment \"Back 3 billing cycles\").", "Select \"View bill (PDF)\" for the desired round.", "Press \"Request OTP\", the system will send OTP code to view eBill.", "When the OTP is entered correctly, the screen will display the billing information."]}, "provider_insctuction_title": {"mea": "How to download invoices Metropolitan Electricity", "pea": "How to download invoices Provincial Electricity", "mwa": "How to download invoices Metropolitan Waterworks", "pwa": "How to download invoices Provincial Waterworks Authority", "dtac": "How to download DTAC invoices", "true": "How to download TrueMove H invoices", "ais": "How to download AIS invoices"}, "provider_name": {"mea": "Metropolitan Electricity", "pea": "Provincial Electricity", "mwa": "Metropolitan Waterworks", "pwa": "Provincial Waterworks Authority", "dtac": "DTAC", "true": "TrueMove H", "ais": "AIS"}}, "Consent": {"please_provide_consent": "Please provide consent to continue."}, "Schema": {"form": "Form", "report": "Report"}, "Builder": {"builder": "\"{name}\" Builder", "toggle_editor": "Show/Hide HTML", "validate_test": "Test validation", "validate_pass": "All pass !", "validate_fail": "Found {error_count} error(s) after submitting", "add_option": "+ Add option", "schema_empty": "This schema is empty, Click to Add step", "step_empty": "This step is empty, Click to Add section", "section_empty": "This section is empty, Click to Add Item", "add_item_btn": "+ Item", "add_page_btn": "+ Page", "dupe_btn": "Duplicate this", "snippet_btn": "From Snippet Center", "basic_field": "Basic Field", "copy_btn": " Copy", "paste_btn": " Paste", "json_editor": "JSON Editor", "save_schema_btn": "Save SCHEMA", "saved": "Saved !", "hide_report": "Hide in report", "fetch_warning": "You are editing English schema", "set_default": "Set to default", "history_unselect": "(Back to schema before viewing history)", "history_original": "Original", "from_db": "From Database", "from_session": "From current session", "please_enter_new_name": "Please enter new {target} name", "csv_reader": "CSV Reader", "settings": "Settings", "promptOnCreate_desc": "Prompt name input when before creating new Step, Section, Item", "snippetsUrl_desc": "URL of Snippets Center", "enable_visible_rule_btn": "เปิดกฏ", "disable_visible_rule_btn": "ปิดกฏ", "author": "Author", "loading": "Loading history . . . "}, "ekyc": {"video": {"loading": "Loading, please wait...", "title": "", "description": "Make sure that your face is in the frame and clearly visible.", "privacy_policy": "Your images will not be used in any other purpose.", "privacy_policy_link": "More info", "start_instruction": "Align your face\nTap '<PERSON><PERSON>' to proceed", "overlay_instructions": ["Live ID Verification", "1. Follow the Instruction above", "2. Position your face\nwithin the frame", "3. Remove hat, glasses or mask"], "start": "Scan your Face", "cancel": "Are you sure you want to leave taking the photo?", "actions": {"none": "Please wait for the next action...", "idle": "Keep your face straight & still", "neutral": "Keep your face straight & still", "mouth_open": "Open your mouth (hold still)", "smile": "Smile (hold still)", "sad": "Do frowny face (hold still)", "turn_left": "Turn your face to the LEFT (slowly)", "turn_right": "Turn your face to the RIGHT (slowly)", "turn_up": "turn your face up (hold still)", "turn_down": "turn your face down (hold still)", "idle_background": "Keep your face straight", "transitioning": "Back to straight & still face", "repeat": "{text} again", "frame": "Position your face to fit the frame", "hold": "Hold steady to complete the scan"}, "instructions": {"none": "", "idle": "Look straight at the camera until you pass", "neutral": "", "mouth_open": "", "smile": "", "sad": "", "turn_left": "Turn slowly, then hold still", "turn_right": "Turn slowly, then hold still", "turn_up": "", "turn_down": "", "idle_background": ""}}, "liveness": {"pass": "Success", "pass_subtitle": "Your selfie is complete!", "fail": "Please try again", "fail_subtitle": "", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Follow the instruction on the top of the screen.", "guide_subtitle_next": "Take a photo of the document for identity verification.", "success_notice": "", "retake": "Retake Photo", "detecting_face_position": "Detecting Face position", "align_your_face": "Align your face with the frame", "press_button": "Align your face with the frame and press the button", "prepare_liveness": "Prepare to take selfie recording", "hold_steady": "Hold steady to complete the scan", "blink_twice": "Blink your eyes twice", "instructions": ["<PERSON><PERSON> in good light", "No sunglasses", "No mask and headgear", "No extreme light", "No background faces"]}, "document": {"header": "Take a photo of your document", "subheader": "Make sure your document is clear and readable", "selector_header_1": "Select the country and document type", "selector_header_2": "Select the document type", "result_header": "The result of photographing identification documents", "pass": "Success", "pass_subtitle": "", "fail": "Fail", "fail_subtitle": "", "capture": "Take a Photo", "confirm": "Use This Photo", "retake": "Retake Photo", "privacy_policy": "Your images will not be used in any other purpose.", "capture_note": "Please capture the image without glare", "privacy_policy_link": "More info", "frame_instruction": "Align MRZ inside the rectangle", "capture_here": "Press here to capture", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of the document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "view_supported_documents": "View Supported Documents", "supported_documents": "Accepted Document Types", "supported_countries": "Accepted Countries", "supported_document_countries": "Countries Accepting {document_type}", "multi_country": "Multi-Country", "worldwide": "Worldwide", "instructions": ["Prepare a valid government-issued ID", "Place your card on a flat surface with photo facing up.", "Avoid any glare or reflection."]}, "front_card": {"header": "Take a photo of your ID's front", "label": "ID Card", "title": "Press the \"Continue\" button to take a picture of your ID", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of Thai ID for identity verification.", "guide_subtitle_next": "Please make sure the info matches your Thai ID.", "success_notice": "", "success_notice_image": "", "instructions": ["Place your card on a flat surface with photo facing up.", "Avoid any glare or reflection."], "retake": "Retake ID's front"}, "passport": {"header": "Take a photo of your passport personal details page", "label": "Passport", "title": "Press the \"Continue\" button to take a picture of your Passport", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "Align MRZ inside the rectangle", "modal_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of passport for identity verification.", "guide_subtitle_next": "Please make sure the info matches your passport.", "success_notice": "", "success_notice_image": "", "instructions": ["Open the passport page with your face image and information. Place on flat surface", "Avoid any glare or reflection."], "nfc": {"required_popup_title": "NFC required", "required_popup_desc": "Please download the official Mobile App OR select a different document (if available) for verification.", "required_permission_popup_title": "NFC Permissions Required", "required_permission_popup_desc": "Go to NFC settings to allow all permissions and try again.", "scan_title": "Validate information by NFC", "scan_desc": "Place the back of your mobile device to the front or back of your passport and slowly slide it up and down or in small circle until the chip is located"}, "retake": "Retake Passport"}, "driver_license": {"header": "Take a photo of your Driver License", "label": "Driver License", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "Align MRZ inside the rectangle", "modal_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Open the document page with your face image and information. Place on flat surface", "Avoid any glare or reflection."], "retake": "Retake Driver License"}, "residence_permit": {"header": "Take a photo of your Residence Permit", "label": "Residence Permit", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "Align MRZ inside the rectangle", "modal_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Open the document page with your face image and information. Place on flat surface", "Avoid any glare or reflection."], "retake": "Retake Residence Permit"}, "thai_alien_card": {"header": "Take a photo of your Thai Alien Card", "label": "Thai Alien Card", "title": "Press the \"Continue\" button to take a picture of your card", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of card for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the card facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Thai Alien Card"}, "ci_passport": {"header": "Take a photo of your CI Passport", "label": "CI Passport", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "work_permit_card": {"header": "Take a photo of your Work Permit Card", "label": "Work Permit Card", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "work_permit_book": {"header": "Take a photo of your Work Permit Book", "label": "Work Permit Book", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "travel_document": {"header": "Take a photo of your Travel Document", "label": "Travel Document", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "white_card": {"header": "Take a photo of your White Card", "label": "White Card", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "border_pass": {"header": "Take a photo of your Border Pass", "label": "Border Pass", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "monk_card": {"header": "Take a photo of your Monk Card", "label": "<PERSON>", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "immigration_card": {"header": "Take a photo of your Immigration Card", "label": "Immigration Card", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "other_document": {"header": "Take a photo of your other document", "label": "Other Document", "title": "Press the \"Continue\" button to take a picture of your document", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the document is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place the document facing up on a flat surface", "Avoid any glare or reflection."], "retake": "Retake Document"}, "portrait": {"title": "Press the \"Continue\" button to take a picture of your ID", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "frame_instruction": "", "modal_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of Thai ID for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "instructions": ["Place your card on a flat surface with photo facing up.", "Avoid any glare or reflection."]}, "backcard": {"header": "Turn your ID card around and Take a photo of your ID's back", "subheader": "Make sure your document is clear and readable", "pass": "Success", "pass_subtitle": "", "fail": "Fail", "fail_subtitle": "", "capture": "Take a Photo", "confirm": "Use This Photo", "retake": "Retake ID's back", "privacy_policy": "Your images will not be used in any other purpose.", "privacy_policy_link": "More info", "capture_note": "Please capture the image without glare", "capture_here": "Press here to capture", "guide_title": "Next Step", "guide_title_next": "Next Step", "guide_subtitle": "Take a photo of the document for identity verification.", "guide_subtitle_next": "Please make sure the info matches your document.", "success_notice": "", "success_notice_image": "", "title": "Take photo of National ID Card (back side)", "description": "To verify that you are the person who wants to register.", "cancel": "Are you sure you want to leave taking the photo?", "modal_instruction": "", "frame_instruction": "", "suggestion_text": "Make sure all information on the card is clearly visible and is not obscured by a reflection", "instructions": ["Place your card on a flat surface with photo facing down.", "Avoid any glare or reflection."]}, "auto": {"header": "Take a photo of your document", "pass": "Success", "pass_subtitle": "", "fail": "Fail", "fail_subtitle": "", "retake": "Retake Photo"}, "recorder": {"error_not_supported": "Camera access is not supported on this browser or device", "error_webview": "Camera access is not supported on this browser", "error_permission_denied": "Please allow the camera permission in the settings menu or privacy and security menu", "error_permission_denied_title": "Camera Permissions Required", "error_permission_denied_desc": "Go to Camera settings to allow all permissions and try again.", "error_open_camera": "There is a problem while requesting your camera", "error_no_face": "No face detected", "error_multiple_faces": "Make sure there is only one face in the frame", "error_small_face": "Move closer until your face fits the frame", "error_big_face": "Move away until your face fits the frame", "error_middle_face": "Please make sure your face is centered within the frame", "error_not_idle": "Look straight at the camera", "camera_permission_toggle": "Use Camera", "camera_load_failed": "Failed to load camera", "camera_load_failed_desc": "Cannot request or load camera. Please try again later.", "camera_load_failed_action": "Retry", "instructions": {"error_no_face": "Position your face to fit the frame", "error_multiple_faces": "Position your face to fit the frame", "error_small_face": "Move your face closer to fit the frame", "error_big_face": "Move your face further to fit the frame", "error_middle_face": "Move your face to fit the frame", "error_not_idle": "Look straight at the camera"}, "error_background_checking_time_out": "Please try again", "error_liveness_load_models": "Cannot load face detection service, Please refresh this page", "error_liveness_init_function": "Cannot start face detection, Please refresh this page", "error_liveness_get_action": "Cannot get actions, Please try again", "error_liveness_flashing": "Please try again", "error_card_max_attempt": "ID verification failed", "error_card_max_attempt_desc": "Please contact the customer support.", "error_card_max_attempt_cross_device": "ID verification failed", "error_card_max_attempt_desc_cross_device": "You can now close this tab on the smart phone. Then click \"Proceed\" on your computer.", "error_session_invalid": "An error has occurred. Please try again", "error_ensure_face_compare_failed": "Face comparison failed. No face detected or multiple faces in the background.", "error_action_time_out": "Position your face to fit the frame before the time runs out", "error_liveness_max_attempt": "Liveness verification failed", "error_liveness_max_attempt_desc": "Please contact the customer support.", "error_liveness_unfocus_app": "Please try again", "error_liveness_unfocus_app_desc": "You switched apps during the photo capture, causing the process to fail. Please retake the photo and avoid switching apps", "error_liveness_default": "Please try again", "error_liveness_default_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_face_compare_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_label_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_missing_frames_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_liveness_screen_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_brightness_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_face_out_frame_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_liveness_detection_face_is_occluded_desc": "Your selfie is unclear. Make sure your face is fully in frame, without masks, glasses, glare, or other faces in the background.", "error_liveness_liveness_detection_face_too_small_desc": "Your face is too far. Please position your face to fit the frame.", "error_liveness_liveness_detection_face_not_found_desc": "Face not found. Please position your face to fit the frame.", "error_liveness_liveness_detection_too_many_faces_desc": "More than one face detected. Please position only your face to fit the frame.", "error_liveness_liveness_detection_face_close_to_border_desc": "Your face is too close to the border. Please position your face to fit the frame.", "error_liveness_liveness_detection_face_cropped_desc": "Some parts of your face are out of frame. Please position your face to fit the frame.", "error_liveness_liveness_detection_face_angle_too_large_desc": "Position your face in the right angle.", "error_liveness_liveness_detection_failed_to_predict_landmarks_desc": "Face not found. Please position your face to fit the frame.", "error_liveness_liveness_detection_service_unavailable_desc": "Service Unavailable. Please try scanning your face again.", "error_liveness_liveness_detection_face_too_close_desc": "Your face is too close. Please position your face to fit the frame.", "error_frontcard_both_face_ocr": "Unclear information on ID document", "error_frontcard_both_face_ocr_desc": "Please try again. Make sure that the information on the ID document is clearly visible.", "error_frontcard_face": "No face detected or clearer face image required", "error_frontcard_face_desc": "Please try again. Make sure that the face image is fully visible on the ID document without glare.", "error_frontcard_ocr": "Incorrect ID Document type", "error_frontcard_ocr_desc": "Please try again with a valid ID document.", "error_frontcard_supported_country": "Incorrect or unsupported country", "error_frontcard_supported_country_desc": "Please try again with a valid ID document from the chosen country.", "error_frontcard_warning": "Fail to detect a legitimate ID document", "error_frontcard_warning_desc": "Please try again with a valid physical ID document", "error_frontcard_hashed_document_match_with": "ID card number does not match", "error_frontcard_hashed_document_match_with_desc": "Please retake the photo using an ID card with a number that matches the one previously provided", "error_frontcard_expiry": "The ID card has expired", "error_frontcard_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_frontcard_ocr_fields": "Unclear information on ID document", "error_frontcard_ocr_fields_desc": "Please try again. Make sure that the information on the ID document is clearly visible.", "error_frontcard_default": "Fail to detect card", "error_frontcard_default_desc": "Please try again", "error_passport_both_face_ocr": "Unclear information on ID document", "error_passport_both_face_ocr_desc": "Please try again. Make sure that the information on the ID document is clearly visible.", "error_passport_face": "No face detected or clearer face image required", "error_passport_face_desc": "Please try again. Make sure that the face image is fully visible on the ID document without glare.", "error_passport_ocr": "Incorrect ID Document type", "error_passport_ocr_desc": "Please try again with a valid ID document.", "error_passport_supported_country": "Incorrect or unsupported country", "error_passport_supported_country_desc": "Please try again with a valid ID document from the chosen country.", "error_passport_mrz": "The information on the document is incorrect.", "error_passport_mrz_desc": "Please retake photo and use the correct documents to verify your identity.", "error_passport_mrz_expiry": "The passport has expired", "error_passport_mrz_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_passport_image_quality": "Fail to detect passport", "error_passport_image_quality_desc": "The system cannot detect the information on the passport. Because the image is not clear or in wrong orientation, Please try again", "error_passport_warning": "Fail to detect a legitimate ID document", "error_passport_warning_desc": "Please try again with a valid physical ID document.", "error_passport_hashed_document_match_with": "Passport number does not match.", "error_passport_hashed_document_match_with_desc": "Please retake the photo using a passport with a number that matches the one previously provided", "error_passport_expiry": "The passport has expired", "error_passport_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_passport_ocr_fields": "Unclear information on ID document", "error_passport_ocr_fields_desc": "Please try again. Make sure that the information on the ID document is clearly visible.", "error_passport_nfc": "Please try again", "error_passport_nfc_desc": "Place the back of your mobile device to the front or back of your passport and slowly slide it up and down or in small circle until the chip is located.", "error_passport_default": "Fail to detect passport", "error_passport_default_desc": "Please try again", "error_passport_liveness_detection": "Fail to detect passport", "error_passport_liveness_detection_desc": "Please try again", "error_thai_alien_card_face": "No face detected or clearer face image required", "error_thai_alien_card_face_desc": "Please try again. Make sure that the face image is fully visible on the ID document without glare.", "error_thai_alien_card_ocr": "Fail to detect card", "error_thai_alien_card_ocr_desc": "Please take an image again using your card", "error_thai_alien_card_both_face_ocr": "Fail to detect card", "error_thai_alien_card_both_face_ocr_desc": "The system cannot detect the information on the card. Because the image is not clear or in wrong orientation, Please try again", "error_thai_alien_card_expiry": "The card has expired", "error_thai_alien_card_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_thai_alien_card_ocr_fields": "The information on the card is not clear", "error_thai_alien_card_ocr_fields_desc": "The system is unable to verify some information on the card, Please try again", "error_thai_alien_card_warning": "Fail to detect card", "error_thai_alien_card_warning_desc": "Please try again", "error_thai_alien_card_hashed_document_match_with": "Document number does not match", "error_thai_alien_card_hashed_document_match_with_desc": "Please retake the photo using a document with a number that matches the one previously provided", "error_thai_alien_card_default": "Fail to detect card", "error_thai_alien_card_default_desc": "Please try again", "error_ci_passport_mrz_expiry": "The passport has expired", "error_ci_passport_mrz_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_ci_passport_expiry": "The passport has expired", "error_ci_passport_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_ci_passport_default": "Fail to detect passport", "error_ci_passport_default_desc": "Please try again", "error_ci_passport_liveness_detection": "Fail to detect document", "error_ci_passport_liveness_detection_desc": "Please try again", "error_work_permit_default": "Fail to detect document", "error_work_permit_default_desc": "Please try again", "error_other_document_face": "No face detected or clearer face image required", "error_other_document_face_desc": "Please try again. Make sure that the face image is fully visible on the ID document without glare.", "error_other_document_ocr": "Incorrect ID Document type", "error_other_document_ocr_desc": "Please try again with a valid ID document.", "error_other_document_default": "Fail to detect document", "error_other_document_default_desc": "Please try again", "error_auto_ocr": "The document image is unclear or is not supported by the system.", "error_auto_ocr_desc": "Please try again, or select a document from the supported list below.", "error_document_both_face_ocr": "Unclear information on ID document", "error_document_both_face_ocr_desc": "Please try again. Make sure that the information on the ID document is clearly visible.", "error_document_face": "No face detected or clearer face image required", "error_document_face_desc": "Please try again. Make sure that the face image is fully visible on the ID document without glare.", "error_document_ocr": "Incorrect ID Document type", "error_document_ocr_desc": "Please try again with a valid ID document.", "error_document_supported_country": "Incorrect or unsupported country", "error_document_supported_country_desc": "Please try again with a valid ID document from the chosen country.", "error_document_orientation": "Your document is misaligned", "error_document_orientation_desc": "Please take a photo of the document within the designated frame", "error_document_liveness_detection": "Fail to detect document", "error_document_liveness_detection_desc": "Please try again", "error_document_mrz": "The information on the document is incorrect.", "error_document_mrz_desc": "Please retake photo and use the correct documents to verify your identity.", "error_document_mrz_expiry": "The document has expired", "error_document_mrz_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_document_image_quality": "Fail to detect document", "error_document_image_quality_desc": "The system cannot detect the information on the document. Because the image is not clear or in wrong orientation, Please try again", "error_document_warning": "Fail to detect a legitimate ID document", "error_document_warning_desc": "Please try again with a valid physical ID document.", "error_document_expiry": "The document has expired", "error_document_expiry_desc": "If the system incorrectly reads the expiration date, Please try again", "error_document_hashed_document_match_with": "Document number does not match", "error_document_hashed_document_match_with_desc": "Please retake the photo using a document with a number that matches the one previously provided", "error_document_id_match_with": "The document does not match the registered one", "error_document_id_match_with_desc": "If the system reads the document number incorrectly, Please try again", "error_document_ocr_fields": "Unclear information on ID document", "error_document_ocr_fields_desc": "Please try again. Make sure that the information on the ID document is clearly visible.", "error_document_age": "Your age is incorrect", "error_document_age_desc": "The age information from the photo does not pass age restriction", "error_document_supported_document_type": "Document not supported", "error_document_supported_document_type_desc": "Please try again using a document from the supported list.", "error_document_default": "Fail to detect document", "error_document_default_desc": "Please try again", "error_backcard_ocr": "Fail to detect the back side of ID card", "error_backcard_ocr_desc": "Please retake the image of the back side of the ID card.", "error_backcard_validity": "Incorrect info on the back side of ID card", "error_backcard_validity_desc": "Please make sure that the information on the back side of the ID card is correct.", "error_backcard_default": "Fail to detect the back side of ID card", "error_backcard_default_desc": "Please retake the image of the back side of the ID card.", "error_quality_blur_detection": "Image is too blurry.", "error_quality_blur_detection_desc": "Make sure that your document is in a clear focus.", "error_quality_glare_detection": "Camera glare detected.", "error_quality_glare_detection_desc": "Avoid taking the photo under direct light.", "error_quality_crop": "Document not fully visible.", "error_quality_crop_desc": "Make sure the entire document page fit in the frame.", "error_quality_brightness": "Image is too dark/too bright.", "error_quality_brightness_desc": "Capture image in a proper lighting condition.", "error_quality_default": "Recheck the capture portrait is clear and texts are readable", "error_quality_default_desc": "Make sure the document and texts are fully visible without glare and center aligned.", "error_server": "System error, please try again later", "action_passed": "Pass", "action_failed": "Liveness Fail", "scan_complete": "<PERSON>an <PERSON>", "action_wait_background_checking": "Verifying\nPlease keep looking at the camera", "card_failed": "Fail because the image is not clear", "action_already_passed": "Already Pass", "verifying_liveness": "Verifying", "checking_quality": "Checking Quality", "uploading_liveness": "Uploading Image", "uploading_document": "Uploading Image", "verifying_card": "Processing", "verifying_card_2": "Checking validity", "please_wait": "Please wait...", "hold_still": "Hold Still", "verifying_nfc": "Processing data from NFC chip", "cancel": "Yes", "not_cancel": "No", "card_passed": "Pass"}, "selector": {"uploading": "Uploading", "processing": "Processing", "cancel_btn": "Cancel", "camera_btn": "Next", "retake_btn": "Retake", "rescan_btn": "Rescan", "select_btn": "Select file", "retake_both_btn": "Retake Photo"}, "popup": {"title": "Cannot access the camera", "please_check_permission": "To allow camera permissions, follow these steps. ", "steps_header": "How to allow access to the camera", "steps_ios": ["1. Press the key icon {icon} on the top right side of the screen.", "2. Press the “Open Camera” button at the bottom right corner of the screen. ", "3. Press “Allow”", ["4. If you do not see that prompt, you may need to specifically enable camera access in your phone settings", "4.1  Scroll down, and select Safari", "4.2  At the bottom, select Camera", "4.3  Set it to Allow"]], "steps_chrome": ["1. Press the key icon {icon} on the top left side of the screen.", "2. Press Site settings on bottom right.", "3. <PERSON><PERSON> {icon} Access your camera", "4. <PERSON><PERSON> {icon} Allow", ["5. If you do not see that prompt, you may need to specifically enable camera access in your phone settings", "5.1  Scroll down, and select <PERSON><PERSON>", "5.2  Scroll down, and select Chrome", "5.3  Scroll down, and select App permission", "5.4  At the bottom, select Camera", "5.5  Set it to Allow"]], "steps_android": ["1. Go to Settings menu {icon}", "2. Select Privacy menu {icon}", "3. Then press Permission manager", "4. Go for Camera menu {icon}", "5. Select Internet browser {icon}", "6. <PERSON><PERSON> While using app"], "steps_chrome_desktop": ["1. Click menu {icon} on the top right of the screen", "2. Select the menu Alway allow https ...", "3. Click the Done button", "4. Click the {icon} button on the top left side of the screen"], "steps_safari_desktop": ["1. Click the {icon} button at the top of the screen", "2. Click the \"turn on the camera\" button", "3. Click the \"Allow \" button"], "steps_firefox_desktop": ["1. Click the {icon} top left button", "2. Click the \"Turn on the camera\" button", "3. Click the \"Allow \" button"], "site_setting": "Site setting", "permissions": "Permissions", "set_camera": "Click on the camera to change the value, then reload the page.", "clear_data": "Press 'clear data'", "desktop_detected": {"header": "Continue on your phone", "steps": [{"title": "SCAN THE QR CODE WITH YOUR CAMERA APP", "description": "You need to take a photo with a smartphone camera for a better quality image.", "warning": "Do not close or refresh this page."}, {"title": "ONCE COMPLETE ON THE SMARTPHONE, CLICK 'PROCEED' TO CONTINUE ON YOUR COMPUTER.", "description": "This may take a few seconds before you can click proceed.", "title_ekyc": {"ekyc_document": "ONCE YOU HAVE COMPLETED THE DOCUMENT VERIFICATION ON YOUR SMARTPHONE, PLEASE CLICK 'PROCEED' ON YOUR COMPUTER TO PROCEED TO THE NEXT STEP", "ekyc_liveness": "ONCE YOU HAVE COMPLETED THE FACE SCAN ON YOUR SMARTPHONE, PLEASE CLICK 'PROCEED' ON YOUR COMPUTER TO PROCEED TO THE NEXT STEP"}}], "proceed": "Proceed", "error_proceed": "Please complete the process on your smartphone by scanning the QR Code", "return": {"title": "Return to your computer to continue.", "description": "You can now close this tab on the smartphone. Then click 'Proceed' on your computer."}}, "lockdown": {"header": "Your device is in lockdown mode", "info": "Please exit the lockdown mode to continue.", "steps": [{"title": "Android", "description": "Settings > Security and Privacy > Auto Blocker > Turn Off"}, {"title": "iOS", "description": "Settings > Privacy & Security > Lockdown Mode > Turn Off Lockdown Mode > Restart"}]}}, "warning": {"portrait_detection": "Potential Image Manipulation", "label_detection": "Potential Non-Physical ID", "color_copyprint_detection": "Potential Copyprinted ID", "screen_detection": "Potential Screen Detection"}, "dashboard_error": {"face_compare": "Fail to detect Liveness", "label": "Fail to detect Liveness", "height_ocr": "Fail to detect Liveness", "face": "No face detected or clearer face image required", "ocr": "Not an ID Document", "both_face_ocr": "Not an ID Document", "expiry": "The ID card has expired", "id_match_with": "The ID card does not match the registered one", "ocr_fields": "Unclear information on ID document", "age": "Your age is incorrect", "warning": "Fail to detect card", "mrz": "Passport MRZ Failed"}}, "validation_no_fields": {"submit": "Please complete and correct all fields before you continue."}, "validation": {"accepted": "The :attribute must be accepted.", "active_url": "The :attribute is not a valid URL.", "after": "The :attribute must be a date after :date.", "after_or_equal": "The :attribute must be a date after or equal to :date.", "alpha": "The :attribute may only contain letters.", "alpha_dash": "The :attribute may only contain letters, numbers, and dashes.", "alpha_num": "The :attribute may only contain letters and numbers.", "array": "The :attribute must be an array.", "before": "The date must be in the valid range", "date_time": "The :attribute must be a date", "datetime_before": "The :attribute must be in the valid range", "datetime_after": "The :attribute must be in the valid range", "before_or_equal": "The :attribute must be a date before or equal to :date.", "before_now": "The :attribute must be a date before now", "after_now": "The :attribute must be a date after now", "between": {"numeric": "The :attribute must be between :min and :max.", "file": "The :attribute must be between :min and :max kilobytes.", "string": "The :attribute must be between :min and :max characters.", "array": "The :attribute must have between :min and :max items."}, "boolean": "The :attribute field must be true or false.", "between_number": "The :attribute field must be between :min and :max.", "business_id": "Please enter valid business registration number.", "confirmed": "The :attribute confirmation does not match.", "date": "The :attribute is not a valid date.", "date_format": "The :attribute does not match the format :format.", "different": "The :attribute must be different from :other.", "different_but_x": "The :attribute must be different from other fields.", "digits": "The :attribute must be :digits digits.", "digits_between": "The :attribute must be between :min and :max digits.", "dimensions": "The :attribute has invalid image dimensions.", "distinct": "The :attribute field has a duplicate value.", "email": "Please enter a valid e-mail.", "exists": "The selected :attribute is invalid.", "file": "The :attribute must be a file.", "filled": "The :attribute field must have a value.", "image": "The :attribute must be an image.", "in": "The selected :attribute is invalid.", "in_array": "The :attribute field does not exist in the list", "integer": "The :attribute must be an integer.", "ip": "The :attribute must be a valid IP address.", "ipv4": "The :attribute must be a valid IPv4 address.", "ipv6": "The :attribute must be a valid IPv6 address.", "is": "The :attribute must be equal to :value.", "json": "The :attribute must be a valid JSON string.", "max": {"numeric": "The :attribute may not be greater than :max.", "file": "The :attribute may not be greater than :max kilobytes.", "string": "The :attribute may not be greater than :max characters.", "array": "The :attribute may not have more than :max items."}, "mimes": "The :attribute must be a file of type: :values.", "mimetypes": "The :attribute must be a file of type: :values.", "min": {"numeric": "The :attribute must be at least :min.", "file": "The :attribute must be at least :min kilobytes.", "string": "The :attribute must be at least :min characters.", "array": "The :attribute must have at least :min items."}, "not_in": "The selected :attribute is invalid.", "numeric": "The :attribute must be a number.", "present": "The :attribute field must be present.", "regex": "The :attribute format is invalid.", "required": "The :attribute field is required.", "required_if": "The :attribute field is required.", "required_if_between": "The :attribute field is required.", "required_if_condition": "The :attribute field is required.", "required_unless": "The :attribute field is required.", "required_with": "The :attribute field is required.", "required_with_all": "The :attribute field is required.", "required_without": "The :attribute field is required.", "required_without_all": "The :attribute field is required.", "same": "The :attribute must match.", "same_but_x": "The :attribute must match.", "size": {"numeric": "The :attribute must be :size.", "file": "The :attribute must be :size kilobytes.", "string": "The :attribute must be :size characters.", "array": "The :attribute must contain :size items."}, "string": "The :attribute must be a string.", "timezone": "The :attribute must be a valid zone.", "unique": "The :attribute has already been taken.", "uploaded": "The :attribute failed to upload.", "url": "The :attribute format is invalid.", "invalid": "The :attribute is invalid.", "pass_checksum": "The :attribute is invalid", "custom": {"attribute-name": {"rule-name": "custom-message"}}}}