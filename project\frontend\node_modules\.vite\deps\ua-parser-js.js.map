{"version": 3, "sources": ["../../ua-parser-js/src/main/ua-parser.mjs"], "sourcesContent": ["// Generated ESM version of ua-parser-js\n// DO NOT EDIT THIS FILE!\n// Source: /src/main/ua-parser.js\n\n/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v2.0.5\n   Copyright © 2012-2025 Fais<PERSON> <<EMAIL>>\n   AGPLv3 License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://uaparser.dev\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n/* jshint esversion: 6 */ \n/* globals window */\n\n\n    \n    //////////////\n    // Constants\n    /////////////\n\n    var LIBVERSION  = '2.0.5',\n        UA_MAX_LENGTH = 500,\n        USER_AGENT  = 'user-agent',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n\n        // typeof\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n\n        // properties\n        UA_BROWSER  = 'browser',\n        UA_CPU      = 'cpu',\n        UA_DEVICE   = 'device',\n        UA_ENGINE   = 'engine',\n        UA_OS       = 'os',\n        UA_RESULT   = 'result',\n        \n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n\n        // device types\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        XR          = 'xr',\n        EMBEDDED    = 'embedded',\n\n        // browser types\n        INAPP       = 'inapp',\n\n        // client hints\n        BRANDS      = 'brands',\n        FORMFACTORS = 'formFactors',\n        FULLVERLIST = 'fullVersionList',\n        PLATFORM    = 'platform',\n        PLATFORMVER = 'platformVersion',\n        BITNESS     = 'bitness',\n        CH_HEADER   = 'sec-ch-ua',\n        CH_HEADER_FULL_VER_LIST = CH_HEADER + '-full-version-list',\n        CH_HEADER_ARCH      = CH_HEADER + '-arch',\n        CH_HEADER_BITNESS   = CH_HEADER + '-' + BITNESS,\n        CH_HEADER_FORM_FACTORS = CH_HEADER + '-form-factors',\n        CH_HEADER_MOBILE    = CH_HEADER + '-' + MOBILE,\n        CH_HEADER_MODEL     = CH_HEADER + '-' + MODEL,\n        CH_HEADER_PLATFORM  = CH_HEADER + '-' + PLATFORM,\n        CH_HEADER_PLATFORM_VER = CH_HEADER_PLATFORM + '-version',\n        CH_ALL_VALUES       = [BRANDS, FULLVERLIST, MOBILE, MODEL, PLATFORM, PLATFORMVER, ARCHITECTURE, FORMFACTORS, BITNESS],\n\n        // device vendors\n        AMAZON      = 'Amazon',\n        APPLE       = 'Apple',\n        ASUS        = 'ASUS',\n        BLACKBERRY  = 'BlackBerry',\n        GOOGLE      = 'Google',\n        HUAWEI      = 'Huawei',\n        LENOVO      = 'Lenovo',\n        HONOR       = 'Honor',\n        LG          = 'LG',\n        MICROSOFT   = 'Microsoft',\n        MOTOROLA    = 'Motorola',\n        NVIDIA      = 'Nvidia',\n        ONEPLUS     = 'OnePlus',\n        OPPO        = 'OPPO',\n        SAMSUNG     = 'Samsung',\n        SHARP       = 'Sharp',\n        SONY        = 'Sony',\n        XIAOMI      = 'Xiaomi',\n        ZEBRA       = 'Zebra',\n\n        // browsers\n        CHROME      = 'Chrome',\n        CHROMIUM    = 'Chromium',\n        CHROMECAST  = 'Chromecast',\n        EDGE        = 'Edge',\n        FIREFOX     = 'Firefox',\n        OPERA       = 'Opera',\n        FACEBOOK    = 'Facebook',\n        SOGOU       = 'Sogou',\n\n        PREFIX_MOBILE  = 'Mobile ',\n        SUFFIX_BROWSER = ' Browser',\n\n        // os\n        WINDOWS     = 'Windows';\n   \n    var isWindow            = typeof window !== UNDEF_TYPE,\n        NAVIGATOR           = (isWindow && window.navigator) ? \n                                window.navigator : \n                                undefined,\n        NAVIGATOR_UADATA    = (NAVIGATOR && NAVIGATOR.userAgentData) ? \n                                NAVIGATOR.userAgentData : \n                                undefined;\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (defaultRgx, extensions) {\n            var mergedRgx = {};\n            var extraRgx = extensions;\n            if (!isExtensions(extensions)) {\n                extraRgx = {};\n                for (var i in extensions) {\n                    for (var j in extensions[i]) {\n                        extraRgx[j] = extensions[i][j].concat(extraRgx[j] ? extraRgx[j] : []);\n                    }\n                }\n            }\n            for (var k in defaultRgx) {\n                mergedRgx[k] = extraRgx[k] && extraRgx[k].length % 2 === 0 ? extraRgx[k].concat(defaultRgx[k]) : defaultRgx[k];\n            }\n            return mergedRgx;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            if (typeof str1 === OBJ_TYPE && str1.length > 0) {\n                for (var i in str1) {\n                    if (lowerize(str2) == lowerize(str1[i])) return true;\n                }\n                return false;\n            }\n            return isString(str1) ? lowerize(str2) == lowerize(str1) : false;\n        },\n        isExtensions = function (obj, deep) {\n            for (var prop in obj) {\n                return /^(browser|cpu|device|engine|os)$/.test(prop) || (deep ? isExtensions(obj[prop]) : false);\n            }\n        },\n        isString = function (val) {\n            return typeof val === STR_TYPE;\n        },\n        itemListToArray = function (header) {\n            if (!header) return undefined;\n            var arr = [];\n            var tokens = strip(/\\\\?\\\"/g, header).split(',');\n            for (var i = 0; i < tokens.length; i++) {\n                if (tokens[i].indexOf(';') > -1) {\n                    var token = trim(tokens[i]).split(';v=');\n                    arr[i] = { brand : token[0], version : token[1] };\n                } else {\n                    arr[i] = trim(tokens[i]);\n                }\n            }\n            return arr;\n        },\n        lowerize = function (str) {\n            return isString(str) ? str.toLowerCase() : str;\n        },\n        majorize = function (version) {\n            return isString(version) ? strip(/[^\\d\\.]/g, version).split('.')[0] : undefined;\n        },\n        setProps = function (arr) {\n            for (var i in arr) {\n                if (!arr.hasOwnProperty(i)) continue;\n\n                var propName = arr[i];\n                if (typeof propName == OBJ_TYPE && propName.length == 2) {\n                    this[propName[0]] = propName[1];\n                } else {\n                    this[propName] = undefined;\n                }\n            }\n            return this;\n        },\n        strip = function (pattern, str) {\n            return isString(str) ? str.replace(pattern, EMPTY) : str;\n        },\n        stripQuotes = function (str) {\n            return strip(/\\\\?\\\"/g, str); \n        },\n        trim = function (str, len) {\n            if (isString(str)) {\n                str = strip(/^\\s\\s*/, str);\n                return typeof len === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            if(!ua || !arrays) return;\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length >= 3) {\n                                    // Check whether q[1] FUNCTION or REGEX\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        if (q.length > 3) {\n                                            this[q[0]] = match ? q[1].apply(this, q.slice(2)) : undefined;\n                                        } else {\n                                            // call function (usually string mapper)\n                                            this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                        }\n                                    } else {\n                                        if (q.length == 3) {\n                                            // sanitize match using given regex\n                                            this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                        } else if (q.length == 4) {\n                                            this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                        } else if (q.length > 4) {\n                                            this[q[0]] = match ? q[3].apply(this, [match.replace(q[1], q[2])].concat(q.slice(4))) : undefined;\n                                        }\n                                    }\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return map.hasOwnProperty('*') ? map['*'] : str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    var windowsVersionMap = {\n            'ME'    : '4.90',\n            'NT 3.51': '3.51',\n            'NT 4.0': '4.0',\n            '2000'  : ['5.0', '5.01'],\n            'XP'    : ['5.1', '5.2'],\n            'Vista' : '6.0',\n            '7'     : '6.1',\n            '8'     : '6.2',\n            '8.1'   : '6.3',\n            '10'    : ['6.4', '10.0'],\n            'NT'    : ''\n        },\n        \n        formFactorsMap = {\n            'embedded'  : 'Automotive',\n            'mobile'    : 'Mobile',\n            'tablet'    : ['Tablet', 'EInk'],\n            'smarttv'   : 'TV',\n            'wearable'  : 'Watch',\n            'xr'        : ['VR', 'XR'],\n            '?'         : ['Desktop', 'Unknown'],\n            '*'         : undefined\n        },\n\n        browserHintsMap = {\n            'Chrome'        : 'Google Chrome',\n            'Edge'          : 'Microsoft Edge',\n            'Edge WebView2' : 'Microsoft Edge WebView2',\n            'Chrome WebView': 'Android WebView',\n            'Chrome Headless':'HeadlessChrome',\n            'Huawei Browser': 'HuaweiBrowser',\n            'MIUI Browser'  : 'Miui Browser',\n            'Opera Mobi'    : 'OperaMobile',\n            'Yandex'        : 'YaBrowser'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var defaultRegexes = {\n\n        browser : [[\n\n            // Most common regardless engine\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, PREFIX_MOBILE + 'Chrome']], [\n            /webview.+edge\\/([\\w\\.]+)/i                                         // Microsoft Edge\n            ], [VERSION, [NAME, EDGE+' WebView']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       \n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /\\b(?:mxbrowser|mxios|myie2)\\/?([-\\w\\.]*)\\b/i                       // Maxthon\n            ], [VERSION, [NAME, 'Maxthon']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\\/ ]?([\\w\\.]*)/i,      \n                                                                                // Lunascape/Maxthon/Netfront/Jasmine/Blazer/Sleipnir\n            // Trident based\n            /(avant|iemobile|slim(?:browser|boat|jet))[\\/ ]?([\\d\\.]*)/i,        // Avant/IEMobile/SlimBrowser/SlimBoat/Slimjet\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Blink/Webkit/KHTML based                                         // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon/LG Browser/Otter/qutebrowser/Dooble\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ//Vivaldi/DuckDuckGo/Klar/Helio/Dragon\n            /(heytap|ovi|115|surf)browser\\/([\\d\\.]+)/i,                         // HeyTap/Ovi/115/Surf\n            /(ecosia|weibo)(?:__| \\w+@)([\\d\\.]+)/i                              // Ecosia/Weibo\n            ], [NAME, VERSION], [\n            /quark(?:pc)?\\/([-\\w\\.]+)/i                                         // Quark\n            ], [VERSION, [NAME, 'Quark']], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UCBrowser']], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart ' + LENOVO + SUFFIX_BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure' + SUFFIX_BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI' + SUFFIX_BROWSER]], [\n            /fxios\\/([\\w\\.-]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, PREFIX_MOBILE + FIREFOX]], [\n            /\\bqihoobrowser\\/?([\\w\\.]*)/i                                       // 360\n            ], [VERSION, [NAME, '360']], [\n            /\\b(qq)\\/([\\w\\.]+)/i                                                // QQ\n            ], [[NAME, /(.+)/, '$1Browser'], VERSION], [\n            /(oculus|sailfish|huawei|vivo|pico)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1' + SUFFIX_BROWSER], VERSION], [              // Oculus/Sailfish/HuaweiBrowser/VivoBrowser/PicoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, SOGOU + ' Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, SOGOU + ' Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345(?=browser|chrome|explorer))\\w*[\\/ ]?v?([\\w\\.]+)/i   // QQ/2345\n            ], [NAME, VERSION], [\n            /(lbbrowser|rekonq)/i                                               // LieBao Browser/Rekonq\n            ], [NAME], [\n            /ome\\/([\\w\\.]+) \\w* ?(iron) saf/i,                                  // Iron\n            /ome\\/([\\w\\.]+).+qihu (360)[es]e/i                                  // 360\n            ], [VERSION, NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION, [TYPE, INAPP]], [\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /(daum)apps[\\/ ]([\\w\\.]+)/i,                                        // Daum App\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(instagram|snapchat|klarna)[\\/ ]([-\\w\\.]+)/i                       // Instagram/Snapchat/Klarna\n            ], [NAME, VERSION, [TYPE, INAPP]], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA'], [TYPE, INAPP]], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok'], [TYPE, INAPP]], [\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME, [TYPE, INAPP]], [\n            /(zalo(?:app)?)[\\/\\sa-z]*([\\w\\.-]+)/i                               // Zalo \n            ], [[NAME, /(.+)/, 'Zalo'], VERSION, [TYPE, INAPP]], [\n\n            /(chromium)[\\/ ]([-\\w\\.]+)/i                                        // Chromium\n            ], [NAME, VERSION], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            /wv\\).+chrome\\/([\\w\\.]+).+edgw\\//i                                  // Edge WebView2\n            ], [VERSION, [NAME, EDGE+' WebView2']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android' + SUFFIX_BROWSER]], [\n\n            /chrome\\/([\\w\\.]+) mobile/i                                         // Chrome Mobile\n            ], [VERSION, [NAME, PREFIX_MOBILE + 'Chrome']], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile(?:\\/\\w+ | ?)safari/i                 // Safari Mobile\n            ], [VERSION, [NAME, PREFIX_MOBILE + 'Safari']], [\n            /iphone .*mobile(?:\\/\\w+ | ?)safari/i\n            ], [[NAME, PREFIX_MOBILE + 'Safari']], [\n            /version\\/([\\w\\.\\,]+) .*(safari)/i                                  // Safari\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, '1']], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(?:mobile|tablet);.*(firefox)\\/([\\w\\.-]+)/i                        // Firefox Mobile\n            ], [[NAME, PREFIX_MOBILE + FIREFOX], VERSION], [\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /(wolvic|librewolf)\\/([\\w\\.]+)/i                                    // Wolvic/LibreWolf\n            ], [NAME, VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Obigo/Mosaic/Go/ICE/UP.Browser/Ladybird\n            /\\b(links) \\(([\\w\\.]+)/i                                            // Links\n            ], [NAME, [VERSION, /_/g, '.']], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /[^\\d\\.]+./, EMPTY]]\n        ],\n\n        cpu : [[\n\n            /\\b((amd|x|x86[-_]?|wow|win)64)\\b/i                                 // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i,                                                     // IA32 (quicktime)\n            /\\b((i[346]|x)86)(pc)?\\b/i                                          // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?[89]e?l?|_?64))\\b/i                               // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(v[67])?ht?n?[fl]p?)\\b/i                                     // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /( (ce|mobile); ppc;|\\/[\\w\\.]+arm\\b)/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            / sun4\\w[;\\)]/i                                                     // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            /\\b(avr32|ia64(?=;)|68k(?=\\))|\\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\\b|pa-risc)/i,\n            /((ppc|powerpc)(64)?)( mac|;|\\))/i,                                 // PowerPC\n            /(?:osf1|[freopnt]{3,4}bsd) (alpha)/i                               // Alpha\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n            /winnt.+\\[axp/i\n            ], [[ARCHITECTURE, 'alpha']]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-(?![lr])\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]((?!sm-[lr]|browser)[-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Honor\n            /\\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\\)|;)/i\n            ], [MODEL, [VENDOR, HONOR], [TYPE, TABLET]], [\n            /honor([-\\w ]+)[;\\)]/i\n            ], [MODEL, [VENDOR, HONOR], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\\w\\. ]*(?= bui|\\)))\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /oid[^\\)]+; (2[\\dbc]{4}(182|283|rp\\w{2})[cgl]|m2105k81a?c)(?: bui|\\))/i,\n            /\\b((?:red)?mi[-_ ]?pad[\\w- ]*)(?: bui|\\))/i                                // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\\))/i, // Xiaomi Mi\n            / ([\\w ]+) miui\\/v?\\d/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n\n            // OnePlus\n            /droid.+; (cph2[3-6]\\d[13579]|((gm|hd)19|(ac|be|in|kb)20|(d[en]|eb|le|mt)21|ne22)[0-2]\\d|p[g-k]\\w[1m]10)\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, ONEPLUS], [TYPE, MOBILE]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, OPPO], [TYPE, MOBILE]], [\n            /\\b(opd2(\\d{3}a?))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, strMapper, { 'OnePlus' : ['203', '304', '403', '404', '413', '415'], '*' : OPPO }], [TYPE, TABLET]], [\n\n            // BLU\n            /(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\\w\\+ ]*)(?: bui|\\))/i  // Vivo series\n            ], [MODEL, [VENDOR, 'BLU'], [TYPE, MOBILE]], [    \n\n            // Vivo\n            /; vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\\d{3,4}(?:f[cu]|xu|[av])|yt\\d?-[jx]?\\d+[lfmx])( bui|;|\\)|\\/)/i,\n            /lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\\w- ]+?)|tb[\\w-]{6,7})( bui|;|\\)|\\/)/i\n            ], [MODEL, [VENDOR, LENOVO], [TYPE, TABLET]], [            \n            /lenovo[-_ ]?([-\\w ]+?)(?: bui|\\)|\\/)/i\n            ], [MODEL, [VENDOR, LENOVO], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ]([\\w\\s]+)(\\)| bui)/i,\n            /((?:moto(?! 360)[-\\w\\(\\) ]+|xt\\d{3,4}[cgkosw\\+]?[-\\d]*|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+(?!.*(?:browser|netcast|android tv|watch|webos))(\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Nokia\n            /(nokia) (t[12][01])/i\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n            /(?:maemo|nokia).*(n900|lumia \\d+|rm-\\d+)/i,\n            /nokia[-_ ]?(([-\\w\\. ]*))/i\n            ], [[MODEL, /_/g, ' '], [TYPE, MOBILE], [VENDOR, 'Nokia']], [\n\n            // Google\n            /(pixel (c|tablet))\\b/i                                             // Google Pixel C/Tablet\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n                                                                                // Google Pixel\n            /droid.+;(?: google)? (g(01[13]a|020[aem]|025[jn]|1b60|1f8f|2ybb|4s1m|576d|5nz6|8hhn|8vou|a02099|c15s|d1yq|e2ae|ec77|gh2x|kv4x|p4bc|pj41|r83y|tt9q|ur25|wvk6)|pixel[\\d ]*a?( pro)?( xl)?( fold)?( \\(5g\\))?)( bui|\\))/i\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n            /(google) (pixelbook( go)?)/i\n            ], [VENDOR, MODEL], [\n\n            // Sony\n            /droid.+; (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-\\w\\w\\d\\d)(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo(?!bc)\\w\\w)( bui|\\))/i,                           // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // TCL\n            /tcl (xess p17aa)/i,\n            /droid [\\w\\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\\w(\\w|\\w\\w))?(\\)| bui)/i\n            ], [MODEL, [VENDOR, 'TCL'], [TYPE, TABLET]], [\n            /droid [\\w\\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\\w(\\w|\\w\\w))?(\\)| bui)/i\n            ], [MODEL, [VENDOR, 'TCL'], [TYPE, MOBILE]], [\n\n            // itel\n            /(itel) ((\\w+))/i\n            ], [[VENDOR, lowerize], MODEL, [TYPE, strMapper, { 'tablet' : ['p10001l', 'w7001'], '*' : 'mobile' }]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // Energizer\n            /; (energy ?\\w+)(?: bui|\\))/i,\n            /; energizer ([\\w ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Energizer'], [TYPE, MOBILE]], [\n\n            // Cat\n            /; cat (b35);/i,\n            /; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Cat'], [TYPE, MOBILE]], [\n\n            // Smartfren\n            /((?:new )?andromax[\\w- ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Smartfren'], [TYPE, MOBILE]], [\n\n            // Nothing\n            /droid.+; (a(in)?(0(15|59|6[35])|142)p?)/i\n            ], [MODEL, [VENDOR, 'Nothing'], [TYPE, MOBILE]], [\n\n            // Archos\n            /; (x67 5g|tikeasy \\w+|ac[1789]\\d\\w+)( b|\\))/i,\n            /archos ?(5|gamepad2?|([\\w ]*[t1789]|hello) ?\\d+[\\w ]*)( b|\\))/i\n            ], [MODEL, [VENDOR, 'Archos'], [TYPE, TABLET]], [\n            /archos ([\\w ]+)( b|\\))/i,\n            /; (ac[3-6]\\d\\w{2,8})( b|\\))/i \n            ], [MODEL, [VENDOR, 'Archos'], [TYPE, MOBILE]], [\n\n            // HMD\n            /; (n159v)/i\n            ], [MODEL, [VENDOR, 'HMD'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(imo) (tab \\w+)/i,                                                 // IMO\n            /(infinix|tecno) (x1101b?|p904|dp(7c|8d|10a)( pro)?|p70[1-3]a?|p904|t1101)/i                     // Infinix XPad / Tecno\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|tecno|micromax|advan)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron/Tecno/Micromax/Advan\n            /; (blu|hmd|imo|infinix|lava|oneplus|tcl)[_ ]([\\w\\+ ]+?)(?: bui|\\)|; r)/i,  // BLU/HMD/IMO/Infinix/Lava/OnePlus/TCL\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(oppo) ?([\\w ]+) bui/i,                                            // OPPO\n            /droid[^;]+; (philips)[_ ]([sv-x][\\d]{3,4}[xz]?)/i                  // Philips\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i                                              // Kindle\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /((?:tegranote|shield t(?!.+d tv))[\\w- ]*?)(?: b|\\))/i              // Nvidia Tablets\n            ], [MODEL, [VENDOR, NVIDIA], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i               // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /(philips)[\\w ]+tv/i,                                               // Philips\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(vizio)(?: |.+model\\/)(\\w+-\\w+)/i,                                 // Vizio\n            /tcast.+(lg)e?. ([-\\w]+)/i                                          // LG SmartTV\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey.*devicetype\\/chromecast/i                                    // Google Chromecast Third Generation\n            ], [[MODEL, CHROMECAST+' Third Generation'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /crkey.*devicetype\\/([^/]*)/i                                       // Google Chromecast with specific device type\n            ], [[MODEL, /^/, 'Chromecast '], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /fuchsia.*crkey/i                                                   // Google Chromecast Nest Hub\n            ], [[MODEL, CHROMECAST+' Nest Hub'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast, Linux-based or unknown\n            ], [[MODEL, CHROMECAST], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /(portaltv)/i                                                       // Facebook Portal TV\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /(shield \\w+ tv)/i                                                  // Nvidia Shield TV\n            ], [MODEL, [VENDOR, NVIDIA], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                          // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mi(tv|box)-?\\w+) bui/i                                            // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, /.+\\/(\\w+)/, '$1', strMapper, {'LG':'lge'}], [MODEL, trim], [TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(playstation \\w+)/i                                                // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) (\\w+)/i,                                                // Nintendo\n            /(retroid) (pocket ([^\\)]+))/i                                      // Retroid Pocket\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield)( bui|\\))/i                                       // Nvidia Portable\n            ], [MODEL, [VENDOR, NVIDIA], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /\\b(sm-[lr]\\d\\d[0156][fnuw]?s?|gear live)\\b/i                       // Samsung Galaxy Watch\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, WEARABLE]], [\n            /((pebble))app/i,                                                   // Pebble\n            /(asus|google|lg|oppo) ((pixel |zen)?watch[\\w ]*)( bui|\\))/i        // Asus ZenWatch / LG Watch / Pixel Watch\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(ow(?:19|20)?we?[1-3]{1,3})/i                                      // Oppo Watch\n            ], [MODEL, [VENDOR, OPPO], [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /(opwwe\\d{3})/i                                                     // OnePlus Watch\n            ], [MODEL, [VENDOR, ONEPLUS], [TYPE, WEARABLE]], [\n            /(moto 360)/i                                                       // Motorola 360\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, WEARABLE]], [\n            /(smartwatch 3)/i                                                   // Sony SmartWatch\n            ], [MODEL, [VENDOR, SONY], [TYPE, WEARABLE]], [\n            /(g watch r)/i                                                      // LG G Watch R\n            ], [MODEL, [VENDOR, LG], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // XR\n            ///////////////////\n\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, XR]], [\n            /(pico) ([\\w ]+) os\\d/i                                             // Pico\n            ], [VENDOR, MODEL, [TYPE, XR]], [\n            /(quest( \\d| pro)?s?).+vr/i                                         // Meta Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, XR]], [\n            /mobile vr; rv.+firefox/i                                           // Unidentifiable VR device using Firefox Reality / Wolvic\n            ], [[TYPE, XR]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n            /(homepod).+mac os/i                                                // Apple HomePod\n            ], [MODEL, [VENDOR, APPLE], [TYPE, EMBEDDED]], [\n            /windows iot/i                                                      // Unidentifiable embedded device using Windows IoT\n            ], [[TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid.+; ([\\w- ]+) (4k|android|smart|google)[- ]?tv/i              // Unidentifiable SmartTV\n            ], [MODEL, [TYPE, SMARTTV]], [\n            /\\b((4k|android|smart|opera)[- ]?tv|tv; rv:|large screen[\\w ]+safari)\\b/i\n            ], [[TYPE, SMARTTV]], [\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+?(mobile|vr|\\d) safari/i\n            ], [MODEL, [TYPE, strMapper, { 'mobile' : 'Mobile', 'xr' : 'VR', '*' : TABLET }]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /droid .+?; ([\\w\\. -]+)( bui|\\))/i                                  // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /(arkweb)\\/([\\w\\.]+)/i                                              // ArkWeb\n            ], [NAME, VERSION], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna/Servo\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n\n            /\\b(libweb)/i                                                       // LibWeb\n            ], [NAME, VERSION], [\n            /ladybird\\//i\n            ], [[NAME, 'LibWeb']], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /(windows nt) (6\\.[23]); arm/i                                      // Windows RT\n            ], [[NAME, /N/, 'R'], [VERSION, strMapper, windowsVersionMap]], [\n            /(windows (?:phone|mobile|iot))(?: os)?[\\/ ]?([\\d\\.]*( se)?)/i,     // Windows IoT/Mobile/Phone\n                                                                                // Windows NT/3.1/95/98/ME/2000/XP/Vista/7/8/8.1/10/11\n            /(windows)[\\/ ](1[01]|2000|3\\.1|7|8(\\.1)?|9[58]|me|server 20\\d\\d( r2)?|vista|xp)/i\n            ], [NAME, VERSION], [\n            /windows nt ?([\\d\\.\\)]*)(?!.+xbox)/i,\n            /\\bwin(?=3| ?9|n)(?:nt| 9x )?([\\d\\.;]*)/i\n            ], [[VERSION, /(;|\\))/g, '', strMapper, windowsVersionMap], [NAME, WINDOWS]], [\n            /(windows ce)\\/?([\\d\\.]*)/i                                         // Windows CE\n            ], [NAME, VERSION], [\n\n            // iOS/macOS\n            /[adehimnop]{4,7}\\b(?:.*os ([\\w]+) like mac|; opera)/i,             // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+(haiku|morphos))/i                   // Mac OS\n            ], [[NAME, 'macOS'], [VERSION, /_/g, '.']], [\n\n            // Google Chromecast\n            /android ([\\d\\.]+).*crkey/i                                         // Google Chromecast, Android-based\n            ], [VERSION, [NAME, CHROMECAST + ' Android']], [\n            /fuchsia.*crkey\\/([\\d\\.]+)/i                                        // Google Chromecast, Fuchsia-based\n            ], [VERSION, [NAME, CHROMECAST + ' Fuchsia']], [\n            /crkey\\/([\\d\\.]+).*devicetype\\/smartspeaker/i                       // Google Chromecast, Linux-based Smart Speaker\n            ], [VERSION, [NAME, CHROMECAST + ' SmartSpeaker']], [\n            /linux.*crkey\\/([\\d\\.]+)/i                                          // Google Chromecast, Legacy Linux-based\n            ], [VERSION, [NAME, CHROMECAST + ' Linux']], [\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast, unknown\n            ], [VERSION, [NAME, CHROMECAST]], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86)/i                              // Android-x86\n            ], [VERSION, NAME], [                                               \n            /(ubuntu) ([\\w\\.]+) like android/i                                  // Ubuntu Touch\n            ], [[NAME, /(.+)/, '$1 Touch'], VERSION], [\n            /(harmonyos)[\\/ ]?([\\d\\.]*)/i,                                      // HarmonyOS\n                                                                                // Android/Blackberry/WebOS/QNX/Bada/RIM/KaiOS/Maemo/MeeGo/S40/Sailfish OS/OpenHarmony/Tizen\n            /(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen)\\w*[-\\/\\.; ]?([\\d\\.]*)/i\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\\/ ]?([\\w\\.]*)/i       // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i,                             // WebOS\n            /webos(?:[ \\/]?|\\.tv-20(?=2[2-9]))(\\d[\\d\\.]*)/i\n            ], [VERSION, [NAME, 'webOS']], [\n            /web0s;.+?(?:chr[o0]me|safari)\\/(\\d+)/i\n                                                                                // https://webostv.developer.lge.com/develop/specifications/web-api-and-web-engine\n            ], [[VERSION, strMapper, {'25':'120','24':'108','23':'94','22':'87','6':'79','5':'68','4':'53','3':'38','2':'538','1':'537','*':'TV'}], [NAME, 'webOS']], [                   \n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google ChromeOS\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, \"Chrome OS\"], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) (\\w+)/i,                                    // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n            /(pico) .+os([\\w\\.]+)/i,                                            // Pico\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /linux.+(mint)[\\/\\(\\) ]?([\\w\\.]*)/i,                                // Mint\n            /(mageia|vectorlinux|fuchsia|arcaos|arch(?= ?linux))[;l ]([\\d\\.]*)/i,  // Mageia/VectorLinux/Fuchsia/ArcaOS/Arch\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire|knoppix)(?: gnu[\\/ ]linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire/Knoppix\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /\\b(aix)[; ]([1-9\\.]{0,4})/i,                                       // AIX\n            /(hurd|linux|morphos)(?: (?:arm|x86|ppc)\\w*| ?)([\\w\\.]*)/i,         // Hurd/Linux/MorphOS\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) ?(r\\d)?/i                                                  // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\d\\.]*)/i                                               // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /\\b(beos|os\\/2|amigaos|openvms|hp-ux|serenityos)/i,                 // BeOS/OS2/AmigaOS/OpenVMS/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Factories\n    ////////////////\n\n    var defaultProps = (function () {\n            var props = { init : {}, isIgnore : {}, isIgnoreRgx : {}, toString : {}};\n            setProps.call(props.init, [\n                [UA_BROWSER, [NAME, VERSION, MAJOR, TYPE]],\n                [UA_CPU, [ARCHITECTURE]],\n                [UA_DEVICE, [TYPE, MODEL, VENDOR]],\n                [UA_ENGINE, [NAME, VERSION]],\n                [UA_OS, [NAME, VERSION]]\n            ]);\n            setProps.call(props.isIgnore, [\n                [UA_BROWSER, [VERSION, MAJOR]],\n                [UA_ENGINE, [VERSION]],\n                [UA_OS, [VERSION]]\n            ]);\n            setProps.call(props.isIgnoreRgx, [\n                [UA_BROWSER, / ?browser$/i],\n                [UA_OS, / ?os$/i]\n            ]);\n            setProps.call(props.toString, [\n                [UA_BROWSER, [NAME, VERSION]],\n                [UA_CPU, [ARCHITECTURE]],\n                [UA_DEVICE, [VENDOR, MODEL]],\n                [UA_ENGINE, [NAME, VERSION]],\n                [UA_OS, [NAME, VERSION]]\n            ]);\n            return props;\n    })();\n\n    var createIData = function (item, itemType) {\n\n        var init_props = defaultProps.init[itemType],\n            is_ignoreProps = defaultProps.isIgnore[itemType] || 0,\n            is_ignoreRgx = defaultProps.isIgnoreRgx[itemType] || 0,\n            toString_props = defaultProps.toString[itemType] || 0;\n\n        function IData () {\n            setProps.call(this, init_props);\n        }\n\n        IData.prototype.getItem = function () {\n            return item;\n        };\n\n        IData.prototype.withClientHints = function () {\n\n            // nodejs / non-client-hints browsers\n            if (!NAVIGATOR_UADATA) {\n                return item\n                        .parseCH()\n                        .get();\n            }\n\n            // browsers based on chromium 85+\n            return NAVIGATOR_UADATA\n                    .getHighEntropyValues(CH_ALL_VALUES)\n                    .then(function (res) {\n                        return item\n                                .setCH(new UACHData(res, false))\n                                .parseCH()\n                                .get();\n            });\n        };\n\n        IData.prototype.withFeatureCheck = function () {\n            return item.detectFeature().get();\n        };\n\n        if (itemType != UA_RESULT) {\n            IData.prototype.is = function (strToCheck) {\n                var is = false;\n                for (var i in this) {\n                    if (this.hasOwnProperty(i) && !has(is_ignoreProps, i) && lowerize(is_ignoreRgx ? strip(is_ignoreRgx, this[i]) : this[i]) == lowerize(is_ignoreRgx ? strip(is_ignoreRgx, strToCheck) : strToCheck)) {\n                        is = true;\n                        if (strToCheck != UNDEF_TYPE) break;\n                    } else if (strToCheck == UNDEF_TYPE && is) {\n                        is = !is;\n                        break;\n                    }\n                }\n                return is;\n            };\n            IData.prototype.toString = function () {\n                var str = EMPTY;\n                for (var i in toString_props) {\n                    if (typeof(this[toString_props[i]]) !== UNDEF_TYPE) {\n                        str += (str ? ' ' : EMPTY) + this[toString_props[i]];\n                    }\n                }\n                return str || UNDEF_TYPE;\n            };\n        }\n\n        if (!NAVIGATOR_UADATA) {\n            IData.prototype.then = function (cb) { \n                var that = this;\n                var IDataResolve = function () {\n                    for (var prop in that) {\n                        if (that.hasOwnProperty(prop)) {\n                            this[prop] = that[prop];\n                        }\n                    }\n                };\n                IDataResolve.prototype = {\n                    is : IData.prototype.is,\n                    toString : IData.prototype.toString\n                };\n                var resolveData = new IDataResolve();\n                cb(resolveData);\n                return resolveData;\n            };\n        }\n\n        return new IData();\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    function UACHData (uach, isHttpUACH) {\n        uach = uach || {};\n        setProps.call(this, CH_ALL_VALUES);\n        if (isHttpUACH) {\n            setProps.call(this, [\n                [BRANDS, itemListToArray(uach[CH_HEADER])],\n                [FULLVERLIST, itemListToArray(uach[CH_HEADER_FULL_VER_LIST])],\n                [MOBILE, /\\?1/.test(uach[CH_HEADER_MOBILE])],\n                [MODEL, stripQuotes(uach[CH_HEADER_MODEL])],\n                [PLATFORM, stripQuotes(uach[CH_HEADER_PLATFORM])],\n                [PLATFORMVER, stripQuotes(uach[CH_HEADER_PLATFORM_VER])],\n                [ARCHITECTURE, stripQuotes(uach[CH_HEADER_ARCH])],\n                [FORMFACTORS, itemListToArray(uach[CH_HEADER_FORM_FACTORS])],\n                [BITNESS, stripQuotes(uach[CH_HEADER_BITNESS])]\n            ]);\n        } else {\n            for (var prop in uach) {\n                if(this.hasOwnProperty(prop) && typeof uach[prop] !== UNDEF_TYPE) this[prop] = uach[prop];\n            }\n        }\n    }\n\n    function UAItem (itemType, ua, rgxMap, uaCH) {\n\n        this.get = function (prop) {\n            if (!prop) return this.data;\n            return this.data.hasOwnProperty(prop) ? this.data[prop] : undefined;\n        };\n\n        this.set = function (prop, val) {\n            this.data[prop] = val;\n            return this;\n        };\n\n        this.setCH = function (ch) {\n            this.uaCH = ch;\n            return this;\n        };\n\n        this.detectFeature = function () {\n            if (NAVIGATOR && NAVIGATOR.userAgent == this.ua) {\n                switch (this.itemType) {\n                    case UA_BROWSER:\n                        // Brave-specific detection\n                        if (NAVIGATOR.brave && typeof NAVIGATOR.brave.isBrave == FUNC_TYPE) {\n                            this.set(NAME, 'Brave');\n                        }\n                        break;\n                    case UA_DEVICE:\n                        // Chrome-specific detection: check for 'mobile' value of navigator.userAgentData\n                        if (!this.get(TYPE) && NAVIGATOR_UADATA && NAVIGATOR_UADATA[MOBILE]) {\n                            this.set(TYPE, MOBILE);\n                        }\n                        // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n                        if (this.get(MODEL) == 'Macintosh' && NAVIGATOR && typeof NAVIGATOR.standalone !== UNDEF_TYPE && NAVIGATOR.maxTouchPoints && NAVIGATOR.maxTouchPoints > 2) {\n                            this.set(MODEL, 'iPad')\n                                .set(TYPE, TABLET);\n                        }\n                        break;\n                    case UA_OS:\n                        // Chrome-specific detection: check for 'platform' value of navigator.userAgentData\n                        if (!this.get(NAME) && NAVIGATOR_UADATA && NAVIGATOR_UADATA[PLATFORM]) {\n                            this.set(NAME, NAVIGATOR_UADATA[PLATFORM]);\n                        }\n                        break;\n                    case UA_RESULT:\n                        var data = this.data;\n                        var detect = function (itemType) {\n                            return data[itemType]\n                                    .getItem()\n                                    .detectFeature()\n                                    .get();\n                        };\n                        this.set(UA_BROWSER, detect(UA_BROWSER))\n                            .set(UA_CPU, detect(UA_CPU))\n                            .set(UA_DEVICE, detect(UA_DEVICE))\n                            .set(UA_ENGINE, detect(UA_ENGINE))\n                            .set(UA_OS, detect(UA_OS));\n                }\n            }\n            return this;\n        };\n\n        this.parseUA = function () {\n            if (this.itemType != UA_RESULT) {\n                rgxMapper.call(this.data, this.ua, this.rgxMap);\n            }\n            if (this.itemType == UA_BROWSER) {\n                this.set(MAJOR, majorize(this.get(VERSION)));\n            }\n            return this;\n        };\n\n        this.parseCH = function () {\n            var uaCH = this.uaCH,\n                rgxMap = this.rgxMap;\n    \n            switch (this.itemType) {\n                case UA_BROWSER:\n                case UA_ENGINE:\n                    var brands = uaCH[FULLVERLIST] || uaCH[BRANDS], prevName;\n                    if (brands) {\n                        for (var i=0; i<brands.length; i++) {\n                            var brandName = brands[i].brand || brands[i],\n                                brandVersion = brands[i].version;\n                            if (this.itemType == UA_BROWSER && \n                                !/not.a.brand/i.test(brandName) && \n                                (!prevName || \n                                    (/Chrom/.test(prevName) && brandName != CHROMIUM) || \n                                    (prevName == EDGE && /WebView2/.test(brandName))\n                                )) {\n                                    brandName = strMapper(brandName, browserHintsMap);\n                                    prevName = this.get(NAME);\n                                    if (!(prevName && !/Chrom/.test(prevName) && /Chrom/.test(brandName))) {\n                                        this.set(NAME, brandName)\n                                            .set(VERSION, brandVersion)\n                                            .set(MAJOR, majorize(brandVersion));\n                                    }\n                                    prevName = brandName;\n                            }\n                            if (this.itemType == UA_ENGINE && brandName == CHROMIUM) {\n                                this.set(VERSION, brandVersion);\n                            }\n                        }\n                    }\n                    break;\n                case UA_CPU:\n                    var archName = uaCH[ARCHITECTURE];\n                    if (archName) {\n                        if (archName && uaCH[BITNESS] == '64') archName += '64';\n                        rgxMapper.call(this.data, archName + ';', rgxMap);\n                    }\n                    break;\n                case UA_DEVICE:\n                    if (uaCH[MOBILE]) {\n                        this.set(TYPE, MOBILE);\n                    }\n                    if (uaCH[MODEL]) {\n                        this.set(MODEL, uaCH[MODEL]);\n                        if (!this.get(TYPE) || !this.get(VENDOR)) {\n                            var reParse = {};\n                            rgxMapper.call(reParse, 'droid 9; ' + uaCH[MODEL] + ')', rgxMap);\n                            if (!this.get(TYPE) && !!reParse.type) {\n                                this.set(TYPE, reParse.type);\n                            }\n                            if (!this.get(VENDOR) && !!reParse.vendor) {\n                                this.set(VENDOR, reParse.vendor);\n                            }\n                        }\n                    }\n                    if (uaCH[FORMFACTORS]) {\n                        var ff;\n                        if (typeof uaCH[FORMFACTORS] !== 'string') {\n                            var idx = 0;\n                            while (!ff && idx < uaCH[FORMFACTORS].length) {\n                                ff = strMapper(uaCH[FORMFACTORS][idx++], formFactorsMap);\n                            }\n                        } else {\n                            ff = strMapper(uaCH[FORMFACTORS], formFactorsMap);\n                        }\n                        this.set(TYPE, ff);\n                    }\n                    break;\n                case UA_OS:\n                    var osName = uaCH[PLATFORM];\n                    if(osName) {\n                        var osVersion = uaCH[PLATFORMVER];\n                        if (osName == WINDOWS) osVersion = (parseInt(majorize(osVersion), 10) >= 13 ? '11' : '10');\n                        this.set(NAME, osName)\n                            .set(VERSION, osVersion);\n                    }\n                    // Xbox-Specific Detection\n                    if (this.get(NAME) == WINDOWS && uaCH[MODEL] == 'Xbox') {\n                        this.set(NAME, 'Xbox')\n                            .set(VERSION, undefined);\n                    }           \n                    break;\n                case UA_RESULT:\n                    var data = this.data;\n                    var parse = function (itemType) {\n                        return data[itemType]\n                                .getItem()\n                                .setCH(uaCH)\n                                .parseCH()\n                                .get();\n                    };\n                    this.set(UA_BROWSER, parse(UA_BROWSER))\n                        .set(UA_CPU, parse(UA_CPU))\n                        .set(UA_DEVICE, parse(UA_DEVICE))\n                        .set(UA_ENGINE, parse(UA_ENGINE))\n                        .set(UA_OS, parse(UA_OS));\n            }\n            return this;\n        };\n\n        setProps.call(this, [\n            ['itemType', itemType],\n            ['ua', ua],\n            ['uaCH', uaCH],\n            ['rgxMap', rgxMap],\n            ['data', createIData(this, itemType)]\n        ]);\n\n        return this;\n    }\n\n    function UAParser (ua, extensions, headers) {\n\n        if (typeof ua === OBJ_TYPE) {\n            if (isExtensions(ua, true)) {\n                if (typeof extensions === OBJ_TYPE) {\n                    headers = extensions;               // case UAParser(extensions, headers)           \n                }\n                extensions = ua;                        // case UAParser(extensions)\n            } else {\n                headers = ua;                           // case UAParser(headers)\n                extensions = undefined;\n            }\n            ua = undefined;\n        } else if (typeof ua === STR_TYPE && !isExtensions(extensions, true)) {\n            headers = extensions;                       // case UAParser(ua, headers)\n            extensions = undefined;\n        }\n\n        if (headers) {\n            if (typeof headers.append === FUNC_TYPE) {\n                // Convert Headers object into a plain object\n                var kv = {};\n                headers.forEach(function (v, k) { kv[String(k).toLowerCase()] = v; });\n                headers = kv;\n            } else {\n                // Normalize headers field name into lowercase\n                var normalized = {};\n                for (var header in headers) {\n                    if (headers.hasOwnProperty(header)) {\n                        normalized[String(header).toLowerCase()] = headers[header];\n                    }\n                }\n                headers = normalized;\n            }\n        }\n        \n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions, headers).getResult();\n        }\n\n        var userAgent = typeof ua === STR_TYPE ? ua :                                       // Passed user-agent string\n                                (headers && headers[USER_AGENT] ? headers[USER_AGENT] :     // User-Agent from passed headers\n                                ((NAVIGATOR && NAVIGATOR.userAgent) ? NAVIGATOR.userAgent : // navigator.userAgent\n                                    EMPTY)),                                                // empty string\n\n            httpUACH = new UACHData(headers, true),\n            regexMap = extensions ? \n                        extend(defaultRegexes, extensions) : \n                        defaultRegexes,\n\n            createItemFunc = function (itemType) {\n                if (itemType == UA_RESULT) {\n                    return function () {\n                        return new UAItem(itemType, userAgent, regexMap, httpUACH)\n                                    .set('ua', userAgent)\n                                    .set(UA_BROWSER, this.getBrowser())\n                                    .set(UA_CPU, this.getCPU())\n                                    .set(UA_DEVICE, this.getDevice())\n                                    .set(UA_ENGINE, this.getEngine())\n                                    .set(UA_OS, this.getOS())\n                                    .get();\n                    };\n                } else {\n                    return function () {\n                        return new UAItem(itemType, userAgent, regexMap[itemType], httpUACH)\n                                    .parseUA()\n                                    .get();\n                    };\n                }\n            };\n            \n        // public methods\n        setProps.call(this, [\n            ['getBrowser', createItemFunc(UA_BROWSER)],\n            ['getCPU', createItemFunc(UA_CPU)],\n            ['getDevice', createItemFunc(UA_DEVICE)],\n            ['getEngine', createItemFunc(UA_ENGINE)],\n            ['getOS', createItemFunc(UA_OS)],\n            ['getResult', createItemFunc(UA_RESULT)],\n            ['getUA', function () { return userAgent; }],\n            ['setUA', function (ua) {\n                if (isString(ua))\n                    userAgent = ua.length > UA_MAX_LENGTH ? trim(ua, UA_MAX_LENGTH) : ua;\n                return this;\n            }]\n        ])\n        .setUA(userAgent);\n\n        return this;\n    }\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR, TYPE]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    export {UAParser};"], "mappings": ";;;AAuBI,IAAI,aAAc;AAAlB,IACI,gBAAgB;AADpB,IAEI,aAAc;AAFlB,IAGI,QAAc;AAHlB,IAII,UAAc;AAJlB,IAOI,YAAc;AAPlB,IAQI,aAAc;AARlB,IASI,WAAc;AATlB,IAUI,WAAc;AAVlB,IAaI,aAAc;AAblB,IAcI,SAAc;AAdlB,IAeI,YAAc;AAflB,IAgBI,YAAc;AAhBlB,IAiBI,QAAc;AAjBlB,IAkBI,YAAc;AAlBlB,IAoBI,OAAc;AApBlB,IAqBI,OAAc;AArBlB,IAsBI,SAAc;AAtBlB,IAuBI,UAAc;AAvBlB,IAwBI,eAAc;AAxBlB,IAyBI,QAAc;AAzBlB,IA0BI,QAAc;AA1BlB,IA6BI,UAAc;AA7BlB,IA8BI,SAAc;AA9BlB,IA+BI,SAAc;AA/BlB,IAgCI,UAAc;AAhClB,IAiCI,WAAc;AAjClB,IAkCI,KAAc;AAlClB,IAmCI,WAAc;AAnClB,IAsCI,QAAc;AAtClB,IAyCI,SAAc;AAzClB,IA0CI,cAAc;AA1ClB,IA2CI,cAAc;AA3ClB,IA4CI,WAAc;AA5ClB,IA6CI,cAAc;AA7ClB,IA8CI,UAAc;AA9ClB,IA+CI,YAAc;AA/ClB,IAgDI,0BAA0B,YAAY;AAhD1C,IAiDI,iBAAsB,YAAY;AAjDtC,IAkDI,oBAAsB,YAAY,MAAM;AAlD5C,IAmDI,yBAAyB,YAAY;AAnDzC,IAoDI,mBAAsB,YAAY,MAAM;AApD5C,IAqDI,kBAAsB,YAAY,MAAM;AArD5C,IAsDI,qBAAsB,YAAY,MAAM;AAtD5C,IAuDI,yBAAyB,qBAAqB;AAvDlD,IAwDI,gBAAsB,CAAC,QAAQ,aAAa,QAAQ,OAAO,UAAU,aAAa,cAAc,aAAa,OAAO;AAxDxH,IA2DI,SAAc;AA3DlB,IA4DI,QAAc;AA5DlB,IA6DI,OAAc;AA7DlB,IA8DI,aAAc;AA9DlB,IA+DI,SAAc;AA/DlB,IAgEI,SAAc;AAhElB,IAiEI,SAAc;AAjElB,IAkEI,QAAc;AAlElB,IAmEI,KAAc;AAnElB,IAoEI,YAAc;AApElB,IAqEI,WAAc;AArElB,IAsEI,SAAc;AAtElB,IAuEI,UAAc;AAvElB,IAwEI,OAAc;AAxElB,IAyEI,UAAc;AAzElB,IA0EI,QAAc;AA1ElB,IA2EI,OAAc;AA3ElB,IA4EI,SAAc;AA5ElB,IA6EI,QAAc;AA7ElB,IAgFI,SAAc;AAhFlB,IAiFI,WAAc;AAjFlB,IAkFI,aAAc;AAlFlB,IAmFI,OAAc;AAnFlB,IAoFI,UAAc;AApFlB,IAqFI,QAAc;AArFlB,IAsFI,WAAc;AAtFlB,IAuFI,QAAc;AAvFlB,IAyFI,gBAAiB;AAzFrB,IA0FI,iBAAiB;AA1FrB,IA6FI,UAAc;AAElB,IAAI,WAAsB,OAAO,WAAW;AAA5C,IACI,YAAuB,YAAY,OAAO,YAClB,OAAO,YACP;AAH5B,IAII,mBAAuB,aAAa,UAAU,gBACtB,UAAU,gBACV;AAM5B,IAAI,SAAS,SAAU,YAAY,YAAY;AACvC,MAAI,YAAY,CAAC;AACjB,MAAI,WAAW;AACf,MAAI,CAAC,aAAa,UAAU,GAAG;AAC3B,eAAW,CAAC;AACZ,aAAS,KAAK,YAAY;AACtB,eAAS,KAAK,WAAW,CAAC,GAAG;AACzB,iBAAS,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,EAAE,OAAO,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;AAAA,MACxE;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,KAAK,YAAY;AACtB,cAAU,CAAC,IAAI,SAAS,CAAC,KAAK,SAAS,CAAC,EAAE,SAAS,MAAM,IAAI,SAAS,CAAC,EAAE,OAAO,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC;AAAA,EACjH;AACA,SAAO;AACX;AAfJ,IAgBI,YAAY,SAAU,KAAK;AACvB,MAAI,QAAQ,CAAC;AACb,WAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC7B,UAAM,IAAI,CAAC,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC;AAAA,EACvC;AACA,SAAO;AACX;AAtBJ,IAuBI,MAAM,SAAU,MAAM,MAAM;AACxB,MAAI,OAAO,SAAS,YAAY,KAAK,SAAS,GAAG;AAC7C,aAAS,KAAK,MAAM;AAChB,UAAI,SAAS,IAAI,KAAK,SAAS,KAAK,CAAC,CAAC,EAAG,QAAO;AAAA,IACpD;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI,IAAI;AAC/D;AA/BJ,IAgCI,eAAe,SAAU,KAAK,MAAM;AAChC,WAAS,QAAQ,KAAK;AAClB,WAAO,mCAAmC,KAAK,IAAI,MAAM,OAAO,aAAa,IAAI,IAAI,CAAC,IAAI;AAAA,EAC9F;AACJ;AApCJ,IAqCI,WAAW,SAAU,KAAK;AACtB,SAAO,OAAO,QAAQ;AAC1B;AAvCJ,IAwCI,kBAAkB,SAAU,QAAQ;AAChC,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,MAAM,CAAC;AACX,MAAI,SAAS,MAAM,UAAU,MAAM,EAAE,MAAM,GAAG;AAC9C,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,CAAC,EAAE,QAAQ,GAAG,IAAI,IAAI;AAC7B,UAAI,QAAQ,KAAK,OAAO,CAAC,CAAC,EAAE,MAAM,KAAK;AACvC,UAAI,CAAC,IAAI,EAAE,OAAQ,MAAM,CAAC,GAAG,SAAU,MAAM,CAAC,EAAE;AAAA,IACpD,OAAO;AACH,UAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AArDJ,IAsDI,WAAW,SAAU,KAAK;AACtB,SAAO,SAAS,GAAG,IAAI,IAAI,YAAY,IAAI;AAC/C;AAxDJ,IAyDI,WAAW,SAAU,SAAS;AAC1B,SAAO,SAAS,OAAO,IAAI,MAAM,YAAY,OAAO,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAC1E;AA3DJ,IA4DI,WAAW,SAAU,KAAK;AACtB,WAAS,KAAK,KAAK;AACf,QAAI,CAAC,IAAI,eAAe,CAAC,EAAG;AAE5B,QAAI,WAAW,IAAI,CAAC;AACpB,QAAI,OAAO,YAAY,YAAY,SAAS,UAAU,GAAG;AACrD,WAAK,SAAS,CAAC,CAAC,IAAI,SAAS,CAAC;AAAA,IAClC,OAAO;AACH,WAAK,QAAQ,IAAI;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AAxEJ,IAyEI,QAAQ,SAAU,SAAS,KAAK;AAC5B,SAAO,SAAS,GAAG,IAAI,IAAI,QAAQ,SAAS,KAAK,IAAI;AACzD;AA3EJ,IA4EI,cAAc,SAAU,KAAK;AACzB,SAAO,MAAM,UAAU,GAAG;AAC9B;AA9EJ,IA+EI,OAAO,SAAU,KAAK,KAAK;AACvB,MAAI,SAAS,GAAG,GAAG;AACf,UAAM,MAAM,UAAU,GAAG;AACzB,WAAO,OAAO,QAAQ,aAAa,MAAM,IAAI,UAAU,GAAG,aAAa;AAAA,EAC3E;AACR;AAMA,IAAI,YAAY,SAAU,IAAI,QAAQ;AAE9B,MAAG,CAAC,MAAM,CAAC,OAAQ;AAEnB,MAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS;AAGhC,SAAO,IAAI,OAAO,UAAU,CAAC,SAAS;AAElC,QAAI,QAAQ,OAAO,CAAC,GAChB,QAAQ,OAAO,IAAI,CAAC;AACxB,QAAI,IAAI;AAGR,WAAO,IAAI,MAAM,UAAU,CAAC,SAAS;AAEjC,UAAI,CAAC,MAAM,CAAC,GAAG;AAAE;AAAA,MAAO;AACxB,gBAAU,MAAM,GAAG,EAAE,KAAK,EAAE;AAE5B,UAAI,CAAC,CAAC,SAAS;AACX,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,kBAAQ,QAAQ,EAAE,CAAC;AACnB,cAAI,MAAM,CAAC;AAEX,cAAI,OAAO,MAAM,YAAY,EAAE,SAAS,GAAG;AACvC,gBAAI,EAAE,WAAW,GAAG;AAChB,kBAAI,OAAO,EAAE,CAAC,KAAK,WAAW;AAE1B,qBAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,cACtC,OAAO;AAEH,qBAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,cACpB;AAAA,YACJ,WAAW,EAAE,UAAU,GAAG;AAEtB,kBAAI,OAAO,EAAE,CAAC,MAAM,aAAa,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO;AACxD,oBAAI,EAAE,SAAS,GAAG;AACd,uBAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,MAAM,MAAM,EAAE,MAAM,CAAC,CAAC,IAAI;AAAA,gBACxD,OAAO;AAEH,uBAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,KAAK,MAAM,OAAO,EAAE,CAAC,CAAC,IAAI;AAAA,gBACxD;AAAA,cACJ,OAAO;AACH,oBAAI,EAAE,UAAU,GAAG;AAEf,uBAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI;AAAA,gBACrD,WAAW,EAAE,UAAU,GAAG;AACtB,uBAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI;AAAA,gBACtE,WAAW,EAAE,SAAS,GAAG;AACrB,uBAAK,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,MAAM,MAAM,CAAC,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI;AAAA,gBAC5F;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ,OAAO;AACH,iBAAK,CAAC,IAAI,QAAQ,QAAQ;AAAA,UAC9B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK;AAAA,EACT;AACJ;AA7DJ,IA+DI,YAAY,SAAU,KAAK,KAAK;AAE5B,WAAS,KAAK,KAAK;AAEf,QAAI,OAAO,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG;AACjD,eAAS,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,QAAQ,KAAK;AACpC,YAAI,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG;AACrB,iBAAQ,MAAM,UAAW,SAAY;AAAA,QACzC;AAAA,MACJ;AAAA,IACJ,WAAW,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG;AACzB,aAAQ,MAAM,UAAW,SAAY;AAAA,IACzC;AAAA,EACJ;AACA,SAAO,IAAI,eAAe,GAAG,IAAI,IAAI,GAAG,IAAI;AACpD;AAMA,IAAI,oBAAoB;AAAA,EAChB,MAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAU,CAAC,OAAO,MAAM;AAAA,EACxB,MAAU,CAAC,OAAO,KAAK;AAAA,EACvB,SAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,OAAU;AAAA,EACV,MAAU,CAAC,OAAO,MAAM;AAAA,EACxB,MAAU;AACd;AAZJ,IAcI,iBAAiB;AAAA,EACb,YAAc;AAAA,EACd,UAAc;AAAA,EACd,UAAc,CAAC,UAAU,MAAM;AAAA,EAC/B,WAAc;AAAA,EACd,YAAc;AAAA,EACd,MAAc,CAAC,MAAM,IAAI;AAAA,EACzB,KAAc,CAAC,WAAW,SAAS;AAAA,EACnC,KAAc;AAClB;AAvBJ,IAyBI,kBAAkB;AAAA,EACd,UAAkB;AAAA,EAClB,QAAkB;AAAA,EAClB,iBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,mBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,gBAAkB;AAAA,EAClB,cAAkB;AAAA,EAClB,UAAkB;AAC1B;AAMA,IAAI,iBAAiB;AAAA,EAEjB,SAAU;AAAA,IAAC;AAAA;AAAA,MAGP;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,gBAAgB,QAAQ,CAAC;AAAA,IAAG;AAAA,MAChD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAK,UAAU,CAAC;AAAA,IAAG;AAAA,MACvC;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG9B;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACrC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MACnC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,IAAG;AAAA,MACjC;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MAEA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC;AAAA,IAAG;AAAA,MACpC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,WAAW,CAAC;AAAA,IAAG;AAAA,MACnC;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAChC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,WAAW,CAAC;AAAA,IAAG;AAAA,MACnC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;AAAA,IAAG;AAAA,MAC5B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAChC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,WAAW,SAAS,cAAc,CAAC;AAAA,IAAG;AAAA,MAC1D;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,cAAc,cAAc,GAAG,OAAO;AAAA,IAAG;AAAA,MAC5D;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,UAAQ,QAAQ,CAAC;AAAA,IAAG;AAAA,MACxC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MACtC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,IAAG;AAAA,MACjC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,IAAG;AAAA,MACjC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MACtC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAS,cAAc,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,gBAAgB,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAC7B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,WAAW,GAAG,OAAO;AAAA,IAAG;AAAA,MAC3C;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,OAAO,cAAc,GAAG,OAAO;AAAA,IAAG;AAAA;AAAA,MACrD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,UAAU,WAAW,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,WAAW,CAAC;AAAA,IAAG;AAAA,MAC3C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,SAAS,GAAG,OAAO;AAAA,IAAG;AAAA,MACzC;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,IAAI;AAAA,IAAG;AAAA,MACX;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,IAAI;AAAA,IAAG;AAAA;AAAA,MAGpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MACnC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAC5C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,QAAQ,GAAG,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAC1B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,MAAM,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAErD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MAEpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAO,WAAW,CAAC;AAAA,IAAG;AAAA,MAE1C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAK,WAAW,CAAC;AAAA,IAAG;AAAA,MAExC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,SAAO,UAAU,GAAG,OAAO;AAAA,IAAG;AAAA,MAEzC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,YAAY,cAAc,CAAC;AAAA,IAAG;AAAA,MAElD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,gBAAgB,QAAQ,CAAC;AAAA,IAAG;AAAA,MAEhD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MAEpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,gBAAgB,QAAQ,CAAC;AAAA,IAAG;AAAA,MAChD;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,gBAAgB,QAAQ,CAAC;AAAA,IAAG;AAAA,MACvC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,IAAI;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC;AAAA,IAAG;AAAA,MAE3B;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA;AAAA,MAGpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,gBAAgB,OAAO,GAAG,OAAO;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,UAAU,GAAG,OAAO;AAAA,IAAG;AAAA,MAClC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,UAAQ,UAAU,CAAC;AAAA,IAAG;AAAA,MAC1C;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MAEA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,CAAC;AAAA,IAAG;AAAA,MAEjC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,CAAC,SAAS,aAAa,KAAK,CAAC;AAAA,EAC3C;AAAA,EAEA,KAAM;AAAA,IAAC;AAAA,MAEH;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,IAAG;AAAA,MAE9B;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,MAAM,CAAC;AAAA,IAAG;AAAA,MAE7B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,IAAG;AAAA,MAE9B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,IAAG;AAAA;AAAA,MAG9B;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,KAAK,CAAC;AAAA,IAAG;AAAA,MAE5B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,IAAG;AAAA;AAAA,MAE9B;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,EAC/B;AAAA,EAEA,QAAS;AAAA,IAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAON;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA,MACA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA;AAAA,MACA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7B;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG9C;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAE,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAE1D;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG3D;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG/C;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC5C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,EAAE,WAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,KAAM,KAAK,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGzH;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7C;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG9C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGhD;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG9C;AAAA,MACA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAChD;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGhD;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG1C;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACpC;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC;AAAA,IAAG;AAAA;AAAA,MAG5D;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAE9C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,KAAK;AAAA,IAAG;AAAA;AAAA,MAGpB;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC5C;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,eAAe,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG/D;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,SAAS,eAAe,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG1E;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACpC;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGlD;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC5C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG5C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGjD;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7C;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,QAAQ,QAAQ,GAAG,OAAO,CAAC,MAAM,WAAW,EAAE,UAAW,CAAC,WAAW,OAAO,GAAG,KAAM,SAAS,CAAC,CAAC;AAAA,IAAG;AAAA;AAAA,MAGxG;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG9C;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG/C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGjD;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGnD;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGnD;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGjD;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAChD;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAGhD;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA,MAG7C;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAEpC;AAAA;AAAA,MAEA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAEpC;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAEpC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACjD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACnD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACpC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,OAAO,GAAG,GAAG,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC/D;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA;AAAA;AAAA;AAAA,MAM7C;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC9B;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,KAAK,SAAS,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAClE;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACrC;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACpC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,CAAC,OAAO,QAAM,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACpD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,aAAW,mBAAmB,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACjF;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,KAAK,aAAa,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACrE;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,aAAW,WAAW,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACzE;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,OAAO,UAAU,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC7D;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACjD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAE;AAAA,MAC7C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACrC;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,QAAQ,aAAa,MAAM,WAAW,EAAC,MAAK,MAAK,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA;AAAA;AAAA;AAAA,MAM3F;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAClD;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACrC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA;AAAA;AAAA;AAAA,MAM/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MACjD;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MACtC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MACjD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAClD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC9C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC5C;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA;AAAA;AAAA;AAAA,MAM/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC;AAAA,IAAG;AAAA,MAC1C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;AAAA,IAAG;AAAA,MAChC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,MAAM,EAAE,CAAC;AAAA,IAAG;AAAA,MAC5C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAAA,IAAG;AAAA;AAAA;AAAA;AAAA,MAMjB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,QAAQ,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC/B;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAChD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA;AAAA;AAAA;AAAA,MAMvB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC7B;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MACtB;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,MAAM,WAAW,EAAE,UAAW,UAAU,MAAO,MAAM,KAAM,OAAO,CAAC,CAAC;AAAA,IAAG;AAAA,MACnF;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACrB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,MAAM,CAAC;AAAA,IAAG;AAAA,MACrB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,CAAC;AAAA,EAClC;AAAA,EAEA,QAAS;AAAA,IAAC;AAAA,MAEN;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAK,MAAM,CAAC;AAAA,IAAG;AAAA,MAEnC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MAEpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAE/B;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,CAAC;AAAA,IAAG;AAAA,MAEvB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,IAAI;AAAA,EACrB;AAAA,EAEA,IAAK;AAAA,IAAC;AAAA;AAAA,MAGF;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,SAAS,WAAW,iBAAiB,CAAC;AAAA,IAAG;AAAA,MAChE;AAAA;AAAA;AAAA,MAEA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,SAAS,WAAW,IAAI,WAAW,iBAAiB,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC9E;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA;AAAA,MAGpB;AAAA;AAAA,MACA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,SAAS,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC;AAAA,IAAG;AAAA,MAC1C;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,OAAO,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC;AAAA,IAAG;AAAA;AAAA,MAG5C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,aAAa,UAAU,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,aAAa,UAAU,CAAC;AAAA,IAAG;AAAA,MAC/C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,aAAa,eAAe,CAAC;AAAA,IAAG;AAAA,MACpD;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,aAAa,QAAQ,CAAC;AAAA,IAAG;AAAA,MAC7C;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,UAAU,CAAC;AAAA,IAAG;AAAA;AAAA,MAGlC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,IAAI;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,QAAQ,UAAU,GAAG,OAAO;AAAA,IAAG;AAAA,MAC1C;AAAA;AAAA;AAAA,MAEA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,UAAU,CAAC;AAAA,IAAG;AAAA,MAClC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,IAAG;AAAA,MACjC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,UAAQ,KAAK,CAAC;AAAA,IAAG;AAAA,MACrC;AAAA;AAAA,MACA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC/B;AAAA;AAAA,IAEA;AAAA,IAAG,CAAC,CAAC,SAAS,WAAW,EAAC,MAAK,OAAM,MAAK,OAAM,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,CAAC,GAAG,CAAC,MAAM,OAAO,CAAC;AAAA,IAAG;AAAA,MAC1J;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;AAAA,IAAG;AAAA;AAAA,MAGjC;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,WAAW,GAAG,OAAO;AAAA,IAAE;AAAA;AAAA,MAGlC;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,IAAG;AAAA,MACpB;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO;AAAA,IAAG;AAAA,MACjC;AAAA;AAAA,MACA;AAAA;AAAA,IACA;AAAA,IAAG,CAAC,MAAM,OAAO;AAAA,EACrB;AACJ;AAMA,IAAI,eAAgB,WAAY;AACxB,MAAI,QAAQ,EAAE,MAAO,CAAC,GAAG,UAAW,CAAC,GAAG,aAAc,CAAC,GAAG,UAAW,CAAC,EAAC;AACvE,WAAS,KAAK,MAAM,MAAM;AAAA,IACtB,CAAC,YAAY,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC;AAAA,IACzC,CAAC,QAAQ,CAAC,YAAY,CAAC;AAAA,IACvB,CAAC,WAAW,CAAC,MAAM,OAAO,MAAM,CAAC;AAAA,IACjC,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC;AAAA,IAC3B,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EAC3B,CAAC;AACD,WAAS,KAAK,MAAM,UAAU;AAAA,IAC1B,CAAC,YAAY,CAAC,SAAS,KAAK,CAAC;AAAA,IAC7B,CAAC,WAAW,CAAC,OAAO,CAAC;AAAA,IACrB,CAAC,OAAO,CAAC,OAAO,CAAC;AAAA,EACrB,CAAC;AACD,WAAS,KAAK,MAAM,aAAa;AAAA,IAC7B,CAAC,YAAY,aAAa;AAAA,IAC1B,CAAC,OAAO,QAAQ;AAAA,EACpB,CAAC;AACD,WAAS,KAAK,MAAM,UAAU;AAAA,IAC1B,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC;AAAA,IAC5B,CAAC,QAAQ,CAAC,YAAY,CAAC;AAAA,IACvB,CAAC,WAAW,CAAC,QAAQ,KAAK,CAAC;AAAA,IAC3B,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC;AAAA,IAC3B,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EAC3B,CAAC;AACD,SAAO;AACf,EAAG;AAEH,IAAI,cAAc,SAAU,MAAM,UAAU;AAExC,MAAI,aAAa,aAAa,KAAK,QAAQ,GACvC,iBAAiB,aAAa,SAAS,QAAQ,KAAK,GACpD,eAAe,aAAa,YAAY,QAAQ,KAAK,GACrD,iBAAiB,aAAa,SAAS,QAAQ,KAAK;AAExD,WAAS,QAAS;AACd,aAAS,KAAK,MAAM,UAAU;AAAA,EAClC;AAEA,QAAM,UAAU,UAAU,WAAY;AAClC,WAAO;AAAA,EACX;AAEA,QAAM,UAAU,kBAAkB,WAAY;AAG1C,QAAI,CAAC,kBAAkB;AACnB,aAAO,KACE,QAAQ,EACR,IAAI;AAAA,IACjB;AAGA,WAAO,iBACE,qBAAqB,aAAa,EAClC,KAAK,SAAU,KAAK;AACjB,aAAO,KACE,MAAM,IAAI,SAAS,KAAK,KAAK,CAAC,EAC9B,QAAQ,EACR,IAAI;AAAA,IACzB,CAAC;AAAA,EACL;AAEA,QAAM,UAAU,mBAAmB,WAAY;AAC3C,WAAO,KAAK,cAAc,EAAE,IAAI;AAAA,EACpC;AAEA,MAAI,YAAY,WAAW;AACvB,UAAM,UAAU,KAAK,SAAU,YAAY;AACvC,UAAI,KAAK;AACT,eAAS,KAAK,MAAM;AAChB,YAAI,KAAK,eAAe,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,KAAK,SAAS,eAAe,MAAM,cAAc,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,eAAe,MAAM,cAAc,UAAU,IAAI,UAAU,GAAG;AAC/L,eAAK;AACL,cAAI,cAAc,WAAY;AAAA,QAClC,WAAW,cAAc,cAAc,IAAI;AACvC,eAAK,CAAC;AACN;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,UAAM,UAAU,WAAW,WAAY;AACnC,UAAI,MAAM;AACV,eAAS,KAAK,gBAAgB;AAC1B,YAAI,OAAO,KAAK,eAAe,CAAC,CAAC,MAAO,YAAY;AAChD,kBAAQ,MAAM,MAAM,SAAS,KAAK,eAAe,CAAC,CAAC;AAAA,QACvD;AAAA,MACJ;AACA,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEA,MAAI,CAAC,kBAAkB;AACnB,UAAM,UAAU,OAAO,SAAU,IAAI;AACjC,UAAI,OAAO;AACX,UAAI,eAAe,WAAY;AAC3B,iBAAS,QAAQ,MAAM;AACnB,cAAI,KAAK,eAAe,IAAI,GAAG;AAC3B,iBAAK,IAAI,IAAI,KAAK,IAAI;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AACA,mBAAa,YAAY;AAAA,QACrB,IAAK,MAAM,UAAU;AAAA,QACrB,UAAW,MAAM,UAAU;AAAA,MAC/B;AACA,UAAI,cAAc,IAAI,aAAa;AACnC,SAAG,WAAW;AACd,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,SAAO,IAAI,MAAM;AACrB;AAMA,SAAS,SAAU,MAAM,YAAY;AACjC,SAAO,QAAQ,CAAC;AAChB,WAAS,KAAK,MAAM,aAAa;AACjC,MAAI,YAAY;AACZ,aAAS,KAAK,MAAM;AAAA,MAChB,CAAC,QAAQ,gBAAgB,KAAK,SAAS,CAAC,CAAC;AAAA,MACzC,CAAC,aAAa,gBAAgB,KAAK,uBAAuB,CAAC,CAAC;AAAA,MAC5D,CAAC,QAAQ,MAAM,KAAK,KAAK,gBAAgB,CAAC,CAAC;AAAA,MAC3C,CAAC,OAAO,YAAY,KAAK,eAAe,CAAC,CAAC;AAAA,MAC1C,CAAC,UAAU,YAAY,KAAK,kBAAkB,CAAC,CAAC;AAAA,MAChD,CAAC,aAAa,YAAY,KAAK,sBAAsB,CAAC,CAAC;AAAA,MACvD,CAAC,cAAc,YAAY,KAAK,cAAc,CAAC,CAAC;AAAA,MAChD,CAAC,aAAa,gBAAgB,KAAK,sBAAsB,CAAC,CAAC;AAAA,MAC3D,CAAC,SAAS,YAAY,KAAK,iBAAiB,CAAC,CAAC;AAAA,IAClD,CAAC;AAAA,EACL,OAAO;AACH,aAAS,QAAQ,MAAM;AACnB,UAAG,KAAK,eAAe,IAAI,KAAK,OAAO,KAAK,IAAI,MAAM,WAAY,MAAK,IAAI,IAAI,KAAK,IAAI;AAAA,IAC5F;AAAA,EACJ;AACJ;AAEA,SAAS,OAAQ,UAAU,IAAI,QAAQ,MAAM;AAEzC,OAAK,MAAM,SAAU,MAAM;AACvB,QAAI,CAAC,KAAM,QAAO,KAAK;AACvB,WAAO,KAAK,KAAK,eAAe,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI;AAAA,EAC9D;AAEA,OAAK,MAAM,SAAU,MAAM,KAAK;AAC5B,SAAK,KAAK,IAAI,IAAI;AAClB,WAAO;AAAA,EACX;AAEA,OAAK,QAAQ,SAAU,IAAI;AACvB,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAEA,OAAK,gBAAgB,WAAY;AAC7B,QAAI,aAAa,UAAU,aAAa,KAAK,IAAI;AAC7C,cAAQ,KAAK,UAAU;AAAA,QACnB,KAAK;AAED,cAAI,UAAU,SAAS,OAAO,UAAU,MAAM,WAAW,WAAW;AAChE,iBAAK,IAAI,MAAM,OAAO;AAAA,UAC1B;AACA;AAAA,QACJ,KAAK;AAED,cAAI,CAAC,KAAK,IAAI,IAAI,KAAK,oBAAoB,iBAAiB,MAAM,GAAG;AACjE,iBAAK,IAAI,MAAM,MAAM;AAAA,UACzB;AAEA,cAAI,KAAK,IAAI,KAAK,KAAK,eAAe,aAAa,OAAO,UAAU,eAAe,cAAc,UAAU,kBAAkB,UAAU,iBAAiB,GAAG;AACvJ,iBAAK,IAAI,OAAO,MAAM,EACjB,IAAI,MAAM,MAAM;AAAA,UACzB;AACA;AAAA,QACJ,KAAK;AAED,cAAI,CAAC,KAAK,IAAI,IAAI,KAAK,oBAAoB,iBAAiB,QAAQ,GAAG;AACnE,iBAAK,IAAI,MAAM,iBAAiB,QAAQ,CAAC;AAAA,UAC7C;AACA;AAAA,QACJ,KAAK;AACD,cAAI,OAAO,KAAK;AAChB,cAAI,SAAS,SAAUA,WAAU;AAC7B,mBAAO,KAAKA,SAAQ,EACX,QAAQ,EACR,cAAc,EACd,IAAI;AAAA,UACjB;AACA,eAAK,IAAI,YAAY,OAAO,UAAU,CAAC,EAClC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAC1B,IAAI,WAAW,OAAO,SAAS,CAAC,EAChC,IAAI,WAAW,OAAO,SAAS,CAAC,EAChC,IAAI,OAAO,OAAO,KAAK,CAAC;AAAA,MACrC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAEA,OAAK,UAAU,WAAY;AACvB,QAAI,KAAK,YAAY,WAAW;AAC5B,gBAAU,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,MAAM;AAAA,IAClD;AACA,QAAI,KAAK,YAAY,YAAY;AAC7B,WAAK,IAAI,OAAO,SAAS,KAAK,IAAI,OAAO,CAAC,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACX;AAEA,OAAK,UAAU,WAAY;AACvB,QAAIC,QAAO,KAAK,MACZC,UAAS,KAAK;AAElB,YAAQ,KAAK,UAAU;AAAA,MACnB,KAAK;AAAA,MACL,KAAK;AACD,YAAI,SAASD,MAAK,WAAW,KAAKA,MAAK,MAAM,GAAG;AAChD,YAAI,QAAQ;AACR,mBAAS,IAAE,GAAG,IAAE,OAAO,QAAQ,KAAK;AAChC,gBAAI,YAAY,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,GACvC,eAAe,OAAO,CAAC,EAAE;AAC7B,gBAAI,KAAK,YAAY,cACjB,CAAC,eAAe,KAAK,SAAS,MAC7B,CAAC,YACG,QAAQ,KAAK,QAAQ,KAAK,aAAa,YACvC,YAAY,QAAQ,WAAW,KAAK,SAAS,IAC/C;AACC,0BAAY,UAAU,WAAW,eAAe;AAChD,yBAAW,KAAK,IAAI,IAAI;AACxB,kBAAI,EAAE,YAAY,CAAC,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS,IAAI;AACnE,qBAAK,IAAI,MAAM,SAAS,EACnB,IAAI,SAAS,YAAY,EACzB,IAAI,OAAO,SAAS,YAAY,CAAC;AAAA,cAC1C;AACA,yBAAW;AAAA,YACnB;AACA,gBAAI,KAAK,YAAY,aAAa,aAAa,UAAU;AACrD,mBAAK,IAAI,SAAS,YAAY;AAAA,YAClC;AAAA,UACJ;AAAA,QACJ;AACA;AAAA,MACJ,KAAK;AACD,YAAI,WAAWA,MAAK,YAAY;AAChC,YAAI,UAAU;AACV,cAAI,YAAYA,MAAK,OAAO,KAAK,KAAM,aAAY;AACnD,oBAAU,KAAK,KAAK,MAAM,WAAW,KAAKC,OAAM;AAAA,QACpD;AACA;AAAA,MACJ,KAAK;AACD,YAAID,MAAK,MAAM,GAAG;AACd,eAAK,IAAI,MAAM,MAAM;AAAA,QACzB;AACA,YAAIA,MAAK,KAAK,GAAG;AACb,eAAK,IAAI,OAAOA,MAAK,KAAK,CAAC;AAC3B,cAAI,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,GAAG;AACtC,gBAAI,UAAU,CAAC;AACf,sBAAU,KAAK,SAAS,cAAcA,MAAK,KAAK,IAAI,KAAKC,OAAM;AAC/D,gBAAI,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,QAAQ,MAAM;AACnC,mBAAK,IAAI,MAAM,QAAQ,IAAI;AAAA,YAC/B;AACA,gBAAI,CAAC,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC,QAAQ,QAAQ;AACvC,mBAAK,IAAI,QAAQ,QAAQ,MAAM;AAAA,YACnC;AAAA,UACJ;AAAA,QACJ;AACA,YAAID,MAAK,WAAW,GAAG;AACnB,cAAI;AACJ,cAAI,OAAOA,MAAK,WAAW,MAAM,UAAU;AACvC,gBAAI,MAAM;AACV,mBAAO,CAAC,MAAM,MAAMA,MAAK,WAAW,EAAE,QAAQ;AAC1C,mBAAK,UAAUA,MAAK,WAAW,EAAE,KAAK,GAAG,cAAc;AAAA,YAC3D;AAAA,UACJ,OAAO;AACH,iBAAK,UAAUA,MAAK,WAAW,GAAG,cAAc;AAAA,UACpD;AACA,eAAK,IAAI,MAAM,EAAE;AAAA,QACrB;AACA;AAAA,MACJ,KAAK;AACD,YAAI,SAASA,MAAK,QAAQ;AAC1B,YAAG,QAAQ;AACP,cAAI,YAAYA,MAAK,WAAW;AAChC,cAAI,UAAU,QAAS,aAAa,SAAS,SAAS,SAAS,GAAG,EAAE,KAAK,KAAK,OAAO;AACrF,eAAK,IAAI,MAAM,MAAM,EAChB,IAAI,SAAS,SAAS;AAAA,QAC/B;AAEA,YAAI,KAAK,IAAI,IAAI,KAAK,WAAWA,MAAK,KAAK,KAAK,QAAQ;AACpD,eAAK,IAAI,MAAM,MAAM,EAChB,IAAI,SAAS,MAAS;AAAA,QAC/B;AACA;AAAA,MACJ,KAAK;AACD,YAAI,OAAO,KAAK;AAChB,YAAI,QAAQ,SAAUD,WAAU;AAC5B,iBAAO,KAAKA,SAAQ,EACX,QAAQ,EACR,MAAMC,KAAI,EACV,QAAQ,EACR,IAAI;AAAA,QACjB;AACA,aAAK,IAAI,YAAY,MAAM,UAAU,CAAC,EACjC,IAAI,QAAQ,MAAM,MAAM,CAAC,EACzB,IAAI,WAAW,MAAM,SAAS,CAAC,EAC/B,IAAI,WAAW,MAAM,SAAS,CAAC,EAC/B,IAAI,OAAO,MAAM,KAAK,CAAC;AAAA,IACpC;AACA,WAAO;AAAA,EACX;AAEA,WAAS,KAAK,MAAM;AAAA,IAChB,CAAC,YAAY,QAAQ;AAAA,IACrB,CAAC,MAAM,EAAE;AAAA,IACT,CAAC,QAAQ,IAAI;AAAA,IACb,CAAC,UAAU,MAAM;AAAA,IACjB,CAAC,QAAQ,YAAY,MAAM,QAAQ,CAAC;AAAA,EACxC,CAAC;AAED,SAAO;AACX;AAEA,SAAS,SAAU,IAAI,YAAY,SAAS;AAExC,MAAI,OAAO,OAAO,UAAU;AACxB,QAAI,aAAa,IAAI,IAAI,GAAG;AACxB,UAAI,OAAO,eAAe,UAAU;AAChC,kBAAU;AAAA,MACd;AACA,mBAAa;AAAA,IACjB,OAAO;AACH,gBAAU;AACV,mBAAa;AAAA,IACjB;AACA,SAAK;AAAA,EACT,WAAW,OAAO,OAAO,YAAY,CAAC,aAAa,YAAY,IAAI,GAAG;AAClE,cAAU;AACV,iBAAa;AAAA,EACjB;AAEA,MAAI,SAAS;AACT,QAAI,OAAO,QAAQ,WAAW,WAAW;AAErC,UAAI,KAAK,CAAC;AACV,cAAQ,QAAQ,SAAU,GAAG,GAAG;AAAE,WAAG,OAAO,CAAC,EAAE,YAAY,CAAC,IAAI;AAAA,MAAG,CAAC;AACpE,gBAAU;AAAA,IACd,OAAO;AAEH,UAAI,aAAa,CAAC;AAClB,eAAS,UAAU,SAAS;AACxB,YAAI,QAAQ,eAAe,MAAM,GAAG;AAChC,qBAAW,OAAO,MAAM,EAAE,YAAY,CAAC,IAAI,QAAQ,MAAM;AAAA,QAC7D;AAAA,MACJ;AACA,gBAAU;AAAA,IACd;AAAA,EACJ;AAEA,MAAI,EAAE,gBAAgB,WAAW;AAC7B,WAAO,IAAI,SAAS,IAAI,YAAY,OAAO,EAAE,UAAU;AAAA,EAC3D;AAEA,MAAI,YAAY,OAAO,OAAO,WAAW;AAAA;AAAA,IAChB,WAAW,QAAQ,UAAU,IAAI,QAAQ,UAAU;AAAA;AAAA,MAClD,aAAa,UAAU,YAAa,UAAU;AAAA;AAAA,QAC5C;AAAA;AAAA;AAAA,KAExB,WAAW,IAAI,SAAS,SAAS,IAAI,GACrC,WAAW,aACC,OAAO,gBAAgB,UAAU,IACjC,gBAEZ,iBAAiB,SAAU,UAAU;AACjC,QAAI,YAAY,WAAW;AACvB,aAAO,WAAY;AACf,eAAO,IAAI,OAAO,UAAU,WAAW,UAAU,QAAQ,EAC5C,IAAI,MAAM,SAAS,EACnB,IAAI,YAAY,KAAK,WAAW,CAAC,EACjC,IAAI,QAAQ,KAAK,OAAO,CAAC,EACzB,IAAI,WAAW,KAAK,UAAU,CAAC,EAC/B,IAAI,WAAW,KAAK,UAAU,CAAC,EAC/B,IAAI,OAAO,KAAK,MAAM,CAAC,EACvB,IAAI;AAAA,MACrB;AAAA,IACJ,OAAO;AACH,aAAO,WAAY;AACf,eAAO,IAAI,OAAO,UAAU,WAAW,SAAS,QAAQ,GAAG,QAAQ,EACtD,QAAQ,EACR,IAAI;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AAGJ,WAAS,KAAK,MAAM;AAAA,IAChB,CAAC,cAAc,eAAe,UAAU,CAAC;AAAA,IACzC,CAAC,UAAU,eAAe,MAAM,CAAC;AAAA,IACjC,CAAC,aAAa,eAAe,SAAS,CAAC;AAAA,IACvC,CAAC,aAAa,eAAe,SAAS,CAAC;AAAA,IACvC,CAAC,SAAS,eAAe,KAAK,CAAC;AAAA,IAC/B,CAAC,aAAa,eAAe,SAAS,CAAC;AAAA,IACvC,CAAC,SAAS,WAAY;AAAE,aAAO;AAAA,IAAW,CAAC;AAAA,IAC3C,CAAC,SAAS,SAAUE,KAAI;AACpB,UAAI,SAASA,GAAE;AACX,oBAAYA,IAAG,SAAS,gBAAgB,KAAKA,KAAI,aAAa,IAAIA;AACtE,aAAO;AAAA,IACX,CAAC;AAAA,EACL,CAAC,EACA,MAAM,SAAS;AAEhB,SAAO;AACX;AAEA,SAAS,UAAU;AACnB,SAAS,UAAW,UAAU,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC;AAC1D,SAAS,MAAM,UAAU,CAAC,YAAY,CAAC;AACvC,SAAS,SAAS,UAAU,CAAC,OAAO,QAAQ,MAAM,SAAS,QAAQ,SAAS,QAAQ,UAAU,QAAQ,CAAC;AACvG,SAAS,SAAS,SAAS,KAAK,UAAU,CAAC,MAAM,OAAO,CAAC;", "names": ["itemType", "uaCH", "rgxMap", "ua"]}