{"version": 3, "sources": ["../../lodash/sum.js"], "sourcesContent": ["var baseSum = require('./_baseSum'),\n    identity = require('./identity');\n\n/**\n * Computes the sum of the values in `array`.\n *\n * @static\n * @memberOf _\n * @since 3.4.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {number} Returns the sum.\n * @example\n *\n * _.sum([4, 2, 8, 6]);\n * // => 20\n */\nfunction sum(array) {\n  return (array && array.length)\n    ? baseSum(array, identity)\n    : 0;\n}\n\nmodule.exports = sum;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,WAAW;AAgBf,aAAS,IAAI,OAAO;AAClB,aAAQ,SAAS,MAAM,SACnB,QAAQ,OAAO,QAAQ,IACvB;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}