import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/@gtm-support/core/lib/assert-is-gtm-id.js
var require_assert_is_gtm_id = __commonJS({
  "node_modules/@gtm-support/core/lib/assert-is-gtm-id.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.assertIsGtmId = exports.GTM_ID_PATTERN = void 0;
    exports.GTM_ID_PATTERN = /^GTM-[0-9A-Z]+$/;
    function assertIsGtmId(id) {
      if (typeof id !== "string" || !exports.GTM_ID_PATTERN.test(id)) {
        throw new Error(`GTM-ID '${id}' is not valid`);
      }
    }
    exports.assertIsGtmId = assertIsGtmId;
  }
});

// node_modules/@gtm-support/core/lib/utils.js
var require_utils = __commonJS({
  "node_modules/@gtm-support/core/lib/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.hasScript = exports.loadScript = void 0;
    function loadScript(id, config) {
      var _a, _b, _c, _d, _e;
      const doc = document;
      const script = doc.createElement("script");
      const scriptLoadListener = (event) => {
        var _a2;
        (_a2 = config.onReady) === null || _a2 === void 0 ? void 0 : _a2.call(config, { id, script });
        script.removeEventListener("load", scriptLoadListener);
      };
      script.addEventListener("load", scriptLoadListener);
      window.dataLayer = (_a = window.dataLayer) !== null && _a !== void 0 ? _a : [];
      (_b = window.dataLayer) === null || _b === void 0 ? void 0 : _b.push({
        event: "gtm.js",
        "gtm.start": (/* @__PURE__ */ new Date()).getTime()
      });
      if (!id) {
        return script;
      }
      script.async = !config.defer;
      script.defer = Boolean(config.defer || config.compatibility);
      if (config.nonce) {
        script.nonce = config.nonce;
      }
      const queryString = new URLSearchParams({
        id,
        ...(_c = config.queryParams) !== null && _c !== void 0 ? _c : {}
      });
      const source = (_d = config.source) !== null && _d !== void 0 ? _d : "https://www.googletagmanager.com/gtm.js";
      script.src = `${source}?${queryString}`;
      const parentElement = (_e = config.parentElement) !== null && _e !== void 0 ? _e : doc.body;
      if (typeof (parentElement === null || parentElement === void 0 ? void 0 : parentElement.appendChild) !== "function") {
        throw new Error("parentElement must be a DOM element");
      }
      parentElement.appendChild(script);
      return script;
    }
    exports.loadScript = loadScript;
    function hasScript(source = "https://www.googletagmanager.com/gtm.js") {
      return Array.from(document.getElementsByTagName("script")).some((script) => script.src.includes(source));
    }
    exports.hasScript = hasScript;
  }
});

// node_modules/@gtm-support/core/lib/gtm-support.js
var require_gtm_support = __commonJS({
  "node_modules/@gtm-support/core/lib/gtm-support.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.GtmSupport = void 0;
    var assert_is_gtm_id_1 = require_assert_is_gtm_id();
    var utils_1 = require_utils();
    var GtmSupport = class {
      /**
       * Constructs a new `GtmSupport` instance.
       *
       * @param options Options.
       */
      constructor(options) {
        this.scriptElements = [];
        this.isInBrowserContext = () => typeof window !== "undefined";
        if (Array.isArray(options.id)) {
          for (const idOrObject of options.id) {
            if (typeof idOrObject === "string") {
              (0, assert_is_gtm_id_1.assertIsGtmId)(idOrObject);
            } else {
              (0, assert_is_gtm_id_1.assertIsGtmId)(idOrObject.id);
            }
          }
        } else {
          (0, assert_is_gtm_id_1.assertIsGtmId)(options.id);
        }
        this.id = options.id;
        this.options = {
          enabled: true,
          debug: false,
          loadScript: true,
          defer: false,
          compatibility: false,
          ...options
        };
        delete this.options.id;
      }
      /**
       * Check if plugin is enabled.
       *
       * @returns `true` if the plugin is enabled, otherwise `false`.
       */
      enabled() {
        var _a;
        return (_a = this.options.enabled) !== null && _a !== void 0 ? _a : true;
      }
      /**
       * Enable or disable plugin.
       *
       * When enabling with this function, the script will be attached to the `document` if:
       *
       * - the script runs in browser context
       * - the `document` doesn't have the script already attached
       * - the `loadScript` option is set to `true`
       *
       * @param enabled `true` to enable, `false` to disable. Default: `true`.
       * @param source The URL of the script, if it differs from the default. Default: 'https://www.googletagmanager.com/gtm.js'.
       */
      enable(enabled = true, source) {
        this.options.enabled = enabled;
        if (this.isInBrowserContext() && enabled && !(0, utils_1.hasScript)(source) && this.options.loadScript) {
          if (Array.isArray(this.id)) {
            this.id.forEach((id) => {
              let scriptElement;
              if (typeof id === "string") {
                scriptElement = (0, utils_1.loadScript)(id, {
                  ...this.options
                });
              } else {
                scriptElement = (0, utils_1.loadScript)(id.id, {
                  ...this.options,
                  queryParams: id.queryParams
                });
              }
              this.scriptElements.push(scriptElement);
            });
          } else {
            const scriptElement = (0, utils_1.loadScript)(this.id, {
              ...this.options
            });
            this.scriptElements.push(scriptElement);
          }
        }
      }
      /**
       * Check if plugin is in debug mode.
       *
       * @returns `true` if the plugin is in debug mode, otherwise `false`.
       */
      debugEnabled() {
        var _a;
        return (_a = this.options.debug) !== null && _a !== void 0 ? _a : false;
      }
      /**
       * Enable or disable debug mode.
       *
       * @param enable `true` to enable, `false` to disable.
       */
      debug(enable) {
        this.options.debug = enable;
      }
      /**
       * Returns the `window.dataLayer` array if the script is running in browser context and the plugin is enabled,
       * otherwise `false`.
       *
       * @returns The `window.dataLayer` if script is running in browser context and plugin is enabled, otherwise `false`.
       */
      dataLayer() {
        var _a;
        if (this.isInBrowserContext() && this.options.enabled) {
          return window.dataLayer = (_a = window.dataLayer) !== null && _a !== void 0 ? _a : [];
        }
        return false;
      }
      /**
       * Track a view event with `event: "content-view"`.
       *
       * The event will only be send if the script runs in browser context and the if plugin is enabled.
       *
       * If debug mode is enabled, a "Dispatching TrackView" is logged,
       * regardless of whether the plugin is enabled or the plugin is being executed in browser context.
       *
       * @param screenName Name of the screen passed as `"content-view-name"`.
       * @param path Path passed as `"content-name"`.
       * @param additionalEventData Additional data for the event object. `event`, `"content-name"` and `"content-view-name"` will always be overridden.
       */
      trackView(screenName, path, additionalEventData = {}) {
        var _a, _b;
        const trigger = this.isInBrowserContext() && ((_a = this.options.enabled) !== null && _a !== void 0 ? _a : false);
        if (this.options.debug) {
          console.log(`[GTM-Support${trigger ? "" : "(disabled)"}]: Dispatching TrackView`, { screenName, path });
        }
        if (trigger) {
          const dataLayer = window.dataLayer = (_b = window.dataLayer) !== null && _b !== void 0 ? _b : [];
          dataLayer.push({
            ...additionalEventData,
            event: "content-view",
            "content-name": path,
            "content-view-name": screenName
          });
        }
      }
      /**
       * Track an event.
       *
       * The event will only be send if the script runs in browser context and the if plugin is enabled.
       *
       * If debug mode is enabled, a "Dispatching event" is logged,
       * regardless of whether the plugin is enabled or the plugin is being executed in browser context.
       *
       * @param param0 Object that will be used for configuring the event object passed to GTM.
       * @param param0.event `event`, default to `"interaction"` when pushed to `window.dataLayer`.
       * @param param0.category Optional `category`, passed as `target`.
       * @param param0.action Optional `action`, passed as `action`.
       * @param param0.label Optional `label`, passed as `"target-properties"`.
       * @param param0.value Optional `value`, passed as `value`.
       * @param param0.noninteraction Optional `noninteraction`, passed as `"interaction-type"`.
       */
      trackEvent({ event, category = null, action = null, label = null, value = null, noninteraction = false, ...rest } = {}) {
        var _a, _b;
        const trigger = this.isInBrowserContext() && ((_a = this.options.enabled) !== null && _a !== void 0 ? _a : false);
        if (this.options.debug) {
          console.log(`[GTM-Support${trigger ? "" : "(disabled)"}]: Dispatching event`, {
            event,
            category,
            action,
            label,
            value,
            ...rest
          });
        }
        if (trigger) {
          const dataLayer = window.dataLayer = (_b = window.dataLayer) !== null && _b !== void 0 ? _b : [];
          dataLayer.push({
            event: event !== null && event !== void 0 ? event : "interaction",
            target: category,
            action,
            "target-properties": label,
            value,
            "interaction-type": noninteraction,
            ...rest
          });
        }
      }
    };
    exports.GtmSupport = GtmSupport;
  }
});

// node_modules/@gtm-support/core/lib/index.js
var require_lib = __commonJS({
  "node_modules/@gtm-support/core/lib/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadScript = exports.hasScript = exports.GtmSupport = exports.GTM_ID_PATTERN = exports.assertIsGtmId = void 0;
    var assert_is_gtm_id_1 = require_assert_is_gtm_id();
    Object.defineProperty(exports, "assertIsGtmId", { enumerable: true, get: function() {
      return assert_is_gtm_id_1.assertIsGtmId;
    } });
    Object.defineProperty(exports, "GTM_ID_PATTERN", { enumerable: true, get: function() {
      return assert_is_gtm_id_1.GTM_ID_PATTERN;
    } });
    var gtm_support_1 = require_gtm_support();
    Object.defineProperty(exports, "GtmSupport", { enumerable: true, get: function() {
      return gtm_support_1.GtmSupport;
    } });
    var utils_1 = require_utils();
    Object.defineProperty(exports, "hasScript", { enumerable: true, get: function() {
      return utils_1.hasScript;
    } });
    Object.defineProperty(exports, "loadScript", { enumerable: true, get: function() {
      return utils_1.loadScript;
    } });
  }
});

// node_modules/@gtm-support/vue2-gtm/dist/index.js
var require_dist = __commonJS({
  "node_modules/@gtm-support/vue2-gtm/dist/index.js"(exports) {
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = exports && exports.__generator || function(thisArg, body) {
      var _ = { label: 0, sent: function() {
        if (t[0] & 1) throw t[1];
        return t[1];
      }, trys: [], ops: [] }, f, y, t, g;
      return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([n, v]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
          if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
          if (y = 0, t) op = [op[0] & 2, t.value];
          switch (op[0]) {
            case 0:
            case 1:
              t = op;
              break;
            case 4:
              _.label++;
              return { value: op[1], done: false };
            case 5:
              _.label++;
              y = op[1];
              op = [0];
              continue;
            case 7:
              op = _.ops.pop();
              _.trys.pop();
              continue;
            default:
              if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                _ = 0;
                continue;
              }
              if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                _.label = op[1];
                break;
              }
              if (op[0] === 6 && _.label < t[1]) {
                _.label = t[1];
                t = op;
                break;
              }
              if (t && _.label < t[2]) {
                _.label = t[2];
                _.ops.push(op);
                break;
              }
              if (t[2]) _.ops.pop();
              _.trys.pop();
              continue;
          }
          op = body.call(thisArg, _);
        } catch (e) {
          op = [6, e];
          y = 0;
        } finally {
          f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.useGtm = exports.GtmPlugin = exports.loadScript = exports.hasScript = exports.GtmSupport = exports.assertIsGtmId = void 0;
    var core_1 = require_lib();
    Object.defineProperty(exports, "GtmPlugin", { enumerable: true, get: function() {
      return core_1.GtmSupport;
    } });
    var gtmPlugin;
    function install(Vue, options) {
      if (options === void 0) {
        options = { id: "" };
      }
      options = __assign({ trackOnNextTick: false }, options);
      gtmPlugin = new core_1.GtmSupport(options);
      Vue.prototype.$gtm = Vue.gtm = gtmPlugin;
      if (gtmPlugin.isInBrowserContext()) {
        if (options.vueRouter) {
          initVueRouterGuard(Vue, options.vueRouter, options.ignoredViews, options.trackOnNextTick);
        }
        if (gtmPlugin.options.enabled && gtmPlugin.options.loadScript) {
          if (Array.isArray(options.id)) {
            options.id.forEach(function(id) {
              if (typeof id === "string") {
                (0, core_1.loadScript)(id, options);
              } else {
                var newConf = __assign({}, options);
                if (id.queryParams != null) {
                  newConf.queryParams = __assign(__assign({}, newConf.queryParams), id.queryParams);
                }
                (0, core_1.loadScript)(id.id, newConf);
              }
            });
          } else {
            (0, core_1.loadScript)(options.id, options);
          }
        }
      }
    }
    function initVueRouterGuard(Vue, vueRouter, ignoredViews, trackOnNextTick, deriveAdditionalEventData) {
      var _this = this;
      if (ignoredViews === void 0) {
        ignoredViews = [];
      }
      if (deriveAdditionalEventData === void 0) {
        deriveAdditionalEventData = function() {
          return {};
        };
      }
      if (!vueRouter) {
        console.warn("[VueGtm]: You tried to register 'vueRouter' for vue-gtm, but 'vue-router' was not found.");
        return;
      }
      vueRouter.afterEach(function(to, from) {
        return __awaiter(_this, void 0, void 0, function() {
          var name, additionalEventData, _a, baseUrl, fullUrl;
          var _b, _c;
          return __generator(this, function(_d) {
            switch (_d.label) {
              case 0:
                if (typeof to.name !== "string" || Array.isArray(ignoredViews) && ignoredViews.includes(to.name) || typeof ignoredViews === "function" && ignoredViews(to, from)) {
                  return [
                    2
                    /*return*/
                  ];
                }
                name = to.meta && typeof to.meta.gtm === "string" && !!to.meta.gtm ? to.meta.gtm : to.name;
                _a = [{}];
                return [4, deriveAdditionalEventData(to, from)];
              case 1:
                additionalEventData = __assign.apply(void 0, [__assign.apply(void 0, _a.concat([_d.sent()])), (_b = to.meta) === null || _b === void 0 ? void 0 : _b.gtmAdditionalEventData]);
                baseUrl = (_c = vueRouter.options.base) !== null && _c !== void 0 ? _c : "";
                fullUrl = baseUrl;
                if (!fullUrl.endsWith("/")) {
                  fullUrl += "/";
                }
                fullUrl += to.fullPath.startsWith("/") ? to.fullPath.substr(1) : to.fullPath;
                if (trackOnNextTick) {
                  Vue.nextTick(function() {
                    gtmPlugin === null || gtmPlugin === void 0 ? void 0 : gtmPlugin.trackView(name, fullUrl, additionalEventData);
                  });
                } else {
                  gtmPlugin === null || gtmPlugin === void 0 ? void 0 : gtmPlugin.trackView(name, fullUrl, additionalEventData);
                }
                return [
                  2
                  /*return*/
                ];
            }
          });
        });
      });
    }
    var _default = { install };
    var core_2 = require_lib();
    Object.defineProperty(exports, "assertIsGtmId", { enumerable: true, get: function() {
      return core_2.assertIsGtmId;
    } });
    Object.defineProperty(exports, "GtmSupport", { enumerable: true, get: function() {
      return core_2.GtmSupport;
    } });
    Object.defineProperty(exports, "hasScript", { enumerable: true, get: function() {
      return core_2.hasScript;
    } });
    Object.defineProperty(exports, "loadScript", { enumerable: true, get: function() {
      return core_2.loadScript;
    } });
    exports.default = _default;
    function useGtm() {
      return gtmPlugin;
    }
    exports.useGtm = useGtm;
  }
});

export {
  require_dist
};
//# sourceMappingURL=chunk-SGI55GFA.js.map
