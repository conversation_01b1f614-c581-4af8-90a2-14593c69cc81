import forEach from 'lodash/forEach';
import mapValues from 'lodash/mapValues';
import pick from 'lodash/pick';
import set from 'lodash/set';
import unset from 'lodash/unset';

import { getCountryChoices } from '@core/helpers/address/countries';
import i18n from '@core/plugins/i18n';

import { DOCUMENT_FORM_ICON_MAP } from '@ekyc/helpers/document-types';

import { CreatorItemAddress } from '@/helpers/creator/items/address';
import { CreatorItemChoice } from '@/helpers/creator/items/choice';
import { CreatorItemCountry } from '@/helpers/creator/items/country';
import { CreatorItemDataField } from '@/helpers/creator/items/data-field';
import { CreatorItemDate } from '@/helpers/creator/items/date';
import { CreatorItemFieldset } from '@/helpers/creator/items/fieldset';
import { CreatorItemFullName } from '@/helpers/creator/items/full-name';
import { CreatorItemGender } from '@/helpers/creator/items/gender';
import { CreatorItemNamePrefix } from '@/helpers/creator/items/name-prefix';
import { CreatorItemParagraph } from '@/helpers/creator/items/paragraph';
import { HasMaxAttemptWarning } from '@/helpers/creator/mixins/has-max-attempt-warning';
import { HasOverrideImages } from '@/helpers/creator/mixins/has-override-images';
import { HasRefs } from '@/helpers/creator/mixins/has-refs';

import {
  doGetUnionAcceptedCountryChoices,
  doGetValidAcceptedDocumentChoices,
  getUnionAcceptedCountryChoices,
} from '../document-utils-next';
import { CreatorItemBackCard } from './backcard';
import { CreatorItemDocumentNumbers } from './document-numbers';

const ClassHasOverrideImages = HasOverrideImages(HasRefs(CreatorItemFieldset), {
  'id-card-illustration': undefined,
  'passport-illustration': undefined,
  'id-card-alert-illustration': undefined,
  'passport-alert-illustration': undefined,
  'phone-call': undefined,
  processing: undefined,
});

const ClassHasMaxAttemptWarning = HasMaxAttemptWarning(
  ClassHasOverrideImages,
  'https://cdn.uppass.io/images/verifio/ekyc-example/sample-id-verification-v-next-max-attempt.png',
  'ekyc_document',
);

const ClassFinal = ClassHasMaxAttemptWarning;

export class CreatorItemDocumentNext<
  T extends Types.ISchemaItemEkycDocument = Types.ISchemaItemEkycDocument,
> extends ClassFinal<T> {
  static readonly TYPE_ID: Types.FlowItemType = 'ekyc_document_item_next';

  static readonly TYPE_LABEL = 'ID Document Capture';

  static readonly TYPE_IMAGE =
    'https://cdn.uppass.io/ekyc/assets/themes/blue/id-card-with-frame.svg';

  static readonly IS_SPECIAL = true;

  declare parent: Types.CreatorStepDocumentNext;

  allowActions = {
    add: false,
    duplicate: false,
    hide: false,
    delete: false,
  };

  canMove = false;

  generateName() {
    this.name = 'ekyc_document';
    return this.name;
  }

  fields = {
    document_type: {
      item: undefined as unknown as CreatorItemChoice<
        Types.ISchemaItemChoice<Types.EkycDocumentItemTypes>
      >,
      class: CreatorItemChoice,
      enabled: true,
      default: {
        name: 'ekyc_document_type',
        choice: {
          type: 'single',
          style: 'custom',
        },
      },
    },
    country: {
      class: CreatorItemCountry,
      item: undefined as unknown as CreatorItemCountry,
      enabled: true,
      default: {
        name: 'ekyc_document_country',
        prefill: {
          disabled: true,
          run_only_empty: true,
        },
      },
      base: {
        searchable: false,
      },
    },
    preview: {
      item: undefined as unknown as CreatorItemParagraph,
      class: CreatorItemParagraph,
      enabled: false,
      default: {},
    },
    ekyc_backcard_item: {
      class: CreatorItemBackCard,
      item: undefined as unknown as CreatorItemBackCard,
      enabled: false,
      base: {
        name: 'ekyc_backcard',
      },
    },
  };

  refs = {
    document_numbers: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemDocumentNumbers>,
    name_prefix: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemNamePrefix>,
    full_name: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemFullName>,
    date_of_birth: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemDate>,
    date_of_issue: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemDate>,
    date_of_expiry: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemDate>,
    home_address: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemAddress>,
    gender: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemGender>,
    full_name_en_first_name: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemDataField>,
    full_name_en_last_name: {
      enabled: true,
      itemName: undefined,
      item: undefined,
    } as Types.RefItemInfo<CreatorItemDataField>,
  };

  lockedFieldInfo: Record<string, boolean> = {
    document_numbers: false,
    name_prefix: false,
    full_name: false,
    date_of_birth: false,
    date_of_issue: false,
    date_of_expiry: false,
    home_address: false,
    gender: false,
    full_name_en_first_name: false,
    full_name_en_last_name: false,
  };

  acceptedDocumentTypes: Types.EkycDocumentItemTypes[] = ['front_card', 'passport'];

  acceptedCountriesMap = {
    front_card: [],
    passport: [],
    residence_permit: [],
    driver_license: [],
    thai_alien_card: [],
    ci_passport: [],
    work_permit_card: [],
    work_permit_book: [],
    travel_document: [],
    white_card: [],
    border_pass: [],
    monk_card: [],
    immigration_card: [],
    other_document: [],
  } as Record<Types.EkycDocumentItemTypes, string[]>;

  includeNfc = false;

  includeBack = {
    front_card: false,
    residence_permit: false,
    driver_license: false,
    thai_alien_card: false,
  };

  autoDetectDocumentType = false;

  autoDetectCountry = {
    passport: false,
    ci_passport: false,
    work_permit_card: false,
    work_permit_book: false,
    travel_document: false,
    immigration_card: false,
    white_card: false,
    border_pass: false,
    monk_card: false,
    other_document: false,
  };

  overrideDocumentTypeImages: Record<Types.EkycDocumentItemTypes, string> = mapValues(
    DOCUMENT_FORM_ICON_MAP,
    () => undefined,
  );

  constructor() {
    super();

    this.properties.basic.items = [
      {
        label: 'Accepted ID Documents',
        get: () => {
          if (this.fields.country.enabled && this.fields.document_type.enabled) {
            return 'Choose the countries and document types allowed for ID verification';
          }
          if (this.fields.country.enabled) {
            return 'Choose the countries allowed for ID verification';
          }
          if (this.fields.document_type.enabled) {
            return 'Choose the document types allowed for ID verification';
          }
          return '';
        },
        type: 'ShowText',
      },
      // ----------------------------------------
      {
        label: 'Accepted Countries',
        class: 'has-small-label',
        type: 'SelectMultipleCheckbox',
        checkShow: () => this.fields.country.enabled,
        validate: val => val.length > 0 || 'Please select at least one country',
        get: () => this.acceptedCountriesMap.passport,
        set: val => {
          this.acceptedCountriesMap.passport = val;
          if (val.length === 1) {
            this.fields.country.item.prefill.disabled = false;
          }
        },
        display: {
          selectAllLabel: 'All countries selected',
          selectSomeLabel: 'countries selected',
          noSelectLabel: 'No country selected',
          searchBoxPlaceholder: 'Search for countries',
        },
        choices: () => doGetUnionAcceptedCountryChoices(this),
      },
      {
        label: 'Set the default country.',
        type: 'Checkbox',
        checkShow: () => this.fields.country.enabled,
        disabled: () => this.acceptedCountriesMap.passport.length < 2,
        get: () => !this.fields.country.item.prefill.disabled,
        set: val => {
          this.fields.country.item.prefill.disabled = !val;
        },
      },
      {
        type: 'Select',
        placeholder: 'Selected default country',
        disabled: () =>
          this.fields.country.item.prefill.disabled ||
          this.acceptedCountriesMap.passport.length < 2,
        get: () => this.fields.country.item.prefill.value,
        set: val => {
          this.fields.country.item.prefill.value = val;
        },
        choices: () =>
          getCountryChoices().filter(c =>
            (this.acceptedCountriesMap.passport || []).includes(c.value),
          ),
        style: 'display: block; margin-bottom: 16px;',
      },
      {
        label: 'Auto detect document type',
        type: 'Checkbox',
        get: () => this.autoDetectDocumentType,
        set: val => {
          this.autoDetectDocumentType = val;
        },
      },
      {
        label: 'Accepted Documents',
        type: 'CheckboxGroup',
        class: 'accepted_documents_ekyc has-small-label',
        checkShow: () => this.fields.document_type.enabled,
        get: () => this.acceptedDocumentTypes,
        validate: val => val.length > 0 || 'Must select at least one document type',
        set: val => {
          this.acceptedDocumentTypes = val;
        },
        choices: () => doGetValidAcceptedDocumentChoices(this),
        hideCheckbox: true,
      },
    ];

    this.properties.dev.items = [
      ...this.properties.dev.items,
      {
        label: 'Enable accepted countries',
        type: 'Switch',
        get: () => this?.fields.country.enabled,
        set: (val: boolean) => {
          this.fields.country.enabled = val;
        },
      },
      {
        label: 'Enable accepted document types',
        type: 'Switch',
        get: () => this?.fields.document_type.enabled,
        set: (val: boolean) => {
          this.fields.document_type.enabled = val;
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'mask_scale_x (% of screen height)',
        placeholder: '0.5',
        type: 'Number',
        decimal: 3,
        get: () => this.schema.frame_size?.mask_scale_x,
        set: val => {
          set(this.schema, 'frame_size.mask_scale_x', val || undefined);
        },
      },
      {
        label: 'mask_scale_y (% of screen height)',
        placeholder: '0.33',
        type: 'Number',
        decimal: 3,
        get: () => this.schema.frame_size?.mask_scale_y,
        set: val => {
          set(this.schema, 'frame_size.mask_scale_y', val || undefined);
        },
      },
      {
        label: 'max_mask_x (% of screen width)',
        placeholder: '0.9',
        type: 'Number',
        decimal: 3,
        get: () => this.schema.frame_size?.max_mask_x,
        set: val => {
          set(this.schema, 'frame_size.max_mask_x', val || undefined);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'camera_rotate_value',
        placeholder: '0-360',
        type: 'Number',
        decimal: 3,
        get: () => this.schema.camera_rotate_value,
        set: val => {
          set(this.schema, 'camera_rotate_value', val || undefined);
        },
      },
      {
        label: 'camera_rotate_field',
        placeholder: 'ekyc_document_camera_rotate_angle',
        type: 'Textbox',
        get: () => this.schema.camera_rotate_field,
        set: val => {
          set(this.schema, 'camera_rotate_field', val || undefined);
        },
      },
      {
        label: 'camera_zoom_value',
        placeholder: '0.* 1.* 2.*',
        type: 'Number',
        decimal: 3,
        get: () => this.schema.camera_zoom_value,
        set: val => {
          set(this.schema, 'camera_zoom_value', val || undefined);
        },
      },
      {
        label: 'camera_zoom_field',
        placeholder: 'ekyc_document_camera_zoom_ratio',
        type: 'Textbox',
        get: () => this.schema.camera_zoom_field,
        set: val => {
          set(this.schema, 'camera_zoom_field', val || undefined);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Sample image (ID Card)',
        type: 'Image',
        get: () => this.overrideImages['id-card-illustration'],
        set: (val: string) => {
          this.overrideImages['id-card-illustration'] = val;
        },
      },
      {
        label: 'Sample image (Passport)',
        type: 'Image',
        get: () => this.overrideImages['passport-illustration'],
        set: (val: string) => {
          this.overrideImages['passport-illustration'] = val;
        },
      },
      {
        label: 'Sample image (Thai Alien Card)',
        type: 'Image',
        get: () => this.overrideImages['alien-card-illustration'],
        set: (val: string) => {
          this.overrideImages['alien-card-illustration'] = val;
        },
      },
      {
        label: 'Processing image',
        type: 'Image',
        get: () => this.overrideImages.processing,
        set: (val: string) => {
          this.overrideImages.processing = val;
        },
      },
      {
        type: 'Divider',
      },
      ...Object.keys(this.overrideDocumentTypeImages).map(
        key =>
          ({
            label: `Choice Image (${key})`,
            type: 'Image',
            get: () => this.overrideDocumentTypeImages[key],
            set: val => {
              this.overrideDocumentTypeImages[key] = val;
            },
          }) as Types.PropertyConfiguratorImage,
      ),
      {
        type: 'Divider',
      },
      {
        label: 'Fail image (ID Card)',
        type: 'Image',
        get: () => this.overrideImages['id-card-alert-illustration'],
        set: (val: string) => {
          this.overrideImages['id-card-alert-illustration'] = val;
        },
      },
      {
        label: 'Fail image (Passport)',
        type: 'Image',
        get: () => this.overrideImages['passport-alert-illustration'],
        set: (val: string) => {
          this.overrideImages['passport-alert-illustration'] = val;
        },
      },
      {
        label: 'Max attempt image',
        type: 'Image',
        get: () => this.overrideImages['phone-call'],
        set: (val: string) => {
          this.overrideImages['phone-call'] = val;
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Show popup that try to convert desktop user to mobile',
        type: 'Switch',
        get: () => this.schema.show_popup_desktop,
        set: (val: boolean) => {
          this.schema.show_popup_desktop = val;
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Show popup if no camera permission',
        type: 'Switch',
        get: () => this.schema.show_popup_camera_permission,
        set: (val: boolean) => {
          this.schema.show_popup_camera_permission = val;
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Show popup go to setting if no camera permission',
        type: 'Switch',
        get: () => this.schema.show_popup_go_to_setting,
        set: (val: boolean) => {
          this.schema.show_popup_go_to_setting = val;
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Ignore OCR',
        type: 'Switch',
        get: () => this.schema.configs?.ignore_ocr || false,
        set: (val: boolean) => {
          this.schema.configs.ignore_ocr = val;
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Ignore Face',
        type: 'Switch',
        get: () => this.schema.configs?.ignore_face || false,
        set: (val: boolean) => {
          set(this.schema, 'configs.ignore_face', val);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Ignore MRZ expiry',
        type: 'Switch',
        get: () => this.schema.configs?.ignore_mrz_expiry || false,
        set: (val: boolean) => {
          set(this.schema, 'configs.ignore_mrz_expiry', val);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Check expiry',
        type: 'Switch',
        get: () => this.schema.configs?.check_expiry || false,
        set: (val: boolean) => {
          set(this.schema, 'configs.check_expiry', val);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Check id match with field',
        type: 'Textbox',
        get: () => this.schema.configs?.check_id_match_with || '',
        set: (val: string) => {
          set(this.schema, 'configs.check_id_match_with', val);
        },
      },
      {
        get: () =>
          'Enter "field name", Note: That field must have value in RegEx form (ex. 1234....89....)',
        type: 'ShowNotice',
      },
      {
        type: 'Divider',
      },
      {
        label: 'Check required OCR fields',
        type: 'InputOption',
        get: () => this.schema.configs?.check_ocr_fields || [],
        set: (val: string[]) => {
          set(this.schema, 'configs.check_ocr_fields', val);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Check warning',
        type: 'Switch',
        get: () => !!this.schema.configs?.check_warning,
        set: val => {
          set(this.schema, 'configs.check_warning', val);
        },
      },
      {
        type: 'Divider',
      },
      {
        label: 'Check age',
        type: 'Rows',
        children: [
          {
            label: 'Which field ?',
            type: 'Select',
            choices: [
              {
                label: 'age',
                value: 'Age',
              },
            ],
            get: () => this.schema.configs?.check_age?.field,
            set: (val: boolean) => {
              set(this.schema, 'configs.check_age.field', val);
            },
          },
          {
            type: 'Columns',
            children: [
              {
                label: 'Min',
                type: 'Number',
                get: () => this.schema.configs?.check_age?.min,
                set: val => {
                  set(this.schema, 'configs.check_age.min', +val);
                },
              },
              {
                label: 'Max',
                type: 'Number',
                get: () => this.schema.configs?.check_age?.max,
                set: val => {
                  set(this.schema, 'configs.check_age.max', +val);
                },
              },
            ],
          },
        ],
      },
    ];
  }

  getAutoFillMapInfo(): Record<
    string,
    (Types.ISchemaItemEkycDocument['autofill_map'][number] & { fill_only?: true })[]
  > {
    const getHomeAddressDest = (key: string) =>
      this.refs.home_address.item?.fields?.[key]?.item?.name || this.refs.home_address.item?.name;
    return {
      document_numbers: [
        {
          src: 'nid',
          dest:
            this.refs.document_numbers.item?.fields?.nid?.item?.name ||
            this.refs.document_numbers.item?.name,
          fill_only: true,
        },
        {
          src: 'document_number',
          dest:
            this.refs.document_numbers.item?.fields?.document_number?.item?.name ||
            this.refs.document_numbers.item?.name,
        },
      ],
      name_prefix: [
        {
          src: 'title',
          dest: this.refs.name_prefix.item?.name,
        },
      ],
      full_name: [
        {
          src: 'firstname',
          dest:
            this.refs.full_name.item?.fields?.first_name?.item?.name ||
            this.refs.full_name.item?.name,
          params: {
            flag_name: 'name_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'lastname',
          dest:
            this.refs.full_name.item?.fields?.last_name?.item?.name ||
            this.refs.full_name.item?.name,
          params: {
            flag_name: 'name_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'middlename',
          dest:
            this.refs.full_name.item?.fields?.middle_name?.item?.name ||
            this.refs.full_name.item?.name,
          params: {
            flag_name: 'name_type',
            flag_equal: 'parts',
          },
          fill_only: true,
        },
        {
          src: 'fullname',
          dest:
            this.refs.full_name.item?.fields?.full?.item?.name || this.refs.full_name.item?.name,
          params: {
            flag_name: 'name_type',
            flag_equal: 'full',
          },
        },
        {
          src: 'name_type',
          dest: this.refs.full_name.item?.fields?.type?.item?.name,
          fill_only: true,
        },
        {
          src: 'show_middle_name',
          dest: this.refs.full_name.item?.fields?.show_middle_name?.item?.name,
          fill_only: true,
        },
      ],
      date_of_birth: [
        {
          src: 'date_of_birth',
          dest: this.refs.date_of_birth.item?.name,
        },
      ],
      date_of_issue: [
        {
          src: 'date_of_issue',
          dest: this.refs.date_of_issue.item?.name,
        },
      ],
      date_of_expiry: [
        {
          src: 'date_of_expiry',
          dest: this.refs.date_of_expiry.item?.name,
        },
      ],
      home_address: [
        {
          src: 'address_validation_type',
          dest: this.refs.home_address.item?.fields?.type?.item?.name,
        },
        {
          src: 'address_country_code',
          dest: getHomeAddressDest('country'),
        },
        {
          src: 'address',
          dest: this.refs.home_address.item?.fields?.full?.item?.name,
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'full',
          },
        },
        {
          src: 'address_address_1',
          dest: getHomeAddressDest('address_1_common'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'address_address_2',
          dest: getHomeAddressDest('address_2_common'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'address_city',
          dest: getHomeAddressDest('city_common'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'address_postal_code',
          dest: getHomeAddressDest('postal_code_common'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'address_zone',
          dest: getHomeAddressDest('zone_common'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'parts',
          },
        },
        {
          src: 'province',
          dest: getHomeAddressDest('province'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'thai',
          },
        },
        {
          src: 'district',
          dest: getHomeAddressDest('district'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'thai',
          },
        },
        {
          src: 'subdistrict',
          dest: getHomeAddressDest('subdistrict'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'thai',
          },
        },
        {
          src: 'address',
          dest: getHomeAddressDest('address'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'thai',
          },
        },
        {
          src: 'zipcode',
          dest: getHomeAddressDest('zipcode'),
          params: {
            flag_name: 'address_validation_type',
            flag_equal: 'thai',
          },
        },
      ],
      gender: [
        {
          src: 'gender_code',
          dest: this.refs.gender.item?.name,
        },
      ],
      full_name_en_first_name: [
        {
          src: 'firstname_en',
          dest: this.refs.full_name_en_first_name.item?.name,
        },
      ],
      full_name_en_last_name: [
        {
          src: 'lastname_en',
          dest: this.refs.full_name_en_last_name.item?.name,
        },
      ],
      document_type: [
        {
          src: 'document_type',
          dest: this.fields.document_type.item.name,
        },
      ],
    };
  }

  populateDocumentTypes() {
    // Populate document type choices
    this.fields.document_type.item.options = [];

    // pop and push 'front_card' to the first index
    const frontCardIndex = this.acceptedDocumentTypes.indexOf('front_card');
    if (frontCardIndex !== -1) {
      this.acceptedDocumentTypes.unshift(this.acceptedDocumentTypes.splice(frontCardIndex, 1)[0]);
    }

    this.acceptedDocumentTypes.forEach((key, i) => {
      const choice: Types.ISchemaItemChoiceEnum<any> = {
        label: i18n.t(`schema.item.ekyc_document_item.document_type.${key}`) as string,
        value: key,
        icon: DOCUMENT_FORM_ICON_MAP[key],
      };
      if (this.overrideDocumentTypeImages[key]) {
        choice.image = this.overrideDocumentTypeImages[key];
      }
      this.fields.document_type.item.options.push(choice);
    });

    if (this.acceptedDocumentTypes.length <= 1 && this.acceptedCountriesMap.passport.length <= 1) {
      set(
        this.fields.document_type.item,
        'prefill.value',
        this.fields.document_type.item.options[0].value,
      );
    } else {
      set(this.fields.document_type.item, 'prefill.value', '');
    }

    this.fields.document_type.item.schema.display.reverse_direction = true;
  }

  populateNfcRequiredFields() {
    if (this.includeNfc) {
      this.refs.document_numbers.enabled = true;
      this.refs.date_of_birth.enabled = true;
      this.refs.date_of_expiry.enabled = true;

      this.lockedFieldInfo.document_numbers = true;
      this.lockedFieldInfo.date_of_birth = true;
      this.lockedFieldInfo.date_of_expiry = true;
    }
  }

  populateBackCardItem() {
    const documentTypeItemName = this.fields.document_type.item.name;
    const maxAttemptName = `${this.name}_max_attempt`;
    const backCardDocTypes = Object.entries(this.includeBack)
      .filter(([, v]) => v)
      .map(([k]) => k);
    this.fields.ekyc_backcard_item.enabled = backCardDocTypes.length > 0;
    this.fields.ekyc_backcard_item.item.rules = [
      `required_if_condition:${documentTypeItemName},in,[${backCardDocTypes.join(';')}],${maxAttemptName}_max_attempt,!=,true`,
    ];
    if (backCardDocTypes.length === 0) {
      this.fields.ekyc_backcard_item.item.refs.laser_number.enabled = false;
    }
  }

  populateCountryItem() {
    const isAcceptedOneCountry = this.acceptedCountriesMap.passport.length === 1;

    if (isAcceptedOneCountry) {
      this.fields.country.item.prefill.value = this.acceptedCountriesMap.passport[0];
      this.fields.country.item.schema.type = 'DataField';
      this.fields.country.item.schema.layout = 'DefaultWrapper';
      this.fields.country.item.prefill.disabled = false;
    } else {
      this.fields.country.item.schema.type = 'CountrySelect';
      this.fields.country.item.schema.layout = 'InputControl';
    }
  }

  doChangedLocaleActions(options: Types.ChangeLocaleActionsOptions = {}) {
    super.doChangedLocaleActions(options);
    this.fields.document_type.item.label = i18n.t(
      'schema.item.ekyc_document_item.document_type.label',
    ) as string;
    this.fields.country.item.label = i18n.t(
      'schema.item.ekyc_document_item.country.label',
    ) as string;
    this.fields.country.item.placeholder.value = i18n.t(
      'schema.item.ekyc_document_item.country.placeholder',
    ) as string;
  }

  setSchema(schema: T) {
    super.setSchema(schema);
    this.setSchemaOverrideImages(schema);
    this.setSchemaMaxAttemptWarning(schema);
    this.required = true;

    if (this.fields.document_type.enabled) {
      // Filter wrong choices
      this.fields.document_type.item.options = this.fields.document_type.item.options.filter(
        o => o.value in DOCUMENT_FORM_ICON_MAP,
      );
      // Populate document type selector
      if (this.fields.document_type.item.options.length === 0) {
        this.populateDocumentTypes();
      } else {
        this.acceptedDocumentTypes = [];
        this.fields.document_type.item.options.forEach(o => {
          this.acceptedDocumentTypes.push(o.value);
          if (o.image) {
            this.overrideDocumentTypeImages[o.value] = o.image;
          }
        });
      }
    }

    if (this.fields.country.enabled) {
      for (const key in this.acceptedCountriesMap) {
        this.acceptedCountriesMap[key] = schema.accepted_countries?.[key] ?? [];
        if (
          this.acceptedCountriesMap[key].length === 0 &&
          key === 'passport' /* NOTE: currently allow passport only */
        ) {
          this.acceptedCountriesMap[key] = getUnionAcceptedCountryChoices(
            this.acceptedDocumentTypes,
          ).map(choice => choice.value);
        }
      }
    }

    this.includeNfc = schema.include_nfc ?? this.includeNfc;

    Object.keys(this.includeBack).forEach(key => {
      this.includeBack[key] = schema.include_backcard?.[key] ?? this.includeBack[key];
    });

    this.autoDetectDocumentType = schema.auto_detect_document_type ?? this.autoDetectDocumentType;

    Object.keys(this.autoDetectCountry).forEach(key => {
      this.autoDetectCountry[key] =
        schema.auto_detect_country?.[key] ?? this.autoDetectCountry[key];
    });
  }

  syncSchema(options: Types.SyncSchemaOptions = {}) {
    this.populateDocumentTypes();

    super.syncSchema(options);
    super.syncSchemaRefs();
    this.syncSchemaOverrideImages();
    this.syncSchemaMaxAttemptWarning();

    const documentTypeEnabled = this.fields.document_type.enabled;
    const countryEnabled = this.fields.country.enabled;

    if (countryEnabled) {
      this.populateCountryItem();
    }

    if (documentTypeEnabled) {
      this.fields.document_type.item.schema.type = 'InputRadioStaticIcon';
      this.fields.document_type.item.schema.display.disabled_default_preview = true;
    }
    this._schema.validator_rule = this.rules.join('|'); // Need to set because fieldset removed it
    this._schema.type = 'Ekyc.DocumentVNext';
    this._schema.selector_field = this.fields.document_type.item.name;

    set(
      this._schema,
      'configs.enabled_vertical_experience',
      this.schema?.configs?.enabled_vertical_experience ?? true,
    );
    set(this._schema, 'configs.check_warning', this.schema?.configs?.check_warning ?? true);

    // Accepted countries
    this._schema.accepted_countries = this.acceptedCountriesMap;

    // BackCard
    Object.entries(this.includeBack).forEach(([key, value]) => {
      if (value) {
        set(this._schema, `include_backcard.${key}`, true);
      } else {
        unset(this._schema, `include_backcard.${key}`);
      }
    });

    // Auto detect document
    if (this.autoDetectDocumentType) {
      this._schema.auto_detect_document_type = true;
    } else {
      unset(this._schema, 'auto_detect_document_type');
    }

    // Auto detect country
    Object.entries(this.autoDetectCountry).forEach(([key, value]) => {
      if (value) {
        set(this._schema, `auto_detect_country.${key}`, true);
      } else {
        unset(this._schema, `auto_detect_country.${key}`);
      }
    });

    // NFC, required OCR for NFC
    if (this.includeNfc) {
      this._schema.include_nfc = true;
      this.populateNfcRequiredFields();
    } else {
      unset(this._schema, 'include_nfc');
    }

    // Autofill map
    const autoFillMap: Types.ISchemaItemEkycDocument['autofill_map'] = [];
    const checkOcrFields: Types.ISchemaItemEkycDocument['configs']['check_ocr_fields'] = [];

    const autoFillMapInfo = this.getAutoFillMapInfo();

    forEach(autoFillMapInfo, (info, key) => {
      if (!this.refs[key]?.enabled) {
        return;
      }

      // autofill_map
      const validAutoFill = info.filter(value => !!value.dest);
      autoFillMap.push(...validAutoFill.map(value => pick(value, ['src', 'dest', 'params'])));

      // check_ocr_fields
      const validCheckOcrFields = validAutoFill.filter(
        value => this.lockedFieldInfo[key] && !value.fill_only,
      );
      checkOcrFields.push(
        ...validCheckOcrFields.map(value => ({
          field: value.src,
          params: value.params,
        })),
      );
    });

    // Add autofill_map for document_type on autoDetectDocumentType enabled
    if (this.autoDetectDocumentType) {
      const documentTypeInfos = Object.entries(autoFillMapInfo)
        .find(([key]) => key === 'document_type')[1]
        .filter(item => !!item.dest);
      if (documentTypeInfos.length > 0) {
        autoFillMap.push(...documentTypeInfos.map(value => pick(value, ['src', 'dest', 'params'])));
      }
    }

    this._schema.autofill_map = autoFillMap;
    this._schema.configs.check_ocr_fields = checkOcrFields.length > 0 ? checkOcrFields : undefined;
  }
}

export default CreatorItemDocumentNext;
