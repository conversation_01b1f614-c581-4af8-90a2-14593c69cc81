{"version": 3, "sources": ["../../lodash/_arrayReduce.js"], "sourcesContent": ["/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\n\nmodule.exports = arrayReduce;\n"], "mappings": ";;;;;AAAA;AAAA;AAYA,aAAS,YAAY,OAAO,UAAU,aAAa,WAAW;AAC5D,UAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM;AAEvC,UAAI,aAAa,QAAQ;AACvB,sBAAc,MAAM,EAAE,KAAK;AAAA,MAC7B;AACA,aAAO,EAAE,QAAQ,QAAQ;AACvB,sBAAc,SAAS,aAAa,MAAM,KAAK,GAAG,OAAO,KAAK;AAAA,MAChE;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}