/// <reference path="./dynamic-event.d.ts" />

declare module Types {
  type MinimalConditionDataObject = {
    category?: Types.DEGuardDataCategory;
    value: Types.DEGuardDataType;
  };

  type MinimalCondition = {
    op: Types.DEGuardOp;
    initial_data: MinimalConditionDataObject;
    expected_data: MinimalConditionDataObject;
    not?: boolean;
  };

  interface CameraBlockRule {
    default: boolean;
    label: boolean;
  }

  interface DeviceSpecialFlags {
    skipLoadModel?: boolean;
    skipGpu?: boolean;
    skipCpu?: boolean;
    skipTfWasm?: boolean;
    skipTfCpu?: boolean;
    skipTestCheck?: boolean;
    forceCameraLabel?: string;
  }

  interface DeviceSpecialFlagCondition {
    name: string;
    enabled?: boolean;
    conditions: MinimalCondition[];
    flags: DeviceSpecialFlags;
  }

  interface UnsupportedBrowserFlags {
    supported: boolean;
    target: 'safari' | 'chrome' | '';
    update: boolean;
  }

  interface UnsupportedBrowserCondition {
    name: string;
    enabled?: boolean;
    conditions: MinimalCondition[];
    flags: UnsupportedBrowserFlags;
  }
}
