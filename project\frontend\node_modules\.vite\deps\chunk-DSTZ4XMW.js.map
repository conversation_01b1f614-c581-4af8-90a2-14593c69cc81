{"version": 3, "sources": ["../../lodash/_baseSortBy.js", "../../lodash/_compareAscending.js", "../../lodash/_compareMultiple.js", "../../lodash/_baseOrderBy.js"], "sourcesContent": ["/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\n\nmodule.exports = baseSortBy;\n", "var isSymbol = require('./isSymbol');\n\n/**\n * Compares values to sort them in ascending order.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {number} Returns the sort order indicator for `value`.\n */\nfunction compareAscending(value, other) {\n  if (value !== other) {\n    var valIsDefined = value !== undefined,\n        valIsNull = value === null,\n        valIsReflexive = value === value,\n        valIsSymbol = isSymbol(value);\n\n    var othIsDefined = other !== undefined,\n        othIsNull = other === null,\n        othIsReflexive = other === other,\n        othIsSymbol = isSymbol(other);\n\n    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||\n        (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||\n        (valIsNull && othIsDefined && othIsReflexive) ||\n        (!valIsDefined && othIsReflexive) ||\n        !valIsReflexive) {\n      return 1;\n    }\n    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||\n        (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||\n        (othIsNull && valIsDefined && valIsReflexive) ||\n        (!othIsDefined && valIsReflexive) ||\n        !othIsReflexive) {\n      return -1;\n    }\n  }\n  return 0;\n}\n\nmodule.exports = compareAscending;\n", "var compareAscending = require('./_compareAscending');\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n      objCriteria = object.criteria,\n      othCriteria = other.criteria,\n      length = objCriteria.length,\n      ordersLength = orders.length;\n\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\n\nmodule.exports = compareMultiple;\n", "var arrayMap = require('./_arrayMap'),\n    baseGet = require('./_baseGet'),\n    baseIteratee = require('./_baseIteratee'),\n    baseMap = require('./_baseMap'),\n    baseSortBy = require('./_baseSortBy'),\n    baseUnary = require('./_baseUnary'),\n    compareMultiple = require('./_compareMultiple'),\n    identity = require('./identity'),\n    isArray = require('./isArray');\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nmodule.exports = baseOrderBy;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAUA,aAAS,WAAW,OAAO,UAAU;AACnC,UAAI,SAAS,MAAM;AAEnB,YAAM,KAAK,QAAQ;AACnB,aAAO,UAAU;AACf,cAAM,MAAM,IAAI,MAAM,MAAM,EAAE;AAAA,MAChC;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,WAAW;AAUf,aAAS,iBAAiB,OAAO,OAAO;AACtC,UAAI,UAAU,OAAO;AACnB,YAAI,eAAe,UAAU,QACzB,YAAY,UAAU,MACtB,iBAAiB,UAAU,OAC3B,cAAc,SAAS,KAAK;AAEhC,YAAI,eAAe,UAAU,QACzB,YAAY,UAAU,MACtB,iBAAiB,UAAU,OAC3B,cAAc,SAAS,KAAK;AAEhC,YAAK,CAAC,aAAa,CAAC,eAAe,CAAC,eAAe,QAAQ,SACtD,eAAe,gBAAgB,kBAAkB,CAAC,aAAa,CAAC,eAChE,aAAa,gBAAgB,kBAC7B,CAAC,gBAAgB,kBAClB,CAAC,gBAAgB;AACnB,iBAAO;AAAA,QACT;AACA,YAAK,CAAC,aAAa,CAAC,eAAe,CAAC,eAAe,QAAQ,SACtD,eAAe,gBAAgB,kBAAkB,CAAC,aAAa,CAAC,eAChE,aAAa,gBAAgB,kBAC7B,CAAC,gBAAgB,kBAClB,CAAC,gBAAgB;AACnB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxCjB;AAAA;AAAA,QAAI,mBAAmB;AAgBvB,aAAS,gBAAgB,QAAQ,OAAO,QAAQ;AAC9C,UAAI,QAAQ,IACR,cAAc,OAAO,UACrB,cAAc,MAAM,UACpB,SAAS,YAAY,QACrB,eAAe,OAAO;AAE1B,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,SAAS,iBAAiB,YAAY,KAAK,GAAG,YAAY,KAAK,CAAC;AACpE,YAAI,QAAQ;AACV,cAAI,SAAS,cAAc;AACzB,mBAAO;AAAA,UACT;AACA,cAAI,QAAQ,OAAO,KAAK;AACxB,iBAAO,UAAU,SAAS,SAAS,KAAK;AAAA,QAC1C;AAAA,MACF;AAQA,aAAO,OAAO,QAAQ,MAAM;AAAA,IAC9B;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3CjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,UAAU;AADd,QAEI,eAAe;AAFnB,QAGI,UAAU;AAHd,QAII,aAAa;AAJjB,QAKI,YAAY;AALhB,QAMI,kBAAkB;AANtB,QAOI,WAAW;AAPf,QAQI,UAAU;AAWd,aAAS,YAAY,YAAY,WAAW,QAAQ;AAClD,UAAI,UAAU,QAAQ;AACpB,oBAAY,SAAS,WAAW,SAAS,UAAU;AACjD,cAAI,QAAQ,QAAQ,GAAG;AACrB,mBAAO,SAAS,OAAO;AACrB,qBAAO,QAAQ,OAAO,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI,QAAQ;AAAA,YACtE;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,oBAAY,CAAC,QAAQ;AAAA,MACvB;AAEA,UAAI,QAAQ;AACZ,kBAAY,SAAS,WAAW,UAAU,YAAY,CAAC;AAEvD,UAAI,SAAS,QAAQ,YAAY,SAAS,OAAO,KAAKA,aAAY;AAChE,YAAI,WAAW,SAAS,WAAW,SAAS,UAAU;AACpD,iBAAO,SAAS,KAAK;AAAA,QACvB,CAAC;AACD,eAAO,EAAE,YAAY,UAAU,SAAS,EAAE,OAAO,SAAS,MAAM;AAAA,MAClE,CAAC;AAED,aAAO,WAAW,QAAQ,SAAS,QAAQ,OAAO;AAChD,eAAO,gBAAgB,QAAQ,OAAO,MAAM;AAAA,MAC9C,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["collection"]}