{"version": 3, "sources": ["../../lodash/_createBaseEach.js", "../../lodash/_baseEach.js"], "sourcesContent": ["var isArrayLike = require('./isArrayLike');\n\n/**\n * Creates a `baseEach` or `baseEachRight` function.\n *\n * @private\n * @param {Function} eachFunc The function to iterate over a collection.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseEach(eachFunc, fromRight) {\n  return function(collection, iteratee) {\n    if (collection == null) {\n      return collection;\n    }\n    if (!isArrayLike(collection)) {\n      return eachFunc(collection, iteratee);\n    }\n    var length = collection.length,\n        index = fromRight ? length : -1,\n        iterable = Object(collection);\n\n    while ((fromRight ? index-- : ++index < length)) {\n      if (iteratee(iterable[index], index, iterable) === false) {\n        break;\n      }\n    }\n    return collection;\n  };\n}\n\nmodule.exports = createBaseEach;\n", "var baseForOwn = require('./_baseForOwn'),\n    createBaseEach = require('./_createBaseEach');\n\n/**\n * The base implementation of `_.forEach` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEach = createBaseEach(baseForOwn);\n\nmodule.exports = baseEach;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAUlB,aAAS,eAAe,UAAU,WAAW;AAC3C,aAAO,SAAS,YAAY,UAAU;AACpC,YAAI,cAAc,MAAM;AACtB,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,YAAY,UAAU,GAAG;AAC5B,iBAAO,SAAS,YAAY,QAAQ;AAAA,QACtC;AACA,YAAI,SAAS,WAAW,QACpB,QAAQ,YAAY,SAAS,IAC7B,WAAW,OAAO,UAAU;AAEhC,eAAQ,YAAY,UAAU,EAAE,QAAQ,QAAS;AAC/C,cAAI,SAAS,SAAS,KAAK,GAAG,OAAO,QAAQ,MAAM,OAAO;AACxD;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,iBAAiB;AAUrB,QAAI,WAAW,eAAe,UAAU;AAExC,WAAO,UAAU;AAAA;AAAA;", "names": []}