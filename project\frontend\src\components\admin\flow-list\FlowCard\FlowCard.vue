<script setup lang="ts">
import ApexCharts from 'apexcharts';
import map from 'lodash/map';

import { useDynamicFormSettingsBase } from '@core/composables/dynamic-form-settings/use-dynamic-form-settings-base';

import ImageSelector from '@helpers/components/ImageSelector.vue';
import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';

import Popper from '@/components/common/Popper.vue';
import { useAdminApplicationManager } from '@/composables/use-admin-application-manager';
import { useAdminFormManager } from '@/composables/use-admin-form-manager';
import { useDialog } from '@/composables/use-dialog';
import { useRouter } from '@/composables/use-router';
import { ADMIN_ALL_STATUS_PATH_STRING } from '@/helpers/admin-dashboard';
import { type PermissionKey, hasPermissions, isSuperUser } from '@/helpers/permission-utils';

import FlowCreateNameModal from '../FlowCreateNameModal.vue';

const props = defineProps({
  flow: {
    type: Object as () => Types.Form,
    required: true,
  },
});
const { callDialog } = useDialog();
const { router, currentRoute } = useRouter();
const { duplicateForm, renameForm, deleteForm, applyForm, setHighlight } = useAdminFormManager();
const { getStatusStat } = useAdminApplicationManager();
const {
  targetSetting: colorSetting,
  isFetching: isFetchingColorSetting,
  fetchFormSetting: fetchColorSetting,
} = useDynamicFormSettingsBase<string>(
  computed(() => props.flow.slug),
  'schema_config.styling.--app-primary-color',
);

const localLoadingState = reactive({
  stat: false,
  rename: false,
  duplicate: false,
  delete: false,
});
const isAnyLoading = computed(() => Object.values(localLoadingState).some(loading => loading));
const popperActive = ref<boolean>(false);
const duplicateModalActive = ref(false);
const renameModalActive = ref(false);
const flowIconColor = ref('var(--app-primary-color)');

const graphDataSource = ref([]);
const statInfo = reactive<Types.ApplicationStatusStat['info']>({
  diff_previous_range_percentage_status: '-',
  diff_previous_range_total_applications: '-',
  diff_previous_range_total_status: '-',
  percentage_status: '-',
  total_applications: 0,
  total_status: 0,
});
const myStatusCount = reactive<Types.ApplicationStatusCount>({
  'ekyc:fail': 0,
  'ekyc:pass': 0,
  'ekyc:need_review': 0,
  incomplete: 0,
  complete: 0,
  [ADMIN_ALL_STATUS_PATH_STRING]: 0,
});
const submittedColor = computed(() => {
  if (+statInfo.diff_previous_range_percentage_status > 0) {
    return '#19CEB0';
  }
  if (+statInfo.diff_previous_range_percentage_status < 0) {
    return '#ED6898';
  }
  return '#0073EC';
});

async function getStat() {
  localLoadingState.stat = true;
  const stat = await getStatusStat({
    form: props.flow.slug,
  });
  if (stat) {
    graphDataSource.value = map(stat.data, (count, date) => ({ x: date, y: count }));
    Object.assign(statInfo, stat.info);
    Object.assign(myStatusCount, stat.status);
  }
  localLoadingState.stat = false;
}

function goToApplicationDashboard(formSlug: string) {
  router.push({
    name: 'admin.flow-detail.overall',
    params: {
      ...currentRoute.value.params,
      formSlug,
    },
  });
}

function onClickFlowCard(flow: Types.Form) {
  goToApplicationDashboard(flow.slug);
}

function onClickViewSubmission(flow: Types.Form) {
  goToApplicationDashboard(flow.slug);
}
function onClickEditFlow(flow: Types.Form) {
  router.push({
    name: 'admin.flow-detail.create',
    params: { formSlug: flow.slug },
  });
}

function onClickRename(flow: Types.Form) {
  renameModalActive.value = true;
}
async function onConfirmRename({ name = '' }) {
  localLoadingState.rename = true;
  await renameForm(props.flow.slug, name);
  localLoadingState.rename = false;
}

function onClickDuplicate() {
  duplicateModalActive.value = true;
}
async function onConfirmDuplicate({ name = '', slug = '' }) {
  localLoadingState.duplicate = true;
  const newFlow = await duplicateForm(name, slug, props.flow.slug);
  if (newFlow) {
    onClickEditFlow({ slug: newFlow.slug } as Types.Form);
  }
  localLoadingState.duplicate = false;
}
function onClickDelete(flow: Types.Form) {
  async function execute() {
    localLoadingState.delete = true;
    await deleteForm(flow.slug);
    localLoadingState.delete = false;
  }
  callDialog('confirm', {
    class: 'delete-dialog',
    title: 'Do you want to delete this flow?',
    message: 'All submissions in this flow will be deleted and cannot be undone.',
    hasIcon: true,
    icon: 'feather-trash',
    confirmText: 'Delete',
    onConfirm: execute,
  });
}
async function onClickApply(flow: Types.Form) {
  await applyForm(flow.slug);
}

function toggleHighlight(flow: Types.Form) {
  setHighlight(flow.slug, !flow.highlight);
}

// Card context menu
const cardContextMenu: {
  label?: string;
  icon?: string;
  onClick?: (flow: Types.Form) => void;
  permissions?: PermissionKey[];
  divider?: true;
}[] = [
  {
    label: 'View Submission',
    icon: 'feather:file-text',
    onClick: onClickViewSubmission,
  },
  {
    label: 'Edit Flow',
    icon: 'feather:tool',
    onClick: onClickEditFlow,
    permissions: ['create_edit_duplicate_flow'],
  },
  {
    label: 'Rename',
    icon: 'feather:edit-3',
    onClick: onClickRename,
    permissions: ['create_edit_duplicate_flow'],
  },
  {
    label: 'Duplicate',
    icon: 'feather:copy',
    onClick: onClickDuplicate,
    permissions: ['create_edit_duplicate_flow'],
  },
  {
    label: 'New application',
    icon: 'feather:file-plus',
    onClick: onClickApply,
    permissions: ['add_new_application'],
  },
  {
    label: 'Delete',
    icon: 'feather:trash',
    onClick: onClickDelete,
    permissions: ['delete_flow'],
  },
  {
    divider: true,
    permissions: ['superuser'],
  },
  {
    label: 'Builder/Schema',
    icon: 'mdi-drawing',
    permissions: ['superuser'],
    onClick: (flow: Types.Form) =>
      router.push({ name: 'admin.builder', params: { formSlug: flow.slug } }),
  },
];

const permittedCardContextMenu = computed(() =>
  cardContextMenu.filter(item => hasPermissions(item.permissions)),
);

// Chart
const chartColor = computed(() => {
  if (+statInfo.diff_previous_range_percentage_status > 0) {
    return '#19CEB0';
  }
  if (+statInfo.diff_previous_range_percentage_status < 0) {
    return '#ED6898';
  }
  return '#0073EC';
});

const chartOptions = computed(() => ({
  series: [
    {
      data: graphDataSource.value,
    },
  ],
  chart: {
    type: 'line',
    height: 100,
    width: '100%',
    sparkline: {
      enabled: true,
    },
    animations: {
      enabled: false,
    },
  },
  tooltip: {
    fixed: {
      enabled: false,
    },
    x: {
      show: true,
    },
    y: {
      title: {
        formatter(seriesName) {
          return '';
        },
      },
    },
    marker: {
      show: false,
    },
  },
  stroke: {
    width: 2,
    curve: 'smooth',
  },
  colors: [chartColor.value],
}));

const chartRef = ref<HTMLDivElement>();
const apexChartInstance = ref<ApexCharts>();

onMounted(async () => {
  await getStat();
  apexChartInstance.value = new ApexCharts(chartRef.value, chartOptions.value);
  apexChartInstance.value.render();

  watch(
    chartOptions,
    () => {
      apexChartInstance.value.updateOptions(chartOptions.value);
    },
    { deep: true },
  );

  await fetchColorSetting();
  if (colorSetting.value) {
    flowIconColor.value = colorSetting.value;
  }
});
</script>

<template>
  <div class="card flow-card" :class="{ 'card-inactive': !flow.is_active }">
    <div class="card-content" @click="onClickFlowCard(flow)">
      <div v-if="statInfo.total_applications === 0" class="no-activity__wrapper">
        <div class="no-activity">
          <div class="no-activity-placeholder">
            <ImageSelector name="no-activity" />
            <span class="no-activity-text">No Activity Found</span>
          </div>
        </div>
      </div>
      <template v-else>
        <b-loading v-model="isAnyLoading" :is-full-page="false" />
        <div>
          <div class="columns">
            <div class="column">
              <div v-if="myStatusCount['ekyc:need_review']" class="flow-status-tag">
                <span>
                  <ReactiveIcon icon="feather:search" />
                </span>
                <span>
                  {{ myStatusCount['ekyc:need_review'] }}
                </span>
              </div>
            </div>

            <div class="column has-text-right">
              <span class="application-stat-text" :style="{ color: submittedColor }">
                {{ statInfo.diff_previous_range_percentage_status }}%
              </span>
            </div>
          </div>
        </div>
        <div class="flow-chart">
          <div ref="chartRef" />
        </div>
      </template>
      <div class="has-text-right highlight-button-wrapper favorite-button-wrapper">
        <button
          class="button is-ghost highlight-button favorite-button"
          :class="flow.highlight ? 'has-text-primary' : ''"
          @click.stop="toggleHighlight(flow)"
        >
          <ReactiveIcon :icon="flow.highlight ? 'uis:star' : 'uil:star'" />
        </button>
      </div>
    </div>
    <footer class="level card-footer" @click="onClickFlowCard(flow)">
      <div class="level-left card-footer-item card-action-main">
        <div class="level-item">
          <span class="flow-icon is-large" :style="{ background: flowIconColor }">
            <span>
              {{ flow.slug.charAt(0).toLowerCase() }}
            </span>
          </span>
        </div>
      </div>
      <div class="level-item card-footer-item card-action-main flow-name">
        <div class="flow-detail-wrapper">
          <span class="flow-title">{{ flow.name }}</span>
          <span v-if="isSuperUser()" class="flow-subtitle">#{{ flow.slug }}</span>
          <span v-else class="flow-subtitle" :class="[flow.is_active ? 'active' : 'inactive']">
            <div class="indicator" />
            {{ flow.is_active ? 'Active' : 'Inactive' }}
          </span>
        </div>
      </div>
      <div class="level-right card-footer-item card-action-more">
        <div class="level-item">
          <Popper :popper-active.sync="popperActive">
            <template #reference="{ toggleContent }">
              <b-button
                type="is-text"
                icon-right="mdi:dots-horizontal"
                @click.stop="toggleContent"
              />
            </template>

            <template #content>
              <div class="dropdown is-active">
                <div class="dropdown-menu" style="display: block !important; position: static">
                  <div class="dropdown-content">
                    <a
                      v-for="(item, i) in permittedCardContextMenu"
                      :key="`card-context-${i}`"
                      class="dropdown-item"
                      @click.stop="item.onClick && item.onClick(flow)"
                    >
                      <template v-if="item.divider">
                        <span>_______________</span>
                      </template>
                      <template v-else>
                        <div class="dropdown-menu-icon">
                          <ReactiveIcon :icon="item.icon" />
                        </div>
                        <span>{{ item.label }}</span>
                      </template>
                    </a>
                  </div>
                </div>
              </div>
            </template>
          </Popper>
        </div>
      </div>
    </footer>
    <flow-create-name-modal
      :flow-name="flow.name"
      :active.sync="renameModalActive"
      no-slug
      @confirm="onConfirmRename"
    />
    <flow-create-name-modal
      :flow-name="flow.name"
      :active.sync="duplicateModalActive"
      @confirm="onConfirmDuplicate"
    />
  </div>
</template>

<style scoped lang="scss">
.active {
  color: #19ceb0 !important;
  .indicator {
    background: #19ceb0;
  }
}
.inactive {
  color: #ed6898 !important;
  .indicator {
    background: #ed6898;
  }
}
.indicator {
  width: 8px;
  height: 8px;
  flex: none;
  order: 0;
  flex-grow: 0;
  margin-right: 8px;
  border-radius: 50px;
}
.flow-item-list
  .flow-card
  .card-footer
  .card-footer-item.flow-name
  .flow-detail-wrapper
  .flow-subtitle {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flow-item-list
  .flow-card
  .card-footer
  .card-footer-item.flow-name
  .flow-detail-wrapper
  .flow-title {
  font-weight: 700;
  font-size: 14px;
  line-height: 24px;
}

.card.flow-card.card-inactive {
  .no-activity {
    opacity: 0.4;
  }
  .flow-chart {
    opacity: 0.4;
  }
  .flow-icon {
    opacity: 0.4;
  }

  .flow-status-tag {
    opacity: 0.4;
  }

  .application-stat-text {
    opacity: 0.4;
  }
}
</style>
