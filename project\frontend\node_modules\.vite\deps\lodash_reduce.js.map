{"version": 3, "sources": ["../../lodash/_baseReduce.js", "../../lodash/reduce.js"], "sourcesContent": ["/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function(value, index, collection) {\n    accumulator = initAccum\n      ? (initAccum = false, value)\n      : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nmodule.exports = baseReduce;\n", "var arrayReduce = require('./_arrayReduce'),\n    baseEach = require('./_baseEach'),\n    baseIteratee = require('./_baseIteratee'),\n    baseReduce = require('./_baseReduce'),\n    isArray = require('./isArray');\n\n/**\n * Reduces `collection` to a value which is the accumulated result of running\n * each element in `collection` thru `iteratee`, where each successive\n * invocation is supplied the return value of the previous. If `accumulator`\n * is not given, the first element of `collection` is used as the initial\n * value. The iteratee is invoked with four arguments:\n * (accumulator, value, index|key, collection).\n *\n * Many lodash methods are guarded to work as iteratees for methods like\n * `_.reduce`, `_.reduceRight`, and `_.transform`.\n *\n * The guarded methods are:\n * `assign`, `defaults`, `defaultsDeep`, `includes`, `merge`, `orderBy`,\n * and `sortBy`\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @returns {*} Returns the accumulated value.\n * @see _.reduceRight\n * @example\n *\n * _.reduce([1, 2], function(sum, n) {\n *   return sum + n;\n * }, 0);\n * // => 3\n *\n * _.reduce({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n *   return result;\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] } (iteration order is not guaranteed)\n */\nfunction reduce(collection, iteratee, accumulator) {\n  var func = isArray(collection) ? arrayReduce : baseReduce,\n      initAccum = arguments.length < 3;\n\n  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEach);\n}\n\nmodule.exports = reduce;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAaA,aAAS,WAAW,YAAY,UAAU,aAAa,WAAW,UAAU;AAC1E,eAAS,YAAY,SAAS,OAAO,OAAOA,aAAY;AACtD,sBAAc,aACT,YAAY,OAAO,SACpB,SAAS,aAAa,OAAO,OAAOA,WAAU;AAAA,MACpD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,eAAe;AAFnB,QAGI,aAAa;AAHjB,QAII,UAAU;AAuCd,aAAS,OAAO,YAAY,UAAU,aAAa;AACjD,UAAI,OAAO,QAAQ,UAAU,IAAI,cAAc,YAC3C,YAAY,UAAU,SAAS;AAEnC,aAAO,KAAK,YAAY,aAAa,UAAU,CAAC,GAAG,aAAa,WAAW,QAAQ;AAAA,IACrF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["collection"]}