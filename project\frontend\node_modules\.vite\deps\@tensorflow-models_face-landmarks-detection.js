import {
  loadGraphModel
} from "./chunk-HFYGB3K7.js";
import {
  Tensor,
  add,
  browser_exports,
  cast,
  clipByValue,
  concat,
  dispose,
  div,
  exp,
  expandDims,
  image,
  mul,
  reshape,
  sigmoid,
  slice,
  squeeze,
  sub,
  tensor1d,
  tensor2d,
  tidy,
  util_exports
} from "./chunk-VFSHU3A2.js";
import {
  require_face_mesh
} from "./chunk-IKRRRNPQ.js";
import "./chunk-EXAI6KDO.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@tensorflow-models/face-landmarks-detection/dist/face-landmarks-detection.esm.js
var t = __toESM(require_face_mesh());
var E = function() {
  return E = Object.assign || function(t2) {
    for (var e, n = 1, r = arguments.length; n < r; n++) for (var i in e = arguments[n]) Object.prototype.hasOwnProperty.call(e, i) && (t2[i] = e[i]);
    return t2;
  }, E.apply(this, arguments);
};
function S(t2, e, n, r) {
  return new (n || (n = Promise))(function(i, o) {
    function a(t3) {
      try {
        s(r.next(t3));
      } catch (t4) {
        o(t4);
      }
    }
    function u(t3) {
      try {
        s(r.throw(t3));
      } catch (t4) {
        o(t4);
      }
    }
    function s(t3) {
      var e2;
      t3.done ? i(t3.value) : (e2 = t3.value, e2 instanceof n ? e2 : new n(function(t4) {
        t4(e2);
      })).then(a, u);
    }
    s((r = r.apply(t2, e || [])).next());
  });
}
function F(t2, e) {
  var n, r, i, o, a = { label: 0, sent: function() {
    if (1 & i[0]) throw i[1];
    return i[1];
  }, trys: [], ops: [] };
  return o = { next: u(0), throw: u(1), return: u(2) }, "function" == typeof Symbol && (o[Symbol.iterator] = function() {
    return this;
  }), o;
  function u(o2) {
    return function(u2) {
      return function(o3) {
        if (n) throw new TypeError("Generator is already executing.");
        for (; a; ) try {
          if (n = 1, r && (i = 2 & o3[0] ? r.return : o3[0] ? r.throw || ((i = r.return) && i.call(r), 0) : r.next) && !(i = i.call(r, o3[1])).done) return i;
          switch (r = 0, i && (o3 = [2 & o3[0], i.value]), o3[0]) {
            case 0:
            case 1:
              i = o3;
              break;
            case 4:
              return a.label++, { value: o3[1], done: false };
            case 5:
              a.label++, r = o3[1], o3 = [0];
              continue;
            case 7:
              o3 = a.ops.pop(), a.trys.pop();
              continue;
            default:
              if (!(i = a.trys, (i = i.length > 0 && i[i.length - 1]) || 6 !== o3[0] && 2 !== o3[0])) {
                a = 0;
                continue;
              }
              if (3 === o3[0] && (!i || o3[1] > i[0] && o3[1] < i[3])) {
                a.label = o3[1];
                break;
              }
              if (6 === o3[0] && a.label < i[1]) {
                a.label = i[1], i = o3;
                break;
              }
              if (i && a.label < i[2]) {
                a.label = i[2], a.ops.push(o3);
                break;
              }
              i[2] && a.ops.pop(), a.trys.pop();
              continue;
          }
          o3 = e.call(t2, a);
        } catch (t3) {
          o3 = [6, t3], r = 0;
        } finally {
          n = i = 0;
        }
        if (5 & o3[0]) throw o3[1];
        return { value: o3[0] ? o3[1] : void 0, done: true };
      }([o2, u2]);
    };
  }
}
function C(t2) {
  var e = t2.map(function(t3) {
    return t3[0];
  });
  return e.push(t2[t2.length - 1][1]), e;
}
var O = { lips: C([[61, 146], [146, 91], [91, 181], [181, 84], [84, 17], [17, 314], [314, 405], [405, 321], [321, 375], [375, 291], [61, 185], [185, 40], [40, 39], [39, 37], [37, 0], [0, 267], [267, 269], [269, 270], [270, 409], [409, 291], [78, 95], [95, 88], [88, 178], [178, 87], [87, 14], [14, 317], [317, 402], [402, 318], [318, 324], [324, 308], [78, 191], [191, 80], [80, 81], [81, 82], [82, 13], [13, 312], [312, 311], [311, 310], [310, 415], [415, 308]]), leftEye: C([[263, 249], [249, 390], [390, 373], [373, 374], [374, 380], [380, 381], [381, 382], [382, 362], [263, 466], [466, 388], [388, 387], [387, 386], [386, 385], [385, 384], [384, 398], [398, 362]]), leftEyebrow: C([[276, 283], [283, 282], [282, 295], [295, 285], [300, 293], [293, 334], [334, 296], [296, 336]]), leftIris: C([[474, 475], [475, 476], [476, 477], [477, 474]]), rightEye: C([[33, 7], [7, 163], [163, 144], [144, 145], [145, 153], [153, 154], [154, 155], [155, 133], [33, 246], [246, 161], [161, 160], [160, 159], [159, 158], [158, 157], [157, 173], [173, 133]]), rightEyebrow: C([[46, 53], [53, 52], [52, 65], [65, 55], [70, 63], [63, 105], [105, 66], [66, 107]]), rightIris: C([[469, 470], [470, 471], [471, 472], [472, 469]]), faceOval: C([[10, 338], [338, 297], [297, 332], [332, 284], [284, 251], [251, 389], [389, 356], [356, 454], [454, 323], [323, 361], [361, 288], [288, 397], [397, 365], [365, 379], [379, 378], [378, 400], [400, 377], [377, 152], [152, 148], [148, 176], [176, 149], [149, 150], [150, 136], [136, 172], [172, 58], [58, 132], [132, 93], [93, 234], [234, 127], [127, 162], [162, 21], [21, 54], [54, 103], [103, 67], [67, 109], [109, 10]]) };
var _ = [[127, 34], [34, 139], [139, 127], [11, 0], [0, 37], [37, 11], [232, 231], [231, 120], [120, 232], [72, 37], [37, 39], [39, 72], [128, 121], [121, 47], [47, 128], [232, 121], [121, 128], [128, 232], [104, 69], [69, 67], [67, 104], [175, 171], [171, 148], [148, 175], [118, 50], [50, 101], [101, 118], [73, 39], [39, 40], [40, 73], [9, 151], [151, 108], [108, 9], [48, 115], [115, 131], [131, 48], [194, 204], [204, 211], [211, 194], [74, 40], [40, 185], [185, 74], [80, 42], [42, 183], [183, 80], [40, 92], [92, 186], [186, 40], [230, 229], [229, 118], [118, 230], [202, 212], [212, 214], [214, 202], [83, 18], [18, 17], [17, 83], [76, 61], [61, 146], [146, 76], [160, 29], [29, 30], [30, 160], [56, 157], [157, 173], [173, 56], [106, 204], [204, 194], [194, 106], [135, 214], [214, 192], [192, 135], [203, 165], [165, 98], [98, 203], [21, 71], [71, 68], [68, 21], [51, 45], [45, 4], [4, 51], [144, 24], [24, 23], [23, 144], [77, 146], [146, 91], [91, 77], [205, 50], [50, 187], [187, 205], [201, 200], [200, 18], [18, 201], [91, 106], [106, 182], [182, 91], [90, 91], [91, 181], [181, 90], [85, 84], [84, 17], [17, 85], [206, 203], [203, 36], [36, 206], [148, 171], [171, 140], [140, 148], [92, 40], [40, 39], [39, 92], [193, 189], [189, 244], [244, 193], [159, 158], [158, 28], [28, 159], [247, 246], [246, 161], [161, 247], [236, 3], [3, 196], [196, 236], [54, 68], [68, 104], [104, 54], [193, 168], [168, 8], [8, 193], [117, 228], [228, 31], [31, 117], [189, 193], [193, 55], [55, 189], [98, 97], [97, 99], [99, 98], [126, 47], [47, 100], [100, 126], [166, 79], [79, 218], [218, 166], [155, 154], [154, 26], [26, 155], [209, 49], [49, 131], [131, 209], [135, 136], [136, 150], [150, 135], [47, 126], [126, 217], [217, 47], [223, 52], [52, 53], [53, 223], [45, 51], [51, 134], [134, 45], [211, 170], [170, 140], [140, 211], [67, 69], [69, 108], [108, 67], [43, 106], [106, 91], [91, 43], [230, 119], [119, 120], [120, 230], [226, 130], [130, 247], [247, 226], [63, 53], [53, 52], [52, 63], [238, 20], [20, 242], [242, 238], [46, 70], [70, 156], [156, 46], [78, 62], [62, 96], [96, 78], [46, 53], [53, 63], [63, 46], [143, 34], [34, 227], [227, 143], [123, 117], [117, 111], [111, 123], [44, 125], [125, 19], [19, 44], [236, 134], [134, 51], [51, 236], [216, 206], [206, 205], [205, 216], [154, 153], [153, 22], [22, 154], [39, 37], [37, 167], [167, 39], [200, 201], [201, 208], [208, 200], [36, 142], [142, 100], [100, 36], [57, 212], [212, 202], [202, 57], [20, 60], [60, 99], [99, 20], [28, 158], [158, 157], [157, 28], [35, 226], [226, 113], [113, 35], [160, 159], [159, 27], [27, 160], [204, 202], [202, 210], [210, 204], [113, 225], [225, 46], [46, 113], [43, 202], [202, 204], [204, 43], [62, 76], [76, 77], [77, 62], [137, 123], [123, 116], [116, 137], [41, 38], [38, 72], [72, 41], [203, 129], [129, 142], [142, 203], [64, 98], [98, 240], [240, 64], [49, 102], [102, 64], [64, 49], [41, 73], [73, 74], [74, 41], [212, 216], [216, 207], [207, 212], [42, 74], [74, 184], [184, 42], [169, 170], [170, 211], [211, 169], [170, 149], [149, 176], [176, 170], [105, 66], [66, 69], [69, 105], [122, 6], [6, 168], [168, 122], [123, 147], [147, 187], [187, 123], [96, 77], [77, 90], [90, 96], [65, 55], [55, 107], [107, 65], [89, 90], [90, 180], [180, 89], [101, 100], [100, 120], [120, 101], [63, 105], [105, 104], [104, 63], [93, 137], [137, 227], [227, 93], [15, 86], [86, 85], [85, 15], [129, 102], [102, 49], [49, 129], [14, 87], [87, 86], [86, 14], [55, 8], [8, 9], [9, 55], [100, 47], [47, 121], [121, 100], [145, 23], [23, 22], [22, 145], [88, 89], [89, 179], [179, 88], [6, 122], [122, 196], [196, 6], [88, 95], [95, 96], [96, 88], [138, 172], [172, 136], [136, 138], [215, 58], [58, 172], [172, 215], [115, 48], [48, 219], [219, 115], [42, 80], [80, 81], [81, 42], [195, 3], [3, 51], [51, 195], [43, 146], [146, 61], [61, 43], [171, 175], [175, 199], [199, 171], [81, 82], [82, 38], [38, 81], [53, 46], [46, 225], [225, 53], [144, 163], [163, 110], [110, 144], [52, 65], [65, 66], [66, 52], [229, 228], [228, 117], [117, 229], [34, 127], [127, 234], [234, 34], [107, 108], [108, 69], [69, 107], [109, 108], [108, 151], [151, 109], [48, 64], [64, 235], [235, 48], [62, 78], [78, 191], [191, 62], [129, 209], [209, 126], [126, 129], [111, 35], [35, 143], [143, 111], [117, 123], [123, 50], [50, 117], [222, 65], [65, 52], [52, 222], [19, 125], [125, 141], [141, 19], [221, 55], [55, 65], [65, 221], [3, 195], [195, 197], [197, 3], [25, 7], [7, 33], [33, 25], [220, 237], [237, 44], [44, 220], [70, 71], [71, 139], [139, 70], [122, 193], [193, 245], [245, 122], [247, 130], [130, 33], [33, 247], [71, 21], [21, 162], [162, 71], [170, 169], [169, 150], [150, 170], [188, 174], [174, 196], [196, 188], [216, 186], [186, 92], [92, 216], [2, 97], [97, 167], [167, 2], [141, 125], [125, 241], [241, 141], [164, 167], [167, 37], [37, 164], [72, 38], [38, 12], [12, 72], [38, 82], [82, 13], [13, 38], [63, 68], [68, 71], [71, 63], [226, 35], [35, 111], [111, 226], [101, 50], [50, 205], [205, 101], [206, 92], [92, 165], [165, 206], [209, 198], [198, 217], [217, 209], [165, 167], [167, 97], [97, 165], [220, 115], [115, 218], [218, 220], [133, 112], [112, 243], [243, 133], [239, 238], [238, 241], [241, 239], [214, 135], [135, 169], [169, 214], [190, 173], [173, 133], [133, 190], [171, 208], [208, 32], [32, 171], [125, 44], [44, 237], [237, 125], [86, 87], [87, 178], [178, 86], [85, 86], [86, 179], [179, 85], [84, 85], [85, 180], [180, 84], [83, 84], [84, 181], [181, 83], [201, 83], [83, 182], [182, 201], [137, 93], [93, 132], [132, 137], [76, 62], [62, 183], [183, 76], [61, 76], [76, 184], [184, 61], [57, 61], [61, 185], [185, 57], [212, 57], [57, 186], [186, 212], [214, 207], [207, 187], [187, 214], [34, 143], [143, 156], [156, 34], [79, 239], [239, 237], [237, 79], [123, 137], [137, 177], [177, 123], [44, 1], [1, 4], [4, 44], [201, 194], [194, 32], [32, 201], [64, 102], [102, 129], [129, 64], [213, 215], [215, 138], [138, 213], [59, 166], [166, 219], [219, 59], [242, 99], [99, 97], [97, 242], [2, 94], [94, 141], [141, 2], [75, 59], [59, 235], [235, 75], [24, 110], [110, 228], [228, 24], [25, 130], [130, 226], [226, 25], [23, 24], [24, 229], [229, 23], [22, 23], [23, 230], [230, 22], [26, 22], [22, 231], [231, 26], [112, 26], [26, 232], [232, 112], [189, 190], [190, 243], [243, 189], [221, 56], [56, 190], [190, 221], [28, 56], [56, 221], [221, 28], [27, 28], [28, 222], [222, 27], [29, 27], [27, 223], [223, 29], [30, 29], [29, 224], [224, 30], [247, 30], [30, 225], [225, 247], [238, 79], [79, 20], [20, 238], [166, 59], [59, 75], [75, 166], [60, 75], [75, 240], [240, 60], [147, 177], [177, 215], [215, 147], [20, 79], [79, 166], [166, 20], [187, 147], [147, 213], [213, 187], [112, 233], [233, 244], [244, 112], [233, 128], [128, 245], [245, 233], [128, 114], [114, 188], [188, 128], [114, 217], [217, 174], [174, 114], [131, 115], [115, 220], [220, 131], [217, 198], [198, 236], [236, 217], [198, 131], [131, 134], [134, 198], [177, 132], [132, 58], [58, 177], [143, 35], [35, 124], [124, 143], [110, 163], [163, 7], [7, 110], [228, 110], [110, 25], [25, 228], [356, 389], [389, 368], [368, 356], [11, 302], [302, 267], [267, 11], [452, 350], [350, 349], [349, 452], [302, 303], [303, 269], [269, 302], [357, 343], [343, 277], [277, 357], [452, 453], [453, 357], [357, 452], [333, 332], [332, 297], [297, 333], [175, 152], [152, 377], [377, 175], [347, 348], [348, 330], [330, 347], [303, 304], [304, 270], [270, 303], [9, 336], [336, 337], [337, 9], [278, 279], [279, 360], [360, 278], [418, 262], [262, 431], [431, 418], [304, 408], [408, 409], [409, 304], [310, 415], [415, 407], [407, 310], [270, 409], [409, 410], [410, 270], [450, 348], [348, 347], [347, 450], [422, 430], [430, 434], [434, 422], [313, 314], [314, 17], [17, 313], [306, 307], [307, 375], [375, 306], [387, 388], [388, 260], [260, 387], [286, 414], [414, 398], [398, 286], [335, 406], [406, 418], [418, 335], [364, 367], [367, 416], [416, 364], [423, 358], [358, 327], [327, 423], [251, 284], [284, 298], [298, 251], [281, 5], [5, 4], [4, 281], [373, 374], [374, 253], [253, 373], [307, 320], [320, 321], [321, 307], [425, 427], [427, 411], [411, 425], [421, 313], [313, 18], [18, 421], [321, 405], [405, 406], [406, 321], [320, 404], [404, 405], [405, 320], [315, 16], [16, 17], [17, 315], [426, 425], [425, 266], [266, 426], [377, 400], [400, 369], [369, 377], [322, 391], [391, 269], [269, 322], [417, 465], [465, 464], [464, 417], [386, 257], [257, 258], [258, 386], [466, 260], [260, 388], [388, 466], [456, 399], [399, 419], [419, 456], [284, 332], [332, 333], [333, 284], [417, 285], [285, 8], [8, 417], [346, 340], [340, 261], [261, 346], [413, 441], [441, 285], [285, 413], [327, 460], [460, 328], [328, 327], [355, 371], [371, 329], [329, 355], [392, 439], [439, 438], [438, 392], [382, 341], [341, 256], [256, 382], [429, 420], [420, 360], [360, 429], [364, 394], [394, 379], [379, 364], [277, 343], [343, 437], [437, 277], [443, 444], [444, 283], [283, 443], [275, 440], [440, 363], [363, 275], [431, 262], [262, 369], [369, 431], [297, 338], [338, 337], [337, 297], [273, 375], [375, 321], [321, 273], [450, 451], [451, 349], [349, 450], [446, 342], [342, 467], [467, 446], [293, 334], [334, 282], [282, 293], [458, 461], [461, 462], [462, 458], [276, 353], [353, 383], [383, 276], [308, 324], [324, 325], [325, 308], [276, 300], [300, 293], [293, 276], [372, 345], [345, 447], [447, 372], [352, 345], [345, 340], [340, 352], [274, 1], [1, 19], [19, 274], [456, 248], [248, 281], [281, 456], [436, 427], [427, 425], [425, 436], [381, 256], [256, 252], [252, 381], [269, 391], [391, 393], [393, 269], [200, 199], [199, 428], [428, 200], [266, 330], [330, 329], [329, 266], [287, 273], [273, 422], [422, 287], [250, 462], [462, 328], [328, 250], [258, 286], [286, 384], [384, 258], [265, 353], [353, 342], [342, 265], [387, 259], [259, 257], [257, 387], [424, 431], [431, 430], [430, 424], [342, 353], [353, 276], [276, 342], [273, 335], [335, 424], [424, 273], [292, 325], [325, 307], [307, 292], [366, 447], [447, 345], [345, 366], [271, 303], [303, 302], [302, 271], [423, 266], [266, 371], [371, 423], [294, 455], [455, 460], [460, 294], [279, 278], [278, 294], [294, 279], [271, 272], [272, 304], [304, 271], [432, 434], [434, 427], [427, 432], [272, 407], [407, 408], [408, 272], [394, 430], [430, 431], [431, 394], [395, 369], [369, 400], [400, 395], [334, 333], [333, 299], [299, 334], [351, 417], [417, 168], [168, 351], [352, 280], [280, 411], [411, 352], [325, 319], [319, 320], [320, 325], [295, 296], [296, 336], [336, 295], [319, 403], [403, 404], [404, 319], [330, 348], [348, 349], [349, 330], [293, 298], [298, 333], [333, 293], [323, 454], [454, 447], [447, 323], [15, 16], [16, 315], [315, 15], [358, 429], [429, 279], [279, 358], [14, 15], [15, 316], [316, 14], [285, 336], [336, 9], [9, 285], [329, 349], [349, 350], [350, 329], [374, 380], [380, 252], [252, 374], [318, 402], [402, 403], [403, 318], [6, 197], [197, 419], [419, 6], [318, 319], [319, 325], [325, 318], [367, 364], [364, 365], [365, 367], [435, 367], [367, 397], [397, 435], [344, 438], [438, 439], [439, 344], [272, 271], [271, 311], [311, 272], [195, 5], [5, 281], [281, 195], [273, 287], [287, 291], [291, 273], [396, 428], [428, 199], [199, 396], [311, 271], [271, 268], [268, 311], [283, 444], [444, 445], [445, 283], [373, 254], [254, 339], [339, 373], [282, 334], [334, 296], [296, 282], [449, 347], [347, 346], [346, 449], [264, 447], [447, 454], [454, 264], [336, 296], [296, 299], [299, 336], [338, 10], [10, 151], [151, 338], [278, 439], [439, 455], [455, 278], [292, 407], [407, 415], [415, 292], [358, 371], [371, 355], [355, 358], [340, 345], [345, 372], [372, 340], [346, 347], [347, 280], [280, 346], [442, 443], [443, 282], [282, 442], [19, 94], [94, 370], [370, 19], [441, 442], [442, 295], [295, 441], [248, 419], [419, 197], [197, 248], [263, 255], [255, 359], [359, 263], [440, 275], [275, 274], [274, 440], [300, 383], [383, 368], [368, 300], [351, 412], [412, 465], [465, 351], [263, 467], [467, 466], [466, 263], [301, 368], [368, 389], [389, 301], [395, 378], [378, 379], [379, 395], [412, 351], [351, 419], [419, 412], [436, 426], [426, 322], [322, 436], [2, 164], [164, 393], [393, 2], [370, 462], [462, 461], [461, 370], [164, 0], [0, 267], [267, 164], [302, 11], [11, 12], [12, 302], [268, 12], [12, 13], [13, 268], [293, 300], [300, 301], [301, 293], [446, 261], [261, 340], [340, 446], [330, 266], [266, 425], [425, 330], [426, 423], [423, 391], [391, 426], [429, 355], [355, 437], [437, 429], [391, 327], [327, 326], [326, 391], [440, 457], [457, 438], [438, 440], [341, 382], [382, 362], [362, 341], [459, 457], [457, 461], [461, 459], [434, 430], [430, 394], [394, 434], [414, 463], [463, 362], [362, 414], [396, 369], [369, 262], [262, 396], [354, 461], [461, 457], [457, 354], [316, 403], [403, 402], [402, 316], [315, 404], [404, 403], [403, 315], [314, 405], [405, 404], [404, 314], [313, 406], [406, 405], [405, 313], [421, 418], [418, 406], [406, 421], [366, 401], [401, 361], [361, 366], [306, 408], [408, 407], [407, 306], [291, 409], [409, 408], [408, 291], [287, 410], [410, 409], [409, 287], [432, 436], [436, 410], [410, 432], [434, 416], [416, 411], [411, 434], [264, 368], [368, 383], [383, 264], [309, 438], [438, 457], [457, 309], [352, 376], [376, 401], [401, 352], [274, 275], [275, 4], [4, 274], [421, 428], [428, 262], [262, 421], [294, 327], [327, 358], [358, 294], [433, 416], [416, 367], [367, 433], [289, 455], [455, 439], [439, 289], [462, 370], [370, 326], [326, 462], [2, 326], [326, 370], [370, 2], [305, 460], [460, 455], [455, 305], [254, 449], [449, 448], [448, 254], [255, 261], [261, 446], [446, 255], [253, 450], [450, 449], [449, 253], [252, 451], [451, 450], [450, 252], [256, 452], [452, 451], [451, 256], [341, 453], [453, 452], [452, 341], [413, 464], [464, 463], [463, 413], [441, 413], [413, 414], [414, 441], [258, 442], [442, 441], [441, 258], [257, 443], [443, 442], [442, 257], [259, 444], [444, 443], [443, 259], [260, 445], [445, 444], [444, 260], [467, 342], [342, 445], [445, 467], [459, 458], [458, 250], [250, 459], [289, 392], [392, 290], [290, 289], [290, 328], [328, 460], [460, 290], [376, 433], [433, 435], [435, 376], [250, 290], [290, 392], [392, 250], [411, 416], [416, 433], [433, 411], [341, 463], [463, 464], [464, 341], [453, 464], [464, 465], [465, 453], [357, 465], [465, 412], [412, 357], [343, 412], [412, 399], [399, 343], [360, 363], [363, 440], [440, 360], [437, 399], [399, 456], [456, 437], [420, 456], [456, 363], [363, 420], [401, 435], [435, 288], [288, 401], [372, 383], [383, 353], [353, 372], [339, 255], [255, 249], [249, 339], [448, 261], [261, 255], [255, 448], [133, 243], [243, 190], [190, 133], [133, 155], [155, 112], [112, 133], [33, 246], [246, 247], [247, 33], [33, 130], [130, 25], [25, 33], [398, 384], [384, 286], [286, 398], [362, 398], [398, 414], [414, 362], [362, 463], [463, 341], [341, 362], [263, 359], [359, 467], [467, 263], [263, 249], [249, 255], [255, 263], [466, 467], [467, 260], [260, 466], [75, 60], [60, 166], [166, 75], [238, 239], [239, 79], [79, 238], [162, 127], [127, 139], [139, 162], [72, 11], [11, 37], [37, 72], [121, 232], [232, 120], [120, 121], [73, 72], [72, 39], [39, 73], [114, 128], [128, 47], [47, 114], [233, 232], [232, 128], [128, 233], [103, 104], [104, 67], [67, 103], [152, 175], [175, 148], [148, 152], [119, 118], [118, 101], [101, 119], [74, 73], [73, 40], [40, 74], [107, 9], [9, 108], [108, 107], [49, 48], [48, 131], [131, 49], [32, 194], [194, 211], [211, 32], [184, 74], [74, 185], [185, 184], [191, 80], [80, 183], [183, 191], [185, 40], [40, 186], [186, 185], [119, 230], [230, 118], [118, 119], [210, 202], [202, 214], [214, 210], [84, 83], [83, 17], [17, 84], [77, 76], [76, 146], [146, 77], [161, 160], [160, 30], [30, 161], [190, 56], [56, 173], [173, 190], [182, 106], [106, 194], [194, 182], [138, 135], [135, 192], [192, 138], [129, 203], [203, 98], [98, 129], [54, 21], [21, 68], [68, 54], [5, 51], [51, 4], [4, 5], [145, 144], [144, 23], [23, 145], [90, 77], [77, 91], [91, 90], [207, 205], [205, 187], [187, 207], [83, 201], [201, 18], [18, 83], [181, 91], [91, 182], [182, 181], [180, 90], [90, 181], [181, 180], [16, 85], [85, 17], [17, 16], [205, 206], [206, 36], [36, 205], [176, 148], [148, 140], [140, 176], [165, 92], [92, 39], [39, 165], [245, 193], [193, 244], [244, 245], [27, 159], [159, 28], [28, 27], [30, 247], [247, 161], [161, 30], [174, 236], [236, 196], [196, 174], [103, 54], [54, 104], [104, 103], [55, 193], [193, 8], [8, 55], [111, 117], [117, 31], [31, 111], [221, 189], [189, 55], [55, 221], [240, 98], [98, 99], [99, 240], [142, 126], [126, 100], [100, 142], [219, 166], [166, 218], [218, 219], [112, 155], [155, 26], [26, 112], [198, 209], [209, 131], [131, 198], [169, 135], [135, 150], [150, 169], [114, 47], [47, 217], [217, 114], [224, 223], [223, 53], [53, 224], [220, 45], [45, 134], [134, 220], [32, 211], [211, 140], [140, 32], [109, 67], [67, 108], [108, 109], [146, 43], [43, 91], [91, 146], [231, 230], [230, 120], [120, 231], [113, 226], [226, 247], [247, 113], [105, 63], [63, 52], [52, 105], [241, 238], [238, 242], [242, 241], [124, 46], [46, 156], [156, 124], [95, 78], [78, 96], [96, 95], [70, 46], [46, 63], [63, 70], [116, 143], [143, 227], [227, 116], [116, 123], [123, 111], [111, 116], [1, 44], [44, 19], [19, 1], [3, 236], [236, 51], [51, 3], [207, 216], [216, 205], [205, 207], [26, 154], [154, 22], [22, 26], [165, 39], [39, 167], [167, 165], [199, 200], [200, 208], [208, 199], [101, 36], [36, 100], [100, 101], [43, 57], [57, 202], [202, 43], [242, 20], [20, 99], [99, 242], [56, 28], [28, 157], [157, 56], [124, 35], [35, 113], [113, 124], [29, 160], [160, 27], [27, 29], [211, 204], [204, 210], [210, 211], [124, 113], [113, 46], [46, 124], [106, 43], [43, 204], [204, 106], [96, 62], [62, 77], [77, 96], [227, 137], [137, 116], [116, 227], [73, 41], [41, 72], [72, 73], [36, 203], [203, 142], [142, 36], [235, 64], [64, 240], [240, 235], [48, 49], [49, 64], [64, 48], [42, 41], [41, 74], [74, 42], [214, 212], [212, 207], [207, 214], [183, 42], [42, 184], [184, 183], [210, 169], [169, 211], [211, 210], [140, 170], [170, 176], [176, 140], [104, 105], [105, 69], [69, 104], [193, 122], [122, 168], [168, 193], [50, 123], [123, 187], [187, 50], [89, 96], [96, 90], [90, 89], [66, 65], [65, 107], [107, 66], [179, 89], [89, 180], [180, 179], [119, 101], [101, 120], [120, 119], [68, 63], [63, 104], [104, 68], [234, 93], [93, 227], [227, 234], [16, 15], [15, 85], [85, 16], [209, 129], [129, 49], [49, 209], [15, 14], [14, 86], [86, 15], [107, 55], [55, 9], [9, 107], [120, 100], [100, 121], [121, 120], [153, 145], [145, 22], [22, 153], [178, 88], [88, 179], [179, 178], [197, 6], [6, 196], [196, 197], [89, 88], [88, 96], [96, 89], [135, 138], [138, 136], [136, 135], [138, 215], [215, 172], [172, 138], [218, 115], [115, 219], [219, 218], [41, 42], [42, 81], [81, 41], [5, 195], [195, 51], [51, 5], [57, 43], [43, 61], [61, 57], [208, 171], [171, 199], [199, 208], [41, 81], [81, 38], [38, 41], [224, 53], [53, 225], [225, 224], [24, 144], [144, 110], [110, 24], [105, 52], [52, 66], [66, 105], [118, 229], [229, 117], [117, 118], [227, 34], [34, 234], [234, 227], [66, 107], [107, 69], [69, 66], [10, 109], [109, 151], [151, 10], [219, 48], [48, 235], [235, 219], [183, 62], [62, 191], [191, 183], [142, 129], [129, 126], [126, 142], [116, 111], [111, 143], [143, 116], [118, 117], [117, 50], [50, 118], [223, 222], [222, 52], [52, 223], [94, 19], [19, 141], [141, 94], [222, 221], [221, 65], [65, 222], [196, 3], [3, 197], [197, 196], [45, 220], [220, 44], [44, 45], [156, 70], [70, 139], [139, 156], [188, 122], [122, 245], [245, 188], [139, 71], [71, 162], [162, 139], [149, 170], [170, 150], [150, 149], [122, 188], [188, 196], [196, 122], [206, 216], [216, 92], [92, 206], [164, 2], [2, 167], [167, 164], [242, 141], [141, 241], [241, 242], [0, 164], [164, 37], [37, 0], [11, 72], [72, 12], [12, 11], [12, 38], [38, 13], [13, 12], [70, 63], [63, 71], [71, 70], [31, 226], [226, 111], [111, 31], [36, 101], [101, 205], [205, 36], [203, 206], [206, 165], [165, 203], [126, 209], [209, 217], [217, 126], [98, 165], [165, 97], [97, 98], [237, 220], [220, 218], [218, 237], [237, 239], [239, 241], [241, 237], [210, 214], [214, 169], [169, 210], [140, 171], [171, 32], [32, 140], [241, 125], [125, 237], [237, 241], [179, 86], [86, 178], [178, 179], [180, 85], [85, 179], [179, 180], [181, 84], [84, 180], [180, 181], [182, 83], [83, 181], [181, 182], [194, 201], [201, 182], [182, 194], [177, 137], [137, 132], [132, 177], [184, 76], [76, 183], [183, 184], [185, 61], [61, 184], [184, 185], [186, 57], [57, 185], [185, 186], [216, 212], [212, 186], [186, 216], [192, 214], [214, 187], [187, 192], [139, 34], [34, 156], [156, 139], [218, 79], [79, 237], [237, 218], [147, 123], [123, 177], [177, 147], [45, 44], [44, 4], [4, 45], [208, 201], [201, 32], [32, 208], [98, 64], [64, 129], [129, 98], [192, 213], [213, 138], [138, 192], [235, 59], [59, 219], [219, 235], [141, 242], [242, 97], [97, 141], [97, 2], [2, 141], [141, 97], [240, 75], [75, 235], [235, 240], [229, 24], [24, 228], [228, 229], [31, 25], [25, 226], [226, 31], [230, 23], [23, 229], [229, 230], [231, 22], [22, 230], [230, 231], [232, 26], [26, 231], [231, 232], [233, 112], [112, 232], [232, 233], [244, 189], [189, 243], [243, 244], [189, 221], [221, 190], [190, 189], [222, 28], [28, 221], [221, 222], [223, 27], [27, 222], [222, 223], [224, 29], [29, 223], [223, 224], [225, 30], [30, 224], [224, 225], [113, 247], [247, 225], [225, 113], [99, 60], [60, 240], [240, 99], [213, 147], [147, 215], [215, 213], [60, 20], [20, 166], [166, 60], [192, 187], [187, 213], [213, 192], [243, 112], [112, 244], [244, 243], [244, 233], [233, 245], [245, 244], [245, 128], [128, 188], [188, 245], [188, 114], [114, 174], [174, 188], [134, 131], [131, 220], [220, 134], [174, 217], [217, 236], [236, 174], [236, 198], [198, 134], [134, 236], [215, 177], [177, 58], [58, 215], [156, 143], [143, 124], [124, 156], [25, 110], [110, 7], [7, 25], [31, 228], [228, 25], [25, 31], [264, 356], [356, 368], [368, 264], [0, 11], [11, 267], [267, 0], [451, 452], [452, 349], [349, 451], [267, 302], [302, 269], [269, 267], [350, 357], [357, 277], [277, 350], [350, 452], [452, 357], [357, 350], [299, 333], [333, 297], [297, 299], [396, 175], [175, 377], [377, 396], [280, 347], [347, 330], [330, 280], [269, 303], [303, 270], [270, 269], [151, 9], [9, 337], [337, 151], [344, 278], [278, 360], [360, 344], [424, 418], [418, 431], [431, 424], [270, 304], [304, 409], [409, 270], [272, 310], [310, 407], [407, 272], [322, 270], [270, 410], [410, 322], [449, 450], [450, 347], [347, 449], [432, 422], [422, 434], [434, 432], [18, 313], [313, 17], [17, 18], [291, 306], [306, 375], [375, 291], [259, 387], [387, 260], [260, 259], [424, 335], [335, 418], [418, 424], [434, 364], [364, 416], [416, 434], [391, 423], [423, 327], [327, 391], [301, 251], [251, 298], [298, 301], [275, 281], [281, 4], [4, 275], [254, 373], [373, 253], [253, 254], [375, 307], [307, 321], [321, 375], [280, 425], [425, 411], [411, 280], [200, 421], [421, 18], [18, 200], [335, 321], [321, 406], [406, 335], [321, 320], [320, 405], [405, 321], [314, 315], [315, 17], [17, 314], [423, 426], [426, 266], [266, 423], [396, 377], [377, 369], [369, 396], [270, 322], [322, 269], [269, 270], [413, 417], [417, 464], [464, 413], [385, 386], [386, 258], [258, 385], [248, 456], [456, 419], [419, 248], [298, 284], [284, 333], [333, 298], [168, 417], [417, 8], [8, 168], [448, 346], [346, 261], [261, 448], [417, 413], [413, 285], [285, 417], [326, 327], [327, 328], [328, 326], [277, 355], [355, 329], [329, 277], [309, 392], [392, 438], [438, 309], [381, 382], [382, 256], [256, 381], [279, 429], [429, 360], [360, 279], [365, 364], [364, 379], [379, 365], [355, 277], [277, 437], [437, 355], [282, 443], [443, 283], [283, 282], [281, 275], [275, 363], [363, 281], [395, 431], [431, 369], [369, 395], [299, 297], [297, 337], [337, 299], [335, 273], [273, 321], [321, 335], [348, 450], [450, 349], [349, 348], [359, 446], [446, 467], [467, 359], [283, 293], [293, 282], [282, 283], [250, 458], [458, 462], [462, 250], [300, 276], [276, 383], [383, 300], [292, 308], [308, 325], [325, 292], [283, 276], [276, 293], [293, 283], [264, 372], [372, 447], [447, 264], [346, 352], [352, 340], [340, 346], [354, 274], [274, 19], [19, 354], [363, 456], [456, 281], [281, 363], [426, 436], [436, 425], [425, 426], [380, 381], [381, 252], [252, 380], [267, 269], [269, 393], [393, 267], [421, 200], [200, 428], [428, 421], [371, 266], [266, 329], [329, 371], [432, 287], [287, 422], [422, 432], [290, 250], [250, 328], [328, 290], [385, 258], [258, 384], [384, 385], [446, 265], [265, 342], [342, 446], [386, 387], [387, 257], [257, 386], [422, 424], [424, 430], [430, 422], [445, 342], [342, 276], [276, 445], [422, 273], [273, 424], [424, 422], [306, 292], [292, 307], [307, 306], [352, 366], [366, 345], [345, 352], [268, 271], [271, 302], [302, 268], [358, 423], [423, 371], [371, 358], [327, 294], [294, 460], [460, 327], [331, 279], [279, 294], [294, 331], [303, 271], [271, 304], [304, 303], [436, 432], [432, 427], [427, 436], [304, 272], [272, 408], [408, 304], [395, 394], [394, 431], [431, 395], [378, 395], [395, 400], [400, 378], [296, 334], [334, 299], [299, 296], [6, 351], [351, 168], [168, 6], [376, 352], [352, 411], [411, 376], [307, 325], [325, 320], [320, 307], [285, 295], [295, 336], [336, 285], [320, 319], [319, 404], [404, 320], [329, 330], [330, 349], [349, 329], [334, 293], [293, 333], [333, 334], [366, 323], [323, 447], [447, 366], [316, 15], [15, 315], [315, 316], [331, 358], [358, 279], [279, 331], [317, 14], [14, 316], [316, 317], [8, 285], [285, 9], [9, 8], [277, 329], [329, 350], [350, 277], [253, 374], [374, 252], [252, 253], [319, 318], [318, 403], [403, 319], [351, 6], [6, 419], [419, 351], [324, 318], [318, 325], [325, 324], [397, 367], [367, 365], [365, 397], [288, 435], [435, 397], [397, 288], [278, 344], [344, 439], [439, 278], [310, 272], [272, 311], [311, 310], [248, 195], [195, 281], [281, 248], [375, 273], [273, 291], [291, 375], [175, 396], [396, 199], [199, 175], [312, 311], [311, 268], [268, 312], [276, 283], [283, 445], [445, 276], [390, 373], [373, 339], [339, 390], [295, 282], [282, 296], [296, 295], [448, 449], [449, 346], [346, 448], [356, 264], [264, 454], [454, 356], [337, 336], [336, 299], [299, 337], [337, 338], [338, 151], [151, 337], [294, 278], [278, 455], [455, 294], [308, 292], [292, 415], [415, 308], [429, 358], [358, 355], [355, 429], [265, 340], [340, 372], [372, 265], [352, 346], [346, 280], [280, 352], [295, 442], [442, 282], [282, 295], [354, 19], [19, 370], [370, 354], [285, 441], [441, 295], [295, 285], [195, 248], [248, 197], [197, 195], [457, 440], [440, 274], [274, 457], [301, 300], [300, 368], [368, 301], [417, 351], [351, 465], [465, 417], [251, 301], [301, 389], [389, 251], [394, 395], [395, 379], [379, 394], [399, 412], [412, 419], [419, 399], [410, 436], [436, 322], [322, 410], [326, 2], [2, 393], [393, 326], [354, 370], [370, 461], [461, 354], [393, 164], [164, 267], [267, 393], [268, 302], [302, 12], [12, 268], [312, 268], [268, 13], [13, 312], [298, 293], [293, 301], [301, 298], [265, 446], [446, 340], [340, 265], [280, 330], [330, 425], [425, 280], [322, 426], [426, 391], [391, 322], [420, 429], [429, 437], [437, 420], [393, 391], [391, 326], [326, 393], [344, 440], [440, 438], [438, 344], [458, 459], [459, 461], [461, 458], [364, 434], [434, 394], [394, 364], [428, 396], [396, 262], [262, 428], [274, 354], [354, 457], [457, 274], [317, 316], [316, 402], [402, 317], [316, 315], [315, 403], [403, 316], [315, 314], [314, 404], [404, 315], [314, 313], [313, 405], [405, 314], [313, 421], [421, 406], [406, 313], [323, 366], [366, 361], [361, 323], [292, 306], [306, 407], [407, 292], [306, 291], [291, 408], [408, 306], [291, 287], [287, 409], [409, 291], [287, 432], [432, 410], [410, 287], [427, 434], [434, 411], [411, 427], [372, 264], [264, 383], [383, 372], [459, 309], [309, 457], [457, 459], [366, 352], [352, 401], [401, 366], [1, 274], [274, 4], [4, 1], [418, 421], [421, 262], [262, 418], [331, 294], [294, 358], [358, 331], [435, 433], [433, 367], [367, 435], [392, 289], [289, 439], [439, 392], [328, 462], [462, 326], [326, 328], [94, 2], [2, 370], [370, 94], [289, 305], [305, 455], [455, 289], [339, 254], [254, 448], [448, 339], [359, 255], [255, 446], [446, 359], [254, 253], [253, 449], [449, 254], [253, 252], [252, 450], [450, 253], [252, 256], [256, 451], [451, 252], [256, 341], [341, 452], [452, 256], [414, 413], [413, 463], [463, 414], [286, 441], [441, 414], [414, 286], [286, 258], [258, 441], [441, 286], [258, 257], [257, 442], [442, 258], [257, 259], [259, 443], [443, 257], [259, 260], [260, 444], [444, 259], [260, 467], [467, 445], [445, 260], [309, 459], [459, 250], [250, 309], [305, 289], [289, 290], [290, 305], [305, 290], [290, 460], [460, 305], [401, 376], [376, 435], [435, 401], [309, 250], [250, 392], [392, 309], [376, 411], [411, 433], [433, 376], [453, 341], [341, 464], [464, 453], [357, 453], [453, 465], [465, 357], [343, 357], [357, 412], [412, 343], [437, 343], [343, 399], [399, 437], [344, 360], [360, 440], [440, 344], [420, 437], [437, 456], [456, 420], [360, 420], [420, 363], [363, 360], [361, 401], [401, 288], [288, 361], [265, 372], [372, 353], [353, 265], [390, 339], [339, 249], [249, 390], [339, 448], [448, 255], [255, 339]];
var j = Object.entries(O).map(function(t2) {
  var e = t2[0];
  return t2[1].map(function(t3) {
    return [t3, e];
  });
}).flat();
var k = new Map(j);
function R(t2) {
  for (var e = { locationData: { relativeKeypoints: [] } }, n = Number.MAX_SAFE_INTEGER, r = Number.MIN_SAFE_INTEGER, i = Number.MAX_SAFE_INTEGER, o = Number.MIN_SAFE_INTEGER, a = 0; a < t2.length; ++a) {
    var u = t2[a];
    n = Math.min(n, u.x), r = Math.max(r, u.x), i = Math.min(i, u.y), o = Math.max(o, u.y), e.locationData.relativeKeypoints.push({ x: u.x, y: u.y });
  }
  return e.locationData.relativeBoundingBox = { xMin: n, yMin: i, xMax: r, yMax: o, width: r - n, height: o - i }, e;
}
var I = { runtime: "mediapipe", maxFaces: 1, refineLandmarks: false };
var L = function() {
  function n(e) {
    var n2 = this;
    this.width = 0, this.height = 0, this.selfieMode = false, this.faceMeshSolution = new t.FaceMesh({ locateFile: function(t2, n3) {
      return e.solutionPath ? e.solutionPath.replace(/\/+$/, "") + "/" + t2 : n3 + "/" + t2;
    } }), this.faceMeshSolution.setOptions({ refineLandmarks: e.refineLandmarks, selfieMode: this.selfieMode, maxNumFaces: e.maxFaces }), this.faceMeshSolution.onResults(function(t2) {
      if (n2.height = t2.image.height, n2.width = t2.image.width, n2.faces = [], null !== t2.multiFaceLandmarks) for (var e2 = t2.multiFaceLandmarks, r = 0; r < e2.length; r++) {
        var i = n2.translateOutput(e2[r]);
        n2.faces.push({ keypoints: i, box: R(i).locationData.relativeBoundingBox });
      }
    });
  }
  return n.prototype.translateOutput = function(t2) {
    var e = this;
    return t2.map(function(t3, n2) {
      var r = { x: t3.x * e.width, y: t3.y * e.height, z: t3.z * e.width }, i = k.get(n2);
      return null != i && (r.name = i), r;
    });
  }, n.prototype.estimateFaces = function(t2, n2) {
    return S(this, void 0, void 0, function() {
      var r, i;
      return F(this, function(o) {
        switch (o.label) {
          case 0:
            return n2 && n2.flipHorizontal && n2.flipHorizontal !== this.selfieMode && (this.selfieMode = n2.flipHorizontal, this.faceMeshSolution.setOptions({ selfieMode: this.selfieMode })), t2 instanceof Tensor ? (i = ImageData.bind, [4, browser_exports.toPixels(t2)]) : [3, 2];
          case 1:
            return r = new (i.apply(ImageData, [void 0, o.sent(), t2.shape[1], t2.shape[0]]))(), [3, 3];
          case 2:
            r = t2, o.label = 3;
          case 3:
            return t2 = r, [4, this.faceMeshSolution.send({ image: t2 })];
          case 4:
            return o.sent(), [2, this.faces];
        }
      });
    });
  }, n.prototype.dispose = function() {
    this.faceMeshSolution.close();
  }, n.prototype.reset = function() {
    this.faceMeshSolution.reset(), this.width = 0, this.height = 0, this.faces = null, this.selfieMode = false;
  }, n.prototype.initialize = function() {
    return this.faceMeshSolution.initialize();
  }, n;
}();
function B(t2) {
  return S(this, void 0, void 0, function() {
    var e, n;
    return F(this, function(r) {
      switch (r.label) {
        case 0:
          return e = function(t3) {
            if (null == t3) return E({}, I);
            var e2 = E({}, t3);
            return e2.runtime = "mediapipe", null == e2.maxFaces && (e2.maxFaces = I.maxFaces), null == e2.refineLandmarks && (e2.refineLandmarks = I.refineLandmarks), e2;
          }(t2), [4, (n = new L(e)).initialize()];
        case 1:
          return r.sent(), [2, n];
      }
    });
  });
}
var D = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {};
var P = {};
(function() {
  var t2;
  function e(t3) {
    var e2 = 0;
    return function() {
      return e2 < t3.length ? { done: false, value: t3[e2++] } : { done: true };
    };
  }
  var n = "function" == typeof Object.defineProperties ? Object.defineProperty : function(t3, e2, n2) {
    return t3 == Array.prototype || t3 == Object.prototype || (t3[e2] = n2.value), t3;
  };
  var r = function(t3) {
    t3 = ["object" == typeof globalThis && globalThis, t3, "object" == typeof window && window, "object" == typeof self && self, "object" == typeof D && D];
    for (var e2 = 0; e2 < t3.length; ++e2) {
      var n2 = t3[e2];
      if (n2 && n2.Math == Math) return n2;
    }
    throw Error("Cannot find global object");
  }(this);
  function i(t3, e2) {
    if (e2) t: {
      var i2 = r;
      t3 = t3.split(".");
      for (var o2 = 0; o2 < t3.length - 1; o2++) {
        var a2 = t3[o2];
        if (!(a2 in i2)) break t;
        i2 = i2[a2];
      }
      (e2 = e2(o2 = i2[t3 = t3[t3.length - 1]])) != o2 && null != e2 && n(i2, t3, { configurable: true, writable: true, value: e2 });
    }
  }
  function o(t3) {
    return (t3 = { next: t3 })[Symbol.iterator] = function() {
      return this;
    }, t3;
  }
  function a(t3) {
    var n2 = "undefined" != typeof Symbol && Symbol.iterator && t3[Symbol.iterator];
    return n2 ? n2.call(t3) : { next: e(t3) };
  }
  function u(t3) {
    if (!(t3 instanceof Array)) {
      t3 = a(t3);
      for (var e2, n2 = []; !(e2 = t3.next()).done; ) n2.push(e2.value);
      t3 = n2;
    }
    return t3;
  }
  i("Symbol", function(t3) {
    function e2(t4, e3) {
      this.g = t4, n(this, "description", { configurable: true, writable: true, value: e3 });
    }
    if (t3) return t3;
    e2.prototype.toString = function() {
      return this.g;
    };
    var r2 = "jscomp_symbol_" + (1e9 * Math.random() >>> 0) + "_", i2 = 0;
    return function t4(n2) {
      if (this instanceof t4) throw new TypeError("Symbol is not a constructor");
      return new e2(r2 + (n2 || "") + "_" + i2++, n2);
    };
  }), i("Symbol.iterator", function(t3) {
    if (t3) return t3;
    t3 = Symbol("Symbol.iterator");
    for (var i2 = "Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "), a2 = 0; a2 < i2.length; a2++) {
      var u2 = r[i2[a2]];
      "function" == typeof u2 && "function" != typeof u2.prototype[t3] && n(u2.prototype, t3, { configurable: true, writable: true, value: function() {
        return o(e(this));
      } });
    }
    return t3;
  });
  var s, c = "function" == typeof Object.create ? Object.create : function(t3) {
    function e2() {
    }
    return e2.prototype = t3, new e2();
  };
  if ("function" == typeof Object.setPrototypeOf) s = Object.setPrototypeOf;
  else {
    var h;
    t: {
      var l = {};
      try {
        l.__proto__ = { a: true }, h = l.a;
        break t;
      } catch (t3) {
      }
      h = false;
    }
    s = h ? function(t3, e2) {
      if (t3.__proto__ = e2, t3.__proto__ !== e2) throw new TypeError(t3 + " is not extensible");
      return t3;
    } : null;
  }
  var f = s;
  function d(t3, e2) {
    if (t3.prototype = c(e2.prototype), t3.prototype.constructor = t3, f) f(t3, e2);
    else for (var n2 in e2) if ("prototype" != n2) if (Object.defineProperties) {
      var r2 = Object.getOwnPropertyDescriptor(e2, n2);
      r2 && Object.defineProperty(t3, n2, r2);
    } else t3[n2] = e2[n2];
    t3.na = e2.prototype;
  }
  function p() {
    this.l = false, this.i = null, this.h = void 0, this.g = 1, this.u = this.o = 0, this.j = null;
  }
  function g(t3) {
    if (t3.l) throw new TypeError("Generator is already running");
    t3.l = true;
  }
  function v(t3, e2) {
    t3.j = { da: e2, ea: true }, t3.g = t3.o || t3.u;
  }
  function m(t3, e2, n2) {
    return t3.g = n2, { value: e2 };
  }
  function y(t3) {
    this.g = new p(), this.h = t3;
  }
  function w(t3, e2, n2, r2) {
    try {
      var i2 = e2.call(t3.g.i, n2);
      if (!(i2 instanceof Object)) throw new TypeError("Iterator result " + i2 + " is not an object");
      if (!i2.done) return t3.g.l = false, i2;
      var o2 = i2.value;
    } catch (e3) {
      return t3.g.i = null, v(t3.g, e3), b(t3);
    }
    return t3.g.i = null, r2.call(t3.g, o2), b(t3);
  }
  function b(t3) {
    for (; t3.g.g; ) try {
      var e2 = t3.h(t3.g);
      if (e2) return t3.g.l = false, { value: e2.value, done: false };
    } catch (e3) {
      t3.g.h = void 0, v(t3.g, e3);
    }
    if (t3.g.l = false, t3.g.j) {
      if (e2 = t3.g.j, t3.g.j = null, e2.ea) throw e2.da;
      return { value: e2.return, done: true };
    }
    return { value: void 0, done: true };
  }
  function x(t3) {
    this.next = function(e2) {
      return g(t3.g), t3.g.i ? e2 = w(t3, t3.g.i.next, e2, t3.g.s) : (t3.g.s(e2), e2 = b(t3)), e2;
    }, this.throw = function(e2) {
      return g(t3.g), t3.g.i ? e2 = w(t3, t3.g.i.throw, e2, t3.g.s) : (v(t3.g, e2), e2 = b(t3)), e2;
    }, this.return = function(e2) {
      return function(t4, e3) {
        g(t4.g);
        var n2 = t4.g.i;
        return n2 ? w(t4, "return" in n2 ? n2.return : function(t5) {
          return { value: t5, done: true };
        }, e3, t4.g.return) : (t4.g.return(e3), b(t4));
      }(t3, e2);
    }, this[Symbol.iterator] = function() {
      return this;
    };
  }
  function M(t3) {
    return function(t4) {
      function e2(e3) {
        return t4.next(e3);
      }
      function n2(e3) {
        return t4.throw(e3);
      }
      return new Promise(function(r2, i2) {
        !function t5(o2) {
          o2.done ? r2(o2.value) : Promise.resolve(o2.value).then(e2, n2).then(t5, i2);
        }(t4.next());
      });
    }(new x(new y(t3)));
  }
  p.prototype.s = function(t3) {
    this.h = t3;
  }, p.prototype.return = function(t3) {
    this.j = { return: t3 }, this.g = this.u;
  }, i("Promise", function(t3) {
    function e2(t4) {
      this.h = 0, this.i = void 0, this.g = [], this.s = false;
      var e3 = this.j();
      try {
        t4(e3.resolve, e3.reject);
      } catch (t5) {
        e3.reject(t5);
      }
    }
    function n2() {
      this.g = null;
    }
    function i2(t4) {
      return t4 instanceof e2 ? t4 : new e2(function(e3) {
        e3(t4);
      });
    }
    if (t3) return t3;
    n2.prototype.h = function(t4) {
      if (null == this.g) {
        this.g = [];
        var e3 = this;
        this.i(function() {
          e3.l();
        });
      }
      this.g.push(t4);
    };
    var o2 = r.setTimeout;
    n2.prototype.i = function(t4) {
      o2(t4, 0);
    }, n2.prototype.l = function() {
      for (; this.g && this.g.length; ) {
        var t4 = this.g;
        this.g = [];
        for (var e3 = 0; e3 < t4.length; ++e3) {
          var n3 = t4[e3];
          t4[e3] = null;
          try {
            n3();
          } catch (t5) {
            this.j(t5);
          }
        }
      }
      this.g = null;
    }, n2.prototype.j = function(t4) {
      this.i(function() {
        throw t4;
      });
    }, e2.prototype.j = function() {
      function t4(t5) {
        return function(r2) {
          n3 || (n3 = true, t5.call(e3, r2));
        };
      }
      var e3 = this, n3 = false;
      return { resolve: t4(this.D), reject: t4(this.l) };
    }, e2.prototype.D = function(t4) {
      if (t4 === this) this.l(new TypeError("A Promise cannot resolve to itself"));
      else if (t4 instanceof e2) this.H(t4);
      else {
        t: switch (typeof t4) {
          case "object":
            var n3 = null != t4;
            break t;
          case "function":
            n3 = true;
            break t;
          default:
            n3 = false;
        }
        n3 ? this.A(t4) : this.o(t4);
      }
    }, e2.prototype.A = function(t4) {
      var e3 = void 0;
      try {
        e3 = t4.then;
      } catch (t5) {
        return void this.l(t5);
      }
      "function" == typeof e3 ? this.I(e3, t4) : this.o(t4);
    }, e2.prototype.l = function(t4) {
      this.u(2, t4);
    }, e2.prototype.o = function(t4) {
      this.u(1, t4);
    }, e2.prototype.u = function(t4, e3) {
      if (0 != this.h) throw Error("Cannot settle(" + t4 + ", " + e3 + "): Promise already settled in state" + this.h);
      this.h = t4, this.i = e3, 2 === this.h && this.G(), this.B();
    }, e2.prototype.G = function() {
      var t4 = this;
      o2(function() {
        if (t4.C()) {
          var e3 = r.console;
          void 0 !== e3 && e3.error(t4.i);
        }
      }, 1);
    }, e2.prototype.C = function() {
      if (this.s) return false;
      var t4 = r.CustomEvent, e3 = r.Event, n3 = r.dispatchEvent;
      return void 0 === n3 || ("function" == typeof t4 ? t4 = new t4("unhandledrejection", { cancelable: true }) : "function" == typeof e3 ? t4 = new e3("unhandledrejection", { cancelable: true }) : (t4 = r.document.createEvent("CustomEvent")).initCustomEvent("unhandledrejection", false, true, t4), t4.promise = this, t4.reason = this.i, n3(t4));
    }, e2.prototype.B = function() {
      if (null != this.g) {
        for (var t4 = 0; t4 < this.g.length; ++t4) u2.h(this.g[t4]);
        this.g = null;
      }
    };
    var u2 = new n2();
    return e2.prototype.H = function(t4) {
      var e3 = this.j();
      t4.M(e3.resolve, e3.reject);
    }, e2.prototype.I = function(t4, e3) {
      var n3 = this.j();
      try {
        t4.call(e3, n3.resolve, n3.reject);
      } catch (t5) {
        n3.reject(t5);
      }
    }, e2.prototype.then = function(t4, n3) {
      function r2(t5, e3) {
        return "function" == typeof t5 ? function(e4) {
          try {
            i3(t5(e4));
          } catch (t6) {
            o3(t6);
          }
        } : e3;
      }
      var i3, o3, a2 = new e2(function(t5, e3) {
        i3 = t5, o3 = e3;
      });
      return this.M(r2(t4, i3), r2(n3, o3)), a2;
    }, e2.prototype.catch = function(t4) {
      return this.then(void 0, t4);
    }, e2.prototype.M = function(t4, e3) {
      function n3() {
        switch (r2.h) {
          case 1:
            t4(r2.i);
            break;
          case 2:
            e3(r2.i);
            break;
          default:
            throw Error("Unexpected state: " + r2.h);
        }
      }
      var r2 = this;
      null == this.g ? u2.h(n3) : this.g.push(n3), this.s = true;
    }, e2.resolve = i2, e2.reject = function(t4) {
      return new e2(function(e3, n3) {
        n3(t4);
      });
    }, e2.race = function(t4) {
      return new e2(function(e3, n3) {
        for (var r2 = a(t4), o3 = r2.next(); !o3.done; o3 = r2.next()) i2(o3.value).M(e3, n3);
      });
    }, e2.all = function(t4) {
      var n3 = a(t4), r2 = n3.next();
      return r2.done ? i2([]) : new e2(function(t5, e3) {
        function o3(e4) {
          return function(n4) {
            a2[e4] = n4, 0 == --u3 && t5(a2);
          };
        }
        var a2 = [], u3 = 0;
        do {
          a2.push(void 0), u3++, i2(r2.value).M(o3(a2.length - 1), e3), r2 = n3.next();
        } while (!r2.done);
      });
    }, e2;
  });
  var A = "function" == typeof Object.assign ? Object.assign : function(t3, e2) {
    for (var n2 = 1; n2 < arguments.length; n2++) {
      var r2 = arguments[n2];
      if (r2) for (var i2 in r2) Object.prototype.hasOwnProperty.call(r2, i2) && (t3[i2] = r2[i2]);
    }
    return t3;
  };
  i("Object.assign", function(t3) {
    return t3 || A;
  }), i("Object.is", function(t3) {
    return t3 || function(t4, e2) {
      return t4 === e2 ? 0 !== t4 || 1 / t4 == 1 / e2 : t4 != t4 && e2 != e2;
    };
  }), i("Array.prototype.includes", function(t3) {
    return t3 || function(t4, e2) {
      var n2 = this;
      n2 instanceof String && (n2 = String(n2));
      var r2 = n2.length;
      for (0 > (e2 = e2 || 0) && (e2 = Math.max(e2 + r2, 0)); e2 < r2; e2++) {
        var i2 = n2[e2];
        if (i2 === t4 || Object.is(i2, t4)) return true;
      }
      return false;
    };
  }), i("String.prototype.includes", function(t3) {
    return t3 || function(t4, e2) {
      if (null == this) throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");
      if (t4 instanceof RegExp) throw new TypeError("First argument to String.prototype.includes must not be a regular expression");
      return -1 !== this.indexOf(t4, e2 || 0);
    };
  }), i("Array.prototype.keys", function(t3) {
    return t3 || function() {
      return function(t4, e2) {
        t4 instanceof String && (t4 += "");
        var n2 = 0, r2 = false, i2 = { next: function() {
          if (!r2 && n2 < t4.length) {
            var i3 = n2++;
            return { value: e2(i3, t4[i3]), done: false };
          }
          return r2 = true, { done: true, value: void 0 };
        } };
        return i2[Symbol.iterator] = function() {
          return i2;
        }, i2;
      }(this, function(t4) {
        return t4;
      });
    };
  });
  var T = this || self;
  function E2(t3, e2) {
    t3 = t3.split(".");
    var n2, r2 = T;
    t3[0] in r2 || void 0 === r2.execScript || r2.execScript("var " + t3[0]);
    for (; t3.length && (n2 = t3.shift()); ) t3.length || void 0 === e2 ? r2 = r2[n2] && r2[n2] !== Object.prototype[n2] ? r2[n2] : r2[n2] = {} : r2[n2] = e2;
  }
  function S2() {
    throw Error("Invalid UTF8");
  }
  function F2(t3, e2) {
    return e2 = String.fromCharCode.apply(null, e2), null == t3 ? e2 : t3 + e2;
  }
  var C2, O2, _2 = "undefined" != typeof TextDecoder, j2 = "undefined" != typeof TextEncoder, k2 = {}, R2 = null;
  function I2(t3) {
    var e2;
    void 0 === e2 && (e2 = 0), B2(), e2 = k2[e2];
    for (var n2 = Array(Math.floor(t3.length / 3)), r2 = e2[64] || "", i2 = 0, o2 = 0; i2 < t3.length - 2; i2 += 3) {
      var a2 = t3[i2], u2 = t3[i2 + 1], s2 = t3[i2 + 2], c2 = e2[a2 >> 2];
      a2 = e2[(3 & a2) << 4 | u2 >> 4], u2 = e2[(15 & u2) << 2 | s2 >> 6], s2 = e2[63 & s2], n2[o2++] = c2 + a2 + u2 + s2;
    }
    switch (c2 = 0, s2 = r2, t3.length - i2) {
      case 2:
        s2 = e2[(15 & (c2 = t3[i2 + 1])) << 2] || r2;
      case 1:
        t3 = t3[i2], n2[o2] = e2[t3 >> 2] + e2[(3 & t3) << 4 | c2 >> 4] + s2 + r2;
    }
    return n2.join("");
  }
  function L2(t3) {
    var e2 = t3.length, n2 = 3 * e2 / 4;
    n2 % 3 ? n2 = Math.floor(n2) : -1 != "=.".indexOf(t3[e2 - 1]) && (n2 = -1 != "=.".indexOf(t3[e2 - 2]) ? n2 - 2 : n2 - 1);
    var r2 = new Uint8Array(n2), i2 = 0;
    return function(t4, e3) {
      function n3(e4) {
        for (; r3 < t4.length; ) {
          var n4 = t4.charAt(r3++), i4 = R2[n4];
          if (null != i4) return i4;
          if (!/^[\s\xa0]*$/.test(n4)) throw Error("Unknown base64 encoding at char: " + n4);
        }
        return e4;
      }
      B2();
      for (var r3 = 0; ; ) {
        var i3 = n3(-1), o2 = n3(0), a2 = n3(64), u2 = n3(64);
        if (64 === u2 && -1 === i3) break;
        e3(i3 << 2 | o2 >> 4), 64 != a2 && (e3(o2 << 4 & 240 | a2 >> 2), 64 != u2 && e3(a2 << 6 & 192 | u2));
      }
    }(t3, function(t4) {
      r2[i2++] = t4;
    }), i2 !== n2 ? r2.subarray(0, i2) : r2;
  }
  function B2() {
    if (!R2) {
      R2 = {};
      for (var t3 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""), e2 = ["+/=", "+/", "-_=", "-_.", "-_"], n2 = 0; 5 > n2; n2++) {
        var r2 = t3.concat(e2[n2].split(""));
        k2[n2] = r2;
        for (var i2 = 0; i2 < r2.length; i2++) {
          var o2 = r2[i2];
          void 0 === R2[o2] && (R2[o2] = i2);
        }
      }
    }
  }
  var P2, z2 = "function" == typeof Uint8Array;
  function U2(t3) {
    return z2 && null != t3 && t3 instanceof Uint8Array;
  }
  function N2(t3) {
    if (this.L = t3, null !== t3 && 0 === t3.length) throw Error("ByteString should be constructed with non-empty values");
  }
  var V2 = "function" == typeof Uint8Array.prototype.slice, H2 = 0;
  function K2(t3, e2) {
    return Error("Invalid wire type: " + t3 + " (at position " + e2 + ")");
  }
  function W2() {
    return Error("Failed to read varint, encoding is invalid.");
  }
  function G2(t3, e2) {
    e2 = void 0 !== (e2 = void 0 === e2 ? {} : e2).v && e2.v, this.h = null, this.g = this.i = this.j = 0, this.v = e2, t3 && X2(this, t3);
  }
  function X2(t3, e2) {
    t3.h = function(t4, e3) {
      if (t4.constructor === Uint8Array) return t4;
      if (t4.constructor === ArrayBuffer) return new Uint8Array(t4);
      if (t4.constructor === Array) return new Uint8Array(t4);
      if (t4.constructor === String) return L2(t4);
      if (t4.constructor === N2) return !e3 && (e3 = t4.L) && e3.constructor === Uint8Array ? e3 : (e3 = null == (e3 = t4.L) || U2(e3) ? e3 : "string" == typeof e3 ? L2(e3) : null, (t4 = t4.L = e3) ? new Uint8Array(t4) : P2 || (P2 = new Uint8Array(0)));
      if (t4 instanceof Uint8Array) return new Uint8Array(t4.buffer, t4.byteOffset, t4.byteLength);
      throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, or Array of numbers");
    }(e2, t3.v), t3.j = 0, t3.i = t3.h.length, t3.g = t3.j;
  }
  function Y2(t3) {
    if (t3.g > t3.i) throw Error("Tried to read past the end of the data " + t3.g + " > " + t3.i);
  }
  function J2(t3) {
    var e2 = t3.h, n2 = e2[t3.g], r2 = 127 & n2;
    if (128 > n2) return t3.g += 1, Y2(t3), r2;
    if (r2 |= (127 & (n2 = e2[t3.g + 1])) << 7, 128 > n2) return t3.g += 2, Y2(t3), r2;
    if (r2 |= (127 & (n2 = e2[t3.g + 2])) << 14, 128 > n2) return t3.g += 3, Y2(t3), r2;
    if (r2 |= (127 & (n2 = e2[t3.g + 3])) << 21, 128 > n2) return t3.g += 4, Y2(t3), r2;
    if (n2 = e2[t3.g + 4], t3.g += 5, r2 |= (15 & n2) << 28, 128 > n2) return Y2(t3), r2;
    if (128 <= e2[t3.g++] && 128 <= e2[t3.g++] && 128 <= e2[t3.g++] && 128 <= e2[t3.g++] && 128 <= e2[t3.g++]) throw W2();
    return Y2(t3), r2;
  }
  G2.prototype.reset = function() {
    this.g = this.j;
  };
  var q2 = [];
  function $2() {
    this.g = [];
  }
  function Z2(t3, e2) {
    for (; 127 < e2; ) t3.g.push(127 & e2 | 128), e2 >>>= 7;
    t3.g.push(e2);
  }
  function Q2(t3) {
    var e2 = {}, n2 = void 0 !== e2.W && e2.W;
    this.l = { v: void 0 !== e2.v && e2.v }, this.W = n2, e2 = this.l, q2.length ? (n2 = q2.pop(), e2 && (n2.v = e2.v), t3 && X2(n2, t3), t3 = n2) : t3 = new G2(t3, e2), this.g = t3, this.j = this.g.g, this.h = this.i = -1;
  }
  function tt2(t3) {
    var e2 = t3.g;
    if (e2.g == e2.i) return false;
    t3.j = t3.g.g;
    var n2 = J2(t3.g) >>> 0;
    if (e2 = n2 >>> 3, !(0 <= (n2 &= 7) && 5 >= n2)) throw K2(n2, t3.j);
    if (1 > e2) throw Error("Invalid field number: " + e2 + " (at position " + t3.j + ")");
    return t3.i = e2, t3.h = n2, true;
  }
  function et2(t3) {
    switch (t3.h) {
      case 0:
        if (0 != t3.h) et2(t3);
        else t: {
          for (var e2 = (t3 = t3.g).g, n2 = e2 + 10; e2 < n2; ) if (0 == (128 & t3.h[e2++])) {
            t3.g = e2, Y2(t3);
            break t;
          }
          throw W2();
        }
        break;
      case 1:
        (t3 = t3.g).g += 8, Y2(t3);
        break;
      case 2:
        2 != t3.h ? et2(t3) : (e2 = J2(t3.g) >>> 0, (t3 = t3.g).g += e2, Y2(t3));
        break;
      case 5:
        (t3 = t3.g).g += 4, Y2(t3);
        break;
      case 3:
        for (e2 = t3.i; ; ) {
          if (!tt2(t3)) throw Error("Unmatched start-group tag: stream EOF");
          if (4 == t3.h) {
            if (t3.i != e2) throw Error("Unmatched end-group tag");
            break;
          }
          et2(t3);
        }
        break;
      default:
        throw K2(t3.h, t3.j);
    }
  }
  $2.prototype.length = function() {
    return this.g.length;
  }, $2.prototype.end = function() {
    var t3 = this.g;
    return this.g = [], t3;
  }, Q2.prototype.reset = function() {
    this.g.reset(), this.j = this.g.g, this.h = this.i = -1;
  };
  var nt2 = [];
  function rt2() {
    this.i = [], this.h = 0, this.g = new $2();
  }
  function it2(t3, e2) {
    0 !== e2.length && (t3.i.push(e2), t3.h += e2.length);
  }
  var ot2 = "function" == typeof Symbol && "symbol" == typeof Symbol() ? Symbol(void 0) : void 0;
  function at2(t3, e2) {
    Object.isFrozen(t3) || (ot2 ? t3[ot2] |= e2 : void 0 !== t3.N ? t3.N |= e2 : Object.defineProperties(t3, { N: { value: e2, configurable: true, writable: true, enumerable: false } }));
  }
  function ut2(t3) {
    var e2;
    return null == (e2 = ot2 ? t3[ot2] : t3.N) ? 0 : e2;
  }
  function st2(t3) {
    return at2(t3, 1), t3;
  }
  function ct2(t3) {
    return !!Array.isArray(t3) && !!(2 & ut2(t3));
  }
  function ht2(t3) {
    if (!Array.isArray(t3)) throw Error("cannot mark non-array as immutable");
    at2(t3, 2);
  }
  function lt2(t3) {
    return null !== t3 && "object" == typeof t3 && !Array.isArray(t3) && t3.constructor === Object;
  }
  var ft2 = Object.freeze(st2([]));
  function dt2(t3) {
    if (ct2(t3.m)) throw Error("Cannot mutate an immutable Message");
  }
  var pt2, gt2 = "undefined" != typeof Symbol && void 0 !== Symbol.hasInstance;
  function vt2(t3) {
    return { value: t3, configurable: false, writable: false, enumerable: false };
  }
  function mt2(t3, e2, n2) {
    return -1 === e2 ? null : e2 >= t3.i ? t3.g ? t3.g[e2] : void 0 : void 0 !== n2 && n2 && t3.g && null != (n2 = t3.g[e2]) ? n2 : t3.m[e2 + t3.h];
  }
  function yt2(t3, e2, n2, r2) {
    r2 = void 0 !== r2 && r2, dt2(t3), e2 < t3.i && !r2 ? t3.m[e2 + t3.h] = n2 : (t3.g || (t3.g = t3.m[t3.i + t3.h] = {}))[e2] = n2;
  }
  function wt2(t3, e2, n2, r2) {
    n2 = void 0 === n2 || n2;
    var i2 = mt2(t3, e2, r2 = void 0 !== r2 && r2);
    return null == i2 && (i2 = ft2), ct2(t3.m) ? n2 && (ht2(i2), Object.freeze(i2)) : (i2 === ft2 || ct2(i2)) && yt2(t3, e2, i2 = st2(i2.slice()), r2), i2;
  }
  function bt2(t3, e2, n2) {
    return null == (t3 = null == (t3 = mt2(t3, e2)) ? t3 : +t3) ? void 0 === n2 ? 0 : n2 : t3;
  }
  function xt2(t3, e2, n2, r2) {
    t3.j || (t3.j = {});
    var i2 = ct2(t3.m), o2 = t3.j[n2];
    if (!o2) {
      r2 = wt2(t3, n2, true, void 0 !== r2 && r2), o2 = [], i2 = i2 || ct2(r2);
      for (var a2 = 0; a2 < r2.length; a2++) o2[a2] = new e2(r2[a2]), i2 && ht2(o2[a2].m);
      i2 && (ht2(o2), Object.freeze(o2)), t3.j[n2] = o2;
    }
    return o2;
  }
  function Mt2(t3, e2, n2, r2, i2) {
    var o2 = void 0 !== o2 && o2;
    return dt2(t3), o2 = xt2(t3, n2, e2, o2), n2 = r2 || new n2(), t3 = wt2(t3, e2), null != i2 ? (o2.splice(i2, 0, n2), t3.splice(i2, 0, n2.m)) : (o2.push(n2), t3.push(n2.m)), n2;
  }
  function At2(t3, e2) {
    return null == (t3 = mt2(t3, e2)) ? 0 : t3;
  }
  function Tt2(t3, e2) {
    return null == (t3 = mt2(t3, e2)) ? "" : t3;
  }
  function Et2(t3) {
    var e2 = Ct2;
    return Ft2(t3, e2 = void 0 === e2 ? Ot2 : e2);
  }
  function St2(t3, e2) {
    if (null != t3) {
      if (Array.isArray(t3)) t3 = Ft2(t3, e2);
      else if (lt2(t3)) {
        var n2, r2 = {};
        for (n2 in t3) r2[n2] = St2(t3[n2], e2);
        t3 = r2;
      } else t3 = e2(t3);
      return t3;
    }
  }
  function Ft2(t3, e2) {
    for (var n2 = t3.slice(), r2 = 0; r2 < n2.length; r2++) n2[r2] = St2(n2[r2], e2);
    return Array.isArray(t3) && 1 & ut2(t3) && st2(n2), n2;
  }
  function Ct2(t3) {
    return t3 && "object" == typeof t3 && t3.toJSON ? t3.toJSON() : (t3 = function(t4) {
      switch (typeof t4) {
        case "number":
          return isFinite(t4) ? t4 : String(t4);
        case "object":
          if (t4 && !Array.isArray(t4)) {
            if (U2(t4)) return I2(t4);
            if (t4 instanceof N2) {
              var e2 = t4.L;
              return e2 = null == e2 || "string" == typeof e2 ? e2 : z2 && e2 instanceof Uint8Array ? I2(e2) : null, (t4.L = e2) || "";
            }
          }
      }
      return t4;
    }(t3), Array.isArray(t3) ? Et2(t3) : t3);
  }
  function Ot2(t3) {
    return U2(t3) ? new Uint8Array(t3) : t3;
  }
  function _t2(t3, e2, n2) {
    t3 || (t3 = pt2), pt2 = null;
    var r2 = this.constructor.h;
    if (t3 || (t3 = r2 ? [r2] : []), this.h = (r2 ? 0 : -1) - (this.constructor.g || 0), this.j = void 0, this.m = t3, t3 = (r2 = this.m.length) - 1, r2 && lt2(r2 = this.m[t3]) ? (this.i = t3 - this.h, this.g = r2) : void 0 !== e2 && -1 < e2 ? (this.i = Math.max(e2, t3 + 1 - this.h), this.g = void 0) : this.i = Number.MAX_VALUE, n2) for (e2 = 0; e2 < n2.length; e2++) if ((t3 = n2[e2]) < this.i) t3 += this.h, (r2 = this.m[t3]) ? Array.isArray(r2) && st2(r2) : this.m[t3] = ft2;
    else {
      var i2 = (r2 = this.g || (this.g = this.m[this.i + this.h] = {}))[t3];
      i2 ? Array.isArray(i2) && st2(i2) : r2[t3] = ft2;
    }
  }
  function jt2() {
    _t2.apply(this, arguments);
  }
  if (_t2.prototype.toJSON = function() {
    return Et2(this.m);
  }, _t2.prototype.toString = function() {
    return this.m.toString();
  }, d(jt2, _t2), gt2) {
    var kt2 = {};
    Object.defineProperties(jt2, (kt2[Symbol.hasInstance] = vt2(function() {
      throw Error("Cannot perform instanceof checks for MutableMessage");
    }), kt2));
  }
  function Rt2(t3, e2, n2) {
    if (n2) {
      var r2, i2 = {};
      for (r2 in n2) {
        var o2 = n2[r2], a2 = o2.ha;
        a2 || (i2.F = o2.la || o2.fa.P, o2.aa ? (i2.U = Ut2(o2.aa), a2 = /* @__PURE__ */ function(t4) {
          return function(e3, n3, r3) {
            return t4.F(e3, n3, r3, t4.U);
          };
        }(i2)) : o2.ca ? (i2.T = Nt2(o2.X.g, o2.ca), a2 = /* @__PURE__ */ function(t4) {
          return function(e3, n3, r3) {
            return t4.F(e3, n3, r3, t4.T);
          };
        }(i2)) : a2 = i2.F, o2.ha = a2), a2(e2, t3, o2.X), i2 = { F: i2.F, U: i2.U, T: i2.T };
      }
    }
    !function(t4, e3) {
      if (e3 = e3.ba) {
        it2(t4, t4.g.end());
        for (var n3 = 0; n3 < e3.length; n3++) it2(t4, e3[n3]);
      }
    }(e2, t3);
  }
  var It2 = Symbol();
  function Lt2(t3, e2, n2) {
    return t3[It2] || (t3[It2] = function(t4, r2) {
      return e2(t4, r2, n2);
    });
  }
  function Bt2(t3) {
    var e2 = t3[It2];
    if (!e2) {
      var n2 = Qt(t3);
      e2 = function(t4, e3) {
        return te(t4, e3, n2);
      }, t3[It2] = e2;
    }
    return e2;
  }
  function Dt2(t3) {
    var e2 = function(t4) {
      var e3 = t4.aa;
      return e3 ? Bt2(e3) : (e3 = t4.ka) ? Lt2(t4.X.g, e3, t4.ca) : void 0;
    }(t3), n2 = t3.X, r2 = t3.fa.O;
    return e2 ? function(t4, i2) {
      return r2(t4, i2, n2, e2);
    } : function(t4, e3) {
      return r2(t4, e3, n2);
    };
  }
  function Pt2(t3, e2, n2, r2, i2, o2) {
    var a2 = 0;
    for ((t3 = t3()).length && "number" != typeof t3[0] && (n2(e2, t3[0]), a2++); a2 < t3.length; ) {
      n2 = t3[a2++];
      for (var u2 = a2 + 1; u2 < t3.length && "number" != typeof t3[u2]; ) u2++;
      var s2 = t3[a2++];
      switch (u2 -= a2) {
        case 0:
          r2(e2, n2, s2);
          break;
        case 1:
          r2(e2, n2, s2, t3[a2++]);
          break;
        case 2:
          i2(e2, n2, s2, t3[a2++], t3[a2++]);
          break;
        case 3:
          u2 = t3[a2++];
          var c2 = t3[a2++], h2 = t3[a2++];
          Array.isArray(h2) ? i2(e2, n2, s2, u2, c2, h2) : o2(e2, n2, s2, u2, c2, h2);
          break;
        case 4:
          o2(e2, n2, s2, t3[a2++], t3[a2++], t3[a2++], t3[a2++]);
          break;
        default:
          throw Error("unexpected number of binary field arguments: " + u2);
      }
    }
    return e2;
  }
  var zt2 = Symbol();
  function Ut2(t3) {
    var e2 = t3[zt2];
    if (!e2) {
      var n2 = Xt2(t3);
      e2 = function(t4, e3) {
        return ne(t4, e3, n2);
      }, t3[zt2] = e2;
    }
    return e2;
  }
  function Nt2(t3, e2) {
    var n2 = t3[zt2];
    return n2 || (n2 = function(t4, n3) {
      return Rt2(t4, n3, e2);
    }, t3[zt2] = n2), n2;
  }
  var Vt2 = Symbol();
  function Ht2(t3, e2) {
    t3.push(e2);
  }
  function Kt2(t3, e2, n2) {
    t3.push(e2, n2.P);
  }
  function Wt2(t3, e2, n2, r2, i2) {
    var o2 = Ut2(i2), a2 = n2.P;
    t3.push(e2, function(t4, e3, n3) {
      return a2(t4, e3, n3, r2, o2);
    });
  }
  function Gt2(t3, e2, n2, r2, i2, o2) {
    var a2 = Nt2(r2, o2), u2 = n2.P;
    t3.push(e2, function(t4, e3, n3) {
      return u2(t4, e3, n3, r2, a2);
    });
  }
  function Xt2(t3) {
    var e2 = t3[Vt2];
    return e2 || Pt2(t3, t3[Vt2] = [], Ht2, Kt2, Wt2, Gt2);
  }
  var Yt2 = Symbol();
  function Jt2(t3, e2) {
    t3[0] = e2;
  }
  function qt2(t3, e2, n2, r2) {
    var i2 = n2.O;
    t3[e2] = r2 ? function(t4, e3, n3) {
      return i2(t4, e3, n3, r2);
    } : i2;
  }
  function $t2(t3, e2, n2, r2, i2, o2) {
    var a2 = n2.O, u2 = Bt2(i2);
    t3[e2] = function(t4, e3, n3) {
      return a2(t4, e3, n3, r2, u2, o2);
    };
  }
  function Zt2(t3, e2, n2, r2, i2, o2, a2) {
    var u2 = n2.O, s2 = Lt2(r2, i2, o2);
    t3[e2] = function(t4, e3, n3) {
      return u2(t4, e3, n3, r2, s2, a2);
    };
  }
  function Qt(t3) {
    var e2 = t3[Yt2];
    return e2 || Pt2(t3, t3[Yt2] = {}, Jt2, qt2, $t2, Zt2);
  }
  function te(t3, e2, n2) {
    for (; tt2(e2) && 4 != e2.h; ) {
      var r2 = e2.i, i2 = n2[r2];
      if (!i2) {
        var o2 = n2[0];
        o2 && (o2 = o2[r2]) && (i2 = n2[r2] = Dt2(o2));
      }
      if (!(i2 && i2(e2, t3, r2) || (i2 = e2, r2 = t3, o2 = i2.j, et2(i2), i2.W))) {
        var a2 = i2.g.h;
        i2 = o2 === (i2 = i2.g.g) ? P2 || (P2 = new Uint8Array(0)) : V2 ? a2.slice(o2, i2) : new Uint8Array(a2.subarray(o2, i2)), (o2 = r2.ba) ? o2.push(i2) : r2.ba = [i2];
      }
    }
    return t3;
  }
  function ee(t3, e2, n2) {
    if (nt2.length) {
      var r2 = nt2.pop();
      t3 && (X2(r2.g, t3), r2.i = -1, r2.h = -1), t3 = r2;
    } else t3 = new Q2(t3);
    try {
      return te(new e2(), t3, Qt(n2));
    } finally {
      (e2 = t3.g).h = null, e2.j = 0, e2.i = 0, e2.g = 0, e2.v = false, t3.i = -1, t3.h = -1, 100 > nt2.length && nt2.push(t3);
    }
  }
  function ne(t3, e2, n2) {
    for (var r2 = n2.length, i2 = 1 == r2 % 2, o2 = i2 ? 1 : 0; o2 < r2; o2 += 2) (0, n2[o2 + 1])(e2, t3, n2[o2]);
    Rt2(t3, e2, i2 ? n2[0] : void 0);
  }
  function re(t3, e2) {
    var n2 = new rt2();
    ne(t3, n2, Xt2(e2)), it2(n2, n2.g.end()), t3 = new Uint8Array(n2.h);
    for (var r2 = (e2 = n2.i).length, i2 = 0, o2 = 0; o2 < r2; o2++) {
      var a2 = e2[o2];
      t3.set(a2, i2), i2 += a2.length;
    }
    return n2.i = [t3], t3;
  }
  function ie(t3, e2) {
    return { O: t3, P: e2 };
  }
  var oe = ie(function(t3, e2, n2) {
    if (5 !== t3.h) return false;
    var r2 = (t3 = t3.g).h[t3.g], i2 = t3.h[t3.g + 1], o2 = t3.h[t3.g + 2], a2 = t3.h[t3.g + 3];
    return t3.g += 4, Y2(t3), t3 = 2 * ((i2 = (r2 << 0 | i2 << 8 | o2 << 16 | a2 << 24) >>> 0) >> 31) + 1, r2 = i2 >>> 23 & 255, i2 &= 8388607, yt2(e2, n2, 255 == r2 ? i2 ? NaN : 1 / 0 * t3 : 0 == r2 ? t3 * Math.pow(2, -149) * i2 : t3 * Math.pow(2, r2 - 150) * (i2 + Math.pow(2, 23))), true;
  }, function(t3, e2, n2) {
    if (null != (e2 = mt2(e2, n2))) {
      Z2(t3.g, 8 * n2 + 5), t3 = t3.g;
      var r2 = e2;
      0 === (r2 = (n2 = 0 > r2 ? 1 : 0) ? -r2 : r2) ? 0 < 1 / r2 ? H2 = 0 : (0, H2 = 2147483648) : isNaN(r2) ? (0, H2 = 2147483647) : 34028234663852886e22 < r2 ? (0, H2 = (n2 << 31 | 2139095040) >>> 0) : 11754943508222875e-54 > r2 ? (r2 = Math.round(r2 / Math.pow(2, -149)), 0, H2 = (n2 << 31 | r2) >>> 0) : (e2 = Math.floor(Math.log(r2) / Math.LN2), r2 *= Math.pow(2, -e2), 16777216 <= (r2 = Math.round(8388608 * r2)) && ++e2, 0, H2 = (n2 << 31 | e2 + 127 << 23 | 8388607 & r2) >>> 0), n2 = H2, t3.g.push(n2 >>> 0 & 255), t3.g.push(n2 >>> 8 & 255), t3.g.push(n2 >>> 16 & 255), t3.g.push(n2 >>> 24 & 255);
    }
  }), ae = ie(function(t3, e2, n2) {
    if (0 !== t3.h) return false;
    for (var r2 = t3.g, i2 = 128, o2 = 0, a2 = t3 = 0; 4 > a2 && 128 <= i2; a2++) i2 = r2.h[r2.g++], Y2(r2), o2 |= (127 & i2) << 7 * a2;
    if (128 <= i2 && (i2 = r2.h[r2.g++], Y2(r2), o2 |= (127 & i2) << 28, t3 |= (127 & i2) >> 4), 128 <= i2) for (a2 = 0; 5 > a2 && 128 <= i2; a2++) i2 = r2.h[r2.g++], Y2(r2), t3 |= (127 & i2) << 7 * a2 + 3;
    if (!(128 > i2)) throw W2();
    return r2 = o2 >>> 0, (t3 = 2147483648 & (i2 = t3 >>> 0)) && (i2 = ~i2 >>> 0, 0 == (r2 = 1 + ~r2 >>> 0) && (i2 = i2 + 1 >>> 0)), r2 = 4294967296 * i2 + (r2 >>> 0), yt2(e2, n2, t3 ? -r2 : r2), true;
  }, function(t3, e2, n2) {
    if (null != (e2 = mt2(e2, n2)) && null != e2) {
      Z2(t3.g, 8 * n2), t3 = t3.g;
      var r2 = e2;
      for (n2 = 0 > r2, e2 = (r2 = Math.abs(r2)) >>> 0, r2 = Math.floor((r2 - e2) / 4294967296), r2 >>>= 0, n2 && (r2 = ~r2 >>> 0, 4294967295 < (e2 = 1 + (~e2 >>> 0)) && (e2 = 0, 4294967295 < ++r2 && (r2 = 0))), n2 = H2 = e2, e2 = r2; 0 < e2 || 127 < n2; ) t3.g.push(127 & n2 | 128), n2 = (n2 >>> 7 | e2 << 25) >>> 0, e2 >>>= 7;
      t3.g.push(n2);
    }
  }), ue = ie(function(t3, e2, n2) {
    return 0 === t3.h && (yt2(e2, n2, J2(t3.g)), true);
  }, function(t3, e2, n2) {
    if (null != (e2 = mt2(e2, n2)) && null != e2) if (Z2(t3.g, 8 * n2), t3 = t3.g, 0 <= (n2 = e2)) Z2(t3, n2);
    else {
      for (e2 = 0; 9 > e2; e2++) t3.g.push(127 & n2 | 128), n2 >>= 7;
      t3.g.push(1);
    }
  }), se = ie(function(t3, e2, n2) {
    if (2 !== t3.h) return false;
    var r2, i2 = J2(t3.g) >>> 0, o2 = (t3 = t3.g).g;
    if (t3.g += i2, Y2(t3), t3 = t3.h, _2) (r2 = C2) || (r2 = C2 = new TextDecoder("utf-8", { fatal: true })), r2 = r2.decode(t3.subarray(o2, o2 + i2));
    else {
      i2 = o2 + i2;
      for (var a2, u2, s2, c2 = [], h2 = null; o2 < i2; ) 128 > (a2 = t3[o2++]) ? c2.push(a2) : 224 > a2 ? o2 >= i2 ? S2() : (u2 = t3[o2++], 194 > a2 || 128 != (192 & u2) ? (o2--, S2()) : c2.push((31 & a2) << 6 | 63 & u2)) : 240 > a2 ? o2 >= i2 - 1 ? S2() : 128 != (192 & (u2 = t3[o2++])) || 224 === a2 && 160 > u2 || 237 === a2 && 160 <= u2 || 128 != (192 & (r2 = t3[o2++])) ? (o2--, S2()) : c2.push((15 & a2) << 12 | (63 & u2) << 6 | 63 & r2) : 244 >= a2 ? o2 >= i2 - 2 ? S2() : 128 != (192 & (u2 = t3[o2++])) || 0 != u2 - 144 + (a2 << 28) >> 30 || 128 != (192 & (r2 = t3[o2++])) || 128 != (192 & (s2 = t3[o2++])) ? (o2--, S2()) : (a2 = (7 & a2) << 18 | (63 & u2) << 12 | (63 & r2) << 6 | 63 & s2, a2 -= 65536, c2.push(55296 + (a2 >> 10 & 1023), 56320 + (1023 & a2))) : S2(), 8192 <= c2.length && (h2 = F2(h2, c2), c2.length = 0);
      r2 = F2(h2, c2);
    }
    return yt2(e2, n2, r2), true;
  }, function(t3, e2, n2) {
    if (null != (e2 = mt2(e2, n2))) {
      var r2 = false;
      if (r2 = void 0 !== r2 && r2, j2) {
        if (r2 && /(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(e2)) throw Error("Found an unpaired surrogate");
        e2 = (O2 || (O2 = new TextEncoder())).encode(e2);
      } else {
        for (var i2 = 0, o2 = new Uint8Array(3 * e2.length), a2 = 0; a2 < e2.length; a2++) {
          var u2 = e2.charCodeAt(a2);
          if (128 > u2) o2[i2++] = u2;
          else {
            if (2048 > u2) o2[i2++] = u2 >> 6 | 192;
            else {
              if (55296 <= u2 && 57343 >= u2) {
                if (56319 >= u2 && a2 < e2.length) {
                  var s2 = e2.charCodeAt(++a2);
                  if (56320 <= s2 && 57343 >= s2) {
                    u2 = 1024 * (u2 - 55296) + s2 - 56320 + 65536, o2[i2++] = u2 >> 18 | 240, o2[i2++] = u2 >> 12 & 63 | 128, o2[i2++] = u2 >> 6 & 63 | 128, o2[i2++] = 63 & u2 | 128;
                    continue;
                  }
                  a2--;
                }
                if (r2) throw Error("Found an unpaired surrogate");
                u2 = 65533;
              }
              o2[i2++] = u2 >> 12 | 224, o2[i2++] = u2 >> 6 & 63 | 128;
            }
            o2[i2++] = 63 & u2 | 128;
          }
        }
        e2 = o2.subarray(0, i2);
      }
      Z2(t3.g, 8 * n2 + 2), Z2(t3.g, e2.length), it2(t3, t3.g.end()), it2(t3, e2);
    }
  }), ce = ie(function(t3, e2, n2, r2, i2) {
    if (2 !== t3.h) return false;
    e2 = Mt2(e2, n2, r2), n2 = t3.g.i, r2 = J2(t3.g) >>> 0;
    var o2 = t3.g.g + r2, a2 = o2 - n2;
    if (0 >= a2 && (t3.g.i = o2, i2(e2, t3), a2 = o2 - t3.g.g), a2) throw Error("Message parsing ended unexpectedly. Expected to read " + r2 + " bytes, instead read " + (r2 - a2) + " bytes, either the data ended unexpectedly or the message misreported its own length");
    return t3.g.g = o2, t3.g.i = n2, true;
  }, function(t3, e2, n2, r2, i2) {
    if (null != (e2 = xt2(e2, r2, n2))) for (r2 = 0; r2 < e2.length; r2++) {
      var o2 = t3;
      Z2(o2.g, 8 * n2 + 2);
      var a2 = o2.g.end();
      it2(o2, a2), a2.push(o2.h), o2 = a2, i2(e2[r2], t3), a2 = t3;
      var u2 = o2.pop();
      for (u2 = a2.h + a2.g.length() - u2; 127 < u2; ) o2.push(127 & u2 | 128), u2 >>>= 7, a2.h++;
      o2.push(u2), a2.h++;
    }
  });
  function he() {
    jt2.apply(this, arguments);
  }
  if (d(he, jt2), gt2) {
    var le = {};
    Object.defineProperties(he, (le[Symbol.hasInstance] = vt2(Object[Symbol.hasInstance]), le));
  }
  function fe(t3) {
    he.call(this, t3);
  }
  function de() {
    return [1, ue, 2, oe, 3, se, 4, se];
  }
  function pe(t3) {
    he.call(this, t3, -1, ve);
  }
  function ge() {
    return [1, ce, fe, de];
  }
  d(fe, he), d(pe, he), pe.prototype.addClassification = function(t3, e2) {
    return Mt2(this, 1, fe, t3, e2), this;
  };
  var ve = [1];
  function me(t3) {
    he.call(this, t3);
  }
  function ye() {
    return [1, oe, 2, oe, 3, oe, 4, oe, 5, oe];
  }
  function we(t3) {
    he.call(this, t3, -1, xe);
  }
  function be() {
    return [1, ce, me, ye];
  }
  d(me, he), d(we, he);
  var xe = [1];
  function Me(t3) {
    he.call(this, t3);
  }
  function Ae() {
    return [1, oe, 2, oe, 3, oe, 4, oe, 5, oe, 6, ae];
  }
  d(Me, he);
  var Te = [[61, 146], [146, 91], [91, 181], [181, 84], [84, 17], [17, 314], [314, 405], [405, 321], [321, 375], [375, 291], [61, 185], [185, 40], [40, 39], [39, 37], [37, 0], [0, 267], [267, 269], [269, 270], [270, 409], [409, 291], [78, 95], [95, 88], [88, 178], [178, 87], [87, 14], [14, 317], [317, 402], [402, 318], [318, 324], [324, 308], [78, 191], [191, 80], [80, 81], [81, 82], [82, 13], [13, 312], [312, 311], [311, 310], [310, 415], [415, 308]], Ee = [[263, 249], [249, 390], [390, 373], [373, 374], [374, 380], [380, 381], [381, 382], [382, 362], [263, 466], [466, 388], [388, 387], [387, 386], [386, 385], [385, 384], [384, 398], [398, 362]], Se = [[276, 283], [283, 282], [282, 295], [295, 285], [300, 293], [293, 334], [334, 296], [296, 336]], Fe = [[33, 7], [7, 163], [163, 144], [144, 145], [145, 153], [153, 154], [154, 155], [155, 133], [33, 246], [246, 161], [161, 160], [160, 159], [159, 158], [158, 157], [157, 173], [173, 133]], Ce = [[46, 53], [53, 52], [52, 65], [65, 55], [70, 63], [63, 105], [105, 66], [66, 107]], Oe = [[10, 338], [338, 297], [297, 332], [332, 284], [284, 251], [251, 389], [389, 356], [356, 454], [454, 323], [323, 361], [361, 288], [288, 397], [397, 365], [365, 379], [379, 378], [378, 400], [400, 377], [377, 152], [152, 148], [148, 176], [176, 149], [149, 150], [150, 136], [136, 172], [172, 58], [58, 132], [132, 93], [93, 234], [234, 127], [127, 162], [162, 21], [21, 54], [54, 103], [103, 67], [67, 109], [109, 10]], _e = [].concat(u(Te), u(Ee), u(Se), u(Fe), u(Ce), u(Oe));
  function je(t3, e2, n2) {
    if (n2 = t3.createShader(0 === n2 ? t3.VERTEX_SHADER : t3.FRAGMENT_SHADER), t3.shaderSource(n2, e2), t3.compileShader(n2), !t3.getShaderParameter(n2, t3.COMPILE_STATUS)) throw Error("Could not compile WebGL shader.\n\n" + t3.getShaderInfoLog(n2));
    return n2;
  }
  function ke(t3) {
    return xt2(t3, fe, 1).map(function(t4) {
      return { index: At2(t4, 1), ga: bt2(t4, 2), label: null != mt2(t4, 3) ? Tt2(t4, 3) : void 0, displayName: null != mt2(t4, 4) ? Tt2(t4, 4) : void 0 };
    });
  }
  function Re(t3) {
    return { x: bt2(t3, 1), y: bt2(t3, 2), z: bt2(t3, 3), visibility: null != mt2(t3, 4) ? bt2(t3, 4) : void 0 };
  }
  function Ie(t3, e2) {
    this.h = t3, this.g = e2, this.l = 0;
  }
  function Le(t3, e2, n2) {
    return function(t4, e3) {
      var n3 = t4.g;
      if (void 0 === t4.o) {
        var r2 = je(n3, "\n  attribute vec2 aVertex;\n  attribute vec2 aTex;\n  varying vec2 vTex;\n  void main(void) {\n    gl_Position = vec4(aVertex, 0.0, 1.0);\n    vTex = aTex;\n  }", 0), i2 = je(n3, "\n  precision mediump float;\n  varying vec2 vTex;\n  uniform sampler2D sampler0;\n  void main(){\n    gl_FragColor = texture2D(sampler0, vTex);\n  }", 1), o2 = n3.createProgram();
        if (n3.attachShader(o2, r2), n3.attachShader(o2, i2), n3.linkProgram(o2), !n3.getProgramParameter(o2, n3.LINK_STATUS)) throw Error("Could not compile WebGL program.\n\n" + n3.getProgramInfoLog(o2));
        r2 = t4.o = o2, n3.useProgram(r2), i2 = n3.getUniformLocation(r2, "sampler0"), t4.j = { K: n3.getAttribLocation(r2, "aVertex"), J: n3.getAttribLocation(r2, "aTex"), ma: i2 }, t4.u = n3.createBuffer(), n3.bindBuffer(n3.ARRAY_BUFFER, t4.u), n3.enableVertexAttribArray(t4.j.K), n3.vertexAttribPointer(t4.j.K, 2, n3.FLOAT, false, 0, 0), n3.bufferData(n3.ARRAY_BUFFER, new Float32Array([-1, -1, -1, 1, 1, 1, 1, -1]), n3.STATIC_DRAW), n3.bindBuffer(n3.ARRAY_BUFFER, null), t4.s = n3.createBuffer(), n3.bindBuffer(n3.ARRAY_BUFFER, t4.s), n3.enableVertexAttribArray(t4.j.J), n3.vertexAttribPointer(t4.j.J, 2, n3.FLOAT, false, 0, 0), n3.bufferData(n3.ARRAY_BUFFER, new Float32Array([0, 1, 0, 0, 1, 0, 1, 1]), n3.STATIC_DRAW), n3.bindBuffer(n3.ARRAY_BUFFER, null), n3.uniform1i(i2, 0);
      }
      r2 = t4.j, n3.useProgram(t4.o), n3.canvas.width = e3.width, n3.canvas.height = e3.height, n3.viewport(0, 0, e3.width, e3.height), n3.activeTexture(n3.TEXTURE0), t4.h.bindTexture2d(e3.glName), n3.enableVertexAttribArray(r2.K), n3.bindBuffer(n3.ARRAY_BUFFER, t4.u), n3.vertexAttribPointer(r2.K, 2, n3.FLOAT, false, 0, 0), n3.enableVertexAttribArray(r2.J), n3.bindBuffer(n3.ARRAY_BUFFER, t4.s), n3.vertexAttribPointer(r2.J, 2, n3.FLOAT, false, 0, 0), n3.bindFramebuffer(n3.DRAW_FRAMEBUFFER ? n3.DRAW_FRAMEBUFFER : n3.FRAMEBUFFER, null), n3.clearColor(0, 0, 0, 0), n3.clear(n3.COLOR_BUFFER_BIT), n3.colorMask(true, true, true, true), n3.drawArrays(n3.TRIANGLE_FAN, 0, 4), n3.disableVertexAttribArray(r2.K), n3.disableVertexAttribArray(r2.J), n3.bindBuffer(n3.ARRAY_BUFFER, null), t4.h.bindTexture2d(0);
    }(t3, e2), "function" == typeof t3.g.canvas.transferToImageBitmap ? Promise.resolve(t3.g.canvas.transferToImageBitmap()) : n2 ? Promise.resolve(t3.g.canvas) : "function" == typeof createImageBitmap ? createImageBitmap(t3.g.canvas) : (void 0 === t3.i && (t3.i = document.createElement("canvas")), new Promise(function(e3) {
      t3.i.height = t3.g.canvas.height, t3.i.width = t3.g.canvas.width, t3.i.getContext("2d", {}).drawImage(t3.g.canvas, 0, 0, t3.g.canvas.width, t3.g.canvas.height), e3(t3.i);
    }));
  }
  function Be(t3) {
    this.g = t3;
  }
  var De = new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1, 0, 10, 9, 1, 7, 0, 65, 0, 253, 15, 26, 11]);
  function Pe(t3, e2) {
    return e2 + t3;
  }
  function ze(t3, e2) {
    window[t3] = e2;
  }
  function Ue(t3) {
    if (this.g = t3, this.listeners = {}, this.j = {}, this.H = {}, this.o = {}, this.u = {}, this.I = this.s = this.Z = true, this.D = Promise.resolve(), this.Y = "", this.C = {}, this.locateFile = t3 && t3.locateFile || Pe, "object" == typeof window) var e2 = window.location.pathname.toString().substring(0, window.location.pathname.toString().lastIndexOf("/")) + "/";
    else {
      if ("undefined" == typeof location) throw Error("solutions can only be loaded on a web page or in a web worker");
      e2 = location.pathname.toString().substring(0, location.pathname.toString().lastIndexOf("/")) + "/";
    }
    if (this.$ = e2, t3.options) for (var n2 = (e2 = a(Object.keys(t3.options))).next(); !n2.done; n2 = e2.next()) {
      n2 = n2.value;
      var r2 = t3.options[n2].default;
      void 0 !== r2 && (this.j[n2] = "function" == typeof r2 ? r2() : r2);
    }
  }
  function Ne(t3) {
    var e2, n2, r2, i2, o2, a2, s2, c2, h2, l2, f2;
    return M(function(d2) {
      switch (d2.g) {
        case 1:
          return t3.Z ? (e2 = void 0 === t3.g.files ? [] : "function" == typeof t3.g.files ? t3.g.files(t3.j) : t3.g.files, m(d2, M(function(t4) {
            switch (t4.g) {
              case 1:
                return t4.o = 2, m(t4, WebAssembly.instantiate(De), 4);
              case 4:
                t4.g = 3, t4.o = 0;
                break;
              case 2:
                return t4.o = 0, t4.j = null, t4.return(false);
              case 3:
                return t4.return(true);
            }
          }), 2)) : d2.return();
        case 2:
          if (n2 = d2.h, "object" == typeof window) return ze("createMediapipeSolutionsWasm", { locateFile: t3.locateFile }), ze("createMediapipeSolutionsPackedAssets", { locateFile: t3.locateFile }), a2 = e2.filter(function(t4) {
            return void 0 !== t4.data;
          }), s2 = e2.filter(function(t4) {
            return void 0 === t4.data;
          }), c2 = Promise.all(a2.map(function(e3) {
            var n3 = Ve(t3, e3.url);
            if (void 0 !== e3.path) {
              var r3 = e3.path;
              n3 = n3.then(function(e4) {
                return t3.overrideFile(r3, e4), Promise.resolve(e4);
              });
            }
            return n3;
          })), h2 = Promise.all(s2.map(function(e3) {
            return void 0 === e3.simd || e3.simd && n2 || !e3.simd && !n2 ? function(t4) {
              var e4 = document.createElement("script");
              return e4.setAttribute("src", t4), e4.setAttribute("crossorigin", "anonymous"), new Promise(function(t5) {
                e4.addEventListener("load", function() {
                  t5();
                }, false), e4.addEventListener("error", function() {
                  t5();
                }, false), document.body.appendChild(e4);
              });
            }(t3.locateFile(e3.url, t3.$)) : Promise.resolve();
          })).then(function() {
            var e3, n3, r3;
            return M(function(i3) {
              if (1 == i3.g) return e3 = window.createMediapipeSolutionsWasm, n3 = window.createMediapipeSolutionsPackedAssets, r3 = t3, m(i3, e3(n3), 2);
              r3.h = i3.h, i3.g = 0;
            });
          }), l2 = M(function(e3) {
            return t3.g.graph && t3.g.graph.url ? e3 = m(e3, Ve(t3, t3.g.graph.url), 0) : (e3.g = 0, e3 = void 0), e3;
          }), m(d2, Promise.all([h2, c2, l2]), 7);
          if ("function" != typeof importScripts) throw Error("solutions can only be loaded on a web page or in a web worker");
          return r2 = e2.filter(function(t4) {
            return void 0 === t4.simd || t4.simd && n2 || !t4.simd && !n2;
          }).map(function(e3) {
            return t3.locateFile(e3.url, t3.$);
          }), importScripts.apply(null, u(r2)), i2 = t3, m(d2, createMediapipeSolutionsWasm(Module), 6);
        case 6:
          i2.h = d2.h, t3.l = new OffscreenCanvas(1, 1), t3.h.canvas = t3.l, o2 = t3.h.GL.createContext(t3.l, { antialias: false, alpha: false, ja: "undefined" != typeof WebGL2RenderingContext ? 2 : 1 }), t3.h.GL.makeContextCurrent(o2), d2.g = 4;
          break;
        case 7:
          if (t3.l = document.createElement("canvas"), !(f2 = t3.l.getContext("webgl2", {})) && !(f2 = t3.l.getContext("webgl", {}))) return alert("Failed to create WebGL canvas context when passing video frame."), d2.return();
          t3.G = f2, t3.h.canvas = t3.l, t3.h.createContext(t3.l, true, true, {});
        case 4:
          t3.i = new t3.h.SolutionWasm(), t3.Z = false, d2.g = 0;
      }
    });
  }
  function Ve(t3, e2) {
    var n2, r2;
    return M(function(i2) {
      return e2 in t3.H ? i2.return(t3.H[e2]) : (n2 = t3.locateFile(e2, ""), r2 = fetch(n2).then(function(t4) {
        return t4.arrayBuffer();
      }), t3.H[e2] = r2, i2.return(r2));
    });
  }
  function He(t3, e2, n2) {
    var r2, i2, o2, u2, s2, c2, h2, l2, f2, d2, p2, g2, v2, y2;
    return M(function(w2) {
      switch (w2.g) {
        case 1:
          if (!n2) return w2.return(e2);
          for (r2 = {}, i2 = 0, o2 = a(Object.keys(n2)), u2 = o2.next(); !u2.done; u2 = o2.next()) s2 = u2.value, "string" != typeof (c2 = n2[s2]) && "texture" === c2.type && void 0 !== e2[c2.stream] && ++i2;
          1 < i2 && (t3.I = false), h2 = a(Object.keys(n2)), u2 = h2.next();
        case 2:
          if (u2.done) {
            w2.g = 4;
            break;
          }
          if (l2 = u2.value, "string" == typeof (f2 = n2[l2])) return v2 = r2, y2 = l2, m(w2, function(t4, e3, n3) {
            var r3;
            return M(function(i3) {
              return "number" == typeof n3 || n3 instanceof Uint8Array || n3 instanceof t4.h.Uint8BlobList ? i3.return(n3) : n3 instanceof t4.h.Texture2dDataOut ? ((r3 = t4.u[e3]) || (r3 = new Ie(t4.h, t4.G), t4.u[e3] = r3), i3.return(Le(r3, n3, t4.I))) : i3.return(void 0);
            });
          }(t3, l2, e2[f2]), 14);
          if (d2 = e2[f2.stream], "detection_list" === f2.type) {
            if (d2) {
              for (var b2 = d2.getRectList(), x2 = d2.getLandmarksList(), A2 = d2.getClassificationsList(), T2 = [], E3 = 0; E3 < b2.size(); ++E3) {
                var S3 = ee(b2.get(E3), Me, Ae);
                S3 = { boundingBox: { xCenter: bt2(S3, 1), yCenter: bt2(S3, 2), height: bt2(S3, 3), width: bt2(S3, 4), rotation: bt2(S3, 5, 0), rectId: At2(S3, 6) }, landmarks: xt2(ee(x2.get(E3), we, be), me, 1).map(Re), V: ke(ee(A2.get(E3), pe, ge)) }, T2.push(S3);
              }
              b2 = T2;
            } else b2 = [];
            r2[l2] = b2, w2.g = 7;
            break;
          }
          if ("proto_list" === f2.type) {
            if (d2) {
              for (b2 = Array(d2.size()), x2 = 0; x2 < d2.size(); x2++) b2[x2] = d2.get(x2);
              d2.delete();
            } else b2 = [];
            r2[l2] = b2, w2.g = 7;
            break;
          }
          if (void 0 === d2) {
            w2.g = 3;
            break;
          }
          if ("float_list" === f2.type) {
            r2[l2] = d2, w2.g = 7;
            break;
          }
          if ("proto" === f2.type) {
            r2[l2] = d2, w2.g = 7;
            break;
          }
          if ("texture" !== f2.type) throw Error("Unknown output config type: '" + f2.type + "'");
          return (p2 = t3.u[l2]) || (p2 = new Ie(t3.h, t3.G), t3.u[l2] = p2), m(w2, Le(p2, d2, t3.I), 13);
        case 13:
          g2 = w2.h, r2[l2] = g2;
        case 7:
          f2.transform && r2[l2] && (r2[l2] = f2.transform(r2[l2])), w2.g = 3;
          break;
        case 14:
          v2[y2] = w2.h;
        case 3:
          u2 = h2.next(), w2.g = 2;
          break;
        case 4:
          return w2.return(r2);
      }
    });
  }
  function Ke(t3, e2) {
    for (var n2 = e2.name || "$", r2 = [].concat(u(e2.wants)), i2 = new t3.h.StringList(), o2 = a(e2.wants), s2 = o2.next(); !s2.done; s2 = o2.next()) i2.push_back(s2.value);
    o2 = t3.h.PacketListener.implement({ onResults: function(i3) {
      for (var o3 = {}, a2 = 0; a2 < e2.wants.length; ++a2) o3[r2[a2]] = i3.get(a2);
      var u2 = t3.listeners[n2];
      u2 && (t3.D = He(t3, o3, e2.outs).then(function(n3) {
        n3 = u2(n3);
        for (var i4 = 0; i4 < e2.wants.length; ++i4) {
          var a3 = o3[r2[i4]];
          "object" == typeof a3 && a3.hasOwnProperty && a3.hasOwnProperty("delete") && a3.delete();
        }
        n3 && (t3.D = n3);
      }));
    } }), t3.i.attachMultiListener(i2, o2), i2.delete();
  }
  function We(t3) {
    var e2 = this;
    t3 = t3 || {};
    var n2 = { url: "face_detection_short.binarypb" }, r2 = { type: 1, graphOptionXref: { calculatorType: "TensorsToDetectionsCalculator", calculatorName: "facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator", fieldName: "min_score_thresh" } };
    this.g = new Ue({ locateFile: t3.locateFile, files: [{ data: true, url: "face_detection_short.binarypb" }, { data: true, url: "face_detection_short_range.tflite" }, { simd: true, url: "face_detection_solution_simd_wasm_bin.js" }, { simd: false, url: "face_detection_solution_wasm_bin.js" }], graph: n2, listeners: [{ wants: ["detections", "image_transformed"], outs: { image: "image_transformed", detections: { type: "detection_list", stream: "detections" } } }], inputs: { image: { type: "video", stream: "input_frames_gpu" } }, options: { useCpuInference: { type: 0, graphOptionXref: { calculatorType: "InferenceCalculator", fieldName: "use_cpu_inference" }, default: "object" == typeof window && void 0 !== window.navigator && ("iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform) || navigator.userAgent.includes("Mac") && "ontouchend" in document) }, selfieMode: { type: 0, graphOptionXref: { calculatorType: "GlScalerCalculator", calculatorIndex: 1, fieldName: "flip_horizontal" } }, model: { type: 0, onChange: function(t4) {
      var i2, o2, u2, s2, c2;
      return M(function(h2) {
        switch (h2.g) {
          case 1:
            i2 = a("short" === t4 ? ["face_detection_short_range.tflite"] : ["face_detection_full_range_sparse.tflite"]), o2 = i2.next();
          case 2:
            if (o2.done) {
              h2.g = 4;
              break;
            }
            return u2 = o2.value, s2 = "third_party/mediapipe/modules/face_detection/" + u2, m(h2, Ve(e2.g, u2), 5);
          case 5:
            c2 = h2.h, e2.g.overrideFile(s2, c2), o2 = i2.next(), h2.g = 2;
            break;
          case 4:
            return n2.url = "short" === t4 ? "face_detection_short.binarypb" : "face_detection_full.binarypb", r2.graphOptionXref.calculatorName = "short" === t4 ? "facedetectionshortrangegpu__facedetectionshortrangecommon__TensorsToDetectionsCalculator" : "facedetectionfullrangegpu__facedetectionfullrangecommon__TensorsToDetectionsCalculator", h2.return(true);
        }
      });
    } }, minDetectionConfidence: r2 } });
  }
  (t2 = Ue.prototype).close = function() {
    return this.i && this.i.delete(), Promise.resolve();
  }, t2.reset = function() {
    var t3 = this;
    return M(function(e2) {
      t3.i && (t3.i.reset(), t3.o = {}, t3.u = {}), e2.g = 0;
    });
  }, t2.setOptions = function(t3, e2) {
    var n2 = this;
    if (e2 = e2 || this.g.options) {
      for (var r2 = [], i2 = [], o2 = {}, u2 = a(Object.keys(t3)), s2 = u2.next(); !s2.done; o2 = { R: o2.R, S: o2.S }, s2 = u2.next()) {
        var c2 = s2.value;
        c2 in this.j && this.j[c2] === t3[c2] || (this.j[c2] = t3[c2], void 0 !== (s2 = e2[c2]) && (s2.onChange && (o2.R = s2.onChange, o2.S = t3[c2], r2.push(/* @__PURE__ */ function(t4) {
          return function() {
            return M(function(e3) {
              if (1 == e3.g) return m(e3, t4.R(t4.S), 2);
              true === e3.h && (n2.s = true), e3.g = 0;
            });
          };
        }(o2))), s2.graphOptionXref && (c2 = { valueNumber: 1 === s2.type ? t3[c2] : 0, valueBoolean: 0 === s2.type && t3[c2], valueString: 2 === s2.type ? t3[c2] : "" }, s2 = Object.assign(Object.assign(Object.assign({}, { calculatorName: "", calculatorIndex: 0 }), s2.graphOptionXref), c2), i2.push(s2))));
      }
      0 === r2.length && 0 === i2.length || (this.s = true, this.B = (void 0 === this.B ? [] : this.B).concat(i2), this.A = (void 0 === this.A ? [] : this.A).concat(r2));
    }
  }, t2.initialize = function() {
    var t3 = this;
    return M(function(e2) {
      return 1 == e2.g ? m(e2, Ne(t3), 2) : 3 != e2.g ? m(e2, function(t4) {
        var e3, n2, r2, i2, o2, u2, s2, c2;
        return M(function(h2) {
          if (1 == h2.g) return t4.g.graph && t4.g.graph.url && t4.Y === t4.g.graph.url ? h2.return() : (t4.s = true, t4.g.graph && t4.g.graph.url ? (t4.Y = t4.g.graph.url, m(h2, Ve(t4, t4.g.graph.url), 3)) : void (h2.g = 2));
          for (2 != h2.g && (e3 = h2.h, t4.i.loadGraph(e3)), n2 = a(Object.keys(t4.C)), r2 = n2.next(); !r2.done; r2 = n2.next()) i2 = r2.value, t4.i.overrideFile(i2, t4.C[i2]);
          if (t4.C = {}, t4.g.listeners) for (o2 = a(t4.g.listeners), u2 = o2.next(); !u2.done; u2 = o2.next()) s2 = u2.value, Ke(t4, s2);
          c2 = t4.j, t4.j = {}, t4.setOptions(c2), h2.g = 0;
        });
      }(t3), 3) : m(e2, function(t4) {
        var e3, n2, r2, i2, o2, u2;
        return M(function(s2) {
          switch (s2.g) {
            case 1:
              if (!t4.s) return s2.return();
              if (!t4.A) {
                s2.g = 2;
                break;
              }
              e3 = a(t4.A), n2 = e3.next();
            case 3:
              if (n2.done) {
                s2.g = 5;
                break;
              }
              return m(s2, (0, n2.value)(), 4);
            case 4:
              n2 = e3.next(), s2.g = 3;
              break;
            case 5:
              t4.A = void 0;
            case 2:
              if (t4.B) {
                for (r2 = new t4.h.GraphOptionChangeRequestList(), i2 = a(t4.B), o2 = i2.next(); !o2.done; o2 = i2.next()) u2 = o2.value, r2.push_back(u2);
                t4.i.changeOptions(r2), r2.delete(), t4.B = void 0;
              }
              t4.s = false, s2.g = 0;
          }
        });
      }(t3), 0);
    });
  }, t2.overrideFile = function(t3, e2) {
    this.i ? this.i.overrideFile(t3, e2) : this.C[t3] = e2;
  }, t2.clearOverriddenFiles = function() {
    this.C = {}, this.i && this.i.clearOverriddenFiles();
  }, t2.send = function(t3, e2) {
    var n2, r2, i2, o2, u2, s2, c2, h2, l2, f2 = this;
    return M(function(d2) {
      switch (d2.g) {
        case 1:
          return f2.g.inputs ? (n2 = 1e3 * (null == e2 ? performance.now() : e2), m(d2, f2.D, 2)) : d2.return();
        case 2:
          return m(d2, f2.initialize(), 3);
        case 3:
          for (r2 = new f2.h.PacketDataList(), i2 = a(Object.keys(t3)), o2 = i2.next(); !o2.done; o2 = i2.next()) if (u2 = o2.value, s2 = f2.g.inputs[u2]) {
            t: {
              var p2 = t3[u2];
              switch (s2.type) {
                case "video":
                  var g2 = f2.o[s2.stream];
                  if (g2 || (g2 = new Ie(f2.h, f2.G), f2.o[s2.stream] = g2), 0 === g2.l && (g2.l = g2.h.createTexture()), "undefined" != typeof HTMLVideoElement && p2 instanceof HTMLVideoElement) var v2 = p2.videoWidth, y2 = p2.videoHeight;
                  else "undefined" != typeof HTMLImageElement && p2 instanceof HTMLImageElement ? (v2 = p2.naturalWidth, y2 = p2.naturalHeight) : (v2 = p2.width, y2 = p2.height);
                  y2 = { glName: g2.l, width: v2, height: y2 }, (v2 = g2.g).canvas.width = y2.width, v2.canvas.height = y2.height, v2.activeTexture(v2.TEXTURE0), g2.h.bindTexture2d(g2.l), v2.texImage2D(v2.TEXTURE_2D, 0, v2.RGBA, v2.RGBA, v2.UNSIGNED_BYTE, p2), g2.h.bindTexture2d(0), g2 = y2;
                  break t;
                case "detections":
                  for ((g2 = f2.o[s2.stream]) || (g2 = new Be(f2.h), f2.o[s2.stream] = g2), g2.data || (g2.data = new g2.g.DetectionListData()), g2.data.reset(p2.length), y2 = 0; y2 < p2.length; ++y2) {
                    v2 = p2[y2];
                    var w2 = g2.data, b2 = w2.setBoundingBox, x2 = y2, M2 = v2.boundingBox, A2 = new Me();
                    if (yt2(A2, 1, M2.xCenter), yt2(A2, 2, M2.yCenter), yt2(A2, 3, M2.height), yt2(A2, 4, M2.width), yt2(A2, 5, M2.rotation), yt2(A2, 6, M2.rectId), M2 = re(A2, Ae), b2.call(w2, x2, M2), v2.landmarks) for (w2 = 0; w2 < v2.landmarks.length; ++w2) {
                      var T2 = !!(A2 = v2.landmarks[w2]).visibility;
                      x2 = (b2 = g2.data).addNormalizedLandmark, M2 = y2, A2 = Object.assign(Object.assign({}, A2), { visibility: T2 ? A2.visibility : 0 }), yt2(T2 = new me(), 1, A2.x), yt2(T2, 2, A2.y), yt2(T2, 3, A2.z), A2.visibility && yt2(T2, 4, A2.visibility), A2 = re(T2, ye), x2.call(b2, M2, A2);
                    }
                    if (v2.V) for (w2 = 0; w2 < v2.V.length; ++w2) x2 = (b2 = g2.data).addClassification, M2 = y2, A2 = v2.V[w2], yt2(T2 = new fe(), 2, A2.ga), A2.index && yt2(T2, 1, A2.index), A2.label && yt2(T2, 3, A2.label), A2.displayName && yt2(T2, 4, A2.displayName), A2 = re(T2, de), x2.call(b2, M2, A2);
                  }
                  g2 = g2.data;
                  break t;
                default:
                  g2 = {};
              }
            }
            switch (c2 = g2, h2 = s2.stream, s2.type) {
              case "video":
                r2.pushTexture2d(Object.assign(Object.assign({}, c2), { stream: h2, timestamp: n2 }));
                break;
              case "detections":
                (l2 = c2).stream = h2, l2.timestamp = n2, r2.pushDetectionList(l2);
                break;
              default:
                throw Error("Unknown input config type: '" + s2.type + "'");
            }
          }
          return f2.i.send(r2), m(d2, f2.D, 4);
        case 4:
          r2.delete(), d2.g = 0;
      }
    });
  }, t2.onResults = function(t3, e2) {
    this.listeners[e2 || "$"] = t3;
  }, E2("Solution", Ue), E2("OptionType", { BOOL: 0, NUMBER: 1, ia: 2, 0: "BOOL", 1: "NUMBER", 2: "STRING" }), (t2 = We.prototype).close = function() {
    return this.g.close(), Promise.resolve();
  }, t2.onResults = function(t3) {
    this.g.onResults(t3);
  }, t2.initialize = function() {
    var t3 = this;
    return M(function(e2) {
      return m(e2, t3.g.initialize(), 0);
    });
  }, t2.reset = function() {
    this.g.reset();
  }, t2.send = function(t3) {
    var e2 = this;
    return M(function(n2) {
      return m(n2, e2.g.send(t3), 0);
    });
  }, t2.setOptions = function(t3) {
    this.g.setOptions(t3);
  }, E2("FaceDetection", We), E2("FACEDETECTION_LIPS", Te), E2("FACEDETECTION_LEFT_EYE", Ee), E2("FACEDETECTION_LEFT_EYEBROW", Se), E2("FACEDETECTION_RIGHT_EYE", Fe), E2("FACEDETECTION_RIGHT_EYEBROW", Ce), E2("FACEDETECTION_FACE_OVAL", Oe), E2("FACEDETECTION_CONTOURS", _e), E2("FACEDETECTION_TESSELATION", [[127, 34], [34, 139], [139, 127], [11, 0], [0, 37], [37, 11], [232, 231], [231, 120], [120, 232], [72, 37], [37, 39], [39, 72], [128, 121], [121, 47], [47, 128], [232, 121], [121, 128], [128, 232], [104, 69], [69, 67], [67, 104], [175, 171], [171, 148], [148, 175], [118, 50], [50, 101], [101, 118], [73, 39], [39, 40], [40, 73], [9, 151], [151, 108], [108, 9], [48, 115], [115, 131], [131, 48], [194, 204], [204, 211], [211, 194], [74, 40], [40, 185], [185, 74], [80, 42], [42, 183], [183, 80], [40, 92], [92, 186], [186, 40], [230, 229], [229, 118], [118, 230], [202, 212], [212, 214], [214, 202], [83, 18], [18, 17], [17, 83], [76, 61], [61, 146], [146, 76], [160, 29], [29, 30], [30, 160], [56, 157], [157, 173], [173, 56], [106, 204], [204, 194], [194, 106], [135, 214], [214, 192], [192, 135], [203, 165], [165, 98], [98, 203], [21, 71], [71, 68], [68, 21], [51, 45], [45, 4], [4, 51], [144, 24], [24, 23], [23, 144], [77, 146], [146, 91], [91, 77], [205, 50], [50, 187], [187, 205], [201, 200], [200, 18], [18, 201], [91, 106], [106, 182], [182, 91], [90, 91], [91, 181], [181, 90], [85, 84], [84, 17], [17, 85], [206, 203], [203, 36], [36, 206], [148, 171], [171, 140], [140, 148], [92, 40], [40, 39], [39, 92], [193, 189], [189, 244], [244, 193], [159, 158], [158, 28], [28, 159], [247, 246], [246, 161], [161, 247], [236, 3], [3, 196], [196, 236], [54, 68], [68, 104], [104, 54], [193, 168], [168, 8], [8, 193], [117, 228], [228, 31], [31, 117], [189, 193], [193, 55], [55, 189], [98, 97], [97, 99], [99, 98], [126, 47], [47, 100], [100, 126], [166, 79], [79, 218], [218, 166], [155, 154], [154, 26], [26, 155], [209, 49], [49, 131], [131, 209], [135, 136], [136, 150], [150, 135], [47, 126], [126, 217], [217, 47], [223, 52], [52, 53], [53, 223], [45, 51], [51, 134], [134, 45], [211, 170], [170, 140], [140, 211], [67, 69], [69, 108], [108, 67], [43, 106], [106, 91], [91, 43], [230, 119], [119, 120], [120, 230], [226, 130], [130, 247], [247, 226], [63, 53], [53, 52], [52, 63], [238, 20], [20, 242], [242, 238], [46, 70], [70, 156], [156, 46], [78, 62], [62, 96], [96, 78], [46, 53], [53, 63], [63, 46], [143, 34], [34, 227], [227, 143], [123, 117], [117, 111], [111, 123], [44, 125], [125, 19], [19, 44], [236, 134], [134, 51], [51, 236], [216, 206], [206, 205], [205, 216], [154, 153], [153, 22], [22, 154], [39, 37], [37, 167], [167, 39], [200, 201], [201, 208], [208, 200], [36, 142], [142, 100], [100, 36], [57, 212], [212, 202], [202, 57], [20, 60], [60, 99], [99, 20], [28, 158], [158, 157], [157, 28], [35, 226], [226, 113], [113, 35], [160, 159], [159, 27], [27, 160], [204, 202], [202, 210], [210, 204], [113, 225], [225, 46], [46, 113], [43, 202], [202, 204], [204, 43], [62, 76], [76, 77], [77, 62], [137, 123], [123, 116], [116, 137], [41, 38], [38, 72], [72, 41], [203, 129], [129, 142], [142, 203], [64, 98], [98, 240], [240, 64], [49, 102], [102, 64], [64, 49], [41, 73], [73, 74], [74, 41], [212, 216], [216, 207], [207, 212], [42, 74], [74, 184], [184, 42], [169, 170], [170, 211], [211, 169], [170, 149], [149, 176], [176, 170], [105, 66], [66, 69], [69, 105], [122, 6], [6, 168], [168, 122], [123, 147], [147, 187], [187, 123], [96, 77], [77, 90], [90, 96], [65, 55], [55, 107], [107, 65], [89, 90], [90, 180], [180, 89], [101, 100], [100, 120], [120, 101], [63, 105], [105, 104], [104, 63], [93, 137], [137, 227], [227, 93], [15, 86], [86, 85], [85, 15], [129, 102], [102, 49], [49, 129], [14, 87], [87, 86], [86, 14], [55, 8], [8, 9], [9, 55], [100, 47], [47, 121], [121, 100], [145, 23], [23, 22], [22, 145], [88, 89], [89, 179], [179, 88], [6, 122], [122, 196], [196, 6], [88, 95], [95, 96], [96, 88], [138, 172], [172, 136], [136, 138], [215, 58], [58, 172], [172, 215], [115, 48], [48, 219], [219, 115], [42, 80], [80, 81], [81, 42], [195, 3], [3, 51], [51, 195], [43, 146], [146, 61], [61, 43], [171, 175], [175, 199], [199, 171], [81, 82], [82, 38], [38, 81], [53, 46], [46, 225], [225, 53], [144, 163], [163, 110], [110, 144], [52, 65], [65, 66], [66, 52], [229, 228], [228, 117], [117, 229], [34, 127], [127, 234], [234, 34], [107, 108], [108, 69], [69, 107], [109, 108], [108, 151], [151, 109], [48, 64], [64, 235], [235, 48], [62, 78], [78, 191], [191, 62], [129, 209], [209, 126], [126, 129], [111, 35], [35, 143], [143, 111], [117, 123], [123, 50], [50, 117], [222, 65], [65, 52], [52, 222], [19, 125], [125, 141], [141, 19], [221, 55], [55, 65], [65, 221], [3, 195], [195, 197], [197, 3], [25, 7], [7, 33], [33, 25], [220, 237], [237, 44], [44, 220], [70, 71], [71, 139], [139, 70], [122, 193], [193, 245], [245, 122], [247, 130], [130, 33], [33, 247], [71, 21], [21, 162], [162, 71], [170, 169], [169, 150], [150, 170], [188, 174], [174, 196], [196, 188], [216, 186], [186, 92], [92, 216], [2, 97], [97, 167], [167, 2], [141, 125], [125, 241], [241, 141], [164, 167], [167, 37], [37, 164], [72, 38], [38, 12], [12, 72], [38, 82], [82, 13], [13, 38], [63, 68], [68, 71], [71, 63], [226, 35], [35, 111], [111, 226], [101, 50], [50, 205], [205, 101], [206, 92], [92, 165], [165, 206], [209, 198], [198, 217], [217, 209], [165, 167], [167, 97], [97, 165], [220, 115], [115, 218], [218, 220], [133, 112], [112, 243], [243, 133], [239, 238], [238, 241], [241, 239], [214, 135], [135, 169], [169, 214], [190, 173], [173, 133], [133, 190], [171, 208], [208, 32], [32, 171], [125, 44], [44, 237], [237, 125], [86, 87], [87, 178], [178, 86], [85, 86], [86, 179], [179, 85], [84, 85], [85, 180], [180, 84], [83, 84], [84, 181], [181, 83], [201, 83], [83, 182], [182, 201], [137, 93], [93, 132], [132, 137], [76, 62], [62, 183], [183, 76], [61, 76], [76, 184], [184, 61], [57, 61], [61, 185], [185, 57], [212, 57], [57, 186], [186, 212], [214, 207], [207, 187], [187, 214], [34, 143], [143, 156], [156, 34], [79, 239], [239, 237], [237, 79], [123, 137], [137, 177], [177, 123], [44, 1], [1, 4], [4, 44], [201, 194], [194, 32], [32, 201], [64, 102], [102, 129], [129, 64], [213, 215], [215, 138], [138, 213], [59, 166], [166, 219], [219, 59], [242, 99], [99, 97], [97, 242], [2, 94], [94, 141], [141, 2], [75, 59], [59, 235], [235, 75], [24, 110], [110, 228], [228, 24], [25, 130], [130, 226], [226, 25], [23, 24], [24, 229], [229, 23], [22, 23], [23, 230], [230, 22], [26, 22], [22, 231], [231, 26], [112, 26], [26, 232], [232, 112], [189, 190], [190, 243], [243, 189], [221, 56], [56, 190], [190, 221], [28, 56], [56, 221], [221, 28], [27, 28], [28, 222], [222, 27], [29, 27], [27, 223], [223, 29], [30, 29], [29, 224], [224, 30], [247, 30], [30, 225], [225, 247], [238, 79], [79, 20], [20, 238], [166, 59], [59, 75], [75, 166], [60, 75], [75, 240], [240, 60], [147, 177], [177, 215], [215, 147], [20, 79], [79, 166], [166, 20], [187, 147], [147, 213], [213, 187], [112, 233], [233, 244], [244, 112], [233, 128], [128, 245], [245, 233], [128, 114], [114, 188], [188, 128], [114, 217], [217, 174], [174, 114], [131, 115], [115, 220], [220, 131], [217, 198], [198, 236], [236, 217], [198, 131], [131, 134], [134, 198], [177, 132], [132, 58], [58, 177], [143, 35], [35, 124], [124, 143], [110, 163], [163, 7], [7, 110], [228, 110], [110, 25], [25, 228], [356, 389], [389, 368], [368, 356], [11, 302], [302, 267], [267, 11], [452, 350], [350, 349], [349, 452], [302, 303], [303, 269], [269, 302], [357, 343], [343, 277], [277, 357], [452, 453], [453, 357], [357, 452], [333, 332], [332, 297], [297, 333], [175, 152], [152, 377], [377, 175], [347, 348], [348, 330], [330, 347], [303, 304], [304, 270], [270, 303], [9, 336], [336, 337], [337, 9], [278, 279], [279, 360], [360, 278], [418, 262], [262, 431], [431, 418], [304, 408], [408, 409], [409, 304], [310, 415], [415, 407], [407, 310], [270, 409], [409, 410], [410, 270], [450, 348], [348, 347], [347, 450], [422, 430], [430, 434], [434, 422], [313, 314], [314, 17], [17, 313], [306, 307], [307, 375], [375, 306], [387, 388], [388, 260], [260, 387], [286, 414], [414, 398], [398, 286], [335, 406], [406, 418], [418, 335], [364, 367], [367, 416], [416, 364], [423, 358], [358, 327], [327, 423], [251, 284], [284, 298], [298, 251], [281, 5], [5, 4], [4, 281], [373, 374], [374, 253], [253, 373], [307, 320], [320, 321], [321, 307], [425, 427], [427, 411], [411, 425], [421, 313], [313, 18], [18, 421], [321, 405], [405, 406], [406, 321], [320, 404], [404, 405], [405, 320], [315, 16], [16, 17], [17, 315], [426, 425], [425, 266], [266, 426], [377, 400], [400, 369], [369, 377], [322, 391], [391, 269], [269, 322], [417, 465], [465, 464], [464, 417], [386, 257], [257, 258], [258, 386], [466, 260], [260, 388], [388, 466], [456, 399], [399, 419], [419, 456], [284, 332], [332, 333], [333, 284], [417, 285], [285, 8], [8, 417], [346, 340], [340, 261], [261, 346], [413, 441], [441, 285], [285, 413], [327, 460], [460, 328], [328, 327], [355, 371], [371, 329], [329, 355], [392, 439], [439, 438], [438, 392], [382, 341], [341, 256], [256, 382], [429, 420], [420, 360], [360, 429], [364, 394], [394, 379], [379, 364], [277, 343], [343, 437], [437, 277], [443, 444], [444, 283], [283, 443], [275, 440], [440, 363], [363, 275], [431, 262], [262, 369], [369, 431], [297, 338], [338, 337], [337, 297], [273, 375], [375, 321], [321, 273], [450, 451], [451, 349], [349, 450], [446, 342], [342, 467], [467, 446], [293, 334], [334, 282], [282, 293], [458, 461], [461, 462], [462, 458], [276, 353], [353, 383], [383, 276], [308, 324], [324, 325], [325, 308], [276, 300], [300, 293], [293, 276], [372, 345], [345, 447], [447, 372], [352, 345], [345, 340], [340, 352], [274, 1], [1, 19], [19, 274], [456, 248], [248, 281], [281, 456], [436, 427], [427, 425], [425, 436], [381, 256], [256, 252], [252, 381], [269, 391], [391, 393], [393, 269], [200, 199], [199, 428], [428, 200], [266, 330], [330, 329], [329, 266], [287, 273], [273, 422], [422, 287], [250, 462], [462, 328], [328, 250], [258, 286], [286, 384], [384, 258], [265, 353], [353, 342], [342, 265], [387, 259], [259, 257], [257, 387], [424, 431], [431, 430], [430, 424], [342, 353], [353, 276], [276, 342], [273, 335], [335, 424], [424, 273], [292, 325], [325, 307], [307, 292], [366, 447], [447, 345], [345, 366], [271, 303], [303, 302], [302, 271], [423, 266], [266, 371], [371, 423], [294, 455], [455, 460], [460, 294], [279, 278], [278, 294], [294, 279], [271, 272], [272, 304], [304, 271], [432, 434], [434, 427], [427, 432], [272, 407], [407, 408], [408, 272], [394, 430], [430, 431], [431, 394], [395, 369], [369, 400], [400, 395], [334, 333], [333, 299], [299, 334], [351, 417], [417, 168], [168, 351], [352, 280], [280, 411], [411, 352], [325, 319], [319, 320], [320, 325], [295, 296], [296, 336], [336, 295], [319, 403], [403, 404], [404, 319], [330, 348], [348, 349], [349, 330], [293, 298], [298, 333], [333, 293], [323, 454], [454, 447], [447, 323], [15, 16], [16, 315], [315, 15], [358, 429], [429, 279], [279, 358], [14, 15], [15, 316], [316, 14], [285, 336], [336, 9], [9, 285], [329, 349], [349, 350], [350, 329], [374, 380], [380, 252], [252, 374], [318, 402], [402, 403], [403, 318], [6, 197], [197, 419], [419, 6], [318, 319], [319, 325], [325, 318], [367, 364], [364, 365], [365, 367], [435, 367], [367, 397], [397, 435], [344, 438], [438, 439], [439, 344], [272, 271], [271, 311], [311, 272], [195, 5], [5, 281], [281, 195], [273, 287], [287, 291], [291, 273], [396, 428], [428, 199], [199, 396], [311, 271], [271, 268], [268, 311], [283, 444], [444, 445], [445, 283], [373, 254], [254, 339], [339, 373], [282, 334], [334, 296], [296, 282], [449, 347], [347, 346], [346, 449], [264, 447], [447, 454], [454, 264], [336, 296], [296, 299], [299, 336], [338, 10], [10, 151], [151, 338], [278, 439], [439, 455], [455, 278], [292, 407], [407, 415], [415, 292], [358, 371], [371, 355], [355, 358], [340, 345], [345, 372], [372, 340], [346, 347], [347, 280], [280, 346], [442, 443], [443, 282], [282, 442], [19, 94], [94, 370], [370, 19], [441, 442], [442, 295], [295, 441], [248, 419], [419, 197], [197, 248], [263, 255], [255, 359], [359, 263], [440, 275], [275, 274], [274, 440], [300, 383], [383, 368], [368, 300], [351, 412], [412, 465], [465, 351], [263, 467], [467, 466], [466, 263], [301, 368], [368, 389], [389, 301], [395, 378], [378, 379], [379, 395], [412, 351], [351, 419], [419, 412], [436, 426], [426, 322], [322, 436], [2, 164], [164, 393], [393, 2], [370, 462], [462, 461], [461, 370], [164, 0], [0, 267], [267, 164], [302, 11], [11, 12], [12, 302], [268, 12], [12, 13], [13, 268], [293, 300], [300, 301], [301, 293], [446, 261], [261, 340], [340, 446], [330, 266], [266, 425], [425, 330], [426, 423], [423, 391], [391, 426], [429, 355], [355, 437], [437, 429], [391, 327], [327, 326], [326, 391], [440, 457], [457, 438], [438, 440], [341, 382], [382, 362], [362, 341], [459, 457], [457, 461], [461, 459], [434, 430], [430, 394], [394, 434], [414, 463], [463, 362], [362, 414], [396, 369], [369, 262], [262, 396], [354, 461], [461, 457], [457, 354], [316, 403], [403, 402], [402, 316], [315, 404], [404, 403], [403, 315], [314, 405], [405, 404], [404, 314], [313, 406], [406, 405], [405, 313], [421, 418], [418, 406], [406, 421], [366, 401], [401, 361], [361, 366], [306, 408], [408, 407], [407, 306], [291, 409], [409, 408], [408, 291], [287, 410], [410, 409], [409, 287], [432, 436], [436, 410], [410, 432], [434, 416], [416, 411], [411, 434], [264, 368], [368, 383], [383, 264], [309, 438], [438, 457], [457, 309], [352, 376], [376, 401], [401, 352], [274, 275], [275, 4], [4, 274], [421, 428], [428, 262], [262, 421], [294, 327], [327, 358], [358, 294], [433, 416], [416, 367], [367, 433], [289, 455], [455, 439], [439, 289], [462, 370], [370, 326], [326, 462], [2, 326], [326, 370], [370, 2], [305, 460], [460, 455], [455, 305], [254, 449], [449, 448], [448, 254], [255, 261], [261, 446], [446, 255], [253, 450], [450, 449], [449, 253], [252, 451], [451, 450], [450, 252], [256, 452], [452, 451], [451, 256], [341, 453], [453, 452], [452, 341], [413, 464], [464, 463], [463, 413], [441, 413], [413, 414], [414, 441], [258, 442], [442, 441], [441, 258], [257, 443], [443, 442], [442, 257], [259, 444], [444, 443], [443, 259], [260, 445], [445, 444], [444, 260], [467, 342], [342, 445], [445, 467], [459, 458], [458, 250], [250, 459], [289, 392], [392, 290], [290, 289], [290, 328], [328, 460], [460, 290], [376, 433], [433, 435], [435, 376], [250, 290], [290, 392], [392, 250], [411, 416], [416, 433], [433, 411], [341, 463], [463, 464], [464, 341], [453, 464], [464, 465], [465, 453], [357, 465], [465, 412], [412, 357], [343, 412], [412, 399], [399, 343], [360, 363], [363, 440], [440, 360], [437, 399], [399, 456], [456, 437], [420, 456], [456, 363], [363, 420], [401, 435], [435, 288], [288, 401], [372, 383], [383, 353], [353, 372], [339, 255], [255, 249], [249, 339], [448, 261], [261, 255], [255, 448], [133, 243], [243, 190], [190, 133], [133, 155], [155, 112], [112, 133], [33, 246], [246, 247], [247, 33], [33, 130], [130, 25], [25, 33], [398, 384], [384, 286], [286, 398], [362, 398], [398, 414], [414, 362], [362, 463], [463, 341], [341, 362], [263, 359], [359, 467], [467, 263], [263, 249], [249, 255], [255, 263], [466, 467], [467, 260], [260, 466], [75, 60], [60, 166], [166, 75], [238, 239], [239, 79], [79, 238], [162, 127], [127, 139], [139, 162], [72, 11], [11, 37], [37, 72], [121, 232], [232, 120], [120, 121], [73, 72], [72, 39], [39, 73], [114, 128], [128, 47], [47, 114], [233, 232], [232, 128], [128, 233], [103, 104], [104, 67], [67, 103], [152, 175], [175, 148], [148, 152], [119, 118], [118, 101], [101, 119], [74, 73], [73, 40], [40, 74], [107, 9], [9, 108], [108, 107], [49, 48], [48, 131], [131, 49], [32, 194], [194, 211], [211, 32], [184, 74], [74, 185], [185, 184], [191, 80], [80, 183], [183, 191], [185, 40], [40, 186], [186, 185], [119, 230], [230, 118], [118, 119], [210, 202], [202, 214], [214, 210], [84, 83], [83, 17], [17, 84], [77, 76], [76, 146], [146, 77], [161, 160], [160, 30], [30, 161], [190, 56], [56, 173], [173, 190], [182, 106], [106, 194], [194, 182], [138, 135], [135, 192], [192, 138], [129, 203], [203, 98], [98, 129], [54, 21], [21, 68], [68, 54], [5, 51], [51, 4], [4, 5], [145, 144], [144, 23], [23, 145], [90, 77], [77, 91], [91, 90], [207, 205], [205, 187], [187, 207], [83, 201], [201, 18], [18, 83], [181, 91], [91, 182], [182, 181], [180, 90], [90, 181], [181, 180], [16, 85], [85, 17], [17, 16], [205, 206], [206, 36], [36, 205], [176, 148], [148, 140], [140, 176], [165, 92], [92, 39], [39, 165], [245, 193], [193, 244], [244, 245], [27, 159], [159, 28], [28, 27], [30, 247], [247, 161], [161, 30], [174, 236], [236, 196], [196, 174], [103, 54], [54, 104], [104, 103], [55, 193], [193, 8], [8, 55], [111, 117], [117, 31], [31, 111], [221, 189], [189, 55], [55, 221], [240, 98], [98, 99], [99, 240], [142, 126], [126, 100], [100, 142], [219, 166], [166, 218], [218, 219], [112, 155], [155, 26], [26, 112], [198, 209], [209, 131], [131, 198], [169, 135], [135, 150], [150, 169], [114, 47], [47, 217], [217, 114], [224, 223], [223, 53], [53, 224], [220, 45], [45, 134], [134, 220], [32, 211], [211, 140], [140, 32], [109, 67], [67, 108], [108, 109], [146, 43], [43, 91], [91, 146], [231, 230], [230, 120], [120, 231], [113, 226], [226, 247], [247, 113], [105, 63], [63, 52], [52, 105], [241, 238], [238, 242], [242, 241], [124, 46], [46, 156], [156, 124], [95, 78], [78, 96], [96, 95], [70, 46], [46, 63], [63, 70], [116, 143], [143, 227], [227, 116], [116, 123], [123, 111], [111, 116], [1, 44], [44, 19], [19, 1], [3, 236], [236, 51], [51, 3], [207, 216], [216, 205], [205, 207], [26, 154], [154, 22], [22, 26], [165, 39], [39, 167], [167, 165], [199, 200], [200, 208], [208, 199], [101, 36], [36, 100], [100, 101], [43, 57], [57, 202], [202, 43], [242, 20], [20, 99], [99, 242], [56, 28], [28, 157], [157, 56], [124, 35], [35, 113], [113, 124], [29, 160], [160, 27], [27, 29], [211, 204], [204, 210], [210, 211], [124, 113], [113, 46], [46, 124], [106, 43], [43, 204], [204, 106], [96, 62], [62, 77], [77, 96], [227, 137], [137, 116], [116, 227], [73, 41], [41, 72], [72, 73], [36, 203], [203, 142], [142, 36], [235, 64], [64, 240], [240, 235], [48, 49], [49, 64], [64, 48], [42, 41], [41, 74], [74, 42], [214, 212], [212, 207], [207, 214], [183, 42], [42, 184], [184, 183], [210, 169], [169, 211], [211, 210], [140, 170], [170, 176], [176, 140], [104, 105], [105, 69], [69, 104], [193, 122], [122, 168], [168, 193], [50, 123], [123, 187], [187, 50], [89, 96], [96, 90], [90, 89], [66, 65], [65, 107], [107, 66], [179, 89], [89, 180], [180, 179], [119, 101], [101, 120], [120, 119], [68, 63], [63, 104], [104, 68], [234, 93], [93, 227], [227, 234], [16, 15], [15, 85], [85, 16], [209, 129], [129, 49], [49, 209], [15, 14], [14, 86], [86, 15], [107, 55], [55, 9], [9, 107], [120, 100], [100, 121], [121, 120], [153, 145], [145, 22], [22, 153], [178, 88], [88, 179], [179, 178], [197, 6], [6, 196], [196, 197], [89, 88], [88, 96], [96, 89], [135, 138], [138, 136], [136, 135], [138, 215], [215, 172], [172, 138], [218, 115], [115, 219], [219, 218], [41, 42], [42, 81], [81, 41], [5, 195], [195, 51], [51, 5], [57, 43], [43, 61], [61, 57], [208, 171], [171, 199], [199, 208], [41, 81], [81, 38], [38, 41], [224, 53], [53, 225], [225, 224], [24, 144], [144, 110], [110, 24], [105, 52], [52, 66], [66, 105], [118, 229], [229, 117], [117, 118], [227, 34], [34, 234], [234, 227], [66, 107], [107, 69], [69, 66], [10, 109], [109, 151], [151, 10], [219, 48], [48, 235], [235, 219], [183, 62], [62, 191], [191, 183], [142, 129], [129, 126], [126, 142], [116, 111], [111, 143], [143, 116], [118, 117], [117, 50], [50, 118], [223, 222], [222, 52], [52, 223], [94, 19], [19, 141], [141, 94], [222, 221], [221, 65], [65, 222], [196, 3], [3, 197], [197, 196], [45, 220], [220, 44], [44, 45], [156, 70], [70, 139], [139, 156], [188, 122], [122, 245], [245, 188], [139, 71], [71, 162], [162, 139], [149, 170], [170, 150], [150, 149], [122, 188], [188, 196], [196, 122], [206, 216], [216, 92], [92, 206], [164, 2], [2, 167], [167, 164], [242, 141], [141, 241], [241, 242], [0, 164], [164, 37], [37, 0], [11, 72], [72, 12], [12, 11], [12, 38], [38, 13], [13, 12], [70, 63], [63, 71], [71, 70], [31, 226], [226, 111], [111, 31], [36, 101], [101, 205], [205, 36], [203, 206], [206, 165], [165, 203], [126, 209], [209, 217], [217, 126], [98, 165], [165, 97], [97, 98], [237, 220], [220, 218], [218, 237], [237, 239], [239, 241], [241, 237], [210, 214], [214, 169], [169, 210], [140, 171], [171, 32], [32, 140], [241, 125], [125, 237], [237, 241], [179, 86], [86, 178], [178, 179], [180, 85], [85, 179], [179, 180], [181, 84], [84, 180], [180, 181], [182, 83], [83, 181], [181, 182], [194, 201], [201, 182], [182, 194], [177, 137], [137, 132], [132, 177], [184, 76], [76, 183], [183, 184], [185, 61], [61, 184], [184, 185], [186, 57], [57, 185], [185, 186], [216, 212], [212, 186], [186, 216], [192, 214], [214, 187], [187, 192], [139, 34], [34, 156], [156, 139], [218, 79], [79, 237], [237, 218], [147, 123], [123, 177], [177, 147], [45, 44], [44, 4], [4, 45], [208, 201], [201, 32], [32, 208], [98, 64], [64, 129], [129, 98], [192, 213], [213, 138], [138, 192], [235, 59], [59, 219], [219, 235], [141, 242], [242, 97], [97, 141], [97, 2], [2, 141], [141, 97], [240, 75], [75, 235], [235, 240], [229, 24], [24, 228], [228, 229], [31, 25], [25, 226], [226, 31], [230, 23], [23, 229], [229, 230], [231, 22], [22, 230], [230, 231], [232, 26], [26, 231], [231, 232], [233, 112], [112, 232], [232, 233], [244, 189], [189, 243], [243, 244], [189, 221], [221, 190], [190, 189], [222, 28], [28, 221], [221, 222], [223, 27], [27, 222], [222, 223], [224, 29], [29, 223], [223, 224], [225, 30], [30, 224], [224, 225], [113, 247], [247, 225], [225, 113], [99, 60], [60, 240], [240, 99], [213, 147], [147, 215], [215, 213], [60, 20], [20, 166], [166, 60], [192, 187], [187, 213], [213, 192], [243, 112], [112, 244], [244, 243], [244, 233], [233, 245], [245, 244], [245, 128], [128, 188], [188, 245], [188, 114], [114, 174], [174, 188], [134, 131], [131, 220], [220, 134], [174, 217], [217, 236], [236, 174], [236, 198], [198, 134], [134, 236], [215, 177], [177, 58], [58, 215], [156, 143], [143, 124], [124, 156], [25, 110], [110, 7], [7, 25], [31, 228], [228, 25], [25, 31], [264, 356], [356, 368], [368, 264], [0, 11], [11, 267], [267, 0], [451, 452], [452, 349], [349, 451], [267, 302], [302, 269], [269, 267], [350, 357], [357, 277], [277, 350], [350, 452], [452, 357], [357, 350], [299, 333], [333, 297], [297, 299], [396, 175], [175, 377], [377, 396], [280, 347], [347, 330], [330, 280], [269, 303], [303, 270], [270, 269], [151, 9], [9, 337], [337, 151], [344, 278], [278, 360], [360, 344], [424, 418], [418, 431], [431, 424], [270, 304], [304, 409], [409, 270], [272, 310], [310, 407], [407, 272], [322, 270], [270, 410], [410, 322], [449, 450], [450, 347], [347, 449], [432, 422], [422, 434], [434, 432], [18, 313], [313, 17], [17, 18], [291, 306], [306, 375], [375, 291], [259, 387], [387, 260], [260, 259], [424, 335], [335, 418], [418, 424], [434, 364], [364, 416], [416, 434], [391, 423], [423, 327], [327, 391], [301, 251], [251, 298], [298, 301], [275, 281], [281, 4], [4, 275], [254, 373], [373, 253], [253, 254], [375, 307], [307, 321], [321, 375], [280, 425], [425, 411], [411, 280], [200, 421], [421, 18], [18, 200], [335, 321], [321, 406], [406, 335], [321, 320], [320, 405], [405, 321], [314, 315], [315, 17], [17, 314], [423, 426], [426, 266], [266, 423], [396, 377], [377, 369], [369, 396], [270, 322], [322, 269], [269, 270], [413, 417], [417, 464], [464, 413], [385, 386], [386, 258], [258, 385], [248, 456], [456, 419], [419, 248], [298, 284], [284, 333], [333, 298], [168, 417], [417, 8], [8, 168], [448, 346], [346, 261], [261, 448], [417, 413], [413, 285], [285, 417], [326, 327], [327, 328], [328, 326], [277, 355], [355, 329], [329, 277], [309, 392], [392, 438], [438, 309], [381, 382], [382, 256], [256, 381], [279, 429], [429, 360], [360, 279], [365, 364], [364, 379], [379, 365], [355, 277], [277, 437], [437, 355], [282, 443], [443, 283], [283, 282], [281, 275], [275, 363], [363, 281], [395, 431], [431, 369], [369, 395], [299, 297], [297, 337], [337, 299], [335, 273], [273, 321], [321, 335], [348, 450], [450, 349], [349, 348], [359, 446], [446, 467], [467, 359], [283, 293], [293, 282], [282, 283], [250, 458], [458, 462], [462, 250], [300, 276], [276, 383], [383, 300], [292, 308], [308, 325], [325, 292], [283, 276], [276, 293], [293, 283], [264, 372], [372, 447], [447, 264], [346, 352], [352, 340], [340, 346], [354, 274], [274, 19], [19, 354], [363, 456], [456, 281], [281, 363], [426, 436], [436, 425], [425, 426], [380, 381], [381, 252], [252, 380], [267, 269], [269, 393], [393, 267], [421, 200], [200, 428], [428, 421], [371, 266], [266, 329], [329, 371], [432, 287], [287, 422], [422, 432], [290, 250], [250, 328], [328, 290], [385, 258], [258, 384], [384, 385], [446, 265], [265, 342], [342, 446], [386, 387], [387, 257], [257, 386], [422, 424], [424, 430], [430, 422], [445, 342], [342, 276], [276, 445], [422, 273], [273, 424], [424, 422], [306, 292], [292, 307], [307, 306], [352, 366], [366, 345], [345, 352], [268, 271], [271, 302], [302, 268], [358, 423], [423, 371], [371, 358], [327, 294], [294, 460], [460, 327], [331, 279], [279, 294], [294, 331], [303, 271], [271, 304], [304, 303], [436, 432], [432, 427], [427, 436], [304, 272], [272, 408], [408, 304], [395, 394], [394, 431], [431, 395], [378, 395], [395, 400], [400, 378], [296, 334], [334, 299], [299, 296], [6, 351], [351, 168], [168, 6], [376, 352], [352, 411], [411, 376], [307, 325], [325, 320], [320, 307], [285, 295], [295, 336], [336, 285], [320, 319], [319, 404], [404, 320], [329, 330], [330, 349], [349, 329], [334, 293], [293, 333], [333, 334], [366, 323], [323, 447], [447, 366], [316, 15], [15, 315], [315, 316], [331, 358], [358, 279], [279, 331], [317, 14], [14, 316], [316, 317], [8, 285], [285, 9], [9, 8], [277, 329], [329, 350], [350, 277], [253, 374], [374, 252], [252, 253], [319, 318], [318, 403], [403, 319], [351, 6], [6, 419], [419, 351], [324, 318], [318, 325], [325, 324], [397, 367], [367, 365], [365, 397], [288, 435], [435, 397], [397, 288], [278, 344], [344, 439], [439, 278], [310, 272], [272, 311], [311, 310], [248, 195], [195, 281], [281, 248], [375, 273], [273, 291], [291, 375], [175, 396], [396, 199], [199, 175], [312, 311], [311, 268], [268, 312], [276, 283], [283, 445], [445, 276], [390, 373], [373, 339], [339, 390], [295, 282], [282, 296], [296, 295], [448, 449], [449, 346], [346, 448], [356, 264], [264, 454], [454, 356], [337, 336], [336, 299], [299, 337], [337, 338], [338, 151], [151, 337], [294, 278], [278, 455], [455, 294], [308, 292], [292, 415], [415, 308], [429, 358], [358, 355], [355, 429], [265, 340], [340, 372], [372, 265], [352, 346], [346, 280], [280, 352], [295, 442], [442, 282], [282, 295], [354, 19], [19, 370], [370, 354], [285, 441], [441, 295], [295, 285], [195, 248], [248, 197], [197, 195], [457, 440], [440, 274], [274, 457], [301, 300], [300, 368], [368, 301], [417, 351], [351, 465], [465, 417], [251, 301], [301, 389], [389, 251], [394, 395], [395, 379], [379, 394], [399, 412], [412, 419], [419, 399], [410, 436], [436, 322], [322, 410], [326, 2], [2, 393], [393, 326], [354, 370], [370, 461], [461, 354], [393, 164], [164, 267], [267, 393], [268, 302], [302, 12], [12, 268], [312, 268], [268, 13], [13, 312], [298, 293], [293, 301], [301, 298], [265, 446], [446, 340], [340, 265], [280, 330], [330, 425], [425, 280], [322, 426], [426, 391], [391, 322], [420, 429], [429, 437], [437, 420], [393, 391], [391, 326], [326, 393], [344, 440], [440, 438], [438, 344], [458, 459], [459, 461], [461, 458], [364, 434], [434, 394], [394, 364], [428, 396], [396, 262], [262, 428], [274, 354], [354, 457], [457, 274], [317, 316], [316, 402], [402, 317], [316, 315], [315, 403], [403, 316], [315, 314], [314, 404], [404, 315], [314, 313], [313, 405], [405, 314], [313, 421], [421, 406], [406, 313], [323, 366], [366, 361], [361, 323], [292, 306], [306, 407], [407, 292], [306, 291], [291, 408], [408, 306], [291, 287], [287, 409], [409, 291], [287, 432], [432, 410], [410, 287], [427, 434], [434, 411], [411, 427], [372, 264], [264, 383], [383, 372], [459, 309], [309, 457], [457, 459], [366, 352], [352, 401], [401, 366], [1, 274], [274, 4], [4, 1], [418, 421], [421, 262], [262, 418], [331, 294], [294, 358], [358, 331], [435, 433], [433, 367], [367, 435], [392, 289], [289, 439], [439, 392], [328, 462], [462, 326], [326, 328], [94, 2], [2, 370], [370, 94], [289, 305], [305, 455], [455, 289], [339, 254], [254, 448], [448, 339], [359, 255], [255, 446], [446, 359], [254, 253], [253, 449], [449, 254], [253, 252], [252, 450], [450, 253], [252, 256], [256, 451], [451, 252], [256, 341], [341, 452], [452, 256], [414, 413], [413, 463], [463, 414], [286, 441], [441, 414], [414, 286], [286, 258], [258, 441], [441, 286], [258, 257], [257, 442], [442, 258], [257, 259], [259, 443], [443, 257], [259, 260], [260, 444], [444, 259], [260, 467], [467, 445], [445, 260], [309, 459], [459, 250], [250, 309], [305, 289], [289, 290], [290, 305], [305, 290], [290, 460], [460, 305], [401, 376], [376, 435], [435, 401], [309, 250], [250, 392], [392, 309], [376, 411], [411, 433], [433, 376], [453, 341], [341, 464], [464, 453], [357, 453], [453, 465], [465, 357], [343, 357], [357, 412], [412, 343], [437, 343], [343, 399], [399, 437], [344, 360], [360, 440], [440, 344], [420, 437], [437, 456], [456, 420], [360, 420], [420, 363], [363, 360], [361, 401], [401, 288], [288, 361], [265, 372], [372, 353], [353, 265], [390, 339], [339, 249], [249, 390], [339, 448], [448, 255], [255, 339]]), E2("VERSION", "0.4.1646425229");
}).call(D);
var z = function() {
  return z = Object.assign || function(t2) {
    for (var e, n = 1, r = arguments.length; n < r; n++) for (var i in e = arguments[n]) Object.prototype.hasOwnProperty.call(e, i) && (t2[i] = e[i]);
    return t2;
  }, z.apply(this, arguments);
};
function U(t2, e, n, r) {
  return new (n || (n = Promise))(function(i, o) {
    function a(t3) {
      try {
        s(r.next(t3));
      } catch (t4) {
        o(t4);
      }
    }
    function u(t3) {
      try {
        s(r.throw(t3));
      } catch (t4) {
        o(t4);
      }
    }
    function s(t3) {
      var e2;
      t3.done ? i(t3.value) : (e2 = t3.value, e2 instanceof n ? e2 : new n(function(t4) {
        t4(e2);
      })).then(a, u);
    }
    s((r = r.apply(t2, e || [])).next());
  });
}
function N(t2, e) {
  var n, r, i, o, a = { label: 0, sent: function() {
    if (1 & i[0]) throw i[1];
    return i[1];
  }, trys: [], ops: [] };
  return o = { next: u(0), throw: u(1), return: u(2) }, "function" == typeof Symbol && (o[Symbol.iterator] = function() {
    return this;
  }), o;
  function u(o2) {
    return function(u2) {
      return function(o3) {
        if (n) throw new TypeError("Generator is already executing.");
        for (; a; ) try {
          if (n = 1, r && (i = 2 & o3[0] ? r.return : o3[0] ? r.throw || ((i = r.return) && i.call(r), 0) : r.next) && !(i = i.call(r, o3[1])).done) return i;
          switch (r = 0, i && (o3 = [2 & o3[0], i.value]), o3[0]) {
            case 0:
            case 1:
              i = o3;
              break;
            case 4:
              return a.label++, { value: o3[1], done: false };
            case 5:
              a.label++, r = o3[1], o3 = [0];
              continue;
            case 7:
              o3 = a.ops.pop(), a.trys.pop();
              continue;
            default:
              if (!((i = (i = a.trys).length > 0 && i[i.length - 1]) || 6 !== o3[0] && 2 !== o3[0])) {
                a = 0;
                continue;
              }
              if (3 === o3[0] && (!i || o3[1] > i[0] && o3[1] < i[3])) {
                a.label = o3[1];
                break;
              }
              if (6 === o3[0] && a.label < i[1]) {
                a.label = i[1], i = o3;
                break;
              }
              if (i && a.label < i[2]) {
                a.label = i[2], a.ops.push(o3);
                break;
              }
              i[2] && a.ops.pop(), a.trys.pop();
              continue;
          }
          o3 = e.call(t2, a);
        } catch (t3) {
          o3 = [6, t3], r = 0;
        } finally {
          n = i = 0;
        }
        if (5 & o3[0]) throw o3[1];
        return { value: o3[0] ? o3[1] : void 0, done: true };
      }([o2, u2]);
    };
  }
}
var V = ["rightEye", "leftEye", "noseTip", "mouthCenter", "rightEarTragion", "leftEarTragion"];
var H = { modelType: "short", runtime: "mediapipe", maxFaces: 1 };
var K = function() {
  function t2(t3) {
    var e = this;
    this.width = 0, this.height = 0, this.selfieMode = false, this.faceDetectorSolution = new P.FaceDetection({ locateFile: function(e2, n) {
      if (t3.solutionPath) {
        var r = t3.solutionPath.replace(/\/+$/, "");
        return "".concat(r, "/").concat(e2);
      }
      return "".concat(n, "/").concat(e2);
    } }), this.faceDetectorSolution.setOptions({ selfieMode: this.selfieMode, model: t3.modelType }), this.faceDetectorSolution.onResults(function(t4) {
      if (e.height = t4.image.height, e.width = t4.image.width, e.faces = [], null !== t4.detections) for (var n = 0, r = t4.detections; n < r.length; n++) {
        var i = r[n];
        e.faces.push(e.normalizedToAbsolute(i.landmarks, (void 0, void 0, void 0, { xMin: a = (o = i.boundingBox).xCenter - o.width / 2, xMax: a + o.width, yMin: u = o.yCenter - o.height / 2, yMax: u + o.height, width: o.width, height: o.height })));
      }
      var o, a, u;
    });
  }
  return t2.prototype.normalizedToAbsolute = function(t3, e) {
    var n = this;
    return { keypoints: t3.map(function(t4, e2) {
      return { x: t4.x * n.width, y: t4.y * n.height, name: V[e2] };
    }), box: { xMin: e.xMin * this.width, yMin: e.yMin * this.height, xMax: e.xMax * this.width, yMax: e.yMax * this.height, width: e.width * this.width, height: e.height * this.height } };
  }, t2.prototype.estimateFaces = function(t3, e) {
    return U(this, void 0, void 0, function() {
      var i, o;
      return N(this, function(a) {
        switch (a.label) {
          case 0:
            return e && e.flipHorizontal && e.flipHorizontal !== this.selfieMode && (this.selfieMode = e.flipHorizontal, this.faceDetectorSolution.setOptions({ selfieMode: this.selfieMode })), t3 instanceof Tensor ? (o = ImageData.bind, [4, browser_exports.toPixels(t3)]) : [3, 2];
          case 1:
            return i = new (o.apply(ImageData, [void 0, a.sent(), t3.shape[1], t3.shape[0]]))(), [3, 3];
          case 2:
            i = t3, a.label = 3;
          case 3:
            return t3 = i, [4, this.faceDetectorSolution.send({ image: t3 })];
          case 4:
            return a.sent(), [2, this.faces];
        }
      });
    });
  }, t2.prototype.dispose = function() {
    this.faceDetectorSolution.close();
  }, t2.prototype.reset = function() {
    this.faceDetectorSolution.reset(), this.width = 0, this.height = 0, this.faces = null, this.selfieMode = false;
  }, t2.prototype.initialize = function() {
    return this.faceDetectorSolution.initialize();
  }, t2;
}();
function W(t2) {
  return U(this, void 0, void 0, function() {
    var e, n;
    return N(this, function(r) {
      switch (r.label) {
        case 0:
          return e = function(t3) {
            if (null == t3) return z({}, H);
            var e2 = z({}, t3);
            return e2.runtime = "mediapipe", null == e2.modelType && (e2.modelType = H.modelType), null == e2.maxFaces && (e2.maxFaces = H.maxFaces), e2;
          }(t2), [4, (n = new K(e)).initialize()];
        case 1:
          return r.sent(), [2, n];
      }
    });
  });
}
function G(t2) {
  return t2 instanceof Tensor ? { height: t2.shape[0], width: t2.shape[1] } : { height: t2.height, width: t2.width };
}
function X(t2) {
  return t2 instanceof Tensor ? t2 : browser_exports.fromPixels(t2);
}
function Y(t2, e) {
  util_exports.assert(0 !== t2.width, function() {
    return "".concat(e, " width cannot be 0.");
  }), util_exports.assert(0 !== t2.height, function() {
    return "".concat(e, " height cannot be 0.");
  });
}
function J(t2, e, n) {
  var r = e.outputTensorSize, i = e.keepAspectRatio, o = e.borderMode, h = e.outputTensorFloatRange, f = G(t2), g = function(t3, e2) {
    return e2 ? { xCenter: e2.xCenter * t3.width, yCenter: e2.yCenter * t3.height, width: e2.width * t3.width, height: e2.height * t3.height, rotation: e2.rotation } : { xCenter: 0.5 * t3.width, yCenter: 0.5 * t3.height, width: t3.width, height: t3.height, rotation: 0 };
  }(f, n), v = function(t3, e2, n2) {
    if (void 0 === n2 && (n2 = false), !n2) return { top: 0, left: 0, right: 0, bottom: 0 };
    var r2 = e2.height, i2 = e2.width;
    Y(e2, "targetSize"), Y(t3, "roi");
    var o2, a, u = r2 / i2, s = t3.height / t3.width, c = 0, h2 = 0;
    return u > s ? (o2 = t3.width, a = t3.width * u, h2 = (1 - s / u) / 2) : (o2 = t3.height / u, a = t3.height, c = (1 - u / s) / 2), t3.width = o2, t3.height = a, { top: h2, left: c, right: c, bottom: h2 };
  }(g, r, i), m = function(t3, e2, n2, r2) {
    var i2 = t3.width, o2 = t3.height, a = r2 ? -1 : 1, u = Math.cos(t3.rotation), s = Math.sin(t3.rotation), c = t3.xCenter, h2 = t3.yCenter, l = 1 / e2, f2 = 1 / n2, d = new Array(16);
    return d[0] = i2 * u * a * l, d[1] = -o2 * s * l, d[2] = 0, d[3] = (-0.5 * i2 * u * a + 0.5 * o2 * s + c) * l, d[4] = i2 * s * a * f2, d[5] = o2 * u * f2, d[6] = 0, d[7] = (-0.5 * o2 * u - 0.5 * i2 * s * a + h2) * f2, d[8] = 0, d[9] = 0, d[10] = i2 * l, d[11] = 0, d[12] = 0, d[13] = 0, d[14] = 0, d[15] = 1, function(t4) {
      if (16 !== t4.length) throw new Error("Array length must be 16 but got ".concat(t4.length));
      return [[t4[0], t4[1], t4[2], t4[3]], [t4[4], t4[5], t4[6], t4[7]], [t4[8], t4[9], t4[10], t4[11]], [t4[12], t4[13], t4[14], t4[15]]];
    }(d);
  }(g, f.width, f.height, false), y = tidy(function() {
    var e2 = X(t2), n2 = tensor2d(function(t3, e3, n3) {
      return Y(n3, "inputResolution"), [1 / n3.width * t3[0][0] * e3.width, 1 / n3.height * t3[0][1] * e3.width, t3[0][3] * e3.width, 1 / n3.width * t3[1][0] * e3.height, 1 / n3.height * t3[1][1] * e3.height, t3[1][3] * e3.height, 0, 0];
    }(m, f, r), [1, 8]), i2 = "zero" === o ? "constant" : "nearest", g2 = image.transform(expandDims(cast(e2, "float32")), n2, "bilinear", i2, 0, [r.height, r.width]);
    return null != h ? function(t3, e3) {
      var n3 = function(t4, e4, n4, r2) {
        var i3 = (r2 - n4) / 255;
        return { scale: i3, offset: n4 - 0 * i3 };
      }(0, 0, e3[0], e3[1]);
      return tidy(function() {
        return add(mul(t3, n3.scale), n3.offset);
      });
    }(g2, h) : g2;
  });
  return { imageTensor: y, padding: v, transformationMatrix: m };
}
function q(t2) {
  null == t2.reduceBoxesInLowestLayer && (t2.reduceBoxesInLowestLayer = false), null == t2.interpolatedScaleAspectRatio && (t2.interpolatedScaleAspectRatio = 1), null == t2.fixedAnchorSize && (t2.fixedAnchorSize = false);
  for (var e = [], n = 0; n < t2.numLayers; ) {
    for (var r = [], i = [], o = [], a = [], u = n; u < t2.strides.length && t2.strides[u] === t2.strides[n]; ) {
      var s = $(t2.minScale, t2.maxScale, u, t2.strides.length);
      if (0 === u && t2.reduceBoxesInLowestLayer) o.push(1), o.push(2), o.push(0.5), a.push(0.1), a.push(s), a.push(s);
      else {
        for (var c = 0; c < t2.aspectRatios.length; ++c) o.push(t2.aspectRatios[c]), a.push(s);
        if (t2.interpolatedScaleAspectRatio > 0) {
          var h = u === t2.strides.length - 1 ? 1 : $(t2.minScale, t2.maxScale, u + 1, t2.strides.length);
          a.push(Math.sqrt(s * h)), o.push(t2.interpolatedScaleAspectRatio);
        }
      }
      u++;
    }
    for (var l = 0; l < o.length; ++l) {
      var f = Math.sqrt(o[l]);
      r.push(a[l] / f), i.push(a[l] * f);
    }
    var d = 0, p = 0;
    if (t2.featureMapHeight.length > 0) d = t2.featureMapHeight[n], p = t2.featureMapWidth[n];
    else {
      var g = t2.strides[n];
      d = Math.ceil(t2.inputSizeHeight / g), p = Math.ceil(t2.inputSizeWidth / g);
    }
    for (var v = 0; v < d; ++v) for (var m = 0; m < p; ++m) for (var y = 0; y < r.length; ++y) {
      var w = { xCenter: (m + t2.anchorOffsetX) / p, yCenter: (v + t2.anchorOffsetY) / d, width: 0, height: 0 };
      t2.fixedAnchorSize ? (w.width = 1, w.height = 1) : (w.width = i[y], w.height = r[y]), e.push(w);
    }
    n = u;
  }
  return e;
}
function $(t2, e, n, r) {
  return 1 === r ? 0.5 * (t2 + e) : t2 + (e - t2) * n / (r - 1);
}
function Z(t2, e) {
  var n = e[0], r = e[1];
  return [n * t2[0] + r * t2[1] + t2[3], n * t2[4] + r * t2[5] + t2[7]];
}
function Q(t2, e, n, r) {
  return U(this, void 0, void 0, function() {
    var r2, s, c, h, l;
    return N(this, function(f) {
      switch (f.label) {
        case 0:
          return t2.sort(function(t3, e2) {
            return Math.max.apply(Math, e2.score) - Math.max.apply(Math, t3.score);
          }), r2 = tensor2d(t2.map(function(t3) {
            return [t3.locationData.relativeBoundingBox.yMin, t3.locationData.relativeBoundingBox.xMin, t3.locationData.relativeBoundingBox.yMax, t3.locationData.relativeBoundingBox.xMax];
          })), s = tensor1d(t2.map(function(t3) {
            return t3.score[0];
          })), [4, image.nonMaxSuppressionAsync(r2, s, e, n)];
        case 1:
          return [4, (c = f.sent()).array()];
        case 2:
          return h = f.sent(), l = t2.filter(function(t3, e2) {
            return h.indexOf(e2) > -1;
          }), dispose([r2, s, c]), [2, l];
      }
    });
  });
}
function tt(t2, e, n) {
  return U(this, void 0, void 0, function() {
    var r, i, a, u, c;
    return N(this, function(l) {
      switch (l.label) {
        case 0:
          return r = t2[0], i = t2[1], a = function(t3, e2, n2) {
            return tidy(function() {
              var r2, i2, o, a2;
              n2.reverseOutputOrder ? (i2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 0], [-1, 1])), r2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 1], [-1, 1])), a2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 2], [-1, 1])), o = squeeze(slice(t3, [0, n2.boxCoordOffset + 3], [-1, 1]))) : (r2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 0], [-1, 1])), i2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 1], [-1, 1])), o = squeeze(slice(t3, [0, n2.boxCoordOffset + 2], [-1, 1])), a2 = squeeze(slice(t3, [0, n2.boxCoordOffset + 3], [-1, 1]))), i2 = add(mul(div(i2, n2.xScale), e2.w), e2.x), r2 = add(mul(div(r2, n2.yScale), e2.h), e2.y), n2.applyExponentialOnBoxSize ? (o = mul(exp(div(o, n2.hScale)), e2.h), a2 = mul(exp(div(a2, n2.wScale)), e2.w)) : (o = mul(div(o, n2.hScale), e2.h), a2 = mul(div(a2, n2.wScale), e2.h));
              var u2 = sub(r2, div(o, 2)), s = sub(i2, div(a2, 2)), c2 = add(r2, div(o, 2)), l2 = add(i2, div(a2, 2)), b = concat([reshape(u2, [n2.numBoxes, 1]), reshape(s, [n2.numBoxes, 1]), reshape(c2, [n2.numBoxes, 1]), reshape(l2, [n2.numBoxes, 1])], 1);
              if (n2.numKeypoints) for (var x = 0; x < n2.numKeypoints; ++x) {
                var M = n2.keypointCoordOffset + x * n2.numValuesPerKeypoint, A = void 0, T = void 0;
                n2.reverseOutputOrder ? (A = squeeze(slice(t3, [0, M], [-1, 1])), T = squeeze(slice(t3, [0, M + 1], [-1, 1]))) : (T = squeeze(slice(t3, [0, M], [-1, 1])), A = squeeze(slice(t3, [0, M + 1], [-1, 1])));
                var E2 = add(mul(div(A, n2.xScale), e2.w), e2.x), S2 = add(mul(div(T, n2.yScale), e2.h), e2.y);
                b = concat([b, reshape(E2, [n2.numBoxes, 1]), reshape(S2, [n2.numBoxes, 1])], 1);
              }
              return b;
            });
          }(i, e, n), u = tidy(function() {
            var t3 = r;
            return n.sigmoidScore ? (null != n.scoreClippingThresh && (t3 = clipByValue(r, -n.scoreClippingThresh, n.scoreClippingThresh)), t3 = sigmoid(t3)) : t3;
          }), [4, et(a, u, n)];
        case 1:
          return c = l.sent(), dispose([a, u]), [2, c];
      }
    });
  });
}
function et(t2, e, n) {
  return U(this, void 0, void 0, function() {
    var r, i, o, a, u, s, c, h, l, f, d, p;
    return N(this, function(g) {
      switch (g.label) {
        case 0:
          return r = [], [4, t2.data()];
        case 1:
          return i = g.sent(), [4, e.data()];
        case 2:
          for (o = g.sent(), a = 0; a < n.numBoxes; ++a) if (!(null != n.minScoreThresh && o[a] < n.minScoreThresh || (u = a * n.numCoords, s = nt(i[u + 0], i[u + 1], i[u + 2], i[u + 3], o[a], n.flipVertically, a), (c = s.locationData.relativeBoundingBox).width < 0 || c.height < 0))) {
            if (n.numKeypoints > 0) for ((h = s.locationData).relativeKeypoints = [], l = n.numKeypoints * n.numValuesPerKeypoint, f = 0; f < l; f += n.numValuesPerKeypoint) d = u + n.keypointCoordOffset + f, p = { x: i[d + 0], y: n.flipVertically ? 1 - i[d + 1] : i[d + 1] }, h.relativeKeypoints.push(p);
            r.push(s);
          }
          return [2, r];
      }
    });
  });
}
function nt(t2, e, n, r, i, o, a) {
  return { score: [i], ind: a, locationData: { relativeBoundingBox: { xMin: e, yMin: o ? 1 - n : t2, xMax: r, yMax: o ? 1 - t2 : n, width: r - e, height: n - t2 } } };
}
var rt;
var it = { reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: [], featureMapWidth: [], numLayers: 4, minScale: 0.1484375, maxScale: 0.75, inputSizeHeight: 128, inputSizeWidth: 128, anchorOffsetX: 0.5, anchorOffsetY: 0.5, strides: [8, 16, 16, 16], aspectRatios: [1], fixedAnchorSize: true };
var ot = { reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 0, featureMapHeight: [], featureMapWidth: [], numLayers: 1, minScale: 0.1484375, maxScale: 0.75, inputSizeHeight: 192, inputSizeWidth: 192, anchorOffsetX: 0.5, anchorOffsetY: 0.5, strides: [4], aspectRatios: [1], fixedAnchorSize: true };
var at = { runtime: "tfjs", modelType: "short", maxFaces: 1, detectorModelUrl: "https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1" };
var ut = { applyExponentialOnBoxSize: false, flipVertically: false, ignoreClasses: [], numClasses: 1, numBoxes: 896, numCoords: 16, boxCoordOffset: 0, keypointCoordOffset: 4, numKeypoints: 6, numValuesPerKeypoint: 2, sigmoidScore: true, scoreClippingThresh: 100, reverseOutputOrder: true, xScale: 128, yScale: 128, hScale: 128, wScale: 128, minScoreThresh: 0.5 };
var st = { applyExponentialOnBoxSize: false, flipVertically: false, ignoreClasses: [], numClasses: 1, numBoxes: 2304, numCoords: 16, boxCoordOffset: 0, keypointCoordOffset: 4, numKeypoints: 6, numValuesPerKeypoint: 2, sigmoidScore: true, scoreClippingThresh: 100, reverseOutputOrder: true, xScale: 192, yScale: 192, hScale: 192, wScale: 192, minScoreThresh: 0.6 };
var ct = { outputTensorSize: { width: 128, height: 128 }, keepAspectRatio: true, outputTensorFloatRange: [-1, 1], borderMode: "zero" };
var ht = { outputTensorSize: { width: 192, height: 192 }, keepAspectRatio: true, outputTensorFloatRange: [-1, 1], borderMode: "zero" };
var lt = function() {
  function t2(t3, e, n) {
    this.detectorModel = e, this.maxFaces = n, "full" === t3 ? (this.imageToTensorConfig = ht, this.tensorsToDetectionConfig = st, this.anchors = q(ot)) : (this.imageToTensorConfig = ct, this.tensorsToDetectionConfig = ut, this.anchors = q(it));
    var r = tensor1d(this.anchors.map(function(t4) {
      return t4.width;
    })), o = tensor1d(this.anchors.map(function(t4) {
      return t4.height;
    })), a = tensor1d(this.anchors.map(function(t4) {
      return t4.xCenter;
    })), u = tensor1d(this.anchors.map(function(t4) {
      return t4.yCenter;
    }));
    this.anchorTensor = { x: a, y: u, w: r, h: o };
  }
  return t2.prototype.dispose = function() {
    this.detectorModel.dispose(), dispose([this.anchorTensor.x, this.anchorTensor.y, this.anchorTensor.w, this.anchorTensor.h]);
  }, t2.prototype.reset = function() {
  }, t2.prototype.detectFaces = function(t3, e) {
    return void 0 === e && (e = false), U(this, void 0, void 0, function() {
      var n, r, i, a, d, p, g, v, m, y, w;
      return N(this, function(b) {
        switch (b.label) {
          case 0:
            return null == t3 ? (this.reset(), [2, []]) : (n = tidy(function() {
              var n2 = cast(X(t3), "float32");
              return e && (n2 = squeeze(image.flipLeftRight(expandDims(n2, 0)), [0])), n2;
            }), r = J(n, this.imageToTensorConfig), i = r.imageTensor, a = r.transformationMatrix, d = this.detectorModel.execute(i, "Identity:0"), p = function(t4) {
              return tidy(function() {
                var e2 = function(t5) {
                  return tidy(function() {
                    return [slice(t5, [0, 0, 0], [1, -1, 1]), slice(t5, [0, 0, 1], [1, -1, -1])];
                  });
                }(t4), n2 = e2[0], r2 = e2[1];
                return { boxes: squeeze(r2), logits: squeeze(n2) };
              });
            }(d), g = p.boxes, [4, tt([v = p.logits, g], this.anchorTensor, this.tensorsToDetectionConfig)]);
          case 1:
            return 0 === (m = b.sent()).length ? (dispose([n, i, d, v, g]), [2, m]) : [4, Q(m, this.maxFaces, 0.3)];
          case 2:
            return y = b.sent(), w = function(t4, e2) {
              void 0 === t4 && (t4 = []);
              var n2, r2 = (n2 = e2, [].concat.apply([], n2));
              return t4.forEach(function(t5) {
                var e3 = t5.locationData;
                e3.relativeKeypoints.forEach(function(t6) {
                  var e4 = Z(r2, [t6.x, t6.y]), n4 = e4[0], i3 = e4[1];
                  t6.x = n4, t6.y = i3;
                });
                var n3 = e3.relativeBoundingBox, i2 = Number.MAX_VALUE, o = Number.MAX_VALUE, a2 = Number.MIN_VALUE, u = Number.MIN_VALUE;
                [[n3.xMin, n3.yMin], [n3.xMin + n3.width, n3.yMin], [n3.xMin + n3.width, n3.yMin + n3.height], [n3.xMin, n3.yMin + n3.height]].forEach(function(t6) {
                  var e4 = Z(r2, t6), n4 = e4[0], s = e4[1];
                  i2 = Math.min(i2, n4), a2 = Math.max(a2, n4), o = Math.min(o, s), u = Math.max(u, s);
                }), e3.relativeBoundingBox = { xMin: i2, xMax: a2, yMin: o, yMax: u, width: a2 - i2, height: u - o };
              }), t4;
            }(y, a), dispose([n, i, d, v, g]), [2, w];
        }
      });
    });
  }, t2.prototype.estimateFaces = function(t3, e) {
    return U(this, void 0, void 0, function() {
      var n, r;
      return N(this, function(i) {
        return n = G(t3), r = !!e && e.flipHorizontal, [2, this.detectFaces(t3, r).then(function(t4) {
          return t4.map(function(t5) {
            for (var e2 = t5.locationData.relativeKeypoints.map(function(t6, e3) {
              return z(z({}, t6), { x: t6.x * n.width, y: t6.y * n.height, name: V[e3] });
            }), r2 = t5.locationData.relativeBoundingBox, i2 = 0, o = ["width", "xMax", "xMin"]; i2 < o.length; i2++) r2[o[i2]] *= n.width;
            for (var a = 0, u = ["height", "yMax", "yMin"]; a < u.length; a++) r2[u[a]] *= n.height;
            return { keypoints: e2, box: r2 };
          });
        })];
      });
    });
  }, t2;
}();
function ft(t2) {
  return U(this, void 0, void 0, function() {
    var e, n, r;
    return N(this, function(i) {
      switch (i.label) {
        case 0:
          return e = function(t3) {
            if (null == t3) return z({}, at);
            var e2 = z({}, t3);
            return null == e2.modelType && (e2.modelType = at.modelType), null == e2.maxFaces && (e2.maxFaces = at.maxFaces), null == e2.detectorModelUrl && ("full" === e2.modelType ? e2.detectorModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/face_detection/full/1" : e2.detectorModelUrl = "https://tfhub.dev/mediapipe/tfjs-model/face_detection/short/1"), e2;
          }(t2), n = "string" == typeof e.detectorModelUrl && e.detectorModelUrl.indexOf("https://tfhub.dev") > -1, [4, loadGraphModel(e.detectorModelUrl, { fromTFHub: n })];
        case 1:
          return r = i.sent(), [2, new lt(e.modelType, r, e.maxFaces)];
      }
    });
  });
}
function dt(t2, e) {
  return U(this, void 0, void 0, function() {
    var n, r;
    return N(this, function(i) {
      if (t2 === rt.MediaPipeFaceDetector) {
        if (r = void 0, null != (n = e)) {
          if ("tfjs" === n.runtime) return [2, ft(n)];
          if ("mediapipe" === n.runtime) return [2, W(n)];
          r = n.runtime;
        }
        throw new Error("Expect modelConfig.runtime to be either 'tfjs' " + "or 'mediapipe', but got ".concat(r));
      }
      throw new Error("".concat(t2, " is not a supported model name."));
    });
  });
}
function pt(t2) {
  return t2.width * t2.height;
}
function gt(t2) {
  var e = t2.xCenter - t2.width / 2, n = e + t2.width, r = t2.yCenter - t2.height / 2;
  return { xMin: e, xMax: n, yMin: r, yMax: r + t2.height, width: t2.width, height: t2.height };
}
function vt(t2, e) {
  var n = gt(t2), r = gt(e);
  if (!function(t3, e2) {
    return !(t3.xMax < e2.xMin || e2.xMax < t3.xMin || t3.yMax < e2.yMin || e2.yMax < t3.yMin);
  }(n, r)) return 0;
  var i = pt(function(t3, e2) {
    var n2 = Math.max(t3.xMin, e2.xMin), r2 = Math.min(t3.xMax, e2.xMax), i2 = Math.max(t3.yMin, e2.yMin), o2 = Math.min(t3.yMax, e2.yMax);
    return { xMin: n2, xMax: r2, yMin: i2, yMax: o2, width: Math.max(r2 - n2, 0), height: Math.max(o2 - i2, 0) };
  }(n, r)), o = pt(n) + pt(r) - i;
  return o > 0 ? i / o : 0;
}
function mt(t2, e, n, r) {
  var i = t2.width, o = t2.height, a = r ? -1 : 1, u = Math.cos(t2.rotation), s = Math.sin(t2.rotation), c = t2.xCenter, h = t2.yCenter, l = 1 / e, f = 1 / n, d = new Array(16);
  return d[0] = i * u * a * l, d[1] = -o * s * l, d[2] = 0, d[3] = (-0.5 * i * u * a + 0.5 * o * s + c) * l, d[4] = i * s * a * f, d[5] = o * u * f, d[6] = 0, d[7] = (-0.5 * o * u - 0.5 * i * s * a + h) * f, d[8] = 0, d[9] = 0, d[10] = i * l, d[11] = 0, d[12] = 0, d[13] = 0, d[14] = 0, d[15] = 1, function(t3) {
    if (16 !== t3.length) throw new Error("Array length must be 16 but got " + t3.length);
    return [[t3[0], t3[1], t3[2], t3[3]], [t3[4], t3[5], t3[6], t3[7]], [t3[8], t3[9], t3[10], t3[11]], [t3[12], t3[13], t3[14], t3[15]]];
  }(d);
}
function yt(t2) {
  return t2 instanceof Tensor ? { height: t2.shape[0], width: t2.shape[1] } : { height: t2.height, width: t2.width };
}
function wt(t2) {
  return t2 - 2 * Math.PI * Math.floor((t2 + Math.PI) / (2 * Math.PI));
}
function bt(t2) {
  return t2 instanceof Tensor ? t2 : browser_exports.fromPixels(t2);
}
function xt(t2, n) {
  util_exports.assert(0 !== t2.width, function() {
    return n + " width cannot be 0.";
  }), util_exports.assert(0 !== t2.height, function() {
    return n + " height cannot be 0.";
  });
}
function Mt(t2, n) {
  var r = function(t3, e, n2, r2) {
    var i = e - t3, o = r2 - n2;
    if (0 === i) throw new Error("Original min and max are both " + t3 + ", range cannot be 0.");
    var a = o / i;
    return { scale: a, offset: n2 - t3 * a };
  }(0, 255, n[0], n[1]);
  return tidy(function() {
    return add(mul(t2, r.scale), r.offset);
  });
}
function At(t2, n, r) {
  var i = n.outputTensorSize, o = n.keepAspectRatio, a = n.borderMode, u = n.outputTensorFloatRange, s = yt(t2), c = function(t3, e) {
    return e ? { xCenter: e.xCenter * t3.width, yCenter: e.yCenter * t3.height, width: e.width * t3.width, height: e.height * t3.height, rotation: e.rotation } : { xCenter: 0.5 * t3.width, yCenter: 0.5 * t3.height, width: t3.width, height: t3.height, rotation: 0 };
  }(s, r), h = function(t3, e, n2) {
    if (void 0 === n2 && (n2 = false), !n2) return { top: 0, left: 0, right: 0, bottom: 0 };
    var r2 = e.height, i2 = e.width;
    xt(e, "targetSize"), xt(t3, "roi");
    var o2, a2, u2 = r2 / i2, s2 = t3.height / t3.width, c2 = 0, h2 = 0;
    return u2 > s2 ? (o2 = t3.width, a2 = t3.width * u2, h2 = (1 - s2 / u2) / 2) : (o2 = t3.height / u2, a2 = t3.height, c2 = (1 - u2 / s2) / 2), t3.width = o2, t3.height = a2, { top: h2, left: c2, right: c2, bottom: h2 };
  }(c, i, o), l = mt(c, s.width, s.height, false), f = tidy(function() {
    var n2 = bt(t2), r2 = tensor2d(function(t3, e, n3) {
      return xt(n3, "inputResolution"), [1 / n3.width * t3[0][0] * e.width, 1 / n3.height * t3[0][1] * e.width, t3[0][3] * e.width, 1 / n3.width * t3[1][0] * e.height, 1 / n3.height * t3[1][1] * e.height, t3[1][3] * e.height, 0, 0];
    }(l, s, i), [1, 8]), o2 = "zero" === a ? "constant" : "nearest", c2 = image.transform(expandDims(cast(n2, "float32")), r2, "bilinear", o2, 0, [i.height, i.width]);
    return null != u ? Mt(c2, u) : c2;
  });
  return { imageTensor: f, padding: h, transformationMatrix: l };
}
function Tt(t2) {
  return { xCenter: t2.xMin + t2.width / 2, yCenter: t2.yMin + t2.height / 2, width: t2.width, height: t2.height };
}
function Et(t2) {
  var e = t2.relativeKeypoints;
  if (e.length <= 1) throw new Error("2 or more keypoints required to calculate a rect.");
  var n = Number.MAX_VALUE, r = Number.MAX_VALUE, i = Number.MIN_VALUE, o = Number.MIN_VALUE;
  return e.forEach(function(t3) {
    n = Math.min(n, t3.x), i = Math.max(i, t3.x), r = Math.min(r, t3.y), o = Math.max(o, t3.y);
  }), { xCenter: (n + i) / 2, yCenter: (r + o) / 2, width: i - n, height: o - r };
}
function St(t2, e, n, r, i) {
  var o = "rect" === n ? function(t3, e2, n2) {
    var r2, i2 = t3.locationData;
    if ("boundingbox" === e2) r2 = Tt(i2.boundingBox);
    else {
      r2 = Et(i2);
      var o2 = n2.width, a = n2.height;
      r2.xCenter = Math.round(r2.xCenter * o2), r2.yCenter = Math.round(r2.yCenter * a), r2.width = Math.round(r2.width * o2), r2.height = Math.round(r2.height * a);
    }
    return r2;
  }(t2, e, r) : function(t3, e2) {
    var n2 = t3.locationData;
    return "boundingbox" === e2 ? Tt(n2.relativeBoundingBox) : Et(n2);
  }(t2, e);
  return i && (o.rotation = function(t3, e2, n2) {
    var r2, i2 = t3.locationData, o2 = n2.rotationVectorStartKeypointIndex, a = n2.rotationVectorEndKeypointIndex;
    r2 = n2.rotationVectorTargetAngle ? n2.rotationVectorTargetAngle : Math.PI * n2.rotationVectorTargetAngleDegree / 180;
    var u = i2.relativeKeypoints[o2].x * e2.width, s = i2.relativeKeypoints[o2].y * e2.height, c = i2.relativeKeypoints[a].x * e2.width, h = i2.relativeKeypoints[a].y * e2.height;
    return wt(r2 - Math.atan2(-(h - s), c - u));
  }(t2, r, i)), o;
}
function Ft(t2, e, n) {
  for (var r = 0; r < e.length; ++r) {
    var i = e[r], o = n[t2[r]];
    o.x = i.x, o.y = i.y;
  }
}
function Ct(t2, e, n, r) {
  if ("string" == typeof e) {
    if ("copy" === e) for (var i = 0; i < n.length; ++i) r[t2[i]].z = n[i].z;
  } else {
    var o = function(t3, e2) {
      for (var n2 = 0, r2 = 0; r2 < e2.length; ++r2) n2 += t3[e2[r2]].z;
      return n2 / e2.length;
    }(r, e);
    for (i = 0; i < t2.length; ++i) r[t2[i]].z = o;
  }
}
function Ot(t2, e) {
  for (var n = function(t3) {
    var e2 = [].concat.apply([], t3.map(function(t4) {
      return t4.indexesMapping;
    }));
    if (0 === e2.length) throw new Error("There should be at least one landmark in indexes mapping");
    var n2 = e2[0], r2 = e2[0], i2 = new Set(e2);
    i2.forEach(function(t4) {
      n2 = Math.min(n2, t4), r2 = Math.max(r2, t4);
    });
    var o2 = i2.size;
    if (0 !== n2) throw new Error("Indexes are expected to start with 0 instead of " + n2);
    if (r2 + 1 !== o2) throw new Error("Indexes should have no gaps but " + (r2 - o2 + 1) + " indexes are missing");
    return o2;
  }(e), r = new Array(n).fill(null).map(Object), i = 0; i < t2.length; ++i) {
    var o = t2[i], a = e[i];
    if (o.length !== a.indexesMapping.length) throw new Error("There are " + o.length + " refinement landmarks while mapping has " + a.indexesMapping.length);
    Ft(a.indexesMapping, o, r), Ct(a.indexesMapping, a.zRefinement, o, r);
  }
  return r;
}
function _t(t2, e) {
  return t2.map(function(t3) {
    var n = E(E({}, t3), { x: t3.x * e.width, y: t3.y * e.height });
    return null != t3.z && (n.z = t3.z * e.width), n;
  });
}
function jt(t2, e) {
  return "none" === t2 ? e : function(t3) {
    return 1 / (1 + Math.exp(-t3));
  }(e);
}
function kt(t2, e, n, r) {
  return S(this, void 0, void 0, function() {
    var i, o, a, u, s, c, h, l;
    return F(this, function(f) {
      switch (f.label) {
        case 0:
          return n = n || e.flipHorizontally || false, r = r || e.flipVertically || false, i = t2.size, o = i / e.numLandmarks, [4, t2.data()];
        case 1:
          for (a = f.sent(), u = [], s = 0; s < e.numLandmarks; ++s) c = s * o, (l = { x: 0, y: 0 }).x = n ? e.inputImageWidth - a[c] : a[c], o > 1 && (l.y = r ? e.inputImageHeight - a[c + 1] : a[c + 1]), o > 2 && (l.z = a[c + 2]), o > 3 && (l.score = jt(e.visibilityActivation, a[c + 3])), u.push(l);
          for (h = 0; h < u.length; ++h) (l = u[h]).x = l.x / e.inputImageWidth, l.y = l.y / e.inputImageHeight, l.z = l.z / e.inputImageWidth / (e.normalizeZ || 1);
          return [2, u];
      }
    });
  });
}
function Rt(t2, e, n) {
  var r = t2.width, i = t2.height, o = t2.rotation;
  if (null == n.rotation && null == n.rotationDegree || (o = function(t3, e2) {
    null != e2.rotation ? t3 += e2.rotation : null != e2.rotationDegree && (t3 += Math.PI * e2.rotationDegree / 180);
    return wt(t3);
  }(o, n)), 0 === o) t2.xCenter = t2.xCenter + r * n.shiftX, t2.yCenter = t2.yCenter + i * n.shiftY;
  else {
    var a = (e.width * r * n.shiftX * Math.cos(o) - e.height * i * n.shiftY * Math.sin(o)) / e.width, u = (e.width * r * n.shiftX * Math.sin(o) + e.height * i * n.shiftY * Math.cos(o)) / e.height;
    t2.xCenter = t2.xCenter + a, t2.yCenter = t2.yCenter + u;
  }
  if (n.squareLong) {
    var s = Math.max(r * e.width, i * e.height);
    r = s / e.width, i = s / e.height;
  } else if (n.squareShort) {
    var c = Math.min(r * e.width, i * e.height);
    r = c / e.width, i = c / e.height;
  }
  return t2.width = r * n.scaleX, t2.height = i * n.scaleY, t2;
}
(rt || (rt = {})).MediaPipeFaceDetector = "MediaPipeFaceDetector";
var It = { runtime: "tfjs", maxFaces: 1, refineLandmarks: false, landmarkModelUrl: "https://tfhub.dev/mediapipe/tfjs-model/face_landmarks_detection/face_mesh/1" };
var Lt = { flipHorizontal: false, staticImageMode: false };
var Bt = { shiftX: 0, shiftY: 0, scaleX: 1.5, scaleY: 1.5, squareLong: true };
var Dt = { outputTensorSize: { width: 192, height: 192 }, outputTensorFloatRange: [0, 1], borderMode: "replicate" };
var Pt = { numLandmarks: 468, inputImageWidth: 192, inputImageHeight: 192, visibilityActivation: "none", flipHorizontally: false, flipVertically: false };
var zt = { numLandmarks: 80, inputImageWidth: 192, inputImageHeight: 192, visibilityActivation: "none", flipHorizontally: false, flipVertically: false };
var Ut = { numLandmarks: 71, inputImageWidth: 192, inputImageHeight: 192, visibilityActivation: "none", flipHorizontally: false, flipVertically: false };
var Nt = { numLandmarks: 5, inputImageWidth: 192, inputImageHeight: 192, visibilityActivation: "none", flipHorizontally: false, flipVertically: false };
var Vt = { indexesMapping: Array.from(Array(468).keys()), zRefinement: "copy" };
var Ht = { indexesMapping: [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291, 185, 40, 39, 37, 0, 267, 269, 270, 409, 78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 191, 80, 81, 82, 13, 312, 311, 310, 415, 76, 77, 90, 180, 85, 16, 315, 404, 320, 307, 306, 184, 74, 73, 72, 11, 302, 303, 304, 408, 62, 96, 89, 179, 86, 15, 316, 403, 319, 325, 292, 183, 42, 41, 38, 12, 268, 271, 272, 407], zRefinement: "none" };
var Kt = { indexesMapping: [33, 7, 163, 144, 145, 153, 154, 155, 133, 246, 161, 160, 159, 158, 157, 173, 130, 25, 110, 24, 23, 22, 26, 112, 243, 247, 30, 29, 27, 28, 56, 190, 226, 31, 228, 229, 230, 231, 232, 233, 244, 113, 225, 224, 223, 222, 221, 189, 35, 124, 46, 53, 52, 65, 143, 111, 117, 118, 119, 120, 121, 128, 245, 156, 70, 63, 105, 66, 107, 55, 193], zRefinement: "none" };
var Wt = { indexesMapping: [263, 249, 390, 373, 374, 380, 381, 382, 362, 466, 388, 387, 386, 385, 384, 398, 359, 255, 339, 254, 253, 252, 256, 341, 463, 467, 260, 259, 257, 258, 286, 414, 446, 261, 448, 449, 450, 451, 452, 453, 464, 342, 445, 444, 443, 442, 441, 413, 265, 353, 276, 283, 282, 295, 372, 340, 346, 347, 348, 349, 350, 357, 465, 383, 300, 293, 334, 296, 336, 285, 417], zRefinement: "none" };
var Gt = { indexesMapping: [468, 469, 470, 471, 472], zRefinement: [33, 7, 163, 144, 145, 153, 154, 155, 133, 246, 161, 160, 159, 158, 157, 173] };
var Xt = { indexesMapping: [473, 474, 475, 476, 477], zRefinement: [263, 249, 390, 373, 374, 380, 381, 382, 362, 466, 388, 387, 386, 385, 384, 398] };
var Yt;
var Jt = function() {
  function t2(t3, e, n, r) {
    this.detector = t3, this.landmarkModel = e, this.maxFaces = n, this.withAttention = r, this.prevFaceRectsFromLandmarks = null;
  }
  return t2.prototype.estimateFaces = function(t3, n) {
    return S(this, void 0, void 0, function() {
      var r, i, o, a, u, s, c, h, l, f, d, p, g, v = this;
      return F(this, function(m) {
        switch (m.label) {
          case 0:
            return r = function(t4) {
              if (null == t4) return E({}, Lt);
              var e = E({}, t4);
              return null == e.flipHorizontal && (e.flipHorizontal = Lt.flipHorizontal), null == e.staticImageMode && (e.staticImageMode = Lt.staticImageMode), e;
            }(n), null == t3 ? (this.reset(), [2, []]) : (i = yt(t3), o = tidy(function() {
              var n2 = cast(bt(t3), "float32");
              if (r.flipHorizontal) {
                n2 = squeeze(image.flipLeftRight(expandDims(n2, 0)), [0]);
              }
              return n2;
            }), a = this.prevFaceRectsFromLandmarks, r.staticImageMode || null == a || a.length < this.maxFaces ? [4, this.detector.detectFaces(o, false)] : [3, 2]);
          case 1:
            return 0 === (s = m.sent()).length ? (this.reset(), o.dispose(), [2, []]) : (u = s.map(function(t4) {
              return v.faceDetectionFrontDetectionToRoi(t4, i);
            }), [3, 3]);
          case 2:
            u = [], m.label = 3;
          case 3:
            return y = 0.5, w = [], [u, a || []].forEach(function(t4) {
              return t4.forEach(function(t5) {
                (w = w.filter(function(e) {
                  return vt(t5, e) <= y;
                })).push(t5);
              });
            }), c = w, [4, Promise.all(c.map(function(t4) {
              return v.faceLandmark(t4, o);
            }))];
          case 4:
            for (h = m.sent(), l = [], this.prevFaceRectsFromLandmarks = [], f = 0; f < h.length; ++f) null != (d = h[f]) && (this.prevFaceRectsFromLandmarks.push(this.faceLandmarksToRoi(d, i)), null != (p = _t(d, i)) && p.forEach(function(t4, e) {
              var n2 = k.get(e);
              null != n2 && (t4.name = n2);
            }), g = R(p), l.push({ keypoints: p, box: g.locationData.relativeBoundingBox }));
            return o.dispose(), [2, l];
        }
        var y, w;
      });
    });
  }, t2.prototype.dispose = function() {
    this.detector.dispose(), this.landmarkModel.dispose();
  }, t2.prototype.reset = function() {
    this.detector.reset(), this.prevFaceRectsFromLandmarks = null;
  }, t2.prototype.faceDetectionFrontDetectionToRoi = function(t3, e) {
    return Rt(St(t3, "boundingbox", "normRect", e, { rotationVectorStartKeypointIndex: 0, rotationVectorEndKeypointIndex: 1, rotationVectorTargetAngleDegree: 0 }), e, Bt);
  }, t2.prototype.faceLandmark = function(t3, n) {
    return S(this, void 0, void 0, function() {
      var r, i, o, a, u, s, c;
      return F(this, function(h) {
        switch (h.label) {
          case 0:
            return r = At(n, Dt, t3).imageTensor, i = ["output_faceflag"].concat(this.withAttention ? ["output_mesh_identity", "output_lips", "Identity_6:0", "Identity_1:0", "Identity_2:0", "Identity_5:0"] : ["output_mesh"]), o = this.landmarkModel.execute(r, i), a = o[0], u = o.slice(1), [4, a.data()];
          case 1:
            return h.sent()[0] < 0.5 ? (dispose(o), dispose(r), [2, null]) : this.withAttention ? [4, this.tensorsToFaceLandmarksWithAttention(u)] : [3, 3];
          case 2:
            return s = h.sent(), [3, 5];
          case 3:
            return [4, this.tensorsToFaceLandmarks(u)];
          case 4:
            s = h.sent(), h.label = 5;
          case 5:
            return c = function(t4, e, n2) {
              void 0 === n2 && (n2 = { ignoreRotation: false });
              for (var r2 = [], i2 = 0, o2 = t4; i2 < o2.length; i2++) {
                var a2 = o2[i2], u2 = a2.x - 0.5, s2 = a2.y - 0.5, c2 = n2.ignoreRotation ? 0 : e.rotation, h2 = Math.cos(c2) * u2 - Math.sin(c2) * s2, l = Math.sin(c2) * u2 + Math.cos(c2) * s2;
                h2 = h2 * e.width + e.xCenter, l = l * e.height + e.yCenter;
                var f = a2.z * e.width, d = E({}, a2);
                d.x = h2, d.y = l, d.z = f, r2.push(d);
              }
              return r2;
            }(s, t3), dispose(o), dispose(r), [2, c];
        }
      });
    });
  }, t2.prototype.tensorsToFaceLandmarks = function(t3) {
    return S(this, void 0, void 0, function() {
      return F(this, function(e) {
        return [2, kt(t3[0], Pt)];
      });
    });
  }, t2.prototype.tensorsToFaceLandmarksWithAttention = function(t3) {
    return S(this, void 0, void 0, function() {
      var e, n, r, i, o, a;
      return F(this, function(u) {
        switch (u.label) {
          case 0:
            return [4, kt(t3[0], Pt)];
          case 1:
            return e = u.sent(), [4, kt(t3[1], zt)];
          case 2:
            return n = u.sent(), [4, kt(t3[3], Ut)];
          case 3:
            return r = u.sent(), [4, kt(t3[5], Ut)];
          case 4:
            return i = u.sent(), [4, kt(t3[4], Nt)];
          case 5:
            return o = u.sent(), [4, kt(t3[2], Nt)];
          case 6:
            return a = u.sent(), [2, Ot([e, n, r, i, o, a], [Vt, Ht, Kt, Wt, Gt, Xt])];
        }
      });
    });
  }, t2.prototype.faceLandmarksToRoi = function(t3, e) {
    return Rt(St(R(t3), "boundingbox", "normRect", e, { rotationVectorStartKeypointIndex: 33, rotationVectorEndKeypointIndex: 263, rotationVectorTargetAngleDegree: 0 }), e, Bt);
  }, t2;
}();
function qt(t2) {
  return S(this, void 0, void 0, function() {
    var e, n, r, i;
    return F(this, function(o) {
      switch (o.label) {
        case 0:
          return e = function(t3) {
            if (null == t3) return E({}, It);
            var e2 = E({}, t3);
            return e2.runtime = "tfjs", null == e2.maxFaces && (e2.maxFaces = It.maxFaces), null == e2.refineLandmarks && (e2.refineLandmarks = It.refineLandmarks), null == e2.landmarkModelUrl && (e2.landmarkModelUrl = e2.refineLandmarks ? "https://tfhub.dev/mediapipe/tfjs-model/face_landmarks_detection/attention_mesh/1" : "https://tfhub.dev/mediapipe/tfjs-model/face_landmarks_detection/face_mesh/1"), e2;
          }(t2), n = "string" == typeof e.landmarkModelUrl && e.landmarkModelUrl.indexOf("https://tfhub.dev") > -1, [4, loadGraphModel(e.landmarkModelUrl, { fromTFHub: n })];
        case 1:
          return r = o.sent(), [4, dt(rt.MediaPipeFaceDetector, { modelType: "short", maxFaces: e.maxFaces, detectorModelUrl: e.detectorModelUrl, runtime: e.runtime })];
        case 2:
          return i = o.sent(), [2, new Jt(i, r, e.maxFaces, e.refineLandmarks)];
      }
    });
  });
}
function $t(t2, e) {
  return S(this, void 0, void 0, function() {
    var n, r;
    return F(this, function(i) {
      if (t2 === Yt.MediaPipeFaceMesh) {
        if (r = void 0, null != (n = e)) {
          if ("tfjs" === n.runtime) return [2, qt(n)];
          if ("mediapipe" === n.runtime) return [2, B(n)];
          r = n.runtime;
        }
        throw new Error("Expect modelConfig.runtime to be either 'tfjs' or 'mediapipe', but got " + r);
      }
      throw new Error(t2 + " is not a supported model name.");
    });
  });
}
!function(t2) {
  t2.MediaPipeFaceMesh = "MediaPipeFaceMesh";
}(Yt || (Yt = {}));
var Zt = Object.freeze({ __proto__: null, getKeypointIndexByContour: function(t2) {
  if (t2 === Yt.MediaPipeFaceMesh) return O;
  throw new Error("Model " + t2 + " is not supported.");
}, getAdjacentPairs: function(t2) {
  if (t2 === Yt.MediaPipeFaceMesh) return _;
  throw new Error("Model " + t2 + " is not supported.");
} });
export {
  Yt as SupportedModels,
  $t as createDetector,
  Zt as util
};
/*! Bundled license information:

@tensorflow-models/face-landmarks-detection/dist/face-landmarks-detection.esm.js:
  (**
      * @license
      * Copyright 2024 Google LLC. All Rights Reserved.
      * Licensed under the Apache License, Version 2.0 (the "License");
      * you may not use this file except in compliance with the License.
      * You may obtain a copy of the License at
      *
      * http://www.apache.org/licenses/LICENSE-2.0
      *
      * Unless required by applicable law or agreed to in writing, software
      * distributed under the License is distributed on an "AS IS" BASIS,
      * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      * See the License for the specific language governing permissions and
      * limitations under the License.
      * =============================================================================
      *)
*/
//# sourceMappingURL=@tensorflow-models_face-landmarks-detection.js.map
