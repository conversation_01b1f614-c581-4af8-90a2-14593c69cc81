import {
  require_toFinite
} from "./chunk-3R6DH2NC.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/toInteger.js
var require_toInteger = __commonJS({
  "node_modules/lodash/toInteger.js"(exports, module) {
    var toFinite = require_toFinite();
    function toInteger(value) {
      var result = toFinite(value), remainder = result % 1;
      return result === result ? remainder ? result - remainder : result : 0;
    }
    module.exports = toInteger;
  }
});

export {
  require_toInteger
};
//# sourceMappingURL=chunk-ZXTARUDF.js.map
