import {
  EffectScope,
  Vue,
  computed,
  customRef,
  defineAsyncComponent,
  defineComponent,
  del,
  effectScope,
  getCurrentInstance,
  getCurrentScope,
  h,
  init_vue_runtime_esm,
  inject,
  isProxy,
  isReactive,
  isReadonly,
  isRef,
  isShallow,
  markRaw,
  mergeDefaults,
  nextTick,
  onActivated,
  onBeforeMount,
  onBeforeUnmount,
  onBeforeUpdate,
  onDeactivated,
  onErrorCaptured,
  onMounted,
  onRenderTracked,
  onRenderTriggered,
  onScopeDispose,
  onServerPrefetch,
  onUnmounted,
  onUpdated,
  provide,
  proxyRefs,
  reactive,
  readonly,
  ref$1,
  set,
  shallowReactive,
  shallowReadonly,
  shallowRef,
  toRaw,
  toRef,
  toRefs,
  triggerRef,
  unref,
  useAttrs,
  useCssModule,
  useCssVars,
  useListeners,
  useSlots,
  version,
  watch,
  watchEffect,
  watchPostEffect,
  watchSyncEffect
} from "./chunk-YO42JT3F.js";
import "./chunk-PLDDJCW6.js";
init_vue_runtime_esm();
export {
  EffectScope,
  computed,
  customRef,
  Vue as default,
  defineAsyncComponent,
  defineComponent,
  del,
  effectScope,
  getCurrentInstance,
  getCurrentScope,
  h,
  inject,
  isProxy,
  isReactive,
  isReadonly,
  isRef,
  isShallow,
  markRaw,
  mergeDefaults,
  nextTick,
  onActivated,
  onBeforeMount,
  onBeforeUnmount,
  onBeforeUpdate,
  onDeactivated,
  onErrorCaptured,
  onMounted,
  onRenderTracked,
  onRenderTriggered,
  onScopeDispose,
  onServerPrefetch,
  onUnmounted,
  onUpdated,
  provide,
  proxyRefs,
  reactive,
  readonly,
  ref$1 as ref,
  set,
  shallowReactive,
  shallowReadonly,
  shallowRef,
  toRaw,
  toRef,
  toRefs,
  triggerRef,
  unref,
  useAttrs,
  useCssModule,
  useCssVars,
  useListeners,
  useSlots,
  version,
  watch,
  watchEffect,
  watchPostEffect,
  watchSyncEffect
};
