import { type FaceDetector, type FaceLandmarker, FilesetResolver } from '@mediapipe/tasks-vision';
import type { FaceDetector as FaceDetectorTf } from '@tensorflow-models/face-detection';
import type { FaceLandmarksDetector as FaceLandmarkerTf } from '@tensorflow-models/face-landmarks-detection';

import { getUserAgent } from '@helpers/helpers/user-agent';

import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { getDeviceSpecialFlagsForLivenessModel } from '@ekyc/helpers/get-device-special-flags';

import {
  getDetectFunction as getBlazefaceDetectFunction,
  getDetectFunctionTf as getBlazefaceDetectFunctionTf,
  getTestDetectFunction as getBlazefaceTestDetectFunction,
  getTestDetectFunctionTf as getBlazefaceTestDetectFunctionTf,
  loadDetector as loadBlazefaceDetector,
  loadDetectorTf as loadBlazefaceDetectorTf,
} from './blazeface';
import {
  getDetectFunction as getFacemeshDetectFunction,
  getDetectFunctionTf as getFacemeshDetectFunctionTf,
  getTestDetectFunction as getFacemeshTestDetectFunction,
  getTestDetectFunctionTf as getFacemeshTestDetectFunctionTf,
  loadDetector as loadFacemeshDetector,
  loadDetectorTf as loadFacemeshDetectorTf,
} from './facemesh';

type ModelBackendType = 'GPU' | 'CPU';
type ModelTfBackendType = 'tf_wasm' | 'tf_cpu';

const PREFIX = '[LOAD_MODEL]';

type AttemptLog = {
  load?: {
    version?: string;
    model?: string;
    overrideBackend?: string;
    deviceFlags?: Types.DeviceSpecialFlags;
  };
  test_detect?: { pass: boolean; face_points: number; points: number[][] };
};

export const modelAttemptLog = reactive<Record<ModelBackendType | ModelTfBackendType, AttemptLog>>({
  GPU: undefined,
  CPU: undefined,
  tf_wasm: undefined,
  tf_cpu: undefined,
});

export const usingModel = ref<ModelBackendType | ModelTfBackendType>('GPU');
export const isLoadedModel = ref(false);
export const isSkipLoadModel = ref(false);

let loadModelPromise: Promise<any> = null;

let detectFn: ReturnType<typeof getBlazefaceDetectFunction | typeof getFacemeshDetectFunction>;

function errorToLog(err: Error) {
  let error = null;
  try {
    error = err?.message;
    if (!error) {
      throw err;
    }
  } catch {
    try {
      error = JSON.parse(JSON.stringify(err));
      if (Object.keys(error).length === 0) {
        throw err;
      }
    } catch {
      error = err;
    }
  }
  return {
    error,
  };
}

async function createLoadModelPromise() {
  const { allSettings } = useEkycSettings();

  isLoadedModel.value = false;
  isSkipLoadModel.value = false;

  // Apply device special flags
  const deviceData = getUserAgent();
  const deviceFlags = getDeviceSpecialFlagsForLivenessModel(deviceData);
  console.log(`${PREFIX}, Device flags:`, deviceFlags);

  // Get backend load sequence
  const overrideBackendType = allSettings.liveness.liveness.backendType;
  let backendSequence = overrideBackendType
    ? [overrideBackendType]
    : (['GPU', 'CPU', 'tf_wasm', 'tf_cpu'] as const);

  // Apply flags to modify backend sequence
  if (deviceFlags.skipGpu) {
    console.log(`${PREFIX}, Skipping GPU backend`);
    backendSequence = backendSequence.filter(b => b !== 'GPU');
  }
  if (deviceFlags.skipCpu) {
    console.log(`${PREFIX}, Skipping CPU backend`);
    backendSequence = backendSequence.filter(b => b !== 'CPU');
  }
  if (deviceFlags.skipTfWasm) {
    console.log(`${PREFIX}, Skipping TF WASM backend`);
    backendSequence = backendSequence.filter(b => b !== 'tf_wasm');
  }
  if (deviceFlags.skipTfCpu) {
    console.log(`${PREFIX}, Skipping TF CPU backend`);
    backendSequence = backendSequence.filter(b => b !== 'tf_cpu');
  }

  const isActiveLiveness = allSettings.liveness.liveness.action_sequence.length > 0;
  const modelType = isActiveLiveness ? 'facemesh' : 'blazeface';

  // Check skip load model ONLY if passive liveness + allow manual snap
  if (deviceFlags.skipLoadModel && !isActiveLiveness) {
    console.log(`${PREFIX}, Skipping load model`);
    isLoadedModel.value = true;
    isSkipLoadModel.value = true;
    modelAttemptLog[backendSequence[0] || '-'] = {
      load: {
        deviceFlags,
      },
    };
    detectFn = () => Promise.resolve([]);
    return modelAttemptLog;
  }

  console.log(`${PREFIX}, Backend Sequence, [${backendSequence}]...`);
  let result: AttemptLog = null;

  // Prepare fileset
  console.log(`${PREFIX}, FilesetResolver: START`);
  const VERSION = '0.10.21';
  const fileset = await FilesetResolver.forVisionTasks(
    `https://cdn.uppass.io/ekyc/models/@mediapipe/tasks-vision/${VERSION}/wasm`,
  );
  console.log(`${PREFIX}, FilesetResolver: SUCCESS`);

  // Prepare test image
  console.log(`${PREFIX}, Preparing test image...`);
  const imgUrl = new URL('@ekyc/assets/sample_face.jpg', import.meta.url).href;
  const imgBitmap = await createImageBitmap(await fetch(imgUrl).then(res => res.blob()));
  console.log(`${PREFIX}, Test image ready!`);

  for (const backendType of backendSequence) {
    try {
      result = {
        load: {
          model: modelType,
          overrideBackend: overrideBackendType,
          deviceFlags,
        },
      };
      console.log(`${PREFIX}, ${backendType}, Loading ${modelType} model...`);
      let detector: FaceDetector | FaceLandmarker | FaceLandmarkerTf | FaceDetectorTf;
      let testDetector: (input: ImageBitmap) => Promise<number[][]>;
      let loaderLog: Record<string, any> = {};

      // Load by MediaPipe
      if (backendType === 'GPU' || backendType === 'CPU') {
        result.load.version = VERSION;

        const loaded = await (isActiveLiveness
          ? loadFacemeshDetector(fileset, backendType)
          : loadBlazefaceDetector(fileset, backendType));
        detector = loaded.detector;
        delete loaded.detector;
        loaderLog = loaded;

        detectFn = isActiveLiveness
          ? getFacemeshDetectFunction(detector as FaceLandmarker)
          : getBlazefaceDetectFunction(detector as FaceDetector);

        testDetector = isActiveLiveness
          ? getFacemeshTestDetectFunction(detector as FaceLandmarker)
          : getBlazefaceTestDetectFunction(detector as FaceDetector);
      }
      // Load by TF
      else if (backendType === 'tf_wasm' || backendType === 'tf_cpu') {
        const resolvedBackendType = ({ tf_wasm: 'wasm', tf_cpu: 'cpu' } as const)[backendType];
        const loaded = await (isActiveLiveness
          ? loadFacemeshDetectorTf(resolvedBackendType)
          : loadBlazefaceDetectorTf(resolvedBackendType));
        detector = loaded.detector;
        delete loaded.detector;
        loaderLog = loaded;

        detectFn = isActiveLiveness
          ? getFacemeshDetectFunctionTf(detector as FaceLandmarkerTf)
          : getBlazefaceDetectFunctionTf(detector as FaceDetectorTf);

        testDetector = isActiveLiveness
          ? getFacemeshTestDetectFunctionTf(detector as FaceLandmarkerTf)
          : getBlazefaceTestDetectFunctionTf(detector as FaceDetectorTf);
      }

      Object.assign(result.load, loaderLog);

      // Test Detect
      let passTestCheck = false;
      if (deviceFlags.skipTestCheck) {
        console.log(`${PREFIX}, Skipping test check`);
        passTestCheck = true;
      } else {
        console.log(`${PREFIX}, ${backendType}, Testing ${modelType} model...`);
        const points = await testDetector(imgBitmap);
        passTestCheck = points?.some(a => a?.some(b => b > 0));
        const pointsToLog = passTestCheck ? points?.slice(0, 2) : points;

        result.test_detect = {
          pass: passTestCheck,
          face_points: points?.length,
          points: pointsToLog,
        };
      }

      if (passTestCheck) {
        console.log(`${PREFIX}, ${backendType}, Success ${modelType}!`);
        usingModel.value = backendType;
        isLoadedModel.value = true;
        modelAttemptLog[backendType] = result;
        return modelAttemptLog;
      }

      throw new Error(`${PREFIX}, ${backendType}, No ${modelType} detected`);
    } catch (err) {
      console.error(`${PREFIX}, ${backendType}, Error`, err);
      Object.assign(result.load, errorToLog(err));
      modelAttemptLog[backendType] = result;
    }
  }

  // Cant load any model
  detectFn = undefined;
  throw new Error(`${PREFIX}, Cant load any model`);
}

export function loadModel() {
  if (loadModelPromise != null) {
    return loadModelPromise;
  }

  if (isLoadedModel.value) {
    return Promise.resolve(modelAttemptLog);
  }

  loadModelPromise = createLoadModelPromise().then(res => {
    loadModelPromise = null;
    return res;
  });
  return loadModelPromise;
}

export async function detect(
  video: HTMLVideoElement,
): Promise<Types.DetectFunctionResult[] | null> {
  if (detectFn) {
    return detectFn(video);
  }
  throw new Error(`${PREFIX}, Detecting, No model is loaded`);
}
