{"version": 3, "sources": ["../../vue-svg-inline-plugin/package.json", "../../vue-svg-inline-plugin/src/index.js"], "sourcesContent": ["{\n\t\"name\": \"vue-svg-inline-plugin\",\n\t\"version\": \"2.2.3\",\n\t\"description\": \"Vue plugin for inline replacement of SVG images with actual content of SVG files.\",\n\t\"main\": \"src/index.js\",\n\t\"types\": \"src/index.d.ts\",\n\t\"unpkg\": \"dist/vue-svg-inline-plugin.min.js\",\n\t\"jsdelivr\": \"dist/vue-svg-inline-plugin.min.js\",\n\t\"files\": [\n\t\t\"index.js\",\n\t\t\"dist/*.js\",\n\t\t\"src/*.{js,d.ts}\"\n\t],\n\t\"directories\": {\n\t\t\"example\": \"examples\"\n\t},\n\t\"scripts\": {\n\t\t\"build\": \"npm run remove && npm run build:modern && npm run build:default\",\n\t\t\"build:default\": \"npm run remove:default && npx cross-env IMPORT_POLYFILLS=1 npm run webpack\",\n\t\t\"build:modern\": \"npm run remove:modern && npm run webpack\",\n\t\t\"remove\": \"npm run remove:default && npm run remove:modern\",\n\t\t\"remove:default\": \"npx rimraf dist/vue-svg-inline-plugin.min.js\",\n\t\t\"remove:modern\": \"npx rimraf dist/vue-svg-inline-plugin-modern.min.js\",\n\t\t\"webpack\": \"npx webpack --progress --color\",\n\t\t\"test\": \"echo \\\"Error: no test specified\\\" && exit 1\"\n\t},\n\t\"repository\": {\n\t\t\"type\": \"git\",\n\t\t\"url\": \"git+https://github.com/oliverfindl/vue-svg-inline-plugin.git\"\n\t},\n\t\"keywords\": [\n\t\t\"vue\",\n\t\t\"vuejs\",\n\t\t\"plugin\",\n\t\t\"vue-plugin\",\n\t\t\"svg\",\n\t\t\"inline\",\n\t\t\"sprites\",\n\t\t\"symbols\",\n\t\t\"vue-svg-inline-plugin\"\n\t],\n\t\"author\": \"Oliver Findl\",\n\t\"license\": \"MIT\",\n\t\"bugs\": {\n\t\t\"url\": \"https://github.com/oliverfindl/vue-svg-inline-plugin/issues\"\n\t},\n\t\"homepage\": \"https://github.com/oliverfindl/vue-svg-inline-plugin#readme\",\n\t\"dependencies\": {\n\t\t\"core-js\": \"^3.25.2\",\n\t\t\"intersection-observer\": \"^0.12.2\",\n\t\t\"whatwg-fetch\": \"^3.6.2\"\n\t},\n\t\"devDependencies\": {\n\t\t\"@babel/core\": \"^7.19.1\",\n\t\t\"@babel/eslint-parser\": \"^7.19.1\",\n\t\t\"@babel/preset-env\": \"^7.19.1\",\n\t\t\"babel-loader\": \"^8.2.5\",\n\t\t\"babel-plugin-remove-template-literals-whitespace\": \"^1.0.4\",\n\t\t\"cross-env\": \"^7.0.3\",\n\t\t\"eslint\": \"^8.23.1\",\n\t\t\"eslint-plugin-vue\": \"^9.5.1\",\n\t\t\"eslint-webpack-plugin\": \"^3.2.0\",\n\t\t\"rimraf\": \"^3.0.2\",\n\t\t\"terser-webpack-plugin\": \"^5.3.6\",\n\t\t\"webpack\": \"^5.74.0\",\n\t\t\"webpack-bundle-analyzer\": \"^4.6.1\",\n\t\t\"webpack-cli\": \"^4.10.0\"\n\t}\n}\n", "/**\n * <AUTHOR>\n * @version 2.2.3\n * @license MIT\n */\n\n\"use strict\";\n\n/* import package.json file as PACKAGE_JSON constant */\nimport PACKAGE_JSON from \"../package.json\";\n\n/* define PACKAGE_NAME constant */\nconst PACKAGE_NAME = PACKAGE_JSON.name;\n\n/* define PACKAGE_VERSION constant */\nconst PACKAGE_VERSION = PACKAGE_JSON.version;\n\n/* import polyfills if requested */\n// It is not possible to perform conditional import, so we use require syntax instead.\n// if(typeof IMPORT_POLYFILLS !== \"undefined\" && !!IMPORT_POLYFILLS) import \"./polyfills\"; // eslint-disable-line no-extra-boolean-cast\nif(typeof IMPORT_POLYFILLS !== \"undefined\" && !!IMPORT_POLYFILLS) require(\"./polyfills\"); // eslint-disable-line no-extra-boolean-cast, no-undef\n\n/* define default options object */\nconst DEFAULT_OPTIONS = {\n\tdirective: {\n\t\tname: \"v-svg-inline\",\n\t\tspriteModifierName: \"sprite\"\n\t},\n\tattributes: {\n\t\tclone: [ \"viewbox\" ],\n\t\tmerge: [ \"class\", \"style\" ],\n\t\tadd: [ {\n\t\t\tname: \"focusable\",\n\t\t\tvalue: false\n\t\t}, {\n\t\t\tname: \"role\",\n\t\t\tvalue: \"presentation\"\n\t\t}, {\n\t\t\tname: \"tabindex\",\n\t\t\tvalue: -1\n\t\t} ],\n\t\tdata: [],\n\t\tremove: [ \"alt\", \"src\", \"data-src\" ]\n\t},\n\tcache: {\n\t\tversion: PACKAGE_VERSION,\n\t\tpersistent: true,\n\t\tremoveRevisions: true\n\t},\n\tintersectionObserverOptions: {},\n\taxios: null,\n\txhtml: false\n};\n\n/* define reference id for image node intersection observer */\nconst OBSERVER_REF_ID = \"observer\";\n\n/* define reference id for svg symbol container node */\nconst CONTAINER_REF_ID = \"container\";\n\n/* define id for cache map local storage key */\n// Will be defined dynamically based on supplied options.cache.version value.\n// const CACHE_ID = `${PACKAGE_NAME}:${PACKAGE_VERSION}`;\n\n/* define id for image node flags */\nconst FLAGS_ID = `${PACKAGE_NAME}-flags`;\n\n/* define id for svg symbol node*/\nconst SYMBOL_ID = `${PACKAGE_NAME}-sprite`; // + `-<NUMBER>` - will be added dynamically\n\n/* define id for svg symbol container node */\nconst CONTAINER_ID = `${SYMBOL_ID}-${CONTAINER_REF_ID}`;\n\n/* define all regular expressions */\nconst REGEXP_SVG_FILENAME = /.+\\.svg(?:[?#].*)?$/i;\nconst REGEXP_SVG_CONTENT = /<svg(\\s+[^>]+)?>([\\s\\S]+)<\\/svg>/i;\nconst REGEXP_ATTRIBUTES = /\\s*([^\\s=]+)[\\s=]+(?:\"([^\"]*)\"|'([^']*)')?\\s*/g;\nconst REGEXP_ATTRIBUTE_NAME = /^[a-z](?:[a-z0-9-:]*[a-z0-9])?$/i;\nconst REGEXP_VUE_DIRECTIVE = /^v-/i;\nconst REGEXP_WHITESPACE = /\\s+/g;\nconst REGEXP_TEMPLATE_LITERALS_WHITESPACE = /[\\n\\t]+/g;\n\n/* define correct response statuses */\nconst CORRECT_RESPONSE_STATUSES = new Set([\n\t200, // https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/200\n\t304 // https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/304\n]);\n\n/**\n * Install method for Vue plugin.\n * @param {Function|Object} VueOrApp - Vue reference (Vue@2) or Vue instance (Vue@3).\n * @param {Object} options - Options object.\n * @returns {void}\n */\nconst install = (VueOrApp = null, options = {}) => {\n\n\t/* store basic types references */\n\tconst _str = \"string\";\n\tconst _fnc = \"function\";\n\tconst _obj = \"object\";\n\n\t/* throw error if VueOrApp argument is missing */\n\tif(!VueOrApp) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [VueOrApp]`);\n\n\t/* throw error if VueOrApp argument is not valid */\n\tif(![ _fnc, _obj ].includes(typeof VueOrApp)) throw new TypeError(`[${PACKAGE_NAME}] Required argument is not valid! [VueOrApp]`);\n\n\t/* throw error if VueOrApp argument is missing directive method */\n\tif(!VueOrApp.directive) throw new Error(`[${PACKAGE_NAME}] Required method is missing! [VueOrApp.directive]`);\n\n\t/* throw error if VueOrApp.directive method is not valid */\n\tif(typeof VueOrApp.directive !== _fnc) throw new TypeError(`[${PACKAGE_NAME}] Required method is not valid! [VueOrApp.directive]`);\n\n\t/* throw error if VueOrApp argument is missing version property */\n\tif(!VueOrApp.version) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [VueOrApp.version]`);\n\n\t/* throw error if VueOrApp.version property is not valid */\n\tif(typeof VueOrApp.version !== _str) throw new TypeError(`[${PACKAGE_NAME}] Required property is not valid! [VueOrApp.version]`);\n\n\t/* throw error if Vue@1 is detected */\n\tif(VueOrApp.version.startsWith(\"1.\")) throw new Error(`[${PACKAGE_NAME}] Vue@1 is not supported!`);\n\n\t/* merge default options object with supplied options object */\n\t[\"directive\", \"attributes\", \"cache\", \"intersectionObserverOptions\"].forEach(option => options[option] = Object.assign({}, DEFAULT_OPTIONS[option], options[option] || {}));\n\toptions = Object.assign({}, DEFAULT_OPTIONS, options);\n\n\t/* loop over all directives options */\n\tfor(const option in options.directive) {\n\n\t\t/* cast directive option to string */\n\t\toptions.directive[option] = options.directive[option].toString().trim().toLowerCase();\n\n\t\t/* throw error if directive option is not valid */\n\t\tif(!options.directive[option] || option === \"name\" && !REGEXP_ATTRIBUTE_NAME.test(options.directive[option])) throw new TypeError(`[${PACKAGE_NAME}] Option is not valid! [options.directives.${option}=\"${options.directives[option]}\"]`);\n\n\t}\n\n\t/* remove starting `v-` from directive name option */\n\toptions.directive.name = options.directive.name.replace(REGEXP_VUE_DIRECTIVE, \"\");\n\n\t/* loop over all attributes options */\n\tfor(const option in options.attributes) {\n\n\t\t/* throw error if option is not valid */\n\t\tif(!Array.isArray(options.attributes[option])) throw new TypeError(`[${PACKAGE_NAME}] Option is not valid! [options.attributes.${option}=${JSON.stringify(options.attributes[option])}]`);\n\n\t\t/* cast option values to strings */\n\t\toptions.attributes[option] = option === \"add\" ? options.attributes[option].map(attribute => ({\n\t\t\tname: attribute.name.toString().trim().toLowerCase(),\n\t\t\tvalue: attribute.value.toString().trim()\n\t\t})) : options.attributes[option].map(attribute => attribute.toString().trim().toLowerCase());\n\n\t\t/* cast option from array to set */\n\t\toptions.attributes[option] = new Set(options.attributes[option]);\n\n\t}\n\n\t/* loop over all cache options */\n\tfor(const option in options.cache) {\n\n\t\t/* cast option value to string if option is version or boolean otherwise */\n\t\toptions.cache[option] = option === \"version\" ? options.cache[option].toString().trim().toLowerCase() : !!options.cache[option];\n\n\t}\n\n\t/* cast xhtml option to boolean */\n\toptions.xhtml = !!options.xhtml;\n\n\t/* store Vue@3 flag */\n\tconst isVue3 = /* !(VueOrApp instanceof Function) && */ VueOrApp.version.startsWith(\"3.\");\n\n\t/* check if fetch is available */\n\toptions._fetch = \"fetch\" in window && typeof fetch === _fnc;\n\n\t/* check if axios is available */\n\toptions._axios = \"axios\" in window && typeof axios === _fnc;\n\n\t/**\n\t * Validate Axios instance get method.\n\t * @param {Axios} axios - Axios instance.\n\t * @returns {Boolean} Validation result.\n\t */\n\tconst validateAxiosGetMethod = (axios = null) => !!axios && typeof axios === _fnc && \"get\" in axios && typeof axios.get === _fnc;\n\n\t/* axios validation result */\n\tlet axiosIsValid = false;\n\n\t/* create new axios instance if not provided or not valid */\n\toptions.axios = ((axiosIsValid = validateAxiosGetMethod(options.axios)) ? options.axios : null) || (options._axios && \"create\" in axios && typeof axios.create === _fnc ? axios.create() : null); // eslint-disable-line no-cond-assign\n\n\t/* check if axios instance exists and is valid */\n\toptions._axios = axiosIsValid || validateAxiosGetMethod(options.axios);\n\n\t/* throw error if fetch and axios are not available */\n\tif(!options._fetch && !options._axios) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [fetch || axios]`);\n\n\t/* check if intersection observer is available */\n\toptions._observer = \"IntersectionObserver\" in window;\n\n\t/* throw error if intersection observer is not available */\n\t// We log error instead and disable lazy processing of image nodes in processing function - processImageNode().\n\t// if(!options._observer) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [IntersectionObserver]`);\n\tif(!options._observer) console.error(`[${PACKAGE_NAME}] Feature is not supported by browser! Disabling lazy processing of image nodes. [IntersectionObserver]`); // eslint-disable-line no-console\n\n\t/* check if local storage is available */\n\toptions._storage = \"localStorage\" in window;\n\n\t/* throw error if local storage is not available */\n\t// We log error instead and disable caching of SVG files in processing function - fetchSvgFile().\n\t// if(!options._storage && options.cache.persistent) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [localStorage]`);\n\tif(!options._storage && options.cache.persistent) console.error(`[${PACKAGE_NAME}] Feature is not supported by browser! Disabling persistent cache of SVG files. [localStorage]`); // eslint-disable-line no-console\n\n\t/* define id for cache map local storage key */\n\tconst CACHE_ID = `${PACKAGE_NAME}:${options.cache.version}`;\n\n\t/* remove previous cache map revisions */\n\tif(options._storage && options.cache.removeRevisions) Object.entries(localStorage).map(item => item.shift()).filter(item => item.startsWith(`${PACKAGE_NAME}:`) && !item.endsWith(`:${options.cache.version}`)).forEach(item => localStorage.removeItem(item));\n\n\t/* create empty cache map or restore stored cache map */\n\tconst cache = options._storage && options.cache.persistent ? new Map(JSON.parse(localStorage.getItem(CACHE_ID) || \"[]\")) : new Map;\n\n\t/* create empty symbol set */\n\tconst symbols = new Set;\n\n\t/* create empty reference map */\n\tconst refs = new Map;\n\n\t/**\n\t * Create image node intersection observer.\n\t * @returns {IntersectionObserver} Image node intersection observer.\n\t */\n\tconst createImageNodeIntersectionObserver = () => {\n\n\t\t/* throw error if intersection observer is not available in browser */\n\t\tif(!options._observer) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [IntersectionObserver]`);\n\n\t\t/* throw error if image node intersection observer already exists */\n\t\tif(refs.has(OBSERVER_REF_ID)) throw new Error(`[${PACKAGE_NAME}] Can not create image node intersection observer, intersection observer already exists!`);\n\n\t\t/* create image node intersection observer */\n\t\tconst observer = new IntersectionObserver((entries, observer) => {\n\n\t\t\t/* loop over all observer entries */\n\t\t\tfor(const entry of entries) {\n\n\t\t\t\t/* skip if entry is not intersecting */\n\t\t\t\tif(!entry.isIntersecting) continue;\n\n\t\t\t\t/* store image node reference */\n\t\t\t\tconst node = entry.target;\n\n\t\t\t\t/* process image node */\n\t\t\t\tprocessImageNode(node);\n\n\t\t\t\t/* stop observing image node */\n\t\t\t\tobserver.unobserve(node);\n\n\t\t\t}\n\n\t\t}, options.intersectionObserverOptions);\n\n\t\t/* set image node intersection observer reference into reference map */\n\t\trefs.set(OBSERVER_REF_ID, observer);\n\n\t\t/* return image node intersection observer reference */\n\t\treturn observer;\n\n\t};\n\n\t/**\n\t * Return image node intersection observer reference.\n\t * @returns {IntersectionObserver} Image node intersection observer reference.\n\t */\n\tconst getImageNodeIntersectionObserver = () => {\n\n\t\t/* return image node intersection observer reference */\n\t\treturn refs.has(OBSERVER_REF_ID) ? refs.get(OBSERVER_REF_ID) : createImageNodeIntersectionObserver();\n\n\t};\n\n\t/**\n\t * Create and append SVG symbol container node into document body.\n\t * @returns {SVGSVGElement} SVG symbol container node reference.\n\t */\n\tconst createSvgSymbolContainer = () => {\n\n\t\t/* throw error if SVG symbol container node already exists */\n\t\tif(refs.has(CONTAINER_REF_ID)) throw new Error(`[${PACKAGE_NAME}] Can not create SVG symbol container node, container node already exists!`);\n\n\t\t/* create svg symbol container node */\n\t\tlet container = createNode(`<svg xmlns=\"http://www.w3.org/2000/svg\" id=\"${CONTAINER_ID}\" style=\"display: none !important;\"></svg>`);\n\n\t\t/* append svg symbol container node into document body */\n\t\tdocument.body.appendChild(container);\n\n\t\t/* set svg symbol container node reference into reference map */\n\t\trefs.set(CONTAINER_REF_ID, container = document.getElementById(CONTAINER_ID));\n\n\t\t/* return svg symbol container node reference */\n\t\treturn container;\n\n\t};\n\n\t/**\n\t * Return SVG symbol container node reference.\n\t * @returns {SVGSVGElement} SVG symbol container node reference.\n\t */\n\tconst getSvgSymbolContainer = () => {\n\n\t\t/* return svg symbol container node reference */\n\t\treturn refs.has(CONTAINER_REF_ID) ? refs.get(CONTAINER_REF_ID) : createSvgSymbolContainer();\n\n\t};\n\n\t/**\n\t * Create document fragment from string representation of node.\n\t * @param {String} string - String representation of node.\n\t * @returns {DocumentFragment} Document fragment created from string representation of node.\n\t */\n\tconst createNode = (string = \"\") => {\n\n\t\t/* throw error if string argument is missing */\n\t\tif(!string) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [string]`);\n\n\t\t/* cast string argument to string */\n\t\tstring = string.toString().trim();\n\n\t\t/* throw error if string argument is not valid */\n\t\tif(!string.startsWith(\"<\") || !string.endsWith(\">\")) throw new TypeError(`[${PACKAGE_NAME}] Argument is not valid! [string=\"${string}\"]`);\n\n\t\t/* remove unncessary whitespace from string argument */\n\t\tstring = string.replace(REGEXP_TEMPLATE_LITERALS_WHITESPACE, \"\");\n\n\t\t/* return document fragment created from string argument */\n\t\treturn document.createRange().createContextualFragment(string);\n\n\t};\n\n\t/**\n\t * Replace node with new node.\n\t * @param {HTMLElement} node - Node.\n\t * @param {HTMLElement|DocumentFragment} newNode - New node.\n\t * @returns {*}\n\t */\n\tconst replaceNode = (node = null, newNode = null) => {\n\n\t\t/* throw error if node argument is missing */\n\t\tif(!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);\n\n\t\t/* throw error if newNode argument is missing */\n\t\tif(!newNode) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [newNode]`);\n\n\t\t/* throw error if node argument is missing parentNode property */\n\t\tif(!node.parentNode) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [node.parentNode]`);\n\n\t\t/* replace node with new node */\n\t\tnode.parentNode.replaceChild(newNode, node);\n\n\t};\n\n\t/**\n\t * Create attribute map from string representation of node.\n\t * @param {String} string - String representation of node.\n\t * @returns {Map} Attribute map.\n\t */\n\tconst createAttributeMapFromString = (string = \"\") => {\n\n\t\t/* throw error if string argument is missing */\n\t\tif(!string) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [string]`);\n\n\t\t/* cast string argument to string */\n\t\tstring = string.toString().trim();\n\n\t\t/* create empty attribute map */\n\t\tconst attributes = new Map;\n\n\t\t/* set last index of regexp */\n\t\tREGEXP_ATTRIBUTES.lastIndex = 0;\n\n\t\t/* parse attributes into attribute map */\n\t\tlet attribute;\n\t\twhile(attribute = REGEXP_ATTRIBUTES.exec(string)) { // eslint-disable-line no-cond-assign\n\n\t\t\t/* check and fix last index of regexp */\n\t\t\tif(attribute.index === REGEXP_ATTRIBUTES.lastIndex) REGEXP_ATTRIBUTES.lastIndex++;\n\n\t\t\t/* store attribute name reference */\n\t\t\tconst name = (attribute[1] || \"\").trim().toLowerCase();\n\n\t\t\t/* skip loop if attribute name is not set or if it is tag */\n\t\t\tif(!name || name.startsWith(\"<\") || name.endsWith(\">\")) continue;\n\n\t\t\t/* throw error if attribute name is not valid */\n\t\t\tif(!REGEXP_ATTRIBUTE_NAME.test(name)) throw new TypeError(`[${PACKAGE_NAME}] Attribute name is not valid! [attribute=\"${name}\"]`);\n\n\t\t\t/* store attribute value reference */\n\t\t\tconst value = (attribute[2] || attribute[3] || \"\").trim();\n\n\t\t\t/* store attribute in attribute map and handle xhtml transformation if xhtml option is enabled */\n\t\t\tattributes.set(name, value ? value : (options.xhtml ? name : \"\"));\n\n\t\t}\n\n\t\t/* return attribute map */\n\t\treturn attributes;\n\n\t};\n\n\t/**\n\t * Create attribute map from named node attribute map.\n\t * @param {NamedNodeMap} namedNodeAttributeMap - Named node attribute map.\n\t * @returns {Map} Attribute map.\n\t */\n\tconst createAttributeMapFromNamedNodeMap = (namedNodeAttributeMap = null) => {\n\t\t\n\t\t/* throw error if namedNodeAttributeMap argument is missing */\n\t\tif(!namedNodeAttributeMap) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [namedNodeAttributeMap]`);\n\n\t\t/* throw error if path argument is not valid */\n\t\tif(!(namedNodeAttributeMap instanceof NamedNodeMap)) throw new TypeError(`[${PACKAGE_NAME}] Argument is not valid! [namedNodeAttributeMap]`);\n\n\t\t/* transform named node attribute map into attribute map */\n\t\tconst attributes = new Map([ ...namedNodeAttributeMap ].map(({ name, value }) => {\n\n\t\t\t/* parse attribute name */\n\t\t\tname = (name || \"\").trim().toLowerCase();\n\n\t\t\t/* throw error if attribute name is not valid */\n\t\t\tif(!REGEXP_ATTRIBUTE_NAME.test(name)) throw new TypeError(`[${PACKAGE_NAME}] Attribute name is not valid! [attribute=\"${name}\"]`);\n\n\t\t\t/* parse attribute value */\n\t\t\tvalue = (value || \"\").trim();\n\n\t\t\t/* return array of attribute name and attribute value and handle xhtml transformation if xhtml option is enabled */\n\t\t\treturn [ name, value ? value : (options.xhtml ? name : \"\") ];\n\n\t\t}));\n\n\t\t/* return attribute map */\n\t\treturn attributes;\n\n\t};\n\n\t/**\n\t * Fetch SVG file and create SVG file object.\n\t * @param {String} path - Path to SVG file.\n\t * @returns {Promise<Object>} SVG file object.\n\t */\n\tconst fetchSvgFile = (path = \"\") => {\n\n\t\t/* throw error if fetch and axios are not available */\n\t\tif(!options._fetch && !options._axios) throw new Error(`[${PACKAGE_NAME}] Feature is not supported by browser! [fetch || axios]`);\n\n\t\t/* throw error if path argument is missing */\n\t\tif(!path) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [path]`);\n\n\t\t/* cast path argument to string */\n\t\tpath = path.toString().trim();\n\n\t\t/* throw error if path argument is not valid */\n\t\tif(!REGEXP_SVG_FILENAME.test(path)) throw new TypeError(`[${PACKAGE_NAME}] Argument is not valid! [path=\"${path}\"]`);\n\n\t\t/* return promise */\n\t\treturn new Promise((resolve, reject) => {\n\n\t\t\t/* create svg file object and store svg file path in it */\n\t\t\tconst file = { path };\n\n\t\t\t/* resolve svg file object if it is already defined in cache map */\n\t\t\tif(cache.has(file.path)) {\n\t\t\t\tfile.content = cache.get(file.path);\n\t\t\t\treturn resolve(file);\n\t\t\t}\n\n\t\t\t/* fetch svg file */\n\t\t\t(options._axios ? options.axios.get : fetch)(file.path)\n\n\t\t\t\t/* validate response status and return response data as string */\n\t\t\t\t.then(response => {\n\n\t\t\t\t\t/* throw error if response status is wrong */\n\t\t\t\t\tif(!CORRECT_RESPONSE_STATUSES.has(response.status | 0)) throw new Error(`Wrong response status! [response.status=${response.status}]`); // PACKAGE_NAME prefix is not required here, it will be added in reject handler.\n\n\t\t\t\t\t/* return response data as string */\n\t\t\t\t\treturn options._axios ? response.data.toString() : response.text();\n\n\t\t\t\t})\n\n\t\t\t\t/* store and resolve svg file object */\n\t\t\t\t.then(content => {\n\n\t\t\t\t\t/* store svg file content in svg file object */\n\t\t\t\t\tfile.content = content.trim();\n\n\t\t\t\t\t/* store svg file object in cache map */\n\t\t\t\t\tcache.set(file.path, file.content);\n\n\t\t\t\t\t/* store cache map in local storage */\n\t\t\t\t\tif(options._storage && options.cache.persistent) localStorage.setItem(CACHE_ID, JSON.stringify([ ...cache ]));\n\n\t\t\t\t\t/* resolve svg file object */\n\t\t\t\t\treturn resolve(file);\n\n\t\t\t\t})\n\n\t\t\t\t/* catch errors */\n\t\t\t\t.catch(reject);\n\n\t\t});\n\n\t};\n\n\t/**\n\t * Parse SVG file object according to image node.\n\t * @param {Object} file - SVG file object.\n\t * @param {HTMLImageElement} node - Image node.\n\t * @returns {String} String representation of SVG node.\n\t */\n\tconst parseSvgFile = (file = null, node = null) => {\n\n\t\t/* throw error if file argument is missing */\n\t\tif(!file) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [file]`);\n\n\t\t/* throw error if node argument is missing */\n\t\tif(!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);\n\n\t\t/* throw error if file argument is missing path property */\n\t\tif(!file.path) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [file.path]`);\n\n\t\t/* cast path property of file argument to string */\n\t\tfile.path = file.path.toString().trim();\n\n\t\t/* throw error if path property of file argument is not valid */\n\t\tif(!REGEXP_SVG_FILENAME.test(file.path)) throw new TypeError(`[${PACKAGE_NAME}] Argument property is not valid! [file.path=\"${file.path}\"]`);\n\n\t\t/* throw error if file argument is missing content property */\n\t\tif(!file.content) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [file.content]`);\n\n\t\t/* cast content property of file argument to string */\n\t\tfile.content = file.content.toString().trim();\n\n\t\t/* throw error if content property of file argument is not valid */\n\t\tif(!REGEXP_SVG_CONTENT.test(file.content)) throw new TypeError(`[${PACKAGE_NAME}] Argument property is not valid! [file.content=\"${file.content}\"]`);\n\n\t\t/* throw error if node argument is missing outerHTML property */\n\t\tif(!node.outerHTML) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [node.outerHTML]`);\n\n\t\t/* check if image node should be handled as svg inline sprite */\n\t\tif(node[FLAGS_ID].has(\"sprite\")) {\n\n\t\t\t/* replace svg file content with symbol usage reference, which will be defined in svg symbol container node */\n\t\t\tfile.content = file.content.replace(REGEXP_SVG_CONTENT, (svg, attributes, symbol) => { // eslint-disable-line no-unused-vars\n\n\t\t\t\t/* check if requested svg file path is already defined in symbol set */\n\t\t\t\tconst symbolAlreadyDefined = symbols.has(file.path);\n\n\t\t\t\t/* generate id for symbol */\n\t\t\t\tconst id = `${SYMBOL_ID}-${symbolAlreadyDefined ? [ ...symbols ].indexOf(file.path) : symbols.size}`;\n\n\t\t\t\t/* create new symbol if symbol is not defined in symbol set */\n\t\t\t\tif(!symbolAlreadyDefined) {\n\n\t\t\t\t\t/* create new symbol node */\n\t\t\t\t\tconst symbolNode = createNode(`\n\t\t\t\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t\t\t\t<symbol id=\"${id}\"${attributes}>\n\t\t\t\t\t\t\t\t${symbol}\n\t\t\t\t\t\t\t</symbol>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t`);\n\n\t\t\t\t\t/* add new symbol node into svg symbol container node */\n\t\t\t\t\tgetSvgSymbolContainer().appendChild(symbolNode.firstChild.firstChild);\n\n\t\t\t\t\t/* store svg file path in symbol set */\n\t\t\t\t\tsymbols.add(file.path);\n\n\t\t\t\t}\n\n\t\t\t\t/* return symbol node usage reference */\n\t\t\t\treturn `\n\t\t\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"${options.attributes.clone.size && (attributes = createAttributeMapFromString(attributes)) ? ` ${[ ...options.attributes.clone ].filter(attribute => !!attribute && attributes.has(attribute)).map(attribute => `${attribute}=\"${attributes.get(attribute)}\"`).join(\" \")}` : \"\" }>\n\t\t\t\t\t\t<use xlink:href=\"#${id}\" href=\"#${id}\"></use>\n\t\t\t\t\t</svg>\n\t\t\t\t`;\n\n\t\t\t});\n\n\t\t}\n\n\t\t/* inject attributes from attribute map into svg file content */\n\t\treturn file.content.replace(REGEXP_SVG_CONTENT, (svg, attributes, symbol) => { // eslint-disable-line no-unused-vars\n\n\t\t\t/* extract attribute maps */\n\t\t\tconst fileAttributes = createAttributeMapFromString(attributes); // svg\n\t\t\tconst nodeAttributes = createAttributeMapFromNamedNodeMap(node.attributes); // img\n\n\t\t\t/* merge attribute maps */\n\t\t\tattributes = new Map([ ...fileAttributes, ...nodeAttributes ]);\n\n\t\t\t/* store attribute names reference for attributes that should have unique values */\n\t\t\tconst uniqueAttributeValues = new Set([ \"class\" ]);\n\n\t\t\t/* loop over all attributes to merge */\n\t\t\tfor(const attribute of options.attributes.merge) {\n\n\t\t\t\t/* extract attribute values */\n\t\t\t\tconst fileValues = fileAttributes.has(attribute) ? fileAttributes.get(attribute).split(REGEXP_WHITESPACE).filter(value => !!value) : []; // svg\n\t\t\t\tconst nodeValues = nodeAttributes.has(attribute) ? nodeAttributes.get(attribute).split(REGEXP_WHITESPACE).filter(value => !!value) : []; // img\n\n\t\t\t\t/* skip loop if xhtml option is enabled and there are not any values */\n\t\t\t\tif(options.xhtml && !fileValues.length && !nodeValues.length) continue;\n\n\t\t\t\t/* merge attribute values */\n\t\t\t\tconst values = [ ...fileValues, ...nodeValues ];\n\n\t\t\t\t/* set attribute values into attribute map */\n\t\t\t\tattributes.set(attribute, (uniqueAttributeValues.has(attribute) ? [ ...new Set(values) ] : values).join(\" \").trim());\n\n\t\t\t}\n\n\t\t\t/* loop over all attributes to add */\n\t\t\tfor(const attribute of options.attributes.add) {\n\n\t\t\t\t/* extract attribute values */\n\t\t\t\tlet values = attribute.value.split(REGEXP_WHITESPACE).filter(value => !!value);\n\n\t\t\t\t/* check if attribute is already defined in attribute map */\n\t\t\t\tif(attributes.has(attribute.name)) {\n\n\t\t\t\t\t/* throw error if attribute to add already exists and can not be merged */\n\t\t\t\t\tif(!options.attributes.merge.has(attribute.name)) throw new Error(`[${PACKAGE_NAME}] Can not add attribute, attribute already exists. [${attribute.name}]`);\n\n\t\t\t\t\t/* extract attribute values */\n\t\t\t\t\tconst oldValues = attributes.get(attribute.name).split(REGEXP_WHITESPACE).filter(value => !!value);\n\n\t\t\t\t\t/* skip loop if xhtml option is enabled and there are not any values */\n\t\t\t\t\tif(options.xhtml && !values.length && !oldValues.length) continue;\n\n\t\t\t\t\t/* merge attribute values */\n\t\t\t\t\tvalues = [ ...oldValues, ...values ];\n\n\t\t\t\t}\n\n\t\t\t\t/* set attribute values into attribute map */\n\t\t\t\tattributes.set(attribute.name, (uniqueAttributeValues.has(attribute.name) ? [ ...new Set(values) ] : values).join(\" \").trim());\n\n\t\t\t}\n\n\t\t\t/* loop over all attributes to transform into data-attributes */\n\t\t\tfor(const attribute of options.attributes.data) {\n\n\t\t\t\t/* skip if attribute is not defined in attribute map */\n\t\t\t\tif(!attributes.has(attribute)) continue;\n\n\t\t\t\t/* extract attribute values */\n\t\t\t\tlet values = attributes.get(attribute).split(REGEXP_WHITESPACE).filter(value => !!value);\n\n\t\t\t\t/* store data-attribute name reference */\n\t\t\t\tconst dataAttribute = `data-${attribute}`;\n\n\t\t\t\t/* check if data-attribute is already defined in attribute map */\n\t\t\t\tif(attributes.has(dataAttribute)) {\n\n\t\t\t\t\t/* throw error if data-attribute already exists and can not be merged */\n\t\t\t\t\tif(!options.attributes.merge.has(dataAttribute)) throw new Error(`[${PACKAGE_NAME}] Can not transform attribute to data-attribute, data-attribute already exists. [${attribute}]`);\n\n\t\t\t\t\t/* extract data-attribute values */\n\t\t\t\t\tconst oldValues = attributes.get(dataAttribute).split(REGEXP_WHITESPACE).filter(value => !!value);\n\n\t\t\t\t\t/* skip loop if xhtml option is enabled and there are not any values */\n\t\t\t\t\tif(options.xhtml && !values.length && !oldValues.length) continue;\n\n\t\t\t\t\t/* merge attribute values */\n\t\t\t\t\tvalues = [ ...oldValues, ...values ];\n\n\t\t\t\t}\n\n\t\t\t\t/* set data-attribute values into attribute map */\n\t\t\t\tattributes.set(dataAttribute, (uniqueAttributeValues.has(attribute) ? [ ...new Set(values) ] : values).join(\" \").trim());\n\n\t\t\t\t/* add attribute to remove from attribute map into options.attributes.remove set if there is not already present */\n\t\t\t\tif(!options.attributes.remove.has(attribute)) options.attributes.remove.add(attribute);\n\n\t\t\t}\n\n\t\t\t/* loop over all attributes to remove */\n\t\t\tfor(const attribute of options.attributes.remove) {\n\n\t\t\t\t/* skip if attribute is not defined in attribute map */\n\t\t\t\tif(!attributes.has(attribute)) continue;\n\n\t\t\t\t/* remove attribute from attribute map */\n\t\t\t\tattributes.delete(attribute);\n\n\t\t\t}\n\n\t\t\t/* return string representation of svg node with injected attributes */\n\t\t\treturn `\n\t\t\t\t<svg${attributes.size ? ` ${[ ...attributes.keys() ].filter(attribute => !!attribute).map(attribute => `${attribute}=\"${attributes.get(attribute)}\"`).join(\" \")}` : \"\"}>\n\t\t\t\t\t${symbol}\n\t\t\t\t</svg>\n\t\t\t`;\n\n\t\t});\n\n\t};\n\n\t/**\n\t * Process image node - replace image node with SVG node.\n\t * @param {HTMLImageElement} node - Image node.\n\t * @returns {*}\n\t */\n\tconst processImageNode = (node = null) => {\n\n\t\t/* throw error if node argument is missing */\n\t\tif(!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);\n\n\t\t/* throw error if node argument is missing data-src and src property */\n\t\tif(!node.dataset.src && !node.src) throw new Error(`[${PACKAGE_NAME}] Required property is missing! [node.data-src || node.src]`);\n\n\t\t/* cast data-src and src properties of node argument argument to strings if defined */\n\t\tif(node.dataset.src) node.dataset.src = node.dataset.src.toString().trim();\n\t\tif(node.src) node.src = node.src.toString().trim();\n\n\t\t/* fetch svg file */\n\t\tfetchSvgFile(node.dataset.src || node.src)\n\n\t\t\t/* process svg file object */\n\t\t\t.then(file => {\n\n\t\t\t\t/* parse svg file object */\n\t\t\t\tconst svgString = parseSvgFile(file, node);\n\n\t\t\t\t/* create svg node */\n\t\t\t\tconst svgNode = createNode(svgString);\n\n\t\t\t\t/* replace image node with svg node */\n\t\t\t\treplaceNode(node, svgNode);\n\n\t\t\t})\n\n\t\t\t/* catch errors */\n\t\t\t.catch(error => console.error(`[${PACKAGE_NAME}] ${error.toString()}`)); // eslint-disable-line no-console\n\n\t};\n\n\t/**\n\t * BeforeMount hook function for Vue directive.\n\t * @param {HTMLImageElement} node - Node that is binded with directive.\n\t * @param {Object} binding - Object containing directive properties.\n\t * @param {VNode} vnode - Virtual node created by Vue compiler.\n\t * @returns {*}\n\t */\n\tconst beforeMount = (node = null, binding = null, vnode = null) => { // eslint-disable-line no-unused-vars\n\n\t\t/* throw error if node argument is missing */\n\t\tif(!node) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [node]`);\n\n\t\t/* throw error if node argument is not valid */\n\t\tif(node.tagName !== \"IMG\") throw new Error(`[${PACKAGE_NAME}] Required argument is not valid! [node]`);\n\n\t\t/* throw error if vnode argument is missing */\n\t\tif(!vnode) throw new Error(`[${PACKAGE_NAME}] Required argument is missing! [vnode]`);\n\n\t\t/* create empty image node flag set if it is not already defined */\n\t\tif(!node[FLAGS_ID]) node[FLAGS_ID] = new Set;\n\n\t\t/* skip if image node is already processed */\n\t\tif(node[FLAGS_ID].has(\"processed\")) return;\n\n\t\t/* set internal processed flag to image node */\n\t\tnode[FLAGS_ID].add(\"processed\");\n\n\t\t/* store vnode directives reference based on Vue version */\n\t\tconst directives = isVue3 ? vnode.dirs : vnode.data.directives;\n\n\t\t/* throw error if image node has more than 1 directive */\n\t\tif(directives.length > 1) throw new Error(`[${PACKAGE_NAME}] Node has more than 1 directive! [${isVue3 ? \"vnode.dirs\" : \"vnode.data.directives\"}]`);\n\n\t\t/* set internal sprite flag to image node */\n\t\tif(!!directives[0].modifiers[options.directive.spriteModifierName]) node[FLAGS_ID].add(\"sprite\"); // eslint-disable-line no-extra-boolean-cast\n\n\t\t/* disable lazy processing of image node if intersection observer is not available */\n\t\tif(!options._observer && node.dataset.src) {\n\n\t\t\t/* transform data-src attribute to src attribute of image node */\n\t\t\tnode.src = node.dataset.src;\n\t\t\tdelete node.dataset.src;\n\n\t\t}\n\n\t\t/* process image node */\n\t\tif(node.dataset.src) getImageNodeIntersectionObserver().observe(node);\n\t\telse processImageNode(node);\n\n\t};\n\n\t/* define vue svg inline directive */\n\tVueOrApp.directive(options.directive.name, isVue3 ? { beforeMount } : { bind: beforeMount });\n\n};\n\n/* export Vue plugin */\nexport default { install };\n"], "mappings": ";;;;;;;;;AAAA;AAAA,EACC,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,OAAS;AAAA,EACT,UAAY;AAAA,EACZ,OAAS;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACA,aAAe;AAAA,IACd,SAAW;AAAA,EACZ;AAAA,EACA,SAAW;AAAA,IACV,OAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,QAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,SAAW;AAAA,IACX,MAAQ;AAAA,EACT;AAAA,EACA,YAAc;AAAA,IACb,MAAQ;AAAA,IACR,KAAO;AAAA,EACR;AAAA,EACA,UAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACA,QAAU;AAAA,EACV,SAAW;AAAA,EACX,MAAQ;AAAA,IACP,KAAO;AAAA,EACR;AAAA,EACA,UAAY;AAAA,EACZ,cAAgB;AAAA,IACf,WAAW;AAAA,IACX,yBAAyB;AAAA,IACzB,gBAAgB;AAAA,EACjB;AAAA,EACA,iBAAmB;AAAA,IAClB,eAAe;AAAA,IACf,wBAAwB;AAAA,IACxB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,oDAAoD;AAAA,IACpD,aAAa;AAAA,IACb,QAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,QAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,SAAW;AAAA,IACX,2BAA2B;AAAA,IAC3B,eAAe;AAAA,EAChB;AACD;;;ACxDA,IAAM,eAAe,gBAAa;AAGlC,IAAM,kBAAkB,gBAAa;AAKrC,IAAG,OAAO,qBAAqB,eAAe,CAAC,CAAC,iBAAkB;AAGlE,IAAM,kBAAkB;AAAA,EACvB,WAAW;AAAA,IACV,MAAM;AAAA,IACN,oBAAoB;AAAA,EACrB;AAAA,EACA,YAAY;AAAA,IACX,OAAO,CAAE,SAAU;AAAA,IACnB,OAAO,CAAE,SAAS,OAAQ;AAAA,IAC1B,KAAK,CAAE;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACR,GAAG;AAAA,MACF,MAAM;AAAA,MACN,OAAO;AAAA,IACR,GAAG;AAAA,MACF,MAAM;AAAA,MACN,OAAO;AAAA,IACR,CAAE;AAAA,IACF,MAAM,CAAC;AAAA,IACP,QAAQ,CAAE,OAAO,OAAO,UAAW;AAAA,EACpC;AAAA,EACA,OAAO;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,iBAAiB;AAAA,EAClB;AAAA,EACA,6BAA6B,CAAC;AAAA,EAC9B,OAAO;AAAA,EACP,OAAO;AACR;AAGA,IAAM,kBAAkB;AAGxB,IAAM,mBAAmB;AAOzB,IAAM,WAAW,GAAG,YAAY;AAGhC,IAAM,YAAY,GAAG,YAAY;AAGjC,IAAM,eAAe,GAAG,SAAS,IAAI,gBAAgB;AAGrD,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,uBAAuB;AAC7B,IAAM,oBAAoB;AAC1B,IAAM,sCAAsC;AAG5C,IAAM,4BAA4B,oBAAI,IAAI;AAAA,EACzC;AAAA;AAAA,EACA;AAAA;AACD,CAAC;AAQD,IAAM,UAAU,CAAC,WAAW,MAAM,UAAU,CAAC,MAAM;AAGlD,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,OAAO;AAGb,MAAG,CAAC,SAAU,OAAM,IAAI,MAAM,IAAI,YAAY,4CAA4C;AAG1F,MAAG,CAAC,CAAE,MAAM,IAAK,EAAE,SAAS,OAAO,QAAQ,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,8CAA8C;AAGhI,MAAG,CAAC,SAAS,UAAW,OAAM,IAAI,MAAM,IAAI,YAAY,oDAAoD;AAG5G,MAAG,OAAO,SAAS,cAAc,KAAM,OAAM,IAAI,UAAU,IAAI,YAAY,sDAAsD;AAGjI,MAAG,CAAC,SAAS,QAAS,OAAM,IAAI,MAAM,IAAI,YAAY,oDAAoD;AAG1G,MAAG,OAAO,SAAS,YAAY,KAAM,OAAM,IAAI,UAAU,IAAI,YAAY,sDAAsD;AAG/H,MAAG,SAAS,QAAQ,WAAW,IAAI,EAAG,OAAM,IAAI,MAAM,IAAI,YAAY,2BAA2B;AAGjG,GAAC,aAAa,cAAc,SAAS,6BAA6B,EAAE,QAAQ,YAAU,QAAQ,MAAM,IAAI,OAAO,OAAO,CAAC,GAAG,gBAAgB,MAAM,GAAG,QAAQ,MAAM,KAAK,CAAC,CAAC,CAAC;AACzK,YAAU,OAAO,OAAO,CAAC,GAAG,iBAAiB,OAAO;AAGpD,aAAU,UAAU,QAAQ,WAAW;AAGtC,YAAQ,UAAU,MAAM,IAAI,QAAQ,UAAU,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY;AAGpF,QAAG,CAAC,QAAQ,UAAU,MAAM,KAAK,WAAW,UAAU,CAAC,sBAAsB,KAAK,QAAQ,UAAU,MAAM,CAAC,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,8CAA8C,MAAM,KAAK,QAAQ,WAAW,MAAM,CAAC,IAAI;AAAA,EAE1O;AAGA,UAAQ,UAAU,OAAO,QAAQ,UAAU,KAAK,QAAQ,sBAAsB,EAAE;AAGhF,aAAU,UAAU,QAAQ,YAAY;AAGvC,QAAG,CAAC,MAAM,QAAQ,QAAQ,WAAW,MAAM,CAAC,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,8CAA8C,MAAM,IAAI,KAAK,UAAU,QAAQ,WAAW,MAAM,CAAC,CAAC,GAAG;AAGxL,YAAQ,WAAW,MAAM,IAAI,WAAW,QAAQ,QAAQ,WAAW,MAAM,EAAE,IAAI,gBAAc;AAAA,MAC5F,MAAM,UAAU,KAAK,SAAS,EAAE,KAAK,EAAE,YAAY;AAAA,MACnD,OAAO,UAAU,MAAM,SAAS,EAAE,KAAK;AAAA,IACxC,EAAE,IAAI,QAAQ,WAAW,MAAM,EAAE,IAAI,eAAa,UAAU,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC;AAG3F,YAAQ,WAAW,MAAM,IAAI,IAAI,IAAI,QAAQ,WAAW,MAAM,CAAC;AAAA,EAEhE;AAGA,aAAU,UAAU,QAAQ,OAAO;AAGlC,YAAQ,MAAM,MAAM,IAAI,WAAW,YAAY,QAAQ,MAAM,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,IAAI,CAAC,CAAC,QAAQ,MAAM,MAAM;AAAA,EAE9H;AAGA,UAAQ,QAAQ,CAAC,CAAC,QAAQ;AAG1B,QAAM;AAAA;AAAA,IAAkD,SAAS,QAAQ,WAAW,IAAI;AAAA;AAGxF,UAAQ,SAAS,WAAW,UAAU,OAAO,UAAU;AAGvD,UAAQ,SAAS,WAAW,UAAU,OAAO,UAAU;AAOvD,QAAM,yBAAyB,CAACA,SAAQ,SAAS,CAAC,CAACA,UAAS,OAAOA,WAAU,QAAQ,SAASA,UAAS,OAAOA,OAAM,QAAQ;AAG5H,MAAI,eAAe;AAGnB,UAAQ,UAAU,eAAe,uBAAuB,QAAQ,KAAK,KAAK,QAAQ,QAAQ,UAAU,QAAQ,UAAU,YAAY,SAAS,OAAO,MAAM,WAAW,OAAO,MAAM,OAAO,IAAI;AAG3L,UAAQ,SAAS,gBAAgB,uBAAuB,QAAQ,KAAK;AAGrE,MAAG,CAAC,QAAQ,UAAU,CAAC,QAAQ,OAAQ,OAAM,IAAI,MAAM,IAAI,YAAY,yDAAyD;AAGhI,UAAQ,YAAY,0BAA0B;AAK9C,MAAG,CAAC,QAAQ,UAAW,SAAQ,MAAM,IAAI,YAAY,yGAAyG;AAG9J,UAAQ,WAAW,kBAAkB;AAKrC,MAAG,CAAC,QAAQ,YAAY,QAAQ,MAAM,WAAY,SAAQ,MAAM,IAAI,YAAY,gGAAgG;AAGhL,QAAM,WAAW,GAAG,YAAY,IAAI,QAAQ,MAAM,OAAO;AAGzD,MAAG,QAAQ,YAAY,QAAQ,MAAM,gBAAiB,QAAO,QAAQ,YAAY,EAAE,IAAI,UAAQ,KAAK,MAAM,CAAC,EAAE,OAAO,UAAQ,KAAK,WAAW,GAAG,YAAY,GAAG,KAAK,CAAC,KAAK,SAAS,IAAI,QAAQ,MAAM,OAAO,EAAE,CAAC,EAAE,QAAQ,UAAQ,aAAa,WAAW,IAAI,CAAC;AAG7P,QAAM,QAAQ,QAAQ,YAAY,QAAQ,MAAM,aAAa,IAAI,IAAI,KAAK,MAAM,aAAa,QAAQ,QAAQ,KAAK,IAAI,CAAC,IAAI,oBAAI;AAG/H,QAAM,UAAU,oBAAI;AAGpB,QAAM,OAAO,oBAAI;AAMjB,QAAM,sCAAsC,MAAM;AAGjD,QAAG,CAAC,QAAQ,UAAW,OAAM,IAAI,MAAM,IAAI,YAAY,+DAA+D;AAGtH,QAAG,KAAK,IAAI,eAAe,EAAG,OAAM,IAAI,MAAM,IAAI,YAAY,0FAA0F;AAGxJ,UAAM,WAAW,IAAI,qBAAqB,CAAC,SAASC,cAAa;AAGhE,iBAAU,SAAS,SAAS;AAG3B,YAAG,CAAC,MAAM,eAAgB;AAG1B,cAAM,OAAO,MAAM;AAGnB,yBAAiB,IAAI;AAGrB,QAAAA,UAAS,UAAU,IAAI;AAAA,MAExB;AAAA,IAED,GAAG,QAAQ,2BAA2B;AAGtC,SAAK,IAAI,iBAAiB,QAAQ;AAGlC,WAAO;AAAA,EAER;AAMA,QAAM,mCAAmC,MAAM;AAG9C,WAAO,KAAK,IAAI,eAAe,IAAI,KAAK,IAAI,eAAe,IAAI,oCAAoC;AAAA,EAEpG;AAMA,QAAM,2BAA2B,MAAM;AAGtC,QAAG,KAAK,IAAI,gBAAgB,EAAG,OAAM,IAAI,MAAM,IAAI,YAAY,4EAA4E;AAG3I,QAAI,YAAY,WAAW,+CAA+C,YAAY,4CAA4C;AAGlI,aAAS,KAAK,YAAY,SAAS;AAGnC,SAAK,IAAI,kBAAkB,YAAY,SAAS,eAAe,YAAY,CAAC;AAG5E,WAAO;AAAA,EAER;AAMA,QAAM,wBAAwB,MAAM;AAGnC,WAAO,KAAK,IAAI,gBAAgB,IAAI,KAAK,IAAI,gBAAgB,IAAI,yBAAyB;AAAA,EAE3F;AAOA,QAAM,aAAa,CAAC,SAAS,OAAO;AAGnC,QAAG,CAAC,OAAQ,OAAM,IAAI,MAAM,IAAI,YAAY,0CAA0C;AAGtF,aAAS,OAAO,SAAS,EAAE,KAAK;AAGhC,QAAG,CAAC,OAAO,WAAW,GAAG,KAAK,CAAC,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,qCAAqC,MAAM,IAAI;AAGxI,aAAS,OAAO,QAAQ,qCAAqC,EAAE;AAG/D,WAAO,SAAS,YAAY,EAAE,yBAAyB,MAAM;AAAA,EAE9D;AAQA,QAAM,cAAc,CAAC,OAAO,MAAM,UAAU,SAAS;AAGpD,QAAG,CAAC,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,wCAAwC;AAGlF,QAAG,CAAC,QAAS,OAAM,IAAI,MAAM,IAAI,YAAY,2CAA2C;AAGxF,QAAG,CAAC,KAAK,WAAY,OAAM,IAAI,MAAM,IAAI,YAAY,mDAAmD;AAGxG,SAAK,WAAW,aAAa,SAAS,IAAI;AAAA,EAE3C;AAOA,QAAM,+BAA+B,CAAC,SAAS,OAAO;AAGrD,QAAG,CAAC,OAAQ,OAAM,IAAI,MAAM,IAAI,YAAY,0CAA0C;AAGtF,aAAS,OAAO,SAAS,EAAE,KAAK;AAGhC,UAAM,aAAa,oBAAI;AAGvB,sBAAkB,YAAY;AAG9B,QAAI;AACJ,WAAM,YAAY,kBAAkB,KAAK,MAAM,GAAG;AAGjD,UAAG,UAAU,UAAU,kBAAkB,UAAW,mBAAkB;AAGtE,YAAM,QAAQ,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE,YAAY;AAGrD,UAAG,CAAC,QAAQ,KAAK,WAAW,GAAG,KAAK,KAAK,SAAS,GAAG,EAAG;AAGxD,UAAG,CAAC,sBAAsB,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,8CAA8C,IAAI,IAAI;AAGhI,YAAM,SAAS,UAAU,CAAC,KAAK,UAAU,CAAC,KAAK,IAAI,KAAK;AAGxD,iBAAW,IAAI,MAAM,QAAQ,QAAS,QAAQ,QAAQ,OAAO,EAAG;AAAA,IAEjE;AAGA,WAAO;AAAA,EAER;AAOA,QAAM,qCAAqC,CAAC,wBAAwB,SAAS;AAG5E,QAAG,CAAC,sBAAuB,OAAM,IAAI,MAAM,IAAI,YAAY,yDAAyD;AAGpH,QAAG,EAAE,iCAAiC,cAAe,OAAM,IAAI,UAAU,IAAI,YAAY,kDAAkD;AAG3I,UAAM,aAAa,IAAI,IAAI,CAAE,GAAG,qBAAsB,EAAE,IAAI,CAAC,EAAE,MAAM,MAAM,MAAM;AAGhF,cAAQ,QAAQ,IAAI,KAAK,EAAE,YAAY;AAGvC,UAAG,CAAC,sBAAsB,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,8CAA8C,IAAI,IAAI;AAGhI,eAAS,SAAS,IAAI,KAAK;AAG3B,aAAO,CAAE,MAAM,QAAQ,QAAS,QAAQ,QAAQ,OAAO,EAAI;AAAA,IAE5D,CAAC,CAAC;AAGF,WAAO;AAAA,EAER;AAOA,QAAM,eAAe,CAAC,OAAO,OAAO;AAGnC,QAAG,CAAC,QAAQ,UAAU,CAAC,QAAQ,OAAQ,OAAM,IAAI,MAAM,IAAI,YAAY,yDAAyD;AAGhI,QAAG,CAAC,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,wCAAwC;AAGlF,WAAO,KAAK,SAAS,EAAE,KAAK;AAG5B,QAAG,CAAC,oBAAoB,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,mCAAmC,IAAI,IAAI;AAGnH,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAGvC,YAAM,OAAO,EAAE,KAAK;AAGpB,UAAG,MAAM,IAAI,KAAK,IAAI,GAAG;AACxB,aAAK,UAAU,MAAM,IAAI,KAAK,IAAI;AAClC,eAAO,QAAQ,IAAI;AAAA,MACpB;AAGA,OAAC,QAAQ,SAAS,QAAQ,MAAM,MAAM,OAAO,KAAK,IAAI,EAGpD,KAAK,cAAY;AAGjB,YAAG,CAAC,0BAA0B,IAAI,SAAS,SAAS,CAAC,EAAG,OAAM,IAAI,MAAM,2CAA2C,SAAS,MAAM,GAAG;AAGrI,eAAO,QAAQ,SAAS,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK;AAAA,MAElE,CAAC,EAGA,KAAK,aAAW;AAGhB,aAAK,UAAU,QAAQ,KAAK;AAG5B,cAAM,IAAI,KAAK,MAAM,KAAK,OAAO;AAGjC,YAAG,QAAQ,YAAY,QAAQ,MAAM,WAAY,cAAa,QAAQ,UAAU,KAAK,UAAU,CAAE,GAAG,KAAM,CAAC,CAAC;AAG5G,eAAO,QAAQ,IAAI;AAAA,MAEpB,CAAC,EAGA,MAAM,MAAM;AAAA,IAEf,CAAC;AAAA,EAEF;AAQA,QAAM,eAAe,CAAC,OAAO,MAAM,OAAO,SAAS;AAGlD,QAAG,CAAC,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,wCAAwC;AAGlF,QAAG,CAAC,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,wCAAwC;AAGlF,QAAG,CAAC,KAAK,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,6CAA6C;AAG5F,SAAK,OAAO,KAAK,KAAK,SAAS,EAAE,KAAK;AAGtC,QAAG,CAAC,oBAAoB,KAAK,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,iDAAiD,KAAK,IAAI,IAAI;AAG3I,QAAG,CAAC,KAAK,QAAS,OAAM,IAAI,MAAM,IAAI,YAAY,gDAAgD;AAGlG,SAAK,UAAU,KAAK,QAAQ,SAAS,EAAE,KAAK;AAG5C,QAAG,CAAC,mBAAmB,KAAK,KAAK,OAAO,EAAG,OAAM,IAAI,UAAU,IAAI,YAAY,oDAAoD,KAAK,OAAO,IAAI;AAGnJ,QAAG,CAAC,KAAK,UAAW,OAAM,IAAI,MAAM,IAAI,YAAY,kDAAkD;AAGtG,QAAG,KAAK,QAAQ,EAAE,IAAI,QAAQ,GAAG;AAGhC,WAAK,UAAU,KAAK,QAAQ,QAAQ,oBAAoB,CAAC,KAAK,YAAY,WAAW;AAGpF,cAAM,uBAAuB,QAAQ,IAAI,KAAK,IAAI;AAGlD,cAAM,KAAK,GAAG,SAAS,IAAI,uBAAuB,CAAE,GAAG,OAAQ,EAAE,QAAQ,KAAK,IAAI,IAAI,QAAQ,IAAI;AAGlG,YAAG,CAAC,sBAAsB;AAGzB,gBAAM,aAAa,WAAW;AAAA;AAAA,qBAEd,EAAE,IAAI,UAAU;AAAA,UAC3B,MAAM;AAAA;AAAA;AAAA,MAGV;AAGD,gCAAsB,EAAE,YAAY,WAAW,WAAW,UAAU;AAGpE,kBAAQ,IAAI,KAAK,IAAI;AAAA,QAEtB;AAGA,eAAO;AAAA,yFAC8E,QAAQ,WAAW,MAAM,SAAS,aAAa,6BAA6B,UAAU,KAAK,IAAI,CAAE,GAAG,QAAQ,WAAW,KAAM,EAAE,OAAO,eAAa,CAAC,CAAC,aAAa,WAAW,IAAI,SAAS,CAAC,EAAE,IAAI,eAAa,GAAG,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,KAAK,EAAG;AAAA,0BAC7U,EAAE,YAAY,EAAE;AAAA;AAAA;AAAA,MAIvC,CAAC;AAAA,IAEF;AAGA,WAAO,KAAK,QAAQ,QAAQ,oBAAoB,CAAC,KAAK,YAAY,WAAW;AAG5E,YAAM,iBAAiB,6BAA6B,UAAU;AAC9D,YAAM,iBAAiB,mCAAmC,KAAK,UAAU;AAGzE,mBAAa,IAAI,IAAI,CAAE,GAAG,gBAAgB,GAAG,cAAe,CAAC;AAG7D,YAAM,wBAAwB,oBAAI,IAAI,CAAE,OAAQ,CAAC;AAGjD,iBAAU,aAAa,QAAQ,WAAW,OAAO;AAGhD,cAAM,aAAa,eAAe,IAAI,SAAS,IAAI,eAAe,IAAI,SAAS,EAAE,MAAM,iBAAiB,EAAE,OAAO,WAAS,CAAC,CAAC,KAAK,IAAI,CAAC;AACtI,cAAM,aAAa,eAAe,IAAI,SAAS,IAAI,eAAe,IAAI,SAAS,EAAE,MAAM,iBAAiB,EAAE,OAAO,WAAS,CAAC,CAAC,KAAK,IAAI,CAAC;AAGtI,YAAG,QAAQ,SAAS,CAAC,WAAW,UAAU,CAAC,WAAW,OAAQ;AAG9D,cAAM,SAAS,CAAE,GAAG,YAAY,GAAG,UAAW;AAG9C,mBAAW,IAAI,YAAY,sBAAsB,IAAI,SAAS,IAAI,CAAE,GAAG,IAAI,IAAI,MAAM,CAAE,IAAI,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAC;AAAA,MAEpH;AAGA,iBAAU,aAAa,QAAQ,WAAW,KAAK;AAG9C,YAAI,SAAS,UAAU,MAAM,MAAM,iBAAiB,EAAE,OAAO,WAAS,CAAC,CAAC,KAAK;AAG7E,YAAG,WAAW,IAAI,UAAU,IAAI,GAAG;AAGlC,cAAG,CAAC,QAAQ,WAAW,MAAM,IAAI,UAAU,IAAI,EAAG,OAAM,IAAI,MAAM,IAAI,YAAY,uDAAuD,UAAU,IAAI,GAAG;AAG1J,gBAAM,YAAY,WAAW,IAAI,UAAU,IAAI,EAAE,MAAM,iBAAiB,EAAE,OAAO,WAAS,CAAC,CAAC,KAAK;AAGjG,cAAG,QAAQ,SAAS,CAAC,OAAO,UAAU,CAAC,UAAU,OAAQ;AAGzD,mBAAS,CAAE,GAAG,WAAW,GAAG,MAAO;AAAA,QAEpC;AAGA,mBAAW,IAAI,UAAU,OAAO,sBAAsB,IAAI,UAAU,IAAI,IAAI,CAAE,GAAG,IAAI,IAAI,MAAM,CAAE,IAAI,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAC;AAAA,MAE9H;AAGA,iBAAU,aAAa,QAAQ,WAAW,MAAM;AAG/C,YAAG,CAAC,WAAW,IAAI,SAAS,EAAG;AAG/B,YAAI,SAAS,WAAW,IAAI,SAAS,EAAE,MAAM,iBAAiB,EAAE,OAAO,WAAS,CAAC,CAAC,KAAK;AAGvF,cAAM,gBAAgB,QAAQ,SAAS;AAGvC,YAAG,WAAW,IAAI,aAAa,GAAG;AAGjC,cAAG,CAAC,QAAQ,WAAW,MAAM,IAAI,aAAa,EAAG,OAAM,IAAI,MAAM,IAAI,YAAY,oFAAoF,SAAS,GAAG;AAGjL,gBAAM,YAAY,WAAW,IAAI,aAAa,EAAE,MAAM,iBAAiB,EAAE,OAAO,WAAS,CAAC,CAAC,KAAK;AAGhG,cAAG,QAAQ,SAAS,CAAC,OAAO,UAAU,CAAC,UAAU,OAAQ;AAGzD,mBAAS,CAAE,GAAG,WAAW,GAAG,MAAO;AAAA,QAEpC;AAGA,mBAAW,IAAI,gBAAgB,sBAAsB,IAAI,SAAS,IAAI,CAAE,GAAG,IAAI,IAAI,MAAM,CAAE,IAAI,QAAQ,KAAK,GAAG,EAAE,KAAK,CAAC;AAGvH,YAAG,CAAC,QAAQ,WAAW,OAAO,IAAI,SAAS,EAAG,SAAQ,WAAW,OAAO,IAAI,SAAS;AAAA,MAEtF;AAGA,iBAAU,aAAa,QAAQ,WAAW,QAAQ;AAGjD,YAAG,CAAC,WAAW,IAAI,SAAS,EAAG;AAG/B,mBAAW,OAAO,SAAS;AAAA,MAE5B;AAGA,aAAO;AAAA,UACA,WAAW,OAAO,IAAI,CAAE,GAAG,WAAW,KAAK,CAAE,EAAE,OAAO,eAAa,CAAC,CAAC,SAAS,EAAE,IAAI,eAAa,GAAG,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE;AAAA,OACnK,MAAM;AAAA;AAAA;AAAA,IAIX,CAAC;AAAA,EAEF;AAOA,QAAM,mBAAmB,CAAC,OAAO,SAAS;AAGzC,QAAG,CAAC,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,wCAAwC;AAGlF,QAAG,CAAC,KAAK,QAAQ,OAAO,CAAC,KAAK,IAAK,OAAM,IAAI,MAAM,IAAI,YAAY,6DAA6D;AAGhI,QAAG,KAAK,QAAQ,IAAK,MAAK,QAAQ,MAAM,KAAK,QAAQ,IAAI,SAAS,EAAE,KAAK;AACzE,QAAG,KAAK,IAAK,MAAK,MAAM,KAAK,IAAI,SAAS,EAAE,KAAK;AAGjD,iBAAa,KAAK,QAAQ,OAAO,KAAK,GAAG,EAGvC,KAAK,UAAQ;AAGb,YAAM,YAAY,aAAa,MAAM,IAAI;AAGzC,YAAM,UAAU,WAAW,SAAS;AAGpC,kBAAY,MAAM,OAAO;AAAA,IAE1B,CAAC,EAGA,MAAM,WAAS,QAAQ,MAAM,IAAI,YAAY,KAAK,MAAM,SAAS,CAAC,EAAE,CAAC;AAAA,EAExE;AASA,QAAM,cAAc,CAAC,OAAO,MAAM,UAAU,MAAM,QAAQ,SAAS;AAGlE,QAAG,CAAC,KAAM,OAAM,IAAI,MAAM,IAAI,YAAY,wCAAwC;AAGlF,QAAG,KAAK,YAAY,MAAO,OAAM,IAAI,MAAM,IAAI,YAAY,0CAA0C;AAGrG,QAAG,CAAC,MAAO,OAAM,IAAI,MAAM,IAAI,YAAY,yCAAyC;AAGpF,QAAG,CAAC,KAAK,QAAQ,EAAG,MAAK,QAAQ,IAAI,oBAAI;AAGzC,QAAG,KAAK,QAAQ,EAAE,IAAI,WAAW,EAAG;AAGpC,SAAK,QAAQ,EAAE,IAAI,WAAW;AAG9B,UAAM,aAAa,SAAS,MAAM,OAAO,MAAM,KAAK;AAGpD,QAAG,WAAW,SAAS,EAAG,OAAM,IAAI,MAAM,IAAI,YAAY,sCAAsC,SAAS,eAAe,uBAAuB,GAAG;AAGlJ,QAAG,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,QAAQ,UAAU,kBAAkB,EAAG,MAAK,QAAQ,EAAE,IAAI,QAAQ;AAG/F,QAAG,CAAC,QAAQ,aAAa,KAAK,QAAQ,KAAK;AAG1C,WAAK,MAAM,KAAK,QAAQ;AACxB,aAAO,KAAK,QAAQ;AAAA,IAErB;AAGA,QAAG,KAAK,QAAQ,IAAK,kCAAiC,EAAE,QAAQ,IAAI;AAAA,QAC/D,kBAAiB,IAAI;AAAA,EAE3B;AAGA,WAAS,UAAU,QAAQ,UAAU,MAAM,SAAS,EAAE,YAAY,IAAI,EAAE,MAAM,YAAY,CAAC;AAE5F;AAGA,IAAO,cAAQ,EAAE,QAAQ;", "names": ["axios", "observer"]}