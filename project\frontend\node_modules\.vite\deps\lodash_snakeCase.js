import {
  require_createCompounder
} from "./chunk-32OCQV76.js";
import "./chunk-TPPCP22B.js";
import "./chunk-VZITUV5G.js";
import "./chunk-CWSHORJK.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/snakeCase.js
var require_snakeCase = __commonJS({
  "node_modules/lodash/snakeCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var snakeCase = createCompounder(function(result, word, index) {
      return result + (index ? "_" : "") + word.toLowerCase();
    });
    module.exports = snakeCase;
  }
});
export default require_snakeCase();
//# sourceMappingURL=lodash_snakeCase.js.map
