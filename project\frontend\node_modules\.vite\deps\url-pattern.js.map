{"version": 3, "sources": ["../../url-pattern/lib/url-pattern.js"], "sourcesContent": ["// Generated by CoffeeScript 1.10.0\nvar slice = [].slice;\n\n(function(root, factory) {\n  if (('function' === typeof define) && (define.amd != null)) {\n    return define([], factory);\n  } else if (typeof exports !== \"undefined\" && exports !== null) {\n    return module.exports = factory();\n  } else {\n    return root.UrlPattern = factory();\n  }\n})(this, function() {\n  var P, UrlPattern, astNodeContainsSegmentsForProvidedParams, astNodeToNames, astNodeToRegexString, baseAstNodeToRegexString, concatMap, defaultOptions, escapeForRegex, getParam, keysAndValuesToObject, newParser, regexGroupCount, stringConcatMap, stringify;\n  escapeForRegex = function(string) {\n    return string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n  };\n  concatMap = function(array, f) {\n    var i, length, results;\n    results = [];\n    i = -1;\n    length = array.length;\n    while (++i < length) {\n      results = results.concat(f(array[i]));\n    }\n    return results;\n  };\n  stringConcatMap = function(array, f) {\n    var i, length, result;\n    result = '';\n    i = -1;\n    length = array.length;\n    while (++i < length) {\n      result += f(array[i]);\n    }\n    return result;\n  };\n  regexGroupCount = function(regex) {\n    return (new RegExp(regex.toString() + '|')).exec('').length - 1;\n  };\n  keysAndValuesToObject = function(keys, values) {\n    var i, key, length, object, value;\n    object = {};\n    i = -1;\n    length = keys.length;\n    while (++i < length) {\n      key = keys[i];\n      value = values[i];\n      if (value == null) {\n        continue;\n      }\n      if (object[key] != null) {\n        if (!Array.isArray(object[key])) {\n          object[key] = [object[key]];\n        }\n        object[key].push(value);\n      } else {\n        object[key] = value;\n      }\n    }\n    return object;\n  };\n  P = {};\n  P.Result = function(value, rest) {\n    this.value = value;\n    this.rest = rest;\n  };\n  P.Tagged = function(tag, value) {\n    this.tag = tag;\n    this.value = value;\n  };\n  P.tag = function(tag, parser) {\n    return function(input) {\n      var result, tagged;\n      result = parser(input);\n      if (result == null) {\n        return;\n      }\n      tagged = new P.Tagged(tag, result.value);\n      return new P.Result(tagged, result.rest);\n    };\n  };\n  P.regex = function(regex) {\n    return function(input) {\n      var matches, result;\n      matches = regex.exec(input);\n      if (matches == null) {\n        return;\n      }\n      result = matches[0];\n      return new P.Result(result, input.slice(result.length));\n    };\n  };\n  P.sequence = function() {\n    var parsers;\n    parsers = 1 <= arguments.length ? slice.call(arguments, 0) : [];\n    return function(input) {\n      var i, length, parser, rest, result, values;\n      i = -1;\n      length = parsers.length;\n      values = [];\n      rest = input;\n      while (++i < length) {\n        parser = parsers[i];\n        result = parser(rest);\n        if (result == null) {\n          return;\n        }\n        values.push(result.value);\n        rest = result.rest;\n      }\n      return new P.Result(values, rest);\n    };\n  };\n  P.pick = function() {\n    var indexes, parsers;\n    indexes = arguments[0], parsers = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    return function(input) {\n      var array, result;\n      result = P.sequence.apply(P, parsers)(input);\n      if (result == null) {\n        return;\n      }\n      array = result.value;\n      result.value = array[indexes];\n      return result;\n    };\n  };\n  P.string = function(string) {\n    var length;\n    length = string.length;\n    return function(input) {\n      if (input.slice(0, length) === string) {\n        return new P.Result(string, input.slice(length));\n      }\n    };\n  };\n  P.lazy = function(fn) {\n    var cached;\n    cached = null;\n    return function(input) {\n      if (cached == null) {\n        cached = fn();\n      }\n      return cached(input);\n    };\n  };\n  P.baseMany = function(parser, end, stringResult, atLeastOneResultRequired, input) {\n    var endResult, parserResult, rest, results;\n    rest = input;\n    results = stringResult ? '' : [];\n    while (true) {\n      if (end != null) {\n        endResult = end(rest);\n        if (endResult != null) {\n          break;\n        }\n      }\n      parserResult = parser(rest);\n      if (parserResult == null) {\n        break;\n      }\n      if (stringResult) {\n        results += parserResult.value;\n      } else {\n        results.push(parserResult.value);\n      }\n      rest = parserResult.rest;\n    }\n    if (atLeastOneResultRequired && results.length === 0) {\n      return;\n    }\n    return new P.Result(results, rest);\n  };\n  P.many1 = function(parser) {\n    return function(input) {\n      return P.baseMany(parser, null, false, true, input);\n    };\n  };\n  P.concatMany1Till = function(parser, end) {\n    return function(input) {\n      return P.baseMany(parser, end, true, true, input);\n    };\n  };\n  P.firstChoice = function() {\n    var parsers;\n    parsers = 1 <= arguments.length ? slice.call(arguments, 0) : [];\n    return function(input) {\n      var i, length, parser, result;\n      i = -1;\n      length = parsers.length;\n      while (++i < length) {\n        parser = parsers[i];\n        result = parser(input);\n        if (result != null) {\n          return result;\n        }\n      }\n    };\n  };\n  newParser = function(options) {\n    var U;\n    U = {};\n    U.wildcard = P.tag('wildcard', P.string(options.wildcardChar));\n    U.optional = P.tag('optional', P.pick(1, P.string(options.optionalSegmentStartChar), P.lazy(function() {\n      return U.pattern;\n    }), P.string(options.optionalSegmentEndChar)));\n    U.name = P.regex(new RegExp(\"^[\" + options.segmentNameCharset + \"]+\"));\n    U.named = P.tag('named', P.pick(1, P.string(options.segmentNameStartChar), P.lazy(function() {\n      return U.name;\n    })));\n    U.escapedChar = P.pick(1, P.string(options.escapeChar), P.regex(/^./));\n    U[\"static\"] = P.tag('static', P.concatMany1Till(P.firstChoice(P.lazy(function() {\n      return U.escapedChar;\n    }), P.regex(/^./)), P.firstChoice(P.string(options.segmentNameStartChar), P.string(options.optionalSegmentStartChar), P.string(options.optionalSegmentEndChar), U.wildcard)));\n    U.token = P.lazy(function() {\n      return P.firstChoice(U.wildcard, U.optional, U.named, U[\"static\"]);\n    });\n    U.pattern = P.many1(P.lazy(function() {\n      return U.token;\n    }));\n    return U;\n  };\n  defaultOptions = {\n    escapeChar: '\\\\',\n    segmentNameStartChar: ':',\n    segmentValueCharset: 'a-zA-Z0-9-_~ %',\n    segmentNameCharset: 'a-zA-Z0-9',\n    optionalSegmentStartChar: '(',\n    optionalSegmentEndChar: ')',\n    wildcardChar: '*'\n  };\n  baseAstNodeToRegexString = function(astNode, segmentValueCharset) {\n    if (Array.isArray(astNode)) {\n      return stringConcatMap(astNode, function(node) {\n        return baseAstNodeToRegexString(node, segmentValueCharset);\n      });\n    }\n    switch (astNode.tag) {\n      case 'wildcard':\n        return '(.*?)';\n      case 'named':\n        return \"([\" + segmentValueCharset + \"]+)\";\n      case 'static':\n        return escapeForRegex(astNode.value);\n      case 'optional':\n        return '(?:' + baseAstNodeToRegexString(astNode.value, segmentValueCharset) + ')?';\n    }\n  };\n  astNodeToRegexString = function(astNode, segmentValueCharset) {\n    if (segmentValueCharset == null) {\n      segmentValueCharset = defaultOptions.segmentValueCharset;\n    }\n    return '^' + baseAstNodeToRegexString(astNode, segmentValueCharset) + '$';\n  };\n  astNodeToNames = function(astNode) {\n    if (Array.isArray(astNode)) {\n      return concatMap(astNode, astNodeToNames);\n    }\n    switch (astNode.tag) {\n      case 'wildcard':\n        return ['_'];\n      case 'named':\n        return [astNode.value];\n      case 'static':\n        return [];\n      case 'optional':\n        return astNodeToNames(astNode.value);\n    }\n  };\n  getParam = function(params, key, nextIndexes, sideEffects) {\n    var index, maxIndex, result, value;\n    if (sideEffects == null) {\n      sideEffects = false;\n    }\n    value = params[key];\n    if (value == null) {\n      if (sideEffects) {\n        throw new Error(\"no values provided for key `\" + key + \"`\");\n      } else {\n        return;\n      }\n    }\n    index = nextIndexes[key] || 0;\n    maxIndex = Array.isArray(value) ? value.length - 1 : 0;\n    if (index > maxIndex) {\n      if (sideEffects) {\n        throw new Error(\"too few values provided for key `\" + key + \"`\");\n      } else {\n        return;\n      }\n    }\n    result = Array.isArray(value) ? value[index] : value;\n    if (sideEffects) {\n      nextIndexes[key] = index + 1;\n    }\n    return result;\n  };\n  astNodeContainsSegmentsForProvidedParams = function(astNode, params, nextIndexes) {\n    var i, length;\n    if (Array.isArray(astNode)) {\n      i = -1;\n      length = astNode.length;\n      while (++i < length) {\n        if (astNodeContainsSegmentsForProvidedParams(astNode[i], params, nextIndexes)) {\n          return true;\n        }\n      }\n      return false;\n    }\n    switch (astNode.tag) {\n      case 'wildcard':\n        return getParam(params, '_', nextIndexes, false) != null;\n      case 'named':\n        return getParam(params, astNode.value, nextIndexes, false) != null;\n      case 'static':\n        return false;\n      case 'optional':\n        return astNodeContainsSegmentsForProvidedParams(astNode.value, params, nextIndexes);\n    }\n  };\n  stringify = function(astNode, params, nextIndexes) {\n    if (Array.isArray(astNode)) {\n      return stringConcatMap(astNode, function(node) {\n        return stringify(node, params, nextIndexes);\n      });\n    }\n    switch (astNode.tag) {\n      case 'wildcard':\n        return getParam(params, '_', nextIndexes, true);\n      case 'named':\n        return getParam(params, astNode.value, nextIndexes, true);\n      case 'static':\n        return astNode.value;\n      case 'optional':\n        if (astNodeContainsSegmentsForProvidedParams(astNode.value, params, nextIndexes)) {\n          return stringify(astNode.value, params, nextIndexes);\n        } else {\n          return '';\n        }\n    }\n  };\n  UrlPattern = function(arg1, arg2) {\n    var groupCount, options, parsed, parser, withoutWhitespace;\n    if (arg1 instanceof UrlPattern) {\n      this.isRegex = arg1.isRegex;\n      this.regex = arg1.regex;\n      this.ast = arg1.ast;\n      this.names = arg1.names;\n      return;\n    }\n    this.isRegex = arg1 instanceof RegExp;\n    if (!(('string' === typeof arg1) || this.isRegex)) {\n      throw new TypeError('argument must be a regex or a string');\n    }\n    if (this.isRegex) {\n      this.regex = arg1;\n      if (arg2 != null) {\n        if (!Array.isArray(arg2)) {\n          throw new Error('if first argument is a regex the second argument may be an array of group names but you provided something else');\n        }\n        groupCount = regexGroupCount(this.regex);\n        if (arg2.length !== groupCount) {\n          throw new Error(\"regex contains \" + groupCount + \" groups but array of group names contains \" + arg2.length);\n        }\n        this.names = arg2;\n      }\n      return;\n    }\n    if (arg1 === '') {\n      throw new Error('argument must not be the empty string');\n    }\n    withoutWhitespace = arg1.replace(/\\s+/g, '');\n    if (withoutWhitespace !== arg1) {\n      throw new Error('argument must not contain whitespace');\n    }\n    options = {\n      escapeChar: (arg2 != null ? arg2.escapeChar : void 0) || defaultOptions.escapeChar,\n      segmentNameStartChar: (arg2 != null ? arg2.segmentNameStartChar : void 0) || defaultOptions.segmentNameStartChar,\n      segmentNameCharset: (arg2 != null ? arg2.segmentNameCharset : void 0) || defaultOptions.segmentNameCharset,\n      segmentValueCharset: (arg2 != null ? arg2.segmentValueCharset : void 0) || defaultOptions.segmentValueCharset,\n      optionalSegmentStartChar: (arg2 != null ? arg2.optionalSegmentStartChar : void 0) || defaultOptions.optionalSegmentStartChar,\n      optionalSegmentEndChar: (arg2 != null ? arg2.optionalSegmentEndChar : void 0) || defaultOptions.optionalSegmentEndChar,\n      wildcardChar: (arg2 != null ? arg2.wildcardChar : void 0) || defaultOptions.wildcardChar\n    };\n    parser = newParser(options);\n    parsed = parser.pattern(arg1);\n    if (parsed == null) {\n      throw new Error(\"couldn't parse pattern\");\n    }\n    if (parsed.rest !== '') {\n      throw new Error(\"could only partially parse pattern\");\n    }\n    this.ast = parsed.value;\n    this.regex = new RegExp(astNodeToRegexString(this.ast, options.segmentValueCharset));\n    this.names = astNodeToNames(this.ast);\n  };\n  UrlPattern.prototype.match = function(url) {\n    var groups, match;\n    match = this.regex.exec(url);\n    if (match == null) {\n      return null;\n    }\n    groups = match.slice(1);\n    if (this.names) {\n      return keysAndValuesToObject(this.names, groups);\n    } else {\n      return groups;\n    }\n  };\n  UrlPattern.prototype.stringify = function(params) {\n    if (params == null) {\n      params = {};\n    }\n    if (this.isRegex) {\n      throw new Error(\"can't stringify patterns generated from a regex\");\n    }\n    if (params !== Object(params)) {\n      throw new Error(\"argument must be an object or undefined\");\n    }\n    return stringify(this.ast, params, {});\n  };\n  UrlPattern.escapeForRegex = escapeForRegex;\n  UrlPattern.concatMap = concatMap;\n  UrlPattern.stringConcatMap = stringConcatMap;\n  UrlPattern.regexGroupCount = regexGroupCount;\n  UrlPattern.keysAndValuesToObject = keysAndValuesToObject;\n  UrlPattern.P = P;\n  UrlPattern.newParser = newParser;\n  UrlPattern.defaultOptions = defaultOptions;\n  UrlPattern.astNodeToRegexString = astNodeToRegexString;\n  UrlPattern.astNodeToNames = astNodeToNames;\n  UrlPattern.getParam = getParam;\n  UrlPattern.astNodeContainsSegmentsForProvidedParams = astNodeContainsSegmentsForProvidedParams;\n  UrlPattern.stringify = stringify;\n  return UrlPattern;\n});\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,QAAQ,CAAC,EAAE;AAEf,KAAC,SAAS,MAAM,SAAS;AACvB,UAAK,eAAe,OAAO,UAAY,OAAO,OAAO,MAAO;AAC1D,eAAO,OAAO,CAAC,GAAG,OAAO;AAAA,MAC3B,WAAW,OAAO,YAAY,eAAe,YAAY,MAAM;AAC7D,eAAO,OAAO,UAAU,QAAQ;AAAA,MAClC,OAAO;AACL,eAAO,KAAK,aAAa,QAAQ;AAAA,MACnC;AAAA,IACF,GAAG,SAAM,WAAW;AAClB,UAAI,GAAG,YAAY,0CAA0C,gBAAgB,sBAAsB,0BAA0B,WAAW,gBAAgB,gBAAgB,UAAU,uBAAuB,WAAW,iBAAiB,iBAAiB;AACtP,uBAAiB,SAAS,QAAQ;AAChC,eAAO,OAAO,QAAQ,0BAA0B,MAAM;AAAA,MACxD;AACA,kBAAY,SAAS,OAAO,GAAG;AAC7B,YAAI,GAAG,QAAQ;AACf,kBAAU,CAAC;AACX,YAAI;AACJ,iBAAS,MAAM;AACf,eAAO,EAAE,IAAI,QAAQ;AACnB,oBAAU,QAAQ,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAAA,QACtC;AACA,eAAO;AAAA,MACT;AACA,wBAAkB,SAAS,OAAO,GAAG;AACnC,YAAI,GAAG,QAAQ;AACf,iBAAS;AACT,YAAI;AACJ,iBAAS,MAAM;AACf,eAAO,EAAE,IAAI,QAAQ;AACnB,oBAAU,EAAE,MAAM,CAAC,CAAC;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AACA,wBAAkB,SAAS,OAAO;AAChC,eAAQ,IAAI,OAAO,MAAM,SAAS,IAAI,GAAG,EAAG,KAAK,EAAE,EAAE,SAAS;AAAA,MAChE;AACA,8BAAwB,SAAS,MAAM,QAAQ;AAC7C,YAAI,GAAG,KAAK,QAAQ,QAAQ;AAC5B,iBAAS,CAAC;AACV,YAAI;AACJ,iBAAS,KAAK;AACd,eAAO,EAAE,IAAI,QAAQ;AACnB,gBAAM,KAAK,CAAC;AACZ,kBAAQ,OAAO,CAAC;AAChB,cAAI,SAAS,MAAM;AACjB;AAAA,UACF;AACA,cAAI,OAAO,GAAG,KAAK,MAAM;AACvB,gBAAI,CAAC,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AAC/B,qBAAO,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC;AAAA,YAC5B;AACA,mBAAO,GAAG,EAAE,KAAK,KAAK;AAAA,UACxB,OAAO;AACL,mBAAO,GAAG,IAAI;AAAA,UAChB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,UAAI,CAAC;AACL,QAAE,SAAS,SAAS,OAAO,MAAM;AAC/B,aAAK,QAAQ;AACb,aAAK,OAAO;AAAA,MACd;AACA,QAAE,SAAS,SAAS,KAAK,OAAO;AAC9B,aAAK,MAAM;AACX,aAAK,QAAQ;AAAA,MACf;AACA,QAAE,MAAM,SAAS,KAAK,QAAQ;AAC5B,eAAO,SAAS,OAAO;AACrB,cAAI,QAAQ;AACZ,mBAAS,OAAO,KAAK;AACrB,cAAI,UAAU,MAAM;AAClB;AAAA,UACF;AACA,mBAAS,IAAI,EAAE,OAAO,KAAK,OAAO,KAAK;AACvC,iBAAO,IAAI,EAAE,OAAO,QAAQ,OAAO,IAAI;AAAA,QACzC;AAAA,MACF;AACA,QAAE,QAAQ,SAAS,OAAO;AACxB,eAAO,SAAS,OAAO;AACrB,cAAI,SAAS;AACb,oBAAU,MAAM,KAAK,KAAK;AAC1B,cAAI,WAAW,MAAM;AACnB;AAAA,UACF;AACA,mBAAS,QAAQ,CAAC;AAClB,iBAAO,IAAI,EAAE,OAAO,QAAQ,MAAM,MAAM,OAAO,MAAM,CAAC;AAAA,QACxD;AAAA,MACF;AACA,QAAE,WAAW,WAAW;AACtB,YAAI;AACJ,kBAAU,KAAK,UAAU,SAAS,MAAM,KAAK,WAAW,CAAC,IAAI,CAAC;AAC9D,eAAO,SAAS,OAAO;AACrB,cAAI,GAAG,QAAQ,QAAQ,MAAM,QAAQ;AACrC,cAAI;AACJ,mBAAS,QAAQ;AACjB,mBAAS,CAAC;AACV,iBAAO;AACP,iBAAO,EAAE,IAAI,QAAQ;AACnB,qBAAS,QAAQ,CAAC;AAClB,qBAAS,OAAO,IAAI;AACpB,gBAAI,UAAU,MAAM;AAClB;AAAA,YACF;AACA,mBAAO,KAAK,OAAO,KAAK;AACxB,mBAAO,OAAO;AAAA,UAChB;AACA,iBAAO,IAAI,EAAE,OAAO,QAAQ,IAAI;AAAA,QAClC;AAAA,MACF;AACA,QAAE,OAAO,WAAW;AAClB,YAAI,SAAS;AACb,kBAAU,UAAU,CAAC,GAAG,UAAU,KAAK,UAAU,SAAS,MAAM,KAAK,WAAW,CAAC,IAAI,CAAC;AACtF,eAAO,SAAS,OAAO;AACrB,cAAI,OAAO;AACX,mBAAS,EAAE,SAAS,MAAM,GAAG,OAAO,EAAE,KAAK;AAC3C,cAAI,UAAU,MAAM;AAClB;AAAA,UACF;AACA,kBAAQ,OAAO;AACf,iBAAO,QAAQ,MAAM,OAAO;AAC5B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,QAAE,SAAS,SAAS,QAAQ;AAC1B,YAAI;AACJ,iBAAS,OAAO;AAChB,eAAO,SAAS,OAAO;AACrB,cAAI,MAAM,MAAM,GAAG,MAAM,MAAM,QAAQ;AACrC,mBAAO,IAAI,EAAE,OAAO,QAAQ,MAAM,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AACA,QAAE,OAAO,SAAS,IAAI;AACpB,YAAI;AACJ,iBAAS;AACT,eAAO,SAAS,OAAO;AACrB,cAAI,UAAU,MAAM;AAClB,qBAAS,GAAG;AAAA,UACd;AACA,iBAAO,OAAO,KAAK;AAAA,QACrB;AAAA,MACF;AACA,QAAE,WAAW,SAAS,QAAQ,KAAK,cAAc,0BAA0B,OAAO;AAChF,YAAI,WAAW,cAAc,MAAM;AACnC,eAAO;AACP,kBAAU,eAAe,KAAK,CAAC;AAC/B,eAAO,MAAM;AACX,cAAI,OAAO,MAAM;AACf,wBAAY,IAAI,IAAI;AACpB,gBAAI,aAAa,MAAM;AACrB;AAAA,YACF;AAAA,UACF;AACA,yBAAe,OAAO,IAAI;AAC1B,cAAI,gBAAgB,MAAM;AACxB;AAAA,UACF;AACA,cAAI,cAAc;AAChB,uBAAW,aAAa;AAAA,UAC1B,OAAO;AACL,oBAAQ,KAAK,aAAa,KAAK;AAAA,UACjC;AACA,iBAAO,aAAa;AAAA,QACtB;AACA,YAAI,4BAA4B,QAAQ,WAAW,GAAG;AACpD;AAAA,QACF;AACA,eAAO,IAAI,EAAE,OAAO,SAAS,IAAI;AAAA,MACnC;AACA,QAAE,QAAQ,SAAS,QAAQ;AACzB,eAAO,SAAS,OAAO;AACrB,iBAAO,EAAE,SAAS,QAAQ,MAAM,OAAO,MAAM,KAAK;AAAA,QACpD;AAAA,MACF;AACA,QAAE,kBAAkB,SAAS,QAAQ,KAAK;AACxC,eAAO,SAAS,OAAO;AACrB,iBAAO,EAAE,SAAS,QAAQ,KAAK,MAAM,MAAM,KAAK;AAAA,QAClD;AAAA,MACF;AACA,QAAE,cAAc,WAAW;AACzB,YAAI;AACJ,kBAAU,KAAK,UAAU,SAAS,MAAM,KAAK,WAAW,CAAC,IAAI,CAAC;AAC9D,eAAO,SAAS,OAAO;AACrB,cAAI,GAAG,QAAQ,QAAQ;AACvB,cAAI;AACJ,mBAAS,QAAQ;AACjB,iBAAO,EAAE,IAAI,QAAQ;AACnB,qBAAS,QAAQ,CAAC;AAClB,qBAAS,OAAO,KAAK;AACrB,gBAAI,UAAU,MAAM;AAClB,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,kBAAY,SAAS,SAAS;AAC5B,YAAI;AACJ,YAAI,CAAC;AACL,UAAE,WAAW,EAAE,IAAI,YAAY,EAAE,OAAO,QAAQ,YAAY,CAAC;AAC7D,UAAE,WAAW,EAAE,IAAI,YAAY,EAAE,KAAK,GAAG,EAAE,OAAO,QAAQ,wBAAwB,GAAG,EAAE,KAAK,WAAW;AACrG,iBAAO,EAAE;AAAA,QACX,CAAC,GAAG,EAAE,OAAO,QAAQ,sBAAsB,CAAC,CAAC;AAC7C,UAAE,OAAO,EAAE,MAAM,IAAI,OAAO,OAAO,QAAQ,qBAAqB,IAAI,CAAC;AACrE,UAAE,QAAQ,EAAE,IAAI,SAAS,EAAE,KAAK,GAAG,EAAE,OAAO,QAAQ,oBAAoB,GAAG,EAAE,KAAK,WAAW;AAC3F,iBAAO,EAAE;AAAA,QACX,CAAC,CAAC,CAAC;AACH,UAAE,cAAc,EAAE,KAAK,GAAG,EAAE,OAAO,QAAQ,UAAU,GAAG,EAAE,MAAM,IAAI,CAAC;AACrE,UAAE,QAAQ,IAAI,EAAE,IAAI,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,KAAK,WAAW;AAC9E,iBAAO,EAAE;AAAA,QACX,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,QAAQ,oBAAoB,GAAG,EAAE,OAAO,QAAQ,wBAAwB,GAAG,EAAE,OAAO,QAAQ,sBAAsB,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC5K,UAAE,QAAQ,EAAE,KAAK,WAAW;AAC1B,iBAAO,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC;AAAA,QACnE,CAAC;AACD,UAAE,UAAU,EAAE,MAAM,EAAE,KAAK,WAAW;AACpC,iBAAO,EAAE;AAAA,QACX,CAAC,CAAC;AACF,eAAO;AAAA,MACT;AACA,uBAAiB;AAAA,QACf,YAAY;AAAA,QACZ,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,cAAc;AAAA,MAChB;AACA,iCAA2B,SAAS,SAAS,qBAAqB;AAChE,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,iBAAO,gBAAgB,SAAS,SAAS,MAAM;AAC7C,mBAAO,yBAAyB,MAAM,mBAAmB;AAAA,UAC3D,CAAC;AAAA,QACH;AACA,gBAAQ,QAAQ,KAAK;AAAA,UACnB,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,OAAO,sBAAsB;AAAA,UACtC,KAAK;AACH,mBAAO,eAAe,QAAQ,KAAK;AAAA,UACrC,KAAK;AACH,mBAAO,QAAQ,yBAAyB,QAAQ,OAAO,mBAAmB,IAAI;AAAA,QAClF;AAAA,MACF;AACA,6BAAuB,SAAS,SAAS,qBAAqB;AAC5D,YAAI,uBAAuB,MAAM;AAC/B,gCAAsB,eAAe;AAAA,QACvC;AACA,eAAO,MAAM,yBAAyB,SAAS,mBAAmB,IAAI;AAAA,MACxE;AACA,uBAAiB,SAAS,SAAS;AACjC,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,iBAAO,UAAU,SAAS,cAAc;AAAA,QAC1C;AACA,gBAAQ,QAAQ,KAAK;AAAA,UACnB,KAAK;AACH,mBAAO,CAAC,GAAG;AAAA,UACb,KAAK;AACH,mBAAO,CAAC,QAAQ,KAAK;AAAA,UACvB,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,mBAAO,eAAe,QAAQ,KAAK;AAAA,QACvC;AAAA,MACF;AACA,iBAAW,SAAS,QAAQ,KAAK,aAAa,aAAa;AACzD,YAAI,OAAO,UAAU,QAAQ;AAC7B,YAAI,eAAe,MAAM;AACvB,wBAAc;AAAA,QAChB;AACA,gBAAQ,OAAO,GAAG;AAClB,YAAI,SAAS,MAAM;AACjB,cAAI,aAAa;AACf,kBAAM,IAAI,MAAM,iCAAiC,MAAM,GAAG;AAAA,UAC5D,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,YAAY,GAAG,KAAK;AAC5B,mBAAW,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,IAAI;AACrD,YAAI,QAAQ,UAAU;AACpB,cAAI,aAAa;AACf,kBAAM,IAAI,MAAM,sCAAsC,MAAM,GAAG;AAAA,UACjE,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,iBAAS,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,IAAI;AAC/C,YAAI,aAAa;AACf,sBAAY,GAAG,IAAI,QAAQ;AAAA,QAC7B;AACA,eAAO;AAAA,MACT;AACA,iDAA2C,SAAS,SAAS,QAAQ,aAAa;AAChF,YAAI,GAAG;AACP,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAI;AACJ,mBAAS,QAAQ;AACjB,iBAAO,EAAE,IAAI,QAAQ;AACnB,gBAAI,yCAAyC,QAAQ,CAAC,GAAG,QAAQ,WAAW,GAAG;AAC7E,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,gBAAQ,QAAQ,KAAK;AAAA,UACnB,KAAK;AACH,mBAAO,SAAS,QAAQ,KAAK,aAAa,KAAK,KAAK;AAAA,UACtD,KAAK;AACH,mBAAO,SAAS,QAAQ,QAAQ,OAAO,aAAa,KAAK,KAAK;AAAA,UAChE,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,yCAAyC,QAAQ,OAAO,QAAQ,WAAW;AAAA,QACtF;AAAA,MACF;AACA,kBAAY,SAAS,SAAS,QAAQ,aAAa;AACjD,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,iBAAO,gBAAgB,SAAS,SAAS,MAAM;AAC7C,mBAAO,UAAU,MAAM,QAAQ,WAAW;AAAA,UAC5C,CAAC;AAAA,QACH;AACA,gBAAQ,QAAQ,KAAK;AAAA,UACnB,KAAK;AACH,mBAAO,SAAS,QAAQ,KAAK,aAAa,IAAI;AAAA,UAChD,KAAK;AACH,mBAAO,SAAS,QAAQ,QAAQ,OAAO,aAAa,IAAI;AAAA,UAC1D,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AACH,gBAAI,yCAAyC,QAAQ,OAAO,QAAQ,WAAW,GAAG;AAChF,qBAAO,UAAU,QAAQ,OAAO,QAAQ,WAAW;AAAA,YACrD,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,QACJ;AAAA,MACF;AACA,mBAAa,SAAS,MAAM,MAAM;AAChC,YAAI,YAAY,SAAS,QAAQ,QAAQ;AACzC,YAAI,gBAAgB,YAAY;AAC9B,eAAK,UAAU,KAAK;AACpB,eAAK,QAAQ,KAAK;AAClB,eAAK,MAAM,KAAK;AAChB,eAAK,QAAQ,KAAK;AAClB;AAAA,QACF;AACA,aAAK,UAAU,gBAAgB;AAC/B,YAAI,EAAG,aAAa,OAAO,QAAS,KAAK,UAAU;AACjD,gBAAM,IAAI,UAAU,sCAAsC;AAAA,QAC5D;AACA,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ;AACb,cAAI,QAAQ,MAAM;AAChB,gBAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,oBAAM,IAAI,MAAM,iHAAiH;AAAA,YACnI;AACA,yBAAa,gBAAgB,KAAK,KAAK;AACvC,gBAAI,KAAK,WAAW,YAAY;AAC9B,oBAAM,IAAI,MAAM,oBAAoB,aAAa,+CAA+C,KAAK,MAAM;AAAA,YAC7G;AACA,iBAAK,QAAQ;AAAA,UACf;AACA;AAAA,QACF;AACA,YAAI,SAAS,IAAI;AACf,gBAAM,IAAI,MAAM,uCAAuC;AAAA,QACzD;AACA,4BAAoB,KAAK,QAAQ,QAAQ,EAAE;AAC3C,YAAI,sBAAsB,MAAM;AAC9B,gBAAM,IAAI,MAAM,sCAAsC;AAAA,QACxD;AACA,kBAAU;AAAA,UACR,aAAa,QAAQ,OAAO,KAAK,aAAa,WAAW,eAAe;AAAA,UACxE,uBAAuB,QAAQ,OAAO,KAAK,uBAAuB,WAAW,eAAe;AAAA,UAC5F,qBAAqB,QAAQ,OAAO,KAAK,qBAAqB,WAAW,eAAe;AAAA,UACxF,sBAAsB,QAAQ,OAAO,KAAK,sBAAsB,WAAW,eAAe;AAAA,UAC1F,2BAA2B,QAAQ,OAAO,KAAK,2BAA2B,WAAW,eAAe;AAAA,UACpG,yBAAyB,QAAQ,OAAO,KAAK,yBAAyB,WAAW,eAAe;AAAA,UAChG,eAAe,QAAQ,OAAO,KAAK,eAAe,WAAW,eAAe;AAAA,QAC9E;AACA,iBAAS,UAAU,OAAO;AAC1B,iBAAS,OAAO,QAAQ,IAAI;AAC5B,YAAI,UAAU,MAAM;AAClB,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC1C;AACA,YAAI,OAAO,SAAS,IAAI;AACtB,gBAAM,IAAI,MAAM,oCAAoC;AAAA,QACtD;AACA,aAAK,MAAM,OAAO;AAClB,aAAK,QAAQ,IAAI,OAAO,qBAAqB,KAAK,KAAK,QAAQ,mBAAmB,CAAC;AACnF,aAAK,QAAQ,eAAe,KAAK,GAAG;AAAA,MACtC;AACA,iBAAW,UAAU,QAAQ,SAAS,KAAK;AACzC,YAAI,QAAQ;AACZ,gBAAQ,KAAK,MAAM,KAAK,GAAG;AAC3B,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,iBAAS,MAAM,MAAM,CAAC;AACtB,YAAI,KAAK,OAAO;AACd,iBAAO,sBAAsB,KAAK,OAAO,MAAM;AAAA,QACjD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,iBAAW,UAAU,YAAY,SAAS,QAAQ;AAChD,YAAI,UAAU,MAAM;AAClB,mBAAS,CAAC;AAAA,QACZ;AACA,YAAI,KAAK,SAAS;AAChB,gBAAM,IAAI,MAAM,iDAAiD;AAAA,QACnE;AACA,YAAI,WAAW,OAAO,MAAM,GAAG;AAC7B,gBAAM,IAAI,MAAM,yCAAyC;AAAA,QAC3D;AACA,eAAO,UAAU,KAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,MACvC;AACA,iBAAW,iBAAiB;AAC5B,iBAAW,YAAY;AACvB,iBAAW,kBAAkB;AAC7B,iBAAW,kBAAkB;AAC7B,iBAAW,wBAAwB;AACnC,iBAAW,IAAI;AACf,iBAAW,YAAY;AACvB,iBAAW,iBAAiB;AAC5B,iBAAW,uBAAuB;AAClC,iBAAW,iBAAiB;AAC5B,iBAAW,WAAW;AACtB,iBAAW,2CAA2C;AACtD,iBAAW,YAAY;AACvB,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;", "names": []}