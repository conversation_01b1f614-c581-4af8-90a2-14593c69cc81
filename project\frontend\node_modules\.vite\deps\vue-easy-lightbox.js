import {
  Vue,
  init_vue_runtime_esm
} from "./chunk-YO42JT3F.js";
import "./chunk-PLDDJCW6.js";

// node_modules/vue-easy-lightbox/dist/vue-easy-lightbox.es5.esm.min.js
init_vue_runtime_esm();
var e = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {};
function n(t, e2) {
  return t(e2 = { exports: {} }, e2.exports), e2.exports;
}
var r = function(t) {
  return t && t.Math == Math && t;
};
var o = r("object" == typeof globalThis && globalThis) || r("object" == typeof window && window) || r("object" == typeof self && self) || r("object" == typeof e && e) || /* @__PURE__ */ function() {
  return this;
}() || Function("return this")();
var i = function(t) {
  try {
    return !!t();
  } catch (t2) {
    return true;
  }
};
var a = !i(function() {
  return 7 != Object.defineProperty({}, 1, { get: function() {
    return 7;
  } })[1];
});
var s = {}.propertyIsEnumerable;
var c = Object.getOwnPropertyDescriptor;
var l = { f: c && !s.call({ 1: 2 }, 1) ? function(t) {
  var e2 = c(this, t);
  return !!e2 && e2.enumerable;
} : s };
var u = function(t, e2) {
  return { enumerable: !(1 & t), configurable: !(2 & t), writable: !(4 & t), value: e2 };
};
var f = {}.toString;
var d = function(t) {
  return f.call(t).slice(8, -1);
};
var p = "".split;
var v = i(function() {
  return !Object("z").propertyIsEnumerable(0);
}) ? function(t) {
  return "String" == d(t) ? p.call(t, "") : Object(t);
} : Object;
var h = function(t) {
  if (null == t) throw TypeError("Can't call method on " + t);
  return t;
};
var g = function(t) {
  return v(h(t));
};
var m = function(t) {
  return "object" == typeof t ? null !== t : "function" == typeof t;
};
var y = function(t, e2) {
  if (!m(t)) return t;
  var n2, r2;
  if (e2 && "function" == typeof (n2 = t.toString) && !m(r2 = n2.call(t))) return r2;
  if ("function" == typeof (n2 = t.valueOf) && !m(r2 = n2.call(t))) return r2;
  if (!e2 && "function" == typeof (n2 = t.toString) && !m(r2 = n2.call(t))) return r2;
  throw TypeError("Can't convert object to primitive value");
};
var b = {}.hasOwnProperty;
var w = function(t, e2) {
  return b.call(t, e2);
};
var _ = o.document;
var x = m(_) && m(_.createElement);
var S = function(t) {
  return x ? _.createElement(t) : {};
};
var O = !a && !i(function() {
  return 7 != Object.defineProperty(S("div"), "a", { get: function() {
    return 7;
  } }).a;
});
var k = Object.getOwnPropertyDescriptor;
var I = { f: a ? k : function(t, e2) {
  if (t = g(t), e2 = y(e2, true), O) try {
    return k(t, e2);
  } catch (t2) {
  }
  if (w(t, e2)) return u(!l.f.call(t, e2), t[e2]);
} };
var E = function(t) {
  if (!m(t)) throw TypeError(String(t) + " is not an object");
  return t;
};
var C = Object.defineProperty;
var z = { f: a ? C : function(t, e2, n2) {
  if (E(t), e2 = y(e2, true), E(n2), O) try {
    return C(t, e2, n2);
  } catch (t2) {
  }
  if ("get" in n2 || "set" in n2) throw TypeError("Accessors not supported");
  return "value" in n2 && (t[e2] = n2.value), t;
} };
var T = a ? function(t, e2, n2) {
  return z.f(t, e2, u(1, n2));
} : function(t, e2, n2) {
  return t[e2] = n2, t;
};
var j = function(t, e2) {
  try {
    T(o, t, e2);
  } catch (n2) {
    o[t] = e2;
  }
  return e2;
};
var L = o["__core-js_shared__"] || j("__core-js_shared__", {});
var A = Function.toString;
"function" != typeof L.inspectSource && (L.inspectSource = function(t) {
  return A.call(t);
});
var M;
var R;
var D;
var P = L.inspectSource;
var N = o.WeakMap;
var F = "function" == typeof N && /native code/.test(P(N));
var B = n(function(t) {
  (t.exports = function(t2, e2) {
    return L[t2] || (L[t2] = void 0 !== e2 ? e2 : {});
  })("versions", []).push({ version: "3.10.1", mode: "global", copyright: "© 2021 Denis Pushkarev (zloirock.ru)" });
});
var Y = 0;
var $ = Math.random();
var G = function(t) {
  return "Symbol(" + String(void 0 === t ? "" : t) + ")_" + (++Y + $).toString(36);
};
var V = B("keys");
var X = function(t) {
  return V[t] || (V[t] = G(t));
};
var U = {};
var W = o.WeakMap;
if (F) {
  H = L.state || (L.state = new W()), K = H.get, q = H.has, Q = H.set;
  M = function(t, e2) {
    return e2.facade = t, Q.call(H, t, e2), e2;
  }, R = function(t) {
    return K.call(H, t) || {};
  }, D = function(t) {
    return q.call(H, t);
  };
} else {
  J = X("state");
  U[J] = true, M = function(t, e2) {
    return e2.facade = t, T(t, J, e2), e2;
  }, R = function(t) {
    return w(t, J) ? t[J] : {};
  }, D = function(t) {
    return w(t, J);
  };
}
var H;
var K;
var q;
var Q;
var J;
var Z;
var tt;
var et = { set: M, get: R, has: D, enforce: function(t) {
  return D(t) ? R(t) : M(t, {});
}, getterFor: function(t) {
  return function(e2) {
    var n2;
    if (!m(e2) || (n2 = R(e2)).type !== t) throw TypeError("Incompatible receiver, " + t + " required");
    return n2;
  };
} };
var nt = n(function(t) {
  var e2 = et.get, n2 = et.enforce, r2 = String(String).split("String");
  (t.exports = function(t2, e3, i2, a2) {
    var s2, c2 = !!a2 && !!a2.unsafe, l2 = !!a2 && !!a2.enumerable, u2 = !!a2 && !!a2.noTargetGet;
    "function" == typeof i2 && ("string" != typeof e3 || w(i2, "name") || T(i2, "name", e3), (s2 = n2(i2)).source || (s2.source = r2.join("string" == typeof e3 ? e3 : ""))), t2 !== o ? (c2 ? !u2 && t2[e3] && (l2 = true) : delete t2[e3], l2 ? t2[e3] = i2 : T(t2, e3, i2)) : l2 ? t2[e3] = i2 : j(e3, i2);
  })(Function.prototype, "toString", function() {
    return "function" == typeof this && e2(this).source || P(this);
  });
});
var rt = o;
var ot = function(t) {
  return "function" == typeof t ? t : void 0;
};
var it = function(t, e2) {
  return arguments.length < 2 ? ot(rt[t]) || ot(o[t]) : rt[t] && rt[t][e2] || o[t] && o[t][e2];
};
var at = Math.ceil;
var st = Math.floor;
var ct = function(t) {
  return isNaN(t = +t) ? 0 : (t > 0 ? st : at)(t);
};
var lt = Math.min;
var ut = function(t) {
  return t > 0 ? lt(ct(t), 9007199254740991) : 0;
};
var ft = Math.max;
var dt = Math.min;
var pt = function(t, e2) {
  var n2 = ct(t);
  return n2 < 0 ? ft(n2 + e2, 0) : dt(n2, e2);
};
var vt = function(t) {
  return function(e2, n2, r2) {
    var o2, i2 = g(e2), a2 = ut(i2.length), s2 = pt(r2, a2);
    if (t && n2 != n2) {
      for (; a2 > s2; ) if ((o2 = i2[s2++]) != o2) return true;
    } else for (; a2 > s2; s2++) if ((t || s2 in i2) && i2[s2] === n2) return t || s2 || 0;
    return !t && -1;
  };
};
var ht = { includes: vt(true), indexOf: vt(false) }.indexOf;
var gt = function(t, e2) {
  var n2, r2 = g(t), o2 = 0, i2 = [];
  for (n2 in r2) !w(U, n2) && w(r2, n2) && i2.push(n2);
  for (; e2.length > o2; ) w(r2, n2 = e2[o2++]) && (~ht(i2, n2) || i2.push(n2));
  return i2;
};
var mt = ["constructor", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "toLocaleString", "toString", "valueOf"];
var yt = mt.concat("length", "prototype");
var bt = { f: Object.getOwnPropertyNames || function(t) {
  return gt(t, yt);
} };
var wt = { f: Object.getOwnPropertySymbols };
var _t = it("Reflect", "ownKeys") || function(t) {
  var e2 = bt.f(E(t)), n2 = wt.f;
  return n2 ? e2.concat(n2(t)) : e2;
};
var xt = function(t, e2) {
  for (var n2 = _t(e2), r2 = z.f, o2 = I.f, i2 = 0; i2 < n2.length; i2++) {
    var a2 = n2[i2];
    w(t, a2) || r2(t, a2, o2(e2, a2));
  }
};
var St = /#|\.prototype\./;
var Ot = function(t, e2) {
  var n2 = It[kt(t)];
  return n2 == Ct || n2 != Et && ("function" == typeof e2 ? i(e2) : !!e2);
};
var kt = Ot.normalize = function(t) {
  return String(t).replace(St, ".").toLowerCase();
};
var It = Ot.data = {};
var Et = Ot.NATIVE = "N";
var Ct = Ot.POLYFILL = "P";
var zt = Ot;
var Tt = I.f;
var jt = function(t, e2) {
  var n2, r2, i2, a2, s2, c2 = t.target, l2 = t.global, u2 = t.stat;
  if (n2 = l2 ? o : u2 ? o[c2] || j(c2, {}) : (o[c2] || {}).prototype) for (r2 in e2) {
    if (a2 = e2[r2], i2 = t.noTargetGet ? (s2 = Tt(n2, r2)) && s2.value : n2[r2], !zt(l2 ? r2 : c2 + (u2 ? "." : "#") + r2, t.forced) && void 0 !== i2) {
      if (typeof a2 == typeof i2) continue;
      xt(a2, i2);
    }
    (t.sham || i2 && i2.sham) && T(a2, "sham", true), nt(n2, r2, a2, t);
  }
};
var Lt = function(t, e2, n2) {
  if (function(t2) {
    if ("function" != typeof t2) throw TypeError(String(t2) + " is not a function");
  }(t), void 0 === e2) return t;
  switch (n2) {
    case 0:
      return function() {
        return t.call(e2);
      };
    case 1:
      return function(n3) {
        return t.call(e2, n3);
      };
    case 2:
      return function(n3, r2) {
        return t.call(e2, n3, r2);
      };
    case 3:
      return function(n3, r2, o2) {
        return t.call(e2, n3, r2, o2);
      };
  }
  return function() {
    return t.apply(e2, arguments);
  };
};
var At = function(t) {
  return Object(h(t));
};
var Mt = Array.isArray || function(t) {
  return "Array" == d(t);
};
var Rt = "process" == d(o.process);
var Dt = it("navigator", "userAgent") || "";
var Pt = o.process;
var Nt = Pt && Pt.versions;
var Ft = Nt && Nt.v8;
Ft ? tt = (Z = Ft.split("."))[0] + Z[1] : Dt && (!(Z = Dt.match(/Edge\/(\d+)/)) || Z[1] >= 74) && (Z = Dt.match(/Chrome\/(\d+)/)) && (tt = Z[1]);
var Bt = tt && +tt;
var Yt = !!Object.getOwnPropertySymbols && !i(function() {
  return !Symbol.sham && (Rt ? 38 === Bt : Bt > 37 && Bt < 41);
});
var $t = Yt && !Symbol.sham && "symbol" == typeof Symbol.iterator;
var Gt = B("wks");
var Vt = o.Symbol;
var Xt = $t ? Vt : Vt && Vt.withoutSetter || G;
var Ut = function(t) {
  return w(Gt, t) && (Yt || "string" == typeof Gt[t]) || (Yt && w(Vt, t) ? Gt[t] = Vt[t] : Gt[t] = Xt("Symbol." + t)), Gt[t];
};
var Wt = Ut("species");
var Ht = function(t, e2) {
  var n2;
  return Mt(t) && ("function" != typeof (n2 = t.constructor) || n2 !== Array && !Mt(n2.prototype) ? m(n2) && null === (n2 = n2[Wt]) && (n2 = void 0) : n2 = void 0), new (void 0 === n2 ? Array : n2)(0 === e2 ? 0 : e2);
};
var Kt = [].push;
var qt = function(t) {
  var e2 = 1 == t, n2 = 2 == t, r2 = 3 == t, o2 = 4 == t, i2 = 6 == t, a2 = 7 == t, s2 = 5 == t || i2;
  return function(c2, l2, u2, f2) {
    for (var d2, p2, h2 = At(c2), g2 = v(h2), m2 = Lt(l2, u2, 3), y2 = ut(g2.length), b2 = 0, w2 = f2 || Ht, _2 = e2 ? w2(c2, y2) : n2 || a2 ? w2(c2, 0) : void 0; y2 > b2; b2++) if ((s2 || b2 in g2) && (p2 = m2(d2 = g2[b2], b2, h2), t)) if (e2) _2[b2] = p2;
    else if (p2) switch (t) {
      case 3:
        return true;
      case 5:
        return d2;
      case 6:
        return b2;
      case 2:
        Kt.call(_2, d2);
    }
    else switch (t) {
      case 4:
        return false;
      case 7:
        Kt.call(_2, d2);
    }
    return i2 ? -1 : r2 || o2 ? o2 : _2;
  };
};
var Qt = { forEach: qt(0), map: qt(1), filter: qt(2), some: qt(3), every: qt(4), find: qt(5), findIndex: qt(6), filterOut: qt(7) };
var Jt = Ut("species");
var Zt = function(t) {
  return Bt >= 51 || !i(function() {
    var e2 = [];
    return (e2.constructor = {})[Jt] = function() {
      return { foo: 1 };
    }, 1 !== e2[t](Boolean).foo;
  });
};
var te = Qt.filter;
var ee = Zt("filter");
jt({ target: "Array", proto: true, forced: !ee }, { filter: function(t) {
  return te(this, t, arguments.length > 1 ? arguments[1] : void 0);
} });
var ne = Qt.map;
var re = Zt("map");
jt({ target: "Array", proto: true, forced: !re }, { map: function(t) {
  return ne(this, t, arguments.length > 1 ? arguments[1] : void 0);
} });
var oe = { CSSRuleList: 0, CSSStyleDeclaration: 0, CSSValueList: 0, ClientRectList: 0, DOMRectList: 0, DOMStringList: 0, DOMTokenList: 1, DataTransferItemList: 0, FileList: 0, HTMLAllCollection: 0, HTMLCollection: 0, HTMLFormElement: 0, HTMLSelectElement: 0, MediaList: 0, MimeTypeArray: 0, NamedNodeMap: 0, NodeList: 1, PaintRequestList: 0, Plugin: 0, PluginArray: 0, SVGLengthList: 0, SVGNumberList: 0, SVGPathSegList: 0, SVGPointList: 0, SVGStringList: 0, SVGTransformList: 0, SourceBufferList: 0, StyleSheetList: 0, TextTrackCueList: 0, TextTrackList: 0, TouchList: 0 };
var ie = function(t, e2) {
  var n2 = [][t];
  return !!n2 && i(function() {
    n2.call(null, e2 || function() {
      throw 1;
    }, 1);
  });
};
var ae = Qt.forEach;
var se = ie("forEach") ? [].forEach : function(t) {
  return ae(this, t, arguments.length > 1 ? arguments[1] : void 0);
};
for (ce in oe) {
  le = o[ce], ue = le && le.prototype;
  if (ue && ue.forEach !== se) try {
    T(ue, "forEach", se);
  } catch (t) {
    ue.forEach = se;
  }
}
var le;
var ue;
var ce;
var fe;
var de = Object.setPrototypeOf || ("__proto__" in {} ? function() {
  var t, e2 = false, n2 = {};
  try {
    (t = Object.getOwnPropertyDescriptor(Object.prototype, "__proto__").set).call(n2, []), e2 = n2 instanceof Array;
  } catch (t2) {
  }
  return function(n3, r2) {
    return E(n3), function(t2) {
      if (!m(t2) && null !== t2) throw TypeError("Can't set " + String(t2) + " as a prototype");
    }(r2), e2 ? t.call(n3, r2) : n3.__proto__ = r2, n3;
  };
}() : void 0);
var pe = function(t, e2, n2) {
  var r2, o2;
  return de && "function" == typeof (r2 = e2.constructor) && r2 !== n2 && m(o2 = r2.prototype) && o2 !== n2.prototype && de(t, o2), t;
};
var ve = Object.keys || function(t) {
  return gt(t, mt);
};
var he = a ? Object.defineProperties : function(t, e2) {
  E(t);
  for (var n2, r2 = ve(e2), o2 = r2.length, i2 = 0; o2 > i2; ) z.f(t, n2 = r2[i2++], e2[n2]);
  return t;
};
var ge = it("document", "documentElement");
var me = X("IE_PROTO");
var ye = function() {
};
var be = function(t) {
  return "<script>" + t + "<\/script>";
};
var we = function() {
  try {
    fe = document.domain && new ActiveXObject("htmlfile");
  } catch (t2) {
  }
  var t, e2;
  we = fe ? function(t2) {
    t2.write(be("")), t2.close();
    var e3 = t2.parentWindow.Object;
    return t2 = null, e3;
  }(fe) : ((e2 = S("iframe")).style.display = "none", ge.appendChild(e2), e2.src = String("javascript:"), (t = e2.contentWindow.document).open(), t.write(be("document.F=Object")), t.close(), t.F);
  for (var n2 = mt.length; n2--; ) delete we.prototype[mt[n2]];
  return we();
};
U[me] = true;
var _e = Object.create || function(t, e2) {
  var n2;
  return null !== t ? (ye.prototype = E(t), n2 = new ye(), ye.prototype = null, n2[me] = t) : n2 = we(), void 0 === e2 ? n2 : he(n2, e2);
};
var xe = "[	\n\v\f\r                　\u2028\u2029\uFEFF]";
var Se = RegExp("^" + xe + xe + "*");
var Oe = RegExp(xe + xe + "*$");
var ke = function(t) {
  return function(e2) {
    var n2 = String(h(e2));
    return 1 & t && (n2 = n2.replace(Se, "")), 2 & t && (n2 = n2.replace(Oe, "")), n2;
  };
};
var Ie = { start: ke(1), end: ke(2), trim: ke(3) };
var Ee = bt.f;
var Ce = I.f;
var ze = z.f;
var Te = Ie.trim;
var je = o.Number;
var Le = je.prototype;
var Ae = "Number" == d(_e(Le));
var Me = function(t) {
  var e2, n2, r2, o2, i2, a2, s2, c2, l2 = y(t, false);
  if ("string" == typeof l2 && l2.length > 2) {
    if (43 === (e2 = (l2 = Te(l2)).charCodeAt(0)) || 45 === e2) {
      if (88 === (n2 = l2.charCodeAt(2)) || 120 === n2) return NaN;
    } else if (48 === e2) {
      switch (l2.charCodeAt(1)) {
        case 66:
        case 98:
          r2 = 2, o2 = 49;
          break;
        case 79:
        case 111:
          r2 = 8, o2 = 55;
          break;
        default:
          return +l2;
      }
      for (a2 = (i2 = l2.slice(2)).length, s2 = 0; s2 < a2; s2++) if ((c2 = i2.charCodeAt(s2)) < 48 || c2 > o2) return NaN;
      return parseInt(i2, r2);
    }
  }
  return +l2;
};
if (zt("Number", !je(" 0o1") || !je("0b1") || je("+0x1"))) {
  for (De = function(t) {
    var e2 = arguments.length < 1 ? 0 : t, n2 = this;
    return n2 instanceof De && (Ae ? i(function() {
      Le.valueOf.call(n2);
    }) : "Number" != d(n2)) ? pe(new je(Me(e2)), n2, De) : Me(e2);
  }, Pe = a ? Ee(je) : "MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","), Ne = 0; Pe.length > Ne; Ne++) w(je, Re = Pe[Ne]) && !w(De, Re) && ze(De, Re, Ce(je, Re));
  De.prototype = Le, Le.constructor = De, nt(o, "Number", De);
}
var Re;
var De;
var Pe;
var Ne;
var Fe = function(t, e2) {
  return (Fe = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(t2, e3) {
    t2.__proto__ = e3;
  } || function(t2, e3) {
    for (var n2 in e3) e3.hasOwnProperty(n2) && (t2[n2] = e3[n2]);
  })(t, e2);
};
function Be(t, e2, n2, r2) {
  var o2, i2 = arguments.length, a2 = i2 < 3 ? e2 : null === r2 ? r2 = Object.getOwnPropertyDescriptor(e2, n2) : r2;
  if ("object" == typeof Reflect && "function" == typeof Reflect.decorate) a2 = Reflect.decorate(t, e2, n2, r2);
  else for (var s2 = t.length - 1; s2 >= 0; s2--) (o2 = t[s2]) && (a2 = (i2 < 3 ? o2(a2) : i2 > 3 ? o2(e2, n2, a2) : o2(e2, n2)) || a2);
  return i2 > 3 && a2 && Object.defineProperty(e2, n2, a2), a2;
}
function Ye(t) {
  return (Ye = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t2) {
    return typeof t2;
  } : function(t2) {
    return t2 && "function" == typeof Symbol && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  })(t);
}
function $e(t, e2, n2) {
  return e2 in t ? Object.defineProperty(t, e2, { value: n2, enumerable: true, configurable: true, writable: true }) : t[e2] = n2, t;
}
function Ge(t) {
  return function(t2) {
    if (Array.isArray(t2)) {
      for (var e2 = 0, n2 = new Array(t2.length); e2 < t2.length; e2++) n2[e2] = t2[e2];
      return n2;
    }
  }(t) || function(t2) {
    if (Symbol.iterator in Object(t2) || "[object Arguments]" === Object.prototype.toString.call(t2)) return Array.from(t2);
  }(t) || function() {
    throw new TypeError("Invalid attempt to spread non-iterable instance");
  }();
}
function Ve() {
  return "undefined" != typeof Reflect && Reflect.defineMetadata && Reflect.getOwnMetadataKeys;
}
function Xe(t, e2) {
  Ue(t, e2), Object.getOwnPropertyNames(e2.prototype).forEach(function(n2) {
    Ue(t.prototype, e2.prototype, n2);
  }), Object.getOwnPropertyNames(e2).forEach(function(n2) {
    Ue(t, e2, n2);
  });
}
function Ue(t, e2, n2) {
  (n2 ? Reflect.getOwnMetadataKeys(e2, n2) : Reflect.getOwnMetadataKeys(e2)).forEach(function(r2) {
    var o2 = n2 ? Reflect.getOwnMetadata(r2, e2, n2) : Reflect.getOwnMetadata(r2, e2);
    n2 ? Reflect.defineMetadata(r2, o2, t, n2) : Reflect.defineMetadata(r2, o2, t);
  });
}
var We = { __proto__: [] } instanceof Array;
function He(t) {
  return function(e2, n2, r2) {
    var o2 = "function" == typeof e2 ? e2 : e2.constructor;
    o2.__decorators__ || (o2.__decorators__ = []), "number" != typeof r2 && (r2 = void 0), o2.__decorators__.push(function(e3) {
      return t(e3, n2, r2);
    });
  };
}
function Ke(t, e2) {
  var n2 = e2.prototype._init;
  e2.prototype._init = function() {
    var e3 = this, n3 = Object.getOwnPropertyNames(t);
    if (t.$options.props) for (var r3 in t.$options.props) t.hasOwnProperty(r3) || n3.push(r3);
    n3.forEach(function(n4) {
      Object.defineProperty(e3, n4, { get: function() {
        return t[n4];
      }, set: function(e4) {
        t[n4] = e4;
      }, configurable: true });
    });
  };
  var r2 = new e2();
  e2.prototype._init = n2;
  var o2 = {};
  return Object.keys(r2).forEach(function(t2) {
    void 0 !== r2[t2] && (o2[t2] = r2[t2]);
  }), o2;
}
var qe = ["data", "beforeCreate", "created", "beforeMount", "mounted", "beforeDestroy", "destroyed", "beforeUpdate", "updated", "activated", "deactivated", "render", "errorCaptured", "serverPrefetch"];
function Qe(e2) {
  var n2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  n2.name = n2.name || e2._componentTag || e2.name;
  var r2 = e2.prototype;
  Object.getOwnPropertyNames(r2).forEach(function(t) {
    if ("constructor" !== t) if (qe.indexOf(t) > -1) n2[t] = r2[t];
    else {
      var e3 = Object.getOwnPropertyDescriptor(r2, t);
      void 0 !== e3.value ? "function" == typeof e3.value ? (n2.methods || (n2.methods = {}))[t] = e3.value : (n2.mixins || (n2.mixins = [])).push({ data: function() {
        return $e({}, t, e3.value);
      } }) : (e3.get || e3.set) && ((n2.computed || (n2.computed = {}))[t] = { get: e3.get, set: e3.set });
    }
  }), (n2.mixins || (n2.mixins = [])).push({ data: function() {
    return Ke(this, e2);
  } });
  var o2 = e2.__decorators__;
  o2 && (o2.forEach(function(t) {
    return t(n2);
  }), delete e2.__decorators__);
  var i2 = Object.getPrototypeOf(e2.prototype), a2 = i2 instanceof Vue ? i2.constructor : Vue, s2 = a2.extend(n2);
  return Ze(s2, e2, a2), Ve() && Xe(s2, e2), s2;
}
var Je = { prototype: true, arguments: true, callee: true, caller: true };
function Ze(t, e2, n2) {
  Object.getOwnPropertyNames(e2).forEach(function(r2) {
    if (!Je[r2]) {
      var o2 = Object.getOwnPropertyDescriptor(t, r2);
      if (!o2 || o2.configurable) {
        var i2, a2, s2 = Object.getOwnPropertyDescriptor(e2, r2);
        if (!We) {
          if ("cid" === r2) return;
          var c2 = Object.getOwnPropertyDescriptor(n2, r2);
          if (i2 = s2.value, a2 = Ye(i2), null != i2 && ("object" === a2 || "function" === a2) && c2 && c2.value === s2.value) return;
        }
        Object.defineProperty(t, r2, s2);
      }
    }
  });
}
function tn(t) {
  return "function" == typeof t ? Qe(t) : function(e2) {
    return Qe(e2, t);
  };
}
tn.registerHooks = function(t) {
  qe.push.apply(qe, Ge(t));
};
var en = "undefined" != typeof Reflect && void 0 !== Reflect.getMetadata;
function nn(t) {
  return void 0 === t && (t = {}), function(e2, n2) {
    !function(t2, e3, n3) {
      if (en && !Array.isArray(t2) && "function" != typeof t2 && void 0 === t2.type) {
        var r2 = Reflect.getMetadata("design:type", e3, n3);
        r2 !== Object && (t2.type = r2);
      }
    }(t, e2, n2), He(function(e3, n3) {
      (e3.props || (e3.props = {}))[n3] = t;
    })(e2, n2);
  };
}
function rn(t, e2) {
  void 0 === e2 && (e2 = {});
  var n2 = e2.deep, r2 = void 0 !== n2 && n2, o2 = e2.immediate, i2 = void 0 !== o2 && o2;
  return He(function(e3, n3) {
    "object" != typeof e3.watch && (e3.watch = /* @__PURE__ */ Object.create(null));
    var o3 = e3.watch;
    "object" != typeof o3[t] || Array.isArray(o3[t]) ? void 0 === o3[t] && (o3[t] = []) : o3[t] = [o3[t]], o3[t].push({ handler: n3, deep: r2, immediate: i2 });
  });
}
!function() {
  if ("undefined" != typeof window) {
    var t, e2 = window, n2 = '<svg><symbol id="icon-rotate-right" viewBox="0 0 1024 1024"><path d="M275.199914 450.496179v20.031994c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399a120.255962 120.255962 0 0 1-72.991978-24.895992c-21.503993-15.839995-35.359989-38.751988-41.567987-68.735979h60.831981c9.247997 23.007993 27.167992 34.495989 53.759983 34.49599 37.535988-0.384 56.863982-21.407993 57.983982-63.071981v-38.751988c-28.095991 8.863997-54.303983 13.119996-78.623975 12.735996a91.263971 91.263971 0 0 1-68.447979-27.711991c-18.847994-18.303994-28.095991-47.231985-27.711991-86.847973z m62.55998 24.863992c7.103998 24.799992 25.215992 37.343988 54.271983 37.663989 27.103992-0.288 44.703986-11.327996 52.831984-33.11999 3.135999-8.383997 2.655999-29.599991-1.28-38.559988-8.607997-19.615994-25.791992-29.695991-51.551984-30.20799-28.383991 0.576-46.303986 12.639996-53.759983 36.159988a58.719982 58.719982 0 0 0-0.512 28.063991z m390.335878 115.711964v-116.895963c-1.12-41.311987-20.447994-62.335981-57.983981-63.07198-37.727988 0.768-56.959982 21.791993-57.695982 63.07198v116.895963c0.768 41.663987 19.999994 62.68798 57.695982 63.071981 37.535988-0.384 56.863982-21.407993 57.983981-63.071981z m-174.815945 3.391999v-123.935961c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399-31.10399-0.384-57.887982-10.751997-80.319975-31.10399-23.935993-20.543994-36.127989-49.791984-36.479989-87.679973z m282.559912-479.07185A509.887841 509.887841 0 0 0 511.99984 0.00032C229.215928 0.00032 0 229.216248 0 512.00016s229.215928 511.99984 511.99984 511.99984 511.99984-229.215928 511.99984-511.99984c0-3.743999-0.032-7.455998-0.128-11.167997-1.631999-11.295996-8.159997-27.103992-31.87199-27.103991-27.487991 0-31.67999 21.247993-32.03199 32.06399l0.032 4.127999a30.62399 30.62399 0 0 0 0.16 2.079999H959.9997c0 247.423923-200.575937 447.99986-447.99986 447.99986S63.99998 759.424083 63.99998 512.00016 264.575917 64.0003 511.99984 64.0003a446.079861 446.079861 0 0 1 277.439913 96.22397l-94.91197 91.679971c-25.439992 24.607992-17.439995 44.991986 17.887994 45.599986l188.031942 3.295999a64.31998 64.31998 0 0 0 65.055979-62.84798l3.295999-188.127942C969.407697 15.040315 949.311703 5.792318 923.871711 30.368311l-87.999972 85.023973z" fill="" ></path></symbol><symbol id="icon-rotate-left" viewBox="0 0 1024 1024"><path d="M275.199914 450.496179v20.031994c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399a120.255962 120.255962 0 0 1-72.991978-24.895992c-21.503993-15.839995-35.359989-38.751988-41.567987-68.735979h60.831981c9.247997 23.007993 27.167992 34.495989 53.759983 34.49599 37.535988-0.384 56.863982-21.407993 57.983982-63.071981v-38.751988c-28.095991 8.863997-54.303983 13.119996-78.623975 12.735996a91.263971 91.263971 0 0 1-68.447979-27.711991c-18.847994-18.303994-28.095991-47.231985-27.711991-86.847973z m62.55998 24.863992c7.103998 24.799992 25.215992 37.343988 54.271983 37.663989 27.103992-0.288 44.703986-11.327996 52.831984-33.11999 3.135999-8.383997 2.655999-29.599991-1.28-38.559988-8.607997-19.615994-25.791992-29.695991-51.551984-30.20799-28.383991 0.576-46.303986 12.639996-53.759983 36.159988a58.719982 58.719982 0 0 0-0.512 28.063991z m390.335878 115.711964v-116.895963c-1.12-41.311987-20.447994-62.335981-57.983981-63.07198-37.727988 0.768-56.959982 21.791993-57.695982 63.07198v116.895963c0.768 41.663987 19.999994 62.68798 57.695982 63.071981 37.535988-0.384 56.863982-21.407993 57.983981-63.071981z m-174.815945 3.391999v-123.935961c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399-31.10399-0.384-57.887982-10.751997-80.319975-31.10399-23.935993-20.543994-36.127989-49.791984-36.479989-87.679973zM188.159941 115.392284A509.887841 509.887841 0 0 1 511.99984 0.00032c282.783912 0 511.99984 229.215928 511.99984 511.99984s-229.215928 511.99984-511.99984 511.99984S0 794.784072 0 512.00016c0-3.743999 0.032-7.455998 0.128-11.167997 1.631999-11.295996 8.159997-27.103992 31.87199-27.103991 27.487991 0 31.67999 21.247993 32.03199 32.06399L63.99998 509.920161a30.62399 30.62399 0 0 1-0.16 2.079999H63.99998c0 247.423923 200.575937 447.99986 447.99986 447.99986s447.99986-200.575937 447.99986-447.99986S759.423763 64.0003 511.99984 64.0003a446.079861 446.079861 0 0 0-277.439913 96.22397l94.91197 91.679971c25.439992 24.607992 17.439995 44.991986-17.887994 45.599986L123.551961 300.800226a64.31998 64.31998 0 0 1-65.055979-62.84798l-3.295999-188.127942C54.591983 15.040315 74.687977 5.792318 100.127969 30.368311l87.999972 85.023973z" fill="" ></path></symbol><symbol id="icon-resize" viewBox="0 0 1024 1024"><path d="M456.036919 791.8108 270.553461 791.8108 460.818829 601.572038l-39.593763-39.567157L231.314785 751.915162l0.873903-183.953615c0-15.465227-12.515035-27.981285-27.981285-27.981285s-27.981285 12.515035-27.981285 27.981285l0 251.829516c0 8.3072 3.415796 14.975063 8.826016 19.564591 5.082762 5.192256 12.132318 8.416693 19.947308 8.416693l251.036453 0c15.46625 0 27.981285-12.514012 27.981285-27.981285C484.018204 804.325835 471.504192 791.8108 456.036919 791.8108zM838.945819 184.644347c-5.082762-5.191232-12.132318-8.416693-19.947308-8.416693L567.961034 176.227654c-15.46625 0-27.981285 12.515035-27.981285 27.981285 0 15.46625 12.514012 27.981285 27.981285 27.981285l185.483458 0L563.206754 422.427962l39.567157 39.567157 189.910281-189.910281-0.873903 183.953615c0 15.46625 12.514012 27.981285 27.981285 27.981285s27.981285-12.514012 27.981285-27.981285L847.772858 204.208938C847.771835 195.902762 844.356039 189.234899 838.945819 184.644347zM847.771835 64.303538 176.227142 64.303538c-61.809741 0-111.924115 50.115398-111.924115 111.924115l0 671.544693c0 61.809741 50.114374 111.924115 111.924115 111.924115l671.544693 0c61.809741 0 111.924115-50.114374 111.924115-111.924115l0-671.544693C959.69595 114.418936 909.581576 64.303538 847.771835 64.303538zM903.733381 847.772346c0 30.878265-25.056676 55.962569-55.962569 55.962569L176.227142 903.734916c-30.90487 0-55.962569-25.084305-55.962569-55.962569l0-671.544693c0-30.9325 25.056676-55.962569 55.962569-55.962569l671.544693 0c30.90487 0 55.962569 25.03007 55.962569 55.962569L903.734404 847.772346z"  ></path></symbol><symbol id="icon-img-broken" viewBox="0 0 1024 1024"><path d="M810.666667 128H213.333333c-46.933333 0-85.333333 38.4-85.333333 85.333333v597.333334c0 46.933333 38.4 85.333333 85.333333 85.333333h597.333334c46.933333 0 85.333333-38.4 85.333333-85.333333V213.333333c0-46.933333-38.4-85.333333-85.333333-85.333333z m0 682.666667H213.333333v-195.413334l42.24 42.24 170.666667-170.666666 170.666667 170.666666 170.666666-170.24L810.666667 530.346667V810.666667z m0-401.493334l-43.093334-43.093333-170.666666 171.093333-170.666667-170.666666-170.666667 170.666666-42.24-42.666666V213.333333h597.333334v195.84z"  ></path></symbol><symbol id="icon-prev" viewBox="0 0 1024 1024"><path d="M784.652701 955.6957 346.601985 517.644983c-2.822492-2.822492-2.822492-7.902977 0-11.289967l439.179713-439.179713c6.77398-6.77398 10.725469-16.370452 10.725469-25.966924L796.507166 36.692393c0-20.32194-16.370452-36.692393-36.692393-36.692393l-4.515987 0c-9.596472 0-19.192944 3.951488-25.966924 10.725469L250.072767 489.420066c-12.418964 12.418964-12.418964 32.740904 0 45.159868l477.565601 477.565601c7.338479 7.338479 17.499449 11.854465 28.224917 11.854465l0 0c22.015436 0 40.079383-18.063947 40.079383-40.079383l0 0C796.507166 973.759647 791.99118 963.598677 784.652701 955.6957z"  ></path></symbol><symbol id="icon-next" viewBox="0 0 1024 1024"><path d="M246.121279 955.6957l438.050717-438.050717c2.822492-2.822492 2.822492-7.902977 0-11.289967L244.992282 67.175303c-6.77398-6.77398-10.725469-16.370452-10.725469-25.966924L234.266814 36.692393C234.266814 16.370452 250.637266 0 270.959206 0l4.515987 0c9.596472 0 19.192944 3.951488 25.966924 10.725469l478.694598 478.694598c12.418964 12.418964 12.418964 32.740904 0 45.159868l-477.565601 477.565601c-7.338479 7.338479-17.499449 11.854465-28.224917 11.854465l0 0c-22.015436 0-40.079383-18.063947-40.079383-40.079383l0 0C234.266814 973.759647 238.7828 963.598677 246.121279 955.6957z"  ></path></symbol><symbol id="icon-zoomin" viewBox="0 0 1024 1024"><path d="M725.504 652.864c46.4-61.44 71.744-136.448 71.744-218.752C797.248 230.464 632.768 64 430.656 64S64 230.464 64 434.112C64 639.36 228.48 805.76 430.656 805.76c86.656 0 164.48-30.144 227.52-81.088L889.984 960 960 891.264l-234.496-238.4z m-294.848 67.456c-155.776 0-282.624-128.896-282.624-286.208s126.848-286.208 282.624-286.208 282.624 128.896 282.624 286.208-126.912 286.208-282.624 286.208z"  ></path><path d="M235.712 369.92h390.72v127.104H235.712z"  ></path><path d="M367.488 238.144h127.104v390.72H367.488z"  ></path></symbol><symbol id="icon-close" viewBox="0 0 1024 1024"><path d="M570.24 512l259.2 259.2-58.88 58.24L512 570.24l-261.12 261.12-58.24-58.24L453.76 512 194.56 252.8l58.24-58.24L512 453.76l261.12-261.12 58.24 58.24z"  ></path></symbol><symbol id="icon-zoomout" viewBox="0 0 1024 1024"><path d="M725.504 652.864c46.4-61.44 71.744-136.448 71.744-218.752C797.248 230.464 632.768 64 430.656 64S64 230.464 64 434.112C64 639.36 228.48 805.76 430.656 805.76c86.656 0 164.48-30.144 227.52-81.088L889.984 960 960 891.264l-234.496-238.4z m-294.848 67.456c-155.776 0-282.624-128.896-282.624-286.208s126.848-286.208 282.624-286.208 282.624 128.896 282.624 286.208-126.912 286.208-282.624 286.208z"  ></path><path d="M235.712 369.92h390.72v127.104H235.712z"  ></path></symbol></svg>';
    if ((t = document.getElementsByTagName("script"))[t.length - 1].getAttribute("data-injectcss") && !e2.__iconfont__svg__cssinject__) {
      e2.__iconfont__svg__cssinject__ = true;
      try {
        document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>");
      } catch (t2) {
        console && console.log(t2);
      }
    }
    !function(t2) {
      if (document.addEventListener) if (~["complete", "loaded", "interactive"].indexOf(document.readyState)) setTimeout(t2, 0);
      else {
        document.addEventListener("DOMContentLoaded", function e3() {
          document.removeEventListener("DOMContentLoaded", e3, false), t2();
        }, false);
      }
      else document.attachEvent && (r2 = t2, o2 = e2.document, i2 = false, (a2 = function() {
        try {
          o2.documentElement.doScroll("left");
        } catch (t3) {
          return void setTimeout(a2, 50);
        }
        n3();
      })(), o2.onreadystatechange = function() {
        "complete" == o2.readyState && (o2.onreadystatechange = null, n3());
      });
      function n3() {
        i2 || (i2 = true, r2());
      }
      var r2, o2, i2, a2;
    }(function() {
      var t2, e3, r2, o2, i2, a2;
      (t2 = document.createElement("div")).innerHTML = n2, n2 = null, (e3 = t2.getElementsByTagName("svg")[0]) && (e3.setAttribute("aria-hidden", "true"), e3.style.position = "absolute", e3.style.width = 0, e3.style.height = 0, e3.style.overflow = "hidden", r2 = e3, (o2 = document.body).firstChild ? (i2 = r2, (a2 = o2.firstChild).parentNode.insertBefore(i2, a2)) : o2.appendChild(r2));
    });
  }
}();
var on = Vue.extend({ props: { type: { type: String, default: "" } }, data: function() {
  return { prefixCls: "vel" };
} });
function an(t, e2, n2, r2, o2, i2, a2, s2, c2, l2) {
  "boolean" != typeof a2 && (c2 = s2, s2 = a2, a2 = false);
  const u2 = "function" == typeof n2 ? n2.options : n2;
  let f2;
  if (t && t.render && (u2.render = t.render, u2.staticRenderFns = t.staticRenderFns, u2._compiled = true, o2 && (u2.functional = true)), r2 && (u2._scopeId = r2), i2 ? (f2 = function(t2) {
    (t2 = t2 || this.$vnode && this.$vnode.ssrContext || this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) || "undefined" == typeof __VUE_SSR_CONTEXT__ || (t2 = __VUE_SSR_CONTEXT__), e2 && e2.call(this, c2(t2)), t2 && t2._registeredComponents && t2._registeredComponents.add(i2);
  }, u2._ssrRegister = f2) : e2 && (f2 = a2 ? function(t2) {
    e2.call(this, l2(t2, this.$root.$options.shadowRoot));
  } : function(t2) {
    e2.call(this, s2(t2));
  }), f2) if (u2.functional) {
    const t2 = u2.render;
    u2.render = function(e3, n3) {
      return f2.call(n3), t2(e3, n3);
    };
  } else {
    const t2 = u2.beforeCreate;
    u2.beforeCreate = t2 ? [].concat(t2, f2) : [f2];
  }
  return n2;
}
var sn = !i(function() {
  return Object.isExtensible(Object.preventExtensions({}));
});
var cn = n(function(t) {
  var e2 = z.f, n2 = G("meta"), r2 = 0, o2 = Object.isExtensible || function() {
    return true;
  }, i2 = function(t2) {
    e2(t2, n2, { value: { objectID: "O" + ++r2, weakData: {} } });
  }, a2 = t.exports = { REQUIRED: false, fastKey: function(t2, e3) {
    if (!m(t2)) return "symbol" == typeof t2 ? t2 : ("string" == typeof t2 ? "S" : "P") + t2;
    if (!w(t2, n2)) {
      if (!o2(t2)) return "F";
      if (!e3) return "E";
      i2(t2);
    }
    return t2[n2].objectID;
  }, getWeakData: function(t2, e3) {
    if (!w(t2, n2)) {
      if (!o2(t2)) return true;
      if (!e3) return false;
      i2(t2);
    }
    return t2[n2].weakData;
  }, onFreeze: function(t2) {
    return sn && a2.REQUIRED && o2(t2) && !w(t2, n2) && i2(t2), t2;
  } };
  U[n2] = true;
});
var ln = (cn.REQUIRED, cn.fastKey, cn.getWeakData, cn.onFreeze, {});
var un = Ut("iterator");
var fn = Array.prototype;
var dn = {};
dn[Ut("toStringTag")] = "z";
var pn = "[object z]" === String(dn);
var vn = Ut("toStringTag");
var hn = "Arguments" == d(/* @__PURE__ */ function() {
  return arguments;
}());
var gn = pn ? d : function(t) {
  var e2, n2, r2;
  return void 0 === t ? "Undefined" : null === t ? "Null" : "string" == typeof (n2 = function(t2, e3) {
    try {
      return t2[e3];
    } catch (t3) {
    }
  }(e2 = Object(t), vn)) ? n2 : hn ? d(e2) : "Object" == (r2 = d(e2)) && "function" == typeof e2.callee ? "Arguments" : r2;
};
var mn = Ut("iterator");
var yn = function(t) {
  var e2 = t.return;
  if (void 0 !== e2) return E(e2.call(t)).value;
};
var bn = function(t, e2) {
  this.stopped = t, this.result = e2;
};
var wn = function(t, e2, n2) {
  var r2, o2, i2, a2, s2, c2, l2, u2, f2 = n2 && n2.that, d2 = !(!n2 || !n2.AS_ENTRIES), p2 = !(!n2 || !n2.IS_ITERATOR), v2 = !(!n2 || !n2.INTERRUPTED), h2 = Lt(e2, f2, 1 + d2 + v2), g2 = function(t2) {
    return r2 && yn(r2), new bn(true, t2);
  }, m2 = function(t2) {
    return d2 ? (E(t2), v2 ? h2(t2[0], t2[1], g2) : h2(t2[0], t2[1])) : v2 ? h2(t2, g2) : h2(t2);
  };
  if (p2) r2 = t;
  else {
    if ("function" != typeof (o2 = function(t2) {
      if (null != t2) return t2[mn] || t2["@@iterator"] || ln[gn(t2)];
    }(t))) throw TypeError("Target is not iterable");
    if (void 0 !== (u2 = o2) && (ln.Array === u2 || fn[un] === u2)) {
      for (i2 = 0, a2 = ut(t.length); a2 > i2; i2++) if ((s2 = m2(t[i2])) && s2 instanceof bn) return s2;
      return new bn(false);
    }
    r2 = o2.call(t);
  }
  for (c2 = r2.next; !(l2 = c2.call(r2)).done; ) {
    try {
      s2 = m2(l2.value);
    } catch (t2) {
      throw yn(r2), t2;
    }
    if ("object" == typeof s2 && s2 && s2 instanceof bn) return s2;
  }
  return new bn(false);
};
var _n = function(t, e2, n2) {
  if (!(t instanceof e2)) throw TypeError("Incorrect " + (n2 ? n2 + " " : "") + "invocation");
  return t;
};
var xn = Ut("iterator");
var Sn = false;
try {
  On = 0, kn = { next: function() {
    return { done: !!On++ };
  }, return: function() {
    Sn = true;
  } };
  kn[xn] = function() {
    return this;
  }, Array.from(kn, function() {
    throw 2;
  });
} catch (t) {
}
var On;
var kn;
var In;
var En;
var Cn;
var zn = z.f;
var Tn = Ut("toStringTag");
var jn = function(t, e2, n2) {
  t && !w(t = n2 ? t : t.prototype, Tn) && zn(t, Tn, { configurable: true, value: e2 });
};
var Ln = function(t, e2, n2) {
  for (var r2 in e2) nt(t, r2, e2[r2], n2);
  return t;
};
var An = !i(function() {
  function t() {
  }
  return t.prototype.constructor = null, Object.getPrototypeOf(new t()) !== t.prototype;
});
var Mn = X("IE_PROTO");
var Rn = Object.prototype;
var Dn = An ? Object.getPrototypeOf : function(t) {
  return t = At(t), w(t, Mn) ? t[Mn] : "function" == typeof t.constructor && t instanceof t.constructor ? t.constructor.prototype : t instanceof Object ? Rn : null;
};
var Pn = Ut("iterator");
var Nn = false;
[].keys && ("next" in (Cn = [].keys()) ? (En = Dn(Dn(Cn))) !== Object.prototype && (In = En) : Nn = true), (null == In || i(function() {
  var t = {};
  return In[Pn].call(t) !== t;
})) && (In = {}), w(In, Pn) || T(In, Pn, function() {
  return this;
});
var Fn = { IteratorPrototype: In, BUGGY_SAFARI_ITERATORS: Nn };
var Bn = Fn.IteratorPrototype;
var Yn = function() {
  return this;
};
var $n = Fn.IteratorPrototype;
var Gn = Fn.BUGGY_SAFARI_ITERATORS;
var Vn = Ut("iterator");
var Xn = function() {
  return this;
};
var Un = function(t, e2, n2, r2, o2, i2, a2) {
  !function(t2, e3, n3) {
    var r3 = e3 + " Iterator";
    t2.prototype = _e(Bn, { next: u(1, n3) }), jn(t2, r3, false), ln[r3] = Yn;
  }(n2, e2, r2);
  var s2, c2, l2, f2 = function(t2) {
    if (t2 === o2 && g2) return g2;
    if (!Gn && t2 in v2) return v2[t2];
    switch (t2) {
      case "keys":
      case "values":
      case "entries":
        return function() {
          return new n2(this, t2);
        };
    }
    return function() {
      return new n2(this);
    };
  }, d2 = e2 + " Iterator", p2 = false, v2 = t.prototype, h2 = v2[Vn] || v2["@@iterator"] || o2 && v2[o2], g2 = !Gn && h2 || f2(o2), m2 = "Array" == e2 && v2.entries || h2;
  if (m2 && (s2 = Dn(m2.call(new t())), $n !== Object.prototype && s2.next && (Dn(s2) !== $n && (de ? de(s2, $n) : "function" != typeof s2[Vn] && T(s2, Vn, Xn)), jn(s2, d2, true))), "values" == o2 && h2 && "values" !== h2.name && (p2 = true, g2 = function() {
    return h2.call(this);
  }), v2[Vn] !== g2 && T(v2, Vn, g2), ln[e2] = g2, o2) if (c2 = { values: f2("values"), keys: i2 ? g2 : f2("keys"), entries: f2("entries") }, a2) for (l2 in c2) (Gn || p2 || !(l2 in v2)) && nt(v2, l2, c2[l2]);
  else jt({ target: e2, proto: true, forced: Gn || p2 }, c2);
  return c2;
};
var Wn = Ut("species");
var Hn = z.f;
var Kn = cn.fastKey;
var qn = et.set;
var Qn = et.getterFor;
var Jn = (function(t, e2, n2) {
  var r2 = -1 !== t.indexOf("Map"), a2 = -1 !== t.indexOf("Weak"), s2 = r2 ? "set" : "add", c2 = o[t], l2 = c2 && c2.prototype, u2 = c2, f2 = {}, d2 = function(t2) {
    var e3 = l2[t2];
    nt(l2, t2, "add" == t2 ? function(t3) {
      return e3.call(this, 0 === t3 ? 0 : t3), this;
    } : "delete" == t2 ? function(t3) {
      return !(a2 && !m(t3)) && e3.call(this, 0 === t3 ? 0 : t3);
    } : "get" == t2 ? function(t3) {
      return a2 && !m(t3) ? void 0 : e3.call(this, 0 === t3 ? 0 : t3);
    } : "has" == t2 ? function(t3) {
      return !(a2 && !m(t3)) && e3.call(this, 0 === t3 ? 0 : t3);
    } : function(t3, n3) {
      return e3.call(this, 0 === t3 ? 0 : t3, n3), this;
    });
  };
  if (zt(t, "function" != typeof c2 || !(a2 || l2.forEach && !i(function() {
    new c2().entries().next();
  })))) u2 = n2.getConstructor(e2, t, r2, s2), cn.REQUIRED = true;
  else if (zt(t, true)) {
    var p2 = new u2(), v2 = p2[s2](a2 ? {} : -0, 1) != p2, h2 = i(function() {
      p2.has(1);
    }), g2 = function(t2, e3) {
      if (!e3 && !Sn) return false;
      var n3 = false;
      try {
        var r3 = {};
        r3[xn] = function() {
          return { next: function() {
            return { done: n3 = true };
          } };
        }, t2(r3);
      } catch (t3) {
      }
      return n3;
    }(function(t2) {
      new c2(t2);
    }), y2 = !a2 && i(function() {
      for (var t2 = new c2(), e3 = 5; e3--; ) t2[s2](e3, e3);
      return !t2.has(-0);
    });
    g2 || ((u2 = e2(function(e3, n3) {
      _n(e3, u2, t);
      var o2 = pe(new c2(), e3, u2);
      return null != n3 && wn(n3, o2[s2], { that: o2, AS_ENTRIES: r2 }), o2;
    })).prototype = l2, l2.constructor = u2), (h2 || y2) && (d2("delete"), d2("has"), r2 && d2("get")), (y2 || v2) && d2(s2), a2 && l2.clear && delete l2.clear;
  }
  f2[t] = u2, jt({ global: true, forced: u2 != c2 }, f2), jn(u2, t), a2 || n2.setStrong(u2, t, r2);
}("Set", function(t) {
  return function() {
    return t(this, arguments.length ? arguments[0] : void 0);
  };
}, { getConstructor: function(t, e2, n2, r2) {
  var o2 = t(function(t2, i3) {
    _n(t2, o2, e2), qn(t2, { type: e2, index: _e(null), first: void 0, last: void 0, size: 0 }), a || (t2.size = 0), null != i3 && wn(i3, t2[r2], { that: t2, AS_ENTRIES: n2 });
  }), i2 = Qn(e2), s2 = function(t2, e3, n3) {
    var r3, o3, s3 = i2(t2), l2 = c2(t2, e3);
    return l2 ? l2.value = n3 : (s3.last = l2 = { index: o3 = Kn(e3, true), key: e3, value: n3, previous: r3 = s3.last, next: void 0, removed: false }, s3.first || (s3.first = l2), r3 && (r3.next = l2), a ? s3.size++ : t2.size++, "F" !== o3 && (s3.index[o3] = l2)), t2;
  }, c2 = function(t2, e3) {
    var n3, r3 = i2(t2), o3 = Kn(e3);
    if ("F" !== o3) return r3.index[o3];
    for (n3 = r3.first; n3; n3 = n3.next) if (n3.key == e3) return n3;
  };
  return Ln(o2.prototype, { clear: function() {
    for (var t2 = i2(this), e3 = t2.index, n3 = t2.first; n3; ) n3.removed = true, n3.previous && (n3.previous = n3.previous.next = void 0), delete e3[n3.index], n3 = n3.next;
    t2.first = t2.last = void 0, a ? t2.size = 0 : this.size = 0;
  }, delete: function(t2) {
    var e3 = i2(this), n3 = c2(this, t2);
    if (n3) {
      var r3 = n3.next, o3 = n3.previous;
      delete e3.index[n3.index], n3.removed = true, o3 && (o3.next = r3), r3 && (r3.previous = o3), e3.first == n3 && (e3.first = r3), e3.last == n3 && (e3.last = o3), a ? e3.size-- : this.size--;
    }
    return !!n3;
  }, forEach: function(t2) {
    for (var e3, n3 = i2(this), r3 = Lt(t2, arguments.length > 1 ? arguments[1] : void 0, 3); e3 = e3 ? e3.next : n3.first; ) for (r3(e3.value, e3.key, this); e3 && e3.removed; ) e3 = e3.previous;
  }, has: function(t2) {
    return !!c2(this, t2);
  } }), Ln(o2.prototype, n2 ? { get: function(t2) {
    var e3 = c2(this, t2);
    return e3 && e3.value;
  }, set: function(t2, e3) {
    return s2(this, 0 === t2 ? 0 : t2, e3);
  } } : { add: function(t2) {
    return s2(this, t2 = 0 === t2 ? 0 : t2, t2);
  } }), a && Hn(o2.prototype, "size", { get: function() {
    return i2(this).size;
  } }), o2;
}, setStrong: function(t, e2, n2) {
  var r2 = e2 + " Iterator", o2 = Qn(e2), i2 = Qn(r2);
  Un(t, e2, function(t2, e3) {
    qn(this, { type: r2, target: t2, state: o2(t2), kind: e3, last: void 0 });
  }, function() {
    for (var t2 = i2(this), e3 = t2.kind, n3 = t2.last; n3 && n3.removed; ) n3 = n3.previous;
    return t2.target && (t2.last = n3 = n3 ? n3.next : t2.state.first) ? "keys" == e3 ? { value: n3.key, done: false } : "values" == e3 ? { value: n3.value, done: false } : { value: [n3.key, n3.value], done: false } : (t2.target = void 0, { value: void 0, done: true });
  }, n2 ? "entries" : "values", !n2, true), function(t2) {
    var e3 = it(t2), n3 = z.f;
    a && e3 && !e3[Wn] && n3(e3, Wn, { configurable: true, get: function() {
      return this;
    } });
  }(e2);
} }), pn ? {}.toString : function() {
  return "[object " + gn(this) + "]";
});
pn || nt(Object.prototype, "toString", Jn, { unsafe: true });
var Zn = function(t) {
  return function(e2, n2) {
    var r2, o2, i2 = String(h(e2)), a2 = ct(n2), s2 = i2.length;
    return a2 < 0 || a2 >= s2 ? t ? "" : void 0 : (r2 = i2.charCodeAt(a2)) < 55296 || r2 > 56319 || a2 + 1 === s2 || (o2 = i2.charCodeAt(a2 + 1)) < 56320 || o2 > 57343 ? t ? i2.charAt(a2) : r2 : t ? i2.slice(a2, a2 + 2) : o2 - 56320 + (r2 - 55296 << 10) + 65536;
  };
};
var tr = { codeAt: Zn(false), charAt: Zn(true) }.charAt;
var er = et.set;
var nr = et.getterFor("String Iterator");
Un(String, "String", function(t) {
  er(this, { type: "String Iterator", string: String(t), index: 0 });
}, function() {
  var t, e2 = nr(this), n2 = e2.string, r2 = e2.index;
  return r2 >= n2.length ? { value: void 0, done: true } : (t = tr(n2, r2), e2.index += t.length, { value: t, done: false });
});
var rr = Ut("unscopables");
var or = Array.prototype;
null == or[rr] && z.f(or, rr, { configurable: true, value: _e(null) });
var ir = function(t) {
  or[rr][t] = true;
};
var ar = et.set;
var sr = et.getterFor("Array Iterator");
var cr = Un(Array, "Array", function(t, e2) {
  ar(this, { type: "Array Iterator", target: g(t), index: 0, kind: e2 });
}, function() {
  var t = sr(this), e2 = t.target, n2 = t.kind, r2 = t.index++;
  return !e2 || r2 >= e2.length ? (t.target = void 0, { value: void 0, done: true }) : "keys" == n2 ? { value: r2, done: false } : "values" == n2 ? { value: e2[r2], done: false } : { value: [r2, e2[r2]], done: false };
}, "values");
ln.Arguments = ln.Array, ir("keys"), ir("values"), ir("entries");
var lr = Ut("iterator");
var ur = Ut("toStringTag");
var fr = cr.values;
for (dr in oe) {
  pr = o[dr], vr = pr && pr.prototype;
  if (vr) {
    if (vr[lr] !== fr) try {
      T(vr, lr, fr);
    } catch (t) {
      vr[lr] = fr;
    }
    if (vr[ur] || T(vr, ur, dr), oe[dr]) {
      for (hr in cr) if (vr[hr] !== cr[hr]) try {
        T(vr, hr, cr[hr]);
      } catch (t) {
        vr[hr] = cr[hr];
      }
    }
  }
}
var pr;
var vr;
var hr;
var dr;
var gr = [].join;
var mr = v != Object;
var yr = ie("join", ",");
jt({ target: "Array", proto: true, forced: mr || !yr }, { join: function(t) {
  return gr.call(g(this), void 0 === t ? "," : t);
} });
var br;
var wr = "undefined" != typeof navigator && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());
function _r(t) {
  return function(t2, e2) {
    return function(t3, e3) {
      var n2 = wr ? e3.media || "default" : t3, r2 = xr[n2] || (xr[n2] = { ids: /* @__PURE__ */ new Set(), styles: [] });
      if (!r2.ids.has(t3)) {
        r2.ids.add(t3);
        var o2 = e3.source;
        if (e3.map && (o2 += "\n/*# sourceURL=" + e3.map.sources[0] + " */", o2 += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(e3.map)))) + " */"), r2.element || (r2.element = document.createElement("style"), r2.element.type = "text/css", e3.media && r2.element.setAttribute("media", e3.media), void 0 === br && (br = document.head || document.getElementsByTagName("head")[0]), br.appendChild(r2.element)), "styleSheet" in r2.element) r2.styles.push(o2), r2.element.styleSheet.cssText = r2.styles.filter(Boolean).join("\n");
        else {
          var i2 = r2.ids.size - 1, a2 = document.createTextNode(o2), s2 = r2.element.childNodes;
          s2[i2] && r2.element.removeChild(s2[i2]), s2.length ? r2.element.insertBefore(a2, s2[i2]) : r2.element.appendChild(a2);
        }
      }
    }(t2, e2);
  };
}
var xr = {};
var Sr = an({ render: function() {
  var t = this.$createElement, e2 = this._self._c || t;
  return e2("svg", { class: this.prefixCls + "-icon icon", attrs: { "aria-hidden": "true" } }, [e2("use", { attrs: { "xlink:href": "#icon-" + this.type } })]);
}, staticRenderFns: [] }, function(t) {
  t && t("data-v-7221e0ec_0", { source: ".vel-icon[data-v-7221e0ec]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}", map: void 0, media: void 0 });
}, on, "data-v-7221e0ec", false, void 0, false, _r, void 0, void 0);
var Or = function() {
};
var kr = an({ render: function() {
  var t = this, e2 = t.$createElement, n2 = t._self._c || e2;
  return n2("div", { class: t.prefixCls + "-toolbar" }, [n2("div", { staticClass: "toolbar-btn toolbar-btn__zoomin", on: { click: t.zoomIn } }, [n2("svg-icon", { attrs: { type: "zoomin" } })], 1), t._v(" "), n2("div", { staticClass: "toolbar-btn toolbar-btn__zoomout", on: { click: t.zoomOut } }, [n2("svg-icon", { attrs: { type: "zoomout" } })], 1), t._v(" "), n2("div", { staticClass: "toolbar-btn toolbar-btn__resize", on: { click: t.resize } }, [n2("svg-icon", { attrs: { type: "resize" } })], 1), t._v(" "), n2("div", { staticClass: "toolbar-btn toolbar-btn__rotate", on: { click: t.rotateLeft } }, [n2("svg-icon", { attrs: { type: "rotate-left" } })], 1), t._v(" "), n2("div", { staticClass: "toolbar-btn toolbar-btn__rotate", on: { click: t.rotateRight } }, [n2("svg-icon", { attrs: { type: "rotate-right" } })], 1)]);
}, staticRenderFns: [] }, function(t) {
  t && t("data-v-59338679_0", { source: ".vel-toolbar[data-v-59338679]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:absolute;overflow:hidden;bottom:8px;left:50%;-webkit-transform:translate(-50%);transform:translate(-50%);opacity:.9;display:-webkit-box;display:-ms-flexbox;display:flex;background-color:#2d2d2d;border-radius:4px;padding:0}.vel-toolbar .toolbar-btn[data-v-59338679]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-ms-flex-negative:0;flex-shrink:0;cursor:pointer;padding:6px 10px;font-size:20px;color:#fff;background-color:#2d2d2d;-webkit-tap-highlight-color:transparent;outline:0}.vel-toolbar .toolbar-btn[data-v-59338679]:active,.vel-toolbar .toolbar-btn[data-v-59338679]:hover{background-color:#3d3d3d}", map: void 0, media: void 0 });
}, Vue.extend({ components: { SvgIcon: Sr }, props: { zoomIn: { type: Function, default: Or }, zoomOut: { type: Function, default: Or }, rotateLeft: { type: Function, default: Or }, rotateRight: { type: Function, default: Or }, resize: { type: Function, default: Or } }, data: function() {
  return { prefixCls: "vel" };
} }), "data-v-59338679", false, void 0, false, _r, void 0, void 0);
var Ir = an({ render: function() {
  var t = this.$createElement, e2 = this._self._c || t;
  return e2("div", { class: this.prefixCls + "-loading" }, [e2("div", { staticClass: "ring" })]);
}, staticRenderFns: [] }, function(t) {
  t && t("data-v-5174e3cb_0", { source: '.vel-loading[data-v-5174e3cb]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.vel-loading .ring[data-v-5174e3cb]{display:inline-block;width:64px;height:64px}.vel-loading .ring[data-v-5174e3cb]::after{content:" ";display:block;width:46px;height:46px;margin:1px;border-radius:50%;border:5px solid rgba(255,255,255,.7);border-color:rgba(255,255,255,.7) transparent rgba(255,255,255,.7) transparent;-webkit-animation:ring-data-v-5174e3cb 1.2s linear infinite;animation:ring-data-v-5174e3cb 1.2s linear infinite}@-webkit-keyframes ring-data-v-5174e3cb{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes ring-data-v-5174e3cb{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}', map: void 0, media: void 0 });
}, Vue.extend({ data: function() {
  return { prefixCls: "vel" };
} }), "data-v-5174e3cb", false, void 0, false, _r, void 0, void 0);
var Er = an({ render: function() {
  var t = this.$createElement, e2 = this._self._c || t;
  return e2("div", { class: this.prefixCls + "-on-error" }, [e2("svg-icon", { attrs: { clas: "icon", type: "img-broken" } })], 1);
}, staticRenderFns: [] }, function(t) {
  t && t("data-v-137806a2_0", { source: ".vel-on-error[data-v-137806a2]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.vel-on-error .icon[data-v-137806a2]{font-size:80px;color:#aaa}", map: void 0, media: void 0 });
}, Vue.extend({ components: { SvgIcon: Sr }, data: function() {
  return { prefixCls: "vel" };
} }), "data-v-137806a2", false, void 0, false, _r, void 0, void 0);
var Cr = an({ render: function() {
  var t = this.$createElement;
  return (this._self._c || t)("div", { class: this.prefixCls + "-img-title" }, [this._t("default")], 2);
}, staticRenderFns: [] }, function(t) {
  t && t("data-v-7f0f8cef_0", { source: ".vel-img-title[data-v-7f0f8cef]{overflow:hidden;position:absolute;left:50%;bottom:60px;-webkit-transform:translate(-50%);transform:translate(-50%);max-width:80%;font-size:12px;line-height:1;text-align:center;text-overflow:ellipsis;color:#ccc;opacity:.8;white-space:nowrap;cursor:default;-webkit-transition:opacity .15s;transition:opacity .15s}.vel-img-title[data-v-7f0f8cef]:hover{opacity:1}", map: void 0, media: void 0 });
}, Vue.extend({ data: function() {
  return { prefixCls: "vel" };
} }), "data-v-7f0f8cef", false, void 0, false, _r, void 0, void 0);
var zr = function(t, e2, n2) {
  var r2 = y(e2);
  r2 in t ? z.f(t, r2, u(0, n2)) : t[r2] = n2;
};
var Tr = Zt("slice");
var jr = Ut("species");
var Lr = [].slice;
var Ar = Math.max;
jt({ target: "Array", proto: true, forced: !Tr }, { slice: function(t, e2) {
  var n2, r2, o2, i2 = g(this), a2 = ut(i2.length), s2 = pt(t, a2), c2 = pt(void 0 === e2 ? a2 : e2, a2);
  if (Mt(i2) && ("function" != typeof (n2 = i2.constructor) || n2 !== Array && !Mt(n2.prototype) ? m(n2) && null === (n2 = n2[jr]) && (n2 = void 0) : n2 = void 0, n2 === Array || void 0 === n2)) return Lr.call(i2, s2, c2);
  for (r2 = new (void 0 === n2 ? Array : n2)(Ar(c2 - s2, 0)), o2 = 0; s2 < c2; s2++, o2++) s2 in i2 && zr(r2, o2, i2[s2]);
  return r2.length = o2, r2;
} });
var Mr = Vue.prototype.$isServer;
var Rr = false;
if (!Mr) try {
  Dr = {};
  Object.defineProperty(Dr, "passive", { get: function() {
    Rr = true;
  } }), window.addEventListener("test-passive", function() {
  }, Dr);
} catch (t) {
}
var Dr;
var Pr = function(t, e2, n2, r2) {
  void 0 === r2 && (r2 = false), Mr || t.addEventListener(e2, n2, !!Rr && { capture: false, passive: r2 });
};
var Nr = function(t, e2, n2) {
  Mr || t.removeEventListener(e2, n2);
};
var Fr = Object.prototype.toString;
var Br = function(t) {
  return function(e2) {
    return Fr.call(e2).slice(8, -1) === t;
  };
};
function Yr(t) {
  return Br("Array")(t);
}
var $r = function(t) {
  return !!t && Br("String")(t);
};
function Gr(t) {
  return null != t;
}
function Vr(t) {
  return function(t2) {
    return !!t2 && Br("Object")(t2);
  }(t) && $r(t.src);
}
var Xr = an({ render: function() {
  var t = this, e2 = t.$createElement, n2 = t._self._c || e2;
  return n2("transition", { attrs: { name: t.prefixCls + "-fade" } }, [t.visible ? n2("div", { ref: "modal", class: [t.prefixCls + "-img-modal", t.prefixCls + "-modal"], on: { click: function(e3) {
    return e3.target !== e3.currentTarget ? null : t.onMaskClick(e3);
  }, wheel: t.onWheel } }, [n2("transition", { attrs: { name: t.prefixCls + "-fade", mode: "out-in" } }, [t.loading ? t._t("loading", [n2("img-loading")]) : t._e(), t._v(" "), t.loadError ? t._t("onerror", [n2("img-on-error")]) : t._e(), t._v(" "), t.loading || t.loadError ? t._e() : n2("div", { class: t.prefixCls + "-img-wrapper", style: t.imgWrapperStyle }, [n2("img", { ref: "realImg", class: t.prefixCls + "-img", attrs: { src: t.visibleImgSrc, alt: t.imgAlt, draggable: "false" }, on: { mousedown: function(e3) {
    return t.handleMouseDown(e3);
  }, mouseup: function(e3) {
    return t.handleMouseUp(e3);
  }, mousemove: function(e3) {
    return t.handleMouseMove(e3);
  }, touchstart: function(e3) {
    return t.handleTouchStart(e3);
  }, touchmove: function(e3) {
    return t.handleTouchMove(e3);
  }, touchend: function(e3) {
    return t.handleTouchEnd(e3);
  }, load: t.handleRealImgLoad, dragstart: function(e3) {
    return t.handleDragStart(e3);
  }, dblclick: t.handleDblClick } })])], 2), t._v(" "), n2("img", { staticStyle: { display: "none" }, attrs: { src: t.visibleImgSrc }, on: { error: t.handleImgError, load: t.handleTestImgLoad } }), t._v(" "), n2("div", { class: t.prefixCls + "-btns-wrapper" }, [t._t("prev-btn", [t.imgList.length > 1 ? n2("div", { staticClass: "btn__prev", class: { disable: !t.loop && t.imgIndex <= 0 }, on: { click: t.onPrevClick } }, [n2("svg-icon", { attrs: { type: "prev" } })], 1) : t._e()], { prev: t.onPrevClick }), t._v(" "), t._t("next-btn", [t.imgList.length > 1 ? n2("div", { staticClass: "btn__next", class: { disable: !t.loop && t.imgIndex >= t.imgList.length - 1 }, on: { click: t.onNextClick } }, [n2("svg-icon", { attrs: { type: "next" } })], 1) : t._e()], { next: t.onNextClick }), t._v(" "), t._t("close-btn", [n2("div", { staticClass: "btn__close", on: { click: t.closeDialog } }, [n2("svg-icon", { attrs: { type: "close" } })], 1)], { close: t.closeDialog }), t._v(" "), !t.imgTitle || t.titleDisabled || t.loading || t.loadError ? t._e() : t._t("title", [n2("img-title", [t._v(t._s(t.imgTitle))])]), t._v(" "), t._t("toolbar", [n2("toolbar", { attrs: { prefixCls: t.prefixCls, zoomIn: t.zoomIn, zoomOut: t.zoomOut, rotateLeft: t.rotateLeft, rotateRight: t.rotateRight, resize: t.resize } })], { toolbarMethods: { zoomIn: t.zoomIn, zoomOut: t.zoomOut, rotate: t.rotateLeft, rotateLeft: t.rotateLeft, rotateRight: t.rotateRight, resize: t.resize } })], 2)], 1) : t._e()]);
}, staticRenderFns: [] }, function(t) {
  t && t("data-v-342a130c_0", { source: ".vel-fade-enter-active[data-v-342a130c],.vel-fade-leave-active[data-v-342a130c]{-webkit-transition:all .3s ease;transition:all .3s ease}.vel-fade-enter[data-v-342a130c],.vel-fade-leave-to[data-v-342a130c]{opacity:0}.vel-img-swiper[data-v-342a130c]{position:relative;display:block}.vel-modal[data-v-342a130c]{z-index:9998;position:fixed;top:0;left:0;right:0;bottom:0;margin:0;background:rgba(0,0,0,.5)}.vel-img-wrapper[data-v-342a130c]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;margin:0;position:absolute;top:50%;left:50%;-webkit-transform:translate(-50% -50%);transform:translate(-50% -50%);-webkit-transition:.3s linear;transition:.3s linear;will-change:transform opacity}.vel-img[data-v-342a130c]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;max-width:80vw;max-height:80vh;display:block;position:relative;-webkit-transition:-webkit-transform .3s ease-in-out;transition:-webkit-transform .3s ease-in-out;transition:transform .3s ease-in-out;transition:transform .3s ease-in-out,-webkit-transform .3s ease-in-out;-webkit-box-shadow:rgba(0,0,0,.7) 0 5px 20px 2px;box-shadow:rgba(0,0,0,.7) 0 5px 20px 2px;background-color:rgba(0,0,0,.7)}@media (max-width:750px){.vel-img[data-v-342a130c]{max-width:85vw;max-height:95vh}}.vel-btns-wrapper .btn__close[data-v-342a130c],.vel-btns-wrapper .btn__next[data-v-342a130c],.vel-btns-wrapper .btn__prev[data-v-342a130c]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);cursor:pointer;opacity:.6;font-size:32px;color:#fff;-webkit-transition:.15s linear;transition:.15s linear;-webkit-tap-highlight-color:transparent;outline:0}.vel-btns-wrapper .btn__close[data-v-342a130c]:hover,.vel-btns-wrapper .btn__next[data-v-342a130c]:hover,.vel-btns-wrapper .btn__prev[data-v-342a130c]:hover{opacity:1}.vel-btns-wrapper .btn__close.disable[data-v-342a130c],.vel-btns-wrapper .btn__close.disable[data-v-342a130c]:hover,.vel-btns-wrapper .btn__next.disable[data-v-342a130c],.vel-btns-wrapper .btn__next.disable[data-v-342a130c]:hover,.vel-btns-wrapper .btn__prev.disable[data-v-342a130c],.vel-btns-wrapper .btn__prev.disable[data-v-342a130c]:hover{cursor:default;opacity:.2}.vel-btns-wrapper .btn__next[data-v-342a130c]{right:12px}.vel-btns-wrapper .btn__prev[data-v-342a130c]{left:12px}.vel-btns-wrapper .btn__close[data-v-342a130c]{top:24px;right:10px}@media (max-width:750px){.vel-btns-wrapper .btn__next[data-v-342a130c],.vel-btns-wrapper .btn__prev[data-v-342a130c]{font-size:20px}.vel-btns-wrapper .btn__close[data-v-342a130c]{font-size:24px}.vel-btns-wrapper .btn__next[data-v-342a130c]{right:4px}.vel-btns-wrapper .btn__prev[data-v-342a130c]{left:4px}}", map: void 0, media: void 0 });
}, function(t) {
  function e2() {
    var e3 = null !== t && t.apply(this, arguments) || this;
    return e3.prefixCls = "vel", e3.scale = 1, e3.lastScale = 1, e3.rotateDeg = 0, e3.imgIndex = 0, e3.top = 0, e3.left = 0, e3.lastX = 0, e3.lastY = 0, e3.isDraging = false, e3.loading = false, e3.loadError = false, e3.isTicking = false, e3.isGesturing = false, e3.wheeling = false, e3.lastBodyStyleOverflowY = "", e3.imgBaseInfo = { width: 0, height: 0, maxScale: 1 }, e3.touches = [], e3.rafId = 0, e3;
  }
  return function(t2, e3) {
    function n2() {
      this.constructor = t2;
    }
    Fe(t2, e3), t2.prototype = null === e3 ? Object.create(e3) : (n2.prototype = e3.prototype, new n2());
  }(e2, t), Object.defineProperty(e2.prototype, "imgList", { get: function() {
    return Yr(this.imgs) ? this.imgs.map(function(t2) {
      return "string" == typeof t2 ? { src: t2 } : Vr(t2) ? t2 : void 0;
    }).filter(Gr) : $r(this.imgs) ? [{ src: this.imgs }] : [];
  }, enumerable: false, configurable: true }), Object.defineProperty(e2.prototype, "visibleImgSrc", { get: function() {
    var t2;
    return null === (t2 = this.imgList[this.imgIndex]) || void 0 === t2 ? void 0 : t2.src;
  }, enumerable: false, configurable: true }), Object.defineProperty(e2.prototype, "imgTitle", { get: function() {
    var t2;
    return null === (t2 = this.imgList[this.imgIndex]) || void 0 === t2 ? void 0 : t2.title;
  }, enumerable: false, configurable: true }), Object.defineProperty(e2.prototype, "imgAlt", { get: function() {
    var t2;
    return (null === (t2 = this.imgList[this.imgIndex]) || void 0 === t2 ? void 0 : t2.alt) || "";
  }, enumerable: false, configurable: true }), Object.defineProperty(e2.prototype, "imgTotal", { get: function() {
    return this.imgList.length || 0;
  }, enumerable: false, configurable: true }), Object.defineProperty(e2.prototype, "imgWrapperStyle", { get: function() {
    var t2 = this, e3 = t2.scale, n2 = t2.top, r2 = t2.left, o2 = t2.rotateDeg, i2 = t2.moveDisabled, a2 = t2.loadError, s2 = t2.isDraging, c2 = t2.isGesturing;
    return { transform: "translate(-50%, -50%) scale(" + e3 + ") rotate(" + o2 + "deg)", top: "calc(50% + " + n2 + "px)", left: "calc(50% + " + r2 + "px)", cursor: i2 || a2 ? "default" : "move", transition: s2 || c2 ? "none" : "" };
  }, enumerable: false, configurable: true }), e2.prototype.checkMoveable = function(t2) {
    return void 0 === t2 && (t2 = 0), !this.moveDisabled && 0 === t2;
  }, e2.prototype.handleMouseDown = function(t2) {
    this.checkMoveable(t2.button) && (this.lastX = t2.clientX, this.lastY = t2.clientY, this.isDraging = true, t2.stopPropagation());
  }, e2.prototype.handleMouseUp = function(t2) {
    this.checkMoveable(t2.button) && (cancelAnimationFrame(this.rafId), this.isDraging = false, this.isTicking = false);
  }, e2.prototype.handleMouseMove = function(t2) {
    var e3 = this;
    this.checkMoveable(t2.button) && (this.isDraging && !this.isTicking && (this.isTicking = true, this.rafId = requestAnimationFrame(function() {
      e3.top = e3.top - e3.lastY + t2.clientY, e3.left = e3.left - e3.lastX + t2.clientX, e3.lastX = t2.clientX, e3.lastY = t2.clientY, e3.isTicking = false;
    })), t2.stopPropagation());
  }, e2.prototype.handleTouchStart = function(t2) {
    var e3 = t2.touches;
    e3.length > 1 ? (this.isGesturing = true, this.touches = e3) : (this.lastX = e3[0].clientX, this.lastY = e3[0].clientY, this.isDraging = true), t2.stopPropagation();
  }, e2.prototype.handleTouchMove = function(t2) {
    var e3 = this;
    if (!this.isTicking) {
      var n2 = t2.touches;
      this.checkMoveable() && !this.isGesturing && this.isDraging ? (this.isTicking = true, this.rafId = requestAnimationFrame(function() {
        if (n2[0]) {
          var t3 = n2[0].clientX, r2 = n2[0].clientY;
          e3.top = e3.top - e3.lastY + r2, e3.left = e3.left - e3.lastX + t3, e3.lastX = t3, e3.lastY = r2, e3.isTicking = false;
        }
      })) : this.isGesturing && this.touches.length > 1 && n2.length > 1 && (this.isTicking = true, this.rafId = requestAnimationFrame(function() {
        var t3 = (e3.getDistance(e3.touches[0], e3.touches[1]) - e3.getDistance(n2[0], n2[1])) / e3.imgBaseInfo.width;
        e3.touches = n2;
        var r2 = e3.scale - 1.3 * t3;
        r2 > 0.5 && r2 < 1.5 * e3.imgBaseInfo.maxScale && (e3.scale = r2), e3.isTicking = false;
      }));
    }
  }, e2.prototype.handleTouchEnd = function(t2) {
    cancelAnimationFrame(this.rafId), this.isDraging = false, this.isGesturing = false, this.isTicking = false;
  }, e2.prototype.handleDragStart = function(t2) {
    t2.preventDefault();
  }, e2.prototype.onWheel = function(t2) {
    var e3 = this;
    this.loadError || this.loading || this.isDraging || this.isGesturing || this.wheeling || !this.scrollDisabled || (this.wheeling = true, setTimeout(function() {
      e3.wheeling = false;
    }, 80), t2.deltaY < 0 ? this.zoomIn() : this.zoomOut());
  }, e2.prototype.handleKeyPress = function(t2) {
    !this.escDisabled && "Escape" === t2.key && this.visible && this.closeDialog(), "ArrowLeft" === t2.key && this.onPrevClick(), "ArrowRight" === t2.key && this.onNextClick();
  }, e2.prototype.handleWindowResize = function(t2) {
    this.getImgSize();
  }, e2.prototype.handleTestImgLoad = function(t2) {
    this.loading = false;
  }, e2.prototype.handleRealImgLoad = function(t2) {
    this.getImgSize();
  }, e2.prototype.handleImgError = function(t2) {
    this.loading = false, this.loadError = true, this.$emit("on-error", t2);
  }, e2.prototype.getImgSize = function() {
    var t2 = this.$refs.realImg;
    if (t2) {
      var e3 = t2.width, n2 = t2.height, r2 = t2.naturalWidth;
      this.imgBaseInfo.maxScale = r2 / e3, this.imgBaseInfo.width = e3, this.imgBaseInfo.height = n2;
    }
  }, e2.prototype.getDistance = function(t2, e3) {
    var n2 = t2.clientX - e3.clientX, r2 = t2.clientY - e3.clientY;
    return Math.sqrt(n2 * n2 + r2 * r2);
  }, e2.prototype.zoom = function(t2) {
    Math.abs(1 - t2) < 0.05 ? t2 = 1 : Math.abs(this.imgBaseInfo.maxScale - t2) < 0.05 && (t2 = this.imgBaseInfo.maxScale), this.lastScale = this.scale, this.scale = t2;
  }, e2.prototype.zoomIn = function() {
    var t2 = this.scale + 0.12;
    t2 < 3 * this.imgBaseInfo.maxScale && this.zoom(t2);
  }, e2.prototype.zoomOut = function() {
    var t2 = this.scale - (this.scale < 0.7 ? 0.1 : 0.12);
    t2 > 0.1 && this.zoom(t2);
  }, e2.prototype.rotateLeft = function() {
    this.rotateDeg -= 90;
  }, e2.prototype.rotateRight = function() {
    this.rotateDeg += 90;
  }, e2.prototype.handleDblClick = function() {
    this.scale !== this.imgBaseInfo.maxScale ? (this.lastScale = this.scale, this.scale = this.imgBaseInfo.maxScale) : this.scale = this.lastScale;
  }, e2.prototype.resize = function() {
    this.scale = 1, this.top = 0, this.left = 0;
  }, e2.prototype.onNextClick = function() {
    var t2 = this.imgIndex, e3 = this.loop ? (t2 + 1) % this.imgList.length : t2 + 1;
    !this.loop && e3 > this.imgList.length - 1 || this.setIndex(e3, ["on-next-click", "on-next"]);
  }, e2.prototype.onPrevClick = function() {
    var t2 = this.imgIndex, e3 = t2 - 1;
    if (0 === t2) {
      if (!this.loop) return;
      e3 = this.imgList.length - 1;
    }
    this.setIndex(e3, ["on-prev-click", "on-prev"]);
  }, e2.prototype.setIndex = function(t2, e3) {
    var n2 = this, r2 = this.imgIndex;
    this.reset(), this.imgIndex = t2, this.imgList[this.imgIndex] === this.imgList[t2] && this.$nextTick(function() {
      n2.loading = false;
    }), this.visible && r2 !== t2 && (e3 && (Yr(e3) ? e3.forEach(function(e4) {
      n2.$emit(e4, r2, t2);
    }) : this.$emit(e3, r2, t2)), this.$emit("on-index-change", r2, t2));
  }, e2.prototype.closeDialog = function() {
    this.$emit("hide");
  }, e2.prototype.onMaskClick = function() {
    this.maskClosable && this.$emit("hide");
  }, e2.prototype.reset = function() {
    this.scale = 1, this.rotateDeg = 0, this.top = 0, this.left = 0, this.isDraging = false, this.loading = true, this.loadError = false;
  }, e2.prototype.init = function() {
    var t2 = this;
    this.reset();
    var e3 = this.imgList.length;
    if (0 === e3) return this.imgIndex = 0, this.loading = false, void this.$nextTick(function() {
      t2.loadError = true;
    });
    this.imgIndex = this.index >= e3 ? e3 - 1 : this.index < 0 ? 0 : this.index;
  }, e2.prototype.disableScrolling = function() {
    document && (this.lastBodyStyleOverflowY = document.body.style.overflowY, document.body.style.overflowY = "hidden");
  }, e2.prototype.enableScrolling = function() {
    document && (document.body.style.overflowY = this.lastBodyStyleOverflowY);
  }, e2.prototype.onVisibleChanged = function(t2) {
    var e3 = this;
    t2 ? (this.init(), this.$nextTick(function() {
      Pr(e3.$refs.modal, "touchmove", function(t3) {
        t3.preventDefault();
      }), e3.scrollDisabled && e3.disableScrolling();
    })) : this.scrollDisabled && this.enableScrolling();
  }, e2.prototype.onIndexChange = function(t2) {
    t2 < 0 || t2 >= this.imgList.length || this.setIndex(t2);
  }, e2.prototype.mounted = function() {
    Pr(document, "keydown", this.handleKeyPress), Pr(window, "resize", this.handleWindowResize);
  }, e2.prototype.beforeDestroy = function() {
    Nr(document, "keydown", this.handleKeyPress), Nr(window, "resize", this.handleWindowResize);
  }, Be([nn({ type: [Array, String], default: function() {
    return "";
  } })], e2.prototype, "imgs", void 0), Be([nn({ type: Boolean, default: false })], e2.prototype, "visible", void 0), Be([nn({ type: Number, default: 0 })], e2.prototype, "index", void 0), Be([nn({ type: Boolean, default: false })], e2.prototype, "escDisabled", void 0), Be([nn({ type: Boolean, default: false })], e2.prototype, "moveDisabled", void 0), Be([nn({ type: Boolean, default: false })], e2.prototype, "titleDisabled", void 0), Be([nn({ type: Boolean, default: false })], e2.prototype, "loop", void 0), Be([nn({ type: Boolean, default: true })], e2.prototype, "scrollDisabled", void 0), Be([nn({ type: Boolean, default: true })], e2.prototype, "maskClosable", void 0), Be([rn("visible", { immediate: true })], e2.prototype, "onVisibleChanged", null), Be([rn("index")], e2.prototype, "onIndexChange", null), e2 = Be([tn({ name: "vue-easy-lightbox", components: { SvgIcon: Sr, Toolbar: kr, ImgLoading: Ir, ImgOnError: Er, ImgTitle: Cr } })], e2);
}(Vue), "data-v-342a130c", false, void 0, false, _r, void 0, void 0);
Xr.install = function(t) {
  t.component("vue-easy-lightbox", Xr);
}, "undefined" != typeof window && window.Vue && window.Vue.use(Xr.install);
var vue_easy_lightbox_es5_esm_min_default = Xr;
export {
  vue_easy_lightbox_es5_esm_min_default as default
};
//# sourceMappingURL=vue-easy-lightbox.js.map
