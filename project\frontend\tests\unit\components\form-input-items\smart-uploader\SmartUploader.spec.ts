// TODO: All libs are bugged, they cant run vue 2.7 with script setup syntax
// Ref: https://github.com/vuejs/vue-test-utils/issues/1983
// import { render } from '@testing-library/vue';
import { mount } from '@vue/test-utils';
import axios from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { createFormApp } from '@core/composables/use-dynamic-form-app';
import FileInfo from '@core/helpers/form-data/FileInfo';

import converters from '@helpers/helpers/image-utils';

import SmartUploader from '@/components/form-input-items/smart-uploader/SmartUploader.vue';

describe('SmartUploader', () => {
  const app = createFormApp({ apiPath: { localSchema: {} as any } });
  const dynamicFormInstance = app.createFormInstance();
  let component: ReturnType<typeof mount>;
  let vm: typeof component.vm &
    InstanceType<typeof SmartUploader> & {
      addFilesHandler: Function;
      removeFile: Function;
      checkPassword: Function;
      flagNeedPassword: Function;
      getAdditionalUploadPayload: Function;
      fileChecker: Function;

      errors: string[];
      value: FileInfo[];
      files: FileInfo[];
      currentPassword: string;
      isPasswordSuccessOnce: boolean;
      hasPasswordFailedOnce: boolean;
      currentErrorFile: File;
      currentFiles: File[];
      modalState: string;
      alreadyAtMax: boolean;
    };

  // Utils function
  const getMockPdfFile = (fileName: string) => {
    const base64 = 'JVBERi0xLjQKJaqrrK0KNCAwIG9iago8PAovVGl0bGUgKP7/';
    const binaryString = atob(base64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new File([bytes], fileName, { type: 'application/pdf' });
  };

  // Mock usePdf
  const mocks = vi.hoisted(() => {
    return {
      usePdf: vi.fn().mockReturnValue({
        openPdfByFile: vi
          .fn()
          .mockResolvedValueOnce({
            success: true,
            needPassword: false,
            pdfProxy: { getData: () => ({ buffer: [1, 2, 3] }) },
          })
          .mockResolvedValueOnce({
            success: false,
            needPassword: true,
            password: '1234',
            pdfProxy: { getData: () => ({ buffer: [1, 2, 3] }) },
          })
          .mockResolvedValueOnce({
            success: true,
            needPassword: true,
            pdfProxy: { getData: () => ({ buffer: [1, 2, 3] }) },
          })
          .mockResolvedValue({
            success: true,
            needPassword: true,
            password: '1234',
            pdfProxy: { getData: () => ({ buffer: [1, 2, 3] }) },
          }),
      }),
    };
  });
  vi.mock('@/composables/use-pdf', () => ({
    usePdf: mocks.usePdf,
  }));

  // Mock Upload API
  const postSpy = vi.spyOn(axios, 'post').mockImplementation(() =>
    Promise.resolve<any>({
      data: {
        id: 'test',
        pass_validation: false,
      } satisfies Types.ApiSmartUploaderUploadResponse,
    }),
  );

  // Mock Converters
  const changeHeicTypeSpy = vi.spyOn(converters, 'changeHeicType').mockImplementation(file =>
    Promise.resolve({
      converted: true,
      file,
    }),
  );
  const resizeImageFileToCapSpy = vi
    .spyOn(converters, 'resizeImageFileToCap')
    .mockImplementation(file =>
      Promise.resolve({
        converted: true,
        file,
      }),
    );

  const defaultSchema: Types.ISchemaItemSmartUploader = {
    name: 'smartuploader_test',
    type: 'SmartUploader',
    override_images: {
      add_file: 'https://example.com/img.jpg',
    },
    upload_url: 'https://example.com/upload/',
  };

  function mountComponent(schema: Partial<Types.ISchemaItemSmartUploader> = defaultSchema) {
    component = mount(SmartUploader, {
      propsData: {
        element: schema,
      },
      provide: { dynamicFormInstance },
    });
    vm = component.vm as any;
  }

  beforeEach(() => {
    postSpy.mockClear();
    changeHeicTypeSpy.mockClear();
    resizeImageFileToCapSpy.mockClear();
  });

  it('the component displayed', () => {
    mountComponent();

    expect(component.exists());
    expect(component.contains('.file-uploader.smart-uploader')).toBe(true);
  });

  it('errors: unsupported_file', async () => {
    mountComponent();

    const file = new File(['foo'], 'foo.txt', {
      type: 'text/plain',
    });
    await vm.addFilesHandler([file]);
    expect(vm.errors).toEqual(['errors.unsupported_file']);
  });

  it('errors: fail validation', async () => {
    mountComponent();

    const file = getMockPdfFile('foo.pdf');

    // Upload
    await vm.addFilesHandler([file]);
    expect(postSpy).toHaveBeenCalledTimes(1);

    expect(dynamicFormInstance.dataSource[vm.$props.element.name]).toHaveLength(1);
  });

  it('default converterMethods must be different other uploaders', async () => {
    mountComponent();

    const file = new File(['foo'], 'foo.png', {
      type: 'image/png',
    });

    await vm.addFilesHandler([file]);
    expect(changeHeicTypeSpy).toHaveBeenCalledTimes(0);
    expect(resizeImageFileToCapSpy).toHaveBeenCalledWith(file, { maxWidth: 1600 });
    expect(vm.value).toHaveLength(1);
  });

  it('schema converterMethods', async () => {
    mountComponent({
      ...defaultSchema,
      configs: {
        // custom schema will bypass default changeHeicTypeSpy
        converter_methods: [
          {
            method: 'resize_to_cap',
            params: {
              maxWidth: 320,
            },
          },
        ],
      },
    });

    const file = new File(['foo'], 'foo.png', {
      type: 'image/png',
    });

    await vm.addFilesHandler([file]);
    expect(changeHeicTypeSpy).toHaveBeenCalledTimes(0);
    expect(resizeImageFileToCapSpy).toHaveBeenCalledWith(file, { maxWidth: 320 });
    expect(vm.value).toHaveLength(1);
  });

  it('Current state should clear value after file list was empty', async () => {
    mountComponent();

    const file = new File(['foo'], 'foo.png', {
      type: 'image/png',
    });

    vm.files = [];
    vm.value = [];
    vm.currentPassword = '1234';
    vm.isPasswordSuccessOnce = true;
    vm.hasPasswordFailedOnce = true;
    vm.currentErrorFile = file;

    await vm.addFilesHandler([file]);
    expect(changeHeicTypeSpy).toHaveBeenCalledTimes(0);
    expect(resizeImageFileToCapSpy).toHaveBeenCalledWith(file, { maxWidth: 1600 });
    expect(vm.files).toHaveLength(1);

    await vm.removeFile(vm.value[0]);
    expect(vm.files).toHaveLength(0);
    expect(vm.currentPassword).toBe('');
    expect(vm.isPasswordSuccessOnce).toBeFalsy();
    expect(vm.hasPasswordFailedOnce).toBeFalsy();
    expect(vm.currentErrorFile).toBeUndefined();
    expect(vm.currentFiles).toHaveLength(0);
  });

  it('Should change modal state to validating', async () => {
    mountComponent();

    vm.modalState = 'none';

    vm.checkPassword();

    expect(vm.modalState).toBe('validating');
  });

  it('Should set flag need password to target file', async () => {
    mountComponent();

    const file = getMockPdfFile('foo.pdf');

    vm.currentFiles = [file];
    vm.flagNeedPassword(file);

    expect(vm.currentFiles[0].needPassword).toBeTruthy();
  });

  it('Should get valid additional payload for target file', async () => {
    mountComponent();

    const file = new File(['foo'], 'foo.png', {
      type: 'image/png',
    });
    vm.currentPassword = 'abcd';
    await vm.addFilesHandler([file]);

    // No need password
    const targetFileInfo = vm.files[0];
    const res1 = vm.getAdditionalUploadPayload(targetFileInfo);
    expect(res1).toStrictEqual({ password: undefined });

    // Need password
    targetFileInfo.need_password = 1;
    const res2 = vm.getAdditionalUploadPayload(targetFileInfo);
    expect(res2).toStrictEqual({ password: 'abcd' });
  });

  describe('Check file by fileChecker', () => {
    it('Should not pass basic check', async () => {
      const file = new File(['foo'], 'foo.png', {
        type: 'image/png',
      });

      const res = await vm.fileChecker(file, 'text');

      expect(res.success).toBeFalsy();
    });

    it('Should not pass max page', async () => {
      mountComponent({
        ...defaultSchema,
        configs: { ...defaultSchema.configs, max_file_count: 2 },
      });

      const file = new File(['foo'], 'foo.png', {
        type: 'image/png',
      });
      vm.alreadyAtMax = true;

      const res = await vm.fileChecker(file, 'png');

      expect(res.success).toBeFalsy();
    });

    it('Should success (non pdf file)', async () => {
      mountComponent();

      const file = new File(['foo'], 'foo.png', {
        type: 'image/png',
      });
      vm.alreadyAtMax = false;

      const res = await vm.fileChecker(file, 'png');

      expect(res.success).toBeTruthy();
    });

    it('Should check fail with password pdf file (invalid_modal_state)', async () => {
      mountComponent({
        ...defaultSchema,
        configs: { ...defaultSchema.configs, max_file_count: 2 },
      });

      vm.isPasswordSuccessOnce = false;
      vm.alreadyAtMax = false;
      vm.modalState = 'validating';

      const file = getMockPdfFile('foo.pdf');

      const res = await vm.fileChecker(file, 'pdf');

      console.log('EASSADASD ', res.message);

      expect(res.message).toContain('invalid_modal_state');
      expect(res.success).toBeFalsy();
    });

    it('Should check fail with password pdf file (password not ok)', async () => {
      mountComponent({
        ...defaultSchema,
        configs: { ...defaultSchema.configs, max_file_count: 2 },
      });

      vm.isPasswordSuccessOnce = false;
      vm.alreadyAtMax = false;

      const file = getMockPdfFile('foo.pdf');

      const res = await vm.fileChecker(file, 'pdf');

      expect(res.message).toContain('password not ok');
      expect(res.success).toBeFalsy();
    });

    it('Should check success with password pdf file', async () => {
      mountComponent({
        ...defaultSchema,
        configs: { ...defaultSchema.configs, max_file_count: 2 },
      });

      vm.isPasswordSuccessOnce = false;
      const file = getMockPdfFile('foo.pdf');
      vm.alreadyAtMax = false;

      const res = await vm.fileChecker(file, 'pdf');

      expect(vm.isPasswordSuccessOnce).toBeTruthy();
      expect(vm.modalState).toBe('validating');
      expect(res.success).toBeTruthy();
    });
  });
});
