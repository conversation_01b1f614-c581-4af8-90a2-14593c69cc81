{"version": 3, "sources": ["../../date-fns/locale/en-US/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,uBAAuB;AAAA,MACzB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AACA,QAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,WAAW,UAAU;AAC9E,aAAO,qBAAqB,KAAK;AAAA,IACnC;AACA,QAAI,WAAW;AACf,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ;AAAA;AAAA;", "names": ["formatRelative"]}